"""Initial database migration

Revision ID: 001
Revises:
Create Date: 2023-07-01

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite


# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if users table exists
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)
    tables = inspector.get_table_names()

    # Only create users table if it doesn't exist
    if 'users' not in tables:
        # Create users table
        op.create_table('users',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('username', sa.String(length=50), nullable=True),
            sa.Column('email', sa.String(length=100), nullable=True),
            sa.Column('hashed_password', sa.String(length=100), nullable=True),
            sa.Column('is_active', sa.<PERSON>(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('updated_at', sa.DateTime(), nullable=True),
            sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
        op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
        op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)

    # Check if files table exists
    if 'files' not in tables:
        # Create files table
        op.create_table('files',
            sa.Column('id', sa.String(length=36), nullable=False),
            sa.Column('filename', sa.String(length=255), nullable=True),
            sa.Column('file_path', sa.String(length=255), nullable=True),
            sa.Column('file_size', sa.Integer(), nullable=True),
            sa.Column('num_rows', sa.Integer(), nullable=True),
            sa.Column('columns', sa.JSON(), nullable=True),
            sa.Column('user_id', sa.Integer(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('updated_at', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_files_id'), 'files', ['id'], unique=False)

    # Check if tasks table exists
    if 'tasks' not in tables:
        # Create tasks table
        op.create_table('tasks',
            sa.Column('id', sa.String(length=36), nullable=False),
            sa.Column('task_type', sa.String(length=50), nullable=True),
            sa.Column('status', sa.String(length=20), nullable=True),
            sa.Column('message', sa.Text(), nullable=True),
            sa.Column('input_file_id', sa.String(length=36), nullable=True),
            sa.Column('result_file_path', sa.String(length=255), nullable=True),
            sa.Column('config', sa.JSON(), nullable=True),
            sa.Column('user_id', sa.Integer(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('updated_at', sa.DateTime(), nullable=True),
            sa.Column('completed_at', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['input_file_id'], ['files.id'], ),
            sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_tasks_id'), 'tasks', ['id'], unique=False)

    # Check if hf_models table exists
    if 'hf_models' not in tables:
        # Create hf_models table
        op.create_table('hf_models',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('name', sa.String(length=100), nullable=True),
            sa.Column('base_model', sa.String(length=100), nullable=True),
            sa.Column('model_path', sa.String(length=255), nullable=True),
            sa.Column('num_epochs', sa.Integer(), nullable=True),
            sa.Column('training_file_id', sa.String(length=36), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('updated_at', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['training_file_id'], ['files.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_hf_models_id'), 'hf_models', ['id'], unique=False)
        op.create_index(op.f('ix_hf_models_name'), 'hf_models', ['name'], unique=True)

    # Check if hf_rules table exists
    if 'hf_rules' not in tables:
        # Create hf_rules table
        op.create_table('hf_rules',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('model_id', sa.Integer(), nullable=True),
            sa.Column('label', sa.String(length=100), nullable=True),
            sa.Column('keywords', sa.Text(), nullable=True),
            sa.Column('confidence_threshold', sa.Float(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True),
            sa.Column('updated_at', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['model_id'], ['hf_models.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_hf_rules_id'), 'hf_rules', ['id'], unique=False)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_index(op.f('ix_hf_rules_id'), table_name='hf_rules')
    op.drop_table('hf_rules')
    op.drop_index(op.f('ix_hf_models_name'), table_name='hf_models')
    op.drop_index(op.f('ix_hf_models_id'), table_name='hf_models')
    op.drop_table('hf_models')
    op.drop_index(op.f('ix_tasks_id'), table_name='tasks')
    op.drop_table('tasks')
    op.drop_index(op.f('ix_files_id'), table_name='files')
    op.drop_table('files')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
