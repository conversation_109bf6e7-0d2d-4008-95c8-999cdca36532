#!/usr/bin/env python3
"""
Test script to verify the agent registry fix.

This script tests that the agent registry can properly load persona configurations
with both 'class_path' and 'agent_class' fields and register the agents correctly.
"""

import os
import sys
import logging
from pathlib import Path

# Add backend root to path
backend_root = Path(__file__).parent
sys.path.insert(0, str(backend_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_persona_loading():
    """Test that persona configurations can be loaded."""
    logger.info("Testing persona configuration loading...")
    
    try:
        from agents.persona_manager import PersonaManager
        
        # Create persona manager
        manager = PersonaManager()
        
        # Load persona configurations
        configs = manager.load_persona_configs()
        
        logger.info(f"✅ Loaded {len(configs)} persona configurations")
        for persona_id, config in configs.items():
            agent_class = config.get('agent_class', 'Not specified')
            logger.info(f"  - {persona_id}: {config.get('name', 'Unknown')} -> {agent_class}")
        
        return True, configs
        
    except Exception as e:
        logger.error(f"❌ Persona loading failed: {e}")
        return False, {}


def test_agent_registry_loading():
    """Test that the agent registry can load configurations."""
    logger.info("Testing agent registry configuration loading...")
    
    try:
        from agents.registry import AgentRegistry
        
        # Load configurations from personas directory
        personas_dir = os.path.join(os.path.dirname(__file__), "personas")
        AgentRegistry.load_configurations(personas_dir)
        
        # Check what was loaded
        configurations = AgentRegistry._configurations
        registry = AgentRegistry._registry
        
        logger.info(f"✅ Agent registry loaded {len(configurations)} configurations")
        logger.info(f"✅ Agent registry has {len(registry)} registered agents")
        
        for persona_id, config in configurations.items():
            agent_class_path = config.get('agent_class') or config.get('class_path', 'Not specified')
            logger.info(f"  - Config: {persona_id} -> {agent_class_path}")
        
        for persona_id, agent_class in registry.items():
            logger.info(f"  - Registered: {persona_id} -> {agent_class.__name__}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent registry loading failed: {e}")
        return False


def test_agent_creation():
    """Test that agents can be created from the registry."""
    logger.info("Testing agent creation...")
    
    try:
        from agents.registry import AgentRegistry
        import asyncio
        
        async def create_test_agents():
            # Try to create instances of registered agents
            registry = AgentRegistry._registry
            created_agents = []
            
            for persona_id, agent_class in registry.items():
                try:
                    logger.info(f"Creating agent for persona: {persona_id}")
                    agent = await AgentRegistry.create_agent_instance(persona_id)
                    if agent:
                        created_agents.append((persona_id, agent))
                        logger.info(f"  ✅ Successfully created {agent_class.__name__}")
                    else:
                        logger.warning(f"  ⚠️ Failed to create agent for {persona_id}")
                except Exception as e:
                    logger.warning(f"  ⚠️ Error creating agent for {persona_id}: {e}")
            
            return created_agents
        
        # Run the async function
        created_agents = asyncio.run(create_test_agents())
        
        logger.info(f"✅ Successfully created {len(created_agents)} agents")
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent creation test failed: {e}")
        return False


def test_specific_agent_imports():
    """Test that specific agent classes can be imported."""
    logger.info("Testing specific agent imports...")
    
    agent_imports = [
        ("agents.analysis_agent.composable_agent", "ComposableAnalysisAgent"),
        ("agents.concierge_agent.concierge", "ConciergeAgent"),
        ("agents.classification.composable_agent", "ComposableClassificationAgent"),
        ("agents.marketing_agent.composable_agent", "ComposableMarketingAgent"),
    ]
    
    successful_imports = 0
    
    for module_path, class_name in agent_imports:
        try:
            import importlib
            module = importlib.import_module(module_path)
            agent_class = getattr(module, class_name)
            logger.info(f"  ✅ Successfully imported {class_name} from {module_path}")
            successful_imports += 1
        except Exception as e:
            logger.error(f"  ❌ Failed to import {class_name} from {module_path}: {e}")
    
    logger.info(f"✅ Successfully imported {successful_imports}/{len(agent_imports)} agent classes")
    return successful_imports == len(agent_imports)


def test_field_compatibility():
    """Test that both 'class_path' and 'agent_class' fields work."""
    logger.info("Testing field compatibility...")
    
    try:
        # Test configuration with agent_class field
        config_with_agent_class = {
            "name": "Test Agent 1",
            "agent_class": "agents.analysis_agent.composable_agent.ComposableAnalysisAgent"
        }
        
        # Test configuration with class_path field
        config_with_class_path = {
            "name": "Test Agent 2", 
            "class_path": "agents.concierge_agent.concierge.ConciergeAgent"
        }
        
        # Test the logic from the registry
        agent_class_path_1 = config_with_agent_class.get("class_path") or config_with_agent_class.get("agent_class")
        agent_class_path_2 = config_with_class_path.get("class_path") or config_with_class_path.get("agent_class")
        
        assert agent_class_path_1 == "agents.analysis_agent.composable_agent.ComposableAnalysisAgent"
        assert agent_class_path_2 == "agents.concierge_agent.concierge.ConciergeAgent"
        
        logger.info("✅ Both 'agent_class' and 'class_path' fields work correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ Field compatibility test failed: {e}")
        return False


def main():
    """Run all agent registry fix tests."""
    logger.info("🔧 Agent Registry Fix Test")
    logger.info("=" * 50)
    
    tests = [
        ("Persona Loading", test_persona_loading),
        ("Agent Registry Loading", test_agent_registry_loading),
        ("Specific Agent Imports", test_specific_agent_imports),
        ("Field Compatibility", test_field_compatibility),
        ("Agent Creation", test_agent_creation)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            if test_name == "Persona Loading":
                result, configs = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    logger.info("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All agent registry tests passed!")
        logger.info("The 'class_path' missing warnings should be resolved.")
        return 0
    else:
        logger.error(f"💥 {total - passed} tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
