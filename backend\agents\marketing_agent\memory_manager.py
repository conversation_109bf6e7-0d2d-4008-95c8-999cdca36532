"""
Memory management system for marketing agent.

This module provides comprehensive memory management including context cleanup,
conversation timeout handling, and memory optimization.
"""

import gc
import os
import time
import logging
import asyncio
import threading
import weakref
from typing import Dict, Any, List, Optional, Set, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import psutil

logger = logging.getLogger(__name__)


@dataclass
class MemoryStats:
    """Memory usage statistics."""
    total_memory_mb: float
    used_memory_mb: float
    available_memory_mb: float
    memory_percent: float
    process_memory_mb: float
    gc_collections: Dict[int, int]
    active_conversations: int
    cached_contexts: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'total_memory_mb': self.total_memory_mb,
            'used_memory_mb': self.used_memory_mb,
            'available_memory_mb': self.available_memory_mb,
            'memory_percent': self.memory_percent,
            'process_memory_mb': self.process_memory_mb,
            'gc_collections': self.gc_collections,
            'active_conversations': self.active_conversations,
            'cached_contexts': self.cached_contexts
        }


@dataclass
class ConversationContext:
    """Represents a conversation context with metadata."""
    conversation_id: str
    user_id: str
    created_at: float
    last_accessed: float
    message_count: int = 0
    context_size_bytes: int = 0
    is_active: bool = True
    cleanup_callbacks: List[Callable] = field(default_factory=list)
    
    def touch(self) -> None:
        """Update last accessed time."""
        self.last_accessed = time.time()
    
    def is_expired(self, timeout: int) -> bool:
        """Check if conversation is expired."""
        return time.time() - self.last_accessed > timeout
    
    def add_cleanup_callback(self, callback: Callable) -> None:
        """Add cleanup callback."""
        self.cleanup_callbacks.append(callback)
    
    async def cleanup(self) -> None:
        """Execute cleanup callbacks."""
        for callback in self.cleanup_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                logger.error(f"Error in cleanup callback: {e}")
        
        self.cleanup_callbacks.clear()
        self.is_active = False


class MemoryManager:
    """Comprehensive memory manager for marketing agent."""
    
    def __init__(
        self,
        max_memory_mb: int = 1024,
        conversation_timeout: int = 1800,  # 30 minutes
        cleanup_interval: int = 300,  # 5 minutes
        gc_threshold: float = 0.8,  # 80% memory usage
        max_conversations: int = 1000
    ):
        self.max_memory_mb = max_memory_mb
        self.conversation_timeout = conversation_timeout
        self.cleanup_interval = cleanup_interval
        self.gc_threshold = gc_threshold
        self.max_conversations = max_conversations
        
        # Conversation tracking
        self._conversations: Dict[str, ConversationContext] = {}
        self._conversation_lock = asyncio.Lock()
        
        # Context caching
        self._context_cache: Dict[str, Any] = {}
        self._context_access_times: Dict[str, float] = {}
        self._context_lock = asyncio.Lock()
        
        # Memory monitoring
        self._memory_stats_history: deque = deque(maxlen=100)
        self._cleanup_stats = {
            'conversations_cleaned': 0,
            'contexts_cleaned': 0,
            'memory_freed_mb': 0,
            'gc_collections_forced': 0
        }
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._monitoring_task: Optional[asyncio.Task] = None
        self._is_running = False
        
        # Weak references for automatic cleanup
        self._weak_refs: Set[weakref.ref] = set()
    
    async def start(self) -> None:
        """Start memory management background tasks."""
        if self._is_running:
            return
        
        self._is_running = True
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        # Start monitoring task
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info("Memory manager started")
    
    async def stop(self) -> None:
        """Stop memory management background tasks."""
        self._is_running = False
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        # Final cleanup
        await self._cleanup_expired_conversations()
        await self._cleanup_context_cache()
        
        logger.info("Memory manager stopped")
    
    async def register_conversation(
        self,
        conversation_id: str,
        user_id: str,
        initial_context: Optional[Dict[str, Any]] = None
    ) -> ConversationContext:
        """Register a new conversation."""
        async with self._conversation_lock:
            # Check if we're at max conversations
            if len(self._conversations) >= self.max_conversations:
                await self._cleanup_oldest_conversations(count=10)
            
            # Create conversation context
            context = ConversationContext(
                conversation_id=conversation_id,
                user_id=user_id,
                created_at=time.time(),
                last_accessed=time.time()
            )
            
            # Calculate initial context size
            if initial_context:
                context.context_size_bytes = len(str(initial_context).encode('utf-8'))
            
            self._conversations[conversation_id] = context
            
            logger.debug(f"Registered conversation {conversation_id} for user {user_id}")
            return context
    
    async def get_conversation(self, conversation_id: str) -> Optional[ConversationContext]:
        """Get conversation context."""
        async with self._conversation_lock:
            context = self._conversations.get(conversation_id)
            if context:
                context.touch()
            return context
    
    async def update_conversation(
        self,
        conversation_id: str,
        message_count: Optional[int] = None,
        context_size: Optional[int] = None
    ) -> bool:
        """Update conversation metadata."""
        async with self._conversation_lock:
            context = self._conversations.get(conversation_id)
            if not context:
                return False
            
            context.touch()
            
            if message_count is not None:
                context.message_count = message_count
            
            if context_size is not None:
                context.context_size_bytes = context_size
            
            return True
    
    async def cleanup_conversation(self, conversation_id: str) -> bool:
        """Manually cleanup a conversation."""
        async with self._conversation_lock:
            context = self._conversations.get(conversation_id)
            if not context:
                return False
            
            await context.cleanup()
            del self._conversations[conversation_id]
            
            # Remove from context cache
            await self._remove_context_cache(conversation_id)
            
            logger.debug(f"Cleaned up conversation {conversation_id}")
            return True
    
    async def cache_context(
        self,
        key: str,
        context: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> None:
        """Cache context data with optional TTL."""
        async with self._context_lock:
            self._context_cache[key] = context
            self._context_access_times[key] = time.time()
            
            # Apply TTL if specified
            if ttl:
                def cleanup_callback():
                    asyncio.create_task(self._remove_context_cache(key))
                
                # Schedule cleanup
                asyncio.get_event_loop().call_later(ttl, cleanup_callback)
    
    async def get_cached_context(self, key: str) -> Optional[Dict[str, Any]]:
        """Get cached context data."""
        async with self._context_lock:
            context = self._context_cache.get(key)
            if context:
                self._context_access_times[key] = time.time()
            return context
    
    async def _remove_context_cache(self, key: str) -> None:
        """Remove context from cache."""
        async with self._context_lock:
            self._context_cache.pop(key, None)
            self._context_access_times.pop(key, None)
    
    async def get_memory_stats(self) -> MemoryStats:
        """Get current memory statistics."""
        try:
            # System memory
            memory = psutil.virtual_memory()
            
            # Process memory
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Garbage collection stats
            gc_stats = {}
            for i in range(3):
                gc_stats[i] = gc.get_count()[i]
            
            stats = MemoryStats(
                total_memory_mb=memory.total / 1024 / 1024,
                used_memory_mb=memory.used / 1024 / 1024,
                available_memory_mb=memory.available / 1024 / 1024,
                memory_percent=memory.percent,
                process_memory_mb=process_memory,
                gc_collections=gc_stats,
                active_conversations=len(self._conversations),
                cached_contexts=len(self._context_cache)
            )
            
            # Store in history
            self._memory_stats_history.append(stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return MemoryStats(0, 0, 0, 0, 0, {}, 0, 0)
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop."""
        while self._is_running:
            try:
                await self._cleanup_expired_conversations()
                await self._cleanup_context_cache()
                await self._check_memory_pressure()
                
                await asyncio.sleep(self.cleanup_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(self.cleanup_interval)
    
    async def _monitoring_loop(self) -> None:
        """Background monitoring loop."""
        while self._is_running:
            try:
                stats = await self.get_memory_stats()
                
                # Check for memory pressure
                if stats.memory_percent > self.gc_threshold * 100:
                    logger.warning(f"High memory usage: {stats.memory_percent:.1f}%")
                    await self._force_cleanup()
                
                await asyncio.sleep(60)  # Monitor every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_expired_conversations(self) -> int:
        """Cleanup expired conversations."""
        async with self._conversation_lock:
            expired_ids = []
            
            for conv_id, context in self._conversations.items():
                if context.is_expired(self.conversation_timeout):
                    expired_ids.append(conv_id)
            
            # Cleanup expired conversations
            for conv_id in expired_ids:
                context = self._conversations[conv_id]
                await context.cleanup()
                del self._conversations[conv_id]
                await self._remove_context_cache(conv_id)
            
            if expired_ids:
                self._cleanup_stats['conversations_cleaned'] += len(expired_ids)
                logger.info(f"Cleaned up {len(expired_ids)} expired conversations")
            
            return len(expired_ids)
    
    async def _cleanup_oldest_conversations(self, count: int = 10) -> int:
        """Cleanup oldest conversations."""
        async with self._conversation_lock:
            if len(self._conversations) <= count:
                return 0
            
            # Sort by last accessed time
            sorted_conversations = sorted(
                self._conversations.items(),
                key=lambda x: x[1].last_accessed
            )
            
            # Remove oldest conversations
            removed_count = 0
            for conv_id, context in sorted_conversations[:count]:
                await context.cleanup()
                del self._conversations[conv_id]
                await self._remove_context_cache(conv_id)
                removed_count += 1
            
            if removed_count > 0:
                self._cleanup_stats['conversations_cleaned'] += removed_count
                logger.info(f"Cleaned up {removed_count} oldest conversations")
            
            return removed_count
    
    async def _cleanup_context_cache(self) -> int:
        """Cleanup old context cache entries."""
        async with self._context_lock:
            current_time = time.time()
            cache_timeout = self.conversation_timeout  # Use same timeout as conversations
            
            expired_keys = []
            for key, access_time in self._context_access_times.items():
                if current_time - access_time > cache_timeout:
                    expired_keys.append(key)
            
            # Remove expired entries
            for key in expired_keys:
                self._context_cache.pop(key, None)
                self._context_access_times.pop(key, None)
            
            if expired_keys:
                self._cleanup_stats['contexts_cleaned'] += len(expired_keys)
                logger.debug(f"Cleaned up {len(expired_keys)} expired context cache entries")
            
            return len(expired_keys)
    
    async def _check_memory_pressure(self) -> None:
        """Check for memory pressure and take action."""
        stats = await self.get_memory_stats()
        
        if stats.memory_percent > self.gc_threshold * 100:
            await self._force_cleanup()
    
    async def _force_cleanup(self) -> None:
        """Force aggressive cleanup."""
        logger.warning("Forcing aggressive cleanup due to memory pressure")
        
        # Cleanup half of the conversations
        conversation_count = len(self._conversations)
        if conversation_count > 10:
            await self._cleanup_oldest_conversations(conversation_count // 2)
        
        # Clear context cache
        async with self._context_lock:
            cache_size = len(self._context_cache)
            self._context_cache.clear()
            self._context_access_times.clear()
            self._cleanup_stats['contexts_cleaned'] += cache_size
        
        # Force garbage collection
        collected = gc.collect()
        self._cleanup_stats['gc_collections_forced'] += 1
        
        logger.info(f"Forced cleanup completed: {collected} objects collected")
    
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """Get cleanup statistics."""
        return self._cleanup_stats.copy()
    
    def add_weak_reference(self, obj: Any, cleanup_callback: Optional[Callable] = None) -> None:
        """Add weak reference for automatic cleanup."""
        def cleanup(ref):
            self._weak_refs.discard(ref)
            if cleanup_callback:
                try:
                    cleanup_callback()
                except Exception as e:
                    logger.error(f"Error in weak reference cleanup: {e}")
        
        weak_ref = weakref.ref(obj, cleanup)
        self._weak_refs.add(weak_ref)


# Global memory manager instance
memory_manager: Optional[MemoryManager] = None


def initialize_memory_manager(config: Dict[str, Any]) -> MemoryManager:
    """Initialize global memory manager."""
    global memory_manager
    
    memory_manager = MemoryManager(
        max_memory_mb=config.get('max_memory_mb', 1024),
        conversation_timeout=config.get('conversation_timeout', 1800),
        cleanup_interval=config.get('cleanup_interval', 300),
        gc_threshold=config.get('gc_threshold', 0.8),
        max_conversations=config.get('max_conversations', 1000)
    )
    
    logger.info("Memory manager initialized")
    return memory_manager


def get_memory_manager() -> Optional[MemoryManager]:
    """Get global memory manager instance."""
    return memory_manager
