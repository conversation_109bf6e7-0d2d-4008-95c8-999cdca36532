"""
Data analysis functions for the Analysis Agent.

This module provides data analysis functions that have been refactored
from the original Streamlit implementation to work with FastAPI.
"""

import logging
import pandas as pd
import numpy as np
import json
import base64
from typing import Dict, Any, Optional, List, Tuple, Union
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from io import BytesIO
import re
import string
from nltk.sentiment.vader import SentimentIntensityAnalyzer
import nltk
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer

# Configure logging
logger = logging.getLogger(__name__)

# Download required NLTK resources (only needed once)
try:
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
    nltk.download('punkt', quiet=True)
    nltk.download('punkt_tab', quiet=True)
except Exception as e:
    logger.warning(f"Failed to download NLTK resources: {str(e)}")


def clean_data(data: pd.DataFrame, parameters: Dict[str, Any]) -> pd.DataFrame:
    """
    Clean data by handling missing values and other issues.

    Args:
        data: The data to clean
        parameters: Cleaning parameters

    Returns:
        Cleaned data
    """
    # Make a copy of the data to avoid modifying the original
    cleaned_data = data.copy()
    
    # Get parameters
    drop_columns = parameters.get("drop_columns", [])
    drop_rows = parameters.get("drop_rows", False)
    fill_method = parameters.get("fill_method", None)
    custom_value = parameters.get("custom_value", None)
    
    # Drop columns if specified
    if drop_columns:
        cleaned_data = cleaned_data.drop(columns=drop_columns)
    
    # Drop rows with missing values if specified
    if drop_rows:
        cleaned_data = cleaned_data.dropna(axis=0)
    
    # Fill missing values if specified
    if fill_method:
        if fill_method == "mean":
            cleaned_data = cleaned_data.fillna(cleaned_data.mean(numeric_only=True))
        elif fill_method == "median":
            cleaned_data = cleaned_data.fillna(cleaned_data.median(numeric_only=True))
        elif fill_method == "mode":
            cleaned_data = cleaned_data.fillna(cleaned_data.mode().iloc[0])
        elif fill_method == "custom" and custom_value is not None:
            try:
                # Try to convert to float if possible
                cleaned_data = cleaned_data.fillna(float(custom_value))
            except ValueError:
                # Otherwise use as string
                cleaned_data = cleaned_data.fillna(custom_value)
    
    return cleaned_data


def visualize_data(data: pd.DataFrame, plot_type: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate a visualization of the data.

    Args:
        data: The data to visualize
        plot_type: The type of plot to generate
        parameters: Visualization parameters

    Returns:
        Visualization data
    """
    # Get parameters
    x_column = parameters.get("x_column", None)
    y_column = parameters.get("y_column", None)
    color_column = parameters.get("color_column", None)
    title = parameters.get("title", f"{plot_type.capitalize()} Plot")
    width = parameters.get("width", 800)
    height = parameters.get("height", 500)
    
    # If columns are not specified, try to infer them
    if not x_column and data.shape[1] > 0:
        x_column = data.columns[0]
    
    if not y_column and data.shape[1] > 1:
        y_column = data.columns[1]
    
    # Create the plot based on the type
    try:
        if plot_type == "histogram":
            if not x_column:
                raise ValueError("No column specified for histogram")
            
            fig = px.histogram(
                data, 
                x=x_column,
                title=title,
                width=width,
                height=height
            )
            
        elif plot_type == "scatter":
            if not x_column or not y_column:
                raise ValueError("Both x and y columns must be specified for scatter plot")
            
            fig = px.scatter(
                data,
                x=x_column,
                y=y_column,
                color=color_column,
                title=title,
                width=width,
                height=height
            )
            
        elif plot_type == "bar":
            if not x_column or not y_column:
                raise ValueError("Both x and y columns must be specified for bar plot")
            
            fig = px.bar(
                data,
                x=x_column,
                y=y_column,
                color=color_column,
                title=title,
                width=width,
                height=height
            )
            
        elif plot_type == "box":
            if not x_column:
                raise ValueError("No column specified for box plot")
            
            fig = px.box(
                data,
                x=x_column,
                y=y_column,
                color=color_column,
                title=title,
                width=width,
                height=height
            )
            
        elif plot_type == "line":
            if not x_column or not y_column:
                raise ValueError("Both x and y columns must be specified for line plot")
            
            fig = px.line(
                data,
                x=x_column,
                y=y_column,
                color=color_column,
                title=title,
                width=width,
                height=height
            )
            
        elif plot_type == "pie":
            if not x_column or not y_column:
                raise ValueError("Both name and value columns must be specified for pie chart")
            
            fig = px.pie(
                data,
                names=x_column,
                values=y_column,
                title=title,
                width=width,
                height=height
            )
            
        elif plot_type == "heatmap":
            # For heatmap, we need a correlation matrix
            corr = data.select_dtypes(include=[np.number]).corr()
            
            fig = px.imshow(
                corr,
                title=title,
                width=width,
                height=height
            )
            
        elif plot_type == "map":
            # For map, we need latitude and longitude columns
            lat_col = parameters.get("lat_column", None)
            lon_col = parameters.get("lon_column", None)
            
            if not lat_col or not lon_col:
                # Try to find latitude and longitude columns
                for col in data.columns:
                    if "lat" in col.lower():
                        lat_col = col
                    elif "lon" in col.lower() or "lng" in col.lower():
                        lon_col = col
            
            if not lat_col or not lon_col:
                raise ValueError("Latitude and longitude columns must be specified for map")
            
            fig = px.scatter_mapbox(
                data,
                lat=lat_col,
                lon=lon_col,
                color=color_column,
                title=title,
                width=width,
                height=height,
                mapbox_style="open-street-map"
            )
            
        else:
            raise ValueError(f"Unsupported plot type: {plot_type}")
        
        # Convert the figure to JSON
        plot_json = fig.to_json()
        
        # Also generate a static image as fallback
        img_bytes = fig.to_image(format="png")
        img_base64 = base64.b64encode(img_bytes).decode("utf-8")
        
        return {
            "plot_type": plot_type,
            "plot_json": plot_json,
            "plot_image": f"data:image/png;base64,{img_base64}"
        }
        
    except Exception as e:
        logger.error(f"Error generating visualization: {str(e)}", exc_info=True)
        
        # Create a simple error plot
        fig = go.Figure()
        fig.add_annotation(
            text=f"Error generating {plot_type} plot: {str(e)}",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False
        )
        
        # Convert the figure to JSON
        plot_json = fig.to_json()
        
        # Also generate a static image as fallback
        img_bytes = fig.to_image(format="png")
        img_base64 = base64.b64encode(img_bytes).decode("utf-8")
        
        return {
            "plot_type": "error",
            "error": str(e),
            "plot_json": plot_json,
            "plot_image": f"data:image/png;base64,{img_base64}"
        }


async def query_data(data: pd.DataFrame, query: str, llm: Any) -> Dict[str, Any]:
    """
    Query the data using natural language.

    Args:
        data: The data to query
        query: The natural language query
        llm: The language model to use

    Returns:
        Query result
    """
    try:
        # Create a prompt template
        prompt_template = """
        You are a data analysis assistant. Answer the following question about the data.
        
        Data information:
        - Shape: {shape}
        - Columns: {columns}
        - Data types: {dtypes}
        - First few rows: {head}
        
        Question: {query}
        
        Provide a clear, concise answer. If you need to perform calculations, explain your approach.
        If the question cannot be answered with the available data, explain why.
        """
        
        # Format the prompt
        prompt = prompt_template.format(
            shape=str(data.shape),
            columns=str(data.columns.tolist()),
            dtypes=str(data.dtypes.to_dict()),
            head=str(data.head().to_dict()),
            query=query
        )
        
        # Create a chain
        from langchain_core.prompts import ChatPromptTemplate
        from langchain.chains import LLMChain
        
        chat_prompt = ChatPromptTemplate.from_template(prompt_template)
        chain = LLMChain(llm=llm, prompt=chat_prompt)
        
        # Execute the chain
        response = await chain.arun(
            shape=str(data.shape),
            columns=str(data.columns.tolist()),
            dtypes=str(data.dtypes.to_dict()),
            head=str(data.head().to_dict()),
            query=query
        )
        
        # Try to extract any relevant data for visualization
        data_preview = None
        try:
            # If the response mentions specific columns, create a preview of those columns
            mentioned_columns = []
            for col in data.columns:
                if col in response:
                    mentioned_columns.append(col)
            
            if mentioned_columns:
                data_preview = data[mentioned_columns].head(10).to_dict(orient="records")
        except Exception as e:
            logger.warning(f"Error creating data preview: {str(e)}")
        
        return {
            "answer": response,
            "data_preview": data_preview
        }
        
    except Exception as e:
        logger.error(f"Error querying data: {str(e)}", exc_info=True)
        return {
            "answer": f"I encountered an error while trying to answer your question: {str(e)}",
            "data_preview": None
        }


def advanced_query(data: pd.DataFrame, query: str) -> pd.DataFrame:
    """
    Execute an advanced query on the data.

    Args:
        data: The data to query
        query: The SQL-like query

    Returns:
        Query result
    """
    try:
        # Extract the actual query from the message
        # Look for patterns like "SELECT ... FROM ..." or "WHERE ..."
        sql_pattern = r"SELECT|WHERE|GROUP BY|ORDER BY|HAVING|JOIN"
        if re.search(sql_pattern, query, re.IGNORECASE):
            # Extract the SQL-like part
            sql_parts = re.split(r"(SELECT|WHERE|GROUP BY|ORDER BY|HAVING|JOIN)", query, flags=re.IGNORECASE)
            query_text = "".join(sql_parts[1:]) if len(sql_parts) > 1 else query
        else:
            # If no SQL keywords found, try to use the whole message as a pandas query
            query_text = query
        
        # Try to execute as a pandas query
        result = data.query(query_text)
        return result
    except Exception as e:
        logger.error(f"Error executing advanced query: {str(e)}", exc_info=True)
        raise ValueError(f"Error executing query: {str(e)}")


def filter_data(data: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
    """
    Filter the data based on specified filters.

    Args:
        data: The data to filter
        filters: The filters to apply

    Returns:
        Filtered data
    """
    try:
        filtered_data = data.copy()
        
        for column, filter_value in filters.items():
            if column not in filtered_data.columns:
                continue
            
            # Handle different filter types based on data type
            if filtered_data[column].dtype == "object":
                # For categorical columns, filter by exact match or list of values
                if isinstance(filter_value, list):
                    filtered_data = filtered_data[filtered_data[column].isin(filter_value)]
                else:
                    filtered_data = filtered_data[filtered_data[column] == filter_value]
            else:
                # For numeric columns, filter by range
                if isinstance(filter_value, list) and len(filter_value) == 2:
                    min_val, max_val = filter_value
                    filtered_data = filtered_data[(filtered_data[column] >= min_val) & (filtered_data[column] <= max_val)]
                elif isinstance(filter_value, (int, float)):
                    filtered_data = filtered_data[filtered_data[column] == filter_value]
        
        return filtered_data
    except Exception as e:
        logger.error(f"Error filtering data: {str(e)}", exc_info=True)
        raise ValueError(f"Error filtering data: {str(e)}")


def text_preprocessing(text: str) -> str:
    """
    Preprocess text for sentiment analysis.

    Args:
        text: The text to preprocess

    Returns:
        Preprocessed text
    """
    # Convert text to lowercase
    text = str(text).lower()
    
    # Remove HTML tags and special characters
    text = re.sub(r'<[^>]*>', '', text)
    text = re.sub(r'[' + string.punctuation + ']', '', text)
    
    # Tokenize text
    tokens = nltk.word_tokenize(text)
    
    # Remove stopwords
    stop_words = set(stopwords.words('english'))
    tokens = [t for t in tokens if t not in stop_words]
    
    # Lemmatize words
    lemmatizer = WordNetLemmatizer()
    tokens = [lemmatizer.lemmatize(t) for t in tokens]
    
    # Join tokens back into a string
    return ' '.join(tokens)


def analyze_sentiment(data: pd.DataFrame, text_column: Optional[str] = None) -> Dict[str, Any]:
    """
    Perform sentiment analysis on text data.

    Args:
        data: The data containing text to analyze
        text_column: The column containing text to analyze

    Returns:
        Sentiment analysis results
    """
    try:
        # If text column is not specified, try to find a text column
        if not text_column:
            # Look for columns that might contain text
            text_columns = []
            for col in data.columns:
                if data[col].dtype == "object":
                    # Check if the column contains text (more than just a few characters)
                    avg_len = data[col].astype(str).str.len().mean()
                    if avg_len > 10:  # Arbitrary threshold
                        text_columns.append(col)
            
            if text_columns:
                text_column = text_columns[0]
            else:
                raise ValueError("No suitable text column found for sentiment analysis")
        
        # Check if the column exists
        if text_column not in data.columns:
            raise ValueError(f"Column '{text_column}' not found in the data")
        
        # Create a copy of the data
        result_data = data.copy()
        
        # Preprocess text data
        result_data['Clean_Text'] = result_data[text_column].apply(lambda x: text_preprocessing(str(x)))
        
        # Initialize sentiment analyzer
        sia = SentimentIntensityAnalyzer()
        
        # Perform sentiment analysis
        result_data['Sentiment'] = result_data['Clean_Text'].apply(lambda x: sia.polarity_scores(x)['compound'])
        
        # Create sentiment labels with improved thresholds
        result_data['Sentiment_Label'] = pd.cut(
            result_data['Sentiment'],
            bins=[-1, -0.1, 0.1, 1],
            labels=['Negative', 'Neutral', 'Positive'],
            include_lowest=True
        )
        
        # Create a summary of the sentiment analysis
        sentiment_counts = result_data['Sentiment_Label'].value_counts().to_dict()
        total_texts = len(result_data)
        
        summary = {
            "total_texts": total_texts,
            "positive_texts": sentiment_counts.get('Positive', 0),
            "negative_texts": sentiment_counts.get('Negative', 0),
            "neutral_texts": sentiment_counts.get('Neutral', 0),
            "positive_percentage": round(sentiment_counts.get('Positive', 0) / total_texts * 100, 2),
            "negative_percentage": round(sentiment_counts.get('Negative', 0) / total_texts * 100, 2),
            "neutral_percentage": round(sentiment_counts.get('Neutral', 0) / total_texts * 100, 2),
            "average_sentiment": round(result_data['Sentiment'].mean(), 2)
        }
        
        # Create visualizations
        try:
            # Sentiment distribution
            fig = px.pie(
                result_data,
                names='Sentiment_Label',
                title='Sentiment Distribution',
                color='Sentiment_Label',
                color_discrete_map={
                    'Positive': 'green',
                    'Neutral': 'gray',
                    'Negative': 'red'
                }
            )
            
            # Convert the figure to JSON
            plot_json = fig.to_json()
            
            # Also generate a static image as fallback
            img_bytes = fig.to_image(format="png")
            img_base64 = base64.b64encode(img_bytes).decode("utf-8")
            
            summary["visualization"] = {
                "plot_json": plot_json,
                "plot_image": f"data:image/png;base64,{img_base64}"
            }
            
            # Check if there's a date column for time-based analysis
            date_columns = []
            for col in data.columns:
                if pd.api.types.is_datetime64_any_dtype(data[col]) or 'date' in col.lower() or 'time' in col.lower():
                    date_columns.append(col)
            
            if date_columns:
                date_column = date_columns[0]
                
                # Ensure date is in datetime format
                result_data[date_column] = pd.to_datetime(result_data[date_column], errors='coerce')
                
                # Group by date and calculate sentiment counts
                daily_sentiment = result_data.groupby(pd.Grouper(key=date_column, freq='D'))['Sentiment_Label'].value_counts().unstack().fillna(0)
                
                # Create an interactive line chart
                fig2 = go.Figure()
                for label in daily_sentiment.columns:
                    fig2.add_trace(go.Scatter(x=daily_sentiment.index, y=daily_sentiment[label],
                                           mode='lines', name=label))
                
                fig2.update_layout(
                    title="Sentiment Over Time",
                    xaxis_title="Date",
                    yaxis_title="Count"
                )
                
                # Convert the figure to JSON
                plot_json2 = fig2.to_json()
                
                # Also generate a static image as fallback
                img_bytes2 = fig2.to_image(format="png")
                img_base64_2 = base64.b64encode(img_bytes2).decode("utf-8")
                
                summary["time_visualization"] = {
                    "plot_json": plot_json2,
                    "plot_image": f"data:image/png;base64,{img_base64_2}"
                }
        except Exception as e:
            logger.warning(f"Error creating sentiment visualizations: {str(e)}")
        
        return {
            "summary": summary,
            "data": result_data[['Clean_Text', 'Sentiment', 'Sentiment_Label']]
        }
        
    except Exception as e:
        logger.error(f"Error performing sentiment analysis: {str(e)}", exc_info=True)
        raise ValueError(f"Error performing sentiment analysis: {str(e)}")
