import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { businessProfileApi } from '@/lib/businessProfileApi';
import { useBusinessProfile } from '@/components/business-profile/BusinessProfileSelector';

export const BusinessProfileDebug: React.FC = () => {
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Use the hook to see what it returns
  const hookData = useBusinessProfile();

  const testDirectAPI = async () => {
    setIsLoading(true);
    setApiError(null);
    setApiResponse(null);

    try {
      console.log('🔍 Testing direct API call...');
      const response = await businessProfileApi.listProfiles();
      console.log('🔍 Direct API response:', response);
      setApiResponse(response);
    } catch (error) {
      console.error('🔍 Direct API error:', error);
      setApiError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log('🔍 Hook data changed:', hookData);
  }, [hookData]);

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle>Business Profile Debug</CardTitle>
          <CardDescription>Debug information for business profile loading</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testDirectAPI} disabled={isLoading}>
            {isLoading ? 'Testing...' : 'Test Direct API Call'}
          </Button>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Hook Data</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-60">
                  {JSON.stringify({
                    profiles: hookData.profiles,
                    profilesLength: hookData.profiles?.length,
                    profilesType: typeof hookData.profiles,
                    profilesIsArray: Array.isArray(hookData.profiles),
                    activeProfile: hookData.activeProfile,
                    isLoading: hookData.isLoading,
                    profileWithDataSources: hookData.profileWithDataSources
                  }, null, 2)}
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Direct API Response</CardTitle>
              </CardHeader>
              <CardContent>
                {apiError ? (
                  <div className="text-red-500 text-sm">
                    <strong>Error:</strong> {apiError}
                  </div>
                ) : apiResponse ? (
                  <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-60">
                    {JSON.stringify(apiResponse, null, 2)}
                  </pre>
                ) : (
                  <div className="text-muted-foreground text-sm">
                    Click "Test Direct API Call" to see response
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Console Logs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                Check the browser console for detailed logs with 🔍 prefix
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
};
