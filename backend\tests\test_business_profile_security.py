"""
Test suite for business profile security and production readiness.

This test suite covers security vulnerabilities, edge cases, and production
scenarios for the business profile implementation.
"""

import pytest
import uuid
from unittest.mock import Mock, patch
from fastapi import HTTPException
from sqlalchemy.orm import Session

from app.security.business_profile_security import (
    validate_uuid_format,
    validate_text_input,
    validate_metadata,
    check_user_profile_limit,
    check_profile_data_source_limit,
    sanitize_log_data,
    BusinessProfileSecurityConfig
)
from app.models.business_profile import (
    BusinessProfileCreate,
    BusinessProfileUpdate,
    BusinessProfileSwitchRequest,
    BusinessProfileDataSourceAssignmentCreate
)


class TestUUIDValidation:
    """Test UUID validation security."""
    
    def test_valid_uuid(self):
        """Test valid UUID passes validation."""
        test_uuid = str(uuid.uuid4())
        result = validate_uuid_format(test_uuid)
        assert result == test_uuid
    
    def test_invalid_uuid_format(self):
        """Test invalid UUID format raises exception."""
        with pytest.raises(HTTPException) as exc_info:
            validate_uuid_format("invalid-uuid")
        assert exc_info.value.status_code == 400
        assert "Invalid UUID format" in str(exc_info.value.detail)
    
    def test_empty_uuid(self):
        """Test empty UUID raises exception."""
        with pytest.raises(HTTPException) as exc_info:
            validate_uuid_format("")
        assert exc_info.value.status_code == 400
    
    def test_none_uuid(self):
        """Test None UUID raises exception."""
        with pytest.raises(HTTPException) as exc_info:
            validate_uuid_format(None)
        assert exc_info.value.status_code == 400
    
    def test_long_string_uuid(self):
        """Test extremely long string raises exception."""
        long_string = "a" * 100
        with pytest.raises(HTTPException) as exc_info:
            validate_uuid_format(long_string)
        assert exc_info.value.status_code == 400
        assert "too long" in str(exc_info.value.detail)


class TestTextInputValidation:
    """Test text input validation security."""
    
    def test_valid_text(self):
        """Test valid text passes validation."""
        result = validate_text_input("Valid business name", "name", 100)
        assert result == "Valid business name"
    
    def test_text_with_excessive_whitespace(self):
        """Test text with excessive whitespace is cleaned."""
        result = validate_text_input("  Multiple   spaces  ", "name", 100)
        assert result == "Multiple spaces"
    
    def test_forbidden_characters(self):
        """Test text with forbidden characters raises exception."""
        forbidden_chars = ['<', '>', '{', '}', '[', ']', '`', '\\', '|']
        for char in forbidden_chars:
            with pytest.raises(HTTPException) as exc_info:
                validate_text_input(f"Text with {char} character", "name", 100)
            assert exc_info.value.status_code == 400
            assert "invalid characters" in str(exc_info.value.detail)
    
    def test_text_too_long(self):
        """Test text exceeding max length raises exception."""
        long_text = "a" * 101
        with pytest.raises(HTTPException) as exc_info:
            validate_text_input(long_text, "name", 100)
        assert exc_info.value.status_code == 400
        assert "too long" in str(exc_info.value.detail)
    
    def test_required_empty_text(self):
        """Test required empty text raises exception."""
        with pytest.raises(HTTPException) as exc_info:
            validate_text_input("", "name", 100, required=True)
        assert exc_info.value.status_code == 400
        assert "required" in str(exc_info.value.detail)
    
    def test_optional_empty_text(self):
        """Test optional empty text returns None."""
        result = validate_text_input("", "description", 100, required=False)
        assert result is None


class TestMetadataValidation:
    """Test metadata validation security."""
    
    def test_valid_metadata(self):
        """Test valid metadata passes validation."""
        metadata = {
            "key1": "value1",
            "key2": 123,
            "key3": True,
            "key4": None,
            "nested": {"inner": "value"}
        }
        result = validate_metadata(metadata)
        assert result == metadata
    
    def test_metadata_too_large(self):
        """Test metadata exceeding size limit raises exception."""
        large_metadata = {"key": "x" * 20000}
        with pytest.raises(HTTPException) as exc_info:
            validate_metadata(large_metadata)
        assert exc_info.value.status_code == 400
        assert "too large" in str(exc_info.value.detail)
    
    def test_invalid_metadata_key(self):
        """Test invalid metadata key raises exception."""
        metadata = {"x" * 101: "value"}  # Key too long
        with pytest.raises(HTTPException) as exc_info:
            validate_metadata(metadata)
        assert exc_info.value.status_code == 400
    
    def test_invalid_metadata_value_type(self):
        """Test invalid metadata value type raises exception."""
        metadata = {"key": object()}  # Invalid type
        with pytest.raises(HTTPException) as exc_info:
            validate_metadata(metadata)
        assert exc_info.value.status_code == 400
    
    def test_none_metadata(self):
        """Test None metadata returns empty dict."""
        result = validate_metadata(None)
        assert result == {}


class TestRateLimiting:
    """Test rate limiting functionality."""
    
    @patch('app.security.business_profile_security.BusinessProfile')
    def test_user_profile_limit_exceeded(self, mock_profile):
        """Test user profile limit enforcement."""
        mock_db = Mock(spec=Session)
        mock_query = Mock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = BusinessProfileSecurityConfig.MAX_PROFILES_PER_USER
        
        with pytest.raises(HTTPException) as exc_info:
            check_user_profile_limit(mock_db, 1)
        assert exc_info.value.status_code == 429
        assert "Maximum number of business profiles" in str(exc_info.value.detail)
    
    @patch('app.security.business_profile_security.BusinessProfileDataSource')
    def test_profile_data_source_limit_exceeded(self, mock_data_source):
        """Test profile data source limit enforcement."""
        mock_db = Mock(spec=Session)
        mock_query = Mock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = BusinessProfileSecurityConfig.MAX_DATA_SOURCES_PER_PROFILE
        
        with pytest.raises(HTTPException) as exc_info:
            check_profile_data_source_limit(mock_db, "profile-id")
        assert exc_info.value.status_code == 429
        assert "Maximum number of data sources" in str(exc_info.value.detail)


class TestDataSanitization:
    """Test data sanitization for logging."""
    
    def test_sanitize_sensitive_data(self):
        """Test sensitive data is redacted in logs."""
        data = {
            "name": "Business Name",
            "password": "secret123",
            "email": "<EMAIL>",
            "token": "abc123"
        }
        result = sanitize_log_data(data)
        assert "[REDACTED]" in result
        assert "secret123" not in result
        assert "<EMAIL>" not in result
        assert "Business Name" in result
    
    def test_sanitize_long_string(self):
        """Test long strings are truncated."""
        long_string = "a" * 200
        result = sanitize_log_data(long_string)
        assert len(result) <= 100
        assert result.endswith("...")
    
    def test_sanitize_nested_data(self):
        """Test nested data structures are sanitized."""
        data = {
            "user": {
                "name": "John",
                "password": "secret"
            },
            "items": ["item1", "item2"]
        }
        result = sanitize_log_data(data)
        assert "[REDACTED]" in result
        assert "secret" not in result


class TestPydanticModelValidation:
    """Test Pydantic model validation."""
    
    def test_business_profile_create_validation(self):
        """Test BusinessProfileCreate validation."""
        # Valid data
        valid_data = {
            "name": "Test Business",
            "description": "A test business",
            "industry": "Technology"
        }
        profile = BusinessProfileCreate(**valid_data)
        assert profile.name == "Test Business"
        
        # Invalid data - empty name
        with pytest.raises(ValueError):
            BusinessProfileCreate(name="")
    
    def test_business_profile_switch_request_validation(self):
        """Test BusinessProfileSwitchRequest validation."""
        # Valid UUID
        valid_uuid = str(uuid.uuid4())
        request = BusinessProfileSwitchRequest(profile_id=valid_uuid)
        assert request.profile_id == valid_uuid
        
        # Invalid UUID
        with pytest.raises(ValueError):
            BusinessProfileSwitchRequest(profile_id="invalid-uuid")
    
    def test_data_source_assignment_validation(self):
        """Test BusinessProfileDataSourceAssignmentCreate validation."""
        # Valid data
        valid_uuid = str(uuid.uuid4())
        assignment = BusinessProfileDataSourceAssignmentCreate(
            data_source_id=valid_uuid,
            priority=5
        )
        assert assignment.data_source_id == valid_uuid
        assert assignment.priority == 5
        
        # Invalid priority
        with pytest.raises(ValueError):
            BusinessProfileDataSourceAssignmentCreate(
                data_source_id=valid_uuid,
                priority=101  # Exceeds max
            )
        
        # Invalid UUID
        with pytest.raises(ValueError):
            BusinessProfileDataSourceAssignmentCreate(
                data_source_id="invalid-uuid"
            )


class TestSecurityConfiguration:
    """Test security configuration constants."""
    
    def test_security_limits(self):
        """Test security configuration limits are reasonable."""
        config = BusinessProfileSecurityConfig
        
        # Check limits are positive and reasonable
        assert config.MAX_PROFILE_NAME_LENGTH > 0
        assert config.MAX_PROFILE_NAME_LENGTH <= 1000
        assert config.MAX_PROFILES_PER_USER > 0
        assert config.MAX_PROFILES_PER_USER <= 1000
        assert config.MAX_DATA_SOURCES_PER_PROFILE > 0
        assert config.MAX_METADATA_SIZE > 0
    
    def test_forbidden_characters_list(self):
        """Test forbidden characters list is comprehensive."""
        config = BusinessProfileSecurityConfig
        
        # Should include common injection characters
        dangerous_chars = ['<', '>', '{', '}', '`', '\\']
        for char in dangerous_chars:
            assert char in config.FORBIDDEN_CHARS
    
    def test_valid_enum_values(self):
        """Test valid enum values are defined."""
        config = BusinessProfileSecurityConfig
        
        assert len(config.VALID_BUSINESS_TYPES) > 0
        assert len(config.VALID_BUSINESS_SIZES) > 0
        assert len(config.VALID_BUSINESS_STAGES) > 0
        assert len(config.VALID_DATA_SOURCE_ROLES) > 0
        
        # Check for expected values
        assert 'B2B' in config.VALID_BUSINESS_TYPES
        assert 'startup' in config.VALID_BUSINESS_SIZES
        assert 'sales_data' in config.VALID_DATA_SOURCE_ROLES
