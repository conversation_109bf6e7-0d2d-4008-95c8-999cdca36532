import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Database, 
  FileText, 
  Globe, 
  Server,
  Search,
  RefreshCw,
  CheckCircle2,
  AlertCircle,
  Loader2,
  X
} from 'lucide-react';
import { SystemDataSource } from '@/types/dashboard-customization';
import { dataSourceApi } from '@/lib/dataSourceApi';
import { useToast } from '@/hooks/use-toast';

interface MultiSelectDataSourceSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDataSourcesSelected: (dataSourceIds: string[]) => void;
  existingAssignments?: string[]; // IDs of already assigned data sources
  selectedDataSources?: string[]; // Currently selected data source IDs
  maxSelections?: number; // Maximum number of selections allowed
}

const DATA_SOURCE_ICONS = {
  file: FileText,
  database: Database,
  api: Globe,
  mcp: Server,
};

const DATA_SOURCE_TYPE_LABELS = {
  file: 'File Upload',
  database: 'Database',
  api: 'API',
  mcp: 'MCP Server',
};

// Custom hook for debounced search
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const MultiSelectDataSourceSelector: React.FC<MultiSelectDataSourceSelectorProps> = ({
  open,
  onOpenChange,
  onDataSourcesSelected,
  existingAssignments = [],
  selectedDataSources = [],
  maxSelections,
}) => {
  const { toast } = useToast();

  // State for existing data sources
  const [systemDataSources, setSystemDataSources] = useState<SystemDataSource[]>([]);
  const [isLoadingDataSources, setIsLoadingDataSources] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  // State for selection
  const [localSelectedDataSources, setLocalSelectedDataSources] = useState<string[]>(selectedDataSources);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastLoadTimeRef = useRef<number>(0);
  const isMountedRef = useRef<boolean>(true);

  // Debounced search term to prevent excessive filtering
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Load system data sources with debouncing
  const loadSystemDataSources = useCallback(async () => {
    // Prevent rapid successive calls
    const now = Date.now();
    if (now - lastLoadTimeRef.current < 1000) { // 1 second debounce
      return;
    }
    lastLoadTimeRef.current = now;

    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    setIsLoadingDataSources(true);
    try {
      const response = await dataSourceApi.getDataSources();
      // Only update state if component is still mounted
      if (isMountedRef.current) {
        setSystemDataSources(response.data_sources || []);
      }
    } catch (error) {
      console.error('Error loading data sources:', error);
      // Only show toast if component is still mounted
      if (isMountedRef.current) {
        toast({
          title: "Error",
          description: "Failed to load data sources. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoadingDataSources(false);
      }
    }
  }, [toast]);

  // Memoized filtered data sources to prevent unnecessary re-renders
  const filteredDataSources = useMemo(() => {
    let filtered = systemDataSources.filter(ds => ds.is_active);

    // Apply search filter using debounced search term
    if (debouncedSearchTerm) {
      filtered = filtered.filter(ds =>
        ds.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
        ds.description?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      );
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(ds => ds.type === typeFilter);
    }

    return filtered;
  }, [systemDataSources, debouncedSearchTerm, typeFilter]);

  // Load data sources when dialog opens (optimized to prevent cascading effects)
  useEffect(() => {
    if (open) {
      // Reset filters and selection state
      setSearchTerm('');
      setTypeFilter('all');
      setLocalSelectedDataSources(selectedDataSources || []);

      // Load data sources only if we don't have them or they're stale
      if (systemDataSources.length === 0 || Date.now() - lastLoadTimeRef.current > 30000) {
        loadSystemDataSources();
      }
    }
  }, [open]); // Removed loadSystemDataSources from dependencies to prevent loops

  // Update local selected data sources when prop changes (only when dialog is closed)
  useEffect(() => {
    if (!open && selectedDataSources) {
      setLocalSelectedDataSources(selectedDataSources);
    }
  }, [selectedDataSources, open]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  // Handle data source selection toggle
  const handleToggleDataSource = (dataSourceId: string) => {
    setLocalSelectedDataSources(prev => {
      const isCurrentlySelected = prev.includes(dataSourceId);
      
      if (isCurrentlySelected) {
        // Remove from selection
        return prev.filter(id => id !== dataSourceId);
      } else {
        // Add to selection if under max limit
        if (maxSelections && prev.length >= maxSelections) {
          toast({
            title: "Selection Limit Reached",
            description: `You can only select up to ${maxSelections} data sources.`,
            variant: "destructive",
          });
          return prev;
        }
        return [...prev, dataSourceId];
      }
    });
  };

  // Handle select all/none
  const handleSelectAll = () => {
    const availableDataSources = filteredDataSources
      .filter(ds => !existingAssignments.includes(ds.id))
      .map(ds => ds.id);
    
    if (maxSelections) {
      const limitedSelection = availableDataSources.slice(0, maxSelections);
      setLocalSelectedDataSources(limitedSelection);
      if (availableDataSources.length > maxSelections) {
        toast({
          title: "Selection Limited",
          description: `Only the first ${maxSelections} data sources were selected due to the limit.`,
        });
      }
    } else {
      setLocalSelectedDataSources(availableDataSources);
    }
  };

  const handleSelectNone = () => {
    setLocalSelectedDataSources([]);
  };

  // Handle confirm selection
  const handleConfirmSelection = () => {
    onDataSourcesSelected(localSelectedDataSources);
    onOpenChange(false);
  };

  // Remove selected data source
  const handleRemoveSelected = (dataSourceId: string) => {
    setLocalSelectedDataSources(prev => prev.filter(id => id !== dataSourceId));
  };

  const formatDataSourceType = (type: string) => {
    return DATA_SOURCE_TYPE_LABELS[type as keyof typeof DATA_SOURCE_TYPE_LABELS] || type;
  };

  const getDataSourceIcon = (type: string) => {
    return DATA_SOURCE_ICONS[type as keyof typeof DATA_SOURCE_ICONS] || Database;
  };

  // Memoized computed values to prevent unnecessary recalculations
  const availableDataSources = useMemo(() =>
    filteredDataSources.filter(ds => !existingAssignments.includes(ds.id)),
    [filteredDataSources, existingAssignments]
  );

  const selectedDataSourcesInfo = useMemo(() =>
    systemDataSources.filter(ds => localSelectedDataSources.includes(ds.id)),
    [systemDataSources, localSelectedDataSources]
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Select Multiple Data Sources</DialogTitle>
          <DialogDescription>
            Choose multiple data sources to assign to your dashboard or widget.
            {maxSelections && ` You can select up to ${maxSelections} data sources.`}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and Filter */}
          <div className="flex items-center space-x-2">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search data sources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="file">Files</SelectItem>
                <SelectItem value="database">Databases</SelectItem>
                <SelectItem value="api">APIs</SelectItem>
                <SelectItem value="mcp">MCP Servers</SelectItem>
              </SelectContent>
            </Select>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={loadSystemDataSources}
              disabled={isLoadingDataSources}
            >
              <RefreshCw className={`h-4 w-4 ${isLoadingDataSources ? 'animate-spin' : ''}`} />
            </Button>
          </div>

          {/* Bulk Selection Controls */}
          {availableDataSources.length > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  disabled={availableDataSources.length === 0}
                >
                  Select All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSelectNone}
                  disabled={localSelectedDataSources.length === 0}
                >
                  Select None
                </Button>
              </div>
              <div className="text-sm text-muted-foreground">
                {localSelectedDataSources.length} of {availableDataSources.length} selected
                {maxSelections && ` (max ${maxSelections})`}
              </div>
            </div>
          )}

          {/* Selected Data Sources Preview */}
          {localSelectedDataSources.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Selected Data Sources</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {selectedDataSourcesInfo.map((dataSource) => {
                    const Icon = getDataSourceIcon(dataSource.type);
                    return (
                      <Badge
                        key={dataSource.id}
                        variant="secondary"
                        className="flex items-center space-x-1 pr-1"
                      >
                        <Icon className="h-3 w-3" />
                        <span>{dataSource.name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                          onClick={() => handleRemoveSelected(dataSource.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Data Sources List */}
          <div className="space-y-2 max-h-[400px] overflow-y-auto">
            {isLoadingDataSources ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading data sources...</span>
              </div>
            ) : availableDataSources.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                  <h4 className="text-lg font-semibold mb-2">No Data Sources Available</h4>
                  <p className="text-sm text-muted-foreground text-center">
                    {systemDataSources.length === 0
                      ? "No data sources have been configured yet. Configure data sources on the Data Integration page."
                      : "All available data sources are already assigned."
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              availableDataSources.map((dataSource) => {
                const Icon = getDataSourceIcon(dataSource.type);
                const isSelected = localSelectedDataSources.includes(dataSource.id);
                const isAlreadyAssigned = existingAssignments.includes(dataSource.id);

                return (
                  <Card
                    key={dataSource.id}
                    className={`cursor-pointer transition-colors ${
                      isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
                    } ${isAlreadyAssigned ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => !isAlreadyAssigned && handleToggleDataSource(dataSource.id)}
                  >
                    <CardContent className="flex items-center justify-between p-4">
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={isSelected}
                          disabled={isAlreadyAssigned}
                          onChange={() => !isAlreadyAssigned && handleToggleDataSource(dataSource.id)}
                          className="mr-2"
                        />
                        <Icon className="h-8 w-8 text-muted-foreground" />
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="font-semibold">{dataSource.name}</h4>
                            <Badge variant="outline">
                              {formatDataSourceType(dataSource.type)}
                            </Badge>
                            {isAlreadyAssigned && (
                              <Badge variant="secondary">Already Assigned</Badge>
                            )}
                          </div>
                          {dataSource.description && (
                            <p className="text-sm text-muted-foreground">{dataSource.description}</p>
                          )}
                          <div className="text-xs text-muted-foreground mt-1">
                            Created {new Date(dataSource.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      {isSelected && (
                        <CheckCircle2 className="h-5 w-5 text-primary" />
                      )}
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleConfirmSelection}
            disabled={localSelectedDataSources.length === 0}
          >
            Add {localSelectedDataSources.length} Data Source{localSelectedDataSources.length !== 1 ? 's' : ''}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
