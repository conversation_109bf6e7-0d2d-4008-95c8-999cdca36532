#!/usr/bin/env python3
"""
Test script to verify what embedder mem0ai is actually using.

This script tests the mem0ai configuration and checks what embedder
is being used and what dimensions it produces.
"""

import sys
import os
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from mem0 import Memory
from agents.utils.qdrant_manager import QdrantManager

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_mem0_embedder():
    """Test what embedder mem0ai is actually using."""
    print("🔧 Testing mem0ai Embedder Configuration")
    print("=" * 50)
    
    # Check if Qdrant is running
    if not QdrantManager.is_qdrant_running():
        print("❌ Qdrant is not running. Please start Qdrant first.")
        return 1
    
    print("✓ Qdrant is running")
    
    # Get Qdrant connection parameters
    qdrant_params = QdrantManager.get_qdrant_connection_params()
    print(f"✓ Using Qdrant at {qdrant_params['host']}:{qdrant_params['port']}")
    
    # Test different embedder configurations
    test_configs = [
        {
            "name": "Hugging Face BAAI/bge-small-en-v1.5",
            "config": {
                "vector_store": {
                    "provider": "qdrant",
                    "config": qdrant_params
                },
                "llm": {
                    "provider": "groq",
                    "config": {
                        "model": "llama-3.1-8b-instant",
                        "api_key": os.getenv("GROQ_API_KEY", "dummy")
                    }
                },
                "embedder": {
                    "provider": "huggingface",
                    "config": {
                        "model": "BAAI/bge-small-en-v1.5"
                    }
                }
            }
        },
        {
            "name": "OpenAI text-embedding-ada-002",
            "config": {
                "vector_store": {
                    "provider": "qdrant",
                    "config": qdrant_params
                },
                "llm": {
                    "provider": "groq",
                    "config": {
                        "model": "llama-3.1-8b-instant",
                        "api_key": os.getenv("GROQ_API_KEY", "dummy")
                    }
                },
                "embedder": {
                    "provider": "openai",
                    "config": {
                        "model": "text-embedding-ada-002",
                        "api_key": os.getenv("OPENAI_API_KEY", "dummy")
                    }
                }
            }
        }
    ]
    
    for test_config in test_configs:
        print(f"\n🧪 Testing: {test_config['name']}")
        print("-" * 40)
        
        try:
            # Initialize mem0ai with the test configuration
            memory = Memory.from_config(test_config["config"])
            print(f"✓ Successfully initialized mem0ai")
            
            # Try to add a test memory to see what dimensions are used
            test_text = "This is a test embedding to check dimensions."
            
            try:
                result = memory.add(
                    test_text,
                    user_id="test_user",
                    metadata={"test": True}
                )
                
                if result:
                    print(f"✓ Successfully created embedding")
                    print(f"  Result: {result}")
                    
                    # Try to get collection info to see dimensions
                    import requests
                    collection_url = f"http://{qdrant_params['host']}:{qdrant_params['port']}/collections/mem0"
                    response = requests.get(collection_url)
                    
                    if response.status_code == 200:
                        collection_info = response.json()
                        if "result" in collection_info and "config" in collection_info["result"]:
                            vector_size = collection_info["result"]["config"]["params"]["vectors"]["size"]
                            print(f"✓ Collection created with vector size: {vector_size}")
                            
                            # Expected dimensions for different models
                            expected_dims = {
                                "BAAI/bge-small-en-v1.5": 384,
                                "text-embedding-ada-002": 1536
                            }
                            
                            model_name = test_config["config"]["embedder"]["config"]["model"]
                            expected = expected_dims.get(model_name, "unknown")
                            
                            if expected != "unknown" and vector_size == expected:
                                print(f"✅ Correct dimensions! Expected {expected}, got {vector_size}")
                            elif expected != "unknown":
                                print(f"❌ Wrong dimensions! Expected {expected}, got {vector_size}")
                            else:
                                print(f"ℹ️  Unknown expected dimensions for {model_name}")
                        else:
                            print("⚠️  Could not get collection vector size")
                    else:
                        print(f"⚠️  Could not get collection info: {response.status_code}")
                else:
                    print("❌ Failed to create embedding (no result)")
                    
            except Exception as e:
                print(f"❌ Error creating embedding: {e}")
                if "Vector dimension error" in str(e):
                    print("   This indicates a dimension mismatch!")
                
        except Exception as e:
            print(f"❌ Failed to initialize mem0ai: {e}")
        
        # Clean up - delete the test collection
        try:
            import requests
            delete_url = f"http://{qdrant_params['host']}:{qdrant_params['port']}/collections/mem0"
            requests.delete(delete_url)
            print("🗑️  Cleaned up test collection")
        except Exception as e:
            print(f"⚠️  Could not clean up test collection: {e}")
    
    print("\n🎯 Test Summary")
    print("=" * 50)
    print("If you see dimension mismatches, it means mem0ai is not using")
    print("the embedder you specified in the configuration.")
    print("\nPossible causes:")
    print("1. Missing dependencies for the embedder")
    print("2. Invalid API keys")
    print("3. mem0ai falling back to default embedder")
    print("4. Configuration not being applied correctly")
    
    return 0


if __name__ == "__main__":
    sys.exit(test_mem0_embedder())
