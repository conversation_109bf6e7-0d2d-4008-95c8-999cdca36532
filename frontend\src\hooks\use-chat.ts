/**
 * Custom hook for chat functionality.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { chatApi, ChatWebSocket, Message, Conversation } from '@/lib/api';
import { useToast } from './use-toast';
import {
  generateTempMessageId,
  generateStreamMessageId,
  generateMessageId
} from '@/utils/idUtils';
// Use console for logging

interface UseChatOptions {
  conversationId?: string;
  personaId?: string;
  initialMessages?: Message[];
}

interface UseChatResult {
  messages: Message[];
  isLoading: boolean;
  isTyping: boolean;
  error: string | null;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  messageDeliveryStatus: Record<string, 'pending' | 'delivered' | 'failed'>;
  sendMessage: (content: string, context?: any, attachments?: any[], currentPersona?: any) => Promise<void>;
  createConversation: (personaId: string, title?: string) => Promise<string>;
  loadConversation: (conversationId: string) => Promise<void>;
  refreshConversation: () => Promise<void>;
  clearMessages: () => void;
  resetConversation: () => void; // ✅ NEW: Add resetConversation to interface
  isAutoRefreshing: boolean;
}

export function useChat({ conversationId: initialConversationId, personaId: initialPersonaId, initialMessages = [] }: UseChatOptions = {}): UseChatResult {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [conversationId, setConversationId] = useState<string | undefined>(initialConversationId);
  // Use a ref for personaId to avoid unnecessary effect triggers if only personaId changes without conversationId
  const personaIdRef = useRef<string | undefined>(initialPersonaId);
  useEffect(() => { personaIdRef.current = initialPersonaId }, [initialPersonaId]);

  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [messageDeliveryStatus, setMessageDeliveryStatus] = useState<Record<string, 'pending' | 'delivered' | 'failed'>>({});
  const [isAutoRefreshing, setIsAutoRefreshing] = useState<boolean>(false);
  const autoRefreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const webSocketRef = useRef<ChatWebSocket | null>(null);
  const { toast } = useToast();

  // Helper to update messages state and ensure sorting
  const updateMessagesState = useCallback((updater: (prev: Message[]) => Message[]) => {
    setMessages(prev => {
      const newState = updater(prev);
      newState.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      console.debug(`useChat: Messages state updated. Count: ${newState.length}. Last message ID: ${newState[newState.length - 1]?.id}`);
      return newState;
    });
  }, []); // Empty dependency array as it uses setMessages directly

  // Load conversation messages function (defined before createConversation)
  const loadConversation = useCallback(async (id: string) => {
    if (!id) {
        console.debug("useChat: loadConversation called with empty ID.");
        // ✅ FIX: Only clear if we're explicitly resetting, not on accidental empty calls
        if (id === "") {
          setMessages([]); // Clear messages only for explicit reset
          setConversationId(undefined); // Clear conversation ID only for explicit reset
        }
        return;
    }

    console.debug(`useChat: Loading conversation ${id}`);
    setIsLoading(true);
    setError(null);

    try {
      const conversation = await chatApi.getConversation(id);
      console.debug(`useChat: Loaded ${conversation.messages.length} messages for conversation ${id}`);

      // Enhanced logging for debugging message loading
      conversation.messages.forEach((msg, index) => {
        console.debug(`useChat: Message ${index + 1}: ID=${msg.id}, sender=${msg.sender}, content_length=${msg.content?.length || 0}`);
      });

      // Verify all messages have required fields
      const validMessages = conversation.messages.filter(msg => {
        const isValid = msg.id && msg.sender && msg.content !== undefined && msg.created_at;
        if (!isValid) {
          console.warn(`useChat: Invalid message found:`, msg);
        }
        return isValid;
      });

      if (validMessages.length !== conversation.messages.length) {
        console.warn(`useChat: Filtered out ${conversation.messages.length - validMessages.length} invalid messages`);
      }

      updateMessagesState(() => validMessages); // Use helper to set and sort
      setConversationId(id); // Ensure conversationId state is updated

      console.debug(`useChat: Successfully loaded ${validMessages.length} valid messages for conversation ${id}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load conversation';
      console.error(`useChat: Error loading conversation ${id}: ${errorMessage}`);
      setError(errorMessage);
      toast({ title: 'Error', description: errorMessage, variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  }, [toast, updateMessagesState]); // Added updateMessagesState dependency

  // Initialize WebSocket connection
  useEffect(() => {
    if (!conversationId) {
        console.debug("useChat: No conversationId, WebSocket not connecting.");
        // Ensure disconnection if conversationId becomes null/undefined
        if (webSocketRef.current) {
            console.debug("useChat: Disconnecting WebSocket due to missing conversationId.");
            webSocketRef.current.disconnect();
            webSocketRef.current = null; // Clear the ref
        }
        return;
    }
    console.debug(`useChat: useEffect triggered for WebSocket setup, conversationId: ${conversationId}`);

    // Create WebSocket instance if it doesn't exist for this conversation
    if (!webSocketRef.current) {
        console.debug(`useChat: Creating new WebSocket instance for ${conversationId}`);
        webSocketRef.current = new ChatWebSocket();
    } else {
        console.debug(`useChat: WebSocket instance exists for ${conversationId}`);
        // If the instance exists but is for a different conversation, disconnect and recreate (handled by connect logic)
    }

    const wsInstance = webSocketRef.current; // Capture instance for cleanup closure

    // Set up event handlers
    const removeMessageHandler = wsInstance.onMessage((data) => {
      console.log('🔌 useChat: WebSocket message received:', data.type, data);

      if (data.type === 'user_message') {
        // Correctly destructure temp_id from data.message
        const { id: finalId, temp_id, ...restOfMessage } = data.message;
        console.debug(`useChat: Received user_message confirmation for temp_id: ${temp_id}, final ID: ${finalId}`);

        updateMessagesState((prev) => {
          console.debug(`useChat: updateMessagesState for user_message. Received temp_id: ${temp_id}, finalId: ${finalId}`);
          console.debug(`useChat: Previous state IDs: ${prev.map(m => m.id).join(', ')}`);
          let messageReplaced = false;
          // Find the optimistic message by temp_id and replace it
          const updatedMessages = prev.map(msg => {
            console.debug(`useChat: Comparing msg.id (${msg.id}) with temp_id (${temp_id})`);
            if (msg.id === temp_id) {
              console.debug(`useChat: Match found! Replacing message ${temp_id} with ${finalId}`);
              messageReplaced = true;
              // Construct the final message object without the temp_id property
              return { id: finalId, ...restOfMessage };
            }
            return msg;
          });

          // If the optimistic message wasn't found and the final message isn't already present, add it
          if (!messageReplaced && !updatedMessages.some(m => m.id === finalId)) {
            console.warn(`useChat: Optimistic message with temp_id ${temp_id} not found. Adding confirmed message ${finalId}.`);
            // Construct the final message object to add
            updatedMessages.push({ id: finalId, ...restOfMessage });
          }
          return updatedMessages;
        });

        // Update delivery status for the *final* ID and remove the temporary one
        setMessageDeliveryStatus(prevStatus => {
          const newStatus = { ...prevStatus };
          if (temp_id) delete newStatus[temp_id]; // Remove temp status
          newStatus[finalId] = 'delivered'; // Set final status using finalId
          return newStatus;
        });
      } else if (data.type === 'ai_message') {
        // Handle regular AI messages (non-streamed or initial greetings)
        console.debug(`useChat: Received ai_message with ID ${data.message.id}`);
        updateMessagesState((prev) => {
          if (prev.some(m => m.id === data.message.id)) return prev; // Avoid duplicates
          return [...prev, data.message];
        });
      } else if (data.type === 'ai_message_start') {
        console.debug(`useChat: Received ai_message_start with ID ${data.message.id}`);
        setIsTyping(true);
        updateMessagesState((prev) => {
          if (prev.some(m => m.id === data.message.id)) return prev;
          return [...prev, data.message];
        });
      } else if (data.type === 'ai_message_complete') {
        console.debug(`useChat: Received ai_message_complete with ID ${data.message.id}`);
        setIsTyping(false);
        updateMessagesState((prev) => {
          const updated = prev.map(msg => msg.id === data.message.id ? data.message : msg);
          if (!updated.some(m => m.id === data.message.id)) updated.push(data.message);
          return updated;
        });
      } else if (data.type === 'ai_message_error') {
        console.debug(`useChat: Received ai_message_error with ID ${data.message.id}`);
        setIsTyping(false);
        toast({ title: 'Error', description: 'An error occurred processing your message.', variant: 'destructive' });
        updateMessagesState((prev) => {
          const updated = prev.map(msg => msg.id === data.message.id ? data.message : msg);
          if (!updated.some(m => m.id === data.message.id)) updated.push(data.message);
          return updated;
        });
      } else if (data.type === 'typing_indicator') {
        setIsTyping(data.is_typing);
      } else if (data.type === 'message_delivered') {
        setMessageDeliveryStatus(prev => ({ ...prev, [data.message_id]: 'delivered' }));
      } else if (data.type === 'message_failed') {
        setMessageDeliveryStatus(prev => ({ ...prev, [data.message_id]: 'failed' }));
        toast({ title: 'Message Failed', description: data.error || 'Failed to deliver message', variant: 'destructive' });
      } else if (data.type === 'stream_start') {
         console.log(`🚀 useChat: Received stream_start for message ${data.message_id}`);
         const messageId = data.message_id || generateStreamMessageId();

         // Update existing message to mark it as streaming, don't create a new one
         // The message should already exist from ai_message_start event
         updateMessagesState(prev => {
           const existingMessage = prev.find(m => m.id === messageId);
           if (existingMessage) {
             // Reduced logging verbosity
             return prev.map(msg =>
               msg.id === messageId
                 ? {
                     ...msg,
                     metadata: {
                       ...msg.metadata,
                       ...data.metadata || {},
                       streaming: true,
                       status: 'streaming'
                     }
                   }
                 : msg
             );
           } else {
             // Fallback: create message if it doesn't exist (shouldn't happen normally)
             console.warn(`⚠️ useChat: Message ${messageId} not found for stream_start, creating fallback message`);
             const emptyMessage: Message = {
               id: messageId,
               conversation_id: conversationId || '',
               sender: 'ai',
               content: '',
               metadata: {
                 ...data.metadata || {},
                 streaming: true,
                 status: 'streaming'
               },
               created_at: new Date().toISOString()
             };
             return [...prev, emptyMessage];
           }
         });
         setIsTyping(true);
      } else if (data.type === 'stream_chunk') {
         // Removed verbose chunk logging to reduce console noise
         updateMessagesState(prev => prev.map(msg =>
           msg.id === data.message_id
             ? {
                 ...msg,
                 content: msg.content + data.content,
                 metadata: {
                   ...msg.metadata,
                   streaming: true,
                   status: 'streaming'
                 }
               }
             : msg
         ));
      } else if (data.type === 'stream_end') {
         console.log(`🏁 useChat: Received stream_end for ID ${data.message_id}`);
         setIsTyping(false);
         updateMessagesState(prev => prev.map(msg =>
           msg.id === data.message_id
             ? {
                 ...msg,
                 metadata: {
                   ...msg.metadata,
                   ...data.metadata,
                   streaming: false,
                   status: 'completed'
                 }
               }
             : msg
         ));
      } else if (data.type === 'connection_status' && data.status === 'connected') {
          console.debug(`useChat: WebSocket received connection_status=connected for ${conversationId}.`);
          // Optionally trigger loadConversation here if needed as a fallback,
          // but createConversation already attempts a delayed load.
          // loadConversation(conversationId);
      }
    });

    const removeStatusHandler = wsInstance.onStatus((status) => {
      console.debug(`useChat: WebSocket status changed to: ${status}`);
      setConnectionStatus(status);
      if (status === 'error') {
        toast({ title: 'Connection Error', description: 'Failed to connect to chat server.', variant: 'destructive' });
      }
    });

    // Connect WebSocket
    // The ChatWebSocket class should ideally handle not reconnecting if already connected/connecting.
    console.debug(`useChat: Calling connect for ${conversationId}`);
    wsInstance.connect(conversationId);

    // Clean up
    return () => {
      console.debug(`useChat: Cleaning up WebSocket useEffect for conversationId: ${conversationId}`);
      removeMessageHandler();
      removeStatusHandler();
      if (wsInstance) {
        console.debug("useChat: Disconnecting WebSocket in cleanup.");
        wsInstance.disconnect();
      }
      // Clear the ref only if this cleanup corresponds to the current conversationId
      // to prevent issues if the ID changes rapidly.
      if (webSocketRef.current === wsInstance) {
          webSocketRef.current = null;
      }
    };
  }, [conversationId, toast, updateMessagesState]); // Rerun when conversationId changes

  // Create a new conversation
  const createConversation = useCallback(async (personaId: string, title?: string): Promise<string> => {
    console.debug(`useChat: Creating new conversation with persona ${personaId}`);
    console.debug(`useChat: Starting createConversation for persona ${personaId}`);
    setIsLoading(true);
    setError(null);
    setMessages([]); // Clear messages for the new conversation

    // Disconnect existing WebSocket *before* creating the new one
    if (webSocketRef.current) {
        console.debug("useChat: Disconnecting existing WebSocket before creating new conversation.");
        webSocketRef.current.disconnect();
        webSocketRef.current = null; // Ensure a new instance is created by the useEffect
    }
    // Set conversationId to undefined *after* explicit disconnect to trigger cleanup if needed,
    // but primarily rely on the explicit disconnect above.
    setConversationId(undefined);


    try {
      // Create the conversation via REST API
      const conversation = await chatApi.createConversation(personaId, title);
      console.debug(`useChat: Backend created conversation ${conversation.id}`);

      // Set the new conversation ID *after* successful creation.
      // This will trigger the useEffect to establish the new WebSocket connection.
      setConversationId(conversation.id);

      // Attempt to load the conversation shortly after creation via REST.
      // This acts as a fallback in case the WebSocket message is missed
      // and ensures the initial greeting is fetched if the broadcast fails or is too fast.
      setTimeout(() => {
          console.debug(`useChat: Attempting delayed loadConversation for ${conversation.id} as fallback.`);
          // Use a function scope check for conversationId to ensure we load the correct one
          const currentConvId = conversation.id;
          setConversationId(currentId => {
              if (currentId === currentConvId) {
                  loadConversation(currentConvId);
              } else {
                  console.debug(`useChat: Conversation changed before delayed load executed for ${currentConvId}. Skipping load.`);
              }
              return currentId; // Return current state
          });
      }, 750); // Delay slightly

      console.debug(`useChat: New conversation ${conversation.id} created. WebSocket connecting...`);
      return conversation.id;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create conversation';
      console.error(`useChat: Error creating conversation: ${errorMessage}`);
      setError(errorMessage);
      toast({ title: 'Error', description: errorMessage, variant: 'destructive' });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [toast, loadConversation]); // Removed conversationId from deps, added loadConversation

  // Send a message
  const sendMessage = useCallback(async (content: string, context: any = {}, attachments: any[] = [], currentPersona?: any): Promise<void> => {
    const currentConversationId = conversationId; // Capture current ID
    if (!currentConversationId) {
        console.warn("useChat: sendMessage called without conversationId.");
        return;
    }

    // Don't allow empty messages, even with data sources
    if (!content.trim()) {
        console.warn("useChat: sendMessage called with empty content.");
        return;
    }

    console.debug(`useChat: Sending message: "${content.substring(0, 30)}..." to ${currentConversationId}`);
    console.debug(`useChat: Context data:`, context);
    console.debug(`useChat: Attachments:`, attachments);
    console.debug(`useChat: Current persona:`, currentPersona);
    const messageId = generateTempMessageId();

    // Enhance context with current persona information for proper routing
    const enhancedContext = {
      ...context,
      // Add current_persona to context if provided - this is crucial for routing
      ...(currentPersona && { current_persona: currentPersona.id })
    };

    console.debug(`useChat: Enhanced context with persona:`, enhancedContext);

    // Separate metadata from context - metadata is for message-level data, context is for agent processing
    const messageMetadata = {
      temp_id: messageId,
      // Include file attachments in message metadata
      attachments: attachments.length > 0 ? attachments : undefined,
      // Include any message-specific metadata here
    };

    const tempUserMessage: Message = {
      id: messageId,
      conversation_id: currentConversationId,
      sender: 'user',
      content,
      metadata: messageMetadata,
      created_at: new Date().toISOString()
    };

    setMessageDeliveryStatus(prev => ({ ...prev, [messageId]: 'pending' }));
    updateMessagesState(prev => [...prev, tempUserMessage]); // Use helper

    if (webSocketRef.current && connectionStatus === 'connected') {
      console.debug(`useChat: WebSocket connected, sending message via WebSocket with temp ID ${messageId}`);
      console.debug(`useChat: Sending enhanced context:`, enhancedContext);
      // Properly separate metadata and context for WebSocket
      webSocketRef.current.sendChatMessage(content, { temp_id: messageId }, enhancedContext);
    } else {
      console.warn(`useChat: WebSocket not connected (status: ${connectionStatus}), falling back to REST API`);
      setIsLoading(true);
      try {
        const response = await chatApi.sendMessage({ conversation_id: currentConversationId, message: content, context: enhancedContext });
        console.debug(`useChat: Received REST API response`);
        updateMessagesState((prev) => {
          const filtered = prev.filter((msg) => msg.id !== tempUserMessage.id);
          return [...filtered, response.user_message, response.ai_message];
        });
         setMessageDeliveryStatus(prev => {
             const newStatus = { ...prev };
             delete newStatus[tempUserMessage.id];
             newStatus[response.user_message.id] = 'delivered';
             return newStatus;
         });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
        console.error(`useChat: Error sending message via REST API: ${errorMessage}`);
        setError(errorMessage);
        toast({ title: 'Error', description: errorMessage, variant: 'destructive' });
        setMessageDeliveryStatus(prev => ({ ...prev, [tempUserMessage.id]: 'failed' }));
      } finally {
        setIsLoading(false);
      }
    }
  }, [conversationId, connectionStatus, createConversation, toast, updateMessagesState]); // Added updateMessagesState

  // Clear messages
  const clearMessages = useCallback(() => {
    console.debug("useChat: Clearing messages.");
    setMessages([]);
  }, []);

  // ✅ NEW: Safe conversation reset function
  const resetConversation = useCallback(() => {
    console.debug("useChat: Safely resetting conversation.");
    setMessages([]);
    setConversationId(undefined);
    setError(null);
    setMessageDeliveryStatus({});
    setIsTyping(false);
    // Close WebSocket connection if exists
    if (webSocketRef.current) {
      webSocketRef.current.disconnect();
      webSocketRef.current = null;
    }
  }, []);

  // Load initial conversation if ID is provided
  useEffect(() => {
    if (initialConversationId) {
      console.debug(`useChat: Initial conversation ID provided: ${initialConversationId}. Loading.`);
      loadConversation(initialConversationId);
    }
    // Intentionally not including loadConversation in deps to only run on mount with initial ID
  }, [initialConversationId]);

  // Refresh conversation - useful when WebSocket connection is lost or for polling
  const refreshConversation = useCallback(async () => {
    if (!conversationId) {
        console.debug("useChat: refreshConversation called but no conversationId.");
        return;
    }
    console.debug(`useChat: Refreshing conversation ${conversationId}. Is auto-refresh: ${isAutoRefreshing}`);
    if (!isAutoRefreshing) setIsLoading(true);
    setError(null);
    try {
      const conversation = await chatApi.getConversation(conversationId);
      console.debug(`useChat: Fetched ${conversation.messages.length} messages during refresh.`);

      // Enhanced logging for refresh debugging
      conversation.messages.forEach((msg, index) => {
        console.debug(`useChat: Refresh Message ${index + 1}: ID=${msg.id}, sender=${msg.sender}, content_length=${msg.content?.length || 0}`);
      });

      // Verify all messages have required fields during refresh
      const validMessages = conversation.messages.filter(msg => {
        const isValid = msg.id && msg.sender && msg.content !== undefined && msg.created_at;
        if (!isValid) {
          console.warn(`useChat: Invalid message found during refresh:`, msg);
        }
        return isValid;
      });

      if (validMessages.length !== conversation.messages.length) {
        console.warn(`useChat: Filtered out ${conversation.messages.length - validMessages.length} invalid messages during refresh`);
      }

      updateMessagesState(() => validMessages); // Use helper
      console.debug(`useChat: Successfully refreshed ${validMessages.length} valid messages for conversation ${conversationId}`);

      if (!isAutoRefreshing) toast({ title: 'Conversation Refreshed', description: 'The conversation has been refreshed.', variant: 'default' });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh conversation';
      console.error(`useChat: Error refreshing conversation ${conversationId}: ${errorMessage}`);
      setError(errorMessage);
      if (!isAutoRefreshing) toast({ title: 'Error', description: errorMessage, variant: 'destructive' });
    } finally {
      if (!isAutoRefreshing) setIsLoading(false);
      setIsAutoRefreshing(false);
    }
  }, [conversationId, toast, isAutoRefreshing, updateMessagesState]); // Added updateMessagesState

  // Auto-refresh logic (simplified)
  useEffect(() => {
    if (autoRefreshTimeoutRef.current) clearTimeout(autoRefreshTimeoutRef.current);
    autoRefreshTimeoutRef.current = null;

    if (conversationId && !isLoading && !isTyping && !isAutoRefreshing && messages.length > 0 && messages[messages.length - 1].sender === 'user') {
      console.debug('useChat: Last message is from user, scheduling auto-refresh.');
      autoRefreshTimeoutRef.current = setTimeout(async () => {
        console.debug('useChat: Auto-refreshing conversation (timer expired).');
        setIsAutoRefreshing(true);
        await refreshConversation();
      }, 5000);
    }

    return () => { if (autoRefreshTimeoutRef.current) clearTimeout(autoRefreshTimeoutRef.current); };
  }, [conversationId, messages, isTyping, isLoading, isAutoRefreshing, refreshConversation]);

  return {
    messages,
    isLoading,
    isTyping,
    error,
    connectionStatus,
    messageDeliveryStatus,
    sendMessage,
    createConversation,
    loadConversation,
    clearMessages,
    resetConversation, // ✅ NEW: Export safe reset function
    refreshConversation,
    isAutoRefreshing,
  };
}
