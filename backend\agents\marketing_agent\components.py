"""
Marketing components for the Datagenius agent system.

This module provides components for marketing content generation tasks using MCP tools.
"""

import logging
import time
import yaml
from typing import Dict, Any, Optional, Tuple, List
from pydantic import BaseModel, Field

from agents.components.base import AgentComponent
from agents.mixins import ToolCompletionMixin
# Removed unused AI intent detection imports - now using LLM-based detection

# Configure logging
logger = logging.getLogger(__name__)


class MarketingTask(BaseModel):
    """Model for marketing task request"""
    task_type: str = Field(..., description="Type of marketing task to perform")
    brand_description: str = Field("", description="Description of the brand")
    target_audience: str = Field("", description="Description of the target audience")
    products_services: str = Field("", description="Description of products or services")
    marketing_goals: str = Field("", description="Marketing goals")
    existing_content: str = Field("", description="Existing marketing content")
    keywords: str = Field("", description="Keywords to target")
    suggested_topics: str = Field("", description="Suggested topics")
    blog_topic: str = Field("", description="Selected blog topics for blog content generation")
    tone: str = Field("Professional", description="Tone of the content")
    file_id: Optional[str] = Field(None, description="ID of the file to process")
    is_first_conversation: bool = Field(False, description="Whether this is the first message in a conversation")
    has_data_source: bool = Field(False, description="Whether a data source is attached to the conversation")
    provider: Optional[str] = Field(None, description="AI provider to use for content generation")
    model: Optional[str] = Field(None, description="AI model to use for content generation")


class MarketingParserComponent(ToolCompletionMixin, AgentComponent):
    """
    Component for parsing marketing requests with enhanced intent detection.

    This component handles:
    - Intent classification (content generation vs conversational)
    - Marketing task extraction from various input sources
    - Context validation and enrichment
    - Universal tool completion and conversational state management
    """

    def get_agent_type(self) -> str:
        """Return the agent type identifier."""
        return "marketing"

    def get_tool_indicators(self) -> List[str]:
        """Return list of context keys that indicate tool-triggered requests."""
        return ["marketing_form_data", "marketing_task", "marketing_content_request"]

    def get_conversational_flags(self) -> List[str]:
        """Return list of context keys that indicate conversational mode."""
        return [
            "skip_marketing_content_generation",
            "is_conversational",
            "content_generation_completed",
            "tool_completed",
            "auto_conversational_mode"
        ]

    def _get_agent_specific_new_request_patterns(self) -> List[str]:
        """Return agent-specific patterns that indicate new tool requests."""
        return [
            "create marketing", "generate content", "develop strategy",
            "marketing plan", "campaign ideas", "promotional content",
            "marketing strategy", "content creation", "brand messaging"
        ]

    def __init__(self):
        super().__init__()
        self._validation_rules = None

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing MarketingParserComponent with AI intent detection")

        # Note: AI intent detection is now handled directly via LLM calls

        # Initialize fallback intent classification patterns
        self._init_intent_patterns()

        # Initialize validation rules
        self._init_validation_rules()

        # Load configuration
        self.config = config

    def _init_intent_patterns(self) -> None:
        """Initialize intent detection patterns."""
        # Explicit content generation patterns
        self.content_generation_patterns = [
            "generate a marketing", "create a marketing", "develop a marketing",
            "build a marketing", "write a marketing", "generate marketing",
            "create marketing", "develop marketing", "build marketing",
            "write marketing", "generate a campaign", "create a campaign",
            "develop a campaign", "generate social media", "create social media",
            "generate seo", "create seo", "generate content for", "create content for"
        ]

        # Follow-up question patterns
        self.follow_up_patterns = [
            "do you have any more", "any other", "what else", "can you suggest",
            "any additional", "more recommendations", "other ideas", "anything else",
            "what about", "how about", "can you also", "do you recommend",
            "what would you suggest", "any tips", "any advice", "what can be done",
            "what else can be done", "what other", "any more ideas", "more suggestions"
        ]

        # Capability inquiry patterns
        self.capability_patterns = [
            "what can you do", "what can you help", "what services", "what do you offer",
            "how can you help", "what are your capabilities", "what can you help me with",
            "how can you assist", "what kind of help"
        ]

    def _init_validation_rules(self) -> None:
        """Initialize validation rules for marketing tasks."""
        self.validation_rules = {
            "required_fields": ["task_type"],
            "optional_fields": [
                "brand_description", "target_audience", "products_services",
                "marketing_goals", "existing_content", "keywords", "suggested_topics", "tone",
                "competitive_landscape", "budget", "timeline", "platforms", "document_format"
            ],
            # Removed hardcoded valid_task_types - now using semantic validation
            "min_task_type_length": 3,
            "max_task_type_length": 50
        }

    async def _llm_detect_language_and_intent(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Use LLM to detect language and marketing intent in a single call.

        Args:
            message: The user's message text
            context: Additional context information

        Returns:
            Dictionary with language, intent, confidence, and reasoning
        """
        try:
            # Get MCP server for LLM calls
            mcp_server = None
            if context:
                for component in context.get("agent_components", []):
                    if hasattr(component, 'call_tool') and component.__class__.__name__ == "MCPServerComponent":
                        mcp_server = component
                        break

            if not mcp_server:
                logger.error("No MCP server available for language and intent detection")
                raise Exception("MCP server not available")

            # Create YAML-structured prompt for language and intent detection

            analysis_config = {
                "task": "language_and_intent_analysis",
                "input": {
                    "message": message,
                    "context": "marketing_agent_conversation"
                },
                "analysis_requirements": {
                    "language_detection": {
                        "supported_languages": ["en", "es", "fr", "de", "it", "pt", "zh", "ja", "ko", "ar", "ru", "hi", "nl", "sv", "da", "no"],
                        "handle_mixed_languages": True,
                        "handle_typos": True
                    },
                    "intent_classification": {
                        "categories": [
                            "marketing_request",
                            "conversational",
                            "follow_up",
                            "greeting",
                            "capability_inquiry",
                            "clarification"
                        ],
                        "marketing_actions": ["create", "develop", "generate", "plan", "design", "optimize", "analyze"],
                        "marketing_subjects": ["campaign", "strategy", "content", "social_media", "seo", "email", "advertising", "branding"]
                    },
                    "response_planning": {
                        "tone_options": ["professional", "friendly", "consultative", "enthusiastic"],
                        "determine_content_generation_need": True
                    }
                },
                "output_format": {
                    "type": "yaml",
                    "structure": {
                        "language": "language_code",
                        "language_name": "full_language_name",
                        "intent_type": "category_from_intent_classification",
                        "confidence": "float_0_to_1",
                        "is_marketing_request": "boolean",
                        "marketing_action": "action_or_null",
                        "marketing_subject": "subject_or_null",
                        "reasoning": "brief_explanation",
                        "suggested_response_tone": "tone_option",
                        "requires_content_generation": "boolean"
                    }
                },
                "guidelines": [
                    "Detect language accurately regardless of mixed languages or typos",
                    "Identify if user wants marketing content created vs just asking questions",
                    "Consider context: greetings, follow-ups, capability questions vs work requests",
                    "Be confident but honest about uncertainty",
                    "For marketing requests, identify specific action and subject matter",
                    "Determine if this requires actual content generation or just conversation"
                ]
            }

            # Create a concise analysis prompt instead of large YAML dump
            analysis_prompt = f"""Analyze this user message for language and marketing intent: "{message}"

Respond with YAML output containing: language, intent_type, confidence, is_marketing_request, suggested_response_tone, and marketing_context."""

            # Use conversation tool for analysis
            tool_result = await mcp_server.call_tool("handle_conversation", {
                "message": analysis_prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "language_and_intent_analysis",
                    "response_format": "yaml",
                    "analysis_type": "multilingual_marketing_intent",
                    "analysis_config": analysis_config  # Move config to user_context
                },
                "intent_type": "analysis",
                "confidence": 1.0,
                "temperature": 0.1,  # Low temperature for consistent analysis
                "max_tokens": 400
            })

            if tool_result.get("isError", False):
                logger.error(f"LLM analysis failed: {tool_result}")
                return self._fallback_language_and_intent_detection(message)

            # Parse the LLM YAML response
            response_text = tool_result.get("content", [{}])[0].get("text", "")

            try:
                analysis_result = yaml.safe_load(response_text)

                # Validate required fields
                required_fields = ["language", "intent_type", "confidence", "is_marketing_request"]
                if all(field in analysis_result for field in required_fields):
                    logger.info(f"LLM YAML Analysis: {analysis_result['language']} | {analysis_result['intent_type']} | confidence: {analysis_result['confidence']}")
                    return analysis_result
                else:
                    logger.warning(f"LLM YAML response missing required fields: {analysis_result}")
                    return self._fallback_language_and_intent_detection(message)

            except yaml.YAMLError as e:
                logger.warning(f"Failed to parse LLM YAML response: {e}. Response: {response_text}")
                return self._fallback_language_and_intent_detection(message)

        except Exception as e:
            logger.error(f"Error in LLM language/intent detection: {e}")
            return self._fallback_language_and_intent_detection(message)

    async def _llm_language_and_intent_detection(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Use LLM for comprehensive language and intent detection.

        Args:
            message: The user's message text
            context: Current context dictionary

        Returns:
            Dictionary with LLM-based language and intent detection
        """
        try:
            # Get MCP server for LLM calls
            mcp_server = None
            for component in context.get("agent_components", []):
                if hasattr(component, 'call_tool'):
                    mcp_server = component
                    break

            if not mcp_server:
                logger.error("No MCP server available for language and intent detection")
                raise Exception("MCP server not available")

            # Get conversation history for context
            conversation_history = context.get("conversation_history", [])
            history_text = ""
            if conversation_history:
                recent_messages = conversation_history[-3:]  # Last 3 messages for context
                history_text = "\n".join([
                    f"{msg.get('sender', 'unknown')}: {msg.get('content', '')}"
                    for msg in recent_messages
                ])

            # Create comprehensive prompt for language and intent detection
            detection_prompt = f"""
            Analyze the following user message for language and intent detection:

            User Message: "{message}"

            Conversation Context (last 3 messages):
            {history_text}

            Please provide a JSON response with the following structure:
            {{
                "language": "language_code (e.g., en, es, fr, de, etc.)",
                "intent_type": "one of: general_question, follow_up_question, content_generation, marketing_advice, greeting",
                "confidence": "confidence_score_between_0_and_1",
                "reasoning": "brief_explanation_of_your_analysis",
                "suggested_response_tone": "one of: professional, friendly, consultative, enthusiastic, casual",
                "requires_content_generation": "boolean_true_if_user_wants_marketing_content_created",
                "is_conversational": "boolean_true_if_this_is_casual_conversation"
            }}

            Guidelines:
            - Detect the primary language of the user's message
            - Determine if this is a follow-up question, content generation request, or general conversation
            - Consider the conversation context when making decisions
            - Be confident in your assessment but realistic about uncertainty
            - Choose appropriate tone based on the user's communication style
            """

            # Call LLM for analysis
            tool_result = await mcp_server.call_tool("handle_conversation", {
                "message": detection_prompt,
                "conversation_history": [],
                "user_context": {"task": "language_intent_detection"},
                "intent_type": "analysis",
                "confidence": 1.0,
                "provider": context.get("provider", "groq"),
                "model": context.get("model", "llama3-70b-8192"),
                "temperature": 0.3,  # Lower temperature for more consistent analysis
                "response_format": "json"
            })

            if tool_result.get("isError", False):
                logger.error(f"LLM language/intent detection failed: {tool_result}")
                raise Exception("LLM analysis failed")

            # Parse the LLM response
            response_text = tool_result.get("content", [{}])[0].get("text", "")

            # Try to extract JSON from the response
            import json
            import re

            # Look for JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    analysis_result = json.loads(json_match.group())

                    # Validate required fields
                    required_fields = ["language", "intent_type", "confidence", "reasoning", "suggested_response_tone"]
                    if all(field in analysis_result for field in required_fields):
                        logger.info(f"LLM language/intent analysis successful: {analysis_result}")
                        return analysis_result
                    else:
                        logger.warning(f"LLM response missing required fields: {analysis_result}")
                        raise Exception("Incomplete LLM analysis")

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse LLM JSON response: {e}")
                    raise Exception("Invalid JSON response from LLM")
            else:
                logger.error(f"No JSON found in LLM response: {response_text}")
                raise Exception("No structured response from LLM")

        except Exception as e:
            logger.error(f"LLM language and intent detection failed: {e}")
            # Instead of fallback, raise the error to be handled upstream
            raise Exception(f"Language and intent detection failed: {str(e)}")





    async def _is_explicit_marketing_request(self, message: str, context: Dict[str, Any] = None) -> bool:
        """
        Determine if a message is explicitly requesting marketing content generation.
        Uses LLM-based analysis for accurate multilingual intent detection.

        Args:
            message: The user's message text
            context: Additional context information

        Returns:
            True if the message is an explicit marketing content generation request, False otherwise
        """
        try:
            # Use LLM for comprehensive language and intent analysis
            analysis = await self._llm_language_and_intent_detection(message, context or {})

            logger.info(f"LLM Analysis - Language: {analysis.get('language')}, Intent: {analysis.get('intent_type')}, Requires Content Generation: {analysis.get('requires_content_generation')}")
            logger.info(f"Reasoning: {analysis.get('reasoning')}")

            # Store analysis results in context for later use
            if context:
                context["llm_analysis"] = analysis

            return analysis.get("requires_content_generation", False)

        except Exception as e:
            logger.error(f"Error in LLM-based marketing request detection: {e}")
            # If LLM fails, we cannot determine intent reliably, so return False to be safe
            # This will route the message to conversational handling instead
            logger.warning("LLM analysis failed, treating as conversational to avoid errors")
            return False







    async def _detect_intent_with_llm(self, message: str, context: Dict[str, Any], mcp_server) -> Dict[str, Any]:
        """
        Use LLM to detect the intent of the user's message.

        Args:
            message: The user's message
            context: Context information including conversation history
            mcp_server: MCP server component for LLM calls

        Returns:
            Dict with intent, confidence, and reasoning
        """
        try:
            # Prepare conversation history for context
            conversation_history = context.get("conversation_history", [])
            history_text = ""
            if conversation_history:
                recent_messages = conversation_history[-3:]  # Last 3 messages for context
                history_text = "\n".join([
                    f"{msg.get('sender', 'unknown')}: {msg.get('content', '')}"
                    for msg in recent_messages
                ])

            # Check if there's marketing form data
            has_form_data = bool(context.get("marketing_form_data") or
                               (context.get("metadata", {}).get("marketing_form_data")) or
                               (context.get("parent_context", {}).get("marketing_form_data")))

            # Create prompt for intent detection
            prompt = f"""Analyze this user message and determine the intent. Consider the conversation context.

User Message: "{message}"

Conversation History:
{history_text if history_text else "No previous conversation"}

Has Marketing Form Data: {has_form_data}

Determine the intent from these options:
1. content_generation - User wants to create/generate marketing content (strategies, campaigns, social media posts, etc.)
2. follow_up_question - User is asking follow-up questions about previously generated content
3. conversational - General conversation, questions about capabilities, or clarifications
4. greeting - Initial greeting or introduction

Respond with ONLY a JSON object in this format:
{{
    "intent": "content_generation|follow_up_question|conversational|greeting",
    "confidence": 0.0-1.0,
    "reasoning": "Brief explanation of why this intent was chosen"
}}"""

            # Call LLM for intent detection
            tool_result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "intent_detection",
                    "has_form_data": has_form_data
                },
                "intent_type": "intent_analysis",
                "confidence": 1.0,
                "temperature": 0.3,  # Low temperature for consistent intent detection
                "max_tokens": 150
            })

            if tool_result.get("isError", False):
                logger.error(f"LLM intent detection failed: {tool_result}")
                return {"intent": "conversational", "confidence": 0.5, "reasoning": "LLM call failed"}

            # Extract and parse the response
            response_text = tool_result.get("content", [{}])[0].get("text", "")

            # Try to parse JSON response
            import json
            try:
                # Clean the response to extract JSON
                response_text = response_text.strip()
                if response_text.startswith("```json"):
                    response_text = response_text[7:]
                if response_text.endswith("```"):
                    response_text = response_text[:-3]
                response_text = response_text.strip()

                # Extract JSON from the response (it might contain extra text)
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1

                if json_start != -1 and json_end > json_start:
                    json_str = response_text[json_start:json_end]
                    result = json.loads(json_str)
                else:
                    # No JSON found, try parsing the whole response
                    result = json.loads(response_text)

                # Validate the result
                valid_intents = ["content_generation", "follow_up_question", "conversational", "greeting"]
                if result.get("intent") not in valid_intents:
                    logger.warning(f"Invalid intent '{result.get('intent')}' from LLM, defaulting to conversational")
                    return {"intent": "conversational", "confidence": 0.5, "reasoning": "Invalid intent from LLM"}

                return result

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM intent response as JSON: {e}")
                logger.error(f"Raw response: {response_text}")
                return {"intent": "conversational", "confidence": 0.5, "reasoning": "Failed to parse LLM response"}

        except Exception as e:
            logger.error(f"Error in LLM intent detection: {e}")
            return {"intent": "conversational", "confidence": 0.5, "reasoning": f"Error: {str(e)}"}

    async def _check_conversation_state(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check conversation state from database history to determine if we're in conversational mode.

        This method looks at recent message metadata to see if content was recently generated
        and the conversation should be in conversational mode.

        Args:
            context: Context dictionary containing conversation history

        Returns:
            Dictionary with conversation state information
        """
        logger.info("Checking conversation state from database history")

        conversation_state = {
            "is_conversational_mode": False,
            "has_recent_content": False,
            "is_follow_up": False,
            "last_content_type": None,
            "messages_since_content": 0
        }

        try:
            # CRITICAL: Check if this is a tool-triggered request first
            # Tool-triggered requests should NEVER be treated as conversational
            if self._is_tool_triggered_request(context):
                logger.info("This is a tool-triggered request - NOT conversational mode")
                return conversation_state

            # Get conversation history from context
            conversation_history = context.get("conversation_history", [])
            if not conversation_history:
                logger.info("No conversation history found")
                return conversation_state

            logger.info(f"Checking {len(conversation_history)} messages for conversation state")

            # Look through recent messages (last 10) for marketing content generation
            recent_messages = conversation_history[-10:] if len(conversation_history) > 10 else conversation_history

            content_found = False
            messages_since_content = 0

            # Iterate through messages in reverse order (newest first)
            for i, msg in enumerate(reversed(recent_messages)):
                sender = msg.get("sender", "")
                metadata = msg.get("metadata", {}) or {}
                content = msg.get("content", "").lower()

                logger.info(f"Checking message {i}: sender={sender}, metadata_keys={list(metadata.keys())}")

                # Check if this is an AI message with marketing content generation metadata
                if sender == "ai":
                    # Check for explicit content generation flags
                    if (metadata.get("generated_content", False) or
                        metadata.get("content_generation_completed", False) or
                        metadata.get("marketing_content_generated", False)):

                        logger.info(f"Found marketing content generation in message {i}")
                        content_found = True
                        conversation_state["has_recent_content"] = True
                        conversation_state["last_content_type"] = metadata.get("content_type", "unknown")
                        conversation_state["messages_since_content"] = messages_since_content
                        break

                    # Check for new conversational state metadata
                    elif metadata.get("conversational_state", {}).get("content_generated", False):
                        logger.info(f"Found conversational state metadata in message {i}")
                        content_found = True
                        conversation_state["has_recent_content"] = True
                        conversation_state["last_content_type"] = metadata.get("conversational_state", {}).get("content_type", "unknown")
                        conversation_state["messages_since_content"] = messages_since_content
                        break

                    # Check for content type indicators in metadata
                    elif (metadata.get("persona") == "marketing" and
                          any(keyword in content for keyword in [
                              "marketing strategy", "campaign plan", "social media content",
                              "seo optimization", "content calendar", "brand messaging"
                          ])):

                        logger.info(f"Found marketing content by content analysis in message {i}")
                        content_found = True
                        conversation_state["has_recent_content"] = True
                        conversation_state["last_content_type"] = "marketing_content"
                        conversation_state["messages_since_content"] = messages_since_content
                        break

                # Count user messages since potential content
                elif sender == "user":
                    messages_since_content += 1

            # If we found recent content and there are follow-up user messages, we're in conversational mode
            if content_found and messages_since_content > 0:
                conversation_state["is_conversational_mode"] = True
                conversation_state["is_follow_up"] = True
                logger.info(f"Conversation is in conversational mode: {messages_since_content} user messages since content generation")
            elif content_found and messages_since_content == 0:
                # Content was just generated, next message should be conversational
                conversation_state["is_conversational_mode"] = True
                logger.info("Content was just generated, setting conversational mode for next message")

            logger.info(f"Conversation state: {conversation_state}")
            return conversation_state

        except Exception as e:
            logger.error(f"Error checking conversation state: {e}", exc_info=True)
            return conversation_state

    def _is_tool_triggered_request(self, context: Dict[str, Any]) -> bool:
        """
        Determine if this is a tool-triggered request (e.g., from a button click with form data).

        Tool-triggered requests should always be processed as content generation, regardless
        of conversation history.

        Args:
            context: Context dictionary

        Returns:
            True if this is a tool-triggered request
        """
        # Check for marketing form data - this indicates a tool/button triggered request
        has_form_data = (
            context.get("marketing_form_data") is not None or
            (context.get("metadata", {}).get("marketing_form_data") is not None) or
            (context.get("context", {}).get("marketing_form_data") is not None)
        )

        # Check for regeneration flag - this also indicates a tool-triggered request
        is_regeneration = (
            context.get("is_regeneration", False) or
            (context.get("metadata", {}).get("is_regeneration", False))
        )

        # Check for explicit tool call indicators
        has_tool_indicators = (
            context.get("tool_call", False) or
            context.get("button_triggered", False) or
            context.get("form_submission", False)
        )

        # Check for specific action types that should trigger tools
        message = context.get("message", "").lower().strip()
        action_context = context.get("context", {})
        is_template_gallery_action = (
            "template_gallery" in message or
            "browse the template gallery" in message or
            "show me the template gallery" in message or
            action_context.get("action_type") == "template_gallery_action"
        )

        is_business_setup_action = (
            "business_setup" in message or
            "business setup guide" in message or
            "start the business setup" in message or
            action_context.get("action_type") == "business_setup_action"
        )

        is_examples_action = (
            "show_examples" in message or
            "marketing examples" in message or
            "show me marketing examples" in message or
            action_context.get("action_type") == "examples_action"
        )

        has_special_action_triggers = is_template_gallery_action or is_business_setup_action or is_examples_action

        result = has_form_data or is_regeneration or has_tool_indicators or has_special_action_triggers

        if result:
            logger.info(f"Tool-triggered request detected: form_data={has_form_data}, "
                       f"regeneration={is_regeneration}, tool_indicators={has_tool_indicators}, "
                       f"special_actions={has_special_action_triggers}")

        return result

    def _analyze_request_intelligently(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Intelligently analyze the request to determine the appropriate processing mode.

        This method provides a comprehensive analysis that distinguishes between:
        1. Tool-triggered requests (button clicks, form submissions)
        2. Conversational follow-ups after tool calls
        3. New conversational messages
        4. Regeneration requests

        Args:
            context: The processing context

        Returns:
            Dictionary with analysis results and processing recommendations
        """
        message = context.get("message", "")

        # Step 1: Check for explicit tool triggers (highest priority)
        # Tool triggers should ALWAYS take precedence over tool completion state
        is_tool_triggered = self._is_tool_triggered_request(context)
        if is_tool_triggered:
            return {
                "should_use_tools": True,
                "should_be_conversational": False,
                "is_follow_up": False,
                "is_tool_call": True,
                "confidence": 0.95,
                "reasoning": "Tool-triggered request detected (form data, regeneration, or explicit tool indicators)",
                "trigger_type": "tool"
            }

        # Step 2: Check for tool completion state (high priority for conversational mode)
        # Only check this if there's no explicit tool trigger
        if self._is_in_tool_completion_state(context):
            return {
                "should_use_tools": False,
                "should_be_conversational": True,
                "is_follow_up": True,
                "is_tool_call": False,
                "confidence": 0.98,
                "reasoning": "Tool completion state detected - automatically returning to conversational mode",
                "trigger_type": "tool_completion"
            }

        # Step 2: Check conversation history for recent tool calls
        conversation_history = context.get("conversation_history", [])
        recent_tool_call = self._find_recent_tool_call_in_history(conversation_history)

        # Step 3: Analyze message content for follow-up patterns
        is_follow_up_message = self._is_follow_up_message_pattern(message)

        # Step 4: Check for explicit content generation patterns in the message
        is_explicit_content_request = self._is_explicit_content_generation_request(message)

        # Step 5: Make decision based on analysis
        if is_explicit_content_request:
            return {
                "should_use_tools": True,
                "should_be_conversational": False,
                "is_follow_up": False,
                "is_tool_call": False,
                "confidence": 0.90,
                "reasoning": "Explicit content generation request detected in message",
                "trigger_type": "explicit_content_request"
            }
        elif recent_tool_call and is_follow_up_message:
            return {
                "should_use_tools": False,
                "should_be_conversational": True,
                "is_follow_up": True,
                "is_tool_call": False,
                "confidence": 0.85,
                "reasoning": f"Follow-up message detected after recent tool call ({recent_tool_call['content_type']})",
                "trigger_type": "follow_up",
                "recent_tool_call": recent_tool_call
            }
        elif recent_tool_call and not self._is_explicit_new_content_request(message):
            return {
                "should_use_tools": False,
                "should_be_conversational": True,
                "is_follow_up": True,
                "is_tool_call": False,
                "confidence": 0.75,
                "reasoning": "Message after recent tool call, treating as conversational unless explicit new content request",
                "trigger_type": "conversational_after_tool",
                "recent_tool_call": recent_tool_call
            }
        else:
            # No recent tool call or explicit new content request
            return {
                "should_use_tools": False,
                "should_be_conversational": True,
                "is_follow_up": False,
                "is_tool_call": False,
                "confidence": 0.70,
                "reasoning": "No tool triggers or recent tool calls detected, treating as conversational",
                "trigger_type": "conversational"
            }

    def _find_recent_tool_call_in_history(self, conversation_history: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Find the most recent tool call in conversation history."""
        if not conversation_history:
            return None

        # Look through last 5 messages for tool calls
        recent_messages = conversation_history[-5:] if len(conversation_history) > 5 else conversation_history

        for msg in reversed(recent_messages):
            if msg.get("sender") == "ai":
                metadata = msg.get("metadata", {}) or {}

                # Check for tool call indicators
                if (metadata.get("generated_content", False) or
                    metadata.get("tool_call_result", False) or
                    metadata.get("conversational_state", {}).get("content_generated", False) or
                    metadata.get("content_generation_completed", False)):

                    return {
                        "timestamp": metadata.get("timestamp", time.time()),
                        "tool_name": metadata.get("tool_name", "marketing_content_generator"),
                        "content_type": metadata.get("content_type", metadata.get("task_type", "unknown")),
                        "messages_ago": len([m for m in reversed(recent_messages) if m.get("sender") == "user" and conversation_history.index(m) > conversation_history.index(msg)])
                    }
        return None

    def _is_follow_up_message_pattern(self, message: str) -> bool:
        """Check if the message matches follow-up question patterns."""
        if not message:
            return False

        message_lower = message.lower().strip()

        # Strong follow-up indicators
        strong_patterns = [
            "what else", "any other", "more ideas", "additional", "furthermore",
            "what about", "how about", "any more", "other ways", "what other",
            "anything else", "more suggestions", "other options", "also",
            "can you also", "do you have more", "any additional"
        ]

        # Weak follow-up indicators (need more context)
        weak_patterns = [
            "can you", "do you", "what", "how", "why", "when", "where"
        ]

        # Check for strong patterns first
        if any(pattern in message_lower for pattern in strong_patterns):
            return True

        # Check for weak patterns with additional context
        if any(pattern in message_lower for pattern in weak_patterns):
            # Additional checks for weak patterns
            if len(message.split()) <= 10:  # Short questions are more likely follow-ups
                return True

        return False

    def _is_explicit_content_generation_request(self, message: str) -> bool:
        """Check if the message is an explicit request for content generation."""
        if not message:
            return False

        message_lower = message.lower().strip()

        # Strong content generation patterns
        strong_patterns = [
            "generate", "create", "develop", "build", "write", "make", "design",
            "marketing strategy", "campaign strategy", "social media strategy",
            "seo strategy", "content strategy", "advertising strategy", "brand strategy",
            "marketing plan", "campaign plan", "social media plan", "content plan",
            "marketing content", "social media content", "promotional content",
            "marketing campaign", "advertising campaign", "email campaign"
        ]

        # Check for strong patterns
        if any(pattern in message_lower for pattern in strong_patterns):
            # Additional validation to avoid false positives for questions
            question_words = ["what", "how", "why", "when", "where", "who", "which", "can you", "do you", "will you"]
            # If it starts with a question word, it's likely a question, not a generation request
            if any(message_lower.startswith(qw) for qw in question_words):
                return False
            return True

        return False

    def _is_explicit_new_content_request(self, message: str) -> bool:
        """Check if the message is an explicit request for new content generation."""
        if not message:
            return False

        message_lower = message.lower().strip()

        # Explicit new content patterns
        explicit_patterns = [
            "create a new", "generate a new", "make a new", "develop a new",
            "create another", "generate another", "make another", "develop another",
            "new marketing strategy", "new campaign", "new content", "new plan",
            "different strategy", "different campaign", "different approach",
            "start over", "from scratch", "completely new"
        ]

        # Check for explicit patterns first
        if any(pattern in message_lower for pattern in explicit_patterns):
            return True

        # Enhanced detection for content type requests in follow-up messages
        content_generation_verbs = [
            "create", "generate", "make", "develop", "build", "write", "design", "craft", "produce"
        ]

        content_type_keywords = [
            "strategy", "plan", "analysis", "campaign", "content", "post", "calendar",
            "positioning", "competitive", "brand", "marketing", "social media", "seo",
            "email", "influencer", "pr", "product launch", "customer persona",
            "market research", "digital", "advertising", "lead generation", "conversion"
        ]

        # Check if message contains generation verb + content type
        has_generation_verb = any(verb in message_lower for verb in content_generation_verbs)
        has_content_type = any(keyword in message_lower for keyword in content_type_keywords)

        if has_generation_verb and has_content_type:
            logger.info(f"Detected new content request: generation verb + content type in message: '{message}'")
            return True

        # Check for "now" patterns that indicate immediate action
        immediate_patterns = [
            "now create", "now generate", "now make", "now develop",
            "let's create", "let's generate", "let's make", "let's develop",
            "can you create", "can you generate", "can you make", "can you develop"
        ]

        if any(pattern in message_lower for pattern in immediate_patterns):
            logger.info(f"Detected immediate content request pattern in message: '{message}'")
            return True

        # Check for specific content type mentions that typically indicate new requests
        specific_content_requests = [
            "brand positioning", "competitive analysis", "content calendar", "email marketing",
            "influencer strategy", "pr strategy", "product launch", "customer persona",
            "market research", "digital strategy", "advertising strategy", "lead generation"
        ]

        if any(content_type in message_lower for content_type in specific_content_requests):
            logger.info(f"Detected specific content type request in message: '{message}'")
            return True

        return False

    def _is_in_tool_completion_state(self, context: Dict[str, Any]) -> bool:
        """
        Check if the context indicates we're in a tool completion state.

        This method checks for flags that indicate a tool has just completed
        and the agent should automatically return to conversational mode.

        IMPORTANT: This should only return True when there's clear evidence
        of a recent tool completion WITHOUT any new tool triggers.

        Args:
            context: Context dictionary

        Returns:
            True if in tool completion state
        """
        # CRITICAL: First, ensure there are no active tool triggers
        # If there are tool triggers, we should NEVER be in completion state
        if self._is_tool_triggered_request(context):
            logger.info("Tool triggers detected - not in completion state")
            return False

        # Check for explicit tool completion flags in current context
        # These flags are set when a tool has just completed
        if (context.get("tool_completed", False) or
            context.get("auto_conversational_mode", False) or
            context.get("metadata", {}).get("tool_completion_reset", False)):
            logger.info("Tool completion state detected from context flags")
            return True

        # Check conversation history for recent tool completion
        # BUT only if this is clearly a follow-up message, not a new tool request
        conversation_history = context.get("conversation_history", [])
        message = context.get("message", "")

        # Only check conversation history if the current message looks like a follow-up
        if (conversation_history and len(conversation_history) > 0 and
            self._is_follow_up_message_pattern(message)):

            # Check the last AI message for tool completion indicators
            for msg in reversed(conversation_history):
                if msg.get("sender") == "ai":
                    metadata = msg.get("metadata", {}) or {}

                    # Check for tool completion metadata
                    if (metadata.get("tool_execution", {}).get("status") == "completed" or
                        metadata.get("conversational_state", {}).get("tool_completed", False) or
                        metadata.get("conversational_state", {}).get("auto_return_to_conversational", False) or
                        metadata.get("tool_completion_reset", False)):
                        logger.info("Tool completion state detected from conversation history for follow-up message")
                        return True

                    # Only check the most recent AI message
                    break

        return False

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        message = context.get("message", "")

        # Note: Intent detection is now handled directly via LLM calls in _detect_intent_with_llm

        # Create a combined context that includes metadata
        combined_ctx = {}

        # Add the regular context
        ctx = context.get("context", {})
        if ctx:
            combined_ctx.update(ctx)

        # Add metadata if available
        metadata = context.get("metadata", {})
        if metadata:
            combined_ctx["metadata"] = metadata

        # Add the original context as parent_context for reference
        combined_ctx["parent_context"] = context

        # Log the incoming context to see what we're working with
        logger.info("MARKETING PARSER COMPONENT - INCOMING CONTEXT:")
        logger.info(f"Message: {message}")
        logger.info(f"Context keys: {list(context.keys())}")
        logger.info(f"Combined context keys: {list(combined_ctx.keys())}")

        # Check for marketing_form_data in various locations
        has_marketing_form_data = False
        if "marketing_form_data" in ctx:
            logger.info(f"FOUND marketing_form_data in context")
            has_marketing_form_data = True
        elif metadata and "marketing_form_data" in metadata:
            logger.info(f"FOUND marketing_form_data in metadata")
            has_marketing_form_data = True
        else:
            logger.info("NO marketing_form_data found in context or metadata")

        # Check if this is a regeneration request
        is_regeneration = False
        if "is_regeneration" in combined_ctx and combined_ctx["is_regeneration"]:
            is_regeneration = True
            logger.info("This is a regeneration request")

        # CRITICAL: Use intelligent request analysis to determine processing mode
        # This properly distinguishes between tool calls, conversational messages, and follow-ups
        request_analysis = self._analyze_request_intelligently(context)

        if request_analysis["should_be_conversational"]:
            logger.info("INTELLIGENT ANALYSIS: Request should be handled conversationally")
            logger.info(f"Analysis: {request_analysis}")
            context["skip_marketing_content_generation"] = True
            context["is_conversational"] = True
            context["is_follow_up_question"] = request_analysis.get("is_follow_up", False)
            context["request_analysis"] = request_analysis
            context["llm_intent"] = {
                "intent": "follow_up_question" if request_analysis.get("is_follow_up") else "conversational",
                "confidence": request_analysis.get("confidence", 0.9),
                "reasoning": request_analysis.get("reasoning", "Intelligent request analysis")
            }
            logger.info("Returning early for conversational message - no marketing task will be created")
            return context
        elif request_analysis["should_use_tools"]:
            logger.info("INTELLIGENT ANALYSIS: Request should use tools for content generation")
            logger.info(f"Analysis: {request_analysis}")
            context["should_use_tools"] = True
            context["skip_conversational_mode"] = True
            context["skip_marketing_content_generation"] = False  # Explicitly ensure content generation is enabled
            context["is_conversational"] = False  # Explicitly ensure it's not marked as conversational
            context["detected_marketing_request"] = True  # Mark as detected marketing request
            context["request_analysis"] = request_analysis
            # Continue with tool processing

        # SECONDARY CHECK: Check for conversational flags set by orchestrator or previous context reset
        # This includes flags restored from conversation history by the orchestrator
        # BUT ONLY if this is NOT a tool-triggered request
        if (not request_analysis.get("should_use_tools", False) and
            (combined_ctx.get("skip_marketing_content_generation", False) or
             combined_ctx.get("is_conversational", False) or
             combined_ctx.get("content_generation_completed", False) or
             combined_ctx.get("tool_completed", False) or
             combined_ctx.get("auto_conversational_mode", False) or
             context.get("skip_marketing_content_generation", False) or
             context.get("is_conversational", False) or
             context.get("content_generation_completed", False) or
             context.get("tool_completed", False) or
             context.get("auto_conversational_mode", False))):

            # Check if this was restored by orchestrator
            orchestrator_restored = (
                context.get("metadata", {}).get("conversational_state_restored", False) or
                combined_ctx.get("metadata", {}).get("conversational_state_restored", False)
            )

            logger.info("CONVERSATIONAL FLAGS DETECTED - treating as conversational message")
            logger.info(f"Orchestrator restored: {orchestrator_restored}")
            logger.info(f"Combined context flags: skip_marketing_content_generation={combined_ctx.get('skip_marketing_content_generation')}, "
                       f"is_conversational={combined_ctx.get('is_conversational')}, "
                       f"content_generation_completed={combined_ctx.get('content_generation_completed')}, "
                       f"tool_completed={combined_ctx.get('tool_completed')}, "
                       f"auto_conversational_mode={combined_ctx.get('auto_conversational_mode')}")
            logger.info(f"Direct context flags: skip_marketing_content_generation={context.get('skip_marketing_content_generation')}, "
                       f"is_conversational={context.get('is_conversational')}, "
                       f"content_generation_completed={context.get('content_generation_completed')}, "
                       f"tool_completed={context.get('tool_completed')}, "
                       f"auto_conversational_mode={context.get('auto_conversational_mode')}")

            context["skip_marketing_content_generation"] = True
            context["is_conversational"] = True
            context["is_follow_up_question"] = True

            reasoning = "Orchestrator-restored conversational state" if orchestrator_restored else "Conversational flags from previous context reset"
            context["llm_intent"] = {"intent": "follow_up_question", "confidence": 1.0, "reasoning": reasoning}
            logger.info("Returning early for conversational message - no marketing task will be created")
            return context

        # Check for special tool actions first (template gallery, business setup, examples)
        special_action_handled = await self._handle_special_tool_actions(context)
        if special_action_handled:
            logger.info("Special tool action handled - returning response")
            context["skip_marketing_content_generation"] = True
            context["is_conversational"] = False
            context["special_action_completed"] = True
            return context

        # PRIORITY CHECK: If we have marketing form data or tool-triggered request, skip LLM intent detection and proceed with content generation
        if has_marketing_form_data or is_regeneration or request_analysis.get("should_use_tools", False):
            logger.info("PRIORITY: Marketing form data, regeneration, or tool-triggered request detected - proceeding directly to content generation")
            context["skip_marketing_content_generation"] = False
            context["is_conversational"] = False
            context["detected_marketing_request"] = True
            context["llm_intent"] = {"intent": "content_generation", "confidence": 1.0, "reasoning": "Tool-triggered request or marketing form data provided"}
            # Skip LLM intent detection and proceed to task creation
        elif not request_analysis.get("should_be_conversational", False):
            # Use LLM-based intent detection only when no form data is available
            try:
                # Get MCP server for LLM intent detection
                mcp_server = None
                for component in context.get("agent_components", []):
                    if hasattr(component, 'call_tool'):
                        mcp_server = component
                        break

                if mcp_server:
                    # Use LLM to determine intent
                    intent_result = await self._detect_intent_with_llm(message, combined_ctx, mcp_server)

                    logger.info(f"LLM Intent Detection Result:")
                    logger.info(f"  - Intent: {intent_result.get('intent', 'unknown')}")
                    logger.info(f"  - Confidence: {intent_result.get('confidence', 0)}")
                    logger.info(f"  - Reasoning: {intent_result.get('reasoning', 'No reasoning provided')}")

                    # Handle based on LLM intent detection
                    intent = intent_result.get('intent', 'conversational')

                    if intent == 'content_generation':
                        logger.info("LLM detected content generation request - proceeding with task creation")
                        context["skip_marketing_content_generation"] = False
                        context["is_conversational"] = False
                        context["detected_marketing_request"] = True
                        context["llm_intent"] = intent_result
                        # Continue to create marketing task

                    elif intent in ['follow_up_question', 'conversational', 'greeting', 'capability_inquiry']:
                        logger.info(f"LLM detected {intent} - handling conversationally")
                        context["skip_marketing_content_generation"] = True
                        context["is_conversational"] = True
                        context["llm_intent"] = intent_result

                        if intent == 'follow_up_question':
                            context["is_follow_up_question"] = True
                        elif intent == 'greeting':
                            context["is_initial_greeting"] = True

                        # Return early for conversational intents
                        logger.info("Returning early for conversational intent - no marketing task will be created")
                        return context

                    else:
                        # Unknown intent, default to conversational to be safe
                        logger.warning(f"Unknown intent '{intent}' detected, defaulting to conversational")
                        context["skip_marketing_content_generation"] = True
                        context["is_conversational"] = True
                        context["llm_intent"] = intent_result
                        return context

                else:
                    logger.error("No MCP server available for LLM intent detection")
                    # Without LLM, we can only check for form data or regeneration
                    if has_marketing_form_data or is_regeneration:
                        logger.info("No LLM available, but form data/regeneration detected - proceeding with task creation")
                        context["skip_marketing_content_generation"] = False
                        context["is_conversational"] = False
                        context["detected_marketing_request"] = True
                    else:
                        logger.info("No LLM available and no form data - defaulting to conversational")
                        context["skip_marketing_content_generation"] = True
                        context["is_conversational"] = True
                        return context

            except Exception as e:
                logger.error(f"Error in LLM intent detection: {e}")
                # Fallback: check for form data or regeneration
                if has_marketing_form_data or is_regeneration:
                    logger.info("LLM failed, but form data/regeneration detected - proceeding with task creation")
                    context["skip_marketing_content_generation"] = False
                    context["is_conversational"] = False
                    context["detected_marketing_request"] = True
                else:
                    logger.info("LLM failed and no form data - defaulting to conversational")
                    context["skip_marketing_content_generation"] = True
                    context["is_conversational"] = True
                    return context

        # Log the decision made by LLM
        logger.info(f"Content request analysis:")
        logger.info(f"  - Has marketing form data: {has_marketing_form_data}")
        logger.info(f"  - Is regeneration: {is_regeneration}")
        logger.info(f"  - LLM intent decision: {context.get('llm_intent', {}).get('intent', 'not_determined')}")
        logger.info(f"  - Skip content generation: {context.get('skip_marketing_content_generation', False)}")
        logger.info(f"  - Is conversational: {context.get('is_conversational', False)}")

        # Check for provider and model in various locations
        provider = None
        model = None

        if "provider" in ctx:
            provider = ctx["provider"]
            logger.info(f"FOUND provider in context: {provider}")
        elif metadata and "provider" in metadata:
            provider = metadata["provider"]
            logger.info(f"FOUND provider in metadata: {provider}")
        else:
            logger.info("NO provider found in context or metadata")

        if "model" in ctx:
            model = ctx["model"]
            logger.info(f"FOUND model in context: {model}")
        elif metadata and "model" in metadata:
            model = metadata["model"]
            logger.info(f"FOUND model in metadata: {model}")
        else:
            logger.info("NO model found in context or metadata")

        # CRITICAL CHECK: If we should skip content generation, do NOT create a marketing task
        # This prevents the content generator from finding a task and processing it
        if context.get("skip_marketing_content_generation", False):
            logger.info("SKIPPING task creation due to skip_marketing_content_generation flag - no marketing task will be created")
            # Ensure no marketing task is created for conversational messages
            context.pop("marketing_task", None)  # Remove any existing task
            return context

        # Additional safety check for conversational messages
        # BUT ONLY if this is NOT a tool-triggered request and there's no detected marketing request
        if (context.get("is_conversational", False) and
            not context.get("detected_marketing_request", False) and
            not request_analysis.get("should_use_tools", False)):
            logger.info("SKIPPING task creation for conversational message without explicit marketing request")
            context["skip_marketing_content_generation"] = True
            context.pop("marketing_task", None)  # Remove any existing task
            return context

        # Check if this is a regeneration request
        is_regeneration = False
        if "is_regeneration" in combined_ctx and combined_ctx["is_regeneration"]:
            is_regeneration = True
            logger.info("This is a regeneration request")

        # Only create marketing task for actual content generation requests
        logger.info("Creating marketing task for content generation request")
        task = await self._parse_marketing_request(message, combined_ctx, is_regeneration)

        # Update the context with the parsed information
        context["marketing_task"] = task.model_dump()

        # Add regeneration flag to the context and metadata
        if is_regeneration:
            context["is_regeneration"] = True
            if "metadata" not in context:
                context["metadata"] = {}
            context["metadata"]["is_regeneration"] = True

        # Also add provider and model to the context if found
        if provider:
            context["provider"] = provider
        if model:
            context["model"] = model

        # Log the task that was created
        logger.info("MARKETING PARSER COMPONENT - CREATED TASK:")
        logger.info(f"Task type: {task.task_type}")
        logger.info(f"Brand description: '{task.brand_description}'")
        logger.info(f"Target audience: '{task.target_audience}'")
        logger.info(f"Products/services: '{task.products_services}'")
        logger.info(f"Marketing goals: '{task.marketing_goals}'")
        logger.info(f"Existing content: '{task.existing_content}'")
        logger.info(f"Keywords: '{task.keywords}'")
        logger.info(f"Suggested topics: '{task.suggested_topics}'")
        logger.info(f"Tone: '{task.tone}'")

        return context

    async def _handle_special_tool_actions(self, context: Dict[str, Any]) -> bool:
        """
        Handle special tool actions like template gallery, business setup, and examples.

        Args:
            context: Context dictionary

        Returns:
            True if a special action was handled, False otherwise
        """
        message = context.get("message", "").lower().strip()
        action_context = context.get("context", {})

        # Get MCP server for tool calls
        mcp_server = None
        for component in context.get("agent_components", []):
            if hasattr(component, 'call_tool'):
                mcp_server = component
                break

        if not mcp_server:
            logger.error("No MCP server available for special tool actions")
            return False

        try:
            # Check for template gallery action
            if ("template_gallery" in message or
                "browse the template gallery" in message or
                "show me the template gallery" in message or
                action_context.get("action_type") == "template_gallery_action"):

                logger.info("Handling template gallery action")
                tool_result = await mcp_server.call_tool("browse_template_gallery", {
                    "category": "all",
                    "industry": "",
                    "business_size": "",
                    "search_term": "",
                    "sort_by": "popular"
                })

                if not tool_result.get("isError", False):
                    context["response"] = tool_result.get("content", "Template gallery loaded successfully.")
                    context["metadata"] = {
                        "tool_used": "browse_template_gallery",
                        "special_action": True,
                        **tool_result.get("metadata", {})
                    }
                    return True

            # Check for business setup action
            elif ("business_setup" in message or
                  "business setup guide" in message or
                  "start the business setup" in message or
                  action_context.get("action_type") == "business_setup_action"):

                logger.info("Handling business setup action")
                tool_result = await mcp_server.call_tool("business_setup_guide", {
                    "step": "start",
                    "business_info": {}
                })

                if not tool_result.get("isError", False):
                    context["response"] = tool_result.get("content", "Business setup guide loaded successfully.")
                    context["metadata"] = {
                        "tool_used": "business_setup_guide",
                        "special_action": True,
                        **tool_result.get("metadata", {})
                    }
                    return True

            # Check for examples action
            elif ("show_examples" in message or
                  "marketing examples" in message or
                  "show me marketing examples" in message or
                  action_context.get("action_type") == "examples_action"):

                logger.info("Handling marketing examples action")
                tool_result = await mcp_server.call_tool("show_marketing_examples", {
                    "content_type": "all",
                    "industry": ""
                })

                if not tool_result.get("isError", False):
                    context["response"] = tool_result.get("content", "Marketing examples loaded successfully.")
                    context["metadata"] = {
                        "tool_used": "show_marketing_examples",
                        "special_action": True,
                        **tool_result.get("metadata", {})
                    }
                    return True

        except Exception as e:
            logger.error(f"Error handling special tool action: {e}")
            return False

        return False

    async def _parse_marketing_request(self, message: str, context: Optional[Dict[str, Any]], is_regeneration: bool = False) -> MarketingTask:
        """
        Parse the marketing request from the message and context.

        Args:
            message: The user's message text
            context: Additional context information

        Returns:
            MarketingTask object with parsed parameters
        """
        # Log the full context structure to debug
        logger.info(f"FULL CONTEXT STRUCTURE: {context}")
        logger.info(f"Is regeneration request: {is_regeneration}")

        # CRITICAL: For conversational messages, don't use preserved form data
        # This prevents regenerating content when user asks follow-up questions
        # BUT: Check if this is actually a new content generation request first
        is_new_content_request = self._is_explicit_new_content_request(message)

        is_conversational = (
            context and (
                context.get("skip_marketing_content_generation", False) or
                context.get("is_conversational", False) or
                context.get("is_follow_up_question", False)
            ) and not context.get("detected_marketing_request", False) and not is_new_content_request
        )

        if is_conversational and not is_regeneration:
            logger.info("Creating minimal task for conversational message - no form data will be used")
            # For conversational messages, create a minimal task that won't trigger content generation
            return MarketingTask(
                task_type="conversational",  # Special task type for conversational messages
                brand_description="",
                target_audience="",
                products_services="",
                marketing_goals="",
                existing_content="",
                keywords="",
                suggested_topics="",
                blog_topic="",
                tone="Professional",
                file_id=None,
                is_first_conversation=False,
                has_data_source=False,
                provider=context.get("provider", "groq"),
                model=context.get("model", "llama3-70b-8192")
            )

        # If this is a new content request in a follow-up message, preserve old form data but change content type
        if is_new_content_request and not is_regeneration:
            logger.info("Detected new content generation request in follow-up message - preserving form data but changing content type")
            # Parse the new content type from the message
            new_content_type = self._extract_content_type_from_message(message)
            logger.info(f"Extracted new content type from message: {new_content_type}")

            # Look for existing form data to preserve
            preserved_form_data = self._find_preserved_form_data(context)

            if preserved_form_data:
                logger.info("Found existing form data to preserve - creating task with preserved data and new content type")
                # Create task with preserved form data but new content type
                return MarketingTask(
                    task_type=new_content_type,
                    brand_description=preserved_form_data.get("brand_description", ""),
                    target_audience=preserved_form_data.get("target_audience", ""),
                    products_services=preserved_form_data.get("products_services", ""),
                    marketing_goals=preserved_form_data.get("marketing_goals", ""),
                    existing_content=preserved_form_data.get("existing_content", ""),
                    keywords=preserved_form_data.get("keywords", ""),
                    suggested_topics=preserved_form_data.get("suggested_topics", ""),
                    blog_topic=preserved_form_data.get("blog_topic", ""),
                    competitive_landscape=preserved_form_data.get("competitive_landscape", ""),
                    budget=preserved_form_data.get("budget", ""),
                    timeline=preserved_form_data.get("timeline", ""),
                    platforms=preserved_form_data.get("platforms", ""),
                    tone=preserved_form_data.get("tone", "Professional"),
                    file_id=preserved_form_data.get("file_id"),
                    is_first_conversation=False,
                    has_data_source=bool(preserved_form_data.get("file_id")),
                    provider=context.get("provider", "groq"),
                    model=context.get("model", "llama3-70b-8192")
                )
            else:
                logger.info("No existing form data found - creating task with new content type and empty fields")
                # Create a fresh task without using old form data
                return MarketingTask(
                    task_type=new_content_type,
                    brand_description="",
                    target_audience="",
                    products_services="",
                    marketing_goals="",
                    existing_content="",
                    keywords="",
                    suggested_topics="",
                    blog_topic="",
                    competitive_landscape="",
                    budget="",
                    timeline="",
                    platforms="",
                    tone="Professional",
                    file_id=None,
                    is_first_conversation=False,
                    has_data_source=False,
                    provider=context.get("provider", "groq"),
                    model=context.get("model", "llama3-70b-8192")
                )

        # Check if orchestrator marked form data for preservation with new content type
        if context.get("preserve_form_data_for_new_content", False) and not is_regeneration:
            logger.info("Orchestrator marked form data for preservation with new content type")
            # Parse the new content type from the message
            new_content_type = self._extract_content_type_from_message(message)
            logger.info(f"Using preserved form data with new content type: {new_content_type}")

            # Use the preserved form data but with new content type
            preserved_form_data = self._find_preserved_form_data(context)
            if preserved_form_data:
                return MarketingTask(
                    task_type=new_content_type,
                    brand_description=preserved_form_data.get("brand_description", ""),
                    target_audience=preserved_form_data.get("target_audience", ""),
                    products_services=preserved_form_data.get("products_services", ""),
                    marketing_goals=preserved_form_data.get("marketing_goals", ""),
                    existing_content=preserved_form_data.get("existing_content", ""),
                    keywords=preserved_form_data.get("keywords", ""),
                    suggested_topics=preserved_form_data.get("suggested_topics", ""),
                    blog_topic=preserved_form_data.get("blog_topic", ""),
                    competitive_landscape=preserved_form_data.get("competitive_landscape", ""),
                    budget=preserved_form_data.get("budget", ""),
                    timeline=preserved_form_data.get("timeline", ""),
                    platforms=preserved_form_data.get("platforms", ""),
                    tone=preserved_form_data.get("tone", "Professional"),
                    file_id=preserved_form_data.get("file_id"),
                    is_first_conversation=False,
                    has_data_source=bool(preserved_form_data.get("file_id")),
                    provider=context.get("provider", "groq"),
                    model=context.get("model", "llama3-70b-8192")
                )

        # Check for marketing_form_data in different possible locations
        form_data = None

        # Log the full context to see what we're working with
        logger.info("=== MARKETING PARSER CONTEXT DEBUG ===")
        logger.info(f"Context keys: {list(context.keys()) if context else 'None'}")
        if context:
            for key, value in context.items():
                if key == "marketing_form_data":
                    logger.info(f"✅ Found marketing_form_data directly: {value}")
                elif isinstance(value, dict) and "marketing_form_data" in value:
                    logger.info(f"✅ Found marketing_form_data in {key}: {value['marketing_form_data']}")
                elif key in ["metadata", "context", "parent_context"]:
                    logger.info(f"📋 Checking {key} for marketing_form_data: {type(value)} with keys {list(value.keys()) if isinstance(value, dict) else 'not dict'}")
        logger.info("=== END CONTEXT DEBUG ===")

        # For regeneration requests, we prioritize finding the form data
        if is_regeneration:
            logger.info("Prioritizing form data for regeneration request")

            # First check if marketing_form_data is directly in the context
            if context and "marketing_form_data" in context:
                form_data = context["marketing_form_data"]
                logger.info(f"Found marketing_form_data directly in context for regeneration")
            # Then check in metadata
            elif context and "metadata" in context and "marketing_form_data" in context["metadata"]:
                form_data = context["metadata"]["marketing_form_data"]
                logger.info(f"Found marketing_form_data in metadata for regeneration")
            # Then check in parent_context
            elif context and "parent_context" in context and "context" in context["parent_context"] and "marketing_form_data" in context["parent_context"]["context"]:
                form_data = context["parent_context"]["context"]["marketing_form_data"]
                logger.info(f"Found marketing_form_data in parent_context for regeneration")

            if not form_data:
                logger.warning("No marketing_form_data found for regeneration request")
        else:
            # First check if marketing_form_data is directly in the context
            if context and "marketing_form_data" in context:
                form_data = context["marketing_form_data"]
                logger.info(f"Found marketing_form_data directly in context")
                logger.info(f"FULL FORM DATA RECEIVED: {form_data}")
            # If not found, check if it's in the metadata
            elif context and "metadata" in context and isinstance(context["metadata"], dict) and "marketing_form_data" in context["metadata"]:
                form_data = context["metadata"]["marketing_form_data"]
                logger.info(f"Found marketing_form_data in context.metadata")
            # If still not found, check if it's in the parent context
            elif "parent_context" in context and isinstance(context["parent_context"], dict):
                parent_context = context["parent_context"]
                if "marketing_form_data" in parent_context:
                    form_data = parent_context["marketing_form_data"]
                    logger.info(f"Found marketing_form_data in parent_context")
                elif "metadata" in parent_context and isinstance(parent_context["metadata"], dict) and "marketing_form_data" in parent_context["metadata"]:
                    form_data = parent_context["metadata"]["marketing_form_data"]
                    logger.info(f"Found marketing_form_data in parent_context.metadata")

        # If form data was found in any location, use it
        if form_data:
            try:
                logger.info(f"Using marketing_form_data: {form_data}")

                # Extract provider and model from various possible locations
                provider = None
                model = None

                # Check direct context
                if context:
                    provider = context.get("provider")
                    model = context.get("model")

                    # Check metadata
                    if not provider and "metadata" in context and isinstance(context["metadata"], dict):
                        provider = context["metadata"].get("provider")
                        model = context["metadata"].get("model")

                # Check form data itself
                if not provider and "provider" in form_data:
                    provider = form_data.get("provider")
                    model = form_data.get("model")

                logger.info(f"Provider: {provider}, Model: {model}")

                # Extract file_id from various possible locations
                file_id = None
                has_data_source = False

                # Check direct context
                if context:
                    file_id = context.get("file_id")

                    # Check for data_source in context
                    if not file_id and "data_source" in context:
                        data_source = context.get("data_source", {})
                        has_data_source = True
                        if isinstance(data_source, dict) and data_source.get("type") == "file" and "id" in data_source:
                            file_id = data_source["id"]

                    # Check for data_source in metadata
                    if not file_id and "metadata" in context and isinstance(context["metadata"], dict) and "data_source" in context["metadata"]:
                        data_source = context["metadata"].get("data_source", {})
                        has_data_source = True
                        if isinstance(data_source, dict) and data_source.get("type") == "file" and "id" in data_source:
                            file_id = data_source["id"]

                logger.info(f"File ID: {file_id}, Has data source: {has_data_source}")

                # Get form values directly without adding defaults
                brand_description = form_data.get("brand_description", "")
                if not brand_description:
                    logger.warning("Brand description is empty in form data")

                target_audience = form_data.get("target_audience", "")
                if not target_audience:
                    logger.warning("Target audience is empty in form data")

                products_services = form_data.get("products_services", "")
                if not products_services:
                    logger.warning("Products/services is empty in form data")

                marketing_goals = form_data.get("marketing_goals", "")
                if not marketing_goals:
                    logger.warning("Marketing goals is empty in form data")

                existing_content = form_data.get("existing_content", "")
                if not existing_content:
                    logger.warning("Existing content is empty in form data")

                keywords = form_data.get("keywords", "")
                if not keywords:
                    logger.warning("Keywords are empty in form data")

                suggested_topics = form_data.get("suggested_topics", "")
                if not suggested_topics:
                    logger.warning("Suggested topics are empty in form data")

                blog_topic = form_data.get("blog_topic", "")
                if not blog_topic:
                    logger.warning("Blog topic is empty in form data")

                tone = form_data.get("tone", "Professional")
                if not tone:
                    logger.warning("Tone is empty in form data, using default: Professional")

                # Log all extracted values
                logger.info(f"EXTRACTED FORM VALUES:")
                logger.info(f"- brand_description: '{brand_description}'")
                logger.info(f"- target_audience: '{target_audience}'")
                logger.info(f"- products_services: '{products_services}'")
                logger.info(f"- marketing_goals: '{marketing_goals}'")
                logger.info(f"- existing_content: '{existing_content}'")
                logger.info(f"- keywords: '{keywords}'")
                logger.info(f"- suggested_topics: '{suggested_topics}'")
                logger.info(f"- blog_topic: '{blog_topic}'")
                logger.info(f"- tone: '{tone}'")

                # Map form data fields to task fields
                task = MarketingTask(
                    task_type=form_data.get("content_type", "marketing_strategy"),
                    brand_description=brand_description,
                    target_audience=target_audience,
                    products_services=products_services,
                    marketing_goals=marketing_goals,
                    existing_content=existing_content,
                    keywords=keywords,
                    suggested_topics=suggested_topics,
                    blog_topic=blog_topic,
                    tone=tone,
                    file_id=file_id,
                    is_first_conversation=False,  # Form submission is never the first conversation
                    has_data_source=has_data_source or "data_source" in context,
                    provider=provider,
                    model=model
                )

                logger.info(f"CREATED MARKETING TASK:")
                logger.info(f"- task_type: '{task.task_type}'")
                logger.info(f"- brand_description: '{task.brand_description}' (length: {len(task.brand_description)})")
                logger.info(f"- target_audience: '{task.target_audience}' (length: {len(task.target_audience)})")
                logger.info(f"- products_services: '{task.products_services}' (length: {len(task.products_services)})")
                logger.info(f"- marketing_goals: '{task.marketing_goals}' (length: {len(task.marketing_goals)})")
                logger.info(f"- provider: '{task.provider}'")
                logger.info(f"- model: '{task.model}'")

                return task
            except Exception as e:
                logger.error(f"Error parsing marketing_form_data: {str(e)}", exc_info=True)
                # Fall back to parsing from the message
        else:
            logger.warning("No marketing_form_data found in any context location")

        # If context contains task parameters, use those
        if context and "task_params" in context:
            try:
                return MarketingTask(**context["task_params"])
            except Exception as e:
                logger.error(f"Error parsing task parameters from context: {str(e)}")
                # Fall back to parsing from the message

        # Extract file ID from context
        file_id = None
        has_data_source = False

        if context and "file_id" in context:
            file_id = context["file_id"]
            has_data_source = True
        elif context and "data_source" in context:
            data_source = context.get("data_source", {})
            has_data_source = True
            if data_source.get("type") == "file" and "id" in data_source:
                file_id = data_source["id"]

        # Check if this is the first message in the conversation
        is_first_conversation = True
        if context and "conversation_messages" in context:
            # If there are 0 or 1 messages (just the current one), it's the first conversation
            message_count = len(context["conversation_messages"])
            is_first_conversation = message_count <= 1
            logger.info(f"Conversation has {message_count} messages, is_first_conversation: {is_first_conversation}")
        else:
            # If we can't determine, assume it might be the first
            logger.info("Could not determine message count, assuming first conversation")

        # Otherwise, try to parse from the message
        task_type = "marketing_strategy"  # Default task type

        # Check for specific task types in the message
        message_lower = message.lower()
        if "campaign" in message_lower:
            task_type = "campaign_strategy"
        elif "social media" in message_lower:
            task_type = "social_media_content"
        elif "seo" in message_lower:
            task_type = "seo_optimization"
        elif "post" in message_lower:
            task_type = "post_composer"

        # Create a basic task with just the type
        # Check if we have marketing_form_data in the context but failed to parse it earlier
        if context and "marketing_form_data" in context:
            # Try one more time to use the form data
            try:
                form_data = context["marketing_form_data"]
                logger.info(f"Using marketing_form_data from fallback: {form_data}")

                # Get form values directly without adding defaults
                brand_description = form_data.get("brand_description", "")
                if not brand_description:
                    logger.warning("Brand description is empty in form data (fallback)")

                target_audience = form_data.get("target_audience", "")
                if not target_audience:
                    logger.warning("Target audience is empty in form data (fallback)")

                products_services = form_data.get("products_services", "")
                if not products_services:
                    logger.warning("Products/services is empty in form data (fallback)")

                marketing_goals = form_data.get("marketing_goals", "")
                if not marketing_goals:
                    logger.warning("Marketing goals is empty in form data (fallback)")

                existing_content = form_data.get("existing_content", "")
                if not existing_content:
                    logger.warning("Existing content is empty in form data (fallback)")

                keywords = form_data.get("keywords", "")
                if not keywords:
                    logger.warning("Keywords are empty in form data (fallback)")

                suggested_topics = form_data.get("suggested_topics", "")
                if not suggested_topics:
                    logger.warning("Suggested topics are empty in form data (fallback)")

                blog_topic = form_data.get("blog_topic", "")
                if not blog_topic:
                    logger.warning("Blog topic is empty in form data (fallback)")

                tone = form_data.get("tone", "Professional")
                if not tone:
                    logger.warning("Tone is empty in form data (fallback), using default: Professional")

                # Log all extracted values
                logger.info(f"EXTRACTED FORM VALUES (FALLBACK):")
                logger.info(f"- brand_description: '{brand_description}'")
                logger.info(f"- target_audience: '{target_audience}'")
                logger.info(f"- products_services: '{products_services}'")
                logger.info(f"- marketing_goals: '{marketing_goals}'")
                logger.info(f"- existing_content: '{existing_content}'")
                logger.info(f"- keywords: '{keywords}'")
                logger.info(f"- suggested_topics: '{suggested_topics}'")
                logger.info(f"- blog_topic: '{blog_topic}'")
                logger.info(f"- tone: '{tone}'")

                # Check for provider and model in form data
                provider = form_data.get("provider")
                model = form_data.get("model")

                task = MarketingTask(
                    task_type=form_data.get("content_type", task_type),
                    brand_description=brand_description,
                    target_audience=target_audience,
                    products_services=products_services,
                    marketing_goals=marketing_goals,
                    existing_content=existing_content,
                    keywords=keywords,
                    suggested_topics=suggested_topics,
                    blog_topic=blog_topic,
                    tone=tone,
                    file_id=file_id,
                    is_first_conversation=is_first_conversation,
                    has_data_source=has_data_source,
                    provider=provider,
                    model=model
                )
            except Exception as e:
                logger.error(f"Error in fallback parsing of marketing_form_data: {str(e)}")
                # If we still fail, create the basic task
                # Try to extract provider and model from context
                provider = None
                model = None

                if context:
                    if "provider" in context:
                        provider = context["provider"]
                    elif "metadata" in context and isinstance(context["metadata"], dict) and "provider" in context["metadata"]:
                        provider = context["metadata"]["provider"]

                    if "model" in context:
                        model = context["model"]
                    elif "metadata" in context and isinstance(context["metadata"], dict) and "model" in context["metadata"]:
                        model = context["metadata"]["model"]

                task = MarketingTask(
                    task_type=task_type,
                    brand_description="",
                    target_audience="",
                    products_services="",
                    marketing_goals="",
                    existing_content="",
                    keywords="",
                    suggested_topics="",
                    blog_topic="",
                    tone="Professional",
                    file_id=file_id,
                    is_first_conversation=is_first_conversation,
                    has_data_source=has_data_source,
                    provider=provider,
                    model=model
                )
        else:
            # No form data, create basic task
            # Try to extract provider and model from context
            provider = None
            model = None

            if context:
                if "provider" in context:
                    provider = context["provider"]
                elif "metadata" in context and isinstance(context["metadata"], dict) and "provider" in context["metadata"]:
                    provider = context["metadata"]["provider"]

                if "model" in context:
                    model = context["model"]
                elif "metadata" in context and isinstance(context["metadata"], dict) and "model" in context["metadata"]:
                    model = context["metadata"]["model"]

            task = MarketingTask(
                task_type=task_type,
                brand_description="",
                target_audience="",
                products_services="",
                marketing_goals="",
                existing_content="",
                keywords="",
                suggested_topics="",
                blog_topic="",
                tone="Professional",
                file_id=file_id,
                is_first_conversation=is_first_conversation,
                has_data_source=has_data_source,
                provider=provider,
                model=model
            )

        return task

    def _find_preserved_form_data(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Find preserved form data from context or conversation history.

        Args:
            context: Context dictionary

        Returns:
            Preserved form data if found, None otherwise
        """
        # First check if marketing_form_data is directly in the context
        if context and "marketing_form_data" in context:
            logger.info("Found marketing_form_data directly in context")
            return context["marketing_form_data"]

        # Check in metadata
        if context and "metadata" in context and isinstance(context["metadata"], dict):
            if "marketing_form_data" in context["metadata"]:
                logger.info("Found marketing_form_data in context.metadata")
                return context["metadata"]["marketing_form_data"]

        # Check in parent_context
        if context and "parent_context" in context and isinstance(context["parent_context"], dict):
            parent_context = context["parent_context"]
            if "marketing_form_data" in parent_context:
                logger.info("Found marketing_form_data in parent_context")
                return parent_context["marketing_form_data"]

            # Check parent_context.context
            if "context" in parent_context and isinstance(parent_context["context"], dict):
                if "marketing_form_data" in parent_context["context"]:
                    logger.info("Found marketing_form_data in parent_context.context")
                    return parent_context["context"]["marketing_form_data"]

        # Check conversation history for recent form data
        conversation_history = context.get("conversation_history", [])
        if conversation_history:
            logger.info(f"Searching conversation history with {len(conversation_history)} messages for form data")
            # Look through recent messages for marketing form data
            for msg in reversed(conversation_history):
                if msg.get("sender") == "user":
                    msg_metadata = msg.get("metadata", {})
                    if msg_metadata.get("marketing_form_data"):
                        logger.info("Found marketing_form_data in conversation history")
                        return msg_metadata["marketing_form_data"]

        logger.info("No preserved form data found")
        return None

    def _extract_content_type_from_message(self, message: str) -> str:
        """
        Extract content type from user message for new content requests.

        Args:
            message: User's message text

        Returns:
            Detected content type or default
        """
        message_lower = message.lower()

        # Extended content type detection with new extensible types
        content_type_keywords = {
            "brand_positioning": ["brand positioning", "positioning", "brand position", "messaging framework", "value proposition"],
            "competitive_analysis": ["competitive analysis", "competitor analysis", "competition analysis", "market analysis", "competitor research"],
            "content_calendar": ["content calendar", "editorial calendar", "content planning", "content schedule"],
            "email_marketing": ["email marketing", "email campaign", "newsletter", "email strategy", "email automation"],
            "campaign_strategy": ["campaign strategy", "campaign", "marketing campaign", "advertising campaign"],
            "social_media_content": ["social media", "social content", "social posts", "instagram", "facebook", "twitter", "linkedin"],
            "seo_optimization": ["seo", "search optimization", "search engine", "keyword strategy", "seo strategy"],
            "post_composer": ["post", "social post", "create post", "write post"],
            "marketing_strategy": ["marketing strategy", "strategy", "marketing plan", "strategic plan"],
            "influencer_marketing": ["influencer", "influencer marketing", "influencer strategy", "influencer campaign"],
            "pr_strategy": ["pr strategy", "public relations", "pr campaign", "media relations"],
            "product_launch": ["product launch", "launch strategy", "product marketing", "launch plan"],
            "customer_persona": ["customer persona", "buyer persona", "target persona", "audience persona"],
            "market_research": ["market research", "market study", "market insights", "consumer research"],
            "brand_strategy": ["brand strategy", "branding", "brand development", "brand identity"],
            "digital_marketing": ["digital marketing", "online marketing", "digital strategy", "web marketing"],
            "content_strategy": ["content strategy", "content marketing", "content plan"],
            "blog_content": ["blog content", "blog post", "blog article", "blog writing", "create blog", "generate blog"],
            "advertising_strategy": ["advertising strategy", "ad strategy", "advertising plan", "ad campaign"],
            "lead_generation": ["lead generation", "lead gen", "lead strategy", "lead nurturing"],
            "conversion_optimization": ["conversion optimization", "cro", "conversion strategy", "funnel optimization"]
        }

        # Check for specific content type keywords
        for content_type, keywords in content_type_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                logger.info(f"Detected content type '{content_type}' from keywords: {keywords}")
                return content_type

        # Check for generic strategy/analysis/plan requests
        if any(word in message_lower for word in ["strategy", "plan", "analysis", "research"]):
            # Try to extract the subject before these words
            import re

            # Look for patterns like "create a [subject] strategy"
            strategy_pattern = r"(?:create|generate|make|develop|build|write)\s+(?:a|an)?\s*([a-zA-Z\s]+?)\s+(?:strategy|plan|analysis|research)"
            match = re.search(strategy_pattern, message_lower)
            if match:
                subject = match.group(1).strip()
                # Convert to content type format
                content_type = subject.replace(" ", "_")
                logger.info(f"Extracted content type from pattern: '{content_type}' from subject: '{subject}'")
                return content_type

        # Default fallback
        logger.info("No specific content type detected, using default: marketing_strategy")
        return "marketing_strategy"


class MCPContentGeneratorComponent(AgentComponent):
    """Component for generating marketing content using MCP tools."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info(f"Initializing MCPContentGeneratorComponent with config: {config}")

    def _find_mcp_server_component(self, context: Dict[str, Any]):
        """Find the MCP server component from context or parent agent."""
        # First try to find it in the context
        for component in context.get("agent_components", []):
            if hasattr(component, 'call_tool'):
                return component

        # Then try to find it in parent agent if available
        if hasattr(self, 'parent_agent') and self.parent_agent:
            for component in self.parent_agent.components:
                if hasattr(component, 'call_tool'):
                    return component
        return None

    async def _generate_multilingual_response(self, context: Dict[str, Any], language: str, response_type: str, tone: str = "professional"):
        """
        Generate a contextual response in the user's language using LLM.

        Args:
            context: Context dictionary to update with response
            language: Target language code
            response_type: Type of response (acknowledgment, general, follow_up)
            tone: Response tone (professional, friendly, consultative, enthusiastic)
        """
        try:
            # Get MCP server for LLM calls
            mcp_server = None
            for component in context.get("agent_components", []):
                if hasattr(component, 'call_tool'):
                    mcp_server = component
                    break

            if not mcp_server:
                logger.error("No MCP server available for multilingual response generation")
                # Generate a basic response without LLM
                await self._generate_basic_fallback_response(context, "no_mcp_server")
                return

            # Create YAML-structured prompt for multilingual response generation
            language_names = {
                'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
                'it': 'Italian', 'pt': 'Portuguese', 'zh': 'Chinese', 'ja': 'Japanese',
                'ko': 'Korean', 'ar': 'Arabic', 'ru': 'Russian', 'hi': 'Hindi'
            }

            language_name = language_names.get(language, 'English')

            # Create YAML configuration for response generation
            response_config = {
                "task": "multilingual_response_generation",
                "input": {
                    "target_language": language,
                    "target_language_name": language_name,
                    "response_type": response_type,
                    "tone": tone,
                    "context": "marketing_assistant_conversation"
                },
                "response_requirements": {
                    "language_constraints": {
                        "use_only_target_language": True,
                        "no_english_if_not_target": True,
                        "maintain_cultural_appropriateness": True
                    },
                    "content_structure": {
                        "acknowledgment_type": {
                            "acknowledge_previous_interaction": True,
                            "offer_continued_assistance": True,
                            "list_specific_services": ["strategy", "campaigns", "content", "social_media", "SEO"],
                            "ask_next_steps": True,
                            "use_bullet_points": True,
                            "keep_concise": True
                        } if response_type == "acknowledgment" else {
                            "introduction_type": {
                                "introduce_as_marketing_expert": True,
                                "list_key_services": ["strategy", "campaigns", "content", "social_media", "SEO", "advertising"],
                                "ask_about_challenges": True,
                                "maintain_engaging_tone": True,
                                "use_bullet_points": True,
                                "keep_professional": True
                            }
                        }
                    },
                    "tone_guidelines": {
                        "primary_tone": tone,
                        "maintain_helpfulness": True,
                        "show_expertise": True,
                        "encourage_engagement": True
                    }
                },
                "output_format": {
                    "type": "direct_text",
                    "language": language_name,
                    "no_metadata": True,
                    "ready_for_user": True
                }
            }

            # Create a concise prompt instead of large YAML dump
            prompt = f"""Generate a marketing assistant {response_type} response in {language_name} with a {tone} tone.

Be helpful, professional, and showcase marketing expertise. Respond naturally and conversationally."""

            # Generate response using LLM
            tool_result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "multilingual_response_generation",
                    "target_language": language,
                    "response_type": response_type,
                    "tone": tone,
                    "response_config": response_config  # Move config to user_context
                },
                "intent_type": "content_generation",
                "confidence": 1.0,
                "temperature": 0.7,  # Moderate creativity for natural responses
                "max_tokens": 300
            })

            if tool_result.get("isError", False):
                logger.error(f"LLM response generation failed: {tool_result}")
                await self._generate_basic_fallback_response(context, "llm_generation_failed")
                return

            # Extract the generated response
            response_text = tool_result.get("content", [{}])[0].get("text", "")

            if response_text.strip():
                context["response"] = response_text.strip()
                logger.info(f"Generated {language} response ({tone} tone): {response_text[:100]}...")
            else:
                logger.warning("Empty response from LLM")
                await self._generate_basic_fallback_response(context, "empty_llm_response")

        except Exception as e:
            logger.error(f"Error generating multilingual response: {e}")
            context["response"] = "I apologize, but I'm experiencing technical difficulties. Please try again in a moment."



    def _extract_provider_and_model(self, context: Dict[str, Any], task: MarketingTask) -> Tuple[str, str]:
        """Extract provider and model from various context locations."""
        provider = None
        model = None

        # Check direct context
        provider = context.get("provider")
        model = context.get("model")

        # Check metadata
        if (not provider or not model) and "metadata" in context:
            metadata = context.get("metadata", {})
            if not provider and "provider" in metadata:
                provider = metadata.get("provider")
                logger.info(f"Found provider in metadata: {provider}")
            if not model and "model" in metadata:
                model = metadata.get("model")
                logger.info(f"Found model in metadata: {model}")

        # Check marketing_form_data in context
        if (not provider or not model) and "marketing_form_data" in context:
            form_data = context.get("marketing_form_data", {})
            if not provider and "provider" in form_data:
                provider = form_data.get("provider")
                logger.info(f"Found provider in marketing_form_data: {provider}")
            if not model and "model" in form_data:
                model = form_data.get("model")
                logger.info(f"Found model in marketing_form_data: {model}")

        # Check marketing_form_data in metadata
        if (not provider or not model) and "metadata" in context and "marketing_form_data" in context["metadata"]:
            form_data = context["metadata"].get("marketing_form_data", {})
            if not provider and "provider" in form_data:
                provider = form_data.get("provider")
                logger.info(f"Found provider in metadata.marketing_form_data: {provider}")
            if not model and "model" in form_data:
                model = form_data.get("model")
                logger.info(f"Found model in metadata.marketing_form_data: {model}")

        # Check task attributes
        if (not provider or not model):
            if not provider and hasattr(task, 'provider') and task.provider:
                provider = task.provider
                logger.info(f"Found provider in task: {provider}")
            if not model and hasattr(task, 'model') and task.model:
                model = task.model
                logger.info(f"Found model in task: {model}")

        # Fall back to agent configuration
        if not provider or not model:
            agent_config = context.get("agent_config", {})
            if not provider:
                provider = agent_config.get("provider", "groq")
                logger.info(f"Using default provider from agent_config: {provider}")
            if not model:
                model = agent_config.get("model", "llama3-70b-8192")
                logger.info(f"Using default model from agent_config: {model}")

        return provider, model

    def _prepare_tool_parameters(self, task: MarketingTask, provider: str, model: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare tool parameters with validation and enhancement."""
        # Check if this is a regeneration request
        is_regeneration = (
            context.get("is_regeneration", False) or
            (context.get("metadata", {}).get("is_regeneration", False) if isinstance(context.get("metadata"), dict) else False)
        )

        # For regeneration, use a slightly higher temperature for more variation
        temperature = 0.8 if is_regeneration else 0.7

        # For blog content, use selected blog topics if available, otherwise fall back to suggested topics
        topics_to_use = task.suggested_topics
        if task.task_type == "blog_content" and task.blog_topic:
            topics_to_use = task.blog_topic
            logger.info(f"Using selected blog topics for blog content generation: '{task.blog_topic}'")
        elif task.suggested_topics:
            logger.info(f"Using suggested topics: '{task.suggested_topics}'")

        # Base parameters
        tool_params = {
            "content_type": task.task_type,
            "brand_description": task.brand_description,
            "target_audience": task.target_audience,
            "products_services": task.products_services,
            "marketing_goals": task.marketing_goals,
            "existing_content": task.existing_content,
            "keywords": task.keywords,
            "suggested_topics": topics_to_use,
            "tone": task.tone,
            "provider": provider,
            "model": model,
            "temperature": temperature,
            "is_first_conversation": task.is_first_conversation,
            "has_data_source": task.has_data_source,
            "is_regeneration": is_regeneration
        }

        # Add file data if available
        if context.get("file_data"):
            tool_params["file_data"] = context["file_data"]
            logger.info("Added file data to tool parameters")

        return tool_params

    def _process_tool_result(self, tool_result: Dict[str, Any]) -> str:
        """Process and validate tool result."""
        if tool_result.get("isError", False):
            error_content = tool_result.get("content", [{"text": "Unknown error"}])
            error_message = error_content[0].get("text", "Unknown error") if error_content else "Unknown error"
            raise ValueError(error_message)

        # Extract the generated content
        content_items = tool_result.get("content", [])
        result = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])

        if not result.strip():
            raise ValueError("No content was generated by the marketing tool")

        return result

    def _update_context_with_result(self, context: Dict[str, Any], result: str, task: MarketingTask, provider: str, model: str):
        """Update context with successful generation result."""
        context["response"] = result

        # Ensure metadata exists
        if "metadata" not in context:
            context["metadata"] = {}

        context["metadata"].update({
            "task_type": task.task_type,
            "is_first_conversation": task.is_first_conversation,
            "has_data_source": task.has_data_source,
            "generated_content": True,
            "content_generation_completed": True,  # Flag for conversation state detection
            "marketing_content_generated": True,   # Additional flag for content detection
            "content_type": task.task_type,        # Store content type for follow-up detection
            "persona": "marketing",
            "agent_id": "composable-marketing-ai",
            "response": result,  # Include the response in metadata for visualization
            "content": result,   # Also include as content for backward compatibility
            "provider": provider,
            "model": model,
            # Include the marketing form data in the response metadata
            "marketing_form_data": {
                "content_type": task.task_type,
                "brand_description": task.brand_description,
                "target_audience": task.target_audience,
                "products_services": task.products_services,
                "marketing_goals": task.marketing_goals,
                "existing_content": task.existing_content,
                "keywords": task.keywords,
                "suggested_topics": task.suggested_topics,
                "blog_topic": task.blog_topic,
                "tone": task.tone,
                "provider": provider,
                "model": model
            },
            # CRITICAL: Add conversational state flags that will be saved to database
            # These flags will be detected by the conversation state checker
            "conversational_state": {
                "content_generated": True,
                "next_message_should_be_conversational": True,
                "content_type": task.task_type,
                "generation_timestamp": time.time(),
                "tool_completed": True,
                "auto_return_to_conversational": True
            },
            # Tool completion metadata for UI indicators
            "tool_execution": {
                "tool_name": "generate_marketing_content",
                "status": "completed",
                "completion_time": time.time(),
                "result_type": "content_generation"
            }
        })

    def _handle_generation_error(self, context: Dict[str, Any], error: Exception):
        """Handle generation errors with proper context updates."""
        error_message = f"I encountered an error generating your marketing content: {str(error)}"

        # Check for specific error types and provide better messages
        if "connection" in str(error).lower() or "timeout" in str(error).lower():
            error_message = "I'm having trouble connecting to the AI provider. Please try again in a moment."
        elif "rate limit" in str(error).lower():
            error_message = "The AI provider is currently rate limiting requests. Please try again in a few minutes."
        elif "authentication" in str(error).lower() or "api key" in str(error).lower():
            error_message = "There's an issue with the AI provider authentication. Please contact support."

        context["response"] = error_message

        # Ensure metadata exists
        if "metadata" not in context:
            context["metadata"] = {}

        context["metadata"].update({
            "error": "content_generation_error",
            "error_details": str(error),
            "error_type": error.__class__.__name__,
            "component": "MCPContentGeneratorComponent"
        })

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Log the incoming context to see what we're working with
        logger.info("=== MCP CONTENT GENERATOR COMPONENT - INCOMING CONTEXT ===")
        logger.info(f"Context keys: {list(context.keys())}")
        logger.info(f"Skip marketing content generation: {context.get('skip_marketing_content_generation', False)}")
        logger.info(f"Is conversational: {context.get('is_conversational', False)}")
        logger.info(f"Is follow-up question: {context.get('is_follow_up_question', False)}")
        logger.info(f"Detected marketing request: {context.get('detected_marketing_request', False)}")
        logger.info(f"Has marketing task: {'marketing_task' in context}")
        if 'marketing_task' in context:
            task_dict = context.get('marketing_task', {})
            logger.info(f"Marketing task type: {task_dict.get('task_type', 'unknown')}")
        logger.info("=== END INCOMING CONTEXT ===")

        # DEBUGGING: Log the message to understand what triggered this
        message = context.get("message", "")
        logger.info(f"Processing message: '{message[:100]}...'")

        # DEBUGGING: Check conversation history for context
        conversation_history = context.get("conversation_history", [])
        if conversation_history:
            recent_messages = conversation_history[-3:]  # Last 3 messages
            logger.info(f"Recent conversation context ({len(recent_messages)} messages):")
            for i, msg in enumerate(recent_messages):
                sender = msg.get("sender", "unknown")
                content = msg.get("content", "")[:50]
                logger.info(f"  {i+1}. {sender}: {content}...")
        else:
            logger.info("No conversation history available")

        # PRIORITY CHECK: Skip content generation for conversational messages
        should_skip = (
            context.get("skip_marketing_content_generation", False) or
            (context.get("is_conversational", False) and not context.get("detected_marketing_request", False)) or
            (context.get("is_follow_up_question", False) and not context.get("detected_marketing_request", False))
        )

        if should_skip:
            logger.info("SKIPPING content generation - this is a conversational message, not a content generation request")
            return await self._handle_skip_content_generation(context)

        # Get the marketing task from the context
        task_dict = context.get("marketing_task", {})
        if not task_dict:
            logger.info("NO marketing_task found in context - handling as conversational message")
            # If no task is found, treat it as conversational
            context["skip_marketing_content_generation"] = True
            return await self._handle_skip_content_generation(context)

        logger.info(f"FOUND marketing_task in context: {task_dict}")

        # Convert to MarketingTask object
        try:
            task = MarketingTask(**task_dict)
        except Exception as e:
            logger.error(f"Error creating MarketingTask object: {e}")
            context["response"] = "Invalid marketing task data provided."
            context["metadata"] = {"error": "invalid_task_data", "error_details": str(e)}
            return context

        # Check if this is a conversational task (should not generate content)
        if task.task_type == "conversational":
            logger.info("Task type is 'conversational' - treating as conversational message")
            context["skip_marketing_content_generation"] = True
            return await self._handle_skip_content_generation(context)

        # Log the task that was loaded
        logger.info("MCP CONTENT GENERATOR COMPONENT - LOADED TASK:")
        logger.info(f"Task type: {task.task_type}")
        logger.info(f"Brand description: '{task.brand_description}'")
        logger.info(f"Target audience: '{task.target_audience}'")

        # Find MCP server component
        mcp_server = self._find_mcp_server_component(context)
        if not mcp_server:
            logger.error("MCP server component not found")
            context["response"] = "MCP server component not found. Cannot execute marketing tasks."
            context["metadata"] = {"error": "mcp_server_not_found"}
            return context

        logger.info("Found MCP server component - proceeding with content generation")

        # Generate the marketing content
        try:
            # Extract provider and model
            provider, model = self._extract_provider_and_model(context, task)
            logger.info(f"Using provider: {provider}, model: {model} for content generation")

            # Prepare tool parameters
            tool_params = self._prepare_tool_parameters(task, provider, model, context)

            # Debug log the parameters being sent to the tool
            logger.info(f"CALLING CONTENT GENERATION TOOL WITH PARAMETERS:")
            logger.info(f"- content_type: {task.task_type}")
            logger.info(f"- provider: {provider}")
            logger.info(f"- model: {model}")

            # Call the MCP marketing content generation tool
            tool_result = await mcp_server.call_tool("generate_marketing_content", tool_params)

            # Process the tool result
            result = self._process_tool_result(tool_result)

            # Update the context with the generated content
            self._update_context_with_result(context, result, task, provider, model)

            # CRITICAL: Reset context state after content generation to enable conversational follow-ups
            # This ensures that subsequent messages are treated as conversational rather than content generation
            logger.info("=== BEFORE CONTEXT RESET ===")
            logger.info(f"Context keys: {list(context.keys())}")
            logger.info(f"Context routing keys: current_persona={context.get('current_persona')}, persona_id={context.get('persona_id')}, agent_id={context.get('agent_id')}")
            logger.info(f"Context generation flags: marketing_task={'marketing_task' in context}, detected_marketing_request={context.get('detected_marketing_request')}")

            self._reset_context_for_conversational_mode(context)

            logger.info("=== AFTER CONTEXT RESET ===")
            logger.info(f"Context keys: {list(context.keys())}")
            logger.info(f"Context routing keys: current_persona={context.get('current_persona')}, persona_id={context.get('persona_id')}, agent_id={context.get('agent_id')}")
            logger.info(f"Context generation flags: marketing_task={'marketing_task' in context}, detected_marketing_request={context.get('detected_marketing_request')}")
            logger.info(f"Context conversational flags: skip_marketing_content_generation={context.get('skip_marketing_content_generation')}, is_conversational={context.get('is_conversational')}")

            logger.info(f"Successfully generated {task.task_type} content: {len(result)} characters")

        except Exception as e:
            logger.error(f"Error generating marketing content: {str(e)}", exc_info=True)
            self._handle_generation_error(context, e)

        return context

    async def _handle_skip_content_generation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle cases where content generation should be skipped using LLM for all responses."""
        logger.info("Skipping marketing content generation as requested by parser component")

        # Get message and conversation context
        message = context.get("message", "")
        conversation_history = context.get("conversation_history", [])

        # Determine the type of response needed
        response_type = "general_conversation"
        if context.get("is_initial_greeting", False):
            response_type = "initial_greeting"
        elif any(phrase in message.lower() for phrase in ["what can you do", "what can you help", "capabilities", "services"]):
            response_type = "capability_inquiry"
        elif any(phrase in message.lower() for phrase in ["improve it", "enhance it", "make it better", "refine it"]):
            response_type = "content_improvement_request"
        elif context.get("is_conversational", False) or context.get("is_follow_up_question", False):
            response_type = "conversational_follow_up"

        # Use LLM to generate appropriate response
        await self._generate_llm_conversation_response(context, message, response_type, conversation_history)

        context["metadata"] = {
            "conversational_response": True,
            "persona": "marketing",
            "agent_id": "composable-marketing-ai",
            "response_type": response_type
        }
        return context

    async def _generate_llm_conversation_response(self, context: Dict[str, Any], message: str, response_type: str, conversation_history: list):
        """Generate conversational responses using LLM for all scenarios."""
        try:
            # Get MCP server for LLM calls
            mcp_server = None
            for component in context.get("agent_components", []):
                if hasattr(component, 'call_tool') and component.__class__.__name__ == "MCPServerComponent":
                    mcp_server = component
                    break

            if not mcp_server:
                logger.error("No MCP server available for conversation response generation")
                context["response"] = "I'm here to help with your marketing needs. How can I assist you today?"
                return

            # Get language and tone from LLM analysis if available
            llm_analysis = context.get("llm_analysis", {})
            language = llm_analysis.get("language", "en")
            tone = llm_analysis.get("suggested_response_tone", "professional")

            # Create conversation context
            history_text = ""
            if conversation_history:
                recent_messages = conversation_history[-3:]  # Last 3 messages for context
                history_text = "\n".join([
                    f"{msg.get('sender', 'unknown')}: {msg.get('content', '')}"
                    for msg in recent_messages
                ])

            # Create comprehensive prompt based on response type
            conversation_config = {
                "task": "marketing_conversation_response",
                "input": {
                    "user_message": message,
                    "response_type": response_type,
                    "conversation_history": history_text,
                    "language": language,
                    "tone": tone
                },
                "persona": {
                    "role": "Composable Marketer",
                    "expertise": ["marketing strategy", "campaign planning", "social media", "SEO", "content creation"],
                    "personality": "helpful, knowledgeable, professional, approachable",
                    "communication_style": "conversational yet expert"
                },
                "response_guidelines": {
                    "initial_greeting": {
                        "introduce_self": True,
                        "list_capabilities": ["Marketing Strategy Development", "Campaign Planning", "Social Media Content", "SEO Optimization"],
                        "offer_quick_actions": ["Create Marketing Strategy", "Develop Campaign Plan", "Generate Social Media Content", "SEO Optimization"],
                        "mention_data_attachment": True,
                        "ask_about_challenges": True
                    } if response_type == "initial_greeting" else {},
                    "capability_inquiry": {
                        "provide_detailed_capabilities": True,
                        "use_structured_format": True,
                        "include_examples": True,
                        "ask_specific_needs": True
                    } if response_type == "capability_inquiry" else {},
                    "conversational_follow_up": {
                        "acknowledge_previous_context": True,
                        "provide_helpful_suggestions": True,
                        "maintain_marketing_focus": True,
                        "ask_follow_up_questions": True
                    } if response_type == "conversational_follow_up" else {},
                    "content_improvement_request": {
                        "acknowledge_request": True,
                        "explain_content_generation_process": True,
                        "suggest_specific_actions": True,
                        "offer_alternatives": True
                    } if response_type == "content_improvement_request" else {},
                    "general_conversation": {
                        "be_helpful_and_engaging": True,
                        "focus_on_marketing_expertise": True,
                        "provide_value": True,
                        "encourage_next_steps": True
                    }
                },
                "output_requirements": {
                    "language": language,
                    "tone": tone,
                    "format": "conversational_text",
                    "length": "concise_but_comprehensive",
                    "include_actionable_suggestions": True
                }
            }

            # Create a concise prompt instead of large YAML dump
            prompt = f"""As the Composable Marketer, provide a {response_type} response to the user's message: "{message}"

Respond naturally and helpfully, showcasing marketing expertise. Use {language} language with a {tone} tone. Be conversational yet professional."""

            # Generate response using LLM
            from agents.utils import create_agent_context

            # Include the conversation config details in user_context instead of the message
            user_context = create_agent_context(
                agent_id="composable-marketing-ai",
                additional_context={
                    "task": "conversation_response",
                    "response_type": response_type,
                    "language": language,
                    "tone": tone,
                    "is_marketing_agent": True,
                    "persona_details": conversation_config.get("persona", {}),
                    "response_guidelines": conversation_config.get("response_guidelines", {}),
                    "output_requirements": conversation_config.get("output_requirements", {})
                }
            )

            tool_result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": conversation_history[-5:] if conversation_history else [],
                "user_context": user_context,
                "intent_type": "marketing_advice",
                "confidence": 1.0,
                "provider": context.get("provider", "groq"),
                "model": context.get("model", "llama3-70b-8192"),
                "temperature": 0.7,
                "max_tokens": 500
            })

            if tool_result.get("isError", False):
                logger.error(f"LLM conversation response generation failed: {tool_result}")
                await self._generate_basic_fallback_response(context, "conversation_generation_failed")
                return

            # Extract the generated response
            response_text = tool_result.get("content", [{}])[0].get("text", "")

            if response_text.strip():
                context["response"] = response_text.strip()
                logger.info(f"Generated conversation response ({response_type}): {response_text[:100]}...")
            else:
                logger.warning("Empty response from LLM for conversation")
                await self._generate_basic_fallback_response(context, "empty_conversation_response")

        except Exception as e:
            logger.error(f"Error generating LLM conversation response: {e}")
            context["response"] = "I'm here to help with your marketing needs. How can I assist you today?"

    async def _handle_conversational_response(self, context: Dict[str, Any], message: str) -> Dict[str, Any]:
        """Handle conversational responses with better context awareness."""
        # Get conversation history from multiple possible locations
        conversation_history = []
        if "conversation_history" in context:
            conversation_history = context["conversation_history"]
        elif "context" in context and "conversation_history" in context["context"]:
            conversation_history = context["context"]["conversation_history"]

        logger.info(f"Checking conversation history for recent marketing content. History length: {len(conversation_history)}")

        has_recent_marketing_content = False
        recent_content_type = None

        # Look for recently generated marketing content or capability responses
        for msg in reversed(conversation_history[-5:]):
            logger.info(f"Checking message: sender={msg.get('sender')}, metadata={msg.get('metadata', {})}")

            # Check for AI messages that contain marketing content or capabilities
            if msg.get("sender") == "ai":
                content = msg.get("content", "").lower()
                metadata = msg.get("metadata", {})

                # Check for actual generated content OR capability responses that indicate marketing context
                is_marketing_content = (
                    metadata.get("generated_content", False) or  # Actual generated content
                    metadata.get("capability_response", False) or  # Capability response
                    metadata.get("persona") == "marketing" or  # Marketing persona response
                    "marketing strategy" in content or
                    "campaign plan" in content or
                    "social media content" in content or
                    "seo optimization" in content
                )

                if is_marketing_content:
                    has_recent_marketing_content = True
                    logger.info(f"Found recent marketing content in conversation")

                    # Try to determine what type of content was discussed
                    if "marketing strategy" in content or "comprehensive marketing" in content:
                        recent_content_type = "strategy"
                    elif "campaign" in content:
                        recent_content_type = "campaign"
                    elif "social media" in content:
                        recent_content_type = "social_media"
                    elif "seo" in content:
                        recent_content_type = "seo"
                    break

        logger.info(f"Has recent marketing content: {has_recent_marketing_content}, content type: {recent_content_type}")

        # Enhanced intent detection for follow-up requests
        message_lower = message.lower()

        # Check for explicit content generation requests (these should NOT be conversational)
        explicit_content_generation_phrases = [
            "create a", "generate a", "develop a", "build a", "write a", "make a",
            "create marketing", "generate marketing", "develop marketing", "build marketing",
            "marketing strategy for", "campaign for", "social media strategy", "seo strategy",
            "content strategy", "advertising strategy", "brand strategy"
        ]

        # Check for conversational follow-up phrases (these should be conversational)
        conversational_follow_up_phrases = [
            "what do you think", "any thoughts", "what would you recommend", "what's your opinion",
            "how does that sound", "what else", "any other ideas", "what about", "tell me more",
            "explain", "clarify", "help me understand", "what does that mean", "how would",
            "why", "when", "where", "who", "which", "thanks", "thank you", "great", "good",
            "interesting", "helpful", "useful", "perfect", "excellent", "awesome"
        ]

        # Check for general questions (these should be conversational)
        general_question_phrases = [
            "how do i", "how can i", "what should i", "where do i", "when should i",
            "why should i", "which is better", "what's the difference", "how does",
            "what are", "can you", "could you", "would you", "do you"
        ]

        is_explicit_content_request = any(phrase in message_lower for phrase in explicit_content_generation_phrases)
        is_conversational_follow_up = any(phrase in message_lower for phrase in conversational_follow_up_phrases)
        is_general_question = any(phrase in message_lower for phrase in general_question_phrases)

        logger.info(f"Intent analysis for message '{message}':")
        logger.info(f"  - Is explicit content request: {is_explicit_content_request}")
        logger.info(f"  - Is conversational follow-up: {is_conversational_follow_up}")
        logger.info(f"  - Is general question: {is_general_question}")

        # If this is an explicit content generation request, remove skip flag and allow content generation
        if is_explicit_content_request:
            logger.info("Detected explicit content generation request - removing skip flag to allow content generation")
            context["skip_marketing_content_generation"] = False
            context["is_conversational"] = False
            context["detected_marketing_request"] = True
            context["response"] = ""  # Clear any existing response to allow content generation

            # Ensure we have a marketing task for content generation
            if not context.get("marketing_task"):
                logger.info("Creating basic marketing task for explicit content request")
                # Parse the message to determine task type
                message_lower = message.lower()
                task_type = "marketing_strategy"  # Default

                if "campaign" in message_lower:
                    task_type = "campaign_strategy"
                elif "social media" in message_lower:
                    task_type = "social_media_content"
                elif "seo" in message_lower:
                    task_type = "seo_optimization"
                elif "post" in message_lower:
                    task_type = "post_composer"

                # Create a basic task from the message
                basic_task = MarketingTask(
                    task_type=task_type,
                    brand_description="",
                    target_audience="",
                    products_services="",
                    marketing_goals="",
                    existing_content="",
                    keywords="",
                    suggested_topics="",
                    blog_topic="",
                    tone="Professional",
                    file_id=None,
                    is_first_conversation=False,
                    has_data_source=False,
                    provider=context.get("provider"),
                    model=context.get("model")
                )

                context["marketing_task"] = basic_task.model_dump()
                logger.info(f"Created basic marketing task for explicit request: {task_type}")

            return context

        # For all other cases (conversational follow-ups, general questions, etc.), provide conversational responses
        if has_recent_marketing_content:
            if is_conversational_follow_up:
                logger.info(f"Providing contextual follow-up suggestions for content type: {recent_content_type}")
                await self._generate_llm_follow_up_suggestions(context, message, recent_content_type)
            else:
                # Provide general acknowledgment for other conversational responses
                logger.info("Providing general acknowledgment response")

                # Use LLM analysis results if available, otherwise detect language
                llm_analysis = context.get("llm_analysis", {})
                detected_language = llm_analysis.get("language", "en")
                suggested_tone = llm_analysis.get("suggested_response_tone", "professional")

                logger.info(f"Using detected language: {detected_language}, tone: {suggested_tone}")

                # Generate response using LLM in the user's language
                await self._generate_multilingual_response(context, detected_language, "acknowledgment", suggested_tone)

        else:
            # General conversational response when no recent marketing content
            logger.info("No recent marketing content found, providing general response")

            # Use LLM analysis results if available, otherwise detect language
            llm_analysis = context.get("llm_analysis", {})
            detected_language = llm_analysis.get("language", "en")
            suggested_tone = llm_analysis.get("suggested_response_tone", "friendly")

            logger.info(f"Using detected language: {detected_language}, tone: {suggested_tone}")

            # Generate response using LLM in the user's language
            await self._generate_multilingual_response(context, detected_language, "general", suggested_tone)

        context["metadata"] = {
            "conversational_response": True,
            "persona": "marketing",
            "agent_id": "composable-marketing-ai"
        }
        return context

    async def _generate_llm_follow_up_suggestions(self, context: Dict[str, Any], message: str, content_type: str = None):
        """Generate contextual follow-up suggestions using LLM based on recent content type."""
        try:
            # Get MCP server for LLM calls
            mcp_server = None
            for component in context.get("agent_components", []):
                if hasattr(component, 'call_tool'):
                    mcp_server = component
                    break

            if not mcp_server:
                logger.error("No MCP server available for follow-up suggestions")
                context["response"] = "I'd be happy to provide more marketing recommendations. What specific area would you like to explore further?"
                return

            # Get language and tone from LLM analysis if available
            llm_analysis = context.get("llm_analysis", {})
            language = llm_analysis.get("language", "en")
            tone = llm_analysis.get("suggested_response_tone", "professional")

            # Create configuration for follow-up suggestions
            suggestions_config = {
                "task": "marketing_follow_up_suggestions",
                "input": {
                    "user_message": message,
                    "previous_content_type": content_type or "general",
                    "language": language,
                    "tone": tone
                },
                "suggestion_categories": {
                    "analytics_optimization": {
                        "focus": "measurement and improvement",
                        "examples": ["conversion tracking", "A/B testing", "competitor analysis"]
                    },
                    "partnership_collaboration": {
                        "focus": "external relationships",
                        "examples": ["influencer partnerships", "cross-promotion", "industry communities"]
                    },
                    "advanced_targeting": {
                        "focus": "audience refinement",
                        "examples": ["lookalike audiences", "retargeting", "personalization"]
                    },
                    "growth_strategies": {
                        "focus": "scaling and expansion",
                        "examples": ["referral programs", "lead magnets", "funnel optimization"]
                    }
                },
                "content_specific_additions": {
                    "strategy": ["implementation timelines", "budget allocation", "performance frameworks"],
                    "campaign": ["creative briefs", "asset libraries", "tracking attribution"],
                    "social_media": ["content calendars", "community management", "social listening"],
                    "seo": ["technical audits", "optimization checklists", "link building"]
                },
                "response_requirements": {
                    "language": language,
                    "tone": tone,
                    "structure": "organized_with_emojis",
                    "include_actionable_items": True,
                    "ask_for_next_steps": True,
                    "be_encouraging": True
                }
            }

            # Create a concise prompt instead of large YAML dump
            prompt = f"""Generate marketing follow-up suggestions for the user's message: "{message}"

Previous content type: {content_type or 'general'}
Provide helpful, actionable marketing recommendations that build on the previous conversation. Use a {tone} tone in {language} language and organize suggestions clearly. End by asking what the user would like to explore further."""

            # Generate suggestions using LLM
            tool_result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "follow_up_suggestions",
                    "content_type": content_type,
                    "language": language,
                    "tone": tone,
                    "suggestion_categories": suggestions_config.get("suggestion_categories", {}),
                    "content_specific_additions": suggestions_config.get("content_specific_additions", {}),
                    "response_requirements": suggestions_config.get("response_requirements", {})
                },
                "intent_type": "marketing_advice",
                "confidence": 1.0,
                "provider": context.get("provider", "groq"),
                "model": context.get("model", "llama3-70b-8192"),
                "temperature": 0.7,
                "max_tokens": 600
            })

            if tool_result.get("isError", False):
                logger.error(f"LLM follow-up suggestions generation failed: {tool_result}")
                context["response"] = "I'd be happy to provide more marketing recommendations. What specific area would you like to explore further?"
                return

            # Extract the generated suggestions
            response_text = tool_result.get("content", [{}])[0].get("text", "")

            if response_text.strip():
                context["response"] = response_text.strip()
                logger.info(f"Generated follow-up suggestions for {content_type}: {response_text[:100]}...")
            else:
                logger.warning("Empty response from LLM for follow-up suggestions")
                context["response"] = "I'd be happy to provide more marketing recommendations. What specific area would you like to explore further?"

        except Exception as e:
            logger.error(f"Error generating LLM follow-up suggestions: {e}")
            await self._generate_basic_fallback_response(context, "follow_up_suggestions_failed")

    async def _generate_basic_fallback_response(self, context: Dict[str, Any], scenario: str):
        """Generate basic fallback responses when LLM is not available."""
        fallback_responses = {
            "no_mcp_server": "I'm experiencing technical difficulties. Please try again in a moment.",
            "llm_generation_failed": "I'm having trouble generating a response right now. Please try again.",
            "empty_llm_response": "I'm here to help with your marketing needs. How can I assist you today?",
            "conversation_generation_failed": "I'm here to help with your marketing needs. How can I assist you today?",
            "empty_conversation_response": "I'm here to help with your marketing needs. How can I assist you today?",
            "follow_up_suggestions_failed": "I'd be happy to provide more marketing recommendations. What specific area would you like to explore further?"
        }

        context["response"] = fallback_responses.get(scenario, "I'm here to help with your marketing needs. How can I assist you today?")

    def _reset_context_for_conversational_mode(self, context: Dict[str, Any]) -> None:
        """
        Reset context state after tool completion to enable conversational follow-ups.

        This method implements the automatic tool-to-conversation transition by:
        1. Clearing tool execution flags and state
        2. Preserving ALL routing and persona information
        3. Setting conversational mode flags
        4. Adding tool completion metadata for UI indicators

        This ensures that subsequent messages stay with the marketing agent but are
        treated as conversational rather than triggering new tool calls.

        Args:
            context: Context dictionary to reset
        """
        logger.info("🔄 TOOL COMPLETION: Resetting context state for conversational mode after tool execution")

        # CRITICAL: PRESERVE ALL ROUTING INFORMATION
        # The routing component looks for these keys to maintain persona continuity:
        # - current_persona, persona_id, agent_id (in context root)
        # - persona, agent_id (in metadata)

        # Ensure routing keys are preserved at context root level
        if not context.get("current_persona"):
            context["current_persona"] = "composable-marketing-ai"
        if not context.get("persona_id"):
            context["persona_id"] = "composable-marketing-ai"
        if not context.get("agent_id"):
            context["agent_id"] = "composable-marketing-ai"

        logger.info(f"Preserved routing keys: current_persona={context.get('current_persona')}, "
                   f"persona_id={context.get('persona_id')}, agent_id={context.get('agent_id')}")

        # Clear ONLY tool execution flags that would trigger new tool calls
        context.pop("detected_marketing_request", None)
        context.pop("marketing_task", None)
        context.pop("is_regeneration", None)
        context.pop("should_use_tools", None)
        context.pop("tool_call_request", None)

        # IMPORTANT: Do NOT clear marketing_form_data completely as it may be needed for context
        # Instead, mark it as used so it doesn't trigger automatic content generation
        if "marketing_form_data" in context:
            context["marketing_form_data_used"] = True
            logger.info("Marked marketing_form_data as used to prevent regeneration")

        # Set conversational mode flags for next message processing
        context["skip_marketing_content_generation"] = True
        context["is_conversational"] = True
        context["tool_completed"] = True
        context["auto_conversational_mode"] = True

        # Add tool completion metadata that will be saved to database
        if "metadata" not in context:
            context["metadata"] = {}

        context["metadata"].update({
            "tool_completion_reset": True,
            "auto_conversational_mode": True,
            "tool_completion_timestamp": time.time(),
            "next_message_mode": "conversational"
        })

        logger.info("✅ TOOL COMPLETION: Context reset complete - next messages will be conversational")
        logger.info(f"🔄 Conversational flags set: skip_content_generation={context.get('skip_marketing_content_generation')}, "
                   f"is_conversational={context.get('is_conversational')}, tool_completed={context.get('tool_completed')}")

        # Set conversational flags to ensure follow-ups are handled conversationally
        context["skip_marketing_content_generation"] = True
        context["is_conversational"] = True
        context["is_follow_up_question"] = False
        context["content_generation_completed"] = True  # Flag to indicate content was just generated

        # Clear any cached LLM analysis that might influence next message processing
        context.pop("llm_analysis", None)
        context.pop("llm_intent", None)

        # PRESERVE ESSENTIAL ROUTING AND PERSONA METADATA
        if "metadata" in context:
            # Ensure persona and routing information is preserved in metadata
            context["metadata"]["persona"] = "marketing"
            context["metadata"]["agent_id"] = "composable-marketing-ai"

            # Remove only processing-related metadata that might interfere with follow-ups
            context["metadata"].pop("is_regeneration", None)
            # Keep marketing_form_data in metadata for context but mark as used
            if "marketing_form_data" in context["metadata"]:
                context["metadata"]["marketing_form_data_used"] = True
        else:
            # Ensure metadata exists with routing information
            context["metadata"] = {
                "persona": "marketing",
                "agent_id": "composable-marketing-ai"
            }

        logger.info("Context reset completed - ALL routing information preserved, subsequent messages will be conversational")

    async def _handle_missing_marketing_task(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle cases where no marketing task is found."""
        logger.error("NO marketing_task found in context")

        # Check if this is a direct message that should be handled conversationally
        if "message" in context and context.get("message"):
            logger.info("No marketing task found, but message is present. Treating as conversational.")
            await self._generate_llm_conversation_response(context, context.get("message", ""), "general_conversation", [])
            context["metadata"] = {"conversational_response": True}
            return context
        else:
            # Generate LLM response for missing task scenario
            await self._generate_llm_conversation_response(context, "", "missing_task_error", [])
            context["metadata"] = {"error": "missing_marketing_task"}
            return context

    def _get_combined_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get combined context including conversation history."""
        combined_ctx = {}
        if "context" in context and isinstance(context["context"], dict):
            combined_ctx.update(context["context"])
        if "conversation_history" in context:
            combined_ctx["conversation_history"] = context["conversation_history"]
        elif "context" in context and "conversation_history" in context["context"]:
            combined_ctx["conversation_history"] = context["context"]["conversation_history"]
        return combined_ctx
