#!/usr/bin/env python3
"""
Test script for agent-specific LLM settings functionality.

This script tests the new LLM provider and model settings for memory service
and concierge agent to ensure they work correctly.
"""

import asyncio
import logging
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db, get_provider_settings, set_provider_settings
from agents.utils.memory_service import MemoryService
from agents.concierge_agent.concierge import ConciergeAgent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_memory_service_settings():
    """Test memory service with user-specific LLM settings."""
    logger.info("Testing memory service with user-specific LLM settings...")
    
    # Test user settings
    test_user_settings = {
        "memory_service_provider": "openai",
        "memory_service_model": "gpt-3.5-turbo",
        "concierge_agent_provider": "groq",
        "concierge_agent_model": "llama-3.1-8b-instant"
    }
    
    try:
        # Test memory service with user settings
        memory_service = MemoryService.get_instance_with_user_settings(test_user_settings)
        
        if memory_service.initialized:
            logger.info("✓ Memory service initialized successfully with user settings")
        else:
            logger.warning("⚠ Memory service not initialized (this may be expected in test environment)")
            
        # Test that settings are stored
        if hasattr(memory_service, '_current_user_settings'):
            stored_settings = memory_service._current_user_settings
            logger.info(f"✓ Memory service stored user settings: {stored_settings}")
        else:
            logger.warning("⚠ Memory service did not store user settings")
            
    except Exception as e:
        logger.error(f"✗ Error testing memory service: {e}")

async def test_concierge_agent_settings():
    """Test concierge agent with user-specific LLM settings."""
    logger.info("Testing concierge agent user settings retrieval...")
    
    try:
        # Create concierge agent instance
        concierge = ConciergeAgent()
        
        # Test user settings retrieval
        test_user_id = "1"
        user_settings = await concierge._get_user_settings(test_user_id)
        
        if user_settings is not None:
            logger.info(f"✓ Successfully retrieved user settings: {user_settings}")
        else:
            logger.info("ℹ No user settings found (this is expected if no user exists)")
            
        # Test model configuration loading with user settings
        test_user_settings = {
            "concierge_agent_provider": "groq",
            "concierge_agent_model": "llama-3.1-8b-instant"
        }
        
        await concierge._load_model_configuration({}, test_user_settings)
        
        if hasattr(concierge, 'analysis_config') and concierge.analysis_config:
            logger.info(f"✓ Concierge agent loaded configuration: provider={concierge.analysis_config.provider}, model={concierge.analysis_config.model}")
        else:
            logger.warning("⚠ Concierge agent configuration not loaded")
            
    except Exception as e:
        logger.error(f"✗ Error testing concierge agent: {e}")

async def test_database_settings():
    """Test database provider settings functionality."""
    logger.info("Testing database provider settings...")
    
    try:
        # Get database session
        db = next(get_db())
        
        # Test user ID (this would normally be a real user)
        test_user_id = 1
        
        # Test setting provider settings
        test_settings = {
            "default_provider": "openai",
            "use_local_llm": False,
            "memory_service_provider": "groq",
            "memory_service_model": "llama-3.1-8b-instant",
            "concierge_agent_provider": "openai",
            "concierge_agent_model": "gpt-4"
        }
        
        # This will fail if user doesn't exist, which is expected in test environment
        try:
            success = set_provider_settings(db, test_user_id, test_settings)
            if success:
                logger.info("✓ Successfully set provider settings")
                
                # Test getting provider settings
                retrieved_settings = get_provider_settings(db, test_user_id)
                logger.info(f"✓ Retrieved settings: {retrieved_settings}")
            else:
                logger.warning("⚠ Failed to set provider settings (user may not exist)")
        except Exception as e:
            logger.info(f"ℹ Database test skipped (user doesn't exist): {e}")
            
    except Exception as e:
        logger.error(f"✗ Error testing database settings: {e}")

async def main():
    """Run all tests."""
    logger.info("Starting agent LLM settings tests...")
    
    await test_memory_service_settings()
    await test_concierge_agent_settings()
    await test_database_settings()
    
    logger.info("Agent LLM settings tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
