#!/usr/bin/env python3
"""
Simple test to verify visualization pipeline is working.
"""

import asyncio
import logging
import os
import pandas as pd
import base64
from io import BytesIO
import matplotlib.pyplot as plt

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_basic_visualization():
    """Test basic visualization generation without PandasAI."""
    logger.info("Testing basic visualization generation...")
    
    # Create test data
    data = {
        "Department": ["Engineering", "Marketing", "Sales", "HR", "Finance"],
        "Average_Salary": [95000, 65000, 70000, 60000, 75000],
        "Employee_Count": [50, 20, 30, 15, 25]
    }
    df = pd.DataFrame(data)
    
    # Create a simple bar chart
    plt.figure(figsize=(10, 6))
    plt.bar(df['Department'], df['Average_Salary'])
    plt.title('Average Salary by Department')
    plt.xlabel('Department')
    plt.ylabel('Average Salary ($)')
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Save to bytes
    img_buffer = BytesIO()
    plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
    img_buffer.seek(0)
    
    # Encode to base64
    img_base64 = base64.b64encode(img_buffer.read()).decode('utf-8')
    plt.close()
    
    # Create visualization metadata in the format expected by frontend
    visualization_data = {
        "type": "chart",
        "title": "Average Salary by Department",
        "description": "Bar chart showing average salary by department",
        "data": {
            "image": f"data:image/png;base64,{img_base64}"
        }
    }
    
    # Create response in MCP format
    response = {
        "isError": False,
        "content": [
            {"type": "text", "text": "📊 Generated visualization: Average Salary by Department"},
            {
                "type": "image",
                "src": f"data:image/png;base64,{img_base64}"
            }
        ],
        "metadata": {
            "visualization": visualization_data,
            "task_type": "pandasai_visualization"
        }
    }
    
    logger.info("✅ Basic visualization generated successfully")
    logger.info(f"Response keys: {list(response.keys())}")
    logger.info(f"Metadata keys: {list(response['metadata'].keys())}")
    logger.info(f"Visualization type: {response['metadata']['visualization']['type']}")
    logger.info(f"Image data length: {len(img_base64)} characters")
    
    return response

async def test_visualization_metadata_processing():
    """Test how the frontend processes visualization metadata."""
    logger.info("Testing visualization metadata processing...")
    
    # Simulate the metadata that would come from the agent
    sample_metadata = {
        "task_type": "pandasai_visualization",
        "visualization": {
            "type": "chart",
            "title": "Sample Chart",
            "description": "A sample chart for testing",
            "data": {
                "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            }
        }
    }
    
    # Test the processVisualizationData function logic
    def process_visualization_data(metadata):
        if not metadata:
            return None
        
        # Handle direct visualization object
        if metadata.get("visualization") and isinstance(metadata["visualization"], dict):
            return metadata["visualization"]
        
        # Handle image-based visualization from content
        if metadata.get("content") and isinstance(metadata["content"], list):
            image_content = next((item for item in metadata["content"] if item.get("type") == "image" and item.get("src")), None)
            if image_content:
                text_content = next((item for item in metadata["content"] if item.get("type") == "text" and item.get("text")), None)
                title = text_content.get("text") if text_content else metadata.get("prompt", "Data Visualization")
                
                return {
                    "type": "chart",
                    "title": title,
                    "description": "Generated using PandasAI analysis",
                    "data": {
                        "image": image_content["src"]
                    }
                }
        
        return None
    
    result = process_visualization_data(sample_metadata)
    
    if result:
        logger.info("✅ Visualization metadata processed successfully")
        logger.info(f"Processed visualization type: {result['type']}")
        logger.info(f"Processed visualization title: {result['title']}")
    else:
        logger.error("❌ Failed to process visualization metadata")
    
    return result

async def main():
    """Run all tests."""
    logger.info("Starting visualization pipeline tests...")
    
    # Test 1: Basic visualization generation
    basic_viz = await test_basic_visualization()
    
    # Test 2: Metadata processing
    processed_metadata = await test_visualization_metadata_processing()
    
    # Test 3: Check if the basic visualization can be processed by the metadata processor
    logger.info("Testing integration between generation and processing...")
    
    def process_visualization_data(metadata):
        if not metadata:
            return None
        
        # Handle direct visualization object
        if metadata.get("visualization") and isinstance(metadata["visualization"], dict):
            return metadata["visualization"]
        
        return None
    
    integrated_result = process_visualization_data(basic_viz["metadata"])
    
    if integrated_result:
        logger.info("✅ Integration test passed - visualization can be processed")
        logger.info(f"Final visualization type: {integrated_result['type']}")
    else:
        logger.error("❌ Integration test failed")
    
    logger.info("All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
