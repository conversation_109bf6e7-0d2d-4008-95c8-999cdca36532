/**
 * Visual Chart Picker Component
 * 
 * Provides an intuitive visual interface for selecting chart types with live previews.
 * Features smart recommendations based on data characteristics and use cases.
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Area<PERSON>hart,
  ScatterChart,
  TrendingUp,
  Target,
  Activity,
  Layers,
  Map,
  Calendar,
  Users,
  Zap,
  Eye,
  Sparkles,
  Brain,
  CheckCircle,
  Star,
  Lightbulb,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChartType {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  category: 'comparison' | 'trend' | 'distribution' | 'relationship' | 'composition' | 'geographic';
  best_for: string[];
  data_requirements: {
    min_columns: number;
    data_types: string[];
    recommended_rows: string;
  };
  complexity: 'simple' | 'moderate' | 'advanced';
  preview_component: React.ComponentType<any>;
  ai_recommendation_score?: number;
  use_cases: string[];
}

interface DataCharacteristics {
  column_count: number;
  row_count: number;
  data_types: string[];
  has_time_series: boolean;
  has_categories: boolean;
  has_numeric: boolean;
  has_geographic: boolean;
}

interface VisualChartPickerProps {
  className?: string;
  data_characteristics?: DataCharacteristics;
  on_chart_select?: (chart_type: ChartType, config?: any) => void;
  show_ai_recommendations?: boolean;
}

export const VisualChartPicker: React.FC<VisualChartPickerProps> = ({
  className,
  data_characteristics,
  on_chart_select,
  show_ai_recommendations = true,
}) => {
  const [chart_types, set_chart_types] = useState<ChartType[]>([]);
  const [filtered_charts, set_filtered_charts] = useState<ChartType[]>([]);
  const [ai_recommendations, set_ai_recommendations] = useState<ChartType[]>([]);
  const [selected_category, set_selected_category] = useState<string>('all');
  const [selected_chart, set_selected_chart] = useState<ChartType | null>(null);
  const [show_preview, set_show_preview] = useState(false);
  const [search_query, set_search_query] = useState('');

  useEffect(() => {
    const charts = get_chart_types();
    set_chart_types(charts);
    
    if (data_characteristics && show_ai_recommendations) {
      generate_ai_recommendations(charts, data_characteristics);
    }
  }, [data_characteristics, show_ai_recommendations]);

  useEffect(() => {
    filter_charts();
  }, [chart_types, selected_category, search_query]);

  const generate_ai_recommendations = (charts: ChartType[], data_chars: DataCharacteristics) => {
    const scored_charts = charts.map(chart => ({
      ...chart,
      ai_recommendation_score: calculate_chart_score(chart, data_chars),
    }));

    const recommendations = scored_charts
      .filter(chart => chart.ai_recommendation_score! > 0.6)
      .sort((a, b) => b.ai_recommendation_score! - a.ai_recommendation_score!)
      .slice(0, 4);

    set_ai_recommendations(recommendations);
  };

  const filter_charts = () => {
    let filtered = chart_types;

    if (selected_category !== 'all') {
      filtered = filtered.filter(chart => chart.category === selected_category);
    }

    if (search_query) {
      filtered = filtered.filter(chart =>
        chart.name.toLowerCase().includes(search_query.toLowerCase()) ||
        chart.description.toLowerCase().includes(search_query.toLowerCase()) ||
        chart.best_for.some(use_case => 
          use_case.toLowerCase().includes(search_query.toLowerCase())
        )
      );
    }

    set_filtered_charts(filtered);
  };

  const handle_chart_select = (chart: ChartType) => {
    set_selected_chart(chart);
    set_show_preview(true);
  };

  const handle_chart_confirm = (chart: ChartType, config?: any) => {
    on_chart_select?.(chart, config);
    set_show_preview(false);
    set_selected_chart(null);
  };

  const get_category_info = (category: string) => {
    const categories = {
      comparison: { label: 'Comparison', icon: BarChart3, description: 'Compare values across categories' },
      trend: { label: 'Trend', icon: LineChart, description: 'Show changes over time' },
      distribution: { label: 'Distribution', icon: Activity, description: 'Show data distribution' },
      relationship: { label: 'Relationship', icon: ScatterChart, description: 'Show correlations' },
      composition: { label: 'Composition', icon: PieChart, description: 'Show parts of a whole' },
      geographic: { label: 'Geographic', icon: Map, description: 'Show location-based data' },
    };
    return categories[category as keyof typeof categories];
  };

  const categories = ['all', 'comparison', 'trend', 'distribution', 'relationship', 'composition', 'geographic'];

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-6 w-6 text-blue-500" />
            <span>Visual Chart Picker</span>
            {show_ai_recommendations && (
              <Badge variant="secondary">
                <Brain className="h-3 w-3 mr-1" />
                AI-Powered
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Choose the perfect chart type for your data with visual previews and smart recommendations.
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue={show_ai_recommendations ? "recommendations" : "browse"}>
        <TabsList className="grid w-full grid-cols-3">
          {show_ai_recommendations && <TabsTrigger value="recommendations">AI Recommendations</TabsTrigger>}
          <TabsTrigger value="browse">Browse Charts</TabsTrigger>
          <TabsTrigger value="guide">Chart Guide</TabsTrigger>
        </TabsList>

        {/* AI Recommendations Tab */}
        {show_ai_recommendations && (
          <TabsContent value="recommendations" className="space-y-4">
            {ai_recommendations.length > 0 ? (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Sparkles className="h-5 w-5 text-purple-500" />
                  <h3 className="text-lg font-semibold">Recommended for Your Data</h3>
                  <Badge variant="outline">{ai_recommendations.length} suggestions</Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {ai_recommendations.map((chart, index) => {
                    const IconComponent = chart.icon;
                    return (
                      <Card 
                        key={chart.id} 
                        className={cn(
                          "cursor-pointer hover:shadow-lg transition-all border-l-4",
                          index === 0 ? "border-l-purple-500 bg-purple-50/50" : "border-l-blue-500"
                        )}
                        onClick={() => handle_chart_select(chart)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-3">
                            <div className="p-2 bg-blue-50 rounded-lg">
                              <IconComponent className="h-6 w-6 text-blue-500" />
                            </div>
                            <div className="flex-1 space-y-2">
                              <div className="flex items-center space-x-2">
                                <h4 className="font-semibold">{chart.name}</h4>
                                <Badge variant="outline" className="text-xs">
                                  {Math.round(chart.ai_recommendation_score! * 100)}% match
                                </Badge>
                                {index === 0 && (
                                  <Badge className="bg-purple-100 text-purple-800 text-xs">
                                    <Star className="h-3 w-3 mr-1" />
                                    Best
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground">{chart.description}</p>
                              <div className="flex flex-wrap gap-1">
                                {chart.best_for.slice(0, 2).map((use_case, idx) => (
                                  <Badge key={idx} variant="secondary" className="text-xs">
                                    {use_case}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Specific Recommendations</h3>
                    <p className="text-muted-foreground">
                      Browse all chart types or provide data characteristics for better suggestions.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        )}

        {/* Browse Charts Tab */}
        <TabsContent value="browse" className="space-y-4">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search chart types..."
                    value={search_query}
                    onChange={(e) => set_search_query(e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Category Tabs */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => {
              const category_info = category === 'all' 
                ? { label: 'All Charts', icon: BarChart3 }
                : get_category_info(category);
              
              if (!category_info) return null;
              
              const IconComponent = category_info.icon;
              return (
                <Button
                  key={category}
                  variant={selected_category === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => set_selected_category(category)}
                  className="flex items-center space-x-1"
                >
                  <IconComponent className="h-4 w-4" />
                  <span>{category_info.label}</span>
                </Button>
              );
            })}
          </div>

          {/* Chart Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filtered_charts.map((chart) => {
              const IconComponent = chart.icon;
              return (
                <Card 
                  key={chart.id} 
                  className="cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() => handle_chart_select(chart)}
                >
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {/* Chart Preview */}
                      <div className="h-24 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border flex items-center justify-center">
                        <IconComponent className="h-8 w-8 text-blue-500" />
                      </div>

                      {/* Chart Info */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-semibold">{chart.name}</h4>
                          <Badge variant="outline" className="text-xs">
                            {chart.complexity}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{chart.description}</p>
                        
                        {/* Best For */}
                        <div className="flex flex-wrap gap-1">
                          {chart.best_for.slice(0, 2).map((use_case, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {use_case}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Chart Guide Tab */}
        <TabsContent value="guide" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lightbulb className="h-5 w-5 text-yellow-500" />
                <span>Chart Selection Guide</span>
              </CardTitle>
              <CardDescription>
                Learn when to use different chart types for maximum impact.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {categories.slice(1).map((category) => {
                const category_info = get_category_info(category);
                if (!category_info) return null;
                
                const IconComponent = category_info.icon;
                const category_charts = chart_types.filter(chart => chart.category === category);
                
                return (
                  <div key={category} className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <IconComponent className="h-5 w-5 text-blue-500" />
                      <h4 className="font-semibold">{category_info.label}</h4>
                    </div>
                    <p className="text-sm text-muted-foreground">{category_info.description}</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {category_charts.map((chart) => (
                        <div key={chart.id} className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                          <chart.icon className="h-5 w-5 text-blue-500" />
                          <div>
                            <p className="font-medium text-sm">{chart.name}</p>
                            <p className="text-xs text-muted-foreground">{chart.best_for[0]}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Chart Preview Dialog */}
      {selected_chart && (
        <Dialog open={show_preview} onOpenChange={set_show_preview}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <selected_chart.icon className="h-5 w-5 text-blue-500" />
                <span>{selected_chart.name}</span>
              </DialogTitle>
              <DialogDescription>
                {selected_chart.description}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Chart Details */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <Label className="font-medium">Category</Label>
                  <p className="text-muted-foreground capitalize">{selected_chart.category}</p>
                </div>
                <div>
                  <Label className="font-medium">Complexity</Label>
                  <p className="text-muted-foreground capitalize">{selected_chart.complexity}</p>
                </div>
                <div>
                  <Label className="font-medium">Min Columns</Label>
                  <p className="text-muted-foreground">{selected_chart.data_requirements.min_columns}</p>
                </div>
                <div>
                  <Label className="font-medium">Recommended Rows</Label>
                  <p className="text-muted-foreground">{selected_chart.data_requirements.recommended_rows}</p>
                </div>
              </div>

              {/* Best For */}
              <div>
                <Label className="font-medium mb-2 block">Best For</Label>
                <div className="flex flex-wrap gap-2">
                  {selected_chart.best_for.map((use_case, index) => (
                    <Badge key={index} variant="outline">
                      {use_case}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Live Preview */}
              <div className="h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <selected_chart.icon className="h-16 w-16 mx-auto mb-4" />
                  <p>Live Chart Preview</p>
                  <p className="text-sm">Interactive preview with your data</p>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => set_show_preview(false)}>
                Cancel
              </Button>
              <Button onClick={() => handle_chart_confirm(selected_chart)}>
                <Zap className="h-4 w-4 mr-2" />
                Create Chart
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

// Calculate AI recommendation score for chart types
function calculate_chart_score(chart: ChartType, data_chars: DataCharacteristics): number {
  let score = 0.3; // Base score

  // Data type compatibility
  const compatible_types = chart.data_requirements.data_types.filter(type =>
    data_chars.data_types.includes(type)
  );
  if (compatible_types.length > 0) {
    score += 0.3 * (compatible_types.length / chart.data_requirements.data_types.length);
  }

  // Column count compatibility
  if (data_chars.column_count >= chart.data_requirements.min_columns) {
    score += 0.2;
  }

  // Special data characteristics
  if (data_chars.has_time_series && chart.category === 'trend') {
    score += 0.3;
  }
  if (data_chars.has_categories && chart.category === 'comparison') {
    score += 0.2;
  }
  if (data_chars.has_geographic && chart.category === 'geographic') {
    score += 0.4;
  }

  return Math.min(score, 1.0);
}

// Mock chart preview component
const ChartPreview: React.FC<any> = () => (
  <div className="h-full w-full bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border flex items-center justify-center">
    <BarChart3 className="h-16 w-16 text-blue-500" />
  </div>
);

// Get available chart types
function get_chart_types(): ChartType[] {
  return [
    {
      id: 'bar',
      name: 'Bar Chart',
      description: 'Compare values across categories',
      icon: BarChart3,
      category: 'comparison',
      best_for: ['Comparing categories', 'Ranking data', 'Showing differences'],
      data_requirements: {
        min_columns: 2,
        data_types: ['categorical', 'numerical'],
        recommended_rows: '3-20',
      },
      complexity: 'simple',
      preview_component: ChartPreview,
      use_cases: ['Sales by region', 'Product comparisons', 'Survey results'],
    },
    {
      id: 'line',
      name: 'Line Chart',
      description: 'Show trends and changes over time',
      icon: LineChart,
      category: 'trend',
      best_for: ['Time series data', 'Trends', 'Continuous data'],
      data_requirements: {
        min_columns: 2,
        data_types: ['date', 'numerical'],
        recommended_rows: '10+',
      },
      complexity: 'simple',
      preview_component: ChartPreview,
      use_cases: ['Stock prices', 'Website traffic', 'Temperature changes'],
    },
    {
      id: 'pie',
      name: 'Pie Chart',
      description: 'Show parts of a whole',
      icon: PieChart,
      category: 'composition',
      best_for: ['Proportions', 'Percentages', 'Parts of whole'],
      data_requirements: {
        min_columns: 2,
        data_types: ['categorical', 'numerical'],
        recommended_rows: '3-7',
      },
      complexity: 'simple',
      preview_component: ChartPreview,
      use_cases: ['Market share', 'Budget allocation', 'Demographics'],
    },
    {
      id: 'scatter',
      name: 'Scatter Plot',
      description: 'Show relationships between variables',
      icon: ScatterChart,
      category: 'relationship',
      best_for: ['Correlations', 'Relationships', 'Outliers'],
      data_requirements: {
        min_columns: 2,
        data_types: ['numerical', 'numerical'],
        recommended_rows: '20+',
      },
      complexity: 'moderate',
      preview_component: ChartPreview,
      use_cases: ['Price vs quality', 'Height vs weight', 'Performance metrics'],
    },
    // Add more chart types...
  ];
}
