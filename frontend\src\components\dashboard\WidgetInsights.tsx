import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { WidgetResponse } from '@/types/dashboard-customization';
import { TrendingUp, TrendingDown, AlertCircle, Info } from 'lucide-react';

interface WidgetInsightsProps {
  widget: WidgetResponse;
  onClose: () => void;
}

export const WidgetInsights: React.FC<WidgetInsightsProps> = ({ widget, onClose }) => {
  // Mock insights data - in a real implementation, this would come from an API
  const insights = {
    summary: {
      trend: 'up',
      change: '+12.5%',
      period: 'last 7 days',
      status: 'healthy'
    },
    ai_insights: 'This widget shows positive growth trends with consistent user engagement. The data quality is good with no anomalies detected.',
    trends: {
      weekly: '+12.5%',
      monthly: '+8.3%',
      quarterly: '+15.2%'
    },
    confidence: 0.85
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Widget Insights: {widget.title}
          </DialogTitle>
          <DialogDescription>
            AI-generated insights and analytics for this widget
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Summary Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {insights.summary.trend === 'up' ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                  <span className="font-semibold">{insights.summary.change}</span>
                  <span className="text-sm text-muted-foreground">
                    {insights.summary.period}
                  </span>
                </div>
                <Badge 
                  variant={insights.summary.status === 'healthy' ? 'default' : 'destructive'}
                >
                  {insights.summary.status}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* AI Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                AI Analysis
                <Badge variant="outline" className="ml-auto">
                  {Math.round(insights.confidence * 100)}% confidence
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                {insights.ai_insights}
              </p>
            </CardContent>
          </Card>

          {/* Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Trend Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">
                    {insights.trends.weekly}
                  </div>
                  <div className="text-xs text-muted-foreground">Weekly</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">
                    {insights.trends.monthly}
                  </div>
                  <div className="text-xs text-muted-foreground">Monthly</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">
                    {insights.trends.quarterly}
                  </div>
                  <div className="text-xs text-muted-foreground">Quarterly</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Widget Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Type:</span>
                  <span>{widget.widget_type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Refresh Interval:</span>
                  <span>{widget.refresh_interval}s</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <Badge variant={widget.is_active ? 'default' : 'secondary'}>
                    {widget.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Last Updated:</span>
                  <span>{new Date(widget.updated_at).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
