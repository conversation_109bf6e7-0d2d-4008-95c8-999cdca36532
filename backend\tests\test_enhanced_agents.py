"""
Test script for enhanced agents.

This script tests the enhanced marketing and analysis agents.
"""

import asyncio
import logging
import sys
import os
import json
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import agent registry
from agents.registry import AgentRegistry


async def test_agent(persona_id: str, message: str) -> None:
    """
    Test an agent with a message.

    Args:
        persona_id: ID of the persona to test
        message: Message to send to the agent
    """
    logger.info(f"Testing agent for persona ID: {persona_id}")
    
    # Create agent instance
    agent = await AgentRegistry.create_agent_instance(persona_id)
    
    if agent is None:
        logger.error(f"Failed to create agent for persona ID: {persona_id}")
        return
    
    logger.info(f"Successfully created agent for persona ID: {persona_id}")
    
    # Process message
    try:
        response = await agent.process_message(
            user_id=1,
            message=message,
            conversation_id="test_conversation",
            context={"is_first_message": True}
        )
        
        logger.info(f"Agent response: {json.dumps(response, indent=2)}")
    except Exception as e:
        logger.error(f"Error processing message: {e}", exc_info=True)


async def main() -> None:
    """Main function."""
    # Load agent configurations
    AgentRegistry.load_configurations("agents/configs")
    
    # Test enhanced marketing agent
    await test_agent(
        "enhanced_marketing_agent",
        "I need help creating a marketing strategy for my new fitness app."
    )
    
    # Test enhanced analysis agent
    await test_agent(
        "enhanced_analysis_agent",
        "I need help analyzing my sales data to identify trends."
    )


if __name__ == "__main__":
    asyncio.run(main())
