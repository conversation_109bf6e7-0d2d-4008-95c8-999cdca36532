"""
Conversation tool for the Datagenius concierge agent.

This module provides an MCP-compatible tool specifically for handling conversational
interactions for the concierge agent. It focuses on natural conversation flow,
context awareness, and proper AI assistant responses.
"""

import logging
import json
from typing import Dict, Any, List

from langchain_core.messages import SystemMessage

from .base import BaseMCPTool
from agents.utils.model_providers.utils import get_model
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class ConversationTool(BaseMCPTool):
    """Tool for handling conversational interactions for the concierge agent."""

    def __init__(self):
        """Initialize the conversation tool."""
        super().__init__(
            name="handle_conversation",
            description="Handle conversational interactions with proper AI assistant responses",
            input_schema={
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "The user's message"
                    },
                    "conversation_history": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "sender": {"type": "string"},
                                "content": {"type": "string"},
                                "timestamp": {"type": "string"}
                            }
                        },
                        "description": "Previous conversation messages"
                    },
                    "user_context": {
                        "type": "object",
                        "description": "Additional context about the user"
                    },
                    "intent_type": {
                        "type": "string",
                        "description": "The detected intent type"
                    },
                    "confidence": {
                        "type": "number",
                        "description": "Confidence level of intent detection"
                    },
                    "is_continuing_conversation": {
                        "type": "boolean",
                        "description": "Whether this is a continuing conversation"
                    },
                    "provider": {
                        "type": "string",
                        "description": "AI provider to use"
                    },
                    "model": {
                        "type": "string",
                        "description": "AI model to use"
                    },
                    "temperature": {
                        "type": "number",
                        "description": "Temperature for response generation"
                    }
                },
                "required": ["message"]
            },
            annotations={
                "title": "Handle Conversation",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the conversation tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            message = arguments["message"]
            conversation_history = arguments.get("conversation_history", [])
            user_context = arguments.get("user_context", {})
            intent_type = arguments.get("intent_type", "general_question")
            confidence = arguments.get("confidence", 0.5)
            is_continuing_conversation = arguments.get("is_continuing_conversation", False)

            provider_id = arguments.get("provider", "groq")
            model_name = arguments.get("model", "llama-3.1-8b-instant")
            temperature = arguments.get("temperature", 0.7)

            # Build conversation context
            conversation_context = self._build_conversation_context(conversation_history)

            # Create the conversational prompt using dynamic system
            system_prompt = await self._create_system_prompt(
                intent_type,
                confidence,
                is_continuing_conversation,
                user_context
            )

            user_prompt = self._create_user_prompt(
                message,
                conversation_context,
                intent_type,
                confidence
            )

            # Initialize LLM client
            try:
                llm = await get_model(provider_id, model_name, {"temperature": temperature})
                logger.info(f"Successfully initialized model from provider '{provider_id}'")
            except Exception as e:
                logger.error(f"Error initializing model: {str(e)}", exc_info=True)
                raise ValueError(f"Failed to initialize model: {str(e)}")

            # Generate response using SystemMessage for proper AI assistant role
            system_message = SystemMessage(content=system_prompt)
            user_message = SystemMessage(content=user_prompt)

            logger.info(f"Generating conversational response for intent: {intent_type}")

            try:
                if hasattr(llm, 'ainvoke'):
                    response = await llm.ainvoke([system_message, user_message])
                    generated_response = response.content if hasattr(response, 'content') else str(response)
                else:
                    raise NotImplementedError("Model does not support ainvoke method")

                logger.info(f"Successfully generated conversational response")
            except Exception as e:
                logger.error(f"Error generating conversational response: {str(e)}", exc_info=True)
                raise ValueError(f"Failed to generate conversational response: {str(e)}")

            return {
                "content": [
                    {
                        "type": "text",
                        "text": generated_response
                    }
                ],
                "metadata": {
                    "intent_type": intent_type,
                    "confidence": confidence,
                    "is_continuing_conversation": is_continuing_conversation,
                    "conversation_length": len(conversation_history),
                    "agent_identity": getattr(self, '_current_agent_identity', 'unknown'),
                    "agent_aware": True
                }
            }

        except Exception as e:
            logger.error(f"Error in ConversationTool execute: {str(e)}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"I apologize, but I encountered an issue processing your message. Please try again."
                    }
                ],
                "metadata": {
                    "error_type": e.__class__.__name__,
                    "error_details": str(e),
                    "component": "ConversationTool"
                }
            }

    def _build_conversation_context(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Build conversation context string from history."""
        if not conversation_history:
            return ""

        recent_messages = conversation_history[-5:]  # Last 5 messages for context
        history_lines = []

        for msg in recent_messages:
            sender = msg.get('sender', 'unknown')
            content = msg.get('content', '')
            if sender and content:
                history_lines.append(f"{sender}: {content}")

        if history_lines:
            return f"Recent Conversation History:\n" + "\n".join(history_lines) + "\n"
        return ""

    async def _create_system_prompt(
        self,
        intent_type: str,
        confidence: float,
        is_continuing_conversation: bool,
        user_context: Dict[str, Any]
    ) -> str:
        """Create the system prompt for the AI assistant using dynamic detection."""

        # Detect agent identity using the dynamic system
        agent_identity = await detect_agent_identity(
            agent_id=user_context.get("agent_id") or user_context.get("persona_id"),
            context=user_context,
            intent_type=intent_type
        )

        logger.info(f"Detected agent identity: {agent_identity} for conversation")

        # Prepare context for prompt customization
        prompt_context = {
            **user_context,
            "is_continuing_conversation": is_continuing_conversation
        }

        # Get the system prompt using the dynamic registry
        system_prompt = await get_agent_system_prompt(agent_identity, prompt_context)

        # Store agent identity for metadata
        self._current_agent_identity = agent_identity

        return system_prompt



    def _create_user_prompt(
        self,
        message: str,
        conversation_context: str,
        intent_type: str,
        confidence: float
    ) -> str:
        """Create the user prompt with context."""

        prompt_parts = []

        if conversation_context:
            prompt_parts.append(conversation_context)

        prompt_parts.append(f"Current user message: \"{message}\"")
        prompt_parts.append(f"Detected intent: {intent_type} (confidence: {confidence:.2f})")

        # Customize instruction based on intent type (extensible approach)
        instruction = self._get_user_prompt_instruction(intent_type)
        prompt_parts.append(f"\n{instruction}")

        return "\n\n".join(prompt_parts)

    def _get_user_prompt_instruction(self, intent_type: str) -> str:
        """
        Get user prompt instruction based on intent type.

        Args:
            intent_type: The detected intent type

        Returns:
            Instruction string for the user prompt
        """
        # Mapping of intent types to instructions
        intent_instructions = {
            "analysis_request": "Generate a natural, conversational response as a data analysis expert. Provide clear explanations about data analysis concepts, statistical methods, or insights from data. Focus on being educational and helpful in explaining complex data concepts in an accessible way. Keep it conversational and engaging.",
            "marketing_advice": "Generate a natural, conversational response as a marketing expert. Provide practical, actionable marketing advice and suggestions. Focus on being helpful and consultative rather than generating formal marketing documents. Keep it conversational and engaging.",
            "classification_request": "Generate a natural, conversational response as a data classification expert. Provide clear explanations about data organization, text processing, or classification concepts. Focus on being helpful in explaining data structuring approaches. Keep it conversational and engaging.",
            "greeting": "Generate a natural, friendly greeting response appropriate for your role as an AI assistant. Be welcoming and helpful while establishing your identity and capabilities.",
            "general_question": "Generate a natural, helpful response as the Datagenius Concierge AI assistant. If the user asked a specific question, answer it informatively. Then, if relevant, mention how specialized Datagenius personas could provide even deeper expertise on the topic. Keep it conversational and engaging."
        }

        # Return specific instruction or default
        return intent_instructions.get(intent_type, intent_instructions["general_question"])
