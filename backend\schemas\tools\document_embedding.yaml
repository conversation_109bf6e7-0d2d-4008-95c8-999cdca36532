name: document_embedding
description: Process documents, create embeddings, and store in vector database
input_schema:
  type: object
  properties:
    file_path:
      type: string
      description: The path to the document file to be processed.
    operation:
      type: string
      enum:
        - embed
        - query
        - query_marketing_fields
      description: The operation to perform (embed, query, or query_marketing_fields).
    query:
      type: string
      description: The query string to use for 'query' or 'query_marketing_fields' operations.
    chunk_size:
      type: integer
      default: 1000
      description: The size of chunks for splitting the document (optional with adaptive chunking).
    chunk_overlap:
      type: integer
      default: 200
      description: The overlap between chunks (optional with adaptive chunking).
    use_adaptive_chunking:
      type: boolean
      default: true
      description: Whether to use adaptive chunking strategies based on content type.
    performance_profile:
      type: string
      enum:
        - speed_optimized
        - quality_optimized
        - balanced
        - memory_optimized
      default: balanced
      description: Performance profile for chunking and embedding operations.
    embeddings_model:
      type: string
      default: "all-MiniLM-L6-v2"
      description: The name of the HuggingFace embeddings model to use.
    marketing_fields: # This seems to be a boolean flag in the original code, but its usage is tied to query_marketing_fields operation
      type: boolean
      default: false
      description: Flag related to marketing field extraction (primarily for 'query_marketing_fields' operation).
  required:
    - file_path
    - operation
output_schema: # Based on the execute method's return structure
  type: object
  properties:
    isError:
      type: boolean
      description: Indicates if an error occurred during execution.
    content:
      type: array
      items:
        type: object
        properties:
          type:
            type: string
            enum: [text]
          text:
            type: string
        required:
          - type
          - text
      description: Main content of the response, usually a text message.
    metadata:
      type: object
      properties:
        vector_store_id:
          type: string
          description: The ID of the created/used vector store (for 'embed' operation).
        file_info:
          type: object
          description: Information about the processed file (for 'embed' operation).
        query:
          type: string
          description: The original query (for 'query' operation).
        results:
          type: array
          items:
            type: object # Structure of individual search results
          description: The search results (for 'query' operation).
        marketing_fields:
          type: object
          description: Extracted marketing fields (for 'query_marketing_fields' operation).
      description: Additional metadata related to the operation.
  required:
    - isError
    - content
annotations:
  title: Document Embedding
  readOnlyHint: true # Assuming this means the tool doesn't modify external state beyond its own DB
  openWorldHint: false # Assuming it operates on provided files, not open web
  categories:
    - document_processing
    - nlp
    - vector_database
