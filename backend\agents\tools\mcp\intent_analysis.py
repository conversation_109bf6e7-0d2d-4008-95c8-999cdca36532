"""
LLM-based Intent Analysis Tool for determining user intent and request type.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from .base import BaseMCPTool
from ...utils.model_providers.utils import get_model
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)

class IntentAnalysisTool(BaseMCPTool):
    """
    Tool for analyzing user intent using LLM to determine the type of request
    and appropriate routing for data analysis tasks.
    """
    
    def __init__(self):
        super().__init__()
        self.name = "intent_analysis"
        self.description = "Analyze user intent to determine request type and routing"
        
    def get_schema(self) -> Dict[str, Any]:
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "description": "User message to analyze for intent"
                        },
                        "context": {
                            "type": "object",
                            "description": "Additional context about available data sources and capabilities",
                            "properties": {
                                "data_sources": {
                                    "type": "array",
                                    "description": "Available data sources",
                                    "items": {"type": "object"}
                                },
                                "content_types": {
                                    "type": "array", 
                                    "description": "Types of content available (structured, text, etc.)",
                                    "items": {"type": "string"}
                                }
                            }
                        },
                        "provider": {
                            "type": "string",
                            "description": "LLM provider to use",
                            "default": "groq"
                        },
                        "model": {
                            "type": "string", 
                            "description": "LLM model to use",
                            "default": "llama-3.1-8b-instant"
                        }
                    },
                    "required": ["message"]
                }
            }
        }
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze user intent using LLM.
        
        Args:
            arguments: Tool arguments containing message and context
            
        Returns:
            Intent analysis results in MCP format
        """
        try:
            message = arguments["message"]
            context = arguments.get("context", {})
            provider = arguments.get("provider", "groq")
            model_name = arguments.get("model", "llama-3.1-8b-instant")
            
            logger.info(f"Analyzing intent for message: '{message[:100]}...'")

            # Detect agent identity for context-aware intent analysis
            agent_id = arguments.get("persona_id") or arguments.get("agent_id")
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=context,
                intent_type="intent_analysis"
            )

            logger.info(f"Detected agent identity: {agent_identity} for intent analysis")

            # Build context information
            data_sources = context.get("data_sources", [])
            content_types = context.get("content_types", [])

            # Create the agent-aware analysis prompt
            prompt = await self._create_agent_aware_analysis_prompt(
                message, data_sources, content_types, agent_identity
            )
            
            # Get LLM model
            model = get_model(provider, model_name)
            if not model:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Model {model_name} not available for provider {provider}"}]
                }
            
            # Generate intent analysis
            try:
                response = await model.agenerate([prompt])
                analysis_text = response.generations[0][0].text.strip()
                
                # Parse the JSON response
                try:
                    intent_data = json.loads(analysis_text)
                except json.JSONDecodeError:
                    # If JSON parsing fails, extract intent from text
                    intent_data = self._extract_intent_from_text(analysis_text)
                
                logger.info(f"Intent analysis result: {intent_data}")
                
                return {
                    "isError": False,
                    "content": [{"type": "text", "text": f"Intent analysis completed: {intent_data.get('primary_intent', 'unknown')}"}],
                    "metadata": {
                        "intent_analysis": intent_data,
                        "original_message": message,
                        "provider": provider,
                        "model": model_name
                    }
                }
                
            except Exception as e:
                logger.error(f"Error generating intent analysis: {e}")
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error analyzing intent: {str(e)}"}]
                }
                
        except Exception as e:
            logger.error(f"Error in intent analysis tool: {e}")
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Intent analysis error: {str(e)}"}]
            }
    
    def _create_intent_analysis_prompt(self, message: str, data_sources: List[Dict], content_types: List[str]) -> str:
        """Create a prompt for intent analysis."""
        
        data_context = ""
        if data_sources:
            data_context = f"\nAvailable data sources: {[ds.get('name', 'unknown') for ds in data_sources]}"
        if content_types:
            data_context += f"\nContent types: {', '.join(content_types)}"
        
        prompt = f"""Analyze the following user message to determine their intent and the most appropriate action to take.

User message: "{message}"
{data_context}

Please analyze the intent and respond with a JSON object containing:
{{
    "primary_intent": "one of: visualization, analysis, search, extraction, classification, content_generation, data_exploration, statistical_analysis, correlation_analysis, data_cleaning, entity_extraction, knowledge_graph, general_query",
    "confidence": 0.0-1.0,
    "intent_details": {{
        "visualization_type": "if visualization intent: chart, plot, graph, table, heatmap, distribution, correlation, etc.",
        "analysis_type": "if analysis intent: statistical, exploratory, predictive, descriptive, etc.",
        "data_operation": "if data operation: filter, transform, clean, aggregate, join, etc.",
        "content_type": "structured, unstructured, text, mixed",
        "specific_request": "more specific description of what the user wants"
    }},
    "suggested_tools": ["list of tools that should be used"],
    "language": "detected language code (en, es, fr, etc.)",
    "urgency": "low, medium, high",
    "complexity": "simple, moderate, complex"
}}

Consider these intent patterns:
- Visualization: "create chart", "show graph", "visualize", "plot", "bar chart", "correlation", "distribution"
- Analysis: "analyze", "insights", "patterns", "trends", "statistics", "summary", "what does this show"
- Search: "find", "search", "look for", "where is", "show me records"
- Extraction: "extract", "get text", "pull out", "entities", "names", "organizations"
- Data exploration: "explore", "overview", "profile", "describe data", "what's in this"

Respond only with the JSON object, no additional text."""

        return prompt
    
    def _extract_intent_from_text(self, text: str) -> Dict[str, Any]:
        """Extract intent information from text if JSON parsing fails."""
        
        # Default intent structure
        intent = {
            "primary_intent": "general_query",
            "confidence": 0.5,
            "intent_details": {
                "specific_request": text[:200]
            },
            "suggested_tools": ["handle_conversation"],
            "language": "en",
            "urgency": "medium",
            "complexity": "moderate"
        }
        
        text_lower = text.lower()
        
        # Simple keyword-based fallback
        if any(word in text_lower for word in ["chart", "plot", "graph", "visualize", "visualization", "bar", "correlation"]):
            intent["primary_intent"] = "visualization"
            intent["suggested_tools"] = ["pandasai_visualization"]
            intent["intent_details"]["visualization_type"] = "chart"
            
        elif any(word in text_lower for word in ["analyze", "analysis", "insights", "patterns", "trends"]):
            intent["primary_intent"] = "analysis"
            intent["suggested_tools"] = ["pandasai_query", "data_access"]
            intent["intent_details"]["analysis_type"] = "exploratory"
            
        elif any(word in text_lower for word in ["search", "find", "look for"]):
            intent["primary_intent"] = "search"
            intent["suggested_tools"] = ["data_access"]
            intent["intent_details"]["data_operation"] = "search"
            
        return intent

    async def _create_agent_aware_analysis_prompt(
        self,
        message: str,
        data_sources: List[Dict],
        content_types: List[str],
        agent_identity: str
    ) -> str:
        """
        Create an agent-aware intent analysis prompt.

        Args:
            message: User message to analyze
            data_sources: Available data sources
            content_types: Available content types
            agent_identity: Detected agent identity

        Returns:
            Agent-aware intent analysis prompt
        """
        # Get agent-specific analysis context
        agent_context = self._get_agent_analysis_context(agent_identity)

        # Build data context
        data_context = ""
        if data_sources:
            data_context = f"\nAvailable data sources: {[ds.get('name', 'unknown') for ds in data_sources]}"
        if content_types:
            data_context += f"\nContent types: {', '.join(content_types)}"

        prompt = f"""You are an intelligent intent analysis system for a {agent_identity} agent.

AGENT CONTEXT:
{agent_context}

Analyze the following user message to determine their intent and the most appropriate action to take.

User message: "{message}"
{data_context}

Provide a detailed analysis in JSON format with the following structure:
{{
    "primary_intent": "main intent category",
    "confidence": confidence_score_0_to_1,
    "intent_details": {{
        "specific_request": "what the user specifically wants",
        "domain": "relevant domain or area",
        "complexity": "simple/moderate/complex",
        "agent_specific_context": "any agent-specific considerations"
    }},
    "suggested_tools": ["list", "of", "recommended", "tools"],
    "suggested_approach": "recommended approach for handling this intent",
    "language": "detected language code",
    "urgency": "low/medium/high",
    "requires_data": boolean_if_data_access_needed,
    "agent_specialization_match": "how well this matches the agent's specialization"
}}

Consider the agent's specialization when analyzing intent and suggesting tools."""

        return prompt

    async def _get_agent_analysis_context(self, agent_identity: str) -> str:
        """Get agent-specific context for intent analysis using system prompt capabilities."""
        try:
            # Get the agent's system prompt to extract capabilities
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract capabilities from system prompt
            capabilities = await self._extract_capabilities_from_prompt(system_prompt)

            # Create context based on extracted capabilities
            if capabilities:
                capability_context = self._create_capability_based_analysis_context(agent_identity, capabilities)
                return capability_context

        except Exception as e:
            logger.warning(f"Failed to get agent system prompt for {agent_identity}: {e}")

        # Fallback to static contexts if dynamic extraction fails
        return self._get_fallback_analysis_context(agent_identity)

    async def _extract_capabilities_from_prompt(self, system_prompt: str) -> List[str]:
        """Extract capabilities from agent system prompt."""
        capabilities = []

        if not system_prompt:
            return capabilities

        # Look for capability sections in the prompt
        capability_patterns = [
            r"Your capabilities include:\s*\n((?:- .+\n?)+)",
            r"capabilities:\s*\n((?:- .+\n?)+)",
            r"You can:\s*\n((?:- .+\n?)+)",
            r"I can help with:\s*\n((?:- .+\n?)+)",
            r"Your core expertise includes:\s*\n((?:- .+\n?)+)"
        ]

        import re
        for pattern in capability_patterns:
            match = re.search(pattern, system_prompt, re.IGNORECASE | re.MULTILINE)
            if match:
                capability_text = match.group(1)
                # Extract individual capabilities
                for line in capability_text.split('\n'):
                    line = line.strip()
                    if line.startswith('- '):
                        capability = line[2:].strip()
                        if capability:
                            capabilities.append(capability)
                break

        return capabilities

    def _create_capability_based_analysis_context(self, agent_identity: str, capabilities: List[str]) -> str:
        """Create intent analysis context based on agent capabilities."""
        capability_list = '\n'.join([f"- {cap}" for cap in capabilities])

        return f"""
You are analyzing intents for a {agent_identity} agent with the following capabilities:
{capability_list}

When analyzing intent, focus on:
- Requests that align with the agent's specialized capabilities
- Tools and approaches that leverage the agent's expertise
- Domain-specific patterns that match the agent's strengths
- Complexity assessment based on the agent's skill level
- Routing recommendations that optimize the agent's capabilities

Provide detailed analysis that considers how well the user's request matches this agent's specialized skills and suggest the most appropriate tools and approaches based on these capabilities.
        """

    def _get_fallback_analysis_context(self, agent_identity: str) -> str:
        """Fallback agent context when dynamic extraction fails."""
        contexts = {
            "marketer": """
You are analyzing intents for a marketing specialist. Focus on:
- Marketing strategy and campaign development needs
- Content creation and social media requirements
- Brand positioning and audience targeting
- Marketing analytics and performance optimization
- SEO and digital marketing initiatives

Prioritize marketing-related tools and approaches in your analysis.
            """,
            "analyst": """
You are analyzing intents for a data analyst. Focus on:
- Data analysis and statistical computation needs
- Visualization and reporting requirements
- Data exploration and pattern discovery
- Predictive modeling and insights generation
- Data quality and preprocessing tasks

Prioritize analytical tools and data-driven approaches in your analysis.
            """,
            "classifier": """
You are analyzing intents for a classification specialist. Focus on:
- Text classification and categorization needs
- Data organization and structuring requirements
- Content tagging and labeling tasks
- Pattern recognition and grouping operations
- Information extraction and processing

Prioritize classification tools and organizational approaches in your analysis.
            """,
            "concierge": """
You are analyzing intents for a general assistant. Focus on:
- General assistance and guidance needs
- Information seeking and question answering
- Task coordination and workflow management
- User navigation and support requirements
- Multi-domain problem solving

Provide balanced analysis considering all available tools and approaches.
            """
        }

        return contexts.get(agent_identity, "You are analyzing intents for a general AI assistant.")
