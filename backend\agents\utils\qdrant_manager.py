"""
Qdrant vector database manager for mem0ai.

This module provides utilities for managing the Qdrant vector database
used by mem0ai for local memory storage.
"""

import os
import time
import logging
import subprocess
import requests
from typing import Optional, Tuple, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class QdrantManager:
    """
    Manager for Qdrant vector database used by mem0ai.

    This class provides utilities for checking if Qdrant is running,
    starting it if needed, and managing its lifecycle.
    """

    DEFAULT_HOST = os.getenv("QDRANT_HOST", "localhost")
    DEFAULT_PORT = int(os.getenv("QDRANT_PORT", "6333"))
    QDRANT_STORAGE_DIR = "qdrant_storage"
    CONTAINER_NAME = "datagenius-qdrant"

    @classmethod
    def is_qdrant_running(cls, host: str = DEFAULT_HOST, port: int = DEFAULT_PORT) -> bool:
        """
        Check if Qdrant is running at the specified host and port.

        Args:
            host: The host where <PERSON>dra<PERSON> is running
            port: The port where Qdrant is running

        Returns:
            True if Qdrant is running, False otherwise
        """
        # Try multiple endpoints - Qdrant might respond to different health check paths
        # The root endpoint is most reliable for Qdrant 1.14.0+
        endpoints = ["/", "/healthz", "/health"]

        for endpoint in endpoints:
            try:
                url = f"http://{host}:{port}{endpoint}"
                logger.info(f"Checking if Qdrant is running at {url}")
                response = requests.get(url, timeout=5)

                if response.status_code == 200:
                    logger.info(f"Qdrant is running at {url}, response: {response.text}")
                    return True
                else:
                    logger.info(f"Qdrant health check at {url} returned status code {response.status_code}")
            except requests.RequestException as e:
                logger.info(f"Failed to connect to Qdrant at {url}: {str(e)}")

        # If we get here, none of the endpoints worked
        logger.info(f"Could not connect to Qdrant at {host}:{port} on any known endpoint")
        return False

    @classmethod
    def is_docker_compose_running(cls) -> bool:
        """
        Check if Docker Compose is running with Qdrant service.

        Returns:
            True if Docker Compose is running with Qdrant, False otherwise
        """
        try:
            # Check if the Qdrant container is running in Docker Compose
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={cls.CONTAINER_NAME}", "--format", "{{.Names}}"],
                capture_output=True,
                text=True,
                check=True
            )
            return cls.CONTAINER_NAME in result.stdout
        except subprocess.CalledProcessError:
            return False
        except Exception as e:
            logger.error(f"Error checking Docker Compose status: {e}")
            return False

    @classmethod
    def is_container_healthy(cls) -> bool:
        """
        Check if the Qdrant container is healthy.

        Returns:
            True if the container is healthy, False otherwise
        """
        try:
            # Check the health status of the Qdrant container
            result = subprocess.run(
                ["docker", "inspect", "--format", "{{.State.Health.Status}}", cls.CONTAINER_NAME],
                capture_output=True,
                text=True,
                check=True
            )
            status = result.stdout.strip()
            logger.debug(f"Qdrant container health status: {status}")
            return status == "healthy"
        except subprocess.CalledProcessError:
            logger.debug("Failed to get container health status")
            return False
        except Exception as e:
            logger.error(f"Error checking container health: {e}")
            return False

    @classmethod
    def get_docker_host(cls) -> str:
        """
        Get the appropriate host for connecting to Docker services.

        In Docker Compose, services can connect to each other using service names.
        When running outside Docker, use localhost.

        Returns:
            Host name to use for connections
        """
        # First check if a specific host is set in environment variables
        env_host = os.getenv("QDRANT_HOST")
        if env_host:
            logger.info(f"Using Qdrant host from environment: {env_host}")
            return env_host

        # Check if we're running inside Docker
        try:
            # This file exists only inside Docker containers
            if os.path.exists('/.dockerenv'):
                logger.info("Running inside Docker, using 'qdrant' as host")
                return "qdrant"  # Use service name in docker-compose.yml
        except Exception as e:
            logger.info(f"Error checking Docker environment: {e}")
            pass

        # Check if DOCKER_ENV environment variable is set
        docker_env = os.getenv("DOCKER_ENV", "false").lower() == "true"
        if docker_env:
            logger.info("DOCKER_ENV is set to true, using 'qdrant' as host")
            return "qdrant"

        # Try to check if Docker network is available
        try:
            # Try to ping the qdrant service by name to see if Docker network DNS is working
            logger.info("Attempting to ping 'qdrant' service to check Docker network DNS")
            ping_cmd = ["ping", "-n", "1", "-w", "1000", "qdrant"] if os.name == 'nt' else ["ping", "-c", "1", "-W", "1", "qdrant"]

            result = subprocess.run(
                ping_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=False,
                text=True
            )
            logger.info(f"Ping result: {result.returncode}, Output: {result.stdout}")
            if result.returncode == 0:
                logger.info("Docker network DNS is working, using 'qdrant' as host")
                return "qdrant"
            else:
                logger.info("Docker network DNS ping failed, will use localhost")
        except Exception as e:
            logger.info(f"Error pinging qdrant service: {e}")
            pass

        # First try localhost since we know it works
        try:
            logger.info("Checking if Qdrant is accessible at localhost")
            for endpoint in ["/", "/health", "/healthz"]:
                try:
                    response = requests.get(f"http://localhost:{cls.DEFAULT_PORT}{endpoint}", timeout=2)
                    if response.status_code == 200:
                        logger.info(f"Successfully connected to Qdrant at localhost:{cls.DEFAULT_PORT}{endpoint}")
                        return "localhost"
                except Exception:
                    pass
        except Exception as e:
            logger.info(f"Error checking localhost: {e}")

        # Try to get container IP directly as a fallback
        try:
            logger.info("Attempting to get Qdrant container IP directly")
            container_ip = subprocess.run(
                ["docker", "inspect", "-f", "{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}", cls.CONTAINER_NAME],
                capture_output=True,
                text=True,
                check=True
            ).stdout.strip()

            if container_ip:
                logger.info(f"Found Qdrant container IP: {container_ip}")
                # Test if we can connect to this IP
                try:
                    for endpoint in ["/", "/health", "/healthz"]:
                        try:
                            response = requests.get(f"http://{container_ip}:{cls.DEFAULT_PORT}{endpoint}", timeout=2)
                            if response.status_code == 200:
                                logger.info(f"Successfully connected to Qdrant at container IP {container_ip}")
                                return container_ip
                        except Exception:
                            pass
                except Exception as e:
                    logger.info(f"Failed to connect to Qdrant at container IP {container_ip}: {e}")
            else:
                logger.info("Could not determine Qdrant container IP")
        except Exception as e:
            logger.info(f"Error getting container IP: {e}")

        logger.info(f"Using default host: {cls.DEFAULT_HOST}")
        return cls.DEFAULT_HOST

    @classmethod
    def start_qdrant(cls, storage_dir: Optional[str] = None) -> Tuple[bool, Optional[subprocess.Popen]]:
        """
        Start Qdrant using Docker.

        Args:
            storage_dir: Directory to store Qdrant data

        Returns:
            Tuple of (success, process)
        """
        # First check if Qdrant is already running
        host = cls.get_docker_host()
        if cls.is_qdrant_running(host=host):
            logger.info(f"Qdrant is already running at {host}:{cls.DEFAULT_PORT}")
            return True, None

        # Check if Docker Compose is running with Qdrant
        if cls.is_docker_compose_running():
            logger.info("Qdrant container exists in Docker Compose but is not responding")
            try:
                # Check if the container is running but not healthy
                container_info = subprocess.run(
                    ["docker", "inspect", "--format", "{{.State.Status}}", cls.CONTAINER_NAME],
                    capture_output=True,
                    text=True,
                    check=True
                )
                container_status = container_info.stdout.strip()

                if container_status != "running":
                    logger.info(f"Qdrant container status is '{container_status}'. Attempting to start it...")
                    # Try to start the container if it exists but is stopped
                    subprocess.run(["docker", "start", cls.CONTAINER_NAME], check=True)
                else:
                    logger.info("Qdrant container is running but not responding. Checking health...")

                    # Check if container has health check
                    if cls.is_container_healthy():
                        logger.info("Qdrant container is healthy but not responding. This might be a network issue.")
                    else:
                        logger.info("Qdrant container is not healthy. Restarting it...")
                        subprocess.run(["docker", "restart", cls.CONTAINER_NAME], check=True)

                # Wait for Qdrant to start and become healthy
                max_retries = 2  # Increased retries for more patience
                retry_delay = 3   # Longer delay between retries
                for i in range(max_retries):
                    if cls.is_qdrant_running(host=host):
                        logger.info("Qdrant started successfully via Docker Compose")
                        return True, None

                    # Check if container is healthy
                    if i > 0 and i % 3 == 0:
                        # Try to restart the container every few attempts
                        if i % 9 == 0:
                            logger.info("Qdrant still not responding, attempting to restart container...")
                            subprocess.run(["docker", "restart", cls.CONTAINER_NAME], check=True)
                            time.sleep(5)  # Give it a bit more time after restart

                        if cls.is_container_healthy():
                            logger.info("Qdrant container is healthy but not responding. Trying different connection parameters...")
                            # Try localhost explicitly
                            if host != "localhost" and cls.is_qdrant_running(host="localhost"):
                                logger.info("Successfully connected to Qdrant at localhost:6333")
                                return True, None
                            # Try container IP directly
                            try:
                                container_ip = subprocess.run(
                                    ["docker", "inspect", "-f", "{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}", cls.CONTAINER_NAME],
                                    capture_output=True,
                                    text=True,
                                    check=True
                                ).stdout.strip()
                                if container_ip and cls.is_qdrant_running(host=container_ip):
                                    logger.info(f"Successfully connected to Qdrant at {container_ip}:6333")
                                    return True, None
                            except Exception as e:
                                logger.debug(f"Error getting container IP: {e}")

                    logger.info(f"Waiting for Qdrant to start (attempt {i+1}/{max_retries})...")
                    time.sleep(retry_delay)

                logger.error("Failed to start Qdrant within the timeout period")
                return False, None
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to start Qdrant container: {e}")
                return False, None

        # If not running in Docker Compose, start as standalone container

        # Use default storage directory if not provided
        if not storage_dir:
            # Create storage directory in the current working directory
            storage_dir = os.path.join(os.getcwd(), cls.QDRANT_STORAGE_DIR)
            os.makedirs(storage_dir, exist_ok=True)

        try:
            # Check if Docker is available
            subprocess.run(["docker", "--version"], check=True, capture_output=True)

            # Pull Qdrant image if needed
            subprocess.run(["docker", "pull", "qdrant/qdrant"], check=True)

            # Start Qdrant container
            cmd = [
                "docker", "run", "-d",
                "-p", f"{cls.DEFAULT_PORT}:{cls.DEFAULT_PORT}",
                "-p", "6334:6334",
                "-v", f"{storage_dir}:/qdrant/storage:z",
                "--name", cls.CONTAINER_NAME,
                "qdrant/qdrant"
            ]

            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # Wait for Qdrant to start
            max_retries = 2
            retry_delay = 3
            for i in range(max_retries):
                if cls.is_qdrant_running():
                    logger.info("Qdrant started successfully as standalone container")
                    return True, process

                # If we've waited a while, check container logs for issues
                if i > 5 and i % 5 == 0:
                    try:
                        logs = subprocess.run(
                            ["docker", "logs", cls.CONTAINER_NAME],
                            capture_output=True,
                            text=True,
                            check=True
                        ).stdout
                        logger.info(f"Qdrant container logs: {logs[-500:] if len(logs) > 500 else logs}")
                    except Exception as e:
                        logger.debug(f"Error getting container logs: {e}")

                logger.info(f"Waiting for Qdrant to start (attempt {i+1}/{max_retries})...")
                time.sleep(retry_delay)

            logger.error("Failed to start Qdrant within the timeout period")
            return False, process

        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to start Qdrant: {e}")
            return False, None
        except Exception as e:
            logger.error(f"Error starting Qdrant: {e}")
            return False, None

    @classmethod
    def stop_qdrant(cls) -> bool:
        """
        Stop the Qdrant Docker container.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if the container exists
            result = subprocess.run(
                ["docker", "ps", "-a", "--filter", f"name={cls.CONTAINER_NAME}", "--format", "{{.Names}}"],
                capture_output=True,
                text=True,
                check=True
            )

            if cls.CONTAINER_NAME not in result.stdout:
                logger.info("Qdrant container not found, nothing to stop")
                return True

            # Stop and remove the container
            subprocess.run(["docker", "stop", cls.CONTAINER_NAME], check=True)
            subprocess.run(["docker", "rm", cls.CONTAINER_NAME], check=True)
            logger.info("Qdrant stopped successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to stop Qdrant: {e}")
            return False
        except Exception as e:
            logger.error(f"Error stopping Qdrant: {e}")
            return False

    @classmethod
    def ensure_qdrant_running(cls) -> bool:
        """
        Ensure that Qdrant is running, starting it if needed.

        Returns:
            True if Qdrant is running, False otherwise
        """
        # Try multiple hosts to be thorough
        hosts_to_try = [cls.get_docker_host(), "localhost", "127.0.0.1"]
        if "qdrant" not in hosts_to_try:
            hosts_to_try.append("qdrant")

        # Remove duplicates while preserving order
        hosts_to_try = list(dict.fromkeys(hosts_to_try))

        logger.info(f"Checking Qdrant availability on hosts: {hosts_to_try}")

        # Try each host
        for host in hosts_to_try:
            logger.info(f"Attempting to connect to Qdrant at {host}:{cls.DEFAULT_PORT}")
            if cls.is_qdrant_running(host=host):
                logger.info(f"Qdrant is already running at {host}:{cls.DEFAULT_PORT}")
                return True
            else:
                logger.info(f"Qdrant is not running at {host}:{cls.DEFAULT_PORT}")

        # If we get here, Qdrant is not running on any of the hosts
        logger.info("Qdrant is not running on any known host, attempting to start it")

        # Check if the container exists but is not responding
        if cls.is_docker_compose_running():
            logger.info("Qdrant container exists in Docker but is not responding")

            # Get container status
            try:
                container_status = subprocess.run(
                    ["docker", "inspect", "--format", "{{.State.Status}}", cls.CONTAINER_NAME],
                    capture_output=True,
                    text=True,
                    check=True
                ).stdout.strip()
                logger.info(f"Qdrant container status: {container_status}")

                # Get health status if available
                try:
                    health_status = subprocess.run(
                        ["docker", "inspect", "--format", "{{.State.Health.Status}}", cls.CONTAINER_NAME],
                        capture_output=True,
                        text=True,
                        check=True
                    ).stdout.strip()
                    logger.info(f"Qdrant container health status: {health_status}")
                except Exception as e:
                    logger.info(f"Could not get health status: {e}")

                # Get container logs
                try:
                    logs = subprocess.run(
                        ["docker", "logs", "--tail", "20", cls.CONTAINER_NAME],
                        capture_output=True,
                        text=True,
                        check=True
                    ).stdout
                    logger.info(f"Qdrant container logs: {logs}")
                except Exception as e:
                    logger.info(f"Could not get container logs: {e}")
            except Exception as e:
                logger.info(f"Error inspecting container: {e}")

        # Try up to 3 times to start Qdrant
        max_attempts = 3
        for attempt in range(max_attempts):
            logger.info(f"Attempting to start Qdrant (attempt {attempt+1}/{max_attempts})")
            success, _ = cls.start_qdrant()
            if success:
                logger.info("Successfully started Qdrant")
                return True

            if attempt < max_attempts - 1:
                logger.warning(f"Failed to start Qdrant (attempt {attempt+1}/{max_attempts}), retrying...")
                # Try to clean up before retrying
                try:
                    logger.info("Stopping Qdrant before retry")
                    cls.stop_qdrant()
                except Exception as e:
                    logger.error(f"Error stopping Qdrant before retry: {e}")
                time.sleep(5)

        logger.error(f"Failed to start Qdrant after {max_attempts} attempts")
        return False

    @classmethod
    def get_qdrant_connection_params(cls) -> Dict[str, Any]:
        """
        Get connection parameters for Qdrant based on the current environment.

        Returns:
            Dictionary with connection parameters
        """
        host = cls.get_docker_host()
        return {
            "host": host,
            "port": cls.DEFAULT_PORT
        }
