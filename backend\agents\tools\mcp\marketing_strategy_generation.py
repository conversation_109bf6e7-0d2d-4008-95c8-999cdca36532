"""
Marketing strategy generation MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool specifically for generating marketing content
for the marketing agent. It focuses solely on marketing-related content generation with
extensible content type support.
"""

import logging
import json
import re
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from enum import Enum

from langchain_core.messages import HumanMessage, SystemMessage

from .base import BaseMCPTool
from agents.utils.model_providers.utils import get_model
from agents.utils.prompt_template import PromptTemplate
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class ContentTypeCategory(str, Enum):
    """Categories for marketing content types."""
    STRATEGIC = "strategic"
    TACTICAL = "tactical"
    OPERATIONAL = "operational"
    CREATIVE = "creative"


class DocumentFormat(str, Enum):
    """Supported document formats."""
    REPORT = "report"
    PRESENTATION = "presentation"
    BRIEF = "brief"
    CHECKLIST = "checklist"
    TEMPLATE = "template"
    GUIDE = "guide"
    ANALYSIS = "analysis"


class ContentTypeDefinition(BaseModel):
    """Definition of a marketing content type."""
    id: str = Field(..., description="Unique identifier for the content type")
    name: str = Field(..., description="Human-readable name")
    description: str = Field(..., description="Description of what this content type generates")
    category: ContentTypeCategory = Field(..., description="Category of the content type")
    required_fields: List[str] = Field(default_factory=list, description="Required input fields")
    optional_fields: List[str] = Field(default_factory=list, description="Optional input fields")
    supported_formats: List[DocumentFormat] = Field(default_factory=lambda: [DocumentFormat.REPORT], description="Supported output formats")
    examples: List[str] = Field(default_factory=list, description="Example use cases")
    keywords: List[str] = Field(default_factory=list, description="Keywords associated with this content type")


class ContentTypeIntelligence:
    """AI-powered content type analysis and understanding."""

    def __init__(self):
        self.default_content_types = self._get_default_content_types()

    def _get_default_content_types(self) -> Dict[str, ContentTypeDefinition]:
        """Get the default content type definitions."""
        return {
            "marketing_strategy": ContentTypeDefinition(
                id="marketing_strategy",
                name="Marketing Strategy",
                description="Comprehensive strategic marketing plan with market analysis, positioning, and implementation roadmap",
                category=ContentTypeCategory.STRATEGIC,
                required_fields=["brand_description", "target_audience", "products_services", "marketing_goals"],
                optional_fields=["existing_content", "keywords", "suggested_topics", "competitive_landscape"],
                supported_formats=[DocumentFormat.REPORT, DocumentFormat.PRESENTATION],
                examples=["Annual marketing strategy", "Product launch strategy", "Market entry strategy"],
                keywords=["strategy", "planning", "market analysis", "positioning", "roadmap"]
            ),
            "campaign_strategy": ContentTypeDefinition(
                id="campaign_strategy",
                name="Campaign Strategy",
                description="Creative campaign concepts with multi-channel approaches and execution plans",
                category=ContentTypeCategory.TACTICAL,
                required_fields=["brand_description", "target_audience", "marketing_goals"],
                optional_fields=["products_services", "keywords", "suggested_topics", "budget", "timeline"],
                supported_formats=[DocumentFormat.BRIEF, DocumentFormat.PRESENTATION],
                examples=["Product launch campaign", "Brand awareness campaign", "Seasonal promotion"],
                keywords=["campaign", "creative", "execution", "channels", "tactics"]
            ),
            "social_media_content": ContentTypeDefinition(
                id="social_media_content",
                name="Social Media Content",
                description="Engaging social media posts and content strategy for various platforms",
                category=ContentTypeCategory.CREATIVE,
                required_fields=["brand_description", "target_audience"],
                optional_fields=["products_services", "existing_content", "keywords", "suggested_topics", "platforms"],
                supported_formats=[DocumentFormat.TEMPLATE, DocumentFormat.GUIDE],
                examples=["Instagram posts", "LinkedIn content", "Twitter campaigns", "Content calendar"],
                keywords=["social media", "posts", "engagement", "platforms", "content"]
            ),
            "seo_optimization": ContentTypeDefinition(
                id="seo_optimization",
                name="SEO Optimization",
                description="Search engine optimization strategy with keyword research and content recommendations",
                category=ContentTypeCategory.TACTICAL,
                required_fields=["brand_description", "target_audience", "keywords"],
                optional_fields=["products_services", "existing_content", "competitors"],
                supported_formats=[DocumentFormat.ANALYSIS, DocumentFormat.CHECKLIST],
                examples=["SEO audit", "Keyword strategy", "Content optimization", "Technical SEO"],
                keywords=["seo", "keywords", "optimization", "search", "ranking"]
            ),
            "post_composer": ContentTypeDefinition(
                id="post_composer",
                name="Post Composer",
                description="Individual social media posts with platform-specific optimization",
                category=ContentTypeCategory.CREATIVE,
                required_fields=["brand_description", "suggested_topics"],
                optional_fields=["target_audience", "keywords", "platform", "tone"],
                supported_formats=[DocumentFormat.TEMPLATE],
                examples=["Instagram post", "LinkedIn article", "Twitter thread", "Facebook update"],
                keywords=["post", "social", "content", "engagement", "platform"]
            )
        }

    def get_content_type_definition(self, content_type: str) -> Optional[ContentTypeDefinition]:
        """Get definition for a content type."""
        return self.default_content_types.get(content_type)

    def is_valid_content_type(self, content_type: str) -> bool:
        """Check if a content type is valid (either predefined or semantically valid)."""
        # Check if it's a predefined type
        if content_type in self.default_content_types:
            return True

        # Check if it's a semantically valid marketing content type
        return self._is_semantic_marketing_content_type(content_type)

    def _is_semantic_marketing_content_type(self, content_type: str) -> bool:
        """Check if a content type is semantically valid for marketing."""
        if not content_type or not isinstance(content_type, str):
            return False

        # Clean and normalize the content type
        normalized = content_type.lower().strip().replace(' ', '_')

        # Check length constraints
        if len(normalized) < 3 or len(normalized) > 50:
            return False

        # Check for valid characters (alphanumeric, underscore, hyphen)
        if not re.match(r'^[a-z0-9_-]+$', normalized):
            return False

        # Marketing-related keywords that indicate valid content types
        marketing_keywords = [
            'strategy', 'campaign', 'content', 'social', 'media', 'seo', 'marketing',
            'brand', 'advertising', 'promotion', 'analysis', 'plan', 'brief',
            'guide', 'template', 'report', 'audit', 'research', 'positioning',
            'messaging', 'creative', 'copy', 'email', 'newsletter', 'blog',
            'video', 'infographic', 'presentation', 'proposal', 'pitch',
            'competitive', 'market', 'customer', 'persona', 'journey',
            'funnel', 'conversion', 'engagement', 'awareness', 'lead',
            'acquisition', 'retention', 'loyalty', 'influencer', 'pr',
            'public_relations', 'crisis', 'communication', 'launch',
            'product', 'service', 'event', 'webinar', 'case_study'
        ]

        # Check if the content type contains marketing-related keywords
        return any(keyword in normalized for keyword in marketing_keywords)

    async def analyze_content_type(self, content_type: str, context: Dict[str, Any] = None) -> ContentTypeDefinition:
        """Analyze and create a definition for a new content type using AI."""
        # For now, create a basic definition for unknown content types
        # This can be enhanced with AI analysis in the future

        if content_type in self.default_content_types:
            return self.default_content_types[content_type]

        # Create a basic definition for unknown but valid content types
        if self._is_semantic_marketing_content_type(content_type):
            return ContentTypeDefinition(
                id=content_type,
                name=content_type.replace('_', ' ').title(),
                description=f"Custom marketing content: {content_type.replace('_', ' ')}",
                category=ContentTypeCategory.TACTICAL,  # Default category
                required_fields=["brand_description", "target_audience"],
                optional_fields=["products_services", "marketing_goals", "keywords", "suggested_topics"],
                supported_formats=[DocumentFormat.REPORT],
                examples=[f"Custom {content_type.replace('_', ' ')} document"],
                keywords=[content_type.replace('_', ' ')]
            )

        # Fallback for invalid content types
        raise ValueError(f"Invalid content type: {content_type}. Must be a marketing-related content type.")

    def get_all_content_types(self) -> Dict[str, ContentTypeDefinition]:
        """Get all available content type definitions."""
        return self.default_content_types.copy()

    def suggest_content_types(self, query: str) -> List[str]:
        """Suggest content types based on a query."""
        query_lower = query.lower()
        suggestions = []

        for content_type, definition in self.default_content_types.items():
            # Check if query matches name, description, or keywords
            if (query_lower in definition.name.lower() or
                query_lower in definition.description.lower() or
                any(query_lower in keyword.lower() for keyword in definition.keywords)):
                suggestions.append(content_type)

        return suggestions[:10]  # Limit to top 10 suggestions


class MarketingStrategyGenerationTool(BaseMCPTool):
    """Tool for generating marketing content with extensible content type support."""

    def __init__(self):
        """Initialize the marketing strategy generation tool."""
        super().__init__(
            name="generate_marketing_content",
            description="Generate any type of marketing document or strategy with AI-powered content understanding",
            input_schema={
                "type": "object",
                "properties": {
                    "content_type": {
                        "type": "string",
                        "description": "Type of marketing content to generate (e.g., 'marketing_strategy', 'brand_positioning', 'competitive_analysis', 'content_calendar', etc.)"
                    },
                    "document_format": {
                        "type": "string",
                        "enum": ["report", "presentation", "brief", "checklist", "template", "guide", "analysis"],
                        "description": "Format of the output document",
                        "default": "report"
                    },
                    "brand_description": {
                        "type": "string",
                        "description": "Description of the brand"
                    },
                    "target_audience": {
                        "type": "string",
                        "description": "Description of the target audience"
                    },
                    "products_services": {
                        "type": "string",
                        "description": "Description of products or services"
                    },
                    "marketing_goals": {
                        "type": "string",
                        "description": "Marketing goals and objectives"
                    },
                    "existing_content": {
                        "type": "string",
                        "description": "Existing marketing content"
                    },
                    "keywords": {
                        "type": "string",
                        "description": "Keywords to target"
                    },
                    "suggested_topics": {
                        "type": "string",
                        "description": "Suggested topics for content"
                    },
                    "tone": {
                        "type": "string",
                        "description": "Tone of the content"
                    },
                    "competitive_landscape": {
                        "type": "string",
                        "description": "Information about competitors and market landscape"
                    },
                    "budget": {
                        "type": "string",
                        "description": "Budget constraints or considerations"
                    },
                    "timeline": {
                        "type": "string",
                        "description": "Timeline or deadline information"
                    },
                    "platforms": {
                        "type": "string",
                        "description": "Specific platforms or channels to focus on"
                    },
                    "provider": {
                        "type": "string",
                        "description": "AI provider to use"
                    },
                    "model": {
                        "type": "string",
                        "description": "AI model to use"
                    },
                    "temperature": {
                        "type": "number",
                        "description": "Temperature for content generation"
                    },
                    "is_regeneration": {
                        "type": "boolean",
                        "description": "Whether this is a regeneration request"
                    },
                    "prompt_override": {
                        "type": "string",
                        "description": "Optional raw prompt to use instead of dynamic generation"
                    },
                    "file_data": {
                        "type": "object",
                        "description": "Optional file data to incorporate into content generation"
                    }
                },
                "required": ["content_type", "brand_description"]
            },
            annotations={
                "title": "Generate Marketing Content",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.prompt_templates = {}
        self.content_type_intelligence = ContentTypeIntelligence()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # Load prompt templates if provided
        if "prompt_templates" in config:
            self.prompt_templates = config["prompt_templates"]
            logger.info(f"Loaded {len(self.prompt_templates)} marketing prompt templates")

        # Ensure we have default templates for essential marketing content types
        # Note: We intentionally don't add default templates here anymore
        # This will force the system to use the detailed fallback templates
        # that properly include all the form data
        logger.info("Marketing tool initialized - will use detailed fallback templates")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            if not arguments:
                raise ValueError("No arguments provided to marketing strategy generation tool")

            content_type = arguments["content_type"]

            # Validate and analyze content type
            if not self.content_type_intelligence.is_valid_content_type(content_type):
                raise ValueError(f"Invalid content type: '{content_type}'. Must be a marketing-related content type.")

            # Get content type definition
            try:
                content_type_def = await self.content_type_intelligence.analyze_content_type(content_type)
                logger.info(f"Using content type definition: {content_type_def.name} ({content_type_def.category})")
            except Exception as e:
                logger.error(f"Error analyzing content type '{content_type}': {e}")
                raise ValueError(f"Unable to process content type '{content_type}': {e}")

            # Extract generation parameters
            provider_id = arguments.get("provider", "groq")
            model_name = arguments.get("model", "llama3-70b-8192")
            temperature = arguments.get("temperature", 0.7)
            document_format = arguments.get("document_format", "report")
            is_regeneration = arguments.get("is_regeneration", False)
            file_data = arguments.get("file_data")

            # Extract marketing-specific parameters
            brand_description = arguments.get("brand_description", "")
            target_audience = arguments.get("target_audience", "")
            products_services = arguments.get("products_services", "")
            marketing_goals = arguments.get("marketing_goals", "")
            existing_content = arguments.get("existing_content", "")
            keywords = arguments.get("keywords", "")
            suggested_topics = arguments.get("suggested_topics", "")
            tone = arguments.get("tone", "professional")

            # Extract additional context fields
            competitive_landscape = arguments.get("competitive_landscape", "")
            budget = arguments.get("budget", "")
            timeline = arguments.get("timeline", "")
            platforms = arguments.get("platforms", "")

            # Agent context for dynamic identity detection
            user_context = arguments.get("context", {})
            agent_id = arguments.get("persona_id") or arguments.get("agent_id")

            # Debug log all received arguments
            logger.info(f"MARKETING TOOL RECEIVED ARGUMENTS:")
            logger.info(f"- content_type: '{content_type}'")
            logger.info(f"- brand_description: '{brand_description}' (length: {len(brand_description)})")
            logger.info(f"- target_audience: '{target_audience}' (length: {len(target_audience)})")
            logger.info(f"- products_services: '{products_services}' (length: {len(products_services)})")
            logger.info(f"- marketing_goals: '{marketing_goals}' (length: {len(marketing_goals)})")
            logger.info(f"- existing_content: '{existing_content}' (length: {len(existing_content)})")
            logger.info(f"- keywords: '{keywords}' (length: {len(keywords)})")
            logger.info(f"- suggested_topics: '{suggested_topics}' (length: {len(suggested_topics)})")
            logger.info(f"- tone: '{tone}'")
            logger.info(f"- provider_id: '{provider_id}'")
            logger.info(f"- model_name: '{model_name}'")
            logger.info(f"- temperature: {temperature}")
            logger.info(f"- is_regeneration: {is_regeneration}")
            logger.info(f"- file_data: {file_data is not None}")
            logger.info(f"- ALL ARGUMENTS: {arguments}")

            # Log field status for debugging
            empty_fields = []
            if not brand_description.strip():
                empty_fields.append("brand_description")
            if not target_audience.strip():
                empty_fields.append("target_audience")
            if not products_services.strip():
                empty_fields.append("products_services")
            if not marketing_goals.strip():
                empty_fields.append("marketing_goals")

            if empty_fields:
                logger.warning(f"Empty fields detected: {empty_fields}. Will proceed with available information.")
            else:
                logger.info("All core marketing fields have content.")

            # Log the final prompt that will be sent to the LLM
            logger.info("=== FINAL PROMPT PREVIEW ===")
            if brand_description:
                logger.info(f"Brand Description in prompt: '{brand_description[:100]}...'")
            if target_audience:
                logger.info(f"Target Audience in prompt: '{target_audience[:100]}...'")
            logger.info("=== END PROMPT PREVIEW ===")

            # Detect agent identity for personalized content generation
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=user_context,
                intent_type="content_generation"
            )

            logger.info(f"Detected agent identity: {agent_identity} for marketing content generation")

            # Process file data if available
            file_context = ""
            if file_data:
                file_context = self._process_file_data(file_data)
                logger.info("Incorporated file data into marketing content generation")

            # Check for prompt override
            prompt_override = arguments.get("prompt_override")
            if prompt_override:
                logger.info("Using prompt override instead of dynamic generation")
                final_prompt = prompt_override
            else:
                logger.info(f"Generating {content_type} marketing content with dynamic AI-powered prompt")
                # Build dynamic prompt using content type intelligence
                final_prompt = await self._build_dynamic_marketing_prompt(
                    content_type_def,
                    {
                        "brand_description": brand_description,
                        "target_audience": target_audience,
                        "products_services": products_services,
                        "marketing_goals": marketing_goals,
                        "existing_content": existing_content,
                        "keywords": keywords,
                        "suggested_topics": suggested_topics,
                        "tone": tone,
                        "competitive_landscape": competitive_landscape,
                        "budget": budget,
                        "timeline": timeline,
                        "platforms": platforms,
                        "document_format": document_format,
                        "is_regeneration": is_regeneration,
                        "file_context": file_context
                    }
                )

                # Log the actual prompt being sent to the LLM for debugging
                logger.info("=== ACTUAL PROMPT BEING SENT TO LLM ===")
                logger.info(f"Prompt length: {len(final_prompt)} characters")
                logger.info(f"First 500 characters: {final_prompt[:500]}")
                logger.info("=== END ACTUAL PROMPT ===")

            # Initialize LLM client using the model provider system
            try:
                llm = await get_model(provider_id, model_name, {"temperature": temperature})
                logger.info(f"Successfully initialized model from provider '{provider_id}'")
            except Exception as e:
                logger.error(f"Error initializing model: {str(e)}", exc_info=True)
                raise ValueError(f"Failed to initialize model: {str(e)}")

            # Get agent-specific system prompt for better content generation
            system_prompt = await self._create_agent_aware_system_prompt(
                agent_identity, content_type, user_context
            )

            # Generate content using SystemMessage + HumanMessage for agent-aware marketing content
            system_message = SystemMessage(content=system_prompt)
            user_message = HumanMessage(content=final_prompt)

            logger.info(f"Generating marketing content for type: {content_type} with agent identity: {agent_identity}")

            # Generate content
            try:
                if hasattr(llm, 'ainvoke'):
                    response = await llm.ainvoke([system_message, user_message])
                    generated_content = response.content if hasattr(response, 'content') else str(response)
                else:
                    raise NotImplementedError("Model does not support ainvoke method")

                logger.info(f"Successfully generated marketing content for type: {content_type}")
            except Exception as e:
                logger.error(f"Error generating marketing content: {str(e)}", exc_info=True)
                raise ValueError(f"Failed to generate marketing content: {str(e)}")

            return {
                "content": [
                    {
                        "type": "text",
                        "text": generated_content
                    }
                ],
                "metadata": {
                    "content_type": content_type,
                    "agent_identity": agent_identity,
                    "agent_aware": True,
                    "parameters": {
                        "brand_description": brand_description[:100] + "..." if len(brand_description) > 100 else brand_description,
                        "target_audience": target_audience[:100] + "..." if len(target_audience) > 100 else target_audience,
                        "tone": tone
                    }
                }
            }

        except Exception as e:
            logger.error(f"Error in MarketingStrategyGenerationTool execute: {str(e)}", exc_info=True)
            logger.error(f"Full error details: {repr(e)}")
            logger.error(f"Error type: {type(e).__name__}")

            # Create detailed error message for debugging
            error_details = {
                "error_message": str(e),
                "error_type": type(e).__name__,
                "error_repr": repr(e),
                "arguments_keys": list(arguments.keys()) if arguments else [],
                "content_type": arguments.get("content_type", "unknown") if arguments else "unknown"
            }

            logger.error(f"Detailed error info: {error_details}")

            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error generating marketing content: {str(e)} (Type: {type(e).__name__})"
                    }
                ],
                "metadata": {
                    "error_type": e.__class__.__name__,
                    "error_details": str(e),
                    "component": "MarketingStrategyGenerationTool",
                    "debug_info": error_details
                }
            }

    def _process_file_data(self, file_data: Dict[str, Any]) -> str:
        """Process file data and extract relevant context for marketing content generation."""
        if not file_data:
            return ""

        try:
            # Extract text content from file data
            content_items = file_data.get("content", [])
            file_text = ""

            for item in content_items:
                if item.get("type") == "text":
                    file_text += item.get("text", "") + "\n"

            if file_text.strip():
                return f"\n\nADDITIONAL CONTEXT FROM UPLOADED FILE:\n{file_text.strip()}\n"

            # If no text content, check for metadata
            metadata = file_data.get("metadata", {})
            if metadata:
                context_parts = []
                for key, value in metadata.items():
                    if isinstance(value, (str, int, float)) and str(value).strip():
                        context_parts.append(f"{key}: {value}")

                if context_parts:
                    return f"\n\nADDITIONAL CONTEXT FROM FILE METADATA:\n" + "\n".join(context_parts) + "\n"

            return ""

        except Exception as e:
            logger.error(f"Error processing file data: {e}")
            return ""

    async def _build_dynamic_marketing_prompt(
        self,
        content_type_def: ContentTypeDefinition,
        context: Dict[str, Any]
    ) -> str:
        """
        Build a dynamic marketing prompt based on content type definition and context.

        Args:
            content_type_def: Content type definition
            context: Context dictionary with all available information

        Returns:
            Dynamically generated prompt text
        """
        # Extract context values
        brand_description = context.get("brand_description", "")
        target_audience = context.get("target_audience", "")
        products_services = context.get("products_services", "")
        marketing_goals = context.get("marketing_goals", "")
        existing_content = context.get("existing_content", "")
        keywords = context.get("keywords", "")
        suggested_topics = context.get("suggested_topics", "")
        tone = context.get("tone", "professional")
        competitive_landscape = context.get("competitive_landscape", "")
        budget = context.get("budget", "")
        timeline = context.get("timeline", "")
        platforms = context.get("platforms", "")
        document_format = context.get("document_format", "report")
        is_regeneration = context.get("is_regeneration", False)
        file_context = context.get("file_context", "")

        # Build context section with available information
        context_lines = []

        if brand_description.strip():
            context_lines.append(f"**Brand Description:** {brand_description}")

        if target_audience.strip():
            context_lines.append(f"**Target Audience:** {target_audience}")

        if products_services.strip():
            context_lines.append(f"**Products/Services:** {products_services}")

        if marketing_goals.strip():
            context_lines.append(f"**Marketing Goals:** {marketing_goals}")

        if existing_content.strip():
            context_lines.append(f"**Existing Content:** {existing_content}")

        if keywords.strip():
            context_lines.append(f"**Keywords:** {keywords}")

        if suggested_topics.strip():
            context_lines.append(f"**Suggested Topics:** {suggested_topics}")

        if competitive_landscape.strip():
            context_lines.append(f"**Competitive Landscape:** {competitive_landscape}")

        if budget.strip():
            context_lines.append(f"**Budget:** {budget}")

        if timeline.strip():
            context_lines.append(f"**Timeline:** {timeline}")

        if platforms.strip():
            context_lines.append(f"**Platforms:** {platforms}")

        context_lines.append(f"**Tone:** {tone}")

        context_section = "\n".join(context_lines)

        # Add file context if available
        if file_context:
            context_section += f"\n\n**Additional Context from Files:**\n{file_context}"

        # Build format-specific instructions
        format_instructions = self._get_format_instructions(document_format)

        # Build category-specific guidance
        category_guidance = self._get_category_guidance(content_type_def.category)

        # Add regeneration note if needed
        regeneration_note = ""
        if is_regeneration:
            regeneration_note = "\n\n**REGENERATION REQUEST:** This is a request to regenerate content. Please provide a fresh perspective or alternative approach while maintaining the core requirements."

        # Build the complete dynamic prompt
        dynamic_prompt = f"""You are tasked with creating a professional {content_type_def.name}.

**Content Type:** {content_type_def.name}
**Description:** {content_type_def.description}
**Output Format:** {document_format.title()}

## Business Context
{context_section}

## Requirements
{category_guidance}

{format_instructions}

## Important Guidelines
- Use ONLY the provided business information above. Do NOT use any example companies or generic information.
- Base all recommendations on the actual brand, audience, and goals provided.
- Create actionable, specific, and practical content.
- Ensure the content aligns with the specified tone and business objectives.
- Structure the content professionally and make it easy to understand and implement.

{regeneration_note}

Please generate the {content_type_def.name} now."""

        logger.info(f"Generated dynamic prompt for {content_type_def.name} ({len(dynamic_prompt)} characters)")
        return dynamic_prompt

    def _get_format_instructions(self, document_format: str) -> str:
        """Get format-specific instructions."""
        format_instructions = {
            "report": """
## Output Format Requirements
- Structure as a comprehensive report with clear sections and subsections
- Include executive summary, detailed analysis, and actionable recommendations
- Use professional formatting with headers, bullet points, and numbered lists
- Provide specific metrics and KPIs where applicable""",

            "presentation": """
## Output Format Requirements
- Structure as presentation slides with clear titles and bullet points
- Include slide titles and concise, impactful content for each slide
- Focus on key insights and visual elements
- Limit text per slide and emphasize main points""",

            "brief": """
## Output Format Requirements
- Create a concise, focused brief document
- Include key objectives, strategies, and next steps
- Use clear, direct language and bullet points
- Keep content actionable and to-the-point""",

            "checklist": """
## Output Format Requirements
- Present as an actionable checklist with clear items
- Include checkboxes or numbered action items
- Organize by priority or chronological order
- Provide specific, measurable tasks""",

            "template": """
## Output Format Requirements
- Create a reusable template structure
- Include placeholders for customization
- Provide clear instructions for each section
- Make it easy to adapt for different scenarios""",

            "guide": """
## Output Format Requirements
- Structure as a step-by-step guide
- Include clear instructions and best practices
- Provide examples and tips throughout
- Make it educational and easy to follow""",

            "analysis": """
## Output Format Requirements
- Present as detailed analytical document
- Include data interpretation and insights
- Provide evidence-based conclusions
- Structure with clear methodology and findings"""
        }

        return format_instructions.get(document_format, format_instructions["report"])

    def _get_category_guidance(self, category: ContentTypeCategory) -> str:
        """Get category-specific guidance."""
        category_guidance = {
            ContentTypeCategory.STRATEGIC: """
- Focus on long-term planning and strategic thinking
- Include market analysis and competitive positioning
- Provide comprehensive roadmaps and implementation plans
- Consider ROI and business impact
- Address scalability and sustainability""",

            ContentTypeCategory.TACTICAL: """
- Focus on specific tactics and execution methods
- Provide detailed implementation steps
- Include timeline and resource requirements
- Address measurement and optimization
- Consider practical constraints and challenges""",

            ContentTypeCategory.OPERATIONAL: """
- Focus on day-to-day operations and processes
- Provide clear workflows and procedures
- Include resource allocation and scheduling
- Address efficiency and productivity
- Consider team coordination and communication""",

            ContentTypeCategory.CREATIVE: """
- Focus on creative concepts and engaging content
- Provide innovative ideas and approaches
- Include visual and design considerations
- Address audience engagement and emotional connection
- Consider brand voice and creative consistency"""
        }

        return category_guidance.get(category, category_guidance[ContentTypeCategory.TACTICAL])

    async def _get_marketing_prompt_template(
        self,
        content_type: str,
        brand_description: str,
        target_audience: str,
        products_services: str,
        marketing_goals: str,
        existing_content: str,
        keywords: str,
        suggested_topics: str,
        tone: str,
        is_regeneration: bool = False,
        file_context: str = ""
    ) -> str:
        """
        Get the appropriate marketing prompt template for the content type.

        Args:
            content_type: Type of marketing content to generate
            brand_description: Description of the brand
            target_audience: Description of the target audience
            products_services: Description of products or services
            marketing_goals: Marketing goals
            existing_content: Existing marketing content
            keywords: Keywords to target
            suggested_topics: Suggested topics
            tone: Tone of the content
            is_regeneration: Whether this is a regeneration request

        Returns:
            Formatted prompt text
        """
        # Map content types to prompt template names
        prompt_mapping = {
            "marketing_strategy": "marketing_strategy",
            "campaign_strategy": "campaign_strategy",
            "social_media_content": "social_media",
            "seo_optimization": "seo_optimization",
            "post_composer": "post_composer",
            "blog_content": "blog_content"
        }

        prompt_name = prompt_mapping.get(content_type)

        # Check if we have a template for this content type
        if prompt_name and prompt_name in self.prompt_templates:
            logger.info(f"Using configured template for '{prompt_name}'")
            try:
                # Add regeneration note if needed
                regeneration_note = ""
                if is_regeneration:
                    regeneration_note = "\n\nNOTE: This is a request to regenerate content. Please provide a fresh perspective or alternative approach while maintaining the core requirements."

                template = PromptTemplate(self.prompt_templates[prompt_name])
                prompt_text = template.format(
                    brand_description=brand_description,
                    target_audience=target_audience,
                    products_services=products_services,
                    marketing_goals=marketing_goals,
                    existing_content=existing_content,
                    keywords=keywords,
                    suggested_topics=suggested_topics,
                    tone=tone
                )

                if is_regeneration:
                    prompt_text += regeneration_note

                logger.info(f"Configured template generated prompt of {len(prompt_text)} characters")
                return prompt_text
            except Exception as e:
                logger.error(f"Error formatting marketing prompt template '{prompt_name}': {str(e)}", exc_info=True)
                # Fall through to fallback templates

        # Fallback to hardcoded marketing templates
        logger.info(f"Using fallback template for '{content_type}' (no configured template found)")
        return self._get_fallback_marketing_template(
            content_type,
            brand_description,
            target_audience,
            products_services,
            marketing_goals,
            existing_content,
            keywords,
            suggested_topics,
            tone,
            is_regeneration,
            file_context
        )

    def _get_fallback_marketing_template(
        self,
        content_type: str,
        brand_description: str,
        target_audience: str,
        products_services: str,
        marketing_goals: str,
        existing_content: str,
        keywords: str,
        suggested_topics: str,
        tone: str,
        is_regeneration: bool = False,
        file_context: str = ""
    ) -> str:
        """Get fallback marketing templates when configured templates are not available."""

        # Add regeneration note if needed
        regeneration_note = ""
        if is_regeneration:
            regeneration_note = "\n\nNOTE: This is a request to regenerate content. Please provide a fresh perspective or alternative approach while maintaining the core requirements."

        # Helper function to format field values, handling empty fields gracefully
        def format_field(field_name: str, field_value: str, default_text: str = "Not specified") -> str:
            if field_value and field_value.strip():
                return f"- {field_name}: {field_value.strip()}"
            else:
                return f"- {field_name}: {default_text}"

        # Build brand information section with available data
        brand_info_lines = [
            format_field("Brand Description", brand_description, "Please provide details about your brand"),
            format_field("Target Audience", target_audience, "Please specify your target audience"),
            format_field("Products/Services", products_services, "Please describe your products or services"),
            format_field("Marketing Goals", marketing_goals, "Please outline your marketing objectives"),
            format_field("Existing Content", existing_content, "No existing content specified"),
            format_field("Keywords", keywords, "No specific keywords provided"),
            format_field("Suggested Topics", suggested_topics, "No specific topics suggested"),
            format_field("Tone", tone, "Professional")
        ]

        brand_info_section = "\n".join(brand_info_lines)
        if file_context:
            brand_info_section += file_context

        templates = {
            "marketing_strategy": f"""
You are a strategic marketing consultant tasked with developing a comprehensive marketing strategy.

## IMPORTANT: Use ONLY the provided business information below. Do NOT use any example companies or generic information.

## BRAND INFORMATION (USE THIS EXACT INFORMATION):
- Brand Description: {brand_description}
- Target Audience: {target_audience}
- Products/Services: {products_services}
- Marketing Goals: {marketing_goals}
- Existing Content: {existing_content}
- Keywords: {keywords}
- Suggested Topics: {suggested_topics}
- Tone: {tone}{file_context}

## Instructions:
Create a comprehensive marketing strategy that includes:
1. Executive Summary (based on the actual brand and products described above)
2. Market Analysis (using the specific industry and market context provided)
3. Target Audience Analysis (using the specific target audience provided)
4. Competitive Analysis (based on the actual products/services described)
5. Marketing Objectives (aligned with the specific marketing goals provided)
6. Marketing Tactics and Channels (tailored to the target audience and goals)
7. Content Strategy (incorporating the suggested topics provided)
8. Budget Considerations
9. Timeline and Implementation Plan
10. KPIs and Success Metrics

CRITICAL: Base your entire strategy on the actual brand description, target audience, products/services, and marketing goals provided above. Do NOT use any fictional company names or examples like "EcoCycle" or "GreenTech". Use only the information provided in the brand information section.

Ensure the strategy is specific to the brand, practical, and actionable.{regeneration_note}
            """,
            "campaign_strategy": f"""
You are a creative campaign director tasked with developing innovative marketing campaigns.

BRAND INFORMATION:
- Brand Description: {brand_description}
- Target Audience: {target_audience}
- Products/Services: {products_services}
- Marketing Goals: {marketing_goals}
- Existing Content: {existing_content}
- Keywords: {keywords}
- Suggested Topics: {suggested_topics}
- Tone: {tone}{file_context}

Generate 5 distinct, creative campaign concepts that align with the brand identity and will resonate with the target audience.
Each campaign should be achievable with realistic resources and have clear business impact.

For each of the 5 campaigns, provide:

### Campaign [Number]: [Creative Name]
* Concept: Brief explanation of the campaign idea and creative angle
* Target Segment: Specific audience segment this will appeal to most
* Core Message: The primary takeaway for the audience
* Campaign Elements: List of deliverables (videos, posts, emails, etc.)
* Channels: Primary platforms for distribution
* Timeline: Suggested duration and key milestones
* Success Metrics: How to measure campaign effectiveness
* Estimated Impact: Expected outcomes tied to marketing goals{regeneration_note}
            """,
            "social_media_content": f"""
You are a social media content creator tasked with creating engaging posts.

BRAND INFORMATION:
- Brand Description: {brand_description}
- Target Audience: {target_audience}
- Products/Services: {products_services}
- Marketing Goals: {marketing_goals}
- Existing Content: {existing_content}
- Keywords: {keywords}
- Suggested Topics: {suggested_topics}
- Tone: {tone}{file_context}

Create 5 engaging social media posts for different platforms (Instagram, Facebook, Twitter, LinkedIn) that include:
1. Post text (appropriate length for the platform)
2. Hashtags (where appropriate)
3. Call to action
4. Image/video description suggestion

Ensure the posts are engaging, on-brand, and designed to drive engagement and conversions.{regeneration_note}
            """,
            "seo_optimization": f"""
You are an SEO specialist developing a comprehensive search optimization strategy.

BRAND INFORMATION:
- Brand Description: {brand_description}
- Target Audience: {target_audience}
- Products/Services: {products_services}
- Marketing Goals: {marketing_goals}
- Keywords: {keywords}
- Existing Content: {existing_content}
- Suggested Topics: {suggested_topics}
- Tone: {tone}{file_context}

Create a detailed SEO strategy that will improve organic visibility and drive qualified traffic.
Focus on both quick wins and long-term sustainable growth.
Provide specific, actionable recommendations rather than general advice.{regeneration_note}
            """,
            "post_composer": f"""
You are a content creator tasked with writing social media posts.

BRAND INFORMATION:
- Brand Description: {brand_description}
- Target Audience: {target_audience}
- Products/Services: {products_services}
- Marketing Goals: {marketing_goals}
- Existing Content: {existing_content}
- Keywords: {keywords}
- Suggested Topics: {suggested_topics}
- Tone: {tone}{file_context}

Create 5 engaging social media posts that include:
1. Post text (appropriate length for the platform)
2. Hashtags (where appropriate)
3. Call to action
4. Image/video description suggestion

Create posts for these platforms: Instagram, Facebook, Twitter, LinkedIn

Ensure the posts are engaging, on-brand, and designed to drive engagement and conversions.{regeneration_note}
            """,
            "blog_content": f"""
You are a professional blog content writer tasked with creating engaging blog posts.

BRAND INFORMATION:
- Brand Description: {brand_description}
- Target Audience: {target_audience}
- Products/Services: {products_services}
- Marketing Goals: {marketing_goals}
- Existing Content: {existing_content}
- Keywords: {keywords}
- Selected Blog Topics: {suggested_topics}
- Tone: {tone}{file_context}

Create a comprehensive blog post based on the selected topics. The blog post should include:

1. **Compelling Headline**: Create an attention-grabbing title that incorporates relevant keywords
2. **Introduction**: Hook the reader and clearly state what they'll learn
3. **Main Content**: Well-structured sections with subheadings that cover the selected topics
4. **SEO Optimization**: Naturally incorporate the provided keywords throughout the content
5. **Call to Action**: End with a clear next step for readers
6. **Meta Description**: Provide a 150-160 character meta description for SEO

## Content Requirements:
- Word count: 800-1200 words
- Use the specified tone throughout
- Make it valuable and actionable for the target audience
- Include relevant examples or case studies where appropriate
- Ensure the content aligns with the brand voice and marketing goals
- Structure with clear headings and subheadings for readability

## Focus Areas:
Based on the selected blog topics: {suggested_topics}

Create content that educates, engages, and drives the reader toward the marketing goals while maintaining the brand's voice and expertise.{regeneration_note}
            """
        }

        return templates.get(content_type, f"Please generate {content_type} content based on the provided brand information.{file_context}{regeneration_note}")

    async def _create_agent_aware_system_prompt(
        self,
        agent_identity: str,
        content_type: str,
        user_context: Dict[str, Any]
    ) -> str:
        """
        Create an agent-aware system prompt for marketing content generation.

        Args:
            agent_identity: The detected agent identity
            content_type: Type of marketing content being generated
            user_context: User context for customization

        Returns:
            Agent-specific system prompt
        """
        # Get base system prompt for the agent identity
        base_prompt = await get_agent_system_prompt(agent_identity, user_context)

        # Add marketing-specific context based on content type
        marketing_context = self._get_marketing_context_for_content_type(content_type)

        # Combine base prompt with marketing-specific instructions
        enhanced_prompt = f"""{base_prompt}

MARKETING CONTENT GENERATION CONTEXT:
{marketing_context}

IMPORTANT INSTRUCTIONS FOR MARKETING CONTENT:
- Focus on creating high-quality, engaging marketing content
- Ensure all content aligns with the brand voice and target audience
- Use the provided brand information as the foundation for all content
- Make content actionable and results-oriented
- Incorporate best practices for the specific content type
- Maintain consistency with the specified tone and style
"""

        return enhanced_prompt

    def _get_marketing_context_for_content_type(self, content_type: str) -> str:
        """Get marketing-specific context based on content type."""
        contexts = {
            "marketing_strategy": """
You are generating a comprehensive marketing strategy. Focus on:
- Strategic thinking and long-term planning
- Data-driven insights and recommendations
- Actionable tactics and implementation plans
- ROI considerations and success metrics
- Market analysis and competitive positioning
            """,
            "campaign_strategy": """
You are creating campaign concepts. Focus on:
- Creative and innovative campaign ideas
- Multi-channel campaign approaches
- Clear campaign objectives and messaging
- Audience engagement strategies
- Campaign timeline and execution plans
            """,
            "social_media_content": """
You are creating social media content. Focus on:
- Platform-specific content optimization
- Engaging and shareable content formats
- Strong calls-to-action
- Visual content suggestions
- Hashtag and engagement strategies
            """,
            "seo_optimization": """
You are developing SEO strategies. Focus on:
- Keyword research and optimization
- Content optimization techniques
- Technical SEO recommendations
- Link building strategies
- Performance measurement and tracking
            """,
            "post_composer": """
You are composing social media posts. Focus on:
- Compelling and engaging post copy
- Platform-appropriate formatting
- Strong calls-to-action
- Visual content recommendations
- Hashtag optimization
            """,
            "blog_content": """
You are creating blog content. Focus on:
- Comprehensive and valuable blog posts
- SEO-optimized content structure
- Engaging headlines and introductions
- Clear, actionable content
- Proper use of keywords and topics
- Strong calls-to-action
- Reader engagement and value
            """
        }

        return contexts.get(content_type, "You are creating marketing content. Focus on engaging, brand-aligned content that drives results.")

    async def _get_agent_aware_marketing_prompt(
        self,
        agent_identity: str,
        content_type: str,
        brand_description: str,
        target_audience: str,
        products_services: str,
        marketing_goals: str,
        existing_content: str,
        keywords: str,
        suggested_topics: str,
        tone: str,
        is_regeneration: bool = False,
        file_context: str = ""
    ) -> str:
        """
        Get marketing prompt that relies on the agent's natural capabilities.

        Args:
            agent_identity: The detected agent identity (kept for interface compatibility)
            content_type: Type of marketing content to generate
            ... (other parameters same as original method)

        Returns:
            Marketing prompt text
        """
        # Simply get the base marketing prompt - the agent's system prompt will handle expertise
        # The agent_identity parameter is kept for interface compatibility but not used
        # since the agent's system prompt already contains their expertise and capabilities
        return await self._get_marketing_prompt_template(
            content_type,
            brand_description,
            target_audience,
            products_services,
            marketing_goals,
            existing_content,
            keywords,
            suggested_topics,
            tone,
            is_regeneration,
            file_context
        )

