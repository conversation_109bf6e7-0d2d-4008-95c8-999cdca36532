# Adaptive Chunking Configuration for Datagenius
# Copy these settings to your main .env file to enable adaptive chunking features

# =============================================================================
# ADAPTIVE CHUNKING SETTINGS
# =============================================================================

# Performance Profile Selection
# Options: speed_optimized, quality_optimized, balanced, memory_optimized
CHUNKING_PERFORMANCE_PROFILE=balanced

# Enable/Disable Adaptive Chunking
# Set to false to use traditional fixed chunking
CHUNKING_USE_ADAPTIVE=true

# Caching Configuration
CHUNKING_ENABLE_CACHING=true

# Batch Processing Settings
CHUNKING_BATCH_SIZE=16
CHUNKING_PARALLEL_WORKERS=3

# =============================================================================
# PERFORMANCE MONITORING
# =============================================================================

# Enable automatic performance monitoring
CHUNKING_ENABLE_MONITORING=true

# Monitoring interval in seconds
CHUNKING_MONITORING_INTERVAL=60

# Enable automatic optimization based on performance metrics
CHUNKING_AUTO_OPTIMIZE=true

# =============================================================================
# PERFORMANCE THRESHOLDS
# =============================================================================

# Processing time thresholds (milliseconds)
CHUNKING_PROCESSING_TIME_WARNING=2000
CHUNKING_PROCESSING_TIME_CRITICAL=5000

# Memory usage thresholds (MB)
CHUNKING_MEMORY_WARNING=512
CHUNKING_MEMORY_CRITICAL=1024

# Cache hit rate thresholds (0.0 - 1.0, lower is worse)
CHUNKING_CACHE_HIT_RATE_WARNING=0.5
CHUNKING_CACHE_HIT_RATE_CRITICAL=0.3

# =============================================================================
# CONTENT-SPECIFIC SETTINGS
# =============================================================================

# Technical Documents
CHUNKING_TECHNICAL_CHUNK_SIZE=800
CHUNKING_TECHNICAL_OVERLAP=150
CHUNKING_TECHNICAL_SEMANTIC=true

# Research Papers
CHUNKING_RESEARCH_CHUNK_SIZE=1500
CHUNKING_RESEARCH_OVERLAP=300
CHUNKING_RESEARCH_SEMANTIC=true

# Legal Documents
CHUNKING_LEGAL_CHUNK_SIZE=1200
CHUNKING_LEGAL_OVERLAP=250
CHUNKING_LEGAL_SEMANTIC=true

# Financial Reports
CHUNKING_FINANCIAL_CHUNK_SIZE=800
CHUNKING_FINANCIAL_OVERLAP=160
CHUNKING_FINANCIAL_SEMANTIC=false

# General Text
CHUNKING_GENERAL_CHUNK_SIZE=1000
CHUNKING_GENERAL_OVERLAP=200
CHUNKING_GENERAL_SEMANTIC=true

# =============================================================================
# EMBEDDING MODEL OPTIMIZATION
# =============================================================================

# Default embedding model for different performance profiles
CHUNKING_SPEED_MODEL=sentence-transformers/all-MiniLM-L6-v2
CHUNKING_QUALITY_MODEL=BAAI/bge-base-en-v1.5
CHUNKING_BALANCED_MODEL=BAAI/bge-small-en-v1.5
CHUNKING_MEMORY_MODEL=sentence-transformers/all-MiniLM-L6-v2

# =============================================================================
# MIGRATION SETTINGS
# =============================================================================

# Enable automatic migration of existing documents to adaptive chunking
CHUNKING_AUTO_MIGRATE=false

# Backup existing embeddings before migration
CHUNKING_BACKUP_ON_MIGRATE=true

# =============================================================================
# DEVELOPMENT/TESTING SETTINGS
# =============================================================================

# Enable detailed logging for chunking operations
CHUNKING_DEBUG_LOGGING=false

# Enable performance benchmarking
CHUNKING_ENABLE_BENCHMARKING=false

# Save performance reports automatically
CHUNKING_AUTO_SAVE_REPORTS=true

# =============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# =============================================================================

# Development environment settings
# Uncomment for development
# CHUNKING_PERFORMANCE_PROFILE=speed_optimized
# CHUNKING_ENABLE_CACHING=false
# CHUNKING_DEBUG_LOGGING=true

# Production environment settings
# Uncomment for production
# CHUNKING_PERFORMANCE_PROFILE=balanced
# CHUNKING_ENABLE_MONITORING=true
# CHUNKING_AUTO_OPTIMIZE=true
# CHUNKING_AUTO_SAVE_REPORTS=true

# Testing environment settings
# Uncomment for testing
# CHUNKING_PERFORMANCE_PROFILE=memory_optimized
# CHUNKING_BATCH_SIZE=4
# CHUNKING_ENABLE_CACHING=false
