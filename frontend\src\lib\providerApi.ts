import { apiRequest } from './api';

export interface Provider {
  id: string;
  name: string;
  description: string;
  endpoint?: string;
  is_available: boolean;
}

export interface ModelPricing {
  prompt: number;
  completion: number;
  unit?: string;
}

export interface ProviderModel {
  id: string;
  name: string;
  description?: string;
  context_length?: number;
  created?: number;
  provider?: string;
  pricing?: {
    input?: ModelPricing;
    output?: ModelPricing;
  };
  category?: string;
}

export interface ProviderApiKey {
  provider_id: string;
  api_key: string;
}

export interface ProviderApiKeyResponse {
  provider_id: string;
  is_valid: boolean;
  message?: string;
}

export interface ProviderSettings {
  default_provider: string;
  use_local_llm: boolean;
  memory_service_provider: string;
  memory_service_model: string;
  concierge_agent_provider: string;
  concierge_agent_model: string;
}

// Provider API functions
export const providerApi = {
  // Get a list of all providers
  getProviders: async (): Promise<{ providers: Provider[] }> => {
    return apiRequest('/providers', {
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutes
    });
  },

  // Get a specific provider
  getProvider: async (provider_id: string): Promise<{ provider: Provider }> => {
    return apiRequest(`/providers/${provider_id}`, {
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutes
    });
  },

  // Set an API key for a provider
  setApiKey: async (provider_id: string, api_key: string): Promise<ProviderApiKeyResponse> => {
    return apiRequest('/providers/api-key', {
      method: 'POST',
      body: JSON.stringify({ provider_id, api_key }),
    });
  },

  // Delete an API key for a provider
  deleteApiKey: async (provider_id: string): Promise<{ message: string }> => {
    return apiRequest(`/providers/api-key/${provider_id}`, {
      method: 'DELETE',
    });
  },

  // Check the status of an API key for a provider
  checkApiKeyStatus: async (provider_id: string): Promise<ProviderApiKeyResponse> => {
    return apiRequest(`/providers/api-key/${provider_id}/status`);
  },

  // Get global provider availability (from .env file)
  getGlobalAvailability: async (): Promise<Record<string, boolean>> => {
    try {
      // Try with hyphen first
      try {
        return await apiRequest('/providers/global-availability', {
          cache: true,
          cacheTtl: 5 * 60 * 1000, // 5 minutes
        });
      } catch (hyphenError) {
        console.warn('Error fetching global-availability, trying with underscore:', hyphenError);
        // Try with underscore as fallback
        return await apiRequest('/providers/global_availability', {
          cache: true,
          cacheTtl: 5 * 60 * 1000, // 5 minutes
        });
      }
    } catch (error) {
      console.error('Error fetching global provider availability:', error);
      // Return empty object as fallback
      return {};
    }
  },

  // Get provider settings
  getSettings: async (): Promise<ProviderSettings> => {
    try {
      return await apiRequest('/providers/settings');
    } catch (error) {
      console.error('Error fetching provider settings:', error);
      // Re-throw the error to be handled by the component
      throw error;
    }
  },

  // Set provider settings
  setSettings: async (settings: ProviderSettings): Promise<ProviderSettings> => {
    return apiRequest('/providers/settings', {
      method: 'POST',
      body: JSON.stringify(settings),
    });
  },

  // Get available models for a provider
  getProviderModels: async (providerId: string): Promise<{ models: ProviderModel[], message?: string }> => {
    try {
      const response = await apiRequest(`/providers/${providerId}/models`, {
        cache: true,
        cacheTtl: 5 * 60 * 1000, // 5 minutes
      });

      // Log the number of models received for debugging
      console.log(`Received ${response.models.length} models for provider ${providerId}`);

      return response;
    } catch (error) {
      console.error(`Error fetching models for provider ${providerId}:`, error);
      return {
        models: [],
        message: error instanceof Error ? error.message : 'Unknown error fetching models'
      };
    }
  },
};
