"""
Enhanced Data Access MCP Tool for Datagenius.

This module provides a comprehensive, unified MCP-compatible tool for accessing data from all sources.
It handles structured data (CSV, Excel, JSON), unstructured data (PDF, DOC, TXT), and mixed content.
Features include vector database integration, knowledge graph operations, cross-modal queries,
and conversation-aware context building.
"""

import logging
import os
import io
import time
import json
import re
import uuid
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, List, Union

import pandas as pd
import numpy as np

from .base import BaseMCPTool
from app.config import UPLOAD_DIR
from app.utils.json_utils import sanitize_json
from app.database import get_db, get_file, get_data_source, get_user_files
from sqlalchemy.orm import Session
from ...utils.vector_service import VectorService
from ...utils.memory_service import MemoryService
from ...utils.knowledge_graph_service import KnowledgeGraphService
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

# Configure logging
logger = logging.getLogger(__name__)


class DataAccessTool(BaseMCPTool):
    """
    Enhanced unified data access tool for all file types and data operations.
    
    Supports:
    - Structured data: CSV, Excel, JSON, Parquet
    - Unstructured data: PDF, DOC, DOCX, TXT
    - Mixed content: Documents with tables and text
    - Vector database operations
    - Knowledge graph operations
    - Cross-modal queries
    - Conversation-aware context
    """

    def __init__(self):
        """Initialize the enhanced data access tool."""
        super().__init__(
            name="data_access",
            description="Enhanced unified tool for accessing all types of data sources with vector database and knowledge graph integration",
            input_schema={
                "type": "object",
                "properties": {
                    "data_source": {
                        "type": ["object", "string", "array"],
                        "description": "Data source(s) - can be single source, object with metadata, or array for cross-modal queries"
                    },
                    "operation": {
                        "type": "string",
                        "enum": [
                            # Basic data operations
                            "load", "describe", "head", "tail", "info", "sample", "query", "search",
                            # Document operations
                            "embed", "search_document", "extract_text", "extract_tables",
                            # Knowledge graph operations
                            "extract_entities", "build_knowledge_graph", "query_knowledge_graph",
                            # Advanced operations
                            "semantic_search", "cross_modal_query", "content_analysis", "hybrid_search",
                            "analyze_structure", "detect_content_type", "send_to_persona",
                            # Cloud storage operations
                            "load_from_s3", "load_from_azure", "load_from_gcp", "stream_data",
                            # Data validation and schema operations
                            "validate_schema", "detect_schema", "data_profiling", "quality_assessment"
                        ],
                        "description": "Operation to perform on the data"
                    },
                    "params": {
                        "type": "object",
                        "description": "Additional parameters for the operation",
                        "properties": {
                            "query": {"type": "string", "description": "Query string for search operations"},
                            "limit": {"type": "integer", "description": "Limit for results"},
                            "chunk_size": {"type": "integer", "description": "Chunk size for document processing"},
                            "chunk_overlap": {"type": "integer", "description": "Chunk overlap for document processing"},
                            "include_metadata": {"type": "boolean", "description": "Include metadata in results"},
                            "conversation_id": {"type": "string", "description": "Conversation ID for context"},
                            "user_id": {"type": ["string", "integer"], "description": "User ID for personalization"},
                            "persona_id": {"type": "string", "description": "Persona ID for context"}
                        }
                    }
                },
                "required": ["operation"]
            },
        )
        
        # Initialize services
        self.vector_service = VectorService()
        self.memory_service = MemoryService()
        self.knowledge_graph_service = KnowledgeGraphService()
        
        # Supported file types
        self.structured_extensions = {'.csv', '.xlsx', '.xls', '.json', '.parquet', '.tsv', '.xml', '.hdf5', '.h5', '.feather', '.orc', '.avro'}
        self.unstructured_extensions = {'.pdf', '.doc', '.docx', '.txt', '.md', '.rtf', '.html', '.epub', '.mobi'}
        self.all_supported_extensions = self.structured_extensions | self.unstructured_extensions
        
        # Content type patterns
        self.table_patterns = [
            r'\|.*\|.*\|',  # Markdown tables
            r'\t.*\t.*\t',  # Tab-separated
            r',.*,.*,',     # CSV-like
        ]
        
        # Initialize directories
        self._setup_directories()
        
        logger.info("Enhanced DataAccessTool initialized with comprehensive file type support")

    def _setup_directories(self):
        """Setup directory paths for file operations."""
        self.data_dir = "data"
        self.upload_dir = UPLOAD_DIR
        
        # Get the absolute path to the backend directory
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        self.backend_dir = current_dir
        
        # Create absolute paths for important directories
        self.abs_upload_dir = os.path.join(current_dir, self.upload_dir)
        self.abs_data_dir = os.path.join(current_dir, self.data_dir)
        
        # Search directories for file resolution
        self.search_dirs = [
            self.upload_dir, self.data_dir, "uploads", "backend/data", "backend/uploads",
            "backend/temp_uploads", self.abs_upload_dir, self.abs_data_dir, "."
        ]
        
        logger.info(f"Initialized directories - Upload: {self.abs_upload_dir}, Data: {self.abs_data_dir}")

    def detect_content_type(self, file_path: str) -> Dict[str, Any]:
        """
        Detect the content type and structure of a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with content type information
        """
        file_extension = os.path.splitext(file_path)[1].lower()
        
        content_info = {
            "file_extension": file_extension,
            "is_structured": file_extension in self.structured_extensions,
            "is_unstructured": file_extension in self.unstructured_extensions,
            "has_tables": False,
            "has_text": False,
            "content_types": []
        }
        
        if file_extension in self.structured_extensions:
            content_info["content_types"].append("structured_data")
            content_info["has_tables"] = True
            
        elif file_extension in self.unstructured_extensions:
            content_info["content_types"].append("unstructured_text")
            content_info["has_text"] = True
            
            # For documents, check if they contain tables
            if file_extension in ['.pdf', '.docx', '.doc']:
                try:
                    text_content = self._extract_text_from_document(file_path)
                    if self._contains_table_patterns(text_content):
                        content_info["has_tables"] = True
                        content_info["content_types"].append("mixed_content")
                except Exception as e:
                    logger.warning(f"Could not analyze document content: {e}")
        
        return content_info

    def _contains_table_patterns(self, text: str) -> bool:
        """Check if text contains table-like patterns."""
        for pattern in self.table_patterns:
            if re.search(pattern, text):
                return True
        return False

    def _extract_text_from_document(self, file_path: str) -> str:
        """Extract text content from document files."""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        try:
            if file_extension == '.pdf':
                try:
                    from langchain_community.document_loaders import PyPDFLoader
                    loader = PyPDFLoader(file_path)
                    documents = loader.load()
                    return "\n\n".join([doc.page_content for doc in documents])
                except ImportError:
                    import PyPDF2
                    with open(file_path, "rb") as f:
                        reader = PyPDF2.PdfReader(f)
                        text = ""
                        for page in reader.pages:
                            text += page.extract_text() + "\n"
                    return text
                    
            elif file_extension in ['.docx', '.doc']:
                try:
                    from langchain_community.document_loaders import Docx2txtLoader
                    loader = Docx2txtLoader(file_path)
                    documents = loader.load()
                    return "\n\n".join([doc.page_content for doc in documents])
                except ImportError:
                    import docx2txt
                    return docx2txt.process(file_path)
                    
            elif file_extension in ['.txt', '.md', '.rtf']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
                    
        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            raise
            
        return ""

    def _extract_tables_from_document(self, file_path: str) -> List[pd.DataFrame]:
        """Extract tables from document files."""
        file_extension = os.path.splitext(file_path)[1].lower()
        tables = []
        
        try:
            if file_extension == '.pdf':
                try:
                    import tabula
                    tables = tabula.read_pdf(file_path, pages='all', multiple_tables=True)
                except ImportError:
                    logger.warning("tabula-py not available for PDF table extraction")
                except Exception as e:
                    logger.warning(f"Could not extract tables from PDF: {e}")
                    
            elif file_extension == '.docx':
                try:
                    from docx import Document
                    doc = Document(file_path)
                    
                    for table in doc.tables:
                        data = []
                        for row in table.rows:
                            row_data = [cell.text.strip() for cell in row.cells]
                            data.append(row_data)
                        
                        if data:
                            df = pd.DataFrame(data[1:], columns=data[0])
                            tables.append(df)
                            
                except ImportError:
                    logger.warning("python-docx not available for DOCX table extraction")
                except Exception as e:
                    logger.warning(f"Could not extract tables from DOCX: {e}")
                    
        except Exception as e:
            logger.error(f"Error extracting tables from {file_path}: {e}")
            
        return tables

    def _dataframe_to_markdown_table(self, df: pd.DataFrame) -> str:
        """
        Convert a pandas DataFrame to a markdown table format.

        Args:
            df: DataFrame to convert

        Returns:
            Markdown table string
        """
        if df.empty:
            return "*No data available*"

        try:
            # Get column names
            columns = df.columns.tolist()

            # Create header row
            header_row = "| " + " | ".join(str(col) for col in columns) + " |"

            # Create separator row
            separator_row = "|" + "|".join([" --- " for _ in columns]) + "|"

            # Create data rows
            data_rows = []
            for _, row in df.iterrows():
                row_values = []
                for col in columns:
                    value = row[col]
                    # Format the value appropriately
                    if pd.isna(value):
                        formatted_value = "N/A"
                    elif isinstance(value, float):
                        # Format floats to 2 decimal places if they have decimals
                        if value == int(value):
                            formatted_value = str(int(value))
                        else:
                            formatted_value = f"{value:.2f}"
                    else:
                        formatted_value = str(value)
                    row_values.append(formatted_value)

                data_row = "| " + " | ".join(row_values) + " |"
                data_rows.append(data_row)

            # Combine all parts
            table_lines = [header_row, separator_row] + data_rows
            return "\n".join(table_lines)

        except Exception as e:
            logger.error(f"Error converting DataFrame to markdown table: {e}")
            return f"```\n{df.to_string()}\n```"

    def _describe_to_markdown_table(self, desc_df: pd.DataFrame) -> str:
        """
        Convert a pandas describe() DataFrame to a markdown table format.

        Args:
            desc_df: DataFrame from df.describe()

        Returns:
            Markdown table string
        """
        if desc_df.empty:
            return "*No statistical summary available*"

        try:
            # Get column names (the original data columns)
            columns = desc_df.columns.tolist()

            # Get statistic names (index of describe DataFrame)
            statistics = desc_df.index.tolist()

            # Create header row
            header_row = "| Statistic | " + " | ".join(str(col) for col in columns) + " |"

            # Create separator row
            separator_row = "|-----------|" + "|".join([" --- " for _ in columns]) + "|"

            # Create data rows
            data_rows = []
            for stat in statistics:
                row_values = [f"**{stat}**"]  # Bold statistic name
                for col in columns:
                    value = desc_df.loc[stat, col]
                    # Format the value appropriately
                    if pd.isna(value):
                        formatted_value = "N/A"
                    elif isinstance(value, float):
                        # Format floats to 2 decimal places if they have decimals
                        if value == int(value):
                            formatted_value = str(int(value))
                        else:
                            formatted_value = f"{value:.2f}"
                    else:
                        formatted_value = str(value)
                    row_values.append(formatted_value)

                data_row = "| " + " | ".join(row_values) + " |"
                data_rows.append(data_row)

            # Combine all parts
            table_lines = [header_row, separator_row] + data_rows
            return "\n".join(table_lines)

        except Exception as e:
            logger.error(f"Error converting describe DataFrame to markdown table: {e}")
            return f"```\n{desc_df.to_string()}\n```"

    async def _resolve_file_path(self, data_source: Union[str, Dict[str, Any]]) -> Optional[str]:
        """
        Resolve file path from various data source formats.

        Args:
            data_source: Data source specification

        Returns:
            Resolved file path or None if not found
        """
        if isinstance(data_source, str):
            # Direct file path or file ID
            if os.path.exists(data_source):
                return data_source

            # Try to find by ID in database
            try:
                db_gen = get_db()
                db: Session = next(db_gen)
                try:
                    db_file = get_file(db, data_source)
                    if db_file and db_file.file_path and os.path.exists(db_file.file_path):
                        return db_file.file_path
                finally:
                    db.close()
            except Exception as e:
                logger.warning(f"Could not query database for file ID {data_source}: {e}")

        elif isinstance(data_source, dict):
            # Extract file path from dictionary
            file_path = data_source.get("file_path")
            if file_path and os.path.exists(file_path):
                return file_path

            # Try database lookup by ID
            file_id = data_source.get("id")
            if file_id:
                try:
                    db_gen = get_db()
                    db: Session = next(db_gen)
                    try:
                        db_file = get_file(db, file_id)
                        if db_file and db_file.file_path and os.path.exists(db_file.file_path):
                            return db_file.file_path
                    finally:
                        db.close()
                except Exception as e:
                    logger.warning(f"Could not query database for file ID {file_id}: {e}")

        # Search in common directories
        file_name = None
        if isinstance(data_source, dict):
            file_name = data_source.get("name") or data_source.get("file_name")
        elif isinstance(data_source, str):
            file_name = data_source

        if file_name:
            for search_dir in self.search_dirs:
                if os.path.exists(search_dir):
                    potential_path = os.path.join(search_dir, file_name)
                    if os.path.exists(potential_path):
                        return potential_path

        return None

    def _load_structured_data(self, file_path: str) -> pd.DataFrame:
        """Load structured data from file with enhanced format support."""
        file_extension = os.path.splitext(file_path)[1].lower()

        try:
            if file_extension == '.csv':
                return pd.read_csv(file_path)
            elif file_extension in ['.xlsx', '.xls']:
                return pd.read_excel(file_path)
            elif file_extension == '.json':
                return pd.read_json(file_path)
            elif file_extension == '.parquet':
                return pd.read_parquet(file_path)
            elif file_extension == '.tsv':
                return pd.read_csv(file_path, sep='\t')
            elif file_extension == '.xml':
                return self._load_xml_data(file_path)
            elif file_extension in ['.hdf5', '.h5']:
                return self._load_hdf5_data(file_path)
            elif file_extension == '.feather':
                return pd.read_feather(file_path)
            elif file_extension == '.orc':
                return self._load_orc_data(file_path)
            elif file_extension == '.avro':
                return self._load_avro_data(file_path)
            else:
                raise ValueError(f"Unsupported structured data format: {file_extension}")

        except Exception as e:
            logger.error(f"Error loading structured data from {file_path}: {e}")
            raise

    def _load_xml_data(self, file_path: str) -> pd.DataFrame:
        """Load XML data into DataFrame."""
        try:
            import xml.etree.ElementTree as ET

            tree = ET.parse(file_path)
            root = tree.getroot()

            # Simple XML to DataFrame conversion
            # This assumes a structure like <root><record><field>value</field></record></root>
            records = []

            # Try to find repeating elements
            children = list(root)
            if children:
                # If root has children, assume they are records
                for child in children:
                    record = {}
                    for subchild in child:
                        record[subchild.tag] = subchild.text
                    if record:  # Only add non-empty records
                        records.append(record)

            if not records:
                # Fallback: treat root attributes as a single record
                records = [root.attrib] if root.attrib else [{"content": ET.tostring(root, encoding='unicode')}]

            return pd.DataFrame(records)

        except Exception as e:
            logger.error(f"Error loading XML data: {e}")
            # Fallback to reading as text
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return pd.DataFrame([{"xml_content": content}])

    def _load_hdf5_data(self, file_path: str) -> pd.DataFrame:
        """Load HDF5 data into DataFrame."""
        try:
            # Try pandas HDF5 support first
            return pd.read_hdf(file_path)
        except Exception as e:
            logger.warning(f"Could not load HDF5 with pandas: {e}")
            try:
                import h5py

                # Read HDF5 file and convert to DataFrame
                data = {}
                with h5py.File(file_path, 'r') as f:
                    def extract_datasets(name, obj):
                        if isinstance(obj, h5py.Dataset):
                            try:
                                data[name] = obj[:]
                            except Exception:
                                data[name] = str(obj)

                    f.visititems(extract_datasets)

                if data:
                    # Try to create DataFrame from extracted data
                    max_len = max(len(v) if hasattr(v, '__len__') else 1 for v in data.values())
                    normalized_data = {}

                    for key, value in data.items():
                        if hasattr(value, '__len__') and len(value) == max_len:
                            normalized_data[key] = value
                        else:
                            normalized_data[key] = [value] * max_len

                    return pd.DataFrame(normalized_data)
                else:
                    return pd.DataFrame([{"hdf5_info": "No readable datasets found"}])

            except ImportError:
                logger.error("h5py not available for HDF5 reading")
                return pd.DataFrame([{"error": "HDF5 support not available"}])

    def _load_orc_data(self, file_path: str) -> pd.DataFrame:
        """Load ORC data into DataFrame."""
        try:
            import pyarrow.orc as orc

            table = orc.read_table(file_path)
            return table.to_pandas()

        except ImportError:
            logger.error("pyarrow not available for ORC reading")
            return pd.DataFrame([{"error": "ORC support not available - install pyarrow"}])
        except Exception as e:
            logger.error(f"Error loading ORC data: {e}")
            return pd.DataFrame([{"error": f"Failed to load ORC: {str(e)}"}])

    def _load_avro_data(self, file_path: str) -> pd.DataFrame:
        """Load Avro data into DataFrame."""
        try:
            import fastavro

            records = []
            with open(file_path, 'rb') as f:
                avro_reader = fastavro.reader(f)
                for record in avro_reader:
                    records.append(record)

            return pd.DataFrame(records)

        except ImportError:
            logger.error("fastavro not available for Avro reading")
            return pd.DataFrame([{"error": "Avro support not available - install fastavro"}])
        except Exception as e:
            logger.error(f"Error loading Avro data: {e}")
            return pd.DataFrame([{"error": f"Failed to load Avro: {str(e)}"}])

    async def _perform_semantic_search(self, query: str, data_sources: List[str],
                                     limit: int = 5) -> Dict[str, Any]:
        """
        Perform semantic search across multiple data sources.

        Args:
            query: Search query
            data_sources: List of data source paths
            limit: Maximum number of results

        Returns:
            Search results with metadata
        """
        all_results = []

        for data_source in data_sources:
            try:
                # Check if document is already embedded
                vector_store_id = None

                # Try to find existing embedding
                try:
                    file_info = self.vector_service._load_file_info_by_path(data_source)
                    if file_info:
                        vector_store_id = file_info.get("vector_store_id")
                except:
                    pass

                # Embed document if not already embedded using adaptive chunking
                if not vector_store_id:
                    vector_store_id, file_info = await self.vector_service.embed_document(
                        file_path=data_source,
                        use_adaptive_chunking=True  # Use adaptive chunking for optimal performance
                    )
                    logger.info(f"Embedded document with adaptive chunking: {data_source}")
                    if 'content_type' in file_info:
                        logger.info(f"Detected content type: {file_info['content_type']}")

                # Search the document
                results = self.vector_service.search_document(
                    vector_store_id=vector_store_id,
                    query=query,
                    limit=limit
                )

                # Add source information to results
                for result in results:
                    result["source_file"] = data_source
                    result["source_type"] = "document"
                    all_results.append(result)

            except Exception as e:
                logger.error(f"Error searching {data_source}: {e}")
                continue

        # Sort by relevance score if available
        all_results.sort(key=lambda x: x.get("score", 0), reverse=True)

        return {
            "results": all_results[:limit],
            "total_sources": len(data_sources),
            "successful_searches": len([r for r in all_results if r.get("content")])
        }

    async def _extract_entities_from_text(self, text: str, source_file: str = None) -> List[Dict[str, Any]]:
        """
        Extract entities from text using knowledge graph service.

        Args:
            text: Text to analyze
            source_file: Source file path for context

        Returns:
            List of extracted entities
        """
        try:
            # Use knowledge graph service for entity extraction
            entities = await self.knowledge_graph_service.extract_entities(
                text=text,
                source=source_file or "text_input"
            )

            return [
                {
                    "text": entity.name,
                    "type": entity.entity_type,
                    "confidence": getattr(entity, 'confidence', 0.8),
                    "source": source_file
                }
                for entity in entities
            ]

        except Exception as e:
            logger.error(f"Error extracting entities: {e}")
            return []

    async def _build_knowledge_graph(self, data_sources: List[str],
                                   graph_name: str = None) -> Dict[str, Any]:
        """
        Build knowledge graph from multiple data sources.

        Args:
            data_sources: List of data source paths
            graph_name: Name for the knowledge graph

        Returns:
            Knowledge graph information
        """
        if not graph_name:
            graph_name = f"kg_{uuid.uuid4().hex[:8]}"

        try:
            # Create knowledge graph
            await self.knowledge_graph_service.create_graph(
                graph_name=graph_name,
                description=f"Knowledge graph from {len(data_sources)} data sources"
            )

            all_entities = []
            all_relationships = []

            for data_source in data_sources:
                try:
                    content_info = self.detect_content_type(data_source)

                    if content_info["has_text"]:
                        # Extract text and entities
                        text = self._extract_text_from_document(data_source)
                        entities = await self._extract_entities_from_text(text, data_source)
                        all_entities.extend(entities)

                        # Add entities to knowledge graph
                        for entity in entities:
                            await self.knowledge_graph_service.add_entity(
                                graph_name=graph_name,
                                entity_name=entity["text"],
                                entity_type=entity["type"],
                                properties={
                                    "source": data_source,
                                    "confidence": entity["confidence"]
                                }
                            )

                    if content_info["has_tables"]:
                        # Extract structured data relationships
                        if content_info["is_structured"]:
                            df = self._load_structured_data(data_source)
                            # Add column relationships and data patterns
                            for col in df.columns:
                                await self.knowledge_graph_service.add_entity(
                                    graph_name=graph_name,
                                    entity_name=col,
                                    entity_type="column",
                                    properties={
                                        "source": data_source,
                                        "data_type": str(df[col].dtype),
                                        "unique_values": int(df[col].nunique())
                                    }
                                )

                except Exception as e:
                    logger.error(f"Error processing {data_source} for knowledge graph: {e}")
                    continue

            return {
                "graph_name": graph_name,
                "entities_count": len(all_entities),
                "sources_processed": len(data_sources),
                "graph_id": graph_name
            }

        except Exception as e:
            logger.error(f"Error building knowledge graph: {e}")
            raise

    async def _perform_cross_modal_query(self, query: str, data_sources: List[str],
                                       params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform cross-modal query across structured and unstructured data.

        Args:
            query: Query string
            data_sources: List of data source paths
            params: Additional parameters

        Returns:
            Combined results from multiple data types
        """
        structured_results = []
        unstructured_results = []

        for data_source in data_sources:
            try:
                content_info = self.detect_content_type(data_source)

                if content_info["is_structured"]:
                    # Query structured data
                    df = self._load_structured_data(data_source)

                    # Simple text search across all columns
                    query_lower = query.lower()
                    mask = df.astype(str).apply(
                        lambda x: x.str.lower().str.contains(query_lower, na=False)
                    ).any(axis=1)

                    matching_rows = df[mask]
                    if not matching_rows.empty:
                        structured_results.append({
                            "source": data_source,
                            "type": "structured",
                            "matches": matching_rows.to_dict(orient="records"),
                            "match_count": len(matching_rows)
                        })

                if content_info["has_text"]:
                    # Query unstructured data using semantic search
                    semantic_results = await self._perform_semantic_search(
                        query, [data_source], limit=params.get("limit", 3)
                    )

                    if semantic_results["results"]:
                        unstructured_results.append({
                            "source": data_source,
                            "type": "unstructured",
                            "matches": semantic_results["results"],
                            "match_count": len(semantic_results["results"])
                        })

            except Exception as e:
                logger.error(f"Error in cross-modal query for {data_source}: {e}")
                continue

        return {
            "query": query,
            "structured_results": structured_results,
            "unstructured_results": unstructured_results,
            "total_structured_matches": sum(r["match_count"] for r in structured_results),
            "total_unstructured_matches": sum(r["match_count"] for r in unstructured_results)
        }

    async def _analyze_content_structure(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze the structure and content of a file.

        Args:
            file_path: Path to the file

        Returns:
            Detailed content analysis
        """
        content_info = self.detect_content_type(file_path)
        analysis = {
            "file_path": file_path,
            "file_name": os.path.basename(file_path),
            "file_size": os.path.getsize(file_path),
            "content_info": content_info
        }

        try:
            if content_info["is_structured"]:
                df = self._load_structured_data(file_path)
                analysis["structured_analysis"] = {
                    "shape": df.shape,
                    "columns": df.columns.tolist(),
                    "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
                    "memory_usage": df.memory_usage(deep=True).sum(),
                    "missing_values": df.isnull().sum().to_dict(),
                    "numeric_columns": df.select_dtypes(include=[np.number]).columns.tolist(),
                    "categorical_columns": df.select_dtypes(include=['object']).columns.tolist()
                }

            if content_info["has_text"]:
                text = self._extract_text_from_document(file_path)
                analysis["text_analysis"] = {
                    "character_count": len(text),
                    "word_count": len(text.split()),
                    "line_count": len(text.split('\n')),
                    "has_tables": content_info["has_tables"],
                    "language": "en"  # Could be enhanced with language detection
                }

                # Extract entities
                entities = await self._extract_entities_from_text(text, file_path)
                analysis["entities"] = entities[:10]  # Top 10 entities

            if content_info["has_tables"] and not content_info["is_structured"]:
                # Extract tables from documents
                tables = self._extract_tables_from_document(file_path)
                analysis["extracted_tables"] = [
                    {
                        "index": i,
                        "shape": table.shape,
                        "columns": table.columns.tolist()
                    }
                    for i, table in enumerate(tables)
                ]

        except Exception as e:
            logger.error(f"Error analyzing content structure: {e}")
            analysis["error"] = str(e)

        return analysis

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the enhanced data access tool.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            # Validate required operation parameter
            if "operation" not in arguments:
                return self._error_response("Operation parameter is required", "MissingOperation")

            operation = arguments["operation"]
            data_source = arguments.get("data_source")
            params = arguments.get("params", {})

            # Agent context for dynamic identity detection
            user_context = arguments.get("context", {})
            agent_id = arguments.get("persona_id") or arguments.get("agent_id") or params.get("persona_id")

            # Detect agent identity for personalized data access
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=user_context,
                intent_type="data_access"
            )

            logger.info(f"Detected agent identity: {agent_identity} for data access operation: {operation}")
            logger.info(f"🚀 Enhanced Data Access Tool - Operation: {operation}")
            logger.info(f"📋 Data source: {data_source}")
            logger.info(f"📋 Parameters: {params}")

            # Handle operations that don't require data sources
            if operation == "detect_content_type" and isinstance(data_source, str):
                file_path = await self._resolve_file_path(data_source)
                if not file_path:
                    return self._error_response("File not found", "FileNotFound")

                content_info = self.detect_content_type(file_path)
                return self._success_response(
                    f"Content type analysis for {os.path.basename(file_path)}",
                    metadata=content_info
                )

            # Handle array of data sources for cross-modal operations
            if isinstance(data_source, list):
                data_source_paths = []
                for ds in data_source:
                    path = await self._resolve_file_path(ds)
                    if path:
                        data_source_paths.append(path)

                if not data_source_paths:
                    return self._error_response("No valid data sources found", "NoDataSources")

                if operation == "cross_modal_query":
                    query = params.get("query", "")
                    if not query:
                        return self._error_response("Query parameter required", "MissingQuery")

                    results = await self._perform_cross_modal_query(query, data_source_paths, params)
                    results["agent_identity"] = agent_identity
                    results["agent_aware"] = True
                    return self._success_response(
                        f"Cross-modal query results for: {query}",
                        metadata=results
                    )

                elif operation == "semantic_search":
                    query = params.get("query", "")
                    if not query:
                        return self._error_response("Query parameter required", "MissingQuery")

                    results = await self._perform_semantic_search(
                        query, data_source_paths, params.get("limit", 5)
                    )
                    return self._success_response(
                        f"Semantic search results for: {query}",
                        metadata=results
                    )

                elif operation == "build_knowledge_graph":
                    graph_name = params.get("graph_name")
                    results = await self._build_knowledge_graph(data_source_paths, graph_name)
                    return self._success_response(
                        f"Knowledge graph built: {results['graph_name']}",
                        metadata=results
                    )

            # Single data source operations
            if not data_source:
                return self._error_response("Data source required", "MissingDataSource")

            file_path = await self._resolve_file_path(data_source)
            if not file_path:
                return self._error_response("File not found", "FileNotFound")

            content_info = self.detect_content_type(file_path)

            # Route to appropriate handler based on operation and content type
            result = await self._handle_single_source_operation(
                operation, file_path, content_info, params
            )

            # Add agent metadata to result
            if "metadata" not in result:
                result["metadata"] = {}
            result["metadata"]["agent_identity"] = agent_identity
            result["metadata"]["agent_aware"] = True

            return result

        except Exception as e:
            logger.error(f"Error in enhanced data access tool: {e}")
            return self._error_response(f"Tool execution error: {str(e)}", "ExecutionError")

    async def _handle_single_source_operation(self, operation: str, file_path: str,
                                            content_info: Dict[str, Any],
                                            params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle operations on a single data source.

        Args:
            operation: Operation to perform
            file_path: Path to the file
            content_info: Content type information
            params: Operation parameters

        Returns:
            Operation results
        """
        try:
            # Content analysis operations
            if operation == "analyze_structure":
                analysis = await self._analyze_content_structure(file_path)
                return self._success_response(
                    f"Content structure analysis for {os.path.basename(file_path)}",
                    metadata=analysis
                )

            # Text extraction operations
            if operation == "extract_text":
                if not content_info["has_text"]:
                    return self._error_response("File does not contain extractable text", "NoTextContent")

                text = self._extract_text_from_document(file_path)
                return self._success_response(
                    f"Extracted text from {os.path.basename(file_path)}",
                    content=[{"type": "text", "text": text[:2000] + "..." if len(text) > 2000 else text}],
                    metadata={"full_text": text, "character_count": len(text)}
                )

            # Table extraction operations
            if operation == "extract_tables":
                if not content_info["has_tables"]:
                    return self._error_response("File does not contain extractable tables", "NoTableContent")

                if content_info["is_structured"]:
                    # Load structured data
                    df = self._load_structured_data(file_path)
                    return self._success_response(
                        f"Loaded structured data from {os.path.basename(file_path)}",
                        metadata={
                            "table_data": df.to_dict(orient="records"),
                            "shape": df.shape,
                            "columns": df.columns.tolist()
                        }
                    )
                else:
                    # Extract tables from documents
                    tables = self._extract_tables_from_document(file_path)
                    return self._success_response(
                        f"Extracted {len(tables)} tables from {os.path.basename(file_path)}",
                        metadata={
                            "tables": [table.to_dict(orient="records") for table in tables],
                            "table_count": len(tables)
                        }
                    )

            # Entity extraction operations
            if operation == "extract_entities":
                if not content_info["has_text"]:
                    return self._error_response("File does not contain text for entity extraction", "NoTextContent")

                text = self._extract_text_from_document(file_path)
                entities = await self._extract_entities_from_text(text, file_path)
                return self._success_response(
                    f"Extracted {len(entities)} entities from {os.path.basename(file_path)}",
                    metadata={"entities": entities}
                )

            # Document embedding operations
            if operation == "embed":
                if not content_info["has_text"]:
                    return self._error_response("File does not contain text for embedding", "NoTextContent")

                # Use adaptive chunking with optional custom parameters
                use_adaptive = params.get("use_adaptive_chunking", True)
                performance_profile = params.get("performance_profile", "balanced")
                chunk_size = params.get("chunk_size", None)  # None enables adaptive
                chunk_overlap = params.get("chunk_overlap", None)  # None enables adaptive

                vector_store_id, file_info = await self.vector_service.embed_document(
                    file_path=file_path,
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap,
                    use_adaptive_chunking=use_adaptive
                )

                # Log adaptive chunking results
                if use_adaptive and 'content_type' in file_info:
                    logger.info(f"Adaptive chunking detected content type: {file_info['content_type']}")
                if 'chunking_strategy' in file_info:
                    logger.info(f"Used chunking strategy: {file_info['chunking_strategy']}")

                return self._success_response(
                    f"Document embedded successfully: {os.path.basename(file_path)}",
                    metadata={
                        "vector_store_id": vector_store_id,
                        "file_info": file_info
                    }
                )

            # Document search operations
            if operation == "search_document":
                query = params.get("query", "")
                if not query:
                    return self._error_response("Query parameter required", "MissingQuery")

                # Check if document is already embedded
                vector_store_id = None
                try:
                    file_info = self.vector_service._load_file_info_by_path(file_path)
                    if file_info:
                        vector_store_id = file_info.get("vector_store_id")
                except:
                    pass

                # Embed document if not already embedded
                if not vector_store_id:
                    vector_store_id, file_info = await self.vector_service.embed_document(
                        file_path=file_path,
                        chunk_size=params.get("chunk_size", 1000),
                        chunk_overlap=params.get("chunk_overlap", 200)
                    )

                # Search the document
                results = self.vector_service.search_document(
                    vector_store_id=vector_store_id,
                    query=query,
                    limit=params.get("limit", 5)
                )

                return self._success_response(
                    f"Search results for '{query}' in {os.path.basename(file_path)}",
                    metadata={
                        "query": query,
                        "results": results,
                        "vector_store_id": vector_store_id
                    }
                )

            # Structured data operations
            if content_info["is_structured"]:
                return await self._handle_structured_data_operations(
                    operation, file_path, params
                )

            # Hybrid operations for mixed content
            if operation == "content_analysis":
                analysis = await self._analyze_content_structure(file_path)
                return self._success_response(
                    f"Comprehensive content analysis for {os.path.basename(file_path)}",
                    metadata=analysis
                )

            # Default: return content type information
            return self._success_response(
                f"Content information for {os.path.basename(file_path)}",
                metadata=content_info
            )

        except Exception as e:
            logger.error(f"Error handling single source operation {operation}: {e}")
            return self._error_response(f"Operation error: {str(e)}", "OperationError")

    async def _handle_structured_data_operations(self, operation: str, file_path: str,
                                               params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle operations specific to structured data.

        Args:
            operation: Operation to perform
            file_path: Path to the structured data file
            params: Operation parameters

        Returns:
            Operation results
        """
        try:
            df = self._load_structured_data(file_path)

            if operation == "load":
                return self._success_response(
                    f"Loaded data from {os.path.basename(file_path)}",
                    metadata={
                        "shape": df.shape,
                        "columns": df.columns.tolist(),
                        "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
                        "file_path": file_path  # Add the file path to metadata
                    }
                )

            elif operation == "describe":
                desc_df = df.describe(include='all')
                # Convert to markdown table format
                markdown_table = self._describe_to_markdown_table(desc_df)
                return self._success_response(
                    f"Statistical description of {os.path.basename(file_path)}",
                    content=[{"type": "text", "text": markdown_table}],
                    metadata={"description": desc_df.to_dict()}
                )

            elif operation == "head":
                n = params.get("n", 5)
                head_df = df.head(n)
                # Convert to markdown table format
                markdown_table = self._dataframe_to_markdown_table(head_df)
                return self._success_response(
                    f"First {n} rows of {os.path.basename(file_path)}",
                    content=[{"type": "text", "text": markdown_table}],
                    metadata={"preview_data": head_df.to_dict(orient="records")}
                )

            elif operation == "tail":
                n = params.get("n", 5)
                tail_df = df.tail(n)
                # Convert to markdown table format
                markdown_table = self._dataframe_to_markdown_table(tail_df)
                return self._success_response(
                    f"Last {n} rows of {os.path.basename(file_path)}",
                    content=[{"type": "text", "text": markdown_table}],
                    metadata={"preview_data": tail_df.to_dict(orient="records")}
                )

            elif operation == "sample":
                n = params.get("n", 5)
                sample_df = df.sample(n=min(n, len(df)))
                # Convert to markdown table format
                markdown_table = self._dataframe_to_markdown_table(sample_df)
                return self._success_response(
                    f"Random sample of {n} rows from {os.path.basename(file_path)}",
                    content=[{"type": "text", "text": markdown_table}],
                    metadata={"preview_data": sample_df.to_dict(orient="records")}
                )

            elif operation == "info":
                buffer = io.StringIO()
                df.info(buf=buffer)
                info_text = buffer.getvalue()
                return self._success_response(
                    f"Data info for {os.path.basename(file_path)}",
                    content=[{"type": "text", "text": info_text}],
                    metadata={
                        "shape": df.shape,
                        "columns": df.columns.tolist(),
                        "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
                        "missing_values": df.isnull().sum().to_dict()
                    }
                )

            elif operation == "query":
                query = params.get("query", "")
                if not query:
                    return self._error_response("Query parameter required", "MissingQuery")

                try:
                    filtered_df = df.query(query)
                    return self._success_response(
                        f"Query results for '{query}' in {os.path.basename(file_path)}",
                        content=[{"type": "text", "text": filtered_df.to_string()}],
                        metadata={
                            "query": query,
                            "filtered_data": filtered_df.to_dict(orient="records"),
                            "filtered_shape": filtered_df.shape
                        }
                    )
                except Exception as e:
                    return self._error_response(f"Query execution error: {str(e)}", "QueryError")

            elif operation == "search":
                query = params.get("query", "")
                if not query:
                    return self._error_response("Query parameter required", "MissingQuery")

                # Simple text search across all columns
                query_lower = query.lower()
                mask = df.astype(str).apply(
                    lambda x: x.str.lower().str.contains(query_lower, na=False)
                ).any(axis=1)

                matching_rows = df[mask]
                return self._success_response(
                    f"Search results for '{query}' in {os.path.basename(file_path)}",
                    content=[{"type": "text", "text": matching_rows.to_string()}],
                    metadata={
                        "query": query,
                        "matches": matching_rows.to_dict(orient="records"),
                        "match_count": len(matching_rows)
                    }
                )

            else:
                return self._error_response(f"Unsupported operation for structured data: {operation}", "UnsupportedOperation")

        except Exception as e:
            logger.error(f"Error in structured data operation {operation}: {e}")
            return self._error_response(f"Structured data operation error: {str(e)}", "StructuredDataError")

    def _success_response(self, message: str, content: List[Dict[str, Any]] = None,
                         metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create a success response in MCP format."""
        response = {
            "isError": False,
            "content": content or [{"type": "text", "text": message}]
        }

        if metadata:
            response["metadata"] = metadata

        return response

    def _error_response(self, message: str, error_type: str) -> Dict[str, Any]:
        """Create an error response in MCP format."""
        return {
            "isError": True,
            "content": [{"type": "text", "text": message}],
            "metadata": {
                "error_type": error_type,
                "component": "DataAccessTool"
            }
        }

    async def _load_from_s3(self, s3_path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Load data from Amazon S3."""
        try:
            import boto3
            from botocore.exceptions import ClientError

            # Parse S3 path (s3://bucket/key)
            if not s3_path.startswith('s3://'):
                return self._error_response("Invalid S3 path format. Use s3://bucket/key", "InvalidS3Path")

            path_parts = s3_path[5:].split('/', 1)
            if len(path_parts) != 2:
                return self._error_response("Invalid S3 path format. Use s3://bucket/key", "InvalidS3Path")

            bucket_name, key = path_parts

            # Initialize S3 client
            aws_access_key = params.get("aws_access_key_id")
            aws_secret_key = params.get("aws_secret_access_key")
            aws_region = params.get("aws_region", "us-east-1")

            if aws_access_key and aws_secret_key:
                s3_client = boto3.client(
                    's3',
                    aws_access_key_id=aws_access_key,
                    aws_secret_access_key=aws_secret_key,
                    region_name=aws_region
                )
            else:
                # Use default credentials
                s3_client = boto3.client('s3', region_name=aws_region)

            # Download file to temporary location
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(key)[1]) as tmp_file:
                s3_client.download_fileobj(bucket_name, key, tmp_file)
                tmp_path = tmp_file.name

            try:
                # Load the downloaded file
                df = self._load_structured_data(tmp_path)

                return self._success_response(
                    f"Successfully loaded data from S3: {s3_path}",
                    metadata={
                        "source": s3_path,
                        "shape": df.shape,
                        "columns": df.columns.tolist(),
                        "data_preview": df.head().to_dict(orient="records")
                    }
                )
            finally:
                # Clean up temporary file
                os.unlink(tmp_path)

        except ImportError:
            return self._error_response("boto3 not available. Install with: pip install boto3", "MissingDependency")
        except ClientError as e:
            return self._error_response(f"S3 access error: {str(e)}", "S3AccessError")
        except Exception as e:
            return self._error_response(f"Error loading from S3: {str(e)}", "S3LoadError")

    async def _load_from_azure(self, azure_path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Load data from Azure Blob Storage."""
        try:
            from azure.storage.blob import BlobServiceClient

            # Parse Azure path (azure://container/blob)
            if not azure_path.startswith('azure://'):
                return self._error_response("Invalid Azure path format. Use azure://container/blob", "InvalidAzurePath")

            path_parts = azure_path[8:].split('/', 1)
            if len(path_parts) != 2:
                return self._error_response("Invalid Azure path format. Use azure://container/blob", "InvalidAzurePath")

            container_name, blob_name = path_parts

            # Initialize Azure client
            connection_string = params.get("azure_connection_string")
            account_name = params.get("azure_account_name")
            account_key = params.get("azure_account_key")

            if connection_string:
                blob_service_client = BlobServiceClient.from_connection_string(connection_string)
            elif account_name and account_key:
                blob_service_client = BlobServiceClient(
                    account_url=f"https://{account_name}.blob.core.windows.net",
                    credential=account_key
                )
            else:
                return self._error_response("Azure credentials required", "MissingCredentials")

            # Download blob to temporary location
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(blob_name)[1]) as tmp_file:
                blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)
                download_stream = blob_client.download_blob()
                tmp_file.write(download_stream.readall())
                tmp_path = tmp_file.name

            try:
                # Load the downloaded file
                df = self._load_structured_data(tmp_path)

                return self._success_response(
                    f"Successfully loaded data from Azure: {azure_path}",
                    metadata={
                        "source": azure_path,
                        "shape": df.shape,
                        "columns": df.columns.tolist(),
                        "data_preview": df.head().to_dict(orient="records")
                    }
                )
            finally:
                # Clean up temporary file
                os.unlink(tmp_path)

        except ImportError:
            return self._error_response("azure-storage-blob not available. Install with: pip install azure-storage-blob", "MissingDependency")
        except Exception as e:
            return self._error_response(f"Error loading from Azure: {str(e)}", "AzureLoadError")

    async def _load_from_gcp(self, gcp_path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Load data from Google Cloud Storage."""
        try:
            from google.cloud import storage

            # Parse GCP path (gs://bucket/object)
            if not gcp_path.startswith('gs://'):
                return self._error_response("Invalid GCP path format. Use gs://bucket/object", "InvalidGCPPath")

            path_parts = gcp_path[5:].split('/', 1)
            if len(path_parts) != 2:
                return self._error_response("Invalid GCP path format. Use gs://bucket/object", "InvalidGCPPath")

            bucket_name, object_name = path_parts

            # Initialize GCP client
            credentials_path = params.get("gcp_credentials_path")
            if credentials_path:
                client = storage.Client.from_service_account_json(credentials_path)
            else:
                # Use default credentials
                client = storage.Client()

            # Download object to temporary location
            import tempfile
            bucket = client.bucket(bucket_name)
            blob = bucket.blob(object_name)

            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(object_name)[1]) as tmp_file:
                blob.download_to_filename(tmp_file.name)
                tmp_path = tmp_file.name

            try:
                # Load the downloaded file
                df = self._load_structured_data(tmp_path)

                return self._success_response(
                    f"Successfully loaded data from GCP: {gcp_path}",
                    metadata={
                        "source": gcp_path,
                        "shape": df.shape,
                        "columns": df.columns.tolist(),
                        "data_preview": df.head().to_dict(orient="records")
                    }
                )
            finally:
                # Clean up temporary file
                os.unlink(tmp_path)

        except ImportError:
            return self._error_response("google-cloud-storage not available. Install with: pip install google-cloud-storage", "MissingDependency")
        except Exception as e:
            return self._error_response(f"Error loading from GCP: {str(e)}", "GCPLoadError")

    async def _validate_schema(self, file_path: str, expected_schema: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data against expected schema."""
        try:
            df = self._load_structured_data(file_path)

            validation_results = {
                "is_valid": True,
                "errors": [],
                "warnings": [],
                "schema_match": True
            }

            # Check column presence
            expected_columns = set(expected_schema.get("columns", []))
            actual_columns = set(df.columns)

            missing_columns = expected_columns - actual_columns
            extra_columns = actual_columns - expected_columns

            if missing_columns:
                validation_results["is_valid"] = False
                validation_results["errors"].append(f"Missing columns: {list(missing_columns)}")

            if extra_columns:
                validation_results["warnings"].append(f"Extra columns: {list(extra_columns)}")

            # Check data types
            expected_dtypes = expected_schema.get("dtypes", {})
            for col, expected_dtype in expected_dtypes.items():
                if col in df.columns:
                    actual_dtype = str(df[col].dtype)
                    if not self._dtype_compatible(actual_dtype, expected_dtype):
                        validation_results["is_valid"] = False
                        validation_results["errors"].append(
                            f"Column '{col}' has dtype '{actual_dtype}', expected '{expected_dtype}'"
                        )

            # Check constraints
            constraints = expected_schema.get("constraints", {})
            for col, constraint_list in constraints.items():
                if col in df.columns:
                    for constraint in constraint_list:
                        if not self._check_constraint(df[col], constraint):
                            validation_results["is_valid"] = False
                            validation_results["errors"].append(
                                f"Column '{col}' violates constraint: {constraint}"
                            )

            return self._success_response(
                f"Schema validation {'passed' if validation_results['is_valid'] else 'failed'}",
                metadata=validation_results
            )

        except Exception as e:
            return self._error_response(f"Schema validation error: {str(e)}", "ValidationError")

    def _dtype_compatible(self, actual: str, expected: str) -> bool:
        """Check if data types are compatible."""
        # Simple compatibility mapping
        compatibility_map = {
            "int64": ["int", "integer", "int64", "int32"],
            "float64": ["float", "double", "float64", "float32"],
            "object": ["string", "str", "object", "text"],
            "bool": ["boolean", "bool"],
            "datetime64[ns]": ["datetime", "timestamp", "date"]
        }

        for actual_type, compatible_types in compatibility_map.items():
            if actual_type in actual and expected.lower() in compatible_types:
                return True

        return actual.lower() == expected.lower()

    def _check_constraint(self, series: pd.Series, constraint: Dict[str, Any]) -> bool:
        """Check if a series satisfies a constraint."""
        constraint_type = constraint.get("type")

        if constraint_type == "not_null":
            return not series.isnull().any()
        elif constraint_type == "unique":
            return series.nunique() == len(series)
        elif constraint_type == "range":
            min_val = constraint.get("min")
            max_val = constraint.get("max")
            if min_val is not None and series.min() < min_val:
                return False
            if max_val is not None and series.max() > max_val:
                return False
            return True
        elif constraint_type == "values":
            allowed_values = set(constraint.get("allowed", []))
            return set(series.unique()).issubset(allowed_values)

        return True

    async def _detect_schema(self, file_path: str) -> Dict[str, Any]:
        """Automatically detect schema from data."""
        try:
            df = self._load_structured_data(file_path)

            schema = {
                "columns": df.columns.tolist(),
                "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
                "shape": df.shape,
                "nullable_columns": df.columns[df.isnull().any()].tolist(),
                "unique_columns": [],
                "numeric_columns": df.select_dtypes(include=[np.number]).columns.tolist(),
                "categorical_columns": [],
                "datetime_columns": df.select_dtypes(include=['datetime64']).columns.tolist()
            }

            # Detect unique columns
            for col in df.columns:
                if df[col].nunique() == len(df):
                    schema["unique_columns"].append(col)

            # Detect categorical columns
            for col in df.select_dtypes(include=['object']).columns:
                unique_ratio = df[col].nunique() / len(df)
                if unique_ratio < 0.1:  # Less than 10% unique values
                    schema["categorical_columns"].append(col)

            # Detect potential constraints
            constraints = {}
            for col in df.columns:
                col_constraints = []

                # Check for null values
                if not df[col].isnull().any():
                    col_constraints.append({"type": "not_null"})

                # Check for uniqueness
                if df[col].nunique() == len(df):
                    col_constraints.append({"type": "unique"})

                # Check for numeric ranges
                if df[col].dtype in [np.int64, np.float64]:
                    col_constraints.append({
                        "type": "range",
                        "min": float(df[col].min()),
                        "max": float(df[col].max())
                    })

                # Check for categorical values
                if col in schema["categorical_columns"]:
                    col_constraints.append({
                        "type": "values",
                        "allowed": df[col].unique().tolist()
                    })

                if col_constraints:
                    constraints[col] = col_constraints

            schema["constraints"] = constraints

            return self._success_response(
                f"Schema detected for {os.path.basename(file_path)}",
                metadata={"detected_schema": schema}
            )

        except Exception as e:
            return self._error_response(f"Schema detection error: {str(e)}", "SchemaDetectionError")

    async def _data_profiling(self, file_path: str) -> Dict[str, Any]:
        """Perform comprehensive data profiling."""
        try:
            df = self._load_structured_data(file_path)

            profile = {
                "basic_info": {
                    "shape": df.shape,
                    "memory_usage": df.memory_usage(deep=True).sum(),
                    "columns": df.columns.tolist()
                },
                "data_types": {col: str(dtype) for col, dtype in df.dtypes.items()},
                "missing_data": {
                    "total_missing": df.isnull().sum().sum(),
                    "missing_by_column": df.isnull().sum().to_dict(),
                    "missing_percentage": (df.isnull().sum() / len(df) * 100).to_dict()
                },
                "duplicates": {
                    "total_duplicates": df.duplicated().sum(),
                    "duplicate_percentage": df.duplicated().sum() / len(df) * 100
                },
                "numeric_summary": {},
                "categorical_summary": {}
            }

            # Numeric column analysis
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                profile["numeric_summary"][col] = {
                    "mean": float(df[col].mean()),
                    "median": float(df[col].median()),
                    "std": float(df[col].std()),
                    "min": float(df[col].min()),
                    "max": float(df[col].max()),
                    "unique_count": int(df[col].nunique()),
                    "outliers": self._detect_outliers(df[col])
                }

            # Categorical column analysis
            categorical_cols = df.select_dtypes(include=['object']).columns
            for col in categorical_cols:
                value_counts = df[col].value_counts()
                profile["categorical_summary"][col] = {
                    "unique_count": int(df[col].nunique()),
                    "most_frequent": value_counts.index[0] if len(value_counts) > 0 else None,
                    "most_frequent_count": int(value_counts.iloc[0]) if len(value_counts) > 0 else 0,
                    "top_values": value_counts.head(5).to_dict()
                }

            return self._success_response(
                f"Data profiling completed for {os.path.basename(file_path)}",
                metadata={"data_profile": profile}
            )

        except Exception as e:
            return self._error_response(f"Data profiling error: {str(e)}", "ProfilingError")

    def _detect_outliers(self, series: pd.Series) -> Dict[str, Any]:
        """Detect outliers using IQR method."""
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1

        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        outliers = series[(series < lower_bound) | (series > upper_bound)]

        return {
            "count": len(outliers),
            "percentage": len(outliers) / len(series) * 100,
            "lower_bound": float(lower_bound),
            "upper_bound": float(upper_bound)
        }

    async def _quality_assessment(self, file_path: str) -> Dict[str, Any]:
        """Assess overall data quality."""
        try:
            df = self._load_structured_data(file_path)

            # Calculate quality metrics
            total_cells = df.shape[0] * df.shape[1]
            missing_cells = df.isnull().sum().sum()
            duplicate_rows = df.duplicated().sum()

            # Quality scores (0-100)
            completeness_score = ((total_cells - missing_cells) / total_cells) * 100
            uniqueness_score = ((df.shape[0] - duplicate_rows) / df.shape[0]) * 100

            # Consistency score (based on data type consistency)
            consistency_issues = 0
            for col in df.select_dtypes(include=['object']).columns:
                # Check for mixed types (simplified)
                sample_values = df[col].dropna().head(100)
                if len(sample_values) > 0:
                    # Check if values look like they should be numeric
                    numeric_like = sum(1 for val in sample_values if str(val).replace('.', '').replace('-', '').isdigit())
                    if 0.1 < numeric_like / len(sample_values) < 0.9:  # Mixed numeric/text
                        consistency_issues += 1

            consistency_score = max(0, 100 - (consistency_issues / len(df.columns)) * 100)

            # Overall quality score
            overall_score = (completeness_score + uniqueness_score + consistency_score) / 3

            quality_assessment = {
                "overall_score": overall_score,
                "completeness_score": completeness_score,
                "uniqueness_score": uniqueness_score,
                "consistency_score": consistency_score,
                "issues": {
                    "missing_data": missing_cells > 0,
                    "duplicate_rows": duplicate_rows > 0,
                    "consistency_issues": consistency_issues > 0
                },
                "recommendations": []
            }

            # Generate recommendations
            if completeness_score < 90:
                quality_assessment["recommendations"].append("Address missing data issues")
            if uniqueness_score < 95:
                quality_assessment["recommendations"].append("Remove or investigate duplicate rows")
            if consistency_score < 80:
                quality_assessment["recommendations"].append("Review data types and format consistency")

            return self._success_response(
                f"Data quality assessment for {os.path.basename(file_path)}",
                metadata={"quality_assessment": quality_assessment}
            )

        except Exception as e:
            return self._error_response(f"Quality assessment error: {str(e)}", "QualityAssessmentError")
