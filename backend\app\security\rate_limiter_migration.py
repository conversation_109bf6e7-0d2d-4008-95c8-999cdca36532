"""
Rate Limiter Migration Plan and Implementation.

This module provides a migration strategy to replace the vulnerable Fixed Window
algorithm with the improved Sliding Window Counter algorithm while maintaining
backward compatibility.
"""

import logging
import time
from typing import Dict, Any, Optional
from .improved_rate_limiter import ImprovedRateLimiter, RateLimitResult
from .advanced_security import RateLimiter as LegacyRateLimiter

logger = logging.getLogger(__name__)


class MigrationRateLimiter:
    """
    Migration wrapper that allows gradual transition from Fixed Window to Sliding Window.
    
    Supports A/B testing and gradual rollout to ensure stability during migration.
    """
    
    def __init__(self, migration_percentage: float = 0.0):
        """
        Initialize migration rate limiter.
        
        Args:
            migration_percentage: Percentage of traffic to route to new algorithm (0.0 to 1.0)
        """
        self.migration_percentage = migration_percentage
        self.improved_limiter = ImprovedRateLimiter()
        self.legacy_limiter = LegacyRateLimiter()
        
        # Migration statistics
        self.migration_stats = {
            "total_requests": 0,
            "new_algorithm_requests": 0,
            "legacy_algorithm_requests": 0,
            "new_algorithm_blocks": 0,
            "legacy_algorithm_blocks": 0,
            "migration_errors": 0
        }
        
        logger.info(f"Migration rate limiter initialized with {migration_percentage*100}% traffic to new algorithm")
    
    def should_use_new_algorithm(self, identifier: str) -> bool:
        """
        Determine if request should use new algorithm based on migration percentage.
        
        Args:
            identifier: Request identifier
            
        Returns:
            True if should use new algorithm
        """
        if self.migration_percentage >= 1.0:
            return True
        elif self.migration_percentage <= 0.0:
            return False
        
        # Use hash of identifier for consistent routing
        identifier_hash = hash(identifier) % 100
        return identifier_hash < (self.migration_percentage * 100)
    
    def is_rate_limited(self, identifier: str, limit_type: str = "default") -> bool:
        """
        Check rate limiting with migration logic.
        
        Args:
            identifier: Unique identifier
            limit_type: Type of rate limit
            
        Returns:
            True if rate limited
        """
        self.migration_stats["total_requests"] += 1
        
        try:
            if self.should_use_new_algorithm(identifier):
                # Use new sliding window algorithm
                self.migration_stats["new_algorithm_requests"] += 1
                result = self.improved_limiter.check_rate_limit(identifier, limit_type)
                
                if not result.allowed:
                    self.migration_stats["new_algorithm_blocks"] += 1
                
                # Log detailed information for monitoring
                if not result.allowed:
                    logger.info(
                        f"New algorithm blocked {identifier}: "
                        f"usage={result.current_usage}, "
                        f"limit={self.improved_limiter.configs[limit_type].requests_per_window}, "
                        f"retry_after={result.retry_after}"
                    )
                
                return not result.allowed
            else:
                # Use legacy fixed window algorithm
                self.migration_stats["legacy_algorithm_requests"] += 1
                is_limited = self.legacy_limiter.is_rate_limited(identifier, limit_type)
                
                if is_limited:
                    self.migration_stats["legacy_algorithm_blocks"] += 1
                
                return is_limited
                
        except Exception as e:
            self.migration_stats["migration_errors"] += 1
            logger.error(f"Error in migration rate limiter: {e}")
            
            # Fallback to legacy algorithm on error
            return self.legacy_limiter.is_rate_limited(identifier, limit_type)
    
    def get_rate_limit_info(self, identifier: str, limit_type: str = "default") -> Dict[str, Any]:
        """Get rate limit information with migration awareness."""
        try:
            if self.should_use_new_algorithm(identifier):
                return self.improved_limiter.get_rate_limit_info(identifier, limit_type)
            else:
                return self.legacy_limiter.get_rate_limit_info(identifier, limit_type)
        except Exception as e:
            logger.error(f"Error getting rate limit info: {e}")
            return self.legacy_limiter.get_rate_limit_info(identifier, limit_type)
    
    def set_migration_percentage(self, percentage: float):
        """
        Update migration percentage for gradual rollout.
        
        Args:
            percentage: New migration percentage (0.0 to 1.0)
        """
        old_percentage = self.migration_percentage
        self.migration_percentage = max(0.0, min(1.0, percentage))
        
        logger.info(f"Migration percentage updated: {old_percentage*100}% -> {self.migration_percentage*100}%")
    
    def get_migration_statistics(self) -> Dict[str, Any]:
        """Get migration statistics for monitoring."""
        stats = self.migration_stats.copy()
        
        # Calculate percentages
        if stats["total_requests"] > 0:
            stats["new_algorithm_percentage"] = (stats["new_algorithm_requests"] / stats["total_requests"]) * 100
            stats["legacy_algorithm_percentage"] = (stats["legacy_algorithm_requests"] / stats["total_requests"]) * 100
        else:
            stats["new_algorithm_percentage"] = 0
            stats["legacy_algorithm_percentage"] = 0
        
        # Calculate block rates
        if stats["new_algorithm_requests"] > 0:
            stats["new_algorithm_block_rate"] = (stats["new_algorithm_blocks"] / stats["new_algorithm_requests"]) * 100
        else:
            stats["new_algorithm_block_rate"] = 0
            
        if stats["legacy_algorithm_requests"] > 0:
            stats["legacy_algorithm_block_rate"] = (stats["legacy_algorithm_blocks"] / stats["legacy_algorithm_requests"]) * 100
        else:
            stats["legacy_algorithm_block_rate"] = 0
        
        # Add configuration info
        stats["migration_percentage"] = self.migration_percentage * 100
        stats["improved_limiter_stats"] = self.improved_limiter.get_statistics()
        
        return stats


def create_migration_plan() -> Dict[str, Any]:
    """
    Create a comprehensive migration plan for rate limiter upgrade.
    
    Returns:
        Migration plan with phases and rollback procedures
    """
    return {
        "phases": [
            {
                "phase": 1,
                "name": "Preparation",
                "duration_days": 3,
                "migration_percentage": 0.0,
                "tasks": [
                    "Deploy new rate limiter code",
                    "Set up monitoring and alerting",
                    "Create rollback procedures",
                    "Test in staging environment"
                ],
                "success_criteria": [
                    "All tests pass in staging",
                    "Monitoring dashboards operational",
                    "Rollback procedures tested"
                ]
            },
            {
                "phase": 2,
                "name": "Canary Release",
                "duration_days": 7,
                "migration_percentage": 0.05,  # 5% of traffic
                "tasks": [
                    "Enable 5% traffic to new algorithm",
                    "Monitor error rates and performance",
                    "Compare blocking rates between algorithms",
                    "Collect user feedback"
                ],
                "success_criteria": [
                    "Error rate < 0.1%",
                    "Performance within 5% of baseline",
                    "No increase in false positives",
                    "No user complaints"
                ]
            },
            {
                "phase": 3,
                "name": "Gradual Rollout",
                "duration_days": 14,
                "migration_percentage": 0.25,  # 25% of traffic
                "tasks": [
                    "Increase to 25% traffic",
                    "Monitor burst attack resilience",
                    "Validate memory usage improvements",
                    "Performance testing under load"
                ],
                "success_criteria": [
                    "Improved burst attack resistance",
                    "Memory usage stable or improved",
                    "Performance maintained under load"
                ]
            },
            {
                "phase": 4,
                "name": "Majority Rollout",
                "duration_days": 7,
                "migration_percentage": 0.75,  # 75% of traffic
                "tasks": [
                    "Increase to 75% traffic",
                    "Final performance validation",
                    "Security testing",
                    "Prepare for full rollout"
                ],
                "success_criteria": [
                    "All security tests pass",
                    "Performance meets requirements",
                    "Ready for full deployment"
                ]
            },
            {
                "phase": 5,
                "name": "Full Deployment",
                "duration_days": 3,
                "migration_percentage": 1.0,  # 100% of traffic
                "tasks": [
                    "Switch to 100% new algorithm",
                    "Monitor for 72 hours",
                    "Remove legacy code",
                    "Update documentation"
                ],
                "success_criteria": [
                    "Stable operation for 72 hours",
                    "All metrics within acceptable ranges",
                    "Legacy code safely removed"
                ]
            }
        ],
        "rollback_triggers": [
            "Error rate > 1%",
            "Performance degradation > 20%",
            "Memory usage increase > 50%",
            "User complaints about false blocks",
            "Security vulnerability discovered"
        ],
        "monitoring_metrics": [
            "Request processing time",
            "Memory usage",
            "Block rate accuracy",
            "False positive rate",
            "Burst attack resistance",
            "Algorithm distribution"
        ],
        "rollback_procedure": [
            "Set migration percentage to 0%",
            "Monitor for 15 minutes",
            "Verify legacy algorithm stability",
            "Investigate root cause",
            "Plan remediation"
        ]
    }


# Example usage and testing functions
def test_migration_limiter():
    """Test the migration rate limiter functionality."""
    logger.info("Testing migration rate limiter...")
    
    # Test with 50% migration
    migration_limiter = MigrationRateLimiter(migration_percentage=0.5)
    
    # Test multiple identifiers
    test_identifiers = ["user1", "user2", "user3", "***********", "***********"]
    
    for identifier in test_identifiers:
        # Test multiple requests
        for i in range(5):
            is_limited = migration_limiter.is_rate_limited(identifier, "api")
            algorithm = "new" if migration_limiter.should_use_new_algorithm(identifier) else "legacy"
            logger.info(f"Request {i+1} for {identifier}: limited={is_limited}, algorithm={algorithm}")
    
    # Print statistics
    stats = migration_limiter.get_migration_statistics()
    logger.info(f"Migration statistics: {stats}")


if __name__ == "__main__":
    test_migration_limiter()
