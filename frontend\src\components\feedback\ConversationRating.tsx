import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Star, Loader2 } from 'lucide-react'; // For StarRating and Loader
import { useConversationFeedback } from '@/hooks/use-feedback'; // Import the hook
import { Textarea } from '@/components/ui/textarea'; // For optional text feedback
import { Label } from '@/components/ui/label';     // For optional text feedback

interface ConversationRatingProps {
  conversationId: string;
  personaId?: string; // Optional: pass if available for better context
  // onFeedbackSubmitted prop can be kept if parent needs to know, or removed if all logic is internal
}

// Basic StarRating component (can be moved to ui components)
interface StarRatingProps {
  rating: number;
  onRatingChange: (rating: number) => void;
  maxStars?: number;
}
const StarRating: React.FC<StarRatingProps> = ({ rating, onRatingChange, maxStars = 5 }) => {
  return (
    <div className="flex items-center gap-1">
      {[...Array(maxStars)].map((_, index) => {
        const starValue = index + 1;
        return (
          <Star
            key={starValue}
            className={`h-6 w-6 cursor-pointer ${
              starValue <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
            }`}
            onClick={() => onRatingChange(starValue)}
          />
        );
      })}
    </div>
  );
};

// Basic TagSelector component (can be moved to ui components)
interface TagSelectorProps {
  tags: string[];
  selectedTags: string[];
  onTagsChange: (selectedTags: string[]) => void;
}
const TagSelector: React.FC<TagSelectorProps> = ({ tags, selectedTags, onTagsChange }) => {
  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onTagsChange(selectedTags.filter(t => t !== tag));
    } else {
      onTagsChange([...selectedTags, tag]);
    }
  };
  return (
    <div className="flex flex-wrap gap-2 mt-3">
      {tags.map(tag => (
        <Button
          key={tag}
          variant={selectedTags.includes(tag) ? 'default' : 'outline'}
          size="sm"
          onClick={() => toggleTag(tag)}
        >
          {tag}
        </Button>
      ))}
    </div>
  );
};


export const ConversationRating: React.FC<ConversationRatingProps> = ({ conversationId, personaId }) => {
  const { mutate: submitConvFeedback, isPending: isLoading, isError, error } = useConversationFeedback();
  const [rating, setRating] = useState(0);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [feedbackText, setFeedbackText] = useState('');

  const feedbackTagOptions = ["Helpful", "Accurate", "Fast", "Clear", "Informative", "Confusing", "Slow", "Incorrect", "Too Brief", "Too Verbose"];

  const handleSubmit = async () => {
    if (rating === 0 && feedbackText.trim() === '' && selectedTags.length === 0) {
      // Optionally, show a toast or message that at least one feedback item is required
      return;
    }
    submitConvFeedback({ 
      conversation_id: conversationId, 
      rating, 
      feedback_tags: selectedTags, 
      feedback_text: feedbackText,
      persona_id: personaId 
    }, {
      onSuccess: () => {
        // Reset form on success
        setRating(0);
        setSelectedTags([]);
        setFeedbackText('');
      }
    });
  };

  return (
    <Card className="p-4 mt-4">
      <h3 className="text-lg font-semibold mb-3">Rate this conversation</h3>
      <div className="mb-3">
        <Label className="text-sm font-medium mb-1 block">Overall Rating</Label>
        <StarRating rating={rating} onRatingChange={setRating} />
      </div>
      <div className="mb-3">
        <Label className="text-sm font-medium mb-1 block">Select tags (optional)</Label>
        <TagSelector tags={feedbackTagOptions} selectedTags={selectedTags} onTagsChange={setSelectedTags} />
      </div>
      <div className="mb-4">
        <Label htmlFor={`convFeedbackText-${conversationId}`} className="text-sm font-medium mb-1 block">Additional Comments (optional)</Label>
        <Textarea 
            id={`convFeedbackText-${conversationId}`}
            value={feedbackText} 
            onChange={(e) => setFeedbackText(e.target.value)} 
            placeholder="Any other thoughts on this conversation?"
            className="mt-1 min-h-[80px]"
        />
      </div>
      <Button onClick={handleSubmit} className="mt-3 w-full" disabled={isLoading || (rating === 0 && feedbackText.trim() === '' && selectedTags.length === 0)}>
        {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
        {isLoading ? 'Submitting...' : 'Submit Feedback'}
      </Button>
      {isError && <p className="text-red-500 text-sm mt-2">{(error as Error)?.message || 'Failed to submit feedback.'}</p>}
    </Card>
  );
};
