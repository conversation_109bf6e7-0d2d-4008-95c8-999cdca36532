/**
 * Utility functions for working with JWT tokens
 */

/**
 * Decode a JWT token to get its payload
 * @param token JWT token string
 * @returns Decoded token payload or null if invalid
 */
export const decodeToken = (token: string): any | null => {
  try {
    if (!token || typeof token !== 'string') {
      return null;
    }

    // JWT tokens are in the format: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.warn('Invalid JWT token format');
      return null;
    }

    const base64Url = parts[1];
    if (!base64Url) {
      console.warn('Invalid JWT token payload');
      return null;
    }

    // Convert base64url to base64
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

    // Add padding if needed
    const padding = '='.repeat((4 - base64.length % 4) % 4);
    const base64Padded = base64 + padding;

    // Decode the base64 string
    try {
      const jsonPayload = decodeURIComponent(
        atob(base64Padded)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );

      const decoded = JSON.parse(jsonPayload);

      // Only log in development mode
      if (import.meta.env.DEV) {
        console.debug('Token decoded successfully:', {
          sub: decoded.sub,
          exp: decoded.exp ? new Date(decoded.exp * 1000).toLocaleString() : 'none',
          iat: decoded.iat ? new Date(decoded.iat * 1000).toLocaleString() : 'none'
        });
      }

      return decoded;
    } catch (e) {
      console.warn('Error parsing JWT payload');
      return null;
    }
  } catch (error) {
    console.warn('Error decoding JWT token');
    return null;
  }
};

/**
 * Check if a token is expired
 * @param token JWT token string
 * @returns true if token is expired or invalid, false otherwise
 */
export const isTokenExpired = (token: string): boolean => {
  const decodedToken = decodeToken(token);
  if (!decodedToken) {
    return true;
  }

  if (!decodedToken.exp) {
    return true;
  }

  // exp claim is in seconds since epoch
  const expirationTime = decodedToken.exp * 1000; // Convert to milliseconds
  const currentTime = Date.now();

  const isExpired = currentTime >= expirationTime;

  if (import.meta.env.DEV) {
    const timeRemaining = expirationTime - currentTime;
    if (isExpired) {
      console.debug(`Token expired at: ${new Date(expirationTime).toLocaleString()}`);
    } else {
      console.debug(`Token valid, expires in: ${Math.round(timeRemaining / 1000)} seconds`);
    }
  }

  return isExpired;
};

/**
 * Get the expiration time of a token in milliseconds
 * @param token JWT token string
 * @returns Expiration time in milliseconds or null if invalid
 */
export const getTokenExpirationTime = (token: string): number | null => {
  const decodedToken = decodeToken(token);
  if (!decodedToken) {
    return null;
  }

  if (!decodedToken.exp) {
    return null;
  }

  const expirationTime = decodedToken.exp * 1000; // Convert to milliseconds
  return expirationTime;
};

/**
 * Calculate time remaining before token expires in milliseconds
 * @param token JWT token string
 * @returns Time remaining in milliseconds or 0 if expired/invalid
 */
export const getTokenTimeRemaining = (token: string): number => {
  const expirationTime = getTokenExpirationTime(token);
  if (!expirationTime) {
    return 0;
  }

  const currentTime = Date.now();
  const timeRemaining = expirationTime - currentTime;

  if (timeRemaining <= 0) {
    return 0;
  }

  return timeRemaining;
};

/**
 * Check if a token should be refreshed (e.g., if it's close to expiring)
 * @param token JWT token string
 * @param refreshThresholdMs Time threshold in milliseconds (default: 10 minutes)
 * @returns true if token should be refreshed, false otherwise
 */
export const shouldRefreshToken = (token: string, refreshThresholdMs: number = 10 * 60 * 1000): boolean => {
  const timeRemaining = getTokenTimeRemaining(token);

  if (timeRemaining <= 0) {
    return true;
  }

  const shouldRefresh = timeRemaining < refreshThresholdMs;

  if (import.meta.env.DEV && shouldRefresh) {
    console.debug(`Token should be refreshed (${Math.round(timeRemaining / 1000)}s remaining, threshold: ${Math.round(refreshThresholdMs / 1000)}s)`);
  }

  return shouldRefresh;
};
