/**
 * Accessibility tests for the Enhanced Dashboard Ribbon Toolbar
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { axe, toHaveNoViolations } from 'jest-axe';
import {
  useScreenReaderAnnouncements,
  useRibbonKeyboardNavigation,
  useAccessibilityPreferences,
  AccessibleRibbonButton,
  AccessibleDropdownMenu,
  useResponsiveBreakpoints,
} from '../RibbonAccessibility';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock navigator
Object.defineProperty(navigator, 'userAgent', {
  writable: true,
  value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
});

describe('RibbonAccessibility', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('useScreenReaderAnnouncements', () => {
    const TestComponent = () => {
      const { announceAction, AnnouncementRegion } = useScreenReaderAnnouncements();
      
      return (
        <div>
          <button onClick={() => announceAction('Test announcement')}>
            Announce
          </button>
          <AnnouncementRegion />
        </div>
      );
    };

    it('renders announcement region with proper ARIA attributes', () => {
      render(<TestComponent />);
      
      const region = screen.getByRole('status');
      expect(region).toHaveAttribute('aria-live', 'polite');
      expect(region).toHaveAttribute('aria-atomic', 'true');
      expect(region).toHaveClass('sr-only');
    });

    it('announces messages to screen readers', async () => {
      const user = userEvent.setup();
      render(<TestComponent />);
      
      const button = screen.getByRole('button', { name: 'Announce' });
      await user.click(button);
      
      const region = screen.getByRole('status');
      expect(region).toHaveTextContent('Test announcement');
    });

    it('clears announcements after timeout', async () => {
      vi.useFakeTimers();
      const user = userEvent.setup();
      render(<TestComponent />);
      
      const button = screen.getByRole('button', { name: 'Announce' });
      await user.click(button);
      
      const region = screen.getByRole('status');
      expect(region).toHaveTextContent('Test announcement');
      
      // Fast-forward time
      vi.advanceTimersByTime(1000);
      
      expect(region).toHaveTextContent('');
      
      vi.useRealTimers();
    });
  });

  describe('useAccessibilityPreferences', () => {
    const TestComponent = () => {
      const { isHighContrast, isReducedMotion, screenReaderMode } = useAccessibilityPreferences();
      
      return (
        <div>
          <div data-testid="high-contrast">{isHighContrast.toString()}</div>
          <div data-testid="reduced-motion">{isReducedMotion.toString()}</div>
          <div data-testid="screen-reader">{screenReaderMode.toString()}</div>
        </div>
      );
    };

    it('detects high contrast preference', () => {
      // Mock high contrast preference
      window.matchMedia = vi.fn().mockImplementation(query => ({
        matches: query === '(prefers-contrast: high)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestComponent />);
      
      expect(screen.getByTestId('high-contrast')).toHaveTextContent('true');
    });

    it('detects reduced motion preference', () => {
      // Mock reduced motion preference
      window.matchMedia = vi.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestComponent />);
      
      expect(screen.getByTestId('reduced-motion')).toHaveTextContent('true');
    });

    it('responds to preference changes', () => {
      const mockMatchMedia = vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      window.matchMedia = mockMatchMedia;
      
      render(<TestComponent />);
      
      // Verify event listeners are added
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-contrast: high)');
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-reduced-motion: reduce)');
    });
  });

  describe('AccessibleRibbonButton', () => {
    const defaultProps = {
      children: 'Test Button',
      ariaLabel: 'Test button',
      ariaDescription: 'This is a test button',
      shortcut: 'Ctrl+T',
      onClick: vi.fn(),
    };

    it('renders with proper accessibility attributes', () => {
      render(<AccessibleRibbonButton {...defaultProps} />);
      
      const button = screen.getByRole('button', { name: 'Test button' });
      expect(button).toHaveAttribute('aria-label', 'Test button');
      expect(button).toHaveAttribute('title', 'Test button (Ctrl+T)');
    });

    it('includes description for screen readers', () => {
      render(<AccessibleRibbonButton {...defaultProps} />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-describedby');
      
      const description = screen.getByText('This is a test button');
      expect(description).toHaveClass('sr-only');
    });

    it('handles click events properly', async () => {
      const user = userEvent.setup();
      render(<AccessibleRibbonButton {...defaultProps} />);
      
      const button = screen.getByRole('button');
      await user.click(button);
      
      expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
    });

    it('applies high contrast styles when needed', () => {
      // Mock high contrast mode
      vi.mocked(useAccessibilityPreferences).mockReturnValue({
        isHighContrast: true,
        isReducedMotion: false,
        screenReaderMode: false,
      });

      render(<AccessibleRibbonButton {...defaultProps} />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('border-2', 'border-current');
    });

    it('disables transitions in reduced motion mode', () => {
      // Mock reduced motion mode
      vi.mocked(useAccessibilityPreferences).mockReturnValue({
        isHighContrast: false,
        isReducedMotion: true,
        screenReaderMode: false,
      });

      render(<AccessibleRibbonButton {...defaultProps} />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('transition-none');
    });

    it('handles disabled state correctly', () => {
      render(<AccessibleRibbonButton {...defaultProps} disabled />);
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveClass('opacity-50', 'pointer-events-none');
    });
  });

  describe('AccessibleDropdownMenu', () => {
    const TestDropdown = () => (
      <AccessibleDropdownMenu
        trigger={<button>Open Menu</button>}
        ariaLabel="Test menu"
      >
        <div role="menuitem">Item 1</div>
        <div role="menuitem">Item 2</div>
        <div role="menuitem">Item 3</div>
      </AccessibleDropdownMenu>
    );

    it('renders trigger with proper attributes', () => {
      render(<TestDropdown />);
      
      const trigger = screen.getByRole('button', { name: 'Open Menu' });
      expect(trigger).toHaveAttribute('aria-expanded', 'false');
      expect(trigger).toHaveAttribute('aria-haspopup', 'menu');
      expect(trigger).toHaveAttribute('aria-label', 'Test menu');
    });

    it('opens menu when trigger is clicked', async () => {
      const user = userEvent.setup();
      render(<TestDropdown />);
      
      const trigger = screen.getByRole('button');
      await user.click(trigger);
      
      expect(trigger).toHaveAttribute('aria-expanded', 'true');
      expect(screen.getByRole('menu')).toBeInTheDocument();
    });

    it('closes menu when escape is pressed', async () => {
      const user = userEvent.setup();
      render(<TestDropdown />);
      
      const trigger = screen.getByRole('button');
      await user.click(trigger);
      
      expect(screen.getByRole('menu')).toBeInTheDocument();
      
      await user.keyboard('{Escape}');
      
      expect(screen.queryByRole('menu')).not.toBeInTheDocument();
      expect(trigger).toHaveFocus();
    });

    it('supports arrow key navigation', async () => {
      const user = userEvent.setup();
      render(<TestDropdown />);
      
      const trigger = screen.getByRole('button');
      await user.click(trigger);
      
      const menu = screen.getByRole('menu');
      const items = screen.getAllByRole('menuitem');
      
      // Focus first item
      items[0].focus();
      
      await user.keyboard('{ArrowDown}');
      
      // This would test actual focus movement in a real implementation
      expect(menu).toBeInTheDocument();
    });
  });

  describe('useResponsiveBreakpoints', () => {
    const TestComponent = () => {
      const { breakpoint, isMobile, isTablet, isDesktop } = useResponsiveBreakpoints();
      
      return (
        <div>
          <div data-testid="breakpoint">{breakpoint}</div>
          <div data-testid="is-mobile">{isMobile.toString()}</div>
          <div data-testid="is-tablet">{isTablet.toString()}</div>
          <div data-testid="is-desktop">{isDesktop.toString()}</div>
        </div>
      );
    };

    it('detects desktop breakpoint by default', () => {
      // Mock desktop width
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200,
      });

      render(<TestComponent />);
      
      expect(screen.getByTestId('breakpoint')).toHaveTextContent('desktop');
      expect(screen.getByTestId('is-desktop')).toHaveTextContent('true');
      expect(screen.getByTestId('is-mobile')).toHaveTextContent('false');
      expect(screen.getByTestId('is-tablet')).toHaveTextContent('false');
    });

    it('detects mobile breakpoint', () => {
      // Mock mobile width
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500,
      });

      render(<TestComponent />);
      
      expect(screen.getByTestId('breakpoint')).toHaveTextContent('mobile');
      expect(screen.getByTestId('is-mobile')).toHaveTextContent('true');
    });

    it('detects tablet breakpoint', () => {
      // Mock tablet width
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 800,
      });

      render(<TestComponent />);
      
      expect(screen.getByTestId('breakpoint')).toHaveTextContent('tablet');
      expect(screen.getByTestId('is-tablet')).toHaveTextContent('true');
    });

    it('responds to window resize events', () => {
      render(<TestComponent />);
      
      // Mock resize event
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500,
      });

      fireEvent(window, new Event('resize'));
      
      // Component should update breakpoint
      expect(screen.getByTestId('breakpoint')).toBeInTheDocument();
    });
  });

  describe('Accessibility Compliance', () => {
    it('passes axe accessibility tests', async () => {
      const { container } = render(
        <AccessibleRibbonButton ariaLabel="Test button">
          Test Button
        </AccessibleRibbonButton>
      );
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('maintains proper focus order', async () => {
      const user = userEvent.setup();
      render(
        <div>
          <AccessibleRibbonButton ariaLabel="Button 1">Button 1</AccessibleRibbonButton>
          <AccessibleRibbonButton ariaLabel="Button 2">Button 2</AccessibleRibbonButton>
          <AccessibleRibbonButton ariaLabel="Button 3">Button 3</AccessibleRibbonButton>
        </div>
      );
      
      const button1 = screen.getByRole('button', { name: 'Button 1' });
      const button2 = screen.getByRole('button', { name: 'Button 2' });
      const button3 = screen.getByRole('button', { name: 'Button 3' });
      
      button1.focus();
      expect(button1).toHaveFocus();
      
      await user.tab();
      expect(button2).toHaveFocus();
      
      await user.tab();
      expect(button3).toHaveFocus();
    });

    it('provides sufficient color contrast', () => {
      render(<AccessibleRibbonButton ariaLabel="Test button">Test Button</AccessibleRibbonButton>);
      
      const button = screen.getByRole('button');
      
      // This would be tested with actual color contrast checking tools
      // For now, we verify the component renders
      expect(button).toBeInTheDocument();
    });

    it('supports screen reader navigation', () => {
      render(
        <AccessibleDropdownMenu
          trigger={<button>Menu</button>}
          ariaLabel="Test menu"
        >
          <div role="menuitem">Item 1</div>
          <div role="menuitem">Item 2</div>
        </AccessibleDropdownMenu>
      );
      
      const trigger = screen.getByRole('button');
      expect(trigger).toHaveAttribute('aria-haspopup', 'menu');
      expect(trigger).toHaveAttribute('aria-expanded', 'false');
    });
  });
});
