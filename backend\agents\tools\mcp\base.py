"""
Base MCP tool interface for the Datagenius backend.

This module defines the base MCP tool interface that all MCP-compatible tools must implement.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class MCPTool(ABC):
    """Base class for MCP-compatible agent tools."""

    def __init__(self):
        """Initialize the base MCP tool."""
        self.config = {}

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        self.config = config
        
        # Additional initialization should be implemented by subclasses
        await self._initialize(config)
        logger.info(f"Initialized MCP tool: {self.name}")

    @abstractmethod
    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Additional initialization for subclasses.

        Args:
            config: Configuration dictionary for the tool
        """
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """
        Get the name of the tool.

        Returns:
            Tool name
        """
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        """
        Get the description of the tool.

        Returns:
            Tool description
        """
        pass

    @property
    @abstractmethod
    def definition(self) -> Dict[str, Any]:
        """
        Get the tool definition in MCP format.

        Returns:
            Tool definition dictionary compatible with MCP
        """
        pass

    @abstractmethod
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution (following the inputSchema)

        Returns:
            Tool execution results in MCP format
        """
        pass


import os # Added for path joining
import sys # Added for path manipulation

# Add yaml_utils import relative to backend directory
try:
    # Adjust path relative to this file's location (backend/agents/tools/mcp -> backend/app/utils)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Path to backend/app/utils
    utils_dir = os.path.abspath(os.path.join(current_dir, '..', '..', '..', 'app', 'utils'))
    if utils_dir not in sys.path:
        # Add 'backend' to path, then 'app'
        sys.path.insert(0, os.path.dirname(os.path.dirname(utils_dir))) # Add 'backend'
    from app.utils.yaml_utils import load_yaml # Only load_yaml is needed here
except ImportError as e:
    print(f"Error importing yaml_utils in mcp.base: {e}. Ensure backend/app/utils is accessible.")
    def load_yaml(*args, **kwargs): raise NotImplementedError("yaml_utils not loaded")


class BaseMCPTool(MCPTool):
    """Base implementation of an MCP-compatible tool."""

    def __init__(self, name: str, description: str, schema_path: Optional[str] = None, 
                 input_schema: Optional[Dict[str, Any]] = None, 
                 output_schema: Optional[Dict[str, Any]] = None, # Added for consistency with plan
                 annotations: Optional[Dict[str, Any]] = None, **kwargs): # Added kwargs
        """
        Initialize the tool.
        
        Args:
            name: Tool name
            description: Tool description
            schema_path: Path to the schema YAML file (relative to schemas/tools)
            input_schema: JSON Schema for tool input (for backward compatibility)
            output_schema: JSON Schema for tool output (for backward compatibility)
            annotations: Optional annotations for the tool (for backward compatibility)
            **kwargs: Additional arguments for backward compatibility (e.g. older schema params)
        """
        super().__init__()
        self._name = name
        self._description = description
        
        # Determine the root directory for schemas (backend/schemas/tools)
        # This assumes base.py is in backend/agents/tools/mcp/
        # So, ../../.. takes us to backend/
        schema_root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'schemas', 'tools'))

        if schema_path:
            full_schema_path = os.path.join(schema_root_dir, schema_path)
            logger.info(f"Loading tool schema from: {full_schema_path}")
            try:
                schema_data = load_yaml(full_schema_path)
                self.input_schema = schema_data.get("input_schema", {})
                self._output_schema = schema_data.get("output_schema", {}) # Store as _output_schema
                self.annotations = schema_data.get("annotations", {})
            except FileNotFoundError:
                logger.error(f"Schema file not found: {full_schema_path}. Tool '{name}' will have empty schemas.")
                self.input_schema = input_schema or {} # Fallback to passed arg or empty
                self._output_schema = output_schema or {}
                self.annotations = annotations or {}
            except Exception as e:
                logger.error(f"Error loading schema from {full_schema_path} for tool '{name}': {e}. Using fallback schemas.")
                self.input_schema = input_schema or {}
                self._output_schema = output_schema or {}
                self.annotations = annotations or {}
        else:
            # Backward compatibility: use direct kwargs or passed arguments
            logger.debug(f"No schema_path provided for tool '{name}'. Using provided or default schemas.")
            self.input_schema = input_schema or kwargs.get("input_schema", {})
            self._output_schema = output_schema or kwargs.get("output_schema", {})
            self.annotations = annotations or kwargs.get("annotations", {})
        
        # Ensure _output_schema exists even if not loaded
        if not hasattr(self, '_output_schema'):
            self._output_schema = {}


    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # Default implementation does nothing
        pass

    @property
    def name(self) -> str:
        """
        Get the name of the tool.

        Returns:
            Tool name
        """
        return self._name

    @property
    def description(self) -> str:
        """
        Get the description of the tool.

        Returns:
            Tool description
        """
        return self._description

    @property
    def definition(self) -> Dict[str, Any]:
        """
        Get the tool definition in MCP format.

        Returns:
            Tool definition dictionary compatible with MCP
        """
        definition = {
            "name": self.name,
            "description": self.description,
            "inputSchema": self.input_schema,
            "annotations": self.annotations
        }
        # Include outputSchema if it's defined and not empty
        if hasattr(self, '_output_schema') and self._output_schema:
            definition["outputSchema"] = self._output_schema
        return definition

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value by key.

        Args:
            key: Configuration key
            default: Default value if key is not found

        Returns:
            Configuration value
        """
        return self.config.get(key, default)
