"""
Prompt template system for the Datagenius backend.

This module provides a template system for system prompts with variable substitution.
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)


class PromptTemplate:
    """Template for system prompts with variable substitution."""

    def __init__(self, template: str):
        """
        Initialize a prompt template.

        Args:
            template: The template string with placeholders for variables
        """
        self.template = template
        logger.debug(f"Initialized prompt template: {template[:50]}...")

    def format(self, **kwargs) -> str:
        """
        Format the template with the provided variables.

        Args:
            **kwargs: Variables to substitute in the template

        Returns:
            Formatted prompt
        """
        try:
            return self.template.format(**kwargs)
        except KeyError as e:
            logger.warning(f"Missing variable in prompt template: {e}")
            # Return template with missing variables marked
            return self.template.format(**{
                **kwargs,
                **{key: f"[MISSING: {key}]" for key in [str(e).strip("'")]}
            })
        except Exception as e:
            logger.error(f"Error formatting prompt template: {e}")
            return self.template
