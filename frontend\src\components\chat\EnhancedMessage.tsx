import React, { useState } from 'react';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import type { Components } from 'react-markdown';
import {
  Copy,
  ThumbsUp,
  ThumbsDown,
  MoreVertical,
  FileText,
  Database,
  User,
  Bot,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { MessageDeliveryStatus } from './ChatLoadingStates';
import { useToast } from '@/components/ui/use-toast';
import { TableVisualization } from '@/components/visualizations/TableVisualization';
import { AnalysisResultVisualization } from '@/components/visualizations/AnalysisResultVisualization';
import { VisualizationRenderer } from '@/components/visualizations/VisualizationRenderer';

interface MessageAttachment {
  id: string;
  name: string;
  type: string;
  size?: number;
}

interface EnhancedMessageProps {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
  personaName?: string;
  personaAvatar?: string;
  attachments?: MessageAttachment[];
  deliveryStatus?: 'pending' | 'delivered' | 'failed';
  isStreaming?: boolean;
  metadata?: Record<string, any>;
  onCopy?: () => void;
  onFeedback?: (rating: number) => void;
  onRetry?: () => void;
  onActionClick?: (actionKey: string) => void;
  className?: string;
}

export const EnhancedMessage: React.FC<EnhancedMessageProps> = ({
  id,
  content,
  sender,
  timestamp,
  personaName,
  personaAvatar,
  attachments = [],
  deliveryStatus,
  isStreaming = false,
  metadata,
  onCopy,
  onFeedback,
  onRetry,
  onActionClick,
  className = ''
}) => {
  const [showActions, setShowActions] = useState(false);
  const [feedbackGiven, setFeedbackGiven] = useState<number | null>(null);
  const { toast } = useToast();

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      toast({
        title: 'Copied to clipboard',
        description: 'Message content has been copied.',
      });
      onCopy?.();
    } catch (error) {
      toast({
        title: 'Copy failed',
        description: 'Could not copy message content.',
        variant: 'destructive'
      });
    }
  };

  const handleFeedback = (rating: number) => {
    setFeedbackGiven(rating);
    onFeedback?.(rating);
    toast({
      title: 'Feedback submitted',
      description: 'Thank you for your feedback!',
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const isUser = sender === 'user';
  const hasError = metadata?.status === 'error' || deliveryStatus === 'failed';

  // Debug streaming status
  if (isStreaming && sender === 'ai') {
    console.log(`🎬 EnhancedMessage: Message ${id} is streaming, content="${content.substring(0, 50)}...", metadata:`, metadata);
  }

  // Helper function to detect if content contains tables
  const detectTables = (content: string): Array<{
    type: 'text' | 'table';
    content: string;
    tableData?: { headers: string[]; rows: string[][] };
  }> => {
    const lines = content.split('\n');
    const sections: Array<{
      type: 'text' | 'table';
      content: string;
      tableData?: { headers: string[]; rows: string[][] };
    }> = [];

    let currentSection = { type: 'text' as const, content: '', tableData: undefined };
    let inTable = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Check if this line looks like a table row (contains |)
      const isTableRow = line.includes('|') && line.split('|').length >= 3;

      if (isTableRow && !inTable) {
        // Starting a new table - save current text section if it has content
        if (currentSection.content.trim()) {
          sections.push(currentSection);
        }

        // Start new table section
        currentSection = { type: 'table', content: line + '\n', tableData: undefined };
        inTable = true;
      } else if (isTableRow && inTable) {
        // Continue table
        currentSection.content += line + '\n';
      } else if (!isTableRow && inTable) {
        // End of table - parse the table data
        const tableData = parseMarkdownTable(currentSection.content);
        if (tableData) {
          currentSection.tableData = tableData;
        }
        sections.push(currentSection);

        // Start new text section
        currentSection = { type: 'text', content: line + '\n', tableData: undefined };
        inTable = false;
      } else {
        // Regular text line
        currentSection.content += line + '\n';
      }
    }

    // Add the last section
    if (currentSection.content.trim()) {
      if (currentSection.type === 'table') {
        const tableData = parseMarkdownTable(currentSection.content);
        if (tableData) {
          currentSection.tableData = tableData;
        }
      }
      sections.push(currentSection);
    }

    return sections;
  };

  // Helper function to parse markdown table into structured data
  const parseMarkdownTable = (tableContent: string): { headers: string[]; rows: string[][] } | null => {
    const lines = tableContent.trim().split('\n').filter(line => line.trim());

    if (lines.length < 2) return null;

    // Parse header row
    const headerLine = lines[0];
    const headers = headerLine.split('|')
      .map(cell => cell.trim())
      .filter(cell => cell !== '');

    if (headers.length === 0) return null;

    // Skip separator row (usually line 1)
    const dataLines = lines.slice(1).filter(line => !line.match(/^[\s\|:\-]+$/));

    // Parse data rows
    const rows: string[][] = [];
    for (const line of dataLines) {
      const cells = line.split('|')
        .map(cell => cell.trim())
        .filter(cell => cell !== '');

      if (cells.length === headers.length) {
        rows.push(cells);
      }
    }

    return rows.length > 0 ? { headers, rows } : null;
  };

  // Function to check if message has visualization data
  const hasVisualizationData = (): boolean => {
    if (!metadata) return false;

    // Check for various visualization indicators
    return !!(
      metadata.visualization ||
      metadata.interactive_chart ||
      (metadata.content && Array.isArray(metadata.content) &&
       metadata.content.some((item: any) => item.type === 'image' && item.src)) ||
      metadata.data_preview ||
      metadata.data_profile ||
      metadata.query_result ||
      (metadata.task_type && ['pandasai_analysis', 'pandasai_visualization', 'pandasai_query'].includes(metadata.task_type))
    );
  };

  // Function to render visualization if present
  const renderVisualization = (): React.ReactNode => {
    if (!hasVisualizationData()) return null;

    return (
      <div className="mt-4">
        <VisualizationRenderer
          metadata={metadata}
          onRetry={() => {
            // TODO: Implement retry functionality
            console.log('Retry visualization for message:', id);
          }}
        />
      </div>
    );
  };

  // Function to parse message content and render text/buttons with markdown support
  const renderMessageContent = (content: string): React.ReactNode => {
    // Check if this is from Composable Analyst (should always use enhanced markdown)
    const isComposableAnalyst = personaName?.toLowerCase().includes('composable analyst') ||
                               personaName?.toLowerCase().includes('composable-analyst') ||
                               metadata?.agent_type === 'analysis';

    // For Composable Analyst, detect and render tables using dedicated components
    if (isComposableAnalyst && !isStreaming) {
      const sections = detectTables(content);

      // If we found tables, render mixed content
      if (sections.some(section => section.type === 'table' && section.tableData)) {
        return (
          <div className="space-y-4">
            {sections.map((section, index) => {
              if (section.type === 'table' && section.tableData) {
                // Render table using TableVisualization component
                const tableVisualization = {
                  type: 'table' as const,
                  title: `Data Table ${index + 1}`,
                  data: {
                    headers: section.tableData.headers,
                    rows: section.tableData.rows
                  }
                };

                return (
                  <div key={`table-${index}`} className="my-4">
                    <TableVisualization
                      visualization={tableVisualization}
                      className="border-0 shadow-md"
                    />
                  </div>
                );
              } else {
                // Render text content with markdown
                const markdownComponents: Components = {
                  h1: ({ children, ...props }) => <h1 className="text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900 border-b border-gray-200 pb-2" {...props}>{children}</h1>,
                  h2: ({ children, ...props }) => <h2 className="text-lg font-semibold mb-2 mt-3 first:mt-0 text-gray-800" {...props}>{children}</h2>,
                  h3: ({ children, ...props }) => <h3 className="text-base font-medium mb-2 mt-3 first:mt-0 text-gray-700" {...props}>{children}</h3>,
                  p: ({ children, ...props }) => <p className="mb-2 last:mb-0" {...props}>{children}</p>,
                  ul: ({ children, ...props }) => <ul className="list-disc list-inside mb-3 space-y-1" {...props}>{children}</ul>,
                  ol: ({ children, ...props }) => <ol className="list-decimal list-inside mb-3 space-y-1" {...props}>{children}</ol>,
                  li: ({ children, ...props }) => <li className="ml-2" {...props}>{children}</li>,
                  strong: ({ children, ...props }) => <strong className="font-semibold" {...props}>{children}</strong>,
                  em: ({ children, ...props }) => <em className="italic" {...props}>{children}</em>,
                  code: ({ children, ...props }) => <code className="bg-gray-200 px-1 py-0.5 rounded text-sm font-mono" {...props}>{children}</code>,
                  pre: ({ children, ...props }) => <pre className="bg-gray-100 p-3 rounded-md overflow-x-auto mb-3" {...props}>{children}</pre>,
                  blockquote: ({ children, ...props }) => <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-3" {...props}>{children}</blockquote>,
                  a: ({ href, children, ...props }) => <a href={href} className="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer" {...props}>{children}</a>,
                };

                return (
                  <div key={`text-${index}`} className="prose prose-sm max-w-none prose-gray">
                    <ReactMarkdown components={markdownComponents}>
                      {section.content}
                    </ReactMarkdown>
                  </div>
                );
              }
            })}
          </div>
        );
      }
    }

    // Check if metadata contains table data that should be rendered with dedicated components
    if (metadata && !isStreaming) {
      // Check for analysis result metadata with content
      if (metadata.content && Array.isArray(metadata.content)) {
        const hasTableContent = metadata.content.some((item: any) => item.type === 'table');

        if (hasTableContent) {
          const analysisVisualization = {
            type: 'analysis_result' as const,
            title: 'Analysis Results',
            data: metadata
          };

          return (
            <div className="space-y-4">
              {/* Render the main content as markdown */}
              <div className="prose prose-sm max-w-none prose-gray">
                <ReactMarkdown components={{
                  h1: ({ children, ...props }) => <h1 className="text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900 border-b border-gray-200 pb-2" {...props}>{children}</h1>,
                  h2: ({ children, ...props }) => <h2 className="text-lg font-semibold mb-2 mt-3 first:mt-0 text-gray-800" {...props}>{children}</h2>,
                  h3: ({ children, ...props }) => <h3 className="text-base font-medium mb-2 mt-3 first:mt-0 text-gray-700" {...props}>{children}</h3>,
                  p: ({ children, ...props }) => <p className="mb-2 last:mb-0" {...props}>{children}</p>,
                }}>
                  {content}
                </ReactMarkdown>
              </div>

              {/* Render the analysis visualization */}
              <AnalysisResultVisualization visualization={analysisVisualization} />
            </div>
          );
        }
      }

      // Check for preview_data that should be rendered as a table
      if (metadata.preview_data && Array.isArray(metadata.preview_data) && metadata.preview_data.length > 0) {
        const firstRow = metadata.preview_data[0];
        const headers = Object.keys(firstRow);
        const rows = metadata.preview_data.map((row: any) => headers.map(header => String(row[header] || '')));

        const tableVisualization = {
          type: 'table' as const,
          title: 'Data Preview',
          data: { headers, rows }
        };

        return (
          <div className="space-y-4">
            {/* Render the main content as markdown */}
            <div className="prose prose-sm max-w-none prose-gray">
              <ReactMarkdown components={{
                h1: ({ children, ...props }) => <h1 className="text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900 border-b border-gray-200 pb-2" {...props}>{children}</h1>,
                h2: ({ children, ...props }) => <h2 className="text-lg font-semibold mb-2 mt-3 first:mt-0 text-gray-800" {...props}>{children}</h2>,
                h3: ({ children, ...props }) => <h3 className="text-base font-medium mb-2 mt-3 first:mt-0 text-gray-700" {...props}>{children}</h3>,
                p: ({ children, ...props }) => <p className="mb-2 last:mb-0" {...props}>{children}</p>,
              }}>
                {content}
              </ReactMarkdown>
            </div>

            {/* Render the table */}
            <TableVisualization
              visualization={tableVisualization}
              className="border-0 shadow-md"
            />
          </div>
        );
      }
    }

    // Check if content contains action buttons
    const hasActionButtons = /\[([^\]]+)\]\(action:([^)]+)\)/g.test(content);

    // Use action button parsing for messages that contain action buttons
    if (hasActionButtons) {
      // If content has action buttons, use the original parsing logic
      const lines = content.split('\n');
      const renderedLines: React.ReactNode[] = [];

      lines.forEach((line, lineIndex) => {
        const lineParts: React.ReactNode[] = [];
        let currentLineIndex = 0;
        const lineRegex = /\[([^\]]+)\]\(action:([^)]+)\)/g;
        let lineMatch;

        while ((lineMatch = lineRegex.exec(line)) !== null) {
          // Add text before the button (with markdown support)
          if (lineMatch.index > currentLineIndex) {
            const textBefore = line.substring(currentLineIndex, lineMatch.index);
            if (textBefore.trim()) {
              const inlineComponents: Components = {
                p: ({ children, ...props }) => <span {...props}>{children}</span>,
                // Prevent block elements in inline context
                h1: ({ children, ...props }) => <strong className="text-lg" {...props}>{children}</strong>,
                h2: ({ children, ...props }) => <strong className="text-base" {...props}>{children}</strong>,
                h3: ({ children, ...props }) => <strong {...props}>{children}</strong>,
              };

              lineParts.push(
                <div key={`text-${lineIndex}-${currentLineIndex}`} className="inline">
                  <ReactMarkdown components={inlineComponents}>
                    {textBefore}
                  </ReactMarkdown>
                </div>
              );
            }
          }

          // Add the button
          const buttonText = lineMatch[1];
          const actionKey = lineMatch[2];
          lineParts.push(
            <Button
              key={`line-${lineIndex}-action-${lineMatch.index}-${actionKey}`}
              variant="outline"
              size="sm"
              className="mx-1 my-0.5 h-auto py-1 px-2 text-sm whitespace-normal text-left inline-block align-middle"
              onClick={() => {
                const finalActionKey = actionKey.trim().toLowerCase();
                console.log(`Action button clicked: ${finalActionKey}`);
                onActionClick?.(finalActionKey);
              }}
            >
              {buttonText}
            </Button>
          );

          currentLineIndex = lineMatch.index + lineMatch[0].length;
        }

        // Add remaining text after the last button
        if (currentLineIndex < line.length) {
          const remainingText = line.substring(currentLineIndex);
          if (remainingText.trim()) {
            const inlineComponents: Components = {
              p: ({ children, ...props }) => <span {...props}>{children}</span>,
              h1: ({ children, ...props }) => <strong className="text-lg" {...props}>{children}</strong>,
              h2: ({ children, ...props }) => <strong className="text-base" {...props}>{children}</strong>,
              h3: ({ children, ...props }) => <strong {...props}>{children}</strong>,
            };

            lineParts.push(
              <div key={`text-${lineIndex}-${currentLineIndex}-end`} className="inline">
                <ReactMarkdown components={inlineComponents}>
                  {remainingText}
                </ReactMarkdown>
              </div>
            );
          }
        }

        // If no buttons were found in this line, render as markdown
        if (lineParts.length === 0 && line.trim()) {
          const inlineComponents: Components = {
            p: ({ children, ...props }) => <span {...props}>{children}</span>,
            h1: ({ children, ...props }) => <strong className="text-lg" {...props}>{children}</strong>,
            h2: ({ children, ...props }) => <strong className="text-base" {...props}>{children}</strong>,
            h3: ({ children, ...props }) => <strong {...props}>{children}</strong>,
          };

          lineParts.push(
            <div key={`line-${lineIndex}`} className="inline">
              <ReactMarkdown components={inlineComponents}>
                {line}
              </ReactMarkdown>
            </div>
          );
        }

        // Add the line parts to rendered lines
        if (lineParts.length > 0) {
          renderedLines.push(
            <div key={`line-${lineIndex}`} className="mb-1">
              {lineParts}
            </div>
          );
        } else if (line.trim() === '') {
          // Add empty lines as spacing
          renderedLines.push(<br key={`line-${lineIndex}`} />);
        }
      });

      return <>{renderedLines}</>;
    } else {
      // If no action buttons, render as full markdown
      const markdownComponents: Components = {
        // Custom components for better styling
        h1: ({ children, ...props }) => <h1 className="text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900 border-b border-gray-200 pb-2" {...props}>{children}</h1>,
        h2: ({ children, ...props }) => <h2 className="text-lg font-semibold mb-2 mt-3 first:mt-0 text-gray-800" {...props}>{children}</h2>,
        h3: ({ children, ...props }) => <h3 className="text-base font-medium mb-2 mt-3 first:mt-0 text-gray-700" {...props}>{children}</h3>,
        p: ({ children, ...props }) => <p className="mb-2 last:mb-0" {...props}>{children}</p>,
        ul: ({ children, ...props }) => <ul className="list-disc list-inside mb-3 space-y-1" {...props}>{children}</ul>,
        ol: ({ children, ...props }) => <ol className="list-decimal list-inside mb-3 space-y-1" {...props}>{children}</ol>,
        li: ({ children, ...props }) => <li className="ml-2" {...props}>{children}</li>,
        strong: ({ children, ...props }) => <strong className="font-semibold" {...props}>{children}</strong>,
        em: ({ children, ...props }) => <em className="italic" {...props}>{children}</em>,
        code: ({ children, ...props }) => <code className="bg-gray-200 px-1 py-0.5 rounded text-sm font-mono" {...props}>{children}</code>,
        pre: ({ children, ...props }) => <pre className="bg-gray-100 p-3 rounded-md overflow-x-auto mb-3" {...props}>{children}</pre>,
        blockquote: ({ children, ...props }) => <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-3" {...props}>{children}</blockquote>,
        a: ({ href, children, ...props }) => <a href={href} className="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer" {...props}>{children}</a>,
        // Enhanced table styling for analysis results
        table: ({ children, ...props }) => (
          <div className="overflow-x-auto my-4 border-2 border-gray-300 rounded-lg shadow-lg">
            <table className="w-full border-collapse bg-white" {...props}>
              {children}
            </table>
          </div>
        ),
        thead: ({ children, ...props }) => (
          <thead className="bg-gradient-to-r from-brand-600 to-brand-700 text-white" {...props}>{children}</thead>
        ),
        th: ({ children, ...props }) => (
          <th className="border border-gray-400 px-4 py-3 text-left font-bold text-white text-sm uppercase tracking-wide" {...props}>
            {children}
          </th>
        ),
        tbody: ({ children, ...props }) => (
          <tbody className="divide-y divide-gray-200" {...props}>{children}</tbody>
        ),
        tr: ({ children, ...props }) => (
          <tr className="hover:bg-gray-50 transition-colors duration-150" {...props}>{children}</tr>
        ),
        td: ({ children, ...props }) => (
          <td className="border border-gray-300 px-4 py-3 text-gray-900 font-medium" {...props}>
            {children}
          </td>
        ),
      };

      return (
        <div className={`prose prose-sm max-w-none prose-gray ${isComposableAnalyst ? 'analysis-markdown' : ''}`}>
          <ReactMarkdown components={markdownComponents}>
            {content}
          </ReactMarkdown>
        </div>
      );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`group mb-4 ${isUser ? 'ml-auto' : ''} max-w-3xl ${className}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className={`flex gap-3 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        <Avatar className="h-8 w-8 flex-shrink-0">
          {isUser ? (
            <AvatarFallback className="bg-brand-500 text-white">
              <User className="h-4 w-4" />
            </AvatarFallback>
          ) : (
            <>
              <AvatarImage src={personaAvatar} />
              <AvatarFallback className="bg-gray-200">
                {personaName ? personaName[0] : <Bot className="h-4 w-4" />}
              </AvatarFallback>
            </>
          )}
        </Avatar>

        {/* Message Content */}
        <div className={`flex-1 ${isUser ? 'text-right' : 'text-left'}`}>
          {/* Header */}
          <div className={`flex items-center gap-2 mb-1 ${isUser ? 'justify-end' : 'justify-start'}`}>
            <span className="text-sm font-medium text-gray-900">
              {isUser ? 'You' : personaName || 'AI Assistant'}
            </span>
            <span className="text-xs text-gray-500">
              {formatTimestamp(timestamp)}
            </span>
            {deliveryStatus && isUser && (
              <MessageDeliveryStatus status={deliveryStatus} />
            )}
          </div>

          {/* Message Bubble */}
          <Card className={`p-3 ${
            isUser 
              ? 'bg-brand-500 text-white border-brand-500' 
              : hasError 
                ? 'bg-red-50 border-red-200 text-red-800'
                : 'bg-gray-50 border-gray-200'
          }`}>
            {/* Attachments */}
            {attachments.length > 0 && (
              <div className="mb-3 space-y-2">
                {attachments.map((attachment) => (
                  <div
                    key={attachment.id}
                    className={`flex items-center gap-2 p-2 rounded ${
                      isUser ? 'bg-brand-400' : 'bg-white border'
                    }`}
                  >
                    {attachment.type.startsWith('image/') ? (
                      <FileText className="h-4 w-4" />
                    ) : (
                      <Database className="h-4 w-4" />
                    )}
                    <span className="text-sm truncate">{attachment.name}</span>
                    {attachment.size && (
                      <span className="text-xs opacity-75">
                        ({Math.round(attachment.size / 1024)}KB)
                      </span>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Content */}
            <div className={`${isUser ? 'text-white' : 'text-gray-800'}`}>
              {hasError ? (
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  <span>
                    {metadata?.error || 'An error occurred processing this message.'}
                  </span>
                </div>
              ) : (
                <div className="markdown-content">
                  {renderMessageContent(content)}
                  {/* Render visualization within the message */}
                  {!isStreaming && renderVisualization()}
                  {/* Streaming cursor */}
                  {isStreaming && (
                    <motion.span
                      className="inline-block w-2 h-4 bg-current ml-1"
                      animate={{ opacity: [1, 0, 1] }}
                      transition={{ duration: 1, repeat: Infinity }}
                    >
                      |
                    </motion.span>
                  )}
                </div>
              )}
            </div>

            {/* Streaming Indicator */}
            {isStreaming && (
              <div className="flex items-center gap-2 mt-2 text-xs opacity-75">
                <motion.div
                  className="w-2 h-2 bg-current rounded-full"
                  animate={{ opacity: [1, 0.3, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                />
                <span>Generating response...</span>
              </div>
            )}
          </Card>

          {/* Actions */}
          <motion.div
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: showActions ? 1 : 0, y: showActions ? 0 : -5 }}
            className={`flex items-center gap-1 mt-2 ${isUser ? 'justify-end' : 'justify-start'}`}
          >
            {/* Copy Button */}
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={handleCopy}
            >
              <Copy className="h-3 w-3 mr-1" />
              Copy
            </Button>

            {/* Feedback Buttons (AI messages only) */}
            {!isUser && !hasError && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`h-6 px-2 text-xs ${
                    feedbackGiven === 1 ? 'text-green-600 bg-green-50' : ''
                  }`}
                  onClick={() => handleFeedback(1)}
                  disabled={feedbackGiven !== null}
                >
                  <ThumbsUp className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`h-6 px-2 text-xs ${
                    feedbackGiven === 0 ? 'text-red-600 bg-red-50' : ''
                  }`}
                  onClick={() => handleFeedback(0)}
                  disabled={feedbackGiven !== null}
                >
                  <ThumbsDown className="h-3 w-3" />
                </Button>
              </>
            )}

            {/* Retry Button (Error messages only) */}
            {hasError && onRetry && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs text-red-600"
                onClick={onRetry}
              >
                Retry
              </Button>
            )}

            {/* More Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align={isUser ? 'end' : 'start'}>
                <DropdownMenuItem onClick={handleCopy}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy message
                </DropdownMenuItem>
                {!isUser && (
                  <DropdownMenuItem>
                    <FileText className="h-4 w-4 mr-2" />
                    View details
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};
