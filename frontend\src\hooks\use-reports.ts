/**
 * React Query hooks for reports functionality
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { reportsApi, ReportConfig, Report } from '@/lib/api';
import { useToast } from './use-toast';

// Query keys
export const reportKeys = {
  all: ['reports'] as const,
  list: (limit?: number, offset?: number) => [...reportKeys.all, 'list', limit, offset] as const,
  detail: (id: string) => [...reportKeys.all, 'detail', id] as const,
};

/**
 * Hook to fetch user reports
 */
export function useReports(limit: number = 20, offset: number = 0) {
  return useQuery({
    queryKey: reportKeys.list(limit, offset),
    queryFn: () => reportsApi.getReports(limit, offset),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 3,
  });
}

/**
 * Hook to fetch a specific report
 */
export function useReport(reportId: string) {
  return useQuery({
    queryKey: reportKeys.detail(reportId),
    queryFn: () => reportsApi.getReport(reportId),
    enabled: !!reportId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });
}

/**
 * Hook to generate a new report
 */
export function useGenerateReport() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (config: ReportConfig) => reportsApi.generateReport(config),
    onSuccess: (data) => {
      // Invalidate reports list to show new report
      queryClient.invalidateQueries({ queryKey: reportKeys.all });
      
      // Cache the new report
      queryClient.setQueryData(reportKeys.detail(data.report_id), data);
      
      toast({
        title: "Report Generated",
        description: `Report "${data.report_name}" has been generated successfully.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Report Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate report",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to export a report
 */
export function useExportReport() {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ reportId, format }: { reportId: string; format: string }) => 
      reportsApi.exportReport(reportId, format),
    onSuccess: (blob, variables) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `report-${variables.reportId}.${variables.format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Report Exported",
        description: `Report has been downloaded as ${variables.format.toUpperCase()}.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export report",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for report management with common operations
 */
export function useReportManager() {
  const reports = useReports();
  const generateReport = useGenerateReport();
  const exportReport = useExportReport();

  const createReport = (config: ReportConfig) => {
    generateReport.mutate(config);
  };

  const downloadReport = (reportId: string, format: string = 'pdf') => {
    exportReport.mutate({ reportId, format });
  };

  return {
    // Data
    reports: reports.data?.reports || [],
    
    // Loading states
    isLoading: reports.isLoading,
    isGenerating: generateReport.isPending,
    isExporting: exportReport.isPending,
    isError: reports.isError,
    error: reports.error,
    
    // Actions
    createReport,
    downloadReport,
    
    // Refetch
    refetch: reports.refetch,
  };
}

/**
 * Hook for report analytics and insights
 */
export function useReportAnalytics() {
  const reports = useReports();

  const analytics = React.useMemo(() => {
    const reportList = reports.data?.reports || [];
    
    const totalReports = reportList.length;
    const recentReports = reportList.filter(report => {
      const reportDate = new Date(report.generated_at);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return reportDate > weekAgo;
    }).length;

    const reportsByType = reportList.reduce((acc, report) => {
      const type = report.config_used.chart_type || 'table';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostUsedDataSources = reportList.reduce((acc, report) => {
      const sourceId = report.config_used.data_source_id;
      acc[sourceId] = (acc[sourceId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalReports,
      recentReports,
      reportsByType,
      mostUsedDataSources,
    };
  }, [reports.data]);

  return {
    analytics,
    isLoading: reports.isLoading,
    isError: reports.isError,
  };
}

/**
 * Hook for report templates and quick actions
 */
export function useReportTemplates() {
  const generateReport = useGenerateReport();

  const templates = [
    {
      id: 'sales-summary',
      name: 'Sales Summary',
      description: 'Monthly sales performance overview',
      config: {
        chart_type: 'bar' as const,
        aggregations: { sales: 'sum', revenue: 'sum' },
        group_by: ['month'],
      },
    },
    {
      id: 'customer-analysis',
      name: 'Customer Analysis',
      description: 'Customer behavior and demographics',
      config: {
        chart_type: 'pie' as const,
        group_by: ['customer_segment'],
        aggregations: { customers: 'count' },
      },
    },
    {
      id: 'revenue-trend',
      name: 'Revenue Trend',
      description: 'Revenue trends over time',
      config: {
        chart_type: 'line' as const,
        group_by: ['date'],
        aggregations: { revenue: 'sum' },
      },
    },
  ];

  const createFromTemplate = (templateId: string, dataSourceId: string, reportName?: string) => {
    const template = templates.find(t => t.id === templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    const config: ReportConfig = {
      report_name: reportName || template.name,
      data_source_id: dataSourceId,
      user_id: '', // Will be set by the API
      ...template.config,
    };

    generateReport.mutate(config);
  };

  return {
    templates,
    createFromTemplate,
    isGenerating: generateReport.isPending,
  };
}
