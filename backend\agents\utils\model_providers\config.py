"""
Configuration for the model provider system.

This module provides configuration values for the model provider system,
including API endpoints and default models.
"""

import os
import logging

# Configure logging
logger = logging.getLogger(__name__)

# API Endpoints
GROQ_ENDPOINT = os.getenv("GROQ_ENDPOINT", "https://api.groq.com/openai/v1")
OPENAI_ENDPOINT = os.getenv("OPENAI_ENDPOINT", "https://api.openai.com/v1")
# Corrected default Gemini endpoint to include API version
GEMINI_ENDPOINT = os.getenv("GEMINI_ENDPOINT", "https://generativelanguage.googleapis.com/v1beta")
OPENROUTER_ENDPOINT = os.getenv("OPENROUTER_ENDPOINT", "https://openrouter.ai/api/v1")
OLLAMA_ENDPOINT = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
REQUESTY_ENDPOINT = os.getenv("REQUESTY_ENDPOINT", "https://router.requesty.ai/v1")
ANTHROPIC_ENDPOINT = os.getenv("ANTHROPIC_ENDPOINT", "https://api.anthropic.com/v1")
COHERE_ENDPOINT = os.getenv("COHERE_ENDPOINT", "https://api.cohere.ai/v1")
MISTRAL_ENDPOINT = os.getenv("MISTRAL_ENDPOINT", "https://api.mistral.ai/v1")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "")

# API Keys
GROQ_API_KEY = os.getenv("GROQ_API_KEY", "")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "")
REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY", "")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "")
COHERE_API_KEY = os.getenv("COHERE_API_KEY", "")
MISTRAL_API_KEY = os.getenv("MISTRAL_API_KEY", "")
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "")

# Default Models
DEFAULT_GROQ_MODEL = os.getenv("DEFAULT_GROQ_MODEL", "llama3-70b-8192")
DEFAULT_OPENAI_MODEL = os.getenv("DEFAULT_OPENAI_MODEL", "gpt-3.5-turbo")
DEFAULT_GEMINI_MODEL = os.getenv("DEFAULT_GEMINI_MODEL", "gemini-pro")
DEFAULT_OPENROUTER_MODEL = os.getenv("DEFAULT_OPENROUTER_MODEL", "openai/gpt-3.5-turbo")
DEFAULT_OLLAMA_MODEL = os.getenv("DEFAULT_OLLAMA_MODEL", "llama3")
DEFAULT_REQUESTY_MODEL = os.getenv("DEFAULT_REQUESTY_MODEL", "openai/gpt-3.5-turbo")
DEFAULT_ANTHROPIC_MODEL = os.getenv("DEFAULT_ANTHROPIC_MODEL", "claude-3-sonnet-20240229")
DEFAULT_COHERE_MODEL = os.getenv("DEFAULT_COHERE_MODEL", "command")
DEFAULT_MISTRAL_MODEL = os.getenv("DEFAULT_MISTRAL_MODEL", "mistral-medium")
DEFAULT_AZURE_OPENAI_MODEL = os.getenv("DEFAULT_AZURE_OPENAI_MODEL", "gpt-35-turbo")

# Provider Configuration
PROVIDER_CONFIG = {
    "groq": {
        "endpoint": GROQ_ENDPOINT,
        "api_key": GROQ_API_KEY,
        "default_model": DEFAULT_GROQ_MODEL,
        "display_name": "Groq"
    },
    "openai": {
        "endpoint": OPENAI_ENDPOINT,
        "api_key": OPENAI_API_KEY,
        "default_model": DEFAULT_OPENAI_MODEL,
        "display_name": "OpenAI"
    },
    "gemini": {
        "endpoint": GEMINI_ENDPOINT,
        "api_key": GEMINI_API_KEY,
        "default_model": DEFAULT_GEMINI_MODEL,
        "display_name": "Google Gemini"
    },
    "openrouter": {
        "endpoint": OPENROUTER_ENDPOINT,
        "api_key": OPENROUTER_API_KEY,
        "default_model": DEFAULT_OPENROUTER_MODEL,
        "display_name": "OpenRouter"
    },
    "ollama": {
        "endpoint": OLLAMA_ENDPOINT,
        "api_key": "",  # Ollama doesn't use API keys
        "default_model": DEFAULT_OLLAMA_MODEL,
        "display_name": "Ollama"
    },
    "requesty": {
        "endpoint": REQUESTY_ENDPOINT,
        "api_key": REQUESTY_API_KEY,
        "default_model": DEFAULT_REQUESTY_MODEL,
        "display_name": "Requesty"
    },
    "anthropic": {
        "endpoint": ANTHROPIC_ENDPOINT,
        "api_key": ANTHROPIC_API_KEY,
        "default_model": DEFAULT_ANTHROPIC_MODEL,
        "display_name": "Anthropic"
    },
    "cohere": {
        "endpoint": COHERE_ENDPOINT,
        "api_key": COHERE_API_KEY,
        "default_model": DEFAULT_COHERE_MODEL,
        "display_name": "Cohere"
    },
    "mistral": {
        "endpoint": MISTRAL_ENDPOINT,
        "api_key": MISTRAL_API_KEY,
        "default_model": DEFAULT_MISTRAL_MODEL,
        "display_name": "Mistral AI"
    },
    "azure": {
        "endpoint": AZURE_OPENAI_ENDPOINT,
        "api_key": AZURE_OPENAI_API_KEY,
        "default_model": DEFAULT_AZURE_OPENAI_MODEL,
        "display_name": "Azure OpenAI"
    }
}

# Log configuration
logger.info("Model provider configuration loaded")
for provider_id, config in PROVIDER_CONFIG.items():
    has_api_key = bool(config["api_key"]) if provider_id != "ollama" else True
    logger.info(f"Provider '{provider_id}': endpoint={config['endpoint']}, has_api_key={has_api_key}")


def get_provider_config(provider_id: str) -> dict:
    """
    Get configuration for a provider.
    
    Args:
        provider_id: Provider ID
        
    Returns:
        Provider configuration dictionary
    """
    provider_id = provider_id.lower()
    return PROVIDER_CONFIG.get(provider_id, {})
