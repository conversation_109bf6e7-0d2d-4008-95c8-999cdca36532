"""
Anthropic model provider for the Datagenius backend.

This module provides a model provider implementation for Anthropic.
"""

import logging
import requests
from typing import Dict, Any, List, Union, Optional
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.language_models.chat_models import BaseChatModel


from .base import BaseModelProvider
from .exceptions import ModelInitializationError, ModelNotFoundError
from .config import get_provider_config

# Configure logging
logger = logging.getLogger(__name__)


class AnthropicProvider(BaseModelProvider):
    """Model provider implementation for Anthropic."""

    @property
    def provider_id(self) -> str:
        """Get the provider ID."""
        return "anthropic"

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        return "Anthropic"

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the provider with configuration.

        Args:
            config: Configuration dictionary for the provider
        """
        await super().initialize(config)

        # Get provider configuration
        provider_config = get_provider_config("anthropic")

        # Set default model if not specified
        if not self._default_model_id:
            self._default_model_id = provider_config.get("default_model", "claude-3-sonnet-20240229")

        # Set default endpoint if not specified
        if not self._endpoint:
            self._endpoint = provider_config.get("endpoint", "https://api.anthropic.com/v1")

        # Set API key from configuration if not specified
        if not self._api_key:
            self._api_key = provider_config.get("api_key", "")

        # Check if we have an API key
        if not self._api_key:
            logger.warning("No Anthropic API key provided")

    async def _initialize_model(self, model_id: str, config: Dict[str, Any]) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Initialize a model instance.

        Args:
            model_id: ID of the model to initialize
            config: Configuration for the model

        Returns:
            Initialized model instance

        Raises:
            ModelInitializationError: If there's an error initializing the model
            ModelNotFoundError: If the model is not found
        """
        try:
            # Check if the model exists
            models = await self.list_models()
            model_exists = any(model["id"] == model_id for model in models)

            if not model_exists:
                # If the model doesn't exist but is a known model, we'll try anyway
                known_models = [
                    "claude-3-opus-20240229",
                    "claude-3-sonnet-20240229",
                    "claude-3-haiku-20240307",
                    "claude-2.1",
                    "claude-2.0"
                ]
                if model_id not in known_models:
                    raise ModelNotFoundError(f"Model '{model_id}' not found in Anthropic")

            # Import here to avoid hard dependencies
            try:
                from langchain_anthropic import ChatAnthropic
            except ImportError:
                logger.warning("langchain-anthropic not installed, attempting to install...")
                import subprocess
                subprocess.check_call(["pip", "install", "langchain-anthropic"])
                from langchain_anthropic import ChatAnthropic

            # Initialize the model
            model = ChatAnthropic(
                temperature=config.get("temperature", 0.7),
                model_name=model_id,
                anthropic_api_key=self._api_key
            )

            logger.info(f"Initialized Anthropic model '{model_id}'")
            return model

        except ImportError as e:
            raise ModelInitializationError(f"Error importing Anthropic: {str(e)}")
        except Exception as e:
            raise ModelInitializationError(f"Error initializing Anthropic model '{model_id}': {str(e)}")

    async def _fetch_models(self) -> List[Dict[str, Any]]:
        """
        Fetch available models from Anthropic.

        Returns:
            List of model metadata dictionaries
        """
        # Anthropic doesn't have a models endpoint, so we return a static list
        return [
            {
                "id": "claude-3-opus-20240229",
                "name": "Claude 3 Opus",
                "description": "Anthropic's most powerful model",
                "context_length": 200000,
                "provider": "anthropic"
            },
            {
                "id": "claude-3-sonnet-20240229",
                "name": "Claude 3 Sonnet",
                "description": "Anthropic's balanced model",
                "context_length": 200000,
                "provider": "anthropic"
            },
            {
                "id": "claude-3-haiku-20240307",
                "name": "Claude 3 Haiku",
                "description": "Anthropic's fastest model",
                "context_length": 200000,
                "provider": "anthropic"
            },
            {
                "id": "claude-2.1",
                "name": "Claude 2.1",
                "description": "Anthropic's previous generation model",
                "context_length": 100000,
                "provider": "anthropic"
            }
        ]

    async def is_available(self) -> bool:
        """
        Check if the Anthropic API is available.

        Returns:
            True if the API is available, False otherwise
        """
        if not self._api_key:
            return False

        try:
            # Make a simple request to check if the API key is valid
            headers = {
                "x-api-key": self._api_key,
                "anthropic-version": "2023-06-01"
            }

            # Use a minimal prompt to check if the API is working
            data = {
                "model": self._default_model_id,
                "max_tokens": 10,
                "messages": [{"role": "user", "content": "Hello"}]
            }

            response = requests.post(
                f"{self._endpoint}/messages",
                headers=headers,
                json=data,
                timeout=5
            )

            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Error checking Anthropic API availability: {str(e)}")
            return False
