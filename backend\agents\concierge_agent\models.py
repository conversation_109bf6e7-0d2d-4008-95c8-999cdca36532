"""
Pydantic models for the Concierge Agent LLM-based analysis system.

This module provides structured validation for LLM responses and intent analysis.
"""

from typing import List, Optional, Dict, Any, Literal, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
import logging

logger = logging.getLogger(__name__)


class ConversationStage(str, Enum):
    """Enumeration of conversation stages."""
    INITIAL = "initial"
    PERSONA_SELECTION = "persona_selection"
    DATA_ATTACHMENT = "data_attachment"
    TASK_EXECUTION = "task_execution"
    HANDOFF = "handoff"
    FOLLOW_UP = "follow_up"
    COMPLETED = "completed"


class PersonaAvailability(str, Enum):
    """Enumeration of persona availability states."""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    PURCHASED = "purchased"
    NOT_PURCHASED = "not_purchased"
    REQUIRES_SETUP = "requires_setup"


class EntityExtraction(BaseModel):
    """Model for extracted entities from user messages."""
    model_config = ConfigDict(extra="allow")  # Allow additional entities to be extracted

    file_type: Optional[str] = Field(None, description="Detected file type (csv, excel, pdf, etc.)")
    industry: Optional[str] = Field(None, description="Detected industry context")
    task_type: Optional[str] = Field(None, description="Detected task type (analysis, visualization, etc.)")
    urgency: Optional[str] = Field(None, description="Detected urgency level (low, medium, high)")
    data_size: Optional[str] = Field(None, description="Estimated data size (small, medium, large)")


class LLMAnalysisResponse(BaseModel):
    """
    Pydantic model for validating LLM analysis responses.

    This ensures the LLM returns structured, validated data that can be safely used
    throughout the system.
    """
    intent_type: Literal[
        "persona_request",
        "persona_query",
        "general_question"
    ] = Field(..., description="The primary intent detected from the user's message")

    confidence: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Confidence score for the intent detection (0.0 to 1.0)"
    )

    entities: EntityExtraction = Field(
        default_factory=EntityExtraction,
        description="Extracted entities from the user's message"
    )

    suggested_personas: List[str] = Field(
        default_factory=list,
        description="List of recommended persona IDs for this request"
    )

    requires_data: bool = Field(
        False,
        description="Whether this request requires data attachment"
    )

    complexity_score: float = Field(
        0.5,
        ge=0.0,
        le=1.0,
        description="Estimated complexity of the task (0.0 to 1.0)"
    )

    reasoning: str = Field(
        ...,
        min_length=10,
        description="Explanation of the analysis and recommendations"
    )

    follow_up_questions: Optional[List[str]] = Field(
        None,
        description="Suggested follow-up questions to clarify the request"
    )

    estimated_time: Optional[str] = Field(
        None,
        description="Estimated time to complete the task"
    )

    @field_validator('suggested_personas')
    @classmethod
    def validate_personas(cls, v):
        """Validate that suggested personas are valid IDs."""
        valid_personas = [
            "concierge-agent",
            "composable-analyst",
            "composable-marketer",
            "composable-classifier",
            "data-assistant"
        ]

        for persona in v:
            if persona not in valid_personas:
                logger.warning(f"Unknown persona suggested: {persona}")

        return v

    @field_validator('confidence')
    @classmethod
    def validate_confidence(cls, v):
        """Ensure confidence is reasonable."""
        if v < 0.1:
            logger.warning(f"Very low confidence score: {v}")
        return v

    @model_validator(mode='after')
    def validate_consistency(self):
        """Validate consistency between fields."""
        intent_type = self.intent_type
        suggested_personas = self.suggested_personas
        requires_data = self.requires_data

        # Validate intent-persona consistency
        intent_persona_mapping = {
            "analysis_request": ["composable-analyst", "data-assistant"],
            "marketing_request": ["composable-marketer"],
            "classification_request": ["composable-classifier"],
            "data_help": ["composable-analyst", "data-assistant"],
        }

        if intent_type in intent_persona_mapping:
            expected_personas = intent_persona_mapping[intent_type]
            if suggested_personas and not any(p in expected_personas for p in suggested_personas):
                logger.warning(f"Persona suggestions {suggested_personas} don't match intent {intent_type}")

        # Validate data requirement consistency
        data_related_intents = ["analysis_request", "data_help"]
        if intent_type in data_related_intents and not requires_data:
            logger.info(f"Intent {intent_type} typically requires data, but requires_data is False")

        return self


class UserIntent(BaseModel):
    """
    Pydantic model for user intent (replaces the dataclass).

    This provides validation and serialization for user intent data.
    """
    model_config = ConfigDict(extra="allow")  # Allow additional fields

    intent_type: str = Field(..., description="The detected intent type")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    entities: Dict[str, Any] = Field(default_factory=dict, description="Extracted entities")
    suggested_personas: List[str] = Field(default_factory=list, description="Suggested personas")
    requires_data: bool = Field(False, description="Whether data is required")
    complexity_score: float = Field(0.5, ge=0.0, le=1.0, description="Task complexity")
    reasoning: Optional[str] = Field(None, description="Analysis reasoning")
    timestamp: datetime = Field(default_factory=datetime.now, description="When the intent was parsed")


class ConversationContext(BaseModel):
    """
    Pydantic model for conversation context (replaces the dataclass).

    This provides validation and serialization for conversation state.
    """
    model_config = ConfigDict(extra="allow")  # Allow additional context fields

    user_id: str = Field(..., description="User identifier")
    session_id: str = Field(..., description="Session identifier")
    conversation_history: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="History of conversation messages"
    )
    user_preferences: Dict[str, Any] = Field(
        default_factory=dict,
        description="User preferences and settings"
    )
    current_task: Optional[str] = Field(None, description="Current active task")
    attached_data: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Data files attached to the conversation"
    )
    last_interaction: datetime = Field(
        default_factory=datetime.now,
        description="Timestamp of last interaction"
    )

    @field_validator('user_id', 'session_id')
    @classmethod
    def convert_ids_to_string(cls, v):
        """Convert user_id and session_id to strings for consistency."""
        return str(v) if v is not None else v


class LLMAnalysisConfig(BaseModel):
    """Configuration for LLM-based analysis."""
    model_config = ConfigDict(extra="forbid")  # Don't allow extra fields for config

    provider: str = Field("groq", description="LLM provider to use")
    model: str = Field("llama-3.1-8b-instant", description="Model to use for analysis")
    temperature: float = Field(0.1, ge=0.0, le=2.0, description="Temperature for LLM generation")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens for response")
    timeout: int = Field(30, description="Timeout in seconds for LLM calls")
    retry_attempts: int = Field(3, description="Number of retry attempts on failure")


class PersonaRecommendation(BaseModel):
    """Model for persona recommendations."""
    persona_id: str = Field(..., description="ID of the recommended persona")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in the recommendation")
    reasoning: str = Field(..., description="Why this persona is recommended")
    availability: PersonaAvailability = Field(..., description="Availability status of the persona")
    estimated_task_time: Optional[str] = Field(None, description="Estimated time to complete the task")
    required_data: List[str] = Field(default_factory=list, description="Types of data this persona needs")
    capabilities: List[str] = Field(default_factory=list, description="Key capabilities of this persona")
    priority: int = Field(default=1, ge=1, le=5, description="Priority ranking (1=highest, 5=lowest)")


class ConversationState(BaseModel):
    """Model for tracking conversation state."""
    conversation_id: str = Field(..., description="Unique conversation identifier")
    user_id: str = Field(..., description="User identifier")
    stage: ConversationStage = Field(default=ConversationStage.INITIAL, description="Current conversation stage")
    selected_persona: Optional[str] = Field(None, description="Currently selected persona")
    recommended_personas: List[PersonaRecommendation] = Field(default_factory=list, description="Recommended personas")
    attached_files: List[Dict[str, Any]] = Field(default_factory=list, description="Files attached to conversation")
    context_summary: str = Field(default="", description="Summary of conversation context")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PersonaRoutingRequest(BaseModel):
    """Model for persona routing requests."""
    source_persona: str = Field(..., description="Current persona")
    target_persona: str = Field(..., description="Target persona to route to")
    user_message: str = Field(..., description="User's message that triggered routing")
    routing_reason: str = Field(..., description="Reason for routing")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional routing context")
    priority: int = Field(default=1, ge=1, le=5, description="Routing priority")


class PersonaRoutingResponse(BaseModel):
    """Model for persona routing responses."""
    success: bool = Field(..., description="Whether routing was successful")
    target_persona: str = Field(..., description="Target persona")
    handoff_message: Optional[str] = Field(None, description="Message for the handoff")
    error_message: Optional[str] = Field(None, description="Error message if routing failed")
    alternative_personas: List[str] = Field(default_factory=list, description="Alternative personas if target unavailable")
    routing_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional routing metadata")


class ConciergeResponse(BaseModel):
    """Model for standardized concierge responses."""
    message: str = Field(..., description="Response message to the user")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Response metadata")
    success: bool = Field(default=True, description="Whether the operation was successful")
    recommendations: List[PersonaRecommendation] = Field(default_factory=list, description="Persona recommendations")
    suggested_actions: List[str] = Field(default_factory=list, description="Suggested next actions")
    requires_user_input: bool = Field(default=False, description="Whether user input is required")
    conversation_stage: Optional[ConversationStage] = Field(None, description="Updated conversation stage")


class ValidationResult(BaseModel):
    """Model for validation results."""
    is_valid: bool = Field(..., description="Whether validation passed")
    errors: List[str] = Field(default_factory=list, description="Validation errors")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Confidence in validation")
    suggestions: List[str] = Field(default_factory=list, description="Suggestions for improvement")
