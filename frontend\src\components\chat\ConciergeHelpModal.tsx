import React from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { HelpCircle, Compass, MessageSquare, FileUp, UserCircle, ArrowRight, CheckCircle2 } from 'lucide-react';

interface ConciergeHelpModalProps {
  trigger?: React.ReactNode;
  className?: string;
}

export const ConciergeHelpModal: React.FC<ConciergeHelpModalProps> = ({
  trigger,
  className = '',
}) => {
  const defaultTrigger = (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
      >
        <HelpCircle className="h-5 w-5" />
      </Button>
    </motion.div>
  );

  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Compass className="h-5 w-5 text-brand-500" />
            Concierge Workflow Guide
          </DialogTitle>
          <DialogDescription>
            Learn how the Datagenius Concierge helps you get the most out of your data
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="space-y-4">
            {/* Step 1 */}
            <motion.div 
              className="flex items-start gap-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 flex items-center justify-center text-brand-600">
                <MessageSquare className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-medium text-lg text-gray-900">1. Welcome & Needs Assessment</h3>
                <p className="text-gray-600 mt-1">
                  The concierge welcomes you and asks about your goals. Be specific about what you're trying to accomplish with your data.
                </p>
              </div>
            </motion.div>
            
            {/* Step 2 */}
            <motion.div 
              className="flex items-start gap-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 flex items-center justify-center text-brand-600">
                <Compass className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-medium text-lg text-gray-900">2. Persona Recommendations</h3>
                <p className="text-gray-600 mt-1">
                  Based on your needs, the concierge recommends specialized AI personas that can help you. Each persona has unique capabilities.
                </p>
              </div>
            </motion.div>
            
            {/* Step 3 */}
            <motion.div 
              className="flex items-start gap-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 flex items-center justify-center text-brand-600">
                <FileUp className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-medium text-lg text-gray-900">3. Data Guidance</h3>
                <p className="text-gray-600 mt-1">
                  The concierge helps you attach and prepare your data files. Different file types (CSV, Excel, PDF) are handled appropriately.
                </p>
              </div>
            </motion.div>
            
            {/* Step 4 */}
            <motion.div 
              className="flex items-start gap-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 flex items-center justify-center text-brand-600">
                <UserCircle className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-medium text-lg text-gray-900">4. Persona Selection & Handoff</h3>
                <p className="text-gray-600 mt-1">
                  Select a specialized persona and the concierge will transfer your conversation and data to them. You can always return to the concierge.
                </p>
              </div>
            </motion.div>
            
            {/* Step 5 */}
            <motion.div 
              className="flex items-start gap-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 flex items-center justify-center text-brand-600">
                <CheckCircle2 className="h-5 w-5" />
              </div>
              <div>
                <h3 className="font-medium text-lg text-gray-900">5. Follow-up & Completion</h3>
                <p className="text-gray-600 mt-1">
                  After working with a specialized persona, the concierge can help you with additional tasks or guide you to other personas.
                </p>
              </div>
            </motion.div>
          </div>
          
          <div className="bg-blue-50 p-4 rounded-md mt-6">
            <div className="flex items-start gap-3">
              <HelpCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-700">Pro Tip</h4>
                <p className="text-blue-600 text-sm mt-1">
                  You can always see your current stage in the workflow at the top of the chat. Use the "Return to Concierge" button when working with a specialized persona to go back to the concierge.
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button type="button" variant="outline" className="w-full">
            Got it
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
