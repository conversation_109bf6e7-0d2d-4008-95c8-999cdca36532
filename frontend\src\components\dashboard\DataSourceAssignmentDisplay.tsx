import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Database, 
  FileText, 
  Globe, 
  Server,
  Plus,
  MoreVertical,
  Edit,
  Trash2,
  RefreshCw,
  AlertCircle,
  CheckCircle2,
  Clock
} from 'lucide-react';
import { DashboardDataSource } from '@/types/dashboard-customization';
import { useToast } from '@/hooks/use-toast';

interface DataSourceAssignmentDisplayProps {
  dataSources: DashboardDataSource[];
  onAddDataSources: () => void;
  onEditDataSource?: (dataSource: DashboardDataSource) => void;
  onRemoveDataSource?: (dataSourceId: string) => void;
  onRefreshDataSource?: (dataSourceId: string) => void;
  isLoading?: boolean;
  className?: string;
}

const DATA_SOURCE_ICONS = {
  file: FileText,
  database: Database,
  api: Globe,
  mcp: Server,
  platform: Database,
};

const DATA_SOURCE_TYPE_LABELS = {
  file: 'File',
  database: 'Database',
  api: 'API',
  mcp: 'MCP Server',
  platform: 'Platform Data',
};

export const DataSourceAssignmentDisplay: React.FC<DataSourceAssignmentDisplayProps> = ({
  dataSources,
  onAddDataSources,
  onEditDataSource,
  onRemoveDataSource,
  onRefreshDataSource,
  isLoading = false,
  className = '',
}) => {
  const { toast } = useToast();
  const [refreshingDataSources, setRefreshingDataSources] = useState<Set<string>>(new Set());

  const formatDataSourceType = (type: string) => {
    return DATA_SOURCE_TYPE_LABELS[type as keyof typeof DATA_SOURCE_TYPE_LABELS] || type;
  };

  const getDataSourceIcon = (type: string) => {
    return DATA_SOURCE_ICONS[type as keyof typeof DATA_SOURCE_ICONS] || Database;
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (isActive: boolean) => {
    return isActive ? CheckCircle2 : AlertCircle;
  };

  const handleRefreshDataSource = async (dataSourceId: string) => {
    if (!onRefreshDataSource) return;

    setRefreshingDataSources(prev => new Set(prev).add(dataSourceId));
    
    try {
      await onRefreshDataSource(dataSourceId);
      toast({
        title: "Data Source Refreshed",
        description: "Data source has been refreshed successfully.",
      });
    } catch (error) {
      console.error('Error refreshing data source:', error);
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh data source. Please try again.",
        variant: "destructive",
      });
    } finally {
      setRefreshingDataSources(prev => {
        const newSet = new Set(prev);
        newSet.delete(dataSourceId);
        return newSet;
      });
    }
  };

  const handleRemoveDataSource = (dataSourceId: string) => {
    if (!onRemoveDataSource) return;

    // Show confirmation before removing
    if (window.confirm('Are you sure you want to remove this data source from the dashboard?')) {
      onRemoveDataSource(dataSourceId);
      toast({
        title: "Data Source Removed",
        description: "Data source has been removed from the dashboard.",
      });
    }
  };

  const formatLastUpdated = (lastUpdated: string | null | undefined) => {
    if (!lastUpdated) return 'Never';
    
    const date = new Date(lastUpdated);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          <span>Loading data sources...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Data Sources</h3>
          <p className="text-sm text-muted-foreground">
            {dataSources.length} data source{dataSources.length !== 1 ? 's' : ''} assigned
          </p>
        </div>
        <Button onClick={onAddDataSources} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Data Sources
        </Button>
      </div>

      {dataSources.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Database className="h-12 w-12 text-muted-foreground mb-4" />
            <h4 className="text-lg font-semibold mb-2">No Data Sources</h4>
            <p className="text-sm text-muted-foreground text-center mb-4">
              Add data sources to power your dashboard widgets with real data.
            </p>
            <Button onClick={onAddDataSources} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Data Source
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {dataSources.map((dataSource) => {
            const Icon = getDataSourceIcon(dataSource.type);
            const StatusIcon = getStatusIcon(dataSource.isActive);
            const isRefreshing = refreshingDataSources.has(dataSource.id);

            return (
              <Card key={dataSource.id} className="relative">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <Icon className="h-5 w-5 text-muted-foreground" />
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-sm truncate">{dataSource.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {formatDataSourceType(dataSource.type)}
                          </Badge>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <StatusIcon 
                                  className={`h-3 w-3 ${getStatusColor(dataSource.isActive)}`} 
                                />
                              </TooltipTrigger>
                              <TooltipContent>
                                {dataSource.isActive ? 'Active' : 'Inactive'}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </div>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onEditDataSource && (
                          <DropdownMenuItem onClick={() => onEditDataSource(dataSource)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        {onRefreshDataSource && (
                          <DropdownMenuItem 
                            onClick={() => handleRefreshDataSource(dataSource.id)}
                            disabled={isRefreshing}
                          >
                            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                            Refresh
                          </DropdownMenuItem>
                        )}
                        {onRemoveDataSource && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleRemoveDataSource(dataSource.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Remove
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  {dataSource.description && (
                    <CardDescription className="text-xs mb-3 line-clamp-2">
                      {dataSource.description}
                    </CardDescription>
                  )}
                  
                  <div className="space-y-2 text-xs text-muted-foreground">
                    <div className="flex items-center justify-between">
                      <span>Last Updated:</span>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{formatLastUpdated(dataSource.lastUpdated)}</span>
                      </div>
                    </div>
                    
                    {dataSource.recordCount !== null && dataSource.recordCount !== undefined && (
                      <div className="flex items-center justify-between">
                        <span>Records:</span>
                        <span>{dataSource.recordCount.toLocaleString()}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <span>Refresh Rate:</span>
                      <span>{Math.floor(dataSource.refreshInterval / 60)}m</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};
