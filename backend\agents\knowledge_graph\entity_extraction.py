"""
Entity extraction for the knowledge graph.

This module provides functions for extracting entities and relationships from text.
"""

import logging
import uuid
import re
from typing import Dict, Any, List, Optional, Tuple, Set
import spacy
from langchain_huggingface import HuggingFaceEmbeddings

from .schema import Entity, Relationship, EntityType, RelationshipType, Property

logger = logging.getLogger(__name__)

# Load spaCy model
try:
    nlp = spacy.load("en_core_web_sm")
    logger.info("Loaded spaCy model: en_core_web_sm")
except Exception as e:
    logger.warning(f"Could not load spaCy model: {str(e)}")
    nlp = None


class EntityExtractor:
    """Extract entities and relationships from text."""

    def __init__(self, use_embeddings: bool = True, embeddings_model: str = "all-MiniLM-L6-v2"):
        """
        Initialize the entity extractor.

        Args:
            use_embeddings: Whether to generate embeddings for entities.
            embeddings_model: Name of the embeddings model to use.
        """
        self.use_embeddings = use_embeddings
        self.embeddings_model = embeddings_model
        
        # Initialize embeddings model if requested
        if use_embeddings:
            try:
                self.embeddings = HuggingFaceEmbeddings(model_name=embeddings_model)
                logger.info(f"Initialized embeddings model: {embeddings_model}")
            except Exception as e:
                logger.warning(f"Could not initialize embeddings model: {str(e)}")
                self.use_embeddings = False
                self.embeddings = None

    async def extract_entities(self, text: str, source: Optional[str] = None) -> List[Entity]:
        """
        Extract entities from text.

        Args:
            text: Text to extract entities from.
            source: Source of the text (e.g., document ID).

        Returns:
            List of extracted entities.
        """
        if not nlp:
            logger.error("spaCy model not loaded, cannot extract entities")
            return []
            
        # Process the text with spaCy
        doc = nlp(text)
        
        # Extract named entities
        entities = []
        seen_texts = set()
        
        for ent in doc.ents:
            # Skip duplicates
            if ent.text.lower() in seen_texts:
                continue
                
            seen_texts.add(ent.text.lower())
            
            # Map spaCy entity type to our entity type
            entity_type = self._map_spacy_entity_type(ent.label_)
            
            # Create entity
            entity_id = str(uuid.uuid4())
            entity = Entity(
                id=entity_id,
                type=entity_type,
                name=ent.text,
                description=f"Extracted from text: '{ent.sent.text}'",
                properties=[
                    Property(name="spacy_label", value=ent.label_, data_type="string"),
                    Property(name="start_char", value=ent.start_char, data_type="integer"),
                    Property(name="end_char", value=ent.end_char, data_type="integer")
                ],
                source=source,
                confidence=0.8  # Default confidence for spaCy entities
            )
            
            # Generate embedding if requested
            if self.use_embeddings and self.embeddings:
                try:
                    embedding = self.embeddings.embed_query(ent.text)
                    entity.embedding = embedding
                except Exception as e:
                    logger.warning(f"Could not generate embedding for entity '{ent.text}': {str(e)}")
            
            entities.append(entity)
        
        # Extract noun chunks as concept entities
        for chunk in doc.noun_chunks:
            # Skip duplicates and single-word chunks that are already entities
            if chunk.text.lower() in seen_texts or len(chunk.text.split()) <= 1:
                continue
                
            seen_texts.add(chunk.text.lower())
            
            # Create entity
            entity_id = str(uuid.uuid4())
            entity = Entity(
                id=entity_id,
                type=EntityType.CONCEPT,
                name=chunk.text,
                description=f"Noun phrase extracted from text: '{chunk.sent.text}'",
                properties=[
                    Property(name="is_noun_chunk", value=True, data_type="boolean"),
                    Property(name="start_char", value=chunk.start_char, data_type="integer"),
                    Property(name="end_char", value=chunk.end_char, data_type="integer")
                ],
                source=source,
                confidence=0.6  # Lower confidence for noun chunks
            )
            
            # Generate embedding if requested
            if self.use_embeddings and self.embeddings:
                try:
                    embedding = self.embeddings.embed_query(chunk.text)
                    entity.embedding = embedding
                except Exception as e:
                    logger.warning(f"Could not generate embedding for entity '{chunk.text}': {str(e)}")
            
            entities.append(entity)
        
        logger.info(f"Extracted {len(entities)} entities from text")
        return entities

    async def extract_relationships(
        self, text: str, entities: List[Entity]
    ) -> List[Relationship]:
        """
        Extract relationships between entities from text.

        Args:
            text: Text to extract relationships from.
            entities: Entities to find relationships between.

        Returns:
            List of extracted relationships.
        """
        if not nlp:
            logger.error("spaCy model not loaded, cannot extract relationships")
            return []
            
        # Process the text with spaCy
        doc = nlp(text)
        
        # Create a mapping from entity name to entity object
        entity_map = {entity.name.lower(): entity for entity in entities}
        
        # Extract relationships
        relationships = []
        seen_relationships = set()
        
        # Extract subject-verb-object relationships
        for sent in doc.sents:
            for token in sent:
                # Look for verbs
                if token.pos_ == "VERB":
                    # Find the subject
                    subjects = [subj for subj in token.children if subj.dep_ in ["nsubj", "nsubjpass"]]
                    
                    # Find the object
                    objects = [obj for obj in token.children if obj.dep_ in ["dobj", "pobj", "attr"]]
                    
                    # Create relationships between subject and object
                    for subj in subjects:
                        for obj in objects:
                            # Get the full subject and object phrases
                            subj_phrase = self._get_phrase(subj)
                            obj_phrase = self._get_phrase(obj)
                            
                            # Check if both phrases are entities
                            subj_entity = entity_map.get(subj_phrase.lower())
                            obj_entity = entity_map.get(obj_phrase.lower())
                            
                            if subj_entity and obj_entity:
                                # Create a relationship key to avoid duplicates
                                rel_key = (subj_entity.id, token.lemma_, obj_entity.id)
                                
                                if rel_key in seen_relationships:
                                    continue
                                    
                                seen_relationships.add(rel_key)
                                
                                # Create relationship
                                relationship_id = str(uuid.uuid4())
                                relationship = Relationship(
                                    id=relationship_id,
                                    type=RelationshipType.CUSTOM,
                                    source_id=subj_entity.id,
                                    target_id=obj_entity.id,
                                    properties=[
                                        Property(name="verb", value=token.lemma_, data_type="string"),
                                        Property(name="sentence", value=sent.text, data_type="string")
                                    ],
                                    confidence=0.7  # Default confidence for extracted relationships
                                )
                                
                                relationships.append(relationship)
        
        # Extract co-occurrence relationships
        for i, entity1 in enumerate(entities):
            for entity2 in entities[i+1:]:
                # Skip if already have a relationship
                rel_key1 = (entity1.id, "co_occurs_with", entity2.id)
                rel_key2 = (entity2.id, "co_occurs_with", entity1.id)
                
                if rel_key1 in seen_relationships or rel_key2 in seen_relationships:
                    continue
                
                # Check if entities co-occur in the same sentence
                entity1_spans = self._find_entity_spans(text, entity1.name)
                entity2_spans = self._find_entity_spans(text, entity2.name)
                
                if self._check_co_occurrence(doc, entity1_spans, entity2_spans):
                    seen_relationships.add(rel_key1)
                    
                    # Create relationship
                    relationship_id = str(uuid.uuid4())
                    relationship = Relationship(
                        id=relationship_id,
                        type=RelationshipType.RELATED_TO,
                        source_id=entity1.id,
                        target_id=entity2.id,
                        properties=[
                            Property(name="relation_type", value="co_occurrence", data_type="string")
                        ],
                        confidence=0.5  # Lower confidence for co-occurrence relationships
                    )
                    
                    relationships.append(relationship)
        
        logger.info(f"Extracted {len(relationships)} relationships from text")
        return relationships

    def _map_spacy_entity_type(self, spacy_type: str) -> EntityType:
        """
        Map spaCy entity type to our entity type.

        Args:
            spacy_type: spaCy entity type.

        Returns:
            Corresponding EntityType.
        """
        mapping = {
            "PERSON": EntityType.PERSON,
            "ORG": EntityType.ORGANIZATION,
            "GPE": EntityType.LOCATION,
            "LOC": EntityType.LOCATION,
            "PRODUCT": EntityType.PRODUCT,
            "EVENT": EntityType.EVENT,
            "WORK_OF_ART": EntityType.CONCEPT,
            "FAC": EntityType.LOCATION,
            "NORP": EntityType.CONCEPT
        }
        
        return mapping.get(spacy_type, EntityType.CONCEPT)

    def _get_phrase(self, token) -> str:
        """
        Get the full phrase for a token, including its children.

        Args:
            token: spaCy token.

        Returns:
            Full phrase.
        """
        # Get all tokens in the subtree
        subtree = list(token.subtree)
        
        # Sort by position in the document
        subtree.sort(key=lambda t: t.i)
        
        # Join the tokens
        return " ".join([t.text for t in subtree])

    def _find_entity_spans(self, text: str, entity_name: str) -> List[Tuple[int, int]]:
        """
        Find all occurrences of an entity in the text.

        Args:
            text: Text to search in.
            entity_name: Entity name to find.

        Returns:
            List of (start, end) character positions.
        """
        spans = []
        start = 0
        
        while True:
            start = text.lower().find(entity_name.lower(), start)
            if start == -1:
                break
            spans.append((start, start + len(entity_name)))
            start += 1
            
        return spans

    def _check_co_occurrence(
        self, doc, entity1_spans: List[Tuple[int, int]], entity2_spans: List[Tuple[int, int]]
    ) -> bool:
        """
        Check if two entities co-occur in the same sentence.

        Args:
            doc: spaCy document.
            entity1_spans: Character spans of the first entity.
            entity2_spans: Character spans of the second entity.

        Returns:
            True if the entities co-occur, False otherwise.
        """
        # Get sentence spans
        sentence_spans = [(sent.start_char, sent.end_char) for sent in doc.sents]
        
        # Check if any pair of entity spans are in the same sentence
        for span1 in entity1_spans:
            for span2 in entity2_spans:
                for sent_start, sent_end in sentence_spans:
                    if (
                        span1[0] >= sent_start and span1[1] <= sent_end and
                        span2[0] >= sent_start and span2[1] <= sent_end
                    ):
                        return True
                        
        return False
