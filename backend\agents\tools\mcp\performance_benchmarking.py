"""
Performance Benchmarking System for MCP Tools.

This module provides comprehensive performance benchmarking with automated
performance tracking, bottleneck identification, load testing, and
performance regression detection.
"""

import logging
import asyncio
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import json
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
import tracemalloc

logger = logging.getLogger(__name__)


class BenchmarkType(Enum):
    """Types of benchmarks."""
    PERFORMANCE = "performance"
    LOAD_TEST = "load_test"
    STRESS_TEST = "stress_test"
    MEMORY_USAGE = "memory_usage"
    CONCURRENCY = "concurrency"
    SCALABILITY = "scalability"


class MetricType(Enum):
    """Performance metric types."""
    EXECUTION_TIME = "execution_time"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"
    THROUGHPUT = "throughput"
    LATENCY = "latency"
    ERROR_RATE = "error_rate"
    RESOURCE_UTILIZATION = "resource_utilization"


@dataclass
class BenchmarkConfig:
    """Configuration for benchmark execution."""
    tool_name: str
    benchmark_type: BenchmarkType
    iterations: int = 10
    concurrent_users: int = 1
    duration_seconds: int = 60
    warmup_iterations: int = 3
    input_data: Dict[str, Any] = None
    agent_type: str = "analyst"
    memory_profiling: bool = True
    cpu_profiling: bool = True
    
    def __post_init__(self):
        if self.input_data is None:
            self.input_data = {}


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    execution_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    peak_memory_mb: float
    throughput_ops_per_sec: float
    latency_percentiles: Dict[str, float]
    error_count: int
    success_count: int
    timestamp: str
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = time.strftime("%Y-%m-%d %H:%M:%S")


@dataclass
class BenchmarkResult:
    """Result of a benchmark execution."""
    config: BenchmarkConfig
    metrics: PerformanceMetrics
    detailed_metrics: List[Dict[str, Any]]
    bottlenecks: List[str]
    recommendations: List[str]
    regression_detected: bool = False
    baseline_comparison: Optional[Dict[str, float]] = None


class SystemMonitor:
    """System resource monitoring during benchmarks."""
    
    def __init__(self):
        self.monitoring = False
        self.metrics = []
        self.monitor_thread = None
    
    def start_monitoring(self, interval: float = 0.1):
        """Start system monitoring."""
        self.monitoring = True
        self.metrics = []
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.start()
    
    def stop_monitoring(self) -> List[Dict[str, Any]]:
        """Stop monitoring and return collected metrics."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        return self.metrics
    
    def _monitor_loop(self, interval: float):
        """Monitoring loop."""
        while self.monitoring:
            try:
                cpu_percent = psutil.cpu_percent(interval=None)
                memory = psutil.virtual_memory()
                
                metric = {
                    "timestamp": time.time(),
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_used_mb": memory.used / (1024 * 1024),
                    "memory_available_mb": memory.available / (1024 * 1024)
                }
                
                self.metrics.append(metric)
                time.sleep(interval)
                
            except Exception as e:
                logger.warning(f"Monitoring error: {e}")
                break


class PerformanceBenchmarking:
    """
    Comprehensive performance benchmarking system for MCP tools.
    
    Features:
    - Performance benchmarking with detailed metrics
    - Load testing with concurrent users
    - Stress testing with resource limits
    - Memory profiling and leak detection
    - CPU profiling and bottleneck identification
    - Automated regression detection
    - Performance trend analysis
    - Scalability testing
    """
    
    def __init__(self, tools_registry: Dict[str, Any] = None):
        """Initialize the performance benchmarking system."""
        self.tools_registry = tools_registry or {}
        self.baselines = {}
        self.results_history = []
        self.baseline_file = "performance_baselines.json"
        
        # Load existing baselines
        self._load_baselines()
    
    def _load_baselines(self):
        """Load performance baselines from file."""
        try:
            with open(self.baseline_file, 'r') as f:
                self.baselines = json.load(f)
        except FileNotFoundError:
            self.baselines = {}
    
    def _save_baselines(self):
        """Save performance baselines to file."""
        with open(self.baseline_file, 'w') as f:
            json.dump(self.baselines, f, indent=2, default=str)
    
    async def run_benchmark(self, config: BenchmarkConfig) -> BenchmarkResult:
        """Run a comprehensive benchmark."""
        logger.info(f"Starting benchmark: {config.tool_name} - {config.benchmark_type.value}")
        
        # Get tool instance
        tool_instance = await self._get_tool_instance(config.tool_name)
        if not tool_instance:
            raise ValueError(f"Tool {config.tool_name} not found")
        
        # Route to appropriate benchmark type
        if config.benchmark_type == BenchmarkType.PERFORMANCE:
            return await self._run_performance_benchmark(tool_instance, config)
        elif config.benchmark_type == BenchmarkType.LOAD_TEST:
            return await self._run_load_test(tool_instance, config)
        elif config.benchmark_type == BenchmarkType.STRESS_TEST:
            return await self._run_stress_test(tool_instance, config)
        elif config.benchmark_type == BenchmarkType.MEMORY_USAGE:
            return await self._run_memory_benchmark(tool_instance, config)
        elif config.benchmark_type == BenchmarkType.CONCURRENCY:
            return await self._run_concurrency_benchmark(tool_instance, config)
        elif config.benchmark_type == BenchmarkType.SCALABILITY:
            return await self._run_scalability_benchmark(tool_instance, config)
        else:
            raise ValueError(f"Unsupported benchmark type: {config.benchmark_type}")
    
    async def _run_performance_benchmark(self, tool_instance, config: BenchmarkConfig) -> BenchmarkResult:
        """Run standard performance benchmark."""
        detailed_metrics = []
        execution_times = []
        memory_usages = []
        cpu_usages = []
        errors = 0
        successes = 0
        
        # Warmup runs
        logger.info(f"Running {config.warmup_iterations} warmup iterations...")
        for _ in range(config.warmup_iterations):
            try:
                await self._execute_tool(tool_instance, config.input_data, config.agent_type)
            except:
                pass
        
        # Actual benchmark runs
        logger.info(f"Running {config.iterations} benchmark iterations...")
        monitor = SystemMonitor()
        
        for i in range(config.iterations):
            monitor.start_monitoring()
            
            # Start memory tracing if enabled
            if config.memory_profiling:
                tracemalloc.start()
            
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            
            try:
                # Execute tool
                result = await self._execute_tool(tool_instance, config.input_data, config.agent_type)
                
                end_time = time.time()
                end_memory = psutil.Process().memory_info().rss / (1024 * 1024)
                
                execution_time = end_time - start_time
                memory_usage = end_memory - start_memory
                
                execution_times.append(execution_time)
                memory_usages.append(memory_usage)
                successes += 1
                
                # Get memory trace if enabled
                if config.memory_profiling:
                    current, peak = tracemalloc.get_traced_memory()
                    tracemalloc.stop()
                    peak_memory = peak / (1024 * 1024)
                else:
                    peak_memory = end_memory
                
                # Stop monitoring and get CPU usage
                system_metrics = monitor.stop_monitoring()
                avg_cpu = statistics.mean([m['cpu_percent'] for m in system_metrics]) if system_metrics else 0
                cpu_usages.append(avg_cpu)
                
                detailed_metrics.append({
                    "iteration": i + 1,
                    "execution_time": execution_time,
                    "memory_usage": memory_usage,
                    "peak_memory": peak_memory,
                    "cpu_usage": avg_cpu,
                    "success": True,
                    "timestamp": time.time()
                })
                
            except Exception as e:
                logger.warning(f"Benchmark iteration {i+1} failed: {e}")
                errors += 1
                monitor.stop_monitoring()
                if config.memory_profiling and tracemalloc.is_tracing():
                    tracemalloc.stop()
                
                detailed_metrics.append({
                    "iteration": i + 1,
                    "error": str(e),
                    "success": False,
                    "timestamp": time.time()
                })
        
        # Calculate aggregate metrics
        if execution_times:
            avg_execution_time = statistics.mean(execution_times)
            avg_memory_usage = statistics.mean(memory_usages) if memory_usages else 0
            avg_cpu_usage = statistics.mean(cpu_usages) if cpu_usages else 0
            peak_memory = max([m.get('peak_memory', 0) for m in detailed_metrics if m.get('success')])
            
            # Calculate percentiles
            execution_times.sort()
            latency_percentiles = {
                "p50": execution_times[len(execution_times) // 2] if execution_times else 0,
                "p90": execution_times[int(len(execution_times) * 0.9)] if execution_times else 0,
                "p95": execution_times[int(len(execution_times) * 0.95)] if execution_times else 0,
                "p99": execution_times[int(len(execution_times) * 0.99)] if execution_times else 0
            }
            
            throughput = successes / sum(execution_times) if execution_times else 0
        else:
            avg_execution_time = 0
            avg_memory_usage = 0
            avg_cpu_usage = 0
            peak_memory = 0
            latency_percentiles = {"p50": 0, "p90": 0, "p95": 0, "p99": 0}
            throughput = 0
        
        metrics = PerformanceMetrics(
            execution_time=avg_execution_time,
            memory_usage_mb=avg_memory_usage,
            cpu_usage_percent=avg_cpu_usage,
            peak_memory_mb=peak_memory,
            throughput_ops_per_sec=throughput,
            latency_percentiles=latency_percentiles,
            error_count=errors,
            success_count=successes,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
        # Identify bottlenecks and generate recommendations
        bottlenecks = self._identify_bottlenecks(metrics, detailed_metrics)
        recommendations = self._generate_recommendations(metrics, bottlenecks)
        
        # Check for regressions
        baseline_key = f"{config.tool_name}_{config.benchmark_type.value}"
        regression_detected = False
        baseline_comparison = None
        
        if baseline_key in self.baselines:
            baseline = self.baselines[baseline_key]
            baseline_comparison = {
                "baseline_time": baseline.get("execution_time", 0),
                "current_time": avg_execution_time,
                "regression_percent": ((avg_execution_time - baseline.get("execution_time", 0)) / baseline.get("execution_time", 1)) * 100
            }
            
            # Consider it a regression if 20% slower
            if baseline_comparison["regression_percent"] > 20:
                regression_detected = True
        
        # Update baseline if this is a successful run
        if successes > errors:
            self.baselines[baseline_key] = {
                "execution_time": avg_execution_time,
                "memory_usage": avg_memory_usage,
                "cpu_usage": avg_cpu_usage,
                "timestamp": time.time()
            }
            self._save_baselines()
        
        result = BenchmarkResult(
            config=config,
            metrics=metrics,
            detailed_metrics=detailed_metrics,
            bottlenecks=bottlenecks,
            recommendations=recommendations,
            regression_detected=regression_detected,
            baseline_comparison=baseline_comparison
        )
        
        self.results_history.append(result)
        return result
    
    async def _get_tool_instance(self, tool_name: str):
        """Get tool instance for benchmarking."""
        # This would need to be implemented based on your tool loading mechanism
        # For now, return a mock tool
        return MockBenchmarkTool(tool_name)
    
    async def _execute_tool(self, tool_instance, input_data: Dict[str, Any], agent_type: str) -> Dict[str, Any]:
        """Execute tool with given input and agent context."""
        enhanced_input = {
            **input_data,
            "agent_identity": agent_type
        }
        
        if hasattr(tool_instance, 'execute'):
            result = await tool_instance.execute(enhanced_input)
        else:
            result = {"status": "success", "message": "Mock execution"}
        
        return result
    
    def _identify_bottlenecks(self, metrics: PerformanceMetrics, detailed_metrics: List[Dict[str, Any]]) -> List[str]:
        """Identify performance bottlenecks."""
        bottlenecks = []
        
        # High execution time
        if metrics.execution_time > 5.0:
            bottlenecks.append("High execution time detected")
        
        # High memory usage
        if metrics.memory_usage_mb > 500:
            bottlenecks.append("High memory usage detected")
        
        # High CPU usage
        if metrics.cpu_usage_percent > 80:
            bottlenecks.append("High CPU usage detected")
        
        # High error rate
        total_operations = metrics.success_count + metrics.error_count
        if total_operations > 0 and (metrics.error_count / total_operations) > 0.1:
            bottlenecks.append("High error rate detected")
        
        # Memory leaks (increasing memory usage over time)
        memory_usages = [m.get('memory_usage', 0) for m in detailed_metrics if m.get('success')]
        if len(memory_usages) > 5:
            first_half = memory_usages[:len(memory_usages)//2]
            second_half = memory_usages[len(memory_usages)//2:]
            if statistics.mean(second_half) > statistics.mean(first_half) * 1.5:
                bottlenecks.append("Potential memory leak detected")
        
        return bottlenecks
    
    def _generate_recommendations(self, metrics: PerformanceMetrics, bottlenecks: List[str]) -> List[str]:
        """Generate performance improvement recommendations."""
        recommendations = []
        
        if "High execution time detected" in bottlenecks:
            recommendations.append("Consider optimizing algorithms or adding caching")
            recommendations.append("Profile code to identify slow operations")
        
        if "High memory usage detected" in bottlenecks:
            recommendations.append("Optimize data structures and reduce memory footprint")
            recommendations.append("Consider streaming processing for large datasets")
        
        if "High CPU usage detected" in bottlenecks:
            recommendations.append("Consider parallel processing or async operations")
            recommendations.append("Optimize computational algorithms")
        
        if "High error rate detected" in bottlenecks:
            recommendations.append("Improve error handling and input validation")
            recommendations.append("Add retry mechanisms for transient failures")
        
        if "Potential memory leak detected" in bottlenecks:
            recommendations.append("Review object lifecycle and garbage collection")
            recommendations.append("Check for circular references or unclosed resources")
        
        # General recommendations based on metrics
        if metrics.latency_percentiles["p95"] > metrics.latency_percentiles["p50"] * 3:
            recommendations.append("High latency variance - investigate outliers")
        
        if metrics.throughput_ops_per_sec < 1:
            recommendations.append("Low throughput - consider performance optimizations")
        
        return recommendations


class MockBenchmarkTool:
    """Mock tool for benchmarking purposes."""
    
    def __init__(self, name: str):
        self.name = name
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Mock execute method with variable performance."""
        # Simulate variable processing time
        processing_time = 0.1 + (hash(str(input_data)) % 100) / 1000
        await asyncio.sleep(processing_time)
        
        # Simulate some memory allocation
        dummy_data = [i for i in range(1000)]
        
        return {
            "status": "success",
            "tool": self.name,
            "input_received": input_data,
            "result": f"Mock execution completed in {processing_time:.3f}s",
            "data_size": len(dummy_data)
        }


class BenchmarkReporter:
    """Generate comprehensive benchmark reports."""

    def __init__(self, benchmarking_system: PerformanceBenchmarking):
        self.system = benchmarking_system

    def generate_performance_report(self, results: List[BenchmarkResult]) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        if not results:
            return {"error": "No benchmark results available"}

        # Aggregate metrics across all results
        all_execution_times = []
        all_memory_usages = []
        all_cpu_usages = []
        total_errors = 0
        total_successes = 0
        regressions = []

        tool_performance = {}
        benchmark_type_performance = {}

        for result in results:
            metrics = result.metrics
            config = result.config

            all_execution_times.append(metrics.execution_time)
            all_memory_usages.append(metrics.memory_usage_mb)
            all_cpu_usages.append(metrics.cpu_usage_percent)
            total_errors += metrics.error_count
            total_successes += metrics.success_count

            if result.regression_detected:
                regressions.append({
                    "tool": config.tool_name,
                    "benchmark_type": config.benchmark_type.value,
                    "regression_percent": result.baseline_comparison.get("regression_percent", 0) if result.baseline_comparison else 0
                })

            # Group by tool
            if config.tool_name not in tool_performance:
                tool_performance[config.tool_name] = []
            tool_performance[config.tool_name].append(metrics)

            # Group by benchmark type
            if config.benchmark_type.value not in benchmark_type_performance:
                benchmark_type_performance[config.benchmark_type.value] = []
            benchmark_type_performance[config.benchmark_type.value].append(metrics)

        # Calculate summary statistics
        report = {
            "summary": {
                "total_benchmarks": len(results),
                "total_operations": total_successes + total_errors,
                "success_rate": (total_successes / (total_successes + total_errors)) * 100 if (total_successes + total_errors) > 0 else 0,
                "avg_execution_time": statistics.mean(all_execution_times) if all_execution_times else 0,
                "avg_memory_usage": statistics.mean(all_memory_usages) if all_memory_usages else 0,
                "avg_cpu_usage": statistics.mean(all_cpu_usages) if all_cpu_usages else 0,
                "regressions_detected": len(regressions)
            },
            "tool_performance": {
                tool: {
                    "avg_execution_time": statistics.mean([m.execution_time for m in metrics]),
                    "avg_memory_usage": statistics.mean([m.memory_usage_mb for m in metrics]),
                    "avg_throughput": statistics.mean([m.throughput_ops_per_sec for m in metrics]),
                    "total_operations": sum([m.success_count + m.error_count for m in metrics]),
                    "error_rate": sum([m.error_count for m in metrics]) / sum([m.success_count + m.error_count for m in metrics]) * 100 if sum([m.success_count + m.error_count for m in metrics]) > 0 else 0
                }
                for tool, metrics in tool_performance.items()
            },
            "benchmark_type_performance": {
                bench_type: {
                    "avg_execution_time": statistics.mean([m.execution_time for m in metrics]),
                    "avg_memory_usage": statistics.mean([m.memory_usage_mb for m in metrics]),
                    "count": len(metrics)
                }
                for bench_type, metrics in benchmark_type_performance.items()
            },
            "regressions": regressions,
            "recommendations": self._generate_global_recommendations(results),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        return report

    def _generate_global_recommendations(self, results: List[BenchmarkResult]) -> List[str]:
        """Generate global recommendations based on all results."""
        recommendations = []

        # Check for consistent issues across tools
        high_memory_tools = []
        slow_tools = []
        error_prone_tools = []

        for result in results:
            if result.metrics.memory_usage_mb > 200:
                high_memory_tools.append(result.config.tool_name)
            if result.metrics.execution_time > 3.0:
                slow_tools.append(result.config.tool_name)
            if result.metrics.error_count > 0:
                error_prone_tools.append(result.config.tool_name)

        if len(high_memory_tools) > len(results) * 0.3:
            recommendations.append("Multiple tools showing high memory usage - consider system-wide memory optimization")

        if len(slow_tools) > len(results) * 0.3:
            recommendations.append("Multiple tools showing slow performance - consider infrastructure upgrades")

        if len(error_prone_tools) > len(results) * 0.2:
            recommendations.append("Multiple tools showing errors - review error handling and input validation")

        # Check for regressions
        regressions = [r for r in results if r.regression_detected]
        if len(regressions) > 0:
            recommendations.append(f"Performance regressions detected in {len(regressions)} tools - investigate recent changes")

        return recommendations

    def save_report(self, report: Dict[str, Any], output_path: str = "performance_report.json"):
        """Save performance report to file."""
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        logger.info(f"Performance report saved to {output_path}")

    def generate_html_report(self, report: Dict[str, Any]) -> str:
        """Generate HTML performance report."""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>MCP Tools Performance Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .metric { display: inline-block; margin: 10px; padding: 10px; background: white; border-radius: 3px; }
                .regression { color: red; font-weight: bold; }
                .good { color: green; }
                table { border-collapse: collapse; width: 100%; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <h1>MCP Tools Performance Report</h1>
            <p>Generated: {timestamp}</p>

            <div class="summary">
                <h2>Summary</h2>
                <div class="metric">Total Benchmarks: {total_benchmarks}</div>
                <div class="metric">Success Rate: {success_rate:.1f}%</div>
                <div class="metric">Avg Execution Time: {avg_execution_time:.3f}s</div>
                <div class="metric">Avg Memory Usage: {avg_memory_usage:.1f}MB</div>
                <div class="metric">Regressions: <span class="regression">{regressions_detected}</span></div>
            </div>

            <h2>Tool Performance</h2>
            <table>
                <tr>
                    <th>Tool</th>
                    <th>Avg Execution Time (s)</th>
                    <th>Avg Memory Usage (MB)</th>
                    <th>Throughput (ops/s)</th>
                    <th>Error Rate (%)</th>
                </tr>
                {tool_rows}
            </table>

            <h2>Recommendations</h2>
            <ul>
                {recommendations}
            </ul>
        </body>
        </html>
        """

        # Generate tool rows
        tool_rows = ""
        for tool, metrics in report.get("tool_performance", {}).items():
            tool_rows += f"""
                <tr>
                    <td>{tool}</td>
                    <td>{metrics['avg_execution_time']:.3f}</td>
                    <td>{metrics['avg_memory_usage']:.1f}</td>
                    <td>{metrics['avg_throughput']:.2f}</td>
                    <td>{metrics['error_rate']:.1f}</td>
                </tr>
            """

        # Generate recommendations
        recommendations_html = ""
        for rec in report.get("recommendations", []):
            recommendations_html += f"<li>{rec}</li>"

        # Fill template
        html_content = html_template.format(
            timestamp=report.get("timestamp", ""),
            total_benchmarks=report["summary"]["total_benchmarks"],
            success_rate=report["summary"]["success_rate"],
            avg_execution_time=report["summary"]["avg_execution_time"],
            avg_memory_usage=report["summary"]["avg_memory_usage"],
            regressions_detected=report["summary"]["regressions_detected"],
            tool_rows=tool_rows,
            recommendations=recommendations_html
        )

        return html_content


class BenchmarkScheduler:
    """Automated benchmark scheduling and execution."""

    def __init__(self, benchmarking_system: PerformanceBenchmarking):
        self.system = benchmarking_system
        self.scheduled_benchmarks = []
        self.running = False

    def schedule_daily_benchmarks(self, tools: List[str]):
        """Schedule daily performance benchmarks for specified tools."""
        for tool in tools:
            config = BenchmarkConfig(
                tool_name=tool,
                benchmark_type=BenchmarkType.PERFORMANCE,
                iterations=20,
                warmup_iterations=5
            )
            self.scheduled_benchmarks.append(config)

    def schedule_weekly_load_tests(self, tools: List[str]):
        """Schedule weekly load tests for specified tools."""
        for tool in tools:
            config = BenchmarkConfig(
                tool_name=tool,
                benchmark_type=BenchmarkType.LOAD_TEST,
                concurrent_users=10,
                duration_seconds=300
            )
            self.scheduled_benchmarks.append(config)

    async def run_scheduled_benchmarks(self) -> List[BenchmarkResult]:
        """Run all scheduled benchmarks."""
        results = []

        for config in self.scheduled_benchmarks:
            try:
                logger.info(f"Running scheduled benchmark: {config.tool_name}")
                result = await self.system.run_benchmark(config)
                results.append(result)
            except Exception as e:
                logger.error(f"Scheduled benchmark failed for {config.tool_name}: {e}")

        return results

    def generate_trend_analysis(self, days: int = 30) -> Dict[str, Any]:
        """Generate performance trend analysis."""
        # This would analyze historical data to identify trends
        # For now, return a placeholder
        return {
            "period_days": days,
            "trends": {
                "performance_improving": [],
                "performance_degrading": [],
                "stable_performance": []
            },
            "recommendations": [
                "Monitor tools showing degrading performance trends",
                "Investigate recent changes for performance improvements",
                "Consider capacity planning based on usage trends"
            ]
        }
