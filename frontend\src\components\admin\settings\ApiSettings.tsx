import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Plus, Trash, Check, X, AlertCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface ApiSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const ApiSettings = ({ settings, onUpdate }: ApiSettingsProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    defaultProvider: 'openai',
    providers: [
      {
        id: 'openai',
        name: 'OpenAI',
        enabled: true,
        apiKey: '',
        defaultModel: 'gpt-4',
        models: ['gpt-4', 'gpt-3.5-turbo'],
        baseUrl: 'https://api.openai.com/v1',
        requestTimeout: 30,
        maxTokens: 4096,
      },
      {
        id: 'groq',
        name: 'Groq',
        enabled: true,
        apiKey: '',
        defaultModel: 'llama3-70b-8192',
        models: ['llama3-70b-8192', 'mixtral-8x7b-32768'],
        baseUrl: 'https://api.groq.com/openai/v1',
        requestTimeout: 30,
        maxTokens: 4096,
      },
      {
        id: 'anthropic',
        name: 'Anthropic',
        enabled: false,
        apiKey: '',
        defaultModel: 'claude-3-opus-20240229',
        models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229'],
        baseUrl: 'https://api.anthropic.com/v1',
        requestTimeout: 30,
        maxTokens: 4096,
      },
    ],
    cacheEnabled: true,
    cacheTTL: 3600,
    rateLimitEnabled: true,
    rateLimit: 100,
    rateLimitWindow: 60,
    fallbackEnabled: true,
    fallbackProvider: 'groq',
  });

  // Initialize form data when settings are loaded
  useEffect(() => {
    if (settings) {
      setFormData({
        defaultProvider: settings.defaultProvider || 'openai',
        providers: settings.providers || formData.providers,
        cacheEnabled: settings.cacheEnabled ?? true,
        cacheTTL: settings.cacheTTL || 3600,
        rateLimitEnabled: settings.rateLimitEnabled ?? true,
        rateLimit: settings.rateLimit || 100,
        rateLimitWindow: settings.rateLimitWindow || 60,
        fallbackEnabled: settings.fallbackEnabled ?? true,
        fallbackProvider: settings.fallbackProvider || 'groq',
      });
    }
  }, [settings]);

  // Handle provider change
  const handleProviderChange = (index: number, field: string, value: any) => {
    const updatedProviders = [...formData.providers];
    updatedProviders[index] = {
      ...updatedProviders[index],
      [field]: value,
    };

    setFormData((prev) => {
      const updated = { ...prev, providers: updatedProviders };
      onUpdate(updated);
      return updated;
    });
  };

  // Handle checkbox change
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => {
      const updated = { ...prev, [name]: checked };
      onUpdate(updated);
      return updated;
    });
  };

  // Handle number input change
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      setFormData((prev) => {
        const updated = { ...prev, [name]: numValue };
        onUpdate(updated);
        return updated;
      });
    }
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => {
      const updated = { ...prev, [name]: value };
      onUpdate(updated);
      return updated;
    });
  };

  // Test API key
  const testApiKey = (provider: any) => {
    // This would be implemented with an actual API call
    toast({
      title: 'API Key Test',
      description: `Testing ${provider.name} API key...`,
    });

    // Simulate API call
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70% chance of success for demo
      if (success) {
        toast({
          title: 'API Key Valid',
          description: `The ${provider.name} API key is valid.`,
        });
      } else {
        toast({
          title: 'API Key Invalid',
          description: `The ${provider.name} API key is invalid or has expired.`,
          variant: 'destructive',
        });
      }
    }, 1500);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">API Providers</h3>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="defaultProvider" className="text-right">
            Default Provider
          </Label>
          <Select
            value={formData.defaultProvider}
            onValueChange={(value) => handleSelectChange('defaultProvider', value)}
          >
            <SelectTrigger id="defaultProvider" className="col-span-3">
              <SelectValue placeholder="Select default provider" />
            </SelectTrigger>
            <SelectContent>
              {formData.providers
                .filter((provider) => provider.enabled)
                .map((provider) => (
                  <SelectItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>

        <Accordion type="single" collapsible className="w-full">
          {formData.providers.map((provider, index) => (
            <AccordionItem key={provider.id} value={provider.id}>
              <AccordionTrigger className="hover:no-underline">
                <div className="flex items-center space-x-2">
                  <span>{provider.name}</span>
                  {provider.enabled ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <Check className="h-3.5 w-3.5 mr-1" />
                      Enabled
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      <X className="h-3.5 w-3.5 mr-1" />
                      Disabled
                    </Badge>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <Card className="border-none shadow-none">
                  <CardContent className="p-0 space-y-4">
                    <div className="flex items-center space-x-2 pt-4">
                      <Checkbox
                        id={`${provider.id}-enabled`}
                        checked={provider.enabled}
                        onCheckedChange={(checked) => handleProviderChange(index, 'enabled', checked as boolean)}
                      />
                      <Label htmlFor={`${provider.id}-enabled`}>Enable {provider.name}</Label>
                    </div>

                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor={`${provider.id}-apiKey`} className="text-right">
                        API Key
                      </Label>
                      <div className="col-span-3 flex space-x-2">
                        <Input
                          id={`${provider.id}-apiKey`}
                          type="password"
                          value={provider.apiKey}
                          onChange={(e) => handleProviderChange(index, 'apiKey', e.target.value)}
                          className="flex-1"
                          placeholder={`Enter ${provider.name} API key`}
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => testApiKey(provider)}
                          disabled={!provider.apiKey}
                        >
                          Test
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor={`${provider.id}-defaultModel`} className="text-right">
                        Default Model
                      </Label>
                      <Select
                        value={provider.defaultModel}
                        onValueChange={(value) => handleProviderChange(index, 'defaultModel', value)}
                      >
                        <SelectTrigger id={`${provider.id}-defaultModel`} className="col-span-3">
                          <SelectValue placeholder="Select default model" />
                        </SelectTrigger>
                        <SelectContent>
                          {provider.models.map((model) => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor={`${provider.id}-baseUrl`} className="text-right">
                        Base URL
                      </Label>
                      <Input
                        id={`${provider.id}-baseUrl`}
                        value={provider.baseUrl}
                        onChange={(e) => handleProviderChange(index, 'baseUrl', e.target.value)}
                        className="col-span-3"
                      />
                    </div>

                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor={`${provider.id}-timeout`} className="text-right">
                        Timeout (seconds)
                      </Label>
                      <Input
                        id={`${provider.id}-timeout`}
                        type="number"
                        min="5"
                        max="120"
                        value={provider.requestTimeout}
                        onChange={(e) => handleProviderChange(index, 'requestTimeout', parseInt(e.target.value, 10))}
                        className="col-span-3"
                      />
                    </div>

                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor={`${provider.id}-maxTokens`} className="text-right">
                        Max Tokens
                      </Label>
                      <Input
                        id={`${provider.id}-maxTokens`}
                        type="number"
                        min="1024"
                        max="32768"
                        step="1024"
                        value={provider.maxTokens}
                        onChange={(e) => handleProviderChange(index, 'maxTokens', parseInt(e.target.value, 10))}
                        className="col-span-3"
                      />
                    </div>
                  </CardContent>
                </Card>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Caching</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="cacheEnabled"
              checked={formData.cacheEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('cacheEnabled', checked as boolean)}
            />
            <Label htmlFor="cacheEnabled">Enable Response Caching</Label>
          </div>
          {formData.cacheEnabled && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="cacheTTL" className="text-right">
                Cache TTL (seconds)
              </Label>
              <Input
                id="cacheTTL"
                name="cacheTTL"
                type="number"
                min="60"
                max="86400"
                value={formData.cacheTTL}
                onChange={handleNumberChange}
                className="col-span-3"
              />
            </div>
          )}
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Rate Limiting</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="rateLimitEnabled"
              checked={formData.rateLimitEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('rateLimitEnabled', checked as boolean)}
            />
            <Label htmlFor="rateLimitEnabled">Enable Rate Limiting</Label>
          </div>
          {formData.rateLimitEnabled && (
            <>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="rateLimit" className="text-right">
                  Rate Limit (requests)
                </Label>
                <Input
                  id="rateLimit"
                  name="rateLimit"
                  type="number"
                  min="10"
                  max="1000"
                  value={formData.rateLimit}
                  onChange={handleNumberChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="rateLimitWindow" className="text-right">
                  Window (seconds)
                </Label>
                <Input
                  id="rateLimitWindow"
                  name="rateLimitWindow"
                  type="number"
                  min="10"
                  max="3600"
                  value={formData.rateLimitWindow}
                  onChange={handleNumberChange}
                  className="col-span-3"
                />
              </div>
            </>
          )}
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Fallback</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="fallbackEnabled"
              checked={formData.fallbackEnabled}
              onCheckedChange={(checked) => handleCheckboxChange('fallbackEnabled', checked as boolean)}
            />
            <Label htmlFor="fallbackEnabled">Enable Provider Fallback</Label>
          </div>
          {formData.fallbackEnabled && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="fallbackProvider" className="text-right">
                Fallback Provider
              </Label>
              <Select
                value={formData.fallbackProvider}
                onValueChange={(value) => handleSelectChange('fallbackProvider', value)}
              >
                <SelectTrigger id="fallbackProvider" className="col-span-3">
                  <SelectValue placeholder="Select fallback provider" />
                </SelectTrigger>
                <SelectContent>
                  {formData.providers
                    .filter((provider) => provider.enabled && provider.id !== formData.defaultProvider)
                    .map((provider) => (
                      <SelectItem key={provider.id} value={provider.id}>
                        {provider.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApiSettings;
