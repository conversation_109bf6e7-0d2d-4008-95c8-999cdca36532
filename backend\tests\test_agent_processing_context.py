"""
Tests for AgentProcessingContext and component refactoring.
"""
import pytest
import sys
from typing import Dict, Any
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import agent schemas using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, PersonaConfig = import_agent_schemas()

# Import BaseComponentConfig directly
from schemas.agent_config_schemas import BaseComponentConfig


class TestAgentProcessingContext:
    """Test AgentProcessingContext functionality."""

    def test_create_context(self):
        """Test creating an AgentProcessingContext."""
        context = AgentProcessingContext(
            message="Test message",
            user_id="test_user",
            session_id="test_session",
            initial_context={"key": "value"},
            metadata={"source": "test"}
        )

        assert context.message == "Test message"
        assert context.user_id == "test_user"
        assert context.session_id == "test_session"
        assert context.initial_context == {"key": "value"}
        assert context.metadata == {"source": "test"}
        assert context.component_data == {}
        assert context.response is None
        assert context.agent_config is None

    def test_context_with_agent_config(self):
        """Test creating context with agent configuration."""
        agent_config = PersonaConfig(
            name="Test Agent",
            description="A test agent",
            system_prompts={"greeting": "Hello!"},
            model_config={"provider": "openai", "model": "gpt-4"},
            components=[
                BaseComponentConfig(
                    name="test_component",
                    type="TestComponent",
                    config={"param": "value"}
                )
            ]
        )

        context = AgentProcessingContext(
            message="Test message",
            user_id="test_user",
            agent_config=agent_config
        )

        assert context.agent_config == agent_config
        assert context.agent_config.name == "Test Agent"
        assert context.agent_config.system_prompts == {"greeting": "Hello!"}

    def test_update_component_data(self):
        """Test updating component data."""
        context = AgentProcessingContext(
            message="Test message",
            user_id="test_user"
        )

        # Add component data
        context.component_data["analysis_task"] = {"type": "data_cleaning"}
        context.component_data["data"] = {"rows": 100}

        assert context.component_data["analysis_task"] == {"type": "data_cleaning"}
        assert context.component_data["data"] == {"rows": 100}

    def test_update_metadata(self):
        """Test updating metadata."""
        context = AgentProcessingContext(
            message="Test message",
            user_id="test_user",
            metadata={"initial": "value"}
        )

        # Update metadata
        context.metadata["error"] = "test_error"
        context.metadata["status"] = "processing"

        assert context.metadata["initial"] == "value"
        assert context.metadata["error"] == "test_error"
        assert context.metadata["status"] == "processing"

    def test_set_response(self):
        """Test setting response."""
        context = AgentProcessingContext(
            message="Test message",
            user_id="test_user"
        )

        # Set response
        context.response = "This is the response"

        assert context.response == "This is the response"

    def test_context_serialization(self):
        """Test context can be serialized to dict."""
        context = AgentProcessingContext(
            message="Test message",
            user_id="test_user",
            session_id="test_session",
            initial_context={"key": "value"},
            metadata={"source": "test"},
            component_data={"task": "analysis"},
            response="Test response"
        )

        # Convert to dict
        context_dict = context.model_dump()

        assert context_dict["message"] == "Test message"
        assert context_dict["user_id"] == "test_user"
        assert context_dict["session_id"] == "test_session"
        assert context_dict["initial_context"] == {"key": "value"}
        assert context_dict["metadata"] == {"source": "test"}
        assert context_dict["component_data"] == {"task": "analysis"}
        assert context_dict["response"] == "Test response"

    def test_context_from_dict(self):
        """Test creating context from dict."""
        context_data = {
            "message": "Test message",
            "user_id": "test_user",
            "session_id": "test_session",
            "initial_context": {"key": "value"},
            "metadata": {"source": "test"},
            "component_data": {"task": "analysis"},
            "response": "Test response"
        }

        context = AgentProcessingContext(**context_data)

        assert context.message == "Test message"
        assert context.user_id == "test_user"
        assert context.session_id == "test_session"
        assert context.initial_context == {"key": "value"}
        assert context.metadata == {"source": "test"}
        assert context.component_data == {"task": "analysis"}
        assert context.response == "Test response"


class TestPersonaConfig:
    """Test PersonaConfig functionality."""

    def test_create_persona_config(self):
        """Test creating a PersonaConfig."""
        config = PersonaConfig(
            name="Test Persona",
            description="A test persona",
            system_prompts={
                "greeting": "Hello! I'm a test persona.",
                "analysis": "Let me analyze your data."
            },
            model_config={
                "provider": "openai",
                "model": "gpt-4",
                "temperature": 0.7
            },
            components=[
                BaseComponentConfig(
                    name="parser",
                    type="AnalysisParserComponent",
                    config={"data_dir": "/tmp"}
                ),
                BaseComponentConfig(
                    name="executor",
                    type="AnalysisExecutorComponent",
                    config={}
                )
            ]
        )

        assert config.name == "Test Persona"
        assert config.description == "A test persona"
        assert len(config.system_prompts) == 2
        assert config.system_prompts["greeting"] == "Hello! I'm a test persona."
        assert config.model_config["provider"] == "openai"
        assert len(config.components) == 2
        assert config.components[0].name == "parser"
        assert config.components[0].type == "AnalysisParserComponent"

    def test_persona_config_validation(self):
        """Test PersonaConfig validation."""
        # Test with minimal required fields
        config = PersonaConfig(
            name="Minimal Persona",
            description="Minimal description"
        )

        assert config.name == "Minimal Persona"
        assert config.description == "Minimal description"
        assert config.system_prompts == {}
        assert config.model_config == {}
        assert config.components == []

    def test_persona_config_serialization(self):
        """Test PersonaConfig serialization."""
        config = PersonaConfig(
            name="Test Persona",
            description="A test persona",
            system_prompts={"greeting": "Hello!"},
            model_config={"provider": "openai"},
            components=[
                BaseComponentConfig(
                    name="test_component",
                    type="TestComponent",
                    config={"param": "value"}
                )
            ]
        )

        # Convert to dict
        config_dict = config.model_dump()

        assert config_dict["name"] == "Test Persona"
        assert config_dict["description"] == "A test persona"
        assert config_dict["system_prompts"] == {"greeting": "Hello!"}
        assert config_dict["model_config"] == {"provider": "openai"}
        assert len(config_dict["components"]) == 1
        assert config_dict["components"][0]["name"] == "test_component"


class TestBaseComponentConfig:
    """Test BaseComponentConfig functionality."""

    def test_create_component_config(self):
        """Test creating a BaseComponentConfig."""
        config = BaseComponentConfig(
            name="test_component",
            type="TestComponent",
            config={
                "param1": "value1",
                "param2": 42,
                "param3": True
            }
        )

        assert config.name == "test_component"
        assert config.type == "TestComponent"
        assert config.config["param1"] == "value1"
        assert config.config["param2"] == 42
        assert config.config["param3"] is True

    def test_component_config_minimal(self):
        """Test creating minimal component config."""
        config = BaseComponentConfig(
            name="minimal_component",
            type="MinimalComponent"
        )

        assert config.name == "minimal_component"
        assert config.type == "MinimalComponent"
        assert config.config == {}

    def test_component_config_serialization(self):
        """Test component config serialization."""
        config = BaseComponentConfig(
            name="test_component",
            type="TestComponent",
            config={"param": "value"}
        )

        # Convert to dict
        config_dict = config.model_dump()

        assert config_dict["name"] == "test_component"
        assert config_dict["type"] == "TestComponent"
        assert config_dict["config"] == {"param": "value"}

    def test_component_config_from_dict(self):
        """Test creating component config from dict."""
        config_data = {
            "name": "test_component",
            "type": "TestComponent",
            "config": {"param": "value"}
        }

        config = BaseComponentConfig(**config_data)

        assert config.name == "test_component"
        assert config.type == "TestComponent"
        assert config.config == {"param": "value"}
