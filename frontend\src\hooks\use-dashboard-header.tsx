import React, { useState, useEffect, useCallback } from 'react';
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';
import { useDashboardManagement } from '@/hooks/use-dashboard-management';
import { useDashboardWebSocket, useDashboardUpdates, useWidgetDataUpdates } from '@/hooks/use-dashboard-websocket';
import { useDashboardPerformance } from '@/hooks/use-dashboard-performance';
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/contexts/AuthContext';
import { DashboardSelector } from '@/components/dashboard/DashboardSelector';
import { DashboardSecurityIndicator } from '@/components/dashboard/DashboardSecurityIndicator';
import { DashboardPerformanceMonitor } from '@/components/dashboard/DashboardPerformanceMonitor';
import { SafeDashboardComponent } from '@/components/dashboard/SafeDashboardComponent';
import { SectionCreate } from '@/types/dashboard-customization';

export interface DashboardHeaderProps {
  // Optimized interface - dashboard management moved to mode-specific toolbars
  showDashboardContext?: boolean;
  dashboardTitle?: string;
  isSystemLoading?: boolean;
  quickActions?: React.ReactNode;
  className?: string;
}

/**
 * Consolidated dashboard header hook that provides all dashboard header functionality.
 * This is the single source of truth for dashboard header state and actions.
 *
 * Features:
 * - Dashboard state management
 * - Real-time WebSocket connections
 * - Performance monitoring
 * - Error handling
 * - All button handlers (Add Widget, Refresh, Export, Settings, etc.)
 * - Custom actions (Dashboard selector, security indicator, performance monitor)
 *
 * Replaces duplicate logic previously found in DashboardWithHeader and other components.
 */
export const useDashboardHeader = () => {
  const { toast } = useToast();
  const { isAuthenticated } = useAuth();

  // Dashboard state management
  const [showDashboardSettings, setShowDashboardSettings] = useState(false);
  const [showWidgetManagement, setShowWidgetManagement] = useState(false);

  // Use unified dashboard store for customizable view
  const {
    activeDashboardId,
    currentLayout,
    isLoading: isDashboardLoading,
    error: dashboardError,
    loadDashboards,
    loadCurrentLayout,
    createSection,
  } = useUnifiedDashboardStore();

  // Dashboard management hook
  const {
    activeDashboard,
  } = useDashboardManagement();

  // Define isLoadingLayout for component usage
  const isLoadingLayout = isDashboardLoading;

  // WebSocket hooks for real-time updates
  const { isConnected: wsConnected, connect: wsConnect, disconnect: wsDisconnect } = useDashboardWebSocket({
    dashboardId: activeDashboardId || undefined,
    showConnectionToasts: true,
    autoConnect: false,
  });

  // Memoize WebSocket connection logic
  const connectWebSocket = useCallback(() => {
    if (!isAuthenticated) {
      wsDisconnect();
      return;
    }

    const connectWithToken = () => {
      const token = localStorage.getItem('token');
      if (token) {
        console.log('🔗 Connecting WebSocket with token...');
        wsConnect(token);
      } else {
        console.warn('⚠️ No token available for WebSocket connection');
      }
    };

    // Initial connection
    connectWithToken();

    // Listen for token refresh events
    const handleTokenRefresh = (event: CustomEvent) => {
      console.log('🔄 Token refreshed, reconnecting WebSocket...');
      wsDisconnect();
      setTimeout(() => {
        const newToken = event.detail?.token || localStorage.getItem('token');
        if (newToken) {
          wsConnect(newToken);
        }
      }, 100); // Small delay to ensure clean disconnect
    };

    window.addEventListener('tokenRefreshed', handleTokenRefresh as EventListener);

    // Return cleanup function
    return () => {
      window.removeEventListener('tokenRefreshed', handleTokenRefresh as EventListener);
      wsDisconnect();
    };
  }, [isAuthenticated, wsConnect, wsDisconnect]);

  // Connect to WebSocket with authentication token
  useEffect(() => {
    return connectWebSocket();
  }, [connectWebSocket]);

  const { updates: dashboardUpdates } = useDashboardUpdates(
    activeDashboardId || undefined
  );

  const { updates: widgetUpdates } = useWidgetDataUpdates(
    currentLayout?.widgets.map(w => w.id) || []
  );

  // Performance monitoring
  const {
    markDataLoadStart,
    markDataLoadEnd,
  } = useDashboardPerformance({
    maxRenderTime: 2000,
    maxDataLoadTime: 10000,
    maxMemoryUsage: 150 * 1024 * 1024,
    maxErrorCount: 3,
  });

  // Memoize initialization function to prevent re-renders
  const initializeDashboards = useCallback(() => {
    markDataLoadStart();
    loadDashboards();
    if (activeDashboardId) {
      loadCurrentLayout().finally(() => {
        markDataLoadEnd();
      });
    } else {
      markDataLoadEnd();
    }
  }, [activeDashboardId, markDataLoadStart, loadDashboards, loadCurrentLayout, markDataLoadEnd]);

  // Initialize dashboards on mount
  useEffect(() => {
    initializeDashboards();
  }, [initializeDashboards]);

  // Handle dashboard errors
  useEffect(() => {
    if (dashboardError) {
      toast({
        title: "Dashboard Error",
        description: dashboardError.message,
        variant: "destructive",
      });
    }
  }, [dashboardError, toast]);

  // Handle real-time dashboard updates
  useEffect(() => {
    if (dashboardUpdates.length > 0) {
      const latestUpdate = dashboardUpdates[dashboardUpdates.length - 1];

      console.log('Dashboard updated:', latestUpdate.payload.dashboard_id);
      // Reload layout when dashboard structure changes
      if (latestUpdate.payload.dashboard_id === activeDashboardId) {
        loadCurrentLayout();
        toast({
          title: "Dashboard Updated",
          description: "Dashboard has been updated in real-time.",
        });
      }
    }
  }, [dashboardUpdates, activeDashboardId, loadCurrentLayout, toast]);

  // Handle real-time widget data updates
  useEffect(() => {
    if (widgetUpdates.length > 0) {
      // Get the most recent update from the array
      const latestUpdate = widgetUpdates[widgetUpdates.length - 1];

      if (latestUpdate?.payload.error) {
        toast({
          title: "Widget Error",
          description: `Widget data update failed: ${latestUpdate.payload.error}`,
          variant: "destructive",
        });
      } else if (latestUpdate) {
        console.log('Widget data updated:', latestUpdate.payload.widget_id);
      }
    }
  }, [widgetUpdates, toast]);

  const handleRefresh = () => {
    markDataLoadStart();
    loadCurrentLayout().finally(() => {
      markDataLoadEnd();
    });
    toast({
      title: "Dashboard Refreshed",
      description: "Dashboard layout has been reloaded.",
    });
  };

  const handleExport = () => {
    // TODO: Implement actual export functionality
    toast({
      title: "Export Started",
      description: "Dashboard data export will be available shortly.",
    });
  };

  const handleAddSection = () => {
    if (!activeDashboardId) {
      toast({
        title: "Error",
        description: "No active dashboard found.",
        variant: "destructive",
      });
      return;
    }

    const newSection: SectionCreate = {
      dashboard_id: activeDashboardId,
      name: "New Section",
      description: "A new section for organizing widgets",
      layout_config: {
        columns: 12,
        rows: 6,
        grid_gap: 4,
        responsive: true,
      },
    };

    createSection(newSection);
    toast({
      title: "Section Added",
      description: "A new section has been added to your dashboard.",
    });
  };

  const handleMainAddWidget = () => {
    // Find the first section or create one if none exists
    if (!currentLayout?.sections || currentLayout.sections.length === 0) {
      handleAddSection();
      return;
    }

    // Trigger the ribbon toolbar's Add Widget functionality
    handleRibbonAddWidget('chart'); // Default to chart widget
  };

  const handleSave = () => {
    // TODO: Implement actual save functionality
    toast({
      title: "Dashboard Saved",
      description: "Dashboard changes have been saved successfully.",
    });
  };

  const handleTemplateSelect = (template: any) => {
    // Handle template selection for dashboard creation
    toast({
      title: "Template Selected",
      description: `Creating dashboard from ${template.name} template...`,
    });

    // TODO: Implement template-based dashboard creation
    // This would typically:
    // 1. Create a new dashboard with template structure
    // 2. Add widgets based on template configuration
    // 3. Apply template styling and layout
    // 4. Navigate to the new dashboard
    console.log('Selected template:', template);
  };

  // Ribbon toolbar handlers
  const handleRibbonAddWidget = (type: string) => {
    if (type === 'section') {
      handleAddSection();
      return;
    }

    if (!currentLayout?.sections || currentLayout.sections.length === 0) {
      handleAddSection();
      return;
    }

    toast({
      title: "Add Widget",
      description: `Adding ${type} widget to dashboard.`,
    });
  };

  // Note: These handlers are no longer used since ribbon is handled by DashboardModeWrapper
  // Keeping them commented for potential future use
  /*
  const handleLayoutChange = (layout: string) => {
    toast({
      title: "Layout Changed",
      description: `Dashboard layout changed to ${layout}.`,
    });
  };

  const handleStyleChange = (style: string) => {
    toast({
      title: "Style Updated",
      description: `Dashboard style updated: ${style}.`,
    });
  };

  const handleFilterToggle = () => {
    toast({
      title: "Filter Toggle",
      description: "Dashboard filters toggled.",
    });
  };

  const handleSortToggle = () => {
    toast({
      title: "Sort Toggle",
      description: "Dashboard sorting toggled.",
    });
  };

  const handleViewChange = (view: string) => {
    toast({
      title: "View Changed",
      description: `Dashboard view changed to ${view}.`,
    });
  };
  */

  /*
  const handleRibbonExport = (format: string) => {
    toast({
      title: "Export Started",
      description: `Exporting dashboard as ${format}.`,
    });
  };

  const handleShare = () => {
    toast({
      title: "Share Dashboard",
      description: "Dashboard sharing options opened.",
    });
  };
  */

  // Prepare optimized header props (dashboard management moved to ribbon)
  const headerProps: DashboardHeaderProps = {
    showDashboardContext: true,
    dashboardTitle: activeDashboard?.name || "Dashboard",
    isSystemLoading: isLoadingLayout,
    // Quick actions can be added here if needed for specific workflows
    quickActions: undefined,
  };

  return {
    headerProps,
    dashboardState: {
      currentLayout,
      activeDashboard,
      activeDashboardId,
      isLoadingLayout,
      showDashboardSettings,
      setShowDashboardSettings,
      showWidgetManagement,
      setShowWidgetManagement,
      wsConnected,
      dashboardError,
    },
    // Expose handlers for external use if needed
    handlers: {
      handleRefresh,
      handleExport,
      handleAddSection,
      handleMainAddWidget,
    }
  };
};
