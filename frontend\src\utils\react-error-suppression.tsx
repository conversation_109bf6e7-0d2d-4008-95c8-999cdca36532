/**
 * React Error Suppression Utilities
 * Handles React warnings and errors gracefully
 */

// Store original console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Patterns to suppress
const SUPPRESSED_ERROR_PATTERNS = [
  // React 18 defaultProps warnings from third-party libraries
  /Warning: Failed prop type/,
  /Warning: React does not recognize the/,
  /Warning: componentWillReceiveProps has been renamed/,
  /Warning: componentWillMount has been renamed/,
  /Warning: componentWillUpdate has been renamed/,
  /defaultProps will be removed/,
  /Support for defaultProps will be removed/,
  
  // Framer Motion warnings
  /Warning: React does not recognize the `.*` prop on a DOM element/,
  /Warning: Received `true` for a non-boolean attribute/,
  
  // React Beautiful DnD warnings
  /react-beautiful-dnd/,
  /Connect\(Droppable\): Support for defaultProps will be removed/,
  /Support for defaultProps will be removed from memo components/,
  
  // Development-only warnings that are safe to suppress in production
  /Warning: Each child in a list should have a unique "key" prop/,
  
  // Third-party library warnings that we can't control
  /Warning: validateDOMNesting/,
  /Warning: Function components cannot be given refs/,
];

const SUPPRESSED_WARN_PATTERNS = [
  // React DevTools warnings
  /Download the React DevTools/,
  
  // Performance warnings that are expected during development
  /Warning: Maximum update depth exceeded/,
  
  // Third-party library warnings
  /deprecated/i,
];

/**
 * Enhanced console error handler
 */
function enhancedConsoleError(...args: any[]) {
  const message = args.join(' ');
  
  // Check if this error should be suppressed
  const shouldSuppress = SUPPRESSED_ERROR_PATTERNS.some(pattern => 
    pattern.test(message)
  );
  
  if (shouldSuppress) {
    // In development, log as debug instead of error
    if (import.meta.env.DEV) {
      console.debug('Suppressed React error:', message);
    }
    return;
  }
  
  // Call original console.error for non-suppressed errors
  originalConsoleError.apply(console, args);
}

/**
 * Enhanced console warn handler
 */
function enhancedConsoleWarn(...args: any[]) {
  const message = args.join(' ');
  
  // Check if this warning should be suppressed
  const shouldSuppress = SUPPRESSED_WARN_PATTERNS.some(pattern => 
    pattern.test(message)
  );
  
  if (shouldSuppress) {
    // In development, log as debug instead of warn
    if (import.meta.env.DEV) {
      console.debug('Suppressed React warning:', message);
    }
    return;
  }
  
  // Call original console.warn for non-suppressed warnings
  originalConsoleWarn.apply(console, args);
}

/**
 * Setup error suppression
 */
export function setupReactErrorSuppression() {
  // Only suppress in production or when explicitly enabled
  if (import.meta.env.PROD || import.meta.env.VITE_SUPPRESS_WARNINGS === 'true') {
    console.error = enhancedConsoleError;
    console.warn = enhancedConsoleWarn;

    console.debug('React error suppression enabled');
  }
}

/**
 * Restore original console methods
 */
export function restoreConsole() {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
}

// React import
import React from 'react';

/**
 * React component error boundary helper
 */
export class ReactErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error to monitoring service
    console.error('React Error Boundary caught error:', error, errorInfo);

    // You can also log to an error reporting service here
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(error, {
        contexts: {
          react: {
            componentStack: errorInfo.componentStack,
          },
        },
      });
    }
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;

      if (FallbackComponent && this.state.error) {
        return <FallbackComponent error={this.state.error} />;
      }

      return (
        <div className="p-4 text-center text-red-600 bg-red-50 rounded-lg border border-red-200">
          <h3 className="font-semibold mb-2">Something went wrong</h3>
          <p className="text-sm">
            {this.state.error?.message || 'An unexpected error occurred'}
          </p>
          <button
            className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
            onClick={() => this.setState({ hasError: false, error: null })}
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook for handling component cleanup
 */
export function useComponentCleanup(cleanup: () => void) {
  React.useEffect(() => {
    return cleanup;
  }, [cleanup]);
}

/**
 * Hook for safe async operations
 */
export function useSafeAsync<T>() {
  const isMountedRef = React.useRef(true);
  
  React.useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  
  const safeSetState = React.useCallback((setter: React.Dispatch<React.SetStateAction<T>>) => {
    return (value: React.SetStateAction<T>) => {
      if (isMountedRef.current) {
        setter(value);
      }
    };
  }, []);
  
  return { isMounted: () => isMountedRef.current, safeSetState };
}

/**
 * Hook for preventing memory leaks in async operations
 */
export function useAbortController() {
  const abortControllerRef = React.useRef<AbortController>();
  
  React.useEffect(() => {
    abortControllerRef.current = new AbortController();
    
    return () => {
      abortControllerRef.current?.abort();
    };
  }, []);
  
  return abortControllerRef.current;
}

/**
 * Higher-order component for error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<{ error: Error }>
) {
  const WrappedComponent = (props: P) => (
    <ReactErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ReactErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

// Auto-setup on module load
if (typeof window !== 'undefined') {
  setupReactErrorSuppression();
}
