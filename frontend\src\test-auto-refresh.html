<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Refresh Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Auto-Refresh Data Source Test</h1>
        
        <div class="test-section info">
            <h3>📋 Test Instructions</h3>
            <p>This test simulates the auto-refresh functionality for data sources:</p>
            <ol>
                <li>Click "Simulate File Upload" to trigger a file upload event</li>
                <li>Click "Simulate Data Source Creation" to trigger a data source creation event</li>
                <li>Watch the log to see refresh events being triggered</li>
                <li>In the real app, these events would automatically refresh the "Attach Data" dialog</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧪 Test Controls</h3>
            <button onclick="simulateFileUpload()">📁 Simulate File Upload</button>
            <button onclick="simulateDataSourceCreation()">🗄️ Simulate Data Source Creation</button>
            <button onclick="clearLog()">🧹 Clear Log</button>
        </div>

        <div class="test-section">
            <h3>📊 Event Log</h3>
            <div id="log" class="log">Ready to test auto-refresh functionality...\n</div>
        </div>

        <div class="test-section success">
            <h3>✅ Expected Behavior</h3>
            <p>When you upload a file or create a data source in the Datagenius app:</p>
            <ul>
                <li>The "Attach Data" dialog will automatically refresh</li>
                <li>New data sources will appear immediately without manual refresh</li>
                <li>Users will see their newly uploaded files right away</li>
                <li>The refresh happens in the background with visual feedback</li>
            </ul>
        </div>
    </div>

    <script>
        // Simulate the data source refresh system
        class MockDataSourceRefreshEmitter {
            constructor() {
                this.listeners = new Set();
                this.refreshCounter = 0;
            }

            subscribe(callback) {
                this.listeners.add(callback);
                return () => {
                    this.listeners.delete(callback);
                };
            }

            emit() {
                this.refreshCounter++;
                this.listeners.forEach(callback => callback());
            }

            getCounter() {
                return this.refreshCounter;
            }
        }

        // Global instance
        const mockEmitter = new MockDataSourceRefreshEmitter();

        // Mock hooks
        function useDataSourceRefreshTrigger() {
            return {
                triggerRefresh: () => {
                    log('🔄 Triggering data source refresh...');
                    mockEmitter.emit();
                }
            };
        }

        function useAutoRefreshOnUpload() {
            const { triggerRefresh } = useDataSourceRefreshTrigger();

            return {
                handleFileUploaded: (uploadedFile) => {
                    log(`📁 File uploaded: ${uploadedFile.name}`);
                    log('⏱️ Waiting 500ms before triggering refresh...');
                    setTimeout(() => {
                        triggerRefresh();
                    }, 500);
                },
                handleDataSourceCreated: (dataSource) => {
                    log(`🗄️ Data source created: ${dataSource.name}`);
                    log('⏱️ Waiting 300ms before triggering refresh...');
                    setTimeout(() => {
                        triggerRefresh();
                    }, 300);
                }
            };
        }

        // Subscribe to refresh events
        mockEmitter.subscribe(() => {
            log('✨ Data source refresh event triggered!');
            log('📋 In the real app, this would refresh the DataSourceSelector component');
            log('🔄 Users would see new data sources immediately');
            log('---');
        });

        // Initialize hooks
        const { handleFileUploaded, handleDataSourceCreated } = useAutoRefreshOnUpload();

        // Test functions
        function simulateFileUpload() {
            const mockFile = {
                name: `test-file-${Date.now()}.csv`,
                id: `file-${Math.random().toString(36).substr(2, 9)}`,
                size: Math.floor(Math.random() * 1000000) + 1000
            };

            log(`🚀 Simulating file upload: ${mockFile.name}`);
            handleFileUploaded(mockFile);
        }

        function simulateDataSourceCreation() {
            const mockDataSource = {
                name: `Data Source ${Date.now()}`,
                id: `ds-${Math.random().toString(36).substr(2, 9)}`,
                type: 'file'
            };

            log(`🚀 Simulating data source creation: ${mockDataSource.name}`);
            handleDataSourceCreated(mockDataSource);
        }

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = 'Ready to test auto-refresh functionality...\n';
        }

        // Initial log
        log('🎯 Auto-refresh system initialized');
        log('📡 Listening for file upload and data source creation events');
        log('---');
    </script>
</body>
</html>
