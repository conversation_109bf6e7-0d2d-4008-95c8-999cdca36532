/**
 * Utilities for handling form schemas in YAML format.
 * 
 * This module provides functions for loading YAML schemas and converting them to Zod schemas.
 */

import { z } from 'zod';
import YAML from 'js-yaml';

/**
 * Type definitions for schema properties
 */
type SchemaPropertyType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'integer' 
  | 'array' 
  | 'object' 
  | 'enum';

interface SchemaProperty {
  type: SchemaPropertyType;
  description?: string;
  required?: boolean;
  default?: any;
  minimum?: number;
  maximum?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  format?: string;
  enum?: any[];
  items?: SchemaProperty;
  properties?: Record<string, SchemaProperty>;
}

interface FormSchema {
  title?: string;
  description?: string;
  properties: Record<string, SchemaProperty>;
  required?: string[];
}

/**
 * Convert a YAML string to a FormSchema object
 * 
 * @param yamlString - YAML schema string
 * @returns Parsed form schema
 */
export function parseYamlSchema(yamlString: string): FormSchema {
  try {
    const schema = YAML.load(yamlString) as FormSchema;
    
    if (!schema || typeof schema !== 'object') {
      throw new Error('Invalid schema format: schema must be an object');
    }
    
    if (!schema.properties || typeof schema.properties !== 'object') {
      throw new Error('Invalid schema format: missing properties object');
    }
    
    return schema;
  } catch (error) {
    console.error('Error parsing YAML schema:', error);
    throw error;
  }
}

/**
 * Convert a property definition to a Zod schema
 * 
 * @param property - Schema property definition
 * @param required - Whether the property is required
 * @returns Zod schema for the property
 */
function propertyToZodSchema(property: SchemaProperty, required: boolean = false): z.ZodTypeAny {
  switch (property.type) {
    case 'string':
      let stringSchema = z.string();
      
      if (property.minLength !== undefined) {
        stringSchema = stringSchema.min(property.minLength, 
          `Must be at least ${property.minLength} characters`);
      }
      
      if (property.maxLength !== undefined) {
        stringSchema = stringSchema.max(property.maxLength, 
          `Must be at most ${property.maxLength} characters`);
      }
      
      if (property.pattern) {
        stringSchema = stringSchema.regex(new RegExp(property.pattern), 
          `Must match pattern: ${property.pattern}`);
      }
      
      if (property.format === 'email') {
        stringSchema = stringSchema.email('Must be a valid email address');
      }
      
      if (property.format === 'uri') {
        stringSchema = stringSchema.url('Must be a valid URL');
      }
      
      if (property.default !== undefined) {
        stringSchema = stringSchema.default(property.default);
      }
      
      return required ? stringSchema : stringSchema.optional();
      
    case 'number':
    case 'integer':
      let numberSchema = property.type === 'integer' ? z.number().int() : z.number();
      
      if (property.minimum !== undefined) {
        numberSchema = numberSchema.min(property.minimum, 
          `Must be at least ${property.minimum}`);
      }
      
      if (property.maximum !== undefined) {
        numberSchema = numberSchema.max(property.maximum, 
          `Must be at most ${property.maximum}`);
      }
      
      if (property.default !== undefined) {
        numberSchema = numberSchema.default(property.default);
      }
      
      return required ? numberSchema : numberSchema.optional();
      
    case 'boolean':
      let boolSchema = z.boolean();
      
      if (property.default !== undefined) {
        boolSchema = boolSchema.default(property.default);
      }
      
      return required ? boolSchema : boolSchema.optional();
      
    case 'enum':
      if (!property.enum || !Array.isArray(property.enum)) {
        throw new Error('Enum type must have an enum array property');
      }
      
      // Handle different enum value types
      if (property.enum.every(item => typeof item === 'string')) {
        const enumSchema = z.enum(property.enum as [string, ...string[]]);
        return required ? enumSchema : enumSchema.optional();
      } else {
        // For mixed types, use union
        const enumSchema = z.union(property.enum.map(item => z.literal(item)));
        return required ? enumSchema : enumSchema.optional();
      }
      
    case 'array':
      if (!property.items) {
        throw new Error('Array type must have an items property');
      }
      
      const itemSchema = propertyToZodSchema(property.items, true);
      let arraySchema = z.array(itemSchema);
      
      if (property.default !== undefined) {
        arraySchema = arraySchema.default(property.default);
      }
      
      return required ? arraySchema : arraySchema.optional();
      
    case 'object':
      if (!property.properties) {
        // Generic object if no properties defined
        return required ? z.record(z.any()) : z.record(z.any()).optional();
      }
      
      const objectSchema = formSchemaToZod({
        properties: property.properties,
        required: property.required
      });
      
      return required ? objectSchema : objectSchema.optional();
      
    default:
      // Default to any for unknown types
      return required ? z.any() : z.any().optional();
  }
}

/**
 * Convert a FormSchema to a Zod schema
 * 
 * @param schema - Form schema definition
 * @returns Zod schema for the form
 */
export function formSchemaToZod(schema: FormSchema): z.ZodObject<any> {
  const schemaMap: Record<string, z.ZodTypeAny> = {};
  const requiredFields = new Set(schema.required || []);
  
  for (const [key, property] of Object.entries(schema.properties)) {
    const isRequired = requiredFields.has(key);
    schemaMap[key] = propertyToZodSchema(property, isRequired);
  }
  
  return z.object(schemaMap);
}

/**
 * Load a YAML schema file and convert it to a Zod schema
 * 
 * @param schemaPath - Path to the YAML schema file
 * @returns Promise resolving to a Zod schema
 */
export async function loadYamlSchemaAsZod(schemaPath: string): Promise<z.ZodObject<any>> {
  try {
    const response = await fetch(schemaPath);
    if (!response.ok) {
      throw new Error(`Failed to load schema: ${response.statusText}`);
    }
    
    const yamlString = await response.text();
    const schema = parseYamlSchema(yamlString);
    return formSchemaToZod(schema);
  } catch (error) {
    console.error('Error loading YAML schema:', error);
    throw error;
  }
}
