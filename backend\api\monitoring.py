"""
API endpoints for model usage monitoring.

This module provides API endpoints for monitoring model usage.
"""

import logging
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

import sys
import os

# Add the parent directory to sys.path to allow importing from backend
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import model provider utilities
from agents.utils.model_providers.monitoring import get_usage_stats, get_request_history, clear_usage_history
from agents.utils.model_providers.cache import get_cache_stats, clear_cache

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/monitoring",
    tags=["monitoring"],
    responses={404: {"description": "Not found"}}
)


class UsageStatsResponse(BaseModel):
    """Response model for usage statistics."""
    total_requests: int
    total_tokens: int
    total_cost: float
    total_latency: float
    error_count: int
    providers: Dict[str, Any]


class RequestHistoryResponse(BaseModel):
    """Response model for request history."""
    requests: List[Dict[str, Any]]


class CacheStatsResponse(BaseModel):
    """Response model for cache statistics."""
    size: int
    max_size: int
    ttl: int
    providers: Dict[str, int]
    avg_age: float
    max_age: float
    min_age: float


@router.get("/usage", response_model=UsageStatsResponse)
async def get_model_usage_stats():
    """
    Get model usage statistics.
    """
    try:
        stats = await get_usage_stats()
        return stats
    except Exception as e:
        logger.error(f"Error getting usage statistics: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting usage statistics: {str(e)}"
        )


@router.get("/history", response_model=RequestHistoryResponse)
async def get_model_request_history(limit: int = 100):
    """
    Get model request history.
    """
    try:
        history = await get_request_history(limit)
        return {"requests": history}
    except Exception as e:
        logger.error(f"Error getting request history: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting request history: {str(e)}"
        )


@router.get("/cache", response_model=CacheStatsResponse)
async def get_model_cache_stats():
    """
    Get model cache statistics.
    """
    try:
        stats = await get_cache_stats()
        return stats
    except Exception as e:
        logger.error(f"Error getting cache statistics: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting cache statistics: {str(e)}"
        )


@router.post("/clear-history")
async def clear_model_history():
    """
    Clear model request history.
    """
    try:
        await clear_usage_history()
        return {"message": "Model request history cleared successfully"}
    except Exception as e:
        logger.error(f"Error clearing request history: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error clearing request history: {str(e)}"
        )


@router.post("/clear-cache")
async def clear_model_cache():
    """
    Clear model response cache.
    """
    try:
        await clear_cache()
        return {"message": "Model response cache cleared successfully"}
    except Exception as e:
        logger.error(f"Error clearing response cache: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error clearing response cache: {str(e)}"
        )
