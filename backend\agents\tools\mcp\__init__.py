"""
MCP Tools System - Complete Production-Ready Implementation.

This module provides a comprehensive, extensible, elegant, and production-ready
MCP tools system with full agent awareness, caching, monitoring, security,
validation, analytics, error handling, and testing capabilities.
"""

import asyncio
import logging
import importlib
from typing import Dict, Any, List, Optional

# Core components
from .base import BaseMCPTool

# Alias for backward compatibility
MCPTool = BaseMCPTool

# System components - Import with error handling for missing modules
try:
    from .integration.system_integration import (
        MCPToolRegistry,
        EnhancedMCPTool,
        IntegrationConfig,
        get_tool_registry,
        create_production_config,
        create_development_config
    )
    INTEGRATION_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Advanced integration features not available: {e}")
    INTEGRATION_AVAILABLE = False
    # No fallback registry - use AVAILABLE_TOOLS directly
    MCPToolRegistry = None

# Individual tools - Import with error handling
AVAILABLE_TOOLS = {}

tool_imports = [
    ("conversation_tool", "ConversationTool"),
    ("intent_detection", "IntentDetectionTool"),
    ("intent_analysis", "IntentAnalysisTool"),
    ("language_detection", "LanguageDetectionTool"),
    ("text_processing", "TextProcessingTool"),
    ("text_classification", "TextClassificationTool"),
    ("sentiment_analysis", "SentimentAnalysisTool"),
    ("data_access", "DataAccessTool"),
    ("statistical_analysis", "StatisticalAnalysisTool"),
    ("pandasai_analysis", "PandasAIAnalysisTool"),
    ("pandasai_visualization", "PandasAIVisualizationTool"),
    ("data_visualization", "DataVisualizationTool"),
    ("interactive_chart_data", "InteractiveChartDataTool"),
    ("marketing_strategy_generation", "MarketingStrategyGenerationTool"),
    ("code_execution_tool", "CodeExecutionTool"),
    ("natural_language_query", "NaturalLanguageQueryTool")
]

for module_name, class_name in tool_imports:
    try:
        module = importlib.import_module(f".{module_name}", package=__name__)
        tool_class = getattr(module, class_name)
        AVAILABLE_TOOLS[class_name] = tool_class
        globals()[class_name] = tool_class
    except ImportError as e:
        logging.warning(f"Tool {class_name} not available: {e}")

# System components - Import with error handling
try:
    from .caching.intelligent_cache import get_cache
    CACHING_AVAILABLE = True
except ImportError:
    CACHING_AVAILABLE = False

try:
    from .monitoring.performance_monitor import get_performance_monitor
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False

try:
    from .error_handling.robust_error_handler import get_error_handler
    ERROR_HANDLING_AVAILABLE = True
except ImportError:
    ERROR_HANDLING_AVAILABLE = False

try:
    from .security.access_control import get_access_controller, UserPermissions, PermissionLevel
    SECURITY_AVAILABLE = True
except ImportError:
    SECURITY_AVAILABLE = False

try:
    from .validation.input_validator import get_input_validator
    VALIDATION_AVAILABLE = True
except ImportError:
    VALIDATION_AVAILABLE = False

try:
    from .analytics.usage_analytics import get_usage_analytics
    ANALYTICS_AVAILABLE = True
except ImportError:
    ANALYTICS_AVAILABLE = False

try:
    from .testing.test_framework import get_test_framework
    TESTING_AVAILABLE = True
except ImportError:
    TESTING_AVAILABLE = False

try:
    from .composition.tool_chain import ToolChain, ChainBuilder
    COMPOSITION_AVAILABLE = True
except ImportError:
    COMPOSITION_AVAILABLE = False

logger = logging.getLogger(__name__)

# Version information
__version__ = "1.0.0"
__author__ = "Datagenius Development Team"
__description__ = "Production-ready MCP tools system with comprehensive features"

# Export main classes and functions
__all__ = [
    # Core classes
    "BaseMCPTool",
    "MCPTool",  # Alias for backward compatibility
    "MCPToolRegistry",

    # Main functions
    "initialize_mcp_system",
    "get_tool_registry",
    "register_all_tools",
    "get_system_status",
    "cleanup_system",
    "get_system_info"
]

# Add available tools to exports
__all__.extend(AVAILABLE_TOOLS.keys())

# Add available system components to exports
if INTEGRATION_AVAILABLE:
    __all__.extend(["EnhancedMCPTool", "IntegrationConfig", "create_production_config", "create_development_config"])

if SECURITY_AVAILABLE:
    __all__.extend(["UserPermissions", "PermissionLevel"])

if COMPOSITION_AVAILABLE:
    __all__.extend(["ToolChain", "ChainBuilder"])


async def initialize_mcp_system(
    config: Optional['IntegrationConfig'] = None,
    environment: str = "production"
) -> Any:
    """
    Initialize the complete MCP tools system.

    Args:
        config: Custom configuration (optional)
        environment: Environment type ("production" or "development")

    Returns:
        Initialized tool registry
    """
    logger.info(f"Initializing MCP tools system for {environment} environment...")

    if INTEGRATION_AVAILABLE:
        # Use enhanced system
        if config is None:
            if environment == "production":
                config = create_production_config()
            else:
                config = create_development_config()

        registry = get_tool_registry(config)
        await register_all_tools(registry)

        # Initialize system components
        components_initialized = []

        if config.enable_caching and CACHING_AVAILABLE:
            get_cache()
            components_initialized.append("caching")

        if config.enable_monitoring and MONITORING_AVAILABLE:
            get_performance_monitor()
            components_initialized.append("monitoring")

        if config.enable_error_handling and ERROR_HANDLING_AVAILABLE:
            get_error_handler()
            components_initialized.append("error_handling")

        if config.enable_security and SECURITY_AVAILABLE:
            get_access_controller()
            components_initialized.append("security")

        if config.enable_validation and VALIDATION_AVAILABLE:
            get_input_validator()
            components_initialized.append("validation")

        if config.enable_analytics and ANALYTICS_AVAILABLE:
            get_usage_analytics()
            components_initialized.append("analytics")

        if config.enable_testing and TESTING_AVAILABLE:
            get_test_framework()
            components_initialized.append("testing")

        logger.info(f"✅ Initialized components: {', '.join(components_initialized)}")

    else:
        # Use basic system
        registry = MCPToolRegistry()
        await register_all_tools(registry)
        logger.info("✅ Basic MCP tools system initialized")

    logger.info("🎉 MCP tools system initialization completed successfully!")
    logger.info(f"📊 Registered {len(registry.list_tools() if hasattr(registry, 'list_tools') else AVAILABLE_TOOLS)} tools")

    return registry


async def register_all_tools(registry: Any) -> Dict[str, Any]:
    """
    Register all available MCP tools with the registry.

    Args:
        registry: Tool registry instance

    Returns:
        Dictionary of registered tools
    """
    logger.info("Registering all available MCP tools...")

    registered_tools = {}

    for tool_name, tool_class in AVAILABLE_TOOLS.items():
        try:
            # Initialize base tool
            base_tool = tool_class()

            # Register with registry
            if INTEGRATION_AVAILABLE and hasattr(registry, 'register_tool'):
                # Enhanced registry
                enhanced_tool = registry.register_tool(base_tool)
                registered_tools[base_tool.name] = enhanced_tool
            else:
                # Basic registry
                registry.register_tool(base_tool)
                registered_tools[base_tool.name] = base_tool

            logger.info(f"✅ Registered: {base_tool.name}")

        except Exception as e:
            logger.error(f"❌ Failed to register {tool_name}: {e}")

    logger.info(f"📋 Successfully registered {len(registered_tools)} tools")
    return registered_tools


async def get_system_status() -> Dict[str, Any]:
    """Get comprehensive system status."""
    if INTEGRATION_AVAILABLE:
        registry = get_tool_registry()
        if hasattr(registry, 'get_system_status'):
            return registry.get_system_status()

    # Basic status
    return {
        "timestamp": "2024-01-01T00:00:00Z",
        "tools": {
            "available": len(AVAILABLE_TOOLS),
            "tool_names": list(AVAILABLE_TOOLS.keys())
        },
        "features": {
            "integration": INTEGRATION_AVAILABLE,
            "caching": CACHING_AVAILABLE,
            "monitoring": MONITORING_AVAILABLE,
            "security": SECURITY_AVAILABLE,
            "analytics": ANALYTICS_AVAILABLE,
            "testing": TESTING_AVAILABLE,
            "composition": COMPOSITION_AVAILABLE
        }
    }


async def cleanup_system() -> None:
    """Clean up all system resources."""
    logger.info("Starting system cleanup...")

    if INTEGRATION_AVAILABLE:
        registry = get_tool_registry()
        if hasattr(registry, 'cleanup'):
            await registry.cleanup()

    logger.info("✅ System cleanup completed")


def get_system_info() -> Dict[str, Any]:
    """Get system information."""
    return {
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "available_tools": len(AVAILABLE_TOOLS),
        "features": {
            "agent_awareness": True,
            "intelligent_caching": CACHING_AVAILABLE,
            "performance_monitoring": MONITORING_AVAILABLE,
            "error_handling": ERROR_HANDLING_AVAILABLE,
            "security_control": SECURITY_AVAILABLE,
            "input_validation": VALIDATION_AVAILABLE,
            "usage_analytics": ANALYTICS_AVAILABLE,
            "tool_composition": COMPOSITION_AVAILABLE,
            "testing_framework": TESTING_AVAILABLE,
            "full_integration": INTEGRATION_AVAILABLE
        },
        "components": {
            "tools": len(AVAILABLE_TOOLS),
            "system_modules": sum([
                CACHING_AVAILABLE,
                MONITORING_AVAILABLE,
                ERROR_HANDLING_AVAILABLE,
                SECURITY_AVAILABLE,
                VALIDATION_AVAILABLE,
                ANALYTICS_AVAILABLE,
                TESTING_AVAILABLE,
                COMPOSITION_AVAILABLE
            ])
        }
    }


# Convenience function for quick setup
async def quick_setup(environment: str = "development") -> Any:
    """
    Quick setup for development or testing.

    Args:
        environment: "development" or "production"

    Returns:
        Configured tool registry
    """
    return await initialize_mcp_system(environment=environment)


# Legacy compatibility
def register_mcp_tools():
    """Legacy function for backward compatibility."""
    logger.warning("Legacy register_mcp_tools called - this function is deprecated. Use initialize_mcp_system() instead")


# Legacy compatibility removed - use initialize_mcp_system() instead
logger.info("ℹ️  Use initialize_mcp_system() for tool registration")

logger.info(f"🚀 MCP Tools System v{__version__} loaded with {len(AVAILABLE_TOOLS)} tools")
