"""
Comprehensive test runner for marketing agent system.

This module provides a test runner that executes all tests and generates
comprehensive reports on system functionality and performance.
"""

import os
import sys
import time
import asyncio
import logging
import subprocess
from typing import Dict, Any, List, Optional
from pathlib import Path
import json

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.agents.marketing_agent.config import get_config
from backend.agents.marketing_agent.cache import initialize_cache, get_cache
from backend.agents.marketing_agent.memory_manager import initialize_memory_manager, get_memory_manager
from backend.agents.marketing_agent.monitoring import monitor
from backend.agents.marketing_agent.analytics import initialize_analytics, get_analytics
from backend.agents.marketing_agent.exceptions import error_handler

logger = logging.getLogger(__name__)


class TestResult:
    """Represents the result of a test execution."""
    
    def __init__(self, name: str, passed: bool, duration: float, details: Optional[str] = None):
        self.name = name
        self.passed = passed
        self.duration = duration
        self.details = details
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'name': self.name,
            'passed': self.passed,
            'duration': self.duration,
            'details': self.details,
            'timestamp': self.timestamp
        }


class MarketingAgentTestRunner:
    """Comprehensive test runner for marketing agent system."""
    
    def __init__(self):
        self.results: List[TestResult] = []
        self.start_time = 0
        self.end_time = 0
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results."""
        self.start_time = time.time()
        logger.info("Starting comprehensive marketing agent test suite")
        
        try:
            # Initialize systems for testing
            await self._initialize_test_systems()
            
            # Run unit tests
            await self._run_unit_tests()
            
            # Run integration tests
            await self._run_integration_tests()
            
            # Run performance tests
            await self._run_performance_tests()
            
            # Run system health checks
            await self._run_health_checks()
            
            # Cleanup test systems
            await self._cleanup_test_systems()
            
        except Exception as e:
            logger.error(f"Error running tests: {e}")
            self.results.append(TestResult(
                "test_runner_error",
                False,
                0,
                f"Test runner error: {str(e)}"
            ))
        
        self.end_time = time.time()
        return self._generate_report()
    
    async def _initialize_test_systems(self) -> None:
        """Initialize systems for testing."""
        try:
            # Initialize configuration
            config = get_config()
            
            # Initialize cache with test configuration
            cache_config = {
                'backend': 'memory',
                'max_size': 100,
                'ttl': 300
            }
            initialize_cache(cache_config)
            
            # Initialize memory manager with test configuration
            memory_config = {
                'max_memory_mb': 256,
                'conversation_timeout': 300,
                'cleanup_interval': 60,
                'max_conversations': 50
            }
            memory_manager = initialize_memory_manager(memory_config)
            await memory_manager.start()
            
            # Initialize analytics with test configuration
            analytics_config = {
                'buffer_size': 100,
                'flush_interval': 60,
                'enable_persistence': False
            }
            analytics = initialize_analytics(analytics_config)
            await analytics.start()
            
            # Start monitoring
            monitor.start_monitoring(interval=5.0)
            
            self.results.append(TestResult(
                "system_initialization",
                True,
                time.time() - self.start_time,
                "All systems initialized successfully"
            ))
            
        except Exception as e:
            self.results.append(TestResult(
                "system_initialization",
                False,
                time.time() - self.start_time,
                f"Initialization failed: {str(e)}"
            ))
    
    async def _run_unit_tests(self) -> None:
        """Run unit tests using pytest."""
        try:
            test_start = time.time()
            
            # Run intent detection tests
            result = subprocess.run([
                sys.executable, '-m', 'pytest',
                'backend/agents/marketing_agent/tests/test_intent_detection.py',
                '-v', '--tb=short'
            ], capture_output=True, text=True, cwd=project_root)
            
            duration = time.time() - test_start
            
            if result.returncode == 0:
                self.results.append(TestResult(
                    "unit_tests_intent_detection",
                    True,
                    duration,
                    "All intent detection tests passed"
                ))
            else:
                self.results.append(TestResult(
                    "unit_tests_intent_detection",
                    False,
                    duration,
                    f"Intent detection tests failed: {result.stderr}"
                ))
            
        except Exception as e:
            self.results.append(TestResult(
                "unit_tests_intent_detection",
                False,
                0,
                f"Error running unit tests: {str(e)}"
            ))
    
    async def _run_integration_tests(self) -> None:
        """Run integration tests."""
        try:
            test_start = time.time()
            
            # Run integration tests
            result = subprocess.run([
                sys.executable, '-m', 'pytest',
                'backend/agents/marketing_agent/tests/test_integration.py',
                '-v', '--tb=short', '-m', 'not slow'
            ], capture_output=True, text=True, cwd=project_root)
            
            duration = time.time() - test_start
            
            if result.returncode == 0:
                self.results.append(TestResult(
                    "integration_tests",
                    True,
                    duration,
                    "All integration tests passed"
                ))
            else:
                self.results.append(TestResult(
                    "integration_tests",
                    False,
                    duration,
                    f"Integration tests failed: {result.stderr}"
                ))
            
        except Exception as e:
            self.results.append(TestResult(
                "integration_tests",
                False,
                0,
                f"Error running integration tests: {str(e)}"
            ))
    
    async def _run_performance_tests(self) -> None:
        """Run performance tests."""
        try:
            # Test cache performance
            await self._test_cache_performance()
            
            # Test memory management performance
            await self._test_memory_performance()
            
            # Test monitoring performance
            await self._test_monitoring_performance()
            
        except Exception as e:
            self.results.append(TestResult(
                "performance_tests",
                False,
                0,
                f"Error running performance tests: {str(e)}"
            ))
    
    async def _test_cache_performance(self) -> None:
        """Test cache system performance."""
        test_start = time.time()
        
        try:
            cache = get_cache()
            if not cache:
                raise Exception("Cache not initialized")
            
            # Test cache operations
            test_data = {"test": "data", "timestamp": time.time()}
            
            # Test set performance
            set_start = time.time()
            for i in range(100):
                await cache.set(f"test_cache_{i}", {"test_key": f"key_{i}"}, test_data)
            set_duration = time.time() - set_start
            
            # Test get performance
            get_start = time.time()
            for i in range(100):
                await cache.get(f"test_cache_{i}", {"test_key": f"key_{i}"})
            get_duration = time.time() - get_start
            
            total_duration = time.time() - test_start
            
            # Performance thresholds
            if set_duration < 1.0 and get_duration < 0.5:
                self.results.append(TestResult(
                    "cache_performance",
                    True,
                    total_duration,
                    f"Cache performance: set={set_duration:.3f}s, get={get_duration:.3f}s"
                ))
            else:
                self.results.append(TestResult(
                    "cache_performance",
                    False,
                    total_duration,
                    f"Cache performance below threshold: set={set_duration:.3f}s, get={get_duration:.3f}s"
                ))
            
        except Exception as e:
            self.results.append(TestResult(
                "cache_performance",
                False,
                time.time() - test_start,
                f"Cache performance test failed: {str(e)}"
            ))
    
    async def _test_memory_performance(self) -> None:
        """Test memory management performance."""
        test_start = time.time()
        
        try:
            memory_manager = get_memory_manager()
            if not memory_manager:
                raise Exception("Memory manager not initialized")
            
            # Test conversation registration performance
            reg_start = time.time()
            for i in range(50):
                await memory_manager.register_conversation(
                    f"test_conv_{i}",
                    f"test_user_{i}",
                    {"test": f"data_{i}"}
                )
            reg_duration = time.time() - reg_start
            
            # Test memory stats
            stats = await memory_manager.get_memory_stats()
            
            total_duration = time.time() - test_start
            
            if reg_duration < 2.0 and stats.active_conversations == 50:
                self.results.append(TestResult(
                    "memory_performance",
                    True,
                    total_duration,
                    f"Memory performance: registration={reg_duration:.3f}s, conversations={stats.active_conversations}"
                ))
            else:
                self.results.append(TestResult(
                    "memory_performance",
                    False,
                    total_duration,
                    f"Memory performance issues: registration={reg_duration:.3f}s, conversations={stats.active_conversations}"
                ))
            
        except Exception as e:
            self.results.append(TestResult(
                "memory_performance",
                False,
                time.time() - test_start,
                f"Memory performance test failed: {str(e)}"
            ))
    
    async def _test_monitoring_performance(self) -> None:
        """Test monitoring system performance."""
        test_start = time.time()
        
        try:
            # Record some operations
            for i in range(20):
                monitor.record_operation(f"test_op_{i}", 0.1, success=True)
            
            # Get performance summary
            summary = monitor.get_performance_summary()
            
            total_duration = time.time() - test_start
            
            if summary['response_time_stats']['count'] >= 20:
                self.results.append(TestResult(
                    "monitoring_performance",
                    True,
                    total_duration,
                    f"Monitoring recorded {summary['response_time_stats']['count']} operations"
                ))
            else:
                self.results.append(TestResult(
                    "monitoring_performance",
                    False,
                    total_duration,
                    f"Monitoring performance issues: only {summary['response_time_stats']['count']} operations recorded"
                ))
            
        except Exception as e:
            self.results.append(TestResult(
                "monitoring_performance",
                False,
                time.time() - test_start,
                f"Monitoring performance test failed: {str(e)}"
            ))
    
    async def _run_health_checks(self) -> None:
        """Run system health checks."""
        try:
            # Cache health check
            cache = get_cache()
            if cache:
                cache_health = await cache.health_check()
                self.results.append(TestResult(
                    "cache_health_check",
                    cache_health['status'] == 'healthy',
                    0,
                    f"Cache status: {cache_health['status']}"
                ))
            
            # Memory manager health check
            memory_manager = get_memory_manager()
            if memory_manager:
                memory_stats = await memory_manager.get_memory_stats()
                self.results.append(TestResult(
                    "memory_health_check",
                    memory_stats.total_memory_mb > 0,
                    0,
                    f"Memory usage: {memory_stats.process_memory_mb:.1f}MB"
                ))
            
            # Monitor health check
            monitor_health = monitor.health_check()
            self.results.append(TestResult(
                "monitor_health_check",
                monitor_health['status'] in ['healthy', 'degraded'],
                0,
                f"Monitor status: {monitor_health['status']}"
            ))
            
        except Exception as e:
            self.results.append(TestResult(
                "health_checks",
                False,
                0,
                f"Health check failed: {str(e)}"
            ))
    
    async def _cleanup_test_systems(self) -> None:
        """Cleanup test systems."""
        try:
            # Stop memory manager
            memory_manager = get_memory_manager()
            if memory_manager:
                await memory_manager.stop()
            
            # Stop analytics
            analytics = get_analytics()
            if analytics:
                await analytics.stop()
            
            # Stop monitoring
            monitor.stop_monitoring()
            
            self.results.append(TestResult(
                "system_cleanup",
                True,
                0,
                "All systems cleaned up successfully"
            ))
            
        except Exception as e:
            self.results.append(TestResult(
                "system_cleanup",
                False,
                0,
                f"Cleanup failed: {str(e)}"
            ))
    
    def _generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results if result.passed)
        failed_tests = total_tests - passed_tests
        
        total_duration = self.end_time - self.start_time
        
        return {
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'total_duration': total_duration,
                'timestamp': time.time()
            },
            'results': [result.to_dict() for result in self.results],
            'failed_tests': [
                result.to_dict() for result in self.results if not result.passed
            ]
        }


async def main():
    """Main test runner function."""
    runner = MarketingAgentTestRunner()
    report = await runner.run_all_tests()
    
    # Print summary
    summary = report['summary']
    print(f"\n{'='*60}")
    print("MARKETING AGENT TEST SUITE RESULTS")
    print(f"{'='*60}")
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Passed: {summary['passed_tests']}")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Success Rate: {summary['success_rate']:.1f}%")
    print(f"Total Duration: {summary['total_duration']:.2f}s")
    
    # Print failed tests
    if report['failed_tests']:
        print(f"\n{'='*60}")
        print("FAILED TESTS")
        print(f"{'='*60}")
        for test in report['failed_tests']:
            print(f"❌ {test['name']}: {test['details']}")
    
    # Save report
    report_path = Path(__file__).parent / "test_report.json"
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\nDetailed report saved to: {report_path}")
    
    # Exit with appropriate code
    sys.exit(0 if summary['failed_tests'] == 0 else 1)


if __name__ == "__main__":
    asyncio.run(main())
