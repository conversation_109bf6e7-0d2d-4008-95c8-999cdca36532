"""
Dashboard Customization API endpoints for Phase 2.4 implementation.
Provides REST API for managing user-customizable dashboard sections and widgets.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List
from app.database import get_db, User
from app.auth import get_current_active_user
from app.services.dashboard_service import DatageniusDashboardService
from pydantic import ValidationError
from app.security.dashboard_security import dashboard_security
from app.models.dashboard_customization import (
    SectionCreate, SectionUpdate, SectionResponse,
    WidgetCreate, WidgetUpdate, WidgetResponse, WidgetMoveRequest,
    WidgetInsightResponse, DashboardLayoutResponse
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dashboard/customization", tags=["dashboard_customization"])


def get_dashboard_customization_service(db: Session = Depends(get_db)) -> DatageniusDashboardService:
    """Dependency to get dashboard customization service."""
    return DatageniusDashboardService(db)


@router.get("/layout", response_model=DashboardLayoutResponse)
async def get_dashboard_layout(
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Get complete dashboard layout for the current user."""
    try:
        # Get user's default dashboard
        dashboards = await service.get_user_dashboards(current_user.id)
        if not dashboards:
            # Create a default dashboard if none exists
            logger.info(f"No dashboards found for user {current_user.id}, creating default dashboard")
            from app.models.dashboard_customization import DashboardCreate
            default_dashboard_data = DashboardCreate(
                name="My Dashboard",
                description="Default dashboard",
                is_default=True,
                is_public=False,
                layout_config={
                    "columns": 12,
                    "rows": 6,
                    "grid_gap": 16,
                    "responsive": True
                },
                theme_config={
                    "primary_color": "#3B82F6",
                    "secondary_color": "#10B981",
                    "background_color": "#FFFFFF",
                    "text_color": "#1F2937"
                },
                refresh_interval=300
            )
            dashboard = await service.create_dashboard(current_user.id, default_dashboard_data)
            dashboards = [dashboard]

        # Use default dashboard or first one
        default_dashboard = next((d for d in dashboards if d.is_default), dashboards[0])
        layout = await service.get_dashboard_layout(default_dashboard.id, current_user.id)
        return DashboardLayoutResponse(**layout)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting dashboard layout: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard layout"
        )


@router.post("/sections", response_model=SectionResponse)
async def create_section(
    section_data: SectionCreate,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Create a new dashboard section."""
    try:
        # Security validation
        section_dict = section_data.model_dump()
        validation_result = dashboard_security.validate_input_security(
            section_dict.get('name', ''), 'section_name'
        )
        if not validation_result['is_safe']:
            dashboard_security.audit_log_operation(
                current_user, 'create_section_failed', 'section', 'new',
                {'threats': validation_result['threats']}
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Security validation failed: {', '.join(validation_result['threats'])}"
            )

        # Check user limits
        limits = dashboard_security.check_dashboard_limits(current_user, service.db)
        if not limits.get('can_create_widget'):  # Sections count towards widget limits
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Maximum section limit reached"
            )

        # Verify user has access to the dashboard
        dashboard = await service.get_dashboard(section_data.dashboard_id, current_user.id)
        if not dashboard:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Dashboard not found"
            )

        section = await service.create_section(current_user.id, section_data)
        
        return SectionResponse(
            id=section.id,
            name=section.name,
            description=section.description,
            color=section.color,
            icon=section.icon,
            layout_config=section.layout_config,
            customization=section.customization,
            data_source_id=section.data_source_id,
            position=section.position,
            is_active=section.is_active,
            created_at=section.created_at.isoformat(),
            updated_at=section.updated_at.isoformat(),
            widget_count=0
        )
    except ValueError as e:
        logger.error(f"Validation error creating section: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating section: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create section"
        )


@router.put("/sections/{section_id}", response_model=SectionResponse)
async def update_section(
    section_id: str,
    section_data: SectionUpdate,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Update a dashboard section."""
    try:
        logger.info(f"Updating section {section_id} with data: {section_data.model_dump()}")
        section = await service.update_section(section_id, current_user.id, section_data)
        
        return SectionResponse(
            id=section.id,
            name=section.name,
            description=section.description,
            color=section.color,
            icon=section.icon,
            layout_config=section.layout_config,
            customization=section.customization,
            data_source_id=section.data_source_id,
            position=section.position,
            is_active=section.is_active,
            created_at=section.created_at.isoformat(),
            updated_at=section.updated_at.isoformat(),
            widget_count=len(section.widgets) if section.widgets else 0
        )
    except ValueError as e:
        logger.error(f"ValueError updating section: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating section: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )


@router.delete("/sections/{section_id}")
async def delete_section(
    section_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Delete a dashboard section."""
    try:
        await service.delete_section(section_id, current_user.id)
        return {"message": "Section deleted successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error deleting section: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/widgets", response_model=WidgetResponse)
async def create_widget(
    widget_data: WidgetCreate,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Create a new dashboard widget."""
    try:
        # Security validation
        widget_dict = widget_data.model_dump()
        widget_validation = dashboard_security.validate_widget_data(widget_dict)
        if not widget_validation['is_valid']:
            dashboard_security.audit_log_operation(
                current_user, 'create_widget_failed', 'widget', 'new',
                {'errors': widget_validation['errors']}
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Security validation failed: {', '.join(widget_validation['errors'])}"
            )

        # Check user limits
        limits = dashboard_security.check_dashboard_limits(current_user, service.db)
        if not limits.get('can_create_widget'):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Maximum widget limit reached"
            )

        widget = await service.create_widget(widget_data, current_user.id)

        # Log successful creation
        dashboard_security.audit_log_operation(
            current_user, 'create_widget', 'widget', widget.id
        )
        
        return WidgetResponse(
            id=widget.id,
            section_id=widget.section_id,
            title=widget.title,
            widget_type=widget.widget_type,
            data_config=widget.data_config,
            visualization_config=widget.visualization_config,
            position_config=widget.position_config,
            customization=widget.customization,
            refresh_interval=widget.refresh_interval,
            is_active=widget.is_active,
            created_at=widget.created_at.isoformat(),
            updated_at=widget.updated_at.isoformat()
        )
    except ValueError as e:
        logger.error(f"Validation error creating widget: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating widget: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create widget"
        )


@router.put("/widgets/{widget_id}", response_model=WidgetResponse)
async def update_widget(
    widget_id: str,
    widget_data: WidgetUpdate,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Update a dashboard widget."""
    try:
        widget = await service.update_widget(widget_id, current_user.id, widget_data)
        
        return WidgetResponse(
            id=widget.id,
            section_id=widget.section_id,
            title=widget.title,
            widget_type=widget.widget_type,
            data_config=widget.data_config,
            visualization_config=widget.visualization_config,
            position_config=widget.position_config,
            customization=widget.customization,
            refresh_interval=widget.refresh_interval,
            is_active=widget.is_active,
            created_at=widget.created_at.isoformat(),
            updated_at=widget.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating widget: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/widgets/move", response_model=WidgetResponse)
async def move_widget(
    move_request: WidgetMoveRequest,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Move a widget to a different section."""
    try:
        widget = await service.move_widget_to_section(move_request, current_user.id)
        
        return WidgetResponse(
            id=widget.id,
            section_id=widget.section_id,
            title=widget.title,
            widget_type=widget.widget_type,
            data_config=widget.data_config,
            visualization_config=widget.visualization_config,
            position_config=widget.position_config,
            customization=widget.customization,
            refresh_interval=widget.refresh_interval,
            is_active=widget.is_active,
            created_at=widget.created_at.isoformat(),
            updated_at=widget.updated_at.isoformat()
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error moving widget: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/widgets/{widget_id}")
async def delete_widget(
    widget_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Delete a dashboard widget."""
    try:
        await service.delete_widget(widget_id, current_user.id)
        return {"message": "Widget deleted successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error deleting widget: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/widgets/{widget_id}/insights", response_model=WidgetInsightResponse)
async def get_widget_insights(
    widget_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Get AI-generated insights for a widget."""
    try:
        insights = await service.generate_widget_insights(widget_id, current_user.id)
        
        return WidgetInsightResponse(
            summary=insights.get("summary", {}),
            ai_insights=insights.get("ai_insights"),
            trends=insights.get("trends"),
            confidence=insights.get("confidence", 0.5)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting widget insights: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate widget insights"
        )


@router.post("/sections/{section_id}/duplicate")
async def duplicate_section(
    section_id: str,
    current_user: User = Depends(get_current_active_user),
    service: DatageniusDashboardService = Depends(get_dashboard_customization_service)
):
    """Duplicate a dashboard section with all its widgets."""
    try:
        new_section = await service.duplicate_section(section_id, current_user.id)
        
        return SectionResponse(
            id=new_section.id,
            name=new_section.name,
            description=new_section.description,
            color=new_section.color,
            icon=new_section.icon,
            layout_config=new_section.layout_config,
            customization=new_section.customization,
            data_source_id=new_section.data_source_id,
            position=new_section.position,
            is_active=new_section.is_active,
            created_at=new_section.created_at.isoformat(),
            updated_at=new_section.updated_at.isoformat(),
            widget_count=len(new_section.widgets) if new_section.widgets else 0
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error duplicating section: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
