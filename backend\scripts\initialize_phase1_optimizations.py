#!/usr/bin/env python3
"""
Phase 1 Performance Optimization Initialization Script

This script initializes all Phase 1 performance optimizations including:
- Database indexes and optimizations
- Cache system setup
- WebSocket optimization
- Performance monitoring
- Verification of target metrics

Run this script after deployment to ensure all optimizations are active.
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.performance import initialize_performance_optimizations, get_performance_summary
from app.performance.database_optimization import db_optimizer
from app.database import get_db
from app.config import DATABASE_URL, REDIS_URL

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Phase1OptimizationInitializer:
    """
    Comprehensive Phase 1 optimization initializer and validator.
    """

    def __init__(self):
        """Initialize the optimization initializer."""
        self.results = {
            "initialization_time": None,
            "database_optimizations": {},
            "cache_system": {},
            "websocket_optimizations": {},
            "performance_metrics": {},
            "target_metrics_status": {},
            "recommendations": []
        }

    async def run_full_initialization(self) -> Dict[str, Any]:
        """
        Run complete Phase 1 optimization initialization.
        
        Returns:
            Dictionary with comprehensive results and status
        """
        start_time = datetime.now()
        logger.info("🚀 Starting Phase 1 Performance Optimization Initialization")
        
        try:
            # Step 1: Verify prerequisites
            logger.info("📋 Step 1: Verifying prerequisites...")
            prereq_results = await self._verify_prerequisites()
            if not prereq_results["all_met"]:
                logger.error("❌ Prerequisites not met. Aborting initialization.")
                return {"status": "failed", "error": "Prerequisites not met", "details": prereq_results}
            
            # Step 2: Initialize database optimizations
            logger.info("🗄️ Step 2: Initializing database optimizations...")
            db_results = await self._initialize_database_optimizations()
            self.results["database_optimizations"] = db_results
            
            # Step 3: Initialize performance optimizations
            logger.info("⚡ Step 3: Initializing performance optimizations...")
            perf_results = await initialize_performance_optimizations()
            self.results["cache_system"] = perf_results.get("cache_system", {})
            self.results["websocket_optimizations"] = perf_results.get("websocket_optimization", {})
            
            # Step 4: Validate target metrics
            logger.info("📊 Step 4: Validating target metrics...")
            metrics_results = await self._validate_target_metrics()
            self.results["target_metrics_status"] = metrics_results
            
            # Step 5: Generate recommendations
            logger.info("💡 Step 5: Generating optimization recommendations...")
            recommendations = await self._generate_recommendations()
            self.results["recommendations"] = recommendations
            
            # Calculate total time
            end_time = datetime.now()
            self.results["initialization_time"] = (end_time - start_time).total_seconds()
            
            # Determine overall status
            overall_status = self._determine_overall_status()
            
            logger.info(f"✅ Phase 1 initialization completed in {self.results['initialization_time']:.2f}s")
            logger.info(f"📈 Overall Status: {overall_status}")
            
            return {
                "status": overall_status,
                "results": self.results,
                "summary": self._generate_summary()
            }
            
        except Exception as e:
            logger.error(f"❌ Phase 1 initialization failed: {e}", exc_info=True)
            return {
                "status": "failed",
                "error": str(e),
                "results": self.results
            }

    async def _verify_prerequisites(self) -> Dict[str, Any]:
        """Verify that all prerequisites are met."""
        results = {
            "database_connection": False,
            "redis_connection": False,
            "required_tables": False,
            "all_met": False
        }
        
        try:
            # Test database connection
            db = next(get_db())
            await db.execute("SELECT 1")
            results["database_connection"] = True
            logger.info("✅ Database connection verified")
            db.close()
            
            # Test required tables
            from app.models.dashboard_customization import Dashboard
            db = next(get_db())
            count = db.query(Dashboard).count()
            results["required_tables"] = True
            logger.info(f"✅ Dashboard tables verified ({count} dashboards found)")
            db.close()
            
        except Exception as e:
            logger.error(f"❌ Database verification failed: {e}")
        
        try:
            # Test Redis connection
            import redis
            redis_client = redis.from_url(REDIS_URL)
            redis_client.ping()
            results["redis_connection"] = True
            logger.info("✅ Redis connection verified")
            
        except Exception as e:
            logger.warning(f"⚠️ Redis connection failed: {e}")
            logger.info("📝 Redis is optional but recommended for optimal performance")
        
        results["all_met"] = results["database_connection"] and results["required_tables"]
        return results

    async def _initialize_database_optimizations(self) -> Dict[str, Any]:
        """Initialize database-specific optimizations."""
        try:
            # Apply database optimizations
            db_results = await db_optimizer.apply_dashboard_optimizations()
            
            # Get performance metrics
            performance_metrics = db_optimizer.get_performance_metrics()
            
            logger.info(f"✅ Database optimizations applied: {db_results['indexes_created']} indexes created")
            
            return {
                "status": "success",
                "indexes_created": db_results["indexes_created"],
                "optimizations_applied": db_results.get("optimizations_applied", []),
                "performance_metrics": performance_metrics,
                "errors": db_results.get("errors", [])
            }
            
        except Exception as e:
            logger.error(f"❌ Database optimization failed: {e}")
            return {
                "status": "failed",
                "error": str(e)
            }

    async def _validate_target_metrics(self) -> Dict[str, Any]:
        """Validate that target metrics are being met."""
        try:
            # Get current performance summary
            summary = get_performance_summary()
            
            # Define target metrics from dashboard.md
            targets = {
                "dashboard_load_time": 1.5,  # seconds
                "cache_hit_ratio": 85,       # percentage
                "memory_usage": 80,          # MB
                "websocket_reconnection": 2, # seconds
                "database_queries_per_load": 8  # count
            }
            
            results = {}
            
            # Check cache hit ratio
            cache_stats = summary.get("cache_stats", {})
            hit_rate = cache_stats.get("hit_rate_percent", 0)
            results["cache_hit_ratio"] = {
                "current": hit_rate,
                "target": targets["cache_hit_ratio"],
                "status": "pass" if hit_rate >= targets["cache_hit_ratio"] else "fail"
            }
            
            # Check database performance
            db_stats = summary.get("database_stats", {})
            avg_query_time = db_stats.get("avg_query_time", 0) * 1000  # Convert to ms
            results["database_performance"] = {
                "current_query_time_ms": avg_query_time,
                "target_load_time_s": targets["dashboard_load_time"],
                "status": "pass" if avg_query_time < 100 else "fail"  # 100ms per query
            }
            
            # Check WebSocket performance
            ws_stats = summary.get("websocket_stats", {})
            error_rate = ws_stats.get("error_rate", 0) * 100
            results["websocket_performance"] = {
                "current_error_rate": error_rate,
                "target_error_rate": 5,  # 5%
                "status": "pass" if error_rate < 5 else "fail"
            }
            
            # Overall health score
            health_score = summary.get("health_score", 0)
            results["overall_health"] = {
                "current": health_score,
                "target": 85,
                "status": "pass" if health_score >= 85 else "fail"
            }
            
            logger.info(f"📊 Target metrics validation completed")
            return results
            
        except Exception as e:
            logger.error(f"❌ Metrics validation failed: {e}")
            return {"error": str(e)}

    async def _generate_recommendations(self) -> list:
        """Generate optimization recommendations based on current state."""
        recommendations = []
        
        try:
            # Get current performance summary
            summary = get_performance_summary()
            
            # Cache recommendations
            cache_stats = summary.get("cache_stats", {})
            if cache_stats.get("hit_rate_percent", 0) < 85:
                recommendations.append({
                    "category": "cache",
                    "priority": "high",
                    "title": "Improve Cache Hit Rate",
                    "description": "Cache hit rate is below target (85%). Consider increasing cache TTL or warming more data.",
                    "action": "Review cache warming strategies and increase TTL for stable data"
                })
            
            if not cache_stats.get("redis_available", False):
                recommendations.append({
                    "category": "cache",
                    "priority": "medium",
                    "title": "Enable Redis Caching",
                    "description": "Redis is not available. Enable Redis for L2 caching to improve performance.",
                    "action": "Install and configure Redis server"
                })
            
            # Database recommendations
            db_stats = summary.get("database_stats", {})
            if db_stats.get("avg_query_time", 0) > 0.1:
                recommendations.append({
                    "category": "database",
                    "priority": "high",
                    "title": "Optimize Slow Queries",
                    "description": "Average query time exceeds 100ms. Consider adding more indexes.",
                    "action": "Analyze slow query log and add appropriate indexes"
                })
            
            # WebSocket recommendations
            ws_stats = summary.get("websocket_stats", {})
            if ws_stats.get("error_rate", 0) > 0.05:
                recommendations.append({
                    "category": "websocket",
                    "priority": "medium",
                    "title": "Reduce WebSocket Errors",
                    "description": "WebSocket error rate is above 5%. Check network stability.",
                    "action": "Investigate connection issues and implement better error handling"
                })
            
            logger.info(f"💡 Generated {len(recommendations)} optimization recommendations")
            return recommendations
            
        except Exception as e:
            logger.error(f"❌ Recommendation generation failed: {e}")
            return []

    def _determine_overall_status(self) -> str:
        """Determine overall initialization status."""
        # Check if critical components are working
        db_status = self.results.get("database_optimizations", {}).get("status")
        cache_status = self.results.get("cache_system", {}).get("status")
        
        if db_status == "success" and cache_status == "success":
            return "success"
        elif db_status == "success" or cache_status == "success":
            return "partial"
        else:
            return "failed"

    def _generate_summary(self) -> Dict[str, Any]:
        """Generate a summary of the initialization results."""
        return {
            "initialization_time": f"{self.results['initialization_time']:.2f}s",
            "database_indexes_created": self.results.get("database_optimizations", {}).get("indexes_created", 0),
            "cache_system_active": self.results.get("cache_system", {}).get("status") == "success",
            "websocket_optimized": self.results.get("websocket_optimizations", {}).get("status") == "success",
            "recommendations_count": len(self.results.get("recommendations", [])),
            "target_metrics_passing": sum(
                1 for metric in self.results.get("target_metrics_status", {}).values()
                if isinstance(metric, dict) and metric.get("status") == "pass"
            )
        }


async def main():
    """Main entry point for the initialization script."""
    print("🚀 Datagenius Phase 1 Performance Optimization Initializer")
    print("=" * 60)
    
    initializer = Phase1OptimizationInitializer()
    results = await initializer.run_full_initialization()
    
    print("\n📋 INITIALIZATION RESULTS")
    print("=" * 60)
    print(f"Status: {results['status'].upper()}")
    
    if results['status'] != 'failed':
        summary = results.get('summary', {})
        print(f"⏱️  Initialization Time: {summary.get('initialization_time', 'N/A')}")
        print(f"🗄️  Database Indexes Created: {summary.get('database_indexes_created', 0)}")
        print(f"💾 Cache System Active: {'✅' if summary.get('cache_system_active') else '❌'}")
        print(f"🔌 WebSocket Optimized: {'✅' if summary.get('websocket_optimized') else '❌'}")
        print(f"📊 Target Metrics Passing: {summary.get('target_metrics_passing', 0)}")
        print(f"💡 Recommendations: {summary.get('recommendations_count', 0)}")
        
        # Show recommendations
        recommendations = results.get('results', {}).get('recommendations', [])
        if recommendations:
            print("\n💡 OPTIMIZATION RECOMMENDATIONS")
            print("-" * 40)
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. [{rec['priority'].upper()}] {rec['title']}")
                print(f"   {rec['description']}")
                print(f"   Action: {rec['action']}\n")
    
    if results['status'] == 'failed':
        print(f"❌ Error: {results.get('error', 'Unknown error')}")
        return 1
    
    print("🎉 Phase 1 optimization initialization completed!")
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
