"""
<PERSON><PERSON><PERSON> to register the mem0ai integration with all components of the Datagenius application.

This script:
1. Ensures all necessary components are registered
2. Updates component configurations to use mem0ai
3. Verifies the integration is working
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

from agents.utils.memory_service import MemoryService
from agents.utils.vector_service import VectorService
from agents.utils.knowledge_graph_service import KnowledgeGraphService
from agents.utils.qdrant_manager import QdrantManager
from agents.components.registry import ComponentRegistry
# Legacy registry import removed - using enhanced MCP system
from app.config import MEM0_SELF_HOSTED

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def register_memory_manager_component():
    """Register the MemoryManagerComponent with all personas."""
    logger.info("Registering MemoryManagerComponent with all personas...")
    
    # Get the component registry
    registry = ComponentRegistry()
    
    # Check if the MemoryManagerComponent is registered
    if "memory_manager" not in registry.get_component_names():
        logger.error("MemoryManagerComponent is not registered")
        return False
    
    # Get all persona configurations
    from agents.persona_manager import PersonaManager
    persona_manager = PersonaManager()
    personas = persona_manager.get_all_personas()
    
    # Update each persona configuration to use the MemoryManagerComponent
    for persona_id, persona_config in personas.items():
        logger.info(f"Updating persona {persona_id} to use MemoryManagerComponent")
        
        # Get the components for this persona
        components = persona_config.get("components", [])
        
        # Check if the MemoryManagerComponent is already in the components
        memory_manager_exists = any(
            component.get("name") == "memory_manager" 
            for component in components
        )
        
        if not memory_manager_exists:
            # Add the MemoryManagerComponent to the components
            components.append({
                "name": "memory_manager",
                "config": {
                    "memory_ttl": 2592000,  # 30 days in seconds
                    "max_memories": 1000,
                    "memory_threshold": 0.7,
                    "enable_cross_session_memory": True,
                    "enable_document_embedding": True,
                    "enable_knowledge_graph": True,
                    "memory_types": ["conversation", "insight", "preference", "document", "entity", "relationship"],
                    "persona_id": persona_id
                }
            })
            
            # Update the persona configuration
            persona_config["components"] = components
            
            # Save the updated configuration
            persona_manager.save_persona_config(persona_id, persona_config)
            
            logger.info(f"Added MemoryManagerComponent to persona {persona_id}")
        else:
            logger.info(f"MemoryManagerComponent already exists for persona {persona_id}")
    
    logger.info("MemoryManagerComponent registered with all personas")
    return True

async def register_mcp_tools():
    """Register the mem0ai MCP tools."""
    logger.info("Registering mem0ai MCP tools...")

    # Use the enhanced MCP system
    from agents.tools.mcp import initialize_mcp_system
    registry = await initialize_mcp_system(environment="development")

    # Check if the Mem0DocumentEmbeddingTool is registered
    if hasattr(registry, 'get_tool') and not registry.get_tool("mem0_document_embedding"):
        logger.info("Registering Mem0DocumentEmbeddingTool")

        # Import and register the tool
        from agents.tools.mcp.mem0_document_embedding import Mem0DocumentEmbeddingTool
        tool = Mem0DocumentEmbeddingTool()
        if hasattr(registry, 'register_tool'):
            registry.register_tool(tool)
        logger.info("Mem0DocumentEmbeddingTool registered successfully")
    else:
        logger.info("Mem0DocumentEmbeddingTool is already registered")
    
    # Update the DataAccessTool to use mem0ai
    if "data_access" in registry.get_tool_names():
        logger.info("Updating DataAccessTool to use mem0ai")
        
        # Get the tool
        data_access_tool = registry.get_tool("data_access")
        
        # Initialize the vector and memory services
        data_access_tool.vector_service = VectorService()
        data_access_tool.memory_service = MemoryService()
        
        logger.info("DataAccessTool updated to use mem0ai")
    
    # Update the PandasAI tools to use mem0ai
    for tool_name in ["pandasai_analysis", "pandasai_visualization", "pandasai_query"]:
        if tool_name in registry.get_tool_names():
            logger.info(f"Updating {tool_name} to use mem0ai")
            
            # Get the tool
            tool = registry.get_tool(tool_name)
            
            # Initialize the vector and memory services
            tool.vector_service = VectorService()
            tool.memory_service = MemoryService()
            
            logger.info(f"{tool_name} updated to use mem0ai")
    
    logger.info("mem0ai MCP tools registered")
    return True

async def verify_integration():
    """Verify the mem0ai integration is working."""
    logger.info("Verifying mem0ai integration...")
    
    # Ensure Qdrant is running if in self-hosted mode
    if MEM0_SELF_HOSTED:
        logger.info("Ensuring Qdrant is running...")
        if not QdrantManager.ensure_qdrant_running():
            logger.error("Failed to ensure Qdrant is running")
            return False
    
    # Test the memory service
    try:
        logger.info("Testing memory service...")
        memory_service = MemoryService()
        memory = memory_service.add_memory(
            "This is a test memory for mem0ai integration verification",
            user_id="system",
            metadata={"test": True, "category": "verification"}
        )
        
        if memory:
            logger.info("Memory service is working")
        else:
            logger.error("Failed to add memory")
            return False
    except Exception as e:
        logger.error(f"Error testing memory service: {e}")
        return False
    
    # Test the vector service
    try:
        logger.info("Testing vector service...")
        vector_service = VectorService()
        
        # Create a test file
        test_file_path = os.path.join(os.getcwd(), "test_verification.txt")
        with open(test_file_path, "w") as f:
            f.write("This is a test document for mem0ai integration verification.")
        
        # Embed the document
        vector_store_id, _ = vector_service.embed_document(test_file_path)
        
        if vector_store_id:
            logger.info("Vector service is working")
        else:
            logger.error("Failed to embed document")
            return False
        
        # Clean up
        os.remove(test_file_path)
    except Exception as e:
        logger.error(f"Error testing vector service: {e}")
        return False
    
    logger.info("mem0ai integration verified successfully")
    return True

async def main():
    """Main entry point for the script."""
    logger.info("Starting mem0ai integration registration...")
    
    # Register the MemoryManagerComponent with all personas
    memory_manager_result = await register_memory_manager_component()
    logger.info(f"MemoryManagerComponent registration {'succeeded' if memory_manager_result else 'failed'}")
    
    # Register the mem0ai MCP tools
    mcp_tools_result = await register_mcp_tools()
    logger.info(f"MCP tools registration {'succeeded' if mcp_tools_result else 'failed'}")
    
    # Verify the integration
    verification_result = await verify_integration()
    logger.info(f"Integration verification {'succeeded' if verification_result else 'failed'}")
    
    # Overall result
    if memory_manager_result and mcp_tools_result and verification_result:
        logger.info("mem0ai integration registration completed successfully")
    else:
        logger.error("mem0ai integration registration failed")

if __name__ == "__main__":
    asyncio.run(main())
