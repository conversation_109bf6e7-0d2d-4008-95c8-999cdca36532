"""
Enhanced composable agent for the Datagenius backend.

This module provides an enhanced implementation of the composable agent architecture,
with improved error handling, component management, and logging.
"""

import logging
from typing import Dict, Any, Optional, List, Type

from .enhanced_base import EnhancedBaseAgent
from .mixins.streaming_mixin import StreamingMixin
from .components.base import AgentComponent
from .components.registry import ComponentRegistry
from .components.persistent_context_manager import PersistentContextManager

logger = logging.getLogger(__name__)


class EnhancedComposableAgent(EnhancedBaseAgent):
    """Enhanced implementation of a composable agent."""

    def __init__(self):
        """Initialize the enhanced composable agent."""
        super().__init__()
        self.components = []
        self.component_map = {}  # Map of component names to instances

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the composable agent with components.

        Args:
            config: Configuration dictionary for the agent
        """
        # Initialize components from configuration
        if "components" in config:
            await self._initialize_components(config["components"])
        else:
            logger.warning(f"No components configured for agent {self.name}")

    async def _initialize_components(self, component_configs: List[Dict[str, Any]]) -> None:
        """
        Initialize components from configuration.

        Args:
            component_configs: List of component configurations
        """
        for idx, component_config in enumerate(component_configs):
            try:
                # Get component type
                component_type = component_config.get("type")
                if not component_type:
                    logger.warning(f"Component configuration at index {idx} missing 'type' field")
                    continue

                # Create component instance
                logger.info(f"Creating component of type '{component_type}'")
                component = await ComponentRegistry.create_component_instance(component_type, component_config)

                if component:
                    self.components.append(component)
                    # Add to component map using the component's name
                    self.component_map[component.name] = component
                    logger.info(f"Added component: {component.name}")
                else:
                    logger.warning(f"Failed to create component of type '{component_type}'")
            except Exception as e:
                logger.error(f"Error initializing component at index {idx}: {e}", exc_info=True)

        logger.info(f"Initialized {len(self.components)} components for agent {self.name}")

        # Ensure essential tools are available in the MCP server component
        await self._ensure_essential_tools()

    async def _ensure_essential_tools(self) -> None:
        """
        Ensure that the MCP server component has all essential tools.
        """
        from .components.mcp_server import MCPServerComponent
        from .components.essential_tools import ensure_essential_tools

        # Find the MCP server component
        mcp_server = next((comp for comp in self.components if isinstance(comp, MCPServerComponent)), None)
        if mcp_server:
            logger.info("Found MCP server component, ensuring essential tools")
            await ensure_essential_tools(mcp_server)
        else:
            logger.warning("No MCP server component found, cannot ensure essential tools")

    async def _process_message(self,
                              user_id: int,
                              message: str,
                              conversation_id: str,
                              context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a user message using the agent's components.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """
        if not self.components:
            logger.warning(f"No components configured for agent {self.name}")
            return {
                "message": "This agent has no components configured. Please check the agent configuration.",
                "metadata": {
                    "error": "no_components_configured"
                }
            }

        # Create initial context with agent components for inter-component communication
        ctx = {
            "user_id": user_id,
            "message": message,
            "conversation_id": conversation_id,
            "context": context or {},
            "agent_config": self.config,
            "agent_components": self.component_map,
            "response": "",
            "metadata": {}
        }

        # Process context through each component
        for component in self.components:
            try:
                logger.debug(f"Processing with component: {component.name}")
                ctx = await component.process(ctx)
                logger.debug(f"Component {component.name} processing complete")
            except Exception as e:
                logger.error(f"Error in component {component.name}: {e}", exc_info=True)
                ctx["metadata"]["component_errors"] = ctx.get("metadata", {}).get("component_errors", []) + [
                    {
                        "component": component.name,
                        "error": str(e)
                    }
                ]

        # Ensure we have a response
        if not ctx.get("response"):
            logger.warning("No response generated by components")
            ctx["response"] = "I'm sorry, I wasn't able to process your request properly."

        return {
            "message": ctx.get("response", ""),
            "metadata": ctx.get("metadata", {})
        }

    async def get_capabilities(self) -> List[str]:
        """
        Return a list of capabilities this agent supports.

        Returns:
            List of capability strings
        """
        # Combine capabilities from all components
        capabilities = set(self.capabilities)
        for component in self.components:
            capabilities.update(component.get_capabilities())
        return list(capabilities)

    def get_component(self, name: str) -> Optional[AgentComponent]:
        """
        Get a component by name.

        Args:
            name: Name of the component

        Returns:
            Component instance if found, None otherwise
        """
        return self.component_map.get(name)
