"""
Tests for WorkflowManager with database persistence.
"""
import pytest
import uuid
import asyncio
from unittest.mock import Mock, AsyncMock
from sqlalchemy.orm import Session

from agents.orchestration.workflow_manager import WorkflowManager
from schemas.db_schemas import WorkflowStatusEnum, TaskStatusEnum


class TestWorkflowManager:
    """Test WorkflowManager with database persistence."""

    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock(spec=Session)
        mock_session.commit = Mock()
        mock_session.rollback = Mock()
        mock_session.close = Mock()
        
        def session_factory():
            return mock_session
        
        return session_factory

    @pytest.fixture
    def workflow_manager(self, mock_db_session_factory):
        """Create a WorkflowManager instance with mocked database."""
        manager = WorkflowManager()
        manager.db_session_factory = mock_db_session_factory
        return manager

    @pytest.mark.asyncio
    async def test_create_workflow(self, workflow_manager):
        """Test creating a workflow."""
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            description="A test workflow",
            user_id="test_user",
            session_id="test_session",
            context={"test": "data"},
            workflow_metadata={"version": "1.0"}
        )
        
        assert isinstance(workflow_id, uuid.UUID)
        assert workflow_id in workflow_manager.workflows

    @pytest.mark.asyncio
    async def test_add_task(self, workflow_manager):
        """Test adding a task to a workflow."""
        # Create a workflow first
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            user_id="test_user"
        )
        
        # Add a task
        task_id = await workflow_manager.add_task(
            workflow_id=workflow_id,
            name="Test Task",
            agent_type="test_agent",
            input_data={"input": "test"},
            dependencies=["task1"],
            task_metadata={"priority": "high"}
        )
        
        assert isinstance(task_id, uuid.UUID)
        workflow = workflow_manager.workflows[workflow_id]
        assert task_id in workflow.tasks

    @pytest.mark.asyncio
    async def test_start_workflow(self, workflow_manager):
        """Test starting a workflow."""
        # Create a workflow with tasks
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            user_id="test_user"
        )
        
        task_id = await workflow_manager.add_task(
            workflow_id=workflow_id,
            name="Test Task",
            agent_type="test_agent",
            input_data={"input": "test"}
        )
        
        # Start the workflow
        result = await workflow_manager.start_workflow(workflow_id)
        
        assert result is True
        workflow = workflow_manager.workflows[workflow_id]
        assert workflow.status == WorkflowStatusEnum.RUNNING

    @pytest.mark.asyncio
    async def test_complete_task(self, workflow_manager):
        """Test completing a task."""
        # Create a workflow with a task
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            user_id="test_user"
        )
        
        task_id = await workflow_manager.add_task(
            workflow_id=workflow_id,
            name="Test Task",
            agent_type="test_agent",
            input_data={"input": "test"}
        )
        
        # Start the workflow
        await workflow_manager.start_workflow(workflow_id)
        
        # Complete the task
        result = await workflow_manager.complete_task(
            workflow_id=workflow_id,
            task_id=task_id,
            output_data={"output": "result"}
        )
        
        assert result is True
        workflow = workflow_manager.workflows[workflow_id]
        task = workflow.tasks[task_id]
        assert task.status == TaskStatusEnum.COMPLETED
        assert task.output_data == {"output": "result"}

    @pytest.mark.asyncio
    async def test_fail_task(self, workflow_manager):
        """Test failing a task."""
        # Create a workflow with a task
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            user_id="test_user"
        )
        
        task_id = await workflow_manager.add_task(
            workflow_id=workflow_id,
            name="Test Task",
            agent_type="test_agent",
            input_data={"input": "test"}
        )
        
        # Start the workflow
        await workflow_manager.start_workflow(workflow_id)
        
        # Fail the task
        result = await workflow_manager.fail_task(
            workflow_id=workflow_id,
            task_id=task_id,
            error_message="Test error"
        )
        
        assert result is True
        workflow = workflow_manager.workflows[workflow_id]
        task = workflow.tasks[task_id]
        assert task.status == TaskStatusEnum.FAILED
        assert task.error_message == "Test error"

    @pytest.mark.asyncio
    async def test_get_workflow_status(self, workflow_manager):
        """Test getting workflow status."""
        # Create a workflow
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            user_id="test_user"
        )
        
        # Get status
        status = await workflow_manager.get_workflow_status(workflow_id)
        
        assert status == WorkflowStatusEnum.PENDING

    @pytest.mark.asyncio
    async def test_get_task_status(self, workflow_manager):
        """Test getting task status."""
        # Create a workflow with a task
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            user_id="test_user"
        )
        
        task_id = await workflow_manager.add_task(
            workflow_id=workflow_id,
            name="Test Task",
            agent_type="test_agent",
            input_data={"input": "test"}
        )
        
        # Get status
        status = await workflow_manager.get_task_status(workflow_id, task_id)
        
        assert status == TaskStatusEnum.PENDING

    @pytest.mark.asyncio
    async def test_workflow_completion(self, workflow_manager):
        """Test workflow completion when all tasks are done."""
        # Create a workflow with multiple tasks
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            user_id="test_user"
        )
        
        task_ids = []
        for i in range(3):
            task_id = await workflow_manager.add_task(
                workflow_id=workflow_id,
                name=f"Test Task {i}",
                agent_type="test_agent",
                input_data={"input": f"test{i}"}
            )
            task_ids.append(task_id)
        
        # Start the workflow
        await workflow_manager.start_workflow(workflow_id)
        
        # Complete all tasks
        for task_id in task_ids:
            await workflow_manager.complete_task(
                workflow_id=workflow_id,
                task_id=task_id,
                output_data={"output": "result"}
            )
        
        # Check workflow status
        workflow = workflow_manager.workflows[workflow_id]
        assert workflow.status == WorkflowStatusEnum.COMPLETED

    @pytest.mark.asyncio
    async def test_workflow_failure(self, workflow_manager):
        """Test workflow failure when a task fails."""
        # Create a workflow with a task
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            user_id="test_user"
        )
        
        task_id = await workflow_manager.add_task(
            workflow_id=workflow_id,
            name="Test Task",
            agent_type="test_agent",
            input_data={"input": "test"}
        )
        
        # Start the workflow
        await workflow_manager.start_workflow(workflow_id)
        
        # Fail the task
        await workflow_manager.fail_task(
            workflow_id=workflow_id,
            task_id=task_id,
            error_message="Test error"
        )
        
        # Check workflow status
        workflow = workflow_manager.workflows[workflow_id]
        assert workflow.status == WorkflowStatusEnum.FAILED

    @pytest.mark.asyncio
    async def test_nonexistent_workflow(self, workflow_manager):
        """Test operations on nonexistent workflow."""
        fake_id = uuid.uuid4()
        
        # Test getting status of nonexistent workflow
        status = await workflow_manager.get_workflow_status(fake_id)
        assert status is None
        
        # Test adding task to nonexistent workflow
        with pytest.raises(ValueError):
            await workflow_manager.add_task(
                workflow_id=fake_id,
                name="Test Task",
                agent_type="test_agent",
                input_data={"input": "test"}
            )

    @pytest.mark.asyncio
    async def test_database_persistence(self, workflow_manager):
        """Test that operations are persisted to database."""
        # Create a workflow
        workflow_id = await workflow_manager.create_workflow(
            name="Test Workflow",
            user_id="test_user"
        )
        
        # Verify database session was used
        db_session = workflow_manager._get_db()
        assert db_session.commit.called
        
        # Add a task
        task_id = await workflow_manager.add_task(
            workflow_id=workflow_id,
            name="Test Task",
            agent_type="test_agent",
            input_data={"input": "test"}
        )
        
        # Verify database session was used again
        assert db_session.commit.call_count >= 2
