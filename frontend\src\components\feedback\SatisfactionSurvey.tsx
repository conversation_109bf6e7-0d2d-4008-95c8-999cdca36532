import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input'; // For NPS
import { useSubmitSurveyResponse } from '@/hooks/use-feedback'; // Import the hook
import { Loader2 } from 'lucide-react'; // For loading state
// Removed incorrect datetime import from @/lib/api

// Define types based on roadmap and usage
interface SurveyQuestionType {
  id: string; // Added id for responses map
  type: 'rating' | 'multiple_choice' | 'text' | 'nps';
  question: string; // Changed from 'text' in roadmap to 'question' for clarity
  scale?: number; // For rating
  options?: string[]; // For multiple_choice
}

interface SurveyType {
  id: string;
  title: string;
  description?: string;
  questions: SurveyQuestionType[];
}

interface SatisfactionSurveyProps {
  survey: SurveyType | null; // Survey can be null if not loaded yet
  isOpen: boolean;
  onClose: () => void; // To close the dialog
  onComplete: () => void; // Called after successful submission
}

// Basic SurveyQuestion component (can be moved to its own file)
interface SurveyQuestionProps {
  question: SurveyQuestionType;
  response: any;
  onResponseChange: (response: any) => void;
}

const SurveyQuestionComponent: React.FC<SurveyQuestionProps> = ({ question, response, onResponseChange }) => {
  switch (question.type) {
    case 'rating':
      return (
        <div className="space-y-2">
          <Label>{question.question}</Label>
          <div className="flex gap-1">
            {[...Array(question.scale || 5)].map((_, i) => (
              <Button
                key={i}
                variant={response === i + 1 ? 'default' : 'outline'}
                onClick={() => onResponseChange(i + 1)}
              >
                {i + 1}
              </Button>
            ))}
          </div>
        </div>
      );
    case 'multiple_choice':
      return (
        <div className="space-y-2">
          <Label>{question.question}</Label>
          <RadioGroup value={response} onValueChange={onResponseChange}>
            {question.options?.map(option => (
              <div key={option} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`${question.id}-${option}`} />
                <Label htmlFor={`${question.id}-${option}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        </div>
      );
    case 'text':
      return (
        <div className="space-y-2">
          <Label htmlFor={question.id}>{question.question}</Label>
          <Textarea id={question.id} value={response || ''} onChange={(e) => onResponseChange(e.target.value)} />
        </div>
      );
    case 'nps': // Net Promoter Score (0-10)
      return (
        <div className="space-y-2">
          <Label>{question.question}</Label>
          <div className="flex flex-wrap gap-1">
            {[...Array(11)].map((_, i) => (
               <Button
                key={i}
                variant={response === i ? 'default' : 'outline'}
                size="sm"
                className="w-8 h-8"
                onClick={() => onResponseChange(i)}
              >
                {i}
              </Button>
            ))}
          </div>
          <p className="text-xs text-muted-foreground flex justify-between"><span>Not at all likely</span> <span>Extremely likely</span></p>
        </div>
      );
    default:
      return <div>Unsupported question type: {question.type}</div>;
  }
};


export const SatisfactionSurvey: React.FC<SatisfactionSurveyProps> = ({ survey, isOpen, onClose, onComplete }) => {
  const { mutate: submitSurvey, isPending: isLoading, isError, error } = useSubmitSurveyResponse();
  const [responses, setResponses] = useState<Record<string, any>>({});
  const [startTime, setStartTime] = useState<number | null>(null);

  useEffect(() => {
    if (isOpen && survey) {
      setResponses({}); // Reset responses when dialog opens with a new survey
      setStartTime(Date.now());
    }
  }, [isOpen, survey]);

  if (!survey) return null; // Don't render if no survey data

  const handleResponseChange = (questionId: string, response: any) => {
    setResponses(prev => ({ ...prev, [questionId]: response }));
  };

  const calculateCompletionTime = (): number | undefined => {
    if (!startTime) return undefined;
    return Math.round((Date.now() - startTime) / 1000); // in seconds
  };

  const handleSubmit = async () => {
    if (!survey) return;
    const completionTimeSeconds = calculateCompletionTime();
    submitSurvey(
      { survey_id: survey.id, responses, completion_time_seconds: completionTimeSeconds },
      {
        onSuccess: () => {
          onComplete();
          onClose(); // Close dialog on successful submission
        },
        // onError is handled by the hook's toast
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>{survey.title}</DialogTitle>
          {survey.description && <p className="text-sm text-muted-foreground pt-1">{survey.description}</p>}
        </DialogHeader>
        <div className="py-4 space-y-6 max-h-[60vh] overflow-y-auto pr-2">
          {survey.questions.map((question) => (
            <SurveyQuestionComponent
              key={question.id}
              question={question}
              response={responses[question.id]}
              onResponseChange={(response) => handleResponseChange(question.id, response)}
            />
          ))}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            disabled={isLoading || Object.keys(responses).length !== survey.questions.length}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isLoading ? "Submitting..." : "Submit Survey"}
          </Button>
        </DialogFooter>
        {isError && <p className="text-sm text-red-500 mt-2 px-6 pb-2">{(error as Error)?.message || 'Failed to submit survey.'}</p>}
      </DialogContent>
    </Dialog>
  );
};
