"""add pricing subscription discount promotion tables

Revision ID: f4c56997e328
Revises: fdcf7758248c
Create Date: 2025-05-27 18:50:10.034369

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f4c56997e328'
down_revision = 'fdcf7758248c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pricing_tiers',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('persona_id', sa.String(length=50), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('base_price', sa.Float(), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('billing_period', sa.String(length=50), nullable=True),
    sa.Column('features', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_pricing_tiers_id'), 'pricing_tiers', ['id'], unique=False)
    op.create_index(op.f('ix_pricing_tiers_persona_id'), 'pricing_tiers', ['persona_id'], unique=False)
    op.create_index(op.f('ix_pricing_tiers_name'), 'pricing_tiers', ['name'], unique=True)


    op.create_table('subscriptions',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('pricing_tier_id', sa.String(length=36), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('next_billing_date', sa.DateTime(), nullable=True),
    sa.Column('auto_renew', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['pricing_tier_id'], ['pricing_tiers.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ), # Assuming 'users' table exists
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_subscriptions_id'), 'subscriptions', ['id'], unique=False)
    op.create_index(op.f('ix_subscriptions_user_id'), 'subscriptions', ['user_id'], unique=False)
    op.create_index(op.f('ix_subscriptions_pricing_tier_id'), 'subscriptions', ['pricing_tier_id'], unique=False)

    op.create_table('discounts',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('code', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('discount_type', sa.String(length=50), nullable=False),
    sa.Column('value', sa.Float(), nullable=False),
    sa.Column('max_uses', sa.Integer(), nullable=True),
    sa.Column('uses_count', sa.Integer(), nullable=True),
    sa.Column('valid_from', sa.DateTime(), nullable=True),
    sa.Column('valid_until', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('applicable_tier_ids', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_discounts_id'), 'discounts', ['id'], unique=False)
    op.create_index(op.f('ix_discounts_code'), 'discounts', ['code'], unique=True)

    op.create_table('promotions',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('promotion_type', sa.String(length=50), nullable=False),
    sa.Column('details', sa.JSON(), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('target_audience_criteria', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_promotions_id'), 'promotions', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_promotions_id'), table_name='promotions')
    op.drop_table('promotions')
    op.drop_index(op.f('ix_discounts_code'), table_name='discounts')
    op.drop_index(op.f('ix_discounts_id'), table_name='discounts')
    op.drop_table('discounts')
    op.drop_index(op.f('ix_subscriptions_pricing_tier_id'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_user_id'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_id'), table_name='subscriptions')
    op.drop_table('subscriptions')
    op.drop_index(op.f('ix_pricing_tiers_name'), table_name='pricing_tiers')
    op.drop_index(op.f('ix_pricing_tiers_persona_id'), table_name='pricing_tiers')
    op.drop_index(op.f('ix_pricing_tiers_id'), table_name='pricing_tiers')
    op.drop_table('pricing_tiers')
    # ### end Alembic commands ###
