import asyncio
import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, Integer
from fastapi import Depends

# Metrics models removed - dashboard service now focuses on customization only
from ..models.dashboard_customization import (
    Dashboard, DashboardSection, DashboardWidget, WidgetInsight, DashboardDataSource,
    DashboardDataSourceAssignment, DashboardDataSourceAssignmentResponse,
    DashboardCreate, DashboardUpdate, SectionCreate, SectionUpdate,
    WidgetCreate, WidgetUpdate, WidgetMoveRequest, PositionConfig,
    DashboardDataSourceCreate, DashboardDataSourceUpdate, DashboardDataSourceResponse,
    DashboardDataSourceAssignmentCreate
)
from ..database import DataSource
from ..database import get_db, get_utc_now
from ..utils.json_utils import serialize_for_db

# Configure logging
logger = logging.getLogger(__name__)

class DatageniusDashboardService:
    """Service for dashboard customization and management."""

    def __init__(self, db: Session = None):
        self.db = db
        # Analytics service removed - focusing on dashboard customization only

    # All metrics calculation methods have been removed

    # Dashboard Management Methods
    async def create_dashboard(self, user_id: int, dashboard_data: DashboardCreate) -> Dashboard:
        """Create a new dashboard."""
        try:
            # Check if this should be the default dashboard
            if dashboard_data.is_default:
                # Unset any existing default dashboard
                existing_default = self.db.query(Dashboard).filter(
                    and_(Dashboard.user_id == user_id, Dashboard.is_default == True)
                ).first()
                if existing_default:
                    existing_default.is_default = False

            # Serialize for database storage
            layout_config = serialize_for_db(dashboard_data.layout_config)
            theme_config = serialize_for_db(dashboard_data.theme_config)

            dashboard = Dashboard(
                id=str(uuid.uuid4()),
                user_id=user_id,
                name=dashboard_data.name,
                description=dashboard_data.description,
                is_default=dashboard_data.is_default,
                is_public=dashboard_data.is_public,
                layout_config=layout_config,
                theme_config=theme_config,
                refresh_interval=dashboard_data.refresh_interval
            )

            self.db.add(dashboard)
            self.db.commit()
            self.db.refresh(dashboard)

            # Process data source assignments if provided
            if dashboard_data.data_source_assignments:
                for assignment in dashboard_data.data_source_assignments:
                    try:
                        # Find existing data source by system_data_source_id
                        data_source = self.db.query(DataSource).filter(
                            and_(
                                DataSource.id == assignment.system_data_source_id,
                                DataSource.user_id == user_id
                            )
                        ).first()

                        if data_source:
                            # Assign existing data source to dashboard
                            dashboard.data_sources.append(data_source)
                            logger.info(f"Assigned data source {data_source.id} to dashboard {dashboard.id}")
                        else:
                            logger.warning(f"Data source {assignment.system_data_source_id} not found for user {user_id}")
                    except Exception as e:
                        logger.warning(f"Failed to assign data source: {e}")
                        continue

                self.db.commit()

            logger.info(f"Created dashboard: {dashboard.name} for user {user_id}")
            return dashboard

        except Exception as e:
            logger.error(f"Error creating dashboard: {e}")
            self.db.rollback()
            raise

    async def get_user_dashboards(self, user_id: int) -> List[Dashboard]:
        """Get all dashboards for a user."""
        try:
            dashboards = self.db.query(Dashboard).filter(
                Dashboard.user_id == user_id
            ).order_by(Dashboard.is_default.desc(), Dashboard.created_at.desc()).all()

            logger.info(f"Retrieved {len(dashboards)} dashboards for user {user_id}")
            return dashboards

        except Exception as e:
            logger.error(f"Error getting user dashboards: {e}")
            return []

    async def get_dashboard(self, dashboard_id: str, user_id: int) -> Optional[Dashboard]:
        """Get a specific dashboard."""
        try:
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if dashboard:
                logger.info(f"Retrieved dashboard: {dashboard.name} for user {user_id}")
            else:
                logger.warning(f"Dashboard {dashboard_id} not found for user {user_id}")

            return dashboard

        except Exception as e:
            logger.error(f"Error getting dashboard: {e}")
            return None

    async def get_dashboard_layout(self, dashboard_id: str, user_id: int) -> Dict[str, Any]:
        """Get dashboard layout with sections and widgets."""
        try:
            dashboard = await self.get_dashboard(dashboard_id, user_id)
            if not dashboard:
                raise ValueError("Dashboard not found")

            # Get sections with widgets
            sections = self.db.query(DashboardSection).filter(
                and_(
                    DashboardSection.dashboard_id == dashboard_id,
                    DashboardSection.user_id == user_id,
                    DashboardSection.is_active == True
                )
            ).order_by(DashboardSection.position).all()

            # Get widgets for all sections
            section_ids = [section.id for section in sections]
            widgets = []
            if section_ids:
                widgets = self.db.query(DashboardWidget).filter(
                    and_(
                        DashboardWidget.section_id.in_(section_ids),
                        DashboardWidget.user_id == user_id,
                        DashboardWidget.is_active == True
                    )
                ).order_by(DashboardWidget.created_at).all()

            # Build layout response
            layout_sections = []
            for section in sections:
                section_widgets = [w for w in widgets if w.section_id == section.id]

                layout_sections.append({
                    "id": section.id,
                    "name": section.name,
                    "description": section.description,
                    "color": section.color,
                    "icon": section.icon,
                    "layout_config": section.layout_config or {},
                    "customization": section.customization or {},
                    "data_source_id": section.data_source_id,
                    "position": section.position,
                    "is_active": section.is_active,
                    "created_at": section.created_at.isoformat(),
                    "updated_at": section.updated_at.isoformat(),
                    "widget_count": len(section_widgets),
                    "widgets": [
                        {
                            "id": widget.id,
                            "name": widget.name,
                            "description": widget.description,
                            "widget_type": widget.widget_type,
                            "config": widget.config or {},
                            "position": widget.position or {},
                            "data_source_id": widget.data_source_id,
                            "is_active": widget.is_active,
                            "created_at": widget.created_at.isoformat(),
                            "updated_at": widget.updated_at.isoformat()
                        }
                        for widget in section_widgets
                    ]
                })

            # Convert to response format
            from ..models.dashboard_customization import DashboardResponse, SectionResponse, WidgetResponse

            dashboard_response = DashboardResponse(
                id=dashboard.id,
                name=dashboard.name,
                description=dashboard.description,
                is_default=dashboard.is_default,
                is_public=dashboard.is_public,
                layout_config=dashboard.layout_config or {},
                theme_config=dashboard.theme_config or {},
                refresh_interval=dashboard.refresh_interval,
                created_at=dashboard.created_at.isoformat(),
                updated_at=dashboard.updated_at.isoformat()
            )

            section_responses = []
            widget_responses = []

            for section in sections:
                section_widgets = [w for w in widgets if w.section_id == section.id]

                section_response = SectionResponse(
                    id=section.id,
                    dashboard_id=section.dashboard_id,
                    name=section.name,
                    description=section.description,
                    color=section.color,
                    icon=section.icon,
                    layout_config=section.layout_config or {},
                    customization=section.customization or {},
                    data_source_id=section.data_source_id,
                    position=section.position,
                    is_active=section.is_active,
                    created_at=section.created_at.isoformat(),
                    updated_at=section.updated_at.isoformat(),
                    widget_count=len(section_widgets)
                )
                section_responses.append(section_response)

                # Add widgets for this section
                for widget in section_widgets:
                    widget_response = WidgetResponse(
                        id=widget.id,
                        section_id=widget.section_id,
                        name=widget.name,
                        description=widget.description,
                        widget_type=widget.widget_type,
                        config=widget.config or {},
                        position=widget.position or {},
                        data_source_id=widget.data_source_id,
                        is_active=widget.is_active,
                        created_at=widget.created_at.isoformat(),
                        updated_at=widget.updated_at.isoformat()
                    )
                    widget_responses.append(widget_response)

            return {
                "dashboard": dashboard_response,
                "sections": section_responses,
                "widgets": widget_responses,
                "total_sections": len(section_responses),
                "total_widgets": len(widget_responses),
                "last_updated": max(
                    [dashboard.updated_at] +
                    [section.updated_at for section in sections] +
                    [widget.updated_at for widget in widgets]
                ).isoformat() if sections or widgets else dashboard.updated_at.isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting dashboard layout {dashboard_id}: {e}")
            raise

    async def update_dashboard(self, dashboard_id: str, user_id: int, dashboard_data: DashboardUpdate) -> Optional[Dashboard]:
        """Update a dashboard."""
        try:
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not dashboard:
                logger.warning(f"Dashboard {dashboard_id} not found for user {user_id}")
                return None

            # Handle default dashboard logic
            if dashboard_data.is_default and not dashboard.is_default:
                # Unset any existing default dashboard
                existing_default = self.db.query(Dashboard).filter(
                    and_(Dashboard.user_id == user_id, Dashboard.is_default == True)
                ).first()
                if existing_default:
                    existing_default.is_default = False

            # Update dashboard fields with proper serialization for JSON fields
            update_data = dashboard_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                if field in ['layout_config', 'theme_config'] and value is not None:
                    # Serialize for database storage
                    value = serialize_for_db(value)
                setattr(dashboard, field, value)

            dashboard.updated_at = get_utc_now()
            self.db.commit()
            self.db.refresh(dashboard)

            logger.info(f"Updated dashboard: {dashboard.name} for user {user_id}")
            return dashboard

        except Exception as e:
            logger.error(f"Error updating dashboard: {e}")
            self.db.rollback()
            raise

    async def delete_dashboard(self, dashboard_id: str, user_id: int) -> bool:
        """Delete a dashboard."""
        try:
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not dashboard:
                logger.warning(f"Dashboard {dashboard_id} not found for user {user_id}")
                return False

            # Don't allow deletion of the last dashboard
            user_dashboard_count = self.db.query(func.count(Dashboard.id)).filter(
                Dashboard.user_id == user_id
            ).scalar()

            if user_dashboard_count <= 1:
                logger.warning(f"Cannot delete the last dashboard for user {user_id}")
                return False

            # If this was the default dashboard, make another one default
            if dashboard.is_default:
                next_dashboard = self.db.query(Dashboard).filter(
                    and_(Dashboard.user_id == user_id, Dashboard.id != dashboard_id)
                ).first()
                if next_dashboard:
                    next_dashboard.is_default = True

            self.db.delete(dashboard)
            self.db.commit()

            logger.info(f"Deleted dashboard: {dashboard.name} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting dashboard: {e}")
            self.db.rollback()
            raise

    async def duplicate_dashboard(self, dashboard_id: str, user_id: int, new_name: str) -> Optional[Dashboard]:
        """Duplicate a dashboard."""
        try:
            original = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not original:
                logger.warning(f"Dashboard {dashboard_id} not found for user {user_id}")
                return None

            # Create new dashboard
            new_dashboard = Dashboard(
                id=str(uuid.uuid4()),
                user_id=user_id,
                name=new_name,
                description=f"Copy of {original.description}" if original.description else None,
                is_default=False,  # Duplicates are never default
                is_public=original.is_public,
                layout_config=original.layout_config,
                theme_config=original.theme_config,
                refresh_interval=original.refresh_interval
            )

            self.db.add(new_dashboard)
            self.db.flush()  # Get the ID

            # Copy sections and widgets
            for section in original.sections:
                new_section = DashboardSection(
                    id=str(uuid.uuid4()),
                    dashboard_id=new_dashboard.id,
                    name=section.name,
                    description=section.description,
                    position=section.position,
                    color=section.color,
                    icon=section.icon,
                    is_collapsible=section.is_collapsible,
                    is_collapsed=section.is_collapsed
                )
                self.db.add(new_section)
                self.db.flush()

                # Copy widgets
                for widget in section.widgets:
                    new_widget = DashboardWidget(
                        id=str(uuid.uuid4()),
                        section_id=new_section.id,
                        title=widget.title,
                        type=widget.type,
                        position=widget.position,
                        width=widget.width,
                        height=widget.height,
                        data_source_id=widget.data_source_id,
                        config=widget.config,
                        is_visible=widget.is_visible
                    )
                    self.db.add(new_widget)

            # Copy data source assignments
            for data_source in original.data_sources:
                new_dashboard.data_sources.append(data_source)

            self.db.commit()
            self.db.refresh(new_dashboard)

            logger.info(f"Duplicated dashboard: {original.name} -> {new_name} for user {user_id}")
            return new_dashboard

        except Exception as e:
            logger.error(f"Error duplicating dashboard: {e}")
            self.db.rollback()
            raise

    # Section Management Methods
    async def create_section(self, user_id: int, section_data: SectionCreate) -> DashboardSection:
        """Create a new dashboard section."""
        try:
            # Verify dashboard ownership
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == section_data.dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not dashboard:
                raise ValueError(f"Dashboard {section_data.dashboard_id} not found for user {user_id}")

            # Get the next position for this dashboard
            max_position = self.db.query(func.max(DashboardSection.position)).filter(
                DashboardSection.dashboard_id == section_data.dashboard_id
            ).scalar() or 0

            # Create new section with proper defaults
            default_layout_config = {
                "columns": 12,
                "rows": 6,
                "grid_gap": 16,
                "responsive": True
            }
            default_customization = {
                "backgroundColor": "#FFFFFF",
                "borderColor": "#E5E7EB"
            }

            # Serialize for database storage
            layout_config = serialize_for_db(section_data.layout_config) if section_data.layout_config else default_layout_config
            customization = serialize_for_db(section_data.customization) if section_data.customization else default_customization

            section = DashboardSection(
                id=str(uuid.uuid4()),
                dashboard_id=section_data.dashboard_id,
                user_id=user_id,
                name=section_data.name,
                description=section_data.description,
                color=section_data.color,
                icon=section_data.icon,
                layout_config=layout_config,
                customization=customization,
                data_source_id=section_data.data_source_id,
                position=max_position + 1,
                is_active=True
            )

            self.db.add(section)
            self.db.commit()
            self.db.refresh(section)

            logger.info(f"Created section {section.id} for dashboard {section_data.dashboard_id}")
            return section

        except Exception as e:
            logger.error(f"Error creating section: {e}")
            self.db.rollback()
            raise

    async def delete_section(self, section_id: str, user_id: int) -> bool:
        """Delete a dashboard section and all its widgets."""
        try:
            # Verify section ownership
            section = self.db.query(DashboardSection).filter(
                and_(DashboardSection.id == section_id, DashboardSection.user_id == user_id)
            ).first()

            if not section:
                raise ValueError(f"Section {section_id} not found for user {user_id}")

            # Delete all widgets in this section first (cascade should handle this, but being explicit)
            widgets = self.db.query(DashboardWidget).filter(
                DashboardWidget.section_id == section_id
            ).all()

            for widget in widgets:
                self.db.delete(widget)

            # Delete the section
            self.db.delete(section)
            self.db.commit()

            logger.info(f"Deleted section {section_id} and {len(widgets)} widgets for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting section {section_id}: {e}")
            self.db.rollback()
            raise

    async def update_section(self, section_id: str, user_id: int, section_data: 'SectionUpdate') -> 'DashboardSection':
        """Update a dashboard section."""
        try:
            # Import here to avoid circular imports
            from app.models.dashboard_customization import SectionUpdate

            # Verify section ownership
            section = self.db.query(DashboardSection).filter(
                and_(DashboardSection.id == section_id, DashboardSection.user_id == user_id)
            ).first()

            if not section:
                raise ValueError(f"Section {section_id} not found for user {user_id}")

            # Update section fields
            if section_data.name is not None:
                section.name = section_data.name
            if section_data.description is not None:
                section.description = section_data.description
            if section_data.color is not None:
                section.color = section_data.color
            if section_data.icon is not None:
                section.icon = section_data.icon
            if section_data.layout_config is not None:
                # Serialize for database storage
                section.layout_config = serialize_for_db(section_data.layout_config)
            if section_data.customization is not None:
                # Serialize for database storage
                section.customization = serialize_for_db(section_data.customization)
            if section_data.data_source_id is not None:
                section.data_source_id = section_data.data_source_id
            if section_data.position is not None:
                section.position = section_data.position
            if section_data.is_active is not None:
                section.is_active = section_data.is_active

            self.db.commit()
            self.db.refresh(section)

            logger.info(f"Updated section {section_id} for user {user_id}")
            return section

        except Exception as e:
            logger.error(f"Error updating section {section_id}: {e}")
            self.db.rollback()
            raise

    # Widget Management Methods
    async def create_widget(self, user_id: int, widget_data: 'WidgetCreate') -> 'DashboardWidget':
        """Create a new dashboard widget."""
        try:
            # Import here to avoid circular imports
            from app.models.dashboard_customization import WidgetCreate, DashboardWidget

            # Verify section exists and get user_id if not provided
            section = self.db.query(DashboardSection).filter(
                DashboardSection.id == widget_data.section_id
            ).first()

            if not section:
                raise ValueError(f"Section {widget_data.section_id} not found")

            # Verify section ownership
            if section.user_id != user_id:
                raise ValueError(f"Section {widget_data.section_id} not found for user {user_id}")

            # Get the next position for this section (simplified approach)
            existing_widgets = self.db.query(DashboardWidget).filter(
                DashboardWidget.section_id == widget_data.section_id
            ).all()

            max_x = 0
            for widget in existing_widgets:
                if widget.position_config and 'x' in widget.position_config:
                    try:
                        x_pos = int(widget.position_config['x'])
                        max_x = max(max_x, x_pos)
                    except (ValueError, TypeError):
                        continue

            # Serialize for database storage
            data_config = serialize_for_db(widget_data.data_config) if widget_data.data_config else {}
            visualization_config = serialize_for_db(widget_data.visualization_config) if widget_data.visualization_config else {}
            position_config = serialize_for_db(widget_data.position_config) if widget_data.position_config else {"x": max_x + 1, "y": 0, "w": 4, "h": 3}
            customization = serialize_for_db(widget_data.customization) if widget_data.customization else {}

            # Create new widget
            widget = DashboardWidget(
                id=str(uuid.uuid4()),
                section_id=widget_data.section_id,
                user_id=user_id,
                title=widget_data.title,
                widget_type=widget_data.widget_type,
                data_config=data_config,
                visualization_config=visualization_config,
                position_config=position_config,
                customization=customization,
                refresh_interval=widget_data.refresh_interval or 300,
                is_active=True
            )

            self.db.add(widget)
            self.db.commit()
            self.db.refresh(widget)

            logger.info(f"Created widget {widget.id} for section {widget_data.section_id}")
            return widget

        except Exception as e:
            logger.error(f"Error creating widget: {e}")
            self.db.rollback()
            raise

    async def get_widget(self, widget_id: str, user_id: int) -> Optional['DashboardWidget']:
        """Get a specific widget."""
        try:
            widget = self.db.query(DashboardWidget).filter(
                and_(DashboardWidget.id == widget_id, DashboardWidget.user_id == user_id)
            ).first()

            if widget:
                logger.info(f"Retrieved widget: {widget.title} for user {user_id}")
            else:
                logger.warning(f"Widget {widget_id} not found for user {user_id}")

            return widget

        except Exception as e:
            logger.error(f"Error getting widget: {e}")
            return None

    async def delete_widget(self, widget_id: str, user_id: int) -> bool:
        """Delete a dashboard widget."""
        try:
            # Verify widget ownership
            widget = self.db.query(DashboardWidget).filter(
                and_(DashboardWidget.id == widget_id, DashboardWidget.user_id == user_id)
            ).first()

            if not widget:
                raise ValueError(f"Widget {widget_id} not found for user {user_id}")

            # Delete the widget
            self.db.delete(widget)
            self.db.commit()

            logger.info(f"Deleted widget {widget_id} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting widget {widget_id}: {e}")
            self.db.rollback()
            raise

    async def update_widget(self, widget_id: str, user_id: int, widget_data: 'WidgetUpdate') -> 'DashboardWidget':
        """Update a dashboard widget."""
        try:
            # Import here to avoid circular imports
            from app.models.dashboard_customization import WidgetUpdate

            # Verify widget ownership
            widget = self.db.query(DashboardWidget).filter(
                and_(DashboardWidget.id == widget_id, DashboardWidget.user_id == user_id)
            ).first()

            if not widget:
                raise ValueError(f"Widget {widget_id} not found for user {user_id}")

            # Update widget fields
            if widget_data.title is not None:
                widget.title = widget_data.title
            if widget_data.data_config is not None:
                # Serialize for database storage
                widget.data_config = serialize_for_db(widget_data.data_config)
            if widget_data.visualization_config is not None:
                # Serialize for database storage
                widget.visualization_config = serialize_for_db(widget_data.visualization_config)
            if widget_data.position_config is not None:
                # Serialize for database storage
                widget.position_config = serialize_for_db(widget_data.position_config)
            if widget_data.customization is not None:
                # Serialize for database storage
                widget.customization = serialize_for_db(widget_data.customization)
            if widget_data.refresh_interval is not None:
                widget.refresh_interval = widget_data.refresh_interval
            if widget_data.is_active is not None:
                widget.is_active = widget_data.is_active

            self.db.commit()
            self.db.refresh(widget)

            logger.info(f"Updated widget {widget_id} for user {user_id}")
            return widget

        except Exception as e:
            logger.error(f"Error updating widget {widget_id}: {e}")
            self.db.rollback()
            raise

    async def move_widget(self, widget_id: str, target_section_id: str, position: Dict[str, Any], user_id: int) -> 'DashboardWidget':
        """Move a widget to a new position and/or section."""
        try:
            # Verify widget ownership
            widget = self.db.query(DashboardWidget).filter(
                and_(DashboardWidget.id == widget_id, DashboardWidget.user_id == user_id)
            ).first()

            if not widget:
                raise ValueError(f"Widget {widget_id} not found for user {user_id}")

            # Verify target section ownership
            target_section = self.db.query(DashboardSection).filter(
                and_(DashboardSection.id == target_section_id, DashboardSection.user_id == user_id)
            ).first()

            if not target_section:
                raise ValueError(f"Target section {target_section_id} not found for user {user_id}")

            # Update widget position and section
            widget.section_id = target_section_id
            widget.position_config = serialize_for_db(position)

            self.db.commit()
            self.db.refresh(widget)

            logger.info(f"Moved widget {widget_id} to section {target_section_id} for user {user_id}")
            return widget

        except Exception as e:
            logger.error(f"Error moving widget {widget_id}: {e}")
            self.db.rollback()
            raise

    # Dashboard Data Source Management Methods
    async def add_dashboard_data_source(self, dashboard_id: str, user_id: int, data_source_data: DashboardDataSourceCreate) -> DashboardDataSourceResponse:
        """Add a data source to a dashboard."""
        try:
            # Verify dashboard ownership
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not dashboard:
                raise ValueError(f"Dashboard {dashboard_id} not found")

            # Serialize for database storage
            connection_config = serialize_for_db(data_source_data.connection_config) if data_source_data.connection_config else {}
            filters = serialize_for_db(data_source_data.filters) if data_source_data.filters else {}

            # Create dashboard data source
            data_source = DashboardDataSource(
                id=str(uuid.uuid4()),
                dashboard_id=dashboard_id,
                name=data_source_data.name,
                type=data_source_data.type,
                connection_config=connection_config,
                query=data_source_data.query,
                filters=filters,
                refresh_rate=data_source_data.refresh_rate,
                cache_enabled=data_source_data.cache_enabled,
                timeout=data_source_data.timeout,
                is_active=True
            )

            self.db.add(data_source)
            self.db.commit()
            self.db.refresh(data_source)

            logger.info(f"Added data source {data_source.name} to dashboard {dashboard_id}")

            return DashboardDataSourceResponse(
                id=data_source.id,
                dashboard_id=data_source.dashboard_id,
                name=data_source.name,
                type=data_source.type,
                connection_config=data_source.connection_config,
                query=data_source.query,
                filters=data_source.filters,
                refresh_rate=data_source.refresh_rate,
                cache_enabled=data_source.cache_enabled,
                timeout=data_source.timeout,
                is_active=data_source.is_active,
                created_at=data_source.created_at.isoformat(),
                updated_at=data_source.updated_at.isoformat()
            )

        except Exception as e:
            logger.error(f"Error adding dashboard data source: {e}")
            self.db.rollback()
            raise

    async def update_dashboard_data_source(self, dashboard_id: str, data_source_id: str, user_id: int, data_source_data: DashboardDataSourceUpdate) -> DashboardDataSourceResponse:
        """Update a dashboard data source."""
        try:
            # Verify dashboard ownership
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not dashboard:
                raise ValueError(f"Dashboard {dashboard_id} not found")

            # Get data source
            data_source = self.db.query(DashboardDataSource).filter(
                and_(DashboardDataSource.id == data_source_id, DashboardDataSource.dashboard_id == dashboard_id)
            ).first()

            if not data_source:
                raise ValueError(f"Data source {data_source_id} not found")

            # Update data source fields with proper serialization for JSON fields
            update_data = data_source_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                if field in ['connection_config', 'filters'] and value is not None:
                    # Serialize for database storage
                    value = serialize_for_db(value)
                setattr(data_source, field, value)

            self.db.commit()
            self.db.refresh(data_source)

            logger.info(f"Updated data source {data_source.name} in dashboard {dashboard_id}")

            return DashboardDataSourceResponse(
                id=data_source.id,
                dashboard_id=data_source.dashboard_id,
                name=data_source.name,
                type=data_source.type,
                connection_config=data_source.connection_config,
                query=data_source.query,
                filters=data_source.filters,
                refresh_rate=data_source.refresh_rate,
                cache_enabled=data_source.cache_enabled,
                timeout=data_source.timeout,
                is_active=data_source.is_active,
                created_at=data_source.created_at.isoformat(),
                updated_at=data_source.updated_at.isoformat()
            )

        except Exception as e:
            logger.error(f"Error updating dashboard data source: {e}")
            self.db.rollback()
            raise

    async def remove_dashboard_data_source(self, dashboard_id: str, data_source_id: str, user_id: int) -> bool:
        """Remove a data source from a dashboard."""
        try:
            # Verify dashboard ownership
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not dashboard:
                raise ValueError(f"Dashboard {dashboard_id} not found")

            # Get data source
            data_source = self.db.query(DashboardDataSource).filter(
                and_(DashboardDataSource.id == data_source_id, DashboardDataSource.dashboard_id == dashboard_id)
            ).first()

            if not data_source:
                raise ValueError(f"Data source {data_source_id} not found")

            self.db.delete(data_source)
            self.db.commit()

            logger.info(f"Removed data source {data_source.name} from dashboard {dashboard_id}")
            return True

        except Exception as e:
            logger.error(f"Error removing dashboard data source: {e}")
            self.db.rollback()
            raise

    async def bulk_assign_data_sources(self, dashboard_id: str, data_source_ids: List[str], user_id: int) -> Dict[str, Any]:
        """Bulk assign multiple data sources to a dashboard."""
        try:
            # Verify dashboard ownership
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not dashboard:
                raise ValueError(f"Dashboard {dashboard_id} not found")

            assigned = 0
            failed = 0
            errors = []

            for data_source_id in data_source_ids:
                try:
                    # Check if data source exists in system
                    system_data_source = self.db.query(DataSource).filter(
                        DataSource.id == data_source_id
                    ).first()

                    if not system_data_source:
                        errors.append(f"System data source {data_source_id} not found")
                        failed += 1
                        continue

                    # Check if already assigned
                    existing = self.db.query(DashboardDataSource).filter(
                        and_(
                            DashboardDataSource.dashboard_id == dashboard_id,
                            DashboardDataSource.name == system_data_source.name
                        )
                    ).first()

                    if existing:
                        errors.append(f"Data source {system_data_source.name} already assigned")
                        failed += 1
                        continue

                    # Create dashboard data source assignment
                    assignment = DashboardDataSourceAssignment(
                        id=str(uuid.uuid4()),
                        dashboard_id=dashboard_id,
                        system_data_source_id=data_source_id,
                        alias=system_data_source.name,  # Use data source name as default alias
                        is_active=True
                    )

                    self.db.add(assignment)
                    assigned += 1

                except Exception as e:
                    errors.append(f"Failed to assign {data_source_id}: {str(e)}")
                    failed += 1

            self.db.commit()

            logger.info(f"Bulk assigned {assigned} data sources to dashboard {dashboard_id}, {failed} failed")

            return {
                "assigned": assigned,
                "failed": failed,
                "errors": errors
            }

        except Exception as e:
            logger.error(f"Error bulk assigning data sources: {e}")
            self.db.rollback()
            raise

    async def assign_system_data_source_to_dashboard(self, dashboard_id: str, user_id: int, assignment_data: DashboardDataSourceAssignmentCreate) -> DashboardDataSourceAssignmentResponse:
        """Assign an existing system data source to a dashboard."""
        try:
            # Verify dashboard ownership
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not dashboard:
                raise ValueError(f"Dashboard {dashboard_id} not found")

            # Verify system data source exists and user has access
            system_data_source = self.db.query(DataSource).filter(
                and_(
                    DataSource.id == assignment_data.system_data_source_id,
                    DataSource.user_id == user_id
                )
            ).first()

            if not system_data_source:
                raise ValueError(f"System data source {assignment_data.system_data_source_id} not found or access denied")

            # Check if assignment already exists
            existing_assignment = self.db.query(DashboardDataSourceAssignment).filter(
                and_(
                    DashboardDataSourceAssignment.dashboard_id == dashboard_id,
                    DashboardDataSourceAssignment.system_data_source_id == assignment_data.system_data_source_id
                )
            ).first()

            if existing_assignment:
                # Update existing assignment
                existing_assignment.alias = assignment_data.alias
                existing_assignment.is_active = assignment_data.is_active
                existing_assignment.updated_at = get_utc_now()
                self.db.commit()
                self.db.refresh(existing_assignment)

                return DashboardDataSourceAssignmentResponse(
                    id=existing_assignment.id,
                    dashboard_id=existing_assignment.dashboard_id,
                    system_data_source_id=existing_assignment.system_data_source_id,
                    alias=existing_assignment.alias,
                    is_active=existing_assignment.is_active,
                    created_at=existing_assignment.created_at.isoformat(),
                    updated_at=existing_assignment.updated_at.isoformat(),
                    system_data_source={
                        'id': system_data_source.id,
                        'name': system_data_source.name,
                        'type': system_data_source.type,
                        'description': system_data_source.description,
                        'is_active': system_data_source.is_active,
                        'updated_at': system_data_source.updated_at.isoformat() if system_data_source.updated_at else None
                    }
                )
            else:
                # Create new assignment
                assignment = DashboardDataSourceAssignment(
                    id=str(uuid.uuid4()),
                    dashboard_id=dashboard_id,
                    system_data_source_id=assignment_data.system_data_source_id,
                    alias=assignment_data.alias,
                    is_active=assignment_data.is_active
                )

                self.db.add(assignment)
                self.db.commit()
                self.db.refresh(assignment)

                return DashboardDataSourceAssignmentResponse(
                    id=assignment.id,
                    dashboard_id=assignment.dashboard_id,
                    system_data_source_id=assignment.system_data_source_id,
                    alias=assignment.alias,
                    is_active=assignment.is_active,
                    created_at=assignment.created_at.isoformat(),
                    updated_at=assignment.updated_at.isoformat(),
                    system_data_source={
                        'id': system_data_source.id,
                        'name': system_data_source.name,
                        'type': system_data_source.type,
                        'description': system_data_source.description,
                        'is_active': system_data_source.is_active,
                        'updated_at': system_data_source.updated_at.isoformat() if system_data_source.updated_at else None
                    }
                )

        except Exception as e:
            logger.error(f"Error assigning system data source to dashboard: {e}")
            self.db.rollback()
            raise

    async def get_dashboard_data_sources(self, dashboard_id: str, user_id: int) -> List[DashboardDataSourceAssignmentResponse]:
        """Get all data source assignments for a dashboard."""
        try:
            # Verify dashboard ownership
            dashboard = self.db.query(Dashboard).filter(
                and_(Dashboard.id == dashboard_id, Dashboard.user_id == user_id)
            ).first()

            if not dashboard:
                raise ValueError(f"Dashboard {dashboard_id} not found")

            # Get assignments with joined system data source info
            assignments = self.db.query(DashboardDataSourceAssignment).filter(
                DashboardDataSourceAssignment.dashboard_id == dashboard_id
            ).all()

            result = []
            for assignment in assignments:
                # Get system data source info
                system_data_source = None
                if assignment.system_data_source:
                    system_data_source = {
                        "id": assignment.system_data_source.id,
                        "name": assignment.system_data_source.name,
                        "type": assignment.system_data_source.type,
                        "description": assignment.system_data_source.description,
                        "is_active": assignment.system_data_source.is_active,
                        "metadata": assignment.system_data_source.source_metadata,
                        "user_id": assignment.system_data_source.user_id,
                        "created_at": assignment.system_data_source.created_at.isoformat() if assignment.system_data_source.created_at else None,
                        "updated_at": assignment.system_data_source.updated_at.isoformat() if assignment.system_data_source.updated_at else None
                    }

                result.append(DashboardDataSourceAssignmentResponse(
                    id=assignment.id,
                    dashboard_id=assignment.dashboard_id,
                    system_data_source_id=assignment.system_data_source_id,
                    alias=assignment.alias,
                    is_active=assignment.is_active,
                    created_at=assignment.created_at.isoformat(),
                    updated_at=assignment.updated_at.isoformat(),
                    system_data_source=system_data_source
                ))

            return result

        except Exception as e:
            logger.error(f"Error getting dashboard data source assignments: {e}")
            return []
