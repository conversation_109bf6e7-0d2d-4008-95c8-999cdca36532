#!/usr/bin/env python3
"""
Test script for the improved data access tool.
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from agents.tools.mcp.data_access import DataAccessTool

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_data_access():
    """Test the refactored data access tool with various data sources."""

    # Initialize the tool
    tool = DataAccessTool()
    await tool._initialize({})

    # Test cases
    test_cases = [
        {
            "name": "Test with file name 'supermarket sales'",
            "data_source": {"name": "supermarket sales"},
            "description": "Should find files with 'supermarket' and 'sales' in the name"
        },
        {
            "name": "Test with recent file ID",
            "data_source": {"id": "eaf2c57f-c6c9-4bf6-ae03-6202cb36fecc"},
            "description": "Should find the specific file by ID"
        },
        {
            "name": "Test with source_metadata",
            "data_source": {
                "source_metadata": {
                    "file_id": "eaf2c57f-c6c9-4bf6-ae03-6202cb36fecc",
                    "file_name": "supermarket sales"
                }
            },
            "description": "Should prioritize source_metadata information"
        },
        {
            "name": "Test with no criteria (most recent file)",
            "data_source": {},
            "description": "Should return the most recently uploaded file"
        },
        {
            "name": "Test database resolution",
            "data_source": {"id": "eaf2c57f-c6c9-4bf6-ae03-6202cb36fecc"},
            "description": "Test database-based file resolution"
        },
        {
            "name": "Test full flow with _find_and_load_data",
            "data_source": {"name": "supermarket sales"},
            "description": "Test the complete refactored flow"
        }
    ]

    for test_case in test_cases:
        print(f"\n{'='*60}")
        print(f"TEST: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        print(f"Data source: {test_case['data_source']}")
        print(f"{'='*60}")

        try:
            if test_case['name'] == "Test full flow with _find_and_load_data":
                # Test the complete refactored method
                file_path, df = await tool._find_and_load_data(test_case['data_source'])

                if file_path and df is not None:
                    print(f"✓ Complete flow successful:")
                    print(f"  - File path: {file_path}")
                    print(f"  - Shape: {df.shape}")
                    print(f"  - Columns: {list(df.columns)}")
                    print(f"  - First few rows:")
                    print(df.head(3).to_string())
                else:
                    print("✗ Complete flow failed")
            elif test_case['name'] == "Test database resolution":
                # Test database resolution specifically
                file_path = await tool._resolve_file_path_from_database(test_case['data_source'])

                if file_path:
                    print(f"✓ Database resolution found: {file_path}")

                    # Test loading
                    df = await tool._load_dataframe(file_path)
                    if df is not None:
                        print(f"✓ Successfully loaded dataframe:")
                        print(f"  - Shape: {df.shape}")
                        print(f"  - Columns: {list(df.columns)}")
                        print(f"  - First few rows:")
                        print(df.head(3).to_string())
                    else:
                        print("✗ Failed to load dataframe")
                else:
                    print("✗ Database resolution failed")
            else:
                # Test smart resolution
                file_path = await tool._resolve_file_path_smart(test_case['data_source'])

                if file_path:
                    print(f"✓ Smart resolution found: {file_path}")

                    # Test loading
                    df = await tool._load_dataframe(file_path)
                    if df is not None:
                        print(f"✓ Successfully loaded dataframe:")
                        print(f"  - Shape: {df.shape}")
                        print(f"  - Columns: {list(df.columns)}")
                        print(f"  - First few rows:")
                        print(df.head(3).to_string())
                    else:
                        print("✗ Failed to load dataframe")
                else:
                    print("✗ Smart resolution failed")

        except Exception as e:
            print(f"✗ Error during test: {e}")
            logger.error(f"Test error: {e}", exc_info=True)

async def list_available_files():
    """List all available files in upload directories."""

    backend_dir = Path(__file__).parent
    upload_dirs = [
        backend_dir / "temp_uploads",
        backend_dir / "uploads",
        backend_dir / "data"
    ]

    print(f"\n{'='*60}")
    print("AVAILABLE FILES")
    print(f"{'='*60}")

    for upload_dir in upload_dirs:
        if upload_dir.exists():
            print(f"\nDirectory: {upload_dir}")
            try:
                files = list(upload_dir.glob("*"))
                if files:
                    # Sort by modification time (newest first)
                    files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                    for i, file_path in enumerate(files[:10]):  # Show first 10 files
                        if file_path.is_file():
                            size = file_path.stat().st_size
                            mtime = file_path.stat().st_mtime
                            print(f"  {i+1:2d}. {file_path.name} ({size} bytes, modified: {mtime})")
                    if len(files) > 10:
                        print(f"  ... and {len(files) - 10} more files")
                else:
                    print("  (no files)")
            except Exception as e:
                print(f"  Error listing files: {e}")
        else:
            print(f"\nDirectory: {upload_dir} (does not exist)")

async def main():
    """Main test function."""
    print("Testing Data Access Tool Smart Resolution")

    # List available files first
    await list_available_files()

    # Run tests
    await test_data_access()

    print(f"\n{'='*60}")
    print("TEST COMPLETE")
    print(f"{'='*60}")

if __name__ == "__main__":
    asyncio.run(main())
