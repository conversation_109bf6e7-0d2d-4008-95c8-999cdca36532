"""
Language detection MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for detecting the language of user messages.
"""

import logging
import json
from typing import Dict, Any, List

from .base import BaseMCPTool
from agents.utils.model_providers.utils import get_model
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class LanguageDetectionTool(BaseMCPTool):
    """Tool for detecting the language of user messages."""

    def __init__(self):
        """Initialize the language detection tool."""
        super().__init__(
            name="detect_language",
            description="Detect the language of a user message",
            input_schema={
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "The user's message to analyze"
                    },
                    "provider": {
                        "type": "string",
                        "description": "AI provider to use for detection"
                    },
                    "model": {
                        "type": "string",
                        "description": "AI model to use for detection"
                    }
                },
                "required": ["message"]
            },
            annotations={
                "title": "Detect Language",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the language detection tool.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            message = arguments["message"]
            provider_id = arguments.get("provider", "groq")
            model_name = arguments.get("model", "llama-3.1-8b-instant")

            # Simple rule-based detection for common cases
            if self._is_simple_case(message):
                language = self._detect_simple_language(message)
                return {
                    "isError": False,
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps({
                                "language": language,
                                "confidence": 0.9,
                                "method": "rule_based"
                            })
                        }
                    ]
                }

            # Detect agent identity for context-aware language detection
            agent_id = arguments.get("persona_id") or arguments.get("agent_id")
            context = arguments.get("context", {})
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=context,
                intent_type="language_detection"
            )

            logger.info(f"Detected agent identity: {agent_identity} for language detection")

            # Use LLM for complex detection with agent awareness
            try:
                model = get_model(provider_id, model_name)
                if not model:
                    raise Exception(f"Model {model_name} not available for provider {provider_id}")

                # Create agent-aware language detection prompt
                prompt = await self._create_agent_aware_language_prompt(message, agent_identity)

                response = await model.ainvoke(prompt)
                response_text = response.content if hasattr(response, 'content') else str(response)

                # Try to extract JSON from response
                try:
                    # Look for JSON in the response
                    import re
                    json_match = re.search(r'\{[^}]+\}', response_text)
                    if json_match:
                        result = json.loads(json_match.group())
                        return {
                            "isError": False,
                            "content": [
                                {
                                    "type": "text",
                                    "text": json.dumps(result)
                                }
                            ]
                        }
                except json.JSONDecodeError:
                    pass

                # Fallback if JSON parsing fails
                return {
                    "isError": False,
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps({
                                "language": "en",
                                "confidence": 0.5,
                                "method": "fallback"
                            })
                        }
                    ]
                }

            except Exception as e:
                logger.error(f"LLM language detection failed: {e}")
                # Fallback to simple detection
                language = self._detect_simple_language(message)
                return {
                    "isError": False,
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps({
                                "language": language,
                                "confidence": 0.7,
                                "method": "fallback"
                            })
                        }
                    ]
                }

        except Exception as e:
            logger.error(f"Language detection failed: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Language detection failed: {str(e)}"
                    }
                ]
            }

    def _is_simple_case(self, message: str) -> bool:
        """Check if this is a simple case that can be handled with rules."""
        # Very short messages or common English patterns
        return len(message.strip()) < 20 or any(word in message.lower() for word in [
            "hello", "hi", "hey", "thanks", "thank you", "yes", "no", "ok", "okay"
        ])

    def _detect_simple_language(self, message: str) -> str:
        """Simple rule-based language detection."""
        message_lower = message.lower()
        
        # Spanish indicators
        if any(word in message_lower for word in ["hola", "gracias", "sí", "no", "por favor"]):
            return "es"
        
        # French indicators
        if any(word in message_lower for word in ["bonjour", "merci", "oui", "non", "s'il vous plaît"]):
            return "fr"
        
        # German indicators
        if any(word in message_lower for word in ["hallo", "danke", "ja", "nein", "bitte"]):
            return "de"
        
        # Default to English
        return "en"

    async def _create_agent_aware_language_prompt(self, message: str, agent_identity: str) -> str:
        """
        Create an agent-aware language detection prompt.

        Args:
            message: Message to analyze
            agent_identity: Detected agent identity

        Returns:
            Agent-aware language detection prompt
        """
        # Get agent-specific context for language detection
        agent_context = await self._get_agent_language_context(agent_identity)

        prompt = f"""You are a language detection system for a {agent_identity} agent.

AGENT CONTEXT:
{agent_context}

Detect the language of this message and respond with only a JSON object:

Message: "{message}"

Respond with this exact JSON format:
{{
    "language": "language_code (e.g., en, es, fr, de, etc.)",
    "confidence": confidence_score_between_0_and_1,
    "method": "llm_based"
}}"""

        return prompt

    async def _get_agent_language_context(self, agent_identity: str) -> str:
        """Get agent-specific context for language detection using system prompt capabilities."""
        try:
            # Get the agent's system prompt to extract capabilities and language preferences
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract capabilities and language context from system prompt
            capabilities = await self._extract_capabilities_from_prompt(system_prompt)
            language_preferences = await self._extract_language_preferences_from_prompt(system_prompt)

            # Create context based on extracted information
            if capabilities or language_preferences:
                context = self._create_capability_based_language_context(
                    agent_identity, capabilities, language_preferences
                )
                return context

        except Exception as e:
            logger.warning(f"Failed to get agent system prompt for {agent_identity}: {e}")

        # Fallback to static contexts if dynamic extraction fails
        return self._get_fallback_language_context(agent_identity)

    async def _extract_capabilities_from_prompt(self, system_prompt: str) -> List[str]:
        """Extract capabilities from agent system prompt."""
        capabilities = []

        if not system_prompt:
            return capabilities

        # Look for capability sections in the prompt
        capability_patterns = [
            r"Your capabilities include:\s*\n((?:- .+\n?)+)",
            r"capabilities:\s*\n((?:- .+\n?)+)",
            r"You can:\s*\n((?:- .+\n?)+)",
            r"I can help with:\s*\n((?:- .+\n?)+)",
            r"Your core expertise includes:\s*\n((?:- .+\n?)+)"
        ]

        import re
        for pattern in capability_patterns:
            match = re.search(pattern, system_prompt, re.IGNORECASE | re.MULTILINE)
            if match:
                capability_text = match.group(1)
                # Extract individual capabilities
                for line in capability_text.split('\n'):
                    line = line.strip()
                    if line.startswith('- '):
                        capability = line[2:].strip()
                        if capability:
                            capabilities.append(capability)
                break

        return capabilities

    async def _extract_language_preferences_from_prompt(self, system_prompt: str) -> Dict[str, Any]:
        """Extract language preferences and cultural context from system prompt."""
        preferences = {}

        if not system_prompt:
            return preferences

        # Look for language-related patterns
        import re

        # Check for multilingual capabilities
        if re.search(r"multilingual|multi-language|international|global", system_prompt, re.IGNORECASE):
            preferences["multilingual_capable"] = True

        # Check for specific language mentions
        language_patterns = {
            "spanish": r"spanish|español|castellano",
            "french": r"french|français|francais",
            "german": r"german|deutsch",
            "italian": r"italian|italiano",
            "portuguese": r"portuguese|português",
            "chinese": r"chinese|中文|mandarin",
            "japanese": r"japanese|日本語|nihongo"
        }

        detected_languages = []
        for lang, pattern in language_patterns.items():
            if re.search(pattern, system_prompt, re.IGNORECASE):
                detected_languages.append(lang)

        if detected_languages:
            preferences["supported_languages"] = detected_languages

        # Check for cultural context awareness
        if re.search(r"cultural|culture|localization|regional", system_prompt, re.IGNORECASE):
            preferences["cultural_aware"] = True

        return preferences

    def _create_capability_based_language_context(
        self,
        agent_identity: str,
        capabilities: List[str],
        language_preferences: Dict[str, Any]
    ) -> str:
        """Create language detection context based on agent capabilities and preferences."""

        context_parts = [f"You are detecting language for a {agent_identity} agent."]

        if capabilities:
            capability_list = '\n'.join([f"- {cap}" for cap in capabilities])
            context_parts.append(f"""
Agent capabilities:
{capability_list}

Consider these capabilities when detecting language:
- Technical terminology related to the agent's expertise
- Domain-specific vocabulary and jargon
- Professional language patterns in the agent's field""")

        if language_preferences:
            if language_preferences.get("multilingual_capable"):
                context_parts.append("- This agent supports multilingual interactions")

            if "supported_languages" in language_preferences:
                langs = ", ".join(language_preferences["supported_languages"])
                context_parts.append(f"- Agent has specific support for: {langs}")

            if language_preferences.get("cultural_aware"):
                context_parts.append("- Consider cultural and regional language variations")

        return '\n'.join(context_parts)

    def _get_fallback_language_context(self, agent_identity: str) -> str:
        """Fallback language context when dynamic extraction fails."""
        contexts = {
            "marketer": """
You are detecting language for a marketing agent. Consider:
- Marketing terminology and business language patterns
- Brand and campaign-related vocabulary
- International marketing contexts and multilingual content
            """,
            "analyst": """
You are detecting language for a data analyst. Consider:
- Technical and analytical terminology
- Data science and statistical language patterns
- Research and academic contexts
            """,
            "classifier": """
You are detecting language for a classification agent. Consider:
- Text processing and categorization contexts
- Document classification scenarios
- Multi-language content organization needs
            """,
            "concierge": """
You are detecting language for a general assistant. Consider:
- General conversation and assistance contexts
- Multi-domain language patterns
- User guidance and support scenarios
            """
        }

        return contexts.get(agent_identity, "You are detecting language for a general AI assistant.")
