"""
API endpoints for marketing content type management.

This module provides endpoints for managing and discovering marketing content types
in the extensible marketing system.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from agents.tools.mcp.marketing_strategy_generation import ContentTypeIntelligence, ContentTypeDefinition

logger = logging.getLogger(__name__)

# Create router for marketing content type endpoints
router = APIRouter(prefix="/marketing", tags=["marketing-content-types"])

# Initialize content type intelligence
content_type_intelligence = ContentTypeIntelligence()


class ContentTypeSuggestionRequest(BaseModel):
    """Request model for content type suggestions."""
    query: str = Field(..., description="Query to search for content types")
    limit: Optional[int] = Field(default=10, ge=1, le=50, description="Maximum number of suggestions")


class ContentTypeSuggestionResponse(BaseModel):
    """Response model for content type suggestions."""
    suggestions: List[str] = Field(..., description="List of suggested content types")
    total: int = Field(..., description="Total number of suggestions found")


class ContentTypeValidationRequest(BaseModel):
    """Request model for content type validation."""
    content_type: str = Field(..., description="Content type to validate")


class ContentTypeValidationResponse(BaseModel):
    """Response model for content type validation."""
    is_valid: bool = Field(..., description="Whether the content type is valid")
    content_type_definition: Optional[ContentTypeDefinition] = Field(None, description="Content type definition if valid")
    error_message: Optional[str] = Field(None, description="Error message if invalid")


class ContentTypeListResponse(BaseModel):
    """Response model for listing content types."""
    content_types: Dict[str, ContentTypeDefinition] = Field(..., description="Available content types")
    total: int = Field(..., description="Total number of content types")


@router.get("/content-types", response_model=ContentTypeListResponse)
async def get_content_types():
    """
    Get all available marketing content types.
    
    Returns:
        List of all available content type definitions
    """
    try:
        content_types = content_type_intelligence.get_all_content_types()
        
        return ContentTypeListResponse(
            content_types=content_types,
            total=len(content_types)
        )
    
    except Exception as e:
        logger.error(f"Error getting content types: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving content types: {str(e)}")


@router.post("/content-types/suggest", response_model=ContentTypeSuggestionResponse)
async def suggest_content_types(request: ContentTypeSuggestionRequest):
    """
    Suggest marketing content types based on a query.
    
    Args:
        request: Content type suggestion request
        
    Returns:
        List of suggested content types
    """
    try:
        suggestions = content_type_intelligence.suggest_content_types(request.query)
        
        # Limit results
        limited_suggestions = suggestions[:request.limit]
        
        return ContentTypeSuggestionResponse(
            suggestions=limited_suggestions,
            total=len(suggestions)
        )
    
    except Exception as e:
        logger.error(f"Error suggesting content types: {e}")
        raise HTTPException(status_code=500, detail=f"Error suggesting content types: {str(e)}")


@router.post("/content-types/validate", response_model=ContentTypeValidationResponse)
async def validate_content_type(request: ContentTypeValidationRequest):
    """
    Validate a marketing content type.
    
    Args:
        request: Content type validation request
        
    Returns:
        Validation result with content type definition if valid
    """
    try:
        is_valid = content_type_intelligence.is_valid_content_type(request.content_type)
        
        if is_valid:
            try:
                content_type_def = await content_type_intelligence.analyze_content_type(request.content_type)
                return ContentTypeValidationResponse(
                    is_valid=True,
                    content_type_definition=content_type_def,
                    error_message=None
                )
            except Exception as e:
                return ContentTypeValidationResponse(
                    is_valid=False,
                    content_type_definition=None,
                    error_message=str(e)
                )
        else:
            return ContentTypeValidationResponse(
                is_valid=False,
                content_type_definition=None,
                error_message=f"Invalid content type: '{request.content_type}'. Must be a marketing-related content type."
            )
    
    except Exception as e:
        logger.error(f"Error validating content type: {e}")
        raise HTTPException(status_code=500, detail=f"Error validating content type: {str(e)}")


@router.get("/content-types/{content_type}", response_model=ContentTypeDefinition)
async def get_content_type_definition(content_type: str):
    """
    Get definition for a specific content type.
    
    Args:
        content_type: Content type identifier
        
    Returns:
        Content type definition
    """
    try:
        if not content_type_intelligence.is_valid_content_type(content_type):
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid content type: '{content_type}'. Must be a marketing-related content type."
            )
        
        content_type_def = await content_type_intelligence.analyze_content_type(content_type)
        return content_type_def
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting content type definition: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting content type definition: {str(e)}")


@router.get("/content-types/search")
async def search_content_types(
    q: str = Query(..., description="Search query"),
    category: Optional[str] = Query(None, description="Filter by category"),
    format: Optional[str] = Query(None, description="Filter by supported format"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results")
):
    """
    Search marketing content types with filters.
    
    Args:
        q: Search query
        category: Filter by category (strategic, tactical, operational, creative)
        format: Filter by supported format
        limit: Maximum number of results
        
    Returns:
        Filtered list of content types
    """
    try:
        all_content_types = content_type_intelligence.get_all_content_types()
        results = []
        
        query_lower = q.lower()
        
        for content_type_id, definition in all_content_types.items():
            # Check if query matches
            matches_query = (
                query_lower in definition.name.lower() or
                query_lower in definition.description.lower() or
                any(query_lower in keyword.lower() for keyword in definition.keywords) or
                query_lower in content_type_id.lower()
            )
            
            if not matches_query:
                continue
            
            # Apply category filter
            if category and definition.category.value != category.lower():
                continue
            
            # Apply format filter
            if format and format.lower() not in [f.value for f in definition.supported_formats]:
                continue
            
            results.append({
                "id": content_type_id,
                "definition": definition
            })
        
        # Limit results
        limited_results = results[:limit]
        
        return {
            "results": limited_results,
            "total": len(results),
            "query": q,
            "filters": {
                "category": category,
                "format": format
            }
        }
    
    except Exception as e:
        logger.error(f"Error searching content types: {e}")
        raise HTTPException(status_code=500, detail=f"Error searching content types: {str(e)}")
