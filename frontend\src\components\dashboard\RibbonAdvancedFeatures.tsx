/**
 * Advanced Ribbon Features
 * 
 * This file provides advanced functionality for the ribbon toolbar:
 * - Contextual right-click menus
 * - Undo/redo functionality with history
 * - User preference persistence
 * - Advanced search with filters
 * - Command palette integration
 * - Customizable Quick Access toolbar
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
} from '@/components/ui/context-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Undo,
  Redo,
  Search,
  Settings,
  Star,
  Pin,
  Copy,
  Trash2,
  Edit,
  MoreHorizontal,
  History,
  Bookmark,
  Filter,
  SortAsc,
  Zap,
} from 'lucide-react';

// Undo/Redo system
interface HistoryAction {
  id: string;
  type: string;
  description: string;
  timestamp: Date;
  data: any;
  undo: () => void;
  redo: () => void;
}

export const useUndoRedo = () => {
  const [history, setHistory] = useState<HistoryAction[]>([]);
  const [currentIndex, setCurrentIndex] = useState(-1);

  const addAction = useCallback((action: Omit<HistoryAction, 'id' | 'timestamp'>) => {
    const newAction: HistoryAction = {
      ...action,
      id: crypto.randomUUID(),
      timestamp: new Date(),
    };

    setHistory(prev => {
      // Remove any actions after current index (when adding new action after undo)
      const newHistory = prev.slice(0, currentIndex + 1);
      newHistory.push(newAction);
      
      // Limit history to 50 actions
      if (newHistory.length > 50) {
        newHistory.shift();
        return newHistory;
      }
      
      return newHistory;
    });

    setCurrentIndex(prev => prev + 1);
  }, [currentIndex]);

  const undo = useCallback(() => {
    if (currentIndex >= 0) {
      const action = history[currentIndex];
      action.undo();
      setCurrentIndex(prev => prev - 1);
      return action;
    }
    return null;
  }, [history, currentIndex]);

  const redo = useCallback(() => {
    if (currentIndex < history.length - 1) {
      const action = history[currentIndex + 1];
      action.redo();
      setCurrentIndex(prev => prev + 1);
      return action;
    }
    return null;
  }, [history, currentIndex]);

  const canUndo = currentIndex >= 0;
  const canRedo = currentIndex < history.length - 1;

  return {
    history,
    currentIndex,
    addAction,
    undo,
    redo,
    canUndo,
    canRedo,
  };
};

// User preferences persistence
interface RibbonPreferences {
  quickAccessItems: string[];
  collapsedGroups: string[];
  activeTab: string;
  compactMode: boolean;
  showTooltips: boolean;
  keyboardShortcuts: Record<string, string>;
}

export const useRibbonPreferences = () => {
  const [preferences, setPreferences] = useState<RibbonPreferences>({
    quickAccessItems: ['save', 'undo', 'redo', 'refresh'],
    collapsedGroups: [],
    activeTab: 'file',
    compactMode: false,
    showTooltips: true,
    keyboardShortcuts: {
      'save': 'Ctrl+S',
      'undo': 'Ctrl+Z',
      'redo': 'Ctrl+Y',
      'refresh': 'F5',
      'search': 'Ctrl+/',
    },
  });

  // Load preferences from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('ribbon-preferences');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setPreferences(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.warn('Failed to parse ribbon preferences:', error);
      }
    }
  }, []);

  // Save preferences to localStorage
  const updatePreferences = useCallback((updates: Partial<RibbonPreferences>) => {
    setPreferences(prev => {
      const newPreferences = { ...prev, ...updates };
      localStorage.setItem('ribbon-preferences', JSON.stringify(newPreferences));
      return newPreferences;
    });
  }, []);

  return { preferences, updatePreferences };
};

// Advanced search with filters
export const useAdvancedSearch = (actions: any[]) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    category: '',
    hasShortcut: false,
    recentlyUsed: false,
  });

  const filteredActions = React.useMemo(() => {
    let filtered = actions;

    // Text search
    if (searchQuery) {
      filtered = filtered.filter(action =>
        action.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        action.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        action.keywords?.some((keyword: string) => 
          keyword.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(action => action.category === filters.category);
    }

    // Shortcut filter
    if (filters.hasShortcut) {
      filtered = filtered.filter(action => action.shortcut);
    }

    // Recently used filter
    if (filters.recentlyUsed) {
      const recentActions = JSON.parse(localStorage.getItem('recent-actions') || '[]');
      filtered = filtered.filter(action => recentActions.includes(action.id));
    }

    return filtered;
  }, [actions, searchQuery, filters]);

  return {
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    filteredActions,
  };
};

// Contextual right-click menu
export const RibbonContextMenu: React.FC<{
  children: React.ReactNode;
  onCustomize?: () => void;
  onAddToQuickAccess?: () => void;
  onRemoveFromQuickAccess?: () => void;
  onCopyAction?: () => void;
  isInQuickAccess?: boolean;
}> = ({
  children,
  onCustomize,
  onAddToQuickAccess,
  onRemoveFromQuickAccess,
  onCopyAction,
  isInQuickAccess = false,
}) => {
  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent className="w-48">
        {isInQuickAccess ? (
          <ContextMenuItem onClick={onRemoveFromQuickAccess}>
            <Trash2 className="h-4 w-4 mr-2" />
            Remove from Quick Access
          </ContextMenuItem>
        ) : (
          <ContextMenuItem onClick={onAddToQuickAccess}>
            <Pin className="h-4 w-4 mr-2" />
            Add to Quick Access
          </ContextMenuItem>
        )}
        
        <ContextMenuItem onClick={onCopyAction}>
          <Copy className="h-4 w-4 mr-2" />
          Copy Action
        </ContextMenuItem>
        
        <ContextMenuSeparator />
        
        <ContextMenuSub>
          <ContextMenuSubTrigger>
            <Settings className="h-4 w-4 mr-2" />
            Customize
          </ContextMenuSubTrigger>
          <ContextMenuSubContent>
            <ContextMenuItem onClick={onCustomize}>
              <Edit className="h-4 w-4 mr-2" />
              Customize Ribbon
            </ContextMenuItem>
            <ContextMenuItem>
              <Star className="h-4 w-4 mr-2" />
              Set as Favorite
            </ContextMenuItem>
          </ContextMenuSubContent>
        </ContextMenuSub>
      </ContextMenuContent>
    </ContextMenu>
  );
};

// Command palette
export const CommandPalette: React.FC<{
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  actions: any[];
  onExecuteAction: (action: any) => void;
}> = ({ isOpen, onOpenChange, actions, onExecuteAction }) => {
  const { searchQuery, setSearchQuery, filters, setFilters, filteredActions } = useAdvancedSearch(actions);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] p-0">
        <DialogHeader className="px-4 pt-4 pb-2">
          <DialogTitle>Command Palette</DialogTitle>
          <DialogDescription>
            Search and execute ribbon actions quickly
          </DialogDescription>
        </DialogHeader>
        
        <Command className="border-0">
          <CommandInput
            placeholder="Search actions..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          
          {/* Filters */}
          <div className="flex items-center gap-2 px-3 py-2 border-b">
            <Badge variant="outline" className="text-xs">
              <Filter className="h-3 w-3 mr-1" />
              Filters
            </Badge>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="has-shortcut"
                checked={filters.hasShortcut}
                onCheckedChange={(checked) => setFilters(prev => ({ ...prev, hasShortcut: checked }))}
              />
              <Label htmlFor="has-shortcut" className="text-xs">Has Shortcut</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="recently-used"
                checked={filters.recentlyUsed}
                onCheckedChange={(checked) => setFilters(prev => ({ ...prev, recentlyUsed: checked }))}
              />
              <Label htmlFor="recently-used" className="text-xs">Recently Used</Label>
            </div>
          </div>
          
          <CommandList>
            <CommandEmpty>No actions found.</CommandEmpty>
            
            {Object.entries(
              filteredActions.reduce((acc, action) => {
                const category = action.category || 'General';
                if (!acc[category]) acc[category] = [];
                acc[category].push(action);
                return acc;
              }, {} as Record<string, any[]>)
            ).map(([category, categoryActions]) => (
              <CommandGroup key={category} heading={category}>
                {categoryActions.map((action) => (
                  <CommandItem
                    key={action.id}
                    onSelect={() => {
                      onExecuteAction(action);
                      onOpenChange(false);
                    }}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        {action.icon && <action.icon className="h-4 w-4 mr-2" />}
                        <div>
                          <div className="font-medium">{action.label}</div>
                          {action.description && (
                            <div className="text-xs text-muted-foreground">{action.description}</div>
                          )}
                        </div>
                      </div>
                      {action.shortcut && (
                        <Badge variant="secondary" className="text-xs">
                          {action.shortcut}
                        </Badge>
                      )}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            ))}
          </CommandList>
        </Command>
      </DialogContent>
    </Dialog>
  );
};
