import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ToolbarToggleButton, HeaderToolbarToggle, InlineToolbarToggle } from '../ToolbarToggleButton';
import { useToolbarVisibilityStore } from '@/stores/toolbar-visibility-store';
import { useDashboardMode } from '@/stores/dashboard-mode-store';

// Mock the stores
jest.mock('@/stores/toolbar-visibility-store');
jest.mock('@/stores/dashboard-mode-store');

const mockUseToolbarVisibilityStore = useToolbarVisibilityStore as jest.MockedFunction<typeof useToolbarVisibilityStore>;
const mockUseDashboardMode = useDashboardMode as jest.MockedFunction<typeof useDashboardMode>;

describe('ToolbarToggleButton', () => {
  const mockToggleSimpleToolbar = jest.fn();
  const mockToggleAdvancedRibbon = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseToolbarVisibilityStore.mockReturnValue({
      isSimpleToolbarVisible: true,
      isAdvancedRibbonVisible: true,
      toggleSimpleToolbar: mockToggleSimpleToolbar,
      toggleAdvancedRibbon: mockToggleAdvancedRibbon,
      setSimpleToolbarVisible: jest.fn(),
      setAdvancedRibbonVisible: jest.fn(),
      getToolbarVisibility: jest.fn(),
      setToolbarVisibility: jest.fn(),
    });
  });

  describe('Simple Mode', () => {
    beforeEach(() => {
      mockUseDashboardMode.mockReturnValue({
        current_mode: 'simple',
        toggle_mode: jest.fn(),
        can_switch: true,
        set_mode: jest.fn(),
        user_preferences: {} as any,
        mode_history: [],
        is_switching: false,
        last_mode_change: null,
        mode_capabilities: {} as any,
        simple_mode_config: {} as any,
        advanced_mode_config: {} as any,
        context_data: {} as any,
        update_preferences: jest.fn(),
        update_simple_config: jest.fn(),
        update_advanced_config: jest.fn(),
        update_context: jest.fn(),
        get_current_config: jest.fn(),
        can_switch_mode: jest.fn(),
        get_mode_capabilities: jest.fn(),
        reset_to_defaults: jest.fn(),
        clear_mode_history: jest.fn(),
      });
    });

    it('should render with correct tooltip for simple mode', () => {
      render(<ToolbarToggleButton />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label', 'Hide Toolbar');
    });

    it('should call toggleSimpleToolbar when clicked in simple mode', () => {
      render(<ToolbarToggleButton />);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(mockToggleSimpleToolbar).toHaveBeenCalledTimes(1);
      expect(mockToggleAdvancedRibbon).not.toHaveBeenCalled();
    });
  });

  describe('Advanced Mode', () => {
    beforeEach(() => {
      mockUseDashboardMode.mockReturnValue({
        current_mode: 'advanced',
        toggle_mode: jest.fn(),
        can_switch: true,
        set_mode: jest.fn(),
        user_preferences: {} as any,
        mode_history: [],
        is_switching: false,
        last_mode_change: null,
        mode_capabilities: {} as any,
        simple_mode_config: {} as any,
        advanced_mode_config: {} as any,
        context_data: {} as any,
        update_preferences: jest.fn(),
        update_simple_config: jest.fn(),
        update_advanced_config: jest.fn(),
        update_context: jest.fn(),
        get_current_config: jest.fn(),
        can_switch_mode: jest.fn(),
        get_mode_capabilities: jest.fn(),
        reset_to_defaults: jest.fn(),
        clear_mode_history: jest.fn(),
      });
    });

    it('should render with correct tooltip for advanced mode', () => {
      render(<ToolbarToggleButton />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label', 'Hide Ribbon');
    });

    it('should call toggleAdvancedRibbon when clicked in advanced mode', () => {
      render(<ToolbarToggleButton />);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(mockToggleAdvancedRibbon).toHaveBeenCalledTimes(1);
      expect(mockToggleSimpleToolbar).not.toHaveBeenCalled();
    });
  });

  describe('Specialized Components', () => {
    beforeEach(() => {
      mockUseDashboardMode.mockReturnValue({
        current_mode: 'simple',
        toggle_mode: jest.fn(),
        can_switch: true,
        set_mode: jest.fn(),
        user_preferences: {} as any,
        mode_history: [],
        is_switching: false,
        last_mode_change: null,
        mode_capabilities: {} as any,
        simple_mode_config: {} as any,
        advanced_mode_config: {} as any,
        context_data: {} as any,
        update_preferences: jest.fn(),
        update_simple_config: jest.fn(),
        update_advanced_config: jest.fn(),
        update_context: jest.fn(),
        get_current_config: jest.fn(),
        can_switch_mode: jest.fn(),
        get_mode_capabilities: jest.fn(),
        reset_to_defaults: jest.fn(),
        clear_mode_history: jest.fn(),
      });
    });

    it('should render HeaderToolbarToggle', () => {
      render(<HeaderToolbarToggle />);
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should render InlineToolbarToggle', () => {
      render(<InlineToolbarToggle />);
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });
});
