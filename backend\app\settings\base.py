"""
Base configuration classes for the Datagenius application.

This module provides the foundation for hierarchical configuration management
with Pydantic validation and environment-specific settings.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from pydantic import BaseModel, Field, field_validator
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class BaseConfig(BaseModel):
    """Base configuration class with common validation and utilities."""
    
    model_config = {"extra": "forbid", "validate_assignment": True}
    
    @classmethod
    def from_env(cls, prefix: str = "") -> "BaseConfig":
        """
        Create configuration from environment variables.
        
        Args:
            prefix: Environment variable prefix
            
        Returns:
            Configuration instance
        """
        env_vars = {}
        for key, value in os.environ.items():
            if prefix and key.startswith(prefix):
                # Remove prefix and convert to lowercase
                config_key = key[len(prefix):].lower()
                env_vars[config_key] = value
            elif not prefix:
                env_vars[key.lower()] = value
        
        return cls(**env_vars)
    
    @classmethod
    def from_yaml(cls, file_path: Union[str, Path]) -> "BaseConfig":
        """
        Create configuration from YAML file.
        
        Args:
            file_path: Path to YAML configuration file
            
        Returns:
            Configuration instance
        """
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        with open(path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        return cls(**data)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return self.model_dump()
    
    def merge_with(self, other: "BaseConfig") -> "BaseConfig":
        """
        Merge this configuration with another configuration.
        
        Args:
            other: Other configuration to merge with
            
        Returns:
            New merged configuration instance
        """
        merged_data = self.to_dict()
        other_data = other.to_dict()
        
        # Deep merge dictionaries
        def deep_merge(dict1: Dict, dict2: Dict) -> Dict:
            result = dict1.copy()
            for key, value in dict2.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result
        
        merged_data = deep_merge(merged_data, other_data)
        return self.__class__(**merged_data)


class EnvironmentConfig(BaseConfig):
    """Environment-specific configuration settings."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    environment: str = Field(default="development", description="Application environment")
    debug: bool = Field(default=False, description="Enable debug mode")
    testing: bool = Field(default=False, description="Enable testing mode")
    
    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment value."""
        allowed_environments = {'development', 'staging', 'production', 'testing'}
        if v not in allowed_environments:
            raise ValueError(f"Environment must be one of: {allowed_environments}")
        return v
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == "production"
    
    @property
    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self.environment == "testing" or self.testing


class LoggingConfig(BaseConfig):
    """Logging configuration settings."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    level: str = Field(default="INFO", description="Logging level")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string"
    )
    file_path: Optional[str] = Field(default=None, description="Log file path")
    max_file_size: int = Field(default=10485760, description="Maximum log file size in bytes")  # 10MB
    backup_count: int = Field(default=5, description="Number of backup log files to keep")
    
    @field_validator('level')
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate logging level."""
        allowed_levels = {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}
        v_upper = v.upper()
        if v_upper not in allowed_levels:
            raise ValueError(f"Log level must be one of: {allowed_levels}")
        return v_upper


class MonitoringConfig(BaseConfig):
    """Monitoring and metrics configuration."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    enabled: bool = Field(default=True, description="Enable monitoring")
    metrics_endpoint: str = Field(default="/metrics", description="Metrics endpoint path")
    health_endpoint: str = Field(default="/health", description="Health check endpoint path")
    performance_tracking: bool = Field(default=True, description="Enable performance tracking")
    error_tracking: bool = Field(default=True, description="Enable error tracking")
    
    # Alerting configuration
    alert_on_errors: bool = Field(default=True, description="Send alerts on errors")
    alert_threshold: int = Field(default=10, description="Error count threshold for alerts")
    alert_window_minutes: int = Field(default=5, description="Time window for error counting")
