/* Import markdown styling for AI agent responses */
@import './styles/markdown.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;

    /* Enhanced Dashboard Design Tokens */
    --dashboard-background: 248 250 252;
    --dashboard-surface: 255 255 255;
    --dashboard-surface-hover: 248 250 252;
    --dashboard-border: 226 232 240;
    --dashboard-border-strong: 203 213 225;

    /* Enhanced Color Palette */
    --dashboard-primary: 59 130 246;
    --dashboard-primary-hover: 37 99 235;
    --dashboard-secondary: 16 185 129;
    --dashboard-secondary-hover: 5 150 105;
    --dashboard-accent: 139 92 246;
    --dashboard-accent-hover: 124 58 237;
    --dashboard-warning: 245 158 11;
    --dashboard-warning-hover: 217 119 6;
    --dashboard-success: 34 197 94;
    --dashboard-success-hover: 22 163 74;
    --dashboard-error: 239 68 68;
    --dashboard-error-hover: 220 38 38;

    /* Typography Scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;

    /* Enhanced Shadow Scale */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-colored: 0 10px 15px -3px rgb(59 130 246 / 0.1), 0 4px 6px -4px rgb(59 130 246 / 0.1);

    /* Gradient Definitions */
    --gradient-primary: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(37 99 235) 100%);
    --gradient-secondary: linear-gradient(135deg, rgb(16 185 129) 0%, rgb(5 150 105) 100%);
    --gradient-accent: linear-gradient(135deg, rgb(139 92 246) 0%, rgb(124 58 237) 100%);
    --gradient-surface: linear-gradient(135deg, rgb(255 255 255) 0%, rgb(248 250 252) 100%);

    /* Animation Durations */
    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --duration-slow: 350ms;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Enhanced Dashboard Design Tokens - Dark Mode */
    --dashboard-background: 15 23 42;
    --dashboard-surface: 30 41 59;
    --dashboard-surface-hover: 51 65 85;
    --dashboard-border: 71 85 105;
    --dashboard-border-strong: 100 116 139;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Enhanced Dashboard Layout Components */
  .dashboard-container {
    @apply h-screen flex flex-col overflow-hidden;
    background: linear-gradient(135deg, rgb(248 250 252) 0%, rgb(241 245 249) 100%);
  }

  .dashboard-header {
    @apply bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-b border-slate-200/50 dark:border-slate-700/50 sticky top-0 z-20;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    min-height: 48px; /* Reduced from 56px */
  }

  /* Fixed Toolbar Styles - Always visible at top */
  .sticky-toolbar {
    @apply fixed bg-background/95 backdrop-blur-sm border-b border-slate-200/50 dark:border-slate-700/50;
    top: 3rem; /* Position directly below the unified header (48px = 3rem) */
    left: 0; /* Initial position - will be overridden by motion.div */
    z-index: 10; /* Below header (z-20) but above content */
    box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.06);
    /* Positioning and transitions handled by framer-motion */
  }

  .sticky-toolbar.simple-mode {
    @apply border-blue-200/50 dark:border-blue-700/50;
  }

  .sticky-toolbar.advanced-mode {
    @apply border-purple-200/50 dark:border-purple-700/50;
  }

  /* Responsive adjustments for fixed toolbars */
  @media (max-width: 768px) {
    .sticky-toolbar {
      top: 3rem; /* Keep consistent with desktop - header height is same on mobile */
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      /* Left and width positioning handled by JavaScript for all screen sizes */
    }

    .dashboard-scrollable-content.with-toolbar {
      /* Height remains consistent */
      height: calc(100vh - 3rem);
      /* Use dynamic toolbar height on mobile too with minimum padding */
      padding-top: max(var(--toolbar-height, 4rem), 3rem);
      /* Fast transitions on mobile */
      transition: padding-top 0.15s ease-in-out;
    }
  }

  /* Smooth scroll behavior for better UX with sticky elements */
  html {
    scroll-behavior: smooth;
  }

  /* Legacy class - no longer needed with fixed toolbar approach */
  .dashboard-content-with-sticky-toolbar {
    scroll-margin-top: 6rem; /* Account for header (3rem) + toolbar height (~3rem) */
  }

  /* Layout adjustments for navbar and header positioning - now handled by responsive components */
  .main-content-with-navbar {
    /* Responsive positioning now handled by individual components */
    margin-left: 0;
    margin-top: 0;
    transition: all 0.3s ease-in-out;
    min-height: calc(100vh - 48px); /* Account for corrected header height (48px) */
  }

  .main-content-with-navbar.navbar-expanded {
    /* Responsive positioning now handled by individual components */
    margin-left: 0;
  }

  .dashboard-content {
    @apply flex-1;
    /* Padding now handled by centered wrapper containers */
    /* Removed space-y-4 to eliminate margins between sections */
  }

  /* Scrollable Dashboard Content Area */
  .dashboard-scrollable-content {
    /* Fixed height container that accounts for header */
    height: calc(100vh - 3rem); /* Full height minus header (3rem) */
    overflow-y: auto;
    overflow-x: hidden;
    /* Add bottom padding to ensure content doesn't get cut off */
    padding-bottom: 2rem;
  }

  /* When toolbar is visible, adjust the scrollable area dynamically */
  .dashboard-scrollable-content.with-toolbar {
    /* Height remains the same - content adjustment handled by padding */
    height: calc(100vh - 3rem);
    /* Dynamic top padding based on actual toolbar height */
    padding-top: max(var(--toolbar-height, 4rem), 3rem);
    /* Fast transition for padding changes */
    transition: padding-top 0.15s ease-in-out;
    /* Ensure content is never hidden behind toolbar */
    scroll-padding-top: var(--toolbar-height, 4rem);
  }

  /* Smooth scrolling for dashboard content */
  .dashboard-scrollable-content {
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }

  .dashboard-scrollable-content::-webkit-scrollbar {
    width: 6px;
  }

  .dashboard-scrollable-content::-webkit-scrollbar-track {
    background: transparent;
  }

  .dashboard-scrollable-content::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }

  .dashboard-scrollable-content::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }

  .dashboard-section {
    @apply bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-xl border border-slate-200/50 dark:border-slate-700/50;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  /* Dashboard centering and width optimization */
  .dashboard-centered-layout {
    @apply flex justify-center min-h-full;
  }

  .dashboard-content-wrapper {
    @apply w-full px-4;
    /* Width now handled dynamically by DashboardLayout component */
    max-width: 100%;
  }

  /* Responsive width adjustments for different screen sizes */
  @media (min-width: 640px) {
    .dashboard-content-wrapper {
      @apply px-6;
      /* Width constraints removed - handled by parent component */
    }
  }

  @media (min-width: 1024px) {
    .dashboard-content-wrapper {
      @apply px-8;
      /* Width constraints removed - handled by parent component */
    }
  }

  @media (min-width: 1536px) {
    .dashboard-content-wrapper {
      /* Width constraints removed - handled by parent component */
    }
  }

  .dashboard-widget {
    @apply bg-white dark:bg-slate-800 rounded-lg border border-slate-200/50 dark:border-slate-700/50 transition-all duration-200;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .dashboard-widget:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  .dashboard-widget-header {
    @apply p-3 border-b border-slate-100 dark:border-slate-700 flex items-center justify-between;
  }

  .dashboard-widget-content {
    @apply p-3;
  }

  /* Dashboard Grid Optimizations */
  .dashboard-grid {
    @apply grid gap-3;
  }

  .dashboard-grid-responsive {
    @apply grid gap-3 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .dashboard-widget-container {
    @apply bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-lg border border-slate-200/50 dark:border-slate-700/50 p-3;
    box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  /* Ribbon Toolbar Optimizations - Standardized */
  :root {
    --ribbon-primary: #3B82F6;
    --ribbon-primary-hover: #2563EB;
    --ribbon-secondary: #6366F1;
    --ribbon-secondary-hover: #4F46E5;
    --ribbon-accent: #10B981;
    --ribbon-accent-hover: #059669;
    --ribbon-border: rgb(226 232 240 / 0.5);
    --ribbon-text: #374151;
    --ribbon-text-muted: #6B7280;
  }

  .ribbon-toolbar-compact {
    @apply border-b bg-background;
  }

  .ribbon-tab-header {
    @apply px-3 py-1.5 border-b;
    height: 40px; /* Fixed height for tab headers */
  }

  .ribbon-tab-content {
    @apply px-3 py-2;
    height: 80px; /* Fixed height for tab content */
  }

  .ribbon-group {
    @apply flex flex-col items-center px-3 py-2 border-r border-[var(--ribbon-border)] last:border-r-0;
    height: 76px; /* Fixed height for ribbon groups (80px - 4px padding) */
  }

  /* Standardized toolbar container heights */
  .ribbon-toolbar-container {
    height: 120px; /* Total height: 40px header + 80px content */
  }

  /* Standardized Ribbon Button Heights */
  .ribbon-button {
    @apply flex flex-col items-center justify-center space-y-0.5 h-auto min-h-[48px] py-2 px-3 min-w-[60px];
    transition: all 0.2s ease-in-out;
  }

  .ribbon-button:hover {
    @apply transform scale-[1.02];
  }

  /* Ensure proper spacing for ribbon button content */
  .ribbon-button > * {
    @apply flex-shrink-0;
  }

  /* Ensure text is fully visible in ribbon buttons */
  .ribbon-button span {
    @apply whitespace-nowrap overflow-visible;
  }

  /* Compact mode toggle switch styling */
  .mode-toggle-compact {
    @apply transition-all duration-200 ease-in-out;
  }

  .mode-toggle-compact:hover {
    @apply transform scale-105;
  }

  /* Enhanced switch thumb visibility */
  .mode-toggle-compact [data-radix-switch-thumb] {
    @apply border-2 border-slate-300 shadow-lg bg-white;
  }

  .mode-toggle-compact[data-state="checked"] [data-radix-switch-thumb] {
    @apply border-blue-200 bg-white shadow-blue-100;
  }

  .mode-toggle-compact[data-state="unchecked"] [data-radix-switch-thumb] {
    @apply border-slate-400 bg-white shadow-slate-200;
  }

  /* Enhanced switch thumb with smooth transitions */
  .enhanced-switch-thumb {
    @apply border-2 border-slate-300 shadow-lg bg-white transition-all duration-200 ease-in-out;
  }

  .enhanced-switch-thumb.checked {
    @apply border-blue-200 bg-white shadow-blue-100;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1), 0 4px 6px -1px rgba(59, 130, 246, 0.1);
  }

  .enhanced-switch-thumb.unchecked {
    @apply border-slate-400 bg-white shadow-slate-200;
  }

  /* Hover effects for better interactivity */
  .mode-toggle-compact:hover .enhanced-switch-thumb {
    @apply shadow-lg transform scale-105;
  }

  .ribbon-button-primary {
    @apply bg-[var(--ribbon-primary)] hover:bg-[var(--ribbon-primary-hover)] text-white min-h-[48px] px-3 min-w-[60px];
  }

  .ribbon-button-secondary {
    @apply bg-[var(--ribbon-secondary)] hover:bg-[var(--ribbon-secondary-hover)] text-white min-h-[48px] px-3 min-w-[60px];
  }

  .ribbon-button-accent {
    @apply bg-[var(--ribbon-accent)] hover:bg-[var(--ribbon-accent-hover)] text-white min-h-[48px] px-3 min-w-[60px];
  }

  .ribbon-button-icon {
    @apply h-7 w-7 p-0;
  }

  .ribbon-button-micro {
    @apply h-6 w-6 p-0;
  }

  .ribbon-separator {
    @apply h-12;
  }

  /* Ribbon Typography */
  .ribbon-title {
    @apply text-sm font-medium text-[var(--ribbon-text)];
  }

  .ribbon-label {
    @apply text-xs text-[var(--ribbon-text-muted)];
  }

  .ribbon-button-text {
    @apply text-sm font-medium;
  }

  /* Ribbon Spacing */
  .ribbon-spacing-xs { @apply p-1.5; }
  .ribbon-spacing-sm { @apply p-2; }
  .ribbon-spacing-md { @apply p-3; }
  .ribbon-spacing-lg { @apply p-4; }

  /* Responsive Dashboard Optimizations - Standardized */
  @media (max-width: 768px) {
    .dashboard-content {
      @apply ribbon-spacing-md;
      /* Removed space-y-3 to eliminate margins between sections on mobile */
    }

    .dashboard-grid {
      @apply gap-2;
    }

    .dashboard-widget-container {
      @apply ribbon-spacing-sm;
    }

    .ribbon-group {
      @apply px-2 py-1.5;
    }
  }

  @media (max-width: 640px) {
    .dashboard-content {
      @apply ribbon-spacing-sm space-y-2;
    }

    .ribbon-tab-content {
      @apply ribbon-spacing-sm;
    }

    .ribbon-group {
      @apply ribbon-spacing-xs;
    }
  }

  /* Enhanced Button Styles */
  .btn-dashboard-primary {
    @apply text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105;
    background: var(--gradient-primary);
    box-shadow: 0 4px 6px -1px rgb(59 130 246 / 0.25), 0 2px 4px -2px rgb(59 130 246 / 0.25);
  }

  .btn-dashboard-primary:hover {
    box-shadow: 0 10px 15px -3px rgb(59 130 246 / 0.25), 0 4px 6px -4px rgb(59 130 246 / 0.25);
  }

  .btn-dashboard-secondary {
    @apply bg-white/80 hover:bg-white dark:bg-slate-700/80 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 font-medium px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .btn-dashboard-secondary:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  /* Enhanced Typography System */
  .dashboard-title {
    @apply text-2xl font-bold text-slate-900 dark:text-slate-100 tracking-tight leading-tight;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .dashboard-subtitle {
    @apply text-lg font-semibold text-slate-700 dark:text-slate-300 leading-snug;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .dashboard-text {
    @apply text-slate-600 dark:text-slate-400 leading-relaxed;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  /* Floating AI Assistant Styles */
  .floating-ai-trigger {
    @apply fixed z-50 transition-all duration-300 ease-in-out;
    filter: drop-shadow(0 10px 25px rgb(0 0 0 / 0.15));
  }

  .floating-ai-trigger:hover {
    filter: drop-shadow(0 20px 35px rgb(0 0 0 / 0.25));
  }

  .floating-ai-modal {
    @apply bg-white/95 backdrop-blur-sm border border-slate-200/50;
    box-shadow:
      0 25px 50px -12px rgb(0 0 0 / 0.25),
      0 0 0 1px rgb(255 255 255 / 0.05);
  }

  .floating-ai-modal.dark {
    @apply bg-slate-800/95 border-slate-700/50;
  }

  .floating-ai-header {
    @apply bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700;
    border-bottom: 1px solid rgb(226 232 240 / 0.5);
  }

  .floating-ai-message-user {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white;
    box-shadow: 0 4px 6px -1px rgb(59 130 246 / 0.25);
  }

  .floating-ai-message-assistant {
    @apply bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-700 dark:to-slate-600 text-slate-900 dark:text-slate-100;
    border: 1px solid rgb(226 232 240 / 0.5);
  }

  .floating-ai-input {
    @apply border-slate-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20;
    transition: all 0.2s ease-in-out;
  }

  /* Typography Scale */
  .text-display-large {
    @apply text-4xl font-bold tracking-tight leading-none;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-display-medium {
    @apply text-3xl font-bold tracking-tight leading-tight;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-display-small {
    @apply text-2xl font-bold tracking-tight leading-tight;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-headline-large {
    @apply text-xl font-semibold leading-snug;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-headline-medium {
    @apply text-lg font-semibold leading-snug;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-headline-small {
    @apply text-base font-semibold leading-normal;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-body-large {
    @apply text-base leading-relaxed;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-body-medium {
    @apply text-sm leading-relaxed;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-body-small {
    @apply text-xs leading-normal;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-label-large {
    @apply text-sm font-medium leading-normal;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-label-medium {
    @apply text-xs font-medium leading-normal;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .text-label-small {
    @apply text-xs font-medium leading-tight;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  /* Loading States */
  .skeleton {
    @apply animate-pulse bg-slate-200 dark:bg-slate-700 rounded;
  }

  .skeleton-text {
    @apply skeleton h-4 w-full;
  }

  .skeleton-title {
    @apply skeleton h-6 w-3/4;
  }

  /* Focus States */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800;
  }

  /* Hover Effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-0.5;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-lg hover:shadow-blue-500/10;
  }
}