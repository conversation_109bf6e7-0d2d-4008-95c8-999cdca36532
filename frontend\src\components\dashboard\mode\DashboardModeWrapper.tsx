/**
 * Dashboard Mode Wrapper Component
 * 
 * Main wrapper component that orchestrates the dual-mode dashboard interface.
 * Handles mode switching, component rendering, and state management.
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// Mode components
import { ModeIndicator, ModeAware } from './ModeIndicator';
import { ModeTransitionDialog, useModeTransition } from './ModeTransitionDialog';

// Simple Mode components
import { TemplateGallery } from './simple/TemplateGallery';
import { GuidedWorkflow } from './simple/GuidedWorkflow';
import { NaturalLanguageWidgetCreator } from './simple/NaturalLanguageWidgetCreator';

// Advanced Mode components
import { EnhancedRibbonToolbar } from './advanced/EnhancedRibbonToolbar';
import { TechnicalControlsPanel } from './advanced/TechnicalControlsPanel';

// Store hooks
import { useDashboardMode, useModeConfig, useModeContext } from '@/stores/dashboard-mode-store';
import { useNavbarStore } from '@/stores/navbar-store';
import { useToolbarVisibilityStore } from '@/stores/toolbar-visibility-store';

// Toolbar toggle component (for inline use)
import { InlineToolbarToggle } from '../ToolbarToggleButton';

// Icons
import {
  Sparkles,
  Settings,
  Layout,
  Zap,
  ChevronDown,
  ChevronUp,
  Eye,
  EyeOff,
  FolderOpen,
  Save,
  Plus,
  Clock,
  Database,
  RefreshCw,
  BarChart3,
  Upload,
  Share2,
  Download,
  Palette,
  Grid3X3,
  Square,
} from 'lucide-react';

// UI Components for ribbon
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Simple Ribbon Toolbar Component
interface SimpleRibbonToolbarProps {
  onOpen?: () => void;
  onSave?: () => void;
  onNew?: () => void;
  onRecent?: () => void;
  onDataConnect?: () => void;
  onDataRefresh?: () => void;
  onAddChart?: () => void;
  onAddSection?: () => void;
  onUploadFile?: () => void;
  onShare?: () => void;
  onExport?: () => void;
  onThemeToggle?: () => void;
  onAutoLayout?: () => void;
  onBrowseTemplates?: () => void;
  onGuidedSetup?: () => void;
  onCreateWithAI?: () => void;
}

const SimpleRibbonToolbar: React.FC<SimpleRibbonToolbarProps> = ({
  onOpen,
  onSave,
  onNew,
  onRecent,
  onDataConnect,
  onDataRefresh,
  onAddChart,
  onAddSection,
  onUploadFile,
  onShare,
  onExport,
  onThemeToggle,
  onAutoLayout,
  onBrowseTemplates,
  onGuidedSetup,
  onCreateWithAI,
}) => {
  const [activeTab, setActiveTab] = React.useState('dashboard');

  // Simple Ribbon Button Component
  const SimpleRibbonButton = ({
    icon: Icon,
    label,
    onClick,
    variant = 'ghost',
    className = '',
  }: {
    icon: React.ComponentType<{ className?: string }>;
    label: string;
    onClick?: () => void;
    variant?: 'ghost' | 'outline' | 'default';
    className?: string;
  }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size="sm"
            onClick={onClick}
            className={cn(
              "ribbon-button flex flex-col items-center justify-center space-y-1 h-14 min-w-[60px] px-2",
              className
            )}
          >
            <Icon className="h-4 w-4" />
            <span className="text-xs font-normal leading-tight text-center whitespace-nowrap">{label}</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p className="font-medium">{label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  // Simple Ribbon Group Component
  const SimpleRibbonGroup = ({
    title,
    children,
    className = '',
  }: {
    title: string;
    children: React.ReactNode;
    className?: string;
  }) => (
    <div className={cn(
      "flex flex-col items-center px-3 py-2 border-r border-slate-200 last:border-r-0 h-full justify-center",
      className
    )}>
      <div className="flex gap-1 justify-center mb-1 flex-shrink-0">
        {children}
      </div>
      <div className="text-xs text-muted-foreground font-medium text-center flex-shrink-0">
        {title}
      </div>
    </div>
  );

  return (
    <div className="border-b bg-background ribbon-toolbar-container h-full max-h-[160px] overflow-hidden">
      <TooltipProvider>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          {/* Tab Headers - Fixed height */}
          <div className="ribbon-tab-header flex items-center justify-between flex-shrink-0">
            <TabsList className="grid w-auto grid-cols-3 h-8">
              <TabsTrigger value="dashboard" className="text-xs px-3 py-1">Dashboard</TabsTrigger>
              <TabsTrigger value="create" className="text-xs px-3 py-1">Create</TabsTrigger>
              <TabsTrigger value="share" className="text-xs px-3 py-1">Share</TabsTrigger>
            </TabsList>

            <div className="flex items-center space-x-2">
              {/* Simple Status Information - Meaningful status for non-technical users */}
              <div className="hidden md:flex items-center space-x-3 px-2 py-0.5 bg-slate-50/50 rounded border">
                {/* Connection Status - Simple indicator */}
                <div className="flex items-center space-x-1">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-slate-600 font-medium">Ready</span>
                </div>

                {/* Last Activity - User-friendly time */}
                <div className="flex items-center space-x-1 text-xs text-slate-600">
                  <Clock className="h-3 w-3" />
                  <span>Just now</span>
                </div>

                {/* Simple Content Indicator */}
                <div className="flex items-center space-x-1 text-xs text-slate-600">
                  <BarChart3 className="h-3 w-3" />
                  <span>0 charts</span>
                </div>
              </div>

              {/* Mode Badge - Matching Advanced Mode style */}
              <div className="flex items-center space-x-1">
                <Badge variant="secondary" className="text-xs px-1.5 py-0">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Simple Mode
                </Badge>
              </div>
              <InlineToolbarToggle
                variant="chevron"
                className="h-8 w-8"
              />
            </div>
          </div>

          {/* Tab Content - Fixed height */}
          <div className="ribbon-tab-content flex-1 min-h-0 overflow-hidden">
            {/* Dashboard Tab */}
            <TabsContent value="dashboard" className="mt-0 h-full p-2">
              <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
                <SimpleRibbonGroup title="File">
                  <SimpleRibbonButton icon={FolderOpen} label="Open" onClick={onOpen} />
                  <SimpleRibbonButton icon={Save} label="Save" onClick={onSave} />
                  <SimpleRibbonButton icon={Plus} label="New" onClick={onNew} />
                  <SimpleRibbonButton icon={Clock} label="Recent" onClick={onRecent} />
                </SimpleRibbonGroup>

                <SimpleRibbonGroup title="Data">
                  <SimpleRibbonButton icon={Database} label="Connect" onClick={onDataConnect} />
                  <SimpleRibbonButton icon={RefreshCw} label="Refresh" onClick={onDataRefresh} />
                  <SimpleRibbonButton icon={Upload} label="Upload" onClick={onUploadFile} />
                </SimpleRibbonGroup>

                <SimpleRibbonGroup title="View">
                  <SimpleRibbonButton icon={Palette} label="Theme" onClick={onThemeToggle} />
                  <SimpleRibbonButton icon={Grid3X3} label="Layout" onClick={onAutoLayout} />
                </SimpleRibbonGroup>
              </div>
            </TabsContent>

            {/* Create Tab */}
            <TabsContent value="create" className="mt-0 h-full p-2">
              <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
                <SimpleRibbonGroup title="Quick Add">
                  <SimpleRibbonButton icon={BarChart3} label="Chart" onClick={onAddChart} />
                  <SimpleRibbonButton icon={Square} label="Section" onClick={onAddSection} />
                </SimpleRibbonGroup>

                <SimpleRibbonGroup title="Templates">
                  <SimpleRibbonButton icon={Layout} label="Browse" onClick={onBrowseTemplates} />
                  <SimpleRibbonButton icon={Sparkles} label="Guided" onClick={onGuidedSetup} />
                  <SimpleRibbonButton icon={Zap} label="AI Create" onClick={onCreateWithAI} />
                </SimpleRibbonGroup>
              </div>
            </TabsContent>

            {/* Share Tab */}
            <TabsContent value="share" className="mt-0 h-full p-2">
              <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
                <SimpleRibbonGroup title="Export">
                  <SimpleRibbonButton icon={Share2} label="Share" onClick={onShare} />
                  <SimpleRibbonButton icon={Download} label="Export" onClick={onExport} />
                </SimpleRibbonGroup>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </TooltipProvider>
    </div>
  );
};

interface DashboardModeWrapperProps {
  className?: string;
  children?: React.ReactNode;
  show_mode_indicator?: boolean;
  show_agent_integration?: boolean;
  on_widget_create?: (widget_config: any) => void;
  on_section_create?: (section_config: any) => void;
  on_template_apply?: (template_id: string) => void;
  on_data_configure?: (data_config: any) => void;
  on_ribbon_action?: (action: string, data?: any) => void;
}

export const DashboardModeWrapper: React.FC<DashboardModeWrapperProps> = ({
  className,
  children,
  show_mode_indicator = false, // Disabled by default as requested
  show_agent_integration = true,
  on_widget_create,
  on_section_create,
  on_template_apply,
  on_data_configure,
  on_ribbon_action,
}) => {
  const [show_advanced_tools, set_show_advanced_tools] = useState(false);

  const { current_mode, toggle_mode, can_switch } = useDashboardMode();
  const { current_config } = useModeConfig();
  const { context, update_context } = useModeContext();
  const {
    is_transitioning,
    transition_dialog,
    initiate_transition,
    cancel_transition
  } = useModeTransition();

  // Get navbar state for toolbar positioning with proper reactivity
  const navbarStore = useNavbarStore();
  const { shouldShowExpanded, getNavbarWidth, isExpanded, isHovered, isLocked } = navbarStore;

  // Use state to track navbar width changes for proper re-rendering
  const [currentNavbarWidth, setCurrentNavbarWidth] = React.useState(() => getNavbarWidth());
  const [isNavbarExpanded, setIsNavbarExpanded] = React.useState(() => shouldShowExpanded());

  // Update navbar state when store changes with immediate effect
  React.useEffect(() => {
    const updateNavbarState = () => {
      const newWidth = getNavbarWidth();
      const newExpanded = shouldShowExpanded();

      setCurrentNavbarWidth(newWidth);
      setIsNavbarExpanded(newExpanded);

      console.log('Navbar state updated:', { newWidth, newExpanded, isExpanded, isHovered, isLocked });
    };

    updateNavbarState();
  }, [isExpanded, isHovered, isLocked, getNavbarWidth, shouldShowExpanded]);

  // Use toolbar visibility store - MUST be declared before any usage
  const {
    isSimpleToolbarVisible,
    isAdvancedRibbonVisible,
    toggleSimpleToolbar,
    toggleAdvancedRibbon
  } = useToolbarVisibilityStore();

  // Check if we're on mobile to adjust toolbar positioning
  const [isMobile, setIsMobile] = React.useState(false);

  // Refs for toolbar elements (kept for potential future use)
  const simpleToolbarRef = React.useRef<HTMLDivElement>(null);
  const advancedToolbarRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);



  // Calculate current toolbar height - increased to accommodate text labels under icons and section labels
  const currentToolbarHeight = React.useMemo(() => {
    const STANDARD_TOOLBAR_HEIGHT = 160; // Further increased height for both button labels and section labels

    if ((current_mode === 'simple' && isSimpleToolbarVisible) ||
        (current_mode === 'advanced' && isAdvancedRibbonVisible)) {
      return STANDARD_TOOLBAR_HEIGHT;
    }

    return 0; // No toolbar visible
  }, [current_mode, isSimpleToolbarVisible, isAdvancedRibbonVisible]);

  // Debug log to verify navbar state changes
  React.useEffect(() => {
    console.log('Toolbar positioning update:', {
      currentNavbarWidth,
      isMobile,
      isNavbarExpanded,
      currentToolbarHeight,
      timestamp: new Date().toISOString()
    });
  }, [currentNavbarWidth, isMobile, isNavbarExpanded, currentToolbarHeight]);

  // Update context when mode changes
  useEffect(() => {
    update_context({
      ai_assistant_active: show_agent_integration,
    });
  }, [current_mode, show_agent_integration, update_context]);

  const handle_mode_toggle = () => {
    if (!can_switch) return;

    const new_mode = current_mode === 'simple' ? 'advanced' : 'simple';
    
    initiate_transition(
      current_mode,
      new_mode,
      (preserve_data: boolean) => {
        toggle_mode();
        
        // Update tool visibility based on new mode
        if (new_mode === 'simple') {
          set_show_simple_tools(true);
          set_show_advanced_tools(false);
        } else {
          set_show_simple_tools(false);
          set_show_advanced_tools(true);
        }
      }
    );
  };

  const handle_widget_create = (widget_config: any) => {
    // Add mode context to widget config
    const enhanced_config = {
      ...widget_config,
      created_in_mode: current_mode,
      mode_specific_config: current_config,
    };
    on_widget_create?.(enhanced_config);
  };

  const handle_agent_action = (action_type: string, data?: any) => {
    switch (action_type) {
      case 'create_widget':
        handle_widget_create(data);
        break;
      case 'analyze_data':
        // Handle data analysis request
        break;
      case 'optimize_dashboard':
        // Handle dashboard optimization
        break;
      case 'show_code':
        // Handle code generation/display
        break;
    }
  };

  return (
    <motion.div
      className={cn("min-h-screen bg-background", className)}
      initial={false}
      animate={{
        marginLeft: currentNavbarWidth,
        width: `calc(100% - ${currentNavbarWidth}px)`
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Content wrapper without additional padding - handled by DashboardLayout */}
      <div className="flex justify-center min-h-full">
        <div className="w-full">
          <div className="min-h-screen bg-background"
            style={{
              width: '100%',
              maxWidth: '100%',
              // Set CSS custom property for dynamic toolbar height
              '--toolbar-height': `${currentToolbarHeight}px`
            } as React.CSSProperties}
          >
      {/* Mode Indicator - Removed as requested */}
      {show_mode_indicator && (
        <div className="sticky top-0 z-40 bg-background border-b">
          <div className="container mx-auto px-4 py-2">
            <div className="flex items-center justify-between">
              <ModeIndicator
                variant="compact"
                show_hints={true}
                show_capabilities={false}
              />
              {/* Mode toggle button removed as requested */}
            </div>
          </div>
        </div>
      )}

      {/* Mode-Specific Toolbars - Sticky positioned */}
      <ModeAware
        simple_content={
          // Simple Mode: Clean, minimal interface with sticky toolbar
          <div className="space-y-4">
            <motion.div
              ref={simpleToolbarRef}
              initial={false}
              animate={{
                height: isSimpleToolbarVisible ? 'auto' : 0,
                opacity: isSimpleToolbarVisible ? 1 : 0,
                marginLeft: isMobile ? 0 : currentNavbarWidth,
                width: isMobile ? '100%' : `calc(100% - ${currentNavbarWidth}px)`
              }}
              transition={{
                duration: 0.3,
                ease: 'easeInOut',
              }}
              style={{
                overflow: 'hidden',
                // Remove inline positioning since motion.div handles it
                borderTop: `2px solid ${isNavbarExpanded ? '#3B82F6' : '#10B981'}`
              }}
              className="sticky-toolbar simple-mode"
            >
              {isSimpleToolbarVisible && (
                <SimpleRibbonToolbar
                  onOpen={() => on_ribbon_action?.('dashboard_open')}
                  onSave={() => on_ribbon_action?.('dashboard_save')}
                  onNew={() => on_ribbon_action?.('dashboard_new')}
                  onRecent={() => on_ribbon_action?.('dashboard_recent')}
                  onDataConnect={() => on_ribbon_action?.('data_connect')}
                  onDataRefresh={() => on_ribbon_action?.('data_refresh')}
                  onAddChart={() => on_widget_create?.({ type: 'chart' })}
                  onAddSection={() => on_section_create?.({})}
                  onUploadFile={() => on_ribbon_action?.('file_upload')}
                  onShare={() => on_ribbon_action?.('share_dashboard')}
                  onExport={() => on_ribbon_action?.('dashboard_export', { format: 'pdf' })}
                  onThemeToggle={() => on_ribbon_action?.('style_theme', { theme: 'toggle' })}
                  onAutoLayout={() => on_ribbon_action?.('layout_auto')}
                  onBrowseTemplates={() => {/* Show template gallery */}}
                  onGuidedSetup={() => {/* Show guided workflow */}}
                  onCreateWithAI={() => {/* Show natural language creator */}}
                />
              )}
            </motion.div>
          </div>
        }
        advanced_content={
          // Advanced Mode: Single consolidated ribbon system with sticky positioning
          <div className="space-y-0">
            <motion.div
              ref={advancedToolbarRef}
              initial={false}
              animate={{
                height: isAdvancedRibbonVisible ? 'auto' : 0,
                opacity: isAdvancedRibbonVisible ? 1 : 0,
                marginLeft: isMobile ? 0 : currentNavbarWidth,
                width: isMobile ? '100%' : `calc(100% - ${currentNavbarWidth}px)`
              }}
              transition={{
                duration: 0.3,
                ease: 'easeInOut',
              }}
              style={{
                overflow: 'hidden',
                // Remove inline positioning since motion.div handles it
                borderTop: `2px solid ${isNavbarExpanded ? '#3B82F6' : '#10B981'}`
              }}
              className="sticky-toolbar advanced-mode"
            >
              {isAdvancedRibbonVisible && (
                <EnhancedRibbonToolbar
              // Dashboard Tab Props
              onOpen={() => on_ribbon_action?.('dashboard_open')}
              onSave={() => on_ribbon_action?.('dashboard_save')}
              onSaveAs={() => on_ribbon_action?.('dashboard_save_as')}
              onNew={() => on_ribbon_action?.('dashboard_new')}
              onRecent={() => on_ribbon_action?.('dashboard_recent')}
              onExport={(format) => on_ribbon_action?.('dashboard_export', { format })}
              onPrint={() => on_ribbon_action?.('dashboard_print')}
              onLogout={() => on_ribbon_action?.('dashboard_logout')}

              // Data Tab Props
              onDataSourceConnect={() => on_ribbon_action?.('data_connect')}
              onDataRefresh={() => on_ribbon_action?.('data_refresh')}
              onQueryManagement={() => on_ribbon_action?.('data_query')}
              onFilterToggle={() => on_ribbon_action?.('data_filter')}
              onSortToggle={() => on_ribbon_action?.('data_sort')}
              onDataPreview={() => on_ribbon_action?.('data_preview')}
              onDataCleaning={() => on_ribbon_action?.('data_clean')}

              // Insert/Widgets Tab Props - Primary widget creation interface (replaces header controls)
              onAddWidget={(type) => on_widget_create?.({ type })}
              onAddSection={() => on_section_create?.({})}
              onAddCustomHTML={() => on_ribbon_action?.('insert_html')}
              onWidgetSync={() => on_ribbon_action?.('widget_sync')}
              onWidgetManagement={() => on_ribbon_action?.('widget_manage')}

              // Style & Layout Tab Props
              onThemeChange={(theme) => on_ribbon_action?.('style_theme', { theme })}
              onLayoutChange={(layout) => on_ribbon_action?.('style_layout', { layout })}
              onStyleChange={(style) => on_ribbon_action?.('style_change', { style })}
              onTypographyChange={(typography) => on_ribbon_action?.('style_typography', { typography })}
              onAlignmentChange={(alignment) => on_ribbon_action?.('style_alignment', { alignment })}

              // Analyze Tab Props
              onDrillDown={() => on_ribbon_action?.('analyze_drill_down')}
              onGenerateInsights={() => on_ribbon_action?.('analyze_insights')}
              onTimeComparison={(period) => on_ribbon_action?.('analyze_time_comparison', { period })}
              onTrendAnalysis={() => on_ribbon_action?.('analyze_trends')}
              onStatisticalAnalysis={() => on_ribbon_action?.('analyze_statistics')}
              onFormulaEditor={() => on_ribbon_action?.('analyze_formula')}
              onRawDataView={() => on_ribbon_action?.('analyze_raw_data')}

              // Export & Share Tab Props
              onShare={() => on_ribbon_action?.('share_dashboard')}
              onGenerateEmbedCode={() => on_ribbon_action?.('share_embed')}
              onPermissionManagement={() => on_ribbon_action?.('share_permissions')}
              onScheduleReport={() => on_ribbon_action?.('share_schedule')}
              onCollaboration={() => on_ribbon_action?.('share_collaborate')}

              // Admin/Settings Tab Props
              onSettings={() => on_ribbon_action?.('admin_settings')}
              onUserPreferences={() => on_ribbon_action?.('admin_preferences')}
              onDataSourceConfig={() => on_data_configure?.({})}
              onAPIKeyManagement={() => on_ribbon_action?.('admin_api_keys')}
              onAccessControl={() => on_ribbon_action?.('admin_access')}
              onLanguageChange={(language) => on_ribbon_action?.('admin_language', { language })}

              // Interface Controls Props
              onModeToggle={handle_mode_toggle}
              onAIAssistantToggle={() => {
                // This will trigger the floating AI assistant
                // The FloatingAITrigger component handles the actual AI assistant display
                on_ribbon_action?.('interface_ai_assistant');
              }}

              // Common Props
              onUndo={() => on_ribbon_action?.('edit_undo')}
              onRedo={() => on_ribbon_action?.('edit_redo')}
              onRefresh={() => on_ribbon_action?.('data_refresh')}
              onViewChange={(view) => on_ribbon_action?.('view_change', { view })}

              // State Props
              widgetCount={0}
              lastUpdated={new Date()}
              isConnected={true}
              canUndo={false}
              canRedo={false}
              currentTheme="light"
              currentLayout="grid"
              availableDataSources={[]}
              userPermissions={{
                canEdit: true,
                canShare: true,
                canExport: true,
                canManageUsers: false,
                canAccessSettings: true,
              }}

              // UI Props
              isCompact={false}
              showQuickAccess={true}
              quickAccessItems={[]}

              // Legacy props for backward compatibility
              on_action={on_ribbon_action}
              show_keyboard_shortcuts={true}
              compact_mode={false}
                />
              )}
            </motion.div>

            {show_advanced_tools && (
              <div className="border-t bg-muted/30">
                <div className="container mx-auto px-4 py-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Settings className="h-4 w-4 text-purple-500" />
                      <span className="text-sm font-medium">Technical Controls</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => set_show_advanced_tools(!show_advanced_tools)}
                    >
                      {show_advanced_tools ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        }
      />

            {/* Main Content Area - Sidebar AI Assistant removed, using FloatingAITrigger only */}
            <div className="flex h-full">
              {/* Main Dashboard Content - Scrollable container */}
              <div className={cn(
                "flex-1 dashboard-scrollable-content",
                (isSimpleToolbarVisible || isAdvancedRibbonVisible) && "with-toolbar"
              )}>
                {children}
              </div>

              {/* Advanced Tools Panel (if in advanced mode and enabled) */}
              {current_mode === 'advanced' && show_advanced_tools && (
                <div className="w-96 border-l bg-background">
                  <div className="sticky top-20 h-[calc(100vh-5rem)] overflow-auto">
                    <TechnicalControlsPanel
                      on_query_execute={(query, type) => {
                        console.log('Execute query:', { query, type });
                      }}
                      on_performance_action={(action) => {
                        console.log('Performance action:', action);
                      }}
                      on_data_access={(action, params) => {
                        console.log('Data access:', { action, params });
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Floating AI Assistant - Removed: Now using FloatingAITrigger in DashboardLayout */}

      {/* Mode Transition Dialog */}
      {transition_dialog && (
        <ModeTransitionDialog
          open={transition_dialog.open}
          from_mode={transition_dialog.from_mode}
          to_mode={transition_dialog.to_mode}
          on_confirm={transition_dialog.on_confirm}
          on_cancel={cancel_transition}
          show_data_preservation={true}
        />
      )}
    </motion.div>
  );
};

// Utility component for mode-aware dashboard sections
interface ModeAwareDashboardSectionProps {
  title: string;
  children: React.ReactNode;
  simple_mode_props?: {
    show_ai_hints?: boolean;
    enable_guided_help?: boolean;
  };
  advanced_mode_props?: {
    show_technical_details?: boolean;
    enable_code_view?: boolean;
  };
}

export const ModeAwareDashboardSection: React.FC<ModeAwareDashboardSectionProps> = ({
  title,
  children,
  simple_mode_props = {},
  advanced_mode_props = {},
}) => {
  const { current_mode } = useDashboardMode();

  return (
    <Card className="">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">{title}</h3>
          <ModeIndicator variant="badge-only" />
        </div>
        
        <ModeAware
          simple_content={
            <div className="space-y-4">
              {simple_mode_props.show_ai_hints && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm text-blue-700">
                    💡 Need help? Ask the AI assistant for guidance!
                  </p>
                </div>
              )}
              {children}
            </div>
          }
          advanced_content={
            <div className="space-y-4">
              {advanced_mode_props.show_technical_details && (
                <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <p className="text-sm text-purple-700">
                    🔧 Technical controls and advanced options are available
                  </p>
                </div>
              )}
              {children}
            </div>
          }
        />
      </CardContent>
    </Card>
  );
};
