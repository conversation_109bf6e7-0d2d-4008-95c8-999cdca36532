#!/usr/bin/env python3
"""
Business Profile Migration Runner

This script runs the business profile database migration.
Run this after implementing the business profile models.
"""

import os
import sys
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.database import engine
from sqlalchemy import text

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_migration():
    """Run the business profile migration."""
    try:
        # Read the migration SQL file
        migration_file = backend_dir / "migrations" / "add_business_profiles.sql"
        
        if not migration_file.exists():
            logger.error(f"Migration file not found: {migration_file}")
            return False
        
        with open(migration_file, 'r') as f:
            migration_sql = f.read()
        
        # Execute the migration
        logger.info("Running business profile migration...")
        
        with engine.connect() as connection:
            # Split the SQL into individual statements
            statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip()]
            
            for i, statement in enumerate(statements):
                if statement:
                    try:
                        logger.info(f"Executing statement {i+1}/{len(statements)}")
                        connection.execute(text(statement))
                        connection.commit()
                    except Exception as e:
                        logger.error(f"Error executing statement {i+1}: {e}")
                        logger.error(f"Statement: {statement[:100]}...")
                        # Continue with other statements
                        continue
        
        logger.info("Business profile migration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return False


def check_migration_status():
    """Check if the migration has already been applied."""
    try:
        with engine.connect() as connection:
            # Check if business_profiles table exists
            result = connection.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'business_profiles'
                );
            """))
            
            table_exists = result.scalar()
            
            if table_exists:
                logger.info("Business profiles table already exists")
                
                # Check table structure
                result = connection.execute(text("""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = 'business_profiles' 
                    ORDER BY ordinal_position;
                """))
                
                columns = result.fetchall()
                logger.info(f"Business profiles table has {len(columns)} columns:")
                for col in columns:
                    logger.info(f"  - {col[0]}: {col[1]}")
                
                return True
            else:
                logger.info("Business profiles table does not exist")
                return False
                
    except Exception as e:
        logger.error(f"Error checking migration status: {e}")
        return False


def main():
    """Main function."""
    logger.info("Business Profile Migration Runner")
    logger.info("=" * 40)
    
    # Check current status
    if check_migration_status():
        response = input("Migration appears to already be applied. Run anyway? (y/N): ")
        if response.lower() != 'y':
            logger.info("Migration cancelled by user")
            return
    
    # Run the migration
    success = run_migration()
    
    if success:
        logger.info("Migration completed successfully!")
        
        # Verify the migration
        logger.info("Verifying migration...")
        if check_migration_status():
            logger.info("Migration verification passed!")
        else:
            logger.warning("Migration verification failed - please check manually")
    else:
        logger.error("Migration failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
