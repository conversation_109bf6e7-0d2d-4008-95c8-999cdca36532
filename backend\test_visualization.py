#!/usr/bin/env python3
"""
Test script to verify visualization functionality.
"""

import asyncio
import logging
import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.tools.mcp.pandasai_visualization import PandasAIVisualizationTool

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_fallback_visualization():
    """Test the fallback visualization functionality."""
    logger.info("Testing fallback visualization...")
    
    # Create a sample CSV file
    test_data = {
        'Product line': ['Fashion accessories', 'Health and beauty', 'Electronic accessories', 'Sports and travel', 'Home and lifestyle'],
        'Total': [178, 165, 170, 166, 160],
        'Quantity': [10, 8, 12, 9, 11],
        'gross income': [8.5, 7.8, 8.1, 7.9, 7.6]
    }
    
    df = pd.DataFrame(test_data)
    test_file = "test_mobile_duka.csv"
    df.to_csv(test_file, index=False)
    logger.info(f"Created test file: {test_file}")
    
    # Initialize the visualization tool
    viz_tool = PandasAIVisualizationTool()
    
    # Test the fallback visualization
    try:
        result = viz_tool._create_fallback_visualization(test_file, "Create a bar chart showing product line distribution")
        
        if result.get("isError"):
            logger.error(f"Fallback visualization failed: {result}")
        else:
            logger.info("Fallback visualization succeeded!")
            logger.info(f"Content items: {len(result.get('content', []))}")
            
            # Check if we have image content
            has_image = any(item.get("type") == "image" for item in result.get("content", []))
            logger.info(f"Has image content: {has_image}")
            
            if has_image:
                logger.info("✅ Fallback visualization is working correctly!")
            else:
                logger.error("❌ No image content found in result")
                
    except Exception as e:
        logger.error(f"Error testing fallback visualization: {e}", exc_info=True)
    
    # Clean up
    try:
        os.remove(test_file)
        logger.info(f"Cleaned up test file: {test_file}")
    except:
        pass

async def test_basic_matplotlib():
    """Test basic matplotlib functionality."""
    logger.info("Testing basic matplotlib...")
    
    try:
        # Create a simple plot
        plt.figure(figsize=(8, 6))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        plt.plot(x, y)
        plt.title("Test Plot")
        plt.xlabel("X")
        plt.ylabel("Y")
        
        # Save to file
        test_plot_file = "test_plot.png"
        plt.savefig(test_plot_file)
        plt.close()
        
        if os.path.exists(test_plot_file):
            logger.info("✅ Basic matplotlib is working!")
            os.remove(test_plot_file)
        else:
            logger.error("❌ Failed to create matplotlib plot")
            
    except Exception as e:
        logger.error(f"Error testing matplotlib: {e}", exc_info=True)

async def main():
    """Main test function."""
    logger.info("Starting visualization tests...")
    
    # Test basic matplotlib
    await test_basic_matplotlib()
    
    # Test fallback visualization
    await test_fallback_visualization()
    
    logger.info("Visualization tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
