"""
Analysis components for the Datagenius agent system.

This module provides components for data analysis tasks, including semantic layer
management and PandasAI training.
"""

import logging
import os
import sys
import pandas as pd
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from ..components.base import AgentComponent
from ..components.mcp_server import MCPServerComponent
from ..tools.mcp import initialize_mcp_system, AVAILABLE_TOOLS
from ..utils.model_providers.utils import get_model

# Configure logging
logger = logging.getLogger(__name__)


class AnalysisTask(BaseModel):
    """Model for analysis task request"""
    task_type: str = Field(..., description="Type of analysis task to perform")
    file_id: Any = Field(..., description="ID of the uploaded file to analyze (can be int or string)")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Task-specific parameters")


class AnalysisLLMComponent(AgentComponent):
    """Component for analysis LLM processing."""

    def __init__(self):
        """Initialize the analysis LLM component."""
        super().__init__()
        self.llm = None

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing AnalysisLLMComponent")

        try:
            # Get provider and model from config
            provider_id = config.get("provider", "").lower()
            model_id = config.get("model")
            api_key = config.get("api_key")

            # Create model config - use temperature=0 for analysis tasks
            model_config = {
                "temperature": 0,  # Use 0 temperature for analysis tasks for more deterministic results
                "api_key": api_key
            }

            # If endpoint is specified, add it to the config
            if "api_endpoint" in config:
                model_config["endpoint"] = config.get("api_endpoint")

            # Use the centralized model provider system to get the model
            self.llm = await get_model(provider_id, model_id, model_config)
            logger.info(f"Initialized model from provider '{provider_id}' for analysis tasks")

        except Exception as e:
            logger.error(f"Error initializing LLM for analysis: {str(e)}", exc_info=True)
            # Create a fake LLM as a fallback
            from langchain.llms.fake import FakeListLLM
            self.llm = FakeListLLM(responses=[
                f"Error initializing LLM for analysis: {str(e)}. Please check your configuration and API keys."
            ])

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state

        Returns:
            Updated AgentProcessingContext object
        """
        # This component doesn't process directly, it's used by other components
        return context


class AnalysisParserComponent(AgentComponent):
    """Component for parsing analysis requests."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing AnalysisParserComponent")

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state

        Returns:
            Updated AgentProcessingContext object
        """
        message = context.message or ""
        ctx = context.initial_context

        # Check if a file is attached by looking in multiple possible locations
        file_id = self._extract_file_id(ctx)

        # If no file is attached, ask the user to attach one
        if not file_id:
            context.response = "I need a data file to perform analysis. Please attach a file using the 'Attach Data' button."
            context.metadata["error"] = "no_file_attached"
            return context

        # Parse the analysis request
        try:
            task = await self._parse_analysis_request(message, ctx, file_id)

            # Update the context with the parsed information
            context.component_data["analysis_task"] = task.model_dump()

            logger.info(f"Parsed analysis request: task_type={task.task_type}, file_id={file_id}")
        except ValueError as e:
            context.response = str(e)
            context.metadata["error"] = "parsing_error"

        return context

    def _extract_file_id(self, context: Dict[str, Any]) -> Optional[str]:
        """
        Extract file ID from context by checking multiple possible locations.

        Args:
            context: Context dictionary containing request data

        Returns:
            File ID if found, None otherwise
        """
        # Check for file_id directly in context
        if "file_id" in context:
            return context["file_id"]

        # Check in data_source object (most common location from frontend)
        if "data_source" in context and isinstance(context["data_source"], dict):
            data_source = context["data_source"]

            # Check for ID in data_source
            if "id" in data_source:
                return data_source["id"]

            # Check for file_id in source_metadata
            if "source_metadata" in data_source and isinstance(data_source["source_metadata"], dict):
                if "file_id" in data_source["source_metadata"]:
                    return data_source["source_metadata"]["file_id"]

        # Check in metadata if present
        if "metadata" in context and isinstance(context["metadata"], dict):
            metadata = context["metadata"]

            # Check for file_id in metadata
            if "file_id" in metadata:
                return metadata["file_id"]

            # Check for file_path in metadata and extract ID
            if "file_path" in metadata and isinstance(metadata["file_path"], str):
                file_path = metadata["file_path"]
                parts = file_path.split('/')
                filename = parts[-1] if parts else ""
                if filename:
                    # Extract ID from filename (assuming format like "file_id.extension")
                    return filename.split('.')[0]

        # No file ID found
        return None

    async def _parse_analysis_request(self, message: str, context: Optional[Dict[str, Any]], file_id: str) -> AnalysisTask:
        """
        Parse the user message to determine the analysis task and parameters.

        Args:
            message: The user's message text
            context: Additional context information
            file_id: ID of the file to analyze

        Returns:
            AnalysisTask object with parsed parameters
        """
        # If context contains task parameters, use those
        if context and "task_params" in context:
            try:
                task_params = context["task_params"]
                # Ensure file_id is set in task_params
                if "file_id" not in task_params:
                    task_params["file_id"] = file_id
                return AnalysisTask(**task_params)
            except Exception as e:
                logger.error(f"Error parsing task parameters from context: {str(e)}")
                # Fall back to parsing from the message

        # Determine task type from message
        task_type = "data_querying"  # Default task type
        parameters = {}

        message_lower = message.lower()
        if "clean" in message_lower or "missing" in message_lower:
            task_type = "data_cleaning"
        elif "visual" in message_lower or "plot" in message_lower or "chart" in message_lower or "graph" in message_lower:
            task_type = "data_visualization"

            # Try to determine visualization type
            if "histogram" in message_lower:
                parameters["plot_type"] = "histogram"
            elif "scatter" in message_lower:
                parameters["plot_type"] = "scatter"
            elif "bar" in message_lower:
                parameters["plot_type"] = "bar"
            elif "line" in message_lower:
                parameters["plot_type"] = "line"
            elif "pie" in message_lower:
                parameters["plot_type"] = "pie"
            elif "box" in message_lower:
                parameters["plot_type"] = "box"
            elif "heatmap" in message_lower:
                parameters["plot_type"] = "heatmap"
            else:
                parameters["plot_type"] = "auto"

        elif "sentiment" in message_lower:
            task_type = "sentiment_analysis"
        elif "filter" in message_lower:
            task_type = "data_filtering"
        elif "advanced" in message_lower or "sql" in message_lower:
            task_type = "advanced_querying"

            # Extract query if present
            if "query:" in message_lower:
                query_part = message.split("query:", 1)[1].strip()
                parameters["query"] = query_part

        # For data querying, use the message as the query
        if task_type == "data_querying":
            parameters["query"] = message

        # Create the analysis task with the provided file_id
        try:
            return AnalysisTask(
                task_type=task_type,
                file_id=file_id,
                parameters=parameters
            )
        except Exception as e:
            logger.error(f"Error creating AnalysisTask: {str(e)}")
            # This might happen if file_id is a string but AnalysisTask expects an int
            # Try to convert file_id to int
            try:
                file_id_int = int(file_id)
                return AnalysisTask(
                    task_type=task_type,
                    file_id=file_id_int,
                    parameters=parameters
                )
            except (ValueError, TypeError):
                # If conversion fails, raise the original error
                raise ValueError(f"Invalid file ID format: {file_id}. Expected an integer.")


class DataLoaderComponent(AgentComponent):
    """Component for loading data from files."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing DataLoaderComponent")
        self.data_dir = config.get("data_dir", "data")

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state

        Returns:
            Updated AgentProcessingContext object
        """
        # Check if we have an analysis task
        if "analysis_task" not in context.component_data:
            return context

        task = context.component_data["analysis_task"]
        file_id = task.get("file_id")

        # If file_id is not in the analysis task, try to extract it from context
        if not file_id:
            # Try to extract file_id using the same method as AnalysisParserComponent
            ctx = context.initial_context
            file_id = self._extract_file_id_from_context(ctx)
            if file_id:
                # Update the analysis task with the file_id
                task["file_id"] = file_id
                logger.info(f"Found file_id in context: {file_id}")

        if not file_id:
            context.response = "No file ID provided for analysis. Please attach a data file using the 'Attach Data' button."
            context.metadata["error"] = "missing_file_id"
            return context

        # Load the data
        try:
            data = await self._load_data(file_id)
            context.component_data["data"] = data
            logger.info(f"Loaded data with shape {data.shape}")
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}", exc_info=True)
            context.response = f"I encountered an error loading your data: {str(e)}"
            context.metadata["error"] = "data_loading_error"

        return context

    def _extract_file_id_from_context(self, context: Dict[str, Any]) -> Optional[str]:
        """
        Extract file ID from context by checking multiple possible locations.
        This is a copy of the method from AnalysisParserComponent.

        Args:
            context: Context dictionary containing request data

        Returns:
            File ID if found, None otherwise
        """
        # Check for file_id directly in context
        if "file_id" in context:
            return context["file_id"]

        # Check in data_source object (most common location from frontend)
        if "data_source" in context and isinstance(context["data_source"], dict):
            data_source = context["data_source"]

            # Check for ID in data_source
            if "id" in data_source:
                return data_source["id"]

            # Check for file_id in source_metadata
            if "source_metadata" in data_source and isinstance(data_source["source_metadata"], dict):
                if "file_id" in data_source["source_metadata"]:
                    return data_source["source_metadata"]["file_id"]

        # Check in metadata if present
        if "metadata" in context and isinstance(context["metadata"], dict):
            metadata = context["metadata"]

            # Check for file_id in metadata
            if "file_id" in metadata:
                return metadata["file_id"]

            # Check for file_path in metadata and extract ID
            if "file_path" in metadata and isinstance(metadata["file_path"], str):
                file_path = metadata["file_path"]
                parts = file_path.split('/')
                filename = parts[-1] if parts else ""
                if filename:
                    # Extract ID from filename (assuming format like "file_id.extension")
                    return filename.split('.')[0]

        # No file ID found
        return None

    async def _load_data(self, file_id: Any) -> pd.DataFrame:
        """
        Load data from a file.

        Args:
            file_id: ID of the file to load (can be int or string)

        Returns:
            Loaded data as a pandas DataFrame

        Raises:
            FileNotFoundError: If no file is found for the given file_id
        """
        # Convert file_id to string for path construction
        file_id_str = str(file_id)

        # Try different file paths based on possible formats
        possible_paths = [
            os.path.join(self.data_dir, f"file_{file_id_str}.csv"),
            os.path.join(self.data_dir, f"{file_id_str}.csv"),
            os.path.join(self.data_dir, f"file_{file_id_str}.xlsx"),
            os.path.join(self.data_dir, f"{file_id_str}.xlsx")
        ]

        # Check if any of the possible file paths exist
        for file_path in possible_paths:
            if os.path.exists(file_path):
                logger.info(f"Found file at path: {file_path}")
                # Load the file based on its extension
                if file_path.endswith(".csv"):
                    return pd.read_csv(file_path)
                elif file_path.endswith((".xlsx", ".xls")):
                    return pd.read_excel(file_path)
                else:
                    raise ValueError(f"Unsupported file format: {file_path}")

        # If no file is found, raise an error
        logger.error(f"No file found for file_id: {file_id_str}. Cannot proceed without attached data.")
        raise FileNotFoundError(f"No data file found for ID: {file_id_str}. Please attach a valid data file.")


class AnalysisExecutorComponent(AgentComponent):
    """Component for executing analysis tasks."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing AnalysisExecutorComponent")

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state

        Returns:
            Updated AgentProcessingContext object
        """
        # Check if we have an analysis task and data
        if "analysis_task" not in context.component_data or "data" not in context.component_data:
            return context

        task = context.component_data["analysis_task"]
        data = context.component_data["data"]

        # Get the LLM component for tasks that need it
        llm = None
        # Note: In the new architecture, we would need to access the LLM differently
        # For now, we'll handle this in the _perform_analysis method

        # Perform the analysis
        try:
            result = await self._perform_analysis(task, data, llm, context)
            # Update context with result
            if "response" in result:
                context.response = result["response"]
            if "metadata" in result:
                context.metadata.update(result["metadata"])
            # Store any additional data in component_data
            for key, value in result.items():
                if key not in ["response", "metadata"]:
                    context.component_data[key] = value
        except Exception as e:
            logger.error(f"Error performing analysis: {str(e)}", exc_info=True)
            context.response = f"I encountered an error performing the analysis: {str(e)}"
            context.metadata["error"] = "analysis_error"

        return context

    async def _perform_analysis(self, task: Dict[str, Any], data: pd.DataFrame, llm: Any, context: "AgentProcessingContext") -> Dict[str, Any]:
        """
        Perform the requested analysis task.

        Args:
            task: The analysis task to perform
            data: The data to analyze
            llm: The language model to use (currently unused)
            context: The AgentProcessingContext object

        Returns:
            Dict containing response text and any additional data
        """
        task_type = task["task_type"]
        parameters = task["parameters"]

        # Get prompt templates from agent config
        prompt_templates = context.agent_config.system_prompts if context.agent_config else {}

        # Map task types to prompt template names
        prompt_mapping = {
            "data_cleaning": "data_cleaning",
            "data_visualization": "data_visualization",
            "data_querying": "data_querying",
            "advanced_querying": "advanced_querying",
            "data_filtering": "data_filtering",
            "sentiment_analysis": "sentiment_analysis"
        }

        # Get the prompt template name for the task type
        prompt_name = prompt_mapping.get(task_type)

        # Get MCP server component from context
        try:
            from agents.components.mcp_server import MCPServerComponent

            # Access MCP server through component registry
            mcp_server = context.get_component('mcp_server')

            if not mcp_server:
                # Try to create MCP server component if not available
                mcp_server = MCPServerComponent()
                context.set_component('mcp_server', mcp_server)

        except Exception as e:
            logger.error(f"Error accessing MCP server component: {e}")
            return {
                "response": f"Unable to access analysis tools: {str(e)}",
                "metadata": {"error": "mcp_server_access_failed", "details": str(e)}
            }

        # Save data to a temporary file
        import tempfile
        import uuid

        temp_dir = tempfile.gettempdir()
        file_id = str(uuid.uuid4())
        file_path = os.path.join(temp_dir, f"{file_id}.csv")

        data.to_csv(file_path, index=False)
        logger.info(f"Saved data to temporary file: {file_path}")

        try:
            if task_type == "data_cleaning":
                # Call the MCP data cleaning tool
                tool_result = await mcp_server.call_tool("clean_data", {
                    "file_path": file_path,
                    "drop_columns": parameters.get("drop_columns", []),
                    "drop_rows": parameters.get("drop_rows", False),
                    "fill_method": parameters.get("fill_method", None),
                    "custom_value": parameters.get("custom_value", None)
                })

                # Extract the result from the tool response
                if tool_result.get("isError", False):
                    raise ValueError(tool_result.get("content", [{"text": "Unknown error"}])[0].get("text", "Unknown error"))

                # Get the summary from metadata
                summary = tool_result.get("metadata", {}).get("summary", {})
                output_path = tool_result.get("metadata", {}).get("output_path", "")

                # Load the cleaned data
                if os.path.exists(output_path):
                    result = pd.read_csv(output_path)
                else:
                    result = data  # Fallback to original data

                # Use prompt template if available
                if prompt_name in prompt_templates:
                    from agents.utils.prompt_template import PromptTemplate
                    template = PromptTemplate(prompt_templates[prompt_name])
                    message = template.format(
                        shape=str(result.shape),
                        columns=", ".join(result.columns.tolist()),
                        missing_values=str(result.isnull().sum().to_dict())
                    )
                else:
                    # Extract message from tool result
                    content_items = tool_result.get("content", [])
                    message = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])
                    if not message:
                        message = "Data cleaning completed successfully."

                return {
                    "response": message,
                    "metadata": {
                        "task_type": task_type,
                        "data_info": {
                            "shape": result.shape,
                            "columns": result.columns.tolist(),
                            "missing_values": result.isnull().sum().to_dict(),
                            "preview": result.head().to_dict(orient="records")
                        }
                    }
                }

            elif task_type == "data_visualization":
                plot_type = parameters.get("plot_type", "auto")
                if plot_type == "auto":
                    plot_type = "bar"  # Default to bar chart

                x_column = parameters.get("x_column")
                y_column = parameters.get("y_column")

                # If columns are not specified, try to infer them
                if not x_column:
                    # Use the first categorical or datetime column as x
                    for col in data.columns:
                        if data[col].dtype == "object" or pd.api.types.is_datetime64_any_dtype(data[col]):
                            x_column = col
                            break

                    # If no categorical column found, use the first column
                    if not x_column and len(data.columns) > 0:
                        x_column = data.columns[0]

                if not y_column:
                    # Use the first numeric column as y
                    for col in data.columns:
                        if pd.api.types.is_numeric_dtype(data[col]) and col != x_column:
                            y_column = col
                            break

                    # If no numeric column found, use the second column if available
                    if not y_column and len(data.columns) > 1:
                        y_column = data.columns[1]

                # Call the MCP data visualization tool
                tool_result = await mcp_server.call_tool("visualize_data", {
                    "file_path": file_path,
                    "plot_type": plot_type,
                    "x_column": x_column,
                    "y_column": y_column,
                    "color_column": parameters.get("color_column"),
                    "title": parameters.get("title"),
                    "width": parameters.get("width", 800),
                    "height": parameters.get("height", 500)
                })

                # Extract the result from the tool response
                if tool_result.get("isError", False):
                    raise ValueError(tool_result.get("content", [{"text": "Unknown error"}])[0].get("text", "Unknown error"))

                # Get the visualization from metadata
                visualization = tool_result.get("metadata", {})

                # Use prompt template if available
                if prompt_name in prompt_templates:
                    from agents.utils.prompt_template import PromptTemplate
                    template = PromptTemplate(prompt_templates[prompt_name])
                    message = template.format(
                        plot_type=plot_type,
                        x_column=x_column,
                        y_column=y_column
                    )
                else:
                    # Extract message from tool result
                    content_items = tool_result.get("content", [])
                    message = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])
                    if not message:
                        message = f"Generated a {plot_type} visualization using {x_column}" + (f" and {y_column}" if y_column else "")

                return {
                    "response": message,
                    "metadata": {
                        "task_type": task_type,
                        "visualization": visualization,
                        "parameters": {
                            "plot_type": plot_type,
                            "x_column": x_column,
                            "y_column": y_column
                        }
                    }
                }

            elif task_type == "data_querying":
                query = parameters.get("query", "")

                # Call the MCP data querying tool
                tool_result = await mcp_server.call_tool("query_data", {
                    "file_path": file_path,
                    "query": query,
                    "provider": "groq",  # Default provider
                    "model": "llama3-70b-8192",  # Default model
                    "temperature": 0.0  # Use 0 temperature for analysis tasks
                })

                # Extract the result from the tool response
                if tool_result.get("isError", False):
                    raise ValueError(tool_result.get("content", [{"text": "Unknown error"}])[0].get("text", "Unknown error"))

                # Get the data preview from metadata
                data_preview = tool_result.get("metadata", {}).get("data_preview")

                # Extract message from tool result
                content_items = tool_result.get("content", [])
                answer = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])

                # For data querying, we use the result from the query_data function
                # but we can enhance it with a prompt template if available
                if "data_querying_response" in prompt_templates:
                    from agents.utils.prompt_template import PromptTemplate
                    template = PromptTemplate(prompt_templates["data_querying_response"])
                    message = template.format(
                        answer=answer,
                        query=query
                    )
                else:
                    message = answer

                return {
                    "response": message,
                    "metadata": {
                        "task_type": task_type,
                        "query": query,
                        "data_preview": data_preview
                    }
                }

            elif task_type == "advanced_querying":
                query = parameters.get("query", "")

                # Call the MCP advanced query tool
                tool_result = await mcp_server.call_tool("advanced_query", {
                    "file_path": file_path,
                    "query": query
                })

                # Extract the result from the tool response
                if tool_result.get("isError", False):
                    raise ValueError(tool_result.get("content", [{"text": "Unknown error"}])[0].get("text", "Unknown error"))

                # Get the result preview from metadata
                result_preview = tool_result.get("metadata", {}).get("result_preview")

                # Extract message from tool result
                content_items = tool_result.get("content", [])
                result_text = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])

                # Use prompt template if available
                if prompt_name in prompt_templates:
                    from agents.utils.prompt_template import PromptTemplate
                    template = PromptTemplate(prompt_templates[prompt_name])
                    message = template.format(
                        query=query,
                        result=result_text
                    )
                else:
                    message = result_text

                return {
                    "response": message,
                    "metadata": {
                        "task_type": task_type,
                        "query": query,
                        "result": result_preview
                    }
                }

            elif task_type == "data_filtering":
                filters = parameters.get("filters", [])
                combine_with = parameters.get("combine_with", "and")

                # Call the MCP data filtering tool
                tool_result = await mcp_server.call_tool("filter_data", {
                    "file_path": file_path,
                    "filters": filters,
                    "combine_with": combine_with
                })

                # Extract the result from the tool response
                if tool_result.get("isError", False):
                    raise ValueError(tool_result.get("content", [{"text": "Unknown error"}])[0].get("text", "Unknown error"))

                # Get the summary and preview from metadata
                summary = tool_result.get("metadata", {}).get("summary", {})
                preview = tool_result.get("metadata", {}).get("preview", [])
                output_path = tool_result.get("metadata", {}).get("output_path", "")

                # Load the filtered data
                if os.path.exists(output_path):
                    result = pd.read_csv(output_path)
                else:
                    result = data  # Fallback to original data

                # Use prompt template if available
                if prompt_name in prompt_templates:
                    from agents.utils.prompt_template import PromptTemplate
                    template = PromptTemplate(prompt_templates[prompt_name])
                    message = template.format(
                        filters=str(filters),
                        result_shape=str(summary.get("filtered_shape", result.shape))
                    )
                else:
                    # Extract message from tool result
                    content_items = tool_result.get("content", [])
                    message = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])
                    if not message:
                        message = f"Data filtered successfully. Result shape: {result.shape}"

                return {
                    "response": message,
                    "metadata": {
                        "task_type": task_type,
                        "filters": filters,
                        "result_shape": result.shape,
                        "preview": preview or result.head().to_dict(orient="records")
                    }
                }

            elif task_type == "sentiment_analysis":
                text_column = parameters.get("text_column", None)

                # Call the MCP sentiment analysis tool
                tool_result = await mcp_server.call_tool("analyze_sentiment", {
                    "file_path": file_path,
                    "text_column": text_column
                })

                # Extract the result from the tool response
                if tool_result.get("isError", False):
                    raise ValueError(tool_result.get("content", [{"text": "Unknown error"}])[0].get("text", "Unknown error"))

                # Get the summary and preview from metadata
                summary = tool_result.get("metadata", {}).get("summary", {})
                preview = tool_result.get("metadata", {}).get("preview", [])

                # Use prompt template if available
                if prompt_name in prompt_templates:
                    from agents.utils.prompt_template import PromptTemplate
                    template = PromptTemplate(prompt_templates[prompt_name])
                    message = template.format(
                        summary=str(summary),
                        text_column=text_column or "auto-detected"
                    )
                else:
                    # Extract message from tool result
                    content_items = tool_result.get("content", [])
                    message = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])
                    if not message:
                        message = "Sentiment analysis completed successfully."

                return {
                    "response": message,
                    "metadata": {
                        "task_type": task_type,
                        "sentiment_summary": summary,
                        "sentiment_data": preview
                    }
                }

            else:
                raise ValueError(f"Unsupported task type: {task_type}")

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"Removed temporary file: {file_path}")
            except Exception as e:
                logger.warning(f"Error removing temporary file: {str(e)}")


class SemanticLayerComponent(AgentComponent):
    """Component for managing semantic layers."""

    def __init__(self):
        """Initialize the semantic layer component."""
        super().__init__()
        self.manager = None

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the component with configuration."""
        from agents.tools.pandasai_v3.semantic_layer import SemanticLayerManager
        self.manager = SemanticLayerManager()
        logger.info("Initialized SemanticLayerComponent")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a request using this component."""
        # Check if we need to create a semantic layer
        if "create_semantic_layer" in context:
            file_path = context["create_semantic_layer"]["file_path"]
            name = context["create_semantic_layer"]["name"]
            description = context["create_semantic_layer"]["description"]

            # Load dataframe
            try:
                if file_path.endswith('.csv'):
                    df = pd.read_csv(file_path)
                elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                    df = pd.read_excel(file_path)
                else:
                    context["response"] = f"Unsupported file format: {file_path}"
                    return context

                # Infer columns
                columns = self.manager.infer_columns(df)

                # Create semantic layer
                layer_path = self.manager.create_layer(df, name, description, columns)

                if layer_path:
                    context["semantic_layer_path"] = layer_path
                    context["response"] = f"Created semantic layer: {layer_path}"
                else:
                    context["response"] = "Failed to create semantic layer"
            except Exception as e:
                logger.error(f"Error creating semantic layer: {e}", exc_info=True)
                context["response"] = f"Error creating semantic layer: {str(e)}"

        return context


class PandasAITrainingComponent(AgentComponent):
    """Component for training PandasAI agents."""

    def __init__(self):
        """Initialize the training component."""
        super().__init__()
        self.wrapper = None

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the component with configuration."""
        from agents.tools.pandasai_v3.wrapper import PandasAIWrapper
        self.wrapper = PandasAIWrapper()
        logger.info("Initialized PandasAITrainingComponent")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process a request using this component."""
        # Check if we need to train a PandasAI agent
        if "train_pandasai" in context:
            file_path = context["train_pandasai"]["file_path"]
            instructions = context["train_pandasai"].get("instructions")
            queries = context["train_pandasai"].get("queries")
            codes = context["train_pandasai"].get("codes")
            api_key = context["train_pandasai"].get("api_key")
            provider = context["train_pandasai"].get("provider", "openai")

            # Initialize PandasAI
            self.wrapper.initialize(api_key, provider)

            # Load dataframe
            if not self.wrapper.load_dataframe(file_path):
                context["response"] = f"Error loading dataframe from {file_path}"
                return context

            # Create agent
            if not self.wrapper.create_agent():
                context["response"] = "Error creating PandasAI Agent"
                return context

            # Train agent
            success = self.wrapper.train(instructions, queries, codes)

            if success:
                context["response"] = "Successfully trained PandasAI Agent"
            else:
                context["response"] = "Failed to train PandasAI Agent"

        return context
