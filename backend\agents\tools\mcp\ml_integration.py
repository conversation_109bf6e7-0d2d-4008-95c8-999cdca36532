"""
Machine Learning Integration Module for MCP Tools.

This module provides comprehensive machine learning capabilities including
automated model selection, classification, regression, clustering, and
model evaluation metrics for the statistical analysis tool.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
import warnings

logger = logging.getLogger(__name__)


class MLTaskType(Enum):
    """Machine learning task types."""
    CLASSIFICATION = "classification"
    REGRESSION = "regression"
    CLUSTERING = "clustering"
    DIMENSIONALITY_REDUCTION = "dimensionality_reduction"
    ANOMALY_DETECTION = "anomaly_detection"
    TIME_SERIES_FORECASTING = "time_series_forecasting"


class ModelType(Enum):
    """Available model types."""
    # Classification
    LOGISTIC_REGRESSION = "logistic_regression"
    RANDOM_FOREST_CLASSIFIER = "random_forest_classifier"
    SVM_CLASSIFIER = "svm_classifier"
    GRADIENT_BOOSTING_CLASSIFIER = "gradient_boosting_classifier"
    NAIVE_BAYES = "naive_bayes"
    KNN_CLASSIFIER = "knn_classifier"
    
    # Regression
    LINEAR_REGRESSION = "linear_regression"
    RANDOM_FOREST_REGRESSOR = "random_forest_regressor"
    SVM_REGRESSOR = "svm_regressor"
    GRADIENT_BOOSTING_REGRESSOR = "gradient_boosting_regressor"
    KNN_REGRESSOR = "knn_regressor"
    
    # Clustering
    KMEANS = "kmeans"
    HIERARCHICAL = "hierarchical"
    DBSCAN = "dbscan"
    GAUSSIAN_MIXTURE = "gaussian_mixture"
    
    # Dimensionality Reduction
    PCA = "pca"
    TSNE = "tsne"
    UMAP = "umap"
    
    # Anomaly Detection
    ISOLATION_FOREST = "isolation_forest"
    ONE_CLASS_SVM = "one_class_svm"
    LOCAL_OUTLIER_FACTOR = "local_outlier_factor"


@dataclass
class MLResult:
    """Container for machine learning results."""
    task_type: MLTaskType
    model_type: ModelType
    model: Any
    predictions: np.ndarray
    performance_metrics: Dict[str, float]
    feature_importance: Optional[Dict[str, float]] = None
    cross_validation_scores: Optional[List[float]] = None
    model_parameters: Dict[str, Any] = None
    preprocessing_info: Dict[str, Any] = None
    interpretation: str = ""
    recommendations: List[str] = None

    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []
        if self.model_parameters is None:
            self.model_parameters = {}
        if self.preprocessing_info is None:
            self.preprocessing_info = {}


class MLIntegration:
    """
    Machine Learning integration with automated model selection and evaluation.
    
    Features:
    - Automated model selection based on data characteristics
    - Comprehensive preprocessing pipeline
    - Cross-validation and performance evaluation
    - Feature importance analysis
    - Model interpretation and recommendations
    - Agent-aware result formatting
    """
    
    def __init__(self):
        """Initialize the ML integration module."""
        self.random_state = 42
        self.test_size = 0.2
        self.cv_folds = 5
        
        # Import ML libraries with fallbacks
        try:
            from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
            from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
            from sklearn.metrics import (
                accuracy_score, precision_score, recall_score, f1_score,
                mean_squared_error, mean_absolute_error, r2_score,
                silhouette_score, adjusted_rand_score
            )
            from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
            from sklearn.linear_model import LogisticRegression, LinearRegression
            from sklearn.svm import SVC, SVR
            from sklearn.cluster import KMeans, DBSCAN
            from sklearn.decomposition import PCA
            from sklearn.neighbors import KNeighborsClassifier, KNeighborsRegressor
            
            self.sklearn_available = True
            self.train_test_split = train_test_split
            self.cross_val_score = cross_val_score
            self.GridSearchCV = GridSearchCV
            self.StandardScaler = StandardScaler
            self.LabelEncoder = LabelEncoder
            self.OneHotEncoder = OneHotEncoder
            
            # Store metrics
            self.metrics = {
                'accuracy_score': accuracy_score,
                'precision_score': precision_score,
                'recall_score': recall_score,
                'f1_score': f1_score,
                'mean_squared_error': mean_squared_error,
                'mean_absolute_error': mean_absolute_error,
                'r2_score': r2_score,
                'silhouette_score': silhouette_score,
                'adjusted_rand_score': adjusted_rand_score
            }
            
            # Store models
            self.models = {
                ModelType.LOGISTIC_REGRESSION: LogisticRegression,
                ModelType.RANDOM_FOREST_CLASSIFIER: RandomForestClassifier,
                ModelType.SVM_CLASSIFIER: SVC,
                ModelType.LINEAR_REGRESSION: LinearRegression,
                ModelType.RANDOM_FOREST_REGRESSOR: RandomForestRegressor,
                ModelType.SVM_REGRESSOR: SVR,
                ModelType.KNN_CLASSIFIER: KNeighborsClassifier,
                ModelType.KNN_REGRESSOR: KNeighborsRegressor,
                ModelType.KMEANS: KMeans,
                ModelType.DBSCAN: DBSCAN,
                ModelType.PCA: PCA
            }
            
        except ImportError:
            logger.warning("Scikit-learn not available - ML functionality will be limited")
            self.sklearn_available = False

    def auto_ml_pipeline(self, data: pd.DataFrame, target_column: str = None,
                        task_type: MLTaskType = None, **kwargs) -> MLResult:
        """
        Automated ML pipeline with model selection and evaluation.
        
        Args:
            data: Input DataFrame
            target_column: Target variable column name (for supervised learning)
            task_type: Type of ML task (auto-detected if None)
            **kwargs: Additional parameters
            
        Returns:
            MLResult with comprehensive analysis results
        """
        if not self.sklearn_available:
            raise ImportError("Scikit-learn required for ML functionality")
        
        # Auto-detect task type if not specified
        if task_type is None:
            task_type = self._detect_task_type(data, target_column)
        
        # Preprocess data
        X, y, preprocessing_info = self._preprocess_data(data, target_column, task_type)
        
        # Select best model
        best_model_type, best_model, best_score = self._auto_model_selection(X, y, task_type)
        
        # Train final model and get predictions
        if task_type in [MLTaskType.CLASSIFICATION, MLTaskType.REGRESSION]:
            X_train, X_test, y_train, y_test = self.train_test_split(
                X, y, test_size=self.test_size, random_state=self.random_state
            )
            
            best_model.fit(X_train, y_train)
            predictions = best_model.predict(X_test)
            
            # Calculate performance metrics
            performance_metrics = self._calculate_performance_metrics(
                y_test, predictions, task_type
            )
            
            # Cross-validation scores
            cv_scores = self.cross_val_score(
                best_model, X, y, cv=self.cv_folds, 
                scoring=self._get_scoring_metric(task_type)
            ).tolist()
            
            # Feature importance
            feature_importance = self._get_feature_importance(
                best_model, data.columns.drop(target_column) if target_column else data.columns
            )
            
        else:  # Unsupervised learning
            best_model.fit(X)
            predictions = best_model.predict(X) if hasattr(best_model, 'predict') else best_model.labels_
            
            performance_metrics = self._calculate_unsupervised_metrics(X, predictions, task_type)
            cv_scores = None
            feature_importance = None
        
        # Generate interpretation and recommendations
        interpretation = self._generate_interpretation(
            task_type, best_model_type, performance_metrics, feature_importance
        )
        recommendations = self._generate_recommendations(
            task_type, performance_metrics, preprocessing_info
        )
        
        return MLResult(
            task_type=task_type,
            model_type=best_model_type,
            model=best_model,
            predictions=predictions,
            performance_metrics=performance_metrics,
            feature_importance=feature_importance,
            cross_validation_scores=cv_scores,
            model_parameters=best_model.get_params(),
            preprocessing_info=preprocessing_info,
            interpretation=interpretation,
            recommendations=recommendations
        )

    def _detect_task_type(self, data: pd.DataFrame, target_column: str = None) -> MLTaskType:
        """Auto-detect the appropriate ML task type."""
        if target_column is None:
            # No target variable - unsupervised learning
            if data.shape[1] > 10:
                return MLTaskType.DIMENSIONALITY_REDUCTION
            else:
                return MLTaskType.CLUSTERING
        
        target_data = data[target_column]
        
        # Check if target is numeric
        if pd.api.types.is_numeric_dtype(target_data):
            # Check if it looks like classification (few unique values)
            unique_ratio = target_data.nunique() / len(target_data)
            if unique_ratio < 0.05 or target_data.nunique() <= 10:
                return MLTaskType.CLASSIFICATION
            else:
                return MLTaskType.REGRESSION
        else:
            # Categorical target
            return MLTaskType.CLASSIFICATION

    def _preprocess_data(self, data: pd.DataFrame, target_column: str = None, 
                        task_type: MLTaskType = None) -> Tuple[np.ndarray, np.ndarray, Dict[str, Any]]:
        """Preprocess data for ML algorithms."""
        preprocessing_info = {
            'original_shape': data.shape,
            'missing_values_handled': 0,
            'categorical_columns_encoded': [],
            'numerical_columns_scaled': [],
            'target_encoded': False
        }
        
        # Separate features and target
        if target_column:
            X = data.drop(columns=[target_column]).copy()
            y = data[target_column].copy()
        else:
            X = data.copy()
            y = None
        
        # Handle missing values
        missing_count = X.isnull().sum().sum()
        if missing_count > 0:
            # Simple imputation - could be enhanced
            for col in X.columns:
                if X[col].isnull().any():
                    if pd.api.types.is_numeric_dtype(X[col]):
                        X[col].fillna(X[col].median(), inplace=True)
                    else:
                        X[col].fillna(X[col].mode()[0] if not X[col].mode().empty else 'Unknown', inplace=True)
            preprocessing_info['missing_values_handled'] = missing_count
        
        # Encode categorical variables
        categorical_columns = X.select_dtypes(include=['object', 'category']).columns
        if len(categorical_columns) > 0:
            # Use label encoding for simplicity - could use one-hot encoding for better results
            label_encoders = {}
            for col in categorical_columns:
                le = self.LabelEncoder()
                X[col] = le.fit_transform(X[col].astype(str))
                label_encoders[col] = le
            preprocessing_info['categorical_columns_encoded'] = categorical_columns.tolist()
            preprocessing_info['label_encoders'] = label_encoders
        
        # Scale numerical features
        numerical_columns = X.select_dtypes(include=[np.number]).columns
        if len(numerical_columns) > 0 and task_type != MLTaskType.CLUSTERING:
            scaler = self.StandardScaler()
            X[numerical_columns] = scaler.fit_transform(X[numerical_columns])
            preprocessing_info['numerical_columns_scaled'] = numerical_columns.tolist()
            preprocessing_info['scaler'] = scaler
        
        # Encode target variable if needed
        if y is not None and not pd.api.types.is_numeric_dtype(y):
            target_encoder = self.LabelEncoder()
            y = target_encoder.fit_transform(y.astype(str))
            preprocessing_info['target_encoded'] = True
            preprocessing_info['target_encoder'] = target_encoder
        
        preprocessing_info['final_shape'] = X.shape
        
        return X.values, y.values if y is not None else None, preprocessing_info

    def _auto_model_selection(self, X: np.ndarray, y: np.ndarray,
                             task_type: MLTaskType) -> Tuple[ModelType, Any, float]:
        """Automatically select the best model for the given task."""
        if task_type == MLTaskType.CLASSIFICATION:
            candidate_models = [
                (ModelType.LOGISTIC_REGRESSION, self.models[ModelType.LOGISTIC_REGRESSION](random_state=self.random_state)),
                (ModelType.RANDOM_FOREST_CLASSIFIER, self.models[ModelType.RANDOM_FOREST_CLASSIFIER](random_state=self.random_state)),
                (ModelType.KNN_CLASSIFIER, self.models[ModelType.KNN_CLASSIFIER]())
            ]
            scoring = 'accuracy'

        elif task_type == MLTaskType.REGRESSION:
            candidate_models = [
                (ModelType.LINEAR_REGRESSION, self.models[ModelType.LINEAR_REGRESSION]()),
                (ModelType.RANDOM_FOREST_REGRESSOR, self.models[ModelType.RANDOM_FOREST_REGRESSOR](random_state=self.random_state)),
                (ModelType.KNN_REGRESSOR, self.models[ModelType.KNN_REGRESSOR]())
            ]
            scoring = 'r2'

        elif task_type == MLTaskType.CLUSTERING:
            # For clustering, we'll use silhouette score to evaluate
            best_model_type = ModelType.KMEANS
            best_model = self.models[ModelType.KMEANS](n_clusters=self._estimate_optimal_clusters(X), random_state=self.random_state)
            return best_model_type, best_model, 0.0

        else:
            # Default to simple models for other tasks
            best_model_type = ModelType.PCA
            best_model = self.models[ModelType.PCA](n_components=min(5, X.shape[1]))
            return best_model_type, best_model, 0.0

        # Evaluate models using cross-validation
        best_score = -np.inf
        best_model_type = None
        best_model = None

        for model_type, model in candidate_models:
            try:
                scores = self.cross_val_score(model, X, y, cv=self.cv_folds, scoring=scoring)
                avg_score = scores.mean()

                if avg_score > best_score:
                    best_score = avg_score
                    best_model_type = model_type
                    best_model = model

            except Exception as e:
                logger.warning(f"Failed to evaluate {model_type}: {e}")
                continue

        return best_model_type, best_model, best_score

    def _estimate_optimal_clusters(self, X: np.ndarray) -> int:
        """Estimate optimal number of clusters using elbow method."""
        if X.shape[0] < 10:
            return 2

        max_clusters = min(10, X.shape[0] // 2)
        inertias = []

        for k in range(2, max_clusters + 1):
            try:
                kmeans = self.models[ModelType.KMEANS](n_clusters=k, random_state=self.random_state)
                kmeans.fit(X)
                inertias.append(kmeans.inertia_)
            except:
                break

        if len(inertias) < 2:
            return 3

        # Simple elbow detection
        diffs = np.diff(inertias)
        if len(diffs) > 1:
            second_diffs = np.diff(diffs)
            elbow_idx = np.argmax(second_diffs) + 2
            return min(elbow_idx, max_clusters)

        return 3

    def _calculate_performance_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                                     task_type: MLTaskType) -> Dict[str, float]:
        """Calculate performance metrics based on task type."""
        metrics = {}

        if task_type == MLTaskType.CLASSIFICATION:
            metrics['accuracy'] = float(self.metrics['accuracy_score'](y_true, y_pred))

            # Handle binary vs multiclass
            average = 'binary' if len(np.unique(y_true)) == 2 else 'weighted'

            try:
                metrics['precision'] = float(self.metrics['precision_score'](y_true, y_pred, average=average, zero_division=0))
                metrics['recall'] = float(self.metrics['recall_score'](y_true, y_pred, average=average, zero_division=0))
                metrics['f1_score'] = float(self.metrics['f1_score'](y_true, y_pred, average=average, zero_division=0))
            except:
                # Fallback for edge cases
                metrics['precision'] = metrics['accuracy']
                metrics['recall'] = metrics['accuracy']
                metrics['f1_score'] = metrics['accuracy']

        elif task_type == MLTaskType.REGRESSION:
            metrics['r2_score'] = float(self.metrics['r2_score'](y_true, y_pred))
            metrics['mean_squared_error'] = float(self.metrics['mean_squared_error'](y_true, y_pred))
            metrics['mean_absolute_error'] = float(self.metrics['mean_absolute_error'](y_true, y_pred))
            metrics['rmse'] = float(np.sqrt(metrics['mean_squared_error']))

        return metrics

    def _calculate_unsupervised_metrics(self, X: np.ndarray, labels: np.ndarray,
                                      task_type: MLTaskType) -> Dict[str, float]:
        """Calculate metrics for unsupervised learning."""
        metrics = {}

        if task_type == MLTaskType.CLUSTERING:
            try:
                if len(np.unique(labels)) > 1:
                    metrics['silhouette_score'] = float(self.metrics['silhouette_score'](X, labels))
                else:
                    metrics['silhouette_score'] = 0.0

                metrics['n_clusters'] = float(len(np.unique(labels)))

            except Exception as e:
                logger.warning(f"Failed to calculate clustering metrics: {e}")
                metrics['silhouette_score'] = 0.0
                metrics['n_clusters'] = 1.0

        elif task_type == MLTaskType.DIMENSIONALITY_REDUCTION:
            # For PCA, we can calculate explained variance ratio
            metrics['n_components'] = float(X.shape[1])

        return metrics

    def _get_scoring_metric(self, task_type: MLTaskType) -> str:
        """Get appropriate scoring metric for cross-validation."""
        if task_type == MLTaskType.CLASSIFICATION:
            return 'accuracy'
        elif task_type == MLTaskType.REGRESSION:
            return 'r2'
        else:
            return 'accuracy'  # Default

    def _get_feature_importance(self, model: Any, feature_names: List[str]) -> Optional[Dict[str, float]]:
        """Extract feature importance from model."""
        try:
            if hasattr(model, 'feature_importances_'):
                importances = model.feature_importances_
            elif hasattr(model, 'coef_'):
                importances = np.abs(model.coef_).flatten()
            else:
                return None

            # Normalize importances
            if importances.sum() > 0:
                importances = importances / importances.sum()

            return dict(zip(feature_names, importances.astype(float)))

        except Exception as e:
            logger.warning(f"Failed to extract feature importance: {e}")
            return None

    def _generate_interpretation(self, task_type: MLTaskType, model_type: ModelType,
                               performance_metrics: Dict[str, float],
                               feature_importance: Optional[Dict[str, float]]) -> str:
        """Generate interpretation of ML results."""
        interpretation_parts = []

        # Task and model description
        task_desc = {
            MLTaskType.CLASSIFICATION: "classification",
            MLTaskType.REGRESSION: "regression",
            MLTaskType.CLUSTERING: "clustering",
            MLTaskType.DIMENSIONALITY_REDUCTION: "dimensionality reduction"
        }.get(task_type, "machine learning")

        model_desc = model_type.value.replace('_', ' ').title()

        interpretation_parts.append(f"Performed {task_desc} analysis using {model_desc}.")

        # Performance interpretation
        if task_type == MLTaskType.CLASSIFICATION:
            accuracy = performance_metrics.get('accuracy', 0)
            if accuracy > 0.9:
                perf_desc = "excellent"
            elif accuracy > 0.8:
                perf_desc = "good"
            elif accuracy > 0.7:
                perf_desc = "moderate"
            else:
                perf_desc = "poor"

            interpretation_parts.append(f"The model achieved {perf_desc} performance with {accuracy:.1%} accuracy.")

        elif task_type == MLTaskType.REGRESSION:
            r2 = performance_metrics.get('r2_score', 0)
            if r2 > 0.8:
                perf_desc = "excellent"
            elif r2 > 0.6:
                perf_desc = "good"
            elif r2 > 0.4:
                perf_desc = "moderate"
            else:
                perf_desc = "poor"

            interpretation_parts.append(f"The model shows {perf_desc} fit with R² = {r2:.3f}.")

        elif task_type == MLTaskType.CLUSTERING:
            silhouette = performance_metrics.get('silhouette_score', 0)
            n_clusters = int(performance_metrics.get('n_clusters', 0))

            if silhouette > 0.7:
                cluster_quality = "well-separated"
            elif silhouette > 0.5:
                cluster_quality = "reasonably separated"
            else:
                cluster_quality = "overlapping"

            interpretation_parts.append(f"Identified {n_clusters} {cluster_quality} clusters.")

        # Feature importance
        if feature_importance:
            top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:3]
            if top_features:
                feature_names = [f[0] for f in top_features]
                interpretation_parts.append(f"Most important features: {', '.join(feature_names)}.")

        return " ".join(interpretation_parts)

    def _generate_recommendations(self, task_type: MLTaskType,
                                performance_metrics: Dict[str, float],
                                preprocessing_info: Dict[str, Any]) -> List[str]:
        """Generate recommendations for improving the model."""
        recommendations = []

        # Performance-based recommendations
        if task_type == MLTaskType.CLASSIFICATION:
            accuracy = performance_metrics.get('accuracy', 0)
            if accuracy < 0.7:
                recommendations.append("Consider collecting more data or engineering additional features")
                recommendations.append("Try ensemble methods or neural networks for better performance")
            elif accuracy < 0.9:
                recommendations.append("Fine-tune hyperparameters for potential improvement")

        elif task_type == MLTaskType.REGRESSION:
            r2 = performance_metrics.get('r2_score', 0)
            if r2 < 0.5:
                recommendations.append("Consider feature engineering or polynomial features")
                recommendations.append("Check for non-linear relationships in the data")

        # Data quality recommendations
        missing_handled = preprocessing_info.get('missing_values_handled', 0)
        if missing_handled > 0:
            recommendations.append("Consider more sophisticated imputation methods for missing values")

        categorical_encoded = preprocessing_info.get('categorical_columns_encoded', [])
        if categorical_encoded:
            recommendations.append("Consider one-hot encoding for categorical variables with few categories")

        # General recommendations
        recommendations.append("Validate results with additional data before deployment")

        return recommendations

    def format_result_for_agent(self, result: MLResult, agent_identity: str = "analyst") -> Dict[str, Any]:
        """
        Format ML results for agent-specific presentation.

        Args:
            result: MLResult to format
            agent_identity: Agent identity for customized formatting

        Returns:
            Formatted result dictionary
        """
        # Base formatting
        formatted_result = {
            "task_type": result.task_type.value,
            "model_type": result.model_type.value,
            "performance_metrics": result.performance_metrics,
            "interpretation": result.interpretation,
            "recommendations": result.recommendations
        }

        # Add optional fields
        if result.feature_importance:
            formatted_result["feature_importance"] = result.feature_importance

        if result.cross_validation_scores:
            formatted_result["cross_validation_scores"] = {
                "scores": result.cross_validation_scores,
                "mean": float(np.mean(result.cross_validation_scores)),
                "std": float(np.std(result.cross_validation_scores))
            }

        # Agent-specific enhancements
        if agent_identity == "marketer":
            formatted_result["business_insights"] = self._add_business_insights(result)
            formatted_result["actionable_recommendations"] = self._add_marketing_recommendations(result)
        elif agent_identity == "concierge":
            formatted_result["simplified_explanation"] = self._add_simplified_ml_explanation(result)
            formatted_result["next_steps"] = self._add_user_friendly_next_steps(result)
        elif agent_identity == "analyst":
            formatted_result["technical_details"] = {
                "model_parameters": result.model_parameters,
                "preprocessing_info": result.preprocessing_info,
                "predictions_sample": result.predictions[:10].tolist() if len(result.predictions) > 0 else []
            }
            formatted_result["advanced_metrics"] = self._add_advanced_metrics(result)

        return formatted_result

    def _add_business_insights(self, result: MLResult) -> List[str]:
        """Add business insights for marketing agents."""
        insights = []

        if result.task_type == MLTaskType.CLASSIFICATION:
            accuracy = result.performance_metrics.get('accuracy', 0)
            if accuracy > 0.8:
                insights.append("High accuracy model can reliably predict customer segments or outcomes")
                insights.append("Consider implementing automated decision-making based on these predictions")
            else:
                insights.append("Model accuracy suggests manual review of predictions is recommended")

        elif result.task_type == MLTaskType.CLUSTERING:
            n_clusters = result.performance_metrics.get('n_clusters', 0)
            insights.append(f"Identified {int(n_clusters)} distinct customer segments for targeted marketing")
            insights.append("Each segment may require different marketing strategies and messaging")

        if result.feature_importance:
            top_feature = max(result.feature_importance.items(), key=lambda x: x[1])
            insights.append(f"'{top_feature[0]}' is the most influential factor - focus marketing efforts here")

        return insights

    def _add_marketing_recommendations(self, result: MLResult) -> List[str]:
        """Add marketing-specific recommendations."""
        recommendations = []

        if result.task_type == MLTaskType.CLASSIFICATION:
            recommendations.append("Use model predictions to personalize customer communications")
            recommendations.append("A/B test marketing campaigns based on predicted customer segments")

        elif result.task_type == MLTaskType.CLUSTERING:
            recommendations.append("Develop targeted marketing campaigns for each identified cluster")
            recommendations.append("Analyze cluster characteristics to understand customer preferences")

        elif result.task_type == MLTaskType.REGRESSION:
            recommendations.append("Use predictions to optimize pricing and resource allocation")
            recommendations.append("Identify high-value customers for premium marketing efforts")

        return recommendations

    def _add_simplified_ml_explanation(self, result: MLResult) -> str:
        """Add simplified explanation for concierge agents."""
        task_explanations = {
            MLTaskType.CLASSIFICATION: "The analysis categorized your data into different groups and learned to predict which group new data points belong to.",
            MLTaskType.REGRESSION: "The analysis found patterns to predict numerical values based on your data characteristics.",
            MLTaskType.CLUSTERING: "The analysis discovered natural groupings in your data without being told what to look for.",
            MLTaskType.DIMENSIONALITY_REDUCTION: "The analysis simplified your data while keeping the most important information."
        }

        base_explanation = task_explanations.get(result.task_type, "The analysis found patterns in your data.")

        # Add performance context
        if result.task_type == MLTaskType.CLASSIFICATION:
            accuracy = result.performance_metrics.get('accuracy', 0)
            if accuracy > 0.8:
                performance_note = " The predictions are quite reliable."
            elif accuracy > 0.6:
                performance_note = " The predictions are reasonably good but should be verified."
            else:
                performance_note = " The predictions need improvement - more data might help."
        else:
            performance_note = " The results show meaningful patterns in your data."

        return base_explanation + performance_note

    def _add_user_friendly_next_steps(self, result: MLResult) -> List[str]:
        """Add user-friendly next steps for concierge agents."""
        steps = []

        if result.task_type in [MLTaskType.CLASSIFICATION, MLTaskType.REGRESSION]:
            steps.append("Test the model with new data to see how well it performs")
            steps.append("Review the most important features to understand what drives the predictions")

        elif result.task_type == MLTaskType.CLUSTERING:
            steps.append("Examine each cluster to understand what makes them different")
            steps.append("Consider how you might treat each group differently")

        steps.append("Collect more data to improve the analysis over time")
        steps.append("Consider consulting with domain experts to validate the findings")

        return steps

    def _add_advanced_metrics(self, result: MLResult) -> Dict[str, Any]:
        """Add advanced metrics for analyst agents."""
        advanced_metrics = {}

        if result.cross_validation_scores:
            scores = result.cross_validation_scores
            advanced_metrics["cv_statistics"] = {
                "mean": float(np.mean(scores)),
                "std": float(np.std(scores)),
                "min": float(np.min(scores)),
                "max": float(np.max(scores)),
                "confidence_interval_95": [
                    float(np.mean(scores) - 1.96 * np.std(scores) / np.sqrt(len(scores))),
                    float(np.mean(scores) + 1.96 * np.std(scores) / np.sqrt(len(scores)))
                ]
            }

        if result.feature_importance:
            # Feature importance statistics
            importances = list(result.feature_importance.values())
            advanced_metrics["feature_importance_stats"] = {
                "entropy": float(-sum(p * np.log2(p + 1e-10) for p in importances if p > 0)),
                "gini_coefficient": float(self._calculate_gini_coefficient(importances)),
                "top_features_contribution": float(sum(sorted(importances, reverse=True)[:3]))
            }

        # Model complexity indicators
        if hasattr(result.model, 'n_estimators'):
            advanced_metrics["model_complexity"] = {"n_estimators": result.model.n_estimators}
        elif hasattr(result.model, 'n_components'):
            advanced_metrics["model_complexity"] = {"n_components": result.model.n_components}

        return advanced_metrics

    def _calculate_gini_coefficient(self, values: List[float]) -> float:
        """Calculate Gini coefficient for feature importance distribution."""
        if not values or all(v == 0 for v in values):
            return 0.0

        sorted_values = sorted(values)
        n = len(sorted_values)
        cumsum = np.cumsum(sorted_values)

        return (n + 1 - 2 * sum((n + 1 - i) * y for i, y in enumerate(cumsum))) / (n * sum(sorted_values))

    def perform_anomaly_detection(self, data: pd.DataFrame, contamination: float = 0.1) -> MLResult:
        """
        Perform anomaly detection on the dataset.

        Args:
            data: Input DataFrame
            contamination: Expected proportion of outliers

        Returns:
            MLResult with anomaly detection results
        """
        if not self.sklearn_available:
            raise ImportError("Scikit-learn required for anomaly detection")

        # Preprocess data
        X, _, preprocessing_info = self._preprocess_data(data, None, MLTaskType.ANOMALY_DETECTION)

        # Use Isolation Forest for anomaly detection
        try:
            from sklearn.ensemble import IsolationForest
            model = IsolationForest(contamination=contamination, random_state=self.random_state)
            predictions = model.fit_predict(X)

            # Convert to binary (1 for normal, 0 for anomaly)
            anomaly_labels = (predictions == -1).astype(int)

            # Calculate metrics
            n_anomalies = np.sum(anomaly_labels)
            anomaly_rate = n_anomalies / len(predictions)

            performance_metrics = {
                'n_anomalies': float(n_anomalies),
                'anomaly_rate': float(anomaly_rate),
                'contamination_expected': contamination
            }

            interpretation = f"Detected {n_anomalies} anomalies ({anomaly_rate:.1%} of data). "
            if anomaly_rate > contamination * 1.5:
                interpretation += "Higher than expected anomaly rate - data may have quality issues."
            elif anomaly_rate < contamination * 0.5:
                interpretation += "Lower than expected anomaly rate - data appears clean."
            else:
                interpretation += "Anomaly rate is within expected range."

            return MLResult(
                task_type=MLTaskType.ANOMALY_DETECTION,
                model_type=ModelType.ISOLATION_FOREST,
                model=model,
                predictions=anomaly_labels,
                performance_metrics=performance_metrics,
                preprocessing_info=preprocessing_info,
                interpretation=interpretation,
                recommendations=[
                    "Review detected anomalies to understand if they represent errors or interesting patterns",
                    "Consider adjusting contamination parameter based on domain knowledge",
                    "Investigate root causes of anomalies for data quality improvement"
                ]
            )

        except ImportError:
            raise ImportError("Isolation Forest not available in this scikit-learn version")
