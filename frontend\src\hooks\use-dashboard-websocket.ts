import { useEffect, useState, useCallback, useRef } from 'react';
import { 
  dashboardWebSocket, 
  DashboardWebSocketMessage, 
  DashboardUpdateMessage,
  DataSourceUpdateMessage,
  WidgetDataUpdateMessage 
} from '@/services/websocket';
import { useToast } from '@/hooks/use-toast';

export interface WebSocketState {
  isConnected: boolean;
  reconnectAttempts: number;
  lastMessage: DashboardWebSocketMessage | null;
  error: string | null;
}

export interface DashboardWebSocketHookOptions {
  autoConnect?: boolean;
  showConnectionToasts?: boolean;
  dashboardId?: string;
  dataSourceIds?: string[];
}

export const useDashboardWebSocket = (options: DashboardWebSocketHookOptions = {}) => {
  const {
    autoConnect = false, // Changed default to false to prevent connection attempts without token
    showConnectionToasts = true,
    dashboardId,
    dataSourceIds = [],
  } = options;

  const { toast } = useToast();
  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    reconnectAttempts: 0,
    lastMessage: null,
    error: null,
  });

  const messageHandlersRef = useRef<Map<string, (message: DashboardWebSocketMessage) => void>>(new Map());
  const unsubscribeFunctionsRef = useRef<(() => void)[]>([]);

  // Connection state handler
  const handleConnectionState = useCallback((connected: boolean) => {
    setState(prev => ({
      ...prev,
      isConnected: connected,
      reconnectAttempts: dashboardWebSocket.getReconnectAttempts(),
      error: connected ? null : prev.error,
    }));

    if (showConnectionToasts) {
      if (connected) {
        toast({
          title: "Dashboard Connected",
          description: "Real-time updates are now active.",
        });
      } else {
        toast({
          title: "Dashboard Disconnected",
          description: "Real-time updates are temporarily unavailable.",
          variant: "destructive",
        });
      }
    }
  }, [showConnectionToasts, toast]);

  // Generic message handler
  const handleMessage = useCallback((message: DashboardWebSocketMessage) => {
    setState(prev => ({
      ...prev,
      lastMessage: message,
    }));

    // Call specific message handlers
    const handler = messageHandlersRef.current.get(message.type);
    if (handler) {
      handler(message);
    }

    // Call global handler
    const globalHandler = messageHandlersRef.current.get('*');
    if (globalHandler) {
      globalHandler(message);
    }
  }, []);

  // Connect to WebSocket
  const connect = useCallback(async (token?: string) => {
    try {
      await dashboardWebSocket.connect(token);
      setState(prev => ({ ...prev, error: null }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to connect';
      setState(prev => ({ ...prev, error: errorMessage }));
      
      if (showConnectionToasts) {
        toast({
          title: "Connection Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    }
  }, [showConnectionToasts, toast]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    dashboardWebSocket.disconnect();
  }, []);

  // Subscribe to message types
  const subscribe = useCallback((
    messageType: string, 
    handler: (message: DashboardWebSocketMessage) => void
  ) => {
    messageHandlersRef.current.set(messageType, handler);
    const unsubscribe = dashboardWebSocket.subscribe(messageType, handleMessage);
    unsubscribeFunctionsRef.current.push(unsubscribe);
    return unsubscribe;
  }, [handleMessage]);

  // Subscribe to dashboard updates
  const subscribeToDashboard = useCallback((dashboardId: string) => {
    dashboardWebSocket.subscribeToDashboard(dashboardId);
  }, []);

  // Unsubscribe from dashboard updates
  const unsubscribeFromDashboard = useCallback((dashboardId: string) => {
    dashboardWebSocket.unsubscribeFromDashboard(dashboardId);
  }, []);

  // Subscribe to data source updates
  const subscribeToDataSource = useCallback((dataSourceId: string) => {
    dashboardWebSocket.subscribeToDataSource(dataSourceId);
  }, []);

  // Request widget refresh
  const refreshWidget = useCallback((widgetId: string) => {
    dashboardWebSocket.requestWidgetRefresh(widgetId);
  }, []);

  // Setup connection and subscriptions
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Subscribe to connection state changes
    const unsubscribeConnectionState = dashboardWebSocket.subscribeToConnectionState(handleConnectionState);
    unsubscribeFunctionsRef.current.push(unsubscribeConnectionState);

    return () => {
      // Cleanup all subscriptions
      unsubscribeFunctionsRef.current.forEach(unsubscribe => unsubscribe());
      unsubscribeFunctionsRef.current = [];
      messageHandlersRef.current.clear();
    };
  }, [autoConnect, connect, handleConnectionState]);

  // Subscribe to dashboard when dashboardId changes
  useEffect(() => {
    if (state.isConnected && dashboardId) {
      subscribeToDashboard(dashboardId);
      
      return () => {
        unsubscribeFromDashboard(dashboardId);
      };
    }
  }, [state.isConnected, dashboardId, subscribeToDashboard, unsubscribeFromDashboard]);

  // Subscribe to data sources when dataSourceIds change
  useEffect(() => {
    if (state.isConnected && dataSourceIds.length > 0) {
      dataSourceIds.forEach(dataSourceId => {
        subscribeToDataSource(dataSourceId);
      });
    }
  }, [state.isConnected, dataSourceIds, subscribeToDataSource]);

  return {
    // State
    ...state,
    
    // Actions
    connect,
    disconnect,
    subscribe,
    subscribeToDashboard,
    unsubscribeFromDashboard,
    subscribeToDataSource,
    refreshWidget,
  };
};

// Specialized hooks for specific message types
export const useDashboardUpdates = (dashboardId?: string) => {
  const [updates, setUpdates] = useState<DashboardUpdateMessage[]>([]);
  
  const { subscribe, isConnected } = useDashboardWebSocket({
    dashboardId,
    showConnectionToasts: false,
  });

  useEffect(() => {
    const unsubscribe = subscribe('dashboard_update', (message) => {
      const updateMessage = message as DashboardUpdateMessage;
      if (!dashboardId || updateMessage.payload.dashboard_id === dashboardId) {
        setUpdates(prev => [...prev.slice(-9), updateMessage]); // Keep last 10 updates
      }
    });

    return unsubscribe;
  }, [subscribe, dashboardId]);

  const clearUpdates = useCallback(() => {
    setUpdates([]);
  }, []);

  return {
    updates,
    clearUpdates,
    isConnected,
  };
};

export const useDataSourceUpdates = (dataSourceIds: string[] = []) => {
  const [updates, setUpdates] = useState<DataSourceUpdateMessage[]>([]);
  
  const { subscribe, isConnected } = useDashboardWebSocket({
    dataSourceIds,
    showConnectionToasts: false,
  });

  useEffect(() => {
    const unsubscribe = subscribe('data_source_update', (message) => {
      const updateMessage = message as DataSourceUpdateMessage;
      if (dataSourceIds.length === 0 || dataSourceIds.includes(updateMessage.payload.data_source_id)) {
        setUpdates(prev => [...prev.slice(-9), updateMessage]); // Keep last 10 updates
      }
    });

    return unsubscribe;
  }, [subscribe, dataSourceIds]);

  const clearUpdates = useCallback(() => {
    setUpdates([]);
  }, []);

  return {
    updates,
    clearUpdates,
    isConnected,
  };
};

export const useWidgetDataUpdates = (widgetIds: string[] = []) => {
  const [updates, setUpdates] = useState<Map<string, WidgetDataUpdateMessage>>(new Map());
  
  const { subscribe, isConnected, refreshWidget } = useDashboardWebSocket({
    showConnectionToasts: false,
  });

  useEffect(() => {
    const unsubscribe = subscribe('widget_data_update', (message) => {
      const updateMessage = message as WidgetDataUpdateMessage;
      if (widgetIds.length === 0 || widgetIds.includes(updateMessage.payload.widget_id)) {
        setUpdates(prev => new Map(prev).set(updateMessage.payload.widget_id, updateMessage));
      }
    });

    return unsubscribe;
  }, [subscribe, widgetIds]);

  const getWidgetData = useCallback((widgetId: string) => {
    return updates.get(widgetId);
  }, [updates]);

  const clearUpdates = useCallback(() => {
    setUpdates(new Map());
  }, []);

  return {
    updates: Array.from(updates.values()),
    getWidgetData,
    clearUpdates,
    refreshWidget,
    isConnected,
  };
};
