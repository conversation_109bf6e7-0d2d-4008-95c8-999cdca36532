"""
Migration to add provider API keys table.

This migration adds a table for storing provider API keys.
"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic
revision = 'provider_api_keys_001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Upgrade database schema."""
    # Check if provider_api_keys table exists
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)
    tables = inspector.get_table_names()

    # Only create provider_api_keys table if it doesn't exist
    if 'provider_api_keys' not in tables:
        # Create provider_api_keys table
        op.create_table(
            'provider_api_keys',
            sa.Column('id', sa.Integer, primary_key=True, index=True),
            sa.Column('provider_id', sa.String(50), nullable=False, index=True),
            sa.Column('api_key', sa.String(255), nullable=False),
            sa.Column('user_id', sa.<PERSON><PERSON>, sa.<PERSON>ey('users.id'), nullable=False, index=True),
            sa.Column('created_at', sa.DateTime(timezone=True)),
            sa.Column('updated_at', sa.DateTime(timezone=True)),
            sa.UniqueConstraint('provider_id', 'user_id', name='uix_provider_user')
        )


def downgrade():
    """Downgrade database schema."""
    op.drop_table('provider_api_keys')
