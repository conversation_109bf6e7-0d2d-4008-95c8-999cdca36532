from fastapi import APIRouter, Depends, HTTPException, status, WebSocket, WebSocketDisconnect # Added WebSocket
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Set # Added Dict, Set
import logging # Added logging

from ..database import get_db 
from ..services.notifications_service import NotificationsService # Changed
from ..models.notifications import NotificationResponse, NotificationUpdate, NotificationPreferences # Changed
from ..auth import get_current_active_user # Corrected import path
from ..models.auth import User # Changed

router = APIRouter(
    prefix="/notifications",
    tags=["notifications"],
    responses={404: {"description": "Not found"}},
)

def get_notifications_service(db: Session = Depends(get_db)) -> NotificationsService:
    return NotificationsService(db)

@router.get("/", response_model=List[NotificationResponse])
async def read_notifications(
    skip: int = 0,
    limit: int = 20,
    include_read: bool = False,
    current_user: User = Depends(get_current_active_user),
    service: NotificationsService = Depends(get_notifications_service),
):
    """
    Retrieve notifications for the current user.
    """
    notifications = await service.get_notifications_for_user(
        user_id=current_user.id, skip=skip, limit=limit, include_read=include_read
    )
    return notifications

@router.get("/unread-count", response_model=int)
async def get_unread_notifications_count(
    current_user: User = Depends(get_current_active_user),
    service: NotificationsService = Depends(get_notifications_service),
):
    """
    Get the count of unread notifications for the current user.
    """
    return await service.get_unread_notification_count(user_id=current_user.id)

@router.patch("/{notification_id}/read", response_model=NotificationResponse)
async def mark_notification_read(
    notification_id: str,
    current_user: User = Depends(get_current_active_user),
    service: NotificationsService = Depends(get_notifications_service),
):
    """
    Mark a specific notification as read.
    """
    notification = await service.mark_notification_as_read(
        notification_id=notification_id, user_id=current_user.id
    )
    if not notification:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Notification not found or not owned by user")
    return notification

@router.post("/mark-all-read", status_code=status.HTTP_200_OK)
async def mark_all_user_notifications_read(
    current_user: User = Depends(get_current_active_user),
    service: NotificationsService = Depends(get_notifications_service),
):
    """
    Mark all unread notifications for the current user as read.
    """
    updated_count = await service.mark_all_notifications_as_read(user_id=current_user.id)
    return {"message": f"{updated_count} notifications marked as read."}

# --- Notification Preferences Endpoints (Optional, could be in a user profile router) ---

@router.get("/preferences", response_model=NotificationPreferences)
async def get_notification_preferences(
    current_user: User = Depends(get_current_active_user),
    service: NotificationsService = Depends(get_notifications_service),
):
    """
    Get current user's notification preferences.
    """
    preferences = await service.get_user_notification_preferences(user_id=current_user.id)
    if not preferences: # Should always return default if not found, but as a safeguard
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Preferences not found")
    return preferences

@router.put("/preferences", response_model=NotificationPreferences)
async def update_notification_preferences(
    preferences_data: NotificationPreferences, # Pydantic model for request body
    current_user: User = Depends(get_current_active_user),
    service: NotificationsService = Depends(get_notifications_service),
):
    """
    Update current user's notification preferences.
    """
    if preferences_data.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Cannot update preferences for another user")
    
    updated_preferences = await service.update_user_notification_preferences(
        user_id=current_user.id, preferences_data=preferences_data
    )
    return updated_preferences

# Import the manager instance from the new utility file
from ..utils.connection_manager import manager # Using manager from new util file
from fastapi import Query # Added Query

@router.websocket("/ws")
async def websocket_notification_stream(
    websocket: WebSocket,
    # How to get user_id for WebSocket? Typically via token in query param or subprotocol
    # For now, let's assume a query parameter `token` which is then validated.
    # Or, if using cookies for auth, FastAPI might pick it up.
    # Simplified: use a query param `user_id` for now, but this is NOT secure for production.
    user_id_param: Optional[str] = Query(None, alias="user_id") # Example: /ws?user_id=123
    # A better way would be to use a token:
    # token: Optional[str] = Query(None),
    # db: Session = Depends(get_db) # if needed for token validation
):
    # user = await get_current_active_user_from_token(token, db) # Hypothetical auth function
    # if not user:
    #     await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
    #     return
    # user_id = str(user.id)

    if not user_id_param: # TEMPORARY: Replace with proper token auth
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="user_id query parameter missing")
        return
    user_id = user_id_param

    await manager.connect(websocket, user_id)
    try:
        while True:
            # Keep connection alive. Client might send pings.
            data = await websocket.receive_text() 
            # Example: if client sends "ping", server can send "pong"
            if data.lower() == "ping":
                await websocket.send_text("pong")
            logger.debug(f"Received from user {user_id} on notification stream: {data}")
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
        logger.info(f"User {user_id} WebSocket disconnected.")
    except Exception as e:
        manager.disconnect(websocket, user_id) # Ensure disconnect on other errors too
        logger.error(f"Error in notification WebSocket for user {user_id}: {e}", exc_info=True)
        # Consider closing with an error code if not already closed by disconnect
        # await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
