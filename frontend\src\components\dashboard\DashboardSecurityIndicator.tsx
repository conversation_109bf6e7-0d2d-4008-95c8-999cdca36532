import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Shield, 
  ShieldAlert, 
  ShieldCheck, 
  AlertTriangle, 
  CheckCircle2,
  Clock,
  Eye,
  EyeOff,
  RefreshCw
} from 'lucide-react';
import { useDashboardSecurity } from '@/contexts/DashboardSecurityContext';

interface DashboardSecurityIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

export const DashboardSecurityIndicator: React.FC<DashboardSecurityIndicatorProps> = ({
  className = '',
  showDetails = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const {
    securityScore,
    alerts,
    isSessionValid,
    sessionExpiry,
    policy,
    permissions,
    acknowledgeAlert,
    clearAlerts,
    extendSession,
    refreshCSRFToken,
    getSecurityReport,
  } = useDashboardSecurity();

  const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged);
  const criticalAlerts = alerts.filter(alert => alert.type === 'error' && !alert.acknowledged);

  const getSecurityLevel = () => {
    if (securityScore >= 90) return { level: 'high', color: 'green', icon: ShieldCheck };
    if (securityScore >= 70) return { level: 'medium', color: 'yellow', icon: Shield };
    return { level: 'low', color: 'red', icon: ShieldAlert };
  };

  const { level, color, icon: SecurityIcon } = getSecurityLevel();

  const formatTimeRemaining = (expiry: Date | null) => {
    if (!expiry) return 'Unknown';
    
    const now = new Date();
    const remaining = expiry.getTime() - now.getTime();
    
    if (remaining <= 0) return 'Expired';
    
    const hours = Math.floor(remaining / (1000 * 60 * 60));
    const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const SecurityContent = () => {
    const report = getSecurityReport();
    
    return (
      <div className="space-y-4">
        {/* Security Score */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <SecurityIcon className={`h-5 w-5 text-${color}-600`} />
                <span>Security Score</span>
              </div>
              <Badge variant={level === 'high' ? 'default' : level === 'medium' ? 'secondary' : 'destructive'}>
                {securityScore}/100
              </Badge>
            </CardTitle>
            <CardDescription>
              Overall security status of your dashboard session
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span>Session Status:</span>
                <div className="flex items-center space-x-2">
                  {isSessionValid ? (
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  )}
                  <span className={isSessionValid ? 'text-green-600' : 'text-red-600'}>
                    {isSessionValid ? 'Active' : 'Expired'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span>Time Remaining:</span>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{formatTimeRemaining(sessionExpiry)}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span>Active Alerts:</span>
                <Badge variant={unacknowledgedAlerts.length > 0 ? 'destructive' : 'secondary'}>
                  {unacknowledgedAlerts.length}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security Alerts */}
        {alerts.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between">
                <span>Security Alerts</span>
                <Button variant="outline" size="sm" onClick={clearAlerts}>
                  Clear All
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {alerts.slice(-5).map((alert) => (
                  <div
                    key={alert.id}
                    className={`p-3 rounded-lg border ${
                      alert.type === 'error' ? 'border-red-200 bg-red-50' :
                      alert.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :
                      'border-blue-200 bg-blue-50'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Badge variant={alert.type === 'error' ? 'destructive' : 'secondary'}>
                            {alert.type.toUpperCase()}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {alert.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        <div className="font-medium text-sm mt-1">{alert.title}</div>
                        <div className="text-xs text-muted-foreground mt-1">{alert.message}</div>
                      </div>
                      {!alert.acknowledged && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => acknowledgeAlert(alert.id)}
                          className="ml-2"
                        >
                          <CheckCircle2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Security Policy Status */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Security Policy</CardTitle>
            <CardDescription>Current security settings and policies</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="flex items-center justify-between">
                <span>Input Validation:</span>
                {policy.enforceInputValidation ? (
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <span>CSRF Protection:</span>
                {policy.requireCSRFTokens ? (
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Audit Logging:</span>
                {policy.enableAuditLogging ? (
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Re-auth Required:</span>
                {policy.requireReauthForSensitiveOps ? (
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security Actions */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Security Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-2">
              <Button
                variant="outline"
                onClick={extendSession}
                disabled={!isSessionValid}
                className="w-full justify-start"
              >
                <Clock className="h-4 w-4 mr-2" />
                Extend Session
              </Button>
              
              <Button
                variant="outline"
                onClick={refreshCSRFToken}
                className="w-full justify-start"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Security Token
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Security Report */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Security Report</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm space-y-2">
              <div className="flex justify-between">
                <span>Total Validations:</span>
                <span>{report.totalValidations}</span>
              </div>
              <div className="flex justify-between">
                <span>Threats Detected:</span>
                <span className={report.threatsDetected > 0 ? 'text-red-600' : 'text-green-600'}>
                  {report.threatsDetected}
                </span>
              </div>
              <div className="flex justify-between">
                <span>High Risk Threats:</span>
                <span className={report.highRiskThreats > 0 ? 'text-red-600' : 'text-green-600'}>
                  {report.highRiskThreats}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  if (showDetails) {
    return (
      <div className={className}>
        <SecurityContent />
      </div>
    );
  }

  return (
    <TooltipProvider>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" className={`relative ${className}`}>
                <SecurityIcon className={`h-4 w-4 mr-2 text-${color}-600`} />
                Security
                {criticalAlerts.length > 0 && (
                  <Badge variant="destructive" className="ml-2 h-4 w-4 p-0 text-xs">
                    {criticalAlerts.length}
                  </Badge>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-center">
                <p>Security Score: {securityScore}/100</p>
                <p className="text-xs">
                  {unacknowledgedAlerts.length} active alert{unacknowledgedAlerts.length !== 1 ? 's' : ''}
                </p>
              </div>
            </TooltipContent>
          </Tooltip>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <SecurityIcon className={`h-5 w-5 text-${color}-600`} />
              <span>Dashboard Security</span>
            </DialogTitle>
            <DialogDescription>
              Monitor and manage your dashboard security settings and alerts.
            </DialogDescription>
          </DialogHeader>
          <SecurityContent />
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
};
