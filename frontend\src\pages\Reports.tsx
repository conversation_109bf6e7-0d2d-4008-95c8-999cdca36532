
import { useState } from "react";
import { motion } from "framer-motion";
import { DashboardLayout } from "@/components/DashboardLayout";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ReportGenerator } from "@/components/reports/ReportGenerator";
import { ScheduledReports } from "@/components/reports/ScheduledReports";
import { SavedReports } from "@/components/reports/SavedReports";

const Reports = () => {
  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Reports & Visualizations</h1>
        </div>

        <Tabs defaultValue="generator" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="generator">Report Generator</TabsTrigger>
            <TabsTrigger value="scheduled">Scheduled Reports</TabsTrigger>
            <TabsTrigger value="saved">Saved Reports</TabsTrigger>
          </TabsList>
          
          <TabsContent value="generator" className="space-y-4">
            <ReportGenerator />
          </TabsContent>
          
          <TabsContent value="scheduled" className="space-y-4">
            <ScheduledReports />
          </TabsContent>
          
          <TabsContent value="saved" className="space-y-4">
            <SavedReports />
          </TabsContent>
        </Tabs>
      </motion.div>
    </DashboardLayout>
  );
};

export default Reports;
