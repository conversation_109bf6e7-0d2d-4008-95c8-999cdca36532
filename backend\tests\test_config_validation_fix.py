#!/usr/bin/env python3
"""
Test script to verify the configuration validation fix.

This script tests that the Pydantic validation errors have been resolved
and the configuration system can load properly.
"""

import os
import sys
import logging
from pathlib import Path

# Add backend root to path
backend_root = Path(__file__).parent
sys.path.insert(0, str(backend_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_configuration_loading():
    """Test that configuration loads without validation errors."""
    logger.info("Testing configuration loading...")
    
    try:
        from app.settings.manager import get_config
        
        # Load the configuration
        config = get_config()
        
        logger.info("✅ Configuration loaded successfully!")
        logger.info(f"App name: {config.name}")
        logger.info(f"Environment: {config.environment.environment}")
        logger.info(f"Database URL scheme: {config.database.url.split('://')[0] if config.database.url else 'Not set'}")
        logger.info(f"LLM default provider: {config.llm.default_provider}")
        
        # Test computed fields work
        logger.info(f"Is development: {config.is_development}")
        logger.info(f"Is production: {config.is_production}")
        
        # Test config summary
        summary = config.config_summary
        logger.info(f"Config summary keys: {list(summary.keys())}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration loading failed: {e}")
        return False


def test_yaml_configuration_loading():
    """Test loading configuration from YAML files."""
    logger.info("Testing YAML configuration loading...")
    
    try:
        from app.settings.manager import ConfigurationManager
        import tempfile
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test YAML config
            config_data = """
name: "Test YAML App"
version: "1.0.0-yaml"
debug: true

database:
  url: "postgresql://yaml:test@localhost:5432/yaml"
  echo: true
  pool_size: 8

security:
  jwt_secret_key: "yaml-test-secret-key-for-validation-testing-purposes"
  access_token_expire_minutes: 45

llm:
  default_provider: "groq"
  default_temperature: 0.8
"""
            
            config_file = Path(temp_dir) / "development.yaml"
            with open(config_file, 'w') as f:
                f.write(config_data)
            
            # Test loading with configuration manager
            manager = ConfigurationManager(config_dir=temp_dir)
            config = manager.load_config()
            
            logger.info("✅ YAML configuration loaded successfully!")
            logger.info(f"App name from YAML: {config.name}")
            logger.info(f"Database pool size from YAML: {config.database.pool_size}")
            logger.info(f"LLM temperature from YAML: {config.llm.default_temperature}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ YAML configuration loading failed: {e}")
        return False


def test_environment_override():
    """Test that environment variables override YAML configuration."""
    logger.info("Testing environment variable override...")
    
    try:
        from app.settings.app import AppConfig
        
        # Set test environment variables
        test_env = {
            "APP_NAME": "Environment Override Test",
            "DATABASE_URL": "postgresql://env:test@localhost:5432/env",
            "JWT_SECRET_KEY": "environment-override-secret-key-for-testing",
            "ACCESS_TOKEN_EXPIRE_MINUTES": "90"
        }
        
        # Store original values
        original_env = {}
        for key, value in test_env.items():
            original_env[key] = os.environ.get(key)
            os.environ[key] = value
        
        try:
            config = AppConfig.from_env()
            
            logger.info("✅ Environment override test successful!")
            logger.info(f"App name from env: {config.name}")
            logger.info(f"Database URL from env: {config.database.url}")
            logger.info(f"Token expire from env: {config.security.access_token_expire_minutes}")
            
            return True
            
        finally:
            # Restore original environment
            for key, value in original_env.items():
                if value is None:
                    os.environ.pop(key, None)
                else:
                    os.environ[key] = value
                    
    except Exception as e:
        logger.error(f"❌ Environment override test failed: {e}")
        return False


def test_computed_fields():
    """Test that computed fields work correctly."""
    logger.info("Testing computed fields...")
    
    try:
        from app.settings.app import AppConfig
        from app.settings.database import DatabaseConfig
        from app.settings.security import SecurityConfig
        
        # Create test configuration
        config = AppConfig(
            name="Computed Fields Test",
            database=DatabaseConfig(url="postgresql://test:test@localhost:5432/test"),
            security=SecurityConfig(
                jwt_secret_key="computed-fields-test-secret-key-for-validation",
                max_upload_size=20971520  # 20MB
            )
        )
        
        # Test computed fields
        assert hasattr(config, 'is_development')
        assert hasattr(config, 'is_production')
        assert hasattr(config, 'config_summary')
        
        # Test security computed fields
        assert hasattr(config.security, 'max_upload_size_mb')
        assert config.security.max_upload_size_mb == 20.0
        
        assert hasattr(config.security, 'password_policy_description')
        assert "Password must contain" in config.security.password_policy_description
        
        # Test database computed fields
        assert hasattr(config.database, 'connect_args')
        assert hasattr(config.database, 'engine_kwargs')
        
        logger.info("✅ Computed fields test successful!")
        logger.info(f"Upload size MB: {config.security.max_upload_size_mb}")
        logger.info(f"Password policy: {config.security.password_policy_description}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Computed fields test failed: {e}")
        return False


def main():
    """Run all configuration validation fix tests."""
    logger.info("🔧 Configuration Validation Fix Test")
    logger.info("=" * 50)
    
    tests = [
        ("Configuration Loading", test_configuration_loading),
        ("YAML Configuration Loading", test_yaml_configuration_loading),
        ("Environment Override", test_environment_override),
        ("Computed Fields", test_computed_fields)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    logger.info("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All configuration validation tests passed!")
        logger.info("The Pydantic validation errors have been resolved.")
        return 0
    else:
        logger.error(f"💥 {total - passed} tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
