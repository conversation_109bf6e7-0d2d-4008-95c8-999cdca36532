"""
AI Field Mapping Service for intelligent business profile auto-fill.

This module uses LLM to intelligently map extracted content from documents
and websites to business profile form fields with confidence scoring.
"""

import logging
import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Import existing AI services
from agents.utils.memory_service import MemoryService
from agents.utils.model_providers.registry import ModelProviderRegistry
from agents.utils.production_provider_manager import ProductionProviderManager

logger = logging.getLogger(__name__)

class FieldMappingConfidence(Enum):
    """Confidence levels for field mapping."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    NONE = "none"

@dataclass
class FieldMapping:
    """Container for field mapping result."""
    field_name: str
    mapped_value: str
    confidence: FieldMappingConfidence
    source: str
    reasoning: str
    alternatives: List[str]

@dataclass
class AutoFillSuggestion:
    """Container for auto-fill suggestion."""
    field_mappings: Dict[str, FieldMapping]
    overall_confidence: float
    source_summary: str
    processing_notes: List[str]

class AIFieldMappingService:
    """Service for AI-powered field mapping and auto-fill suggestions."""
    
    def __init__(self):
        self.memory_service = MemoryService()
        self.provider_manager = ProductionProviderManager()
        self.analysis_config = None
        
        # Business profile field definitions
        self.field_definitions = {
            'name': {
                'description': 'The official business or company name',
                'examples': ['Acme Corporation', 'Smith & Associates LLC', 'TechStart Inc'],
                'keywords': ['company', 'business', 'corporation', 'inc', 'llc', 'ltd', 'name']
            },
            'description': {
                'description': 'A brief overview of what the business does',
                'examples': ['We provide innovative software solutions', 'Leading provider of consulting services'],
                'keywords': ['about', 'overview', 'description', 'mission', 'what we do', 'services']
            },
            'industry': {
                'description': 'The industry or sector the business operates in',
                'examples': ['Technology', 'Healthcare', 'Finance', 'Retail', 'Manufacturing'],
                'keywords': ['industry', 'sector', 'market', 'field', 'domain']
            },
            'business_type': {
                'description': 'Type of business model',
                'options': ['B2B', 'B2C', 'B2B2C', 'marketplace', 'saas', 'ecommerce'],
                'keywords': ['b2b', 'b2c', 'business to business', 'consumer', 'marketplace', 'saas', 'ecommerce']
            },
            'business_size': {
                'description': 'Size of the business',
                'options': ['startup', 'small', 'medium', 'large', 'enterprise'],
                'keywords': ['startup', 'small business', 'medium', 'large', 'enterprise', 'employees', 'team size']
            },
            'target_audience': {
                'description': 'Primary target audience or customer base',
                'examples': ['Small businesses', 'Enterprise clients', 'Individual consumers'],
                'keywords': ['target', 'audience', 'customers', 'clients', 'market']
            },
            'products_services': {
                'description': 'Main products or services offered',
                'examples': ['Software development', 'Consulting services', 'E-commerce platform'],
                'keywords': ['products', 'services', 'offerings', 'solutions', 'provides']
            },
            'marketing_goals': {
                'description': 'Marketing objectives and goals',
                'examples': ['Increase brand awareness', 'Generate leads', 'Drive sales'],
                'keywords': ['marketing', 'goals', 'objectives', 'growth', 'awareness', 'leads']
            },
            'competitive_landscape': {
                'description': 'Information about competitors and market position',
                'examples': ['Competing with established players', 'Market leader in niche'],
                'keywords': ['competitors', 'competition', 'market position', 'competitive advantage']
            },
            'budget_indicators': {
                'description': 'Budget range or financial constraints',
                'examples': ['Small budget', 'Mid-range investment', 'Enterprise level'],
                'keywords': ['budget', 'investment', 'financial', 'cost', 'pricing']
            },
            'geographic_focus': {
                'description': 'Geographic markets or regions served',
                'examples': ['Local', 'National', 'Global', 'North America'],
                'keywords': ['location', 'geographic', 'region', 'market', 'local', 'national', 'global']
            },
            'business_stage': {
                'description': 'Current stage of business development',
                'options': ['idea', 'startup', 'growth', 'mature', 'enterprise'],
                'keywords': ['stage', 'phase', 'development', 'startup', 'growth', 'mature']
            },

            # Marketing-specific fields (consolidated from marketing form)
            'budget': {
                'description': 'Budget constraints, allocations, or financial considerations for marketing',
                'examples': ['$10,000 monthly marketing budget', 'Limited budget for digital advertising', 'Enterprise-level marketing investment'],
                'keywords': ['budget', 'marketing budget', 'advertising spend', 'financial constraints', 'investment', 'allocation']
            },
            'timeline': {
                'description': 'Timeline constraints, deadlines, or scheduling requirements',
                'examples': ['Launch campaign by Q2', '6-month marketing timeline', 'Urgent deadline next month'],
                'keywords': ['timeline', 'deadline', 'schedule', 'timeframe', 'launch date', 'urgency', 'timing']
            },
            'platforms': {
                'description': 'Specific platforms, channels, or mediums for content distribution',
                'examples': ['Facebook, Instagram, LinkedIn', 'Email marketing and SEO', 'Traditional media and digital channels'],
                'keywords': ['platforms', 'channels', 'social media', 'facebook', 'instagram', 'linkedin', 'email', 'seo', 'digital', 'traditional media']
            }
        }

    async def _load_concierge_model_config(self, user_settings: Optional[Dict[str, Any]] = None):
        """Load the same model configuration used by the concierge agent."""
        try:
            # Import the centralized configuration utilities (same as concierge)
            from agents.utils.model_init import load_agent_database_config, merge_agent_config
            from agents.concierge_agent.config_loader import get_config_loader

            # Load database configuration for concierge agent
            db_config = await load_agent_database_config("concierge-agent")

            # Get the same config loader used by concierge agent
            config_loader = get_config_loader()
            yaml_config = {}  # We'll use defaults if no YAML config

            # Merge YAML config with database config (database takes priority)
            merged_config = merge_agent_config(yaml_config, db_config, "concierge-agent")

            # Check if user has specific concierge agent settings (same logic as concierge)
            provider = "groq"
            model = "llama-3.1-8b-instant"

            if user_settings:
                user_provider = user_settings.get("concierge_agent_provider", "").lower()
                user_model = user_settings.get("concierge_agent_model", "")

                # Use user settings if both provider and model are specified
                if user_provider and user_model:
                    provider = user_provider
                    model = user_model
                    logger.info(f"Using user-configured concierge agent settings: {provider}/{model}")
                else:
                    # Fall back to merged config
                    provider = merged_config.get("provider", provider)
                    model = merged_config.get("model", model)
            else:
                # Use merged config
                provider = merged_config.get("provider", provider)
                model = merged_config.get("model", model)

            # Create analysis config with final settings (same as concierge)
            analysis_config_data = {
                "provider": provider,
                "model": model,
                "temperature": 0.1,  # Keep low for analysis tasks
                "max_tokens": None,
                "timeout": 30,
                "retry_attempts": 3
            }

            # Load the analysis configuration with merged values
            self.analysis_config = config_loader.load_analysis_config(analysis_config_data)

            logger.info(f"Inherited model configuration from concierge agent: provider={analysis_config_data['provider']}, model={analysis_config_data['model']}")
            return self.analysis_config

        except Exception as e:
            logger.error(f"Error loading concierge model configuration: {e}")
            # Fall back to default configuration
            from agents.concierge_agent.config_loader import get_config_loader
            config_loader = get_config_loader()
            self.analysis_config = config_loader.load_analysis_config()
            logger.info("Using default model configuration as fallback")
            return self.analysis_config

    async def map_extracted_data_to_fields(
        self,
        extracted_data: Dict[str, Any],
        source_type: str = "document",
        user_settings: Optional[Dict[str, Any]] = None
    ) -> AutoFillSuggestion:
        """
        Map extracted data to business profile fields using AI.

        Args:
            extracted_data: Data extracted from document or website
            source_type: Type of source ("document" or "website")
            user_settings: Optional user settings for model configuration

        Returns:
            AutoFillSuggestion with mapped fields and confidence scores
        """
        try:
            logger.info(f"Mapping extracted data from {source_type} to business profile fields")

            # Prepare the mapping prompt
            mapping_prompt = self._create_mapping_prompt(extracted_data, source_type)

            # Get AI model for processing (inherit from concierge agent)
            model = await self._get_ai_model(user_settings)

            # Process with AI
            ai_response = await self._process_with_ai(model, mapping_prompt)

            # Parse AI response
            field_mappings = self._parse_ai_response(ai_response, extracted_data, source_type)

            # Calculate overall confidence
            overall_confidence = self._calculate_overall_confidence(field_mappings)

            # Create processing notes
            processing_notes = self._create_processing_notes(extracted_data, field_mappings)

            return AutoFillSuggestion(
                field_mappings=field_mappings,
                overall_confidence=overall_confidence,
                source_summary=self._create_source_summary(extracted_data, source_type),
                processing_notes=processing_notes
            )

        except Exception as e:
            logger.error(f"Error mapping extracted data: {e}")
            raise

    def _create_mapping_prompt(self, extracted_data: Dict[str, Any], source_type: str) -> str:
        """Create prompt for AI field mapping."""
        
        prompt = f"""
You are an AI assistant specialized in mapping business information to structured fields.

TASK: Analyze the following {source_type} data and map it to business profile fields.

EXTRACTED DATA:
{json.dumps(extracted_data, indent=2)}

BUSINESS PROFILE FIELDS TO MAP:
"""
        
        for field_name, field_info in self.field_definitions.items():
            prompt += f"\n{field_name}:"
            prompt += f"\n  Description: {field_info['description']}"
            
            if 'options' in field_info:
                prompt += f"\n  Options: {', '.join(field_info['options'])}"
            elif 'examples' in field_info:
                prompt += f"\n  Examples: {', '.join(field_info['examples'])}"
            
            prompt += f"\n  Keywords: {', '.join(field_info['keywords'])}"
            prompt += "\n"

        prompt += """
INSTRUCTIONS:
1. For each field, analyze the extracted data to find relevant information
2. Provide the best mapping for each field, or "null" if no relevant data found
3. Assign confidence levels: HIGH, MEDIUM, LOW, or NONE
4. Provide reasoning for each mapping
5. Suggest alternatives if applicable

RESPONSE FORMAT (JSON):
{
  "mappings": {
    "field_name": {
      "value": "mapped_value_or_null",
      "confidence": "HIGH|MEDIUM|LOW|NONE",
      "reasoning": "explanation of why this mapping was chosen",
      "alternatives": ["alternative1", "alternative2"],
      "source_reference": "which part of extracted data was used"
    }
  },
  "analysis_notes": ["note1", "note2"]
}

Respond with valid JSON only.
"""
        
        return prompt

    async def _get_ai_model(self, user_settings: Optional[Dict[str, Any]] = None):
        """Get AI model for processing using the exact same configuration as concierge agent."""
        try:
            # Load the concierge agent's model configuration
            if self.analysis_config is None:
                await self._load_concierge_model_config(user_settings)

            # Use the same model loading approach as concierge agent
            from agents.utils.model_providers.utils import get_model

            logger.info(f"Using inherited concierge model configuration: provider={self.analysis_config.provider}, model={self.analysis_config.model}")

            return await get_model(
                provider_id=self.analysis_config.provider,
                model_id=self.analysis_config.model,
                config={
                    "temperature": self.analysis_config.temperature,
                    "max_tokens": 2000,  # Suitable for field mapping tasks
                    "top_p": 0.9,
                    "frequency_penalty": 0.1
                }
            )

        except Exception as e:
            logger.error(f"Error getting AI model: {e}")
            raise

    async def _process_with_ai(self, model, prompt: str) -> str:
        """Process prompt with AI model."""
        try:
            # Create messages for the model
            messages = [
                {"role": "system", "content": "You are an expert business analyst specializing in data extraction and field mapping. Always respond with valid JSON."},
                {"role": "user", "content": prompt}
            ]
            
            # Process with model
            response = await model.ainvoke(messages)
            
            # Extract content from response
            if hasattr(response, 'content'):
                return response.content
            elif isinstance(response, str):
                return response
            else:
                return str(response)
                
        except Exception as e:
            logger.error(f"Error processing with AI: {e}")
            raise

    def _parse_ai_response(
        self, 
        ai_response: str, 
        extracted_data: Dict[str, Any], 
        source_type: str
    ) -> Dict[str, FieldMapping]:
        """Parse AI response into field mappings."""
        field_mappings = {}
        
        try:
            # Check if response is empty or None
            if not ai_response or not ai_response.strip():
                logger.warning("AI response is empty, using fallback mapping")
                field_mappings = self._fallback_mapping(extracted_data, source_type)
                return field_mappings

            # Clean the response - remove markdown formatting if present
            cleaned_response = ai_response.strip()
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response.replace('```json', '').replace('```', '').strip()
            elif cleaned_response.startswith('```'):
                cleaned_response = cleaned_response.replace('```', '').strip()

            # Try to parse JSON response
            response_data = json.loads(cleaned_response)
            mappings = response_data.get('mappings', {})

            for field_name, mapping_data in mappings.items():
                if field_name in self.field_definitions:
                    # Parse confidence level
                    confidence_str = mapping_data.get('confidence', 'NONE').upper()
                    try:
                        confidence = FieldMappingConfidence(confidence_str.lower())
                    except ValueError:
                        confidence = FieldMappingConfidence.NONE

                    # Create field mapping
                    field_mappings[field_name] = FieldMapping(
                        field_name=field_name,
                        mapped_value=mapping_data.get('value', ''),
                        confidence=confidence,
                        source=f"{source_type}:{mapping_data.get('source_reference', 'unknown')}",
                        reasoning=mapping_data.get('reasoning', ''),
                        alternatives=mapping_data.get('alternatives', [])
                    )

        except json.JSONDecodeError as e:
            logger.error(f"Error parsing AI response JSON: {e}")
            logger.error(f"AI response content: {repr(ai_response[:500])}")  # Log first 500 chars
            # Fallback to basic mapping
            field_mappings = self._fallback_mapping(extracted_data, source_type)
        
        return field_mappings

    def _fallback_mapping(self, extracted_data: Dict[str, Any], source_type: str) -> Dict[str, FieldMapping]:
        """Fallback mapping when AI parsing fails."""
        field_mappings = {}
        
        # Basic keyword-based mapping
        content = str(extracted_data)
        
        for field_name, field_info in self.field_definitions.items():
            keywords = field_info.get('keywords', [])
            
            # Simple keyword matching
            found_value = ""
            confidence = FieldMappingConfidence.NONE
            
            for keyword in keywords:
                if keyword.lower() in content.lower():
                    # Extract surrounding context
                    import re
                    pattern = rf'.{{0,50}}{re.escape(keyword)}.{{0,50}}'
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        found_value = matches[0].strip()
                        confidence = FieldMappingConfidence.LOW
                        break
            
            field_mappings[field_name] = FieldMapping(
                field_name=field_name,
                mapped_value=found_value,
                confidence=confidence,
                source=f"{source_type}:fallback",
                reasoning="Fallback keyword matching",
                alternatives=[]
            )
        
        return field_mappings

    def _calculate_overall_confidence(self, field_mappings: Dict[str, FieldMapping]) -> float:
        """Calculate overall confidence score."""
        if not field_mappings:
            return 0.0
        
        confidence_scores = {
            FieldMappingConfidence.HIGH: 1.0,
            FieldMappingConfidence.MEDIUM: 0.7,
            FieldMappingConfidence.LOW: 0.4,
            FieldMappingConfidence.NONE: 0.0
        }
        
        total_score = 0.0
        mapped_fields = 0
        
        for mapping in field_mappings.values():
            if mapping.mapped_value and mapping.mapped_value.lower() != 'null':
                total_score += confidence_scores.get(mapping.confidence, 0.0)
                mapped_fields += 1
        
        if mapped_fields == 0:
            return 0.0
        
        return total_score / mapped_fields

    def _create_processing_notes(
        self, 
        extracted_data: Dict[str, Any], 
        field_mappings: Dict[str, FieldMapping]
    ) -> List[str]:
        """Create processing notes for the user."""
        notes = []
        
        # Count successful mappings
        successful_mappings = sum(
            1 for mapping in field_mappings.values() 
            if mapping.mapped_value and mapping.mapped_value.lower() != 'null'
        )
        
        notes.append(f"Successfully mapped {successful_mappings} out of {len(field_mappings)} fields")
        
        # Note high confidence mappings
        high_confidence = [
            mapping.field_name for mapping in field_mappings.values()
            if mapping.confidence == FieldMappingConfidence.HIGH
        ]
        
        if high_confidence:
            notes.append(f"High confidence mappings: {', '.join(high_confidence)}")
        
        # Note fields that need review
        low_confidence = [
            mapping.field_name for mapping in field_mappings.values()
            if mapping.confidence == FieldMappingConfidence.LOW and mapping.mapped_value
        ]
        
        if low_confidence:
            notes.append(f"Fields requiring review: {', '.join(low_confidence)}")
        
        # Note unmapped fields
        unmapped = [
            mapping.field_name for mapping in field_mappings.values()
            if not mapping.mapped_value or mapping.mapped_value.lower() == 'null'
        ]
        
        if unmapped:
            notes.append(f"Unmapped fields: {', '.join(unmapped)}")
        
        return notes

    def _create_source_summary(self, extracted_data: Dict[str, Any], source_type: str) -> str:
        """Create a summary of the data source."""
        if source_type == "document":
            filename = extracted_data.get('source_file', 'Unknown file')
            file_type = extracted_data.get('file_type', 'Unknown type')
            return f"Document: {filename} ({file_type})"
        elif source_type == "website":
            url = extracted_data.get('source_url', 'Unknown URL')
            title = extracted_data.get('business_name', 'Unknown title')
            return f"Website: {title} ({url})"
        else:
            return f"Source: {source_type}"

    async def validate_field_mapping(self, field_name: str, value: str) -> Tuple[bool, str]:
        """
        Validate a field mapping against field definitions.
        
        Args:
            field_name: Name of the field
            value: Proposed value
            
        Returns:
            Tuple of (is_valid, validation_message)
        """
        if field_name not in self.field_definitions:
            return False, f"Unknown field: {field_name}"
        
        field_def = self.field_definitions[field_name]
        
        # Check if field has predefined options
        if 'options' in field_def:
            if value.lower() not in [opt.lower() for opt in field_def['options']]:
                return False, f"Value must be one of: {', '.join(field_def['options'])}"
        
        # Basic validation rules
        if field_name == 'name' and len(value) > 255:
            return False, "Business name must be less than 255 characters"
        
        if field_name == 'description' and len(value) > 2000:
            return False, "Description must be less than 2000 characters"
        
        return True, "Valid"

    async def get_field_suggestions(self, field_name: str, partial_value: str) -> List[str]:
        """
        Get suggestions for a field based on partial input.

        Args:
            field_name: Name of the field
            partial_value: Partial value entered by user

        Returns:
            List of suggestions
        """
        if field_name not in self.field_definitions:
            return []

        field_def = self.field_definitions[field_name]

        # For fields with predefined options, filter based on partial input
        if 'options' in field_def:
            return [
                option for option in field_def['options']
                if partial_value.lower() in option.lower()
            ]

        # For other fields, return example-based suggestions
        if 'examples' in field_def:
            return [
                example for example in field_def['examples']
                if partial_value.lower() in example.lower()
            ][:3]  # Limit to 3 suggestions

        return []

    def get_field_validation_rules(self, field_name: str) -> Dict[str, Any]:
        """
        Get validation rules for a field.

        Args:
            field_name: Name of the field

        Returns:
            Dictionary with validation rules
        """
        if field_name not in self.field_definitions:
            return {}

        field_def = self.field_definitions[field_name]
        rules = {}

        if 'options' in field_def:
            rules['type'] = 'select'
            rules['options'] = field_def['options']
        elif 'maxLength' in field_def:
            rules['type'] = 'text'
            rules['maxLength'] = field_def['maxLength']
        else:
            rules['type'] = 'text'

        rules['required'] = field_name == 'name'  # Only name is required
        rules['description'] = field_def.get('description', '')
        rules['examples'] = field_def.get('examples', [])

        return rules
