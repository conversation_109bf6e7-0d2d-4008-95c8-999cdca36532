"""
Shared tools for agents in the Datagenius backend.

This package contains tools that can be used by any agent in the system,
using the Model Context Protocol (MCP) standard.
"""

import logging

# Configure logging
logger = logging.getLogger(__name__)

# Import MCP tools package
from . import mcp

# Import MCP system for easy access (legacy registry removed)
from .mcp import initialize_mcp_system, AVAILABLE_TOOLS

# Import MCP tool classes for easy access
from .mcp.data_analysis import DataAnalysisTool
from .mcp.data_cleaning import DataCleaningTool
from .mcp.data_visualization import DataVisualizationTool
from .mcp.data_querying import DataQueryingTool
from .mcp.advanced_query import AdvancedQueryTool
from .mcp.data_filtering import DataFilteringTool
from .mcp.sentiment_analysis import SentimentAnalysisTool
from .mcp.text_processing import TextProcessingTool
from .mcp.text_classification import TextClassificationTool
from .mcp.marketing_strategy_generation import MarketingStrategyGenerationTool
from .mcp.conversation_tool import ConversationTool
from .mcp.persona_marketplace_tool import PersonaMarketplaceTool
from .mcp.statistical_analysis import StatisticalAnalysisTool
from .mcp.document_embedding import DocumentEmbeddingTool
from .mcp.pandasai_analysis import PandasAIAnalysisTool
from .mcp.pandasai_visualization import PandasAIVisualizationTool
from .mcp.pandasai_query import PandasAIQueryTool
from .mcp.data_storytelling import DataStorytellingTool
from .mcp.natural_language_query import NaturalLanguageQueryTool
from .mcp.deployment import DeploymentTool

logger.info("MCP tools package loaded")
