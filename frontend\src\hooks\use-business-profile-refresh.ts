/**
 * Business Profile Refresh Hook
 * 
 * Provides a centralized event system for triggering data refreshes when
 * business profiles change. Components can subscribe to profile changes
 * and automatically refresh their data.
 */

import React, { useEffect, useCallback, useRef } from 'react';
import { BusinessProfile } from '@/lib/businessProfileApi';
import { businessProfileEventEmitter } from '@/stores/business-profile-store';

// Event emitter for general refresh events
class BusinessProfileRefreshEmitter {
  private listeners: Set<() => void> = new Set();

  subscribe(callback: () => void) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  emit() {
    this.listeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in business profile refresh listener:', error);
      }
    });
  }
}

// Global refresh emitter instance
const businessProfileRefreshEmitter = new BusinessProfileRefreshEmitter();

/**
 * Hook for triggering business profile refresh events
 */
export const useBusinessProfileRefreshTrigger = () => {
  const triggerRefresh = useCallback(() => {
    console.log('🔄 Triggering business profile refresh...');
    businessProfileRefreshEmitter.emit();
  }, []);

  return { triggerRefresh };
};

/**
 * Hook for listening to business profile refresh events
 */
export const useBusinessProfileRefresh = () => {
  const [refreshTrigger, setRefreshTrigger] = React.useState(0);

  useEffect(() => {
    const unsubscribe = businessProfileRefreshEmitter.subscribe(() => {
      setRefreshTrigger(prev => prev + 1);
    });

    return unsubscribe;
  }, []);

  return { refreshTrigger };
};

/**
 * Hook for auto-refreshing when business profile changes
 */
export const useAutoRefreshOnProfileChange = (
  refreshCallback: () => void | Promise<void>,
  deps: React.DependencyList = []
) => {
  const callbackRef = useRef(refreshCallback);
  callbackRef.current = refreshCallback;

  useEffect(() => {
    const unsubscribe = businessProfileEventEmitter.subscribe(async (profile) => {
      console.log('🔄 Profile changed, triggering auto-refresh:', profile?.name || 'None');
      try {
        await callbackRef.current();
      } catch (error) {
        console.error('Error in auto-refresh callback:', error);
      }
    });

    return unsubscribe;
  }, deps);
};

/**
 * Hook for components that need to refresh data when profile changes
 */
export const useProfileAwareDataLoader = <T>(
  loadDataFn: (profileId: string | null) => Promise<T>,
  deps: React.DependencyList = []
) => {
  const [data, setData] = React.useState<T | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const loadDataRef = useRef(loadDataFn);
  loadDataRef.current = loadDataFn;

  const loadData = useCallback(async (profileId: string | null) => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await loadDataRef.current(profileId);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
      setData(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Listen for profile changes and reload data
  useEffect(() => {
    const unsubscribe = businessProfileEventEmitter.subscribe((profile) => {
      console.log('🔄 Profile changed, reloading data for:', profile?.name || 'None');
      loadData(profile?.id || null);
    });

    return unsubscribe;
  }, deps);

  // Manual refresh function
  const refresh = useCallback(() => {
    businessProfileRefreshEmitter.emit();
  }, []);

  return {
    data,
    isLoading,
    error,
    refresh,
    loadData,
  };
};

/**
 * Hook for dashboard components that need to refresh when profile changes
 */
export const useDashboardProfileRefresh = (
  refreshCallback: () => void | Promise<void>
) => {
  useAutoRefreshOnProfileChange(refreshCallback, []);
};

/**
 * Hook for data source components that need to refresh when profile changes
 */
export const useDataSourceProfileRefresh = (
  refreshCallback: () => void | Promise<void>
) => {
  useAutoRefreshOnProfileChange(refreshCallback, []);
};

/**
 * Hook for agent/chat components that need to refresh when profile changes
 */
export const useAgentProfileRefresh = (
  refreshCallback: () => void | Promise<void>
) => {
  useAutoRefreshOnProfileChange(refreshCallback, []);
};
