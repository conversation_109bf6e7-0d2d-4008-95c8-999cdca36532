/**
 * Autofill Progress Component
 * 
 * Standardized progress indicator for autofill operations across the application.
 * Provides consistent visual feedback for loading states, progress, and errors.
 */

import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface AutofillProgressProps {
  isLoading: boolean;
  progress: number;
  message: string;
  error?: string | null;
  success?: boolean;
  className?: string;
  showProgressBar?: boolean;
  showIcon?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
}

export const AutofillProgress: React.FC<AutofillProgressProps> = ({
  isLoading,
  progress,
  message,
  error,
  success,
  className,
  showProgressBar = true,
  showIcon = true,
  variant = 'default'
}) => {
  const getIcon = () => {
    if (!showIcon) return null;
    
    if (error) {
      return <XCircle className="h-4 w-4 text-destructive" />;
    }
    
    if (success || progress === 100) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    
    if (isLoading) {
      return <Loader2 className="h-4 w-4 animate-spin text-primary" />;
    }
    
    return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
  };

  const getProgressColor = () => {
    if (error) return 'bg-destructive';
    if (success || progress === 100) return 'bg-green-600';
    return 'bg-primary';
  };

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        {getIcon()}
        <span className={cn(
          'text-sm',
          error ? 'text-destructive' : 
          success || progress === 100 ? 'text-green-600' : 
          'text-muted-foreground'
        )}>
          {error || message}
        </span>
        {showProgressBar && isLoading && (
          <div className="flex-1 max-w-[100px]">
            <Progress value={progress} className="h-2" />
          </div>
        )}
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={cn('space-y-3', className)}>
        <div className="flex items-center space-x-3">
          {getIcon()}
          <div className="flex-1">
            <div className={cn(
              'font-medium text-sm',
              error ? 'text-destructive' : 
              success || progress === 100 ? 'text-green-600' : 
              'text-foreground'
            )}>
              {error ? 'Error' : success || progress === 100 ? 'Complete' : 'Processing'}
            </div>
            <div className="text-sm text-muted-foreground">
              {error || message}
            </div>
          </div>
          {!error && (
            <div className="text-sm text-muted-foreground">
              {progress}%
            </div>
          )}
        </div>
        
        {showProgressBar && !error && (
          <Progress
            value={progress}
            className="h-2"
            indicatorClassName={getProgressColor()}
          />
        )}
        
        {error && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center space-x-2">
        {getIcon()}
        <span className={cn(
          'text-sm',
          error ? 'text-destructive' : 
          success || progress === 100 ? 'text-green-600' : 
          'text-muted-foreground'
        )}>
          {error || message}
        </span>
      </div>
      
      {showProgressBar && isLoading && !error && (
        <Progress value={progress} className="h-2" />
      )}
      
      {error && (
        <Alert variant="destructive" className="mt-2">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
};

/**
 * Autofill Status Badge Component
 * 
 * Shows the current status of autofill operation as a badge.
 */
export interface AutofillStatusBadgeProps {
  isLoading: boolean;
  error?: string | null;
  success?: boolean;
  className?: string;
}

export const AutofillStatusBadge: React.FC<AutofillStatusBadgeProps> = ({
  isLoading,
  error,
  success,
  className
}) => {
  const getStatusInfo = () => {
    if (error) {
      return {
        text: 'Failed',
        className: 'bg-destructive/10 text-destructive border-destructive/20',
        icon: <XCircle className="h-3 w-3" />
      };
    }
    
    if (success) {
      return {
        text: 'Complete',
        className: 'bg-green-100 text-green-800 border-green-200',
        icon: <CheckCircle className="h-3 w-3" />
      };
    }
    
    if (isLoading) {
      return {
        text: 'Processing',
        className: 'bg-primary/10 text-primary border-primary/20',
        icon: <Loader2 className="h-3 w-3 animate-spin" />
      };
    }
    
    return {
      text: 'Ready',
      className: 'bg-muted text-muted-foreground border-border',
      icon: <AlertCircle className="h-3 w-3" />
    };
  };

  const status = getStatusInfo();

  return (
    <div className={cn(
      'inline-flex items-center space-x-1 px-2 py-1 rounded-md border text-xs font-medium',
      status.className,
      className
    )}>
      {status.icon}
      <span>{status.text}</span>
    </div>
  );
};

/**
 * Autofill Button Component
 * 
 * Standardized button for triggering autofill operations.
 */
export interface AutofillButtonProps {
  onClick: () => void;
  isLoading: boolean;
  disabled?: boolean;
  children?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
}

export const AutofillButton: React.FC<AutofillButtonProps> = ({
  onClick,
  isLoading,
  disabled = false,
  children = 'Autofill',
  className,
  variant = 'outline',
  size = 'default'
}) => {
  const isDisabled = disabled || isLoading;

  return (
    <button
      onClick={onClick}
      disabled={isDisabled}
      className={cn(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        'disabled:pointer-events-none disabled:opacity-50',
        
        // Variant styles
        variant === 'default' && 'bg-primary text-primary-foreground hover:bg-primary/90',
        variant === 'outline' && 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        variant === 'ghost' && 'hover:bg-accent hover:text-accent-foreground',
        
        // Size styles
        size === 'default' && 'h-10 px-4 py-2',
        size === 'sm' && 'h-9 rounded-md px-3',
        size === 'lg' && 'h-11 rounded-md px-8',
        
        className
      )}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Processing...
        </>
      ) : (
        children
      )}
    </button>
  );
};

export default AutofillProgress;
