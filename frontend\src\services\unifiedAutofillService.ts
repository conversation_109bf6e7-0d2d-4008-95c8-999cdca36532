/**
 * Unified Autofill Service
 * 
 * Consolidates shared autofill logic for both marketing and business profile forms.
 * Provides standardized error handling, loading states, and user feedback patterns.
 */

import { toast } from '@/hooks/use-toast';
import { fileApi } from '@/lib/api';
import { processDocumentForAutoFill, processUrlForAutoFill, processMultipleSourcesForAutoFill } from '@/lib/businessProfileAutoFillApi';
import axios from 'axios';

export interface AutofillResult {
  success: boolean;
  data?: Record<string, any>;
  error?: string;
  source?: string;
}

export interface AutofillOptions {
  showToasts?: boolean;
  onProgress?: (message: string, progress?: number) => void;
  onError?: (error: string) => void;
  onSuccess?: (data: Record<string, any>) => void;
}

export class UnifiedAutofillService {
  private static instance: UnifiedAutofillService;
  
  private constructor() {}
  
  public static getInstance(): UnifiedAutofillService {
    if (!UnifiedAutofillService.instance) {
      UnifiedAutofillService.instance = new UnifiedAutofillService();
    }
    return UnifiedAutofillService.instance;
  }

  /**
   * Autofill from marketing document query (legacy marketing form approach)
   */
  async autofillFromMarketingDocument(
    fileId: string,
    options: AutofillOptions = {}
  ): Promise<AutofillResult> {
    const { showToasts = true, onProgress, onError, onSuccess } = options;
    
    try {
      if (onProgress) onProgress('Processing document...', 25);
      
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      const token = localStorage.getItem('token');

      if (!token) {
        const error = 'Authentication required';
        if (showToasts) {
          toast({
            title: "Authentication Error",
            description: "You need to be logged in to fetch document data.",
            variant: "destructive",
          });
        }
        if (onError) onError(error);
        return { success: false, error };
      }

      if (onProgress) onProgress('Querying document with AI...', 50);

      const response = await axios.post(
        `${API_BASE_URL}/document-query`,
        {
          file_id: fileId,
          query_type: "marketing_fields"
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (onProgress) onProgress('Processing results...', 75);

      if (response.data && response.data.results) {
        const data = response.data.results;
        
        if (showToasts) {
          toast({
            title: "Form Autofilled",
            description: "Marketing form fields have been filled based on your document.",
          });
        }
        
        if (onSuccess) onSuccess(data);
        if (onProgress) onProgress('Complete!', 100);
        
        return { success: true, data, source: 'marketing_document' };
      } else {
        const error = 'No relevant data found in document';
        if (showToasts) {
          toast({
            title: "No Data Found",
            description: "Could not extract relevant information from the document.",
            variant: "default",
          });
        }
        if (onError) onError(error);
        return { success: false, error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (showToasts) {
        toast({
          title: "Autofill Failed",
          description: `Error processing document: ${errorMessage}`,
          variant: "destructive",
        });
      }
      
      if (onError) onError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Autofill from business profile document processing
   */
  async autofillFromBusinessDocument(
    file: File,
    options: AutofillOptions = {}
  ): Promise<AutofillResult> {
    const { showToasts = true, onProgress, onError, onSuccess } = options;
    
    try {
      if (onProgress) onProgress('Uploading document...', 25);
      
      const result = await processDocumentForAutoFill(file);
      
      if (onProgress) onProgress('Processing complete!', 100);
      
      if (result.success && result.field_mappings) {
        const data = this.transformBusinessProfileData(result.field_mappings);
        
        if (showToasts) {
          toast({
            title: "Document Processed",
            description: result.message || "Business profile fields have been filled based on your document.",
          });
        }
        
        if (onSuccess) onSuccess(data);
        return { success: true, data, source: 'business_document' };
      } else {
        const error = result.message || 'Failed to process document';
        if (showToasts) {
          toast({
            title: "Processing Failed",
            description: error,
            variant: "destructive",
          });
        }
        if (onError) onError(error);
        return { success: false, error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (showToasts) {
        toast({
          title: "Processing Failed",
          description: `Error processing document: ${errorMessage}`,
          variant: "destructive",
        });
      }
      
      if (onError) onError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Autofill from URL
   */
  async autofillFromUrl(
    url: string,
    options: AutofillOptions = {}
  ): Promise<AutofillResult> {
    const { showToasts = true, onProgress, onError, onSuccess } = options;
    
    try {
      if (onProgress) onProgress('Analyzing website...', 50);
      
      const result = await processUrlForAutoFill(url);
      
      if (onProgress) onProgress('Analysis complete!', 100);
      
      if (result.success && result.field_mappings) {
        const data = this.transformBusinessProfileData(result.field_mappings);
        
        if (showToasts) {
          toast({
            title: "Website Analyzed",
            description: result.message || "Business profile fields have been filled based on website content.",
          });
        }
        
        if (onSuccess) onSuccess(data);
        return { success: true, data, source: 'website' };
      } else {
        const error = result.message || 'Failed to analyze website';
        if (showToasts) {
          toast({
            title: "Analysis Failed",
            description: error,
            variant: "destructive",
          });
        }
        if (onError) onError(error);
        return { success: false, error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (showToasts) {
        toast({
          title: "Analysis Failed",
          description: `Error analyzing website: ${errorMessage}`,
          variant: "destructive",
        });
      }
      
      if (onError) onError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Autofill from multiple sources
   */
  async autofillFromMultipleSources(
    files?: File[],
    urls?: string[],
    options: AutofillOptions = {}
  ): Promise<AutofillResult> {
    const { showToasts = true, onProgress, onError, onSuccess } = options;
    
    try {
      if (onProgress) onProgress('Processing multiple sources...', 50);
      
      const result = await processMultipleSourcesForAutoFill(files, urls);
      
      if (onProgress) onProgress('Processing complete!', 100);
      
      if (result.success && result.field_mappings) {
        const data = this.transformBusinessProfileData(result.field_mappings);
        
        if (showToasts) {
          toast({
            title: "Sources Processed",
            description: result.message || "Business profile fields have been filled based on multiple sources.",
          });
        }
        
        if (onSuccess) onSuccess(data);
        return { success: true, data, source: 'multiple_sources' };
      } else {
        const error = result.message || 'Failed to process sources';
        if (showToasts) {
          toast({
            title: "Processing Failed",
            description: error,
            variant: "destructive",
          });
        }
        if (onError) onError(error);
        return { success: false, error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      if (showToasts) {
        toast({
          title: "Processing Failed",
          description: `Error processing sources: ${errorMessage}`,
          variant: "destructive",
        });
      }
      
      if (onError) onError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Upload file and get autofill data (for marketing form compatibility)
   */
  async uploadFileAndAutofill(
    file: File,
    options: AutofillOptions = {}
  ): Promise<AutofillResult> {
    const { showToasts = true, onProgress, onError } = options;
    
    try {
      if (onProgress) onProgress('Uploading file...', 25);
      
      if (showToasts) {
        toast({
          title: "Uploading File",
          description: `Uploading ${file.name} for analysis...`,
        });
      }

      const uploadedFile = await fileApi.uploadFile(file);
      
      if (onProgress) onProgress('File uploaded, processing...', 50);
      
      // Use marketing document autofill for uploaded files
      return await this.autofillFromMarketingDocument(uploadedFile.id, {
        ...options,
        showToasts: false, // Avoid duplicate toasts
        onProgress: (message, progress) => {
          if (onProgress) onProgress(message, progress ? 50 + (progress * 0.5) : undefined);
        }
      });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      
      if (showToasts) {
        toast({
          title: "Upload Failed",
          description: `Failed to upload ${file.name}. Please try again.`,
          variant: "destructive",
        });
      }
      
      if (onError) onError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Transform business profile API response to form data format
   */
  private transformBusinessProfileData(fieldMappings: Record<string, any>): Record<string, any> {
    const transformedData: Record<string, any> = {};
    
    for (const [fieldName, mapping] of Object.entries(fieldMappings)) {
      if (mapping && typeof mapping === 'object' && 'value' in mapping) {
        transformedData[fieldName] = mapping.value;
      } else {
        transformedData[fieldName] = mapping;
      }
    }
    
    return transformedData;
  }

  /**
   * Standardized error handling
   */
  handleError(error: any, context: string, showToast: boolean = true): string {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    if (showToast) {
      toast({
        title: `${context} Failed`,
        description: errorMessage,
        variant: "destructive",
      });
    }
    
    console.error(`${context} error:`, error);
    return errorMessage;
  }

  /**
   * Standardized success handling
   */
  handleSuccess(message: string, description?: string, showToast: boolean = true): void {
    if (showToast) {
      toast({
        title: message,
        description: description || "Operation completed successfully.",
      });
    }
  }
}

export const unifiedAutofillService = UnifiedAutofillService.getInstance();
