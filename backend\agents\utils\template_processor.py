"""
Template processor utilities for the Datagenius backend.

This module provides utilities for processing templates with actual values,
particularly for data profiles and other structured content.
"""

import logging
import re
from typing import Dict, Any, Optional, Union, List

logger = logging.getLogger(__name__)


def format_data_profile(template: str, metadata: Dict[str, Any]) -> str:
    """
    Format a data profile template with actual values from metadata.

    Args:
        template: The template string with placeholders
        metadata: Metadata containing actual values to replace placeholders

    Returns:
        Formatted template with actual values
    """
    try:
        # Create a dictionary of replacements from metadata
        replacements = {}

        # Basic information
        replacements["data_name"] = metadata.get("file_name", "your data")

        # Extract row and column counts
        if "row_count" in metadata:
            replacements["row_count"] = format_number(metadata["row_count"])
        elif "shape" in metadata:
            replacements["row_count"] = format_number(metadata["shape"][0])

        if "column_count" in metadata:
            replacements["column_count"] = format_number(metadata["column_count"])
        elif "shape" in metadata:
            replacements["column_count"] = format_number(metadata["shape"][1])

        # Data types
        if "data_types" in metadata:
            replacements["data_types"] = format_data_types(metadata["data_types"])
        elif "dtypes" in metadata:
            replacements["data_types"] = format_data_types(metadata["dtypes"])

        # Memory usage
        if "memory_usage" in metadata:
            replacements["memory_usage"] = format_memory_usage(metadata["memory_usage"])
        else:
            replacements["memory_usage"] = "Not available"

        # Data quality
        if "missing_values" in metadata:
            replacements["missing_values"] = format_missing_values(metadata["missing_values"])
        else:
            replacements["missing_values"] = "Not analyzed"

        # Generate dynamic content based on actual data analysis
        if "duplicates" not in replacements:
            replacements["duplicates"] = _analyze_duplicates(metadata)

        if "issues" not in replacements:
            replacements["issues"] = _detect_data_issues(metadata)

        if "column_summary" not in replacements:
            replacements["column_summary"] = _generate_column_summary(metadata)

        # Generate intelligent recommendations based on data characteristics
        if not any(f"recommendation_{i}" in replacements for i in range(1, 6)):
            recommendations = _generate_data_recommendations(metadata)
            for i, rec in enumerate(recommendations[:5], 1):
                replacements[f"recommendation_{i}"] = rec

        # Format the template with the replacements
        # Use a regex-based approach to replace only the placeholders that exist in the template
        formatted_template = template
        for key, value in replacements.items():
            placeholder = "{" + key + "}"
            formatted_template = formatted_template.replace(placeholder, str(value))

        return formatted_template

    except Exception as e:
        logger.error(f"Error formatting data profile template: {str(e)}", exc_info=True)
        # Return the original template with an error note
        return template + f"\n\nError formatting template: {str(e)}"


def format_number(number: Union[int, float, str, None]) -> str:
    """
    Format a number with commas as thousands separators.

    Args:
        number: The number to format

    Returns:
        Formatted number string
    """
    # Validate the input
    if number is None:
        logger.warning("Received None value for number formatting, using 'N/A'")
        return "N/A"

    try:
        # Convert to int if it's a string or float
        if isinstance(number, str):
            # Check if the string is empty or not a valid number
            if not number.strip() or not any(c.isdigit() for c in number):
                logger.warning(f"Invalid number string: '{number}', using 'N/A'")
                return "N/A"
            number = float(number)

        # Validate the number is not NaN or infinity
        if isinstance(number, float) and (number != number or number == float('inf') or number == float('-inf')):
            logger.warning(f"Invalid number value: {number}, using 'N/A'")
            return "N/A"

        # Format as integer if it's a whole number
        if isinstance(number, float) and number.is_integer():
            return f"{int(number):,}"
        elif isinstance(number, int):
            return f"{number:,}"
        else:
            # Format with 2 decimal places for floats
            return f"{number:,.2f}"
    except (ValueError, TypeError) as e:
        # Log the error and return a fallback value
        logger.error(f"Error formatting number '{number}': {str(e)}")
        return "N/A"


def format_data_types(dtypes: Dict[str, str]) -> str:
    """
    Format data types dictionary into a readable string.

    Args:
        dtypes: Dictionary of column names to data types

    Returns:
        Formatted string of data types
    """
    if not dtypes:
        return "Not available"

    # Count occurrences of each data type
    type_counts = {}
    for dtype in dtypes.values():
        dtype_str = str(dtype)
        # Simplify pandas/numpy dtypes
        if "int" in dtype_str:
            simple_type = "numeric"
        elif "float" in dtype_str:
            simple_type = "numeric"
        elif "object" in dtype_str:
            simple_type = "categorical"
        elif "datetime" in dtype_str:
            simple_type = "datetime"
        elif "bool" in dtype_str:
            simple_type = "boolean"
        else:
            simple_type = dtype_str

        type_counts[simple_type] = type_counts.get(simple_type, 0) + 1

    # Format as a readable string
    type_strings = [f"{count} {dtype}" for dtype, count in type_counts.items()]
    return ", ".join(type_strings)


def format_missing_values(missing_values: Dict[str, int]) -> str:
    """
    Format missing values dictionary into a readable string.

    Args:
        missing_values: Dictionary of column names to missing value counts

    Returns:
        Formatted string of missing values
    """
    if not missing_values:
        return "None detected"

    # Count total missing values
    total_missing = sum(missing_values.values())
    if total_missing == 0:
        return "None detected"

    # Count columns with missing values
    cols_with_missing = sum(1 for count in missing_values.values() if count > 0)

    return f"{total_missing} missing values across {cols_with_missing} columns"


def format_memory_usage(memory_usage: Union[int, float]) -> str:
    """
    Format memory usage into a human-readable string.

    Args:
        memory_usage: Memory usage in bytes

    Returns:
        Formatted memory usage string
    """
    try:
        # Convert to int if it's a string
        if isinstance(memory_usage, str):
            memory_usage = float(memory_usage)

        # Format based on size
        if memory_usage < 1024:
            return f"{memory_usage} bytes"
        elif memory_usage < 1024 * 1024:
            return f"{memory_usage / 1024:.2f} KB"
        elif memory_usage < 1024 * 1024 * 1024:
            return f"{memory_usage / (1024 * 1024):.2f} MB"
        else:
            return f"{memory_usage / (1024 * 1024 * 1024):.2f} GB"
    except (ValueError, TypeError):
        # Return as-is if conversion fails
        return str(memory_usage)


def _analyze_duplicates(metadata: Dict[str, Any]) -> str:
    """Analyze duplicate data based on metadata."""
    try:
        # Check for duplicate information in metadata
        if "duplicates" in metadata:
            duplicate_count = metadata["duplicates"]
            if isinstance(duplicate_count, dict):
                total_duplicates = sum(duplicate_count.values())
            else:
                total_duplicates = int(duplicate_count)

            if total_duplicates == 0:
                return "No duplicate rows detected"
            else:
                return f"{total_duplicates:,} duplicate rows found"

        # Estimate based on data characteristics
        row_count = metadata.get("row_count", 0)
        if row_count > 10000:
            return "Duplicate analysis recommended for large dataset"
        else:
            return "No duplicate analysis performed"

    except Exception as e:
        logger.error(f"Error analyzing duplicates: {e}")
        return "Duplicate analysis unavailable"


def _detect_data_issues(metadata: Dict[str, Any]) -> str:
    """Detect data quality issues based on metadata."""
    try:
        issues = []

        # Check for missing values
        missing_values = metadata.get("missing_values", {})
        if missing_values:
            total_missing = sum(missing_values.values()) if isinstance(missing_values, dict) else missing_values
            if total_missing > 0:
                issues.append(f"Missing values: {total_missing:,}")

        # Check for data type inconsistencies
        data_types = metadata.get("data_types", {})
        if data_types and isinstance(data_types, dict):
            object_columns = sum(1 for dtype in data_types.values() if "object" in str(dtype))
            if object_columns > len(data_types) * 0.5:
                issues.append("High proportion of text columns may indicate data type issues")

        # Check for memory usage concerns
        memory_usage = metadata.get("memory_usage", 0)
        if memory_usage > 100 * 1024 * 1024:  # > 100MB
            issues.append("Large memory usage - consider data optimization")

        return "; ".join(issues) if issues else "No significant data quality issues detected"

    except Exception as e:
        logger.error(f"Error detecting data issues: {e}")
        return "Data quality analysis unavailable"


def _generate_column_summary(metadata: Dict[str, Any]) -> str:
    """Generate a summary of columns based on metadata."""
    try:
        data_types = metadata.get("data_types", {})
        if not data_types:
            return "Column information not available"

        # Categorize columns by type
        numeric_cols = []
        text_cols = []
        date_cols = []
        other_cols = []

        for col, dtype in data_types.items():
            dtype_str = str(dtype).lower()
            if any(t in dtype_str for t in ["int", "float", "number"]):
                numeric_cols.append(col)
            elif any(t in dtype_str for t in ["object", "string", "text"]):
                text_cols.append(col)
            elif any(t in dtype_str for t in ["datetime", "date", "time"]):
                date_cols.append(col)
            else:
                other_cols.append(col)

        summary_parts = []
        if numeric_cols:
            summary_parts.append(f"{len(numeric_cols)} numeric columns")
        if text_cols:
            summary_parts.append(f"{len(text_cols)} text columns")
        if date_cols:
            summary_parts.append(f"{len(date_cols)} date columns")
        if other_cols:
            summary_parts.append(f"{len(other_cols)} other columns")

        return ", ".join(summary_parts) if summary_parts else "Column summary unavailable"

    except Exception as e:
        logger.error(f"Error generating column summary: {e}")
        return "Column summary unavailable"


def _generate_data_recommendations(metadata: Dict[str, Any]) -> List[str]:
    """Generate intelligent recommendations based on data characteristics."""
    try:
        recommendations = []

        # Analyze data size and structure
        row_count = metadata.get("row_count", 0)
        column_count = metadata.get("column_count", 0)

        if row_count > 100000:
            recommendations.append("Consider data sampling for faster analysis and visualization")

        if column_count > 50:
            recommendations.append("Focus on key columns for initial analysis to reduce complexity")

        # Analyze data types
        data_types = metadata.get("data_types", {})
        if data_types:
            object_ratio = sum(1 for dtype in data_types.values() if "object" in str(dtype)) / len(data_types)
            if object_ratio > 0.7:
                recommendations.append("Review text columns for potential categorization or encoding")

        # Analyze missing values
        missing_values = metadata.get("missing_values", {})
        if missing_values:
            total_missing = sum(missing_values.values()) if isinstance(missing_values, dict) else missing_values
            missing_ratio = total_missing / (row_count * column_count) if row_count and column_count else 0
            if missing_ratio > 0.1:
                recommendations.append("Develop strategy for handling missing values before analysis")

        # Memory usage recommendations
        memory_usage = metadata.get("memory_usage", 0)
        if memory_usage > 500 * 1024 * 1024:  # > 500MB
            recommendations.append("Consider data compression or chunked processing for large dataset")

        # Default recommendations if none generated
        if not recommendations:
            recommendations = [
                "Explore data distributions to understand value ranges and patterns",
                "Check for correlations between numeric variables",
                "Validate data quality and consistency across all columns",
                "Consider creating visualizations to identify trends and outliers",
                "Document data sources and transformation steps for reproducibility"
            ]

        return recommendations[:5]  # Return max 5 recommendations

    except Exception as e:
        logger.error(f"Error generating recommendations: {e}")
        return [
            "Perform exploratory data analysis to understand your dataset",
            "Check data quality and handle missing values appropriately",
            "Create visualizations to identify patterns and insights",
            "Document your analysis process for future reference",
            "Consider statistical analysis based on your research questions"
        ]
