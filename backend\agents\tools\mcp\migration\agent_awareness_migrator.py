"""
Agent Awareness Migration System.

This module provides automatic detection and migration of MCP tools to ensure
they have proper agent identity integration. It scans the codebase, identifies
tools that need enhancement, and provides automated migration capabilities.
"""

import logging
import os
import ast
import importlib
import inspect
from typing import Dict, Any, List, Tuple, Optional, Type
from pathlib import Path

from ..base import BaseMCPTool
from ..enhanced_base import Enhanced<PERSON><PERSON>Tool
from ..mixins.agent_aware_mixin import AgentAwareMixin
from ..factory.agent_aware_factory import get_agent_aware_factory

logger = logging.getLogger(__name__)


class AgentAwarenessMigrator:
    """
    System for detecting and migrating MCP tools to agent awareness.
    
    This migrator can:
    1. Scan the codebase for MCP tools
    2. Detect which tools need agent awareness
    3. Automatically migrate tools to use agent identity
    4. Generate migration reports and recommendations
    5. Validate agent awareness implementation
    """
    
    def __init__(self, tools_directory: str = None):
        """
        Initialize the agent awareness migrator.
        
        Args:
            tools_directory: Directory containing MCP tools
        """
        self.tools_directory = tools_directory or os.path.dirname(os.path.dirname(__file__))
        self.factory = get_agent_aware_factory()
        self.scan_results = {}
        self.migration_results = {}
    
    def scan_tools_directory(self) -> Dict[str, Any]:
        """
        Scan the tools directory for MCP tools and analyze their agent awareness.
        
        Returns:
            Scan results with tool analysis
        """
        scan_results = {
            "total_tools": 0,
            "agent_aware_tools": 0,
            "needs_migration": 0,
            "enhanced_tools": 0,
            "legacy_tools": 0,
            "tools_analysis": {},
            "migration_recommendations": []
        }
        
        # Scan Python files in tools directory
        tools_path = Path(self.tools_directory)
        python_files = list(tools_path.glob("*.py"))
        
        for file_path in python_files:
            if file_path.name.startswith("__") or file_path.name in ["base.py", "enhanced_base.py"]:
                continue
            
            try:
                analysis = self._analyze_tool_file(file_path)
                if analysis["has_tool_class"]:
                    scan_results["total_tools"] += 1
                    scan_results["tools_analysis"][file_path.name] = analysis
                    
                    # Categorize tools
                    if analysis["is_enhanced"]:
                        scan_results["enhanced_tools"] += 1
                        scan_results["agent_aware_tools"] += 1
                    elif analysis["has_agent_awareness"]:
                        scan_results["agent_aware_tools"] += 1
                    else:
                        scan_results["needs_migration"] += 1
                        scan_results["legacy_tools"] += 1
                        
                        # Add migration recommendation
                        scan_results["migration_recommendations"].append({
                            "file": file_path.name,
                            "tool_class": analysis["tool_class_name"],
                            "priority": self._calculate_migration_priority(analysis),
                            "complexity": analysis["migration_complexity"]
                        })
                        
            except Exception as e:
                logger.warning(f"Failed to analyze {file_path}: {e}")
        
        self.scan_results = scan_results
        logger.info(f"Scanned {scan_results['total_tools']} tools, {scan_results['needs_migration']} need migration")
        
        return scan_results
    
    def _analyze_tool_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Analyze a Python file to determine its tool characteristics.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            Analysis results
        """
        analysis = {
            "file_path": str(file_path),
            "has_tool_class": False,
            "tool_class_name": None,
            "is_enhanced": False,
            "has_agent_awareness": False,
            "has_agent_imports": False,
            "has_detect_agent_identity": False,
            "has_agent_metadata": False,
            "migration_complexity": "low",
            "imports": [],
            "classes": []
        }
        
        try:
            # Parse the file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            # Analyze imports
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        analysis["imports"].append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for alias in node.names:
                            full_import = f"{node.module}.{alias.name}"
                            analysis["imports"].append(full_import)
            
            # Check for agent-related imports
            agent_imports = [
                "agents.utils.agent_identity",
                "agents.utils.system_prompts",
                "detect_agent_identity",
                "get_agent_system_prompt"
            ]
            
            analysis["has_agent_imports"] = any(
                any(agent_import in imp for agent_import in agent_imports)
                for imp in analysis["imports"]
            )
            
            # Analyze classes
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    analysis["classes"].append(node.name)
                    
                    # Check if it's a tool class
                    base_names = [base.id if isinstance(base, ast.Name) else str(base) for base in node.bases]
                    
                    if any("Tool" in base for base in base_names):
                        analysis["has_tool_class"] = True
                        analysis["tool_class_name"] = node.name
                        
                        # Check if it's enhanced
                        if "EnhancedMCPTool" in base_names:
                            analysis["is_enhanced"] = True
                        
                        # Check for agent awareness patterns
                        class_content = ast.get_source_segment(content, node) or ""
                        
                        if "detect_agent_identity" in class_content:
                            analysis["has_detect_agent_identity"] = True
                        
                        if "agent_identity" in class_content and "metadata" in class_content:
                            analysis["has_agent_metadata"] = True
                        
                        # Determine migration complexity
                        if len(class_content) > 5000:  # Large class
                            analysis["migration_complexity"] = "high"
                        elif len(class_content) > 2000:  # Medium class
                            analysis["migration_complexity"] = "medium"
            
            # Determine overall agent awareness
            analysis["has_agent_awareness"] = (
                analysis["has_agent_imports"] and
                analysis["has_detect_agent_identity"] and
                analysis["has_agent_metadata"]
            )
            
        except Exception as e:
            logger.warning(f"Failed to parse {file_path}: {e}")
        
        return analysis
    
    def _calculate_migration_priority(self, analysis: Dict[str, Any]) -> str:
        """
        Calculate migration priority based on tool analysis.
        
        Args:
            analysis: Tool analysis results
            
        Returns:
            Priority level (high, medium, low)
        """
        # High priority: Complex tools without any agent awareness
        if (analysis["migration_complexity"] == "high" and 
            not analysis["has_agent_awareness"]):
            return "high"
        
        # Medium priority: Tools with partial agent awareness
        if (analysis["has_agent_imports"] or 
            analysis["has_detect_agent_identity"]):
            return "medium"
        
        # Low priority: Simple tools without agent awareness
        return "low"
    
    def migrate_tool_file(self, file_path: str, backup: bool = True) -> Dict[str, Any]:
        """
        Migrate a specific tool file to include agent awareness.
        
        Args:
            file_path: Path to the tool file
            backup: Whether to create a backup
            
        Returns:
            Migration results
        """
        migration_result = {
            "file_path": file_path,
            "success": False,
            "backup_created": False,
            "changes_made": [],
            "errors": []
        }
        
        try:
            file_path_obj = Path(file_path)
            
            # Create backup if requested
            if backup:
                backup_path = file_path_obj.with_suffix(f"{file_path_obj.suffix}.backup")
                backup_path.write_text(file_path_obj.read_text(encoding='utf-8'), encoding='utf-8')
                migration_result["backup_created"] = True
            
            # Read current content
            content = file_path_obj.read_text(encoding='utf-8')
            
            # Apply migrations
            modified_content = content
            
            # Add agent imports if missing
            if "from agents.utils.agent_identity import detect_agent_identity" not in content:
                import_line = "from agents.utils.agent_identity import detect_agent_identity\n"
                modified_content = self._add_import(modified_content, import_line)
                migration_result["changes_made"].append("Added agent_identity import")
            
            if "from agents.utils.system_prompts import get_agent_system_prompt" not in content:
                import_line = "from agents.utils.system_prompts import get_agent_system_prompt\n"
                modified_content = self._add_import(modified_content, import_line)
                migration_result["changes_made"].append("Added system_prompts import")
            
            # Add agent detection to execute method
            if "detect_agent_identity" not in content:
                modified_content = self._add_agent_detection(modified_content)
                migration_result["changes_made"].append("Added agent identity detection")
            
            # Add agent metadata to responses
            if '"agent_identity"' not in content:
                modified_content = self._add_agent_metadata(modified_content)
                migration_result["changes_made"].append("Added agent metadata to responses")
            
            # Write modified content
            if modified_content != content:
                file_path_obj.write_text(modified_content, encoding='utf-8')
                migration_result["success"] = True
                logger.info(f"Successfully migrated {file_path}")
            else:
                migration_result["success"] = True
                migration_result["changes_made"].append("No changes needed")
            
        except Exception as e:
            migration_result["errors"].append(str(e))
            logger.error(f"Failed to migrate {file_path}: {e}")
        
        return migration_result
    
    def _add_import(self, content: str, import_line: str) -> str:
        """Add an import line to the content."""
        lines = content.split('\n')
        
        # Find the last import line
        last_import_index = -1
        for i, line in enumerate(lines):
            if line.strip().startswith(('import ', 'from ')):
                last_import_index = i
        
        # Insert after the last import
        if last_import_index >= 0:
            lines.insert(last_import_index + 1, import_line.rstrip())
        else:
            # Insert after module docstring or at the beginning
            insert_index = 0
            if lines and lines[0].strip().startswith('"""'):
                # Find end of docstring
                for i in range(1, len(lines)):
                    if '"""' in lines[i]:
                        insert_index = i + 1
                        break
            lines.insert(insert_index, import_line.rstrip())
        
        return '\n'.join(lines)
    
    def _add_agent_detection(self, content: str) -> str:
        """Add agent detection code to the execute method."""
        # This is a simplified implementation
        # In practice, you'd want more sophisticated AST manipulation
        
        agent_detection_code = '''
            # Agent context for dynamic identity detection
            user_context = arguments.get("context", {})
            agent_id = arguments.get("persona_id") or arguments.get("agent_id")
            
            # Detect agent identity for personalized processing
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=user_context,
                intent_type="{tool_name}"
            )
            
            logger.info(f"Detected agent identity: {{agent_identity}} for {tool_name}")
        '''
        
        # Find execute method and add agent detection
        # This is a simplified approach - real implementation would use AST
        if "async def execute" in content and "detect_agent_identity" not in content:
            content = content.replace(
                "async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:",
                f"async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:{agent_detection_code}"
            )
        
        return content
    
    def _add_agent_metadata(self, content: str) -> str:
        """Add agent metadata to response structures."""
        # This is a simplified implementation
        # In practice, you'd want more sophisticated pattern matching
        
        if '"metadata"' in content and '"agent_identity"' not in content:
            # Add agent metadata to existing metadata structures
            content = content.replace(
                '"metadata": {',
                '"metadata": {\n                    "agent_identity": agent_identity,\n                    "agent_aware": True,'
            )
        
        return content
    
    def generate_migration_report(self) -> str:
        """
        Generate a comprehensive migration report.
        
        Returns:
            Migration report as formatted string
        """
        if not self.scan_results:
            self.scan_tools_directory()
        
        report = f"""
# Agent Awareness Migration Report

## Summary
- **Total Tools**: {self.scan_results['total_tools']}
- **Agent-Aware Tools**: {self.scan_results['agent_aware_tools']}
- **Enhanced Tools**: {self.scan_results['enhanced_tools']}
- **Legacy Tools**: {self.scan_results['legacy_tools']}
- **Tools Needing Migration**: {self.scan_results['needs_migration']}

## Migration Recommendations

### High Priority
"""
        
        high_priority = [r for r in self.scan_results['migration_recommendations'] if r['priority'] == 'high']
        for rec in high_priority:
            report += f"- **{rec['file']}** ({rec['tool_class']}) - {rec['complexity']} complexity\n"
        
        report += "\n### Medium Priority\n"
        medium_priority = [r for r in self.scan_results['migration_recommendations'] if r['priority'] == 'medium']
        for rec in medium_priority:
            report += f"- **{rec['file']}** ({rec['tool_class']}) - {rec['complexity']} complexity\n"
        
        report += "\n### Low Priority\n"
        low_priority = [r for r in self.scan_results['migration_recommendations'] if r['priority'] == 'low']
        for rec in low_priority:
            report += f"- **{rec['file']}** ({rec['tool_class']}) - {rec['complexity']} complexity\n"
        
        report += f"""

## Next Steps

1. **Immediate**: Migrate high-priority tools using the automated migrator
2. **Short-term**: Update medium-priority tools with agent awareness
3. **Long-term**: Migrate remaining legacy tools
4. **Ongoing**: Use EnhancedMCPTool for all new tools

## Migration Commands

```python
# Scan tools
migrator = AgentAwarenessMigrator()
results = migrator.scan_tools_directory()

# Migrate specific tool
migrator.migrate_tool_file("path/to/tool.py")

# Batch migrate all tools
migrator.batch_migrate_tools()
```
"""
        
        return report
    
    def batch_migrate_tools(self, priority_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        Migrate multiple tools in batch.
        
        Args:
            priority_filter: Filter by priority (high, medium, low)
            
        Returns:
            Batch migration results
        """
        if not self.scan_results:
            self.scan_tools_directory()
        
        batch_results = {
            "total_attempted": 0,
            "successful_migrations": 0,
            "failed_migrations": 0,
            "results": []
        }
        
        recommendations = self.scan_results['migration_recommendations']
        
        if priority_filter:
            recommendations = [r for r in recommendations if r['priority'] == priority_filter]
        
        for rec in recommendations:
            file_path = os.path.join(self.tools_directory, rec['file'])
            result = self.migrate_tool_file(file_path)
            
            batch_results["total_attempted"] += 1
            if result["success"]:
                batch_results["successful_migrations"] += 1
            else:
                batch_results["failed_migrations"] += 1
            
            batch_results["results"].append(result)
        
        logger.info(f"Batch migration completed: {batch_results['successful_migrations']}/{batch_results['total_attempted']} successful")
        
        return batch_results


# Global migrator instance
agent_awareness_migrator = AgentAwarenessMigrator()


def get_migrator() -> AgentAwarenessMigrator:
    """Get the global agent awareness migrator."""
    return agent_awareness_migrator
