#!/usr/bin/env python3
"""
Test script to verify the PersonaConfig schema.
"""

import sys
from pathlib import Path

# Add backend root to path
backend_root = Path(__file__).parent
sys.path.insert(0, str(backend_root))

def test_schema():
    """Test the PersonaConfig schema."""
    try:
        from schemas.agent_config_schemas import PersonaConfig
        
        # Test data with agent_class field
        test_data = {
            "id": "test-agent",
            "name": "Test Agent",
            "agent_class": "agents.test.TestAgent",
            "description": "A test agent"
        }
        
        # Try to create PersonaConfig instance
        config = PersonaConfig(**test_data)
        print(f"✅ Schema validation passed!")
        print(f"✅ agent_class: {config.agent_class}")
        return True
        
    except Exception as e:
        print(f"❌ Schema validation failed: {e}")
        return False

if __name__ == "__main__":
    success = test_schema()
    sys.exit(0 if success else 1)
