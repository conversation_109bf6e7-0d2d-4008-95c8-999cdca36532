"""
Core Orchestrator for managing multi-agent workflows in Datagenius.

This orchestrator provides:
- Agent-specific context preservation
- Intelligent routing and conversation management
- Performance monitoring and error handling
- Extensible architecture for multi-agent workflows
"""

import logging
import time
from typing import Dict, Any, Optional, List

# Import necessary components/services
from sqlalchemy.orm import Session
from agents.registry import AgentRegistry # Changed to absolute import
from .routing_component import RoutingComponent
from ..database import get_conversation, update_conversation # Added DB imports
# from ..services.conversation_service import ConversationService
# from ..models.conversation import Conversation

logger = logging.getLogger(__name__)


class Orchestrator:
    """
    Manages the execution flow of tasks across multiple agents/personas.

    Features:
    - Agent-specific context preservation
    - Intelligent routing and conversation management
    - Performance monitoring and error handling
    - Extensible architecture for multi-agent workflows
    """

    def __init__(self):
        """Initialize the Orchestrator."""
        # Initialize necessary services or registries here
        self.agent_registry = AgentRegistry # Use the imported registry
        self.routing_component = RoutingComponent() # Instantiate routing component

        # Performance and monitoring
        self.performance_metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_processing_time": 0.0,
            "total_errors": 0,
            "average_response_time": 0.0,
            "average_processing_time": 0.0,
            "agent_usage": {},
            "context_preservation_stats": {
                "marketing_form_preserved": 0,
                "analysis_data_preserved": 0,
                "basic_preservation": 0,
                "preservation_failures": 0
            }
        }

        # Agent-specific configuration
        self.agent_specific_configs = {
            "composable-marketing-ai": {
                "context_priority": ["marketing_form_data", "latest_data_source"],
                "preserve_marketing_metadata": True,
                "max_context_history": 20
            },
            "composable-analysis-ai": {
                "context_priority": ["analysis_data", "non_marketing_data", "latest_data_source"],
                "exclude_marketing_form_data": True,
                "max_context_history": 15
            },
            "concierge-agent": {
                "context_priority": ["latest_data_source"],
                "preserve_all_metadata": True,
                "max_context_history": 25
            }
        }

        logger.info("Orchestrator initialized with agent-specific configurations.")

    async def handle_incoming_message(self, db: Session, user_id: int, conversation_id: str, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handles an incoming user message, routes it appropriately, and returns the final response.

        Args:
            db: Database session.
            user_id: The ID of the user.
            conversation_id: The ID of the conversation.
            message: The user's message text.
            context: Optional additional context (e.g., attached files, session info).

        Returns:
            A dictionary containing the response message and any metadata.
        """
        start_time = time.time()
        self.performance_metrics["total_requests"] += 1

        logger.info(f"Orchestrator handling message for conversation {conversation_id}")
        logger.info(f"Message: '{message[:100]}...', Context keys: {list(context.keys()) if context else []}")
        if context is None:
            context = {}

        try:
            # Ensure conversation history is properly formatted and available
            enhanced_context = await self._enhance_context_with_conversation_history(
                db, user_id, conversation_id, message, context
            )

            # DEBUG: Log the raw context received
            logger.info(f"=== ORCHESTRATOR RECEIVED CONTEXT ===")
            logger.info(f"Raw context: {context}")
            logger.info(f"Context type: {type(context)}")
            logger.info(f"Context keys: {list(context.keys()) if context else 'No context'}")
            logger.info(f"=== END ORCHESTRATOR RECEIVED CONTEXT ===")

            # --- Get Current Conversation State ---
            conversation = get_conversation(db, conversation_id)
            if not conversation:
                logger.error(f"Conversation {conversation_id} not found during orchestration.")
                self.performance_metrics["failed_requests"] += 1
                # Return an error response immediately
                return {
                    "message": "Error: Conversation not found.",
                    "metadata": {"error": "conversation_not_found", "conversation_id": conversation_id}
                }
            current_state = conversation.state
            current_persona = conversation.persona_id
            logger.info(f"Current conversation state for {conversation_id}: {current_state}")
            logger.info(f"✅ ORCHESTRATOR: Current conversation persona for {conversation_id}: {current_persona}")

            # Add state and persona to context for potential use by agents/components and routing
            context["current_conversation_state"] = current_state
            context["current_persona"] = current_persona
            logger.info(f"✅ ORCHESTRATOR: Added current_persona to context: {current_persona}")
            # --- End Get State ---

            # 1. Determine the target agent/persona using the routing component
            # TODO: Pass current_state to determine_target_agent when implemented

            # DEBUG LOGGING: Log routing decision
            logger.info(f"=== ORCHESTRATOR ROUTING DEBUG ===")
            logger.info(f"Message: '{message}'")
            logger.info(f"Context keys: {list(context.keys()) if context else 'No context'}")
            logger.info(f"User ID: {user_id}, Conversation ID: {conversation_id}")

            target_persona_id = await self.routing_component.determine_target_agent(message, enhanced_context)

            # Apply agent-specific context preservation after routing decision
            enhanced_context = await self._apply_agent_specific_context_preservation(
                enhanced_context, target_persona_id, conversation_id
            )
            logger.info(f"Routing component determined target agent: {target_persona_id}")
            logger.info(f"=== END ORCHESTRATOR ROUTING DEBUG ===")

            # Update agent usage metrics
            if target_persona_id not in self.performance_metrics["agent_usage"]:
                self.performance_metrics["agent_usage"][target_persona_id] = 0
            self.performance_metrics["agent_usage"][target_persona_id] += 1

            # 2. Instantiate the target agent
            logger.info(f"🔄 Loading agent instance for: {target_persona_id}")
            agent_instance = await self.agent_registry.create_agent_instance(target_persona_id)

            if not agent_instance:
                logger.error(f"❌ Failed to create agent instance for persona ID: {target_persona_id}")
                self.performance_metrics["failed_requests"] += 1
                return {
                    "message": f"Sorry, I could not find an agent for '{target_persona_id}'.",
                    "metadata": {"error": "agent_instantiation_failed", "persona_id": target_persona_id}
                }
            logger.info(f"✅ Successfully loaded agent instance: {agent_instance.name if hasattr(agent_instance, 'name') else target_persona_id}")

            # 3. Call the agent's process_message method
            logger.debug(f"Calling process_message for agent {agent_instance.name}")

            # Call with standard arguments: message, user_id, conversation_id, context
            # The 'context' variable from handle_incoming_message already contains relevant session/task context.
            # 'user_id' and 'conversation_id' are passed directly.

            logger.debug(f"🔄 Orchestrator calling agent_instance.process_message with message: '{message[:50]}...', user_id: {user_id}, conversation_id: {conversation_id}, context keys: {list(enhanced_context.keys()) if enhanced_context else None}")
            try:
                agent_response = await agent_instance.process_message(
                    message=message,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    context=enhanced_context
                )
                logger.info(f"✅ Agent {target_persona_id} returned response: content_length={len(str(agent_response.get('message', '')))}, metadata_keys={list(agent_response.get('metadata', {}).keys())}")
            except Exception as agent_error:
                logger.error(f"❌ Agent {target_persona_id} failed to process message: {str(agent_error)}", exc_info=True)
                raise agent_error
            logger.debug(f"Agent {agent_instance.name} processing complete.")

            # 4. Update Conversation State and Persona
            new_state = "PROCESSING_COMPLETE" # Placeholder state
            update_data = {"state": new_state}

            # Update persona_id if it has changed (persona switching)
            if current_persona != target_persona_id:
                update_data["persona_id"] = target_persona_id
                logger.info(f"Conversation {conversation_id} persona changed from {current_persona} to {target_persona_id}")

            try:
                update_conversation(db, conversation_id, update_data=update_data)
                logger.info(f"Updated conversation {conversation_id} state to: {new_state}")
                if "persona_id" in update_data:
                    logger.info(f"Updated conversation {conversation_id} persona to: {target_persona_id}")
            except Exception as e:
                logger.error(f"Failed to update conversation state/persona for {conversation_id}: {e}", exc_info=True)
                # Add error to metadata but continue returning the agent response
                agent_response["metadata"] = agent_response.get("metadata", {})
                agent_response["metadata"]["state_update_error"] = str(e)
            # --- End Update State ---

            # 5. Process the agent's response (Further steps can be added here later)
            #    - Could involve further steps, quality checks, or routing to other agents
            #      based on the response or updated context.

            # 6. Format and return the final response
            final_response = {
                "message": agent_response.get("message", "Sorry, I encountered an issue."),
                "metadata": agent_response.get("metadata", {})
            }

            # Update performance metrics
            end_time = time.time()
            response_time = end_time - start_time
            self.performance_metrics["successful_requests"] += 1

            # Update average response time
            total_successful = self.performance_metrics["successful_requests"]
            current_avg = self.performance_metrics["average_response_time"]
            self.performance_metrics["average_response_time"] = (
                (current_avg * (total_successful - 1) + response_time) / total_successful
            )

            # Add performance metadata
            final_response["metadata"]["orchestrator_metrics"] = {
                "response_time": response_time,
                "target_agent": target_persona_id,
                "context_preservation_applied": True
            }

            logger.info(f"Orchestrator finished handling message for conversation {conversation_id} in {response_time:.2f}s")
            return final_response

        except Exception as e:
            # Handle any unexpected errors
            end_time = time.time()
            response_time = end_time - start_time
            self.performance_metrics["failed_requests"] += 1

            logger.error(f"❌ Orchestrator error handling message for conversation {conversation_id}: {str(e)}", exc_info=True)
            logger.error(f"❌ Error details - user_id: {user_id}, message: '{message[:100]}...'")
            logger.error(f"❌ Context keys: {list(context.keys()) if context else 'None'}")
            logger.error(f"❌ Target agent: {target_persona_id if 'target_persona_id' in locals() else 'unknown'}")

            return {
                "message": "I encountered an unexpected error while processing your request. Please try again.",
                "metadata": {
                    "error": "orchestrator_error",
                    "error_details": str(e),
                    "response_time": response_time,
                    "conversation_id": conversation_id
                }
            }

    async def handle_streaming_message(self, db: Session, user_id: int, conversation_id: str, message: str, context: Optional[Dict[str, Any]] = None):
        """
        Handle an incoming user message with streaming response support.

        Args:
            db: Database session.
            user_id: The ID of the user.
            conversation_id: The ID of the conversation.
            message: The user's message text.
            context: Optional additional context (e.g., attached files, session info).

        Yields:
            Dictionary containing streaming chunks and metadata.
        """
        start_time = time.time()
        self.performance_metrics["total_requests"] += 1

        logger.info(f"Orchestrator handling streaming message for conversation {conversation_id}")
        logger.info(f"Message: '{message[:100]}...', Context keys: {list(context.keys()) if context else []}")
        if context is None:
            context = {}

        try:
            # Ensure conversation history is properly formatted and available
            enhanced_context = await self._enhance_context_with_conversation_history(
                db, user_id, conversation_id, message, context
            )

            # DIAGNOSTIC: Log context state before routing
            logger.info("=== ORCHESTRATOR PRE-ROUTING DIAGNOSTIC ===")
            logger.info(f"Enhanced context keys: {list(enhanced_context.keys())}")
            logger.info(f"Enhanced context routing keys: current_persona={enhanced_context.get('current_persona')}, persona_id={enhanced_context.get('persona_id')}, agent_id={enhanced_context.get('agent_id')}")
            logger.info(f"Enhanced context flags: skip_marketing_content_generation={enhanced_context.get('skip_marketing_content_generation')}, is_conversational={enhanced_context.get('is_conversational')}")

            # 1. Determine the appropriate agent using routing component
            target_persona_id = await self.routing_component.determine_target_agent(message, enhanced_context)

            # Apply agent-specific context preservation after routing decision
            enhanced_context = await self._apply_agent_specific_context_preservation(
                enhanced_context, target_persona_id, conversation_id
            )
            logger.info(f"Orchestrator determined agent: {target_persona_id}")

            # DIAGNOSTIC: Log context state after routing
            logger.info("=== ORCHESTRATOR POST-ROUTING DIAGNOSTIC ===")
            logger.info(f"Final target_persona_id: {target_persona_id}")
            logger.info(f"Enhanced context routing keys: current_persona={enhanced_context.get('current_persona')}, persona_id={enhanced_context.get('persona_id')}, agent_id={enhanced_context.get('agent_id')}")
            logger.info("=== END ORCHESTRATOR DIAGNOSTIC ===")

            # Update agent usage metrics
            if target_persona_id not in self.performance_metrics["agent_usage"]:
                self.performance_metrics["agent_usage"][target_persona_id] = 0
            self.performance_metrics["agent_usage"][target_persona_id] += 1

            # 2. Load the agent instance using agent registry
            agent_instance = await self.agent_registry.create_agent_instance(target_persona_id)
            if not agent_instance:
                logger.error(f"Failed to load agent: {target_persona_id}")
                yield {
                    "type": "content",
                    "content": "I apologize, but I'm unable to process your request at the moment. Please try again later."
                }
                return

            # 3. Use universal streaming capability (all agents now support streaming)
            logger.info(f"🚀 Using universal streaming for agent {target_persona_id}")

            # Check if agent supports streaming
            if hasattr(agent_instance, 'supports_streaming') and agent_instance.supports_streaming():
                logger.info(f"✅ Agent {target_persona_id} supports streaming")
            else:
                logger.warning(f"❌ Agent {target_persona_id} does not support streaming")

            # Call the agent's universal streaming method
            chunk_count = 0
            async for chunk_data in agent_instance.process_streaming_message(
                message=message,
                user_id=user_id,
                conversation_id=conversation_id,
                context=enhanced_context
            ):
                chunk_count += 1
                # Only log every 25th chunk to reduce verbosity
                if chunk_count % 25 == 0:
                    logger.debug(f"📦 Orchestrator yielding chunk {chunk_count}: {chunk_data.get('type')} - {chunk_data.get('content', '')[:50]}...")
                yield chunk_data

            logger.info(f"🏁 Streaming completed for agent {target_persona_id}, total chunks: {chunk_count}")

            # Update performance metrics
            processing_time = time.time() - start_time
            self.performance_metrics["total_processing_time"] += processing_time
            self.performance_metrics["average_processing_time"] = (
                self.performance_metrics["total_processing_time"] /
                self.performance_metrics["total_requests"]
            )

        except Exception as e:
            logger.error(f"Error in orchestrator streaming processing: {str(e)}")
            self.performance_metrics["total_errors"] += 1

            yield {
                "type": "content",
                "content": f"I apologize, but I encountered an error while processing your request: {str(e)}"
            }
            yield {
                "type": "metadata",
                "metadata": {"error": str(e), "status": "error"}
            }



    async def _enhance_context_with_conversation_history(
        self,
        db: Session,
        user_id: int,
        conversation_id: str,
        current_message: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Enhance the context with properly formatted conversation history.

        Args:
            db: Database session
            user_id: The user ID
            conversation_id: The conversation ID
            current_message: The current message being processed
            context: The existing context

        Returns:
            Enhanced context with conversation history
        """
        enhanced_context = context.copy()

        # Preserve current_persona if it exists in the original context
        current_persona = context.get("current_persona")
        if current_persona:
            logger.info(f"Orchestrator: Preserving current_persona from context: {current_persona}")
            enhanced_context["current_persona"] = current_persona

        try:
            # Import here to avoid circular imports
            from app.database import get_conversation_messages

            # Get conversation history from database
            messages = get_conversation_messages(db, conversation_id, limit=20)

            if messages:
                # Format conversation history for agent consumption
                conversation_history = []

                for msg in messages:
                    # Skip the current message if it's already in the database
                    if msg.content == current_message and msg.sender == "user":
                        continue

                    formatted_message = {
                        "id": msg.id,
                        "sender": msg.sender,
                        "content": msg.content,
                        "timestamp": msg.created_at.isoformat() if msg.created_at else None,
                        "metadata": msg.message_metadata or {}
                    }
                    conversation_history.append(formatted_message)

                # Add conversation history to enhanced context
                enhanced_context["conversation_history"] = conversation_history
                enhanced_context["conversation_context"] = {
                    "total_messages": len(conversation_history),
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "last_updated": conversation_history[-1]["timestamp"] if conversation_history else None
                }

                # If current_persona is not in context, try to derive it from conversation history
                if not enhanced_context.get("current_persona"):
                    logger.info("=== ORCHESTRATOR PERSONA DERIVATION ===")
                    logger.info("No current_persona in context, attempting to derive from conversation history")

                    # Look for the most recent AI message to determine which persona was active
                    for msg in reversed(conversation_history):
                        if msg.get("sender") == "ai":
                            msg_metadata = msg.get("metadata", {})
                            logger.info(f"Checking AI message metadata: {msg_metadata}")

                            # Check for persona information in metadata
                            if msg_metadata.get("persona"):
                                derived_persona = msg_metadata["persona"]
                                logger.info(f"Found persona in metadata: {derived_persona}")

                                # Map persona names to IDs if needed
                                persona_mapping = {
                                    "marketing": "composable-marketing-ai",
                                    "analysis": "composable-analysis-ai",
                                    "concierge": "concierge-agent"
                                }
                                if derived_persona in persona_mapping:
                                    derived_persona = persona_mapping[derived_persona]
                                    logger.info(f"Mapped persona to ID: {derived_persona}")

                                enhanced_context["current_persona"] = derived_persona
                                logger.info(f"✅ Orchestrator: Derived current_persona from conversation history: {derived_persona}")
                                break
                            # Also check for agent_id or similar fields
                            elif msg_metadata.get("agent_id"):
                                enhanced_context["current_persona"] = msg_metadata["agent_id"]
                                logger.info(f"✅ Orchestrator: Derived current_persona from agent_id: {msg_metadata['agent_id']}")
                                break

                    if not enhanced_context.get("current_persona"):
                        logger.warning("❌ Could not derive current_persona from conversation history")
                    logger.info("=== END PERSONA DERIVATION ===")

                # Basic data source preservation (agent-specific logic will be applied later)
                # Only preserve if no data source is currently in context
                if "data_source" not in enhanced_context:
                    # Look for the most recent data source in conversation history
                    for msg in reversed(conversation_history):
                        if msg.get("metadata", {}).get("data_source"):
                            enhanced_context["data_source"] = msg["metadata"]["data_source"]
                            logger.info(f"Orchestrator: Preserved latest data source from conversation: {enhanced_context['data_source']}")
                            break

                # CRITICAL: Restore conversational state from recent AI message metadata
                # This ensures that conversational state persists between message processing cycles
                await self._restore_conversational_state_from_history(enhanced_context, conversation_history)

                # UNIVERSAL: Enhanced conversational state restoration for all agent types
                # This provides universal tool completion support across all agents
                try:
                    from agents.mixins.orchestrator_enhancement import UniversalToolCompletionOrchestrator
                    UniversalToolCompletionOrchestrator.enhance_context_with_universal_tool_completion(
                        enhanced_context,
                        conversation_history
                    )
                except ImportError:
                    logger.warning("Universal tool completion enhancement not available - using legacy restoration only")

                logger.info(f"Orchestrator: Enhanced context with {len(conversation_history)} conversation messages")
            else:
                logger.info(f"Orchestrator: No conversation history found for conversation {conversation_id}")
                enhanced_context["conversation_history"] = []
                enhanced_context["conversation_context"] = {
                    "total_messages": 0,
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "last_updated": None
                }

        except Exception as e:
            logger.error(f"Orchestrator: Error enhancing context with conversation history: {str(e)}")
            # Ensure we have at least empty conversation history
            enhanced_context["conversation_history"] = []
            enhanced_context["conversation_context"] = {
                "total_messages": 0,
                "conversation_id": conversation_id,
                "user_id": user_id,
                "last_updated": None,
                "error": str(e)
            }

        return enhanced_context

    async def _restore_conversational_state_from_history(
        self,
        enhanced_context: Dict[str, Any],
        conversation_history: List[Dict[str, Any]]
    ) -> None:
        """
        Restore conversational state flags from recent AI message metadata.

        This method checks the most recent AI messages for conversational state metadata
        and restores the appropriate flags to ensure conversational mode persists
        between message processing cycles.

        Args:
            enhanced_context: The context to enhance with conversational state
            conversation_history: List of conversation messages
        """
        try:
            logger.info("🔄 ORCHESTRATOR: Checking for conversational state in conversation history")

            # Only restore conversational state if there are no explicit tool triggers in current context
            # Tool triggers should always take precedence over conversational state
            has_tool_triggers = (
                enhanced_context.get("marketing_form_data") is not None or
                enhanced_context.get("is_regeneration", False) or
                enhanced_context.get("tool_call", False) or
                enhanced_context.get("button_triggered", False) or
                enhanced_context.get("form_submission", False)
            )

            if has_tool_triggers:
                logger.info("🔄 ORCHESTRATOR: Tool triggers detected - skipping conversational state restoration")
                return

            # Look through recent AI messages (last 3) for conversational state metadata
            recent_ai_messages = []
            for msg in reversed(conversation_history):
                if msg.get("sender") == "ai":
                    recent_ai_messages.append(msg)
                    if len(recent_ai_messages) >= 3:  # Only check last 3 AI messages
                        break

            for msg in recent_ai_messages:
                metadata = msg.get("metadata", {}) or {}

                # Check for conversational state metadata
                conversational_state = metadata.get("conversational_state", {})
                if conversational_state:
                    logger.info(f"🔄 ORCHESTRATOR: Found conversational state metadata: {conversational_state}")

                    # Check if this indicates the next message should be conversational
                    if (conversational_state.get("next_message_should_be_conversational", False) or
                        conversational_state.get("auto_return_to_conversational", False) or
                        conversational_state.get("tool_completed", False)):

                        # Restore conversational state flags
                        enhanced_context["skip_marketing_content_generation"] = True
                        enhanced_context["is_conversational"] = True
                        enhanced_context["content_generation_completed"] = True
                        enhanced_context["tool_completed"] = True
                        enhanced_context["auto_conversational_mode"] = True

                        # Add metadata about state restoration
                        if "metadata" not in enhanced_context:
                            enhanced_context["metadata"] = {}
                        enhanced_context["metadata"]["conversational_state_restored"] = True
                        enhanced_context["metadata"]["restored_from_message_id"] = msg.get("id")
                        enhanced_context["metadata"]["restoration_timestamp"] = time.time()

                        logger.info("✅ ORCHESTRATOR: Conversational state restored from conversation history")
                        logger.info(f"🔄 ORCHESTRATOR: Restored flags - skip_content_generation=True, is_conversational=True")
                        return

                # Also check for legacy metadata patterns
                if (metadata.get("generated_content", False) or
                    metadata.get("content_generation_completed", False) or
                    metadata.get("marketing_content_generated", False)):

                    # Check if there have been user messages since this content generation
                    # If so, we should be in conversational mode
                    user_messages_since = 0
                    found_this_message = False

                    for check_msg in reversed(conversation_history):
                        if check_msg.get("id") == msg.get("id"):
                            found_this_message = True
                            continue
                        if found_this_message and check_msg.get("sender") == "user":
                            user_messages_since += 1

                    if user_messages_since > 0:
                        # There have been user messages since content generation
                        # This indicates we should be in conversational mode
                        enhanced_context["skip_marketing_content_generation"] = True
                        enhanced_context["is_conversational"] = True
                        enhanced_context["is_follow_up_question"] = True
                        enhanced_context["content_generation_completed"] = True

                        if "metadata" not in enhanced_context:
                            enhanced_context["metadata"] = {}
                        enhanced_context["metadata"]["conversational_state_restored"] = True
                        enhanced_context["metadata"]["restored_from_legacy_metadata"] = True
                        enhanced_context["metadata"]["user_messages_since_content"] = user_messages_since

                        logger.info("✅ ORCHESTRATOR: Conversational state restored from legacy metadata")
                        logger.info(f"🔄 ORCHESTRATOR: {user_messages_since} user messages since content generation")
                        return

            logger.info("🔄 ORCHESTRATOR: No conversational state found in recent conversation history")

        except Exception as e:
            logger.error(f"❌ ORCHESTRATOR: Error restoring conversational state: {str(e)}", exc_info=True)
            # Don't fail the entire request if state restoration fails

    async def _apply_agent_specific_context_preservation(
        self,
        enhanced_context: Dict[str, Any],
        target_persona_id: str,
        conversation_id: str
    ) -> Dict[str, Any]:
        """
        Apply agent-specific context preservation logic.

        Args:
            enhanced_context: The enhanced context with conversation history
            target_persona_id: The target agent/persona ID
            conversation_id: The conversation ID (for future extensibility)

        Returns:
            Enhanced context with agent-specific data source preservation
        """
        try:
            conversation_history = enhanced_context.get("conversation_history", [])
            agent_config = self.agent_specific_configs.get(target_persona_id, {})
            max_history = agent_config.get("max_context_history", 20)

            # Limit conversation history based on agent configuration
            if len(conversation_history) > max_history:
                conversation_history = conversation_history[-max_history:]
                enhanced_context["conversation_history"] = conversation_history
                logger.info(f"Limited conversation history to {max_history} messages for {target_persona_id}")

            # Agent-specific context preservation
            if target_persona_id == "composable-marketing-ai":
                # For marketing agent, prioritize marketing form data sources
                logger.info("Applying marketing agent specific context preservation")

                # Check if this is a new content generation request before preserving old form data
                current_message = enhanced_context.get("message", "")
                is_new_content_request = self._is_new_marketing_content_request(current_message)

                # Always preserve form data, but handle differently for new content requests
                # Look for marketing form data source first
                for msg in reversed(conversation_history):
                    msg_metadata = msg.get("metadata", {})
                    if msg_metadata.get("marketing_form_data") and msg_metadata.get("data_source"):
                        enhanced_context["data_source"] = msg_metadata["data_source"]
                        enhanced_context["marketing_form_data"] = msg_metadata["marketing_form_data"]
                        self.performance_metrics["context_preservation_stats"]["marketing_form_preserved"] += 1
                        logger.info(f"Marketing agent: Preserved marketing form data source: {enhanced_context['data_source']}")

                        # For new content requests, mark that this is preserved data for content type change
                        if is_new_content_request:
                            enhanced_context["preserve_form_data_for_new_content"] = True
                            logger.info("Marketing agent: Marked form data for preservation with new content type")

                        return enhanced_context

                # Also preserve marketing form data if found separately
                for msg in reversed(conversation_history):
                    msg_metadata = msg.get("metadata", {})
                    if msg_metadata.get("marketing_form_data") and "marketing_form_data" not in enhanced_context:
                        enhanced_context["marketing_form_data"] = msg_metadata["marketing_form_data"]
                        logger.info("Marketing agent: Preserved marketing form data from conversation history")

                        # For new content requests, mark that this is preserved data for content type change
                        if is_new_content_request:
                            enhanced_context["preserve_form_data_for_new_content"] = True
                            logger.info("Marketing agent: Marked form data for preservation with new content type")

                        break

            elif target_persona_id == "composable-analysis-ai":
                # For analysis agent, prioritize analysis-related data sources
                logger.info("Applying analysis agent specific context preservation")

                # Look for analysis-specific data sources (avoid marketing form data)
                for msg in reversed(conversation_history):
                    msg_metadata = msg.get("metadata", {})
                    data_source = msg_metadata.get("data_source")
                    # Skip marketing form data sources for analysis agent
                    if data_source and not msg_metadata.get("marketing_form_data"):
                        enhanced_context["data_source"] = data_source
                        self.performance_metrics["context_preservation_stats"]["analysis_data_preserved"] += 1
                        logger.info(f"Analysis agent: Preserved analysis data source: {data_source}")
                        break

            elif target_persona_id == "concierge-agent":
                # For concierge agent, preserve all metadata for comprehensive context
                logger.info("Applying concierge agent specific context preservation")

                # Preserve all types of metadata for concierge
                if agent_config.get("preserve_all_metadata", True):
                    all_metadata = {}
                    for msg in conversation_history:
                        msg_metadata = msg.get("metadata", {})
                        all_metadata.update(msg_metadata)

                    enhanced_context["all_conversation_metadata"] = all_metadata
                    logger.info("Concierge agent: Preserved all conversation metadata")

            # For other agents, use the basic data source preservation already applied
            # Track basic preservation
            if enhanced_context.get("data_source") and target_persona_id not in ["composable-marketing-ai", "composable-analysis-ai"]:
                self.performance_metrics["context_preservation_stats"]["basic_preservation"] += 1

        except Exception as e:
            logger.error(f"Error in agent-specific context preservation: {str(e)}")
            self.performance_metrics["context_preservation_stats"]["preservation_failures"] += 1
            # Continue with existing context if error occurs

        return enhanced_context

    def _is_new_marketing_content_request(self, message: str) -> bool:
        """
        Check if the message is a new marketing content generation request.

        Args:
            message: User's message text

        Returns:
            True if this is a new content generation request
        """
        if not message:
            return False

        message_lower = message.lower().strip()

        # Explicit new content patterns
        explicit_patterns = [
            "create a new", "generate a new", "make a new", "develop a new",
            "create another", "generate another", "make another", "develop another",
            "new marketing strategy", "new campaign", "new content", "new plan",
            "different strategy", "different campaign", "different approach",
            "start over", "from scratch", "completely new"
        ]

        # Check for explicit patterns first
        if any(pattern in message_lower for pattern in explicit_patterns):
            return True

        # Enhanced detection for content type requests
        content_generation_verbs = [
            "create", "generate", "make", "develop", "build", "write", "design", "craft", "produce"
        ]

        content_type_keywords = [
            "strategy", "plan", "analysis", "campaign", "content", "post", "calendar",
            "positioning", "competitive", "brand", "marketing", "social media", "seo",
            "email", "influencer", "pr", "product launch", "customer persona",
            "market research", "digital", "advertising", "lead generation", "conversion"
        ]

        # Check if message contains generation verb + content type
        has_generation_verb = any(verb in message_lower for verb in content_generation_verbs)
        has_content_type = any(keyword in message_lower for keyword in content_type_keywords)

        if has_generation_verb and has_content_type:
            return True

        # Check for specific content type mentions that typically indicate new requests
        specific_content_requests = [
            "brand positioning", "competitive analysis", "content calendar", "email marketing",
            "influencer strategy", "pr strategy", "product launch", "customer persona",
            "market research", "digital strategy", "advertising strategy", "lead generation"
        ]

        if any(content_type in message_lower for content_type in specific_content_requests):
            return True

        return False

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get current performance metrics for monitoring and debugging.

        Returns:
            Dictionary containing performance metrics
        """
        return self.performance_metrics.copy()

    def reset_performance_metrics(self) -> None:
        """Reset performance metrics to initial state."""
        self.performance_metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "agent_usage": {},
            "context_preservation_stats": {
                "marketing_form_preserved": 0,
                "analysis_data_preserved": 0,
                "basic_preservation": 0,
                "preservation_failures": 0
            }
        }
        logger.info("Performance metrics reset")

    def get_agent_config(self, agent_id: str) -> Dict[str, Any]:
        """
        Get configuration for a specific agent.

        Args:
            agent_id: The agent/persona ID

        Returns:
            Agent configuration dictionary
        """
        return self.agent_specific_configs.get(agent_id, {})

    def update_agent_config(self, agent_id: str, config: Dict[str, Any]) -> None:
        """
        Update configuration for a specific agent.

        Args:
            agent_id: The agent/persona ID
            config: New configuration to merge
        """
        if agent_id not in self.agent_specific_configs:
            self.agent_specific_configs[agent_id] = {}

        self.agent_specific_configs[agent_id].update(config)
        logger.info(f"Updated configuration for agent {agent_id}")

    async def validate_context_preservation(self, conversation_id: str, expected_data_source: str = None) -> Dict[str, Any]:
        """
        Validate that context preservation is working correctly for a conversation.

        Args:
            conversation_id: The conversation ID to validate
            expected_data_source: Optional expected data source for validation

        Returns:
            Validation results
        """
        try:
            # This would typically check database state and conversation history
            # For now, return basic validation info
            return {
                "conversation_id": conversation_id,
                "validation_passed": True,
                "expected_data_source": expected_data_source,
                "timestamp": time.time(),
                "message": "Context preservation validation completed"
            }
        except Exception as e:
            logger.error(f"Error validating context preservation: {str(e)}")
            return {
                "conversation_id": conversation_id,
                "validation_passed": False,
                "error": str(e),
                "timestamp": time.time()
            }

    def get_orchestrator_status(self) -> Dict[str, Any]:
        """
        Get comprehensive status of the orchestrator.

        Returns:
            Status information including metrics, configurations, and health
        """
        return {
            "status": "active",
            "performance_metrics": self.get_performance_metrics(),
            "agent_configurations": len(self.agent_specific_configs),
            "supported_agents": list(self.agent_specific_configs.keys()),
            "routing_component_status": "active",
            "agent_registry_status": "active",
            "timestamp": time.time()
        }

# Potentially create a singleton instance if needed application-wide
# orchestrator = Orchestrator()
