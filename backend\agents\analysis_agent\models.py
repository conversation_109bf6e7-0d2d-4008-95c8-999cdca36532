"""
Pydantic models for the Analysis Agent.

This module defines Pydantic models for analysis agent requests and responses.
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class AnalysisTaskRequest(BaseModel):
    """Request model for analysis task."""
    task_type: str = Field(..., description="Type of analysis task to perform")
    file_id: int = Field(..., description="ID of the uploaded file to analyze")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Task-specific parameters")


class AnalysisTaskResponse(BaseModel):
    """Response model for analysis task."""
    message: str = Field(..., description="Response message")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class DataCleaningParameters(BaseModel):
    """Parameters for data cleaning task."""
    drop_columns: Optional[List[str]] = Field(None, description="Columns to drop")
    drop_rows: bool = Field(False, description="Whether to drop rows with missing values")
    fill_method: Optional[str] = Field(None, description="Method to fill missing values (mean, median, mode, custom)")
    custom_value: Optional[str] = Field(None, description="Custom value to fill missing values")


class DataVisualizationParameters(BaseModel):
    """Parameters for data visualization task."""
    plot_type: str = Field(..., description="Type of plot to generate")
    x_column: Optional[str] = Field(None, description="Column for x-axis")
    y_column: Optional[str] = Field(None, description="Column for y-axis")
    color_column: Optional[str] = Field(None, description="Column for color")
    title: Optional[str] = Field(None, description="Plot title")
    width: Optional[int] = Field(None, description="Plot width")
    height: Optional[int] = Field(None, description="Plot height")


class DataQueryingParameters(BaseModel):
    """Parameters for data querying task."""
    query: str = Field(..., description="Natural language query")


class AdvancedQueryingParameters(BaseModel):
    """Parameters for advanced querying task."""
    query: str = Field(..., description="SQL-like query")


class DataFilteringParameters(BaseModel):
    """Parameters for data filtering task."""
    filters: Dict[str, Any] = Field(..., description="Filters to apply")


class SentimentAnalysisParameters(BaseModel):
    """Parameters for sentiment analysis task."""
    text_column: str = Field(..., description="Column containing text to analyze")
    date_column: Optional[str] = Field(None, description="Column containing dates for time-based analysis")


class AnalysisCapability(BaseModel):
    """Model for an analysis capability."""
    id: str = Field(..., description="Capability identifier")
    name: str = Field(..., description="Display name of the capability")
    description: str = Field(..., description="Description of the capability")


class AnalysisCapabilitiesResponse(BaseModel):
    """Response model for analysis capabilities."""
    capabilities: List[AnalysisCapability] = Field(..., description="List of analysis capabilities")
