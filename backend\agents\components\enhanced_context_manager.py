"""
Enhanced component for managing shared context across agent interactions and personas.
"""

import logging
import json
import time
import uuid
import sys
from typing import Dict, Any, List, Optional, Set
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent
from .context_manager import ContextManagerComponent

logger = logging.getLogger(__name__)


class EnhancedContextManagerComponent(ContextManagerComponent):
    """
    Enhanced version of the ContextManagerComponent with improved context sharing,
    synchronization, and persistence capabilities.
    """

    def __init__(self):
        """Initialize the EnhancedContextManagerComponent."""
        super().__init__()
        self.context_history = {}  # Store historical context for each conversation
        self.shared_entities = {}  # Store shared entities across personas
        self.context_versions = {}  # Track context versions for synchronization

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        await super()._initialize(config)
        logger.info(f"EnhancedContextManagerComponent '{self.name}' initialized.")
        self.max_history_size = config.get("max_history_size", 10)  # Maximum number of historical contexts to keep
        self.sync_interval = config.get("sync_interval", 300)  # Sync interval in seconds
        self.enable_entity_tracking = config.get("enable_entity_tracking", True)
        self.enable_context_versioning = config.get("enable_context_versioning", True)

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process the context with enhanced context management capabilities.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object with enhanced context management.
        """
        # First, process with the base context manager
        # The base class process method now correctly handles AgentProcessingContext
        context = await super().process(context)

        user_message = context.message or ""
        conversation_id = str(context.conversation_id) # Ensure string
        current_persona = context.agent_config.id if context.agent_config else "unknown"

        logger.debug(f"EnhancedContextManagerComponent processing for conversation {conversation_id}")

        # Initialize enhanced context in component_data if not present
        component_specific_data = context.component_data.setdefault(self.name, {})
        enhanced_context_data = component_specific_data.setdefault("enhanced_context", {
            "version": 1,
            "last_sync": time.time(),
            "tracked_entities": {},
            "context_history": []
        })

        # Track context history
        await self._track_context_history(conversation_id, context) # Pass AgentProcessingContext

        # Track entities across personas
        if self.enable_entity_tracking:
            await self._track_entities(conversation_id, context) # Pass AgentProcessingContext

        # Version the context for synchronization
        if self.enable_context_versioning:
            await self._version_context(conversation_id, current_persona, context) # Pass AgentProcessingContext

        # Check if context synchronization is needed
        if self._needs_synchronization(context): # Pass AgentProcessingContext
            await self._synchronize_context(conversation_id, current_persona, context) # Pass AgentProcessingContext

        return context

    async def _track_context_history(self, conversation_id: str, context: "AgentProcessingContext") -> None:
        """
        Track the history of context for a conversation.

        Args:
            conversation_id: The ID of the conversation.
            context: The current AgentProcessingContext.
        """
        base_class_name = ContextManagerComponent.__name__ # Get base class name for its component_data
        base_shared_context = context.component_data.get(base_class_name, {}).get("shared_across_personas", {})

        current_enhanced_context = context.component_data.get(self.name, {}).get("enhanced_context", {})

        history_entry = {
            "timestamp": time.time(),
            "persona_id": context.agent_config.id if context.agent_config else "unknown",
            "message": context.message or "",
            "shared_context_from_base": base_shared_context, # Data from base ContextManager
            "enhanced_context_snapshot": current_enhanced_context # Snapshot of this component's data
        }

        if conversation_id not in self.context_history:
            self.context_history[conversation_id] = []
        self.context_history[conversation_id].append(history_entry)

        if len(self.context_history[conversation_id]) > self.max_history_size:
            self.context_history[conversation_id] = self.context_history[conversation_id][-self.max_history_size:]

        # Update this component's enhanced_context data
        enhanced_context_data = context.component_data.setdefault(self.name, {}).setdefault("enhanced_context", {})
        enhanced_context_data["context_history"] = self.context_history[conversation_id][-3:]
        logger.debug(f"Tracked context history for conversation {conversation_id}")

    async def _track_entities(self, conversation_id: str, context: "AgentProcessingContext") -> None:
        """
        Track entities across personas for a conversation.

        Args:
            conversation_id: The ID of the conversation.
            context: The current AgentProcessingContext.
        """
        if conversation_id not in self.shared_entities:
            self.shared_entities[conversation_id] = {}

        # Extract entities from the base ContextManagerComponent's data
        base_class_name = ContextManagerComponent.__name__
        base_component_data = context.component_data.get(base_class_name, {})
        current_entities = base_component_data.get("shared_across_personas", {}).get("entities", {})

        if current_entities:
            for entity_type, entities in current_entities.items():
                if entity_type not in self.shared_entities[conversation_id]:
                    self.shared_entities[conversation_id][entity_type] = set()
                if isinstance(entities, list):
                    self.shared_entities[conversation_id][entity_type].update(entities)
                else:
                    self.shared_entities[conversation_id][entity_type].add(entities)

            serializable_entities = {etype: list(ents) for etype, ents in self.shared_entities[conversation_id].items()}

            enhanced_context_data = context.component_data.setdefault(self.name, {}).setdefault("enhanced_context", {})
            enhanced_context_data["tracked_entities"] = serializable_entities
            logger.debug(f"Tracked entities for conversation {conversation_id}: {serializable_entities}")

    async def _version_context(self, conversation_id: str, persona_id: str, context: "AgentProcessingContext") -> None:
        """
        Version the context for synchronization.

        Args:
            conversation_id: The ID of the conversation.
            persona_id: The ID of the current persona.
            context: The current AgentProcessingContext.
        """
        if conversation_id not in self.context_versions:
            self.context_versions[conversation_id] = {"global_version": 1, "persona_versions": {}}

        enhanced_context_data = context.component_data.setdefault(self.name, {}).setdefault("enhanced_context", {"version": 1})
        current_version = enhanced_context_data.get("version", 1)

        if persona_id not in self.context_versions[conversation_id]["persona_versions"]:
            self.context_versions[conversation_id]["persona_versions"][persona_id] = current_version

        if current_version < self.context_versions[conversation_id]["global_version"]:
            enhanced_context_data["version"] = self.context_versions[conversation_id]["global_version"]
            enhanced_context_data["needs_sync"] = True
            logger.debug(f"Updated context version for {persona_id} to {enhanced_context_data['version']}")
        elif current_version > self.context_versions[conversation_id]["global_version"]:
            self.context_versions[conversation_id]["global_version"] = current_version
            logger.debug(f"Updated global context version to {current_version}")

        self.context_versions[conversation_id]["persona_versions"][persona_id] = enhanced_context_data["version"]

    def _needs_synchronization(self, context: "AgentProcessingContext") -> bool:
        """
        Determine if context synchronization is needed.

        Args:
            context: The current AgentProcessingContext.

        Returns:
            True if synchronization is needed, False otherwise.
        """
        enhanced_context_data = context.component_data.get(self.name, {}).get("enhanced_context", {})

        if enhanced_context_data.get("needs_sync", False):
            return True

        last_sync = enhanced_context_data.get("last_sync", 0)
        if time.time() - last_sync > self.sync_interval:
            return True

        return False

    async def _synchronize_context(self, conversation_id: str, persona_id: str, context: "AgentProcessingContext") -> None:
        """
        Synchronize context across personas.

        Args:
            conversation_id: The ID of the conversation.
            persona_id: The ID of the current persona.
            context: The current AgentProcessingContext.
        """
        enhanced_context_data = context.component_data.setdefault(self.name, {}).setdefault("enhanced_context", {})
        enhanced_context_data["last_sync"] = time.time()
        enhanced_context_data["needs_sync"] = False

        if self.enable_entity_tracking and conversation_id in self.shared_entities:
            serializable_entities = {etype: list(ents) for etype, ents in self.shared_entities[conversation_id].items()}
            enhanced_context_data["tracked_entities"] = serializable_entities

        if conversation_id in self.context_history:
            enhanced_context_data["context_history"] = self.context_history[conversation_id][-3:]

        logger.debug(f"Synchronized context for {persona_id} in conversation {conversation_id}")

    async def _save_context(self, conversation_id: str, context: "AgentProcessingContext", target_persona: str) -> None:
        """
        Save context for transfer to another persona.

        Args:
            conversation_id: The ID of the conversation.
            context: The current AgentProcessingContext.
            target_persona: The target persona.
        """
        base_class_name = ContextManagerComponent.__name__
        base_shared_context = context.component_data.get(base_class_name, {}).get("shared_across_personas", {})
        current_enhanced_context = context.component_data.get(self.name, {}).get("enhanced_context", {})

        context_to_transfer_dict = {
            "conversation_id": conversation_id,
            "user_id": str(context.user_id),
            "source_persona": context.agent_config.id if context.agent_config else "concierge",
            "conversation_history": context.initial_context.get("conversation_history", []), # From initial context
            "attached_files": context.initial_context.get("attached_files", []), # From initial context
            "shared_context_from_base": base_shared_context, # Data from base ContextManager
            "enhanced_context_from_derived": current_enhanced_context, # Data from this EnhancedContextManager
            "metadata_for_transfer": { # Renamed to avoid collision with AgentProcessingContext.metadata
                "shared_from": context.agent_config.id if context.agent_config else "concierge",
                "timestamp": time.time(),
                "original_request": context.message or "",
                "transfer_id": str(uuid.uuid4())
            }
        }

        key = f"transfer:{conversation_id}:{target_persona}"
        self.context_store[key] = context_to_transfer_dict # self.context_store is the in-memory dict from base
        logger.debug(f"Saved enhanced context for transfer: {key}")

        if self.enable_context_versioning and conversation_id in self.context_versions:
            self.context_versions[conversation_id]["global_version"] += 1
            source_persona_id = context.agent_config.id if context.agent_config else "concierge"
            self.context_versions[conversation_id]["persona_versions"][source_persona_id] = self.context_versions[conversation_id]["global_version"]
            logger.debug(f"Updated context version for transfer: {self.context_versions[conversation_id]['global_version']}")

    def _merge_context(self, target_apc_context: "AgentProcessingContext", shared_context_dict: Dict[str, Any]) -> None:
        """
        Merge shared context (from _save_context format) into the target AgentProcessingContext.
        This method is specific to EnhancedContextManagerComponent and does not call super()._merge_context
        as the base ContextManagerComponent does not define it.

        Args:
            target_apc_context: The target AgentProcessingContext to update.
            shared_context_dict: The dictionary containing shared context data (typically from another persona via self.context_store).
        """
        # Merge data from "shared_context_from_base" into the base's component_data area
        base_class_name = ContextManagerComponent.__name__
        target_base_shared_data = target_apc_context.component_data.setdefault(base_class_name, {}).setdefault("shared_across_personas", {})
        source_base_shared_data = shared_context_dict.get("shared_context_from_base", {})
        if source_base_shared_data:
            target_base_shared_data.update(source_base_shared_data) # Simple dict update

        # Merge data from "enhanced_context_from_derived" into this component's data area
        target_enhanced_data = target_apc_context.component_data.setdefault(self.name, {}).setdefault("enhanced_context", {})
        source_enhanced_data = shared_context_dict.get("enhanced_context_from_derived", {})

        if source_enhanced_data:
            # Merge tracked entities carefully
            if "tracked_entities" in source_enhanced_data:
                target_tracked_entities = target_enhanced_data.setdefault("tracked_entities", {})
                for entity_type, entities in source_enhanced_data["tracked_entities"].items():
                    current_target_entities_set = set(target_tracked_entities.get(entity_type, []))
                    current_target_entities_set.update(entities)
                    target_tracked_entities[entity_type] = list(current_target_entities_set)

            # Merge context history carefully
            if "context_history" in source_enhanced_data:
                target_history = target_enhanced_data.setdefault("context_history", [])
                existing_timestamps = {entry["timestamp"] for entry in target_history}
                for entry in source_enhanced_data["context_history"]:
                    if entry["timestamp"] not in existing_timestamps:
                        target_history.append(entry)
                target_history.sort(key=lambda x: x["timestamp"])
                if len(target_history) > self.max_history_size:
                    target_enhanced_data["context_history"] = target_history[-self.max_history_size:]

            # Update version and sync info
            target_enhanced_data["version"] = source_enhanced_data.get("version", target_enhanced_data.get("version", 1))
            target_enhanced_data["last_sync"] = time.time()
            target_enhanced_data["needs_sync"] = False

        # Add transfer information to metadata of the target AgentProcessingContext
        transfer_metadata = shared_context_dict.get("metadata_for_transfer", {})
        if transfer_metadata:
            target_apc_context.metadata["context_transfer_info"] = { # Renamed to avoid direct overwrite
                "source_persona": shared_context_dict.get("source_persona"), # from top level of shared_context_dict
                "timestamp": transfer_metadata.get("timestamp"),
                "transfer_id": transfer_metadata.get("transfer_id")
            }

        logger.debug(f"Merged enhanced context from {shared_context_dict.get('source_persona')}")

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["enhanced_context_management", "context_synchronization", "entity_tracking"])
