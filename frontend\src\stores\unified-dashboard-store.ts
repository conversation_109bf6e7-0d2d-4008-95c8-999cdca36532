/**
 * Unified Dashboard Store
 * 
 * Comprehensive state management for multi-dashboard functionality with backend synchronization.
 * Replaces multiple dashboard stores with a single source of truth.
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { dashboardCustomizationApi, dashboardManagementApi } from '@/lib/api';
import {
  Dashboard,
  DashboardCreate,
  DashboardUpdate,
  DashboardLayoutResponse,
  SectionResponse,
  WidgetResponse,
  SectionCreate,
  WidgetCreate,
  DashboardError,
  VisualizationType,
  DashboardDataSourceAssignment,
  DashboardDataSourceAssignmentCreate,
  DashboardDataSourceAssignmentUpdate
} from '@/types/dashboard-customization';

interface UnifiedDashboardState {
  // Dashboard management
  dashboards: Dashboard[];
  activeDashboardId: string | null;
  
  // Current dashboard layout
  currentLayout: DashboardLayoutResponse | null;
  
  // Loading and error states
  isLoading: boolean;
  error: DashboardError | null;
  
  // Optimistic updates tracking
  pendingOperations: Set<string>;
  
  // Actions - Dashboard management
  loadDashboards: () => Promise<void>;
  createDashboard: (dashboard: DashboardCreate) => Promise<Dashboard>;
  updateDashboard: (id: string, updates: DashboardUpdate) => Promise<Dashboard>;
  deleteDashboard: (id: string) => Promise<void>;
  setActiveDashboard: (id: string) => Promise<void>;
  
  // Actions - Layout management
  loadCurrentLayout: () => Promise<void>;
  
  // Actions - Section management
  createSection: (section: SectionCreate) => Promise<SectionResponse>;
  updateSection: (id: string, updates: any) => Promise<SectionResponse>;
  deleteSection: (id: string) => Promise<void>;
  duplicateSection: (id: string) => Promise<SectionResponse>;
  
  // Actions - Widget management
  createWidget: (widget: WidgetCreate) => Promise<WidgetResponse>;
  updateWidget: (id: string, updates: any) => Promise<WidgetResponse>;
  deleteWidget: (id: string) => Promise<void>;
  moveWidget: (widgetId: string, targetSectionId: string, position: any) => Promise<WidgetResponse>;

  // Actions - Data source management
  addDashboardDataSource: (dashboardId: string, dataSource: DashboardDataSourceAssignmentCreate) => Promise<DashboardDataSourceAssignment>;
  updateDashboardDataSource: (dashboardId: string, dataSourceId: string, updates: DashboardDataSourceAssignmentUpdate) => Promise<DashboardDataSourceAssignment>;
  deleteDashboardDataSource: (dashboardId: string, dataSourceId: string) => Promise<void>;
  refreshDashboardDataSources: (dashboardId: string) => Promise<void>;

  // Utility actions
  clearError: () => void;
  reset: () => void;
  clearAllData: () => void;
}

const initialState = {
  dashboards: [],
  activeDashboardId: null,
  currentLayout: null,
  isLoading: false,
  error: null,
  pendingOperations: new Set<string>(),
};

export const useUnifiedDashboardStore = create<UnifiedDashboardState>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Dashboard management actions
      loadDashboards: async () => {
        set({ isLoading: true, error: null });
        try {
          const dashboards = await dashboardManagementApi.getDashboards();

          // If no dashboards exist, automatically create a default dashboard with retry logic
          if (dashboards.length === 0) {
            let retryCount = 0;
            const maxRetries = 3;

            while (retryCount < maxRetries) {
              try {
                console.log(`Creating default dashboard (attempt ${retryCount + 1}/${maxRetries})...`);

                // Check if user has necessary permissions
                const userProfile = localStorage.getItem('userProfile');
                if (!userProfile) {
                  throw new Error('User profile not found. Please log in again.');
                }

                const defaultDashboard = await dashboardManagementApi.initializeDefaultDashboard();

                // Verify the dashboard was created successfully
                if (!defaultDashboard || !defaultDashboard.id) {
                  throw new Error('Dashboard creation returned invalid response');
                }

                set({
                  dashboards: [defaultDashboard],
                  activeDashboardId: defaultDashboard.id,
                  isLoading: false,
                  error: null
                });

                // Load the layout for the newly created dashboard with error handling
                try {
                  await get().loadCurrentLayout();
                } catch (layoutError) {
                  console.warn('Failed to load layout for new dashboard:', layoutError);
                  // Continue anyway - layout can be loaded later
                }

                console.log('Default dashboard created successfully');
                return;
              } catch (initError) {
                retryCount++;
                console.error(`Error creating default dashboard (attempt ${retryCount}):`, initError);

                if (retryCount >= maxRetries) {
                  // All retries failed - provide detailed error information
                  const errorMessage = initError instanceof Error ? initError.message : 'Unknown error occurred';
                  const isNetworkError = errorMessage.includes('fetch') || errorMessage.includes('network');
                  const isPermissionError = errorMessage.includes('permission') || errorMessage.includes('unauthorized');

                  let userFriendlyMessage = 'Failed to initialize dashboard. ';
                  if (isNetworkError) {
                    userFriendlyMessage += 'Please check your internet connection and try again.';
                  } else if (isPermissionError) {
                    userFriendlyMessage += 'You may not have permission to create dashboards. Please contact your administrator.';
                  } else {
                    userFriendlyMessage += 'Please try refreshing the page or creating a dashboard manually.';
                  }

                  set({
                    dashboards: [],
                    activeDashboardId: null,
                    isLoading: false,
                    currentLayout: null,
                    error: {
                      message: userFriendlyMessage,
                      details: errorMessage,
                      canRetry: true,
                      retryAction: 'loadDashboards'
                    }
                  });
                  return;
                } else {
                  // Wait before retrying (exponential backoff)
                  await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
                }
              }
            }
          }

          // Load data source assignments for each dashboard
          const dashboardsWithDataSources = await Promise.all(
            dashboards.map(async (dashboard) => {
              try {
                const dataSources = await dashboardManagementApi.getDashboardDataSources(dashboard.id);
                return {
                  ...dashboard,
                  data_source_assignments: dataSources
                };
              } catch (error) {
                console.warn(`Failed to load data sources for dashboard ${dashboard.id}:`, error);
                return {
                  ...dashboard,
                  data_source_assignments: []
                };
              }
            })
          );

          // Find default dashboard or use first one
          const defaultDashboard = dashboardsWithDataSources.find(d => d.is_default) || dashboardsWithDataSources[0];
          set({
            dashboards: dashboardsWithDataSources,
            activeDashboardId: defaultDashboard.id,
            isLoading: false
          });

          // Load the layout for the active dashboard
          await get().loadCurrentLayout();
        } catch (error) {
          console.error('Error loading dashboards:', error);
          set({
            error: {
              message: error instanceof Error ? error.message : 'Failed to load dashboards'
            },
            isLoading: false
          });
        }
      },

      createDashboard: async (dashboard: DashboardCreate) => {
        const operationId = `create-dashboard-${Date.now()}`;
        get().pendingOperations.add(operationId);

        try {
          const newDashboard = await dashboardManagementApi.createDashboard(dashboard);

          set(state => ({
            dashboards: [...state.dashboards, newDashboard]
          }));

          return newDashboard;
        } catch (error) {
          console.error('Error creating dashboard:', error);
          set({
            error: {
              message: error instanceof Error ? error.message : 'Failed to create dashboard'
            }
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      updateDashboard: async (id: string, updates: DashboardUpdate) => {
        const operationId = `update-dashboard-${id}`;
        get().pendingOperations.add(operationId);

        try {
          const updatedDashboard = await dashboardManagementApi.updateDashboard(id, updates);

          set(state => ({
            dashboards: state.dashboards.map(dashboard =>
              dashboard.id === id ? updatedDashboard : dashboard
            )
          }));

          return updatedDashboard;
        } catch (error) {
          console.error('Error updating dashboard:', error);
          set({
            error: {
              message: error instanceof Error ? error.message : 'Failed to update dashboard'
            }
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      deleteDashboard: async (id: string) => {
        const operationId = `delete-dashboard-${id}`;
        get().pendingOperations.add(operationId);

        try {
          await dashboardManagementApi.deleteDashboard(id);

          set(state => ({
            dashboards: state.dashboards.filter(dashboard => dashboard.id !== id),
            activeDashboardId: state.activeDashboardId === id ? null : state.activeDashboardId
          }));

          // If we deleted the active dashboard, set a new active one
          const { dashboards, activeDashboardId } = get();
          if (!activeDashboardId && dashboards.length > 0) {
            await get().setActiveDashboard(dashboards[0].id);
          }
        } catch (error) {
          console.error('Error deleting dashboard:', error);
          set({
            error: {
              message: error instanceof Error ? error.message : 'Failed to delete dashboard'
            }
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      setActiveDashboard: async (id: string) => {
        set({ activeDashboardId: id });
        await get().loadCurrentLayout();
      },

      // Layout management actions
      loadCurrentLayout: async () => {
        set({ isLoading: true, error: null });
        try {
          const layout = await dashboardCustomizationApi.getDashboardLayout();
          set({ currentLayout: layout, isLoading: false });
        } catch (error) {
          set({ 
            error: { 
              message: error instanceof Error ? error.message : 'Failed to load dashboard layout' 
            }, 
            isLoading: false 
          });
        }
      },

      // Section management actions
      createSection: async (section: SectionCreate) => {
        const operationId = `create-section-${Date.now()}`;
        get().pendingOperations.add(operationId);

        try {
          // Add dashboard_id if not provided
          const activeDashboardId = get().activeDashboardId;
          if (!activeDashboardId) {
            throw new Error('No active dashboard selected');
          }

          const sectionWithDashboardId = {
            ...section,
            dashboard_id: section.dashboard_id || activeDashboardId
          };

          const newSection = await dashboardCustomizationApi.createSection(sectionWithDashboardId);
          
          // Update current layout
          set(state => ({
            currentLayout: state.currentLayout ? {
              ...state.currentLayout,
              sections: [...state.currentLayout.sections, newSection],
              total_sections: state.currentLayout.total_sections + 1
            } : null
          }));
          
          return newSection;
        } catch (error) {
          set({ 
            error: { 
              message: error instanceof Error ? error.message : 'Failed to create section' 
            } 
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      updateSection: async (id: string, updates: any) => {
        const operationId = `update-section-${id}`;
        get().pendingOperations.add(operationId);
        
        try {
          const updatedSection = await dashboardCustomizationApi.updateSection(id, updates);
          
          // Update current layout
          set(state => ({
            currentLayout: state.currentLayout ? {
              ...state.currentLayout,
              sections: state.currentLayout.sections.map(section =>
                section.id === id ? updatedSection : section
              )
            } : null
          }));
          
          return updatedSection;
        } catch (error) {
          set({ 
            error: { 
              message: error instanceof Error ? error.message : 'Failed to update section' 
            } 
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      deleteSection: async (id: string) => {
        const operationId = `delete-section-${id}`;
        get().pendingOperations.add(operationId);
        
        try {
          await dashboardCustomizationApi.deleteSection(id);
          
          // Update current layout
          set(state => ({
            currentLayout: state.currentLayout ? {
              ...state.currentLayout,
              sections: state.currentLayout.sections.filter(section => section.id !== id),
              widgets: state.currentLayout.widgets.filter(widget => widget.section_id !== id),
              total_sections: state.currentLayout.total_sections - 1,
              total_widgets: state.currentLayout.widgets.filter(widget => widget.section_id !== id).length
            } : null
          }));
        } catch (error) {
          set({ 
            error: { 
              message: error instanceof Error ? error.message : 'Failed to delete section' 
            } 
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      duplicateSection: async (id: string) => {
        const operationId = `duplicate-section-${id}`;
        get().pendingOperations.add(operationId);
        
        try {
          const duplicatedSection = await dashboardCustomizationApi.duplicateSection(id);
          
          // Update current layout
          set(state => ({
            currentLayout: state.currentLayout ? {
              ...state.currentLayout,
              sections: [...state.currentLayout.sections, duplicatedSection],
              total_sections: state.currentLayout.total_sections + 1
            } : null
          }));
          
          return duplicatedSection;
        } catch (error) {
          set({ 
            error: { 
              message: error instanceof Error ? error.message : 'Failed to duplicate section' 
            } 
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      // Widget management actions
      createWidget: async (widget: WidgetCreate) => {
        const operationId = `create-widget-${Date.now()}`;
        get().pendingOperations.add(operationId);
        
        try {
          const newWidget = await dashboardCustomizationApi.createWidget(widget);
          
          // Update current layout
          set(state => ({
            currentLayout: state.currentLayout ? {
              ...state.currentLayout,
              widgets: [...state.currentLayout.widgets, newWidget],
              total_widgets: state.currentLayout.total_widgets + 1
            } : null
          }));
          
          return newWidget;
        } catch (error) {
          set({ 
            error: { 
              message: error instanceof Error ? error.message : 'Failed to create widget' 
            } 
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      updateWidget: async (id: string, updates: any) => {
        const operationId = `update-widget-${id}`;

        // Check if there's already a pending operation for this widget
        const existingOperation = Array.from(get().pendingOperations).find(op =>
          op.startsWith(`update-widget-${id}`) || op.startsWith(`refresh-widget-${id}`)
        );

        if (existingOperation) {
          console.warn(`Widget ${id} update skipped - operation already in progress: ${existingOperation}`);
          return get().currentLayout?.widgets.find(w => w.id === id);
        }

        get().pendingOperations.add(operationId);

        try {
          // Get current widget state for conflict detection
          const currentWidget = get().currentLayout?.widgets.find(w => w.id === id);
          if (!currentWidget) {
            throw new Error('Widget not found in current layout');
          }

          // Add timestamp to detect conflicts
          const updateWithTimestamp = {
            ...updates,
            last_modified: new Date().toISOString(),
            version: (currentWidget as any).version ? (currentWidget as any).version + 1 : 1
          };

          const updatedWidget = await dashboardCustomizationApi.updateWidget(id, updateWithTimestamp);

          // Update current layout with conflict resolution
          set(state => {
            if (!state.currentLayout) return state;

            const widgetIndex = state.currentLayout.widgets.findIndex(w => w.id === id);
            if (widgetIndex === -1) {
              console.warn(`Widget ${id} not found during update`);
              return state;
            }

            // Check for version conflicts
            const currentVersion = (state.currentLayout.widgets[widgetIndex] as any).version || 0;
            const updatedVersion = (updatedWidget as any).version || 0;

            if (updatedVersion < currentVersion) {
              console.warn(`Version conflict detected for widget ${id}. Skipping update.`);
              return state;
            }

            return {
              ...state,
              currentLayout: {
                ...state.currentLayout,
                widgets: state.currentLayout.widgets.map(widget =>
                  widget.id === id ? updatedWidget : widget
                )
              }
            };
          });

          return updatedWidget;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update widget';

          // Handle specific error types
          if (errorMessage.includes('conflict') || errorMessage.includes('version')) {
            // Reload the current layout to get the latest state
            console.warn(`Conflict detected for widget ${id}, reloading layout...`);
            await get().loadCurrentLayout();
          }

          set({
            error: {
              message: errorMessage,
              details: `Widget update failed: ${errorMessage}`,
              canRetry: true,
              retryAction: 'updateWidget',
              retryParams: { id, updates }
            }
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      deleteWidget: async (id: string) => {
        const operationId = `delete-widget-${id}`;
        get().pendingOperations.add(operationId);
        
        try {
          await dashboardCustomizationApi.deleteWidget(id);
          
          // Update current layout
          set(state => ({
            currentLayout: state.currentLayout ? {
              ...state.currentLayout,
              widgets: state.currentLayout.widgets.filter(widget => widget.id !== id),
              total_widgets: state.currentLayout.total_widgets - 1
            } : null
          }));
        } catch (error) {
          set({ 
            error: { 
              message: error instanceof Error ? error.message : 'Failed to delete widget' 
            } 
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      moveWidget: async (widgetId: string, targetSectionId: string, position: any) => {
        const operationId = `move-widget-${widgetId}`;
        get().pendingOperations.add(operationId);
        
        try {
          const movedWidget = await dashboardCustomizationApi.moveWidget({
            widget_id: widgetId,
            target_section_id: targetSectionId,
            position_config: position
          });
          
          // Update current layout
          set(state => ({
            currentLayout: state.currentLayout ? {
              ...state.currentLayout,
              widgets: state.currentLayout.widgets.map(widget =>
                widget.id === widgetId ? movedWidget : widget
              )
            } : null
          }));
          
          return movedWidget;
        } catch (error) {
          set({ 
            error: { 
              message: error instanceof Error ? error.message : 'Failed to move widget' 
            } 
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      // Data source management actions
      addDashboardDataSource: async (dashboardId: string, dataSource: DashboardDataSourceAssignmentCreate) => {
        const operationId = `add-datasource-${Date.now()}`;
        get().pendingOperations.add(operationId);

        try {
          const newDataSource = await dashboardManagementApi.addDashboardDataSource(dashboardId, dataSource);

          // Update dashboard with new data source assignment in local state
          set(state => ({
            dashboards: state.dashboards.map(dashboard =>
              dashboard.id === dashboardId
                ? {
                    ...dashboard,
                    data_source_assignments: [...(dashboard.data_source_assignments || []), newDataSource],
                    updated_at: new Date().toISOString()
                  }
                : dashboard
            )
          }));

          return newDataSource;
        } catch (error) {
          console.error('Error adding dashboard data source:', error);
          set({
            error: {
              message: error instanceof Error ? error.message : 'Failed to add data source'
            }
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      updateDashboardDataSource: async (dashboardId: string, dataSourceId: string, updates: DashboardDataSourceAssignmentUpdate) => {
        const operationId = `update-datasource-${dataSourceId}`;
        get().pendingOperations.add(operationId);

        try {
          const updatedDataSource = await dashboardManagementApi.updateDashboardDataSource(
            dashboardId,
            dataSourceId,
            updates
          );

          // Update dashboard data source assignment in local state
          set(state => ({
            dashboards: state.dashboards.map(dashboard =>
              dashboard.id === dashboardId
                ? {
                    ...dashboard,
                    data_source_assignments: dashboard.data_source_assignments?.map(ds =>
                      ds.id === dataSourceId ? updatedDataSource : ds
                    ),
                    updated_at: new Date().toISOString()
                  }
                : dashboard
            )
          }));

          return updatedDataSource;
        } catch (error) {
          console.error('Error updating dashboard data source:', error);
          set({
            error: {
              message: error instanceof Error ? error.message : 'Failed to update data source'
            }
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      deleteDashboardDataSource: async (dashboardId: string, dataSourceId: string) => {
        const operationId = `delete-datasource-${dataSourceId}`;
        get().pendingOperations.add(operationId);

        try {
          await dashboardManagementApi.removeDashboardDataSource(dashboardId, dataSourceId);

          // Remove data source from dashboard in local state
          set(state => ({
            dashboards: state.dashboards.map(dashboard =>
              dashboard.id === dashboardId
                ? {
                    ...dashboard,
                    data_source_assignments: dashboard.data_source_assignments?.filter(ds => ds.id !== dataSourceId),
                    updated_at: new Date().toISOString()
                  }
                : dashboard
            )
          }));
        } catch (error) {
          console.error('Error deleting dashboard data source:', error);
          set({
            error: {
              message: error instanceof Error ? error.message : 'Failed to delete data source'
            }
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      refreshDashboardDataSources: async (dashboardId: string) => {
        const operationId = `refresh-datasources-${dashboardId}`;
        get().pendingOperations.add(operationId);

        try {
          const dataSources = await dashboardManagementApi.getDashboardDataSources(dashboardId);

          // Update dashboard data source assignments in local state
          set(state => ({
            dashboards: state.dashboards.map(dashboard =>
              dashboard.id === dashboardId
                ? {
                    ...dashboard,
                    data_source_assignments: dataSources, // Use the correct field name
                    updated_at: new Date().toISOString()
                  }
                : dashboard
            )
          }));
        } catch (error) {
          console.error('Error refreshing dashboard data sources:', error);
          set({
            error: {
              message: error instanceof Error ? error.message : 'Failed to refresh data sources'
            }
          });
          throw error;
        } finally {
          get().pendingOperations.delete(operationId);
        }
      },

      // Utility actions
      clearError: () => set({ error: null }),

      reset: () => set(initialState),

      clearAllData: () => {
        console.log('🧹 Clearing all dashboard data from store...');
        set({
          dashboards: [],
          activeDashboardId: null,
          currentLayout: null,
          isLoading: false,
          error: null,
          pendingOperations: new Set<string>(),
        });
        console.log('✅ Dashboard store data cleared');
      },
    }),
    {
      name: 'unified-dashboard-store',
      partialize: (state) => ({
        dashboards: state.dashboards,
        activeDashboardId: state.activeDashboardId,
      }),
    }
  )
);
