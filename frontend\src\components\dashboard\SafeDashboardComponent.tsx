import React from 'react';
import { DashboardErrorBoundary } from './DashboardErrorBoundary';

interface SafeDashboardComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  componentName?: string;
}

/**
 * A wrapper component that safely renders dashboard components
 * with error boundaries and fallbacks for authentication issues
 */
export const SafeDashboardComponent: React.FC<SafeDashboardComponentProps> = ({
  children,
  fallback,
  componentName = 'Component'
}) => {
  const defaultFallback = (
    <span className="text-xs text-slate-500 px-2 py-1 bg-slate-50 rounded border">
      {componentName}
    </span>
  );

  return (
    <DashboardErrorBoundary fallback={fallback || defaultFallback}>
      {children}
    </DashboardErrorBoundary>
  );
};

export default SafeDashboardComponent;
