import React, { useState, useCallback } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  MoreVertical, 
  Edit, 
  Trash2, 
  Move, 
  Copy, 
  Settings,
  RefreshCw,
  GripVertical,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { WidgetResponse, SectionResponse } from '@/types/dashboard-customization';
import { DynamicWidget } from './DynamicWidget';
import { useToast } from '@/hooks/use-toast';

interface DragDropWidgetGridProps {
  widgets: WidgetResponse[];
  sections: SectionResponse[];
  onWidgetUpdate: (id: string, updates: Partial<WidgetResponse>) => void;
  onWidgetRemove: (id: string) => void;
  onWidgetMove: (widgetId: string, targetSectionId: string, newPosition: number) => void;
  onWidgetDuplicate?: (widget: WidgetResponse) => void;
  onWidgetCustomize?: (widget: WidgetResponse) => void;
  onWidgetResize?: (widgetId: string, newSize: { w: number; h: number }) => void;
  onWidgetRefresh?: (widgetId: string) => void;
  currentSectionId: string;
  isEditing?: boolean;
}

interface WidgetWithPosition extends WidgetResponse {
  dragId: string;
  position: number;
}

export const DragDropWidgetGrid: React.FC<DragDropWidgetGridProps> = ({
  widgets,
  sections,
  onWidgetUpdate,
  onWidgetRemove,
  onWidgetMove,
  onWidgetDuplicate,
  onWidgetCustomize,
  onWidgetResize,
  onWidgetRefresh,
  currentSectionId,
  isEditing = false,
}) => {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [widgetToDelete, setWidgetToDelete] = useState<WidgetResponse | null>(null);
  const [isDeletingWidget, setIsDeletingWidget] = useState(false);
  const [expandedWidgets, setExpandedWidgets] = useState<Set<string>>(new Set());

  // Prepare widgets with drag IDs and positions
  const widgetsWithPosition: WidgetWithPosition[] = widgets
    .filter(widget => widget.section_id === currentSectionId)
    .map((widget, index) => ({
      ...widget,
      dragId: `widget-${widget.id}`,
      position: index,
    }))
    .sort((a, b) => (a.position_config?.y || 0) - (b.position_config?.y || 0));

  // Handle drag end
  const handleDragEnd = useCallback((result: DropResult) => {
    const { destination, source, draggableId } = result;

    // If dropped outside a droppable area
    if (!destination) {
      return;
    }

    // If dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const widgetId = draggableId.replace('widget-', '');
    const targetSectionId = destination.droppableId.replace('section-', '');

    // Handle move to different section or reorder within same section
    onWidgetMove(widgetId, targetSectionId, destination.index);

    toast({
      title: "Widget Moved",
      description: "Widget has been moved successfully.",
    });
  }, [onWidgetMove, toast]);

  // Handle widget deletion
  const handleDeleteWidget = useCallback((widget: WidgetResponse) => {
    setWidgetToDelete(widget);
    setDeleteDialogOpen(true);
  }, []);

  const confirmDeleteWidget = useCallback(async () => {
    if (!widgetToDelete) return;

    setIsDeletingWidget(true);
    try {
      await onWidgetRemove(widgetToDelete.id);
      setDeleteDialogOpen(false);
      setWidgetToDelete(null);
      toast({
        title: "Widget Deleted",
        description: `"${widgetToDelete.title}" has been removed from the dashboard.`,
      });
    } catch (error) {
      console.error('Failed to delete widget:', error);
      toast({
        title: "Failed to Delete Widget",
        description: error instanceof Error ? error.message : "An unexpected error occurred while deleting the widget.",
        variant: "destructive",
      });
    } finally {
      setIsDeletingWidget(false);
    }
  }, [widgetToDelete, onWidgetRemove, toast]);

  // Handle widget duplication
  const handleDuplicateWidget = useCallback((widget: WidgetResponse) => {
    if (onWidgetDuplicate) {
      onWidgetDuplicate(widget);
      toast({
        title: "Widget Duplicated",
        description: "Widget has been duplicated successfully.",
      });
    }
  }, [onWidgetDuplicate, toast]);

  // Handle widget expansion/collapse
  const toggleWidgetExpansion = useCallback((widgetId: string) => {
    setExpandedWidgets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(widgetId)) {
        newSet.delete(widgetId);
      } else {
        newSet.add(widgetId);
      }
      return newSet;
    });
  }, []);

  // Handle widget resize
  const handleWidgetResize = useCallback((widgetId: string, direction: 'expand' | 'shrink') => {
    if (!onWidgetResize) return;

    const widget = widgets.find(w => w.id === widgetId);
    if (!widget?.position_config) return;

    const currentW = widget.position_config.w || 4;
    const currentH = widget.position_config.h || 3;

    let newW = currentW;
    let newH = currentH;

    if (direction === 'expand') {
      newW = Math.min(currentW + 1, 12);
      newH = Math.min(currentH + 1, 8);
    } else {
      newW = Math.max(currentW - 1, 2);
      newH = Math.max(currentH - 1, 2);
    }

    onWidgetResize(widgetId, { w: newW, h: newH });
  }, [widgets, onWidgetResize]);

  return (
    <div>
      <DragDropContext onDragEnd={handleDragEnd}>
        {/* Current Section Widgets */}
        <Droppable droppableId={`section-${currentSectionId}`} type="widget">
          {(provided, snapshot) => (
            <div
              ref={provided?.innerRef}
              {...(provided?.droppableProps || {})}
              className={`min-h-[200px] rounded-lg border-2 border-dashed transition-colors ${
                snapshot?.isDraggingOver
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground/20'
              }`}
            >
              {widgetsWithPosition.length === 0 ? (
                <div className="flex items-center justify-center h-32 text-muted-foreground">
                  <div className="text-center">
                    <Move className="h-8 w-8 mx-auto mb-2" />
                    <p>No widgets in this section</p>
                    <p className="text-sm">Drag widgets here or add new ones</p>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 px-4">
                  {widgetsWithPosition.map((widget, index) => (
                    <Draggable
                      key={widget.dragId}
                      draggableId={widget.dragId}
                      index={index}
                      isDragDisabled={!isEditing}
                    >
                      {(provided, snapshot) => (
                        <div
                          ref={provided?.innerRef}
                          {...(provided?.draggableProps || {})}
                          className={`relative transition-transform ${
                            snapshot?.isDragging ? 'rotate-2 scale-105' : ''
                          }`}
                          style={{
                            ...(provided?.draggableProps?.style || {}),
                            gridColumn: `span ${Math.min(widget.position_config?.w || 4, 4)}`,
                            gridRow: `span ${Math.min(widget.position_config?.h || 3, 3)}`,
                          }}
                        >
                          <Card className={`h-full ${snapshot?.isDragging ? 'shadow-lg' : ''}`}>
                            <CardHeader className="pb-2">
                              <div className="flex items-center justify-between">
                                <CardTitle className="text-sm truncate flex-1">
                                  {widget.title}
                                </CardTitle>
                                <div className="flex items-center space-x-1">
                                  {isEditing && (
                                    <div
                                      {...(provided?.dragHandleProps || {})}
                                      className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded"
                                    >
                                      <GripVertical className="h-4 w-4 text-muted-foreground" />
                                    </div>
                                  )}
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                        <MoreVertical className="h-3 w-3" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      {onWidgetCustomize && (
                                        <DropdownMenuItem onClick={() => onWidgetCustomize(widget)}>
                                          <Edit className="h-4 w-4 mr-2" />
                                          Edit
                                        </DropdownMenuItem>
                                      )}
                                      {onWidgetDuplicate && (
                                        <DropdownMenuItem onClick={() => handleDuplicateWidget(widget)}>
                                          <Copy className="h-4 w-4 mr-2" />
                                          Duplicate
                                        </DropdownMenuItem>
                                      )}
                                      {onWidgetResize && (
                                        <>
                                          <DropdownMenuItem onClick={() => handleWidgetResize(widget.id, 'expand')}>
                                            <Maximize2 className="h-4 w-4 mr-2" />
                                            Expand
                                          </DropdownMenuItem>
                                          <DropdownMenuItem onClick={() => handleWidgetResize(widget.id, 'shrink')}>
                                            <Minimize2 className="h-4 w-4 mr-2" />
                                            Shrink
                                          </DropdownMenuItem>
                                        </>
                                      )}
                                      <DropdownMenuItem onClick={() => toggleWidgetExpansion(widget.id)}>
                                        {expandedWidgets.has(widget.id) ? (
                                          <>
                                            <Minimize2 className="h-4 w-4 mr-2" />
                                            Collapse
                                          </>
                                        ) : (
                                          <>
                                            <Maximize2 className="h-4 w-4 mr-2" />
                                            Expand View
                                          </>
                                        )}
                                      </DropdownMenuItem>
                                      {onWidgetRefresh && (
                                        <DropdownMenuItem onClick={() => onWidgetRefresh(widget.id)}>
                                          <RefreshCw className="h-4 w-4 mr-2" />
                                          Refresh Data
                                        </DropdownMenuItem>
                                      )}
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem 
                                        onClick={() => handleDeleteWidget(widget)}
                                        className="text-destructive"
                                      >
                                        <Trash2 className="h-4 w-4 mr-2" />
                                        Delete
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline" className="text-xs">
                                  {widget.widget_type}
                                </Badge>
                                {widget.refresh_interval && (
                                  <Badge variant="secondary" className="text-xs">
                                    {Math.floor(widget.refresh_interval / 60)}m
                                  </Badge>
                                )}
                              </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <div className={`transition-all duration-300 ${
                                expandedWidgets.has(widget.id) ? 'h-96' : 'h-48'
                              }`}>
                                <DynamicWidget
                                  widget={widget}
                                  onRemove={() => handleDeleteWidget(widget)}
                                  onUpdate={onWidgetUpdate ? (updates) => onWidgetUpdate(widget.id, updates) : undefined}
                                  onCustomize={onWidgetCustomize}
                                />
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      )}
                    </Draggable>
                  ))}
                </div>
              )}
              {provided?.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Widget</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{widgetToDelete?.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeletingWidget}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteWidget}
              disabled={isDeletingWidget}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeletingWidget ? 'Deleting...' : 'Delete Widget'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
