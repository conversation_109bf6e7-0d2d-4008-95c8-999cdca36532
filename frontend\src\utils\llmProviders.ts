/**
 * LLM provider interface and implementations.
 */

export interface LLMProviderOptions {
  /** API key for the provider */
  apiKey?: string;
  /** Base URL for the provider */
  baseUrl?: string;
  /** Model to use */
  model?: string;
  /** Maximum number of tokens to generate */
  maxTokens?: number;
  /** Temperature for generation */
  temperature?: number;
  /** Whether to stream the response */
  stream?: boolean;
  /** Additional provider-specific options */
  [key: string]: any;
}

export interface LLMMessage {
  /** Role of the message sender */
  role: 'system' | 'user' | 'assistant';
  /** Content of the message */
  content: string;
  /** Additional message metadata */
  metadata?: any;
}

export interface LLMResponse {
  /** Generated text */
  text: string;
  /** Usage information */
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  /** Additional response metadata */
  metadata?: any;
}

export interface LLMProvider {
  /** Name of the provider */
  name: string;
  /** Generate a completion */
  generateCompletion(messages: LLMMessage[], options?: LLMProviderOptions): Promise<LLMResponse>;
  /** Stream a completion */
  streamCompletion?(messages: LLMMessage[], options?: LLMProviderOptions): AsyncGenerator<string, void, unknown>;
  /** Check if the provider is available */
  isAvailable(): Promise<boolean>;
}

/**
 * OpenAI provider implementation.
 */
export class OpenAIProvider implements LLMProvider {
  public name = 'openai';
  private apiKey: string;
  private baseUrl: string;
  private defaultModel: string;

  constructor(options: LLMProviderOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.baseUrl = options.baseUrl || 'https://api.openai.com/v1';
    this.defaultModel = options.model || 'gpt-3.5-turbo';
  }

  async generateCompletion(messages: LLMMessage[], options: LLMProviderOptions = {}): Promise<LLMResponse> {
    const model = options.model || this.defaultModel;
    const temperature = options.temperature ?? 0.7;
    const maxTokens = options.maxTokens ?? 1000;

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({
        model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        temperature,
        max_tokens: maxTokens,
        stream: false,
      }),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
      throw new Error(error.error?.message || 'Failed to generate completion');
    }

    const data = await response.json();
    
    return {
      text: data.choices[0].message.content,
      usage: {
        promptTokens: data.usage.prompt_tokens,
        completionTokens: data.usage.completion_tokens,
        totalTokens: data.usage.total_tokens,
      },
      metadata: {
        model: data.model,
        provider: this.name,
      },
    };
  }

  async *streamCompletion(messages: LLMMessage[], options: LLMProviderOptions = {}): AsyncGenerator<string, void, unknown> {
    const model = options.model || this.defaultModel;
    const temperature = options.temperature ?? 0.7;
    const maxTokens = options.maxTokens ?? 1000;

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'Accept': 'text/event-stream',
      },
      body: JSON.stringify({
        model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        temperature,
        max_tokens: maxTokens,
        stream: true,
      }),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
      throw new Error(error.error?.message || 'Failed to generate completion');
    }

    if (!response.body) {
      throw new Error('Response body is null');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n').filter(line => line.trim() !== '');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') break;
            
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              if (content) {
                yield content;
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiKey) return false;
    
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      
      return response.ok;
    } catch (error) {
      console.error('Error checking OpenAI availability:', error);
      return false;
    }
  }
}

/**
 * Anthropic provider implementation.
 */
export class AnthropicProvider implements LLMProvider {
  public name = 'anthropic';
  private apiKey: string;
  private baseUrl: string;
  private defaultModel: string;

  constructor(options: LLMProviderOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.baseUrl = options.baseUrl || 'https://api.anthropic.com/v1';
    this.defaultModel = options.model || 'claude-2';
  }

  async generateCompletion(messages: LLMMessage[], options: LLMProviderOptions = {}): Promise<LLMResponse> {
    const model = options.model || this.defaultModel;
    const temperature = options.temperature ?? 0.7;
    const maxTokens = options.maxTokens ?? 1000;

    // Convert messages to Anthropic format
    const prompt = this.convertMessagesToPrompt(messages);

    const response = await fetch(`${this.baseUrl}/complete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model,
        prompt,
        max_tokens_to_sample: maxTokens,
        temperature,
        stream: false,
      }),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(error.error || 'Failed to generate completion');
    }

    const data = await response.json();
    
    return {
      text: data.completion,
      metadata: {
        model: data.model,
        provider: this.name,
      },
    };
  }

  private convertMessagesToPrompt(messages: LLMMessage[]): string {
    let prompt = '';
    
    for (const message of messages) {
      if (message.role === 'system') {
        prompt += `\n\nHuman: <system>${message.content}</system>\n\nAssistant: I'll follow these instructions.`;
      } else if (message.role === 'user') {
        prompt += `\n\nHuman: ${message.content}`;
      } else if (message.role === 'assistant') {
        prompt += `\n\nAssistant: ${message.content}`;
      }
    }
    
    // Add the final assistant prompt
    prompt += '\n\nAssistant:';
    
    return prompt;
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiKey) return false;
    
    try {
      // Anthropic doesn't have a simple endpoint to check availability,
      // so we'll just check if the API key is set
      return true;
    } catch (error) {
      console.error('Error checking Anthropic availability:', error);
      return false;
    }
  }
}

/**
 * LLM provider manager for handling multiple providers with fallback.
 */
export class LLMProviderManager {
  private static instance: LLMProviderManager;
  private providers: LLMProvider[] = [];
  private defaultProvider: LLMProvider | null = null;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance of the LLM provider manager.
   */
  public static getInstance(): LLMProviderManager {
    if (!LLMProviderManager.instance) {
      LLMProviderManager.instance = new LLMProviderManager();
    }
    return LLMProviderManager.instance;
  }

  /**
   * Register a provider with the manager.
   * @param provider The provider to register
   * @param isDefault Whether this provider should be the default
   */
  public registerProvider(provider: LLMProvider, isDefault: boolean = false): void {
    this.providers.push(provider);
    
    if (isDefault || !this.defaultProvider) {
      this.defaultProvider = provider;
    }
  }

  /**
   * Get a provider by name.
   * @param name The name of the provider
   * @returns The provider or null if not found
   */
  public getProvider(name: string): LLMProvider | null {
    return this.providers.find(p => p.name === name) || null;
  }

  /**
   * Get the default provider.
   * @returns The default provider or null if none is set
   */
  public getDefaultProvider(): LLMProvider | null {
    return this.defaultProvider;
  }

  /**
   * Generate a completion using the specified provider or fallback to others if it fails.
   * @param providerName The name of the provider to use (or null for default)
   * @param messages The messages to generate a completion for
   * @param options Options for the provider
   * @returns The generated completion
   */
  public async generateCompletion(
    providerName: string | null,
    messages: LLMMessage[],
    options: LLMProviderOptions = {}
  ): Promise<LLMResponse> {
    // Get the specified provider or default
    let provider = providerName
      ? this.getProvider(providerName)
      : this.defaultProvider;
    
    if (!provider) {
      throw new Error('No provider available');
    }
    
    try {
      // Try the specified provider
      return await provider.generateCompletion(messages, options);
    } catch (error) {
      console.error(`Error with provider ${provider.name}:`, error);
      
      // Try fallback providers
      for (const fallbackProvider of this.providers) {
        if (fallbackProvider !== provider) {
          try {
            console.log(`Trying fallback provider: ${fallbackProvider.name}`);
            return await fallbackProvider.generateCompletion(messages, options);
          } catch (fallbackError) {
            console.error(`Error with fallback provider ${fallbackProvider.name}:`, fallbackError);
          }
        }
      }
      
      // If all providers fail, throw the original error
      throw error;
    }
  }

  /**
   * Stream a completion using the specified provider.
   * @param providerName The name of the provider to use (or null for default)
   * @param messages The messages to generate a completion for
   * @param options Options for the provider
   * @returns An async generator that yields chunks of the completion
   */
  public async *streamCompletion(
    providerName: string | null,
    messages: LLMMessage[],
    options: LLMProviderOptions = {}
  ): AsyncGenerator<string, void, unknown> {
    // Get the specified provider or default
    let provider = providerName
      ? this.getProvider(providerName)
      : this.defaultProvider;
    
    if (!provider) {
      throw new Error('No provider available');
    }
    
    if (!provider.streamCompletion) {
      throw new Error(`Provider ${provider.name} does not support streaming`);
    }
    
    try {
      // Try the specified provider
      for await (const chunk of provider.streamCompletion(messages, options)) {
        yield chunk;
      }
    } catch (error) {
      console.error(`Error with provider ${provider.name}:`, error);
      
      // Try fallback providers
      for (const fallbackProvider of this.providers) {
        if (fallbackProvider !== provider && fallbackProvider.streamCompletion) {
          try {
            console.log(`Trying fallback provider: ${fallbackProvider.name}`);
            for await (const chunk of fallbackProvider.streamCompletion(messages, options)) {
              yield chunk;
            }
            return;
          } catch (fallbackError) {
            console.error(`Error with fallback provider ${fallbackProvider.name}:`, fallbackError);
          }
        }
      }
      
      // If all providers fail, throw the original error
      throw error;
    }
  }

  /**
   * Check which providers are available.
   * @returns An object mapping provider names to availability
   */
  public async checkAvailability(): Promise<Record<string, boolean>> {
    const availability: Record<string, boolean> = {};
    
    for (const provider of this.providers) {
      try {
        availability[provider.name] = await provider.isAvailable();
      } catch (error) {
        console.error(`Error checking availability for ${provider.name}:`, error);
        availability[provider.name] = false;
      }
    }
    
    return availability;
  }
}

// Export a singleton instance
export const llmProviderManager = LLMProviderManager.getInstance();
