import logging
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, case

# Assuming User, Persona models are accessible for relationship or direct query
from ..database import Persona as PersonaModel # Changed
# For now, using the placeholders defined in feedback.py for FKs.
from ..models.feedback import UserFeedback, FeedbackSurvey, SurveyResponse, FeedbackAnalytics, NPSAnalytics, FeedbackAlert # Changed
# We'll need a db session, typically via Depends(get_db) in API layer, passed to service methods
# from backend.app.database import get_db # Placeholder for db session dependency

logger = logging.getLogger(__name__)

class FeedbackService:
    def __init__(self, db: Session): # db session will be injected
        self.db = db

    def _analyze_sentiment(self, text: Optional[str]) -> Optional[str]:
        """Enhanced sentiment analysis with weighted keywords and context awareness."""
        if not text:
            return None

        text_lower = text.lower()

        # Enhanced weighted keyword analysis
        positive_keywords = {
            'excellent': 3, 'amazing': 3, 'outstanding': 3, 'perfect': 3, 'brilliant': 3,
            'great': 2, 'good': 2, 'helpful': 2, 'useful': 2, 'love': 2, 'awesome': 2, 'fantastic': 2,
            'nice': 1, 'ok': 1, 'fine': 1, 'decent': 1, 'satisfactory': 1, 'pleased': 1
        }

        negative_keywords = {
            'terrible': 3, 'awful': 3, 'horrible': 3, 'worst': 3, 'hate': 3, 'disgusting': 3,
            'bad': 2, 'poor': 2, 'disappointing': 2, 'useless': 2, 'frustrating': 2, 'annoying': 2,
            'slow': 1, 'confusing': 1, 'unclear': 1, 'difficult': 1, 'boring': 1, 'meh': 1
        }

        # Calculate weighted sentiment scores
        positive_score = sum(weight for word, weight in positive_keywords.items() if word in text_lower)
        negative_score = sum(weight for word, weight in negative_keywords.items() if word in text_lower)

        # Check for negations and intensifiers
        negation_words = ['not', 'no', 'never', 'nothing', 'nowhere', 'neither', 'nobody', "don't", "doesn't", "didn't", "won't", "can't", "couldn't"]
        intensifiers = ['very', 'really', 'extremely', 'incredibly', 'absolutely', 'totally', 'completely']

        has_negation = any(neg in text_lower for neg in negation_words)
        has_intensifier = any(intensifier in text_lower for intensifier in intensifiers)

        # Apply negation logic (simple approach)
        if has_negation:
            # Flip the scores if negation is present
            positive_score, negative_score = negative_score, positive_score

        # Apply intensifier boost
        if has_intensifier:
            positive_score *= 1.5
            negative_score *= 1.5

        # Check for specific patterns
        if any(phrase in text_lower for phrase in ['could be better', 'needs improvement', 'not satisfied']):
            negative_score += 2

        if any(phrase in text_lower for phrase in ['highly recommend', 'exceeded expectations', 'very satisfied']):
            positive_score += 2

        # Determine sentiment based on weighted scores with threshold
        score_diff = abs(positive_score - negative_score)

        if positive_score > negative_score and score_diff >= 1:
            return "positive"
        elif negative_score > positive_score and score_diff >= 1:
            return "negative"
        else:
            # For ambiguous cases, check text length and structure
            words = text_lower.split()
            if len(words) < 3:
                return "neutral"  # Very short feedback is likely neutral

            # Check for question marks (often neutral inquiries)
            if '?' in text:
                return "neutral"

            return "neutral"

    def _calculate_sentiment_from_rating(self, rating: int) -> str:
        if rating >= 4:
            return "positive"
        if rating <= 2:
            return "negative"
        return "neutral"

    async def save_feedback(self, feedback: UserFeedback) -> UserFeedback:
        """Saves a UserFeedback object to the database."""
        feedback.id = str(uuid.uuid4()) # Ensure ID is generated if not already
        self.db.add(feedback)
        self.db.commit()
        self.db.refresh(feedback)
        logger.info(f"Feedback saved: ID {feedback.id}, Type {feedback.feedback_type}")
        return feedback

    async def collect_message_feedback(self, user_id: int, message_id: str,
                                     rating: int, feedback_text: Optional[str] = None,
                                     conversation_id: Optional[str] = None,
                                     persona_id: Optional[str] = None) -> UserFeedback:
        """Collect feedback for a specific AI message."""
        feedback = UserFeedback(
            user_id=user_id,
            message_id=message_id,
            conversation_id=conversation_id,
            persona_id=persona_id,
            feedback_type="message_rating",
            rating=rating,
            feedback_text=feedback_text,
            sentiment=self._analyze_sentiment(feedback_text) if feedback_text else self._calculate_sentiment_from_rating(rating)
        )
        return await self.save_feedback(feedback)

    async def collect_conversation_feedback(self, user_id: int, conversation_id: str,
                                          rating: int, feedback_tags: Optional[List[str]] = None,
                                          feedback_text: Optional[str] = None,
                                          persona_id: Optional[str] = None) -> UserFeedback:
        """Collect feedback for an entire conversation."""
        feedback = UserFeedback(
            user_id=user_id,
            conversation_id=conversation_id,
            persona_id=persona_id,
            feedback_type="conversation_rating",
            rating=rating,
            feedback_text=feedback_text,
            feedback_tags=feedback_tags or [],
            sentiment=self._analyze_sentiment(feedback_text) if feedback_text else self._calculate_sentiment_from_rating(rating)
        )
        return await self.save_feedback(feedback)

    async def update_persona_rating(self, persona_id: str):
        """Updates a persona's average rating and review count based on feedback."""
        logger.info(f"Updating persona rating for persona_id: {persona_id}")

        avg_rating_query = self.db.query(func.avg(UserFeedback.rating)).filter(
            UserFeedback.persona_id == persona_id,
            UserFeedback.feedback_type == "persona_review",
            UserFeedback.rating.isnot(None) # Ensure only rated feedback is considered
        )
        avg_rating = avg_rating_query.scalar()

        review_count_query = self.db.query(func.count(UserFeedback.id)).filter(
            UserFeedback.persona_id == persona_id,
            UserFeedback.feedback_type == "persona_review"
        )
        review_count = review_count_query.scalar()

        persona = self.db.query(PersonaModel).filter(PersonaModel.id == persona_id).first()
        if persona:
            persona.rating = round(avg_rating, 2) if avg_rating is not None else 0.0
            persona.review_count = review_count if review_count is not None else 0
            persona.updated_at = datetime.utcnow() # Assuming PersonaModel has updated_at
            self.db.commit()
            self.db.refresh(persona)
            logger.info(f"Persona {persona_id} rating updated: Avg Rating={persona.rating}, Review Count={persona.review_count}")
        else:
            logger.warning(f"Persona with ID {persona_id} not found for rating update.")

    async def collect_persona_review(self, user_id: int, persona_id: str,
                                   rating: int, review_text: str) -> UserFeedback:
        """Collect a review for a persona."""
        feedback = UserFeedback(
            user_id=user_id,
            persona_id=persona_id,
            feedback_type="persona_review",
            rating=rating,
            feedback_text=review_text,
            sentiment=self._analyze_sentiment(review_text)
        )
        saved_feedback = await self.save_feedback(feedback)
        await self.update_persona_rating(persona_id) # Trigger update
        return saved_feedback

    async def create_survey(self, survey_data: Dict[str, Any]) -> FeedbackSurvey:
        """Creates a new feedback survey."""
        survey = FeedbackSurvey(
            id=str(uuid.uuid4()),
            title=survey_data["title"],
            description=survey_data.get("description"),
            survey_type=survey_data["survey_type"],
            questions=survey_data["questions"],
            target_audience=survey_data.get("target_audience"),
            is_active=survey_data.get("is_active", True),
            expires_at=survey_data.get("expires_at")
        )
        self.db.add(survey)
        self.db.commit()
        self.db.refresh(survey)
        logger.info(f"Survey created: ID {survey.id}, Title {survey.title}")
        return survey

    async def create_satisfaction_survey(self, trigger_event: str, user_id: int) -> Optional[FeedbackSurvey]:
        """Create targeted satisfaction surveys based on user behavior or event."""
        survey_configs = {
            "post_conversation": {
                "title": "How was your AI conversation?",
                "survey_type": "satisfaction",
                "questions": [
                    {"id": "q1", "type": "rating", "text": "How satisfied were you with the AI's responses?", "scale": 5},
                    {"id": "q2", "type": "multiple_choice", "text": "What did you like most?", "options": ["Accuracy", "Speed", "Helpfulness", "Clarity"]},
                    {"id": "q3", "type": "text", "text": "How can we improve your experience?"}
                ],
                "target_audience": {"user_id": user_id} # Example targeting
            },
            "weekly_nps": {
                "title": "Net Promoter Score Survey",
                "survey_type": "nps",
                "questions": [
                    {"id": "q1", "type": "nps", "text": "How likely are you to recommend Datagenius to a colleague?"}, # Scale 0-10
                    {"id": "q2", "type": "text", "text": "What's the main reason for your score?"}
                ],
                "target_audience": {"user_segment": "active_weekly"} # Example targeting
            }
        }
        if trigger_event in survey_configs:
            return await self.create_survey(survey_configs[trigger_event])
        logger.warning(f"No survey config found for trigger_event: {trigger_event}")
        return None

    async def submit_survey_response(self, survey_id: str, user_id: int, responses: Dict[str, Any], completion_time_seconds: Optional[int] = None) -> SurveyResponse:
        """Submits a response for a survey."""
        response = SurveyResponse(
            id=str(uuid.uuid4()),
            survey_id=survey_id,
            user_id=user_id,
            responses=responses,
            completion_time_seconds=completion_time_seconds,
            is_complete=True, # Assume complete on submission
            completed_at=datetime.utcnow()
        )
        self.db.add(response)
        self.db.commit()
        self.db.refresh(response)
        logger.info(f"Survey response submitted: ID {response.id} for Survey ID {survey_id} by User ID {user_id}")
        return response

    async def get_feedback_by_period(self, time_period: str = "week") -> List[UserFeedback]:
        """Helper to get feedback data for a given period (dummy)."""
        # In real app, query DB with date filters
        end_date = datetime.utcnow()
        if time_period == "week":
            start_date = end_date - timedelta(days=7)
        elif time_period == "month":
            start_date = end_date - timedelta(days=30)
        else: # day
            start_date = end_date - timedelta(days=1)

        return self.db.query(UserFeedback).filter(UserFeedback.created_at >= start_date).all()


    async def analyze_feedback_trends(self, time_period: str = "week") -> FeedbackAnalytics:
        """Analyze feedback trends and generate insights."""
        feedback_data = await self.get_feedback_by_period(time_period)

        avg_rating = None
        ratings = [f.rating for f in feedback_data if f.rating is not None]
        if ratings:
            avg_rating = round(sum(ratings) / len(ratings), 2)

        sentiments = [f.sentiment for f in feedback_data if f.sentiment]
        sentiment_dist = {s: sentiments.count(s) for s in set(sentiments)}

        # Extract common issues and suggestions from negative feedback
        common_issues_map: Dict[str, int] = {}
        improvement_suggestions_list: List[str] = []

        negative_feedback_texts = [fb.feedback_text for fb in feedback_data if fb.sentiment == "negative" and fb.feedback_text]

        # Simplified keyword extraction for common issues
        # In a real app, use NLP for better theme extraction (e.g., TF-IDF, LDA, or clustering)
        issue_keywords = {
            "slow": "Performance: Slowness reported",
            "confusing": "UX: Interface confusing",
            "bug": "Technical: Bug reported",
            "error": "Technical: Error encountered",
            "difficult": "UX: Difficult to use",
            "crash": "Technical: Application crashed",
            "missing": "Feature: Missing functionality",
            "expensive": "Pricing: Considered expensive"
        }

        for text in negative_feedback_texts:
            text_lower = text.lower()
            for keyword, issue_description in issue_keywords.items():
                if keyword in text_lower:
                    common_issues_map[issue_description] = common_issues_map.get(issue_description, 0) + 1

        # Sort common issues by frequency
        sorted_common_issues = sorted(common_issues_map.items(), key=lambda item: item[1], reverse=True)
        top_common_issues = [f"{issue} (Count: {count})" for issue, count in sorted_common_issues[:3]] # Top 3

        # Generate simple suggestions based on top issues
        if any("Performance" in issue for issue in top_common_issues):
            improvement_suggestions_list.append("Suggestion: Investigate and optimize application performance.")
        if any("UX" in issue for issue in top_common_issues):
            improvement_suggestions_list.append("Suggestion: Conduct UX review and simplify user interface.")
        if any("Technical" in issue for issue in top_common_issues):
            improvement_suggestions_list.append("Suggestion: Prioritize bug fixing and improve error handling.")
        if not improvement_suggestions_list and top_common_issues: # Generic if specific keywords didn't match
            improvement_suggestions_list.append("Suggestion: Review negative feedback for actionable insights.")
        if not improvement_suggestions_list and not top_common_issues and negative_feedback_texts:
             improvement_suggestions_list.append("Suggestion: Further analyze negative feedback texts for specific themes.")


        nps_analytics = await self.calculate_nps_analytics(time_period)

        return FeedbackAnalytics(
            average_rating=avg_rating,
            sentiment_distribution=sentiment_dist,
            common_issues=top_common_issues if top_common_issues else ["No specific common issues identified from keywords."],
            improvement_suggestions=improvement_suggestions_list if improvement_suggestions_list else ["No specific suggestions generated."],
            nps_score=nps_analytics.nps_score if nps_analytics else None
        )

    async def calculate_nps_analytics(self, time_period: str = "month") -> NPSAnalytics:
        """Calculates NPS score and distribution from actual survey responses."""
        end_date = datetime.utcnow()
        if time_period == "week":
            start_date = end_date - timedelta(days=7)
        elif time_period == "month":
            start_date = end_date - timedelta(days=30)
        elif time_period == "day":
            start_date = end_date - timedelta(days=1)
        else: # Default to month if invalid period
            start_date = end_date - timedelta(days=30)

        # Query SurveyResponses joined with FeedbackSurvey to filter by survey_type and date
        nps_survey_responses = self.db.query(SurveyResponse)\
            .join(FeedbackSurvey, SurveyResponse.survey_id == FeedbackSurvey.id)\
            .filter(
                FeedbackSurvey.survey_type == "nps",
                SurveyResponse.completed_at >= start_date,
                SurveyResponse.completed_at <= end_date,
                SurveyResponse.is_complete == True
            ).all()

        promoters = 0
        passives = 0
        detractors = 0
        total_responses = len(nps_survey_responses)

        if total_responses == 0:
            return NPSAnalytics(nps_score=0.0, promoters=0, passives=0, detractors=0, total_responses=0)

        for sr in nps_survey_responses:
            # Assuming the NPS question is the first question (e.g., 'q1') and its response is the score.
            # This needs to be robust if question IDs or structure vary.
            # A common pattern is to have a specific question_id for NPS score.
            # For now, let's try to find a response value that is an integer between 0-10.
            nps_score_value = None
            if isinstance(sr.responses, dict):
                for q_id, answer in sr.responses.items():
                    # Attempt to find the NPS score, assuming it's an integer response.
                    # A more robust way would be to check question type from FeedbackSurvey.questions
                    if isinstance(answer, (int, float)) and 0 <= answer <= 10:
                        nps_score_value = int(answer)
                        break
                    # If answer is a dict itself (e.g. from a complex question type)
                    elif isinstance(answer, dict) and 'score' in answer and isinstance(answer['score'], (int, float)) and 0 <= answer['score'] <= 10:
                        nps_score_value = int(answer['score'])
                        break

            if nps_score_value is not None:
                if nps_score_value >= 9:
                    promoters += 1
                elif nps_score_value >= 7:
                    passives += 1
                else: # 0-6
                    detractors += 1
            else:
                logger.warning(f"Could not extract NPS score from survey response ID {sr.id}, responses: {sr.responses}")
                total_responses -=1 # Adjust total if a response couldn't be parsed

        if total_responses == 0: # Recalculate if all responses were unparsable
             return NPSAnalytics(nps_score=0.0, promoters=0, passives=0, detractors=0, total_responses=0)

        nps_score_calculated = ((promoters / total_responses) - (detractors / total_responses)) * 100

        return NPSAnalytics(
            nps_score=round(nps_score_calculated, 2),
            promoters=promoters,
            passives=passives,
            detractors=detractors,
            total_responses=total_responses
        )

    async def get_negative_feedback_alerts(self, threshold_rating: int = 2) -> List[FeedbackAlert]:
        """Get alerts for negative feedback requiring attention (dummy)."""
        negative_feedback = self.db.query(UserFeedback).filter(
            UserFeedback.rating != None, UserFeedback.rating <= threshold_rating
        ).order_by(UserFeedback.created_at.desc()).limit(10).all()

        alerts = []
        for fb in negative_feedback:
            alerts.append(FeedbackAlert(
                feedback_id=str(fb.id), # Ensure ID is string
                user_id=str(fb.user_id),
                alert_type="low_rating_alert",
                message_preview=fb.feedback_text[:100] if fb.feedback_text else f"Rating: {fb.rating}",
                created_at=fb.created_at
            ))
        return alerts

    async def get_active_surveys_for_user(self, user_id: int) -> List[FeedbackSurvey]:
        """Retrieves active surveys targeted for a user."""
        # 1. Fetch all active surveys
        active_surveys_query = self.db.query(FeedbackSurvey).filter(
            FeedbackSurvey.is_active == True,
            (FeedbackSurvey.expires_at == None) | (FeedbackSurvey.expires_at > datetime.utcnow())
        )
        all_active_surveys = active_surveys_query.all()

        if not all_active_surveys:
            return []

        # 2. Get IDs of surveys already responded to by the user
        responded_survey_ids = {
            r.survey_id for r in
            self.db.query(SurveyResponse.survey_id).filter(SurveyResponse.user_id == user_id, SurveyResponse.is_complete == True).all()
        }

        # Fetch user details for criteria checking (example: user registration date, purchase count)
        # This might require importing UserModelDB from ..models.auth or ..database
        from backend.app.models.auth import User as UserModelDB # Assuming this is the SQLAlchemy model
        user = self.db.query(UserModelDB).filter(UserModelDB.id == user_id).first()
        if not user: # Should not happen if user_id is from an authenticated user
            return []

        eligible_surveys = []
        for survey in all_active_surveys:
            # 3. Filter out already responded surveys
            if survey.id in responded_survey_ids:
                continue

            # 4. Check target_audience_criteria
            if not survey.target_audience: # No specific targeting, eligible by default
                eligible_surveys.append(survey)
                continue

            criteria_met = True
            # Example criteria checks:
            if "user_id" in survey.target_audience and survey.target_audience["user_id"] != user_id:
                criteria_met = False

            if "user_segment" in survey.target_audience:
                # This would require a more complex user segmentation logic
                # e.g., check user.segment, user.activity_level, etc.
                # For now, let's assume a simple check if a 'segment' field exists on User model
                # user_segment = getattr(user, "segment", "general").lower()
                # if user_segment != survey.target_audience["user_segment"].lower():
                #     criteria_met = False
                pass # Placeholder for actual segmentation logic

            if "min_registration_days" in survey.target_audience:
                if user.created_at:
                    days_since_registration = (datetime.utcnow().replace(tzinfo=None) - user.created_at.replace(tzinfo=None)).days
                    if days_since_registration < survey.target_audience["min_registration_days"]:
                        criteria_met = False
                else: # Cannot check if created_at is missing
                    criteria_met = False

            # Add more criteria checks as needed (e.g., purchase history, feature usage)

            if criteria_met:
                eligible_surveys.append(survey)

        return eligible_surveys[:2] # Limit to 2 for now, as in original dummy

    async def get_survey_details(self, survey_id: str) -> Optional[FeedbackSurvey]:
        """Retrieves details for a specific survey."""
        return self.db.query(FeedbackSurvey).filter(FeedbackSurvey.id == survey_id).first()
