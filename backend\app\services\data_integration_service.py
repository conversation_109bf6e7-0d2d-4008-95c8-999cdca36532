"""
Data Integration Service for Datagenius.
Provides data access and integration capabilities for various data sources.
"""

import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..database import DataSource
from app.utils.memory_management import memory_manager, memory_safe_operation, cleanup_on_exit

logger = logging.getLogger(__name__)


class DataIntegrationService:
    """Service for data integration and access."""
    
    def __init__(self, db: Session):
        """Initialize the data integration service."""
        self.db = db
        logger.info("Data Integration Service initialized")
    
    async def get_user_data_sources(self, user_id: int) -> List[DataSource]:
        """Get all data sources for a user."""
        try:
            return self.db.query(DataSource).filter(
                DataSource.user_id == user_id
            ).all()
        except Exception as e:
            logger.error(f"Error getting user data sources: {e}")
            return []
    
    async def get_data_source(self, source_id: str, user_id: int) -> Optional[DataSource]:
        """Get a specific data source by ID and user."""
        try:
            return self.db.query(DataSource).filter(
                and_(
                    DataSource.id == source_id,
                    DataSource.user_id == user_id
                )
            ).first()
        except Exception as e:
            logger.error(f"Error getting data source: {e}")
            return None
    
    async def get_file_data(self, source_id: str) -> Optional[List[Dict[str, Any]]]:
        """Get data from a file data source."""
        try:
            # This is a placeholder implementation
            # In a real implementation, this would:
            # 1. Get the data source from database
            # 2. Load the file data based on the file_id
            # 3. Parse the file (CSV, Excel, etc.)
            # 4. Return the data as a list of dictionaries
            
            # Implement actual data retrieval from database
            from sqlalchemy.orm import Session
            from app.database.models import DataSource
            from app.database.database import get_db

            try:
                # Get database session
                db_session = next(get_db())

                # Query the actual data source
                data_source = db_session.query(DataSource).filter(
                    DataSource.id == source_id
                ).first()

                if not data_source:
                    logger.error(f"Data source {source_id} not found")
                    return None

                # Load and return actual data based on source type
                if data_source.source_type == 'csv':
                    return self._load_csv_data(data_source.file_path)
                elif data_source.source_type == 'json':
                    return self._load_json_data(data_source.file_path)
                elif data_source.source_type == 'database':
                    return self._load_database_data(data_source.connection_string, data_source.query)
                else:
                    logger.error(f"Unsupported data source type: {data_source.source_type}")
                    return None

            except Exception as db_error:
                logger.error(f"Database error retrieving data source {source_id}: {db_error}")
                return None
            finally:
                if 'db_session' in locals():
                    db_session.close()
            
        except Exception as e:
            logger.error(f"Error getting file data: {e}")
            return None

    @cleanup_on_exit
    def _load_csv_data(self, file_path: str) -> List[Dict[str, Any]]:
        """Load data from CSV file with proper error handling, security, and memory management."""
        with memory_safe_operation(f"load_csv_{file_path}"):
            try:
                import pandas as pd
                import os

                # Security: Validate file path
                if not os.path.exists(file_path):
                    logger.error(f"CSV file not found: {file_path}")
                    return None

                # Security: Check file extension
                if not file_path.lower().endswith('.csv'):
                    logger.error(f"Invalid file type for CSV loader: {file_path}")
                    return None

                # Security: Check file size (max 100MB)
                max_file_size = 100 * 1024 * 1024
                if os.path.getsize(file_path) > max_file_size:
                    logger.error(f"CSV file too large: {file_path}")
                    return None

                # Load CSV with memory management and chunking for large files
                try:
                    # Try to load in chunks if file is large
                    file_size = os.path.getsize(file_path)
                    if file_size > 10 * 1024 * 1024:  # 10MB threshold
                        chunks = []
                        chunk_size = 10000

                        for chunk in pd.read_csv(file_path, encoding='utf-8', chunksize=chunk_size):
                            chunks.append(chunk.to_dict('records'))
                            # Clean up chunk immediately
                            del chunk

                        # Combine all chunks
                        data = []
                        for chunk_data in chunks:
                            data.extend(chunk_data)

                        # Clean up chunks list
                        del chunks

                    else:
                        # Load small files normally
                        df = pd.read_csv(file_path, encoding='utf-8')
                        data = df.to_dict('records')
                        # Clean up DataFrame immediately
                        del df

                    logger.info(f"Successfully loaded {len(data)} records from CSV: {file_path}")
                    return data

                except pd.errors.EmptyDataError:
                    logger.error(f"CSV file is empty: {file_path}")
                    return None
                except pd.errors.ParserError as e:
                    logger.error(f"CSV parsing error in {file_path}: {e}")
                    return None

            except Exception as e:
                logger.error(f"Error loading CSV data from {file_path}: {e}")
                return None

    def _load_json_data(self, file_path: str) -> List[Dict[str, Any]]:
        """Load data from JSON file with proper error handling and security."""
        try:
            import json
            import os

            # Security: Validate file path
            if not os.path.exists(file_path):
                logger.error(f"JSON file not found: {file_path}")
                return None

            # Security: Check file extension
            if not file_path.lower().endswith('.json'):
                logger.error(f"Invalid file type for JSON loader: {file_path}")
                return None

            # Load JSON with size limit for security
            max_file_size = 100 * 1024 * 1024  # 100MB limit
            if os.path.getsize(file_path) > max_file_size:
                logger.error(f"JSON file too large: {file_path}")
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Ensure data is a list of dictionaries
            if isinstance(data, dict):
                data = [data]
            elif not isinstance(data, list):
                logger.error(f"Invalid JSON structure in {file_path}")
                return None

            logger.info(f"Successfully loaded {len(data)} records from JSON: {file_path}")
            return data

        except Exception as e:
            logger.error(f"Error loading JSON data from {file_path}: {e}")
            return None

    def _load_database_data(self, connection_string: str, query: str) -> List[Dict[str, Any]]:
        """Load data from database with proper error handling and security."""
        try:
            import pandas as pd
            from sqlalchemy import create_engine, text

            # Security: Validate query (basic SQL injection prevention)
            dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'EXEC']
            query_upper = query.upper()
            for keyword in dangerous_keywords:
                if keyword in query_upper:
                    logger.error(f"Dangerous SQL keyword detected: {keyword}")
                    return None

            # Create engine with security settings
            engine = create_engine(
                connection_string,
                pool_pre_ping=True,
                pool_recycle=3600,
                connect_args={"timeout": 30}
            )

            # Execute query with timeout and memory management
            df = pd.read_sql(text(query), engine, chunksize=10000)

            # Process chunks to manage memory
            all_data = []
            for chunk in df:
                all_data.extend(chunk.to_dict('records'))
                # Clean up chunk to prevent memory leaks
                del chunk

            # Clean up engine
            engine.dispose()

            logger.info(f"Successfully loaded {len(all_data)} records from database")
            return all_data

        except Exception as e:
            logger.error(f"Error loading database data: {e}")
            return None
    
    async def test_data_source_connection(self, source_id: str, user_id: int) -> Dict[str, Any]:
        """Test connection to a data source."""
        try:
            data_source = await self.get_data_source(source_id, user_id)
            if not data_source:
                return {"success": False, "error": "Data source not found"}
            
            # Mock connection test - in real implementation this would:
            # - Test database connections
            # - Validate API endpoints
            # - Check file accessibility
            # - Verify MCP connections
            
            return {
                "success": True,
                "message": f"Connection to {data_source.name} successful",
                "source_type": data_source.type,
                "response_time": 0.1
            }
            
        except Exception as e:
            logger.error(f"Error testing data source connection: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_data_source_schema(self, source_id: str, user_id: int) -> Dict[str, Any]:
        """Get schema information for a data source."""
        try:
            data_source = await self.get_data_source(source_id, user_id)
            if not data_source:
                return {"success": False, "error": "Data source not found"}
            
            # Mock schema - in real implementation this would introspect the actual data
            mock_schema = {
                "success": True,
                "columns": [
                    {"name": "id", "type": "integer", "nullable": False},
                    {"name": "name", "type": "string", "nullable": False},
                    {"name": "value", "type": "numeric", "nullable": True},
                    {"name": "category", "type": "string", "nullable": True}
                ],
                "row_count": 5,
                "data_source_type": data_source.type
            }
            
            return mock_schema
            
        except Exception as e:
            logger.error(f"Error getting data source schema: {e}")
            return {"success": False, "error": str(e)}
    
    async def create_data_source(self, user_id: int, source_data: Dict[str, Any]) -> Optional[DataSource]:
        """Create a new data source."""
        try:
            # This is a placeholder - in real implementation would create actual data source
            logger.info(f"Would create data source for user {user_id}: {source_data}")
            return None
        except Exception as e:
            logger.error(f"Error creating data source: {e}")
            return None
    
    async def update_data_source(self, source_id: str, user_id: int, updates: Dict[str, Any]) -> Optional[DataSource]:
        """Update an existing data source."""
        try:
            data_source = await self.get_data_source(source_id, user_id)
            if not data_source:
                return None
            
            # Update fields
            for key, value in updates.items():
                if hasattr(data_source, key):
                    setattr(data_source, key, value)
            
            self.db.commit()
            self.db.refresh(data_source)
            
            logger.info(f"Updated data source {source_id}")
            return data_source
            
        except Exception as e:
            logger.error(f"Error updating data source: {e}")
            self.db.rollback()
            return None
    
    async def delete_data_source(self, source_id: str, user_id: int) -> bool:
        """Delete a data source."""
        try:
            data_source = await self.get_data_source(source_id, user_id)
            if not data_source:
                return False
            
            self.db.delete(data_source)
            self.db.commit()
            
            logger.info(f"Deleted data source {source_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting data source: {e}")
            self.db.rollback()
            return False
