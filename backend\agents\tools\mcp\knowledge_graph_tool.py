"""
Knowledge Graph MCP Tool for Datagenius.

This tool provides comprehensive knowledge graph operations using mem0ai,
including entity extraction, relationship mapping, and graph querying.
"""

import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .base import BaseMCPTool
from ...utils.knowledge_graph_service import KnowledgeGraphService
from ...utils.memory_service import MemoryService

logger = logging.getLogger(__name__)


class KnowledgeGraphTool(BaseMCPTool):
    """
    MCP tool for knowledge graph operations.

    Provides functionality for:
    - Entity extraction from text
    - Relationship identification
    - Graph construction and querying
    - Knowledge discovery
    """

    def __init__(self):
        """Initialize the Knowledge Graph Tool."""
        super().__init__()
        self.knowledge_graph_service = KnowledgeGraphService()
        self.memory_service = MemoryService()

        # Entity extraction patterns
        self.entity_patterns = {
            "PERSON": [
                r"\b[A-Z][a-z]+ [A-Z][a-z]+\b",  # First Last names
                r"\b(?:Mr|Mrs|Ms|Dr|Prof)\.? [A-Z][a-z]+\b"  # Titles
            ],
            "ORGANIZATION": [
                r"\b[A-Z][a-zA-Z]+ (?:Inc|Corp|LLC|Ltd|Company|Corporation)\b",
                r"\b[A-Z][a-zA-Z]+ (?:University|College|Institute)\b"
            ],
            "LOCATION": [
                r"\b[A-Z][a-z]+(?:, [A-Z][a-z]+)*\b",  # City, State
                r"\b[A-Z][a-z]+ (?:Street|Avenue|Road|Boulevard)\b"
            ],
            "DATE": [
                r"\b\d{1,2}/\d{1,2}/\d{4}\b",  # MM/DD/YYYY
                r"\b\d{4}-\d{2}-\d{2}\b",      # YYYY-MM-DD
                r"\b(?:January|February|March|April|May|June|July|August|September|October|November|December) \d{1,2}, \d{4}\b"
            ]
        }

    @property
    def definition(self) -> Dict[str, Any]:
        """Return the MCP tool definition."""
        return {
            "name": "knowledge_graph_tool",
            "description": "Extract entities, build knowledge graphs, and perform graph queries",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": [
                            "extract_entities",
                            "create_graph",
                            "add_entity",
                            "add_relationship",
                            "query_graph",
                            "find_connections",
                            "get_entity_info",
                            "analyze_document"
                        ],
                        "description": "The knowledge graph operation to perform"
                    },
                    "text": {
                        "type": "string",
                        "description": "Text to analyze for entity extraction or document analysis"
                    },
                    "graph_name": {
                        "type": "string",
                        "description": "Name of the knowledge graph"
                    },
                    "entity_name": {
                        "type": "string",
                        "description": "Name of the entity"
                    },
                    "entity_type": {
                        "type": "string",
                        "description": "Type of the entity (PERSON, ORGANIZATION, LOCATION, etc.)"
                    },
                    "source_entity": {
                        "type": "string",
                        "description": "Source entity for relationship"
                    },
                    "target_entity": {
                        "type": "string",
                        "description": "Target entity for relationship"
                    },
                    "relationship_type": {
                        "type": "string",
                        "description": "Type of relationship (WORKS_FOR, LOCATED_IN, etc.)"
                    },
                    "query": {
                        "type": "string",
                        "description": "Query to search the knowledge graph"
                    },
                    "properties": {
                        "type": "object",
                        "description": "Additional properties for entities or relationships"
                    }
                },
                "required": ["operation"]
            }
        }

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the knowledge graph operation."""
        try:
            operation = arguments.get("operation")

            if operation == "extract_entities":
                return await self._extract_entities(arguments)
            elif operation == "create_graph":
                return await self._create_graph(arguments)
            elif operation == "add_entity":
                return await self._add_entity(arguments)
            elif operation == "add_relationship":
                return await self._add_relationship(arguments)
            elif operation == "query_graph":
                return await self._query_graph(arguments)
            elif operation == "find_connections":
                return await self._find_connections(arguments)
            elif operation == "get_entity_info":
                return await self._get_entity_info(arguments)
            elif operation == "analyze_document":
                return await self._analyze_document(arguments)
            else:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Unknown operation: {operation}"}]
                }

        except Exception as e:
            logger.error(f"Error in knowledge graph tool: {e}")
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Error: {str(e)}"}]
            }

    async def _extract_entities(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Extract entities from text."""
        text = arguments.get("text", "")
        if not text:
            return {
                "isError": True,
                "content": [{"type": "text", "text": "Text is required for entity extraction"}]
            }

        entities = []

        # Use pattern-based extraction (could be enhanced with NER models)
        import re
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    entities.append({
                        "text": match.group(),
                        "type": entity_type,
                        "start": match.start(),
                        "end": match.end(),
                        "confidence": 0.8  # Pattern-based confidence
                    })

        # Remove duplicates and sort by position
        unique_entities = []
        seen = set()
        for entity in sorted(entities, key=lambda x: x["start"]):
            key = (entity["text"].lower(), entity["type"])
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)

        return {
            "content": [
                {
                    "type": "text",
                    "text": f"Extracted {len(unique_entities)} entities from text"
                },
                {
                    "type": "text",
                    "text": json.dumps(unique_entities, indent=2)
                }
            ]
        }

    async def _create_graph(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new knowledge graph."""
        graph_name = arguments.get("graph_name", "")
        if not graph_name:
            return {
                "isError": True,
                "content": [{"type": "text", "text": "Graph name is required"}]
            }

        description = arguments.get("description", f"Knowledge graph: {graph_name}")
        metadata = arguments.get("properties", {})

        try:
            graph_id = self.knowledge_graph_service.create_graph(
                name=graph_name,
                description=description,
                metadata=metadata
            )

            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Created knowledge graph '{graph_name}' with ID: {graph_id}"
                    }
                ]
            }
        except Exception as e:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Failed to create graph: {str(e)}"}]
            }

    async def _add_entity(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Add an entity to the knowledge graph."""
        graph_name = arguments.get("graph_name", "")
        entity_name = arguments.get("entity_name", "")
        entity_type = arguments.get("entity_type", "")

        if not all([graph_name, entity_name, entity_type]):
            return {
                "isError": True,
                "content": [{"type": "text", "text": "Graph name, entity name, and entity type are required"}]
            }

        # Get graph ID (simplified - in real implementation, would look up by name)
        graph_id = f"graph_{graph_name}"
        description = arguments.get("description", f"{entity_type}: {entity_name}")
        properties = arguments.get("properties", {})

        try:
            entity_id = self.knowledge_graph_service.add_entity(
                graph_id=graph_id,
                entity_type=entity_type,
                name=entity_name,
                description=description,
                properties=properties
            )

            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Added entity '{entity_name}' of type '{entity_type}' with ID: {entity_id}"
                    }
                ]
            }
        except Exception as e:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Failed to add entity: {str(e)}"}]
            }

    async def _add_relationship(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Add a relationship between entities."""
        graph_name = arguments.get("graph_name", "")
        source_entity = arguments.get("source_entity", "")
        target_entity = arguments.get("target_entity", "")
        relationship_type = arguments.get("relationship_type", "")

        if not all([graph_name, source_entity, target_entity, relationship_type]):
            return {
                "isError": True,
                "content": [{"type": "text", "text": "Graph name, source entity, target entity, and relationship type are required"}]
            }

        graph_id = f"graph_{graph_name}"
        properties = arguments.get("properties", {})

        try:
            relationship_id = await self.knowledge_graph_service.add_relationship(
                graph_id=graph_id,
                source_entity_id=f"entity_{source_entity}",
                target_entity_id=f"entity_{target_entity}",
                relationship_type=relationship_type,
                properties=properties
            )

            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Added relationship '{relationship_type}' between '{source_entity}' and '{target_entity}' with ID: {relationship_id}"
                    }
                ]
            }
        except Exception as e:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Failed to add relationship: {str(e)}"}]
            }

    async def _query_graph(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Query the knowledge graph."""
        graph_name = arguments.get("graph_name", "")
        query = arguments.get("query", "")

        if not all([graph_name, query]):
            return {
                "isError": True,
                "content": [{"type": "text", "text": "Graph name and query are required"}]
            }

        graph_id = f"graph_{graph_name}"

        try:
            results = await self.knowledge_graph_service.query_graph(
                graph_id=graph_id,
                query=query
            )

            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Query results for '{query}' in graph '{graph_name}'"
                    },
                    {
                        "type": "text",
                        "text": json.dumps(results, indent=2)
                    }
                ]
            }
        except Exception as e:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Failed to query graph: {str(e)}"}]
            }

    async def _find_connections(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Find connections between entities."""
        graph_name = arguments.get("graph_name", "")
        source_entity = arguments.get("source_entity", "")
        target_entity = arguments.get("target_entity", "")

        if not all([graph_name, source_entity, target_entity]):
            return {
                "isError": True,
                "content": [{"type": "text", "text": "Graph name, source entity, and target entity are required"}]
            }

        graph_id = f"graph_{graph_name}"

        try:
            connections = await self.knowledge_graph_service.find_path(
                graph_id=graph_id,
                source_entity_id=f"entity_{source_entity}",
                target_entity_id=f"entity_{target_entity}"
            )

            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Connections between '{source_entity}' and '{target_entity}'"
                    },
                    {
                        "type": "text",
                        "text": json.dumps(connections, indent=2)
                    }
                ]
            }
        except Exception as e:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Failed to find connections: {str(e)}"}]
            }

    async def _get_entity_info(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Get information about an entity."""
        graph_name = arguments.get("graph_name", "")
        entity_name = arguments.get("entity_name", "")

        if not all([graph_name, entity_name]):
            return {
                "isError": True,
                "content": [{"type": "text", "text": "Graph name and entity name are required"}]
            }

        graph_id = f"graph_{graph_name}"
        entity_id = f"entity_{entity_name}"

        try:
            entity_info = await self.knowledge_graph_service.get_entity(
                graph_id=graph_id,
                entity_id=entity_id
            )

            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Information for entity '{entity_name}'"
                    },
                    {
                        "type": "text",
                        "text": json.dumps(entity_info, indent=2)
                    }
                ]
            }
        except Exception as e:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Failed to get entity info: {str(e)}"}]
            }

    async def _analyze_document(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a document and build a knowledge graph."""
        text = arguments.get("text", "")
        graph_name = arguments.get("graph_name", "document_analysis")

        if not text:
            return {
                "isError": True,
                "content": [{"type": "text", "text": "Text is required for document analysis"}]
            }

        try:
            # Extract entities
            entities_result = await self._extract_entities({"text": text})
            entities_data = json.loads(entities_result["content"][1]["text"])

            # Create graph
            await self._create_graph({
                "graph_name": graph_name,
                "description": f"Knowledge graph from document analysis at {datetime.now().isoformat()}"
            })

            # Add entities to graph
            added_entities = []
            for entity in entities_data:
                try:
                    await self._add_entity({
                        "graph_name": graph_name,
                        "entity_name": entity["text"],
                        "entity_type": entity["type"],
                        "properties": {
                            "confidence": entity["confidence"],
                            "position": {"start": entity["start"], "end": entity["end"]}
                        }
                    })
                    added_entities.append(entity["text"])
                except Exception as e:
                    logger.warning(f"Failed to add entity {entity['text']}: {e}")

            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Analyzed document and created knowledge graph '{graph_name}'"
                    },
                    {
                        "type": "text",
                        "text": f"Added {len(added_entities)} entities: {', '.join(added_entities[:10])}"
                        + ("..." if len(added_entities) > 10 else "")
                    }
                ]
            }

        except Exception as e:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Failed to analyze document: {str(e)}"}]
            }
