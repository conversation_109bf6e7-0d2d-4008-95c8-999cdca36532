"""
Agent API endpoints for the Datagenius backend.

This module provides API endpoints for interacting with agents.
"""

import logging
import uuid
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session

from ..models.agent import AgentInvokeRequest, AgentResponse, AgentListResponse, AgentInfo
from ..models.auth import User
from ..database import get_db
from ..auth import get_current_active_user
# Import the agent registry using centralized import utility
from ..utils.import_utils import import_agents_registry

AgentRegistry = import_agents_registry()

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/agents", tags=["Agents"])


@router.get("", response_model=AgentListResponse)
async def list_agents(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    List all available agents.

    Returns a list of all registered agents and their capabilities.
    """
    logger.info(f"User {current_user.id} requested list of agents")

    # Get all registered persona IDs from the registry
    persona_ids = AgentRegistry.list_registered_personas()

    # Create a list of AgentInfo objects
    agents = []
    for persona_id in persona_ids:
        # Get configuration for the persona
        config = AgentRegistry.get_configuration(persona_id)

        if config:
            # Use configuration data if available
            capabilities = config.get("capabilities", [])
            agents.append(AgentInfo(
                persona_id=persona_id,
                name=config.get("name", persona_id),
                description=config.get("description", "No description available"),
                capabilities=capabilities
            ))
        else:
            # Fall back to creating a temporary instance if no configuration is available
            agent_class = AgentRegistry.get_agent_class(persona_id)
            if agent_class:
                # Create a temporary instance to get capabilities
                agent = agent_class()
                capabilities = await agent.get_capabilities()

                # Add agent info to the list
                agents.append(AgentInfo(
                    persona_id=persona_id,
                    name=agent_class.__name__,
                    description=agent_class.__doc__ or "No description available",
                    capabilities=capabilities
                ))

    return AgentListResponse(agents=agents)


@router.post("/{persona_id}/invoke", response_model=AgentResponse)
async def invoke_agent(
    persona_id: str,
    request: AgentInvokeRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Invoke an agent for a specific persona.

    Args:
        persona_id: ID of the persona to invoke
        request: Agent invocation request

    Returns:
        Agent response
    """
    logger.info(f"User {current_user.id} invoked agent for persona {persona_id}")

    try:
        # Create an agent instance using the registry
        agent = await AgentRegistry.create_agent_instance(persona_id)
        if not agent:
            logger.error(f"No agent found for persona: {persona_id}")
            raise HTTPException(status_code=404, detail=f"No agent found for persona: {persona_id}")

        # Override configuration with request config if provided
        if request.config:
            # Merge the request config with the agent's existing config
            agent.config.update(request.config)

        # Process the message
        response = await agent.process_message(
            user_id=current_user.id,
            message=request.message,
            conversation_id=request.conversation_id or str(uuid.uuid4()),
            context=request.context
        )

        return AgentResponse(
            message=response.get("message", ""),
            conversation_id=request.conversation_id,
            metadata=response.get("metadata")
        )
    except Exception as e:
        logger.error(f"Error invoking agent for persona {persona_id}: {str(e)}", exc_info=True)
        return AgentResponse(
            message="An error occurred while processing your request.",
            conversation_id=request.conversation_id,
            error=str(e)
        )
