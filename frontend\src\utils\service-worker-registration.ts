/**
 * Service Worker Registration for Phase 1 Performance Optimizations
 * 
 * Handles registration, updates, and communication with the service worker
 * for advanced browser caching and offline support.
 */

interface ServiceWorkerStats {
  registered: boolean;
  active: boolean;
  cacheStats: Record<string, number>;
  updateAvailable: boolean;
}

class ServiceWorkerManager {
  private registration: ServiceWorkerRegistration | null = null;
  private updateAvailable = false;
  private stats: ServiceWorkerStats = {
    registered: false,
    active: false,
    cacheStats: {},
    updateAvailable: false
  };

  constructor() {
    this.registerServiceWorker();
  }

  /**
   * Register the service worker
   */
  private async registerServiceWorker(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      console.log('Service Worker not supported');
      return;
    }

    try {
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      this.stats.registered = true;
      console.log('Service Worker registered successfully');

      // Handle updates
      this.registration.addEventListener('updatefound', () => {
        this.handleUpdate();
      });

      // Check if service worker is active
      if (this.registration.active) {
        this.stats.active = true;
        this.setupMessageChannel();
      }

      // Listen for service worker state changes
      if (this.registration.installing) {
        this.trackInstalling(this.registration.installing);
      } else if (this.registration.waiting) {
        this.updateAvailable = true;
        this.stats.updateAvailable = true;
      }

      // Listen for controller changes
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('Service Worker controller changed');
        window.location.reload();
      });

    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }

  /**
   * Handle service worker updates
   */
  private handleUpdate(): void {
    if (!this.registration) return;

    const newWorker = this.registration.installing;
    if (!newWorker) return;

    this.trackInstalling(newWorker);
  }

  /**
   * Track installing service worker
   */
  private trackInstalling(worker: ServiceWorker): void {
    worker.addEventListener('statechange', () => {
      if (worker.state === 'installed') {
        if (navigator.serviceWorker.controller) {
          // New update available
          this.updateAvailable = true;
          this.stats.updateAvailable = true;
          this.notifyUpdateAvailable();
        } else {
          // First install
          this.stats.active = true;
          this.setupMessageChannel();
          console.log('Service Worker installed for the first time');
        }
      }
    });
  }

  /**
   * Setup message channel with service worker
   */
  private setupMessageChannel(): void {
    if (!navigator.serviceWorker.controller) return;

    // Get initial cache stats
    this.getCacheStats();

    // Set up periodic stats updates
    setInterval(() => {
      this.getCacheStats();
    }, 60000); // Every minute
  }

  /**
   * Notify about available updates
   */
  private notifyUpdateAvailable(): void {
    console.log('Service Worker update available');
    
    // You can integrate this with your notification system
    if (window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('sw-update-available'));
    }
  }

  /**
   * Apply service worker update
   */
  public async applyUpdate(): Promise<void> {
    if (!this.registration || !this.registration.waiting) {
      return;
    }

    // Tell the waiting service worker to skip waiting
    this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
  }

  /**
   * Get cache statistics from service worker
   */
  public async getCacheStats(): Promise<Record<string, number>> {
    if (!navigator.serviceWorker.controller) {
      return {};
    }

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        this.stats.cacheStats = event.data;
        resolve(event.data);
      };

      navigator.serviceWorker.controller.postMessage(
        { type: 'GET_CACHE_STATS' },
        [messageChannel.port2]
      );
    });
  }

  /**
   * Clear all service worker caches
   */
  public async clearCaches(): Promise<void> {
    if (!navigator.serviceWorker.controller) {
      return;
    }

    navigator.serviceWorker.controller.postMessage({ type: 'CLEAR_CACHE' });
    
    // Also clear local storage cache
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('cache:')) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log('All caches cleared');
  }

  /**
   * Get service worker statistics
   */
  public getStats(): ServiceWorkerStats {
    return { ...this.stats };
  }

  /**
   * Check if service worker is supported and active
   */
  public isActive(): boolean {
    return this.stats.registered && this.stats.active;
  }

  /**
   * Check if update is available
   */
  public isUpdateAvailable(): boolean {
    return this.updateAvailable;
  }

  /**
   * Unregister service worker (for development/testing)
   */
  public async unregister(): Promise<void> {
    if (!this.registration) return;

    try {
      await this.registration.unregister();
      this.stats.registered = false;
      this.stats.active = false;
      console.log('Service Worker unregistered');
    } catch (error) {
      console.error('Service Worker unregistration failed:', error);
    }
  }
}

// Global service worker manager instance
export const serviceWorkerManager = new ServiceWorkerManager();

// Utility functions
export const clearServiceWorkerCaches = () => serviceWorkerManager.clearCaches();
export const getServiceWorkerStats = () => serviceWorkerManager.getStats();
export const applyServiceWorkerUpdate = () => serviceWorkerManager.applyUpdate();

// React hook for service worker integration
export function useServiceWorker() {
  const [stats, setStats] = React.useState<ServiceWorkerStats>(serviceWorkerManager.getStats());
  const [updateAvailable, setUpdateAvailable] = React.useState(serviceWorkerManager.isUpdateAvailable());

  React.useEffect(() => {
    // Update stats periodically
    const interval = setInterval(() => {
      setStats(serviceWorkerManager.getStats());
      setUpdateAvailable(serviceWorkerManager.isUpdateAvailable());
    }, 5000);

    // Listen for update events
    const handleUpdate = () => {
      setUpdateAvailable(true);
    };

    window.addEventListener('sw-update-available', handleUpdate);

    return () => {
      clearInterval(interval);
      window.removeEventListener('sw-update-available', handleUpdate);
    };
  }, []);

  return {
    stats,
    updateAvailable,
    applyUpdate: serviceWorkerManager.applyUpdate.bind(serviceWorkerManager),
    clearCaches: serviceWorkerManager.clearCaches.bind(serviceWorkerManager),
    isActive: serviceWorkerManager.isActive()
  };
}

// Initialize service worker on module load
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  // Only register in production to avoid development issues
  console.log('Initializing Service Worker for production...');
}

export default serviceWorkerManager;
