/**
 * Natural Language Widget Creator Component
 * 
 * Allows users to create dashboard widgets using natural language descriptions.
 * Features AI-powered interpretation, smart suggestions, and one-click creation.
 */

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  MessageCircle,
  Sparkles,
  Wand2,
  BarChart3,
  PieChart,
  LineChart,
  TrendingUp,
  Target,
  Calendar,
  Users,
  DollarSign,
  Send,
  Lightbulb,
  CheckCircle,
  AlertCircle,
  Loader2,
  Mic,
  <PERSON>c<PERSON>ff,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface WidgetSuggestion {
  id: string;
  title: string;
  description: string;
  widget_type: 'chart' | 'kpi' | 'table' | 'gauge' | 'text';
  chart_type?: 'bar' | 'line' | 'pie' | 'area' | 'scatter';
  confidence: number;
  data_requirements: string[];
  preview_config: any;
  estimated_setup_time: string;
}

interface NaturalLanguageWidgetCreatorProps {
  className?: string;
  available_data_sources?: string[];
  on_widget_create?: (widget_config: any) => void;
  on_suggestion_select?: (suggestion: WidgetSuggestion) => void;
}

export const NaturalLanguageWidgetCreator: React.FC<NaturalLanguageWidgetCreatorProps> = ({
  className,
  available_data_sources = [],
  on_widget_create,
  on_suggestion_select,
}) => {
  const [input_text, set_input_text] = useState('');
  const [suggestions, set_suggestions] = useState<WidgetSuggestion[]>([]);
  const [is_analyzing, set_is_analyzing] = useState(false);
  const [is_listening, set_is_listening] = useState(false);
  const [selected_suggestion, set_selected_suggestion] = useState<WidgetSuggestion | null>(null);
  const [show_preview, set_show_preview] = useState(false);
  const input_ref = useRef<HTMLInputElement>(null);

  // Example prompts to help users get started
  const example_prompts = [
    "Show me sales by month as a line chart",
    "Create a KPI widget for total revenue",
    "Display customer satisfaction as a gauge",
    "Make a pie chart of product categories",
    "Show top 10 customers in a table",
    "Track monthly growth rate",
  ];

  const handle_analyze_input = async () => {
    if (!input_text.trim() || is_analyzing) return;

    set_is_analyzing(true);
    set_suggestions([]);

    try {
      // Simulate AI analysis (replace with actual API call)
      const analyzed_suggestions = await analyze_natural_language_input(
        input_text,
        available_data_sources
      );
      set_suggestions(analyzed_suggestions);
    } catch (error) {
      console.error('Error analyzing input:', error);
      // Show error state
    } finally {
      set_is_analyzing(false);
    }
  };

  const handle_suggestion_click = (suggestion: WidgetSuggestion) => {
    set_selected_suggestion(suggestion);
    set_show_preview(true);
    on_suggestion_select?.(suggestion);
  };

  const handle_create_widget = (suggestion: WidgetSuggestion) => {
    const widget_config = {
      title: suggestion.title,
      widget_type: suggestion.widget_type,
      chart_type: suggestion.chart_type,
      data_config: suggestion.preview_config,
      description: suggestion.description,
      natural_language_query: input_text,
    };

    on_widget_create?.(widget_config);
    set_show_preview(false);
    set_input_text('');
    set_suggestions([]);
  };

  const handle_example_click = (example: string) => {
    set_input_text(example);
    input_ref.current?.focus();
  };

  const toggle_voice_input = () => {
    set_is_listening(!is_listening);
    // Voice input implementation would go here
  };

  const get_widget_icon = (widget_type: string, chart_type?: string) => {
    if (widget_type === 'kpi') return TrendingUp;
    if (widget_type === 'table') return Users;
    if (widget_type === 'gauge') return Target;
    if (widget_type === 'text') return MessageCircle;
    
    // Chart types
    if (chart_type === 'pie') return PieChart;
    if (chart_type === 'line') return LineChart;
    return BarChart3;
  };

  const get_confidence_color = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Wand2 className="h-5 w-5 text-purple-500" />
            <span>Create Widget with Natural Language</span>
            <Badge variant="secondary">AI-Powered</Badge>
          </CardTitle>
          <CardDescription>
            Describe what you want to visualize in plain English, and I'll suggest the best widgets for your data.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Input Area */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <div className="flex-1 relative">
                <Input
                  ref={input_ref}
                  value={input_text}
                  onChange={(e) => set_input_text(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handle_analyze_input()}
                  placeholder="Describe the widget you want to create..."
                  disabled={is_analyzing}
                  className="pr-20"
                />
                <div className="absolute right-1 top-1 flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggle_voice_input}
                    className={cn(
                      "h-8 w-8 p-0",
                      is_listening && "text-red-500"
                    )}
                  >
                    {is_listening ? (
                      <MicOff className="h-4 w-4" />
                    ) : (
                      <Mic className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    onClick={handle_analyze_input}
                    disabled={!input_text.trim() || is_analyzing}
                    size="sm"
                    className="h-8"
                  >
                    {is_analyzing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>

            {/* Example Prompts */}
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground flex items-center space-x-1">
                <Lightbulb className="h-4 w-4" />
                <span>Try these examples:</span>
              </p>
              <div className="flex flex-wrap gap-2">
                {example_prompts.slice(0, 3).map((example, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handle_example_click(example)}
                    className="text-xs h-7"
                  >
                    "{example}"
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Analysis Status */}
          {is_analyzing && (
            <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
              <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
              <span className="text-sm text-blue-700">
                Analyzing your request and finding the best visualization options...
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Suggestions */}
      {suggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              <span>Widget Suggestions</span>
              <Badge variant="outline">{suggestions.length} options</Badge>
            </CardTitle>
            <CardDescription>
              Based on your description, here are the best widget options:
            </CardDescription>
          </CardHeader>

          <CardContent>
            <div className="grid gap-4">
              {suggestions.map((suggestion) => {
                const IconComponent = get_widget_icon(suggestion.widget_type, suggestion.chart_type);
                return (
                  <Card
                    key={suggestion.id}
                    className="cursor-pointer hover:shadow-md transition-shadow border-l-4 border-l-blue-500"
                    onClick={() => handle_suggestion_click(suggestion)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3 flex-1">
                          <div className="p-2 bg-blue-50 rounded-lg">
                            <IconComponent className="h-5 w-5 text-blue-500" />
                          </div>
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium">{suggestion.title}</h4>
                              <Badge
                                variant="outline"
                                className={cn("text-xs", get_confidence_color(suggestion.confidence))}
                              >
                                {Math.round(suggestion.confidence * 100)}% match
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {suggestion.description}
                            </p>
                            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                              <span>Type: {suggestion.widget_type}</span>
                              {suggestion.chart_type && (
                                <span>Chart: {suggestion.chart_type}</span>
                              )}
                              <span>Setup: {suggestion.estimated_setup_time}</span>
                            </div>
                            {suggestion.data_requirements.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {suggestion.data_requirements.map((req, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {req}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {suggestion.confidence >= 0.8 && (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          )}
                          {suggestion.confidence < 0.6 && (
                            <AlertCircle className="h-5 w-5 text-yellow-500" />
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Preview Dialog */}
      {selected_suggestion && (
        <Dialog open={show_preview} onOpenChange={set_show_preview}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Sparkles className="h-5 w-5 text-blue-500" />
                <span>Widget Preview: {selected_suggestion.title}</span>
              </DialogTitle>
              <DialogDescription>
                {selected_suggestion.description}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Widget Details */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium">Widget Type</p>
                  <p className="text-muted-foreground capitalize">{selected_suggestion.widget_type}</p>
                </div>
                <div>
                  <p className="font-medium">Setup Time</p>
                  <p className="text-muted-foreground">{selected_suggestion.estimated_setup_time}</p>
                </div>
                <div>
                  <p className="font-medium">Confidence</p>
                  <p className="text-muted-foreground">{Math.round(selected_suggestion.confidence * 100)}%</p>
                </div>
                <div>
                  <p className="font-medium">Chart Type</p>
                  <p className="text-muted-foreground capitalize">
                    {selected_suggestion.chart_type || 'N/A'}
                  </p>
                </div>
              </div>

              {/* Data Requirements */}
              <div>
                <p className="font-medium mb-2">Required Data Fields</p>
                <div className="flex flex-wrap gap-2">
                  {selected_suggestion.data_requirements.map((req, index) => (
                    <Badge key={index} variant="outline">
                      {req}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Preview Area */}
              <div className="h-48 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                  <p>Widget Preview</p>
                  <p className="text-sm">Live preview will appear here</p>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => set_show_preview(false)}>
                Cancel
              </Button>
              <Button onClick={() => handle_create_widget(selected_suggestion)}>
                <Wand2 className="h-4 w-4 mr-2" />
                Create Widget
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

// Simulate natural language analysis (replace with actual AI service)
async function analyze_natural_language_input(
  input: string,
  available_data_sources: string[]
): Promise<WidgetSuggestion[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  const lower_input = input.toLowerCase();
  const suggestions: WidgetSuggestion[] = [];

  // Simple keyword-based analysis (replace with actual NLP)
  if (lower_input.includes('sales') && lower_input.includes('month')) {
    suggestions.push({
      id: 'sales-monthly-line',
      title: 'Monthly Sales Trend',
      description: 'Line chart showing sales performance over time',
      widget_type: 'chart',
      chart_type: 'line',
      confidence: 0.9,
      data_requirements: ['date', 'sales_amount'],
      preview_config: { x_axis: 'month', y_axis: 'sales' },
      estimated_setup_time: '2 minutes',
    });
  }

  if (lower_input.includes('kpi') || lower_input.includes('total')) {
    suggestions.push({
      id: 'revenue-kpi',
      title: 'Total Revenue KPI',
      description: 'Key performance indicator showing total revenue',
      widget_type: 'kpi',
      confidence: 0.85,
      data_requirements: ['revenue'],
      preview_config: { metric: 'total_revenue', format: 'currency' },
      estimated_setup_time: '1 minute',
    });
  }

  if (lower_input.includes('pie') || lower_input.includes('categories')) {
    suggestions.push({
      id: 'category-pie',
      title: 'Category Distribution',
      description: 'Pie chart showing distribution across categories',
      widget_type: 'chart',
      chart_type: 'pie',
      confidence: 0.8,
      data_requirements: ['category', 'value'],
      preview_config: { category_field: 'category', value_field: 'amount' },
      estimated_setup_time: '2 minutes',
    });
  }

  // Add more suggestions based on input analysis
  return suggestions;
}
