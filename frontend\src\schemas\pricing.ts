// Corresponds to Pydantic schemas in backend/app/models/pricing.py

export interface PricingTierBase {
  name: string;
  description?: string | null;
  base_price: number;
  currency?: string;
  billing_period?: string | null; // e.g., "monthly", "yearly", "one-time"
  features?: string[] | null;
  persona_id?: string | null;
  is_active?: boolean;
}

export interface PricingTierCreate extends PricingTierBase {}

export interface PricingTierResponse extends PricingTierBase {
  id: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface SubscriptionBase {
  user_id: number; // Assuming user ID is number in frontend context too
  pricing_tier_id: string;
  status?: string; // e.g., "active", "cancelled", "past_due"
  auto_renew?: boolean;
}

export interface SubscriptionCreate extends SubscriptionBase {}

export interface SubscriptionResponse extends SubscriptionBase {
  id: string;
  start_date: string; // ISO date string
  end_date?: string | null; // ISO date string
  next_billing_date?: string | null; // ISO date string
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  // The backend Pydantic schema has pricing_tier: Optional[PricingTierResponse] = None
  // This means it can be a full PricingTierResponse object or null.
  pricing_tier?: PricingTierResponse | null; 
}

export interface DiscountBase {
  code: string;
  description?: string | null;
  discount_type: string; // "percentage", "fixed_amount"
  value: number; // Percentage (e.g., 10 for 10%) or fixed amount
  max_uses?: number | null;
  valid_from?: string | null; // ISO date string
  valid_until?: string | null; // ISO date string
  is_active?: boolean;
  applicable_tier_ids?: string[] | null;
}

export interface DiscountCreate extends DiscountBase {}

export interface DiscountResponse extends DiscountBase {
  id: string;
  uses_count: number;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

export interface PromotionBase {
  name: string;
  description?: string | null;
  promotion_type: string; // e.g., "free_trial", "bundle_discount", "upgrade_offer"
  details?: Record<string, any> | null;
  start_date?: string | null; // ISO date string
  end_date?: string | null; // ISO date string
  is_active?: boolean;
  target_audience_criteria?: Record<string, any> | null;
}

export interface PromotionCreate extends PromotionBase {}

export interface PromotionResponse extends PromotionBase {
  id: string;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}

// For Cart API responses if needed by other components
export interface CartTotalRequest {
  discount_code?: string | null;
}

export interface CartItemDetail {
  persona_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency: string;
}

export interface CartTotalResponse {
  items: CartItemDetail[];
  subtotal: number;
  discount_applied?: {
    code: string;
    amount: number;
  } | null;
  total_amount: number;
  currency: string;
}

export interface DynamicPriceResponse {
  price: number;
  currency: string;
  original_price: number;
}
