#!/usr/bin/env python3
"""
Test script to check vector service initialization.
"""

import os
import sys
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_vector_service():
    """Test vector service initialization."""
    try:
        logger.info("Testing vector service initialization...")

        # First test memory service
        logger.info("Testing memory service first...")
        from agents.utils.memory_service import MemoryService
        memory_service = MemoryService()
        logger.info(f"Memory service initialized: {memory_service.initialized}")

        if not memory_service.initialized:
            logger.error("❌ Memory service failed to initialize - this will cause vector service to fail")
            return False

        # Import the vector service
        from agents.utils.vector_service import VectorService

        # Try to initialize the vector service
        vector_service = VectorService()

        if vector_service.initialized:
            logger.info("✅ Vector service initialized successfully!")
            return True
        else:
            logger.error("❌ Vector service failed to initialize")
            logger.error(f"Memory service status: {vector_service.memory_service.initialized if hasattr(vector_service, 'memory_service') else 'No memory service'}")
            return False

    except Exception as e:
        logger.error(f"❌ Error testing vector service: {e}", exc_info=True)
        return False

def test_memory_service():
    """Test memory service initialization."""
    try:
        logger.info("Testing memory service initialization...")

        # Import the memory service
        from agents.utils.memory_service import MemoryService

        # Try to initialize the memory service
        memory_service = MemoryService()

        if memory_service.initialized:
            logger.info("✅ Memory service initialized successfully!")
            return True
        else:
            logger.error("❌ Memory service failed to initialize")
            return False

    except Exception as e:
        logger.error(f"❌ Error testing memory service: {e}", exc_info=True)
        return False

def test_qdrant_connection():
    """Test Qdrant connection."""
    try:
        logger.info("Testing Qdrant connection...")

        from agents.utils.qdrant_manager import QdrantManager

        # Check if Qdrant is running
        if QdrantManager.is_qdrant_running():
            logger.info("✅ Qdrant is running and accessible!")

            # Get connection parameters
            params = QdrantManager.get_qdrant_connection_params()
            logger.info(f"Qdrant connection params: {params}")
            return True
        else:
            logger.error("❌ Qdrant is not running or not accessible")
            return False

    except Exception as e:
        logger.error(f"❌ Error testing Qdrant connection: {e}", exc_info=True)
        return False

def test_ollama_connection():
    """Test Ollama connection."""
    try:
        logger.info("Testing Ollama connection...")

        import requests

        # Check if Ollama is running
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            logger.info(f"✅ Ollama is running with {len(models)} models!")
            for model in models:
                logger.info(f"  - {model.get('name', 'Unknown')}")
            return True
        else:
            logger.error(f"❌ Ollama returned status code: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"❌ Error testing Ollama connection: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("🔍 Starting vector service diagnostics...")

    # Test individual components
    qdrant_ok = test_qdrant_connection()
    ollama_ok = test_ollama_connection()
    memory_ok = test_memory_service()
    vector_ok = test_vector_service()

    # Summary
    logger.info("\n📊 Test Results Summary:")
    logger.info(f"  Qdrant:        {'✅ OK' if qdrant_ok else '❌ FAIL'}")
    logger.info(f"  Ollama:        {'✅ OK' if ollama_ok else '❌ FAIL'}")
    logger.info(f"  Memory Service: {'✅ OK' if memory_ok else '❌ FAIL'}")
    logger.info(f"  Vector Service: {'✅ OK' if vector_ok else '❌ FAIL'}")

    if all([qdrant_ok, ollama_ok, memory_ok, vector_ok]):
        logger.info("\n🎉 All tests passed! Vector service should work correctly.")
        sys.exit(0)
    else:
        logger.error("\n💥 Some tests failed. Check the logs above for details.")
        sys.exit(1)
