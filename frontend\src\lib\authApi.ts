/**
 * Authentication API service for interacting with the backend.
 */
import { User } from '@/contexts/AuthContext';
import { initializeDeviceFingerprint } from '@/utils/deviceFingerprint';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Helper function to get the auth token
const getToken = (): string | null => {
  return localStorage.getItem('token');
};

// Types
export interface LoginRequest {
  email: string;
  password: string;
  device_fingerprint?: string;
  device_info?: DeviceInfo;
}

export interface DeviceInfo {
  user_agent: string;
  platform: string;
  screen_resolution: string;
  timezone: string;
  language: string;
  ip_address?: string; // Will be set by the backend
}

export interface RegisterRequest {
  email: string;
  password: string;
  username?: string;
  first_name?: string;
  last_name?: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface PasswordChangeRequest {
  current_password: string;
  new_password: string;
}

export interface IndustrySelectionRequest {
  industry: string;
  completed?: boolean;
}

export interface PasswordResetRequest {
  email: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
  device_fingerprint?: string;
  device_info?: DeviceInfo;
}

export interface GoogleAuthRequest {
  code: string;
  state?: string;
}

export interface PasswordResetConfirmRequest {
  token: string;
  new_password: string;
}

// Auth API functions
export const authApi = {
  // Login with email and password
  login: async (data: LoginRequest): Promise<TokenResponse> => {
    // Add device fingerprint and info
    const fingerprint = await initializeDeviceFingerprint();
    const deviceInfo: DeviceInfo = {
      user_agent: navigator.userAgent,
      platform: navigator.platform,
      screen_resolution: `${window.screen.width}x${window.screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
    };

    const loginData = {
      ...data,
      device_fingerprint: fingerprint,
      device_info: deviceInfo,
    };

    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Login failed' }));
      throw new Error(error.detail || 'Login failed');
    }

    return response.json();
  },

  // Register a new user
  register: async (data: RegisterRequest): Promise<User> => {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Registration failed' }));
      throw new Error(error.detail || 'Registration failed');
    }

    return response.json();
  },

  // Get current user profile
  getCurrentUser: async (): Promise<User> => {
    console.log('authApi.getCurrentUser called');
    const token = getToken();
    if (!token) {
      console.error('No authentication token found in localStorage');
      throw new Error('No authentication token found');
    }
    console.log('Token found:', token.substring(0, 15) + '...');

    try {
      console.log('Fetching user data from API...');
      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      console.log('Response status:', response.status);
      if (!response.ok) {
        const error = await response.json().catch(() => ({ detail: 'Failed to fetch user data' }));
        console.error('Error response:', error);
        throw new Error(error.detail || 'Failed to fetch user data');
      }

      const userData = await response.json();
      console.log('User data received:', userData);
      return userData;
    } catch (error) {
      console.error('Error in getCurrentUser:', error);
      throw error;
    }
  },

  // Update user profile
  updateUser: async (data: Partial<User>): Promise<User> => {
    const token = getToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Failed to update user data' }));
      throw new Error(error.detail || 'Failed to update user data');
    }

    return response.json();
  },

  // Change password
  changePassword: async (data: PasswordChangeRequest): Promise<{ message: string }> => {
    const token = getToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_BASE_URL}/auth/change-password`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Failed to change password' }));
      throw new Error(error.detail || 'Failed to change password');
    }

    return response.json();
  },

  // Request password reset
  requestPasswordReset: async (data: PasswordResetRequest): Promise<{ message: string }> => {
    const response = await fetch(`${API_BASE_URL}/auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Failed to request password reset' }));
      throw new Error(error.detail || 'Failed to request password reset');
    }

    return response.json();
  },

  // Confirm password reset
  confirmPasswordReset: async (data: PasswordResetConfirmRequest): Promise<{ message: string }> => {
    const response = await fetch(`${API_BASE_URL}/auth/reset-password-confirm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Failed to reset password' }));
      throw new Error(error.detail || 'Failed to reset password');
    }

    return response.json();
  },

  // Refresh token
  refreshToken: async (refreshToken: string): Promise<TokenResponse> => {
    // Add device fingerprint and info
    const fingerprint = await initializeDeviceFingerprint();
    const deviceInfo: DeviceInfo = {
      user_agent: navigator.userAgent,
      platform: navigator.platform,
      screen_resolution: `${window.screen.width}x${window.screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
    };

    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refresh_token: refreshToken,
        device_fingerprint: fingerprint,
        device_info: deviceInfo,
      }),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Failed to refresh token' }));
      throw new Error(error.detail || 'Failed to refresh token');
    }

    return response.json();
  },

  // Logout
  logout: async (): Promise<{ message: string }> => {
    const token = getToken();
    if (!token) {
      return { message: 'Logged out successfully' };
    }

    const response = await fetch(`${API_BASE_URL}/auth/logout`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Failed to logout' }));
      throw new Error(error.detail || 'Failed to logout');
    }

    return response.json();
  },

  // Verify email
  verifyEmail: async (token: string): Promise<{ message: string }> => {
    const response = await fetch(`${API_BASE_URL}/auth/verify-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Failed to verify email' }));
      throw new Error(error.detail || 'Failed to verify email');
    }

    return response.json();
  },

  // Google OAuth login
  googleLogin: async (): Promise<void> => {
    // Redirect to backend endpoint that will redirect to Google
    window.location.href = `${API_BASE_URL}/auth/google/login`;
  },

  // Google OAuth callback
  googleCallback: async (code: string, state?: string): Promise<TokenResponse> => {
    console.log('authApi.googleCallback called with code:', code.substring(0, 10) + '...');

    // Create request body with code and optional state
    const requestBody: { code: string; state?: string } = { code };
    if (state) {
      requestBody.state = state;
      console.log('State parameter included in request');
    } else {
      console.log('No state parameter provided');
    }

    try {
      console.log(`Sending POST request to ${API_BASE_URL}/auth/google/callback`);

      // Add a timeout to the fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`${API_BASE_URL}/auth/google/callback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      // Clear the timeout
      clearTimeout(timeoutId);

      console.log('Response status:', response.status);
      if (!response.ok) {
        let errorDetail = 'Google authentication failed';
        try {
          const errorResponse = await response.json();
          errorDetail = errorResponse.detail || errorDetail;
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        console.error('Error response:', errorDetail);
        throw new Error(errorDetail);
      }

      let data;
      try {
        data = await response.json();
      } catch (e) {
        console.error('Error parsing JSON response:', e);
        throw new Error('Invalid response from server');
      }

      if (!data || !data.access_token) {
        console.error('Invalid token response:', data);
        throw new Error('Invalid token response from server');
      }

      console.log('Token response received:', {
        access_token: data.access_token ? 'present' : 'missing',
        refresh_token: data.refresh_token ? 'present' : 'missing',
        token_type: data.token_type,
        expires_in: data.expires_in
      });
      return data;
    } catch (error) {
      if (error.name === 'AbortError') {
        console.error('Request timed out');
        throw new Error('Request timed out. Please try again.');
      }
      console.error('Error in googleCallback:', error);
      throw error;
    }
  },

  // Update industry selection
  updateIndustrySelection: async (data: IndustrySelectionRequest): Promise<User> => {
    const token = getToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_BASE_URL}/auth/industry-selection`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        industry: data.industry,
        completed: data.completed ?? true,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || 'Failed to update industry selection');
    }

    return response.json();
  },
};
