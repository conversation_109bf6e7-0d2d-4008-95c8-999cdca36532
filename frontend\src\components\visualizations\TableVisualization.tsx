import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Table as TableIcon } from 'lucide-react';
import { VisualizationData } from '@/utils/visualization';
import { SaveToDashboardButton } from './SaveToDashboardButton';

interface TableVisualizationProps {
  visualization: VisualizationData;
  className?: string;
}

export const TableVisualization = ({ visualization, className = '' }: TableVisualizationProps) => {
  const { data, title, description } = visualization;
  const { headers, rows } = data;

  return (
    <Card className={`overflow-hidden shadow-lg border-gray-200 ${className}`}>
      <CardHeader className="pb-2 bg-gradient-to-r from-white to-gray-50 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <TableIcon className="h-5 w-5 text-brand-500" />
          <CardTitle className="text-lg text-brand-700">{title || 'Data Table'}</CardTitle>
        </div>
        {description && <CardDescription className="text-gray-600">{description}</CardDescription>}
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-brand-600 to-brand-700 hover:from-brand-700 hover:to-brand-800">
                {headers.map((header, index) => (
                  <TableHead key={index} className="font-bold text-white text-sm uppercase tracking-wide border-r border-brand-500 last:border-r-0">
                    {header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {rows.map((row, rowIndex) => (
                <TableRow key={rowIndex} className="hover:bg-gray-50 transition-colors duration-150 border-b border-gray-200">
                  {row.map((cell, cellIndex) => (
                    <TableCell key={cellIndex} className="font-medium text-gray-900 border-r border-gray-200 last:border-r-0 py-3">
                      {cell}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between items-center border-t border-gray-100 bg-gray-50 py-3 px-4">
        <div className="text-sm text-gray-500">
          {rows.length} rows × {headers.length} columns
        </div>
        <SaveToDashboardButton visualization={visualization} />
      </CardFooter>
    </Card>
  );
};
