"""create_report_and_schedule_tables

Revision ID: 6898b1ccb51b
Revises: ccc028f0f372 
Create Date: 2025-05-28 17:02:11.306601

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6898b1ccb51b'
down_revision = 'ccc028f0f372' # Point to the last known good migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('reports',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('report_name', sa.String(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('generated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('config_used', sa.JSON(), nullable=False),
    sa.Column('data', sa.JSON(), nullable=False),
    sa.Column('summary', sa.JSON(), nullable=True),
    sa.Column('visualization_config', sa.JSO<PERSON>(), nullable=True),
    sa.Column('data_source_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_reports_data_source_id'), 'reports', ['data_source_id'], unique=False)
    op.create_index(op.f('ix_reports_id'), 'reports', ['id'], unique=False)
    op.create_index(op.f('ix_reports_user_id'), 'reports', ['user_id'], unique=False)
    
    op.create_table('report_schedules',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('report_config', sa.JSON(), nullable=False),
    sa.Column('cron_expression', sa.String(), nullable=False),
    sa.Column('next_run_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('last_run_status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_report_schedules_id'), 'report_schedules', ['id'], unique=False)
    op.create_index(op.f('ix_report_schedules_next_run_time'), 'report_schedules', ['next_run_time'], unique=False)
    op.create_index(op.f('ix_report_schedules_user_id'), 'report_schedules', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_report_schedules_user_id'), table_name='report_schedules')
    op.drop_index(op.f('ix_report_schedules_next_run_time'), table_name='report_schedules')
    op.drop_index(op.f('ix_report_schedules_id'), table_name='report_schedules')
    op.drop_table('report_schedules')
    
    op.drop_index(op.f('ix_reports_user_id'), table_name='reports')
    op.drop_index(op.f('ix_reports_id'), table_name='reports')
    op.drop_index(op.f('ix_reports_data_source_id'), table_name='reports')
    op.drop_table('reports')
    # ### end Alembic commands ###
