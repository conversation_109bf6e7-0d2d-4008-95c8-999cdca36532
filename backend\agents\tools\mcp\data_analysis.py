"""
Data analysis MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for analyzing data using pandas.
"""

import logging
import os
import io
import json
import pandas as pd
from typing import Dict, Any, List, Optional

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class DataAnalysisTool(BaseMCPTool):
    """Tool for analyzing data using pandas."""

    def __init__(self):
        """Initialize the data analysis tool."""
        super().__init__(
            name="analyze_data",
            description="Analyze data using pandas operations",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "operation": {"type": "string", "enum": ["describe", "head", "tail", "info", "sample", "query"]},
                    "params": {"type": "object"}
                },
                "required": ["file_path", "operation"]
            },
            annotations={
                "title": "Analyze Data",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.data_dir = "data"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        self.data_dir = config.get("data_dir", self.data_dir)
        logger.info(f"Initialized data analysis tool with data directory: {self.data_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the data analysis tool.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            file_path = arguments["file_path"]
            operation = arguments["operation"]
            params = arguments.get("params", {})

            # Log the original file path
            logger.info(f"DataAnalysisTool received file_path: {file_path}")
            logger.info(f"Current working directory: {os.getcwd()}")
            logger.info(f"Data directory setting: {self.data_dir}")

            # Check if the path is relative and prepend the data directory
            if not os.path.isabs(file_path):
                original_path = file_path
                file_path = os.path.join(self.data_dir, file_path)
                logger.info(f"Converted relative path '{original_path}' to '{file_path}'")

            # Try multiple possible paths if the file doesn't exist
            if not os.path.exists(file_path):
                logger.warning(f"File not found at path: {file_path}")

                # Try alternative paths
                alternative_paths = [
                    file_path,
                    os.path.join("data", os.path.basename(file_path)),
                    os.path.join("uploads", os.path.basename(file_path)),
                    os.path.join("backend/data", os.path.basename(file_path)),
                    os.path.basename(file_path)
                ]

                logger.info(f"Trying alternative paths: {alternative_paths}")

                for alt_path in alternative_paths:
                    if os.path.exists(alt_path):
                        logger.info(f"Found file at alternative path: {alt_path}")
                        file_path = alt_path
                        break

            # Check if the file exists after trying alternative paths
            if not os.path.exists(file_path):
                logger.error(f"File not found after trying all alternative paths: {file_path}")
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }

            logger.info(f"Using file path: {file_path}")

            # Load the data
            try:
                if file_path.endswith(".csv"):
                    logger.info(f"Loading CSV file: {file_path}")
                    df = pd.read_csv(file_path)
                    logger.info(f"Successfully loaded CSV file with shape: {df.shape}")
                elif file_path.endswith((".xls", ".xlsx")):
                    logger.info(f"Loading Excel file: {file_path}")
                    df = pd.read_excel(file_path)
                    logger.info(f"Successfully loaded Excel file with shape: {df.shape}")
                elif file_path.endswith(".json"):
                    logger.info(f"Loading JSON file: {file_path}")
                    df = pd.read_json(file_path)
                    logger.info(f"Successfully loaded JSON file with shape: {df.shape}")
                else:
                    logger.error(f"Unsupported file format: {file_path}")
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Unsupported file format: {file_path}"
                            }
                        ]
                    }

                # Log the first few rows for debugging
                logger.info(f"First 2 rows of the dataframe:\n{df.head(2)}")

            except Exception as e:
                logger.error(f"Error loading data from {file_path}: {str(e)}", exc_info=True)
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Error loading data from {file_path}: {str(e)}"
                        }
                    ]
                }

            # Prepare metadata with basic info about the dataframe
            metadata = {
                "columns": df.columns.tolist(),
                "shape": df.shape,
                "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()}
            }

            # Perform the operation
            if operation == "describe":
                # Get statistical description
                desc_df = df.describe()
                result = self._describe_to_markdown_table(desc_df)

                # Add the description as structured data for better visualization
                metadata["description"] = desc_df.to_dict()

            elif operation == "head":
                n = params.get("n", 5)
                head_df = df.head(n)
                result = self._dataframe_to_markdown_table(head_df)

                # Add the preview data in a structured format
                metadata["preview_data"] = head_df.to_dict(orient="records")
                metadata["preview_type"] = "head"

            elif operation == "tail":
                n = params.get("n", 5)
                tail_df = df.tail(n)
                result = self._dataframe_to_markdown_table(tail_df)

                # Add the preview data in a structured format
                metadata["preview_data"] = tail_df.to_dict(orient="records")
                metadata["preview_type"] = "tail"

            elif operation == "sample":
                n = params.get("n", 5)
                sample_df = df.sample(n=min(n, len(df)))
                result = self._dataframe_to_markdown_table(sample_df)

                # Add the preview data in a structured format
                metadata["preview_data"] = sample_df.to_dict(orient="records")
                metadata["preview_type"] = "sample"

            elif operation == "info":
                buffer = io.StringIO()
                df.info(buf=buffer)
                result = buffer.getvalue()

                # Add missing values count
                metadata["missing_values"] = df.isna().sum().to_dict()

            elif operation == "query":
                query = params.get("query", "")
                if not query:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Query parameter is required for query operation"
                            }
                        ]
                    }
                try:
                    filtered_df = df.query(query)
                    result = self._dataframe_to_markdown_table(filtered_df.head(10))

                    # Add the filtered data
                    metadata["filtered_data"] = filtered_df.head(10).to_dict(orient="records")
                    metadata["query"] = query
                    metadata["filtered_shape"] = filtered_df.shape

                except Exception as e:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": f"Error executing query: {str(e)}"
                            }
                        ]
                    }
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported operation: {operation}"
                        }
                    ]
                }

            # Create a table visualization for the data preview if available
            table_content = None
            if "preview_data" in metadata or "filtered_data" in metadata:
                preview_data = metadata.get("preview_data") or metadata.get("filtered_data")
                if preview_data:
                    table_content = {
                        "type": "table",
                        "table": {
                            "headers": list(preview_data[0].keys()) if preview_data else [],
                            "rows": [[str(row.get(col, "")) for col in preview_data[0].keys()] for row in preview_data]
                        }
                    }

            # Return the result with metadata
            response = {
                "content": [
                    {
                        "type": "text",
                        "text": result
                    }
                ],
                "metadata": metadata
            }

            # Add table visualization if available
            if table_content:
                response["content"].append(table_content)

            return response
        except Exception as e:
            logger.error(f"Error analyzing data: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error analyzing data: {str(e)}"
                    }
                ]
            }

    def _dataframe_to_markdown_table(self, df: pd.DataFrame) -> str:
        """
        Convert a pandas DataFrame to a markdown table format.

        Args:
            df: DataFrame to convert

        Returns:
            Markdown table string
        """
        if df.empty:
            return "*No data available*"

        try:
            # Get column names
            columns = df.columns.tolist()

            # Create header row
            header_row = "| " + " | ".join(str(col) for col in columns) + " |"

            # Create separator row
            separator_row = "|" + "|".join([" --- " for _ in columns]) + "|"

            # Create data rows
            data_rows = []
            for _, row in df.iterrows():
                row_values = []
                for col in columns:
                    value = row[col]
                    # Format the value appropriately
                    if pd.isna(value):
                        formatted_value = "N/A"
                    elif isinstance(value, float):
                        # Format floats to 2 decimal places if they have decimals
                        if value == int(value):
                            formatted_value = str(int(value))
                        else:
                            formatted_value = f"{value:.2f}"
                    else:
                        formatted_value = str(value)
                    row_values.append(formatted_value)

                data_row = "| " + " | ".join(row_values) + " |"
                data_rows.append(data_row)

            # Combine all parts
            table_lines = [header_row, separator_row] + data_rows
            return "\n".join(table_lines)

        except Exception as e:
            logger.error(f"Error converting DataFrame to markdown table: {e}")
            return f"```\n{df.to_string()}\n```"

    def _describe_to_markdown_table(self, desc_df: pd.DataFrame) -> str:
        """
        Convert a pandas describe() DataFrame to a markdown table format.

        Args:
            desc_df: DataFrame from df.describe()

        Returns:
            Markdown table string
        """
        if desc_df.empty:
            return "*No statistical summary available*"

        try:
            # Get column names (the original data columns)
            columns = desc_df.columns.tolist()

            # Get statistic names (index of describe DataFrame)
            statistics = desc_df.index.tolist()

            # Create header row
            header_row = "| Statistic | " + " | ".join(str(col) for col in columns) + " |"

            # Create separator row
            separator_row = "|-----------|" + "|".join([" --- " for _ in columns]) + "|"

            # Create data rows
            data_rows = []
            for stat in statistics:
                row_values = [f"**{stat}**"]  # Bold statistic name
                for col in columns:
                    value = desc_df.loc[stat, col]
                    # Format the value appropriately
                    if pd.isna(value):
                        formatted_value = "N/A"
                    elif isinstance(value, float):
                        # Format floats to 2 decimal places if they have decimals
                        if value == int(value):
                            formatted_value = str(int(value))
                        else:
                            formatted_value = f"{value:.2f}"
                    else:
                        formatted_value = str(value)
                    row_values.append(formatted_value)

                data_row = "| " + " | ".join(row_values) + " |"
                data_rows.append(data_row)

            # Combine all parts
            table_lines = [header_row, separator_row] + data_rows
            return "\n".join(table_lines)

        except Exception as e:
            logger.error(f"Error converting describe DataFrame to markdown table: {e}")
            return f"```\n{desc_df.to_string()}\n```"
