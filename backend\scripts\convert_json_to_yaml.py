import argparse
import os
import sys
import shutil
import glob

# Add the backend directory to the Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(script_dir)
sys.path.insert(0, backend_dir)

try:
    from app.utils.yaml_utils import convert_file, load_yaml # Also import load_yaml for potential validation
except ImportError as e:
    print(f"Error importing yaml_utils: {e}")
    print("Ensure the script is run from a context where 'backend' is accessible or in the Python path.")
    sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="Convert JSON files to YAML format.")
    parser.add_argument("--directory", required=True, help="Directory containing JSON files to convert.")
    parser.add_argument("--pattern", default="*.json", help="Glob pattern to match JSON files (e.g., '*_info.json'). Default is '*.json'.")
    parser.add_argument("--backup", action="store_true", help="Create a backup (.bak) of the original JSON file.")
    parser.add_argument("--recursive", action="store_true", default=True, help="Search directories recursively. Default is True.") # Defaulting to recursive as per plan examples
    parser.add_argument("--delete-original", action="store_true", help="Delete the original JSON file after successful conversion.")


    args = parser.parse_args()

    if not os.path.isdir(args.directory):
        print(f"Error: Directory not found: {args.directory}")
        sys.exit(1)

    print(f"Starting conversion in directory: {args.directory}")
    print(f"Matching pattern: {args.pattern}")
    print(f"Recursive search: {args.recursive}")
    print(f"Backup original files: {args.backup}")
    print(f"Delete original files: {args.delete_original}")
    print("-" * 30)

    converted_count = 0
    error_count = 0

    search_pattern = os.path.join(args.directory, '**', args.pattern) if args.recursive else os.path.join(args.directory, args.pattern)

    for json_path in glob.glob(search_pattern, recursive=args.recursive):
        if not os.path.isfile(json_path) or not json_path.lower().endswith('.json'):
            continue # Skip directories or non-json files caught by glob

        base_name = os.path.splitext(json_path)[0]
        yaml_path = base_name + ".yaml"

        print(f"Processing: {json_path}")

        try:
            # 1. Backup (if requested)
            if args.backup:
                backup_path = json_path + ".bak"
                print(f"  Creating backup: {backup_path}")
                shutil.copy2(json_path, backup_path) # copy2 preserves metadata

            # 2. Convert
            print(f"  Converting to: {yaml_path}")
            success = convert_file(json_path, yaml_path)

            if success:
                print("  Conversion successful.")
                # 3. Optional: Validate YAML output (basic load check)
                try:
                    load_yaml(yaml_path)
                    print("  YAML validation (load check) successful.")

                    # 4. Delete original (if requested and conversion+validation successful)
                    if args.delete_original:
                        print(f"  Deleting original file: {json_path}")
                        os.remove(json_path)

                    converted_count += 1
                except Exception as e:
                    print(f"  Error validating converted YAML file {yaml_path}: {e}")
                    error_count += 1
                    # Optional: Handle validation failure (e.g., restore from backup?)

            else:
                print(f"  Conversion failed for {json_path}.")
                error_count += 1
                # Optional: Handle conversion failure (e.g., restore from backup?)


        except Exception as e:
            print(f"  An unexpected error occurred processing {json_path}: {e}")
            error_count += 1

    print("-" * 30)
    print(f"Conversion finished.")
    print(f"Successfully converted: {converted_count}")
    print(f"Errors encountered: {error_count}")

if __name__ == "__main__":
    main()
