import React, { useState, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Download, Filter, Search, SortAsc, SortDesc } from 'lucide-react';

// Import recharts components for visualizations
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Treemap,
  TreemapProps,
} from 'recharts';

// Define the classification result type
export interface ClassificationResult {
  text: string;
  label?: string;
  confidence?: number;
  theme?: string;
  category?: string;
  reasoning?: string;
  all_labels?: Record<string, number>;
  error?: string;
}

interface ClassificationResultsVisualizationProps {
  results: ClassificationResult[];
  classificationType: 'llm' | 'huggingface';
  onExport?: () => void;
}

// Define colors for charts
const COLORS = [
  '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8',
  '#82CA9D', '#FFC658', '#8DD1E1', '#A4DE6C', '#D0ED57'
];

export function ClassificationResultsVisualization({
  results,
  classificationType,
  onExport,
}: ClassificationResultsVisualizationProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filterCategory, setFilterCategory] = useState<string>('');

  // Extract all unique categories/labels from results
  const allCategories = useMemo(() => {
    const categories = new Set<string>();
    
    results.forEach(result => {
      if (classificationType === 'llm') {
        if (result.theme) categories.add(result.theme);
        if (result.category) categories.add(result.category);
      } else {
        if (result.label) categories.add(result.label);
        if (result.all_labels) {
          Object.keys(result.all_labels).forEach(label => categories.add(label));
        }
      }
    });
    
    return Array.from(categories);
  }, [results, classificationType]);

  // Filter and sort results
  const filteredResults = useMemo(() => {
    let filtered = [...results];
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(result => 
        result.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (result.label && result.label.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (result.theme && result.theme.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (result.category && result.category.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    
    // Apply category filter
    if (filterCategory) {
      filtered = filtered.filter(result => 
        (result.label && result.label === filterCategory) ||
        (result.theme && result.theme === filterCategory) ||
        (result.category && result.category === filterCategory)
      );
    }
    
    // Apply sorting
    if (sortField) {
      filtered.sort((a, b) => {
        let aValue: any = a[sortField as keyof ClassificationResult];
        let bValue: any = b[sortField as keyof ClassificationResult];
        
        // Handle undefined values
        if (aValue === undefined) return sortDirection === 'asc' ? -1 : 1;
        if (bValue === undefined) return sortDirection === 'asc' ? 1 : -1;
        
        // Compare values
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === 'asc' 
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        } else {
          return sortDirection === 'asc' 
            ? (aValue > bValue ? 1 : -1)
            : (aValue < bValue ? 1 : -1);
        }
      });
    }
    
    return filtered;
  }, [results, searchTerm, filterCategory, sortField, sortDirection]);

  // Prepare data for category distribution chart
  const categoryDistributionData = useMemo(() => {
    const distribution: Record<string, number> = {};
    
    results.forEach(result => {
      if (classificationType === 'llm') {
        const category = result.category || 'Unknown';
        distribution[category] = (distribution[category] || 0) + 1;
      } else {
        const label = result.label || 'Unknown';
        distribution[label] = (distribution[label] || 0) + 1;
      }
    });
    
    return Object.entries(distribution).map(([name, value]) => ({ name, value }));
  }, [results, classificationType]);

  // Prepare data for confidence distribution chart (Hugging Face only)
  const confidenceDistributionData = useMemo(() => {
    if (classificationType !== 'huggingface') return [];
    
    const ranges = [
      { name: '0.0-0.2', min: 0, max: 0.2, count: 0 },
      { name: '0.2-0.4', min: 0.2, max: 0.4, count: 0 },
      { name: '0.4-0.6', min: 0.4, max: 0.6, count: 0 },
      { name: '0.6-0.8', min: 0.6, max: 0.8, count: 0 },
      { name: '0.8-1.0', min: 0.8, max: 1.0, count: 0 },
    ];
    
    results.forEach(result => {
      if (result.confidence !== undefined) {
        const range = ranges.find(r => result.confidence! >= r.min && result.confidence! < r.max);
        if (range) range.count++;
      }
    });
    
    return ranges;
  }, [results, classificationType]);

  // Prepare data for hierarchical visualization (LLM only)
  const hierarchicalData = useMemo(() => {
    if (classificationType !== 'llm') return { name: 'root', children: [] };
    
    const themeMap: Record<string, Record<string, number>> = {};
    
    results.forEach(result => {
      const theme = result.theme || 'Unknown';
      const category = result.category || 'Unknown';
      
      if (!themeMap[theme]) {
        themeMap[theme] = {};
      }
      
      themeMap[theme][category] = (themeMap[theme][category] || 0) + 1;
    });
    
    const children = Object.entries(themeMap).map(([theme, categories]) => ({
      name: theme,
      children: Object.entries(categories).map(([category, count]) => ({
        name: category,
        value: count,
      })),
    }));
    
    return {
      name: 'root',
      children,
    };
  }, [results, classificationType]);

  // Toggle sort direction
  const toggleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Classification Results</CardTitle>
        <CardDescription>
          {results.length} texts classified using {classificationType === 'llm' ? 'LLM' : 'Hugging Face'} classification
        </CardDescription>
        <div className="flex flex-col sm:flex-row gap-2 mt-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search results..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select value={filterCategory} onValueChange={setFilterCategory}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All categories</SelectItem>
              {allCategories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {onExport && (
            <Button variant="outline" onClick={onExport}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="table">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="table">Table View</TabsTrigger>
            <TabsTrigger value="charts">Charts</TabsTrigger>
            <TabsTrigger value="hierarchy">Hierarchy</TabsTrigger>
          </TabsList>
          
          <TabsContent value="table" className="mt-4">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[300px]">
                      <Button 
                        variant="ghost" 
                        onClick={() => toggleSort('text')}
                        className="flex items-center gap-1 font-medium"
                      >
                        Text
                        {sortField === 'text' && (
                          sortDirection === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                        )}
                      </Button>
                    </TableHead>
                    {classificationType === 'llm' ? (
                      <>
                        <TableHead>
                          <Button 
                            variant="ghost" 
                            onClick={() => toggleSort('theme')}
                            className="flex items-center gap-1 font-medium"
                          >
                            Theme
                            {sortField === 'theme' && (
                              sortDirection === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                            )}
                          </Button>
                        </TableHead>
                        <TableHead>
                          <Button 
                            variant="ghost" 
                            onClick={() => toggleSort('category')}
                            className="flex items-center gap-1 font-medium"
                          >
                            Category
                            {sortField === 'category' && (
                              sortDirection === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                            )}
                          </Button>
                        </TableHead>
                      </>
                    ) : (
                      <>
                        <TableHead>
                          <Button 
                            variant="ghost" 
                            onClick={() => toggleSort('label')}
                            className="flex items-center gap-1 font-medium"
                          >
                            Label
                            {sortField === 'label' && (
                              sortDirection === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                            )}
                          </Button>
                        </TableHead>
                        <TableHead>
                          <Button 
                            variant="ghost" 
                            onClick={() => toggleSort('confidence')}
                            className="flex items-center gap-1 font-medium"
                          >
                            Confidence
                            {sortField === 'confidence' && (
                              sortDirection === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
                            )}
                          </Button>
                        </TableHead>
                      </>
                    )}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredResults.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={classificationType === 'llm' ? 3 : 3} className="text-center py-4">
                        No results found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredResults.map((result, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{result.text}</TableCell>
                        {classificationType === 'llm' ? (
                          <>
                            <TableCell>
                              {result.theme ? (
                                <Badge variant="outline">{result.theme}</Badge>
                              ) : (
                                <span className="text-muted-foreground">N/A</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {result.category ? (
                                <Badge variant="outline">{result.category}</Badge>
                              ) : (
                                <span className="text-muted-foreground">N/A</span>
                              )}
                            </TableCell>
                          </>
                        ) : (
                          <>
                            <TableCell>
                              {result.label ? (
                                <Badge variant="outline">{result.label}</Badge>
                              ) : (
                                <span className="text-muted-foreground">N/A</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {result.confidence !== undefined ? (
                                <div className="flex items-center gap-2">
                                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                                    <div 
                                      className="bg-primary h-2.5 rounded-full" 
                                      style={{ width: `${result.confidence * 100}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-sm">{(result.confidence * 100).toFixed(0)}%</span>
                                </div>
                              ) : (
                                <span className="text-muted-foreground">N/A</span>
                              )}
                            </TableCell>
                          </>
                        )}
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
          
          <TabsContent value="charts" className="mt-4 space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Category Distribution</h3>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={categoryDistributionData}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {categoryDistributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} texts`, 'Count']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
            
            {classificationType === 'huggingface' && confidenceDistributionData.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-2">Confidence Distribution</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={confidenceDistributionData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value} texts`, 'Count']} />
                      <Legend />
                      <Bar dataKey="count" name="Number of texts" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="hierarchy" className="mt-4">
            {classificationType === 'llm' ? (
              <div>
                <h3 className="text-lg font-medium mb-2">Hierarchical Classification</h3>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <Treemap
                      data={hierarchicalData.children}
                      dataKey="value"
                      ratio={4/3}
                      stroke="#fff"
                      fill="#8884d8"
                      content={<CustomizedContent colors={COLORS} />}
                    />
                  </ResponsiveContainer>
                </div>
                <div className="mt-4 text-sm text-muted-foreground">
                  The treemap shows the hierarchical relationship between themes and categories.
                  Larger blocks represent more frequent classifications.
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                Hierarchical visualization is only available for LLM classification
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between text-sm text-muted-foreground">
        <div>Showing {filteredResults.length} of {results.length} results</div>
        {filterCategory && (
          <div>
            Filtered by: <Badge variant="outline">{filterCategory}</Badge>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}

// Custom Treemap content component
interface CustomizedContentProps {
  root?: any;
  depth?: number;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  index?: number;
  colors: string[];
  name?: string;
  children?: any[];
}

const CustomizedContent = (props: CustomizedContentProps) => {
  const { root, depth = 0, x = 0, y = 0, width = 0, height = 0, index = 0, colors, name } = props;

  return (
    <g>
      <rect
        x={x}
        y={y}
        width={width}
        height={height}
        style={{
          fill: depth === 1 ? colors[index % colors.length] : 'rgba(255,255,255,0.3)',
          stroke: '#fff',
          strokeWidth: 2 / (depth + 1e-10),
          strokeOpacity: 1 / (depth + 1e-10),
        }}
      />
      {depth === 1 ? (
        <text
          x={x + width / 2}
          y={y + height / 2 + 7}
          textAnchor="middle"
          fill="#fff"
          fontSize={14}
        >
          {name}
        </text>
      ) : null}
      {depth === 1 ? (
        <text
          x={x + 4}
          y={y + 18}
          fill="#fff"
          fontSize={16}
          fillOpacity={0.9}
        >
          {name}
        </text>
      ) : null}
    </g>
  );
};
