import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MoreVertical,
  Settings,
  BarChart3,
  Maximize2,
  Minimize2,
  Move,
  Copy,
  Trash2,
  RefreshCw,
  Download,
  Share,
  Eye,
  EyeOff,
  Lock,
  Unlock
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { WidgetResponse } from '@/types/dashboard-customization';

interface EnhancedWidgetWrapperProps {
  widget: WidgetResponse;
  children: React.ReactNode;
  isEditing?: boolean;
  isExpanded?: boolean;
  isSelected?: boolean;
  isDragging?: boolean;
  isResizing?: boolean;
  onEdit?: () => void;
  onCustomize?: () => void;
  onInsights?: () => void;
  onExpand?: () => void;
  onDuplicate?: () => void;
  onRemove?: () => void;
  onRefresh?: () => void;
  onExport?: () => void;
  onShare?: () => void;
  onToggleVisibility?: () => void;
  onToggleLock?: () => void;
  onMove?: () => void;
  className?: string;
}

export const EnhancedWidgetWrapper: React.FC<EnhancedWidgetWrapperProps> = ({
  widget,
  children,
  isEditing = false,
  isExpanded = false,
  isSelected = false,
  isDragging = false,
  isResizing = false,
  onEdit,
  onCustomize,
  onInsights,
  onExpand,
  onDuplicate,
  onRemove,
  onRefresh,
  onExport,
  onShare,
  onToggleVisibility,
  onToggleLock,
  onMove,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const handleRefresh = async () => {
    if (!onRefresh) return;
    setIsLoading(true);
    try {
      await onRefresh();
    } finally {
      setIsLoading(false);
    }
  };

  const getWidgetIcon = (type: string) => {
    const icons = {
      chart: BarChart3,
      table: BarChart3,
      kpi: BarChart3,
      gauge: BarChart3,
      default: BarChart3
    };
    return icons[type as keyof typeof icons] || icons.default;
  };

  const IconComponent = getWidgetIcon(widget.widget_type);

  return (
    <TooltipProvider>
      <motion.div
        ref={cardRef}
        layout
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ 
          opacity: 1, 
          scale: 1,
          rotateX: isDragging ? 5 : 0,
          rotateY: isDragging ? 5 : 0,
          z: isDragging ? 50 : 0
        }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ 
          duration: 0.2,
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
        className={cn(
          "group relative",
          isExpanded && "fixed inset-4 z-50",
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Card className={cn(
          "dashboard-widget h-full transition-all duration-200",
          "hover:shadow-lg hover:shadow-blue-500/10",
          "border-2 transition-colors",
          isSelected && "border-blue-500 ring-2 ring-blue-500/20",
          isDragging && "shadow-2xl border-blue-400 bg-blue-50/50 dark:bg-blue-900/20",
          isResizing && "border-green-400 ring-2 ring-green-400/20",
          !widget.is_active && "opacity-60",
          isExpanded && "h-full flex flex-col shadow-2xl"
        )}>
          {/* Widget Header */}
          <div className={cn(
            "dashboard-widget-header relative",
            "bg-gradient-to-r from-transparent to-slate-50/50 dark:to-slate-800/50"
          )}>
            <div className="flex items-center justify-between">
              {/* Left side - Icon and Title */}
              <div className="flex items-center space-x-3 min-w-0 flex-1">
                <div className={cn(
                  "p-1.5 rounded-lg transition-colors",
                  "bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400"
                )}>
                  <IconComponent className="h-4 w-4" />
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="font-semibold text-slate-900 dark:text-slate-100 truncate">
                    {widget.title}
                  </h3>
                  {widget.description && (
                    <p className="text-xs text-slate-500 dark:text-slate-400 truncate">
                      {widget.description}
                    </p>
                  )}
                </div>
              </div>

              {/* Right side - Status and Actions */}
              <div className="flex items-center space-x-2">
                {/* Status indicators */}
                <div className="flex items-center space-x-1">
                  {!widget.is_active && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div>
                          <Badge variant="secondary" className="text-xs">
                            <EyeOff className="h-3 w-3 mr-1" />
                            Hidden
                          </Badge>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>Widget is hidden</TooltipContent>
                    </Tooltip>
                  )}
                  
                  {isLoading && (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <RefreshCw className="h-4 w-4 text-blue-500" />
                    </motion.div>
                  )}
                </div>

                {/* Quick actions - visible on hover or editing */}
                <AnimatePresence>
                  {(isHovered || isEditing || isExpanded) && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="flex items-center space-x-1"
                    >
                      {/* Expand/Minimize */}
                      {onExpand && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={onExpand}
                              className="h-7 w-7 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/50"
                            >
                              {isExpanded ? (
                                <Minimize2 className="h-3 w-3" />
                              ) : (
                                <Maximize2 className="h-3 w-3" />
                              )}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            {isExpanded ? 'Minimize' : 'Expand'}
                          </TooltipContent>
                        </Tooltip>
                      )}

                      {/* Refresh */}
                      {onRefresh && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleRefresh}
                              disabled={isLoading}
                              className="h-7 w-7 p-0 hover:bg-green-100 dark:hover:bg-green-900/50"
                            >
                              <RefreshCw className={cn(
                                "h-3 w-3",
                                isLoading && "animate-spin"
                              )} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Refresh widget</TooltipContent>
                        </Tooltip>
                      )}

                      {/* More actions menu */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 p-0 hover:bg-slate-100 dark:hover:bg-slate-700"
                          >
                            <MoreVertical className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          {onEdit && (
                            <DropdownMenuItem onClick={onEdit}>
                              <Settings className="h-4 w-4 mr-2" />
                              Edit Widget
                            </DropdownMenuItem>
                          )}
                          {onCustomize && (
                            <DropdownMenuItem onClick={onCustomize}>
                              <Settings className="h-4 w-4 mr-2" />
                              Customize
                            </DropdownMenuItem>
                          )}
                          {onInsights && (
                            <DropdownMenuItem onClick={onInsights}>
                              <BarChart3 className="h-4 w-4 mr-2" />
                              View Insights
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuSeparator />
                          
                          {onDuplicate && (
                            <DropdownMenuItem onClick={onDuplicate}>
                              <Copy className="h-4 w-4 mr-2" />
                              Duplicate
                            </DropdownMenuItem>
                          )}
                          {onMove && (
                            <DropdownMenuItem onClick={onMove}>
                              <Move className="h-4 w-4 mr-2" />
                              Move to Section
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuSeparator />
                          
                          {onExport && (
                            <DropdownMenuItem onClick={onExport}>
                              <Download className="h-4 w-4 mr-2" />
                              Export Data
                            </DropdownMenuItem>
                          )}
                          {onShare && (
                            <DropdownMenuItem onClick={onShare}>
                              <Share className="h-4 w-4 mr-2" />
                              Share Widget
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuSeparator />
                          
                          {onToggleVisibility && (
                            <DropdownMenuItem onClick={onToggleVisibility}>
                              {widget.is_active ? (
                                <>
                                  <EyeOff className="h-4 w-4 mr-2" />
                                  Hide Widget
                                </>
                              ) : (
                                <>
                                  <Eye className="h-4 w-4 mr-2" />
                                  Show Widget
                                </>
                              )}
                            </DropdownMenuItem>
                          )}
                          
                          {onRemove && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={onRemove}
                                className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Remove Widget
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Drag handle for editing mode */}
            {isEditing && (
              <div className="absolute -top-2 -left-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center cursor-move shadow-lg">
                <Move className="h-3 w-3 text-white" />
              </div>
            )}
          </div>

          {/* Widget Content */}
          <CardContent className={cn(
            "dashboard-widget-content flex-1",
            isExpanded && "overflow-auto"
          )}>
            {children}
          </CardContent>

          {/* Resize handles for editing mode */}
          {isEditing && !isExpanded && (
            <>
              {/* Corner resize handle */}
              <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-green-500 rounded-full cursor-se-resize shadow-lg opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="absolute inset-1 border-r border-b border-white"></div>
              </div>
              
              {/* Edge resize handles */}
              <div className="absolute -right-1 top-1/2 -translate-y-1/2 w-2 h-8 bg-green-400 rounded cursor-e-resize opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-8 h-2 bg-green-400 rounded cursor-s-resize opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </>
          )}
        </Card>

        {/* Selection overlay */}
        {isSelected && (
          <div className="absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none bg-blue-500/5"></div>
        )}

        {/* Drag preview overlay */}
        {isDragging && (
          <div className="absolute inset-0 bg-blue-500/10 rounded-lg pointer-events-none border-2 border-dashed border-blue-400"></div>
        )}
      </motion.div>
    </TooltipProvider>
  );
};
