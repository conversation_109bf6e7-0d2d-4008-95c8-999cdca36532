/**
 * Comprehensive tests for the Enhanced Dashboard Ribbon Toolbar
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { EnhancedRibbonToolbar } from '../mode/advanced/EnhancedRibbonToolbar';

// Mock dependencies
vi.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: vi.fn(),
  }),
}));

vi.mock('react-hotkeys-hook', () => ({
  useHotkeys: vi.fn(),
}));

vi.mock('../RibbonAccessibility', () => ({
  useScreenReaderAnnouncements: () => ({
    announceAction: vi.fn(),
    AnnouncementRegion: () => <div data-testid="announcement-region" />,
  }),
  useRibbonKeyboardNavigation: () => ({
    ribbonRef: { current: null },
    focusNext: vi.fn(),
    focusPrevious: vi.fn(),
  }),
  useAccessibilityPreferences: () => ({
    isHighContrast: false,
    isReducedMotion: false,
    screenReaderMode: false,
  }),
  useResponsiveBreakpoints: () => ({
    breakpoint: 'desktop',
    isMobile: false,
    isTablet: false,
    isDesktop: true,
  }),
}));

vi.mock('../RibbonAdvancedFeatures', () => ({
  useUndoRedo: () => ({
    history: [],
    addAction: vi.fn(),
    undo: vi.fn(),
    redo: vi.fn(),
    canUndo: false,
    canRedo: false,
  }),
  useRibbonPreferences: () => ({
    preferences: {
      quickAccessItems: ['save', 'undo', 'redo', 'refresh'],
      collapsedGroups: [],
      activeTab: 'file',
      compactMode: false,
      showTooltips: true,
      keyboardShortcuts: {},
    },
    updatePreferences: vi.fn(),
  }),
  RibbonContextMenu: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  CommandPalette: () => <div data-testid="command-palette" />,
}));

// RibbonTabsContent functionality is now consolidated into EnhancedRibbonToolbar

describe('DashboardRibbonToolbar', () => {
  const defaultProps = {
    onSave: vi.fn(),
    onExport: vi.fn(),
    onAddWidget: vi.fn(),
    onRefresh: vi.fn(),
    onSettings: vi.fn(),
    widgetCount: 5,
    isConnected: true,
    userPermissions: {
      canEdit: true,
      canShare: true,
      canExport: true,
      canManageUsers: false,
      canAccessSettings: true,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders the ribbon toolbar with all tabs', () => {
      render(<EnhancedRibbonToolbar {...defaultProps} />);

      expect(screen.getByRole('toolbar')).toBeInTheDocument();
      expect(screen.getByText('File')).toBeInTheDocument();
      expect(screen.getByText('Data')).toBeInTheDocument();
      expect(screen.getByText('Insert')).toBeInTheDocument();
      expect(screen.getByText('Style')).toBeInTheDocument();
      expect(screen.getByText('Analyze')).toBeInTheDocument();
      expect(screen.getByText('Share')).toBeInTheDocument();
      expect(screen.getByText('Admin')).toBeInTheDocument();
    });

    it('renders with proper accessibility attributes', () => {
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const toolbar = screen.getByRole('toolbar');
      expect(toolbar).toHaveAttribute('aria-label', 'Dashboard ribbon toolbar');
      expect(toolbar).toHaveAttribute('aria-orientation', 'horizontal');
    });

    it('renders announcement region for screen readers', () => {
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      expect(screen.getByTestId('announcement-region')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    it('starts with File tab active by default', () => {
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const fileTab = screen.getByRole('tab', { name: /file operations tab/i });
      expect(fileTab).toHaveAttribute('aria-selected', 'true');
    });

    it('switches tabs when clicked', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const dataTab = screen.getByRole('tab', { name: /data/i });
      await user.click(dataTab);
      
      expect(dataTab).toHaveAttribute('aria-selected', 'true');
    });

    it('shows correct tab content when switching tabs', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      // File tab should show dashboard operations
      expect(screen.getByText('Open')).toBeInTheDocument();
      expect(screen.getByText('Save')).toBeInTheDocument();
      
      // Switch to Data tab
      const dataTab = screen.getByRole('tab', { name: /data/i });
      await user.click(dataTab);
      
      // Data tab should show data operations
      expect(screen.getByText('New Connection')).toBeInTheDocument();
      expect(screen.getByText('Refresh All')).toBeInTheDocument();
    });
  });

  describe('File Tab Functionality', () => {
    it('calls onSave when Save button is clicked', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const saveButton = screen.getByRole('button', { name: /save dashboard/i });
      await user.click(saveButton);
      
      expect(defaultProps.onSave).toHaveBeenCalledTimes(1);
    });

    it('shows export dropdown with format options', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const exportButton = screen.getByRole('button', { name: /export/i });
      await user.click(exportButton);
      
      expect(screen.getByText('PDF Document')).toBeInTheDocument();
      expect(screen.getByText('Excel Workbook')).toBeInTheDocument();
      expect(screen.getByText('CSV Data')).toBeInTheDocument();
    });

    it('calls onExport with correct format when export option is selected', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const exportButton = screen.getByRole('button', { name: /export/i });
      await user.click(exportButton);
      
      const pdfOption = screen.getByText('PDF Document');
      await user.click(pdfOption);
      
      expect(defaultProps.onExport).toHaveBeenCalledWith('pdf');
    });
  });

  describe('Data Tab Functionality', () => {
    beforeEach(async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const dataTab = screen.getByRole('tab', { name: /data/i });
      await user.click(dataTab);
    });

    it('shows data connection options', () => {
      expect(screen.getByText('New Connection')).toBeInTheDocument();
      expect(screen.getByText('Refresh All')).toBeInTheDocument();
    });

    it('calls onRefresh when refresh button is clicked', async () => {
      const user = userEvent.setup();
      
      const refreshButton = screen.getByRole('button', { name: /refresh all data/i });
      await user.click(refreshButton);
      
      expect(defaultProps.onRefresh).toHaveBeenCalledTimes(1);
    });

    it('shows import data options in dropdown', async () => {
      const user = userEvent.setup();
      
      const importButton = screen.getByRole('button', { name: /import data/i });
      await user.click(importButton);
      
      expect(screen.getByText('CSV File')).toBeInTheDocument();
      expect(screen.getByText('Excel File')).toBeInTheDocument();
      expect(screen.getByText('SQL Database')).toBeInTheDocument();
      expect(screen.getByText('API Endpoint')).toBeInTheDocument();
    });
  });

  describe('Insert Tab Functionality', () => {
    beforeEach(async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const insertTab = screen.getByRole('tab', { name: /insert/i });
      await user.click(insertTab);
    });

    it('shows widget creation options', () => {
      expect(screen.getByText('Add Widget')).toBeInTheDocument();
      expect(screen.getByText('Section')).toBeInTheDocument();
    });

    it('calls onAddWidget when widget type is selected', async () => {
      const user = userEvent.setup();
      
      const addWidgetButton = screen.getByRole('button', { name: /add widget/i });
      await user.click(addWidgetButton);
      
      const chartOption = screen.getByText('Bar Chart');
      await user.click(chartOption);
      
      expect(defaultProps.onAddWidget).toHaveBeenCalledWith('chart');
    });

    it('shows chart type quick buttons', () => {
      expect(screen.getByRole('button', { name: /bar chart/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /line chart/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /pie chart/i })).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('opens search dialog when search button is clicked', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const searchButton = screen.getByRole('button', { name: /search actions/i });
      await user.click(searchButton);
      
      expect(screen.getByText('Search Ribbon Actions')).toBeInTheDocument();
    });

    it('filters actions based on search query', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const searchButton = screen.getByRole('button', { name: /search actions/i });
      await user.click(searchButton);
      
      const searchInput = screen.getByPlaceholderText('Search actions...');
      await user.type(searchInput, 'save');
      
      expect(screen.getByText('Save Dashboard')).toBeInTheDocument();
    });
  });

  describe('Quick Access Toolbar', () => {
    it('renders quick access toolbar with default actions', () => {
      render(<DashboardRibbonToolbar {...defaultProps} showQuickAccess={true} />);
      
      // Quick access should be rendered (mocked component)
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });

    it('opens customization dialog when customize button is clicked', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} showQuickAccess={true} />);
      
      // This would be tested with the actual implementation
      // For now, we verify the component structure exists
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('shows mobile interface on small screens', () => {
      // Mock mobile breakpoint
      vi.mocked(require('../RibbonAccessibility').useResponsiveBreakpoints).mockReturnValue({
        breakpoint: 'mobile',
        isMobile: true,
        isTablet: false,
        isDesktop: false,
      });

      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      expect(screen.getByTestId('mobile-ribbon-interface')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels for all interactive elements', () => {
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const toolbar = screen.getByRole('toolbar');
      expect(toolbar).toHaveAttribute('aria-label');
      
      const tablist = screen.getByRole('tablist');
      expect(tablist).toHaveAttribute('aria-label');
    });

    it('manages focus properly when switching tabs', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const dataTab = screen.getByRole('tab', { name: /data/i });
      await user.click(dataTab);
      
      expect(dataTab).toHaveFocus();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      const fileTab = screen.getByRole('tab', { name: /file operations tab/i });
      fileTab.focus();
      
      await user.keyboard('{ArrowRight}');
      
      // This would test actual keyboard navigation in a real implementation
      expect(fileTab).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles missing props gracefully', () => {
      const minimalProps = {};
      
      expect(() => {
        render(<DashboardRibbonToolbar {...minimalProps} />);
      }).not.toThrow();
    });

    it('handles undefined callback functions', async () => {
      const user = userEvent.setup();
      const propsWithUndefined = {
        ...defaultProps,
        onSave: undefined,
        onExport: undefined,
      };
      
      render(<DashboardRibbonToolbar {...propsWithUndefined} />);
      
      // Should not throw when clicking buttons with undefined callbacks
      const saveButton = screen.getByRole('button', { name: /save dashboard/i });
      await user.click(saveButton);
      
      expect(saveButton).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('does not re-render unnecessarily', () => {
      const { rerender } = render(<DashboardRibbonToolbar {...defaultProps} />);
      
      // Re-render with same props
      rerender(<DashboardRibbonToolbar {...defaultProps} />);
      
      // Component should still be rendered correctly
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });

    it('memoizes expensive computations', () => {
      render(<DashboardRibbonToolbar {...defaultProps} />);
      
      // Verify that computed values like widget types and export formats
      // are properly memoized (this would be tested with performance monitoring)
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });
  });
});
