import { VisualizationData } from '@/utils/visualization';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { BarChart3, Database, Info, Image as ImageIcon, FileText } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import type { Components } from 'react-markdown';

interface AnalysisResultVisualizationProps {
  visualization: VisualizationData;
}

export const AnalysisResultVisualization = ({ visualization }: AnalysisResultVisualizationProps) => {
  const { data } = visualization;

  // Handle new mixed content format
  if (data.content && Array.isArray(data.content)) {
    return <MixedContentVisualization visualization={visualization} />;
  }

  // Handle legacy format
  const { preview_data, columns, description, metadata } = data;

  // Parse tables from description text if available
  const parseTablesFromText = (text: string) => {
    const tables: Array<{ headers: string[], rows: string[][] }> = [];
    
    // Look for markdown-style tables
    const lines = text.split('\n');
    let currentTable: { headers: string[], rows: string[][] } | null = null;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Check if this line looks like a table header
      if (line.includes('|') && line.split('|').length > 2) {
        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);
        
        // Check if next line is a separator (contains dashes)
        if (i + 1 < lines.length && lines[i + 1].includes('-')) {
          // This is a header row
          currentTable = { headers: cells, rows: [] };
          i++; // Skip the separator line
        } else if (currentTable) {
          // This is a data row
          currentTable.rows.push(cells);
        }
      } else if (currentTable && currentTable.rows.length > 0) {
        // End of table
        tables.push(currentTable);
        currentTable = null;
      }
    }
    
    // Add the last table if it exists
    if (currentTable && currentTable.rows.length > 0) {
      tables.push(currentTable);
    }
    
    return tables;
  };

  const parsedTables = description ? parseTablesFromText(description) : [];

  return (
    <Card className="shadow-md border-gray-200 w-full">
      <CardHeader className="bg-gradient-to-r from-white to-gray-50 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-brand-500" />
          <CardTitle className="text-brand-700">{visualization.title || 'Analysis Results'}</CardTitle>
        </div>
        {visualization.description && <CardDescription>{visualization.description}</CardDescription>}
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="results" className="w-full">
          <TabsList className="w-full border-b rounded-none px-4 bg-gray-50">
            <TabsTrigger value="results">Results</TabsTrigger>
            {preview_data && preview_data.length > 0 && <TabsTrigger value="data">Data Preview</TabsTrigger>}
            {metadata && Object.keys(metadata).length > 0 && <TabsTrigger value="info">Data Info</TabsTrigger>}
          </TabsList>

          <TabsContent value="results" className="p-4">
            {parsedTables.length > 0 ? (
              <div className="space-y-6">
                {parsedTables.map((table, tableIndex) => (
                  <div key={tableIndex} className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {table.headers.map((header, index) => (
                            <TableHead key={index} className="font-medium text-gray-700">
                              {header}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {table.rows.map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {row.map((cell, cellIndex) => (
                              <TableCell key={cellIndex} className="text-gray-700">
                                {cell}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ))}
              </div>
            ) : description ? (
              <div className="prose prose-sm max-w-none">
                <ReactMarkdown
                  components={{
                    table: ({ children, ...props }) => (
                      <div className="overflow-x-auto my-4 border-2 border-gray-700 rounded-lg shadow-lg">
                        <table className="w-full border-collapse bg-white" {...props}>
                          {children}
                        </table>
                      </div>
                    ),
                    thead: ({ children, ...props }) => (
                      <thead className="bg-gray-800 text-white" {...props}>{children}</thead>
                    ),
                    th: ({ children, ...props }) => (
                      <th className="border border-gray-600 px-4 py-3 text-left font-bold text-white text-sm uppercase tracking-wide" {...props}>
                        {children}
                      </th>
                    ),
                    td: ({ children, ...props }) => (
                      <td className="border border-gray-300 px-4 py-3 text-gray-900 font-medium" {...props}>
                        {children}
                      </td>
                    ),
                    pre: ({ children, ...props }) => (
                      <pre className="bg-gray-100 border border-gray-300 p-4 rounded-lg overflow-x-auto text-sm text-gray-800" {...props}>
                        {children}
                      </pre>
                    ),
                    code: ({ children, ...props }) => (
                      <code className="bg-gray-200 border border-gray-300 px-2 py-1 rounded text-sm font-mono text-gray-800" {...props}>
                        {children}
                      </code>
                    )
                  }}
                >
                  {description}
                </ReactMarkdown>
              </div>
            ) : (
              <div className="text-center p-8 text-gray-500">
                <Database className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                <p>No analysis results available</p>
              </div>
            )}
          </TabsContent>

          {preview_data && preview_data.length > 0 && (
            <TabsContent value="data" className="p-4">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {Object.keys(preview_data[0]).map((column, index) => (
                        <TableHead key={index} className="font-medium text-gray-700">
                          {column}
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {preview_data.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {Object.values(row).map((value, cellIndex) => (
                          <TableCell key={cellIndex} className="text-gray-700">
                            {String(value)}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          )}

          {metadata && Object.keys(metadata).length > 0 && (
            <TabsContent value="info" className="p-4">
              <div className="space-y-4">
                {metadata.shape && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                      <Info className="h-4 w-4" />
                      Data Shape
                    </h3>
                    <div className="bg-gray-50 p-3 rounded text-sm">
                      <p>Rows: {metadata.shape[0]}, Columns: {metadata.shape[1]}</p>
                    </div>
                  </div>
                )}

                {columns && columns.length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Columns</h3>
                    <div className="bg-gray-50 p-3 rounded text-sm">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {columns.map((column, index) => (
                          <div key={index} className="flex items-center justify-between bg-white p-2 rounded border">
                            <span className="font-medium">{column}</span>
                            {metadata.dtypes && metadata.dtypes[column] && (
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                {metadata.dtypes[column]}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {metadata.missing_values && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Missing Values</h3>
                    <div className="bg-gray-50 p-3 rounded text-sm">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {Object.entries(metadata.missing_values).map(([column, count], index) => (
                          <div key={index} className="flex items-center justify-between bg-white p-2 rounded border">
                            <span>{column}</span>
                            <span className={`font-medium ${count === 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {count} missing
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
};

// New component to handle mixed content (text, tables, images)
const MixedContentVisualization = ({ visualization }: { visualization: VisualizationData }) => {
  const { data } = visualization;
  const { content } = data;

  // Separate content by type
  const textContent = content.filter((item: any) => item.type === 'text');
  const tableContent = content.filter((item: any) => item.type === 'table');
  const imageContent = content.filter((item: any) => item.type === 'image');

  // Enhanced markdown components for better styling
  const markdownComponents: Components = {
    h1: ({ children, ...props }) => <h1 className="text-xl font-bold mb-3 text-gray-900 border-b border-gray-200 pb-2" {...props}>{children}</h1>,
    h2: ({ children, ...props }) => <h2 className="text-lg font-semibold mb-2 text-gray-800 mt-4" {...props}>{children}</h2>,
    h3: ({ children, ...props }) => <h3 className="text-base font-medium mb-2 text-gray-700 mt-3" {...props}>{children}</h3>,
    p: ({ children, ...props }) => <p className="mb-3" {...props}>{children}</p>,
    ul: ({ children, ...props }) => <ul className="list-disc list-inside mb-3 space-y-1" {...props}>{children}</ul>,
    ol: ({ children, ...props }) => <ol className="list-decimal list-inside mb-3 space-y-1" {...props}>{children}</ol>,
    li: ({ children, ...props }) => <li className="mb-1" {...props}>{children}</li>,
    code: ({ children, ...props }) => <code className="bg-gray-200 px-1 py-0.5 rounded text-sm font-mono" {...props}>{children}</code>,
    pre: ({ children, ...props }) => <pre className="bg-gray-100 p-3 rounded-lg overflow-x-auto mb-3" {...props}>{children}</pre>,
    blockquote: ({ children, ...props }) => <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-3" {...props}>{children}</blockquote>,
    a: ({ href, children, ...props }) => <a href={href} className="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer" {...props}>{children}</a>,
    // Enhanced table styling
    table: ({ children, ...props }) => (
      <div className="overflow-x-auto my-4 border-2 border-gray-300 rounded-lg shadow-lg">
        <table className="w-full border-collapse bg-white" {...props}>
          {children}
        </table>
      </div>
    ),
    thead: ({ children, ...props }) => (
      <thead className="bg-gradient-to-r from-brand-600 to-brand-700 text-white" {...props}>{children}</thead>
    ),
    th: ({ children, ...props }) => (
      <th className="border border-gray-400 px-4 py-3 text-left font-bold text-white text-sm uppercase tracking-wide" {...props}>
        {children}
      </th>
    ),
    tbody: ({ children, ...props }) => (
      <tbody className="divide-y divide-gray-200" {...props}>{children}</tbody>
    ),
    tr: ({ children, ...props }) => (
      <tr className="hover:bg-gray-50 transition-colors duration-150" {...props}>{children}</tr>
    ),
    td: ({ children, ...props }) => (
      <td className="border border-gray-300 px-4 py-3 text-gray-900 font-medium" {...props}>
        {children}
      </td>
    ),
  };

  return (
    <Card className="shadow-lg border-gray-200 w-full">
      <CardHeader className="bg-gradient-to-r from-white to-gray-50 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-brand-500" />
          <CardTitle className="text-brand-700">{visualization.title || 'Analysis Results'}</CardTitle>
        </div>
        {visualization.description && <CardDescription className="text-gray-600">{visualization.description}</CardDescription>}
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="results" className="w-full">
          <TabsList className="w-full border-b rounded-none px-4 bg-gray-50">
            <TabsTrigger value="results">Results</TabsTrigger>
            {tableContent.length > 0 && <TabsTrigger value="tables">Tables</TabsTrigger>}
            {imageContent.length > 0 && <TabsTrigger value="charts">Charts</TabsTrigger>}
          </TabsList>

          <TabsContent value="results" className="p-6">
            <div className="space-y-6">
              {/* Render text content with enhanced markdown */}
              {textContent.map((item: any, index: number) => (
                <div key={index} className="prose prose-sm max-w-none">
                  <ReactMarkdown components={markdownComponents}>
                    {item.text}
                  </ReactMarkdown>
                </div>
              ))}

              {/* Render inline tables */}
              {tableContent.map((item: any, index: number) => (
                <div key={index} className="overflow-x-auto border-2 border-gray-300 rounded-lg shadow-lg">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gradient-to-r from-brand-600 to-brand-700">
                        {(item.columns || Object.keys(item.data[0] || {})).map((header: string, headerIndex: number) => (
                          <TableHead key={headerIndex} className="font-bold text-white text-sm uppercase tracking-wide">
                            {header}
                          </TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {(item.data || []).map((row: any, rowIndex: number) => (
                        <TableRow key={rowIndex} className="hover:bg-gray-50 transition-colors duration-150">
                          {(item.columns || Object.keys(row)).map((col: string, cellIndex: number) => (
                            <TableCell key={cellIndex} className="font-medium text-gray-900 border-r border-gray-200 last:border-r-0">
                              {row[col]}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ))}

              {/* Render inline images */}
              {imageContent.map((item: any, index: number) => (
                <div key={index} className="flex justify-center bg-white rounded-lg border border-gray-200 p-4">
                  <img
                    src={item.src}
                    alt={`Analysis chart ${index + 1}`}
                    className="max-w-full max-h-[600px] object-contain rounded-md shadow-sm"
                    style={{ minHeight: '200px' }}
                  />
                </div>
              ))}
            </div>
          </TabsContent>

          {tableContent.length > 0 && (
            <TabsContent value="tables" className="p-6">
              <div className="space-y-6">
                {tableContent.map((item: any, index: number) => (
                  <div key={index}>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Table {index + 1}</h3>
                    <div className="overflow-x-auto border-2 border-gray-300 rounded-lg shadow-lg">
                      <Table>
                        <TableHeader>
                          <TableRow className="bg-gradient-to-r from-brand-600 to-brand-700">
                            {(item.columns || Object.keys(item.data[0] || {})).map((header: string, headerIndex: number) => (
                              <TableHead key={headerIndex} className="font-bold text-white text-sm uppercase tracking-wide">
                                {header}
                              </TableHead>
                            ))}
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {(item.data || []).map((row: any, rowIndex: number) => (
                            <TableRow key={rowIndex} className="hover:bg-gray-50 transition-colors duration-150">
                              {(item.columns || Object.keys(row)).map((col: string, cellIndex: number) => (
                                <TableCell key={cellIndex} className="font-medium text-gray-900 border-r border-gray-200 last:border-r-0">
                                  {row[col]}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          )}

          {imageContent.length > 0 && (
            <TabsContent value="charts" className="p-6">
              <div className="space-y-6">
                {imageContent.map((item: any, index: number) => (
                  <div key={index}>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Chart {index + 1}</h3>
                    <div className="flex justify-center bg-white rounded-lg border border-gray-200 p-4">
                      <img
                        src={item.src}
                        alt={`Analysis chart ${index + 1}`}
                        className="max-w-full max-h-[600px] object-contain rounded-md shadow-sm"
                        style={{ minHeight: '200px' }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
};
