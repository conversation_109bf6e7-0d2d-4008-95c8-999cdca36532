"""
FastAPI endpoints for the Concierge Agent.

This module provides REST API endpoints for interacting with the concierge agent,
including conversation management, persona recommendations, and routing.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..database import get_db
from ..auth import get_current_active_user
from ..models.auth import User
from agents.concierge_agent.concierge import ConciergeAgent
from agents.concierge_agent.models import (
    ConversationState, PersonaRecommendation, ConciergeResponse,
    PersonaRoutingRequest, PersonaRoutingResponse, ConversationStage
)
from agents.concierge_agent.services import ConciergeService
from agents.concierge_agent.exceptions import ConciergeAgentException

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/concierge", tags=["Concierge"])

# Initialize concierge agent
concierge_agent = ConciergeAgent()


# Request/Response Models
class ConciergeMessageRequest(BaseModel):
    """Request model for concierge messages."""
    message: str = Field(..., description="User message")
    conversation_id: str = Field(..., description="Conversation ID")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context")


class PersonaRecommendationRequest(BaseModel):
    """Request model for persona recommendations."""
    message: str = Field(..., description="User message")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context")
    max_recommendations: int = Field(default=3, ge=1, le=10, description="Maximum recommendations")


class ConversationStateResponse(BaseModel):
    """Response model for conversation state."""
    conversation_id: str
    user_id: str
    stage: ConversationStage
    selected_persona: Optional[str]
    recommended_personas: List[PersonaRecommendation]
    context_summary: str
    metadata: Dict[str, Any]


class HealthCheckResponse(BaseModel):
    """Response model for health check."""
    status: str
    agent_name: str
    components_loaded: int
    service_status: str
    last_request_count: int


@router.post("/message", response_model=ConciergeResponse)
async def send_message(
    request: ConciergeMessageRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Send a message to the concierge agent.
    
    Args:
        request: Message request containing user message and context
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Concierge response with recommendations and metadata
    """
    try:
        logger.info(f"User {current_user.id} sending message to concierge: {request.message[:50]}...")
        
        # Add user context
        context = request.context.copy()
        context.update({
            "user_id": str(current_user.id),
            "username": current_user.username,
            "email": current_user.email
        })
        
        # Process message through concierge agent
        response = await concierge_agent.process_message(
            message=request.message,
            user_id=str(current_user.id),
            conversation_id=request.conversation_id,
            context=context
        )
        
        # Convert to ConciergeResponse format
        if isinstance(response, dict):
            return ConciergeResponse(
                message=response.get("message", ""),
                metadata=response.get("metadata", {}),
                success=response.get("success", True),
                recommendations=[],  # Will be populated by the agent
                suggested_actions=response.get("metadata", {}).get("suggested_actions", []),
                requires_user_input=response.get("metadata", {}).get("requires_user_input", False)
            )
        
        return response
        
    except ConciergeAgentException as e:
        logger.error(f"Concierge agent error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.to_dict()
        )
    except Exception as e:
        logger.error(f"Error processing concierge message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Internal server error", "message": str(e)}
        )


@router.post("/recommendations", response_model=List[PersonaRecommendation])
async def get_persona_recommendations(
    request: PersonaRecommendationRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get persona recommendations based on user message.
    
    Args:
        request: Recommendation request
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List of persona recommendations
    """
    try:
        logger.info(f"User {current_user.id} requesting persona recommendations")
        
        # Add user context
        context = request.context.copy()
        context.update({
            "user_id": str(current_user.id),
            "username": current_user.username,
            "email": current_user.email
        })
        
        # Get recommendations from concierge service
        if hasattr(concierge_agent, 'concierge_service') and concierge_agent.concierge_service:
            recommendations = await concierge_agent.concierge_service.recommendation_service.get_persona_recommendations(
                user_message=request.message,
                user_context=context,
                max_recommendations=request.max_recommendations
            )
            return recommendations
        else:
            # Fallback to agent method
            intent = await concierge_agent.parse_user_intent(request.message, context)
            # Convert intent to recommendations (simplified)
            return []
        
    except Exception as e:
        logger.error(f"Error getting persona recommendations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to get recommendations", "message": str(e)}
        )


@router.get("/conversation/{conversation_id}/state", response_model=ConversationStateResponse)
async def get_conversation_state(
    conversation_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get the current state of a conversation.
    
    Args:
        conversation_id: ID of the conversation
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Current conversation state
    """
    try:
        logger.info(f"User {current_user.id} requesting state for conversation {conversation_id}")
        
        # Get conversation state from concierge service
        if hasattr(concierge_agent, 'concierge_service') and concierge_agent.concierge_service:
            state = await concierge_agent.concierge_service.state_manager.get_conversation_state(
                conversation_id=conversation_id,
                user_id=str(current_user.id)
            )
            
            return ConversationStateResponse(
                conversation_id=state.conversation_id,
                user_id=state.user_id,
                stage=state.stage,
                selected_persona=state.selected_persona,
                recommended_personas=state.recommended_personas,
                context_summary=state.context_summary,
                metadata=state.metadata
            )
        else:
            # Fallback response
            return ConversationStateResponse(
                conversation_id=conversation_id,
                user_id=str(current_user.id),
                stage=ConversationStage.INITIAL,
                selected_persona=None,
                recommended_personas=[],
                context_summary="",
                metadata={}
            )
        
    except Exception as e:
        logger.error(f"Error getting conversation state: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to get conversation state", "message": str(e)}
        )


@router.post("/route", response_model=PersonaRoutingResponse)
async def route_to_persona(
    request: PersonaRoutingRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Route user to a specific persona.
    
    Args:
        request: Routing request
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Routing response with success status and handoff information
    """
    try:
        logger.info(f"User {current_user.id} requesting route from {request.source_persona} to {request.target_persona}")
        
        # Add user context to routing request
        request.context.update({
            "user_id": str(current_user.id),
            "username": current_user.username,
            "email": current_user.email
        })
        
        # Route through concierge service
        if hasattr(concierge_agent, 'concierge_service') and concierge_agent.concierge_service:
            response = await concierge_agent.concierge_service.routing_service.route_to_persona(request)
            return response
        else:
            # Fallback routing logic
            return PersonaRoutingResponse(
                success=True,
                target_persona=request.target_persona,
                handoff_message=f"Connecting you to {request.target_persona}...",
                routing_metadata={"fallback": True}
            )
        
    except Exception as e:
        logger.error(f"Error routing to persona: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to route to persona", "message": str(e)}
        )


@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """
    Check the health status of the concierge agent.
    
    Returns:
        Health status information
    """
    try:
        # Check agent status
        components_loaded = len(concierge_agent.components) if hasattr(concierge_agent, 'components') else 0
        service_status = "active" if hasattr(concierge_agent, 'concierge_service') and concierge_agent.concierge_service else "inactive"
        
        return HealthCheckResponse(
            status="healthy",
            agent_name=concierge_agent.name if hasattr(concierge_agent, 'name') else "Concierge Agent",
            components_loaded=components_loaded,
            service_status=service_status,
            last_request_count=getattr(concierge_agent, 'request_count', 0)
        )
        
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            agent_name="Concierge Agent",
            components_loaded=0,
            service_status="error",
            last_request_count=0
        )


@router.get("/capabilities")
async def get_capabilities():
    """
    Get the capabilities of the concierge agent.
    
    Returns:
        List of agent capabilities
    """
    try:
        capabilities = [
            "intelligent_intent_recognition",
            "persona_recommendation",
            "conversation_management",
            "context_awareness",
            "persona_routing",
            "error_handling",
            "performance_monitoring"
        ]
        
        return {
            "capabilities": capabilities,
            "description": "Advanced concierge agent with LLM-based understanding and routing",
            "version": "3.0.0"
        }
        
    except Exception as e:
        logger.error(f"Error getting capabilities: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to get capabilities", "message": str(e)}
        )
