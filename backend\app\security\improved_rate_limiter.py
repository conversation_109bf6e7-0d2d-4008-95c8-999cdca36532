"""
Improved Rate Limiter with Sliding Window Counter Algorithm.

This module implements a resilient rate limiting system that replaces the vulnerable
Fixed Window algorithm with a Sliding Window Counter approach for better security
and user experience.
"""

import time
import logging
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""
    requests_per_window: int
    window_size_seconds: int
    burst_allowance: float = 1.5  # Allow 50% burst above normal rate
    sub_window_count: int = 10  # Number of sub-windows for sliding effect
    
    def __post_init__(self):
        """Calculate derived values."""
        self.sub_window_size = self.window_size_seconds / self.sub_window_count
        self.requests_per_sub_window = self.requests_per_window / self.sub_window_count
        self.burst_limit = int(self.requests_per_window * self.burst_allowance)


@dataclass
class RateLimitResult:
    """Result of rate limit check."""
    allowed: bool
    remaining_requests: int
    reset_time: float
    retry_after: Optional[int] = None
    current_usage: int = 0
    window_usage_percent: float = 0.0


class SlidingWindowCounter:
    """
    Sliding Window Counter rate limiter implementation.
    
    This algorithm divides the time window into smaller sub-windows and maintains
    counters for each. It provides better burst handling and smoother rate limiting
    compared to Fixed Window while being more memory efficient than Sliding Window Log.
    """
    
    def __init__(self, config: RateLimitConfig):
        """
        Initialize sliding window counter.
        
        Args:
            config: Rate limiting configuration
        """
        self.config = config
        self.counters: Dict[str, deque] = defaultdict(lambda: deque(maxlen=config.sub_window_count))
        self.last_cleanup = time.time()
        self.cleanup_interval = 60  # Cleanup every minute
        
        logger.info(f"Initialized sliding window counter: {config.requests_per_window} requests per {config.window_size_seconds}s")
    
    def check_rate_limit(self, identifier: str) -> RateLimitResult:
        """
        Check if request is within rate limits.
        
        Args:
            identifier: Unique identifier (IP, user ID, etc.)
            
        Returns:
            RateLimitResult with decision and metadata
        """
        current_time = time.time()
        
        # Cleanup old data periodically
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_data(current_time)
            self.last_cleanup = current_time
        
        # Get or create counter for this identifier
        counter = self.counters[identifier]
        
        # Calculate current sub-window
        current_sub_window = int(current_time // self.config.sub_window_size)
        
        # Update counter structure
        self._update_counter(counter, current_sub_window, current_time)
        
        # Calculate current usage
        current_usage = self._calculate_current_usage(counter, current_time)
        
        # Check if request is allowed
        if current_usage < self.config.requests_per_window:
            # Allow request and increment counter
            self._increment_counter(counter, current_sub_window)
            
            remaining = self.config.requests_per_window - current_usage - 1
            reset_time = self._calculate_reset_time(current_time)
            
            return RateLimitResult(
                allowed=True,
                remaining_requests=max(0, remaining),
                reset_time=reset_time,
                current_usage=current_usage + 1,
                window_usage_percent=(current_usage + 1) / self.config.requests_per_window * 100
            )
        else:
            # Check burst allowance
            if current_usage < self.config.burst_limit:
                # Allow burst but warn
                self._increment_counter(counter, current_sub_window)
                
                logger.warning(f"Burst allowance used for {identifier}: {current_usage + 1}/{self.config.burst_limit}")
                
                return RateLimitResult(
                    allowed=True,
                    remaining_requests=0,
                    reset_time=self._calculate_reset_time(current_time),
                    current_usage=current_usage + 1,
                    window_usage_percent=(current_usage + 1) / self.config.requests_per_window * 100
                )
            else:
                # Rate limit exceeded
                retry_after = int(self._calculate_retry_after(current_time))
                
                return RateLimitResult(
                    allowed=False,
                    remaining_requests=0,
                    reset_time=self._calculate_reset_time(current_time),
                    retry_after=retry_after,
                    current_usage=current_usage,
                    window_usage_percent=current_usage / self.config.requests_per_window * 100
                )
    
    def _update_counter(self, counter: deque, current_sub_window: int, current_time: float):
        """Update counter structure for current time."""
        # Initialize counter if empty
        if not counter:
            counter.append({
                'sub_window': current_sub_window,
                'count': 0,
                'timestamp': current_time
            })
            return
        
        # Check if we need to add new sub-windows
        latest_sub_window = counter[-1]['sub_window']
        
        if current_sub_window > latest_sub_window:
            # Add missing sub-windows (filled with zeros)
            windows_to_add = min(
                current_sub_window - latest_sub_window,
                self.config.sub_window_count
            )
            
            for i in range(1, windows_to_add + 1):
                counter.append({
                    'sub_window': latest_sub_window + i,
                    'count': 0,
                    'timestamp': current_time
                })
    
    def _calculate_current_usage(self, counter: deque, current_time: float) -> int:
        """Calculate current usage within the sliding window."""
        if not counter:
            return 0
        
        window_start_time = current_time - self.config.window_size_seconds
        total_count = 0
        
        for sub_window_data in counter:
            sub_window_time = sub_window_data['timestamp']
            
            if sub_window_time >= window_start_time:
                # Full sub-window within current window
                total_count += sub_window_data['count']
            elif sub_window_time + self.config.sub_window_size > window_start_time:
                # Partial sub-window overlap - calculate weighted contribution
                overlap_duration = sub_window_time + self.config.sub_window_size - window_start_time
                overlap_ratio = overlap_duration / self.config.sub_window_size
                total_count += int(sub_window_data['count'] * overlap_ratio)
        
        return total_count
    
    def _increment_counter(self, counter: deque, current_sub_window: int):
        """Increment counter for current sub-window."""
        if counter and counter[-1]['sub_window'] == current_sub_window:
            counter[-1]['count'] += 1
        else:
            # This shouldn't happen if _update_counter was called first
            logger.warning("Counter increment called without proper update")
    
    def _calculate_reset_time(self, current_time: float) -> float:
        """Calculate when the rate limit will reset."""
        return current_time + self.config.window_size_seconds
    
    def _calculate_retry_after(self, current_time: float) -> float:
        """Calculate retry-after time in seconds."""
        # Find the oldest sub-window that will expire soonest
        return self.config.sub_window_size
    
    def _cleanup_old_data(self, current_time: float):
        """Clean up old counter data to prevent memory leaks."""
        cutoff_time = current_time - (self.config.window_size_seconds * 2)  # Keep 2x window for safety
        
        identifiers_to_remove = []
        
        for identifier, counter in self.counters.items():
            # Remove old sub-windows
            while counter and counter[0]['timestamp'] < cutoff_time:
                counter.popleft()
            
            # Remove empty counters
            if not counter:
                identifiers_to_remove.append(identifier)
        
        # Remove empty counters
        for identifier in identifiers_to_remove:
            del self.counters[identifier]
        
        if identifiers_to_remove:
            logger.debug(f"Cleaned up {len(identifiers_to_remove)} empty rate limit counters")


class ImprovedRateLimiter:
    """
    Improved rate limiter with multiple algorithms and configurations.
    
    Replaces the vulnerable Fixed Window algorithm with resilient alternatives.
    """
    
    def __init__(self):
        """Initialize improved rate limiter."""
        # Define rate limit configurations for different use cases
        self.configs = {
            "default": RateLimitConfig(
                requests_per_window=100,
                window_size_seconds=3600,  # 1 hour
                burst_allowance=1.2,  # 20% burst
                sub_window_count=12  # 5-minute sub-windows
            ),
            "auth": RateLimitConfig(
                requests_per_window=10,
                window_size_seconds=300,  # 5 minutes
                burst_allowance=1.0,  # No burst for auth
                sub_window_count=10  # 30-second sub-windows
            ),
            "api": RateLimitConfig(
                requests_per_window=1000,
                window_size_seconds=3600,  # 1 hour
                burst_allowance=1.5,  # 50% burst
                sub_window_count=20  # 3-minute sub-windows
            ),
            "upload": RateLimitConfig(
                requests_per_window=20,
                window_size_seconds=3600,  # 1 hour
                burst_allowance=1.1,  # 10% burst
                sub_window_count=12  # 5-minute sub-windows
            )
        }
        
        # Create sliding window counters for each configuration
        self.limiters = {
            name: SlidingWindowCounter(config)
            for name, config in self.configs.items()
        }
        
        logger.info("Improved rate limiter initialized with sliding window counters")
    
    def is_rate_limited(self, identifier: str, limit_type: str = "default") -> bool:
        """
        Check if identifier is rate limited (backward compatibility).
        
        Args:
            identifier: Unique identifier
            limit_type: Type of rate limit
            
        Returns:
            True if rate limited
        """
        result = self.check_rate_limit(identifier, limit_type)
        return not result.allowed
    
    def check_rate_limit(self, identifier: str, limit_type: str = "default") -> RateLimitResult:
        """
        Check rate limit with detailed result.
        
        Args:
            identifier: Unique identifier
            limit_type: Type of rate limit
            
        Returns:
            Detailed rate limit result
        """
        if limit_type not in self.limiters:
            limit_type = "default"
        
        limiter = self.limiters[limit_type]
        return limiter.check_rate_limit(identifier)
    
    def get_rate_limit_info(self, identifier: str, limit_type: str = "default") -> Dict[str, Any]:
        """Get rate limit information for identifier."""
        result = self.check_rate_limit(identifier, limit_type)
        
        return {
            "requests_remaining": result.remaining_requests,
            "window_reset": result.reset_time,
            "retry_after": result.retry_after,
            "current_usage": result.current_usage,
            "usage_percent": result.window_usage_percent,
            "burst_available": result.allowed or result.current_usage < self.configs[limit_type].burst_limit
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get rate limiter statistics."""
        stats = {}
        
        for limit_type, limiter in self.limiters.items():
            stats[limit_type] = {
                "active_identifiers": len(limiter.counters),
                "config": {
                    "requests_per_window": limiter.config.requests_per_window,
                    "window_size_seconds": limiter.config.window_size_seconds,
                    "burst_allowance": limiter.config.burst_allowance,
                    "sub_window_count": limiter.config.sub_window_count
                }
            }
        
        return stats


# Global improved rate limiter instance
improved_rate_limiter = ImprovedRateLimiter()
