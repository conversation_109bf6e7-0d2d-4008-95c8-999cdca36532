"""
API Endpoints for Search and Recommendations.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel # Added BaseModel import

from ..services.search_service import SearchService, get_search_service
from ..models.search import (
    SearchQueryLogCreate, SearchQueryLogResponse,
    SearchRecommendationRequest, SearchRecommendationResponse,
    UserSearchActivity as UserSearchActivitySchema # Pydantic schema for response
)
from ..auth import get_current_active_user, get_optional_current_user # Corrected import
from ..models.auth import User as UserSchema

router = APIRouter(prefix="/search", tags=["Search & Recommendations"])

class SearchResultsResponse(BaseModel):
    query: str
    filters: Optional[Dict[str, Any]] = None
    results: List[Dict[str, Any]] # Generic results for now
    total_count: int
    limit: int
    offset: int

@router.get("/", response_model=SearchResultsResponse)
async def perform_search_endpoint(
    query: str = Query(..., min_length=1, max_length=200, description="Search query term"),
    type_filter: Optional[str] = Query(None, alias="type", description="Filter by item type (e.g., persona, report)"),
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    service: SearchService = Depends(get_search_service),
    current_user: Optional[UserSchema] = Depends(get_optional_current_user) # Allow anonymous search
):
    """
    Perform a search across the platform.
    Returns a list of search results.
    """
    filters: Dict[str, Any] = {}
    if type_filter:
        filters["type"] = type_filter

    user_id = current_user.id if current_user else None

    # In a real scenario, you might pass more context for personalization
    search_results_data = await service.perform_search(
        query=query,
        user_id=user_id,
        filters=filters if filters else None,
        limit=limit,
        offset=offset
    )
    return SearchResultsResponse(**search_results_data)

@router.post("/recommendations", response_model=SearchRecommendationResponse)
async def get_recommendations_endpoint(
    request_data: SearchRecommendationRequest,
    service: SearchService = Depends(get_search_service),
    current_user: Optional[UserSchema] = Depends(get_optional_current_user) # Allow recommendations for anonymous based on session
):
    """
    Get search or content recommendations.
    """
    # If user is logged in and request_data.user_id is not set, use current_user.id
    if current_user and request_data.user_id is None:
        request_data.user_id = current_user.id

    return await service.get_search_recommendations(request_data)

@router.get("/history/me", response_model=List[SearchQueryLogResponse])
async def get_my_search_history_endpoint(
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    service: SearchService = Depends(get_search_service),
    current_user: UserSchema = Depends(get_current_active_user) # Requires authenticated user
):
    """
    Get the search history for the currently authenticated user.
    """
    history_db = await service.get_user_search_history(user_id=current_user.id, limit=limit, offset=offset)
    # Convert SQLAlchemy models to Pydantic response models
    return [SearchQueryLogResponse.model_validate(item) for item in history_db]

# Note: Logging search activity is handled within perform_search in the service.
# If explicit logging via API is needed, an endpoint like POST /log-search could be added.
