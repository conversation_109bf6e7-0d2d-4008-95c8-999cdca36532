/**
 * Business Profile API client
 * 
 * This module provides API functions for managing business profiles
 * and their data source associations.
 */

import { apiRequest } from './api';

// Types
export interface BusinessProfile {
  id: string;
  user_id: number;
  name: string;
  description?: string;
  industry?: string;
  business_type?: 'B2B' | 'B2C' | 'B2B2C' | 'marketplace' | 'saas' | 'ecommerce';
  business_size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  target_audience?: string;
  products_services?: string;
  marketing_goals?: string;
  competitive_landscape?: string;
  budget_indicators?: string;
  geographic_focus?: string;
  business_stage?: 'idea' | 'startup' | 'growth' | 'mature' | 'enterprise';

  // Marketing-specific fields (consolidated from marketing form)
  budget?: string;
  timeline?: string;
  platforms?: string;

  is_active: boolean;
  knowledge_graph_id?: string;
  context_metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  data_source_count?: number;
  dashboard_count?: number;
}

export interface BusinessProfileCreate {
  name: string;
  description?: string;
  industry?: string;
  business_type?: 'B2B' | 'B2C' | 'B2B2C' | 'marketplace' | 'saas' | 'ecommerce';
  business_size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  target_audience?: string;
  products_services?: string;
  marketing_goals?: string;
  competitive_landscape?: string;
  budget_indicators?: string;
  geographic_focus?: string;
  business_stage?: 'idea' | 'startup' | 'growth' | 'mature' | 'enterprise';

  // Marketing-specific fields (consolidated from marketing form)
  budget?: string;
  timeline?: string;
  platforms?: string;

  context_metadata?: Record<string, any>;
}

export interface BusinessProfileUpdate {
  name?: string;
  description?: string;
  industry?: string;
  business_type?: 'B2B' | 'B2C' | 'B2B2C' | 'marketplace' | 'saas' | 'ecommerce';
  business_size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  target_audience?: string;
  products_services?: string;
  marketing_goals?: string;
  competitive_landscape?: string;
  budget_indicators?: string;
  geographic_focus?: string;
  business_stage?: 'idea' | 'startup' | 'growth' | 'mature' | 'enterprise';

  // Marketing-specific fields (consolidated from marketing form)
  budget?: string;
  timeline?: string;
  platforms?: string;

  context_metadata?: Record<string, any>;
  is_active?: boolean;
}

export interface BusinessProfileDataSourceAssignment {
  id: string;
  business_profile_id: string;
  data_source_id: string;
  role?: 'sales_data' | 'business_description' | 'marketing_materials' | 'financial_data' | 'customer_data' | 'product_data' | 'competitor_analysis' | 'website_content' | 'social_media' | 'other';
  priority: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BusinessProfileDataSourceAssignmentCreate {
  data_source_id: string;
  role?: 'sales_data' | 'business_description' | 'marketing_materials' | 'financial_data' | 'customer_data' | 'product_data' | 'competitor_analysis' | 'website_content' | 'social_media' | 'other';
  priority?: number;
  is_active?: boolean;
}

export interface BusinessProfileWithDataSources extends BusinessProfile {
  data_source_assignments: BusinessProfileDataSourceAssignment[];
}

export interface BusinessProfileListResponse {
  profiles: BusinessProfile[];
  total: number;
  active_profile_id?: string;
}

export interface BusinessProfileSwitchRequest {
  profile_id: string;
}

// API functions
export const businessProfileApi = {
  // Profile management
  async createProfile(profileData: BusinessProfileCreate): Promise<BusinessProfile> {
    return apiRequest('/business-profiles/', {
      method: 'POST',
      body: JSON.stringify(profileData),
    });
  },

  async listProfiles(): Promise<BusinessProfileListResponse> {
    try {
      console.log('🔍 Making API request to list profiles...');
      const response = await apiRequest('/business-profiles/');
      console.log('🔍 API response for list profiles:', response);

      // Ensure the response has the expected structure
      if (!response || typeof response !== 'object') {
        throw new Error('Invalid response format from server');
      }

      // Ensure profiles is an array
      if (!Array.isArray(response.profiles)) {
        console.warn('🔍 Response profiles is not an array:', response.profiles);
        return {
          profiles: [],
          total: 0,
          active_profile_id: null
        };
      }

      return response;
    } catch (error) {
      console.error('🔍 Error in listProfiles:', error);
      throw error;
    }
  },

  async getActiveProfile(): Promise<BusinessProfile> {
    return apiRequest('/business-profiles/active');
  },

  async getProfile(profileId: string): Promise<BusinessProfile> {
    return apiRequest(`/business-profiles/${profileId}`);
  },

  async getProfileWithDataSources(profileId: string): Promise<BusinessProfileWithDataSources> {
    return apiRequest(`/business-profiles/${profileId}/with-data-sources`);
  },

  async updateProfile(profileId: string, updateData: BusinessProfileUpdate): Promise<BusinessProfile> {
    return apiRequest(`/business-profiles/${profileId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  async deleteProfile(profileId: string): Promise<void> {
    return apiRequest(`/business-profiles/${profileId}`, {
      method: 'DELETE',
    });
  },

  async switchActiveProfile(profileId: string): Promise<void> {
    return apiRequest('/business-profiles/switch', {
      method: 'POST',
      body: JSON.stringify({ profile_id: profileId }),
    });
  },

  // Data source assignments
  async assignDataSource(profileId: string, assignmentData: BusinessProfileDataSourceAssignmentCreate): Promise<BusinessProfileDataSourceAssignment> {
    return apiRequest(`/business-profiles/${profileId}/data-sources`, {
      method: 'POST',
      body: JSON.stringify(assignmentData),
    });
  },

  async unassignDataSource(profileId: string, dataSourceId: string): Promise<void> {
    return apiRequest(`/business-profiles/${profileId}/data-sources/${dataSourceId}`, {
      method: 'DELETE',
    });
  },

  async updateDataSourceAssignment(
    profileId: string,
    dataSourceId: string,
    updateData: Partial<BusinessProfileDataSourceAssignmentCreate>
  ): Promise<BusinessProfileDataSourceAssignment> {
    return apiRequest(`/business-profiles/${profileId}/data-sources/${dataSourceId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  // Template management
  async getBusinessProfileTemplate(industry: string): Promise<{
    industry: string;
    template: Record<string, any>;
    available_industries: string[];
  }> {
    return apiRequest(`/business-profiles/templates/${encodeURIComponent(industry)}`);
  },

  async getAvailableTemplates(): Promise<{
    industries: string[];
    total: number;
  }> {
    return apiRequest('/business-profiles/templates');
  },

  // Default profile management
  async createDefaultProfile(industry: string = 'General'): Promise<BusinessProfile> {
    return apiRequest('/business-profiles/default', {
      method: 'POST',
      body: JSON.stringify({ industry }),
    });
  }
};

// Helper functions
export const getBusinessTypeLabel = (type?: string): string => {
  switch (type) {
    case 'B2B': return 'Business to Business';
    case 'B2C': return 'Business to Consumer';
    case 'B2B2C': return 'Business to Business to Consumer';
    case 'marketplace': return 'Marketplace';
    case 'saas': return 'Software as a Service';
    case 'ecommerce': return 'E-commerce';
    default: return type || 'Not specified';
  }
};

export const getBusinessSizeLabel = (size?: string): string => {
  switch (size) {
    case 'startup': return 'Startup';
    case 'small': return 'Small Business';
    case 'medium': return 'Medium Business';
    case 'large': return 'Large Business';
    case 'enterprise': return 'Enterprise';
    default: return size || 'Not specified';
  }
};

export const getBusinessStageLabel = (stage?: string): string => {
  switch (stage) {
    case 'idea': return 'Idea Stage';
    case 'startup': return 'Startup Stage';
    case 'growth': return 'Growth Stage';
    case 'mature': return 'Mature Business';
    case 'enterprise': return 'Enterprise';
    default: return stage || 'Not specified';
  }
};

export const getDataSourceRoleLabel = (role?: string): string => {
  switch (role) {
    case 'sales_data': return 'Sales Data';
    case 'business_description': return 'Business Description';
    case 'marketing_materials': return 'Marketing Materials';
    case 'financial_data': return 'Financial Data';
    case 'customer_data': return 'Customer Data';
    case 'product_data': return 'Product Data';
    case 'competitor_analysis': return 'Competitor Analysis';
    case 'website_content': return 'Website Content';
    case 'social_media': return 'Social Media';
    case 'other': return 'Other';
    default: return 'Not specified';
  }
};

export const businessTypeOptions = [
  { value: 'B2B', label: 'Business to Business (B2B)' },
  { value: 'B2C', label: 'Business to Consumer (B2C)' },
  { value: 'B2B2C', label: 'Business to Business to Consumer (B2B2C)' },
  { value: 'marketplace', label: 'Marketplace' },
  { value: 'saas', label: 'Software as a Service (SaaS)' },
  { value: 'ecommerce', label: 'E-commerce' }
];

export const businessSizeOptions = [
  { value: 'startup', label: 'Startup' },
  { value: 'small', label: 'Small Business' },
  { value: 'medium', label: 'Medium Business' },
  { value: 'large', label: 'Large Business' },
  { value: 'enterprise', label: 'Enterprise' }
];

export const businessStageOptions = [
  { value: 'idea', label: 'Idea Stage' },
  { value: 'startup', label: 'Startup Stage' },
  { value: 'growth', label: 'Growth Stage' },
  { value: 'mature', label: 'Mature Business' },
  { value: 'enterprise', label: 'Enterprise' }
];

export const dataSourceRoleOptions = [
  { value: 'business_description', label: 'Business Description' },
  { value: 'sales_data', label: 'Sales Data' },
  { value: 'marketing_materials', label: 'Marketing Materials' },
  { value: 'financial_data', label: 'Financial Data' },
  { value: 'customer_data', label: 'Customer Data' },
  { value: 'product_data', label: 'Product Data' },
  { value: 'competitor_analysis', label: 'Competitor Analysis' },
  { value: 'website_content', label: 'Website Content' },
  { value: 'social_media', label: 'Social Media' },
  { value: 'other', label: 'Other' }
];
