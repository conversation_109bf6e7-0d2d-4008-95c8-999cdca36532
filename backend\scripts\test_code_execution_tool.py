#!/usr/bin/env python3
"""
Test script for the Code Execution MCP tool.

This script tests the secure code execution functionality and ensures
the tool works correctly with various code examples.
"""

import os
import sys
import logging
import asyncio
import json
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from agents.tools.mcp.code_execution_tool import CodeExecutionTool, SecureCodeExecutor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_code_execution_tool():
    """Test the code execution tool functionality."""
    
    logger.info("=== Testing Code Execution Tool ===")
    
    # Test 1: Initialize the tool
    logger.info("\n1. Testing tool initialization...")
    try:
        code_tool = CodeExecutionTool()
        await code_tool.initialize({
            "timeout": 30,
            "max_memory": 100
        })
        logger.info("✅ CodeExecutionTool initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize CodeExecutionTool: {e}")
        return False
    
    # Test 2: Basic calculation
    logger.info("\n2. Testing basic calculation...")
    try:
        result = await code_tool.execute({
            "code": """
# Basic calculation
result = 2 + 2
print(f"2 + 2 = {result}")

# More complex calculation
import math
area = math.pi * (5 ** 2)
print(f"Area of circle with radius 5: {area:.2f}")
"""
        })
        
        if not result.get("isError", True):
            logger.info("✅ Basic calculation test passed")
            logger.info(f"   Output: {result['content'][0]['text']}")
        else:
            logger.warning(f"⚠️ Basic calculation test failed: {result}")
    except Exception as e:
        logger.error(f"❌ Error in basic calculation test: {e}")
    
    # Test 3: Data analysis with pandas
    logger.info("\n3. Testing data analysis...")
    try:
        result = await code_tool.execute({
            "code": """
import pandas as pd
import numpy as np

# Create sample data
data = {
    'Name': ['Alice', 'Bob', 'Charlie', 'Diana', 'Eve'],
    'Age': [25, 30, 35, 28, 32],
    'Salary': [50000, 60000, 70000, 55000, 65000],
    'Department': ['IT', 'HR', 'IT', 'Finance', 'IT']
}

df = pd.DataFrame(data)
print("Sample Data:")
print(df)
print()

# Basic statistics
print("Basic Statistics:")
print(df.describe())
print()

# Group by department
print("Average Salary by Department:")
dept_avg = df.groupby('Department')['Salary'].mean()
print(dept_avg)
"""
        })
        
        if not result.get("isError", True):
            logger.info("✅ Data analysis test passed")
            logger.info(f"   Generated {result['metadata']['files_generated']} files")
        else:
            logger.warning(f"⚠️ Data analysis test failed: {result}")
    except Exception as e:
        logger.error(f"❌ Error in data analysis test: {e}")
    
    # Test 4: Visualization
    logger.info("\n4. Testing visualization...")
    try:
        result = await code_tool.execute({
            "code": """
import matplotlib.pyplot as plt
import numpy as np

# Create sample data
x = np.linspace(0, 10, 100)
y = np.sin(x)

# Create plot
plt.figure(figsize=(10, 6))
plt.plot(x, y, 'b-', linewidth=2, label='sin(x)')
plt.xlabel('x')
plt.ylabel('sin(x)')
plt.title('Sine Wave')
plt.grid(True, alpha=0.3)
plt.legend()

# Save plot
plt.savefig('sine_wave.png', dpi=150, bbox_inches='tight')
plt.close()

print("Generated sine wave plot")
"""
        })
        
        if not result.get("isError", True):
            logger.info("✅ Visualization test passed")
            logger.info(f"   Generated {result['metadata']['files_generated']} files")
            
            # Check if image was generated
            for content in result['content']:
                if content.get('type') == 'image':
                    logger.info("   📊 Image visualization generated successfully")
        else:
            logger.warning(f"⚠️ Visualization test failed: {result}")
    except Exception as e:
        logger.error(f"❌ Error in visualization test: {e}")
    
    # Test 5: Security validation - should fail
    logger.info("\n5. Testing security validation...")
    try:
        result = await code_tool.execute({
            "code": """
import os
os.system('ls')  # This should be blocked
"""
        })
        
        if result.get("isError", False):
            logger.info("✅ Security validation test passed (correctly blocked dangerous code)")
        else:
            logger.warning("⚠️ Security validation test failed (dangerous code was allowed)")
    except Exception as e:
        logger.error(f"❌ Error in security validation test: {e}")
    
    # Test 6: Error handling
    logger.info("\n6. Testing error handling...")
    try:
        result = await code_tool.execute({
            "code": """
# This should cause a runtime error
x = 1 / 0
"""
        })
        
        if result.get("isError", False):
            logger.info("✅ Error handling test passed (correctly caught runtime error)")
            logger.info(f"   Error: {result['content'][0]['text']}")
        else:
            logger.warning("⚠️ Error handling test failed (runtime error not caught)")
    except Exception as e:
        logger.error(f"❌ Error in error handling test: {e}")
    
    return True


async def test_secure_executor():
    """Test the secure code executor directly."""
    
    logger.info("\n=== Testing Secure Code Executor ===")
    
    executor = SecureCodeExecutor(timeout=30, max_memory=100)
    
    # Test code validation
    logger.info("\n1. Testing code validation...")
    
    # Valid code
    is_valid, error = executor.validate_code("print('Hello, World!')")
    if is_valid:
        logger.info("✅ Valid code correctly validated")
    else:
        logger.warning(f"⚠️ Valid code incorrectly rejected: {error}")
    
    # Invalid code (restricted import)
    is_valid, error = executor.validate_code("import subprocess")
    if not is_valid:
        logger.info("✅ Invalid code correctly rejected")
        logger.info(f"   Reason: {error}")
    else:
        logger.warning("⚠️ Invalid code incorrectly allowed")
    
    # Test execution
    logger.info("\n2. Testing direct execution...")
    
    result = executor.execute_code("""
import pandas as pd
data = {'A': [1, 2, 3], 'B': [4, 5, 6]}
df = pd.DataFrame(data)
print("DataFrame created:")
print(df)
print(f"Shape: {df.shape}")
""")
    
    if result["success"]:
        logger.info("✅ Direct execution test passed")
        logger.info(f"   Output: {result['stdout']}")
    else:
        logger.warning(f"⚠️ Direct execution test failed: {result['error']}")
    
    return True


if __name__ == "__main__":
    async def main():
        """Main test function."""
        logger.info("Starting Code Execution Tool tests...")
        
        # Test the secure executor
        executor_success = await test_secure_executor()
        
        # Test the MCP tool
        tool_success = await test_code_execution_tool()
        
        if executor_success and tool_success:
            logger.info("\n🎉 All tests passed! Code Execution Tool is ready!")
            logger.info("\nKey features verified:")
            logger.info("✅ Secure code validation")
            logger.info("✅ Basic calculations and data analysis")
            logger.info("✅ Pandas data manipulation")
            logger.info("✅ Matplotlib visualizations")
            logger.info("✅ Error handling and security restrictions")
            logger.info("✅ File generation and image output")
        else:
            logger.error("\n❌ Some tests failed. Please check the logs above.")
            sys.exit(1)
    
    asyncio.run(main())
