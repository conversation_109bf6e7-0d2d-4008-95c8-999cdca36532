import React from 'react';
import { motion } from 'framer-motion';
import { useNavbarStore } from '@/stores/navbar-store';
import { cn } from '@/lib/utils';

interface ResponsivePageWrapperProps {
  children: React.ReactNode;
  className?: string;
  enableTransitions?: boolean;
  fullHeight?: boolean;
}

/**
 * ResponsivePageWrapper - A wrapper component that makes any page content responsive to the LeftNavbar state
 * 
 * This component automatically adjusts the positioning and width of page content based on whether
 * the navbar is expanded or collapsed, ensuring consistent responsive behavior across all pages.
 */
export const ResponsivePageWrapper: React.FC<ResponsivePageWrapperProps> = ({
  children,
  className,
  enableTransitions = true,
  fullHeight = true
}) => {
  const { shouldShowExpanded, getNavbarWidth } = useNavbarStore();
  
  // Get current navbar state for responsive positioning
  const isNavbarExpanded = shouldShowExpanded();
  const navbarWidth = getNavbarWidth();

  if (enableTransitions) {
    return (
      <motion.div
        className={cn(
          "transition-all duration-300 ease-in-out",
          fullHeight && "min-h-screen",
          className
        )}
        initial={false}
        animate={{
          marginLeft: navbarWidth,
          width: `calc(100% - ${navbarWidth}px)`
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <div
      className={cn(
        "transition-all duration-300 ease-in-out",
        fullHeight && "min-h-screen",
        className
      )}
      style={{
        marginLeft: navbarWidth,
        width: `calc(100% - ${navbarWidth}px)`
      }}
    >
      {children}
    </div>
  );
};

/**
 * ResponsiveContentArea - A simpler wrapper for content areas that need responsive positioning
 * without full page layout concerns
 */
export const ResponsiveContentArea: React.FC<{
  children: React.ReactNode;
  className?: string;
  padding?: boolean;
}> = ({
  children,
  className,
  padding = true
}) => {
  const { shouldShowExpanded, getNavbarWidth } = useNavbarStore();
  const navbarWidth = getNavbarWidth();

  return (
    <motion.div
      className={cn(
        "transition-all duration-300 ease-in-out",
        padding && "p-4",
        className
      )}
      initial={false}
      animate={{
        marginLeft: navbarWidth,
        width: `calc(100% - ${navbarWidth}px)`
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {children}
    </motion.div>
  );
};
