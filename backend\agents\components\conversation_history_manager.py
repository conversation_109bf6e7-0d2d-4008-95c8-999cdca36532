"""
Conversation History Manager Component for robust conversation history handling across all agents.

This component provides a standardized way to handle conversation history, memory management,
and context preservation for all agents in the Datagenius system.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

from .base import AgentComponent

logger = logging.getLogger(__name__)


@dataclass
class ConversationEntry:
    """Represents a single conversation entry."""
    timestamp: str
    sender: str  # "user" or "ai"
    content: str
    metadata: Dict[str, Any]
    message_id: Optional[str] = None


@dataclass
class ConversationMemory:
    """Represents conversation memory for a specific conversation."""
    conversation_id: str
    user_id: str
    persona_id: str
    entries: List[ConversationEntry]
    last_updated: datetime
    total_messages: int

    def add_entry(self, entry: ConversationEntry) -> None:
        """Add a new conversation entry."""
        self.entries.append(entry)
        self.last_updated = datetime.now()
        self.total_messages = len(self.entries)

    def get_recent_entries(self, limit: int = 30) -> List[ConversationEntry]:
        """Get recent conversation entries."""
        return self.entries[-limit:] if len(self.entries) > limit else self.entries

    def get_formatted_history(self, limit: int = 30, include_metadata: bool = False) -> List[Dict[str, Any]]:
        """Get formatted conversation history for LLM consumption."""
        recent_entries = self.get_recent_entries(limit)
        formatted = []

        for entry in recent_entries:
            formatted_entry = {
                "sender": entry.sender,
                "content": entry.content,
                "timestamp": entry.timestamp
            }
            if include_metadata:
                formatted_entry["metadata"] = entry.metadata
            formatted.append(formatted_entry)

        return formatted


class ConversationHistoryManagerComponent(AgentComponent):
    """
    Component for managing conversation history across all agents.

    This component provides:
    - Standardized conversation history handling
    - Memory management with configurable limits
    - Database and in-memory history merging
    - Context preservation across conversations
    - Automatic cleanup of old conversations
    """

    def __init__(self):
        """Initialize the conversation history manager."""
        super().__init__()
        self.name = "ConversationHistoryManager"

        # In-memory conversation storage
        self.conversations: Dict[str, ConversationMemory] = {}

        # Configuration defaults
        self.max_memory_entries = 100  # Maximum entries per conversation in memory
        self.max_database_entries = 50  # Maximum entries to retrieve from database
        self.max_llm_context_entries = 30  # Maximum entries to send to LLM
        self.cleanup_interval_hours = 24  # Hours before cleaning up old conversations
        self.max_conversations_in_memory = 1000  # Maximum conversations to keep in memory

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary
        """
        # Load configuration
        self.max_memory_entries = config.get("max_memory_entries", 100)
        self.max_database_entries = config.get("max_database_entries", 50)
        self.max_llm_context_entries = config.get("max_llm_context_entries", 30)
        self.cleanup_interval_hours = config.get("cleanup_interval_hours", 24)
        self.max_conversations_in_memory = config.get("max_conversations_in_memory", 1000)

        logger.info(f"ConversationHistoryManager initialized with limits: "
                   f"memory={self.max_memory_entries}, db={self.max_database_entries}, "
                   f"llm={self.max_llm_context_entries}")

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process conversation history for the current context.

        Args:
            context: The current AgentProcessingContext

        Returns:
            Enhanced context with conversation history
        """
        user_id = str(context.user_id)
        conversation_id = str(context.conversation_id)
        persona_id = context.agent_config.id if context.agent_config else "unknown"

        logger.debug(f"Processing conversation history for user {user_id}, "
                    f"conversation {conversation_id}, persona {persona_id}")

        # Initialize component data
        component_data = context.component_data.setdefault(self.name, {})

        # Get or create conversation memory
        conversation_memory = await self._get_conversation_memory(
            conversation_id, user_id, persona_id, context
        )

        # Store conversation memory in component data
        component_data["conversation_memory"] = conversation_memory
        component_data["conversation_history"] = conversation_memory.get_formatted_history(
            limit=self.max_llm_context_entries
        )
        component_data["total_messages"] = conversation_memory.total_messages
        component_data["is_continuing_conversation"] = conversation_memory.total_messages > 0

        # Add conversation history to context for other components
        context.initial_context["conversation_history"] = component_data["conversation_history"]
        context.initial_context["is_continuing_conversation"] = component_data["is_continuing_conversation"]
        context.initial_context["conversation_context"] = {
            "total_messages": conversation_memory.total_messages,
            "conversation_id": conversation_id,
            "user_id": user_id,
            "persona_id": persona_id,
            "last_updated": conversation_memory.last_updated.isoformat()
        }

        # Periodic cleanup
        if len(self.conversations) > self.max_conversations_in_memory:
            await self._cleanup_old_conversations()

        logger.info(f"Conversation history processed: {conversation_memory.total_messages} total messages, "
                   f"{len(component_data['conversation_history'])} in context")

        return context

    async def _get_conversation_memory(
        self,
        conversation_id: str,
        user_id: str,
        persona_id: str,
        context: "AgentProcessingContext"
    ) -> ConversationMemory:
        """
        Get or create conversation memory, merging database and in-memory history.

        Args:
            conversation_id: The conversation ID
            user_id: The user ID
            persona_id: The persona ID
            context: The current context

        Returns:
            ConversationMemory object with merged history
        """
        memory_key = f"{user_id}_{conversation_id}_{persona_id}"

        # Get existing memory or create new
        if memory_key in self.conversations:
            conversation_memory = self.conversations[memory_key]
        else:
            conversation_memory = ConversationMemory(
                conversation_id=conversation_id,
                user_id=user_id,
                persona_id=persona_id,
                entries=[],
                last_updated=datetime.now(),
                total_messages=0
            )
            self.conversations[memory_key] = conversation_memory

        # Merge database history if available
        database_history = context.initial_context.get("conversation_history", [])
        if database_history:
            await self._merge_database_history(conversation_memory, database_history)

        return conversation_memory

    async def _merge_database_history(
        self,
        conversation_memory: ConversationMemory,
        database_history: List[Dict[str, Any]]
    ) -> None:
        """
        Merge database conversation history with in-memory history.

        Args:
            conversation_memory: The conversation memory object
            database_history: History from database
        """
        # Convert database history to conversation entries
        database_entries = []
        for db_msg in database_history[-self.max_database_entries:]:  # Limit database entries
            entry = ConversationEntry(
                timestamp=db_msg.get("timestamp", datetime.now().isoformat()),
                sender=db_msg.get("sender", "unknown"),
                content=db_msg.get("content", ""),
                metadata=db_msg.get("metadata", {}),
                message_id=db_msg.get("id")
            )
            database_entries.append(entry)

        # Merge with existing entries, avoiding duplicates
        existing_timestamps = {entry.timestamp for entry in conversation_memory.entries}

        for db_entry in database_entries:
            if db_entry.timestamp not in existing_timestamps:
                conversation_memory.entries.insert(0, db_entry)  # Insert at beginning (older)

        # Sort by timestamp to maintain chronological order
        conversation_memory.entries.sort(key=lambda x: x.timestamp)

        # Trim to maximum memory entries
        if len(conversation_memory.entries) > self.max_memory_entries:
            conversation_memory.entries = conversation_memory.entries[-self.max_memory_entries:]

        conversation_memory.total_messages = len(conversation_memory.entries)
        conversation_memory.last_updated = datetime.now()

        logger.debug(f"Merged {len(database_entries)} database entries, "
                    f"total entries: {conversation_memory.total_messages}")

    async def add_conversation_entry(
        self,
        conversation_id: str,
        user_id: str,
        persona_id: str,
        sender: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add a new conversation entry to memory.

        Args:
            conversation_id: The conversation ID
            user_id: The user ID
            persona_id: The persona ID
            sender: The sender ("user" or "ai")
            content: The message content
            metadata: Optional metadata
        """
        memory_key = f"{user_id}_{conversation_id}_{persona_id}"

        if memory_key not in self.conversations:
            self.conversations[memory_key] = ConversationMemory(
                conversation_id=conversation_id,
                user_id=user_id,
                persona_id=persona_id,
                entries=[],
                last_updated=datetime.now(),
                total_messages=0
            )

        entry = ConversationEntry(
            timestamp=datetime.now().isoformat(),
            sender=sender,
            content=content,
            metadata=metadata or {},
            message_id=None
        )

        self.conversations[memory_key].add_entry(entry)

        # Trim if necessary
        if len(self.conversations[memory_key].entries) > self.max_memory_entries:
            self.conversations[memory_key].entries = self.conversations[memory_key].entries[-self.max_memory_entries:]
            self.conversations[memory_key].total_messages = len(self.conversations[memory_key].entries)

        logger.debug(f"Added conversation entry for {memory_key}: {sender} - {content[:50]}...")

    async def _cleanup_old_conversations(self) -> None:
        """Clean up old conversations from memory."""
        cutoff_time = datetime.now() - timedelta(hours=self.cleanup_interval_hours)

        conversations_to_remove = [
            key for key, memory in self.conversations.items()
            if memory.last_updated < cutoff_time
        ]

        for key in conversations_to_remove:
            del self.conversations[key]

        if conversations_to_remove:
            logger.info(f"Cleaned up {len(conversations_to_remove)} old conversations from memory")

    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get statistics about conversation memory usage."""
        total_conversations = len(self.conversations)
        total_entries = sum(len(memory.entries) for memory in self.conversations.values())

        return {
            "total_conversations": total_conversations,
            "total_entries": total_entries,
            "max_memory_entries": self.max_memory_entries,
            "max_conversations_in_memory": self.max_conversations_in_memory,
            "memory_usage_percent": (total_conversations / self.max_conversations_in_memory) * 100
        }
