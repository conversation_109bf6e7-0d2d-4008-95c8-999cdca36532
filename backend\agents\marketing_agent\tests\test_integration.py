"""
Comprehensive integration tests for marketing agent.

This module provides thorough integration testing of the complete
marketing agent system including all components working together.
"""

import pytest
import asyncio
import json
import time
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from backend.agents.marketing_agent.composable_agent import ComposableMarketingAgent
from backend.agents.marketing_agent.components import MarketingParserComponent, MarketingContentGeneratorComponent
from backend.agents.marketing_agent.cache import MarketingAgentCache, MemoryCacheBackend
from backend.agents.marketing_agent.memory_manager import MemoryManager
from backend.agents.marketing_agent.monitoring import MarketingAgentMonitor
from backend.agents.marketing_agent.exceptions import MarketingAgentException, ValidationError
from backend.agents.marketing_agent.validation import task_validator


class TestMarketingAgentIntegration:
    """Integration tests for the complete marketing agent system."""
    
    @pytest.fixture
    async def marketing_agent(self):
        """Create a marketing agent for testing."""
        config = {
            "provider": "groq",
            "model": "llama3-70b-8192",
            "temperature": 0.7,
            "max_tokens": 4000
        }
        
        agent = ComposableMarketingAgent(config)
        await agent.initialize()
        return agent
    
    @pytest.fixture
    def cache_backend(self):
        """Create a cache backend for testing."""
        return MemoryCacheBackend(max_size=100, default_ttl=300)
    
    @pytest.fixture
    def cache_manager(self, cache_backend):
        """Create a cache manager for testing."""
        return MarketingAgentCache(backend=cache_backend)
    
    @pytest.fixture
    def memory_manager(self):
        """Create a memory manager for testing."""
        return MemoryManager(
            max_memory_mb=512,
            conversation_timeout=300,
            cleanup_interval=60,
            max_conversations=100
        )
    
    @pytest.fixture
    def monitor(self):
        """Create a monitor for testing."""
        return MarketingAgentMonitor()
    
    @pytest.mark.asyncio
    async def test_complete_marketing_strategy_flow(self, marketing_agent, cache_manager, memory_manager):
        """Test complete flow from user input to marketing strategy generation."""
        # Start memory manager
        await memory_manager.start()
        
        try:
            # Simulate user input with marketing form data
            user_input = {
                "message": "generate a marketing strategy",
                "marketing_form_data": {
                    "brand_description": "Innovative tech startup focused on AI solutions",
                    "target_audience": "Tech-savvy professionals aged 25-45",
                    "products_services": "AI-powered productivity tools",
                    "marketing_goals": "Increase brand awareness and user acquisition",
                    "budget": "$50,000",
                    "timeline": "6 months"
                },
                "user_id": "test_user_123",
                "conversation_id": "conv_456"
            }
            
            # Register conversation
            await memory_manager.register_conversation(
                user_input["conversation_id"],
                user_input["user_id"],
                user_input
            )
            
            # Mock MCP server for content generation
            with patch.object(marketing_agent, 'mcp_server') as mock_mcp:
                mock_mcp.call_tool.return_value = {
                    "content": "# Comprehensive Marketing Strategy\n\n## Executive Summary\nThis strategy focuses on...",
                    "metadata": {"generated_content": True, "content_type": "marketing_strategy"}
                }
                
                # Process the request
                response = await marketing_agent.process_message(
                    user_input["message"],
                    user_input
                )
                
                # Verify response
                assert response is not None
                assert "content" in response
                assert response["metadata"]["generated_content"] == True
                
                # Verify MCP tool was called
                mock_mcp.call_tool.assert_called_once()
                call_args = mock_mcp.call_tool.call_args
                assert call_args[0][0] == "generate_marketing_content"
                
                # Verify task data was passed correctly
                task_data = call_args[1]["task_data"]
                assert task_data["task_type"] == "marketing_strategy"
                assert task_data["brand_description"] == user_input["marketing_form_data"]["brand_description"]
        
        finally:
            await memory_manager.stop()
    
    @pytest.mark.asyncio
    async def test_follow_up_question_handling(self, marketing_agent, memory_manager):
        """Test handling of follow-up questions after content generation."""
        await memory_manager.start()
        
        try:
            conversation_id = "conv_follow_up"
            user_id = "user_follow_up"
            
            # Register conversation
            await memory_manager.register_conversation(conversation_id, user_id)
            
            # Simulate conversation history with generated content
            conversation_history = [
                {
                    "sender": "user",
                    "content": "generate a marketing strategy",
                    "timestamp": "2024-01-01T10:00:00Z"
                },
                {
                    "sender": "ai",
                    "content": "Here's your marketing strategy...",
                    "timestamp": "2024-01-01T10:01:00Z",
                    "metadata": {"generated_content": True}
                }
            ]
            
            # Test follow-up question
            follow_up_input = {
                "message": "what else can be done",
                "conversation_history": conversation_history,
                "user_id": user_id,
                "conversation_id": conversation_id
            }
            
            # Mock conversation tool
            with patch.object(marketing_agent, 'mcp_server') as mock_mcp:
                mock_mcp.call_tool.return_value = {
                    "content": "Here are some additional marketing recommendations...",
                    "metadata": {"conversational_response": True}
                }
                
                response = await marketing_agent.process_message(
                    follow_up_input["message"],
                    follow_up_input
                )
                
                # Verify conversational response
                assert response is not None
                assert "content" in response
                assert response["metadata"]["conversational_response"] == True
                
                # Verify conversation tool was called
                mock_mcp.call_tool.assert_called_once()
                call_args = mock_mcp.call_tool.call_args
                assert call_args[0][0] == "handle_conversation"
        
        finally:
            await memory_manager.stop()
    
    @pytest.mark.asyncio
    async def test_caching_integration(self, marketing_agent, cache_manager):
        """Test integration with caching system."""
        # Test data
        cache_context = {
            "brand_description": "Test Brand",
            "target_audience": "Test Audience",
            "marketing_goals": "Test Goals"
        }
        
        test_content = {
            "strategy": "Test marketing strategy content",
            "generated_at": time.time()
        }
        
        # Test cache set
        success = await cache_manager.set(
            "marketing_strategy",
            cache_context,
            test_content,
            ttl=300
        )
        assert success == True
        
        # Test cache get
        cached_content = await cache_manager.get(
            "marketing_strategy",
            cache_context
        )
        assert cached_content == test_content
        
        # Test cache invalidation
        invalidated = await cache_manager.invalidate_by_event(
            "brand_info_changed",
            {"user_id": "test_user"}
        )
        assert invalidated >= 0
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, marketing_agent):
        """Test error handling across the system."""
        # Test with invalid input
        invalid_input = {
            "message": "",  # Empty message
            "user_id": "test_user",
            "conversation_id": "test_conv"
        }
        
        # Should handle gracefully
        response = await marketing_agent.process_message(
            invalid_input["message"],
            invalid_input
        )
        
        # Should return error response
        assert response is not None
        assert "metadata" in response
        assert response["metadata"].get("error") == True
    
    @pytest.mark.asyncio
    async def test_performance_monitoring_integration(self, marketing_agent, monitor):
        """Test performance monitoring integration."""
        # Start monitoring
        monitor.start_monitoring(interval=1.0)
        
        try:
            # Simulate some operations
            for i in range(5):
                start_time = time.time()
                
                # Simulate processing
                await asyncio.sleep(0.1)
                
                duration = time.time() - start_time
                monitor.record_operation(
                    f"test_operation_{i}",
                    duration,
                    success=True
                )
            
            # Wait for monitoring to collect data
            await asyncio.sleep(2.0)
            
            # Get performance summary
            summary = monitor.get_performance_summary()
            
            # Verify metrics
            assert "response_time_stats" in summary
            assert "request_counts" in summary
            assert summary["response_time_stats"]["count"] == 5
            
            # Test health check
            health = monitor.health_check()
            assert health["status"] in ["healthy", "degraded", "unhealthy"]
        
        finally:
            monitor.stop_monitoring()
    
    @pytest.mark.asyncio
    async def test_memory_management_integration(self, memory_manager):
        """Test memory management integration."""
        await memory_manager.start()
        
        try:
            # Register multiple conversations
            conversations = []
            for i in range(10):
                conv_id = f"conv_{i}"
                user_id = f"user_{i}"
                
                context = await memory_manager.register_conversation(
                    conv_id, user_id, {"test": f"data_{i}"}
                )
                conversations.append((conv_id, context))
            
            # Verify conversations are registered
            assert len(memory_manager._conversations) == 10
            
            # Test context caching
            await memory_manager.cache_context(
                "test_context",
                {"cached": "data"},
                ttl=60
            )
            
            cached_data = await memory_manager.get_cached_context("test_context")
            assert cached_data == {"cached": "data"}
            
            # Test memory stats
            stats = await memory_manager.get_memory_stats()
            assert stats.active_conversations == 10
            assert stats.cached_contexts >= 1
            
            # Test conversation cleanup
            conv_id, _ = conversations[0]
            success = await memory_manager.cleanup_conversation(conv_id)
            assert success == True
            assert len(memory_manager._conversations) == 9
        
        finally:
            await memory_manager.stop()
    
    @pytest.mark.asyncio
    async def test_validation_integration(self):
        """Test validation system integration."""
        # Test valid marketing task
        valid_task = {
            "task_type": "marketing_strategy",
            "brand_description": "Tech startup focused on AI solutions",
            "target_audience": "Tech professionals",
            "marketing_goals": "Increase brand awareness"
        }
        
        result = task_validator.validate_task(valid_task)
        assert result.is_valid == True
        assert len([issue for issue in result.issues if issue.severity.value == "error"]) == 0
        
        # Test invalid marketing task
        invalid_task = {
            "task_type": "invalid_type",
            "brand_description": "x"  # Too short
        }
        
        result = task_validator.validate_task(invalid_task)
        assert result.is_valid == False
        assert len([issue for issue in result.issues if issue.severity.value == "error"]) > 0
    
    @pytest.mark.asyncio
    async def test_concurrent_processing(self, marketing_agent, memory_manager):
        """Test concurrent processing of multiple requests."""
        await memory_manager.start()
        
        try:
            async def process_request(request_id: int):
                """Process a single request."""
                user_input = {
                    "message": f"generate marketing strategy {request_id}",
                    "user_id": f"user_{request_id}",
                    "conversation_id": f"conv_{request_id}",
                    "marketing_form_data": {
                        "brand_description": f"Brand {request_id}",
                        "target_audience": f"Audience {request_id}",
                        "marketing_goals": f"Goals {request_id}"
                    }
                }
                
                # Register conversation
                await memory_manager.register_conversation(
                    user_input["conversation_id"],
                    user_input["user_id"]
                )
                
                # Mock response
                with patch.object(marketing_agent, 'mcp_server') as mock_mcp:
                    mock_mcp.call_tool.return_value = {
                        "content": f"Marketing strategy {request_id}",
                        "metadata": {"generated_content": True}
                    }
                    
                    response = await marketing_agent.process_message(
                        user_input["message"],
                        user_input
                    )
                    
                    return response
            
            # Process multiple requests concurrently
            tasks = [process_request(i) for i in range(5)]
            responses = await asyncio.gather(*tasks)
            
            # Verify all responses
            assert len(responses) == 5
            for i, response in enumerate(responses):
                assert response is not None
                assert "content" in response
                assert f"Marketing strategy {i}" in response["content"]
        
        finally:
            await memory_manager.stop()
    
    @pytest.mark.asyncio
    async def test_system_health_check(self, marketing_agent, cache_manager, memory_manager, monitor):
        """Test complete system health check."""
        # Start all components
        await memory_manager.start()
        monitor.start_monitoring(interval=1.0)
        
        try:
            # Test cache health
            cache_health = await cache_manager.health_check()
            assert cache_health["status"] in ["healthy", "unhealthy"]
            
            # Test memory manager health (via stats)
            memory_stats = await memory_manager.get_memory_stats()
            assert memory_stats.total_memory_mb > 0
            
            # Test monitor health
            monitor_health = monitor.health_check()
            assert monitor_health["status"] in ["healthy", "degraded", "unhealthy"]
            
            # Test agent health (basic functionality)
            test_input = {
                "message": "test health check",
                "user_id": "health_check_user",
                "conversation_id": "health_check_conv"
            }
            
            with patch.object(marketing_agent, 'mcp_server') as mock_mcp:
                mock_mcp.call_tool.return_value = {
                    "content": "Health check response",
                    "metadata": {"conversational_response": True}
                }
                
                response = await marketing_agent.process_message(
                    test_input["message"],
                    test_input
                )
                
                assert response is not None
        
        finally:
            await memory_manager.stop()
            monitor.stop_monitoring()


class TestMarketingAgentLoadTesting:
    """Load testing for marketing agent system."""
    
    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_high_load_processing(self):
        """Test system under high load."""
        # This test would simulate high concurrent load
        # Skip in normal test runs due to resource requirements
        pytest.skip("Load test - run manually when needed")
    
    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_memory_pressure_handling(self):
        """Test system behavior under memory pressure."""
        # This test would simulate memory pressure scenarios
        # Skip in normal test runs due to resource requirements
        pytest.skip("Memory pressure test - run manually when needed")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "not slow"])
