#!/usr/bin/env python3
"""
Test script to verify the visualization pipeline is working correctly.
"""

import asyncio
import sys
import os
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.analysis_agent.composable_agent import ComposableAnalysisAgent
from agents.tools.mcp.pandasai_visualization import PandasAIVisualizationTool
from agents.tools.mcp.data_access import DataAccessTool

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_visualization_pipeline():
    """Test the visualization pipeline with a sample data file."""
    
    # Create a sample CSV file for testing
    sample_data = """Name,Age,Salary,Department
John,25,50000,Engineering
Jane,30,60000,<PERSON>,35,70000,<PERSON>,28,55000,<PERSON>,32,65000,<PERSON>,29,58000,Sales"""
    
    # Write sample data to a temporary file
    test_file_path = "temp_uploads/test_data.csv"
    os.makedirs("temp_uploads", exist_ok=True)
    
    with open(test_file_path, "w") as f:
        f.write(sample_data)
    
    logger.info(f"Created test data file: {test_file_path}")
    
    # Test the PandasAI visualization tool directly
    viz_tool = PandasAIVisualizationTool()
    
    # Test arguments
    test_args = {
        "data_source": test_file_path,
        "prompt": "Create a bar chart showing the average salary by department",
        "api_key": "test_key",
        "provider": "groq",
        "model": "llama3-8b-8192"
    }
    
    logger.info("Testing PandasAI visualization tool...")
    try:
        result = await viz_tool.execute(test_args)
        logger.info(f"Visualization tool result: {result}")
        
        if result.get("isError"):
            logger.error(f"Visualization tool error: {result}")
        else:
            logger.info("✅ Visualization tool executed successfully")
            
            # Check if visualization metadata is present
            if result.get("metadata", {}).get("visualization"):
                logger.info("✅ Visualization metadata found in result")
                viz_data = result["metadata"]["visualization"]
                logger.info(f"Visualization type: {viz_data.get('type')}")
                logger.info(f"Visualization title: {viz_data.get('title')}")
            else:
                logger.warning("⚠️ No visualization metadata found in result")
                
    except Exception as e:
        logger.error(f"Error testing visualization tool: {e}")
    
    # Test the composable analysis agent
    logger.info("\nTesting composable analysis agent...")
    try:
        # Initialize agent with proper configuration
        from agents.components.essential_tools import create_mcp_server_with_essential_tools

        # Create MCP server with essential tools
        mcp_server = await create_mcp_server_with_essential_tools()

        # Initialize agent with MCP server
        agent = ComposableAnalysisAgent()
        agent.mcp_server = mcp_server

        # Initialize agent with basic config
        await agent.initialize({
            "provider": "groq",
            "model": "llama3-8b-8192",
            "api_key": "test_key"
        })

        # Test message requesting visualization
        test_message = "Create a bar chart showing the average salary by department"

        # Test context
        test_context = {
            "conversation_history": [],
            "user_id": "test_user",
            "persona_id": "composable-analyst",
            "conversation_id": "test_conversation",
            "data_sources": [{"id": test_file_path, "name": "test_data.csv", "type": "csv"}]
        }

        result = await agent.process_message("test_user", test_message, "test_conversation", test_context)
        logger.info(f"Agent result: {result}")

        if result.get("metadata", {}).get("visualization"):
            logger.info("✅ Agent returned visualization metadata")
        else:
            logger.warning("⚠️ Agent did not return visualization metadata")

    except Exception as e:
        logger.error(f"Error testing analysis agent: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
    
    # Clean up
    try:
        os.remove(test_file_path)
        logger.info("Cleaned up test file")
    except:
        pass

if __name__ == "__main__":
    asyncio.run(test_visualization_pipeline())
