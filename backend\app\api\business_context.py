"""
Business Context Detection API endpoints.

This module provides REST API endpoints for detecting business context
from uploaded data and user interactions.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field

from ..auth import get_current_active_user
from ..database import User
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from agents.tools.mcp.business_context_detection import BusinessContextTool

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/agents", tags=["business-context"])


class BusinessContextRequest(BaseModel):
    """Request model for business context detection."""
    data_sources: Optional[List[str]] = Field(default=[], description="List of data source IDs to analyze")
    conversation_history: Optional[List[Dict[str, Any]]] = Field(default=[], description="Recent conversation messages")
    analysis_depth: str = Field(default="standard", description="Analysis depth: quick, standard, or comprehensive")
    include_recommendations: bool = Field(default=True, description="Whether to include marketing recommendations")
    use_ai_analysis: bool = Field(default=True, description="Whether to use AI-powered analysis for better accuracy")


class BusinessContextResponse(BaseModel):
    """Response model for business context detection."""
    success: bool
    business_context: Optional[Dict[str, Any]] = None
    recommendations: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.post("/detect-business-context", response_model=BusinessContextResponse)
async def detect_business_context(
    request: BusinessContextRequest,
    current_user: User = Depends(get_current_active_user)
) -> BusinessContextResponse:
    """
    Detect business context from uploaded data and conversation history.
    
    This endpoint analyzes user data sources and conversation history to extract
    business context information such as industry, business type, target market,
    and marketing challenges.
    """
    try:
        logger.info(f"Business context detection requested by user {current_user.id}")
        
        # Create business context detection tool instance
        context_tool = BusinessContextTool()
        await context_tool.initialize({})
        
        # Prepare user profile information
        user_profile = {
            "user_id": current_user.id,
            "email": current_user.email,
            "name": getattr(current_user, 'full_name', ''),
            "company": getattr(current_user, 'company', ''),
            "industry": getattr(current_user, 'industry', ''),
            "role": getattr(current_user, 'role', '')
        }
        
        # Filter data sources to only include user's own data
        user_data_sources = []
        if request.data_sources:
            # Here you would validate that the data sources belong to the current user
            # For now, we'll pass them through but in production you'd check ownership
            user_data_sources = request.data_sources
        
        # Execute business context detection
        detection_params = {
            "data_sources": user_data_sources,
            "conversation_history": request.conversation_history or [],
            "user_profile": user_profile,
            "analysis_depth": request.analysis_depth,
            "use_ai_analysis": request.use_ai_analysis
        }
        
        result = await context_tool.execute(**detection_params)
        
        if not result.get("success"):
            logger.error(f"Business context detection failed: {result.get('error')}")
            return BusinessContextResponse(
                success=False,
                error=result.get("error", "Unknown error occurred during context detection")
            )
        
        # Log successful detection
        business_context = result.get("business_context", {})
        confidence_score = business_context.get("confidence_score", 0.0)
        logger.info(f"Business context detected for user {current_user.id} with confidence {confidence_score:.2f}")
        
        # Prepare response
        response_data = {
            "success": True,
            "business_context": business_context,
            "metadata": result.get("metadata", {})
        }
        
        # Include recommendations if requested
        if request.include_recommendations:
            response_data["recommendations"] = result.get("recommendations", [])
        
        return BusinessContextResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in business context detection: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during business context detection"
        )


@router.get("/business-context-capabilities")
async def get_business_context_capabilities(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Get information about business context detection capabilities.
    
    Returns information about what types of context can be detected
    and what data sources are supported.
    """
    try:
        logger.info(f"Business context capabilities requested by user {current_user.id}")
        capabilities = {
            "supported_industries": [
                "technology", "healthcare", "finance", "retail", "education",
                "manufacturing", "real_estate", "consulting", "media", "nonprofit"
            ],
            "detectable_attributes": [
                "industry", "business_type", "business_size", "target_market",
                "key_products", "marketing_challenges", "competitive_advantages",
                "current_marketing_channels", "budget_indicators", "geographic_focus"
            ],
            "supported_data_sources": [
                "business_plans", "financial_documents", "marketing_materials",
                "website_content", "social_media_profiles", "competitor_analysis",
                "customer_surveys", "sales_data"
            ],
            "analysis_depths": {
                "quick": "Basic industry and business type detection",
                "standard": "Comprehensive context analysis with recommendations",
                "comprehensive": "Deep analysis with detailed insights and strategic recommendations"
            },
            "confidence_levels": {
                "low": "0.0 - 0.3: Limited data available, basic assumptions",
                "medium": "0.3 - 0.7: Good data coverage, reliable insights",
                "high": "0.7 - 1.0: Comprehensive data, high-confidence recommendations"
            }
        }
        
        return {
            "success": True,
            "capabilities": capabilities,
            "service_status": "available"
        }
        
    except Exception as e:
        logger.error(f"Error getting business context capabilities: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving business context capabilities"
        )


@router.post("/validate-business-context")
async def validate_business_context(
    context: Dict[str, Any],
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Validate and score a business context object.
    
    This endpoint can be used to validate business context data
    and get a confidence score for the provided information.
    """
    try:
        logger.info(f"Business context validation requested by user {current_user.id}")
        # Create business context detection tool for validation
        context_tool = BusinessContextTool()
        await context_tool.initialize({})
        
        # Validate required fields
        required_fields = ["industry", "business_type", "target_market"]
        missing_fields = [field for field in required_fields if not context.get(field)]
        
        # Calculate completeness score
        total_fields = [
            "industry", "business_type", "business_size", "target_market",
            "key_products", "marketing_challenges", "competitive_advantages",
            "current_marketing_channels", "budget_indicators", "geographic_focus"
        ]
        
        filled_fields = sum(1 for field in total_fields if context.get(field))
        completeness_score = filled_fields / len(total_fields)
        
        # Validate field values
        validation_errors = []
        
        # Industry validation
        valid_industries = [
            "technology", "healthcare", "finance", "retail", "education",
            "manufacturing", "real_estate", "consulting", "media", "nonprofit"
        ]
        if context.get("industry") and context["industry"] not in valid_industries:
            validation_errors.append(f"Invalid industry: {context['industry']}")
        
        # Business type validation
        valid_business_types = ["B2B", "B2C", "B2B2C", "marketplace"]
        if context.get("business_type") and context["business_type"] not in valid_business_types:
            validation_errors.append(f"Invalid business type: {context['business_type']}")
        
        # Business size validation
        valid_business_sizes = ["startup", "small", "medium", "large", "enterprise"]
        if context.get("business_size") and context["business_size"] not in valid_business_sizes:
            validation_errors.append(f"Invalid business size: {context['business_size']}")
        
        # Calculate overall confidence score
        confidence_score = completeness_score
        if validation_errors:
            confidence_score *= 0.5  # Reduce confidence if there are validation errors
        if missing_fields:
            confidence_score *= 0.7  # Reduce confidence if required fields are missing
        
        return {
            "success": True,
            "validation_result": {
                "is_valid": len(validation_errors) == 0 and len(missing_fields) == 0,
                "confidence_score": round(confidence_score, 2),
                "completeness_score": round(completeness_score, 2),
                "missing_fields": missing_fields,
                "validation_errors": validation_errors,
                "field_coverage": f"{filled_fields}/{len(total_fields)} fields completed"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating business context: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error validating business context"
        )
