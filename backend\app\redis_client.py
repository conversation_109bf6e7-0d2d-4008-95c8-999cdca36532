"""
Redis client for the Datagenius backend.

This module provides Redis client functionality for token blacklisting and session management.
"""

import logging
import json
import time
from typing import Optional, Any, Dict

import redis
from . import config

# Configure logging
logger = logging.getLogger(__name__)

# Initialize Redis client
try:
    redis_client = redis.from_url(config.REDIS_URL)
    logger.info(f"Connected to Redis at {config.REDIS_URL.split('@')[-1]}")
    # Test Redis connection
    redis_client.ping()
    logger.info("Redis connection test successful")
except redis.exceptions.ConnectionError as e:
    logger.error(f"Failed to connect to Redis: {e}")
    logger.error("Using in-memory storage for token blacklisting (not suitable for production)")
    logger.error("OAuth state management will not work correctly without Redis")
    redis_client = None
except Exception as e:
    logger.error(f"Redis error: {e}")
    logger.error("OAuth state management will not work correctly without Redis")
    redis_client = None

# In-memory storage for token blacklisting and other data (fallback if Redis is not available)
_token_blacklist = set()
_refresh_tokens = {}
_key_value_store = {}

# Token blacklisting
def blacklist_token(token: str, expires_in: int = 3600) -> bool:
    """
    Add a token to the blacklist.

    Args:
        token: JWT token to blacklist
        expires_in: Time in seconds until the token expires

    Returns:
        True if the token was added to the blacklist, False otherwise
    """
    if redis_client:
        try:
            # Add the token to the Redis blacklist with expiration
            redis_client.setex(f"blacklist:{token}", expires_in, "1")
            return True
        except Exception as e:
            logger.error(f"Error adding token to Redis blacklist: {e}")
            # Fall back to in-memory storage
            _token_blacklist.add(token)
            return True
    else:
        # Use in-memory storage
        _token_blacklist.add(token)
        return True

def is_token_blacklisted(token: str) -> bool:
    """
    Check if a token is blacklisted.

    Args:
        token: JWT token to check

    Returns:
        True if the token is blacklisted, False otherwise
    """
    if redis_client:
        try:
            # Check if the token is in the Redis blacklist
            return bool(redis_client.exists(f"blacklist:{token}"))
        except Exception as e:
            logger.error(f"Error checking token in Redis blacklist: {e}")
            # Fall back to in-memory storage
            return token in _token_blacklist
    else:
        # Use in-memory storage
        return token in _token_blacklist

# Refresh token management
def store_refresh_token(user_id: int, token: str, expires: int, metadata: Optional[Dict[str, Any]] = None, device_fingerprint: Optional[str] = None, refresh_count: int = 0) -> bool:
    """
    Store a refresh token.

    Args:
        user_id: User ID
        token: Refresh token
        expires: Expiration time in seconds
        metadata: Optional metadata about the token
        device_fingerprint: Optional device fingerprint
        refresh_count: Number of times this token has been refreshed

    Returns:
        True if the token was stored, False otherwise
    """
    if redis_client:
        try:
            # Store the token in Redis with expiration
            redis_client.setex(f"refresh:{token}", expires, str(user_id))

            # Store metadata if provided
            if metadata:
                redis_client.setex(f"refresh:{token}:metadata", expires, json.dumps(metadata))

            # Store device fingerprint if provided
            if device_fingerprint:
                redis_client.setex(f"refresh:{token}:device", expires, device_fingerprint)

            # Store refresh count
            redis_client.setex(f"refresh:{token}:count", expires, str(refresh_count))

            return True
        except Exception as e:
            logger.error(f"Error storing refresh token in Redis: {e}")
            # Fall back to in-memory storage
            _refresh_tokens[token] = {
                "user_id": user_id,
                "metadata": metadata,
                "device_fingerprint": device_fingerprint,
                "refresh_count": refresh_count
            }
            return True
    else:
        # Use in-memory storage
        _refresh_tokens[token] = {
            "user_id": user_id,
            "metadata": metadata,
            "device_fingerprint": device_fingerprint,
            "refresh_count": refresh_count
        }
        return True

def get_token_refresh_count(token: str) -> int:
    """
    Get the refresh count for a token.

    Args:
        token: Refresh token

    Returns:
        Number of times the token has been refreshed, or 0 if not found
    """
    if redis_client:
        try:
            # Get the refresh count from Redis
            count = redis_client.get(f"refresh:{token}:count")
            if count:
                return int(count.decode('utf-8'))
            return 0
        except Exception as e:
            logger.error(f"Error getting refresh count from Redis: {e}")
            # Fall back to in-memory storage
            token_data = _refresh_tokens.get(token)
            return token_data.get("refresh_count", 0) if token_data else 0
    else:
        # Use in-memory storage
        token_data = _refresh_tokens.get(token)
        return token_data.get("refresh_count", 0) if token_data else 0


def increment_token_refresh_count(token: str, expires: int) -> int:
    """
    Increment the refresh count for a token.

    Args:
        token: Refresh token
        expires: Expiration time in seconds

    Returns:
        New refresh count
    """
    current_count = get_token_refresh_count(token)
    new_count = current_count + 1

    if redis_client:
        try:
            # Update the refresh count in Redis
            redis_client.setex(f"refresh:{token}:count", expires, str(new_count))
        except Exception as e:
            logger.error(f"Error updating refresh count in Redis: {e}")
            # Fall back to in-memory storage
            if token in _refresh_tokens:
                _refresh_tokens[token]["refresh_count"] = new_count
    else:
        # Use in-memory storage
        if token in _refresh_tokens:
            _refresh_tokens[token]["refresh_count"] = new_count

    return new_count


def get_token_metadata(token: str) -> Optional[Dict[str, Any]]:
    """
    Get the metadata associated with a refresh token.

    Args:
        token: Refresh token

    Returns:
        Metadata dictionary if found, None otherwise
    """
    if redis_client:
        try:
            # Get the metadata from Redis
            metadata_json = redis_client.get(f"refresh:{token}:metadata")
            if metadata_json:
                return json.loads(metadata_json.decode('utf-8'))
            return None
        except Exception as e:
            logger.error(f"Error getting metadata from Redis: {e}")
            # Fall back to in-memory storage
            token_data = _refresh_tokens.get(token)
            return token_data.get("metadata") if token_data else None
    else:
        # Use in-memory storage
        token_data = _refresh_tokens.get(token)
        return token_data.get("metadata") if token_data else None


def get_token_device_fingerprint(token: str) -> Optional[str]:
    """
    Get the device fingerprint associated with a refresh token.

    Args:
        token: Refresh token

    Returns:
        Device fingerprint if found, None otherwise
    """
    if redis_client:
        try:
            # Get the device fingerprint from Redis
            device = redis_client.get(f"refresh:{token}:device")
            if device:
                return device.decode('utf-8')
            return None
        except Exception as e:
            logger.error(f"Error getting device fingerprint from Redis: {e}")
            # Fall back to in-memory storage
            token_data = _refresh_tokens.get(token)
            return token_data.get("device_fingerprint") if token_data else None
    else:
        # Use in-memory storage
        token_data = _refresh_tokens.get(token)
        return token_data.get("device_fingerprint") if token_data else None


def validate_refresh_token(token: str, device_fingerprint: Optional[str] = None) -> Optional[int]:
    """
    Validate a refresh token.

    Args:
        token: Refresh token
        device_fingerprint: Optional device fingerprint to validate against

    Returns:
        User ID if the token is valid, None otherwise
    """
    # First, check if the token exists
    user_id = None

    if redis_client:
        try:
            # Get the user ID from Redis
            user_id_bytes = redis_client.get(f"refresh:{token}")
            if user_id_bytes:
                user_id = int(user_id_bytes)
            else:
                return None
        except Exception as e:
            logger.error(f"Error validating refresh token in Redis: {e}")
            # Fall back to in-memory storage
            token_data = _refresh_tokens.get(token)
            if token_data:
                user_id = token_data["user_id"]
            else:
                return None
    else:
        # Use in-memory storage
        token_data = _refresh_tokens.get(token)
        if token_data:
            user_id = token_data["user_id"]
        else:
            return None

    # If device fingerprint validation is requested
    if device_fingerprint:
        stored_fingerprint = get_token_device_fingerprint(token)

        # If there's a stored fingerprint and it doesn't match, reject the token
        if stored_fingerprint and stored_fingerprint != device_fingerprint:
            logger.warning(f"Device fingerprint mismatch for token. Expected: {stored_fingerprint}, Got: {device_fingerprint}")
            return None

    return user_id

def invalidate_refresh_token(token: str) -> bool:
    """
    Invalidate a refresh token.

    Args:
        token: Refresh token

    Returns:
        True if the token was invalidated, False otherwise
    """
    if redis_client:
        try:
            # Delete the token from Redis
            redis_client.delete(f"refresh:{token}")
            return True
        except Exception as e:
            logger.error(f"Error invalidating refresh token in Redis: {e}")
            # Fall back to in-memory storage
            if token in _refresh_tokens:
                del _refresh_tokens[token]
            return True
    else:
        # Use in-memory storage
        if token in _refresh_tokens:
            del _refresh_tokens[token]
        return True

# Cache management
def set_cache(key: str, value: Any, expires_in: int = 3600) -> bool:
    """
    Set a value in the cache.

    Args:
        key: Cache key
        value: Value to cache
        expires_in: Time in seconds until the value expires

    Returns:
        True if the value was cached, False otherwise
    """
    if redis_client:
        try:
            # Serialize the value to JSON
            serialized_value = json.dumps(value)
            # Store the value in Redis with expiration
            redis_client.setex(f"cache:{key}", expires_in, serialized_value)
            logger.debug(f"Successfully set cache for key: {key} with expiry: {expires_in}s")
            return True
        except Exception as e:
            logger.error(f"Error setting cache in Redis: {e}")
            return False
    else:
        logger.warning("Redis not available, caching not supported - OAuth will not work correctly")
        return False

def get_cache(key: str) -> Optional[Any]:
    """
    Get a value from the cache.

    Args:
        key: Cache key

    Returns:
        Cached value if found, None otherwise
    """
    if redis_client:
        try:
            # Get the value from Redis
            value = redis_client.get(f"cache:{key}")
            if value:
                # Deserialize the value from JSON
                logger.debug(f"Cache hit for key: {key}")
                return json.loads(value)
            logger.debug(f"Cache miss for key: {key}")
            return None
        except Exception as e:
            logger.error(f"Error getting cache from Redis: {e}")
            return None
    else:
        logger.warning("Redis not available, caching not supported - OAuth will not work correctly")
        return None

def delete_cache(key: str) -> bool:
    """
    Delete a value from the cache.

    Args:
        key: Cache key

    Returns:
        True if the value was deleted, False otherwise
    """
    if redis_client:
        try:
            # Delete the value from Redis
            full_key = f"cache:{key}"
            logger.debug(f"Deleting cache key: {full_key}")
            result = redis_client.delete(full_key)
            logger.debug(f"Redis delete result: {result}")
            return result > 0
        except Exception as e:
            logger.error(f"Error deleting cache from Redis: {e}")
            return False
    else:
        logger.warning("Redis not available, caching not supported - OAuth will not work correctly")
        return False


def get_current_timestamp() -> int:
    """
    Get the current Unix timestamp.

    Returns:
        Current Unix timestamp in seconds
    """
    return int(time.time())


# OAuth state management
def set_with_expiry(key: str, value: str, expires_in: int) -> bool:
    """
    Set a value with expiration.

    Args:
        key: Key to store
        value: Value to store
        expires_in: Time in seconds until the value expires

    Returns:
        True if the value was stored, False otherwise
    """
    if redis_client:
        try:
            # Store the value in Redis with expiration
            redis_client.setex(key, expires_in, value)
            logger.debug(f"Successfully set key: {key} with expiry: {expires_in}s")
            return True
        except Exception as e:
            logger.error(f"Error setting key in Redis: {e}")
            # Fall back to in-memory storage
            _key_value_store[key] = value
            logger.warning(f"Stored key {key} in memory instead of Redis")
            return True
    else:
        # Use in-memory storage
        _key_value_store[key] = value
        logger.warning(f"Redis not available, stored key {key} in memory")
        return True


def get_value(key: str) -> Optional[str]:
    """
    Get a value.

    Args:
        key: Key to retrieve

    Returns:
        Value if found, None otherwise
    """
    if redis_client:
        try:
            # Get the value from Redis
            value = redis_client.get(key)
            if value:
                return value.decode('utf-8')
            # If not in Redis, check in-memory storage
            if key in _key_value_store:
                logger.debug(f"Found key {key} in memory storage")
                return _key_value_store[key]
            return None
        except Exception as e:
            logger.error(f"Error getting value from Redis: {e}")
            # Check in-memory storage
            if key in _key_value_store:
                logger.debug(f"Found key {key} in memory storage after Redis error")
                return _key_value_store[key]
            return None
    else:
        # Use in-memory storage
        if key in _key_value_store:
            logger.debug(f"Redis not available, found key {key} in memory storage")
            return _key_value_store[key]
        logger.warning(f"Redis not available, key {key} not found in memory storage")
        return None


def delete_key(key: str) -> bool:
    """
    Delete a key.

    Args:
        key: Key to delete

    Returns:
        True if the key was deleted, False otherwise
    """
    if redis_client:
        try:
            # Delete the key from Redis
            result = redis_client.delete(key)
            # Also remove from in-memory storage if it exists
            if key in _key_value_store:
                del _key_value_store[key]
                logger.debug(f"Deleted key {key} from memory storage")
            logger.debug(f"Redis delete result for key {key}: {result}")
            return result > 0 or key in _key_value_store
        except Exception as e:
            logger.error(f"Error deleting key from Redis: {e}")
            # Try to delete from in-memory storage
            if key in _key_value_store:
                del _key_value_store[key]
                logger.debug(f"Deleted key {key} from memory storage after Redis error")
                return True
            return False
    else:
        # Use in-memory storage
        if key in _key_value_store:
            del _key_value_store[key]
            logger.debug(f"Redis not available, deleted key {key} from memory storage")
            return True
        logger.warning(f"Redis not available, key {key} not found in memory storage")
        return False
