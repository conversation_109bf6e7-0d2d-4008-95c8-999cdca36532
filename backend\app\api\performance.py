"""
Performance monitoring and optimization API endpoints.

This module provides REST API endpoints for monitoring and controlling
Phase 1 performance optimizations.
"""

import logging
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..database import get_db
from ..auth import get_current_active_user, User
from ..performance import (
    performance_manager,
    initialize_performance_optimizations,
    get_performance_summary
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/performance", tags=["performance"])


@router.get("/status")
async def get_performance_status(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Get current performance optimization status and metrics.
    
    Returns comprehensive performance metrics including:
    - Cache performance (hit rates, memory usage)
    - Database performance (query times, connection stats)
    - WebSocket performance (connection counts, error rates)
    - Overall health score
    """
    try:
        summary = get_performance_summary()
        
        # Add user-friendly interpretations
        summary["interpretations"] = _interpret_performance_metrics(summary)
        
        return {
            "status": "success",
            "data": summary,
            "message": "Performance metrics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance metrics"
        )


@router.post("/initialize")
async def initialize_optimizations(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Initialize or re-initialize all Phase 1 performance optimizations.
    
    This endpoint triggers:
    - Cache system initialization
    - Database index creation and optimization
    - WebSocket connection pool setup
    - Performance monitoring activation
    
    Note: This operation may take several seconds to complete.
    """
    try:
        logger.info(f"Performance optimization initialization requested by user {current_user.id}")
        
        results = await initialize_performance_optimizations()
        
        return {
            "status": "success" if results["overall_status"] == "success" else "partial",
            "data": results,
            "message": f"Performance optimizations {results['overall_status']}"
        }
        
    except Exception as e:
        logger.error(f"Failed to initialize performance optimizations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initialize performance optimizations"
        )


@router.get("/cache/stats")
async def get_cache_statistics(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Get detailed cache performance statistics.
    
    Returns:
    - Hit rates for each cache level (L1, L2, L3)
    - Memory usage and cache sizes
    - Cache warming statistics
    - Dependency tracking information
    """
    try:
        cache_stats = performance_manager.cache_manager.get_stats()
        memory_usage = await performance_manager.cache_manager.get_memory_usage()
        
        return {
            "status": "success",
            "data": {
                "cache_stats": cache_stats,
                "memory_usage": memory_usage,
                "recommendations": _get_cache_recommendations(cache_stats)
            },
            "message": "Cache statistics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to get cache statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve cache statistics"
        )


@router.post("/cache/clear")
async def clear_cache(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Clear all cache levels.
    
    This will clear:
    - L1 memory cache
    - L2 Redis cache
    - Cache dependencies
    
    Use with caution as this will temporarily reduce performance.
    """
    try:
        logger.info(f"Cache clear requested by user {current_user.id}")
        
        performance_manager.cache_manager.clear()
        
        return {
            "status": "success",
            "message": "All cache levels cleared successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear cache"
        )


@router.get("/database/stats")
async def get_database_statistics(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Get detailed database performance statistics.
    
    Returns:
    - Connection pool statistics
    - Query performance metrics
    - Index usage statistics
    - Optimization recommendations
    """
    try:
        pool_stats = performance_manager.pool_manager.get_stats()
        db_metrics = performance_manager.db_optimizer.get_performance_metrics()
        
        return {
            "status": "success",
            "data": {
                "connection_pool": pool_stats,
                "database_metrics": db_metrics,
                "recommendations": _get_database_recommendations(pool_stats)
            },
            "message": "Database statistics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to get database statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve database statistics"
        )


@router.get("/websocket/stats")
async def get_websocket_statistics(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Get detailed WebSocket performance statistics.
    
    Returns:
    - Active connection counts
    - Reconnection statistics
    - Message queue status
    - Error rates and performance metrics
    """
    try:
        ws_stats = performance_manager.ws_manager.get_performance_stats()
        
        return {
            "status": "success",
            "data": {
                "websocket_stats": ws_stats,
                "recommendations": _get_websocket_recommendations(ws_stats)
            },
            "message": "WebSocket statistics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to get WebSocket statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve WebSocket statistics"
        )


@router.post("/database/optimize")
async def optimize_database(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Trigger database optimization procedures.
    
    This will:
    - Create missing performance indexes
    - Update database statistics
    - Apply PostgreSQL-specific optimizations
    """
    try:
        logger.info(f"Database optimization requested by user {current_user.id}")
        
        results = await performance_manager.db_optimizer.apply_dashboard_optimizations()
        
        return {
            "status": "success",
            "data": results,
            "message": f"Database optimization completed: {results['indexes_created']} indexes created"
        }
        
    except Exception as e:
        logger.error(f"Failed to optimize database: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to optimize database"
        )


def _interpret_performance_metrics(summary: Dict[str, Any]) -> Dict[str, str]:
    """Provide user-friendly interpretations of performance metrics."""
    interpretations = {}
    
    # Cache interpretation
    cache_stats = summary.get("cache_stats", {})
    hit_rate = cache_stats.get("hit_rate_percent", 0)
    if hit_rate >= 85:
        interpretations["cache"] = "Excellent - Cache is performing optimally"
    elif hit_rate >= 70:
        interpretations["cache"] = "Good - Cache performance is acceptable"
    elif hit_rate >= 50:
        interpretations["cache"] = "Fair - Cache could be improved"
    else:
        interpretations["cache"] = "Poor - Cache needs optimization"
    
    # Database interpretation
    db_stats = summary.get("database_stats", {})
    avg_query_time = db_stats.get("avg_query_time", 0)
    if avg_query_time <= 0.1:
        interpretations["database"] = "Excellent - Database queries are very fast"
    elif avg_query_time <= 0.5:
        interpretations["database"] = "Good - Database performance is acceptable"
    elif avg_query_time <= 1.0:
        interpretations["database"] = "Fair - Database could be optimized"
    else:
        interpretations["database"] = "Poor - Database needs optimization"
    
    # WebSocket interpretation
    ws_stats = summary.get("websocket_stats", {})
    error_rate = ws_stats.get("error_rate", 0)
    if error_rate <= 0.01:
        interpretations["websocket"] = "Excellent - WebSocket connections are stable"
    elif error_rate <= 0.05:
        interpretations["websocket"] = "Good - WebSocket performance is acceptable"
    elif error_rate <= 0.1:
        interpretations["websocket"] = "Fair - Some WebSocket issues detected"
    else:
        interpretations["websocket"] = "Poor - WebSocket connections need attention"
    
    # Overall health
    health_score = summary.get("health_score", 0)
    if health_score >= 90:
        interpretations["overall"] = "Excellent - System is performing optimally"
    elif health_score >= 75:
        interpretations["overall"] = "Good - System performance is acceptable"
    elif health_score >= 60:
        interpretations["overall"] = "Fair - Some optimization opportunities exist"
    else:
        interpretations["overall"] = "Poor - System needs performance optimization"
    
    return interpretations


def _get_cache_recommendations(cache_stats: Dict[str, Any]) -> List[str]:
    """Get cache optimization recommendations."""
    recommendations = []
    
    hit_rate = cache_stats.get("hit_rate_percent", 0)
    if hit_rate < 85:
        recommendations.append("Consider increasing cache TTL or warming more data")
    
    memory_size = cache_stats.get("memory_cache_size", 0)
    max_size = cache_stats.get("memory_cache_max_size", 1000)
    if memory_size / max_size > 0.9:
        recommendations.append("Consider increasing memory cache size")
    
    if not cache_stats.get("redis_available", False):
        recommendations.append("Enable Redis for L2 caching to improve performance")
    
    return recommendations


def _get_database_recommendations(pool_stats: Dict[str, Any]) -> List[str]:
    """Get database optimization recommendations."""
    recommendations = []
    
    avg_query_time = pool_stats.get("avg_query_time", 0)
    if avg_query_time > 0.5:
        recommendations.append("Consider adding database indexes for slow queries")
    
    slow_queries = pool_stats.get("slow_queries", 0)
    total_queries = pool_stats.get("query_count", 1)
    if slow_queries / total_queries > 0.1:
        recommendations.append("High percentage of slow queries detected")
    
    connection_errors = pool_stats.get("connection_errors", 0)
    if connection_errors > 0:
        recommendations.append("Database connection errors detected - check configuration")
    
    return recommendations


def _get_websocket_recommendations(ws_stats: Dict[str, Any]) -> List[str]:
    """Get WebSocket optimization recommendations."""
    recommendations = []
    
    error_rate = ws_stats.get("error_rate", 0)
    if error_rate > 0.05:
        recommendations.append("High WebSocket error rate - check network stability")
    
    queued_messages = ws_stats.get("queued_messages", 0)
    if queued_messages > 100:
        recommendations.append("High number of queued messages - check connection stability")
    
    reconnections = ws_stats.get("reconnections", 0)
    total_connections = ws_stats.get("total_connections", 1)
    if reconnections / total_connections > 0.1:
        recommendations.append("High reconnection rate detected")
    
    return recommendations
