import { useState, useEffect } from 'react';
import { providerApi } from '@/lib/api';

export interface ProviderAvailability {
  // User-specific provider availability
  userProviders: Record<string, boolean>;
  // Global provider availability (from .env file)
  globalProviders: Record<string, boolean>;
  // Combined availability (true if either user or global is available)
  combinedAvailability: Record<string, boolean>;
  // Loading state
  isLoading: boolean;
  // Error state
  error: string | null;
  // Refresh function
  refresh: () => Promise<void>;
}

/**
 * Hook to check for provider availability from both user-specific and global sources
 */
export function useProviderAvailability(): ProviderAvailability {
  const [userProviders, setUserProviders] = useState<Record<string, boolean>>({});
  const [globalProviders, setGlobalProviders] = useState<Record<string, boolean>>({});
  const [combinedAvailability, setCombinedAvailability] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProviderAvailability = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Fetching provider availability...');

      // Initialize with default fallback values
      let userAvailability: Record<string, boolean> = {};
      let globalAvailability: Record<string, boolean> = {};

      // Fetch user-specific provider availability with error handling
      try {
        console.log('Fetching user-specific provider availability...');
        const userResponse = await providerApi.getProviders();
        console.log('User provider response:', userResponse);

        userResponse.providers.forEach(provider => {
          userAvailability[provider.id] = provider.is_available;
        });

        console.log('User provider availability:', userAvailability);
        setUserProviders(userAvailability);
      } catch (userError) {
        console.error('Error fetching user provider availability:', userError);
        // Set fallback availability for common providers
        userAvailability = {
          'groq': true,
          'openai': false,
          'gemini': false,
          'openrouter': false,
          'anthropic': false,
          'requesty': false
        };
        setUserProviders(userAvailability);
      }

      // Fetch global provider availability with error handling
      try {
        console.log('Fetching global provider availability...');
        globalAvailability = await providerApi.getGlobalAvailability();
        console.log('Global provider availability:', globalAvailability);
        setGlobalProviders(globalAvailability);
      } catch (globalError) {
        console.error('Error fetching global provider availability:', globalError);
        // Continue with empty global availability
        globalAvailability = {};
        setGlobalProviders({});
      }

      // Combine the two sources
      const combined: Record<string, boolean> = {};

      // Include all provider IDs from both sources
      const allProviderIds = new Set([
        ...Object.keys(userAvailability),
        ...Object.keys(globalAvailability)
      ]);

      console.log('All provider IDs:', Array.from(allProviderIds));

      // A provider is available if it's available in either source
      allProviderIds.forEach(providerId => {
        combined[providerId] =
          (userAvailability[providerId] || false) ||
          (globalAvailability[providerId] || false);
      });

      console.log('Combined provider availability:', combined);
      setCombinedAvailability(combined);
    } catch (err) {
      console.error('Error fetching provider availability:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');

      // Set fallback availability for essential providers
      const fallbackAvailability = {
        'groq': true, // Assume groq is available as it's the default
        'openai': false,
        'gemini': false,
        'openrouter': false,
        'anthropic': false,
        'requesty': false
      };

      setUserProviders(fallbackAvailability);
      setGlobalProviders({});
      setCombinedAvailability(fallbackAvailability);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch provider availability on mount
  useEffect(() => {
    fetchProviderAvailability();
  }, []);

  return {
    userProviders,
    globalProviders,
    combinedAvailability,
    isLoading,
    error,
    refresh: fetchProviderAvailability
  };
}
