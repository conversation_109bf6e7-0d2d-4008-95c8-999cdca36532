#!/usr/bin/env python3
"""
Test script to verify file type detection in the data access tool.

This script tests that:
1. PDF files are properly detected and rejected for data analysis
2. CSV/Excel files are accepted for data analysis
3. Appropriate error messages are returned
"""

import logging
import sys
import os
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_file_type_detection():
    """Test file type detection logic."""
    logger.info("🚀 Testing file type detection logic")
    
    # Define file extensions
    data_file_extensions = ['.csv', '.xlsx', '.xls', '.json']
    document_file_extensions = ['.pdf', '.docx', '.txt']
    
    def check_file_type(file_path):
        """Simulate the file type checking logic."""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension in data_file_extensions:
            return "data_file", file_extension
        elif file_extension in document_file_extensions:
            return "document_file", file_extension
        else:
            return "unknown", file_extension
    
    # Test cases
    test_files = [
        "sales_data.csv",
        "financial_report.xlsx", 
        "marketing_strategy.pdf",
        "company_overview.docx",
        "data_export.json",
        "unknown_file.xyz"
    ]
    
    logger.info("\n" + "="*60)
    logger.info("FILE TYPE DETECTION TEST RESULTS")
    logger.info("="*60)
    
    for file_path in test_files:
        file_type, extension = check_file_type(file_path)
        
        if file_type == "data_file":
            status = "✅ ACCEPTED for data analysis"
            action = "Would load as DataFrame"
        elif file_type == "document_file":
            status = "❌ REJECTED for data analysis"
            action = "Would suggest document tools"
        else:
            status = "❌ UNKNOWN file type"
            action = "Would show error message"
        
        logger.info(f"📁 {file_path:<25} | {extension:<6} | {status}")
        logger.info(f"   └─ Action: {action}")
        logger.info("")
    
    # Test the specific error case from the user
    logger.info("="*60)
    logger.info("SPECIFIC TEST: PDF FILE HANDLING")
    logger.info("="*60)
    
    pdf_file = "temp_uploads/df0c3ebb-2f78-48f2-ba84-857260f8a3ca.pdf"
    file_type, extension = check_file_type(pdf_file)
    
    if file_type == "document_file":
        logger.info(f"✅ PDF file correctly identified as document")
        logger.info(f"📄 File: {pdf_file}")
        logger.info(f"🔍 Extension: {extension}")
        logger.info(f"💬 User would see: 'This is a document file, not a data file'")
        logger.info(f"📋 Suggestion: 'Please upload CSV, Excel, or JSON for data analysis'")
        return True
    else:
        logger.error(f"❌ PDF file incorrectly handled as: {file_type}")
        return False

def main():
    """Main test function."""
    logger.info("Starting file type detection tests...")
    
    success = test_file_type_detection()
    
    if success:
        logger.info("\n🎉 File type detection test completed successfully!")
        logger.info("📈 The fix should resolve the PDF file error.")
        return 0
    else:
        logger.error("\n💥 File type detection test failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
