#!/usr/bin/env python3
"""
Migration utility to upgrade existing embedded documents to adaptive chunking.

This script identifies documents embedded with traditional chunking and
re-embeds them using adaptive chunking strategies for improved performance.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
import yaml
import json
from datetime import datetime

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.utils.vector_service import VectorService
from agents.utils.adaptive_chunking import AdaptiveChunker
from agents.utils.chunking_performance_manager import ChunkingPerformanceManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdaptiveChunkingMigrator:
    """Utility to migrate existing documents to adaptive chunking."""
    
    def __init__(self, dry_run: bool = True):
        """
        Initialize the migrator.
        
        Args:
            dry_run: If True, only analyze without making changes
        """
        self.dry_run = dry_run
        self.vector_service = VectorService()
        self.adaptive_chunker = AdaptiveChunker()
        self.performance_manager = ChunkingPerformanceManager()
        self.migration_stats = {
            "total_documents": 0,
            "migrated_documents": 0,
            "skipped_documents": 0,
            "failed_documents": 0,
            "performance_improvements": []
        }
    
    def scan_existing_documents(self) -> List[Dict[str, Any]]:
        """
        Scan for existing embedded documents.
        
        Returns:
            List of document information dictionaries
        """
        documents = []
        vector_db_dir = self.vector_service.vector_db_dir
        
        if not os.path.exists(vector_db_dir):
            logger.warning(f"Vector database directory not found: {vector_db_dir}")
            return documents
        
        # Scan for document info files
        for file_path in Path(vector_db_dir).glob("*_info.yaml"):
            try:
                with open(file_path, 'r') as f:
                    file_info = yaml.safe_load(f)
                
                # Check if this document uses traditional chunking
                if self._is_traditional_chunking(file_info):
                    documents.append({
                        "vector_store_id": file_info.get("vector_store_id"),
                        "file_path": file_info.get("file_path"),
                        "file_info": file_info,
                        "info_file_path": str(file_path)
                    })
                    
            except Exception as e:
                logger.error(f"Error reading {file_path}: {e}")
        
        logger.info(f"Found {len(documents)} documents using traditional chunking")
        return documents
    
    def _is_traditional_chunking(self, file_info: Dict[str, Any]) -> bool:
        """
        Check if a document uses traditional chunking.
        
        Args:
            file_info: Document file information
            
        Returns:
            True if document uses traditional chunking
        """
        # Check for traditional chunking indicators
        has_chunk_size = "chunk_size" in file_info
        has_chunk_overlap = "chunk_overlap" in file_info
        no_adaptive_strategy = "chunking_strategy" not in file_info or file_info.get("chunking_strategy") == "traditional"
        no_content_type = "content_type" not in file_info
        
        return has_chunk_size and has_chunk_overlap and no_adaptive_strategy and no_content_type
    
    async def analyze_migration_benefits(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze potential benefits of migrating to adaptive chunking.
        
        Args:
            documents: List of documents to analyze
            
        Returns:
            Analysis results
        """
        analysis = {
            "total_documents": len(documents),
            "content_type_distribution": {},
            "chunk_size_distribution": {},
            "estimated_improvements": {},
            "recommendations": []
        }
        
        for doc in documents:
            file_info = doc["file_info"]
            file_path = doc["file_path"]
            
            # Analyze content type
            if os.path.exists(file_path):
                try:
                    # Read a sample of the document to analyze content type
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        sample_text = f.read(2000)  # Read first 2KB
                    
                    file_extension = os.path.splitext(file_path)[1].lower()
                    content_type = self.adaptive_chunker.analyze_content_type(sample_text, file_extension)
                    
                    # Update distribution
                    content_type_str = content_type.value
                    analysis["content_type_distribution"][content_type_str] = \
                        analysis["content_type_distribution"].get(content_type_str, 0) + 1
                    
                    # Analyze current chunk size
                    current_chunk_size = file_info.get("chunk_size", 1000)
                    chunk_size_range = self._get_chunk_size_range(current_chunk_size)
                    analysis["chunk_size_distribution"][chunk_size_range] = \
                        analysis["chunk_size_distribution"].get(chunk_size_range, 0) + 1
                    
                    # Estimate improvement potential
                    optimal_strategy = self.adaptive_chunker.strategies.get(content_type)
                    if optimal_strategy:
                        improvement = self._estimate_improvement(file_info, optimal_strategy)
                        if improvement["potential_improvement"] > 0:
                            analysis["estimated_improvements"][file_path] = improvement
                
                except Exception as e:
                    logger.warning(f"Could not analyze {file_path}: {e}")
        
        # Generate recommendations
        analysis["recommendations"] = self._generate_migration_recommendations(analysis)
        
        return analysis
    
    def _get_chunk_size_range(self, chunk_size: int) -> str:
        """Get chunk size range category."""
        if chunk_size < 500:
            return "small (<500)"
        elif chunk_size < 1000:
            return "medium (500-1000)"
        elif chunk_size < 1500:
            return "large (1000-1500)"
        else:
            return "very_large (>1500)"
    
    def _estimate_improvement(self, current_info: Dict[str, Any], optimal_strategy) -> Dict[str, Any]:
        """Estimate improvement potential from migration."""
        current_chunk_size = current_info.get("chunk_size", 1000)
        current_overlap = current_info.get("chunk_overlap", 200)
        
        optimal_chunk_size = optimal_strategy.chunk_size
        optimal_overlap = optimal_strategy.chunk_overlap
        
        # Estimate processing time improvement (simplified)
        size_improvement = abs(current_chunk_size - optimal_chunk_size) / current_chunk_size
        overlap_improvement = abs(current_overlap - optimal_overlap) / current_overlap
        semantic_improvement = 0.2 if optimal_strategy.semantic_splitting else 0
        
        total_improvement = (size_improvement + overlap_improvement + semantic_improvement) * 100
        
        return {
            "current_chunk_size": current_chunk_size,
            "optimal_chunk_size": optimal_chunk_size,
            "current_overlap": current_overlap,
            "optimal_overlap": optimal_overlap,
            "semantic_splitting": optimal_strategy.semantic_splitting,
            "potential_improvement": min(total_improvement, 50)  # Cap at 50%
        }
    
    def _generate_migration_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate migration recommendations based on analysis."""
        recommendations = []
        
        total_docs = analysis["total_documents"]
        improvements = analysis["estimated_improvements"]
        
        if total_docs == 0:
            recommendations.append("No documents found that need migration.")
            return recommendations
        
        # Calculate average improvement potential
        if improvements:
            avg_improvement = sum(imp["potential_improvement"] for imp in improvements.values()) / len(improvements)
            recommendations.append(f"Average performance improvement potential: {avg_improvement:.1f}%")
        
        # Content type recommendations
        content_dist = analysis["content_type_distribution"]
        if content_dist:
            most_common = max(content_dist.items(), key=lambda x: x[1])
            recommendations.append(f"Most common content type: {most_common[0]} ({most_common[1]} documents)")
            recommendations.append(f"Consider prioritizing {most_common[0]} documents for migration")
        
        # High-impact recommendations
        high_impact_docs = [path for path, imp in improvements.items() if imp["potential_improvement"] > 25]
        if high_impact_docs:
            recommendations.append(f"{len(high_impact_docs)} documents have high improvement potential (>25%)")
            recommendations.append("Recommend migrating high-impact documents first")
        
        return recommendations
    
    async def migrate_document(self, doc_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate a single document to adaptive chunking.
        
        Args:
            doc_info: Document information
            
        Returns:
            Migration result
        """
        file_path = doc_info["file_path"]
        vector_store_id = doc_info["vector_store_id"]
        old_file_info = doc_info["file_info"]
        
        result = {
            "file_path": file_path,
            "vector_store_id": vector_store_id,
            "success": False,
            "error": None,
            "old_chunks": old_file_info.get("total_chunks", 0),
            "new_chunks": 0,
            "performance_improvement": 0
        }
        
        try:
            if not os.path.exists(file_path):
                result["error"] = "File no longer exists"
                return result
            
            if self.dry_run:
                logger.info(f"[DRY RUN] Would migrate: {file_path}")
                result["success"] = True
                return result
            
            # Backup old file info
            backup_path = doc_info["info_file_path"] + ".backup"
            with open(backup_path, 'w') as f:
                yaml.dump(old_file_info, f)
            
            # Re-embed with adaptive chunking
            logger.info(f"Migrating document: {file_path}")
            new_vector_store_id, new_file_info = self.vector_service.embed_document(
                file_path=file_path,
                use_adaptive_chunking=True
            )
            
            result["new_vector_store_id"] = new_vector_store_id
            result["new_chunks"] = new_file_info.get("total_chunks", 0)
            result["content_type"] = new_file_info.get("content_type", "unknown")
            result["chunking_strategy"] = new_file_info.get("chunking_strategy", "adaptive")
            result["success"] = True
            
            # Calculate performance improvement
            old_chunks = result["old_chunks"]
            new_chunks = result["new_chunks"]
            if old_chunks > 0:
                chunk_improvement = abs(new_chunks - old_chunks) / old_chunks * 100
                result["performance_improvement"] = chunk_improvement
            
            logger.info(f"Successfully migrated {file_path}: {old_chunks} -> {new_chunks} chunks")
            
        except Exception as e:
            logger.error(f"Error migrating {file_path}: {e}")
            result["error"] = str(e)
        
        return result
    
    async def migrate_all_documents(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Migrate all documents to adaptive chunking.
        
        Args:
            documents: List of documents to migrate
            
        Returns:
            Migration summary
        """
        results = []
        
        for i, doc in enumerate(documents, 1):
            logger.info(f"Processing document {i}/{len(documents)}: {doc['file_path']}")
            
            result = await self.migrate_document(doc)
            results.append(result)
            
            # Update stats
            if result["success"]:
                self.migration_stats["migrated_documents"] += 1
                if result["performance_improvement"] > 0:
                    self.migration_stats["performance_improvements"].append(result["performance_improvement"])
            else:
                self.migration_stats["failed_documents"] += 1
        
        self.migration_stats["total_documents"] = len(documents)
        
        return {
            "results": results,
            "stats": self.migration_stats,
            "summary": self._generate_migration_summary(results)
        }
    
    def _generate_migration_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate migration summary."""
        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]
        
        summary = {
            "total_processed": len(results),
            "successful_migrations": len(successful),
            "failed_migrations": len(failed),
            "average_performance_improvement": 0,
            "content_types_migrated": {},
            "errors": [r["error"] for r in failed if r["error"]]
        }
        
        if successful:
            improvements = [r["performance_improvement"] for r in successful if r["performance_improvement"] > 0]
            if improvements:
                summary["average_performance_improvement"] = sum(improvements) / len(improvements)
            
            # Count content types
            for result in successful:
                content_type = result.get("content_type", "unknown")
                summary["content_types_migrated"][content_type] = \
                    summary["content_types_migrated"].get(content_type, 0) + 1
        
        return summary
    
    def save_migration_report(self, analysis: Dict[str, Any], migration_results: Dict[str, Any] = None):
        """Save migration analysis and results to file."""
        report = {
            "timestamp": datetime.now().isoformat(),
            "analysis": analysis,
            "migration_results": migration_results,
            "dry_run": self.dry_run
        }
        
        filename = f"adaptive_chunking_migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            logger.info(f"Migration report saved to: {filename}")
        except Exception as e:
            logger.error(f"Error saving migration report: {e}")

async def main():
    """Main migration execution."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrate documents to adaptive chunking")
    parser.add_argument("--dry-run", action="store_true", help="Analyze only, don't migrate")
    parser.add_argument("--migrate", action="store_true", help="Perform actual migration")
    parser.add_argument("--force", action="store_true", help="Force migration without confirmation")
    
    args = parser.parse_args()
    
    if not args.dry_run and not args.migrate:
        args.dry_run = True  # Default to dry run
    
    migrator = AdaptiveChunkingMigrator(dry_run=args.dry_run)
    
    try:
        # Scan for existing documents
        logger.info("Scanning for documents using traditional chunking...")
        documents = migrator.scan_existing_documents()
        
        if not documents:
            logger.info("No documents found that need migration.")
            return
        
        # Analyze migration benefits
        logger.info("Analyzing migration benefits...")
        analysis = await migrator.analyze_migration_benefits(documents)
        
        # Print analysis results
        print("\n" + "="*60)
        print("ADAPTIVE CHUNKING MIGRATION ANALYSIS")
        print("="*60)
        print(f"Total documents to migrate: {analysis['total_documents']}")
        print(f"Content type distribution: {analysis['content_type_distribution']}")
        print(f"High-impact documents: {len(analysis['estimated_improvements'])}")
        
        print("\nRecommendations:")
        for rec in analysis["recommendations"]:
            print(f"  • {rec}")
        
        # Save analysis report
        migrator.save_migration_report(analysis)
        
        # Perform migration if requested
        migration_results = None
        if args.migrate:
            if not args.force:
                response = input(f"\nProceed with migrating {len(documents)} documents? (y/N): ")
                if response.lower() != 'y':
                    logger.info("Migration cancelled by user.")
                    return
            
            logger.info("Starting migration...")
            migration_results = await migrator.migrate_all_documents(documents)
            
            # Print migration results
            summary = migration_results["summary"]
            print(f"\nMigration completed:")
            print(f"  Successful: {summary['successful_migrations']}")
            print(f"  Failed: {summary['failed_migrations']}")
            print(f"  Average improvement: {summary['average_performance_improvement']:.1f}%")
            
            # Save complete report
            migrator.save_migration_report(analysis, migration_results)
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
