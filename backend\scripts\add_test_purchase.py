"""
Add a test purchase for a user.

This script adds a test purchase for a user by creating a purchase record
and a purchased item record in the database.
"""

import os
import sys
import uuid
import logging
from datetime import datetime

# Add the parent directory to sys.path to allow importing from app
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from sqlalchemy.orm import Session
from app.database import (
    get_db, create_purchase, add_purchased_item, get_user_by_email,
    get_persona, create_persona
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def add_test_purchase(user_email: str, persona_id: str, price: float = 10.0):
    """
    Add a test purchase for a user.

    Args:
        user_email: Email of the user
        persona_id: ID of the persona to purchase
        price: Price of the persona
    """
    logger.info(f"Adding test purchase for persona {persona_id} for user {user_email}")

    # Get database session
    db = next(get_db())

    try:
        # Get the user
        user = get_user_by_email(db, user_email)
        if not user:
            logger.error(f"User with email {user_email} not found")
            return

        # Check if the persona exists
        persona = get_persona(db, persona_id)
        if not persona:
            logger.warning(f"Persona {persona_id} not found in database, creating it")
            
            # Create a basic persona record
            persona_data = {
                "id": persona_id,
                "name": persona_id.replace("-", " ").title(),
                "description": f"AI persona for {persona_id}",
                "industry": "Technology",
                "skills": ["Data Analysis", "Visualization", "Reporting"],
                "rating": 4.5,
                "review_count": 0,
                "image_url": "/placeholder.svg",
                "price": price,
                "provider": "groq",
                "model": "llama3-70b-8192",
                "is_active": True,
                "age_restriction": 0
            }
            
            persona = create_persona(db, persona_data)
            logger.info(f"Created persona {persona_id} in database")

        # Create a purchase
        purchase = create_purchase(
            db,
            user_id=user.id,
            payment_method="credit_card",
            total_amount=price,
            payment_status="completed"  # Set as completed immediately
        )
        logger.info(f"Created purchase {purchase.id} for user {user.id}")
        
        # Add the purchased item
        purchased_item = add_purchased_item(
            db,
            purchase_id=purchase.id,
            persona_id=persona_id,
            quantity=1,
            price=price
        )
        logger.info(f"Added purchased item {purchased_item.id} for persona {persona_id}")
        
        logger.info(f"Successfully added test purchase for persona {persona_id} for user {user_email}")
    except Exception as e:
        logger.error(f"Error adding test purchase: {e}", exc_info=True)


if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python add_test_purchase.py <user_email> <persona_id> [price]")
        sys.exit(1)
        
    user_email = sys.argv[1]
    persona_id = sys.argv[2]
    price = float(sys.argv[3]) if len(sys.argv) > 3 else 10.0
    
    add_test_purchase(user_email, persona_id, price)
