/**
 * Data Source Selector Component
 * 
 * Allows users to select and configure data sources for dashboard widgets.
 * Supports file uploads, database connections, and platform data.
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Database,
  FileText,
  Globe,
  BarChart3,
  RefreshCw,
  Settings,
  Upload,
  Search,
  Filter,
} from 'lucide-react';
import { dashboardDataService, DataSourceInfo, QueryResult } from '@/services/dashboard-data-service';
import { DataSourceConfig } from '@/types/dashboard-customization';

interface DataSourceSelectorProps {
  value?: DataSourceConfig;
  onChange: (config: DataSourceConfig) => void;
  className?: string;
}

const DATA_SOURCE_ICONS = {
  file: FileText,
  database: Database,
  api: Globe,
  platform: BarChart3,
};

export const DataSourceSelector: React.FC<DataSourceSelectorProps> = ({
  value,
  onChange,
  className,
}) => {
  const [dataSources, setDataSources] = useState<DataSourceInfo[]>([]);
  const [selectedDataSource, setSelectedDataSource] = useState<DataSourceInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState<QueryResult | null>(null);
  
  // Form state for data source configuration
  const [config, setConfig] = useState<DataSourceConfig>({
    dataSourceId: value?.dataSourceId || '',
    query: value?.query || '',
    filters: value?.filters || {},
    aggregation: value?.aggregation || '',
    groupBy: value?.groupBy || [],
    sortBy: value?.sortBy || '',
    limit: value?.limit || 100,
  });

  // Load available data sources on mount
  useEffect(() => {
    loadDataSources();
  }, []);

  // Update config when value prop changes
  useEffect(() => {
    if (value) {
      setConfig(value);
      if (value.dataSourceId) {
        const dataSource = dataSources.find(ds => ds.id === value.dataSourceId);
        setSelectedDataSource(dataSource || null);
      }
    }
  }, [value, dataSources]);

  const loadDataSources = async () => {
    setIsLoading(true);
    try {
      const sources = await dashboardDataService.getAvailableDataSources();
      setDataSources(sources);
    } catch (error) {
      console.error('Failed to load data sources:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDataSourceChange = (dataSourceId: string) => {
    const dataSource = dataSources.find(ds => ds.id === dataSourceId);
    setSelectedDataSource(dataSource || null);
    
    const newConfig = {
      ...config,
      dataSourceId,
    };
    setConfig(newConfig);
    onChange(newConfig);
  };

  const handleConfigChange = (updates: Partial<DataSourceConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    onChange(newConfig);
  };

  const handlePreviewData = async () => {
    if (!selectedDataSource) return;
    
    setIsLoading(true);
    try {
      const result = await dashboardDataService.executeQuery(selectedDataSource.id, config);
      setPreviewData(result);
      setShowPreview(true);
    } catch (error) {
      console.error('Failed to preview data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDataSourceType = (type: string) => {
    switch (type) {
      case 'file': return 'Uploaded File';
      case 'database': return 'Database';
      case 'api': return 'API';
      case 'platform': return 'Platform Data';
      default: return type;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Data Source Selection */}
      <div className="space-y-2">
        <Label>Data Source</Label>
        <div className="flex space-x-2">
          <Select
            value={config.dataSourceId}
            onValueChange={handleDataSourceChange}
            disabled={isLoading}
          >
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Select a data source" />
            </SelectTrigger>
            <SelectContent>
              {dataSources.map((dataSource) => {
                const Icon = DATA_SOURCE_ICONS[dataSource.type];
                return (
                  <SelectItem key={dataSource.id} value={dataSource.id}>
                    <div className="flex items-center space-x-2">
                      <Icon className="h-4 w-4" />
                      <span>{dataSource.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {formatDataSourceType(dataSource.type)}
                      </Badge>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={loadDataSources}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        
        {selectedDataSource && (
          <p className="text-sm text-muted-foreground">
            {selectedDataSource.description}
            {selectedDataSource.recordCount && (
              <span className="ml-2">• {selectedDataSource.recordCount.toLocaleString()} records</span>
            )}
          </p>
        )}
      </div>

      {/* Configuration Options */}
      {selectedDataSource && (
        <div className="space-y-4 p-4 border rounded-lg">
          <h4 className="font-semibold flex items-center">
            <Settings className="h-4 w-4 mr-2" />
            Data Configuration
          </h4>

          {/* Query/Filter */}
          <div className="space-y-2">
            <Label>Query/Filter</Label>
            <Textarea
              value={config.query || ''}
              onChange={(e) => handleConfigChange({ query: e.target.value })}
              placeholder="Enter SQL query or filter conditions..."
              rows={3}
            />
          </div>

          {/* Aggregation */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Aggregation</Label>
              <Select
                value={config.aggregation || ''}
                onValueChange={(value) => handleConfigChange({ aggregation: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select aggregation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">None</SelectItem>
                  <SelectItem value="sum">Sum</SelectItem>
                  <SelectItem value="avg">Average</SelectItem>
                  <SelectItem value="count">Count</SelectItem>
                  <SelectItem value="min">Minimum</SelectItem>
                  <SelectItem value="max">Maximum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Sort By</Label>
              <Input
                value={config.sortBy || ''}
                onChange={(e) => handleConfigChange({ sortBy: e.target.value })}
                placeholder="Column name"
              />
            </div>
          </div>

          {/* Group By and Limit */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Group By</Label>
              <Input
                value={config.groupBy?.join(', ') || ''}
                onChange={(e) => handleConfigChange({ 
                  groupBy: e.target.value.split(',').map(s => s.trim()).filter(Boolean) 
                })}
                placeholder="Column names (comma-separated)"
              />
            </div>

            <div className="space-y-2">
              <Label>Limit</Label>
              <Input
                type="number"
                value={config.limit || 100}
                onChange={(e) => handleConfigChange({ limit: parseInt(e.target.value) || 100 })}
                min={1}
                max={10000}
              />
            </div>
          </div>

          {/* Preview Button */}
          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={handlePreviewData}
              disabled={isLoading}
            >
              <Search className="h-4 w-4 mr-2" />
              Preview Data
            </Button>
          </div>
        </div>
      )}

      {/* Data Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Data Preview</DialogTitle>
            <DialogDescription>
              Preview of data from {selectedDataSource?.name}
            </DialogDescription>
          </DialogHeader>
          
          {previewData && (
            <div className="space-y-4">
              {/* Query Stats */}
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <span>{previewData.totalRecords} records</span>
                <span>•</span>
                <span>{previewData.executionTime}ms</span>
                {previewData.error && (
                  <>
                    <span>•</span>
                    <span className="text-destructive">Error: {previewData.error}</span>
                  </>
                )}
              </div>

              {/* Data Table */}
              {previewData.data.length > 0 && (
                <div className="border rounded-lg overflow-auto max-h-96">
                  <table className="w-full text-sm">
                    <thead className="bg-muted">
                      <tr>
                        {previewData.columns.map((column) => (
                          <th key={column} className="p-2 text-left font-medium">
                            {column}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {previewData.data.slice(0, 10).map((row, index) => (
                        <tr key={index} className="border-t">
                          {previewData.columns.map((column) => (
                            <td key={column} className="p-2">
                              {String(row[column] || '')}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  
                  {previewData.data.length > 10 && (
                    <div className="p-2 text-center text-sm text-muted-foreground border-t">
                      Showing first 10 of {previewData.totalRecords} records
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreview(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
