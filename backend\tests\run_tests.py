#!/usr/bin/env python3
"""
Test runner for the Datagenius backend.

This script runs all tests and provides a summary of results.
"""
import sys
import os
import subprocess
import argparse
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))


def run_tests(test_pattern=None, verbose=False, coverage=False):
    """
    Run tests using pytest.
    
    Args:
        test_pattern: Optional pattern to filter tests
        verbose: Whether to run in verbose mode
        coverage: Whether to generate coverage report
    """
    cmd = ["python", "-m", "pytest"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=app", "--cov=agents", "--cov=schemas", "--cov-report=html", "--cov-report=term"])
    
    if test_pattern:
        cmd.append(f"-k {test_pattern}")
    
    # Add the tests directory
    cmd.append("tests/")
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, cwd=backend_dir, check=False)
        return result.returncode == 0
    except FileNotFoundError:
        print("Error: pytest not found. Please install pytest:")
        print("pip install pytest pytest-cov pytest-asyncio")
        return False


def run_specific_test_file(test_file, verbose=False):
    """
    Run a specific test file.
    
    Args:
        test_file: Path to the test file
        verbose: Whether to run in verbose mode
    """
    cmd = ["python", "-m", "pytest"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.append(test_file)
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, cwd=backend_dir, check=False)
        return result.returncode == 0
    except FileNotFoundError:
        print("Error: pytest not found. Please install pytest:")
        print("pip install pytest pytest-cov pytest-asyncio")
        return False


def check_test_dependencies():
    """Check if required test dependencies are installed."""
    required_packages = ["pytest", "pytest-asyncio", "pytest-cov"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("Missing required test dependencies:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nInstall them with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run Datagenius backend tests")
    parser.add_argument(
        "--pattern", "-k",
        help="Run tests matching this pattern"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests in verbose mode"
    )
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Generate coverage report"
    )
    parser.add_argument(
        "--file", "-f",
        help="Run a specific test file"
    )
    parser.add_argument(
        "--workflow",
        action="store_true",
        help="Run only workflow-related tests"
    )
    parser.add_argument(
        "--context",
        action="store_true",
        help="Run only AgentProcessingContext tests"
    )
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="Check if test dependencies are installed"
    )
    
    args = parser.parse_args()
    
    if args.check_deps:
        if check_test_dependencies():
            print("All test dependencies are installed.")
            return 0
        else:
            return 1
    
    # Check dependencies before running tests
    if not check_test_dependencies():
        return 1
    
    success = True
    
    if args.file:
        # Run specific test file
        success = run_specific_test_file(args.file, args.verbose)
    elif args.workflow:
        # Run workflow tests
        success = run_tests("workflow", args.verbose, args.coverage)
    elif args.context:
        # Run context tests
        success = run_tests("context", args.verbose, args.coverage)
    else:
        # Run all tests
        success = run_tests(args.pattern, args.verbose, args.coverage)
    
    if success:
        print("\n" + "=" * 50)
        print("All tests passed! ✅")
        print("=" * 50)
        return 0
    else:
        print("\n" + "=" * 50)
        print("Some tests failed! ❌")
        print("=" * 50)
        return 1


if __name__ == "__main__":
    sys.exit(main())
