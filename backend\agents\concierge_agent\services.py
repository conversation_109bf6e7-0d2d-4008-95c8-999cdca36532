"""
Service layer for the Concierge Agent.

This module provides high-level services for concierge operations including
persona management, conversation state tracking, and routing coordination.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from contextlib import asynccontextmanager

from .models import (
    ConversationState, PersonaRecommendation, PersonaRoutingRequest,
    PersonaRoutingResponse, ConciergeResponse, ValidationResult,
    ConversationStage, PersonaAvailability
)
from .exceptions import (
    ConciergeAgentException, PersonaNotAvailableError, ValidationError,
    ConversationContextError, PersonaRoutingError, ConciergeExceptionHandler
)

logger = logging.getLogger(__name__)


class ConversationStateManager:
    """Manages conversation state and context for the concierge agent."""
    
    def __init__(self, max_states: int = 1000, state_ttl: int = 3600):
        self.states: Dict[str, ConversationState] = {}
        self.max_states = max_states
        self.state_ttl = state_ttl
        self.last_cleanup = datetime.now(timezone.utc)
        
    async def get_conversation_state(
        self,
        conversation_id: str,
        user_id: str
    ) -> ConversationState:
        """Get or create conversation state."""
        try:
            # Check if state exists
            if conversation_id in self.states:
                state = self.states[conversation_id]
                # Update last accessed time
                state.last_updated = datetime.now(timezone.utc)
                return state
            
            # Create new state
            state = ConversationState(
                conversation_id=conversation_id,
                user_id=user_id,
                stage=ConversationStage.INITIAL,
                last_updated=datetime.now(timezone.utc)
            )
            
            self.states[conversation_id] = state
            
            # Cleanup old states if needed
            await self._cleanup_if_needed()
            
            return state
            
        except Exception as e:
            logger.error(f"Error getting conversation state: {e}")
            raise ConversationContextError(
                conversation_id=conversation_id,
                operation="get_state",
                details=str(e)
            )
    
    async def update_conversation_state(
        self,
        conversation_id: str,
        updates: Dict[str, Any]
    ) -> ConversationState:
        """Update conversation state with new data."""
        try:
            if conversation_id not in self.states:
                raise ConversationContextError(
                    conversation_id=conversation_id,
                    operation="update_state",
                    details="Conversation state not found"
                )
            
            state = self.states[conversation_id]
            
            # Update fields
            for key, value in updates.items():
                if hasattr(state, key):
                    setattr(state, key, value)
                else:
                    # Add to metadata if field doesn't exist
                    state.metadata[key] = value
            
            state.last_updated = datetime.now(timezone.utc)
            
            return state
            
        except Exception as e:
            logger.error(f"Error updating conversation state: {e}")
            raise ConversationContextError(
                conversation_id=conversation_id,
                operation="update_state",
                details=str(e)
            )
    
    async def _cleanup_if_needed(self):
        """Cleanup old conversation states if needed."""
        now = datetime.now(timezone.utc)
        
        # Only cleanup every 5 minutes
        if (now - self.last_cleanup).seconds < 300:
            return
        
        # Remove states older than TTL
        expired_states = []
        for conv_id, state in self.states.items():
            age_seconds = (now - state.last_updated).total_seconds()
            if age_seconds > self.state_ttl:
                expired_states.append(conv_id)
        
        for conv_id in expired_states:
            del self.states[conv_id]
        
        # If still too many states, remove oldest
        if len(self.states) > self.max_states:
            sorted_states = sorted(
                self.states.items(),
                key=lambda x: x[1].last_updated
            )
            
            to_remove = len(self.states) - self.max_states
            for i in range(to_remove):
                conv_id = sorted_states[i][0]
                del self.states[conv_id]
        
        self.last_cleanup = now
        logger.info(f"Cleaned up {len(expired_states)} expired conversation states")


class PersonaRecommendationService:
    """Service for generating and managing persona recommendations."""
    
    def __init__(self, agent_registry=None):
        self.agent_registry = agent_registry
        self.recommendation_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def get_persona_recommendations(
        self,
        user_message: str,
        user_context: Dict[str, Any],
        max_recommendations: int = 3
    ) -> List[PersonaRecommendation]:
        """Generate persona recommendations based on user message and context."""
        try:
            # Create cache key
            cache_key = f"{hash(user_message)}_{hash(str(user_context))}"
            
            # Check cache
            if cache_key in self.recommendation_cache:
                cached_result, timestamp = self.recommendation_cache[cache_key]
                if (datetime.now(timezone.utc) - timestamp).seconds < self.cache_ttl:
                    return cached_result
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(
                user_message, user_context, max_recommendations
            )
            
            # Cache result
            self.recommendation_cache[cache_key] = (
                recommendations,
                datetime.now(timezone.utc)
            )
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating persona recommendations: {e}")
            return []
    
    async def _generate_recommendations(
        self,
        user_message: str,
        user_context: Dict[str, Any],
        max_recommendations: int
    ) -> List[PersonaRecommendation]:
        """Generate persona recommendations using rule-based logic."""
        recommendations = []
        
        # Get available personas
        available_personas = await self._get_available_personas(user_context)
        
        # Analyze message for keywords and intent
        message_lower = user_message.lower()
        
        # Marketing-related keywords
        if any(keyword in message_lower for keyword in [
            'marketing', 'campaign', 'promotion', 'brand', 'advertising',
            'social media', 'content', 'seo', 'email marketing'
        ]):
            if 'composable-marketer' in available_personas:
                recommendations.append(PersonaRecommendation(
                    persona_id='composable-marketer',
                    confidence=0.9,
                    reasoning='Your request involves marketing activities',
                    availability=PersonaAvailability.AVAILABLE,
                    capabilities=['Marketing Strategy', 'Campaign Planning', 'Content Creation'],
                    priority=1
                ))
        
        # Analysis-related keywords
        if any(keyword in message_lower for keyword in [
            'analyze', 'analysis', 'data', 'chart', 'graph', 'statistics',
            'report', 'insights', 'trends', 'visualization'
        ]):
            if 'composable-analyst' in available_personas:
                recommendations.append(PersonaRecommendation(
                    persona_id='composable-analyst',
                    confidence=0.85,
                    reasoning='Your request involves data analysis',
                    availability=PersonaAvailability.AVAILABLE,
                    capabilities=['Data Analysis', 'Visualization', 'Statistical Insights'],
                    priority=1
                ))
        
        # Classification-related keywords
        if any(keyword in message_lower for keyword in [
            'classify', 'categorize', 'label', 'tag', 'organize',
            'sort', 'group', 'classification'
        ]):
            if 'composable-classifier' in available_personas:
                recommendations.append(PersonaRecommendation(
                    persona_id='composable-classifier',
                    confidence=0.8,
                    reasoning='Your request involves classification tasks',
                    availability=PersonaAvailability.AVAILABLE,
                    capabilities=['Data Classification', 'Content Categorization'],
                    priority=2
                ))
        
        # If no specific matches, recommend general assistant
        if not recommendations and 'data-assistant' in available_personas:
            recommendations.append(PersonaRecommendation(
                persona_id='data-assistant',
                confidence=0.6,
                reasoning='General data assistance for your request',
                availability=PersonaAvailability.AVAILABLE,
                capabilities=['General Assistance', 'Data Help'],
                priority=3
            ))
        
        # Sort by priority and confidence
        recommendations.sort(key=lambda x: (x.priority, -x.confidence))
        
        return recommendations[:max_recommendations]
    
    async def _get_available_personas(self, user_context: Dict[str, Any]) -> List[str]:
        """Get list of available personas for the user."""
        # This would typically check user purchases, API keys, etc.
        # For now, return a default set
        return [
            'concierge-agent',
            'composable-analyst',
            'composable-marketer',
            'composable-classifier',
            'data-assistant'
        ]


class PersonaRoutingService:
    """Service for handling persona routing and handoffs."""
    
    def __init__(self, agent_registry=None):
        self.agent_registry = agent_registry
        self.active_routes = {}
        
    async def route_to_persona(
        self,
        routing_request: PersonaRoutingRequest
    ) -> PersonaRoutingResponse:
        """Route user to a specific persona."""
        try:
            # Validate target persona availability
            is_available = await self._check_persona_availability(
                routing_request.target_persona,
                routing_request.context
            )
            
            if not is_available:
                # Find alternatives
                alternatives = await self._find_alternative_personas(
                    routing_request.target_persona,
                    routing_request.context
                )
                
                return PersonaRoutingResponse(
                    success=False,
                    target_persona=routing_request.target_persona,
                    error_message=f"Persona {routing_request.target_persona} is not available",
                    alternative_personas=alternatives
                )
            
            # Create handoff message
            handoff_message = await self._create_handoff_message(routing_request)
            
            # Track the routing
            route_id = f"{routing_request.source_persona}_{routing_request.target_persona}_{datetime.now(timezone.utc).timestamp()}"
            self.active_routes[route_id] = {
                "request": routing_request,
                "timestamp": datetime.now(timezone.utc),
                "status": "active"
            }
            
            return PersonaRoutingResponse(
                success=True,
                target_persona=routing_request.target_persona,
                handoff_message=handoff_message,
                routing_metadata={"route_id": route_id}
            )
            
        except Exception as e:
            logger.error(f"Error routing to persona: {e}")
            raise PersonaRoutingError(
                source_persona=routing_request.source_persona,
                target_persona=routing_request.target_persona,
                routing_reason=str(e)
            )
    
    async def _check_persona_availability(
        self,
        persona_id: str,
        context: Dict[str, Any]
    ) -> bool:
        """Check if a persona is available for the user."""
        # This would check user purchases, API keys, system status, etc.
        # For now, assume all personas are available
        available_personas = [
            'concierge-agent',
            'composable-analyst',
            'composable-marketer',
            'composable-classifier',
            'data-assistant'
        ]
        return persona_id in available_personas
    
    async def _find_alternative_personas(
        self,
        target_persona: str,
        context: Dict[str, Any]
    ) -> List[str]:
        """Find alternative personas when target is unavailable."""
        # Mapping of personas to alternatives
        alternatives_map = {
            'composable-analyst': ['data-assistant', 'concierge-agent'],
            'composable-marketer': ['concierge-agent'],
            'composable-classifier': ['composable-analyst', 'concierge-agent'],
            'data-assistant': ['composable-analyst', 'concierge-agent']
        }
        
        return alternatives_map.get(target_persona, ['concierge-agent'])
    
    async def _create_handoff_message(
        self,
        routing_request: PersonaRoutingRequest
    ) -> str:
        """Create a handoff message for the target persona."""
        return f"Hello! The Concierge has connected you with me to help with: {routing_request.user_message}"


class ConciergeService:
    """Main service class that coordinates all concierge operations."""
    
    def __init__(self, agent_registry=None):
        self.state_manager = ConversationStateManager()
        self.recommendation_service = PersonaRecommendationService(agent_registry)
        self.routing_service = PersonaRoutingService(agent_registry)
        self.exception_handler = ConciergeExceptionHandler()
        
    @asynccontextmanager
    async def conversation_context(self, conversation_id: str, user_id: str):
        """Context manager for conversation operations."""
        try:
            state = await self.state_manager.get_conversation_state(conversation_id, user_id)
            yield state
        except Exception as e:
            logger.error(f"Error in conversation context: {e}")
            raise
        finally:
            # Any cleanup operations
            pass
    
    async def process_user_message(
        self,
        message: str,
        conversation_id: str,
        user_id: str,
        context: Dict[str, Any]
    ) -> ConciergeResponse:
        """Process a user message and return appropriate response."""
        try:
            async with self.conversation_context(conversation_id, user_id) as state:
                # Get persona recommendations
                recommendations = await self.recommendation_service.get_persona_recommendations(
                    message, context
                )
                
                # Update conversation state
                await self.state_manager.update_conversation_state(
                    conversation_id,
                    {
                        "recommended_personas": recommendations,
                        "stage": ConversationStage.PERSONA_SELECTION
                    }
                )
                
                # Generate response
                if recommendations:
                    response_message = self._create_recommendation_message(recommendations)
                    suggested_actions = [f"Select {rec.persona_id}" for rec in recommendations[:3]]
                else:
                    response_message = "I can help you with various tasks. What would you like to work on?"
                    suggested_actions = ["Tell me more about your task", "Browse available personas"]
                
                return ConciergeResponse(
                    message=response_message,
                    success=True,
                    recommendations=recommendations,
                    suggested_actions=suggested_actions,
                    conversation_stage=state.stage
                )
                
        except Exception as e:
            logger.error(f"Error processing user message: {e}")
            return ConciergeResponse(
                message="I encountered an issue processing your request. Please try again.",
                success=False,
                metadata={"error": str(e)}
            )
    
    def _create_recommendation_message(self, recommendations: List[PersonaRecommendation]) -> str:
        """Create a user-friendly message with persona recommendations."""
        if not recommendations:
            return "I can help you with various tasks. What would you like to work on?"
        
        message = "Based on your request, I recommend these AI specialists:\n\n"
        
        for i, rec in enumerate(recommendations[:3], 1):
            message += f"{i}. **{rec.persona_id.replace('-', ' ').title()}** - {rec.reasoning}\n"
        
        message += "\nWhich one would you like to work with?"
        return message
