/**
 * Performance optimization utilities for the dashboard application.
 * Provides memoization, debouncing, throttling, and other performance enhancements.
 *
 * Phase 1 Enhancements:
 * - Browser cache management (L1)
 * - Memory usage monitoring
 * - Bundle optimization helpers
 * - Performance metrics collection
 */

import { useCallback, useMemo, useRef, useEffect } from 'react';

// Debounce function
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// Throttle function
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Memoization with TTL
export class MemoCache<T = any> {
  private cache = new Map<string, { value: T; timestamp: number; ttl: number }>();
  private defaultTTL: number;

  constructor(defaultTTL: number = 5 * 60 * 1000) { // 5 minutes default
    this.defaultTTL = defaultTTL;
  }

  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return undefined;
    }

    return entry.value;
  }

  set(key: string, value: T, ttl?: number): void {
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
    });
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Global memo cache instance
export const globalMemoCache = new MemoCache();

// Memoized function creator
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  keyGenerator?: (...args: Parameters<T>) => string,
  ttl?: number
): T {
  const cache = new MemoCache(ttl);
  
  return ((...args: Parameters<T>) => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    
    let result = cache.get(key);
    if (result === undefined) {
      result = func(...args);
      cache.set(key, result);
    }
    
    return result;
  }) as T;
}

// React hooks for performance optimization

// Debounced value hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Throttled callback hook
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const throttledCallback = useRef<T>();
  
  if (!throttledCallback.current) {
    throttledCallback.current = throttle(callback, delay) as T;
  }
  
  return throttledCallback.current;
}

// Debounced callback hook
export function useDebounceCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T {
  return useCallback(
    debounce(callback, delay),
    [delay, ...deps]
  ) as T;
}

// Memoized async function hook
export function useMemoAsync<T>(
  asyncFunction: () => Promise<T>,
  deps: React.DependencyList,
  ttl?: number
): { data: T | null; loading: boolean; error: Error | null; refetch: () => void } {
  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);
  const cache = useRef(new MemoCache(ttl));
  
  const memoizedFunction = useMemo(() => {
    const key = JSON.stringify(deps);
    return async () => {
      const cached = cache.current.get(key);
      if (cached !== undefined) {
        return cached;
      }
      
      const result = await asyncFunction();
      cache.current.set(key, result);
      return result;
    };
  }, deps);
  
  const execute = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await memoizedFunction();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [memoizedFunction]);
  
  useEffect(() => {
    execute();
  }, [execute]);
  
  const refetch = useCallback(() => {
    const key = JSON.stringify(deps);
    cache.current.delete(key);
    execute();
  }, [execute, deps]);
  
  return { data, loading, error, refetch };
}

// Virtual scrolling utilities
export interface VirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}

export function useVirtualScroll<T>(
  items: T[],
  options: VirtualScrollOptions
) {
  const { itemHeight, containerHeight, overscan = 5 } = options;
  const [scrollTop, setScrollTop] = React.useState(0);
  
  const visibleCount = Math.ceil(containerHeight / itemHeight);
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(items.length, startIndex + visibleCount + overscan * 2);
  
  const visibleItems = items.slice(startIndex, endIndex);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    startIndex,
    endIndex,
    setScrollTop,
  };
}

// Image lazy loading utilities
export function useLazyImage(src: string, placeholder?: string) {
  const [imageSrc, setImageSrc] = React.useState(placeholder || '');
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [isError, setIsError] = React.useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          const img = new Image();
          img.onload = () => {
            setImageSrc(src);
            setIsLoaded(true);
            observer.disconnect();
          };
          img.onerror = () => {
            setIsError(true);
            observer.disconnect();
          };
          img.src = src;
        }
      },
      { threshold: 0.1 }
    );
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => observer.disconnect();
  }, [src]);
  
  return { imageSrc, isLoaded, isError, imgRef };
}

// Bundle splitting utilities
export function loadComponent<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>
): React.LazyExoticComponent<T> {
  return React.lazy(importFunc);
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  
  startTiming(label: string): () => number {
    const start = performance.now();
    
    return () => {
      const duration = performance.now() - start;
      this.recordMetric(label, duration);
      return duration;
    };
  }
  
  recordMetric(label: string, value: number): void {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }
    
    const values = this.metrics.get(label)!;
    values.push(value);
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }
  
  getMetrics(label: string): {
    count: number;
    average: number;
    min: number;
    max: number;
    latest: number;
  } | null {
    const values = this.metrics.get(label);
    if (!values || values.length === 0) return null;
    
    return {
      count: values.length,
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      latest: values[values.length - 1],
    };
  }
  
  getAllMetrics(): Record<string, ReturnType<PerformanceMonitor['getMetrics']>> {
    const result: Record<string, ReturnType<PerformanceMonitor['getMetrics']>> = {};
    
    for (const [label] of this.metrics) {
      result[label] = this.getMetrics(label);
    }
    
    return result;
  }
  
  clearMetrics(label?: string): void {
    if (label) {
      this.metrics.delete(label);
    } else {
      this.metrics.clear();
    }
  }
}

// Global performance monitor
export const performanceMonitor = new PerformanceMonitor();

// React hook for performance monitoring
export function usePerformanceMonitor(label: string) {
  const endTiming = useRef<(() => number) | null>(null);
  
  const startTiming = useCallback(() => {
    endTiming.current = performanceMonitor.startTiming(label);
  }, [label]);
  
  const stopTiming = useCallback(() => {
    if (endTiming.current) {
      const duration = endTiming.current();
      endTiming.current = null;
      return duration;
    }
    return 0;
  }, []);
  
  const getMetrics = useCallback(() => {
    return performanceMonitor.getMetrics(label);
  }, [label]);
  
  return { startTiming, stopTiming, getMetrics };
}

// Memory management utilities
export function cleanupMemory() {
  // Clear global caches
  globalMemoCache.cleanup();
  
  // Force garbage collection if available (development only)
  if (process.env.NODE_ENV === 'development' && 'gc' in window) {
    (window as any).gc();
  }
}

// Automatic cleanup on page visibility change
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    cleanupMemory();
  }
});

// Phase 1 Performance Optimization Classes

interface FrontendMetrics {
  renderTime: number;
  dataLoadTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  bundleSize: number;
}

interface CacheConfig {
  maxAge: number;
  maxEntries: number;
  strategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
}

class FrontendPerformanceOptimizer {
  private metrics: FrontendMetrics;
  private cacheConfig: CacheConfig;
  private performanceObserver: PerformanceObserver | null = null;
  private memoryMonitorInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.metrics = {
      renderTime: 0,
      dataLoadTime: 0,
      memoryUsage: 0,
      cacheHitRate: 0,
      bundleSize: 0
    };

    this.cacheConfig = {
      maxAge: 1800000, // 30 minutes
      maxEntries: 1000,
      strategy: 'stale-while-revalidate'
    };

    this.initializePerformanceMonitoring();
    this.initializeBrowserCache();
  }

  private initializePerformanceMonitoring(): void {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry);
        }
      });

      try {
        this.performanceObserver.observe({
          entryTypes: ['navigation', 'resource', 'measure', 'paint']
        });
      } catch (error) {
        console.warn('Performance Observer not fully supported:', error);
      }
    }

    this.startMemoryMonitoring();
  }

  private processPerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case 'navigation':
        const navEntry = entry as PerformanceNavigationTiming;
        this.metrics.renderTime = navEntry.loadEventEnd - navEntry.navigationStart;
        break;

      case 'resource':
        const resourceEntry = entry as PerformanceResourceTiming;
        if (resourceEntry.name.includes('/api/')) {
          this.metrics.dataLoadTime = resourceEntry.responseEnd - resourceEntry.requestStart;
        }
        break;

      case 'paint':
        if (entry.name === 'first-contentful-paint') {
          console.log(`First Contentful Paint: ${entry.startTime}ms`);
        }
        break;
    }
  }

  private startMemoryMonitoring(): void {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      this.memoryMonitorInterval = setInterval(() => {
        const memInfo = (performance as any).memory;
        this.metrics.memoryUsage = memInfo.usedJSHeapSize;

        // Alert if memory usage exceeds 80MB threshold
        if (this.metrics.memoryUsage > 80 * 1024 * 1024) {
          console.warn(`High memory usage detected: ${(this.metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB`);
          this.triggerGarbageCollection();
        }
      }, 30000); // Check every 30 seconds
    }
  }

  private initializeBrowserCache(): void {
    if (typeof window !== 'undefined') {
      this.setupCacheInterceptor();
      this.preloadCriticalResources();
    }
  }

  private setupCacheInterceptor(): void {
    // Enhanced fetch with caching
    const originalFetch = window.fetch;

    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const url = typeof input === 'string' ? input : input.toString();

      if (this.isCacheableRequest(url)) {
        const cacheKey = this.generateCacheKey(url, init);
        const cachedResponse = this.getCachedResponse(cacheKey);

        if (cachedResponse && this.cacheConfig.strategy === 'cache-first') {
          this.updateCacheHitRate(true);
          return cachedResponse;
        }
      }

      const response = await originalFetch(input, init);

      if (response.ok && this.isCacheableRequest(url)) {
        const cacheKey = this.generateCacheKey(url, init);
        this.cacheResponse(cacheKey, response.clone());
      }

      this.updateCacheHitRate(false);
      return response;
    };
  }

  private isCacheableRequest(url: string): boolean {
    const cacheablePatterns = [
      '/api/dashboards',
      '/api/performance',
      '/api/data-sources',
      '/api/widgets'
    ];

    return cacheablePatterns.some(pattern => url.includes(pattern)) &&
           !url.includes('real-time') &&
           !url.includes('websocket');
  }

  private generateCacheKey(url: string, init?: RequestInit): string {
    const method = init?.method || 'GET';
    const body = init?.body ? JSON.stringify(init.body) : '';
    return `${method}:${url}:${body}`;
  }

  private getCachedResponse(cacheKey: string): Response | null {
    try {
      const cached = localStorage.getItem(`cache:${cacheKey}`);
      if (cached) {
        const { data, timestamp } = JSON.parse(cached);

        if (Date.now() - timestamp < this.cacheConfig.maxAge) {
          return new Response(JSON.stringify(data), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        } else {
          localStorage.removeItem(`cache:${cacheKey}`);
        }
      }
    } catch (error) {
      console.warn('Error reading from cache:', error);
    }

    return null;
  }

  private async cacheResponse(cacheKey: string, response: Response): Promise<void> {
    try {
      const data = await response.json();
      const cacheEntry = { data, timestamp: Date.now() };

      if (this.getCacheSize() >= this.cacheConfig.maxEntries) {
        this.evictOldestCacheEntries();
      }

      localStorage.setItem(`cache:${cacheKey}`, JSON.stringify(cacheEntry));
    } catch (error) {
      console.warn('Error caching response:', error);
    }
  }

  private getCacheSize(): number {
    let count = 0;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('cache:')) {
        count++;
      }
    }
    return count;
  }

  private evictOldestCacheEntries(): void {
    const cacheEntries: Array<{ key: string; timestamp: number }> = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('cache:')) {
        try {
          const cached = localStorage.getItem(key);
          if (cached) {
            const { timestamp } = JSON.parse(cached);
            cacheEntries.push({ key, timestamp });
          }
        } catch (error) {
          localStorage.removeItem(key);
        }
      }
    }

    cacheEntries.sort((a, b) => a.timestamp - b.timestamp);
    const toRemove = Math.ceil(cacheEntries.length * 0.1);

    for (let i = 0; i < toRemove; i++) {
      localStorage.removeItem(cacheEntries[i].key);
    }
  }

  private updateCacheHitRate(isHit: boolean): void {
    const currentRate = this.metrics.cacheHitRate;
    this.metrics.cacheHitRate = isHit ?
      (currentRate * 0.9 + 1 * 0.1) :
      (currentRate * 0.9 + 0 * 0.1);
  }

  private preloadCriticalResources(): void {
    const criticalResources = [
      '/api/dashboards',
      '/api/performance/status'
    ];

    criticalResources.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = url;
      link.as = 'fetch';
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });
  }

  private triggerGarbageCollection(): void {
    if (process.env.NODE_ENV === 'development' && 'gc' in window) {
      (window as any).gc();
    }

    this.clearExpiredCache();
  }

  private clearExpiredCache(): void {
    const now = Date.now();
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('cache:')) {
        try {
          const cached = localStorage.getItem(key);
          if (cached) {
            const { timestamp } = JSON.parse(cached);
            if (now - timestamp > this.cacheConfig.maxAge) {
              keysToRemove.push(key);
            }
          }
        } catch (error) {
          keysToRemove.push(key);
        }
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
  }

  public getMetrics(): FrontendMetrics {
    return { ...this.metrics };
  }

  public clearCache(): void {
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('cache:')) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log(`Cleared ${keysToRemove.length} cache entries`);
  }

  public cleanup(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }

    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval);
    }
  }
}

// Global performance optimizer instance
export const frontendOptimizer = typeof window !== 'undefined' ? new FrontendPerformanceOptimizer() : null;

// Phase 1 utility functions
export const clearBrowserCache = () => frontendOptimizer?.clearCache();
export const getFrontendMetrics = () => frontendOptimizer?.getMetrics();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    frontendOptimizer?.cleanup();
  });
}

// Export all utilities
export {
  debounce,
  throttle,
  memoize,
  MemoCache,
  useDebounce,
  useThrottle,
  useDebounceCallback,
  useMemoAsync,
  useVirtualScroll,
  useLazyImage,
  loadComponent,
  PerformanceMonitor,
  usePerformanceMonitor,
  cleanupMemory,
  FrontendPerformanceOptimizer,
};
