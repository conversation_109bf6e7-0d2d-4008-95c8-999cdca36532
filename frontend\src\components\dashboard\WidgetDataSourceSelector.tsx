import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Database, 
  FileText, 
  Globe, 
  Server,
  Settings,
  AlertCircle,
  Eye
} from 'lucide-react';
import { DashboardDataSourceAssignment, WidgetDataSourceConfig } from '@/types/dashboard-customization';

interface WidgetDataSourceSelectorProps {
  availableDataSources: DashboardDataSourceAssignment[];
  value?: WidgetDataSourceConfig;
  onChange: (config: WidgetDataSourceConfig) => void;
  className?: string;
}

const DATA_SOURCE_ICONS = {
  file: FileText,
  database: Database,
  api: Globe,
  platform: Server,
};

export const WidgetDataSourceSelector: React.FC<WidgetDataSourceSelectorProps> = ({
  availableDataSources,
  value,
  onChange,
  className = '',
}) => {
  const [selectedDataSource, setSelectedDataSource] = useState<DashboardDataSourceAssignment | null>(
    value ? availableDataSources.find(ds => ds.id === value.dashboard_data_source_assignment_id) || null : null
  );

  const [config, setConfig] = useState<WidgetDataSourceConfig>({
    dashboard_data_source_assignment_id: value?.dashboard_data_source_assignment_id || '',
    query: value?.query || '',
    filters: value?.filters || {},
    aggregation: value?.aggregation || '',
    groupBy: value?.groupBy || [],
    sortBy: value?.sortBy || '',
    limit: value?.limit || 100,
  });

  const handleDataSourceChange = (dataSourceId: string) => {
    const dataSource = availableDataSources.find(ds => ds.id === dataSourceId);
    setSelectedDataSource(dataSource || null);

    const newConfig = {
      ...config,
      dashboard_data_source_assignment_id: dataSourceId,
    };
    setConfig(newConfig);
    onChange(newConfig);
  };

  const handleConfigChange = (updates: Partial<WidgetDataSourceConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    onChange(newConfig);
  };

  const formatDataSourceType = (assignment: DashboardDataSourceAssignment) => {
    // Use system data source type if available, otherwise infer from assignment
    const systemDataSource = assignment.system_data_source;
    if (systemDataSource) {
      if (systemDataSource.file_path) return 'Uploaded File';
      if (systemDataSource.source_type === 'database') return 'Database';
      if (systemDataSource.source_type === 'api') return 'API';
      return 'Platform Data';
    }
    return 'Data Source';
  };

  const getDataSourceIcon = (assignment: DashboardDataSourceAssignment) => {
    const systemDataSource = assignment.system_data_source;
    if (systemDataSource) {
      if (systemDataSource.file_path) return FileText;
      if (systemDataSource.source_type === 'database') return Database;
      if (systemDataSource.source_type === 'api') return Globe;
    }
    return Server;
  };

  // Filter only active data source assignments
  const activeDataSources = availableDataSources.filter(ds => ds.is_active);

  if (activeDataSources.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
          <h4 className="text-lg font-semibold mb-2">No Data Sources Available</h4>
          <p className="text-sm text-muted-foreground text-center">
            This dashboard doesn't have any configured data sources. 
            Please add data sources in the dashboard settings first.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Data Source Selection */}
      <div className="space-y-2">
        <Label>Data Source</Label>
        <Select
          value={config.dashboard_data_source_assignment_id}
          onValueChange={handleDataSourceChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a data source from this dashboard" />
          </SelectTrigger>
          <SelectContent>
            {activeDataSources.map((assignment) => {
              const Icon = getDataSourceIcon(assignment);
              const displayName = assignment.alias || assignment.system_data_source?.name || 'Unknown Data Source';
              return (
                <SelectItem key={assignment.id} value={assignment.id}>
                  <div className="flex items-center space-x-2">
                    <Icon className="h-4 w-4" />
                    <span>{displayName}</span>
                    <Badge variant="outline" className="text-xs">
                      {formatDataSourceType(assignment)}
                    </Badge>
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
        
        {selectedDataSource && (
          <div className="text-xs text-muted-foreground">
            {selectedDataSource.system_data_source?.description && (
              <p>{selectedDataSource.system_data_source.description}</p>
            )}
            {selectedDataSource.alias && selectedDataSource.alias !== selectedDataSource.system_data_source?.name && (
              <p>Alias: {selectedDataSource.alias}</p>
            )}
          </div>
        )}
      </div>

      {/* Configuration Options */}
      {selectedDataSource && (
        <div className="space-y-4 p-4 border rounded-lg">
          <h4 className="font-semibold flex items-center">
            <Settings className="h-4 w-4 mr-2" />
            Data Configuration
          </h4>

          {/* Query/Filter */}
          <div className="space-y-2">
            <Label>Query/Filter</Label>
            <Textarea
              value={config.query || ''}
              onChange={(e) => handleConfigChange({ query: e.target.value })}
              placeholder="Enter SQL query or filter conditions..."
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              Optional: Add custom queries or filters to refine the data
            </p>
          </div>

          {/* Aggregation */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Aggregation</Label>
              <Select
                value={config.aggregation || ''}
                onValueChange={(value) => handleConfigChange({ aggregation: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select aggregation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">None</SelectItem>
                  <SelectItem value="sum">Sum</SelectItem>
                  <SelectItem value="avg">Average</SelectItem>
                  <SelectItem value="count">Count</SelectItem>
                  <SelectItem value="min">Minimum</SelectItem>
                  <SelectItem value="max">Maximum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Sort By</Label>
              <Input
                value={config.sortBy || ''}
                onChange={(e) => handleConfigChange({ sortBy: e.target.value })}
                placeholder="Column name"
              />
            </div>
          </div>

          {/* Group By */}
          <div className="space-y-2">
            <Label>Group By</Label>
            <Input
              value={config.groupBy?.join(', ') || ''}
              onChange={(e) => handleConfigChange({ 
                groupBy: e.target.value.split(',').map(s => s.trim()).filter(Boolean) 
              })}
              placeholder="Column names (comma-separated)"
            />
          </div>

          {/* Limit */}
          <div className="space-y-2">
            <Label>Record Limit</Label>
            <Input
              type="number"
              min="1"
              max="10000"
              value={config.limit || 100}
              onChange={(e) => handleConfigChange({ limit: parseInt(e.target.value) || 100 })}
            />
            <p className="text-xs text-muted-foreground">
              Maximum number of records to display (1-10,000)
            </p>
          </div>

          {/* Data Source Info */}
          <div className="bg-muted/50 p-3 rounded-lg">
            <div className="text-sm">
              <div className="flex items-center space-x-2 mb-2">
                <Database className="h-4 w-4" />
                <span className="font-medium">Data Source Details</span>
              </div>
              <div className="space-y-1 text-muted-foreground">
                <p>Type: {formatDataSourceType(selectedDataSource.type)}</p>
                {selectedDataSource.lastUpdated && (
                  <p>Last Updated: {new Date(selectedDataSource.lastUpdated).toLocaleString()}</p>
                )}
                {selectedDataSource.refreshInterval && (
                  <p>Refresh Interval: {selectedDataSource.refreshInterval}s</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
