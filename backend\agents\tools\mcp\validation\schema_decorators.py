"""
Schema decorators for automatic MCP tool validation.

This module provides decorators and utilities for automatically registering
validation schemas with MCP tools, making the system extensible for new tools.
"""

import logging
from typing import Dict, Any, Type, Optional, Callable
from functools import wraps
from pydantic import BaseModel

logger = logging.getLogger(__name__)


def auto_register_schema(schema_class: Optional[Type[BaseModel]] = None):
    """
    Decorator to automatically register a validation schema for an MCP tool.
    
    Usage:
        @auto_register_schema(MyCustomSchema)
        class MyTool(BaseMCPTool):
            pass
            
        # Or let it infer the schema
        @auto_register_schema()
        class MyTool(BaseMCPTool):
            pass
    
    Args:
        schema_class: Optional Pydantic schema class. If None, will infer from tool.
    """
    def decorator(tool_class):
        try:
            from .input_validator import get_input_validator
            
            validator = get_input_validator()
            
            # Get tool name
            if hasattr(tool_class, 'name'):
                tool_name = tool_class.name
            else:
                # Try to instantiate to get name
                try:
                    instance = tool_class()
                    tool_name = getattr(instance, 'name', tool_class.__name__.lower())
                except:
                    tool_name = tool_class.__name__.lower()
            
            # Register schema
            if schema_class:
                validator.add_custom_schema(tool_name, schema_class)
                logger.info(f"Auto-registered custom schema for {tool_name}")
            else:
                # Let the validator infer the schema
                success = validator.register_tool_schema(tool_name, tool_class)
                if success:
                    logger.info(f"Auto-registered inferred schema for {tool_name}")
                else:
                    logger.warning(f"Failed to auto-register schema for {tool_name}")
            
        except Exception as e:
            logger.warning(f"Failed to auto-register schema for {tool_class.__name__}: {e}")
        
        return tool_class
    
    return decorator


def with_validation_schema(schema_class: Type[BaseModel]):
    """
    Class decorator to associate a validation schema with a tool.
    
    Usage:
        @with_validation_schema(MySchema)
        class MyTool(BaseMCPTool):
            pass
    
    Args:
        schema_class: Pydantic schema class for validation
    """
    def decorator(tool_class):
        # Store schema reference on the class
        tool_class._validation_schema = schema_class
        
        # Auto-register with validator
        return auto_register_schema(schema_class)(tool_class)
    
    return decorator


def schema_field(field_type: Type, description: str = "", **kwargs):
    """
    Helper function to create Pydantic Field definitions for dynamic schemas.
    
    Usage:
        dynamic_fields = {
            'my_field': schema_field(str, "Description of field", default="default_value")
        }
    
    Args:
        field_type: Python type for the field
        description: Field description
        **kwargs: Additional Field parameters
    
    Returns:
        Tuple of (type, Field) for use in dynamic schema creation
    """
    from pydantic import Field
    return (field_type, Field(description=description, **kwargs))


class SchemaBuilder:
    """
    Builder class for creating dynamic validation schemas.
    """
    
    def __init__(self, base_schema: Type[BaseModel] = None):
        """
        Initialize schema builder.
        
        Args:
            base_schema: Base schema to inherit from
        """
        from .input_validator import BaseInputSchema
        self.base_schema = base_schema or BaseInputSchema
        self.fields = {}
        self.validators = {}
    
    def add_field(self, name: str, field_type: Type, description: str = "", **kwargs):
        """
        Add a field to the schema.
        
        Args:
            name: Field name
            field_type: Python type
            description: Field description
            **kwargs: Additional Field parameters
        
        Returns:
            Self for chaining
        """
        from pydantic import Field
        self.fields[name] = (field_type, Field(description=description, **kwargs))
        return self
    
    def add_validator(self, field_name: str, validator_func: Callable):
        """
        Add a field validator.
        
        Args:
            field_name: Name of field to validate
            validator_func: Validation function
        
        Returns:
            Self for chaining
        """
        self.validators[field_name] = validator_func
        return self
    
    def build(self, schema_name: str = "DynamicSchema") -> Type[BaseModel]:
        """
        Build the dynamic schema.
        
        Args:
            schema_name: Name for the schema class
        
        Returns:
            Dynamic Pydantic schema class
        """
        # Create class attributes
        class_attrs = {}
        
        # Add field annotations
        annotations = {}
        for field_name, (field_type, field_def) in self.fields.items():
            annotations[field_name] = field_type
            class_attrs[field_name] = field_def
        
        # Add validators
        for field_name, validator_func in self.validators.items():
            class_attrs[f'validate_{field_name}'] = validator_func
        
        # Set annotations
        class_attrs['__annotations__'] = annotations
        
        # Create dynamic class
        dynamic_schema = type(schema_name, (self.base_schema,), class_attrs)
        
        return dynamic_schema


def create_tool_schema(tool_name: str, fields: Dict[str, Any], base_schema: Type[BaseModel] = None) -> Type[BaseModel]:
    """
    Create a validation schema for a tool.
    
    Usage:
        schema = create_tool_schema("my_tool", {
            'input_text': schema_field(str, "Text to process"),
            'options': schema_field(Dict[str, Any], "Processing options", default={})
        })
    
    Args:
        tool_name: Name of the tool
        fields: Dictionary of field definitions
        base_schema: Base schema to inherit from
    
    Returns:
        Dynamic Pydantic schema class
    """
    builder = SchemaBuilder(base_schema)
    
    for field_name, field_def in fields.items():
        if isinstance(field_def, tuple) and len(field_def) == 2:
            field_type, field_info = field_def
            builder.fields[field_name] = (field_type, field_info)
        else:
            logger.warning(f"Invalid field definition for {field_name} in {tool_name}")
    
    schema_class = builder.build(f"{tool_name.title()}Schema")
    
    # Auto-register the schema
    try:
        from .input_validator import get_input_validator
        validator = get_input_validator()
        validator.add_custom_schema(tool_name, schema_class)
        logger.info(f"Created and registered schema for {tool_name}")
    except Exception as e:
        logger.warning(f"Failed to register created schema for {tool_name}: {e}")
    
    return schema_class


# Convenience function for quick schema registration
def register_tool_schema(tool_name: str, schema_class: Type[BaseModel]):
    """
    Register a schema for a tool.
    
    Args:
        tool_name: Name of the tool
        schema_class: Pydantic schema class
    """
    try:
        from .input_validator import get_input_validator
        validator = get_input_validator()
        validator.add_custom_schema(tool_name, schema_class)
        logger.info(f"Registered schema for {tool_name}")
    except Exception as e:
        logger.error(f"Failed to register schema for {tool_name}: {e}")


# Export commonly used items
__all__ = [
    'auto_register_schema',
    'with_validation_schema', 
    'schema_field',
    'SchemaBuilder',
    'create_tool_schema',
    'register_tool_schema'
]
