"""
Embedding configuration API for the Datagenius backend.

This module provides API endpoints for managing embedding model configurations.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..database import get_db, User
from ..auth import get_current_active_user
from agents.utils.embedding_config import get_embedding_config_manager
from agents.utils.memory_service import MemoryService

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/embedding-config",
    tags=["embedding-config"],
    responses={404: {"description": "Not found"}},
)


class EmbeddingProviderResponse(BaseModel):
    """Model for embedding provider response."""
    id: str
    name: str
    description: str
    requires_api_key: bool


class EmbeddingModelResponse(BaseModel):
    """Model for embedding model response."""
    id: str
    name: str
    dimensions: Optional[int] = None
    description: str
    size: Optional[str] = None
    languages: Optional[List[str]] = None
    cost_per_1k_tokens: Optional[float] = None


class EmbeddingConfigRequest(BaseModel):
    """Model for embedding configuration request."""
    provider: str
    model: str


class EmbeddingConfigResponse(BaseModel):
    """Model for embedding configuration response."""
    provider: str
    model: str
    is_valid: bool
    reasoning: Optional[str] = None


class EmbeddingRecommendationRequest(BaseModel):
    """Model for embedding recommendation request."""
    use_case: str = "general_purpose"
    prefer_free: bool = True


@router.get("/providers", response_model=List[EmbeddingProviderResponse])
async def get_embedding_providers(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get list of available embedding providers (Hugging Face only).

    Args:
        current_user: Current authenticated user

    Returns:
        List of available embedding providers
    """
    try:
        # Return only Hugging Face provider
        return [
            EmbeddingProviderResponse(
                id="huggingface",
                name="Hugging Face",
                description="Free, open-source embedding models from Hugging Face",
                requires_api_key=False
            )
        ]
    except Exception as e:
        logger.error(f"Error getting embedding providers: {e}")
        raise HTTPException(status_code=500, detail="Failed to get embedding providers")


@router.get("/providers/{provider}/models", response_model=List[EmbeddingModelResponse])
async def get_embedding_models(
    provider: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get list of available models for a specific provider.

    Args:
        provider: Embedding provider ID
        current_user: Current authenticated user

    Returns:
        List of available models for the provider
    """
    try:
        manager = get_embedding_config_manager()
        models = manager.get_available_models(provider)

        if not models:
            raise HTTPException(status_code=404, detail=f"Provider '{provider}' not found or has no models")

        return [
            EmbeddingModelResponse(
                id=model["id"],
                name=model["name"],
                dimensions=model.get("dimensions"),
                description=model["description"],
                size=model.get("size"),
                languages=model.get("languages"),
                cost_per_1k_tokens=model.get("cost_per_1k_tokens")
            )
            for model in models
        ]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting embedding models for provider {provider}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get embedding models")


@router.post("/validate", response_model=EmbeddingConfigResponse)
async def validate_embedding_config(
    request: EmbeddingConfigRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Validate an embedding configuration.

    Args:
        request: Embedding configuration to validate
        current_user: Current authenticated user

    Returns:
        Validation result
    """
    try:
        manager = get_embedding_config_manager()
        is_valid = manager.validate_config(request.provider, request.model)

        reasoning = None
        if not is_valid:
            reasoning = f"Invalid combination: provider '{request.provider}' does not support model '{request.model}'"
        else:
            model_info = manager.get_model_info(request.provider, request.model)
            if model_info:
                reasoning = f"Valid configuration: {model_info.get('description', 'No description available')}"

        return EmbeddingConfigResponse(
            provider=request.provider,
            model=request.model,
            is_valid=is_valid,
            reasoning=reasoning
        )
    except Exception as e:
        logger.error(f"Error validating embedding config: {e}")
        raise HTTPException(status_code=500, detail="Failed to validate embedding configuration")


@router.post("/recommend", response_model=EmbeddingConfigResponse)
async def get_embedding_recommendation(
    request: EmbeddingRecommendationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get embedding model recommendation for a specific use case.

    Args:
        request: Recommendation request
        current_user: Current authenticated user

    Returns:
        Recommended embedding configuration
    """
    try:
        manager = get_embedding_config_manager()
        suggestion = manager.suggest_best_model(request.use_case, request.prefer_free)

        return EmbeddingConfigResponse(
            provider=suggestion["provider"],
            model=suggestion["model"],
            is_valid=True,
            reasoning=suggestion["reasoning"]
        )
    except Exception as e:
        logger.error(f"Error getting embedding recommendation: {e}")
        raise HTTPException(status_code=500, detail="Failed to get embedding recommendation")


@router.get("/recommendations/{use_case}")
async def get_model_recommendations(
    use_case: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get model recommendations for a specific use case.

    Args:
        use_case: Use case for recommendations
        current_user: Current authenticated user

    Returns:
        List of recommended model IDs
    """
    try:
        manager = get_embedding_config_manager()
        recommendations = manager.get_recommendations(use_case)

        if not recommendations:
            raise HTTPException(status_code=404, detail=f"No recommendations found for use case '{use_case}'")

        return {
            "use_case": use_case,
            "recommendations": recommendations
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting recommendations for use case {use_case}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get recommendations")


@router.get("/requirements/{provider}")
async def get_provider_requirements(
    provider: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get installation requirements for a specific provider.

    Args:
        provider: Provider ID
        current_user: Current authenticated user

    Returns:
        Installation requirements
    """
    try:
        manager = get_embedding_config_manager()
        requirements = manager.get_requirements(provider)

        return {
            "provider": provider,
            "requirements": requirements
        }
    except Exception as e:
        logger.error(f"Error getting requirements for provider {provider}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get provider requirements")


@router.get("/model-info/{provider}/{model_id}")
async def get_model_info(
    provider: str,
    model_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get detailed information about a specific model.

    Args:
        provider: Provider ID
        model_id: Model ID
        current_user: Current authenticated user

    Returns:
        Detailed model information
    """
    try:
        manager = get_embedding_config_manager()
        model_info = manager.get_model_info(provider, model_id)

        if not model_info:
            raise HTTPException(
                status_code=404,
                detail=f"Model '{model_id}' not found for provider '{provider}'"
            )

        return model_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model info for {provider}/{model_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get model information")


@router.post("/refresh-memory-service")
async def refresh_memory_service(
    current_user: User = Depends(get_current_active_user)
):
    """
    Refresh the memory service configuration to use updated embedding settings.

    Args:
        current_user: Current authenticated user

    Returns:
        Status of the refresh operation
    """
    try:
        # Force refresh of memory service instances
        # This will cause them to reinitialize with new embedding configuration
        from agents.utils.vector_service import VectorService

        # Clear any cached instances if they exist
        logger.info("Refreshing memory service configuration...")

        return {
            "status": "success",
            "message": "Memory service configuration refreshed. New embedding settings will be used for future operations.",
            "recommendation": "Restart the backend for immediate effect on all operations."
        }
    except Exception as e:
        logger.error(f"Error refreshing memory service: {e}")
        raise HTTPException(status_code=500, detail="Failed to refresh memory service")
