#!/usr/bin/env python3
"""
Cross-Agent Intelligence Database Migration Script.

This script runs the database migration to add tables for cross-agent intelligence
including agent insights, interactions, and shared contexts.
"""

import os
import sys
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.database import engine, get_db
from sqlalchemy import text
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def read_migration_file() -> str:
    """Read the migration SQL file."""
    migration_file = backend_dir / "migrations" / "add_cross_agent_intelligence.sql"
    
    if not migration_file.exists():
        raise FileNotFoundError(f"Migration file not found: {migration_file}")
    
    with open(migration_file, 'r', encoding='utf-8') as f:
        return f.read()


def run_migration():
    """Run the cross-agent intelligence migration."""
    logger.info("Starting cross-agent intelligence database migration...")
    
    try:
        # Read migration SQL
        migration_sql = read_migration_file()
        logger.info("Migration SQL file loaded successfully")
        
        # Connect to database and run migration
        with engine.connect() as connection:
            # Start a transaction
            trans = connection.begin()
            
            try:
                # Execute the entire migration as one block to handle functions properly
                logger.info("Executing migration as single block...")
                connection.execute(text(migration_sql))
                logger.info("Migration executed successfully!")
                
                # Commit the transaction
                trans.commit()
                logger.info("Migration completed successfully!")
                
            except Exception as e:
                # Rollback on error
                trans.rollback()
                logger.error(f"Migration failed, rolling back: {e}")
                raise
                
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise


def verify_migration():
    """Verify that the migration was successful."""
    logger.info("Verifying migration...")
    
    try:
        with engine.connect() as connection:
            # Check if tables exist
            tables_to_check = ['agent_insights', 'agent_interactions', 'shared_contexts']
            
            for table in tables_to_check:
                result = connection.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = '{table}'
                    );
                """))
                
                exists = result.scalar()
                if exists:
                    logger.info(f"✓ Table '{table}' exists")
                else:
                    logger.error(f"✗ Table '{table}' does not exist")
                    return False
            
            # Check if indexes exist
            indexes_to_check = [
                'idx_agent_insights_profile_agent',
                'idx_agent_interactions_profile_agent',
                'idx_shared_contexts_target'
            ]
            
            for index in indexes_to_check:
                result = connection.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM pg_indexes 
                        WHERE schemaname = 'public' 
                        AND indexname = '{index}'
                    );
                """))
                
                exists = result.scalar()
                if exists:
                    logger.info(f"✓ Index '{index}' exists")
                else:
                    logger.warning(f"⚠ Index '{index}' does not exist")
            
            # Check if functions exist
            functions_to_check = [
                'cleanup_expired_shared_contexts',
                'get_cross_agent_stats',
                'get_collaboration_patterns'
            ]
            
            for function in functions_to_check:
                result = connection.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM pg_proc 
                        WHERE proname = '{function}'
                    );
                """))
                
                exists = result.scalar()
                if exists:
                    logger.info(f"✓ Function '{function}' exists")
                else:
                    logger.warning(f"⚠ Function '{function}' does not exist")
            
            # Check if views exist
            views_to_check = ['agent_performance_view', 'insight_analytics_view']
            
            for view in views_to_check:
                result = connection.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.views 
                        WHERE table_schema = 'public' 
                        AND table_name = '{view}'
                    );
                """))
                
                exists = result.scalar()
                if exists:
                    logger.info(f"✓ View '{view}' exists")
                else:
                    logger.warning(f"⚠ View '{view}' does not exist")
            
            logger.info("Migration verification completed!")
            return True
            
    except Exception as e:
        logger.error(f"Migration verification failed: {e}")
        return False


def test_basic_operations():
    """Test basic operations on the new tables."""
    logger.info("Testing basic operations...")
    
    try:
        with engine.connect() as connection:
            trans = connection.begin()
            
            try:
                # First create a test user and business profile
                connection.execute(text("""
                    INSERT INTO users (id, username, email, hashed_password, is_active)
                    VALUES (999, 'test_user', '<EMAIL>', 'hashed', true)
                    ON CONFLICT (id) DO NOTHING
                """))

                connection.execute(text("""
                    INSERT INTO business_profiles (
                        id, user_id, name, industry, business_type,
                        target_audience, products_services, marketing_goals
                    ) VALUES (
                        'test-profile-456', 999, 'Test Business', 'Technology', 'B2B',
                        'Developers', 'Software Solutions', 'Increase awareness'
                    ) ON CONFLICT (id) DO NOTHING
                """))

                # Test inserting a sample insight
                connection.execute(text("""
                    INSERT INTO agent_insights (
                        id, business_profile_id, source_agent_id, insight_type,
                        content, relevance_tags, confidence_score
                    ) VALUES (
                        'test-insight-123', 'test-profile-456', 'test-agent', 'general',
                        'This is a test insight', '["test", "migration"]', 0.9
                    )
                """))
                
                # Test querying the insight
                result = connection.execute(text("""
                    SELECT id, content FROM agent_insights WHERE id = 'test-insight-123'
                """))
                
                row = result.fetchone()
                if row:
                    logger.info(f"✓ Successfully inserted and retrieved test insight: {row[1]}")
                else:
                    logger.error("✗ Failed to retrieve test insight")
                    return False
                
                # Test inserting a sample interaction
                connection.execute(text("""
                    INSERT INTO agent_interactions (
                        id, business_profile_id, agent_id, user_message, 
                        agent_response, outcome
                    ) VALUES (
                        'test-interaction-123', 'test-profile-456', 'test-agent',
                        'Test user message', 'Test agent response', 'success'
                    )
                """))
                
                # Test querying the interaction
                result = connection.execute(text("""
                    SELECT id, outcome FROM agent_interactions WHERE id = 'test-interaction-123'
                """))
                
                row = result.fetchone()
                if row:
                    logger.info(f"✓ Successfully inserted and retrieved test interaction: {row[1]}")
                else:
                    logger.error("✗ Failed to retrieve test interaction")
                    return False
                
                # Test the stats function
                result = connection.execute(text("""
                    SELECT * FROM get_cross_agent_stats('test-profile-456')
                """))
                
                stats = result.fetchone()
                if stats:
                    logger.info(f"✓ Stats function working: {stats[0]} insights, {stats[1]} interactions")
                else:
                    logger.warning("⚠ Stats function returned no results")
                
                # Clean up test data
                connection.execute(text("DELETE FROM agent_insights WHERE id = 'test-insight-123'"))
                connection.execute(text("DELETE FROM agent_interactions WHERE id = 'test-interaction-123'"))
                connection.execute(text("DELETE FROM business_profiles WHERE id = 'test-profile-456'"))
                connection.execute(text("DELETE FROM users WHERE id = 999"))
                
                trans.commit()
                logger.info("Basic operations test completed successfully!")
                return True
                
            except Exception as e:
                trans.rollback()
                logger.error(f"Basic operations test failed: {e}")
                return False
                
    except Exception as e:
        logger.error(f"Failed to test basic operations: {e}")
        return False


def main():
    """Main migration function."""
    logger.info("Cross-Agent Intelligence Database Migration")
    logger.info("=" * 50)
    
    try:
        # Run the migration
        run_migration()
        
        # Verify the migration
        if verify_migration():
            logger.info("Migration verification passed!")
        else:
            logger.error("Migration verification failed!")
            return 1
        
        # Test basic operations
        if test_basic_operations():
            logger.info("Basic operations test passed!")
        else:
            logger.error("Basic operations test failed!")
            return 1
        
        logger.info("=" * 50)
        logger.info("Cross-agent intelligence migration completed successfully!")
        logger.info("The following features are now available:")
        logger.info("- Agent insights storage and retrieval")
        logger.info("- Agent interaction tracking")
        logger.info("- Cross-agent context sharing")
        logger.info("- Collaboration pattern analysis")
        logger.info("- Performance metrics and analytics")
        
        return 0
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
