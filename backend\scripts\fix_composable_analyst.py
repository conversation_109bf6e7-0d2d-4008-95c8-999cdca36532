"""
Comprehensive fix for the composable analyst agent.

This script:
1. Ensures the composable-analyst.yaml file has proper encoding
2. Manually registers the composable analysis agent
3. Restarts the server to apply the changes
"""

import os
import sys
import logging
import yaml
import importlib
import asyncio
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import the agent modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def fix_yaml_file():
    """
    Fix the composable-analyst.yaml file encoding.
    """
    try:
        # Path to the YAML file
        yaml_path = os.path.join(parent_dir, "personas", "composable-analyst.yaml")
        fixed_path = os.path.join(parent_dir, "personas", "composable-analyst-fixed.yaml")
        
        # Check if the file exists
        if not os.path.exists(yaml_path):
            logger.error(f"YAML file not found: {yaml_path}")
            return False
        
        # Read the file content
        with open(yaml_path, "r", encoding="utf-8", errors="ignore") as f:
            content = f.read()
        
        # Parse the YAML content
        try:
            config = yaml.safe_load(content)
            logger.info("Successfully parsed YAML content")
        except Exception as e:
            logger.error(f"Error parsing YAML content: {e}")
            return False
        
        # Write the fixed content back to a new file
        with open(fixed_path, "w", encoding="utf-8") as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        # Replace the original file with the fixed one
        os.remove(yaml_path)
        os.rename(fixed_path, yaml_path)
        
        logger.info(f"Fixed YAML file: {yaml_path}")
        return True
    except Exception as e:
        logger.error(f"Error fixing YAML file: {e}")
        return False

def fix_agent_registration():
    """
    Manually register the composable analysis agent.
    """
    try:
        # Import necessary modules
        from agents.registry import AgentRegistry
        from agents.analysis_agent.composable_agent import ComposableAnalysisAgent
        
        # Log the current state
        logger.info("Current registered personas: %s", AgentRegistry.list_registered_personas())
        
        # Register the composable analysis agent manually
        if "composable-analysis-ai" not in AgentRegistry.list_registered_personas():
            AgentRegistry.register("composable-analysis-ai", ComposableAnalysisAgent)
            logger.info("Registered composable analysis agent")
        else:
            logger.info("Composable analysis agent already registered")
        
        # Log the updated state
        logger.info("Updated registered personas: %s", AgentRegistry.list_registered_personas())
        
        # Try to create an instance to verify it works
        async def test_agent():
            agent = await AgentRegistry.create_agent_instance("composable-analysis-ai")
            if agent:
                logger.info("Successfully created composable analysis agent instance")
                return True
            else:
                logger.error("Failed to create composable analysis agent instance")
                return False
        
        # Run the test
        success = asyncio.run(test_agent())
        if success:
            logger.info("Composable analysis agent registration fixed")
        return success
    except Exception as e:
        logger.error(f"Error fixing agent registration: {e}")
        return False

def fix_init_file():
    """
    Fix the __init__.py file to ensure the agent is always registered.
    """
    try:
        # Path to the __init__.py file
        init_path = os.path.join(parent_dir, "agents", "__init__.py")
        
        # Check if the file exists
        if not os.path.exists(init_path):
            logger.error(f"__init__.py file not found: {init_path}")
            return False
        
        # Read the file content
        with open(init_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check if the file already has our fix
        if "Ensuring critical agents are registered" in content:
            logger.info("__init__.py file already fixed")
            return True
        
        # Add our fix to the file
        with open(init_path, "w", encoding="utf-8") as f:
            # Replace the old code with our new code
            new_content = content.replace(
                "# Load configurations from the personas directory\ntry:\n    AgentRegistry.load_configurations(personas_dir)\n    logger.info(f\"Loaded agent configurations from {personas_dir}\")\nexcept Exception as e:\n    logger.error(f\"Error loading agent configurations: {e}\")\n\n    # Fall back to static registration if configuration loading fails\n    logger.info(\"Falling back to static agent registration\")\n\n    # Import composable agent implementations\n    from .classification.composable_agent import ComposableClassificationAgent\n    from .marketing_agent.composable_agent import ComposableMarketingAgent\n    from .analysis_agent.composable_agent import ComposableAnalysisAgent\n\n    # Register composable agents\n    AgentRegistry.register(\"composable-ai\", ComposableAgent)\n    AgentRegistry.register(\"composable-classifier-ai\", ComposableClassificationAgent)\n    AgentRegistry.register(\"composable-marketing-ai\", ComposableMarketingAgent)\n    AgentRegistry.register(\"composable-analysis-ai\", ComposableAnalysisAgent)\n\n    logger.info(\"Registered composable agents with registry\")",
                """# Import composable agent implementations first
from .classification.composable_agent import ComposableClassificationAgent
from .marketing_agent.composable_agent import ComposableMarketingAgent
from .analysis_agent.composable_agent import ComposableAnalysisAgent

# Load configurations from the personas directory
try:
    AgentRegistry.load_configurations(personas_dir)
    logger.info(f"Loaded agent configurations from {personas_dir}")
except Exception as e:
    logger.error(f"Error loading agent configurations: {e}")
    logger.info("Will ensure agents are registered manually")

# Ensure critical agents are always registered, regardless of YAML loading
logger.info("Ensuring critical agents are registered")

# Register composable agents if they're not already registered
if "composable-ai" not in AgentRegistry.list_registered_personas():
    AgentRegistry.register("composable-ai", ComposableAgent)
    logger.info("Registered ComposableAgent")

if "composable-classifier-ai" not in AgentRegistry.list_registered_personas():
    AgentRegistry.register("composable-classifier-ai", ComposableClassificationAgent)
    logger.info("Registered ComposableClassificationAgent")

if "composable-marketing-ai" not in AgentRegistry.list_registered_personas():
    AgentRegistry.register("composable-marketing-ai", ComposableMarketingAgent)
    logger.info("Registered ComposableMarketingAgent")

if "composable-analysis-ai" not in AgentRegistry.list_registered_personas():
    AgentRegistry.register("composable-analysis-ai", ComposableAnalysisAgent)
    logger.info("Registered ComposableAnalysisAgent")

logger.info("Critical agents registration complete")"""
            )
            f.write(new_content)
        
        logger.info(f"Fixed __init__.py file: {init_path}")
        return True
    except Exception as e:
        logger.error(f"Error fixing __init__.py file: {e}")
        return False

def main():
    """
    Main function to fix the composable analyst agent.
    """
    logger.info("Starting comprehensive fix for composable analyst agent")
    
    # Fix the YAML file
    yaml_fixed = fix_yaml_file()
    logger.info(f"YAML file fix {'succeeded' if yaml_fixed else 'failed'}")
    
    # Fix the __init__.py file
    init_fixed = fix_init_file()
    logger.info(f"__init__.py file fix {'succeeded' if init_fixed else 'failed'}")
    
    # Fix the agent registration
    registration_fixed = fix_agent_registration()
    logger.info(f"Agent registration fix {'succeeded' if registration_fixed else 'failed'}")
    
    # Overall success
    success = yaml_fixed and init_fixed and registration_fixed
    
    if success:
        logger.info("All fixes applied successfully")
        logger.info("Please restart the server to apply the changes")
    else:
        logger.error("Some fixes failed, please check the logs")
    
    return success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
