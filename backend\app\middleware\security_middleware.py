"""
Enhanced security middleware for file uploads and general security.

This module provides comprehensive security features including:
- Streaming file upload with memory management
- File type validation with magic number checking
- Resource cleanup management
- Input sanitization
- Rate limiting
"""

import os
import tempfile
import logging
import hashlib
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from fastapi import HTT<PERSON><PERSON>x<PERSON>, UploadFile, Request
from fastapi.responses import JSONResponse
import psutil
import re

from ..config import MAX_UPLOAD_SIZE, UPLOAD_DIR

logger = logging.getLogger(__name__)


class ValidationResult:
    """Result of file validation."""
    
    def __init__(self, is_valid: bool, message: str = "", metadata: Dict[str, Any] = None):
        self.is_valid = is_valid
        self.message = message
        self.metadata = metadata or {}


class SecureFileUploader:
    """
    Secure file uploader with streaming support and comprehensive validation.
    """
    
    def __init__(self):
        """Initialize secure file uploader."""
        self.allowed_extensions = {'.csv', '.xlsx', '.xls', '.pdf', '.doc', '.docx'}
        self.max_file_size = MAX_UPLOAD_SIZE
        self.chunk_size = 8192  # 8KB chunks for streaming
        
        # MIME type to extension mapping for validation
        self.allowed_mime_types = {
            'text/csv': ['.csv'],
            'application/vnd.ms-excel': ['.xls'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
            'application/pdf': ['.pdf'],
            'application/msword': ['.doc'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
        }
        
        # Magic numbers for file type validation
        self.magic_numbers = {
            b'\x50\x4B\x03\x04': ['.xlsx', '.docx'],  # ZIP-based formats
            b'\x50\x4B\x05\x06': ['.xlsx', '.docx'],  # Empty ZIP
            b'\x50\x4B\x07\x08': ['.xlsx', '.docx'],  # ZIP variant
            b'\xD0\xCF\x11\xE0': ['.doc', '.xls'],    # OLE formats
            b'%PDF': ['.pdf'],                         # PDF format
        }
        
        logger.info(f"Initialized SecureFileUploader with max size: {self.max_file_size} bytes")

    async def validate_file(self, file: UploadFile) -> ValidationResult:
        """
        Comprehensive file validation.

        Args:
            file: The uploaded file to validate

        Returns:
            ValidationResult with validation status and details
        """
        try:
            # 1. Check file extension
            if not self._is_allowed_extension(file.filename):
                return ValidationResult(
                    False,
                    f"File type not allowed. Allowed types: {', '.join(self.allowed_extensions)}"
                )

            # 2. Read first chunk to check magic numbers and size
            first_chunk = await file.read(self.chunk_size)
            await file.seek(0)  # Reset file pointer

            if not first_chunk:
                return ValidationResult(False, "File is empty")

            # 3. Validate magic numbers
            if not self._validate_magic_numbers(first_chunk, file.filename):
                return ValidationResult(
                    False,
                    "File content doesn't match the file extension"
                )

            # 4. Check file size by reading in chunks (streaming approach)
            file_size = await self._get_file_size_streaming(file)
            if file_size > self.max_file_size:
                return ValidationResult(
                    False,
                    f"File too large ({file_size} bytes). Maximum allowed: {self.max_file_size} bytes"
                )

            # 5. Basic content scanning for suspicious patterns
            if await self._contains_suspicious_content(file):
                return ValidationResult(False, "File contains suspicious content")

            return ValidationResult(
                True,
                "File validation successful",
                {"file_size": file_size, "content_type": file.content_type}
            )

        except Exception as e:
            logger.error(f"File validation error: {str(e)}", exc_info=True)
            return ValidationResult(False, f"Validation error: {str(e)}")

    def _is_allowed_extension(self, filename: str) -> bool:
        """Check if file extension is allowed."""
        if not filename:
            return False
        
        extension = os.path.splitext(filename.lower())[1]
        return extension in self.allowed_extensions

    def _validate_magic_numbers(self, file_header: bytes, filename: str) -> bool:
        """Validate file magic numbers against extension."""
        if not filename:
            return False

        extension = os.path.splitext(filename.lower())[1]

        # For CSV files, check for text content
        if extension == '.csv':
            try:
                # CSV files should be readable as text
                file_header.decode('utf-8')
                return True
            except UnicodeDecodeError:
                return False

        # Check magic numbers for binary formats
        for magic_bytes, valid_extensions in self.magic_numbers.items():
            if file_header.startswith(magic_bytes):
                return extension in valid_extensions

        # Special handling for PDF files - they might not start exactly at the beginning
        if extension == '.pdf':
            # Look for %PDF anywhere in the first chunk
            if b'%PDF' in file_header[:100]:  # Check first 100 bytes
                return True

        # If no magic number matches but extension is CSV, allow it
        if extension == '.csv':
            return True

        return False

    async def _get_file_size_streaming(self, file: UploadFile) -> int:
        """Get file size using streaming approach to avoid loading entire file into memory."""
        file_size = 0
        await file.seek(0)
        
        while True:
            chunk = await file.read(self.chunk_size)
            if not chunk:
                break
            file_size += len(chunk)
            
            # Early termination if file is too large
            if file_size > self.max_file_size:
                await file.seek(0)  # Reset for potential future use
                return file_size
        
        await file.seek(0)  # Reset file pointer
        return file_size

    async def _contains_suspicious_content(self, file: UploadFile) -> bool:
        """Basic content scanning for suspicious patterns."""
        suspicious_patterns = [
            b'<script',
            b'javascript:',
            b'vbscript:',
            b'<?php',
            b'<%',
            b'exec(',
            b'system(',
            b'shell_exec(',
        ]
        
        await file.seek(0)
        
        # Read file in chunks to avoid memory issues
        while True:
            chunk = await file.read(self.chunk_size)
            if not chunk:
                break
            
            chunk_lower = chunk.lower()
            for pattern in suspicious_patterns:
                if pattern in chunk_lower:
                    await file.seek(0)  # Reset file pointer
                    return True
        
        await file.seek(0)  # Reset file pointer
        return False

    async def upload_file_streaming(self, file: UploadFile, user_id: str) -> Tuple[str, Dict[str, Any]]:
        """
        Upload file using streaming approach to minimize memory usage.

        Args:
            file: The uploaded file
            user_id: ID of the user uploading the file

        Returns:
            Tuple of (file_path, file_info)
        """
        # Validate file first
        validation_result = await self.validate_file(file)
        if not validation_result.is_valid:
            raise HTTPException(status_code=400, detail=validation_result.message)

        # Generate unique filename
        file_uuid = hashlib.md5(f"{user_id}_{file.filename}_{datetime.now().isoformat()}".encode()).hexdigest()
        file_extension = os.path.splitext(file.filename.lower())[1]
        final_filename = f"{file_uuid}{file_extension}"

        # Ensure upload directory exists
        os.makedirs(UPLOAD_DIR, exist_ok=True)
        file_path = os.path.join(UPLOAD_DIR, final_filename)

        # Stream file to disk
        file_size = 0
        file_hash = hashlib.sha256()

        try:
            async with self.managed_file_processing(file_path) as temp_path:
                with open(temp_path, "wb") as temp_file:
                    await file.seek(0)

                    while True:
                        chunk = await file.read(self.chunk_size)
                        if not chunk:
                            break

                        temp_file.write(chunk)
                        file_size += len(chunk)
                        file_hash.update(chunk)

                        # Check size limit during streaming
                        if file_size > self.max_file_size:
                            raise HTTPException(
                                status_code=400,
                                detail=f"File too large: {file_size} bytes"
                            )

                # Move temp file to final location
                os.rename(temp_path, file_path)

                file_info = {
                    "original_filename": file.filename,
                    "stored_filename": final_filename,
                    "file_path": file_path,
                    "file_size": file_size,
                    "file_hash": file_hash.hexdigest(),
                    "content_type": file.content_type,
                    "upload_timestamp": datetime.now().isoformat(),
                    "user_id": user_id
                }

                logger.info(f"Successfully uploaded file: {file.filename} ({file_size} bytes)")
                return file_path, file_info

        except Exception as e:
            logger.error(f"Error during file upload: {str(e)}", exc_info=True)
            # Cleanup on error
            if os.path.exists(file_path):
                try:
                    os.unlink(file_path)
                except Exception as cleanup_error:
                    logger.error(f"Error cleaning up file: {cleanup_error}")
            raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

    @asynccontextmanager
    async def managed_file_processing(self, final_path: str):
        """
        Context manager for safe file processing with automatic cleanup.

        Args:
            final_path: Final path where file should be stored

        Yields:
            Temporary file path for processing
        """
        temp_file = None
        temp_path = None

        try:
            # Create temporary file in same directory as final file
            temp_dir = os.path.dirname(final_path)
            temp_file = tempfile.NamedTemporaryFile(
                dir=temp_dir,
                delete=False,
                prefix="upload_temp_",
                suffix=".tmp"
            )
            temp_path = temp_file.name
            temp_file.close()  # Close file handle but keep file

            yield temp_path

        except Exception as e:
            logger.error(f"Error in managed file processing: {str(e)}")
            raise
        finally:
            # Cleanup temporary file if it still exists
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                    logger.debug(f"Cleaned up temporary file: {temp_path}")
                except Exception as cleanup_error:
                    logger.error(f"Error cleaning up temporary file {temp_path}: {cleanup_error}")


class MemoryManager:
    """
    Memory management system for monitoring and controlling memory usage.
    """

    def __init__(self, max_memory_mb: int = 512):
        """
        Initialize memory manager.

        Args:
            max_memory_mb: Maximum memory usage in MB before warnings
        """
        self.max_memory_mb = max_memory_mb
        self.max_memory_bytes = max_memory_mb * 1024 * 1024

    def get_current_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage information."""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                "rss_mb": memory_info.rss / 1024 / 1024,  # Resident Set Size
                "vms_mb": memory_info.vms / 1024 / 1024,  # Virtual Memory Size
                "percent": process.memory_percent(),
                "available_system_mb": psutil.virtual_memory().available / 1024 / 1024
            }
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return {"error": str(e)}

    @asynccontextmanager
    async def monitor_memory(self, operation_name: str):
        """
        Context manager for monitoring memory usage during operations.

        Args:
            operation_name: Name of the operation being monitored
        """
        initial_memory = self.get_current_memory_usage()
        start_time = datetime.now()

        try:
            logger.debug(f"Starting {operation_name} - Initial memory: {initial_memory.get('rss_mb', 0):.2f}MB")
            yield

        finally:
            final_memory = self.get_current_memory_usage()
            duration = (datetime.now() - start_time).total_seconds()

            memory_increase = final_memory.get('rss_mb', 0) - initial_memory.get('rss_mb', 0)

            logger.info(
                f"Completed {operation_name} - "
                f"Duration: {duration:.2f}s, "
                f"Memory change: {memory_increase:+.2f}MB, "
                f"Final memory: {final_memory.get('rss_mb', 0):.2f}MB"
            )

            # Warn if memory usage is high
            if final_memory.get('rss_mb', 0) > self.max_memory_mb:
                logger.warning(
                    f"High memory usage detected: {final_memory.get('rss_mb', 0):.2f}MB "
                    f"(limit: {self.max_memory_mb}MB)"
                )

    def check_memory_threshold(self) -> bool:
        """
        Check if current memory usage is within acceptable limits.

        Returns:
            True if memory usage is acceptable, False otherwise
        """
        current_memory = self.get_current_memory_usage()
        current_mb = current_memory.get('rss_mb', 0)

        if current_mb > self.max_memory_mb:
            logger.warning(f"Memory threshold exceeded: {current_mb:.2f}MB > {self.max_memory_mb}MB")
            return False

        return True


class EnhancedInputValidator:
    """
    Comprehensive input validation and sanitization system for Phase 1 security enhancement.

    Provides advanced SQL injection protection, XSS prevention, and context-aware validation
    as specified in the refactor.md Phase 1 requirements.
    """

    def __init__(self):
        """Initialize the enhanced input validator."""
        self.sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)",
            r"(UNION\s+SELECT)",
            r"(\'\s*(OR|AND)\s*\'\w*\'\s*=\s*\'\w*)",
            r"(--|\#|/\*|\*/)",
            r"(\bEXEC\s*\()",
            r"(\bSP_\w+)",
            r"(\bxp_\w+)",
            r"(\bsp_\w+)",
            r"(\bFN_\w+)",
            r"(;\s*(DROP|DELETE|INSERT|UPDATE))",
            r"(\bINTO\s+OUTFILE)",
            r"(\bLOAD_FILE\s*\()",
        ]

        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>.*?</iframe>",
            r"<object[^>]*>.*?</object>",
            r"<embed[^>]*>.*?</embed>",
            r"<applet[^>]*>.*?</applet>",
            r"<meta[^>]*>",
            r"<link[^>]*>",
            r"<style[^>]*>.*?</style>",
            r"expression\s*\(",
            r"url\s*\(",
            r"@import",
            r"vbscript:",
            r"data:text/html",
        ]

        self.path_traversal_patterns = [
            r"\.\.[\\/]",
            r"[\\/]\.\.[\\/]",
            r"\.\.\\",
            r"\.\./",
            r"%2e%2e%2f",
            r"%2e%2e\\",
            r"\.\.%2f",
            r"\.\.%5c",
        ]

        self.command_injection_patterns = [
            r"\$\(",
            r"`.*`",
            r"\|\s*\w+\s*;",  # More specific: pipe followed by command and semicolon
            r"&&\s*\w+\s*;",  # More specific: AND followed by command and semicolon
            r"\|\|\s*\w+\s*;", # More specific: OR followed by command and semicolon
            r";\s*(rm|del|cat|ls|dir|type|echo|ping|wget|curl|nc|netcat|bash|sh|cmd|powershell)\s",
            r"&\s*(rm|del|cat|ls|dir|type|echo|ping|wget|curl|nc|netcat|bash|sh|cmd|powershell)\s",
            r"\|\s*(rm|del|cat|ls|dir|type|echo|ping|wget|curl|nc|netcat|bash|sh|cmd|powershell)\s",
        ]

        logger.info("Enhanced input validator initialized with comprehensive security patterns")

    def sanitize_input(self, data: Any, context: str = "general") -> Any:
        """
        Sanitize input data based on context.

        Args:
            data: Input data to sanitize
            context: Context for sanitization (sql_query, html_content, file_path, etc.)

        Returns:
            Sanitized data
        """
        if isinstance(data, str):
            return self._sanitize_string(data, context)
        elif isinstance(data, dict):
            return {key: self.sanitize_input(value, context) for key, value in data.items()}
        elif isinstance(data, list):
            return [self.sanitize_input(item, context) for item in data]
        else:
            return data

    def _sanitize_string(self, text: str, context: str) -> str:
        """
        Sanitize string input based on context.

        Args:
            text: String to sanitize
            context: Sanitization context

        Returns:
            Sanitized string
        """
        if not text:
            return text

        # Context-aware sanitization
        if context == "sql_query":
            return self._sanitize_sql(text)
        elif context == "html_content":
            return self._sanitize_html(text)
        elif context == "file_path":
            return self._sanitize_file_path(text)
        elif context == "command":
            return self._sanitize_command(text)
        else:
            return self._general_sanitize(text)

    def _sanitize_sql(self, text: str) -> str:
        """Sanitize text for SQL injection prevention."""
        import re

        sanitized = text
        for pattern in self.sql_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)

        # Additional SQL-specific sanitization
        sanitized = sanitized.replace("'", "''")  # Escape single quotes
        sanitized = sanitized.replace('"', '""')  # Escape double quotes

        return sanitized.strip()

    def _sanitize_html(self, text: str) -> str:
        """Sanitize HTML content for XSS prevention."""
        import re
        import html

        # First escape HTML entities
        sanitized = html.escape(text)

        # Remove dangerous patterns
        for pattern in self.xss_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)

        return sanitized

    def _sanitize_file_path(self, path: str) -> str:
        """Sanitize file paths for path traversal prevention."""
        import re
        import os

        sanitized = path

        # Remove path traversal patterns
        for pattern in self.path_traversal_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)

        # Normalize path and ensure it's safe
        sanitized = os.path.normpath(sanitized)

        # Remove any remaining dangerous characters
        sanitized = re.sub(r'[<>:"|?*]', '', sanitized)

        return sanitized

    def _sanitize_command(self, text: str) -> str:
        """Sanitize text for command injection prevention."""
        import re

        sanitized = text

        # Remove command injection patterns
        for pattern in self.command_injection_patterns:
            sanitized = re.sub(pattern, '', sanitized)

        return sanitized.strip()

    def _general_sanitize(self, text: str) -> str:
        """General text sanitization."""
        import html

        # Basic HTML escaping
        sanitized = html.escape(text)

        # Remove null bytes and control characters
        sanitized = ''.join(char for char in sanitized if ord(char) >= 32 or char in '\t\n\r')

        return sanitized

    def validate_input_security(self, data: Any, context: str = "general") -> Dict[str, Any]:
        """
        Validate input for security threats.

        Args:
            data: Input data to validate
            context: Validation context

        Returns:
            Validation result with threat detection
        """
        threats_detected = []

        if isinstance(data, str):
            threats_detected.extend(self._detect_threats_in_string(data, context))
        elif isinstance(data, dict):
            for key, value in data.items():
                sub_result = self.validate_input_security(value, context)
                threats_detected.extend(sub_result.get("threats", []))
        elif isinstance(data, list):
            for item in data:
                sub_result = self.validate_input_security(item, context)
                threats_detected.extend(sub_result.get("threats", []))

        return {
            "is_safe": len(threats_detected) == 0,
            "threats": threats_detected,
            "context": context
        }

    def _detect_threats_in_string(self, text: str, context: str) -> List[str]:
        """Detect security threats in string input."""
        import re

        threats = []

        # SQL injection detection
        for pattern in self.sql_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                threats.append(f"SQL injection pattern detected: {pattern}")

        # XSS detection
        for pattern in self.xss_patterns:
            if re.search(pattern, text, re.IGNORECASE | re.DOTALL):
                threats.append(f"XSS pattern detected: {pattern}")

        # Path traversal detection
        if context == "file_path":
            for pattern in self.path_traversal_patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    threats.append(f"Path traversal pattern detected: {pattern}")

        # Command injection detection
        if context == "command":
            for pattern in self.command_injection_patterns:
                if re.search(pattern, text):
                    threats.append(f"Command injection pattern detected: {pattern}")

        return threats

    def __init__(self):
        """Initialize input validator."""
        # SQL injection patterns
        self.sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)",
            r"(UNION\s+SELECT)",
            r"(\'\s*(OR|AND)\s*\'\w*\'\s*=\s*\'\w*)",
            r"(--|\#|/\*|\*/)",
            r"(\bEXEC\s*\()",
            r"(\bSP_\w+)",
            r"(\bxp_cmdshell\b)",
            r"(\b(TRUNCATE|SHUTDOWN|BACKUP)\b)",
        ]

        # XSS patterns
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"vbscript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>.*?</iframe>",
            r"<object[^>]*>.*?</object>",
            r"<embed[^>]*>.*?</embed>",
            r"<link[^>]*>.*?</link>",
            r"<meta[^>]*>.*?</meta>",
        ]

        # Path traversal patterns
        self.path_traversal_patterns = [
            r"(\.\./|\.\.\\)",
            r"(/etc/passwd|/etc/shadow|/windows/system32)",
            r"(\\\\|//)",
            r"(\.\./){2,}",
        ]

        # Command injection patterns - more specific to avoid false positives
        self.command_injection_patterns = [
            r"(\$\()",  # Command substitution
            r"(\$\{[^}]*\})",  # Variable substitution
            r"(`[^`]*`)",  # Backtick command execution
            r"(;\s*(cat|ls|dir|type|echo|ping|wget|curl|rm|del|nc|netcat|bash|sh|cmd|powershell)\s)",
            r"(&\s*(cat|ls|dir|type|echo|ping|wget|curl|rm|del|nc|netcat|bash|sh|cmd|powershell)\s)",
            r"(\|\s*(cat|ls|dir|type|echo|ping|wget|curl|rm|del|nc|netcat|bash|sh|cmd|powershell)\s)",
        ]

    def sanitize_input(self, data: Any, context: str = "general") -> Any:
        """
        Sanitize input data based on context.

        Args:
            data: Input data to sanitize
            context: Context for sanitization (general, sql_query, html_content, file_path)

        Returns:
            Sanitized data
        """
        if data is None:
            return data

        if isinstance(data, str):
            return self._sanitize_string(data, context)
        elif isinstance(data, dict):
            return {key: self.sanitize_input(value, context) for key, value in data.items()}
        elif isinstance(data, list):
            return [self.sanitize_input(item, context) for item in data]
        else:
            return data

    def _sanitize_string(self, text: str, context: str) -> str:
        """Sanitize string input based on context."""
        if not text:
            return text

        # Remove null bytes
        text = text.replace('\x00', '')

        # Context-specific sanitization
        if context == "sql_query":
            return self._sanitize_sql(text)
        elif context == "html_content":
            return self._sanitize_html(text)
        elif context == "file_path":
            return self._sanitize_file_path(text)
        else:
            return self._general_sanitize(text)

    def _sanitize_sql(self, text: str) -> str:
        """Sanitize SQL-related input."""
        # Remove dangerous SQL patterns
        for pattern in self.sql_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        # Escape single quotes
        text = text.replace("'", "''")

        return text.strip()

    def _sanitize_html(self, text: str) -> str:
        """Sanitize HTML content."""
        # Basic HTML escaping
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')
        text = text.replace('"', '&quot;')
        text = text.replace("'", '&#x27;')

        # Remove dangerous patterns
        for pattern in self.xss_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        return text

    def _sanitize_file_path(self, text: str) -> str:
        """Sanitize file path input."""
        # Remove path traversal patterns
        for pattern in self.path_traversal_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        # Remove dangerous characters
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in dangerous_chars:
            text = text.replace(char, '')

        return text.strip()

    def _general_sanitize(self, text: str) -> str:
        """General sanitization for text input."""
        # Remove control characters except newline, tab, and carriage return
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t\r')

        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        # Remove extremely long sequences of the same character
        text = re.sub(r'(.)\1{50,}', r'\1' * 10, text)

        return text

    def validate_input(self, data: Any, input_type: str = "general") -> Dict[str, Any]:
        """
        Validate input data for security threats.

        Args:
            data: Input data to validate
            input_type: Type of input for validation rules

        Returns:
            Validation result with threats detected
        """
        threats = []
        warnings = []

        if isinstance(data, str):
            # Check for SQL injection
            if self._contains_sql_injection(data):
                threats.append("sql_injection")

            # Check for XSS
            if self._contains_xss(data):
                threats.append("xss")

            # Check for path traversal
            if self._contains_path_traversal(data):
                threats.append("path_traversal")

            # Check for command injection
            if self._contains_command_injection(data):
                threats.append("command_injection")

            # Check for suspicious patterns
            if len(data) > 10000:
                warnings.append("input_too_long")

            # Check for excessive special characters
            if len(data) > 0:
                special_char_ratio = sum(1 for c in data if not c.isalnum() and not c.isspace()) / len(data)
                if special_char_ratio > 0.5:
                    warnings.append("high_special_char_ratio")

        return {
            "is_safe": len(threats) == 0,
            "threats": threats,
            "warnings": warnings,
            "sanitized_data": self.sanitize_input(data, input_type)
        }

    def _contains_sql_injection(self, text: str) -> bool:
        """Check for SQL injection patterns."""
        for pattern in self.sql_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False

    def _contains_xss(self, text: str) -> bool:
        """Check for XSS patterns."""
        for pattern in self.xss_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False

    def _contains_path_traversal(self, text: str) -> bool:
        """Check for path traversal patterns."""
        for pattern in self.path_traversal_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False

    def _contains_command_injection(self, text: str) -> bool:
        """Check for command injection patterns."""
        for pattern in self.command_injection_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False


# Global instances
secure_file_uploader = SecureFileUploader()
memory_manager = MemoryManager()
input_validator = EnhancedInputValidator()
