"""
Component registration for the marketing agent.

This module registers marketing components with the component registry.
"""

import logging
from agents.components.registry import ComponentRegistry
from .components import (
    MarketingParserComponent,
    MCPContentGeneratorComponent
)

logger = logging.getLogger(__name__)


def register_marketing_components():
    """Register all marketing components with the registry."""
    # Register marketing parser component
    ComponentRegistry.register("marketing_parser", MarketingParserComponent)
    logger.info("Registered MarketingParserComponent with component registry")

    # Register marketing content generator component
    ComponentRegistry.register("marketing_content_generator", MCPContentGeneratorComponent)
    logger.info("Registered MCPContentGeneratorComponent with component registry")

    # Log the registered components
    component_names = ComponentRegistry.list_registered_components()
    logger.info(f"Registered marketing components: {component_names}")
