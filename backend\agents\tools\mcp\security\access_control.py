"""
Security and Access Control System for MCP Tools.

This module provides comprehensive security features including input sanitization,
rate limiting, access control based on user permissions, and audit logging.
"""

import asyncio
import hashlib
import hmac
import logging
import time
from typing import Dict, Any, List, Optional, Set, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import ipaddress
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class PermissionLevel(Enum):
    """Permission levels for tool access."""
    NONE = "none"
    READ = "read"
    WRITE = "write"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


class SecurityThreatLevel(Enum):
    """Security threat levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AuditEventType(Enum):
    """Types of audit events."""
    ACCESS_GRANTED = "access_granted"
    ACCESS_DENIED = "access_denied"
    RATE_LIMITED = "rate_limited"
    SECURITY_VIOLATION = "security_violation"
    TOOL_EXECUTION = "tool_execution"
    AUTHENTICATION_FAILURE = "authentication_failure"
    PERMISSION_ESCALATION = "permission_escalation"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"


@dataclass
class UserPermissions:
    """User permission configuration."""
    user_id: str
    tool_permissions: Dict[str, PermissionLevel] = field(default_factory=dict)
    global_permission: PermissionLevel = PermissionLevel.READ
    allowed_agents: Set[str] = field(default_factory=set)
    rate_limit_override: Optional[int] = None
    ip_whitelist: Set[str] = field(default_factory=set)
    expires_at: Optional[datetime] = None
    is_active: bool = True


@dataclass
class SecurityContext:
    """Security context for requests."""
    user_id: Optional[str]
    agent_identity: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    session_id: Optional[str]
    request_id: Optional[str]
    timestamp: datetime = field(default_factory=datetime.now)
    authentication_method: Optional[str] = None


@dataclass
class AuditEvent:
    """Audit event for security logging."""
    event_id: str
    event_type: AuditEventType
    user_id: Optional[str]
    tool_name: str
    agent_identity: Optional[str]
    security_context: SecurityContext
    result: str  # "success", "denied", "error"
    threat_level: SecurityThreatLevel
    details: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class RateLimitConfig:
    """Rate limiting configuration."""
    requests_per_minute: int = 60
    requests_per_hour: int = 1000
    requests_per_day: int = 10000
    burst_limit: int = 10
    cooldown_period: int = 300  # seconds


class RateLimiter:
    """Token bucket rate limiter."""
    
    def __init__(self, config: RateLimitConfig):
        """Initialize rate limiter."""
        self.config = config
        self.user_buckets: Dict[str, Dict[str, deque]] = defaultdict(lambda: {
            "minute": deque(),
            "hour": deque(),
            "day": deque(),
            "burst": deque()
        })
        self.blocked_users: Dict[str, datetime] = {}
    
    async def check_rate_limit(self, user_id: str, tool_name: str) -> tuple[bool, Dict[str, Any]]:
        """
        Check if user is within rate limits.
        
        Args:
            user_id: User identifier
            tool_name: Tool being accessed
            
        Returns:
            Tuple of (allowed, rate_limit_info)
        """
        now = datetime.now()
        
        # Check if user is in cooldown
        if user_id in self.blocked_users:
            if now < self.blocked_users[user_id]:
                return False, {
                    "blocked": True,
                    "cooldown_until": self.blocked_users[user_id].isoformat(),
                    "reason": "Rate limit exceeded - in cooldown period"
                }
            else:
                del self.blocked_users[user_id]
        
        buckets = self.user_buckets[user_id]
        
        # Clean old entries
        self._clean_buckets(buckets, now)
        
        # Check limits
        minute_count = len(buckets["minute"])
        hour_count = len(buckets["hour"])
        day_count = len(buckets["day"])
        burst_count = len(buckets["burst"])
        
        # Check burst limit (last 10 seconds)
        if burst_count >= self.config.burst_limit:
            self._block_user(user_id, now)
            return False, {
                "blocked": True,
                "reason": "Burst limit exceeded",
                "limit": self.config.burst_limit,
                "current": burst_count
            }
        
        # Check minute limit
        if minute_count >= self.config.requests_per_minute:
            return False, {
                "blocked": False,
                "reason": "Minute rate limit exceeded",
                "limit": self.config.requests_per_minute,
                "current": minute_count,
                "reset_in_seconds": 60 - (now.second)
            }
        
        # Check hour limit
        if hour_count >= self.config.requests_per_hour:
            return False, {
                "blocked": False,
                "reason": "Hour rate limit exceeded",
                "limit": self.config.requests_per_hour,
                "current": hour_count,
                "reset_in_seconds": 3600 - (now.minute * 60 + now.second)
            }
        
        # Check day limit
        if day_count >= self.config.requests_per_day:
            return False, {
                "blocked": False,
                "reason": "Daily rate limit exceeded",
                "limit": self.config.requests_per_day,
                "current": day_count,
                "reset_in_seconds": 86400 - (now.hour * 3600 + now.minute * 60 + now.second)
            }
        
        # Add request to buckets
        buckets["minute"].append(now)
        buckets["hour"].append(now)
        buckets["day"].append(now)
        buckets["burst"].append(now)
        
        return True, {
            "allowed": True,
            "remaining": {
                "minute": self.config.requests_per_minute - minute_count - 1,
                "hour": self.config.requests_per_hour - hour_count - 1,
                "day": self.config.requests_per_day - day_count - 1
            }
        }
    
    def _clean_buckets(self, buckets: Dict[str, deque], now: datetime):
        """Clean old entries from rate limit buckets."""
        # Clean minute bucket
        while buckets["minute"] and now - buckets["minute"][0] > timedelta(minutes=1):
            buckets["minute"].popleft()
        
        # Clean hour bucket
        while buckets["hour"] and now - buckets["hour"][0] > timedelta(hours=1):
            buckets["hour"].popleft()
        
        # Clean day bucket
        while buckets["day"] and now - buckets["day"][0] > timedelta(days=1):
            buckets["day"].popleft()
        
        # Clean burst bucket (10 seconds)
        while buckets["burst"] and now - buckets["burst"][0] > timedelta(seconds=10):
            buckets["burst"].popleft()
    
    def _block_user(self, user_id: str, now: datetime):
        """Block user for cooldown period."""
        self.blocked_users[user_id] = now + timedelta(seconds=self.config.cooldown_period)
        logger.warning(f"User {user_id} blocked for rate limit violation")


class SecurityValidator:
    """Security validation for inputs and requests."""
    
    def __init__(self):
        """Initialize security validator."""
        self.threat_patterns = self._initialize_threat_patterns()
        self.suspicious_patterns = self._initialize_suspicious_patterns()
    
    def _initialize_threat_patterns(self) -> Dict[str, List[str]]:
        """Initialize threat detection patterns."""
        return {
            "sql_injection": [
                r"(?i)(union\s+select|drop\s+table|delete\s+from|insert\s+into)",
                r"(?i)(exec\s*\(|execute\s*\(|sp_executesql)",
                r"(?i)(or\s+1\s*=\s*1|and\s+1\s*=\s*1)",
                r"(?i)(information_schema|sys\.tables|sys\.columns)"
            ],
            "xss": [
                r"(?i)<script[^>]*>.*?</script>",
                r"(?i)javascript:",
                r"(?i)on\w+\s*=",
                r"(?i)<iframe[^>]*>.*?</iframe>",
                r"(?i)document\.cookie",
                r"(?i)window\.location"
            ],
            "command_injection": [
                r"(?i)(\||;|&|`|\$\(|\${)",
                r"(?i)(rm\s+-rf|del\s+\/|format\s+c:)",
                r"(?i)(wget|curl|nc|netcat)",
                r"(?i)(\/bin\/|\/usr\/bin\/|cmd\.exe)"
            ],
            "path_traversal": [
                r"\.\.[\\/]",
                r"[\\/]\.\.[\\/]",
                r"(?i)(etc[\\/]passwd|windows[\\/]system32)",
                r"(?i)(\.\.[\\/]){2,}"
            ],
            "data_exfiltration": [
                r"(?i)(password|passwd|secret|key|token)",
                r"(?i)(credit.*card|ssn|social.*security)",
                r"(?i)(api.*key|access.*token|bearer)",
                r"(?i)(private.*key|certificate)"
            ]
        }
    
    def _initialize_suspicious_patterns(self) -> List[str]:
        """Initialize suspicious activity patterns."""
        return [
            r"(?i)(admin|administrator|root|superuser)",
            r"(?i)(test|debug|dev|development)",
            r"(?i)(backup|dump|export|download)",
            r"(?i)(config|configuration|settings)",
            r"(?i)(internal|private|confidential)"
        ]
    
    async def validate_input_security(self, input_data: Dict[str, Any]) -> tuple[bool, SecurityThreatLevel, List[str]]:
        """
        Validate input for security threats.
        
        Args:
            input_data: Input data to validate
            
        Returns:
            Tuple of (is_safe, threat_level, threats_found)
        """
        threats_found = []
        max_threat_level = SecurityThreatLevel.LOW
        
        # Convert input to string for pattern matching
        input_text = json.dumps(input_data, default=str).lower()
        
        # Check for threat patterns
        for threat_type, patterns in self.threat_patterns.items():
            for pattern in patterns:
                if self._match_pattern(pattern, input_text):
                    threats_found.append(f"{threat_type}: {pattern}")
                    max_threat_level = SecurityThreatLevel.CRITICAL
        
        # Check for suspicious patterns
        for pattern in self.suspicious_patterns:
            if self._match_pattern(pattern, input_text):
                threats_found.append(f"suspicious: {pattern}")
                if max_threat_level == SecurityThreatLevel.LOW:
                    max_threat_level = SecurityThreatLevel.MEDIUM
        
        is_safe = max_threat_level in [SecurityThreatLevel.LOW, SecurityThreatLevel.MEDIUM]
        
        return is_safe, max_threat_level, threats_found
    
    def _match_pattern(self, pattern: str, text: str) -> bool:
        """Match security pattern against text."""
        import re
        try:
            return bool(re.search(pattern, text))
        except re.error:
            return False
    
    async def validate_ip_address(self, ip_address: str, whitelist: Set[str]) -> bool:
        """Validate IP address against whitelist."""
        if not ip_address:
            return False
        
        if not whitelist:
            return True  # No restrictions if no whitelist
        
        try:
            ip = ipaddress.ip_address(ip_address)
            
            for allowed_ip in whitelist:
                if '/' in allowed_ip:
                    # CIDR notation
                    network = ipaddress.ip_network(allowed_ip, strict=False)
                    if ip in network:
                        return True
                else:
                    # Exact IP match
                    if ip == ipaddress.ip_address(allowed_ip):
                        return True
            
            return False
        except ValueError:
            return False


class AccessController:
    """
    Comprehensive access control system for MCP tools.
    
    Features:
    - User permission management
    - Tool-specific access control
    - Rate limiting
    - IP whitelisting
    - Security threat detection
    - Audit logging
    - Session management
    """
    
    def __init__(self):
        """Initialize access controller."""
        self.user_permissions: Dict[str, UserPermissions] = {}
        self.rate_limiter = RateLimiter(RateLimitConfig())
        self.security_validator = SecurityValidator()
        self.audit_log: List[AuditEvent] = []
        self.active_sessions: Dict[str, SecurityContext] = {}
        
        # Default tool permissions
        self.default_tool_permissions = {
            "conversation_tool": PermissionLevel.READ,
            "intent_detection": PermissionLevel.READ,
            "language_detection": PermissionLevel.READ,
            "text_processing": PermissionLevel.READ,
            "data_access": PermissionLevel.WRITE,
            "statistical_analysis": PermissionLevel.WRITE,
            "data_visualization": PermissionLevel.WRITE,
            "marketing_strategy_generation": PermissionLevel.ADMIN,
            "code_execution_tool": PermissionLevel.ADMIN
        }
        
        logger.info("Access controller initialized")

    async def _get_or_create_user_permissions(self, user_id: str, agent_identity: Optional[str] = None) -> Optional[UserPermissions]:
        """
        Get or create user permissions based on their purchases.

        Args:
            user_id: User ID
            agent_identity: Current agent identity (persona)

        Returns:
            UserPermissions object with appropriate permissions
        """
        try:
            # Check if we already have cached permissions for this user
            if user_id in self.user_permissions:
                cached_perms = self.user_permissions[user_id]
                # Check if permissions are still valid (not expired)
                if not cached_perms.expires_at or cached_perms.expires_at > datetime.now():
                    return cached_perms

            # Import database functions
            from app.database import get_db
            from app.services.persona_service import persona_service

            # Get database session
            db = next(get_db())

            try:
                # Convert user_id to int (database expects integer user IDs)
                user_id_int = int(user_id)

                # Get user's purchased personas
                purchased_personas = persona_service.get_user_purchased_personas(db, user_id_int)
                logger.info(f"User {user_id} has purchased personas: {purchased_personas}")

                # Create user permissions based on purchases
                user_perms = UserPermissions(
                    user_id=user_id,
                    global_permission=PermissionLevel.READ,  # Base permission level
                    allowed_agents=set(purchased_personas),  # Allow access to purchased agents
                    is_active=True,
                    expires_at=datetime.now() + timedelta(hours=1)  # Cache for 1 hour
                )

                # Grant tool permissions based on purchased personas
                tool_permissions = {}

                # Basic tools available to all authenticated users
                basic_tools = {
                    "conversation_tool": PermissionLevel.READ,
                    "intent_detection": PermissionLevel.READ,
                    "language_detection": PermissionLevel.READ,
                    "text_processing": PermissionLevel.READ
                }
                tool_permissions.update(basic_tools)

                # Advanced tools based on purchased personas
                for persona_id in purchased_personas:
                    if persona_id == "concierge-agent":
                        # Concierge agent tools
                        tool_permissions.update({
                            "get_persona_marketplace_info": PermissionLevel.READ,
                            "get_user_knowledge_graph": PermissionLevel.READ
                        })
                    elif "marketing" in persona_id.lower():
                        # Marketing agent tools
                        tool_permissions.update({
                            "generate_marketing_content": PermissionLevel.ADMIN,
                            "marketing_strategy_generation": PermissionLevel.ADMIN,
                            "data_access": PermissionLevel.WRITE
                        })
                    elif "analysis" in persona_id.lower() or "analyst" in persona_id.lower():
                        # Analysis agent tools
                        tool_permissions.update({
                            "data_access": PermissionLevel.WRITE,
                            "data_analysis": PermissionLevel.WRITE,
                            "data_visualization": PermissionLevel.WRITE,
                            "data_querying": PermissionLevel.WRITE,
                            "statistical_analysis": PermissionLevel.WRITE,
                            "pandasai_analysis": PermissionLevel.WRITE,
                            "code_execution_tool": PermissionLevel.ADMIN
                        })
                    elif "classification" in persona_id.lower():
                        # Classification agent tools
                        tool_permissions.update({
                            "text_classification": PermissionLevel.WRITE,
                            "data_access": PermissionLevel.READ
                        })

                user_perms.tool_permissions = tool_permissions

                # Cache the permissions
                self.user_permissions[user_id] = user_perms

                logger.info(f"Created permissions for user {user_id}: {len(tool_permissions)} tools, {len(purchased_personas)} agents")
                return user_perms

            except ValueError:
                logger.error(f"Invalid user_id format: {user_id}")
                return None
            except Exception as e:
                logger.error(f"Error checking user purchases for {user_id}: {e}")
                return None
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error in _get_or_create_user_permissions: {e}")
            return None

    async def check_access(
        self,
        tool_name: str,
        security_context: SecurityContext,
        input_data: Optional[Dict[str, Any]] = None
    ) -> tuple[bool, Dict[str, Any]]:
        """
        Check if access should be granted for tool execution.

        Args:
            tool_name: Name of the tool
            security_context: Security context for the request
            input_data: Input data for security validation

        Returns:
            Tuple of (access_granted, access_info)
        """
        access_info = {
            "timestamp": datetime.now().isoformat(),
            "tool_name": tool_name,
            "user_id": security_context.user_id,
            "checks_performed": []
        }

        try:
            # 1. User authentication check
            if not security_context.user_id:
                await self._log_audit_event(
                    AuditEventType.ACCESS_DENIED,
                    tool_name,
                    security_context,
                    "No user authentication",
                    SecurityThreatLevel.MEDIUM
                )
                access_info["reason"] = "Authentication required"
                return False, access_info

            access_info["checks_performed"].append("authentication")

            # 2. Dynamic user permissions check based on purchases
            user_perms = await self._get_or_create_user_permissions(security_context.user_id, security_context.agent_identity)
            if not user_perms or not user_perms.is_active:
                await self._log_audit_event(
                    AuditEventType.ACCESS_DENIED,
                    tool_name,
                    security_context,
                    "User not found or inactive",
                    SecurityThreatLevel.MEDIUM
                )
                access_info["reason"] = "User not authorized"
                return False, access_info
            
            # Check expiration
            if user_perms.expires_at and datetime.now() > user_perms.expires_at:
                await self._log_audit_event(
                    AuditEventType.ACCESS_DENIED,
                    tool_name,
                    security_context,
                    "User permissions expired",
                    SecurityThreatLevel.LOW
                )
                access_info["reason"] = "Permissions expired"
                return False, access_info
            
            access_info["checks_performed"].append("user_permissions")
            
            # 3. Tool-specific permissions check
            required_permission = self.default_tool_permissions.get(tool_name, PermissionLevel.READ)
            user_tool_permission = user_perms.tool_permissions.get(tool_name, user_perms.global_permission)
            
            if not self._has_sufficient_permission(user_tool_permission, required_permission):
                await self._log_audit_event(
                    AuditEventType.ACCESS_DENIED,
                    tool_name,
                    security_context,
                    f"Insufficient permissions: {user_tool_permission.value} < {required_permission.value}",
                    SecurityThreatLevel.MEDIUM
                )
                access_info["reason"] = f"Insufficient permissions for {tool_name}"
                return False, access_info
            
            access_info["checks_performed"].append("tool_permissions")
            
            # 4. Agent access check
            if security_context.agent_identity and user_perms.allowed_agents:
                if security_context.agent_identity not in user_perms.allowed_agents:
                    await self._log_audit_event(
                        AuditEventType.ACCESS_DENIED,
                        tool_name,
                        security_context,
                        f"Agent {security_context.agent_identity} not in allowed list",
                        SecurityThreatLevel.MEDIUM
                    )
                    access_info["reason"] = f"Agent {security_context.agent_identity} not authorized"
                    return False, access_info
            
            access_info["checks_performed"].append("agent_access")
            
            # 5. IP whitelist check
            if user_perms.ip_whitelist and security_context.ip_address:
                ip_allowed = await self.security_validator.validate_ip_address(
                    security_context.ip_address,
                    user_perms.ip_whitelist
                )
                if not ip_allowed:
                    await self._log_audit_event(
                        AuditEventType.ACCESS_DENIED,
                        tool_name,
                        security_context,
                        f"IP {security_context.ip_address} not in whitelist",
                        SecurityThreatLevel.HIGH
                    )
                    access_info["reason"] = "IP address not authorized"
                    return False, access_info
            
            access_info["checks_performed"].append("ip_whitelist")
            
            # 6. Rate limiting check
            rate_limit_config = RateLimitConfig()
            if user_perms.rate_limit_override:
                rate_limit_config.requests_per_minute = user_perms.rate_limit_override
            
            rate_allowed, rate_info = await self.rate_limiter.check_rate_limit(
                security_context.user_id,
                tool_name
            )
            
            if not rate_allowed:
                await self._log_audit_event(
                    AuditEventType.RATE_LIMITED,
                    tool_name,
                    security_context,
                    f"Rate limit exceeded: {rate_info}",
                    SecurityThreatLevel.LOW
                )
                access_info["reason"] = "Rate limit exceeded"
                access_info["rate_limit_info"] = rate_info
                return False, access_info
            
            access_info["checks_performed"].append("rate_limiting")
            access_info["rate_limit_info"] = rate_info
            
            # 7. Input security validation
            if input_data:
                is_safe, threat_level, threats = await self.security_validator.validate_input_security(input_data)
                
                if not is_safe or threat_level == SecurityThreatLevel.CRITICAL:
                    await self._log_audit_event(
                        AuditEventType.SECURITY_VIOLATION,
                        tool_name,
                        security_context,
                        f"Security threats detected: {threats}",
                        threat_level
                    )
                    access_info["reason"] = "Security violation detected"
                    access_info["security_threats"] = threats
                    return False, access_info
                
                if threats:
                    access_info["security_warnings"] = threats
            
            access_info["checks_performed"].append("input_security")
            
            # All checks passed
            await self._log_audit_event(
                AuditEventType.ACCESS_GRANTED,
                tool_name,
                security_context,
                "All security checks passed",
                SecurityThreatLevel.LOW
            )
            
            access_info["access_granted"] = True
            access_info["user_permission_level"] = user_tool_permission.value
            
            return True, access_info
            
        except Exception as e:
            logger.error(f"Access control error: {e}")
            await self._log_audit_event(
                AuditEventType.ACCESS_DENIED,
                tool_name,
                security_context,
                f"Access control system error: {str(e)}",
                SecurityThreatLevel.HIGH
            )
            access_info["reason"] = "Access control system error"
            return False, access_info
    
    def _has_sufficient_permission(self, user_permission: PermissionLevel, required_permission: PermissionLevel) -> bool:
        """Check if user permission level is sufficient."""
        permission_hierarchy = {
            PermissionLevel.NONE: 0,
            PermissionLevel.READ: 1,
            PermissionLevel.WRITE: 2,
            PermissionLevel.ADMIN: 3,
            PermissionLevel.SUPER_ADMIN: 4
        }
        
        return permission_hierarchy[user_permission] >= permission_hierarchy[required_permission]
    
    async def _log_audit_event(
        self,
        event_type: AuditEventType,
        tool_name: str,
        security_context: SecurityContext,
        details: str,
        threat_level: SecurityThreatLevel
    ):
        """Log audit event."""
        event = AuditEvent(
            event_id=f"{int(time.time())}_{hash(details) % 10000}",
            event_type=event_type,
            user_id=security_context.user_id,
            tool_name=tool_name,
            agent_identity=security_context.agent_identity,
            security_context=security_context,
            result="success" if event_type == AuditEventType.ACCESS_GRANTED else "denied",
            threat_level=threat_level,
            details={"message": details}
        )
        
        self.audit_log.append(event)
        
        # Limit audit log size
        if len(self.audit_log) > 10000:
            self.audit_log = self.audit_log[-5000:]
        
        # Log to system logger
        log_data = {
            "event_type": event_type.value,
            "user_id": security_context.user_id,
            "tool_name": tool_name,
            "threat_level": threat_level.value,
            "details": details
        }
        
        if threat_level == SecurityThreatLevel.CRITICAL:
            logger.critical(f"SECURITY CRITICAL: {json.dumps(log_data)}")
        elif threat_level == SecurityThreatLevel.HIGH:
            logger.error(f"SECURITY HIGH: {json.dumps(log_data)}")
        elif threat_level == SecurityThreatLevel.MEDIUM:
            logger.warning(f"SECURITY MEDIUM: {json.dumps(log_data)}")
        else:
            logger.info(f"SECURITY INFO: {json.dumps(log_data)}")
    
    def add_user_permissions(self, user_permissions: UserPermissions):
        """Add or update user permissions."""
        self.user_permissions[user_permissions.user_id] = user_permissions
        logger.info(f"Updated permissions for user: {user_permissions.user_id}")
    
    def revoke_user_access(self, user_id: str):
        """Revoke user access."""
        if user_id in self.user_permissions:
            self.user_permissions[user_id].is_active = False
            logger.info(f"Revoked access for user: {user_id}")

    def clear_user_permissions_cache(self, user_id: str):
        """Clear cached permissions for a user (e.g., after a new purchase)."""
        if user_id in self.user_permissions:
            del self.user_permissions[user_id]
            logger.info(f"Cleared permissions cache for user: {user_id}")

    def clear_all_permissions_cache(self):
        """Clear all cached permissions (e.g., for system maintenance)."""
        self.user_permissions.clear()
        logger.info("Cleared all permissions cache")

    def get_audit_log(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent audit events."""
        recent_events = self.audit_log[-limit:]
        return [
            {
                "event_id": event.event_id,
                "event_type": event.event_type.value,
                "user_id": event.user_id,
                "tool_name": event.tool_name,
                "result": event.result,
                "threat_level": event.threat_level.value,
                "timestamp": event.timestamp.isoformat(),
                "details": event.details
            }
            for event in recent_events
        ]
    
    def get_security_statistics(self) -> Dict[str, Any]:
        """Get security statistics."""
        if not self.audit_log:
            return {"total_events": 0}
        
        # Calculate statistics
        total_events = len(self.audit_log)
        event_types = defaultdict(int)
        threat_levels = defaultdict(int)
        
        for event in self.audit_log:
            event_types[event.event_type.value] += 1
            threat_levels[event.threat_level.value] += 1
        
        # Recent events (last hour)
        recent_cutoff = datetime.now() - timedelta(hours=1)
        recent_events = [e for e in self.audit_log if e.timestamp >= recent_cutoff]
        
        return {
            "total_events": total_events,
            "recent_events_1h": len(recent_events),
            "event_types": dict(event_types),
            "threat_levels": dict(threat_levels),
            "active_users": len([p for p in self.user_permissions.values() if p.is_active]),
            "blocked_users": len(self.rate_limiter.blocked_users)
        }


# Global access controller instance
_global_access_controller: Optional[AccessController] = None


def get_access_controller() -> AccessController:
    """Get global access controller instance."""
    global _global_access_controller
    if _global_access_controller is None:
        _global_access_controller = AccessController()
    return _global_access_controller


def refresh_user_permissions(user_id: str):
    """Refresh user permissions after a purchase or permission change."""
    controller = get_access_controller()
    controller.clear_user_permissions_cache(user_id)
    logger.info(f"Refreshed permissions for user {user_id}")


def refresh_all_permissions():
    """Refresh all user permissions (for system maintenance)."""
    controller = get_access_controller()
    controller.clear_all_permissions_cache()
    logger.info("Refreshed all user permissions")


def require_permission(required_permission: PermissionLevel):
    """Decorator to require specific permission level for tool access."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract security context from arguments
            # This would be implemented based on your specific argument structure
            access_controller = get_access_controller()
            
            # For now, return the function result
            # In practice, you'd extract security context and check permissions
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator
