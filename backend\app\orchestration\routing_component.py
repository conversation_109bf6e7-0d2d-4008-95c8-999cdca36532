"""
Component responsible for routing incoming requests to the appropriate agent/persona.
"""

import logging
from typing import Dict, Any, Optional

# Import AgentRegistry to check available agents if needed later
# from ..agents.registry import AgentRegistry

logger = logging.getLogger(__name__)


class RoutingComponent:
    """
    Determines the target agent for a given user request.
    """

    def __init__(self):
        """Initialize the RoutingComponent."""
        # self.agent_registry = AgentRegistry # Could be injected if needed
        logger.info("RoutingComponent initialized.")

    async def determine_target_agent(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Analyzes the message and context to determine the best agent.

        Args:
            message: The user's message text.
            context: Optional context dictionary.

        Returns:
            The ID/Name of the target agent (e.g., "ConciergeAgent", "MarketingAgent").
        """
        if context is None:
            context = {}

        # DEBUG LOGGING: Log routing decision process
        logger.info(f"=== ROUTING COMPONENT DEBUG ===")
        logger.info(f"Message: '{message}'")
        logger.info(f"Context: {context}")

        # Check if there's a current persona in context that should be maintained
        current_persona = context.get("current_persona") or context.get("persona_id")
        if current_persona:
            logger.info(f"Current persona in context: {current_persona}")
        else:
            logger.info("No current persona found in context")

        logger.debug(f"RoutingComponent determining target for message: {message[:100]}...")

        # --- Intelligent Routing Logic ---
        # Priority-based routing: user persona selection takes precedence

        message_lower = message.lower()

        # Check for explicit routing instructions in context (highest priority)
        explicit_target = context.get("route_to_persona") or context.get("target_persona")
        if explicit_target:
            logger.info(f"Explicit routing to persona: {explicit_target}")
            final_target = explicit_target
            logger.info(f"=== ROUTING DECISION: {final_target} ===")
            logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
            return final_target

        # Check for current persona in conversation (second highest priority)
        # This ensures that once a user selects a persona, subsequent messages stay with that persona
        current_persona = context.get("current_persona")
        logger.info(f"🎯 ROUTING: Current persona from context: {current_persona}")

        # Also check alternative keys for current persona
        if not current_persona:
            current_persona = context.get("persona_id") or context.get("agent_id")
            if current_persona:
                logger.info(f"🎯 ROUTING: Found current persona from alternative key: {current_persona}")

        # Debug: Log all context keys to help troubleshoot
        logger.info(f"🔍 ROUTING: Available context keys: {list(context.keys())}")

        # DIAGNOSTIC: Log detailed context state for follow-up message debugging
        logger.info("=== ROUTING DIAGNOSTIC INFO ===")
        logger.info(f"Message: {message[:100]}...")
        logger.info(f"Context routing keys: current_persona={context.get('current_persona')}, persona_id={context.get('persona_id')}, agent_id={context.get('agent_id')}")
        logger.info(f"Context flags: skip_marketing_content_generation={context.get('skip_marketing_content_generation')}, is_conversational={context.get('is_conversational')}")
        logger.info(f"Context metadata: {context.get('metadata', {})}")
        logger.info(f"Marketing form data present: {'marketing_form_data' in context}")
        logger.info("=== END ROUTING DIAGNOSTIC ===")

        if current_persona and current_persona != "concierge-agent":
            # Maintain conversation with current persona for all messages
            # Persona switching should only happen through explicit UI actions, not message content
            logger.info(f"✅ PERSONA ROUTING: Maintaining conversation with current persona: {current_persona}")
            logger.info(f"✅ PERSONA ROUTING: Message will be routed directly to {current_persona}, bypassing concierge")
            final_target = current_persona
            logger.info(f"🎯 === ROUTING DECISION: {final_target} (PERSONA CONTINUITY) ===")
            logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
            return final_target
        elif current_persona == "concierge-agent":
            logger.info(f"🤖 ROUTING: Current persona is concierge-agent, will continue with content-based routing")
        else:
            logger.info(f"❓ ROUTING: No current persona found in context, will use content-based routing")

        # Content-based routing (ONLY applies when concierge is active or no persona is set)
        # If we have a non-concierge current_persona, we should have already returned above
        # This section should only execute for concierge conversations or error scenarios

        # Safety check: Content-based routing should only happen for concierge or no persona
        if current_persona and current_persona != "concierge-agent":
            logger.error(f"❌ ROUTING: Logic error - should not reach content-based routing with current_persona: {current_persona}")
            final_target = current_persona
            logger.info(f"🎯 === ROUTING DECISION: {final_target} (SAFETY FALLBACK) ===")
            logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
            return final_target

        # Check for marketing form submissions - route directly to marketing agent
        if context.get("marketing_form_data"):
            logger.info("Marketing form data detected - routing directly to marketing agent")
            final_target = "composable-marketing-ai"
            logger.info(f"=== ROUTING DECISION: {final_target} ===")
            logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
            return final_target

        # For very specific content generation requests, route directly to appropriate agent
        # Check for marketing content generation requests (very specific keywords)
        marketing_keywords = [
            "generate marketing_strategy content",
            "generate campaign_strategy content",
            "generate social_media_content content",
            "generate seo_optimization content",
            "generate post_composer content",
            "regenerate marketing_strategy content",
            "regenerate campaign_strategy content",
            "regenerate social_media_content content",
            "regenerate seo_optimization content",
            "regenerate post_composer content"
        ]

        if any(keyword in message_lower for keyword in marketing_keywords):
            logger.info(f"Marketing content generation request detected: {message}")
            final_target = "composable-marketing-ai"
            logger.info(f"=== ROUTING DECISION: {final_target} ===")
            logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
            return final_target

        # For data analysis requests with attached data sources, route to analysis agent
        has_data_source = (context.get("data_source") or
                          context.get("data_sources") or
                          context.get("attached_files"))

        if has_data_source:
            logger.info(f"Data source attached - routing to analysis agent, has_data_source: {bool(has_data_source)}")
            final_target = "composable-analysis-ai"
            logger.info(f"=== ROUTING DECISION: {final_target} ===")
            logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
            return final_target

        # If we reach here, no current_persona was found in context
        # This should not happen in normal operation since WebSocket sets conversation.persona_id as fallback
        # Only route to concierge if explicitly requested or in error scenarios
        logger.warning("⚠️ ROUTING: No current_persona found in context - this indicates a potential issue")
        logger.warning("⚠️ ROUTING: Conversations should maintain their designated persona")

        # Check if this is an explicit concierge request
        message_lower = message.lower()
        concierge_keywords = ["concierge", "help me choose", "which persona", "what can you do"]
        is_explicit_concierge_request = any(keyword in message_lower for keyword in concierge_keywords)

        if is_explicit_concierge_request:
            logger.info("🤖 ROUTING: Explicit concierge request detected")
            final_target = "concierge-agent"
            logger.info(f"🎯 === ROUTING DECISION: {final_target} (EXPLICIT CONCIERGE REQUEST) ===")
        else:
            # This is an error scenario - we should have a current_persona
            logger.error("❌ ROUTING: No current_persona and no explicit concierge request")
            logger.error("❌ ROUTING: This indicates a routing configuration issue")
            final_target = "concierge-agent"  # Fallback to prevent system failure
            logger.info(f"🎯 === ROUTING DECISION: {final_target} (ERROR FALLBACK) ===")

        logger.info(f"=== END ROUTING COMPONENT DEBUG ===")
        return final_target


