import React from 'react';
import { useDashboardHeader } from '@/hooks/use-dashboard-header';
import { UnifiedHeader } from './UnifiedHeader';
import { Dashboard } from './Dashboard';

interface DashboardWithHeaderProps {
  className?: string;
}

export const DashboardWithHeader: React.FC<DashboardWithHeaderProps> = ({ className }) => {
  // Use consolidated dashboard header hook - this provides all header functionality
  const { headerProps } = useDashboardHeader();

  return (
    <div className="h-full flex flex-col">
      {/* Header at the top */}
      <UnifiedHeader {...headerProps} />
      {/* Dashboard content below header */}
      <div className="flex-1 overflow-hidden">
        <Dashboard className={className} />
      </div>
    </div>
  );
};
