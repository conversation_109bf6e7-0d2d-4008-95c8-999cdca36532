import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import {
  MessageSquare,
  UserCircle,
  FileUp,
  Compass,
  ArrowRight,
  CheckCircle2,
  HelpCircle,
  AlertCircle
} from 'lucide-react';
import { WorkflowStage } from './WorkflowStageIndicator';
import { createReactSyncApp } from '@/utils/createReactSyncApp';

interface WorkflowStep {
  stage: WorkflowStage;
  title: string;
  description: string;
  icon: React.ReactNode;
  timestamp?: string;
  isCompleted: boolean;
  isActive: boolean;
}

interface WorkflowVisualizationProps {
  steps: WorkflowStep[];
  className?: string;
  showCard?: boolean;
}

export const WorkflowVisualization: React.FC<WorkflowVisualizationProps> = ({
  steps,
  className = '',
  showCard = true
}) => {
  const workflowContent = (
    <div className="relative">
      {/* Vertical line connecting all steps */}
      <motion.div
        className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"
        initial={{ scaleY: 0 }}
        animate={{
          scaleY: 1,
          transition: {
            duration: 0.8,
            ease: "easeOut"
          }
        }}
        style={{ originY: 0 }}
      />

      {/* Steps */}
      <div className="space-y-6">
        {steps.map((step, index) => (
          <motion.div
            key={step.stage}
            initial={{ opacity: 0, x: -10 }}
            animate={{
              opacity: 1,
              x: 0,
              transition: {
                delay: index * 0.15,
                type: "spring",
                stiffness: 100,
                damping: 15
              }
            }}
            className="relative flex items-start"
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.2 }
            }}
          >
            {/* Step icon */}
            <motion.div
              className={`z-10 flex items-center justify-center w-12 h-12 rounded-full ${
                step.isActive
                  ? 'bg-brand-100 text-brand-600'
                  : step.isCompleted
                    ? 'bg-green-100 text-green-600'
                    : 'bg-gray-100 text-gray-400'
              }`}
              animate={step.isActive ? {
                scale: [1, 1.05, 1],
                transition: {
                  repeat: Infinity,
                  repeatType: "reverse",
                  duration: 2
                }
              } : {}}
            >
              {step.icon}
            </motion.div>

            {/* Step content */}
            <div className="ml-4 flex-1">
              <div className="flex items-center">
                <h3 className={`font-medium ${
                  step.isActive
                    ? 'text-brand-600'
                    : step.isCompleted
                      ? 'text-green-600'
                      : 'text-gray-500'
                }`}>
                  {step.title}
                </h3>
                {step.isCompleted && (
                  <CheckCircle2 className="ml-2 h-4 w-4 text-green-500" />
                )}
                {step.isActive && (
                  <div className="ml-2 h-2 w-2 rounded-full bg-brand-500 animate-pulse" />
                )}
              </div>
              <p className="text-sm text-gray-500 mt-1">{step.description}</p>
              {step.timestamp && (
                <p className="text-xs text-gray-400 mt-1">{step.timestamp}</p>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  if (!showCard) {
    return <div className={className}>{workflowContent}</div>;
  }

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Compass className="h-5 w-5 text-brand-500" />
          Concierge Workflow
        </CardTitle>
      </CardHeader>
      <CardContent>
        {workflowContent}
      </CardContent>
    </Card>
  );
};

// Helper function to create workflow steps from concierge state
export const createWorkflowSteps = (
  conciergeState: any
): WorkflowStep[] => {
  if (!conciergeState) return [];

  const currentStage = conciergeState.stage as WorkflowStage;
  const highlights = conciergeState.highlights || [];

  // Define all possible steps
  const allSteps: WorkflowStep[] = [
    {
      stage: 'greeting',
      title: 'Welcome',
      description: 'Initial greeting and introduction',
      icon: <MessageSquare className="h-5 w-5" />,
      isCompleted: false,
      isActive: currentStage === 'greeting'
    },
    {
      stage: 'needs_assessment',
      title: 'Needs Assessment',
      description: 'Understanding your requirements',
      icon: <HelpCircle className="h-5 w-5" />,
      isCompleted: false,
      isActive: currentStage === 'needs_assessment'
    },
    {
      stage: 'persona_recommendation',
      title: 'Persona Recommendations',
      description: 'Suggesting appropriate AI personas',
      icon: <Compass className="h-5 w-5" />,
      isCompleted: false,
      isActive: currentStage === 'persona_recommendation'
    },
    {
      stage: 'data_guidance',
      title: 'Data Guidance',
      description: 'Guidance on attaching data files',
      icon: <FileUp className="h-5 w-5" />,
      isCompleted: false,
      isActive: currentStage === 'data_guidance'
    },
    {
      stage: 'data_attached',
      title: 'Data Attached',
      description: 'Data files have been attached',
      icon: <FileUp className="h-5 w-5" />,
      isCompleted: false,
      isActive: currentStage === 'data_attached'
    },
    {
      stage: 'persona_selection',
      title: 'Persona Selection',
      description: 'Selecting an AI persona',
      icon: <UserCircle className="h-5 w-5" />,
      isCompleted: false,
      isActive: currentStage === 'persona_selection'
    },
    {
      stage: 'handoff',
      title: 'Handoff',
      description: 'Transferring to the selected persona',
      icon: <ArrowRight className="h-5 w-5" />,
      isCompleted: false,
      isActive: currentStage === 'handoff'
    },
    {
      stage: 'followup',
      title: 'Follow-up',
      description: 'Following up after using a specialized persona',
      icon: <MessageSquare className="h-5 w-5" />,
      isCompleted: false,
      isActive: currentStage === 'followup'
    },
    {
      stage: 'completed',
      title: 'Completed',
      description: 'Workflow completed',
      icon: <CheckCircle2 className="h-5 w-5" />,
      isCompleted: false,
      isActive: currentStage === 'completed'
    }
  ];

  // Determine which steps are completed based on the current stage
  const stageOrder: WorkflowStage[] = [
    'greeting',
    'needs_assessment',
    'persona_recommendation',
    'data_guidance',
    'data_attached',
    'persona_selection',
    'handoff',
    'followup',
    'completed'
  ];

  const currentStageIndex = stageOrder.indexOf(currentStage);

  // Mark steps as completed based on the current stage
  allSteps.forEach(step => {
    const stepIndex = stageOrder.indexOf(step.stage);
    if (stepIndex < currentStageIndex) {
      step.isCompleted = true;
    }
  });

  // Add timestamps from highlights if available
  highlights.forEach((highlight: any) => {
    const matchingStep = allSteps.find(step => step.stage === highlight.type ||
      (highlight.type === 'data_attached' && step.stage === 'data_attached') ||
      (highlight.type === 'recommendation' && step.stage === 'persona_recommendation') ||
      (highlight.type === 'data_guidance' && step.stage === 'data_guidance') ||
      (highlight.type === 'selection' && step.stage === 'persona_selection') ||
      (highlight.type === 'handoff' && step.stage === 'handoff') ||
      (highlight.type === 'greeting' && step.stage === 'greeting')
    );

    if (matchingStep && highlight.timestamp) {
      const date = new Date(highlight.timestamp * 1000);
      matchingStep.timestamp = date.toLocaleTimeString();
    }
  });

  // Filter to only include steps that are relevant to the current workflow
  // (completed steps, the active step, and the next step)
  return allSteps.filter(step => {
    const stepIndex = stageOrder.indexOf(step.stage);
    return step.isCompleted || step.isActive || stepIndex === currentStageIndex + 1;
  });
};
