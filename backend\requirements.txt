# Core FastAPI and web framework dependencies
fastapi
uvicorn[standard]
pydantic
pydantic-settings
pydantic[email]

# Database dependencies
sqlalchemy
alembic
psycopg2-binary
asyncpg
psycopg2

# Authentication and security
PyJWT[crypto]
passlib[bcrypt]
python-multipart
cryptography
authlib
email-validator

# Redis for caching and sessions
redis
hiredis
python-redis

# HTTP client
httpx
aiohttp
requests

# Data processing
pandas
numpy
openpyxl
XlsxWriter
python-docx
PyPDF2

# AI and ML dependencies
openai
anthropic
groq
google-generativeai
langchain
langchain-core
langchain-groq
langchain-openai
langchain-google-genai
langchain-community
langchain-huggingface
langchain-ollama
transformers
torch
sentence-transformers
accelerate
huggingface-hub

# Vector database and embeddings
qdrant-client
chromadb

# Memory and knowledge management
mem0ai

# Data analysis and visualization
plotly
matplotlib
seaborn
scikit-learn
pandasai
kaleido
reportlab

# NLP and text processing
nltk
wordcloud
spacy
textblob

# File handling and utilities
python-magic
pillow

# Development and testing
pytest
pytest-asyncio
pytest-cov
pytest-mock
pytest-timeout
black
flake8
mypy

# Environment and configuration
python-dotenv
pyyaml

# Logging and monitoring
structlog
prometheus-client

# Date and time utilities
python-dateutil
croniter

# Email utilities
emails

# Background tasks
celery

# Image processing
opencv-python

# API documentation
sphinx
sphinx-rtd-theme

# Performance monitoring and optimization
psutil
memory-profiler

# Security enhancements
bandit
pip-audit  # Alternative to safety for vulnerability scanning
bcrypt

# Additional utilities
click
rich
typer

# Streaming responses
sse-starlette

# Workflow and orchestration
aiofiles

# Knowledge graph and entity processing
networkx
rdflib

# Advanced caching
diskcache
cachetools

# Data validation and serialization
marshmallow
jsonschema

# Async database support
databases[postgresql]

# Enhanced HTTP and API features
starlette
websockets

# Monitoring and observability
opentelemetry-api
opentelemetry-sdk

# Advanced security
argon2-cffi
pyotp

# Performance profiling
py-spy
line-profiler

# Advanced testing
locust
factory-boy
fake

# Enhanced file processing
#textract==1.6.1

# Web scraping and URL processing for auto-fill feature
requests
beautifulsoup4
lxml
html2text
urllib3
aiofiles

# Enhanced web scraping with Crawl4AI
crawl4ai

# Document processing for auto-fill
pypdf
python-docx
openpyxl

# Advanced data analysis
statsmodels
scipy

# Enhanced monitoring
influxdb-client

bleach