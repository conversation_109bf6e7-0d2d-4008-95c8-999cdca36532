import React, { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, X, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { FormItem, FormLabel, FormDescription, FormMessage } from '@/components/ui/form';

// Error boundary component for the dropdown
const DropdownErrorBoundary: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({
  children,
  fallback = <div className="p-4 text-sm text-muted-foreground">Unable to load topics</div>
}) => {
  try {
    return <>{children}</>;
  } catch (error) {
    console.error('BlogTopicSelector dropdown error:', error);
    return <>{fallback}</>;
  }
};

interface BlogTopicSelectorProps {
  value: string;
  onChange: (value: string) => void;
  suggestedTopics: string;
  className?: string;
}

export function BlogTopicSelector({
  value,
  onChange,
  suggestedTopics,
  className
}: BlogTopicSelectorProps) {
  const [open, setOpen] = useState(false);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [availableTopics, setAvailableTopics] = useState<string[]>([]);
  const [customTopic, setCustomTopic] = useState('');

  // Ensure arrays are always valid
  const safeSelectedTopics = Array.isArray(selectedTopics) ? selectedTopics : [];
  const safeAvailableTopics = Array.isArray(availableTopics) ? availableTopics : [];

  // Parse suggested topics into individual options
  useEffect(() => {
    try {
      console.log('BlogTopicSelector - Processing suggested topics:', { suggestedTopics, type: typeof suggestedTopics });
      if (suggestedTopics && typeof suggestedTopics === 'string') {
        let topics: string[] = [];

        // First, try to extract numbered list items with **bold** formatting
        const numberedBoldPattern = /\d+\.\s*\*\*(.*?)\*\*/g;
        let match: RegExpExecArray | null;
        while ((match = numberedBoldPattern.exec(suggestedTopics)) !== null) {
          const topic = match[1].trim();
          if (topic && topic.length > 0 && topic.length < 150) {
            topics.push(topic);
          }
        }

        // If no bold numbered items found, try regular numbered list
        if (topics.length === 0) {
          const numberedPattern = /^\s*\d+\.\s*(.+?)(?=\s*[-\n]|$)/gm;
          while ((match = numberedPattern.exec(suggestedTopics)) !== null) {
            let topic = match[1].trim();
            // Remove any trailing description after colon or dash
            topic = topic.split(/[:\-]/)[0].trim();
            // Remove markdown formatting
            topic = topic.replace(/\*\*(.*?)\*\*/g, '$1');
            topic = topic.replace(/\*(.*?)\*/g, '$1');
            topic = topic.replace(/`(.*?)`/g, '$1');

            if (topic && topic.length > 0 && topic.length < 150) {
              topics.push(topic);
            }
          }
        }

        // If still no topics found, try bullet points
        if (topics.length === 0) {
          const bulletPattern = /^\s*[-•*]\s*(.+?)(?=\s*[-\n]|$)/gm;
          while ((match = bulletPattern.exec(suggestedTopics)) !== null) {
            let topic = match[1].trim();
            topic = topic.split(/[:\-]/)[0].trim();
            topic = topic.replace(/\*\*(.*?)\*\*/g, '$1');
            topic = topic.replace(/\*(.*?)\*/g, '$1');

            if (topic && topic.length > 0 && topic.length < 150) {
              topics.push(topic);
            }
          }
        }

        // If still no topics, fall back to simple comma/newline split
        if (topics.length === 0) {
          topics = suggestedTopics
            .split(/[,\n;|]/)
            .map(topic => topic.trim())
            .filter(topic => topic.length > 0)
            .map(topic => topic.replace(/^[-•*]\s*/, ''))
            .map(topic => topic.replace(/^\d+\.\s*/, ''))
            .map(topic => topic.replace(/^["'`]|["'`]$/g, ''))
            .filter(topic => topic.length > 0 && topic.length < 150)
            .slice(0, 15);
        }

        // Clean up and deduplicate
        topics = topics
          .map(topic => topic.trim())
          .filter(topic => topic.length > 0)
          .slice(0, 15); // Limit to 15 topics max

        console.log('BlogTopicSelector - Parsed topics:', topics);
        setAvailableTopics([...new Set(topics)]); // Remove duplicates
      } else {
        console.log('BlogTopicSelector - No valid suggested topics, setting empty array');
        setAvailableTopics([]);
      }
    } catch (error) {
      console.error('Error parsing suggested topics:', error);
      setAvailableTopics([]);
    }
  }, [suggestedTopics]);

  // Parse current value into selected topics
  useEffect(() => {
    try {
      if (value && typeof value === 'string') {
        const topics = value
          .split(/[,\n;|]/) // Split by comma, newline, semicolon, or pipe
          .map(topic => topic.trim())
          .filter(topic => topic.length > 0);
        setSelectedTopics(topics);
      } else {
        setSelectedTopics([]);
      }
    } catch (error) {
      console.error('Error parsing current value:', error);
      setSelectedTopics([]);
    }
  }, [value]);

  // Update form value when selected topics change
  useEffect(() => {
    try {
      const safeTopics = Array.isArray(selectedTopics) ? selectedTopics : [];
      const newValue = safeTopics.join(', ');
      if (newValue !== value) {
        onChange(newValue);
      }
    } catch (error) {
      console.error('Error updating form value:', error);
    }
  }, [selectedTopics, onChange, value]);

  const handleTopicSelect = (topic: string) => {
    try {
      if (!topic || typeof topic !== 'string') return;

      if (safeSelectedTopics.includes(topic)) {
        // Remove topic
        setSelectedTopics(prev => (Array.isArray(prev) ? prev : []).filter(t => t !== topic));
      } else {
        // Add topic
        setSelectedTopics(prev => [...(Array.isArray(prev) ? prev : []), topic]);
      }
    } catch (error) {
      console.error('Error selecting topic:', error);
    }
  };

  const handleTopicRemove = (topicToRemove: string) => {
    try {
      if (!topicToRemove || typeof topicToRemove !== 'string') return;
      setSelectedTopics(prev => (Array.isArray(prev) ? prev : []).filter(topic => topic !== topicToRemove));
    } catch (error) {
      console.error('Error removing topic:', error);
    }
  };

  const handleCustomTopicAdd = () => {
    try {
      const trimmedTopic = customTopic?.trim();
      if (trimmedTopic && !safeSelectedTopics.includes(trimmedTopic)) {
        setSelectedTopics(prev => [...(Array.isArray(prev) ? prev : []), trimmedTopic]);
        setCustomTopic('');
      }
    } catch (error) {
      console.error('Error adding custom topic:', error);
    }
  };

  const handleCustomTopicKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleCustomTopicAdd();
    }
  };

  return (
    <FormItem className={className}>
      <FormLabel>Blog Topics</FormLabel>
      <div className="space-y-3">
        {/* Selected Topics Display */}
        {safeSelectedTopics.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {safeSelectedTopics.map((topic) => (
              <Badge key={topic} variant="secondary" className="text-sm">
                {topic}
                <button
                  type="button"
                  onClick={() => handleTopicRemove(topic)}
                  className="ml-2 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Topic Selector */}
        <Popover open={open} onOpenChange={(newOpen) => {
          try {
            setOpen(newOpen);
          } catch (error) {
            console.error('Error setting popover state:', error);
            setOpen(false);
          }
        }}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-full justify-between"
              onClick={() => {
                try {
                  setOpen(!open);
                } catch (error) {
                  console.error('Error toggling popover:', error);
                }
              }}
            >
              {safeSelectedTopics.length > 0
                ? `${safeSelectedTopics.length} topic${safeSelectedTopics.length > 1 ? 's' : ''} selected`
                : "Select blog topics..."}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <DropdownErrorBoundary>
              <Command>
                <CommandInput placeholder="Search topics..." />
                <CommandEmpty>No topics found.</CommandEmpty>
                <CommandList>
                {(() => {
                  try {
                    // Ensure arrays are valid and contain only strings
                    const validAvailableTopics = Array.isArray(safeAvailableTopics)
                      ? safeAvailableTopics.filter(topic => topic && typeof topic === 'string' && topic.trim().length > 0)
                      : [];
                    const validSelectedTopics = Array.isArray(safeSelectedTopics)
                      ? safeSelectedTopics.filter(topic => topic && typeof topic === 'string' && topic.trim().length > 0)
                      : [];

                    if (validAvailableTopics.length === 0) {
                      return null;
                    }

                    return (
                      <CommandGroup heading="Suggested Topics">
                        {/* Select All option */}
                        <CommandItem
                          onSelect={() => {
                            try {
                              const allSelected = validAvailableTopics.every(topic => validSelectedTopics.includes(topic));
                              if (allSelected) {
                                // Deselect all suggested topics
                                const newSelected = validSelectedTopics.filter(topic => !validAvailableTopics.includes(topic));
                                setSelectedTopics(newSelected);
                              } else {
                                // Select all suggested topics
                                const newSelected = [...new Set([...validSelectedTopics, ...validAvailableTopics])];
                                setSelectedTopics(newSelected);
                              }
                            } catch (error) {
                              console.error('Error in select all:', error);
                            }
                          }}
                          className="cursor-pointer font-medium border-b"
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              validAvailableTopics.length > 0 && validAvailableTopics.every(topic => validSelectedTopics.includes(topic)) ? "opacity-100" : "opacity-0"
                            )}
                          />
                          {validAvailableTopics.length > 0 && validAvailableTopics.every(topic => validSelectedTopics.includes(topic)) ? "Deselect All" : "Select All"}
                        </CommandItem>

                        {validAvailableTopics.map((topic, index) => (
                          <CommandItem
                            key={`topic-${index}-${topic}`}
                            onSelect={() => handleTopicSelect(topic)}
                            className="cursor-pointer"
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                validSelectedTopics.includes(topic) ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {topic}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    );
                  } catch (error) {
                    console.error('Error rendering suggested topics:', error);
                    return (
                      <CommandGroup heading="Error">
                        <CommandItem disabled>
                          Unable to load topics
                        </CommandItem>
                      </CommandGroup>
                    );
                  }
                })()}
              </CommandList>
            </Command>
            </DropdownErrorBoundary>
          </PopoverContent>
        </Popover>

        {/* Custom Topic Input */}
        <div className="flex gap-2">
          <Input
            placeholder="Add custom topic..."
            value={customTopic}
            onChange={(e) => setCustomTopic(e.target.value)}
            onKeyDown={handleCustomTopicKeyDown}
            className="flex-1"
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleCustomTopicAdd}
            disabled={!customTopic.trim() || safeSelectedTopics.includes(customTopic.trim())}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <FormDescription>
        {safeAvailableTopics.length > 0
          ? `${safeAvailableTopics.length} AI-suggested topics available. Select multiple topics or add your own.`
          : "Add custom blog topics or let AI suggest topics in the 'Suggested Topics' field above."
        }
      </FormDescription>
      <FormMessage />
    </FormItem>
  );
}
