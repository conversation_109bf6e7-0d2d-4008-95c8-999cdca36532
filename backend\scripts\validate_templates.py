#!/usr/bin/env python3
"""
Template Validation Script

This script validates the marketing templates YAML file to ensure
all templates have the correct structure and required fields.
"""

import os
import sys
import yaml
import logging
from typing import Dict, Any, List
from pathlib import Path

# Add backend to path for imports
backend_root = Path(__file__).parent.parent
sys.path.insert(0, str(backend_root))

from agents.tools.mcp.template_gallery_tools import template_repository

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TemplateValidator:
    """Validates marketing template YAML files."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.template_ids = set()
    
    def validate_required_fields(self, template: Dict[str, Any], template_index: int) -> bool:
        """Validate that all required fields are present."""
        required_fields = [
            'id', 'name', 'description', 'category', 'industry', 'business_size',
            'difficulty', 'time_estimate', 'action_type', 'preview', 'tags',
            'template_content', 'usage_count', 'rating', 'created_date', 'updated_date'
        ]
        
        is_valid = True
        for field in required_fields:
            if field not in template:
                self.errors.append(f"Template {template_index}: Missing required field '{field}'")
                is_valid = False
        
        return is_valid
    
    def validate_field_types(self, template: Dict[str, Any], template_index: int) -> bool:
        """Validate field types."""
        is_valid = True
        
        # String fields
        string_fields = ['id', 'name', 'description', 'category', 'industry', 
                        'business_size', 'difficulty', 'time_estimate', 'action_type', 
                        'preview', 'created_date', 'updated_date']
        
        for field in string_fields:
            if field in template and not isinstance(template[field], str):
                self.errors.append(f"Template {template_index}: Field '{field}' must be a string")
                is_valid = False
        
        # List fields
        if 'tags' in template and not isinstance(template['tags'], list):
            self.errors.append(f"Template {template_index}: Field 'tags' must be a list")
            is_valid = False
        
        # Dictionary fields
        if 'template_content' in template and not isinstance(template['template_content'], dict):
            self.errors.append(f"Template {template_index}: Field 'template_content' must be a dictionary")
            is_valid = False
        
        # Numeric fields
        if 'usage_count' in template and not isinstance(template['usage_count'], int):
            self.errors.append(f"Template {template_index}: Field 'usage_count' must be an integer")
            is_valid = False
        
        if 'rating' in template and not isinstance(template['rating'], (int, float)):
            self.errors.append(f"Template {template_index}: Field 'rating' must be a number")
            is_valid = False
        
        return is_valid
    
    def validate_enum_values(self, template: Dict[str, Any], template_index: int) -> bool:
        """Validate enumerated field values."""
        is_valid = True
        
        # Difficulty levels
        valid_difficulties = ['beginner', 'intermediate', 'advanced']
        if template.get('difficulty') not in valid_difficulties:
            self.errors.append(
                f"Template {template_index}: Invalid difficulty '{template.get('difficulty')}'. "
                f"Must be one of: {valid_difficulties}"
            )
            is_valid = False
        
        # Categories
        valid_categories = ['strategy', 'content', 'social', 'email', 'campaigns', 'research']
        if template.get('category') not in valid_categories:
            self.errors.append(
                f"Template {template_index}: Invalid category '{template.get('category')}'. "
                f"Must be one of: {valid_categories}"
            )
            is_valid = False
        
        # Business sizes
        valid_business_sizes = ['all', 'startup', 'small', 'medium', 'large', 'enterprise']
        if template.get('business_size') not in valid_business_sizes:
            self.errors.append(
                f"Template {template_index}: Invalid business_size '{template.get('business_size')}'. "
                f"Must be one of: {valid_business_sizes}"
            )
            is_valid = False
        
        return is_valid
    
    def validate_unique_ids(self, template: Dict[str, Any], template_index: int) -> bool:
        """Validate that template IDs are unique."""
        template_id = template.get('id')
        if not template_id:
            return False  # Already caught by required fields validation
        
        if template_id in self.template_ids:
            self.errors.append(f"Template {template_index}: Duplicate template ID '{template_id}'")
            return False
        
        self.template_ids.add(template_id)
        return True
    
    def validate_content_structure(self, template: Dict[str, Any], template_index: int) -> bool:
        """Validate template content structure."""
        is_valid = True
        template_content = template.get('template_content', {})
        
        # Recommended content fields
        recommended_fields = [
            'marketing_goals', 'suggested_topics', 'tone', 'target_audience', 'keywords'
        ]
        
        missing_recommended = []
        for field in recommended_fields:
            if field not in template_content:
                missing_recommended.append(field)
        
        if missing_recommended:
            self.warnings.append(
                f"Template {template_index}: Missing recommended content fields: {missing_recommended}"
            )
        
        # Validate specific content based on action type
        action_type = template.get('action_type', '')
        
        if action_type == 'social_media_content' and 'platforms' not in template_content:
            self.warnings.append(
                f"Template {template_index}: Social media templates should specify platforms"
            )
        
        if action_type == 'email_marketing' and 'timeline' not in template_content:
            self.warnings.append(
                f"Template {template_index}: Email marketing templates should specify timeline"
            )
        
        if action_type in ['blog_content', 'seo_optimization'] and 'keywords' not in template_content:
            self.warnings.append(
                f"Template {template_index}: SEO/blog templates should specify keywords"
            )
        
        return is_valid
    
    def validate_template(self, template: Dict[str, Any], template_index: int) -> bool:
        """Validate a single template."""
        is_valid = True
        
        # Run all validations
        is_valid &= self.validate_required_fields(template, template_index)
        is_valid &= self.validate_field_types(template, template_index)
        is_valid &= self.validate_enum_values(template, template_index)
        is_valid &= self.validate_unique_ids(template, template_index)
        is_valid &= self.validate_content_structure(template, template_index)
        
        return is_valid
    
    def validate_yaml_file(self, file_path: str) -> bool:
        """Validate the entire YAML file."""
        try:
            logger.info(f"Validating template file: {file_path}")
            
            # Load YAML file
            with open(file_path, 'r', encoding='utf-8') as f:
                templates_data = yaml.safe_load(f)
            
            if not isinstance(templates_data, list):
                self.errors.append("Template file must contain a list of templates")
                return False
            
            if not templates_data:
                self.errors.append("Template file is empty")
                return False
            
            # Validate each template
            all_valid = True
            for i, template in enumerate(templates_data):
                if not isinstance(template, dict):
                    self.errors.append(f"Template {i}: Must be a dictionary/object")
                    all_valid = False
                    continue
                
                template_valid = self.validate_template(template, i)
                all_valid &= template_valid
            
            return all_valid
            
        except yaml.YAMLError as e:
            self.errors.append(f"YAML parsing error: {e}")
            return False
        except FileNotFoundError:
            self.errors.append(f"Template file not found: {file_path}")
            return False
        except Exception as e:
            self.errors.append(f"Unexpected error: {e}")
            return False
    
    def print_results(self):
        """Print validation results."""
        if self.errors:
            logger.error(f"Found {len(self.errors)} validation errors:")
            for error in self.errors:
                logger.error(f"  ❌ {error}")
        
        if self.warnings:
            logger.warning(f"Found {len(self.warnings)} warnings:")
            for warning in self.warnings:
                logger.warning(f"  ⚠️  {warning}")
        
        if not self.errors and not self.warnings:
            logger.info("✅ All templates are valid!")
        elif not self.errors:
            logger.info("✅ All templates are valid (with warnings)")
        else:
            logger.error("❌ Template validation failed")


def main():
    """Main validation function."""
    # Get template file path
    templates_file = template_repository.templates_file_path
    
    if not os.path.exists(templates_file):
        logger.error(f"Template file not found: {templates_file}")
        logger.info("Creating default template file...")
        template_repository._ensure_templates_file_exists()
    
    # Validate templates
    validator = TemplateValidator()
    is_valid = validator.validate_yaml_file(templates_file)
    
    # Print results
    validator.print_results()
    
    # Test repository loading
    try:
        logger.info("Testing template repository loading...")
        templates = template_repository.load_templates()
        logger.info(f"Successfully loaded {len(templates)} templates")
        
        # Test filtering
        strategy_templates = template_repository.filter_templates(category="strategy")
        logger.info(f"Found {len(strategy_templates)} strategy templates")
        
        # Test search
        search_results = template_repository.filter_templates(search_term="email")
        logger.info(f"Found {len(search_results)} templates matching 'email'")
        
    except Exception as e:
        logger.error(f"Error testing template repository: {e}")
        is_valid = False
    
    # Exit with appropriate code
    sys.exit(0 if is_valid else 1)


if __name__ == "__main__":
    main()
