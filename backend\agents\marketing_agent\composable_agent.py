"""
Composable marketing agent for the Datagenius backend.

This module provides a composable implementation of the marketing agent
that uses the component-based architecture with MCP tools.
"""

import logging
import time
from typing import Dict, Any, Optional, List

from agents.composable import ComposableAgent
from agents.mixins import ToolCompletionMixin
from .components import (
    Marketing<PERSON>arserComponent,
    MCPContentGeneratorComponent
)
from .context_aware_greeting import ContextAwareGreetingGenerator
from agents.components.mcp_server import MCPServerComponent
from .config import get_config, MarketingAgentConfig
from .cache import initialize_cache, get_cache
from .memory_manager import initialize_memory_manager, get_memory_manager
from .monitoring import monitor, performance_monitor
from .analytics import initialize_analytics, get_analytics, EventType
from .exceptions import error_handler, MarketingAgentException

# Configure logging
logger = logging.getLogger(__name__)


class ComposableMarketingAgent(ToolCompletionMixin, ComposableAgent):
    """Composable implementation of the marketing agent using MCP tools."""

    def get_agent_type(self) -> str:
        """Return the agent type identifier."""
        return "marketing"

    def get_tool_indicators(self) -> List[str]:
        """Return list of context keys that indicate tool-triggered requests."""
        return ["marketing_form_data", "marketing_task", "marketing_content_request"]

    def get_conversational_flags(self) -> List[str]:
        """Return list of context keys that indicate conversational mode."""
        return [
            "skip_marketing_content_generation",
            "is_conversational",
            "content_generation_completed",
            "tool_completed",
            "auto_conversational_mode"
        ]

    def _get_agent_specific_new_request_patterns(self) -> List[str]:
        """Return agent-specific patterns that indicate new tool requests."""
        return [
            "create marketing", "generate content", "develop strategy",
            "marketing plan", "campaign ideas", "promotional content",
            "marketing strategy", "content creation", "brand messaging"
        ]

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the composable marketing agent with enhanced systems.

        Args:
            config: Configuration dictionary for the agent
        """
        logger.info("Initializing Enhanced Composable Marketing Agent with MCP tools")
        logger.info(f"Agent configuration: {config}")

        # Initialize context-aware greeting generator
        self.greeting_generator = ContextAwareGreetingGenerator()
        logger.info("Context-aware greeting generator initialized")

        # Load database configuration and merge with YAML config
        merged_config = await self._load_merged_configuration(config)

        # Initialize enhanced systems
        await self._initialize_enhanced_systems(merged_config)

        # Log available components in registry
        from agents.components.registry import ComponentRegistry
        available_components = ComponentRegistry.list_registered_components()
        logger.info(f"Available components in registry: {available_components}")

        # IMPORTANT: We're going to initialize our components directly instead of relying on the parent
        # This ensures we have the components we need regardless of registry state

        # Initialize our components list
        self.components = []

        # Check if components are configured in the YAML
        has_configured_components = "components" in config and len(config["components"]) > 0
        logger.info(f"Has configured components: {has_configured_components}")

        if has_configured_components:
            # Process components from configuration
            logger.info("Processing components from configuration")
            for component_config in config["components"]:
                component_type = component_config.get("type")
                component_name = component_config.get("name", component_type)

                logger.info(f"Processing component: type={component_type}, name={component_name}")

                if component_type == "conversation_history_manager":
                    # Skip conversation history manager - handled by orchestrator
                    logger.info(f"Skipping conversation_history_manager - handled by orchestrator")
                    continue

                elif component_type == "memory_manager":
                    # Skip memory manager - handled by enhanced systems
                    logger.info(f"Skipping memory_manager - handled by enhanced systems")
                    continue

                elif component_type == "marketing_parser":
                    # Create and initialize the parser component
                    parser_component = MarketingParserComponent()
                    await parser_component.initialize(component_config)
                    self.components.append(parser_component)
                    logger.info(f"Added marketing parser component: {parser_component.name}")

                elif component_type == "marketing_content_generator":
                    # Create and initialize the content generator component
                    generator_component = MCPContentGeneratorComponent()
                    await generator_component.initialize(component_config)
                    self.components.append(generator_component)
                    logger.info(f"Added marketing content generator component: {generator_component.name}")

                elif component_type == "mcp_server":
                    # Create and initialize the MCP server component
                    mcp_server_component = MCPServerComponent()
                    await mcp_server_component.initialize(component_config)
                    self.components.append(mcp_server_component)
                    logger.info(f"Added MCP server component: {mcp_server_component.name}")

                else:
                    logger.warning(f"Unknown component type: {component_type}")
        else:
            # If no components were configured, set up the default components
            logger.info("No components configured, setting up default marketing components")

            # Create and initialize the parser component
            parser_component = MarketingParserComponent()
            await parser_component.initialize({
                "name": "marketing_parser"
            })
            self.components.append(parser_component)
            logger.info(f"Added default marketing parser component: {parser_component.name}")

            # Create and initialize the MCP server component with essential tools
            from agents.components import create_mcp_server_with_essential_tools

            # Create MCP server with enhanced data access and marketing tools
            mcp_server_component = await create_mcp_server_with_essential_tools({
                "name": "marketing_tools",
                "server_name": "datagenius-marketing-tools",
                "server_version": "1.0.0",
                "tools": [
                    # Enhanced data access tool (supports all file types)
                    {"type": "data_access"},
                    # Marketing content generation
                    {
                        "type": "generate_marketing_content",
                        "prompt_templates": {
                            "marketing_strategy": config.get("system_prompts", {}).get("marketing_strategy", ""),
                            "social_media": config.get("system_prompts", {}).get("social_media", "")
                        }
                    },
                    # Conversation tool for natural responses
                    {"type": "handle_conversation"},
                    # Language detection tool
                    {"type": "detect_language"},
                    # Intent detection tool
                    {"type": "detect_intent"},
                    # Enhanced marketing content tools
                    {"type": "generate_blog_content"},
                    {"type": "generate_email_marketing"},
                    {"type": "generate_ad_copy"},
                    {"type": "generate_press_release"},
                    # Analysis and research tools
                    {"type": "generate_competitor_analysis"},
                    {"type": "generate_audience_research"},
                    {"type": "generate_market_analysis"},
                    # Template and setup tools
                    {"type": "browse_template_gallery"},
                    {"type": "business_setup_guide"},
                    {"type": "show_marketing_examples"},
                    # Business context detection
                    {"type": "detect_business_context"}
                ]
            })
            self.components.append(mcp_server_component)
            logger.info(f"Added default MCP server component: {mcp_server_component.name}")

            # Create and initialize the content generator component
            generator_component = MCPContentGeneratorComponent()
            await generator_component.initialize({
                "name": "content_generator"
            })
            self.components.append(generator_component)
            logger.info(f"Added default marketing content generator component: {generator_component.name}")

        logger.info(f"Initialized {len(self.components)} marketing components")

    async def _initialize_enhanced_systems(self, config: Dict[str, Any]) -> None:
        """Initialize enhanced systems for the marketing agent."""
        try:
            # Initialize configuration management
            agent_config = get_config()
            logger.info("Configuration management initialized")

            # Initialize cache system
            cache_config = {
                'backend': config.get('cache', {}).get('backend', 'memory'),
                'max_size': config.get('cache', {}).get('max_size', 1000),
                'ttl': config.get('cache', {}).get('ttl', 3600),
                'redis_url': config.get('cache', {}).get('redis_url')
            }

            cache = initialize_cache(cache_config)
            logger.info(f"Cache system initialized with {cache_config['backend']} backend")

            # Initialize memory manager
            memory_config = {
                'max_memory_mb': config.get('memory', {}).get('max_memory_mb', 1024),
                'conversation_timeout': config.get('memory', {}).get('conversation_timeout', 1800),
                'cleanup_interval': config.get('memory', {}).get('cleanup_interval', 300),
                'max_conversations': config.get('memory', {}).get('max_conversations', 1000)
            }

            memory_manager = initialize_memory_manager(memory_config)
            await memory_manager.start()
            logger.info("Memory manager initialized and started")

            # Initialize analytics system
            analytics_config = {
                'buffer_size': config.get('analytics', {}).get('buffer_size', 1000),
                'flush_interval': config.get('analytics', {}).get('flush_interval', 300),
                'enable_persistence': config.get('analytics', {}).get('enable_persistence', True),
                'storage_path': config.get('analytics', {}).get('storage_path')
            }

            analytics = initialize_analytics(analytics_config)
            await analytics.start()
            logger.info("Analytics system initialized and started")

            # Start performance monitoring
            if config.get('monitoring', {}).get('enabled', True):
                monitor.start_monitoring(
                    interval=config.get('monitoring', {}).get('interval', 30.0)
                )
                logger.info("Performance monitoring started")

            # Store references for cleanup
            self._enhanced_systems = {
                'config': agent_config,
                'cache': cache,
                'memory_manager': memory_manager,
                'analytics': analytics,
                'monitor': monitor
            }

        except Exception as e:
            logger.error(f"Error initializing enhanced systems: {e}")
            # Continue with basic functionality if enhanced systems fail
            self._enhanced_systems = {}

    async def generate_context_aware_greeting(self, context: Dict[str, Any]) -> str:
        """
        Generate a context-aware greeting based on user information and interaction history.

        Args:
            context: User context including conversation history, data sources, etc.

        Returns:
            Personalized greeting message
        """
        try:
            if hasattr(self, 'greeting_generator'):
                return await self.greeting_generator.generate_greeting(context)
            else:
                # Fallback to basic greeting if generator not initialized
                return self._get_basic_greeting(context)
        except Exception as e:
            logger.error(f"Error generating context-aware greeting: {e}")
            return self._get_basic_greeting(context)

    def _get_basic_greeting(self, context: Dict[str, Any]) -> str:
        """Get basic greeting as fallback."""
        has_data = context.get("has_data_source", False)
        if has_data:
            return """Hello! I'm your Composable Marketer. I see you have data uploaded - perfect! I can create personalized marketing strategies and content based on your information.

[📊 Marketing Strategy](action:marketing_strategy) [🎯 Campaign Plan](action:campaign_strategy) [📱 Social Media](action:social_media_content)

What marketing goal can I help you achieve today?"""
        else:
            return """Hello! I'm your Composable Marketer, ready to help you create professional marketing content that drives results.

[📊 Marketing Strategy](action:marketing_strategy) [🎯 Campaign Plan](action:campaign_strategy) [📱 Social Media](action:social_media_content)

What marketing challenge can I help you tackle today?"""

    async def _load_merged_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Load and merge YAML configuration with database configuration.

        Args:
            config: YAML configuration dictionary

        Returns:
            Merged configuration with database settings taking priority
        """
        try:
            # Import the centralized configuration utilities
            from ..utils.model_init import load_agent_database_config, merge_agent_config

            # Load database configuration for this agent
            db_config = await load_agent_database_config("composable-marketing-ai")

            # Merge YAML config with database config (database takes priority)
            merged_config = merge_agent_config(config, db_config, "composable-marketing-ai")

            return merged_config

        except Exception as e:
            logger.error(f"Error loading database configuration for marketing agent: {e}")
            # Fall back to original config
            return config

    @performance_monitor("process_message")
    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message using the agent's enhanced components and systems.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """
        start_time = time.time()
        session_id = context.get('session_id') if context else None

        try:
            logger.info(f"Processing message for user {user_id} in conversation {conversation_id}")
            logger.info(f"Message: {message[:50]}...")
            logger.info(f"Number of components available: {len(self.components)}")

            # Track user message event
            analytics = get_analytics()
            if analytics:
                analytics.track_user_message(
                    message=message,
                    user_id=str(user_id),
                    conversation_id=conversation_id,
                    session_id=session_id,
                    metadata=context
                )

            # Register/update conversation in memory manager
            memory_manager = get_memory_manager()
            if memory_manager:
                await memory_manager.register_conversation(
                    conversation_id,
                    str(user_id),
                    context
                )

            if not self.components:
                logger.error("No components available for processing message")
                # Generate LLM error response for configuration issue
                error_ctx = {"message": message, "error_type": "no_components"}
                await self._generate_error_response(error_ctx, "No components configured")
                return {
                    "message": error_ctx.get("response", "This agent has no components configured. Please check the agent configuration."),
                    "metadata": {
                        "error": "no_components_configured"
                    }
                }

            # Log the components that will be used
            for i, component in enumerate(self.components):
                logger.info(f"Component {i+1}: {component.name} ({component.__class__.__name__})")

            # Create initial context
            ctx = {
                "user_id": user_id,
                "message": message,
                "conversation_id": conversation_id,
                "context": context or {},
                "agent_config": self.config,
                "agent_components": self.components,
                "response": "",
                "metadata": {}
                # Removed is_first_message logic, now handled by queue task type
            }

            # Extract marketing_form_data from nested context and promote it to top level
            if context and "marketing_form_data" in context:
                ctx["marketing_form_data"] = context["marketing_form_data"]
                logger.info(f"Promoted marketing_form_data to top level: {ctx['marketing_form_data']}")

            # Also promote provider and model to top level if they exist
            if context and "provider" in context:
                ctx["provider"] = context["provider"]
                logger.info(f"Promoted provider to top level: {ctx['provider']}")

            if context and "model" in context:
                ctx["model"] = context["model"]
                logger.info(f"Promoted model to top level: {ctx['model']}")

            # Promote data_source to top level if it exists
            if context and "data_source" in context:
                ctx["data_source"] = context["data_source"]
                logger.info(f"Promoted data_source to top level")

            # Promote data_sources to top level if it exists
            if context and "data_sources" in context:
                ctx["data_sources"] = context["data_sources"]
                logger.info(f"Promoted data_sources to top level")

            # Promote conversation_history to top level if it exists
            if context and "conversation_history" in context:
                ctx["conversation_history"] = context["conversation_history"]
                logger.info(f"Promoted conversation_history to top level with {len(context['conversation_history'])} messages")

            # Check if we need to process a file for the persona (only for initial file uploads, not follow-up questions)
            # For marketing agent, we should only process files if this is an initial upload with no meaningful message
            # Follow-up questions should use conversation context instead of reprocessing files
            should_process_file = (
                context and
                context.get("send_file_to_persona") and
                context.get("data_source") and
                (not message or message.strip() == "" or len(message.strip()) < 10)  # Only for initial uploads or very short messages
            )

            if should_process_file:
                logger.info("Processing file for initial persona introduction (send_file_to_persona is true)")
                # Find MCP server component
                mcp_server = None
                for component in self.components:
                    if component.__class__.__name__ == "MCPServerComponent":
                        mcp_server = component
                        break

                if mcp_server:
                    try:
                        data_source = context.get("data_source")
                        logger.info(f"Processing file for marketing agent: {data_source}")

                        # Use enhanced data access tool to detect content type and process accordingly
                        content_type_result = await mcp_server.call_tool("data_access", {
                            "operation": "detect_content_type",
                            "data_source": data_source
                        })

                        if not content_type_result.get("isError", False):
                            content_info = content_type_result.get("metadata", {})
                            logger.info(f"Content type detected: {content_info}")

                            # Process based on content type
                            if content_info.get("has_text"):
                                # Extract text and entities for marketing insights
                                logger.info("Processing text content for marketing insights")

                                # Extract text content
                                text_result = await mcp_server.call_tool("data_access", {
                                    "operation": "extract_text",
                                    "data_source": data_source
                                })

                                # Extract entities for marketing targeting
                                entities_result = await mcp_server.call_tool("data_access", {
                                    "operation": "extract_entities",
                                    "data_source": data_source
                                })

                                # Generate LLM response for text content analysis
                                await self._generate_file_analysis_response(
                                    ctx, mcp_server, "text",
                                    text_result, entities_result
                                )
                                ctx["metadata"]["file_processed"] = True
                                ctx["metadata"]["content_type"] = "text"

                                # Extract entities for metadata
                                entities = []
                                if not entities_result.get("isError", False):
                                    entities = entities_result.get("metadata", {}).get("entities", [])
                                ctx["metadata"]["entities"] = entities[:10] if entities else []

                            elif content_info.get("is_structured"):
                                # Process structured data for marketing analytics
                                logger.info("Processing structured data for marketing analytics")

                                # Get data overview
                                info_result = await mcp_server.call_tool("data_access", {
                                    "operation": "info",
                                    "data_source": data_source
                                })

                                # Get sample data
                                sample_result = await mcp_server.call_tool("data_access", {
                                    "operation": "head",
                                    "data_source": data_source,
                                    "params": {"n": 5}
                                })

                                # Generate LLM response for structured data analysis
                                await self._generate_file_analysis_response(
                                    ctx, mcp_server, "structured",
                                    info_result, sample_result
                                )
                                ctx["metadata"]["file_processed"] = True
                                ctx["metadata"]["content_type"] = "structured"

                                # Extract data shape for metadata
                                shape = None
                                if not info_result.get("isError", False):
                                    info_metadata = info_result.get("metadata", {})
                                    shape = info_metadata.get("shape", [0, 0])
                                ctx["metadata"]["data_shape"] = shape

                            else:
                                # Fallback for unknown content types
                                logger.info("Unknown content type, generating LLM response")
                                await self._generate_file_analysis_response(
                                    ctx, mcp_server, "unknown", None, None
                                )
                                ctx["metadata"]["file_processed"] = True
                                ctx["metadata"]["content_type"] = "unknown"

                        else:
                            # Error detecting content type
                            logger.warning("Failed to detect content type, generating LLM response")
                            await self._generate_file_analysis_response(
                                ctx, mcp_server, "error", None, None
                            )
                            ctx["metadata"]["file_processed"] = True
                            ctx["metadata"]["error"] = "content_type_detection_failed"

                    except Exception as e:
                        logger.error(f"Error processing file: {e}", exc_info=True)
                        await self._generate_file_analysis_response(
                            ctx, mcp_server, "error", None, None
                        )
                        ctx["metadata"]["file_processed"] = True
                        ctx["metadata"]["error_details"] = str(e)
            else:
                # For follow-up questions, check if we have file context from conversation history
                logger.info("Checking for file context in follow-up message")

                # Look for data source in current context or conversation history
                data_source = context.get("data_source")
                marketing_form_data = context.get("marketing_form_data")

                if not data_source:
                    # Check conversation history for file context
                    conversation_history = context.get("conversation_history", [])
                    for msg in reversed(conversation_history):
                        msg_metadata = msg.get("metadata", {})
                        if msg_metadata.get("data_source"):
                            data_source = msg_metadata["data_source"]
                            if msg_metadata.get("marketing_form_data"):
                                marketing_form_data = msg_metadata["marketing_form_data"]
                            logger.info(f"Found file context from conversation history: {data_source}")
                            break

                # If we have file context and this is a meaningful follow-up question,
                # add file context to the processing context
                if data_source and message and len(message.strip()) > 10:
                    logger.info(f"Adding file context to follow-up message processing: {data_source}")
                    ctx["data_source"] = data_source
                    if marketing_form_data:
                        ctx["marketing_form_data"] = marketing_form_data
                        logger.info("Added marketing form data to context for follow-up processing")

                # Log that we're proceeding with normal conversation flow
                if context and context.get("send_file_to_persona"):
                    logger.info("Skipping initial file processing for follow-up question - using conversation context instead")
                    # Clear the send_file_to_persona flag to prevent future attempts
                    context["send_file_to_persona"] = False

            # Process context through each component
            # This loop now handles all non-initiation messages
            logger.info("Processing message through component chain.")
            for component in self.components:
                try:
                    logger.info(f"Processing with component: {component.name}")
                    logger.info(f"BEFORE PROCESSING - ctx type: {type(ctx)}")
                    logger.info(f"BEFORE PROCESSING - component.process method: {type(component.process)}")

                    # Check if component.process is async
                    import inspect
                    if inspect.iscoroutinefunction(component.process):
                        logger.info(f"Component {component.name}.process is async - awaiting")
                        ctx = await component.process(ctx)
                    else:
                        logger.info(f"Component {component.name}.process is sync - calling directly")
                        ctx = component.process(ctx)

                    logger.info(f"AFTER PROCESSING - ctx type: {type(ctx)}")

                    # Validate ctx is a dictionary before accessing
                    if not isinstance(ctx, dict):
                        logger.error(f"Component {component.name} returned {type(ctx)} instead of dict")
                        raise ValueError(f"Component {component.name} returned invalid context type: {type(ctx)}")

                    logger.info(f"After component {component.name}, response length: {len(ctx.get('response', ''))}")
                except Exception as e:
                    logger.error(f"Error in component {component.name}: {str(e)}", exc_info=True)
                    logger.error(f"ERROR HANDLING - ctx type: {type(ctx)}")

                    # Handle case where ctx might be a coroutine
                    if inspect.iscoroutine(ctx):
                        logger.error(f"ctx is a coroutine! Attempting to await it...")
                        try:
                            ctx = await ctx
                            logger.info(f"Successfully awaited ctx, new type: {type(ctx)}")
                        except Exception as await_error:
                            logger.error(f"Failed to await ctx: {await_error}")
                            # Create fallback context
                            ctx = {
                                "user_id": user_id,
                                "message": message,
                                "conversation_id": conversation_id,
                                "context": context or {},
                                "response": "",
                                "metadata": {"error": f"Component {component.name} processing failed"}
                            }

                    # Ensure ctx is a dictionary
                    if not isinstance(ctx, dict):
                        logger.error(f"ctx is not a dict after error handling, creating fallback")
                        ctx = {
                            "user_id": user_id,
                            "message": message,
                            "conversation_id": conversation_id,
                            "context": context or {},
                            "response": "",
                            "metadata": {"error": f"Component {component.name} returned invalid type"}
                        }

                    # Initialize metadata if it doesn't exist
                    if "metadata" not in ctx:
                        ctx["metadata"] = {}

                    # Initialize errors array if it doesn't exist
                    if "errors" not in ctx["metadata"]:
                        ctx["metadata"]["errors"] = []

                    # Add the error to the errors array
                    ctx["metadata"]["errors"].append({
                        "component": component.name,
                        "error": str(e),
                        "error_type": e.__class__.__name__
                    })

                    # Generate LLM error response
                    await self._generate_error_response(ctx, str(e))
                    ctx["metadata"]["error"] = "component_error"
                    ctx["metadata"]["error_details"] = str(e)

            # Handle LLM conversational responses
            if ctx.get("use_llm_for_response", False) and not ctx.get("response"):
                logger.info("Using LLM for conversational response")
                try:
                    # Find MCP server component
                    mcp_server = None
                    for component in self.components:
                        if component.__class__.__name__ == "MCPServerComponent":
                            mcp_server = component
                            break

                    if mcp_server:
                        # Use conversation tool for natural response with marketing expertise
                        conversation_history = ctx.get("conversation_history", [])

                        # Enhanced user context for marketing conversations
                        user_context = {
                            "is_marketing_agent": True,
                            "persona": "marketing",
                            "conversational_response": True,
                            "marketing_expertise": True,
                            "is_follow_up_question": ctx.get("is_follow_up_question", False),
                            "marketing_conversation_context": ctx.get("marketing_conversation_context", False)
                        }

                        # Create agent context using the dynamic system
                        from agents.utils import create_agent_context

                        enhanced_user_context = create_agent_context(
                            agent_id="composable-marketing-ai",
                            additional_context={
                                **user_context,
                                "is_marketing_agent": True  # Backward compatibility
                            }
                        )

                        tool_result = await mcp_server.call_tool("handle_conversation", {
                            "message": message,
                            "conversation_history": conversation_history,
                            "user_context": enhanced_user_context,
                            "intent_type": "marketing_advice" if ctx.get("is_follow_up_question") else "general_question",
                            "confidence": 0.9 if ctx.get("is_follow_up_question") else 0.8,
                            "is_continuing_conversation": len(conversation_history) > 0,
                            "provider": self.config.get("provider", "groq"),
                            "model": self.config.get("model", "llama3-70b-8192"),
                            "temperature": 0.7
                        })

                        if not tool_result.get("isError", False):
                            ctx["response"] = tool_result.get("content", [{}])[0].get("text", "")
                            logger.info("Successfully generated LLM conversational response")
                        else:
                            logger.error(f"Error in conversation tool: {tool_result}")
                            await self._generate_fallback_response(ctx, "conversation_tool_error")
                    else:
                        logger.warning("No MCP server component found for LLM response")
                        await self._generate_fallback_response(ctx, "no_mcp_server")

                except Exception as e:
                    logger.error(f"Error generating LLM response: {str(e)}", exc_info=True)
                    await self._generate_fallback_response(ctx, "llm_error")

            # Ensure we have a response
            if not ctx.get("response"):
                logger.warning("No response generated by components")
                await self._generate_fallback_response(ctx, "no_response")

            logger.info(f"Final response length: {len(ctx.get('response', ''))}")

            # Track agent response
            processing_time = time.time() - start_time
            if analytics:
                analytics.track_agent_response(
                    response={"content": ctx.get("response", ""), "metadata": ctx.get("metadata", {})},
                    user_id=str(user_id),
                    conversation_id=conversation_id,
                    session_id=session_id,
                    processing_time=processing_time
                )

            # Update conversation metrics
            if memory_manager:
                await memory_manager.update_conversation(
                    conversation_id,
                    context_size=len(str(ctx).encode('utf-8'))
                )

            # Get the response and metadata
            response = ctx.get("response", "")
            metadata = ctx.get("metadata", {})

            # Ensure the response is included in the metadata for visualization
            if response and "generated_content" in metadata and metadata.get("generated_content") == True:
                metadata["response"] = response
                metadata["content"] = response

            # Add diagnostic information to help troubleshoot issues
            if "errors" in metadata and metadata["errors"]:
                logger.error(f"Errors occurred during processing: {metadata['errors']}")

            # Log the final response and metadata for debugging
            logger.info(f"Final response: {response[:100]}...")
            logger.info(f"Final metadata keys: {list(metadata.keys())}")

            if "error" in metadata:
                logger.error(f"Error in metadata: {metadata['error']}")
                if "error_details" in metadata:
                    logger.error(f"Error details: {metadata['error_details']}")

            return {
                "message": response,
                "metadata": metadata
            }
        except Exception as e:
            # Catch any uncaught exceptions in the process_message method
            logger.error(f"Uncaught exception in process_message: {str(e)}", exc_info=True)

            # Handle error with enhanced error handling
            try:
                error_response = error_handler.handle_exception(e, {
                    'user_id': str(user_id),
                    'conversation_id': conversation_id,
                    'message': message,
                    'processing_time': time.time() - start_time
                })

                # Track error event
                if analytics:
                    analytics.track_error(
                        error=e,
                        user_id=str(user_id),
                        conversation_id=conversation_id,
                        session_id=session_id,
                        context={'message': message}
                    )

                return error_response

            except Exception as error_handling_error:
                logger.error(f"Error in error handling: {error_handling_error}")

                # Generate appropriate error response using LLM if possible
                error_type = "CONNECTION_ERROR" if ("connection" in str(e).lower() or "timeout" in str(e).lower()) else "UNEXPECTED_ERROR"

                # Try to generate LLM error response
                try:
                    error_ctx = {"message": message, "error_type": error_type, "error_details": str(e)}
                    await self._generate_error_response(error_ctx, str(e))
                    error_message = error_ctx.get("response", "I encountered an error while processing your message. Please try again.")
                except:
                    # Final fallback if LLM error generation fails
                    if error_type == "CONNECTION_ERROR":
                        error_message = "I'm having trouble connecting to the AI provider. Please try again in a moment."
                    else:
                        error_message = "I encountered an unexpected error while processing your message. Please try again."

                return {
                    "message": error_message,
                    "metadata": {
                        "error": True,
                        "error_code": error_type,
                        "timestamp": time.time()
                    }
                }

    async def _generate_file_analysis_response(self, ctx: Dict[str, Any], mcp_server, content_type: str,
                                             primary_result: Dict[str, Any] = None,
                                             secondary_result: Dict[str, Any] = None):
        """Generate LLM response for file analysis based on content type."""
        try:
            # Create configuration for file analysis response
            analysis_config = {
                "task": "file_analysis_response",
                "input": {
                    "content_type": content_type,
                    "primary_result": primary_result,
                    "secondary_result": secondary_result
                },
                "response_requirements": {
                    "tone": "professional_friendly",
                    "include_analysis_summary": True,
                    "include_capability_overview": True,
                    "include_next_steps_question": True,
                    "use_emojis": True,
                    "be_encouraging": True
                },
                "content_type_specifics": {
                    "text": {
                        "highlight": ["document_analysis", "entity_extraction", "content_themes"],
                        "capabilities": ["marketing_strategy", "campaign_content", "social_media", "seo_content"]
                    },
                    "structured": {
                        "highlight": ["data_overview", "key_fields", "analytics_potential"],
                        "capabilities": ["data_driven_strategy", "performance_campaigns", "audience_segmentation", "social_media_with_data"]
                    },
                    "unknown": {
                        "highlight": ["file_uploaded", "ready_to_help"],
                        "capabilities": ["marketing_strategy", "campaign_planning", "social_media", "seo_optimization"]
                    },
                    "error": {
                        "highlight": ["file_received", "technical_issue", "still_ready_to_help"],
                        "capabilities": ["marketing_strategy", "campaign_planning", "social_media", "seo_optimization"]
                    }
                }
            }

            # Create a concise prompt instead of large YAML dump
            prompt = f"""Generate a marketing assistant response for file analysis of {content_type} content.

Acknowledge the file upload, provide relevant analysis insights, and guide the user toward next steps. Be encouraging and showcase marketing expertise."""

            # Generate response using LLM
            tool_result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "file_analysis_response",
                    "content_type": content_type,
                    "analysis_config": analysis_config,  # Move config to user_context
                    "primary_result": primary_result,
                    "secondary_result": secondary_result
                },
                "intent_type": "file_analysis",
                "confidence": 1.0,
                "temperature": 0.7,
                "max_tokens": 400
            })

            if not tool_result.get("isError", False):
                response_text = tool_result.get("content", [{}])[0].get("text", "")
                if response_text.strip():
                    ctx["response"] = response_text.strip()
                    return

        except Exception as e:
            logger.error(f"Error generating LLM file analysis response: {e}")

        # Generate basic fallback response if LLM fails
        await self._generate_basic_file_response(ctx, content_type)

    async def _generate_error_response(self, ctx: Dict[str, Any], error_details: str):
        """Generate LLM response for error scenarios."""
        try:
            # Find MCP server component
            mcp_server = None
            for component in self.components:
                if hasattr(component, 'call_tool'):
                    mcp_server = component
                    break

            if mcp_server:
                import yaml

                error_config = {
                    "task": "error_response_generation",
                    "input": {
                        "error_type": ctx.get("error_type", "general"),
                        "error_details": error_details,
                        "user_message": ctx.get("message", "")
                    },
                    "response_requirements": {
                        "tone": "apologetic_helpful",
                        "acknowledge_issue": True,
                        "provide_reassurance": True,
                        "suggest_alternatives": True,
                        "maintain_marketing_focus": True,
                        "be_professional": True
                    },
                    "error_type_specifics": {
                        "component_error": "Technical issue with processing",
                        "no_components": "Configuration issue",
                        "connection_error": "AI provider connectivity issue",
                        "llm_error": "AI response generation issue",
                        "no_response": "Processing completed but no response generated"
                    }
                }

                prompt = f"""Generate a helpful error response for a marketing assistant according to this YAML configuration:

{yaml.dump(error_config, default_flow_style=False, allow_unicode=True)}

Create a professional, apologetic response that acknowledges the issue while maintaining confidence in the marketing assistant's capabilities."""

                tool_result = await mcp_server.call_tool("handle_conversation", {
                    "message": prompt,
                    "conversation_history": [],
                    "user_context": {
                        "task": "error_response",
                        "error_type": ctx.get("error_type", "general")
                    },
                    "intent_type": "error_handling",
                    "confidence": 1.0,
                    "temperature": 0.6,
                    "max_tokens": 200
                })

                if not tool_result.get("isError", False):
                    response_text = tool_result.get("content", [{}])[0].get("text", "")
                    if response_text.strip():
                        ctx["response"] = response_text.strip()
                        return

        except Exception as e:
            logger.error(f"Error generating LLM error response: {e}")

        # Fallback error response
        ctx["response"] = "I apologize, but I encountered an issue while processing your request. Please try again, and I'll do my best to help you with your marketing needs."

    async def _generate_fallback_response(self, ctx: Dict[str, Any], scenario: str):
        """Generate LLM fallback response for various scenarios."""
        try:
            # Find MCP server component
            mcp_server = None
            for component in self.components:
                if hasattr(component, 'call_tool'):
                    mcp_server = component
                    break

            if mcp_server:
                fallback_config = {
                    "task": "fallback_response_generation",
                    "input": {
                        "scenario": scenario,
                        "user_message": ctx.get("message", "")
                    },
                    "response_requirements": {
                        "tone": "helpful_professional",
                        "maintain_marketing_expertise": True,
                        "offer_assistance": True,
                        "be_encouraging": True,
                        "suggest_next_steps": True
                    },
                    "scenario_specifics": {
                        "conversation_tool_error": "AI conversation tool failed",
                        "no_mcp_server": "No AI server available",
                        "llm_error": "AI response generation failed",
                        "no_response": "No response was generated"
                    }
                }

                # Create a concise prompt instead of large YAML dump
                prompt = f"""Generate a helpful fallback response for a marketing assistant.

Scenario: {scenario}
User message: {ctx.get("message", "")}

Create a professional response that maintains the marketing assistant's helpful persona while addressing the technical issue gracefully."""

                tool_result = await mcp_server.call_tool("handle_conversation", {
                    "message": prompt,
                    "conversation_history": [],
                    "user_context": {
                        "task": "fallback_response",
                        "scenario": scenario,
                        "fallback_config": fallback_config  # Move config to user_context
                    },
                    "intent_type": "fallback_handling",
                    "confidence": 1.0,
                    "temperature": 0.6,
                    "max_tokens": 150
                })

                if not tool_result.get("isError", False):
                    response_text = tool_result.get("content", [{}])[0].get("text", "")
                    if response_text.strip():
                        ctx["response"] = response_text.strip()
                        return

        except Exception as e:
            logger.error(f"Error generating LLM fallback response: {e}")

        # Generate basic fallback response
        await self._generate_basic_fallback_response(ctx, "final_fallback")

    async def _generate_basic_file_response(self, ctx: Dict[str, Any], content_type: str):
        """Generate basic file response when LLM is not available."""
        file_responses = {
            "text": "I've analyzed your document and I'm ready to help create marketing content! What type of marketing content would you like me to create?",
            "structured": "I've analyzed your data file and I'm ready to help create data-driven marketing content! What marketing content would you like me to create from your data?",
            "unknown": "I can see you've uploaded a file. I'm ready to help you create marketing content! What type of marketing content would you like me to help you create?",
            "error": "I can see you've uploaded a file. I'm ready to help you create marketing content! What type of marketing content would you like me to help you create?"
        }

        ctx["response"] = file_responses.get(content_type, file_responses["unknown"])

    async def _generate_basic_fallback_response(self, ctx: Dict[str, Any], scenario: str):
        """Generate basic fallback responses when LLM is not available."""
        fallback_responses = {
            "final_fallback": "I'm here to help with your marketing needs. How can I assist you today?",
            "no_mcp_server": "I'm experiencing technical difficulties. Please try again in a moment.",
            "llm_error": "I'm having trouble generating a response right now. Please try again.",
            "conversation_tool_error": "I'm here to help with your marketing needs. What would you like to work on?",
            "no_response": "I'm sorry, I wasn't able to process your request properly."
        }

        ctx["response"] = fallback_responses.get(scenario, "I'm here to help with your marketing needs. How can I assist you today?")

    async def cleanup(self) -> None:
        """Cleanup enhanced systems when agent is destroyed."""
        try:
            if hasattr(self, '_enhanced_systems'):
                # Stop memory manager
                memory_manager = self._enhanced_systems.get('memory_manager')
                if memory_manager:
                    await memory_manager.stop()

                # Stop analytics
                analytics = self._enhanced_systems.get('analytics')
                if analytics:
                    await analytics.stop()

                # Stop monitoring
                monitor_instance = self._enhanced_systems.get('monitor')
                if monitor_instance:
                    monitor_instance.stop_monitoring()

                logger.info("Enhanced systems cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def __del__(self):
        """Destructor to ensure cleanup."""
        try:
            import asyncio
            if hasattr(self, '_enhanced_systems'):
                # Try to run cleanup if event loop is available
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        asyncio.create_task(self.cleanup())
                    else:
                        loop.run_until_complete(self.cleanup())
                except RuntimeError:
                    # No event loop available, skip async cleanup
                    pass
        except Exception:
            # Ignore errors in destructor
            pass
