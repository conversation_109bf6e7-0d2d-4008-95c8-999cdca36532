#!/bin/bash
# <PERSON>ript to create an admin user for Datagenius

echo "Datagenius Admin User Creation Tool"
echo "==================================="
echo

read -p "Enter admin email: " EMAIL
read -s -p "Enter admin password: " PASSWORD
echo
read -p "Enter username (optional, press Enter to skip): " USERNAME
read -p "Enter first name (optional, press Enter to skip): " FIRSTNAME
read -p "Enter last name (optional, press Enter to skip): " LASTNAME

echo
echo "Creating admin user..."

CMD="python create_admin.py --email \"$EMAIL\" --password \"$PASSWORD\""

if [ ! -z "$USERNAME" ]; then
    CMD="$CMD --username \"$USERNAME\""
fi

if [ ! -z "$FIRSTNAME" ]; then
    CMD="$CMD --first-name \"$FIRSTNAME\""
fi

if [ ! -z "$LASTNAME" ]; then
    CMD="$CMD --last-name \"$LASTNAME\""
fi

eval $CMD

echo
