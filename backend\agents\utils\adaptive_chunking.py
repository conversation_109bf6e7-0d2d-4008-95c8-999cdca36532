"""
Adaptive chunking strategies for optimal vector database performance.

This module provides intelligent chunking strategies that adapt based on:
- Document type and content structure
- Embedding model characteristics
- Query patterns and performance metrics
- Content semantic boundaries
"""

import re
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
import spacy
from langchain.text_splitter import (
    RecursiveCharacterTextSplitter,
    SpacyTextSplitter,
    NLTKTextSplitter,
    TokenTextSplitter
)

logger = logging.getLogger(__name__)

class ContentType(Enum):
    """Content types for adaptive chunking."""
    TECHNICAL_DOCUMENT = "technical"
    NARRATIVE_TEXT = "narrative"
    STRUCTURED_DATA = "structured"
    CODE_DOCUMENTATION = "code"
    RESEARCH_PAPER = "research"
    LEGAL_DOCUMENT = "legal"
    FINANCIAL_REPORT = "financial"
    GENERAL_TEXT = "general"

@dataclass
class ChunkingStrategy:
    """Configuration for a specific chunking strategy."""
    chunk_size: int
    chunk_overlap: int
    separators: List[str]
    preserve_structure: bool = True
    semantic_splitting: bool = False
    min_chunk_size: int = 100
    max_chunk_size: int = 2000
    overlap_ratio: float = 0.1  # Overlap as ratio of chunk size

@dataclass
class ChunkMetadata:
    """Metadata for each chunk."""
    chunk_id: str
    content_type: ContentType
    semantic_score: float
    position_in_document: int
    total_chunks: int
    keywords: List[str]
    sentence_count: int
    word_count: int
    complexity_score: float

class AdaptiveChunker:
    """
    Intelligent chunking system that adapts strategies based on content analysis.
    """
    
    def __init__(self):
        """Initialize the adaptive chunker."""
        self.strategies = self._initialize_strategies()
        self.nlp = None
        self._load_nlp_models()
        
    def _load_nlp_models(self):
        """Load NLP models for content analysis."""
        try:
            # Download required NLTK data
            nltk.download('punkt', quiet=True)
            nltk.download('punkt_tab', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('averaged_perceptron_tagger', quiet=True)
            
            # Load spaCy model for advanced processing
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
                self.nlp = None
                
        except Exception as e:
            logger.error(f"Error loading NLP models: {e}")
    
    def _initialize_strategies(self) -> Dict[ContentType, ChunkingStrategy]:
        """Initialize chunking strategies for different content types."""
        return {
            ContentType.TECHNICAL_DOCUMENT: ChunkingStrategy(
                chunk_size=800,
                chunk_overlap=150,
                separators=["\n\n", "\n", ".", "!", "?", ";", " "],
                preserve_structure=True,
                semantic_splitting=True,
                min_chunk_size=200,
                max_chunk_size=1200
            ),
            ContentType.NARRATIVE_TEXT: ChunkingStrategy(
                chunk_size=1200,
                chunk_overlap=200,
                separators=["\n\n", "\n", ".", "!", "?", " "],
                preserve_structure=False,
                semantic_splitting=True,
                min_chunk_size=300,
                max_chunk_size=1800
            ),
            ContentType.STRUCTURED_DATA: ChunkingStrategy(
                chunk_size=600,
                chunk_overlap=100,
                separators=["\n\n", "\n", "|", ",", " "],
                preserve_structure=True,
                semantic_splitting=False,
                min_chunk_size=150,
                max_chunk_size=1000
            ),
            ContentType.CODE_DOCUMENTATION: ChunkingStrategy(
                chunk_size=1000,
                chunk_overlap=100,
                separators=["\n\n", "\n", "```", "```python", "```javascript", " "],
                preserve_structure=True,
                semantic_splitting=False,
                min_chunk_size=200,
                max_chunk_size=1500
            ),
            ContentType.RESEARCH_PAPER: ChunkingStrategy(
                chunk_size=1500,
                chunk_overlap=300,
                separators=["\n\n", "\n", ".", "!", "?", " "],
                preserve_structure=True,
                semantic_splitting=True,
                min_chunk_size=400,
                max_chunk_size=2000
            ),
            ContentType.LEGAL_DOCUMENT: ChunkingStrategy(
                chunk_size=1000,
                chunk_overlap=250,
                separators=["\n\n", "\n", ".", ";", " "],
                preserve_structure=True,
                semantic_splitting=True,
                min_chunk_size=300,
                max_chunk_size=1500
            ),
            ContentType.FINANCIAL_REPORT: ChunkingStrategy(
                chunk_size=800,
                chunk_overlap=160,
                separators=["\n\n", "\n", ".", " "],
                preserve_structure=True,
                semantic_splitting=False,
                min_chunk_size=200,
                max_chunk_size=1200
            ),
            ContentType.GENERAL_TEXT: ChunkingStrategy(
                chunk_size=1000,
                chunk_overlap=200,
                separators=["\n\n", "\n", ".", "!", "?", " "],
                preserve_structure=False,
                semantic_splitting=True,
                min_chunk_size=250,
                max_chunk_size=1500
            )
        }
    
    def analyze_content_type(self, text: str, file_extension: str = None) -> ContentType:
        """
        Analyze content to determine the most appropriate content type.
        
        Args:
            text: The text content to analyze
            file_extension: Optional file extension for additional context
            
        Returns:
            Detected content type
        """
        text_lower = text.lower()
        
        # File extension hints
        if file_extension:
            if file_extension in ['.py', '.js', '.md', '.rst']:
                return ContentType.CODE_DOCUMENTATION
            elif file_extension in ['.csv', '.json', '.xml']:
                return ContentType.STRUCTURED_DATA
        
        # Content pattern analysis
        technical_indicators = [
            'algorithm', 'implementation', 'function', 'method', 'class',
            'api', 'database', 'server', 'client', 'protocol', 'framework'
        ]
        
        legal_indicators = [
            'whereas', 'hereby', 'pursuant', 'agreement', 'contract',
            'liability', 'jurisdiction', 'clause', 'section', 'article'
        ]
        
        financial_indicators = [
            'revenue', 'profit', 'loss', 'balance sheet', 'income statement',
            'cash flow', 'assets', 'liabilities', 'equity', 'financial'
        ]
        
        research_indicators = [
            'abstract', 'methodology', 'results', 'conclusion', 'hypothesis',
            'experiment', 'analysis', 'study', 'research', 'findings'
        ]
        
        # Count indicators
        technical_score = sum(1 for indicator in technical_indicators if indicator in text_lower)
        legal_score = sum(1 for indicator in legal_indicators if indicator in text_lower)
        financial_score = sum(1 for indicator in financial_indicators if indicator in text_lower)
        research_score = sum(1 for indicator in research_indicators if indicator in text_lower)
        
        # Determine content type based on highest score
        scores = {
            ContentType.TECHNICAL_DOCUMENT: technical_score,
            ContentType.LEGAL_DOCUMENT: legal_score,
            ContentType.FINANCIAL_REPORT: financial_score,
            ContentType.RESEARCH_PAPER: research_score
        }
        
        max_score = max(scores.values())
        if max_score >= 3:  # Threshold for confident classification
            return max(scores, key=scores.get)
        
        # Check for structured data patterns
        if re.search(r'^\s*[\w\s]+\s*[|,]\s*[\w\s]+', text, re.MULTILINE):
            return ContentType.STRUCTURED_DATA
        
        # Check for narrative patterns
        sentences = sent_tokenize(text[:1000])  # Sample first 1000 chars
        if len(sentences) > 5:
            avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
            if avg_sentence_length > 15:  # Longer sentences suggest narrative
                return ContentType.NARRATIVE_TEXT
        
        return ContentType.GENERAL_TEXT
    
    def calculate_semantic_boundaries(self, text: str) -> List[int]:
        """
        Calculate semantic boundaries in text for better chunking.
        
        Args:
            text: Input text
            
        Returns:
            List of character positions representing semantic boundaries
        """
        boundaries = []
        
        if not self.nlp:
            # Fallback to sentence boundaries
            sentences = sent_tokenize(text)
            pos = 0
            for sentence in sentences:
                pos += len(sentence)
                boundaries.append(pos)
            return boundaries
        
        # Use spaCy for advanced semantic analysis
        doc = self.nlp(text)
        
        # Add sentence boundaries
        for sent in doc.sents:
            boundaries.append(sent.end_char)
        
        # Add topic change boundaries (simplified heuristic)
        # Look for significant entity changes or topic shifts
        prev_entities = set()
        for i, sent in enumerate(doc.sents):
            current_entities = {ent.label_ for ent in sent.ents}
            
            if i > 0 and len(current_entities.intersection(prev_entities)) < 0.3 * len(prev_entities):
                # Significant entity change suggests topic shift
                boundaries.append(sent.start_char)
            
            prev_entities = current_entities
        
        return sorted(set(boundaries))
    
    def chunk_with_strategy(
        self, 
        text: str, 
        strategy: ChunkingStrategy,
        content_type: ContentType
    ) -> List[Tuple[str, ChunkMetadata]]:
        """
        Chunk text using the specified strategy.
        
        Args:
            text: Text to chunk
            strategy: Chunking strategy to use
            content_type: Type of content being chunked
            
        Returns:
            List of (chunk_text, metadata) tuples
        """
        chunks_with_metadata = []
        
        if strategy.semantic_splitting and self.nlp:
            # Use semantic-aware chunking
            chunks = self._semantic_chunk(text, strategy)
        else:
            # Use traditional text splitting
            splitter = RecursiveCharacterTextSplitter(
                chunk_size=strategy.chunk_size,
                chunk_overlap=strategy.chunk_overlap,
                separators=strategy.separators,
                length_function=len
            )
            chunks = splitter.split_text(text)
        
        # Generate metadata for each chunk
        for i, chunk in enumerate(chunks):
            metadata = self._generate_chunk_metadata(
                chunk, content_type, i, len(chunks)
            )
            chunks_with_metadata.append((chunk, metadata))
        
        return chunks_with_metadata
    
    def _semantic_chunk(self, text: str, strategy: ChunkingStrategy) -> List[str]:
        """
        Perform semantic-aware chunking using NLP analysis.
        
        Args:
            text: Text to chunk
            strategy: Chunking strategy
            
        Returns:
            List of text chunks
        """
        if not self.nlp:
            # Fallback to regular chunking
            splitter = RecursiveCharacterTextSplitter(
                chunk_size=strategy.chunk_size,
                chunk_overlap=strategy.chunk_overlap,
                separators=strategy.separators
            )
            return splitter.split_text(text)
        
        # Get semantic boundaries
        boundaries = self.calculate_semantic_boundaries(text)
        
        chunks = []
        current_chunk = ""
        
        sentences = sent_tokenize(text)
        sentence_pos = 0
        
        for sentence in sentences:
            sentence_end = sentence_pos + len(sentence)
            
            # Check if adding this sentence would exceed chunk size
            if len(current_chunk) + len(sentence) > strategy.chunk_size and current_chunk:
                # Check if we're at a semantic boundary
                if any(abs(boundary - sentence_pos) < 50 for boundary in boundaries):
                    # We're near a semantic boundary, end chunk here
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # Not at semantic boundary, but chunk is getting too large
                    if len(current_chunk) > strategy.min_chunk_size:
                        chunks.append(current_chunk.strip())
                        current_chunk = sentence
                    else:
                        current_chunk += " " + sentence
            else:
                if current_chunk:
                    current_chunk += " " + sentence
                else:
                    current_chunk = sentence
            
            sentence_pos = sentence_end
        
        # Add final chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _generate_chunk_metadata(
        self, 
        chunk: str, 
        content_type: ContentType, 
        position: int, 
        total_chunks: int
    ) -> ChunkMetadata:
        """Generate metadata for a chunk."""
        import uuid
        
        # Basic metrics
        words = word_tokenize(chunk)
        sentences = sent_tokenize(chunk)
        
        # Extract keywords (simple approach)
        try:
            stop_words = set(stopwords.words('english'))
            keywords = [word.lower() for word in words 
                       if word.isalpha() and word.lower() not in stop_words and len(word) > 3]
            keywords = list(set(keywords))[:10]  # Top 10 unique keywords
        except:
            keywords = []
        
        # Calculate complexity score (based on sentence length and vocabulary diversity)
        avg_sentence_length = len(words) / max(len(sentences), 1)
        vocab_diversity = len(set(words)) / max(len(words), 1)
        complexity_score = (avg_sentence_length * vocab_diversity) / 10  # Normalized
        
        # Calculate semantic score (placeholder - could be enhanced with actual semantic analysis)
        semantic_score = min(1.0, len(keywords) / 10.0)
        
        return ChunkMetadata(
            chunk_id=str(uuid.uuid4()),
            content_type=content_type,
            semantic_score=semantic_score,
            position_in_document=position,
            total_chunks=total_chunks,
            keywords=keywords,
            sentence_count=len(sentences),
            word_count=len(words),
            complexity_score=complexity_score
        )
    
    def chunk_document(
        self, 
        text: str, 
        file_extension: str = None,
        custom_strategy: ChunkingStrategy = None
    ) -> List[Tuple[str, ChunkMetadata]]:
        """
        Main method to chunk a document with adaptive strategy selection.
        
        Args:
            text: Document text to chunk
            file_extension: Optional file extension for content type hints
            custom_strategy: Optional custom chunking strategy
            
        Returns:
            List of (chunk_text, metadata) tuples
        """
        # Analyze content type
        content_type = self.analyze_content_type(text, file_extension)
        logger.info(f"Detected content type: {content_type.value}")
        
        # Select strategy
        if custom_strategy:
            strategy = custom_strategy
        else:
            strategy = self.strategies[content_type]
        
        logger.info(f"Using chunking strategy: chunk_size={strategy.chunk_size}, "
                   f"overlap={strategy.chunk_overlap}, semantic={strategy.semantic_splitting}")
        
        # Perform chunking
        chunks_with_metadata = self.chunk_with_strategy(text, strategy, content_type)
        
        logger.info(f"Generated {len(chunks_with_metadata)} chunks")
        return chunks_with_metadata
