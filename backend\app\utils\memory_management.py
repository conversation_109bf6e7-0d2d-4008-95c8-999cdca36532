"""
Production-ready memory management utilities for Datagenius.

This module provides comprehensive memory management, resource cleanup,
and protection against memory leaks with proper error handling.
"""

import gc
import psutil
import logging
import threading
import weakref
from typing import Dict, Any, List, Optional, Callable, Union
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class MemoryStats:
    """Memory statistics container."""
    total_memory: int
    available_memory: int
    used_memory: int
    memory_percent: float
    process_memory: int
    timestamp: datetime


class MemoryError(Exception):
    """Custom exception for memory-related errors."""
    
    def __init__(self, message: str, memory_stats: MemoryStats = None):
        super().__init__(message)
        self.memory_stats = memory_stats
        self.timestamp = datetime.now()


class MemoryManager:
    """
    Production-ready memory management system with leak detection,
    resource cleanup, and automatic garbage collection.
    """
    
    def __init__(self, max_memory_percent: float = 80.0, cleanup_interval: int = 300):
        """
        Initialize memory manager with safety limits.
        
        Args:
            max_memory_percent: Maximum memory usage percentage before cleanup
            cleanup_interval: Automatic cleanup interval in seconds
        """
        self.max_memory_percent = max_memory_percent
        self.cleanup_interval = cleanup_interval
        self.tracked_objects: Dict[str, weakref.ref] = {}
        self.cleanup_callbacks: List[Callable] = []
        self.memory_history: List[MemoryStats] = []
        self.max_history_size = 1000
        
        # Start background cleanup thread
        self._cleanup_thread = threading.Thread(target=self._background_cleanup, daemon=True)
        self._cleanup_thread.start()
        
        logger.info(f"MemoryManager initialized with {max_memory_percent}% limit")
    
    def get_memory_stats(self) -> MemoryStats:
        """Get current memory statistics with error handling."""
        try:
            # System memory
            memory = psutil.virtual_memory()
            
            # Process memory
            process = psutil.Process()
            process_memory = process.memory_info().rss
            
            stats = MemoryStats(
                total_memory=memory.total,
                available_memory=memory.available,
                used_memory=memory.used,
                memory_percent=memory.percent,
                process_memory=process_memory,
                timestamp=datetime.now()
            )
            
            # Store in history with size limit
            self.memory_history.append(stats)
            if len(self.memory_history) > self.max_history_size:
                self.memory_history.pop(0)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            raise MemoryError(f"Failed to get memory statistics: {e}")
    
    def check_memory_limit(self) -> bool:
        """Check if memory usage is within limits."""
        try:
            stats = self.get_memory_stats()
            
            if stats.memory_percent > self.max_memory_percent:
                logger.warning(f"Memory usage {stats.memory_percent:.1f}% exceeds limit {self.max_memory_percent}%")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking memory limit: {e}")
            return False
    
    @contextmanager
    def memory_limit_context(self, operation_name: str = "operation"):
        """Context manager for memory-limited operations."""
        initial_stats = self.get_memory_stats()
        
        try:
            if not self.check_memory_limit():
                self.force_cleanup()
                
            if not self.check_memory_limit():
                raise MemoryError(f"Insufficient memory for {operation_name}")
            
            logger.debug(f"Starting {operation_name} with {initial_stats.memory_percent:.1f}% memory usage")
            yield
            
        except Exception as e:
            logger.error(f"Error in {operation_name}: {e}")
            raise
            
        finally:
            # Cleanup after operation
            self.cleanup_operation_resources()
            
            final_stats = self.get_memory_stats()
            memory_diff = final_stats.process_memory - initial_stats.process_memory
            
            if memory_diff > 0:
                logger.debug(f"{operation_name} used {memory_diff / 1024 / 1024:.2f}MB additional memory")
    
    def track_object(self, obj: Any, name: str) -> None:
        """Track object for memory leak detection."""
        try:
            def cleanup_callback(ref):
                if name in self.tracked_objects:
                    del self.tracked_objects[name]
                logger.debug(f"Tracked object '{name}' was garbage collected")
            
            self.tracked_objects[name] = weakref.ref(obj, cleanup_callback)
            logger.debug(f"Now tracking object: {name}")
            
        except Exception as e:
            logger.error(f"Error tracking object {name}: {e}")
    
    def cleanup_dataframes(self) -> int:
        """Clean up pandas DataFrames and numpy arrays."""
        cleaned_count = 0
        
        try:
            # Force garbage collection
            gc.collect()
            
            # Clean up any remaining DataFrame references
            for obj in gc.get_objects():
                if isinstance(obj, pd.DataFrame):
                    try:
                        # Clear DataFrame data
                        obj.drop(obj.index, inplace=True)
                        cleaned_count += 1
                    except Exception:
                        pass
                elif isinstance(obj, np.ndarray):
                    try:
                        # Clear numpy array
                        obj.fill(0)
                        cleaned_count += 1
                    except Exception:
                        pass
            
            logger.debug(f"Cleaned up {cleaned_count} data objects")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error cleaning up dataframes: {e}")
            return 0
    
    def force_cleanup(self) -> Dict[str, int]:
        """Force comprehensive memory cleanup."""
        cleanup_stats = {
            "dataframes_cleaned": 0,
            "callbacks_executed": 0,
            "gc_collected": 0
        }
        
        try:
            # Execute cleanup callbacks
            for callback in self.cleanup_callbacks:
                try:
                    callback()
                    cleanup_stats["callbacks_executed"] += 1
                except Exception as e:
                    logger.error(f"Error in cleanup callback: {e}")
            
            # Clean up DataFrames
            cleanup_stats["dataframes_cleaned"] = self.cleanup_dataframes()
            
            # Force garbage collection multiple times
            for _ in range(3):
                collected = gc.collect()
                cleanup_stats["gc_collected"] += collected
            
            logger.info(f"Force cleanup completed: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Error in force cleanup: {e}")
            return cleanup_stats
    
    def cleanup_operation_resources(self) -> None:
        """Clean up resources after an operation."""
        try:
            # Light cleanup - just garbage collection
            gc.collect()
            
            # Check for memory leaks in tracked objects
            leaked_objects = []
            for name, ref in list(self.tracked_objects.items()):
                if ref() is not None:
                    leaked_objects.append(name)
            
            if leaked_objects:
                logger.warning(f"Potential memory leaks detected in: {leaked_objects}")
                
        except Exception as e:
            logger.error(f"Error in operation cleanup: {e}")
    
    def register_cleanup_callback(self, callback: Callable) -> None:
        """Register a cleanup callback function."""
        if callable(callback):
            self.cleanup_callbacks.append(callback)
            logger.debug("Registered cleanup callback")
        else:
            raise ValueError("Callback must be callable")
    
    def _background_cleanup(self) -> None:
        """Background thread for periodic cleanup."""
        import time
        
        while True:
            try:
                time.sleep(self.cleanup_interval)
                
                stats = self.get_memory_stats()
                if stats.memory_percent > self.max_memory_percent * 0.8:  # 80% of limit
                    logger.info("Performing background memory cleanup")
                    self.force_cleanup()
                    
            except Exception as e:
                logger.error(f"Error in background cleanup: {e}")


# Global memory manager instance
memory_manager = MemoryManager()


@contextmanager
def memory_safe_operation(operation_name: str = "operation"):
    """Decorator for memory-safe operations."""
    with memory_manager.memory_limit_context(operation_name):
        yield


def cleanup_on_exit(func: Callable) -> Callable:
    """Decorator to ensure cleanup on function exit."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        finally:
            memory_manager.cleanup_operation_resources()
    
    return wrapper


def track_memory_usage(func: Callable) -> Callable:
    """Decorator to track memory usage of functions."""
    def wrapper(*args, **kwargs):
        initial_stats = memory_manager.get_memory_stats()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            final_stats = memory_manager.get_memory_stats()
            memory_diff = final_stats.process_memory - initial_stats.process_memory
            
            if memory_diff > 1024 * 1024:  # Log if > 1MB
                logger.info(f"{func.__name__} used {memory_diff / 1024 / 1024:.2f}MB memory")
    
    return wrapper
