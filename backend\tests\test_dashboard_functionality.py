"""
Test dashboard functionality to verify CRUD operations, widget management, and data source integration.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from app.services.dashboard_service import DatageniusDashboardService
from app.models.dashboard_customization import (
    DashboardCreate, DashboardUpdate, SectionCreate, WidgetCreate,
    DashboardDataSourceAssignmentCreate
)


class TestDashboardFunctionality:
    """Test suite for dashboard functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def dashboard_service(self, mock_db_session):
        """Dashboard service with mocked database."""
        return DatageniusDashboardService(db=mock_db_session)

    @pytest.fixture
    def sample_dashboard_data(self):
        """Sample dashboard creation data."""
        return DashboardCreate(
            name="Test Dashboard",
            description="A test dashboard for verification",
            is_default=True,
            is_public=False,
            layout_config={
                "columns": 12,
                "rows": 12,
                "grid_gap": 16,
                "responsive": True
            },
            theme_config={
                "primary_color": "#3B82F6",
                "secondary_color": "#10B981",
                "background_color": "#F9FAFB",
                "text_color": "#1F2937"
            },
            refresh_interval=300,
            tags=["test"]
        )

    @pytest.fixture
    def sample_section_data(self):
        """Sample section creation data."""
        return SectionCreate(
            dashboard_id="test-dashboard-id",
            name="Test Section",
            description="A test section",
            color="#3B82F6",
            icon="Grid",
            layout_config={
                "columns": 12,
                "rows": 6,
                "grid_gap": 16
            }
        )

    @pytest.fixture
    def sample_widget_data(self):
        """Sample widget creation data."""
        return WidgetCreate(
            section_id="test-section-id",
            title="Test Widget",
            widget_type="chart",
            position_config={
                "x": 0,
                "y": 0,
                "w": 4,
                "h": 3
            },
            data_config={
                "data_source": "test-source",
                "query": "SELECT * FROM test_table"
            },
            visualization_config={
                "chart_type": "bar",
                "colors": ["#3B82F6", "#10B981"]
            }
        )

    def test_dashboard_create_data_structure(self, sample_dashboard_data):
        """Test that dashboard creation data has correct structure."""
        assert sample_dashboard_data.name == "Test Dashboard"
        assert sample_dashboard_data.is_default is True
        assert sample_dashboard_data.layout_config["columns"] == 12
        assert sample_dashboard_data.theme_config["primary_color"] == "#3B82F6"
        assert sample_dashboard_data.refresh_interval == 300

    def test_section_create_data_structure(self, sample_section_data):
        """Test that section creation data has correct structure."""
        assert sample_section_data.name == "Test Section"
        assert sample_section_data.color == "#3B82F6"
        assert sample_section_data.layout_config["columns"] == 12

    def test_widget_create_data_structure(self, sample_widget_data):
        """Test that widget creation data has correct structure."""
        assert sample_widget_data.title == "Test Widget"
        assert sample_widget_data.widget_type == "chart"
        assert sample_widget_data.position_config["w"] == 4
        assert sample_widget_data.position_config["h"] == 3

    def test_dashboard_service_initialization(self, dashboard_service):
        """Test that dashboard service initializes correctly."""
        assert dashboard_service is not None
        assert hasattr(dashboard_service, 'db')

    @pytest.mark.asyncio
    async def test_dashboard_creation_flow(self, dashboard_service, sample_dashboard_data, mock_db_session):
        """Test the dashboard creation flow."""
        # Mock database operations
        mock_dashboard = Mock()
        mock_dashboard.id = "test-dashboard-id"
        mock_dashboard.name = sample_dashboard_data.name
        mock_dashboard.description = sample_dashboard_data.description
        mock_dashboard.is_default = sample_dashboard_data.is_default
        mock_dashboard.is_public = sample_dashboard_data.is_public
        mock_dashboard.layout_config = sample_dashboard_data.layout_config
        mock_dashboard.theme_config = sample_dashboard_data.theme_config
        mock_dashboard.refresh_interval = sample_dashboard_data.refresh_interval
        
        # Mock database session methods
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        mock_db_session.add = Mock()
        mock_db_session.commit = Mock()
        mock_db_session.refresh = Mock()
        
        # Mock the dashboard creation
        with patch('app.services.dashboard_service.Dashboard') as mock_dashboard_class:
            mock_dashboard_class.return_value = mock_dashboard
            
            # This would normally call the actual service method
            # For now, we just verify the data structure is correct
            assert sample_dashboard_data.name == "Test Dashboard"
            assert sample_dashboard_data.layout_config is not None
            assert sample_dashboard_data.theme_config is not None

    def test_data_source_assignment_structure(self):
        """Test data source assignment data structure."""
        assignment_data = DashboardDataSourceAssignmentCreate(
            system_data_source_id="test-data-source-id",
            alias="Test Data Source",
            is_active=True
        )
        
        assert assignment_data.system_data_source_id == "test-data-source-id"
        assert assignment_data.alias == "Test Data Source"
        assert assignment_data.is_active is True

    def test_dashboard_update_structure(self):
        """Test dashboard update data structure."""
        update_data = DashboardUpdate(
            name="Updated Dashboard",
            description="Updated description",
            refresh_interval=600
        )
        
        assert update_data.name == "Updated Dashboard"
        assert update_data.description == "Updated description"
        assert update_data.refresh_interval == 600

    def test_hierarchical_data_source_architecture(self):
        """Test that data sources can be configured at dashboard level."""
        # Test that dashboard can have data source assignments
        dashboard_data = DashboardCreate(
            name="Dashboard with Data Sources",
            description="Dashboard with hierarchical data sources",
            data_source_assignments=[
                DashboardDataSourceAssignmentCreate(
                    system_data_source_id="source-1",
                    alias="Primary Source",
                    is_active=True
                ),
                DashboardDataSourceAssignmentCreate(
                    system_data_source_id="source-2",
                    alias="Secondary Source",
                    is_active=True
                )
            ]
        )
        
        assert len(dashboard_data.data_source_assignments) == 2
        assert dashboard_data.data_source_assignments[0].alias == "Primary Source"
        assert dashboard_data.data_source_assignments[1].alias == "Secondary Source"

    def test_multi_dashboard_support(self):
        """Test that multiple dashboards can be created with different configurations."""
        dashboard1 = DashboardCreate(
            name="Analytics Dashboard",
            description="Dashboard for analytics",
            is_default=True,
            theme_config={"primary_color": "#3B82F6"}
        )
        
        dashboard2 = DashboardCreate(
            name="Monitoring Dashboard", 
            description="Dashboard for monitoring",
            is_default=False,
            theme_config={"primary_color": "#10B981"}
        )
        
        assert dashboard1.name != dashboard2.name
        assert dashboard1.is_default != dashboard2.is_default
        assert dashboard1.theme_config["primary_color"] != dashboard2.theme_config["primary_color"]

    def test_widget_position_configuration(self):
        """Test widget position configuration for responsive layout."""
        widget_data = WidgetCreate(
            section_id="test-section",
            title="Responsive Widget",
            widget_type="kpi",
            position_config={
                "x": 0,
                "y": 0,
                "w": 6,  # Half width
                "h": 4,  # Standard height
                "minW": 3,
                "minH": 2,
                "maxW": 12,
                "maxH": 8
            }
        )
        
        assert widget_data.position_config["w"] == 6
        assert widget_data.position_config["h"] == 4
        assert widget_data.position_config["minW"] == 3
        assert widget_data.position_config["maxW"] == 12


if __name__ == "__main__":
    pytest.main([__file__])
