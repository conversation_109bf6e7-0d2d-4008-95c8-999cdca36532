import React from "react";
import { DashboardLayout } from "@/components/DashboardLayout";
import { Dashboard } from "@/components/Dashboard";
import { UnifiedHeader } from "@/components/UnifiedHeader";
import { useDashboardHeader } from "@/hooks/use-dashboard-header";
import { DashboardSecurityProvider } from "@/contexts/DashboardSecurityContext";
import { DashboardModeWrapper } from "@/components/dashboard/mode/DashboardModeWrapper";

const MainDashboard: React.FC = () => {
  const { headerProps } = useDashboardHeader();

  return (
    <DashboardSecurityProvider>
      <div className="h-screen flex flex-col overflow-hidden">
        {/* UnifiedHeader on top of everything */}
        <UnifiedHeader {...headerProps} />

        {/* DashboardModeWrapper below header with optimized layout */}
        <div className="flex-1 overflow-hidden">
          <DashboardModeWrapper
            className="h-full"
            show_mode_indicator={false}
            show_agent_integration={true}
            on_widget_create={(widget_config) => {
              // Handle widget creation from AI assistant
              console.log('Creating widget from AI:', widget_config);
            }}
            on_section_create={(section_config) => {
              // Handle section creation
              console.log('Creating section:', section_config);
            }}
            on_template_apply={(template_id) => {
              // Handle template application
              console.log('Applying template:', template_id);
            }}
            on_data_configure={(data_config) => {
              // Handle data configuration
              console.log('Configuring data:', data_config);
            }}
            on_ribbon_action={(action, data) => {
              // Handle ribbon toolbar actions
              console.log('Ribbon action:', action, data);
            }}
          >
            <DashboardLayout
              showHeader={false}
              showFloatingAI={true}
              showDashboardControls={false}
              onWidgetCreate={(widget_config) => {
                // Handle widget creation from floating AI
                console.log('Creating widget from floating AI:', widget_config);
              }}
              onSectionCreate={(section_config) => {
                // Handle section creation from floating AI
                console.log('Creating section from floating AI:', section_config);
              }}
              onTemplateApply={(template_id) => {
                // Handle template application from floating AI
                console.log('Applying template from floating AI:', template_id);
              }}
              onDataConfigure={(data_config) => {
                // Handle data configuration from floating AI
                console.log('Configuring data from floating AI:', data_config);
              }}
              onDataAnalyze={(analysis_request) => {
                // Handle data analysis from floating AI
                console.log('Analyzing data from floating AI:', analysis_request);
              }}
              onDashboardOptimize={(optimization_params) => {
                // Handle dashboard optimization from floating AI
                console.log('Optimizing dashboard from floating AI:', optimization_params);
              }}
              onCodeGenerate={(code_request) => {
                // Handle code generation from floating AI
                console.log('Generating code from floating AI:', code_request);
              }}
            >
              <div className="h-full w-full">
                <Dashboard className="h-full w-full" />
              </div>
            </DashboardLayout>
          </DashboardModeWrapper>
        </div>
      </div>
    </DashboardSecurityProvider>
  );
};

export default MainDashboard;