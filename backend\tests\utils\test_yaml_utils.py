"""
Unit tests for YAML utilities.

This module contains tests for the YAML utility functions in app.utils.yaml_utils.
"""

import os
import sys
import unittest
import tempfile
import json
import yaml
from pathlib import Path

# Add the backend directory to the Python path
test_dir = os.path.dirname(os.path.abspath(__file__))
utils_dir = os.path.dirname(test_dir)
backend_dir = os.path.dirname(utils_dir)
sys.path.insert(0, backend_dir)

# Import the functions to test
from app.utils.yaml_utils import (
    load_yaml,
    save_yaml,
    json_to_yaml,
    yaml_to_json,
    convert_file,
    schema_to_yaml,
    yaml_to_schema
)


class TestYAMLUtils(unittest.TestCase):
    """Test cases for YAML utility functions."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for test files
        self.temp_dir = tempfile.TemporaryDirectory()
        self.test_dir = self.temp_dir.name

    def tearDown(self):
        """Clean up test environment."""
        # Remove the temporary directory
        self.temp_dir.cleanup()

    def test_load_yaml(self):
        """Test loading a YAML file."""
        # Create a test YAML file
        test_data = {
            "name": "Test",
            "values": [1, 2, 3],
            "nested": {
                "key": "value"
            }
        }
        test_file = os.path.join(self.test_dir, "test.yaml")
        with open(test_file, "w") as f:
            yaml.dump(test_data, f)

        # Test loading the file
        loaded_data = load_yaml(test_file)
        self.assertEqual(loaded_data, test_data)

    def test_load_yaml_with_encoding(self):
        """Test loading a YAML file with different encodings."""
        # Create a test YAML file with non-ASCII characters
        test_data = {
            "name": "Test with Unicode: 你好",
            "values": [1, 2, 3]
        }
        test_file = os.path.join(self.test_dir, "test_unicode.yaml")
        with open(test_file, "w", encoding="utf-8") as f:
            yaml.dump(test_data, f)

        # Test loading with default encoding
        loaded_data = load_yaml(test_file)
        self.assertEqual(loaded_data, test_data)

        # Test loading with explicit utf-8 encoding
        loaded_data = load_yaml(test_file, default_encoding="utf-8")
        self.assertEqual(loaded_data, test_data)

    def test_load_yaml_nonexistent_file(self):
        """Test loading a non-existent YAML file."""
        test_file = os.path.join(self.test_dir, "nonexistent.yaml")
        with self.assertRaises(Exception):
            load_yaml(test_file)

    def test_save_yaml(self):
        """Test saving data to a YAML file."""
        test_data = {
            "name": "Test",
            "values": [1, 2, 3],
            "nested": {
                "key": "value"
            }
        }
        test_file = os.path.join(self.test_dir, "save_test.yaml")

        # Test saving the data
        result = save_yaml(test_data, test_file)
        self.assertTrue(result)
        self.assertTrue(os.path.exists(test_file))

        # Verify the saved data
        with open(test_file, "r") as f:
            saved_data = yaml.safe_load(f)
        self.assertEqual(saved_data, test_data)

    def test_json_to_yaml(self):
        """Test converting JSON data to YAML string."""
        json_data = {
            "name": "Test",
            "values": [1, 2, 3],
            "nested": {
                "key": "value"
            }
        }

        # Convert to YAML
        yaml_str = json_to_yaml(json_data)

        # Parse the YAML string back to verify
        parsed_data = yaml.safe_load(yaml_str)
        self.assertEqual(parsed_data, json_data)

    def test_yaml_to_json(self):
        """Test converting YAML string to JSON data."""
        yaml_str = """
        name: Test
        values:
          - 1
          - 2
          - 3
        nested:
          key: value
        """

        # Convert to JSON
        json_data = yaml_to_json(yaml_str)

        # Expected result
        expected = {
            "name": "Test",
            "values": [1, 2, 3],
            "nested": {
                "key": "value"
            }
        }

        self.assertEqual(json_data, expected)

    def test_convert_file_json_to_yaml(self):
        """Test converting a JSON file to YAML."""
        # Create a test JSON file
        json_data = {
            "name": "Test",
            "values": [1, 2, 3],
            "nested": {
                "key": "value"
            }
        }
        json_file = os.path.join(self.test_dir, "test.json")
        with open(json_file, "w") as f:
            json.dump(json_data, f)

        # Convert to YAML
        yaml_file = os.path.join(self.test_dir, "test.yaml")
        result = convert_file(json_file, yaml_file)
        self.assertTrue(result)
        self.assertTrue(os.path.exists(yaml_file))

        # Verify the converted data
        with open(yaml_file, "r") as f:
            yaml_data = yaml.safe_load(f)
        self.assertEqual(yaml_data, json_data)

    def test_convert_file_invalid_input(self):
        """Test converting with invalid input file."""
        # Create a test text file
        text_file = os.path.join(self.test_dir, "test.txt")
        with open(text_file, "w") as f:
            f.write("This is not a JSON file")

        # Try to convert to YAML
        yaml_file = os.path.join(self.test_dir, "test.yaml")
        with self.assertRaises(ValueError):
            convert_file(text_file, yaml_file)

    def test_convert_file_invalid_output(self):
        """Test converting with invalid output file."""
        # Create a test JSON file
        json_file = os.path.join(self.test_dir, "test.json")
        with open(json_file, "w") as f:
            json.dump({"test": "data"}, f)

        # Try to convert to a non-YAML file
        text_file = os.path.join(self.test_dir, "test.txt")
        with self.assertRaises(ValueError):
            convert_file(json_file, text_file)

    def test_schema_to_yaml(self):
        """Test converting a JSON Schema to YAML."""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer", "minimum": 0},
                "address": {
                    "type": "object",
                    "properties": {
                        "street": {"type": "string"},
                        "city": {"type": "string"}
                    },
                    "required": ["street", "city"]
                }
            },
            "required": ["name", "age"]
        }

        # Convert to YAML
        yaml_str = schema_to_yaml(schema)

        # Parse the YAML string back to verify
        parsed_schema = yaml.safe_load(yaml_str)
        self.assertEqual(parsed_schema, schema)

    def test_yaml_to_schema(self):
        """Test converting YAML to a JSON Schema."""
        yaml_str = """
        type: object
        properties:
          name:
            type: string
          age:
            type: integer
            minimum: 0
          address:
            type: object
            properties:
              street:
                type: string
              city:
                type: string
            required:
              - street
              - city
        required:
          - name
          - age
        """

        # Convert to schema
        schema = yaml_to_schema(yaml_str)

        # Expected result
        expected = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer", "minimum": 0},
                "address": {
                    "type": "object",
                    "properties": {
                        "street": {"type": "string"},
                        "city": {"type": "string"}
                    },
                    "required": ["street", "city"]
                }
            },
            "required": ["name", "age"]
        }

        self.assertEqual(schema, expected)

    def test_yaml_to_schema_invalid(self):
        """Test converting invalid YAML to a JSON Schema."""
        yaml_str = "not a valid schema"

        # This should raise a ValueError
        with self.assertRaises(ValueError):
            yaml_to_schema(yaml_str)


if __name__ == "__main__":
    unittest.main()
