"""
PandasAI v3 visualization MCP tool.

This module provides an MCP-compatible tool for visualizing data using PandasAI v3.
It integrates with mem0ai for enhanced visualization capabilities.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional
import base64
import matplotlib.pyplot as plt
import seaborn as sns
import io
import uuid

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import <PERSON>rror<PERSON>and<PERSON>
from ..chart_optimization import chart_enhancer, ChartOptimizer
from app.utils.json_utils import sanitize_json
from ...utils.memory_service import MemoryService
from ...utils.vector_service import VectorService
from .interactive_chart_data import InteractiveChartDataTool

logger = logging.getLogger(__name__)

class PandasAIVisualizationTool(BaseMCPTool):
    """Tool for visualizing data using PandasAI v3."""

    def __init__(self):
        """Initialize the PandasAI visualization tool."""
        super().__init__(
            name="pandasai_visualization",
            description="Visualize data using PandasAI v3 with mem0ai integration",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "data_source": {
                        "type": ["object", "string"],
                        "description": "Data source information (can be an object with id/name or a string identifier)"
                    },
                    "query": {"type": "string", "description": "Visualization query or prompt"},
                    "prompt": {"type": "string", "description": "Visualization prompt (alias for query)"},
                    "api_key": {"type": "string"},
                    "provider": {"type": "string", "default": "openai"},
                    "model": {"type": "string"},
                    "user_id": {"type": "string"},
                    "persona_id": {"type": "string"},
                    "conversation_id": {"type": "string"},
                    "store_in_memory": {"type": "boolean", "default": True}
                },
                "required": []  # Make all fields optional to allow flexible input
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()
        self.memory_service = MemoryService()
        self.vector_service = VectorService()
        self.interactive_chart_tool = InteractiveChartDataTool()
        self._last_dataframe = None  # Cache for DataFrame to use in interactive chart generation

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the PandasAI visualization tool."""
        file_path = arguments.get("file_path")
        data_source = arguments.get("data_source")
        # Handle both 'prompt' and 'query' for compatibility
        prompt = arguments.get("prompt") or arguments.get("query")
        api_key = arguments.get("api_key")
        provider = arguments.get("provider", "openai")

        # Validate required fields
        if not prompt:
            return {
                "isError": True,
                "content": [{"type": "text", "text": "No prompt or query provided. Please provide a visualization request."}]
            }

        # Log the input arguments for debugging
        logger.info(f"PandasAI visualization tool called with: prompt='{prompt}', provider='{provider}'")
        logger.info(f"File path: {file_path}, Data source: {data_source}")

        # Get API key from environment if not provided
        if not api_key:
            # Try to get API key from environment variables
            env_var_name = f"{provider.upper()}_API_KEY"
            api_key = os.getenv(env_var_name)
            logger.info(f"Using API key from environment variable {env_var_name}")

            # If still no API key, try OpenAI as fallback
            if not api_key and provider != "openai":
                logger.info(f"No API key for {provider}, trying OpenAI as fallback")
                provider = "openai"
                api_key = os.getenv("OPENAI_API_KEY")

            # If still no API key, return error
            if not api_key:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"No API key provided for {provider} and no fallback available. Please provide an API key."}]
                }

        # Process data source to get file path
        if data_source and not file_path:
            logger.info(f"Processing data source to get file path: {data_source}")

            # If data_source is a dict with file_path, use it directly
            if isinstance(data_source, dict) and "file_path" in data_source:
                file_path = data_source["file_path"]
                logger.info(f"Using file_path from data_source: {file_path}")
            # Also check for direct string data_source that might be a file path
            elif isinstance(data_source, str) and (data_source.endswith('.csv') or data_source.endswith('.xlsx') or '/' in data_source):
                file_path = data_source
                logger.info(f"Using data_source as file_path: {file_path}")
            else:
                try:
                    # Import the data access tool
                    from .data_access import DataAccessTool

                    # Create and initialize the tool
                    data_tool = DataAccessTool()
                    await data_tool.initialize({})

                    # Call the tool to load the data
                    data_result = await data_tool.execute({
                        "data_source": data_source,
                        "operation": "load",
                        "params": {"create_sample": True}
                    })

                    # Check if we got a valid result
                    if not data_result.get("isError", False) and "metadata" in data_result:
                        # Extract the file path from the result
                        file_path = data_result["metadata"].get("file_path")
                        logger.info(f"Retrieved file path from data_access tool: {file_path}")
                    else:
                        logger.error(f"Error retrieving file path from data_access tool: {data_result}")
                        return {
                            "isError": True,
                            "content": [{"type": "text", "text": "Could not access the data source. Please provide a valid file path or data source."}]
                        }
                except Exception as e:
                    logger.error(f"Error using data_access tool: {e}", exc_info=True)
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": f"Error accessing data source: {str(e)}"}]
                    }

        # Try to find file path if it's still not available
        if not file_path and isinstance(data_source, dict):
            # Try to extract file information from data source
            file_id = data_source.get("id")
            file_name = data_source.get("name")

            if file_id or file_name:
                logger.info(f"Trying to find file path for ID: {file_id}, Name: {file_name}")

                # Check common locations
                possible_paths = []

                # Add paths based on file_id
                if file_id:
                    possible_paths.extend([
                        f"data/{file_id}.csv",
                        f"uploads/{file_id}.csv",
                        f"temp_uploads/{file_id}.csv",
                        f"backend/data/{file_id}.csv",
                        f"data/{file_id}.xlsx",
                        f"uploads/{file_id}.xlsx",
                        f"temp_uploads/{file_id}.xlsx",
                        f"backend/data/{file_id}.xlsx"
                    ])

                # Add paths based on file_name
                if file_name:
                    possible_paths.extend([
                        f"data/{file_name}.csv",
                        f"uploads/{file_name}.csv",
                        f"temp_uploads/{file_name}.csv",
                        f"backend/data/{file_name}.csv",
                        f"data/{file_name}.xlsx",
                        f"uploads/{file_name}.xlsx",
                        f"temp_uploads/{file_name}.xlsx",
                        f"backend/data/{file_name}.xlsx",
                        f"data/{file_name}",
                        f"uploads/{file_name}",
                        f"temp_uploads/{file_name}",
                        f"backend/data/{file_name}"
                    ])

                # Add any additional common paths if needed
                pass

                # Check if any of these paths exist
                for path in possible_paths:
                    if os.path.exists(path):
                        file_path = path
                        logger.info(f"Found file at path: {file_path}")
                        break

        # Ensure we have a file path
        if not file_path:
            return {
                "isError": True,
                "content": [{"type": "text", "text": "No file path could be determined from the data source. Please provide a valid file path or data source."}]
            }

        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"File does not exist: {file_path}")
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"File does not exist: {file_path}"}]
            }

        # Check cache first
        cached_result = self.cache.get(file_path, prompt, provider)
        if cached_result:
            logger.info(f"Using cached result for visualization prompt: {prompt}")
            return cached_result

        try:
            # Initialize PandasAI
            logger.info(f"Initializing PandasAI with provider: {provider}")
            self.pandasai.initialize(api_key, provider)

            # Load dataframe
            logger.info(f"Loading dataframe from file: {file_path}")
            if not self.pandasai.load_dataframe(file_path):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error loading dataframe from {file_path}. Please make sure it's a valid data file (CSV, Excel, etc.)."}]
                }

            # Cache the DataFrame for interactive chart generation
            logger.info(f"🎨 DATAFRAME CACHING: Checking PandasAI instance")
            logger.info(f"🎨 hasattr(self.pandasai, 'df'): {hasattr(self.pandasai, 'df')}")
            logger.info(f"🎨 self.pandasai.df is not None: {getattr(self.pandasai, 'df', None) is not None}")

            if hasattr(self.pandasai, 'df') and self.pandasai.df is not None:
                logger.info(f"🎨 PandasAI DataFrame found, attempting conversion")
                logger.info(f"🎨 DataFrame type: {type(self.pandasai.df)}")

                # Convert PandasAI DataFrame to regular pandas DataFrame for interactive chart tool
                try:
                    if hasattr(self.pandasai.df, 'to_pandas'):
                        # PandasAI v3 DataFrame has to_pandas() method
                        logger.info(f"🎨 Using to_pandas() method")
                        self._last_dataframe = self.pandasai.df.to_pandas()
                        logger.info(f"🎨 ✅ Cached DataFrame (converted from PandasAI) with shape: {self._last_dataframe.shape}")
                    elif hasattr(self.pandasai.df, '_df'):
                        # PandasAI DataFrame might have internal _df attribute
                        logger.info(f"🎨 Using _df attribute")
                        self._last_dataframe = self.pandasai.df._df
                        logger.info(f"🎨 ✅ Cached DataFrame (from _df attribute) with shape: {self._last_dataframe.shape}")
                    else:
                        # Fallback: assume it's already a pandas DataFrame
                        logger.info(f"🎨 Using direct assignment")
                        self._last_dataframe = self.pandasai.df
                        logger.info(f"🎨 ✅ Cached DataFrame (direct) with shape: {self._last_dataframe.shape}")

                    # Show sample of cached data
                    if self._last_dataframe is not None:
                        logger.info(f"🎨 Cached DataFrame columns: {list(self._last_dataframe.columns)}")
                        logger.info(f"🎨 Cached DataFrame sample: {self._last_dataframe.head(2).to_dict('records')}")

                except Exception as e:
                    logger.error(f"🎨 ❌ Error converting PandasAI DataFrame to pandas DataFrame: {e}")
                    import traceback
                    logger.error(f"🎨 ❌ Full traceback: {traceback.format_exc()}")
                    self._last_dataframe = None
            else:
                logger.warning("🎨 ❌ Could not cache DataFrame - not available in PandasAI instance")
                logger.warning(f"🎨 PandasAI instance: {self.pandasai}")
                logger.warning(f"🎨 PandasAI df attribute: {getattr(self.pandasai, 'df', 'NOT_FOUND')}")

            # Create agent with model if provided
            model = arguments.get("model")
            logger.info(f"Creating PandasAI agent with model: {model}")
            if not self.pandasai.create_agent(model=model):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "Error creating PandasAI Agent. Please check your API key and provider settings."}]
                }

            # Enhanced visualization-specific prompt with better instructions
            viz_prompt = f"""Create a visualization chart for: {prompt}

IMPORTANT INSTRUCTIONS:
1. Use proper column names with spaces enclosed in double quotes (not backticks)
2. Generate matplotlib or seaborn plots only
3. Always use plt.savefig() to save the chart
4. Return only the chart image, no text explanations
5. If column names have spaces, use double quotes in SQL queries
6. Example: SELECT "Product line", COUNT(*) FROM table_name GROUP BY "Product line"

Create the visualization now."""

            logger.info(f"Sending enhanced visualization prompt to PandasAI: {viz_prompt}")

            # Chat with agent
            result = self.pandasai.chat(viz_prompt)
            logger.info(f"PandasAI result type: {result.get('type')}")
            logger.info(f"PandasAI result content preview: {str(result)[:200]}...")

            # Handle error with detailed logging and fallback
            if "error" in result:
                error_msg = result['error']
                logger.error(f"Error in PandasAI visualization: {error_msg}")

                # Try fallback visualization
                logger.info("Attempting fallback visualization due to PandasAI error")
                fallback_result = self._create_fallback_visualization(file_path, prompt)

                if not fallback_result.get("isError", True):
                    logger.info("Fallback visualization succeeded")
                    return fallback_result

                # If fallback also fails, return detailed error
                if "Binder Error" in str(error_msg) or "column" in str(error_msg).lower():
                    helpful_msg = f"SQL column error detected. The data columns available are likely different from what was expected. Original error: {error_msg}"
                elif "ParseError" in str(error_msg) or "Invalid expression" in str(error_msg):
                    helpful_msg = f"SQL syntax error detected. There may be an issue with column name formatting. Original error: {error_msg}"
                else:
                    helpful_msg = f"PandasAI visualization error: {error_msg}"

                return {
                    "isError": True,
                    "content": [{"type": "text", "text": helpful_msg}]
                }

            # For visualization, we expect a chart result
            if result.get("type") != "chart":
                logger.error(f"Expected chart result but got: {result.get('type', 'unknown')}")
                logger.error(f"Full result: {result}")

                # Try fallback visualization when PandasAI doesn't return a chart
                logger.info("Attempting fallback visualization due to non-chart result")
                fallback_result = self._create_fallback_visualization(file_path, prompt)

                if not fallback_result.get("isError", True):
                    logger.info("Fallback visualization succeeded")
                    return fallback_result

                # If fallback fails, provide helpful error message
                if result.get("type") == "string" and isinstance(result.get("value"), str):
                    # Sometimes PandasAI returns string results instead of charts
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": f"PandasAI returned text instead of a chart: {result.get('value', 'No content')}. Please try a more specific visualization request."}]
                    }

                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Expected a chart result, but got {result.get('type', 'unknown')}. Try rephrasing your visualization request with more specific chart type (e.g., 'create a bar chart', 'make a line plot')."}]
                }

            # Use enhanced chart processing for better performance and UX
            try:
                image_path = result["image_path"]
                logger.info(f"Processing chart with enhanced optimization: {image_path}")

                if not os.path.exists(image_path):
                    logger.error(f"Chart image file does not exist: {image_path}")
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": "The visualization was generated but the image file could not be found."}]
                    }

                # Use the enhanced chart response system
                enhanced_response = chart_enhancer.enhance_chart_response(
                    image_path=image_path,
                    query=prompt,
                    provider=provider,
                    optimize=True  # Enable optimization for better performance
                )

                # Extract interactive chart data alongside the static image
                interactive_chart_data = None
                try:
                    logger.info("🎨 STARTING INTERACTIVE CHART DATA EXTRACTION")

                    # Try to get DataFrame data from the current context
                    dataframe_data = None
                    logger.info(f"🎨 Checking for cached DataFrame: hasattr={hasattr(self, '_last_dataframe')}, not None={getattr(self, '_last_dataframe', None) is not None}")

                    if hasattr(self, '_last_dataframe') and self._last_dataframe is not None:
                        # Convert DataFrame to dict format for the interactive tool
                        dataframe_data = self._last_dataframe.to_dict('records')
                        logger.info(f"🎨 Using cached DataFrame with {len(dataframe_data)} records for interactive chart")
                        logger.info(f"🎨 DataFrame columns: {list(self._last_dataframe.columns)}")
                        logger.info(f"🎨 DataFrame shape: {self._last_dataframe.shape}")
                    else:
                        logger.warning("🎨 No cached DataFrame available for interactive chart generation")
                        logger.warning(f"🎨 _last_dataframe value: {getattr(self, '_last_dataframe', 'ATTRIBUTE_NOT_FOUND')}")

                    # Detect chart type from original query for better hint
                    def detect_chart_type_hint(query: str) -> str:
                        query_lower = query.lower()
                        if any(word in query_lower for word in ["pie", "proportion", "percentage", "share", "distribution"]):
                            return "pie"
                        elif any(word in query_lower for word in ["line", "trend", "over time", "timeline"]):
                            return "line"
                        elif any(word in query_lower for word in ["scatter", "correlation", "relationship"]):
                            return "scatter"
                        elif any(word in query_lower for word in ["bar", "compare", "comparison"]):
                            return "bar"
                        else:
                            return "auto"

                    # Use original query for chart type detection, enhanced prompt for processing
                    chart_type_hint = detect_chart_type_hint(prompt)

                    logger.info(f"🎨 CALLING INTERACTIVE CHART TOOL")
                    logger.info(f"🎨 Original prompt: {prompt}")
                    logger.info(f"🎨 Detected chart type hint: {chart_type_hint}")
                    logger.info(f"🎨 DataFrame shape: {dataframe_data.get('shape') if isinstance(dataframe_data, dict) else 'Unknown'}")

                    interactive_result = await self.interactive_chart_tool.execute(
                        query=prompt,  # Use enhanced prompt for processing
                        file_path=file_path if file_path else None,
                        dataframe_data=dataframe_data,
                        chart_type_hint=chart_type_hint  # Use detected hint
                    )

                    logger.info(f"🎨 Interactive chart tool result: {interactive_result}")
                    logger.info(f"🎨 Interactive result isError: {interactive_result.get('isError', 'Not specified')}")

                    if not interactive_result.get("isError", True):
                        interactive_chart_data = interactive_result.get("interactive_chart")
                        logger.info("🎨 Successfully extracted interactive chart data")
                        logger.info(f"🎨 Interactive chart data keys: {list(interactive_chart_data.keys()) if interactive_chart_data else 'None'}")

                        # Debug the chart data specifically for pie charts
                        if interactive_chart_data and chart_type_hint == "pie":
                            chart_data = interactive_chart_data.get("chart_data", [])
                            logger.info(f"🥧 PIE CHART FINAL RESULT: {len(chart_data)} items")
                            logger.info(f"🥧 PIE CHART DATA: {chart_data}")

                    else:
                        logger.warning(f"🎨 Interactive chart data extraction failed: {interactive_result.get('error', 'Unknown error')}")
                        logger.warning(f"🎨 Full interactive result: {interactive_result}")

                except Exception as e:
                    logger.warning(f"Error extracting interactive chart data: {e}")
                    # Continue with static image only

                # If enhancement succeeded, use the enhanced response
                if not enhanced_response.get("isError", False):
                    logger.info("Successfully enhanced chart response with optimization")

                    # Extract the enhanced metadata for our response
                    enhanced_metadata = enhanced_response.get("metadata", {})

                    # Try to extract interactive chart data
                    interactive_chart_data = None
                    try:
                        logger.info("Attempting to extract interactive chart data")

                        # Try to get DataFrame data from the current context
                        dataframe_data = None
                        if hasattr(self, '_last_dataframe') and self._last_dataframe is not None:
                            # Convert DataFrame to dict format for the interactive tool
                            dataframe_data = self._last_dataframe.to_dict('records')
                            logger.info(f"🎨 Using cached DataFrame with {len(dataframe_data)} records for interactive chart (fallback)")
                            logger.info(f"🎨 DataFrame columns: {list(self._last_dataframe.columns)}")
                        else:
                            logger.warning("🎨 No cached DataFrame available for interactive chart generation (fallback)")

                        interactive_result = await self.interactive_chart_tool.execute(
                            query=prompt,
                            file_path=file_path if file_path else None,
                            dataframe_data=dataframe_data,
                            chart_type_hint="auto"
                        )

                        logger.info(f"🎨 Interactive chart tool result (fallback): {interactive_result}")

                        if not interactive_result.get("isError", True):
                            interactive_chart_data = interactive_result.get("interactive_chart")
                            logger.info("🎨 Successfully extracted interactive chart data (fallback)")
                            logger.info(f"🎨 Interactive chart data keys (fallback): {list(interactive_chart_data.keys()) if interactive_chart_data else 'None'}")
                        else:
                            logger.warning(f"🎨 Interactive chart data extraction failed (fallback): {interactive_result.get('error', 'Unknown error')}")
                            logger.warning(f"🎨 Full interactive result (fallback): {interactive_result}")
                    except Exception as e:
                        logger.warning(f"Error extracting interactive chart data: {e}")

                    # Create visualization data with enhanced metadata and optional interactive data
                    # Use interactive_chart type if we have interactive data, otherwise use chart type
                    viz_type = "interactive_chart" if interactive_chart_data else "chart"

                    if interactive_chart_data:
                        # For interactive charts, structure data properly for frontend
                        chart_data = interactive_chart_data.get("data", {}).get("chart_data", [])
                        chart_type = interactive_chart_data.get("chart_type", "bar")

                        # Ensure chart_data is properly formatted
                        if not chart_data or not isinstance(chart_data, list):
                            logger.warning("🎨 Interactive chart data is empty or invalid, using fallback")
                            chart_data = []

                        visualization_data = {
                            "type": "interactive_chart",
                            "title": interactive_chart_data.get("title", f"Visualization for: {prompt}"),
                            "description": interactive_chart_data.get("description", f"Interactive chart generated using {provider} provider"),
                            "data": {
                                "chart_data": chart_data,
                                "chart_type": chart_type,
                                "columns": interactive_chart_data.get("data", {}).get("columns", []),
                                "metadata": {
                                    "x_axis": interactive_chart_data.get("data", {}).get("metadata", {}).get("x_axis"),
                                    "y_axes": interactive_chart_data.get("data", {}).get("metadata", {}).get("y_axes", []),
                                    "color_column": interactive_chart_data.get("data", {}).get("metadata", {}).get("color_column"),
                                    "data_type": "backend_generated",
                                    "total_records": len(chart_data),
                                    "provider": provider,
                                    "model": model
                                },
                                "fallback_image": enhanced_response["content"][1]["src"]  # Keep static image as fallback
                            },
                            "config": {
                                "type": "interactive_chart",
                                "chart_type": chart_type,
                                "responsive": True,
                                "interactive": True,
                                "optimized": True,
                                "animation": True
                            }
                        }
                        logger.info(f"🎨 Created interactive visualization with chart_type: {chart_type}")
                        logger.info(f"🎨 Chart data records: {len(chart_data)}")
                        logger.info(f"🎨 Chart metadata: {visualization_data['data']['metadata']}")
                    else:
                        # Fallback to static chart
                        visualization_data = {
                            "type": "chart",
                            "title": f"Visualization for: {prompt}",
                            "description": f"Generated using {provider} provider",
                            "data": {
                                "image": enhanced_response["content"][1]["src"],  # Use optimized image
                                "metadata": enhanced_metadata.get("chart_info", {}),
                                "optimization": enhanced_metadata.get("optimization", {}),
                                "performance": enhanced_metadata.get("performance", {})
                            },
                            "config": {
                                "type": "enhanced_chart",
                                "options": {
                                    "responsive": True,
                                    "optimized": True,
                                    "interactive": False
                                }
                            }
                        }
                else:
                    # Fallback to basic processing if enhancement fails
                    logger.warning("Chart enhancement failed, using basic processing")
                    with open(image_path, "rb") as image_file:
                        encoded_image = base64.b64encode(image_file.read()).decode("utf-8")

                    # Try to extract interactive chart data even in fallback mode
                    interactive_chart_data = None
                    try:
                        logger.info("Attempting to extract interactive chart data in fallback mode")

                        # Try to get DataFrame data from the current context
                        dataframe_data = None
                        if hasattr(self, '_last_dataframe') and self._last_dataframe is not None:
                            # Convert DataFrame to dict format for the interactive tool
                            dataframe_data = self._last_dataframe.to_dict('records')
                            logger.info(f"🎨 Using cached DataFrame with {len(dataframe_data)} records for interactive chart (fallback mode)")
                            logger.info(f"🎨 DataFrame columns: {list(self._last_dataframe.columns)}")
                        else:
                            logger.warning("🎨 No cached DataFrame available for interactive chart generation (fallback mode)")

                        interactive_result = await self.interactive_chart_tool.execute(
                            query=prompt,
                            file_path=file_path if file_path else None,
                            dataframe_data=dataframe_data,
                            chart_type_hint="auto"
                        )

                        logger.info(f"🎨 Interactive chart tool result (fallback mode): {interactive_result}")

                        if not interactive_result.get("isError", True):
                            interactive_chart_data = interactive_result.get("interactive_chart")
                            logger.info("🎨 Successfully extracted interactive chart data in fallback mode")
                            logger.info(f"🎨 Interactive chart data keys (fallback mode): {list(interactive_chart_data.keys()) if interactive_chart_data else 'None'}")
                        else:
                            logger.warning(f"🎨 Interactive chart data extraction failed in fallback: {interactive_result.get('error', 'Unknown error')}")
                            logger.warning(f"🎨 Full interactive result (fallback mode): {interactive_result}")

                    except Exception as e:
                        logger.warning(f"Error extracting interactive chart data in fallback: {e}")

                    # Handle fallback case with or without interactive data
                    if interactive_chart_data:
                        # Use interactive chart even if enhancement failed
                        visualization_data = {
                            "type": "interactive_chart",
                            "title": interactive_chart_data.get("title", f"Visualization for: {prompt}"),
                            "description": interactive_chart_data.get("description", f"Interactive chart generated using {provider} provider"),
                            "data": {
                                **interactive_chart_data.get("data", {}),
                                "fallback_image": f"data:image/png;base64,{encoded_image}"
                            },
                            "config": {
                                **interactive_chart_data.get("config", {}),
                                "type": "interactive_chart",
                                "options": {
                                    "responsive": True,
                                    "interactive": True
                                }
                            }
                        }
                        logger.info("🎨 Using interactive chart despite enhancement failure")
                        logger.info(f"🎨 Fallback interactive chart data: {interactive_chart_data}")
                        logger.info(f"🎨 Fallback visualization type: {visualization_data['type']}")
                    else:
                        # Pure static fallback
                        visualization_data = {
                            "type": "chart",
                            "title": f"Visualization for: {prompt}",
                            "description": f"Generated using {provider} provider",
                            "data": {
                                "image": f"data:image/png;base64,{encoded_image}",
                                "metadata": {"enhancement_failed": True}
                            },
                            "config": {
                                "type": "basic_chart",
                                "options": {
                                    "interactive": False
                                }
                            }
                        }

                # Create metadata and sanitize it to handle any potential NaN values
                metadata = {
                    "file_path": file_path,
                    "prompt": prompt,
                    "provider": provider,
                    "image_path": image_path,
                    "visualization": visualization_data  # Add the visualization data to the metadata
                }

                # Sanitize metadata to ensure it's JSON serializable
                sanitized_metadata = sanitize_json(metadata)

                # Create response with enhanced visualization data
                if visualization_data["type"] == "interactive_chart":
                    # For interactive charts, include the structured data in metadata
                    enhanced_metadata = {
                        **sanitized_metadata,
                        "visualization": visualization_data,
                        "interactive_chart": {
                            "chart_data": visualization_data["data"]["chart_data"],
                            "chart_type": visualization_data["data"]["chart_type"],
                            "metadata": visualization_data["data"]["metadata"],
                            "config": visualization_data["config"]
                        },
                        "task_type": "pandasai_visualization",
                        "data_source": "backend_generated"
                    }

                    response = {
                        "isError": False,
                        "content": [
                            {"type": "text", "text": f"📊 Generated interactive visualization for: {prompt}"}
                        ],
                        "metadata": enhanced_metadata
                    }
                    logger.info(f"🎨 Interactive chart response metadata keys: {list(enhanced_metadata.keys())}")
                    logger.info(f"🎨 Interactive chart data records in response: {len(enhanced_metadata['interactive_chart']['chart_data'])}")
                else:
                    # For static charts, include the image
                    response = {
                        "isError": False,
                        "content": [
                            {"type": "text", "text": f"📊 Generated visualization for: {prompt}"},
                            {
                                "type": "image",
                                "src": visualization_data["data"]["image"],
                                "metadata": visualization_data["data"].get("metadata", {})
                            }
                        ],
                        "metadata": sanitized_metadata
                    }

                # Cache the response
                self.cache.set(file_path, prompt, provider, response)
                logger.info("Successfully generated and cached visualization")

                # Store the visualization in memory if requested
                store_in_memory = arguments.get("store_in_memory", True)
                if store_in_memory:
                    user_id = arguments.get("user_id", "system")
                    persona_id = arguments.get("persona_id", "unknown")
                    conversation_id = arguments.get("conversation_id", "unknown")

                    # Create metadata for the memory
                    memory_metadata = {
                        "type": "visualization",
                        "file_path": file_path,
                        "prompt": prompt,
                        "provider": provider,
                        "persona_id": persona_id,
                        "conversation_id": conversation_id,
                        "visualization_type": "pandasai",
                        "image_path": image_path
                    }

                    # Store in memory
                    try:
                        self.memory_service.add_memory(
                            content=f"Visualization of {os.path.basename(file_path)}: {prompt}",
                            user_id=user_id,
                            metadata=memory_metadata
                        )
                        logger.info(f"Stored visualization in memory for user {user_id}")
                    except Exception as e:
                        logger.error(f"Error storing visualization in memory: {e}")

                return response
            except Exception as e:
                logger.error(f"Error reading chart image: {e}", exc_info=True)
                error_metadata = {
                    "error_type": e.__class__.__name__,
                    "error_details": str(e),
                    "file_path": file_path,
                    "prompt": prompt,
                    "provider": provider
                }

                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error displaying chart: {str(e)}"}],
                    "metadata": sanitize_json(error_metadata)
                }

        except Exception as e:
            logger.error(f"Error executing PandasAI visualization tool: {e}", exc_info=True)
            error_metadata = {
                "error_type": e.__class__.__name__,
                "error_details": str(e),
                "component": "PandasAIVisualizationTool"
            }

            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Error: {str(e)}"}],
                "metadata": sanitize_json(error_metadata)
            }

    def _create_fallback_visualization(self, file_path: str, prompt: str) -> Dict[str, Any]:
        """Create a basic fallback visualization when PandasAI fails."""
        try:
            logger.info(f"Creating fallback visualization for: {prompt}")

            # Load the data directly
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            elif file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            else:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "Fallback visualization only supports CSV and Excel files."}]
                }

            logger.info(f"Loaded dataframe with shape: {df.shape}")
            logger.info(f"Columns: {list(df.columns)}")

            # Create a simple visualization based on the data
            plt.figure(figsize=(10, 6))

            # Try to create a meaningful chart based on data types
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()

            if len(categorical_cols) > 0 and len(numeric_cols) > 0:
                # Create a bar chart of the first categorical column
                cat_col = categorical_cols[0]
                if len(numeric_cols) > 0:
                    # Group by categorical and sum numeric
                    grouped = df.groupby(cat_col)[numeric_cols[0]].sum().head(10)
                    grouped.plot(kind='bar')
                    plt.title(f'{numeric_cols[0]} by {cat_col}')
                else:
                    # Just count the categorical values
                    df[cat_col].value_counts().head(10).plot(kind='bar')
                    plt.title(f'Count of {cat_col}')
            elif len(categorical_cols) > 0:
                # Just categorical data - create a count plot
                df[categorical_cols[0]].value_counts().head(10).plot(kind='bar')
                plt.title(f'Distribution of {categorical_cols[0]}')
            elif len(numeric_cols) >= 2:
                # Numeric data - create a scatter plot
                plt.scatter(df[numeric_cols[0]], df[numeric_cols[1]], alpha=0.6)
                plt.xlabel(numeric_cols[0])
                plt.ylabel(numeric_cols[1])
                plt.title(f'{numeric_cols[0]} vs {numeric_cols[1]}')
            else:
                # Single numeric column - histogram
                if len(numeric_cols) > 0:
                    df[numeric_cols[0]].hist(bins=20)
                    plt.title(f'Distribution of {numeric_cols[0]}')
                else:
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": "Unable to create a meaningful visualization from this data."}]
                    }

            plt.xticks(rotation=45)
            plt.tight_layout()

            # Save the chart
            chart_filename = f"temp_fallback_chart_{uuid.uuid4().hex[:8]}.png"
            chart_path = os.path.join("exports", "charts", chart_filename)
            os.makedirs(os.path.dirname(chart_path), exist_ok=True)
            plt.savefig(chart_path, dpi=150, bbox_inches='tight')
            plt.close()

            # Convert to base64
            with open(chart_path, "rb") as image_file:
                encoded_image = base64.b64encode(image_file.read()).decode("utf-8")

            # Clean up the file
            try:
                os.remove(chart_path)
            except:
                pass

            return {
                "isError": False,
                "content": [
                    {"type": "text", "text": f"📊 Created fallback visualization for: {prompt}"},
                    {
                        "type": "image",
                        "src": f"data:image/png;base64,{encoded_image}"
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "prompt": prompt,
                    "visualization_type": "fallback",
                    "chart_path": chart_path
                }
            }

        except Exception as e:
            logger.error(f"Error creating fallback visualization: {e}", exc_info=True)
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Failed to create fallback visualization: {str(e)}"}]
            }
