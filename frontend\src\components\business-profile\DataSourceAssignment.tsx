/**
 * Data Source Assignment Component
 * 
 * Component for assigning data sources to business profiles with role selection.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Database, FileUp, RefreshCw, MessageSquare, Trash2, Edit, ArrowUpDown } from 'lucide-react';
import { DataSource } from '@/lib/dataSourceApi';
import { 
  BusinessProfileDataSourceAssignment, 
  BusinessProfileDataSourceAssignmentCreate,
  businessProfileApi,
  dataSourceRoleOptions,
  getDataSourceRoleLabel 
} from '@/lib/businessProfileApi';
import { useToast } from '@/hooks/use-toast';

interface DataSourceAssignmentProps {
  profileId: string;
  assignments: BusinessProfileDataSourceAssignment[];
  availableDataSources: DataSource[];
  onAssignmentsChange: (assignments: BusinessProfileDataSourceAssignment[]) => void;
  className?: string;
}

export const DataSourceAssignment: React.FC<DataSourceAssignmentProps> = ({
  profileId,
  assignments,
  availableDataSources,
  onAssignmentsChange,
  className
}) => {
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [selectedDataSources, setSelectedDataSources] = useState<string[]>([]);
  const [defaultRole, setDefaultRole] = useState<string>('other');
  const [isAssigning, setIsAssigning] = useState(false);
  const { toast } = useToast();

  // Get unassigned data sources
  const assignedDataSourceIds = assignments.map(a => a.data_source_id);
  const unassignedDataSources = availableDataSources.filter(
    ds => !assignedDataSourceIds.includes(ds.id)
  );

  const getDataSourceIcon = (type: string) => {
    switch (type) {
      case 'file': return FileUp;
      case 'database': return Database;
      case 'api': return RefreshCw;
      case 'mcp': return MessageSquare;
      default: return Database;
    }
  };

  const getDataSourceTypeLabel = (type: string) => {
    switch (type) {
      case 'file': return 'File Upload';
      case 'database': return 'Database';
      case 'api': return 'API';
      case 'mcp': return 'MCP Server';
      default: return type;
    }
  };

  const handleAssignDataSources = async () => {
    if (selectedDataSources.length === 0) return;

    try {
      setIsAssigning(true);
      const newAssignments: BusinessProfileDataSourceAssignment[] = [];

      for (const dataSourceId of selectedDataSources) {
        const assignmentData: BusinessProfileDataSourceAssignmentCreate = {
          data_source_id: dataSourceId,
          role: defaultRole as any,
          priority: assignments.length + newAssignments.length + 1,
          is_active: true
        };

        const assignment = await businessProfileApi.assignDataSource(profileId, assignmentData);
        newAssignments.push(assignment);
      }

      // Update assignments
      onAssignmentsChange([...assignments, ...newAssignments]);
      
      // Reset dialog state
      setSelectedDataSources([]);
      setDefaultRole('other');
      setIsAssignDialogOpen(false);

      toast({
        title: 'Data Sources Assigned',
        description: `Successfully assigned ${newAssignments.length} data source${newAssignments.length !== 1 ? 's' : ''}`,
      });
    } catch (error) {
      console.error('Error assigning data sources:', error);
      toast({
        title: 'Error',
        description: 'Failed to assign data sources',
        variant: 'destructive',
      });
    } finally {
      setIsAssigning(false);
    }
  };

  const handleUnassignDataSource = async (assignment: BusinessProfileDataSourceAssignment) => {
    try {
      await businessProfileApi.unassignDataSource(profileId, assignment.data_source_id);
      
      // Update assignments
      const updatedAssignments = assignments.filter(a => a.id !== assignment.id);
      onAssignmentsChange(updatedAssignments);

      toast({
        title: 'Data Source Unassigned',
        description: 'Data source removed from business profile',
      });
    } catch (error) {
      console.error('Error unassigning data source:', error);
      toast({
        title: 'Error',
        description: 'Failed to unassign data source',
        variant: 'destructive',
      });
    }
  };

  const handleUpdateRole = async (assignment: BusinessProfileDataSourceAssignment, newRole: string) => {
    try {
      const updatedAssignment = await businessProfileApi.updateDataSourceAssignment(
        profileId,
        assignment.data_source_id,
        { role: newRole as any }
      );

      // Update assignments
      const updatedAssignments = assignments.map(a => 
        a.id === assignment.id ? updatedAssignment : a
      );
      onAssignmentsChange(updatedAssignments);

      toast({
        title: 'Role Updated',
        description: 'Data source role updated successfully',
      });
    } catch (error) {
      console.error('Error updating role:', error);
      toast({
        title: 'Error',
        description: 'Failed to update data source role',
        variant: 'destructive',
      });
    }
  };

  const handleDataSourceSelection = (dataSourceId: string, checked: boolean) => {
    if (checked) {
      setSelectedDataSources(prev => [...prev, dataSourceId]);
    } else {
      setSelectedDataSources(prev => prev.filter(id => id !== dataSourceId));
    }
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Data Sources
              </CardTitle>
              <CardDescription>
                Assign data sources to this business profile and define their roles
              </CardDescription>
            </div>
            {unassignedDataSources.length > 0 && (
              <Button onClick={() => setIsAssignDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Assign Data Sources
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {assignments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No Data Sources Assigned</p>
              <p className="text-sm mb-4">
                Assign data sources to provide context for AI agents
              </p>
              {unassignedDataSources.length > 0 && (
                <Button onClick={() => setIsAssignDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Assign Your First Data Source
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {assignments.map((assignment) => {
                const dataSource = availableDataSources.find(ds => ds.id === assignment.data_source_id);
                if (!dataSource) return null;

                const IconComponent = getDataSourceIcon(dataSource.type);

                return (
                  <div key={assignment.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3 min-w-0">
                      <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <IconComponent className="h-5 w-5 text-primary" />
                      </div>
                      <div className="min-w-0">
                        <h4 className="font-medium truncate">{dataSource.name}</h4>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Badge variant="outline" className="text-xs">
                            {getDataSourceTypeLabel(dataSource.type)}
                          </Badge>
                          <span>•</span>
                          <span>Priority {assignment.priority}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <Select
                        value={assignment.role || 'other'}
                        onValueChange={(value) => handleUpdateRole(assignment, value)}
                      >
                        <SelectTrigger className="w-[180px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {dataSourceRoleOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUnassignDataSource(assignment)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Assign Data Sources Dialog */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Assign Data Sources</DialogTitle>
            <DialogDescription>
              Select data sources to assign to this business profile and set their default role.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="default-role">Default Role for Selected Data Sources</Label>
              <Select value={defaultRole} onValueChange={setDefaultRole}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dataSourceRoleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Available Data Sources</Label>
              <div className="max-h-[300px] overflow-y-auto space-y-2">
                {unassignedDataSources.map((dataSource) => {
                  const IconComponent = getDataSourceIcon(dataSource.type);
                  const isSelected = selectedDataSources.includes(dataSource.id);

                  return (
                    <div key={dataSource.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => handleDataSourceSelection(dataSource.id, checked as boolean)}
                      />
                      <div className="w-8 h-8 rounded bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <IconComponent className="h-4 w-4 text-primary" />
                      </div>
                      <div className="min-w-0">
                        <p className="font-medium truncate">{dataSource.name}</p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Badge variant="outline" className="text-xs">
                            {getDataSourceTypeLabel(dataSource.type)}
                          </Badge>
                          {dataSource.description && (
                            <>
                              <span>•</span>
                              <span className="truncate">{dataSource.description}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleAssignDataSources}
              disabled={selectedDataSources.length === 0 || isAssigning}
            >
              {isAssigning ? 'Assigning...' : `Assign ${selectedDataSources.length} Data Source${selectedDataSources.length !== 1 ? 's' : ''}`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
