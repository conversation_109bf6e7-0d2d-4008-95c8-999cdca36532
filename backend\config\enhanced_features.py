"""
Enhanced features configuration for Datagenius.

This module provides configuration for enhanced marketing agent capabilities
including intelligent caching, analytics, monitoring, and error handling.
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class EnhancedFeaturesConfig:
    """Configuration for enhanced marketing agent features."""
    
    # Intent Detection
    enable_ai_intent_detection: bool = True
    intent_detection_confidence_threshold: float = 0.7
    intent_cache_ttl: int = 300  # 5 minutes
    
    # Error Handling
    enable_enhanced_error_handling: bool = True
    user_friendly_errors: bool = True
    error_tracking: bool = True
    
    # Analytics
    enable_analytics: bool = True
    analytics_backend: str = "memory"  # "memory", "redis", "database"
    track_user_interactions: bool = True
    track_performance_metrics: bool = True
    
    # Caching
    enable_intelligent_caching: bool = True
    cache_backend: str = "memory"  # "memory", "redis"
    cache_ttl: int = 3600  # 1 hour
    max_cache_size: int = 1000
    
    # Monitoring
    enable_performance_monitoring: bool = True
    monitoring_interval: int = 60  # seconds
    health_check_enabled: bool = True
    
    # Quick Actions
    enable_quick_action_buttons: bool = True
    quick_actions: list = None
    
    # Conversational Features
    enable_conversational_follow_ups: bool = True
    context_preservation: bool = True
    conversation_memory_limit: int = 10  # messages
    
    def __post_init__(self):
        """Initialize default quick actions if not provided."""
        if self.quick_actions is None:
            self.quick_actions = [
                {
                    "id": "marketing_strategy",
                    "label": "Create Marketing Strategy",
                    "description": "Generate a comprehensive marketing strategy",
                    "icon": "strategy",
                    "category": "strategy"
                },
                {
                    "id": "campaign_strategy", 
                    "label": "Create Campaign Strategy",
                    "description": "Develop a targeted campaign plan",
                    "icon": "campaign",
                    "category": "campaign"
                },
                {
                    "id": "social_media_content",
                    "label": "Social Media Content",
                    "description": "Create engaging social media posts",
                    "icon": "social",
                    "category": "content"
                },
                {
                    "id": "seo_optimization",
                    "label": "SEO Optimization",
                    "description": "Optimize content for search engines",
                    "icon": "seo",
                    "category": "seo"
                },
                {
                    "id": "content_analysis",
                    "label": "Analyze Content",
                    "description": "Analyze existing marketing content",
                    "icon": "analysis",
                    "category": "analysis"
                }
            ]


def load_enhanced_features_config() -> EnhancedFeaturesConfig:
    """
    Load enhanced features configuration from environment variables.
    
    Returns:
        EnhancedFeaturesConfig: Configuration object
    """
    config = EnhancedFeaturesConfig()
    
    # Intent Detection
    config.enable_ai_intent_detection = os.getenv("ENABLE_AI_INTENT_DETECTION", "true").lower() == "true"
    config.intent_detection_confidence_threshold = float(os.getenv("INTENT_CONFIDENCE_THRESHOLD", "0.7"))
    config.intent_cache_ttl = int(os.getenv("INTENT_CACHE_TTL", "300"))
    
    # Error Handling
    config.enable_enhanced_error_handling = os.getenv("ENABLE_ENHANCED_ERROR_HANDLING", "true").lower() == "true"
    config.user_friendly_errors = os.getenv("USER_FRIENDLY_ERRORS", "true").lower() == "true"
    config.error_tracking = os.getenv("ERROR_TRACKING", "true").lower() == "true"
    
    # Analytics
    config.enable_analytics = os.getenv("ENABLE_ANALYTICS", "true").lower() == "true"
    config.analytics_backend = os.getenv("ANALYTICS_BACKEND", "memory")
    config.track_user_interactions = os.getenv("TRACK_USER_INTERACTIONS", "true").lower() == "true"
    config.track_performance_metrics = os.getenv("TRACK_PERFORMANCE_METRICS", "true").lower() == "true"
    
    # Caching
    config.enable_intelligent_caching = os.getenv("ENABLE_INTELLIGENT_CACHING", "true").lower() == "true"
    config.cache_backend = os.getenv("CACHE_BACKEND", "memory")
    config.cache_ttl = int(os.getenv("CACHE_TTL", "3600"))
    config.max_cache_size = int(os.getenv("MAX_CACHE_SIZE", "1000"))
    
    # Monitoring
    config.enable_performance_monitoring = os.getenv("ENABLE_PERFORMANCE_MONITORING", "true").lower() == "true"
    config.monitoring_interval = int(os.getenv("MONITORING_INTERVAL", "60"))
    config.health_check_enabled = os.getenv("HEALTH_CHECK_ENABLED", "true").lower() == "true"
    
    # Quick Actions
    config.enable_quick_action_buttons = os.getenv("ENABLE_QUICK_ACTION_BUTTONS", "true").lower() == "true"
    
    # Conversational Features
    config.enable_conversational_follow_ups = os.getenv("ENABLE_CONVERSATIONAL_FOLLOW_UPS", "true").lower() == "true"
    config.context_preservation = os.getenv("CONTEXT_PRESERVATION", "true").lower() == "true"
    config.conversation_memory_limit = int(os.getenv("CONVERSATION_MEMORY_LIMIT", "10"))
    
    logger.info("Enhanced features configuration loaded")
    logger.info(f"AI Intent Detection: {config.enable_ai_intent_detection}")
    logger.info(f"Enhanced Error Handling: {config.enable_enhanced_error_handling}")
    logger.info(f"Analytics: {config.enable_analytics}")
    logger.info(f"Intelligent Caching: {config.enable_intelligent_caching}")
    logger.info(f"Performance Monitoring: {config.enable_performance_monitoring}")
    
    return config


def get_redis_config() -> Dict[str, Any]:
    """
    Get Redis configuration for caching and analytics.
    
    Returns:
        Dict containing Redis configuration
    """
    return {
        "host": os.getenv("REDIS_HOST", "localhost"),
        "port": int(os.getenv("REDIS_PORT", "6379")),
        "db": int(os.getenv("REDIS_DB", "0")),
        "password": os.getenv("REDIS_PASSWORD"),
        "decode_responses": True,
        "socket_connect_timeout": 5,
        "socket_timeout": 5,
        "retry_on_timeout": True,
        "health_check_interval": 30
    }


def get_analytics_config() -> Dict[str, Any]:
    """
    Get analytics configuration.
    
    Returns:
        Dict containing analytics configuration
    """
    config = load_enhanced_features_config()
    
    return {
        "enabled": config.enable_analytics,
        "backend": config.analytics_backend,
        "track_user_interactions": config.track_user_interactions,
        "track_performance_metrics": config.track_performance_metrics,
        "redis_config": get_redis_config() if config.analytics_backend == "redis" else None,
        "retention_days": int(os.getenv("ANALYTICS_RETENTION_DAYS", "30")),
        "batch_size": int(os.getenv("ANALYTICS_BATCH_SIZE", "100")),
        "flush_interval": int(os.getenv("ANALYTICS_FLUSH_INTERVAL", "60"))
    }


def get_cache_config() -> Dict[str, Any]:
    """
    Get caching configuration.
    
    Returns:
        Dict containing cache configuration
    """
    config = load_enhanced_features_config()
    
    return {
        "enabled": config.enable_intelligent_caching,
        "backend": config.cache_backend,
        "ttl": config.cache_ttl,
        "max_size": config.max_cache_size,
        "redis_config": get_redis_config() if config.cache_backend == "redis" else None,
        "compression": os.getenv("CACHE_COMPRESSION", "true").lower() == "true",
        "key_prefix": os.getenv("CACHE_KEY_PREFIX", "datagenius:cache:")
    }


def get_monitoring_config() -> Dict[str, Any]:
    """
    Get monitoring configuration.
    
    Returns:
        Dict containing monitoring configuration
    """
    config = load_enhanced_features_config()
    
    return {
        "enabled": config.enable_performance_monitoring,
        "interval": config.monitoring_interval,
        "health_check_enabled": config.health_check_enabled,
        "metrics_retention": int(os.getenv("METRICS_RETENTION_HOURS", "24")),
        "alert_thresholds": {
            "response_time_ms": int(os.getenv("ALERT_RESPONSE_TIME_MS", "5000")),
            "error_rate_percent": float(os.getenv("ALERT_ERROR_RATE_PERCENT", "5.0")),
            "memory_usage_percent": float(os.getenv("ALERT_MEMORY_USAGE_PERCENT", "80.0"))
        }
    }


# Global configuration instance
_enhanced_config: Optional[EnhancedFeaturesConfig] = None


def get_enhanced_config() -> EnhancedFeaturesConfig:
    """
    Get the global enhanced features configuration.
    
    Returns:
        EnhancedFeaturesConfig: Global configuration instance
    """
    global _enhanced_config
    if _enhanced_config is None:
        _enhanced_config = load_enhanced_features_config()
    return _enhanced_config


def reload_enhanced_config() -> EnhancedFeaturesConfig:
    """
    Reload the enhanced features configuration.
    
    Returns:
        EnhancedFeaturesConfig: Reloaded configuration instance
    """
    global _enhanced_config
    _enhanced_config = load_enhanced_features_config()
    return _enhanced_config


def is_feature_enabled(feature_name: str) -> bool:
    """
    Check if a specific enhanced feature is enabled.
    
    Args:
        feature_name: Name of the feature to check
        
    Returns:
        bool: True if feature is enabled, False otherwise
    """
    config = get_enhanced_config()
    return getattr(config, f"enable_{feature_name}", False)


def get_quick_actions() -> list:
    """
    Get the configured quick actions for the marketing agent.
    
    Returns:
        List of quick action configurations
    """
    config = get_enhanced_config()
    return config.quick_actions if config.enable_quick_action_buttons else []


# Export commonly used configurations
__all__ = [
    "EnhancedFeaturesConfig",
    "load_enhanced_features_config",
    "get_enhanced_config",
    "reload_enhanced_config",
    "is_feature_enabled",
    "get_quick_actions",
    "get_redis_config",
    "get_analytics_config",
    "get_cache_config",
    "get_monitoring_config"
]
