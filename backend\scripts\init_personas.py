"""
Initialize personas from YAML files.

This script loads all persona configurations from YAML files and inserts them into the database.
"""

import os
import sys
import yaml
import logging
from pathlib import Path

# Add the parent directory to sys.path to allow importing from app
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from sqlalchemy.orm import Session
from app.database import get_db, get_persona, create_persona, update_persona
from app.models.persona import PersonaBase

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def load_personas_from_yaml(personas_dir: str = None) -> None:
    """
    Load personas from YAML files and insert them into the database.

    Args:
        personas_dir: Directory containing persona configuration files
    """
    if personas_dir is None:
        # Use absolute path to personas directory
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        personas_dir = os.path.join(parent_dir, "personas")

    logger.info(f"Loading personas from {personas_dir}")
    config_path = Path(personas_dir)

    if not config_path.exists():
        logger.warning(f"Configuration directory {personas_dir} does not exist")
        return

    # Get database session
    logger.debug("Getting database session")
    db = next(get_db())
    logger.debug("Database session obtained")

    # Load main persona configurations
    logger.debug(f"Searching for YAML files in {config_path}")
    yaml_files = list(config_path.glob("*.yaml"))
    logger.debug(f"Found {len(yaml_files)} YAML files")

    for file_path in yaml_files:
        logger.debug(f"Processing file: {file_path}")
        try:
            # Skip version files (they have a dash in the filename)
            if "-" in file_path.stem:
                logger.debug(f"Skipping version file: {file_path}")
                continue

            with open(file_path, "r") as f:
                config = yaml.safe_load(f)

            persona_id = config.get("id")
            if not persona_id:
                logger.warning(f"Missing persona ID in configuration file {file_path}")
                continue

            # Check if persona exists in database
            existing_persona = get_persona(db, persona_id)

            try:
                if existing_persona:
                    logger.info(f"Persona '{persona_id}' already exists in database, updating")
                    # Update persona with latest configuration
                    update_data = {
                        "name": config.get("name", existing_persona.name),
                        "description": config.get("description", existing_persona.description),
                        "industry": config.get("industry", existing_persona.industry),
                        "skills": config.get("skills", existing_persona.skills),
                        "rating": config.get("rating", existing_persona.rating),
                        "review_count": config.get("review_count", existing_persona.review_count),
                        "image_url": config.get("image_url", existing_persona.image_url),
                        "price": config.get("price", existing_persona.price),
                        "provider": config.get("provider", existing_persona.provider),
                        "model": config.get("model", existing_persona.model),
                        "is_active": config.get("is_active", existing_persona.is_active),
                        "age_restriction": config.get("age_restriction", existing_persona.age_restriction),
                        "content_filters": config.get("content_filters", existing_persona.content_filters)
                    }
                    logger.debug(f"Update data for persona '{persona_id}': {update_data}")
                    updated_persona = update_persona(db, persona_id, update_data)
                    logger.info(f"Successfully updated persona '{persona_id}' in database")
                else:
                    logger.info(f"Creating new persona '{persona_id}' in database")
                    # Create new persona
                    create_data = {
                        "id": persona_id,
                        "name": config.get("name", ""),
                        "description": config.get("description", ""),
                        "industry": config.get("industry", ""),
                        "skills": config.get("skills", []),
                        "rating": config.get("rating", 4.5),
                        "review_count": config.get("review_count", 0),
                        "image_url": config.get("image_url", "/placeholder.svg"),
                        "price": config.get("price", 10.0),
                        "provider": config.get("provider", "groq"),
                        "model": config.get("model", ""),
                        "is_active": config.get("is_active", True),
                        "age_restriction": config.get("age_restriction", 0),
                        "content_filters": config.get("content_filters", {})
                    }
                    logger.debug(f"Create data for persona '{persona_id}': {create_data}")
                    new_persona = create_persona(db, create_data)
                    logger.info(f"Successfully created persona '{persona_id}' in database")
            except Exception as e:
                logger.error(f"Error processing persona '{persona_id}': {e}", exc_info=True)

            logger.info(f"Successfully processed persona '{persona_id}' from {file_path}")
        except Exception as e:
            logger.error(f"Error processing configuration file {file_path}: {e}")


if __name__ == "__main__":
    # Load personas from YAML files
    load_personas_from_yaml()
    logger.info("Persona initialization complete")
