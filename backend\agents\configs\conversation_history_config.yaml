# Conversation History Manager Configuration
# This configuration can be included in any agent to enable robust conversation history handling

conversation_history_manager:
  name: "ConversationHistoryManager"
  type: "ConversationHistoryManager"
  config:
    # Memory limits
    max_memory_entries: 100          # Maximum entries per conversation in memory
    max_database_entries: 50         # Maximum entries to retrieve from database
    max_llm_context_entries: 30      # Maximum entries to send to LLM for context
    
    # Cleanup settings
    cleanup_interval_hours: 24       # Hours before cleaning up old conversations
    max_conversations_in_memory: 1000 # Maximum conversations to keep in memory
    
    # Performance settings
    enable_duplicate_detection: true  # Remove duplicate messages
    enable_chronological_sorting: true # Sort messages by timestamp
    enable_automatic_cleanup: true   # Automatically clean up old conversations
    
    # Logging
    log_level: "INFO"               # Logging level for conversation history operations
    log_memory_stats: true          # Log memory usage statistics
    
  # Component capabilities
  capabilities:
    - "conversation_history"
    - "memory_management"
    - "context_preservation"
    - "duplicate_detection"
    - "automatic_cleanup"

# Example usage in agent configuration:
# components:
#   - name: "ConversationHistoryManager"
#     type: "ConversationHistoryManager"
#     config:
#       max_memory_entries: 100
#       max_llm_context_entries: 30
