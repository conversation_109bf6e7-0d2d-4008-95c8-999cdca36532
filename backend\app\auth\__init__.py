"""
Authentication utilities for the ClassyWeb application.
"""
import logging
import secrets
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional, Tuple

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
import jwt
from jwt import InvalidTokenError
from passlib.context import CryptContext
from sqlalchemy.orm import Session

from .. import config # Adjusted import path
from ..database import get_db, get_user_by_email, get_user_by_id, User # Adjusted import path
from ..redis_client import blacklist_token, is_token_blacklisted, store_refresh_token, validate_refresh_token, invalidate_refresh_token

# Set up logging
logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 token URL - Note: The tokenUrl might need adjustment depending on the final router prefix
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token") # Keep as relative for now

# Token generation
def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.

    Args:
        data: Data to encode in the token
        expires_delta: Optional expiration time delta

    Returns:
        JWT token string
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)

    # Add jti (JWT ID) claim for token identification (useful for blacklisting)
    jti = str(uuid.uuid4())
    to_encode.update({"exp": expire, "jti": jti})

    encoded_jwt = jwt.encode(to_encode, config.JWT_SECRET_KEY, algorithm=config.JWT_ALGORITHM)
    return encoded_jwt

def create_refresh_token(user_id: int, metadata: dict = None, device_fingerprint: str = None, refresh_count: int = 0) -> str:
    """
    Create a refresh token.

    Args:
        user_id: User ID
        metadata: Optional metadata about the token (user agent, IP, etc.)
        device_fingerprint: Optional device fingerprint for security validation

    Returns:
        Refresh token string
    """
    # Generate a random token
    token = secrets.token_urlsafe(64)

    # Store in Redis with expiration
    expires_delta = timedelta(days=config.REFRESH_TOKEN_EXPIRE_DAYS).total_seconds()
    store_refresh_token(user_id, token, int(expires_delta), metadata, device_fingerprint, refresh_count)

    return token

# Password utilities
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against a hash.

    Args:
        plain_password: Plain text password
        hashed_password: Hashed password

    Returns:
        True if the password matches the hash, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    Hash a password.

    Args:
        password: Plain text password

    Returns:
        Hashed password
    """
    return pwd_context.hash(password)

# Token generation for email verification and password reset
def generate_verification_token() -> str:
    """
    Generate a random token for email verification.

    Returns:
        Random token string
    """
    return secrets.token_urlsafe(32)

# User authentication
async def authenticate_user(db: Session, email: str, password: str) -> Tuple[Optional[User], Optional[str]]:
    """
    Authenticate a user with email and password.

    Args:
        db: Database session
        email: User email
        password: User password

    Returns:
        Tuple containing (User object if authentication is successful, error message if any)
        If authentication fails, the first element will be None and the second will contain the error message
        If authentication succeeds, the second element will be None
    """
    logger.debug(f"Authenticating user with email: {email}")
    user = get_user_by_email(db, email)
    if not user:
        logger.debug(f"User with email {email} not found")
        return None, "user_not_found"
    if not user.hashed_password:
        logger.debug(f"User {email} is OAuth-only (no password)")
        return None, "oauth_only_user"  # OAuth-only user
    if not verify_password(password, user.hashed_password):
        logger.debug(f"Invalid password for user {email}")
        return None, "invalid_password"
    logger.debug(f"User {email} authenticated successfully")
    return user, None

# Current user dependency
async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    """
    Get the current user from the JWT token.

    Args:
        token: JWT token
        db: Database session

    Returns:
        User object

    Raises:
        HTTPException: If the token is invalid or the user is not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Check if token is blacklisted
    if is_token_blacklisted(token):
        logger.warning("Attempt to use blacklisted token")
        raise credentials_exception

    try:
        payload = jwt.decode(token, config.JWT_SECRET_KEY, algorithms=[config.JWT_ALGORITHM])
        user_id = payload.get("sub")
        if not user_id:
            raise credentials_exception
    except InvalidTokenError:
        raise credentials_exception

    user = get_user_by_id(db, int(user_id))
    if user is None:
        raise credentials_exception

    return user

# Current active user dependency
async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Get the current active user.

    Args:
        current_user: Current user

    Returns:
        User object

    Raises:
        HTTPException: If the user is inactive
    """
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Optional current user dependency
async def get_optional_current_user(
    token: Optional[str] = Depends(oauth2_scheme), db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get the current user from the JWT token, or None if no token is provided.

    Args:
        token: JWT token (optional)
        db: Database session

    Returns:
        User object or None
    """
    if token is None:
        return None

    # Check if token is blacklisted
    if is_token_blacklisted(token):
        logger.warning("Attempt to use blacklisted token in optional auth")
        return None

    try:
        payload = jwt.decode(token, config.JWT_SECRET_KEY, algorithms=[config.JWT_ALGORITHM])
        user_id = payload.get("sub")
        if not user_id:
            return None
    except InvalidTokenError:
        return None

    user = get_user_by_id(db, int(user_id))
    return user

# Get user from token (for WebSocket authentication)
async def get_current_user_from_token(token: str, db: Session) -> User:
    """
    Get the current user from a JWT token string (for WebSocket authentication).

    Args:
        token: JWT token string
        db: Database session

    Returns:
        User object

    Raises:
        Exception: If the token is invalid or the user is not found
    """
    # Check if token is blacklisted
    if is_token_blacklisted(token):
        logger.warning("Attempt to use blacklisted token in WebSocket")
        raise Exception("Invalid or expired token")

    try:
        payload = jwt.decode(token, config.JWT_SECRET_KEY, algorithms=[config.JWT_ALGORITHM])
        user_id = payload.get("sub")
        if not user_id:
            raise Exception("Invalid token payload")
    except InvalidTokenError:
        raise Exception("Invalid token")

    user = get_user_by_id(db, int(user_id))
    if user is None:
        raise Exception("User not found")

    if not user.is_active:
        raise Exception("Inactive user")

    return user
