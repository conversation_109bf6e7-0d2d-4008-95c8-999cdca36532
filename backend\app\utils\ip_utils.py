"""
Utility functions for IP address handling and security.
"""

import hashlib
import logging
import ipad<PERSON>
from typing import Optional

# Configure logging
logger = logging.getLogger(__name__)

def hash_ip_address(ip: str, salt: Optional[str] = None) -> str:
    """
    Hash an IP address for secure storage.
    
    Args:
        ip: The IP address to hash
        salt: Optional salt to add to the hash (defaults to empty string)
    
    Returns:
        A SHA-256 hash of the IP address
    """
    if not ip:
        return ""
    
    try:
        # Validate IP address format
        ipaddress.ip_address(ip)
        
        # Create a salted hash of the IP
        salted_ip = f"{ip}{salt or ''}"
        return hashlib.sha256(salted_ip.encode()).hexdigest()
    except ValueError:
        # Invalid IP address format
        logger.warning(f"Invalid IP address format: {ip}")
        return ""

def compare_ip_addresses(ip1: str, hashed_ip2: str, salt: Optional[str] = None) -> bool:
    """
    Compare an IP address with a hashed IP address.
    
    Args:
        ip1: The plaintext IP address
        hashed_ip2: The hashed IP address to compare against
        salt: Optional salt used in the hash (defaults to empty string)
    
    Returns:
        True if the hashed version of ip1 matches hashed_ip2, False otherwise
    """
    if not ip1 or not hashed_ip2:
        return False
    
    # Hash the first IP and compare with the second hashed IP
    hashed_ip1 = hash_ip_address(ip1, salt)
    return hashed_ip1 == hashed_ip2

def anonymize_ip_for_logging(ip: str) -> str:
    """
    Anonymize an IP address for logging purposes.
    
    For IPv4, masks the last octet (e.g., 192.168.1.xxx)
    For IPv6, masks the last 80 bits
    
    Args:
        ip: The IP address to anonymize
    
    Returns:
        Anonymized IP address
    """
    if not ip:
        return ""
    
    try:
        ip_obj = ipaddress.ip_address(ip)
        
        if ip_obj.version == 4:
            # For IPv4, mask the last octet
            parts = ip.split('.')
            if len(parts) == 4:
                return f"{parts[0]}.{parts[1]}.{parts[2]}.xxx"
        elif ip_obj.version == 6:
            # For IPv6, mask the last 80 bits (show only first 48 bits)
            hex_parts = ip_obj.exploded.split(':')
            if len(hex_parts) == 8:
                return f"{hex_parts[0]}:{hex_parts[1]}:{hex_parts[2]}:xxxx:xxxx:xxxx:xxxx:xxxx"
        
        # Fallback for any other case
        return "x.x.x.x"
    except ValueError:
        # Invalid IP address format
        logger.warning(f"Invalid IP address format for anonymization: {ip}")
        return "x.x.x.x"
