"""
Model provider system for the Datagenius backend.

This package provides a centralized system for managing AI model providers,
allowing for model-agnostic agent implementations.
"""

import logging
from .registry import ModelProviderRegistry
from .base import ModelProvider, BaseModelProvider
from .exceptions import ModelProviderError, ModelInitializationError, ModelNotFoundError

# Configure logging
logger = logging.getLogger(__name__)

# Import utility functions for easier access
from .utils import get_model, get_provider, list_available_providers, list_available_models

# Export classes and functions for easier imports
__all__ = [
    "ModelProvider",
    "BaseModelProvider",
    "ModelProviderRegistry",
    "ModelProviderError",
    "ModelInitializationError",
    "ModelNotFoundError",
    "get_model",
    "get_provider",
    "list_available_providers",
    "list_available_models"
]

# Register model providers
from .register import register_model_providers
register_model_providers()

# Log the model provider system initialization
logger.info("Initialized model provider system")
