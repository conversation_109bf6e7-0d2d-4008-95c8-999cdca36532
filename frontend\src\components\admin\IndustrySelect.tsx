import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Function to generate a consistent color based on industry name
export const getIndustryColor = (industry: string): string => {
  const colors = [
    'bg-blue-500', 'bg-green-500', 'bg-amber-500', 'bg-red-500',
    'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
  ];

  // Simple hash function to get a consistent index
  let hash = 0;
  for (let i = 0; i < industry.length; i++) {
    hash = industry.charCodeAt(i) + ((hash << 5) - hash);
  }

  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

interface IndustrySelectProps {
  industries: string[];
  value: string;
  onChange: (value: string) => void;
  name?: string;
}

const IndustrySelect: React.FC<IndustrySelectProps> = ({
  industries,
  value,
  onChange,
  name = 'industry',
}) => {
  return (
    <Select
      name={name}
      value={value}
      onValueChange={onChange}
    >
      <SelectTrigger className="bg-white">
        {value ? (
          <div className="flex items-center">
            <span className={`h-3 w-3 rounded-full ${getIndustryColor(value)} mr-2`}></span>
            <span>{value}</span>
          </div>
        ) : (
          <SelectValue placeholder="Select industry" />
        )}
      </SelectTrigger>
      <SelectContent>
        {industries.map((industry) => (
          <SelectItem key={industry} value={industry} className="py-1.5">
            <div className="flex items-center">
              <span className={`h-3 w-3 rounded-full ${getIndustryColor(industry)} mr-2`}></span>
              <span>{industry}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default IndustrySelect;
