/**
 * Dashboard Cache Clearing Utility
 * 
 * This utility provides functions to clear cached dashboard data from the frontend
 * store and browser storage to ensure a clean slate for testing the new hierarchical
 * data source architecture.
 */

import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';

/**
 * Clear all dashboard-related data from the frontend store
 */
export const clearDashboardStore = () => {
  console.log('🧹 Clearing dashboard store data...');
  
  const store = useUnifiedDashboardStore.getState();
  
  // Reset all dashboard-related state
  store.clearAllData();
  
  console.log('✅ Dashboard store cleared');
};

/**
 * Clear dashboard-related data from browser storage
 */
export const clearDashboardBrowserStorage = () => {
  console.log('🧹 Clearing dashboard browser storage...');
  
  // Clear localStorage items related to dashboards
  const keysToRemove = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (
      key.includes('dashboard') ||
      key.includes('widget') ||
      key.includes('section') ||
      key.includes('layout') ||
      key.includes('unified-dashboard-store')
    )) {
      keysToRemove.push(key);
    }
  }
  
  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
    console.log(`  Removed localStorage key: ${key}`);
  });
  
  // Clear sessionStorage items related to dashboards
  const sessionKeysToRemove = [];
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && (
      key.includes('dashboard') ||
      key.includes('widget') ||
      key.includes('section') ||
      key.includes('layout')
    )) {
      sessionKeysToRemove.push(key);
    }
  }
  
  sessionKeysToRemove.forEach(key => {
    sessionStorage.removeItem(key);
    console.log(`  Removed sessionStorage key: ${key}`);
  });
  
  console.log('✅ Dashboard browser storage cleared');
};

/**
 * Clear dashboard-related IndexedDB data (if any)
 */
export const clearDashboardIndexedDB = async () => {
  console.log('🧹 Clearing dashboard IndexedDB data...');
  
  try {
    // Check if IndexedDB is available
    if (!window.indexedDB) {
      console.log('  IndexedDB not available');
      return;
    }
    
    // Get list of databases
    const databases = await indexedDB.databases();
    
    for (const db of databases) {
      if (db.name && (
        db.name.includes('dashboard') ||
        db.name.includes('widget') ||
        db.name.includes('layout')
      )) {
        console.log(`  Deleting IndexedDB: ${db.name}`);
        const deleteRequest = indexedDB.deleteDatabase(db.name);
        
        await new Promise((resolve, reject) => {
          deleteRequest.onsuccess = () => resolve(true);
          deleteRequest.onerror = () => reject(deleteRequest.error);
        });
      }
    }
    
    console.log('✅ Dashboard IndexedDB cleared');
  } catch (error) {
    console.warn('⚠️  Error clearing IndexedDB:', error);
  }
};

/**
 * Clear all dashboard cache and reload the page
 */
export const clearDashboardCacheAndReload = async () => {
  console.log('🔄 Starting complete dashboard cache clear...');
  
  try {
    // Clear store data
    clearDashboardStore();
    
    // Clear browser storage
    clearDashboardBrowserStorage();
    
    // Clear IndexedDB
    await clearDashboardIndexedDB();
    
    console.log('✅ All dashboard cache cleared successfully');
    
    // Reload the page to ensure clean state
    console.log('🔄 Reloading page for clean state...');
    window.location.reload();
    
  } catch (error) {
    console.error('❌ Error clearing dashboard cache:', error);
    throw error;
  }
};

/**
 * Verify that dashboard cache has been cleared
 */
export const verifyDashboardCacheCleared = () => {
  console.log('🔍 Verifying dashboard cache is cleared...');
  
  const store = useUnifiedDashboardStore.getState();
  
  // Check store state
  const storeChecks = {
    dashboards: store.dashboards.length === 0,
    currentDashboard: store.currentDashboard === null,
    currentLayout: Object.keys(store.currentLayout).length === 0,
    activeDashboard: store.activeDashboard === null,
  };
  
  // Check localStorage
  const localStorageKeys = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (
      key.includes('dashboard') ||
      key.includes('widget') ||
      key.includes('section') ||
      key.includes('layout')
    )) {
      localStorageKeys.push(key);
    }
  }
  
  // Check sessionStorage
  const sessionStorageKeys = [];
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && (
      key.includes('dashboard') ||
      key.includes('widget') ||
      key.includes('section') ||
      key.includes('layout')
    )) {
      sessionStorageKeys.push(key);
    }
  }
  
  const verification = {
    store: storeChecks,
    localStorage: localStorageKeys.length === 0,
    sessionStorage: sessionStorageKeys.length === 0,
    localStorageKeys,
    sessionStorageKeys,
  };
  
  const isClean = Object.values(storeChecks).every(Boolean) && 
                  verification.localStorage && 
                  verification.sessionStorage;
  
  if (isClean) {
    console.log('✅ Dashboard cache verification passed - all clean');
  } else {
    console.warn('⚠️  Dashboard cache verification failed:');
    console.warn('  Store checks:', storeChecks);
    console.warn('  Remaining localStorage keys:', localStorageKeys);
    console.warn('  Remaining sessionStorage keys:', sessionStorageKeys);
  }
  
  return {
    isClean,
    ...verification,
  };
};

/**
 * Development utility to clear dashboard cache without page reload
 * Useful for testing during development
 */
export const clearDashboardCacheDevMode = async () => {
  console.log('🔧 Development mode: Clearing dashboard cache without reload...');
  
  try {
    // Clear store data
    clearDashboardStore();
    
    // Clear browser storage
    clearDashboardBrowserStorage();
    
    // Clear IndexedDB
    await clearDashboardIndexedDB();
    
    console.log('✅ Dashboard cache cleared (dev mode)');
    
    // Verify clearing
    const verification = verifyDashboardCacheCleared();
    
    return verification;
    
  } catch (error) {
    console.error('❌ Error clearing dashboard cache (dev mode):', error);
    throw error;
  }
};

/**
 * Add a method to the unified dashboard store to clear all data
 */
export const addClearMethodToStore = () => {
  const store = useUnifiedDashboardStore;
  
  // Add clearAllData method if it doesn't exist
  if (!store.getState().clearAllData) {
    store.setState((state) => ({
      ...state,
      clearAllData: () => {
        store.setState({
          dashboards: [],
          currentDashboard: null,
          currentLayout: {},
          activeDashboard: null,
          isLoading: false,
          error: null,
          // Reset any other dashboard-related state
        });
      }
    }));
  }
};

// Initialize the clear method when this module is imported
addClearMethodToStore();
