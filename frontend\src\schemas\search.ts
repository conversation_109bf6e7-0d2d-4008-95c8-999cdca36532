// Corresponds to Pydantic schemas in backend/app/models/search.py
// import { User } from './auth'; // Assuming you have an auth schema with User - Removed for now

export interface SearchQueryLogCreate {
  query: string;
  user_id?: number | null;
  session_id?: string | null;
  filters_applied?: Record<string, any> | null;
  result_count?: number | null;
  clicked_result_ids?: string[] | null;
  user_ip?: string | null;
  user_agent?: string | null;
}

export interface SearchQueryLogResponse extends SearchQueryLogCreate {
  id: string;
  search_timestamp: string; // ISO date string
}

export interface SearchRecommendationRequest {
  user_id?: number | null;
  session_id?: string | null;
  current_query?: string | null;
  context_items?: string[] | null; // e.g., items in cart, recently viewed
  limit?: number;
}

export interface SearchRecommendation {
  item_id: string;
  item_type: string; // e.g., "persona", "report", "dashboard_widget"
  score: number;
  reasoning?: string | null;
  // Optional: Add fields for displaying item details like title, description, image
  title?: string | null;
  description?: string | null;
  image_url?: string | null;
}

export interface SearchRecommendationResponse {
  recommendations: SearchRecommendation[];
  request_id?: string | null;
}

// For the SearchResultsResponse in the API
// Note: `results` is List[Dict[str, Any]] in backend, so we use a generic approach here
// or define specific result item types if known.
export interface SearchResultItem {
  id: string;
  type: string;
  title?: string | null;
  snippet?: string | null;
  score?: number | null;
  // Add other common fields you expect in search results
  [key: string]: any; // Allow other properties
}

export interface SearchResultsResponse {
  query: string;
  filters?: Record<string, any> | null;
  results: SearchResultItem[];
  total_count: number;
  limit: number;
  offset: number;
}
