"""
LLM classifier for the Datagenius classification agent.

This module provides functions for classifying text using LLMs.
"""

import logging
import pandas as pd
from typing import List, Dict, Any, Optional, Union
import asyncio
import json

# Configure logging
logger = logging.getLogger(__name__)

async def classify_texts_with_llm_v2(
    texts: List[str],
    hierarchy_levels: List[str] = None,
    provider_id: str = "groq",
    model_id: str = "llama3-70b-8192",
    config: Dict[str, Any] = None,
    sample_size: Optional[int] = None
) -> pd.DataFrame:
    """
    Classify texts using an LLM through the model provider system.

    Args:
        texts: List of texts to classify
        hierarchy_levels: List of hierarchy levels for classification (e.g., ["theme", "category"])
        provider_id: ID of the model provider to use
        model_id: ID of the model to use
        config: Configuration for the model
        sample_size: Optional sample size to limit processing

    Returns:
        DataFrame with classification results
    """
    try:
        from agents.utils.model_providers import get_model
    except ImportError:
        logger.error("Model provider system not available")
        raise ImportError("Model provider system not available")

    logger.info(f"Classifying {len(texts)} texts with {provider_id} model {model_id}")

    # Use default hierarchy levels if none provided
    if not hierarchy_levels:
        hierarchy_levels = ["theme", "category"]
    
    # Sample texts if sample_size is provided
    if sample_size and sample_size < len(texts):
        import random
        sampled_indices = random.sample(range(len(texts)), sample_size)
        sampled_texts = [texts[i] for i in sampled_indices]
        texts_to_process = sampled_texts
        logger.info(f"Sampled {sample_size} texts for classification")
    else:
        texts_to_process = texts

    # Default configuration
    if config is None:
        config = {"temperature": 0.1}

    try:
        # Get the model from the provider system
        model = await get_model(provider_id, model_id, config)
        
        # Process texts
        results = []
        for text in texts_to_process:
            # Create a prompt for the LLM
            prompt = f"""
            Please classify the following text into {len(hierarchy_levels)} levels: {', '.join(hierarchy_levels)}.
            
            Text: "{text}"
            
            Respond with a JSON object with the following structure:
            {{
                "text": "the original text",
                {', '.join([f'"{level.lower()}": "the {level.lower()} classification"' for level in hierarchy_levels])}
            }}
            
            Only respond with the JSON object, no other text.
            """
            
            # Get the classification from the LLM
            response = await model.agenerate_text(prompt)
            
            try:
                # Parse the JSON response
                result_json = json.loads(response)
                
                # Add the original text if not included
                if "text" not in result_json:
                    result_json["text"] = text
                
                results.append(result_json)
                
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse LLM response as JSON: {response}")
                # Create a fallback result
                result = {"text": text}
                for level in hierarchy_levels:
                    result[level.lower()] = "Unknown"
                results.append(result)
        
        # Convert results to DataFrame
        results_df = pd.DataFrame(results)
        logger.info(f"Classification complete. Classified {len(results_df)} texts.")
        return results_df
        
    except Exception as e:
        logger.error(f"Error classifying texts with LLM: {str(e)}", exc_info=True)
        # Return empty DataFrame with expected columns
        columns = ["text"] + [level.lower() for level in hierarchy_levels]
        return pd.DataFrame(columns=columns)
