#!/usr/bin/env python3
"""
Test script to verify concierge agent streaming functionality.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.concierge_agent.concierge import ConciergeAgent

async def test_concierge_streaming():
    """Test concierge agent streaming functionality."""
    print('🧪 Testing Concierge Agent Streaming...')
    
    try:
        agent = ConciergeAgent()
        await agent.initialize({
            'provider': 'groq',
            'model': 'llama3-70b-8192',
            'temperature': 0.7
        })
        
        print('✅ Concierge agent initialized')
        
        # Test 1: Simple message to concierge
        print('\n📝 Test 1: Simple message to concierge')
        result1 = await agent.process_message(
            message='hello, what can you help me with?',
            user_id='1',
            conversation_id='test-conversation-1',
            context={}
        )
        print(f'✅ Response generated: {len(result1["message"])} characters')
        print(f'✅ Success: {result1.get("success", False)}')
        print(f'Response preview: {result1["message"][:200]}...')
        
        # Test 2: Test streaming functionality
        print('\n📝 Test 2: Test streaming functionality')
        chunk_count = 0
        async for chunk in agent.process_streaming_message(
            message='tell me about your capabilities',
            user_id='1',
            conversation_id='test-conversation-1',
            context={}
        ):
            chunk_count += 1
            chunk_type = chunk.get('type', 'unknown')
            content_preview = chunk.get('content', '')[:50] if chunk.get('content') else 'No content'
            print(f'  Chunk {chunk_count}: {chunk_type} - {content_preview}...')
            
            if chunk_count > 20:  # Prevent infinite loops
                break
        
        print(f'✅ Streaming completed with {chunk_count} chunks')
        
        return True
            
    except Exception as e:
        print(f'❌ Error during test: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = asyncio.run(test_concierge_streaming())
    if success:
        print('\n🎉 Concierge streaming test completed successfully!')
        sys.exit(0)
    else:
        print('\n💥 Concierge streaming test failed!')
        sys.exit(1)
