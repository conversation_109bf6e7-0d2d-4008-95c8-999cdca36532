"""
Crawl4AI-enhanced web scraping service for extracting business information from websites.

This module provides an advanced web scraping service that uses Crawl4AI for improved
business data extraction, with intelligent fallback to the existing WebScrapingService.
Integrates with the existing AI provider system and maintains backward compatibility.
"""

import logging
import os
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import json
import yaml

from pydantic import BaseModel, Field

from .web_scraping_service import WebScrapingService
from .ai_field_mapping_service import AIFieldMappingService
from ..settings.crawl4ai import crawl4ai_config, get_browser_args

logger = logging.getLogger(__name__)

# Try to import Crawl4AI with graceful fallback
try:
    from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, BrowserConfig, CacheMode
    from crawl4ai import LLMExtractionStrategy, JsonCssExtractionStrategy
    from crawl4ai import LLMConfig
    CRAWL4AI_AVAILABLE = True
    logger.info("Crawl4AI successfully imported")
except ImportError as e:
    logger.warning(f"Crawl4AI not available: {e}")
    CRAWL4AI_AVAILABLE = False
    # Create dummy classes to prevent import errors
    class AsyncWebCrawler: pass
    class CrawlerRunConfig: pass
    class BrowserConfig: pass
    class CacheMode: pass
    class LLMExtractionStrategy: pass
    class JsonCssExtractionStrategy: pass
    class LLMConfig: pass

class BusinessProfileSchema(BaseModel):
    """Pydantic schema for business profile data extraction."""
    business_name: Optional[str] = Field(None, description="Official business or company name")
    description: Optional[str] = Field(None, description="Business description, mission, or value proposition")
    industry: Optional[str] = Field(None, description="Industry or business sector")
    services: Optional[List[str]] = Field(None, description="List of services or products offered")
    contact_email: Optional[str] = Field(None, description="Primary business email address")
    contact_phone: Optional[str] = Field(None, description="Primary business phone number")
    address: Optional[str] = Field(None, description="Business address or location")
    website: Optional[str] = Field(None, description="Official website URL")
    social_media: Optional[Dict[str, str]] = Field(None, description="Social media profiles")
    founded_year: Optional[str] = Field(None, description="Year the business was founded")
    employee_count: Optional[str] = Field(None, description="Number of employees or company size")

@dataclass
class ExtractionResult:
    """Container for extraction results with quality metrics."""
    data: Dict[str, Any]
    confidence_score: float
    extraction_method: str
    processing_time: float
    error_message: Optional[str] = None

class Crawl4AIWebService:
    """
    Enhanced web scraping service using Crawl4AI with intelligent fallback.
    
    This service provides advanced web scraping capabilities for business profile
    autofill, using Crawl4AI as the primary extraction method with fallback to
    the existing WebScrapingService for reliability.
    """
    
    def __init__(self):
        self.fallback_service = WebScrapingService()
        self.ai_mapping_service = None
        self.browser_config = None
        self.extraction_config = None
        self._initialize_configs()
    
    def _initialize_configs(self):
        """Initialize Crawl4AI browser and extraction configurations."""
        if not CRAWL4AI_AVAILABLE:
            logger.warning("Crawl4AI not available - browser configuration disabled")
            self.browser_config = None
            return

        try:
            # Browser configuration optimized for business websites
            self.browser_config = BrowserConfig(
                headless=crawl4ai_config.headless,
                verbose=False,
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                java_script_enabled=crawl4ai_config.javascript_enabled,
                accept_downloads=False,
                ignore_https_errors=crawl4ai_config.ignore_https_errors,
                extra_args=get_browser_args()
            )

            logger.info("Crawl4AI browser configuration initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing Crawl4AI configurations: {e}")
            self.browser_config = None
    
    async def _get_ai_mapping_service(self) -> AIFieldMappingService:
        """Get or create AI mapping service with concierge agent configuration."""
        if self.ai_mapping_service is None:
            self.ai_mapping_service = AIFieldMappingService()
            # Load concierge agent model configuration
            await self.ai_mapping_service._load_concierge_model_config(user_settings=None)
            logger.info("AI mapping service initialized with concierge agent configuration")
        return self.ai_mapping_service
    
    async def _create_llm_extraction_strategy(self) -> LLMExtractionStrategy:
        """Create LLM extraction strategy using inherited AI configuration."""
        try:
            mapping_service = await self._get_ai_mapping_service()
            
            # Create LLM config using the same provider as concierge agent
            llm_config = LLMConfig(
                provider=f"{mapping_service.analysis_config.provider}/{mapping_service.analysis_config.model}",
                api_token=os.getenv("OPENAI_API_KEY") if "openai" in mapping_service.analysis_config.provider.lower() else "no-token",
                temperature=0.1,  # Low temperature for consistent extraction
                max_tokens=2000
            )
            
            extraction_instruction = """
            Extract business profile information from the provided content and return it as a single JSON object.

            Focus on identifying:
            1. Official business name and any alternate names
            2. Clear business description and value proposition
            3. Industry classification and business sector
            4. Services, products, or offerings
            5. Contact information (email, phone, address)
            6. Company details (founding year, size, etc.)
            7. Social media presence and website

            Return ONLY a valid JSON object with this exact structure (no additional text):
            {
              "business_name": "Company Name",
              "description": "Business description",
              "industry": "Industry sector",
              "services": ["Service 1", "Service 2"],
              "contact_email": "<EMAIL>",
              "contact_phone": "+1234567890",
              "address": "Business address",
              "website": "https://company.com",
              "social_media": {
                "facebook": "https://facebook.com/company",
                "linkedin": "https://linkedin.com/company"
              },
              "founded_year": "2020",
              "employee_count": "50-100"
            }

            Rules:
            - Return ONLY the JSON object, no explanations or markdown
            - Use empty string "" for missing information, not null
            - Ensure all fields are present even if empty
            - Use proper JSON syntax with double quotes
            """
            
            return LLMExtractionStrategy(
                llm_config=llm_config,
                schema=BusinessProfileSchema.model_json_schema(),
                extraction_type="schema",
                instruction=extraction_instruction,
                apply_chunking=False  # Process content as a whole for business profiles
            )
            
        except Exception as e:
            logger.error(f"Error creating LLM extraction strategy: {e}")
            raise
    
    async def _create_css_extraction_strategy(self) -> JsonCssExtractionStrategy:
        """Create CSS-based extraction strategy for structured data."""
        schema = {
            "name": "BusinessProfile",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "business_name",
                    "selector": "h1, .company-name, .business-name, [itemProp='name'], .site-title",
                    "type": "text"
                },
                {
                    "name": "description",
                    "selector": ".description, .about, [itemProp='description'], .company-description",
                    "type": "text"
                },
                {
                    "name": "contact_email",
                    "selector": "[href^='mailto:'], [itemProp='email']",
                    "type": "attribute",
                    "attribute": "href"
                },
                {
                    "name": "contact_phone",
                    "selector": "[href^='tel:'], [itemProp='telephone']",
                    "type": "attribute", 
                    "attribute": "href"
                },
                {
                    "name": "address",
                    "selector": ".address, [itemProp='address'], .location",
                    "type": "text"
                }
            ]
        }
        
        return JsonCssExtractionStrategy(schema, verbose=False)

    def _parse_extracted_content_as_yaml(self, content: str, url: str) -> Dict[str, Any]:
        """
        Parse extracted content, prioritizing JSON then converting to YAML-compatible format.

        Args:
            content: Raw extracted content from Crawl4AI
            url: Source URL for logging

        Returns:
            Dictionary with parsed business data
        """
        if not content or not content.strip():
            logger.warning(f"No extracted content to parse for {url}")
            return {}

        # Clean the content - remove markdown formatting if present
        cleaned_content = content.strip()
        if cleaned_content.startswith('```json'):
            cleaned_content = cleaned_content.replace('```json', '').replace('```', '').strip()
        elif cleaned_content.startswith('```'):
            cleaned_content = cleaned_content.replace('```', '').strip()

        # Try parsing as JSON first (more reliable from LLM)
        try:
            parsed_content = json.loads(cleaned_content)

            # Handle different data structures
            if isinstance(parsed_content, dict):
                logger.info(f"Successfully parsed extracted content as JSON for {url}")
                return parsed_content
            elif isinstance(parsed_content, list):
                if len(parsed_content) > 0 and isinstance(parsed_content[0], dict):
                    logger.info(f"Extracted JSON data from list format for {url}")
                    return parsed_content[0]  # Take first item if it's a dict
                else:
                    logger.warning(f"JSON content is a list but items are not dicts for {url}")
                    return {"items": parsed_content}
            else:
                logger.warning(f"JSON content is neither dict nor list for {url}")
                return {"raw_content": str(parsed_content)}

        except json.JSONDecodeError as json_error:
            logger.warning(f"Failed to parse content as JSON for {url}: {json_error}")

            # Fallback to YAML parsing
            try:
                parsed_content = yaml.safe_load(cleaned_content)
                logger.info(f"Successfully parsed content as YAML fallback for {url}")

                if isinstance(parsed_content, dict):
                    return parsed_content
                elif isinstance(parsed_content, list) and len(parsed_content) > 0:
                    return parsed_content[0] if isinstance(parsed_content[0], dict) else {"items": parsed_content}
                else:
                    return {"raw_content": str(parsed_content)}

            except yaml.YAMLError as yaml_error:
                logger.warning(f"Failed to parse content as YAML fallback for {url}: {yaml_error}")

                # Final fallback - return as raw content
                return {"raw_content": cleaned_content}

    async def _extract_with_crawl4ai(self, url: str) -> ExtractionResult:
        """Extract business information using Crawl4AI with LLM strategy."""
        start_time = asyncio.get_event_loop().time()

        try:
            if not CRAWL4AI_AVAILABLE:
                raise Exception("Crawl4AI is not available - package not installed")

            if not crawl4ai_config.enabled or not self.browser_config:
                raise Exception("Crawl4AI is disabled or not properly configured")

            logger.info(f"Starting Crawl4AI extraction for URL: {url}")

            # Create extraction strategy
            extraction_strategy = await self._create_llm_extraction_strategy()

            # Configure crawler for business websites
            crawler_config = CrawlerRunConfig(
                # Content selection optimized for business information
                target_elements=[
                    "main", "article", ".content", ".main-content",
                    ".about", ".company-info", ".business-info",
                    "[itemtype*='Organization']", "[itemtype*='LocalBusiness']"
                ],

                # Filtering to focus on relevant content
                word_count_threshold=10,
                excluded_tags=["script", "style", "nav", "footer", "header", "aside"],
                exclude_external_links=True,
                exclude_social_media_links=False,  # Keep social media for business profiles

                # Enable JavaScript for dynamic content
                js_code=[
                    "window.scrollTo(0, document.body.scrollHeight/2);",  # Scroll to load content
                    "await new Promise(resolve => setTimeout(resolve, 1000));"  # Wait for content
                ],

                # Extraction configuration
                extraction_strategy=extraction_strategy,
                cache_mode=CacheMode.BYPASS,  # Always get fresh data for business profiles

                # Performance settings
                page_timeout=crawl4ai_config.timeout_seconds * 1000,  # Convert to milliseconds
                delay_before_return_html=2.0
            )

            # Execute crawling
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                result = await crawler.arun(url=url, config=crawler_config)

                if not result.success:
                    raise Exception(f"Crawl4AI extraction failed: {result.error_message}")

                # Parse extracted content as YAML for consistency with project
                extracted_data = self._parse_extracted_content_as_yaml(result.extracted_content, url)

                # Calculate confidence score based on data completeness
                confidence_score = self._calculate_confidence_score(extracted_data, result)

                processing_time = asyncio.get_event_loop().time() - start_time

                # Transform to standard format
                business_data = self._transform_crawl4ai_data(extracted_data, result, url)

                logger.info(f"Crawl4AI extraction completed for {url} in {processing_time:.2f}s with confidence {confidence_score:.2f}")

                return ExtractionResult(
                    data=business_data,
                    confidence_score=confidence_score,
                    extraction_method="crawl4ai_llm",
                    processing_time=processing_time
                )

        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"Crawl4AI extraction failed for {url}: {e}")

            return ExtractionResult(
                data={},
                confidence_score=0.0,
                extraction_method="crawl4ai_llm",
                processing_time=processing_time,
                error_message=str(e)
            )

    def _calculate_confidence_score(self, extracted_data: Dict[str, Any], crawl_result) -> float:
        """Calculate confidence score based on extraction quality."""
        if not extracted_data or extracted_data.get('raw_content'):
            return 0.0  # No structured data extracted

        score = 0.0
        max_score = 1.0

        # Check for key business fields (higher weight)
        key_fields = ['business_name', 'description', 'industry']
        field_weight = 0.4
        key_found = 0

        for field in key_fields:
            value = extracted_data.get(field, "")
            if value and str(value).strip() and str(value).strip() != "":
                key_found += 1

        if key_found > 0:
            score += field_weight * (key_found / len(key_fields))

        # Check for contact information
        contact_fields = ['contact_email', 'contact_phone', 'address']
        contact_weight = 0.3
        contact_found = 0

        for field in contact_fields:
            value = extracted_data.get(field, "")
            if value and str(value).strip() and str(value).strip() != "":
                contact_found += 1

        if contact_found > 0:
            score += contact_weight * (contact_found / len(contact_fields))

        # Check for structured data presence
        if hasattr(crawl_result, 'metadata') and crawl_result.metadata:
            if any(key in crawl_result.metadata for key in ['og:title', 'og:description', 'application/ld+json']):
                score += 0.1

        # Check content length and quality
        if hasattr(crawl_result, 'markdown') and crawl_result.markdown:
            content_length = len(crawl_result.markdown)
            if content_length > 500:
                score += 0.1
            elif content_length > 200:
                score += 0.05

        # Additional services/products information
        services = extracted_data.get('services', [])
        if services and isinstance(services, list) and len(services) > 0:
            score += 0.1

        return min(score, max_score)

    def _transform_crawl4ai_data(self, extracted_data: Dict[str, Any], crawl_result, url: str) -> Dict[str, Any]:
        """Transform Crawl4AI extracted data to standard business info format."""
        business_info = {
            'source_url': url,
            'extraction_timestamp': asyncio.get_event_loop().time(),
            'extraction_method': 'crawl4ai_enhanced'
        }

        # Map extracted fields to standard format
        field_mapping = {
            'business_name': 'business_name',
            'description': 'description',
            'industry': 'industry',
            'services': 'services',
            'contact_email': 'contact_email',
            'contact_phone': 'contact_phone',
            'address': 'address',
            'website': 'website',
            'social_media': 'social_media',
            'founded_year': 'founded_year',
            'employee_count': 'employee_count'
        }

        for crawl_field, standard_field in field_mapping.items():
            value = extracted_data.get(crawl_field)
            if value and str(value).strip():
                # Clean up email and phone formats
                if crawl_field == 'contact_email' and value.startswith('mailto:'):
                    value = value.replace('mailto:', '')
                elif crawl_field == 'contact_phone' and value.startswith('tel:'):
                    value = value.replace('tel:', '')

                business_info[standard_field] = value

        # Add structured data if available
        if hasattr(crawl_result, 'metadata') and crawl_result.metadata:
            business_info['meta_data'] = crawl_result.metadata

        # Add social links from crawl result
        if hasattr(crawl_result, 'links') and crawl_result.links:
            social_links = []
            if hasattr(crawl_result.links, 'external'):
                for link in crawl_result.links.external:
                    if any(social in link.href.lower() for social in ['facebook', 'twitter', 'linkedin', 'instagram']):
                        social_links.append(link.href)
            business_info['social_links'] = social_links

        # Add content summary
        if hasattr(crawl_result, 'markdown') and crawl_result.markdown:
            business_info['content_summary'] = crawl_result.markdown[:1000]

        return business_info

    def _is_high_quality_result(self, result: ExtractionResult) -> bool:
        """Determine if the extraction result is of sufficient quality."""
        if result.error_message:
            return False

        # Minimum confidence threshold
        if result.confidence_score < crawl4ai_config.min_confidence_score:
            return False

        # Check for essential business information
        essential_fields = ['business_name', 'description']
        found_essential = 0

        for field in essential_fields:
            if result.data.get(field) and len(str(result.data[field]).strip()) > 5:
                found_essential += 1

        # Need at least one essential field
        return found_essential > 0

    async def extract_business_info(self, url: str) -> Dict[str, Any]:
        """
        Extract business information from a website URL with intelligent fallback.

        This method tries Crawl4AI first for enhanced extraction, then falls back
        to the existing WebScrapingService if needed. Maintains compatibility with
        the existing API while providing improved data quality.

        Args:
            url: Website URL to scrape

        Returns:
            Dictionary with extracted business information in standard format
        """
        logger.info(f"Starting enhanced business info extraction for URL: {url}")

        crawl4ai_result = None

        # Try Crawl4AI first if available and enabled
        if CRAWL4AI_AVAILABLE and crawl4ai_config.enabled:
            for attempt in range(crawl4ai_config.max_retries + 1):
                try:
                    logger.info(f"Attempting Crawl4AI extraction (attempt {attempt + 1}/{crawl4ai_config.max_retries + 1})")
                    crawl4ai_result = await self._extract_with_crawl4ai(url)

                    if self._is_high_quality_result(crawl4ai_result):
                        logger.info(f"Crawl4AI extraction successful with confidence {crawl4ai_result.confidence_score:.2f}")
                        return crawl4ai_result.data
                    else:
                        logger.warning(f"Crawl4AI result quality insufficient (confidence: {crawl4ai_result.confidence_score:.2f})")

                except Exception as e:
                    logger.warning(f"Crawl4AI attempt {attempt + 1} failed: {e}")
                    if attempt < crawl4ai_config.max_retries:
                        await asyncio.sleep(1)  # Brief delay before retry
                    continue

        # Fallback to existing web scraping service
        logger.info("Falling back to existing WebScrapingService")
        try:
            async with self.fallback_service:
                fallback_data = await self.fallback_service.extract_business_info(url)

            logger.info("Fallback extraction completed successfully")

            # Add metadata to indicate fallback was used
            fallback_data['extraction_method'] = 'fallback_webscraping'
            fallback_data['crawl4ai_attempted'] = CRAWL4AI_AVAILABLE and crawl4ai_config.enabled
            fallback_data['crawl4ai_available'] = CRAWL4AI_AVAILABLE

            if crawl4ai_result and crawl4ai_result.error_message:
                fallback_data['crawl4ai_error'] = crawl4ai_result.error_message

            return fallback_data

        except Exception as fallback_error:
            logger.error(f"Both Crawl4AI and fallback extraction failed for {url}")

            # Return minimal data structure with error information
            return {
                'source_url': url,
                'extraction_timestamp': asyncio.get_event_loop().time(),
                'extraction_method': 'failed',
                'crawl4ai_attempted': CRAWL4AI_AVAILABLE and crawl4ai_config.enabled,
                'crawl4ai_available': CRAWL4AI_AVAILABLE,
                'crawl4ai_error': crawl4ai_result.error_message if crawl4ai_result else None,
                'fallback_error': str(fallback_error),
                'business_name': '',
                'description': '',
                'industry': '',
                'contact_info': {'emails': [], 'phones': [], 'addresses': []},
                'social_links': [],
                'structured_data': {},
                'meta_data': {},
                'content_summary': ''
            }

    async def __aenter__(self):
        """Async context manager entry."""
        await self.fallback_service.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.fallback_service.__aexit__(exc_type, exc_val, exc_tb)
