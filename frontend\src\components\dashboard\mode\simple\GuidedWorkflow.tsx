/**
 * Guided Workflow Component
 * 
 * Provides step-by-step guidance for dashboard creation in Simple Mode.
 * Features progress indicators, contextual help, and interactive tutorials.
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CheckCircle,
  Circle,
  ArrowRight,
  ArrowLeft,
  Play,
  Pause,
  RotateCcw,
  Lightbulb,
  Target,
  Database,
  BarChart3,
  Palette,
  Share,
  HelpCircle,
  Sparkles,
  Clock,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  estimated_time: string;
  required: boolean;
  completed: boolean;
  skippable: boolean;
  help_content?: string;
  actions?: WorkflowAction[];
  validation?: () => boolean;
}

interface WorkflowAction {
  id: string;
  label: string;
  type: 'primary' | 'secondary' | 'link';
  action: () => void;
}

interface GuidedWorkflowProps {
  className?: string;
  workflow_type?: 'dashboard_creation' | 'data_setup' | 'widget_creation' | 'customization';
  on_step_complete?: (step_id: string) => void;
  on_workflow_complete?: () => void;
  on_workflow_skip?: () => void;
  auto_start?: boolean;
}

export const GuidedWorkflow: React.FC<GuidedWorkflowProps> = ({
  className,
  workflow_type = 'dashboard_creation',
  on_step_complete,
  on_workflow_complete,
  on_workflow_skip,
  auto_start = false,
}) => {
  const [current_step_index, set_current_step_index] = useState(0);
  const [is_active, set_is_active] = useState(auto_start);
  const [is_paused, set_is_paused] = useState(false);
  const [show_help, set_show_help] = useState(false);
  const [workflow_steps, set_workflow_steps] = useState<WorkflowStep[]>([]);

  // Initialize workflow steps based on type
  useEffect(() => {
    const steps = get_workflow_steps(workflow_type);
    set_workflow_steps(steps);
  }, [workflow_type]);

  const current_step = workflow_steps[current_step_index];
  const progress = workflow_steps.length > 0 ? ((current_step_index + 1) / workflow_steps.length) * 100 : 0;
  const completed_steps = workflow_steps.filter(step => step.completed).length;

  const handle_next_step = () => {
    if (current_step && !current_step.completed) {
      // Mark current step as completed
      const updated_steps = [...workflow_steps];
      updated_steps[current_step_index].completed = true;
      set_workflow_steps(updated_steps);
      
      on_step_complete?.(current_step.id);
    }

    if (current_step_index < workflow_steps.length - 1) {
      set_current_step_index(current_step_index + 1);
    } else {
      // Workflow completed
      set_is_active(false);
      on_workflow_complete?.();
    }
  };

  const handle_previous_step = () => {
    if (current_step_index > 0) {
      set_current_step_index(current_step_index - 1);
    }
  };

  const handle_skip_step = () => {
    if (current_step?.skippable) {
      handle_next_step();
    }
  };

  const handle_restart_workflow = () => {
    set_current_step_index(0);
    set_is_active(true);
    set_is_paused(false);
    
    // Reset all steps to incomplete
    const reset_steps = workflow_steps.map(step => ({ ...step, completed: false }));
    set_workflow_steps(reset_steps);
  };

  const handle_start_workflow = () => {
    set_is_active(true);
    set_is_paused(false);
  };

  const handle_pause_workflow = () => {
    set_is_paused(!is_paused);
  };

  const handle_skip_workflow = () => {
    set_is_active(false);
    on_workflow_skip?.();
  };

  if (!is_active) {
    return (
      <Card className={cn("border-dashed border-2 border-blue-200", className)}>
        <CardContent className="flex flex-col items-center justify-center py-8 text-center">
          <Sparkles className="h-12 w-12 text-blue-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Ready to get started?</h3>
          <p className="text-muted-foreground mb-4 max-w-md">
            Let me guide you through creating your dashboard step by step. 
            This will take about {get_estimated_total_time(workflow_steps)}.
          </p>
          <div className="flex space-x-2">
            <Button onClick={handle_start_workflow}>
              <Play className="h-4 w-4 mr-2" />
              Start Guided Setup
            </Button>
            <Button variant="outline" onClick={handle_skip_workflow}>
              Skip & Create Manually
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Progress Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5 text-blue-500" />
                <span>Dashboard Setup Guide</span>
                <Badge variant="secondary">
                  Step {current_step_index + 1} of {workflow_steps.length}
                </Badge>
              </CardTitle>
              <CardDescription>
                {completed_steps} of {workflow_steps.length} steps completed
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handle_pause_workflow}
              >
                {is_paused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handle_restart_workflow}
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <Progress value={progress} className="mt-3" />
        </CardHeader>
      </Card>

      {/* Current Step */}
      {current_step && (
        <Card className="border-blue-200 shadow-md">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <current_step.icon className="h-6 w-6 text-blue-500" />
                </div>
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <span>{current_step.title}</span>
                    {current_step.required && (
                      <Badge variant="destructive" className="text-xs">Required</Badge>
                    )}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {current_step.description}
                  </CardDescription>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{current_step.estimated_time}</span>
                    </div>
                    {current_step.help_content && (
                      <Button
                        variant="link"
                        size="sm"
                        onClick={() => set_show_help(true)}
                        className="h-auto p-0 text-blue-500"
                      >
                        <HelpCircle className="h-4 w-4 mr-1" />
                        Need help?
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Step Actions */}
            {current_step.actions && current_step.actions.length > 0 && (
              <div className="space-y-2">
                {current_step.actions.map((action) => (
                  <Button
                    key={action.id}
                    variant={action.type === 'primary' ? 'default' : 'outline'}
                    onClick={action.action}
                    className="w-full justify-start"
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            )}

            <Separator />

            {/* Navigation */}
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={handle_previous_step}
                disabled={current_step_index === 0}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex space-x-2">
                {current_step.skippable && (
                  <Button
                    variant="ghost"
                    onClick={handle_skip_step}
                  >
                    Skip Step
                  </Button>
                )}
                <Button onClick={handle_next_step}>
                  {current_step_index === workflow_steps.length - 1 ? 'Complete' : 'Next'}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Steps Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">All Steps</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {workflow_steps.map((step, index) => {
              const StepIcon = step.icon;
              const is_current = index === current_step_index;
              const is_completed = step.completed;
              const is_accessible = index <= current_step_index;

              return (
                <div
                  key={step.id}
                  className={cn(
                    "flex items-center space-x-3 p-3 rounded-lg transition-colors",
                    is_current && "bg-blue-50 border border-blue-200",
                    is_completed && !is_current && "bg-green-50",
                    !is_accessible && "opacity-50"
                  )}
                >
                  <div className="flex-shrink-0">
                    {is_completed ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <Circle className={cn(
                        "h-5 w-5",
                        is_current ? "text-blue-500" : "text-muted-foreground"
                      )} />
                    )}
                  </div>
                  <StepIcon className={cn(
                    "h-5 w-5",
                    is_current ? "text-blue-500" : "text-muted-foreground"
                  )} />
                  <div className="flex-1">
                    <p className={cn(
                      "font-medium",
                      is_current && "text-blue-700"
                    )}>
                      {step.title}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {step.estimated_time}
                    </p>
                  </div>
                  {step.required && (
                    <Badge variant="outline" className="text-xs">
                      Required
                    </Badge>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Help Dialog */}
      {current_step?.help_content && (
        <Dialog open={show_help} onOpenChange={set_show_help}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Lightbulb className="h-5 w-5 text-yellow-500" />
                <span>Help: {current_step.title}</span>
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p>{current_step.help_content}</p>
            </div>
            <DialogFooter>
              <Button onClick={() => set_show_help(false)}>
                Got it
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

// Get workflow steps based on type
function get_workflow_steps(type: string): WorkflowStep[] {
  const workflows = {
    dashboard_creation: [
      {
        id: 'setup_goal',
        title: 'Define Your Goal',
        description: 'What do you want to achieve with this dashboard?',
        icon: Target,
        estimated_time: '2 min',
        required: true,
        completed: false,
        skippable: false,
        help_content: 'Think about what questions you want your dashboard to answer. This will help guide the design and widget selection.',
        actions: [
          {
            id: 'business_overview',
            label: 'Business Overview Dashboard',
            type: 'primary' as const,
            action: () => console.log('Business overview selected'),
          },
          {
            id: 'sales_tracking',
            label: 'Sales Tracking Dashboard',
            type: 'secondary' as const,
            action: () => console.log('Sales tracking selected'),
          },
        ],
      },
      {
        id: 'connect_data',
        title: 'Connect Your Data',
        description: 'Upload files or connect to data sources',
        icon: Database,
        estimated_time: '3 min',
        required: true,
        completed: false,
        skippable: false,
        help_content: 'You can upload CSV, Excel files, or connect to databases. The AI will help analyze your data structure.',
      },
      {
        id: 'create_widgets',
        title: 'Add Widgets',
        description: 'Create charts and KPIs to visualize your data',
        icon: BarChart3,
        estimated_time: '5 min',
        required: true,
        completed: false,
        skippable: false,
        help_content: 'Start with the most important metrics. You can always add more widgets later.',
      },
      {
        id: 'customize_design',
        title: 'Customize Design',
        description: 'Adjust colors, layout, and styling',
        icon: Palette,
        estimated_time: '3 min',
        required: false,
        completed: false,
        skippable: true,
        help_content: 'Make your dashboard visually appealing and aligned with your brand.',
      },
      {
        id: 'share_dashboard',
        title: 'Share & Collaborate',
        description: 'Set up sharing and collaboration options',
        icon: Share,
        estimated_time: '2 min',
        required: false,
        completed: false,
        skippable: true,
        help_content: 'Decide who can view or edit your dashboard and set up any automated reports.',
      },
    ],
  };

  return workflows[type as keyof typeof workflows] || [];
}

// Calculate total estimated time
function get_estimated_total_time(steps: WorkflowStep[]): string {
  const total_minutes = steps.reduce((total, step) => {
    const minutes = parseInt(step.estimated_time.replace(/\D/g, ''));
    return total + minutes;
  }, 0);

  return `${total_minutes} minutes`;
}
