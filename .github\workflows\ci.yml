name: Datagenius CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.10'
  NODE_VERSION: '18'

jobs:
  # Backend Testing
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: datagenius_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
      
      qdrant:
        image: qdrant/qdrant:latest
        ports:
          - 6333:6333
        options: >-
          --health-cmd "curl -f http://localhost:6333/health || exit 1"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov pytest-mock
    
    - name: Set up environment variables
      run: |
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/datagenius_test" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/1" >> $GITHUB_ENV
        echo "QDRANT_HOST=localhost" >> $GITHUB_ENV
        echo "QDRANT_PORT=6333" >> $GITHUB_ENV
        echo "SECRET_KEY=test-secret-key-for-ci" >> $GITHUB_ENV
        echo "TESTING=true" >> $GITHUB_ENV
        echo "MEM0_SELF_HOSTED=true" >> $GITHUB_ENV
    
    - name: Wait for services
      run: |
        sleep 10
        curl -f http://localhost:6333/health || exit 1
    
    - name: Run database migrations
      working-directory: ./backend
      run: |
        python -c "
        from app.database import init_db
        init_db()
        print('Database initialized successfully')
        "
    
    - name: Run unit tests
      working-directory: ./backend
      run: |
        pytest tests/ -v --cov=agents --cov=app --cov-report=xml --cov-report=term-missing
    
    - name: Run integration tests
      working-directory: ./backend
      run: |
        pytest tests/ -v -m "integration" --cov-append
    
    - name: Run security tests
      working-directory: ./backend
      run: |
        pytest tests/ -v -m "security"
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

  # Frontend Testing
  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run linting
      working-directory: ./frontend
      run: npm run lint
    
    - name: Run type checking
      working-directory: ./frontend
      run: npm run type-check
    
    - name: Run unit tests
      working-directory: ./frontend
      run: npm run test:coverage
    
    - name: Build application
      working-directory: ./frontend
      run: npm run build
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # End-to-End Testing
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: datagenius_e2e
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Set up environment variables
      run: |
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/datagenius_e2e" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/2" >> $GITHUB_ENV
        echo "SECRET_KEY=test-secret-key-for-e2e" >> $GITHUB_ENV
        echo "TESTING=true" >> $GITHUB_ENV
    
    - name: Start backend server
      working-directory: ./backend
      run: |
        python -c "
        from app.database import init_db
        init_db()
        "
        uvicorn app.main:app --host 0.0.0.0 --port 8000 &
        sleep 10
    
    - name: Build and start frontend
      working-directory: ./frontend
      run: |
        npm run build
        npm run preview -- --host 0.0.0.0 --port 3000 &
        sleep 5
    
    - name: Install Playwright
      working-directory: ./frontend
      run: npx playwright install --with-deps
    
    - name: Run E2E tests
      working-directory: ./frontend
      run: npm run test:e2e
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: e2e-test-results
        path: frontend/test-results/

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Run Bandit security linter
      working-directory: ./backend
      run: |
        pip install bandit
        bandit -r . -f json -o bandit-report.json || true
    
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      with:
        name: security-scan-results
        path: |
          trivy-results.sarif
          backend/bandit-report.json

  # Performance Testing
  performance-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install locust
    
    - name: Run performance tests
      working-directory: ./backend
      run: |
        # Add performance test commands here
        echo "Performance tests would run here"

  # Deployment (only on main branch)
  deploy:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, e2e-tests, security-scan]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to staging
      run: |
        echo "Deployment to staging would happen here"
        # Add deployment commands
    
    - name: Run smoke tests
      run: |
        echo "Smoke tests would run here"
        # Add smoke test commands
    
    - name: Deploy to production
      if: success()
      run: |
        echo "Deployment to production would happen here"
        # Add production deployment commands
