"""
Unified Security Middleware for Datagenius Backend

This middleware consolidates all security functionality into a single, efficient layer:
- Request validation and sanitization
- Rate limiting with IP-based tracking
- Threat detection with proper error handling
- File upload security validation
- Security headers management
- Environment-specific configuration
"""

import time
import json
import logging
import re
import ipaddress
from typing import Dict, Any, Set, Optional, List
from datetime import datetime, timedelta, timezone
from collections import defaultdict, deque
from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class UnifiedSecurityMiddleware(BaseHTTPMiddleware):
    """
    Consolidated security middleware that combines all security functionality.
    """
    
    def __init__(self, app, config: Optional[Dict] = None):
        super().__init__(app)
        self.config = config or {}
        
        # Core configuration
        self.enable_sanitization = self.config.get("enable_sanitization", True)
        self.enable_threat_detection = self.config.get("enable_threat_detection", True)
        self.enable_rate_limiting = self.config.get("enable_rate_limiting", True)
        self.strict_mode = self.config.get("strict_mode", False)
        self.environment = self.config.get("environment", "development")
        
        # Rate limiting configuration
        self.rate_limits = self.config.get("rate_limits", {
            "default": {"limit": 100, "window": 60},
            "file_upload": {"limit": 10, "window": 60},
            "auth": {"limit": 5, "window": 300}
        })
        
        # Security patterns for threat detection
        self.threat_patterns = {
            'sql_injection': [
                r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
                r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
                r"(--|#|/\*|\*/)",
                r"(\bxp_cmdshell\b|\bsp_executesql\b)"
            ],
            'xss': [
                r"<script[^>]*>.*?</script>",
                r"javascript:",
                r"on\w+\s*=",
                r"<iframe[^>]*>.*?</iframe>"
            ],
            'command_injection': [
                r"[;&|`$](?![^']*'[^']*$)(?![^\"]*\"[^\"]*$)",  # Improved to avoid false positives in strings
                r"\b(cat|ls|pwd|whoami|id|uname)\s+[/\\]",  # Only match when followed by path
                r"\$\([^)]*\)",
                r"`[^`]*`"
            ]
        }
        
        # Endpoints configuration
        self.skip_endpoints = {
            "/health", "/docs", "/openapi.json", "/redoc", "/favicon.ico",
            "/ws/", "/websocket"  # WebSocket endpoints
        }
        
        self.file_upload_endpoints = {
            "/business-profile-autofill/process-document",
            "/business-profile-autofill/process-multiple",
            "/files/upload", "/files/"
        }
        
        self.auth_endpoints = {
            "/auth/login", "/auth/refresh", "/auth/register", "/auth/logout"
        }
        
        # Security tracking
        self.rate_limit_storage: Dict[str, deque] = defaultdict(deque)
        self.blocked_ips: Set[str] = set()
        self.suspicious_ips: Dict[str, int] = defaultdict(int)
        self.failed_attempts: Dict[str, int] = defaultdict(int)
        
        # Security headers
        self.security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            'Referrer-Policy': 'strict-origin-when-cross-origin',
        }
        
        logger.info(f"Unified security middleware initialized for {self.environment} environment")

    async def dispatch(self, request: Request, call_next):
        """Process request through unified security middleware."""
        start_time = time.time()
        
        try:
            # Skip security for WebSocket connections
            if self._is_websocket_request(request):
                return await call_next(request)
            
            # Skip security for certain endpoints
            if self._should_skip_security(request):
                return await call_next(request)
            
            # Get client IP
            client_ip = self._get_client_ip(request)
            
            # Check if IP is blocked
            if self._is_ip_blocked(client_ip):
                return self._create_error_response(
                    status.HTTP_403_FORBIDDEN,
                    "Access denied from blocked IP"
                )
            
            # Rate limiting
            if self.enable_rate_limiting:
                rate_limit_result = self._check_rate_limits(request, client_ip)
                if not rate_limit_result["allowed"]:
                    self._record_security_event("rate_limit_exceeded", client_ip, rate_limit_result)
                    return self._create_error_response(
                        status.HTTP_429_TOO_MANY_REQUESTS,
                        "Rate limit exceeded",
                        {"retry_after": rate_limit_result.get("retry_after", 60)}
                    )
            
            # Request validation
            validation_result = await self._validate_request(request, client_ip)
            if not validation_result["is_valid"]:
                self._record_security_event("request_validation_failed", client_ip, validation_result["reason"])
                return self._create_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    validation_result["reason"]
                )
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            self._add_security_headers(response)
            
            # Log successful request
            processing_time = time.time() - start_time
            self._log_request(request, response, client_ip, processing_time)
            
            return response
            
        except Exception as e:
            logger.error(f"Unified security middleware error: {e}")
            self._record_security_event("middleware_error", self._get_client_ip(request), str(e))
            
            return self._create_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                "Internal server error"
            )

    def _is_websocket_request(self, request: Request) -> bool:
        """Check if request is a WebSocket connection."""
        return (request.url.path.startswith('/ws/') or 
                'websocket' in request.headers.get('upgrade', '').lower())

    def _should_skip_security(self, request: Request) -> bool:
        """Check if security should be skipped for this endpoint."""
        path = request.url.path
        return any(path.startswith(endpoint) for endpoint in self.skip_endpoints)

    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request headers."""
        # Check for forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"

    def _is_ip_blocked(self, client_ip: str) -> bool:
        """Check if IP is blocked."""
        return client_ip in self.blocked_ips

    def _check_rate_limits(self, request: Request, client_ip: str) -> Dict[str, Any]:
        """Check rate limits for the request."""
        # Determine rate limit type based on endpoint
        limit_type = self._get_rate_limit_type(request)
        rate_config = self.rate_limits.get(limit_type, self.rate_limits["default"])
        
        current_time = time.time()
        window_start = current_time - rate_config["window"]
        
        # Create key for rate limiting
        rate_key = f"{client_ip}:{limit_type}"
        
        # Clean old entries
        request_times = self.rate_limit_storage[rate_key]
        while request_times and request_times[0] < window_start:
            request_times.popleft()
        
        # Check if limit exceeded
        if len(request_times) >= rate_config["limit"]:
            return {
                "allowed": False,
                "retry_after": int(request_times[0] + rate_config["window"] - current_time)
            }
        
        # Add current request
        request_times.append(current_time)
        
        return {"allowed": True}

    def _get_rate_limit_type(self, request: Request) -> str:
        """Determine rate limit type based on request."""
        path = request.url.path

        if any(path.startswith(endpoint) for endpoint in self.auth_endpoints):
            return "auth"
        elif any(path.startswith(endpoint) for endpoint in self.file_upload_endpoints):
            return "file_upload"
        else:
            return "default"

    async def _validate_request(self, request: Request, client_ip: str) -> Dict[str, Any]:
        """Validate request for security threats."""
        result = {"is_valid": True, "reason": ""}

        # Special logging for business profile autofill endpoint
        if request.url.path == "/business-profile-autofill/process-document":
            logger.info(f"DEBUG: Validating request for business profile autofill")
            logger.info(f"DEBUG: Method: {request.method}, Content-Type: {request.headers.get('content-type', '')}")
            logger.info(f"DEBUG: Headers: {dict(request.headers)}")

        try:
            # Check request size
            content_length = request.headers.get('content-length')
            if content_length and int(content_length) > 50 * 1024 * 1024:  # 50MB limit
                result["is_valid"] = False
                result["reason"] = "Request size exceeds maximum allowed"
                return result

            # Skip detailed validation for file uploads
            is_file_upload = self._is_file_upload_request(request)
            if is_file_upload:
                if request.url.path == "/business-profile-autofill/process-document":
                    logger.info(f"DEBUG: Skipping detailed validation for file upload request: {request.url.path}")
                return result

            # Validate query parameters
            for param, value in request.query_params.items():
                if self.enable_sanitization and not self._is_safe_input(value, "query_param"):
                    if self.strict_mode:
                        result["is_valid"] = False
                        result["reason"] = f"Security threat detected in parameter '{param}'"
                        return result
                    else:
                        logger.warning(f"Potential threat in query param '{param}': {value[:100]}")

            # Validate request body for POST/PUT/PATCH requests
            if request.method in ["POST", "PUT", "PATCH"]:
                body_validation = await self._validate_request_body(request, client_ip)
                if not body_validation["is_valid"]:
                    return body_validation

            return result

        except Exception as e:
            logger.error(f"Request validation error: {e}")
            result["is_valid"] = False
            result["reason"] = "Request validation failed"
            return result

    def _is_file_upload_request(self, request: Request) -> bool:
        """Check if request is a file upload."""
        content_type = request.headers.get("content-type", "").lower()
        path = request.url.path

        # Check endpoint match
        is_endpoint_match = any(path.startswith(endpoint) for endpoint in self.file_upload_endpoints)
        is_multipart = content_type.startswith("multipart/form-data")
        has_boundary = "boundary=" in content_type

        # Force logging for debugging - use INFO level to ensure it shows
        if path == "/business-profile-autofill/process-document":
            logger.info(f"DEBUG: File upload detection for autofill endpoint")
            logger.info(f"DEBUG: Path: {path}, Content-Type: {content_type}")
            logger.info(f"DEBUG: Endpoint match: {is_endpoint_match}, Multipart: {is_multipart}, Boundary: {has_boundary}")
            logger.info(f"DEBUG: File upload endpoints: {self.file_upload_endpoints}")

        result = is_endpoint_match or is_multipart or has_boundary

        if path == "/business-profile-autofill/process-document":
            logger.info(f"DEBUG: File upload request detected: {result}")

        return result

    async def _validate_request_body(self, request: Request, client_ip: str) -> Dict[str, Any]:
        """Validate request body for security threats."""
        result = {"is_valid": True, "reason": ""}

        try:
            content_type = request.headers.get("content-type", "").lower()

            # Skip validation for binary content
            if (content_type.startswith("multipart/form-data") or
                content_type.startswith("application/octet-stream") or
                content_type.startswith("image/") or
                content_type.startswith("video/") or
                content_type.startswith("audio/") or
                content_type.startswith("application/pdf")):
                return result

            # Read and validate body
            body = await request.body()
            if not body:
                return result

            # Try to parse as JSON
            try:
                body_data = json.loads(body.decode('utf-8'))
                return self._validate_json_data(body_data, client_ip)
            except (json.JSONDecodeError, UnicodeDecodeError):
                # Validate as text for text-based content
                if content_type.startswith("text/") or content_type.startswith("application/json"):
                    try:
                        body_str = body.decode('utf-8', errors='ignore')
                        if not self._is_safe_input(body_str, "request_body"):
                            if self.strict_mode:
                                result["is_valid"] = False
                                result["reason"] = "Security threat detected in request body"
                            else:
                                logger.warning(f"Potential threat in request body: {body_str[:100]}")
                    except Exception as e:
                        logger.warning(f"Could not validate request body as text: {e}")

                return result

        except Exception as e:
            logger.error(f"Body validation error: {e}")
            result["is_valid"] = False
            result["reason"] = "Body validation failed"
            return result

    def _validate_json_data(self, data: Any, client_ip: str, path: str = "") -> Dict[str, Any]:
        """Recursively validate JSON data."""
        result = {"is_valid": True, "reason": ""}

        try:
            if isinstance(data, str):
                if not self._is_safe_input(data, "json_field"):
                    if self.strict_mode:
                        result["is_valid"] = False
                        result["reason"] = f"Security threat detected in JSON field{path}"
                    else:
                        logger.warning(f"Potential threat in JSON field{path}: {data[:100]}")
            elif isinstance(data, dict):
                for key, value in data.items():
                    sub_result = self._validate_json_data(value, client_ip, f"{path}.{key}")
                    if not sub_result["is_valid"]:
                        return sub_result
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    sub_result = self._validate_json_data(item, client_ip, f"{path}[{i}]")
                    if not sub_result["is_valid"]:
                        return sub_result

            return result

        except Exception as e:
            logger.error(f"JSON validation error: {e}")
            result["is_valid"] = False
            result["reason"] = "JSON validation failed"
            return result

    def _is_safe_input(self, text: str, context: str) -> bool:
        """Check if input text is safe from security threats."""
        if not self.enable_threat_detection:
            return True

        if not text or len(text) > 1024 * 1024:  # 1MB limit
            return False

        # Skip threat detection for certain contexts in development
        if self.environment == "development" and context in ["user_agent", "file_content"]:
            return True

        try:
            # Check for threat patterns
            for threat_type, patterns in self.threat_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, text, re.IGNORECASE):
                        logger.warning(f"Potential {threat_type} detected in {context}: {pattern}")
                        return False

            return True

        except Exception as e:
            logger.error(f"Threat detection error: {e}")
            return True  # Fail open to avoid breaking legitimate requests

    def _create_error_response(self, status_code: int, message: str, extra_data: Dict = None) -> JSONResponse:
        """Create standardized error response."""
        content = {"detail": message}
        if extra_data:
            content.update(extra_data)

        return JSONResponse(status_code=status_code, content=content)

    def _add_security_headers(self, response: Response):
        """Add security headers to response."""
        for header, value in self.security_headers.items():
            response.headers[header] = value

    def _record_security_event(self, event_type: str, client_ip: str, details: Any):
        """Record security event for monitoring."""
        event = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'event_type': event_type,
            'client_ip': client_ip,
            'details': details,
        }

        logger.warning(f"Security event: {json.dumps(event)}")

        # Track failed attempts
        if event_type in ['request_validation_failed', 'rate_limit_exceeded']:
            self.failed_attempts[client_ip] += 1

            # Block IP after too many failed attempts
            threshold = 50 if self.environment == "development" else 10
            if self.failed_attempts[client_ip] > threshold:
                self.blocked_ips.add(client_ip)
                logger.error(f"IP {client_ip} blocked due to suspicious activity")

    def _log_request(self, request: Request, response: Response, client_ip: str, processing_time: float):
        """Log request details."""
        log_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'client_ip': client_ip,
            'method': request.method,
            'path': str(request.url.path),
            'status_code': response.status_code,
            'processing_time': round(processing_time, 3),
            'user_agent': request.headers.get('user-agent', 'unknown'),
        }

        # Log as info for successful requests, warning for errors
        if response.status_code < 400:
            logger.info(f"Request: {json.dumps(log_entry)}")
        else:
            logger.warning(f"Error request: {json.dumps(log_entry)}")

    def cleanup_old_data(self):
        """Clean up old rate limiting and tracking data."""
        current_time = time.time()

        # Clean rate limit storage
        for key in list(self.rate_limit_storage.keys()):
            request_times = self.rate_limit_storage[key]
            while request_times and request_times[0] < current_time - 3600:  # 1 hour
                request_times.popleft()

            if not request_times:
                del self.rate_limit_storage[key]

        # Reset failed attempts periodically
        if int(current_time) % 3600 == 0:  # Every hour
            self.failed_attempts.clear()
            # Don't clear blocked IPs automatically - they should be manually reviewed

    def get_security_stats(self) -> Dict[str, Any]:
        """Get security statistics for monitoring."""
        return {
            "blocked_ips": len(self.blocked_ips),
            "failed_attempts": dict(self.failed_attempts),
            "rate_limit_entries": len(self.rate_limit_storage),
            "environment": self.environment,
            "config": {
                "sanitization_enabled": self.enable_sanitization,
                "threat_detection_enabled": self.enable_threat_detection,
                "rate_limiting_enabled": self.enable_rate_limiting,
                "strict_mode": self.strict_mode
            }
        }

    def unblock_ip(self, client_ip: str) -> bool:
        """Manually unblock an IP address."""
        if client_ip in self.blocked_ips:
            self.blocked_ips.remove(client_ip)
            if client_ip in self.failed_attempts:
                del self.failed_attempts[client_ip]
            logger.info(f"Unblocked IP {client_ip}")
            return True
        return False


# Configuration for different environments
UNIFIED_SECURITY_CONFIG = {
    'development': {
        'enable_sanitization': True,
        'enable_threat_detection': False,  # Disabled in development to reduce false positives
        'enable_rate_limiting': True,
        'strict_mode': False,
        'environment': 'development',
        'rate_limits': {
            'default': {'limit': 1000, 'window': 60},
            'file_upload': {'limit': 100, 'window': 60},
            'auth': {'limit': 20, 'window': 60}
        }
    },
    'testing': {
        'enable_sanitization': True,
        'enable_threat_detection': True,
        'enable_rate_limiting': False,  # Disabled for automated testing
        'strict_mode': False,
        'environment': 'testing',
        'rate_limits': {
            'default': {'limit': 1000, 'window': 60},
            'file_upload': {'limit': 100, 'window': 60},
            'auth': {'limit': 100, 'window': 60}
        }
    },
    'production': {
        'enable_sanitization': True,
        'enable_threat_detection': True,
        'enable_rate_limiting': True,
        'strict_mode': True,
        'environment': 'production',
        'rate_limits': {
            'default': {'limit': 100, 'window': 60},
            'file_upload': {'limit': 20, 'window': 60},
            'auth': {'limit': 5, 'window': 300}
        }
    }
}
