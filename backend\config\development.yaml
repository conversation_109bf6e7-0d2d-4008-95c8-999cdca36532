# Development environment configuration
# This file contains development-specific settings that override defaults

name: "Datagenius Development"
version: "1.0.0-dev"
description: "AI-powered data analysis platform (Development)"

environment:
  environment: "development"
  debug: true
  testing: false

database:
  url: "*********************************************************/datagenius"
  echo: true
  pool_size: 5
  max_overflow: 10
  pool_pre_ping: true
  pool_recycle: 3600
  connect_timeout: 30
  statement_timeout: 30000
  auto_migrate: true
  backup_enabled: false

redis:
  url: "redis://localhost:6379/0"
  max_connections: 10
  socket_timeout: 5
  default_ttl: 1800  # 30 minutes for development

security:
  jwt_secret_key: "development-secret-key-change-in-production-must-be-32-chars"
  access_token_expire_minutes: 60  # Longer for development
  refresh_token_expire_days: 14
  max_refresh_count: 50
  enforce_ip_validation: false
  ip_change_lockout: false
  max_upload_size: 52428800  # 50MB for development
  rate_limiting_enabled: false  # Disabled for development
  cors_origins:
    - "http://localhost:3000"
    - "http://localhost:5173"
    - "http://localhost:8080"
  enforce_https: false
  require_email_verification: false
  two_factor_auth_enabled: false

llm:
  default_provider: "groq"
  fallback_providers:
    - "groq"
    - "openai"
  default_temperature: 0.7
  default_max_tokens: 2048
  enable_caching: true
  cache_ttl_seconds: 1800  # 30 minutes
  enable_streaming: true
  content_filter_enabled: false  # Relaxed for development
  log_requests: true
  log_responses: true  # Enable for debugging
  track_usage: true

email:
  enabled: false  # Disabled for development
  sender: "<EMAIL>"
  smtp_server: "localhost"
  smtp_port: 1025  # MailHog default port
  use_tls: false

files:
  upload_dir: "temp_uploads_dev"
  max_upload_size: 52428800  # 50MB
  chunking_performance_profile: "fast"
  chunking_use_adaptive: true
  chunking_enable_caching: true
  chunking_batch_size: 8  # Smaller for development
  chunking_parallel_workers: 2

vector:
  qdrant_host: "localhost"
  qdrant_port: 6333
  qdrant_https: false
  mem0_self_hosted: true
  mem0_default_ttl: 86400  # 1 day for development
  mem0_max_memories: 500
  mem0_memory_threshold: 0.6  # Lower threshold for development

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/datagenius_dev.log"
  max_file_size: 5242880  # 5MB
  backup_count: 3

monitoring:
  enabled: true
  performance_tracking: true
  error_tracking: true
  alert_on_errors: false  # No alerts in development
  alert_threshold: 50

frontend_url: "http://localhost:5173"

google_redirect_uri: "http://localhost:5173/auth/google/callback"
