#!/usr/bin/env python3
"""
Enhanced Prompt System Validation Script

This script validates that the enhanced prompt system works correctly
and maintains backward compatibility with existing functionality.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from typing import Dict, Any, List

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Import only the core system prompt utilities to avoid dependency issues
try:
    from agents.utils.system_prompts import (
        EnhancedPromptProcessor
    )
    SYSTEM_PROMPTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import system prompts module: {e}")
    SYSTEM_PROMPTS_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def validate_operating_mode_detection():
    """Test operating mode detection functionality."""
    logger.info("🔍 Testing Operating Mode Detection...")

    if not SYSTEM_PROMPTS_AVAILABLE:
        logger.warning("⚠️ SKIP: System prompts module not available")
        return True

    test_cases = [
        {"message": "Give me a quick summary", "expected": "QUICK"},
        {"message": "Can you explain how this works?", "expected": "EDUCATIONAL"},
        {"message": "I need a detailed analysis", "expected": "DETAILED"},
        {"message": "Help me understand the concept", "expected": "EDUCATIONAL"},
        {"message": "Brief overview please", "expected": "QUICK"},
        {"message": "Analyze this data thoroughly", "expected": "DETAILED"}
    ]

    passed = 0
    failed = 0

    for test_case in test_cases:
        user_context = {"message": test_case["message"]}
        detected_mode = EnhancedPromptProcessor.detect_operating_mode(user_context)

        if detected_mode == test_case["expected"]:
            logger.info(f"✅ PASS: '{test_case['message']}' -> {detected_mode}")
            passed += 1
        else:
            logger.error(f"❌ FAIL: '{test_case['message']}' -> Expected: {test_case['expected']}, Got: {detected_mode}")
            failed += 1

    logger.info(f"Operating Mode Detection: {passed} passed, {failed} failed")
    return failed == 0


async def validate_enhanced_prompt_structure():
    """Test enhanced prompt structure generation."""
    logger.info("🔍 Testing Enhanced Prompt Structure...")

    if not SYSTEM_PROMPTS_AVAILABLE:
        logger.warning("⚠️ SKIP: System prompts module not available")
        return True

    # Test that the enhanced processor methods exist and work
    try:
        methodology = EnhancedPromptProcessor.get_methodology_framework()
        response_formats = EnhancedPromptProcessor.get_response_formats()
        behavioral_guidelines = EnhancedPromptProcessor.get_behavioral_guidelines()
        tool_integration = EnhancedPromptProcessor.get_standardized_tool_integration()

        required_content = [
            ("Methodology Framework", methodology, "## SYSTEMATIC METHODOLOGY"),
            ("Response Formats", response_formats, "## RESPONSE FORMATS"),
            ("Behavioral Guidelines", behavioral_guidelines, "## BEHAVIORAL GUIDELINES"),
            ("Tool Integration", tool_integration, "## TOOL INTEGRATION STANDARDS")
        ]

        passed = 0
        failed = 0

        for name, content, expected_header in required_content:
            if expected_header in content and len(content) > 100:
                logger.info(f"✅ PASS: {name} contains expected content")
                passed += 1
            else:
                logger.error(f"❌ FAIL: {name} missing expected content or too short")
                failed += 1

        logger.info(f"Enhanced Prompt Structure: {passed} passed, {failed} failed")
        return failed == 0

    except Exception as e:
        logger.error(f"❌ ERROR: Enhanced prompt structure test failed - {str(e)}")
        return False


async def validate_backward_compatibility():
    """Test that existing functionality still works."""
    logger.info("🔍 Testing Backward Compatibility...")

    if not SYSTEM_PROMPTS_AVAILABLE:
        logger.warning("⚠️ SKIP: System prompts module not available")
        return True

    try:
        # Test that the EnhancedPromptProcessor class exists and has required methods
        required_methods = [
            'detect_operating_mode',
            'get_methodology_framework',
            'get_response_formats',
            'get_operating_mode_instructions',
            'get_behavioral_guidelines',
            'get_standardized_tool_integration'
        ]

        missing_methods = []
        for method_name in required_methods:
            if not hasattr(EnhancedPromptProcessor, method_name):
                missing_methods.append(method_name)

        if missing_methods:
            logger.error(f"❌ FAIL: Missing methods: {missing_methods}")
            return False

        logger.info("✅ PASS: All required methods exist")
        return True

    except Exception as e:
        logger.error(f"❌ ERROR: Backward compatibility test failed - {str(e)}")
        return False


async def validate_persona_configurations():
    """Validate that persona YAML configurations are valid."""
    logger.info("🔍 Testing Persona Configurations...")
    
    personas_dir = backend_dir / "personas"
    if not personas_dir.exists():
        logger.error("❌ FAIL: Personas directory not found")
        return False
    
    passed = 0
    failed = 0
    
    for yaml_file in personas_dir.glob("*.yaml"):
        try:
            import yaml
            with open(yaml_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # Check for required fields
            required_fields = ["id", "name", "description", "system_prompts"]
            missing_fields = [field for field in required_fields if field not in config]
            
            if missing_fields:
                logger.error(f"❌ FAIL: {yaml_file.name} missing fields: {missing_fields}")
                failed += 1
            else:
                # Check system prompts structure
                system_prompts = config.get("system_prompts", {})
                if "default" not in system_prompts:
                    logger.error(f"❌ FAIL: {yaml_file.name} missing default system prompt")
                    failed += 1
                else:
                    logger.info(f"✅ PASS: {yaml_file.name} configuration valid")
                    passed += 1
                    
        except Exception as e:
            logger.error(f"❌ ERROR: {yaml_file.name} - {str(e)}")
            failed += 1
    
    logger.info(f"Persona Configurations: {passed} passed, {failed} failed")
    return failed == 0


async def main():
    """Run all validation tests."""
    logger.info("🚀 Starting Enhanced Prompt System Validation")
    
    tests = [
        ("Operating Mode Detection", validate_operating_mode_detection),
        ("Enhanced Prompt Structure", validate_enhanced_prompt_structure),
        ("Backward Compatibility", validate_backward_compatibility),
        ("Persona Configurations", validate_persona_configurations)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ CRITICAL ERROR in {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("VALIDATION SUMMARY")
    logger.info(f"{'='*50}")
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
        if result:
            passed_tests += 1
    
    logger.info(f"\nOverall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 All validation tests passed! Enhanced prompt system is working correctly.")
        return 0
    else:
        logger.error("⚠️  Some validation tests failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
