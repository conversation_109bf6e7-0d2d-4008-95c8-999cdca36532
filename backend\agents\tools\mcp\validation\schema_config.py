"""
Configuration system for MCP tool validation schemas.

This module provides configuration-based schema management, allowing
schemas to be defined in YAML/JSON files and automatically loaded.
"""

import logging
import yaml
import json
from typing import Dict, Any, Type, Optional, List
from pathlib import Path
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class SchemaConfig(BaseModel):
    """Configuration for a tool schema."""
    
    tool_name: str = Field(description="Name of the tool")
    schema_type: str = Field(default="dynamic", description="Type of schema (static, dynamic, inferred)")
    base_schema: Optional[str] = Field(default=None, description="Base schema to inherit from")
    fields: Dict[str, Any] = Field(default_factory=dict, description="Field definitions")
    validators: Dict[str, str] = Field(default_factory=dict, description="Custom validators")
    enabled: bool = Field(default=True, description="Whether schema is enabled")
    priority: int = Field(default=100, description="Schema priority (lower = higher priority)")


class SchemaConfigManager:
    """
    Manager for loading and applying schema configurations.
    """
    
    def __init__(self, config_paths: Optional[List[str]] = None):
        """
        Initialize schema config manager.
        
        Args:
            config_paths: List of paths to search for config files
        """
        self.config_paths = config_paths or [
            "backend/agents/tools/mcp/config/schemas.yaml",
            "backend/agents/tools/mcp/config/schemas.json",
            "config/mcp_schemas.yaml",
            "config/mcp_schemas.json"
        ]
        self.schemas: Dict[str, SchemaConfig] = {}
        self.loaded_configs: List[str] = []
    
    def load_configurations(self) -> bool:
        """
        Load schema configurations from files.
        
        Returns:
            True if any configurations were loaded
        """
        loaded_any = False
        
        for config_path in self.config_paths:
            if self._load_config_file(config_path):
                loaded_any = True
        
        if loaded_any:
            logger.info(f"Loaded {len(self.schemas)} schema configurations")
        
        return loaded_any
    
    def _load_config_file(self, config_path: str) -> bool:
        """
        Load a single configuration file.
        
        Args:
            config_path: Path to configuration file
        
        Returns:
            True if file was loaded successfully
        """
        try:
            path = Path(config_path)
            if not path.exists():
                return False
            
            with open(path, 'r', encoding='utf-8') as f:
                if path.suffix.lower() in ['.yaml', '.yml']:
                    data = yaml.safe_load(f)
                elif path.suffix.lower() == '.json':
                    data = json.load(f)
                else:
                    logger.warning(f"Unsupported config file format: {config_path}")
                    return False
            
            # Parse schema configurations
            schemas_data = data.get('schemas', {})
            for tool_name, schema_data in schemas_data.items():
                try:
                    schema_config = SchemaConfig(tool_name=tool_name, **schema_data)
                    self.schemas[tool_name] = schema_config
                except Exception as e:
                    logger.warning(f"Invalid schema config for {tool_name}: {e}")
            
            self.loaded_configs.append(config_path)
            logger.debug(f"Loaded schema config from {config_path}")
            return True
            
        except Exception as e:
            logger.warning(f"Failed to load config from {config_path}: {e}")
            return False
    
    def get_schema_config(self, tool_name: str) -> Optional[SchemaConfig]:
        """
        Get schema configuration for a tool.
        
        Args:
            tool_name: Name of the tool
        
        Returns:
            Schema configuration or None
        """
        return self.schemas.get(tool_name)
    
    def apply_configurations(self):
        """
        Apply loaded configurations to the input validator.
        """
        try:
            from .input_validator import get_input_validator
            from .schema_decorators import create_tool_schema, SchemaBuilder
            
            validator = get_input_validator()
            
            # Sort by priority
            sorted_schemas = sorted(
                self.schemas.values(),
                key=lambda x: x.priority
            )
            
            for schema_config in sorted_schemas:
                if not schema_config.enabled:
                    continue
                
                try:
                    if schema_config.schema_type == "dynamic":
                        # Create dynamic schema
                        schema_class = self._create_dynamic_schema(schema_config)
                        if schema_class:
                            validator.add_custom_schema(schema_config.tool_name, schema_class)
                            logger.info(f"Applied dynamic schema for {schema_config.tool_name}")
                    
                    elif schema_config.schema_type == "inferred":
                        # Let validator infer schema
                        # This would require the tool class to be available
                        logger.debug(f"Inferred schema requested for {schema_config.tool_name}")
                    
                except Exception as e:
                    logger.warning(f"Failed to apply schema for {schema_config.tool_name}: {e}")
        
        except Exception as e:
            logger.error(f"Failed to apply schema configurations: {e}")
    
    def _create_dynamic_schema(self, config: SchemaConfig) -> Optional[Type[BaseModel]]:
        """
        Create a dynamic schema from configuration.
        
        Args:
            config: Schema configuration
        
        Returns:
            Dynamic schema class or None
        """
        try:
            from .schema_decorators import SchemaBuilder, schema_field
            from .input_validator import BaseInputSchema
            
            # Determine base schema
            base_schema = BaseInputSchema
            if config.base_schema:
                # Try to resolve base schema by name
                base_schema = self._resolve_base_schema(config.base_schema)
            
            builder = SchemaBuilder(base_schema)
            
            # Add fields from configuration
            for field_name, field_config in config.fields.items():
                field_type = self._resolve_field_type(field_config.get('type', 'str'))
                description = field_config.get('description', '')
                default = field_config.get('default')
                required = field_config.get('required', False)
                
                field_kwargs = {}
                if not required and default is not None:
                    field_kwargs['default'] = default
                elif not required:
                    field_kwargs['default'] = None
                
                # Add other field properties
                for prop in ['min_length', 'max_length', 'ge', 'le', 'gt', 'lt']:
                    if prop in field_config:
                        field_kwargs[prop] = field_config[prop]
                
                builder.add_field(field_name, field_type, description, **field_kwargs)
            
            # Build schema
            schema_name = f"{config.tool_name.title()}ConfigSchema"
            return builder.build(schema_name)
            
        except Exception as e:
            logger.warning(f"Failed to create dynamic schema for {config.tool_name}: {e}")
            return None
    
    def _resolve_base_schema(self, base_schema_name: str) -> Type[BaseModel]:
        """
        Resolve base schema by name.
        
        Args:
            base_schema_name: Name of base schema
        
        Returns:
            Base schema class
        """
        from .input_validator import (
            BaseInputSchema, FileInputSchema, QueryInputSchema,
            PandasAIInputSchema, IntentDetectionInputSchema
        )
        
        schema_map = {
            'BaseInputSchema': BaseInputSchema,
            'FileInputSchema': FileInputSchema,
            'QueryInputSchema': QueryInputSchema,
            'PandasAIInputSchema': PandasAIInputSchema,
            'IntentDetectionInputSchema': IntentDetectionInputSchema
        }
        
        return schema_map.get(base_schema_name, BaseInputSchema)
    
    def _resolve_field_type(self, type_name: str) -> Type:
        """
        Resolve field type by name.
        
        Args:
            type_name: Name of the type
        
        Returns:
            Python type
        """
        from typing import Dict, List, Any, Optional
        
        type_map = {
            'str': str,
            'string': str,
            'int': int,
            'integer': int,
            'float': float,
            'number': float,
            'bool': bool,
            'boolean': bool,
            'dict': Dict[str, Any],
            'list': List[Any],
            'any': Any,
            'optional_str': Optional[str],
            'optional_int': Optional[int],
            'optional_float': Optional[float],
            'optional_bool': Optional[bool]
        }
        
        return type_map.get(type_name.lower(), str)
    
    def export_current_schemas(self, output_path: str):
        """
        Export current schema configurations to a file.
        
        Args:
            output_path: Path to output file
        """
        try:
            export_data = {
                'schemas': {
                    name: {
                        'schema_type': config.schema_type,
                        'base_schema': config.base_schema,
                        'fields': config.fields,
                        'validators': config.validators,
                        'enabled': config.enabled,
                        'priority': config.priority
                    }
                    for name, config in self.schemas.items()
                }
            }
            
            path = Path(output_path)
            with open(path, 'w', encoding='utf-8') as f:
                if path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(export_data, f, default_flow_style=False)
                else:
                    json.dump(export_data, f, indent=2)
            
            logger.info(f"Exported schema configurations to {output_path}")
            
        except Exception as e:
            logger.error(f"Failed to export schemas to {output_path}: {e}")


# Global config manager instance
schema_config_manager = SchemaConfigManager()


def load_schema_configurations() -> bool:
    """
    Load schema configurations from files.
    
    Returns:
        True if configurations were loaded
    """
    return schema_config_manager.load_configurations()


def apply_schema_configurations():
    """
    Apply loaded schema configurations.
    """
    schema_config_manager.apply_configurations()


def get_schema_config_manager() -> SchemaConfigManager:
    """
    Get the global schema config manager.
    
    Returns:
        Schema config manager instance
    """
    return schema_config_manager
