"""
Universal streaming mixin for all agents.

This module provides a streaming capability that can be mixed into any agent
to automatically support streaming responses without requiring custom code.
"""

import asyncio
import logging
import yaml
from pathlib import Path
from typing import Dict, Any, AsyncGenerator, Optional, List
from abc import ABC

logger = logging.getLogger(__name__)

# Load streaming configuration
def load_streaming_config() -> Dict[str, Any]:
    """Load streaming configuration from YAML file."""
    try:
        config_path = Path(__file__).parent.parent / "config" / "streaming_config.yaml"
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        else:
            logger.warning(f"Streaming config file not found at {config_path}, using defaults")
            return {}
    except Exception as e:
        logger.error(f"Error loading streaming config: {e}")
        return {}

# Global streaming configuration
STREAMING_CONFIG = load_streaming_config()


class StreamingMixin(ABC):
    """
    Mixin class that provides universal streaming functionality to any agent.
    
    This mixin automatically adds streaming support to any agent that inherits from it.
    It works by wrapping the existing process_message method and streaming the response.
    """

    async def process_streaming_message(
        self,
        message: str,
        user_id: str,
        conversation_id: str,
        context: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Universal streaming message processor that works with any agent.
        
        This method automatically provides streaming functionality by:
        1. Calling the agent's regular process_message method
        2. Streaming the response in chunks with natural delays
        3. Handling errors gracefully
        
        Args:
            message: User's message
            user_id: The ID of the user
            conversation_id: The ID of the conversation
            context: Additional context from the orchestrator
            
        Yields:
            Dictionary containing streaming chunks and metadata
        """
        try:
            logger.debug(f"Starting streaming response for agent {getattr(self, 'name', 'unknown')}")
            
            # Check if agent has streaming configuration
            streaming_config = self._get_streaming_config(context)
            
            # Call the agent's regular process_message method
            # Enhanced signature detection for maximum compatibility
            response = await self._call_process_message_with_signature_detection(
                message, user_id, conversation_id, context
            )
            
            if not response.get("success", True):
                # If processing failed, yield the error
                yield {
                    "type": "content",
                    "content": response.get("message", "I encountered an error processing your request.")
                }
                yield {
                    "type": "metadata",
                    "metadata": response.get("metadata", {"error": "Processing failed"})
                }
                return
            
            response_message = response.get("message", "")
            response_metadata = response.get("metadata", {})
            
            # Stream the response based on configuration
            async for chunk_data in self._stream_response(
                response_message, 
                response_metadata, 
                streaming_config
            ):
                yield chunk_data
                
        except Exception as e:
            logger.error(f"Error in universal streaming: {str(e)}")
            yield {
                "type": "content",
                "content": "I apologize, but I encountered an error while processing your request."
            }
            yield {
                "type": "metadata",
                "metadata": {"error": str(e), "streaming": True}
            }

    async def _call_process_message_with_signature_detection(
        self,
        message: str,
        user_id: str,
        conversation_id: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Enhanced signature detection that can handle any process_message signature.

        This method uses introspection to determine the correct way to call
        process_message for maximum compatibility with current and future agents.
        """
        import inspect

        try:
            # Get the method signature
            sig = inspect.signature(self.process_message)
            param_names = list(sig.parameters.keys())

            # Remove 'self' from parameter names
            if param_names and param_names[0] == 'self':
                param_names = param_names[1:]

            logger.debug(f"Agent {getattr(self, 'name', 'unknown')} process_message signature: {param_names}")

            # Build arguments based on parameter names and types
            args = []
            kwargs = {}

            for param_name in param_names:
                param = sig.parameters[param_name]

                if param_name in ['message', 'msg', 'user_message', 'text']:
                    # Message parameter
                    if param.annotation == str or param.annotation == inspect.Parameter.empty:
                        args.append(message)
                    else:
                        args.append(str(message))

                elif param_name in ['user_id', 'userid', 'user', 'uid']:
                    # User ID parameter - handle type conversion
                    if param.annotation == int:
                        args.append(int(user_id) if isinstance(user_id, str) else user_id)
                    elif param.annotation == str:
                        args.append(str(user_id))
                    else:
                        # Default behavior based on current patterns
                        if param_names[0] == 'message':
                            # Concierge style - user_id as string
                            args.append(str(user_id))
                        else:
                            # Marketing/Analysis style - user_id as int
                            args.append(int(user_id) if isinstance(user_id, str) else user_id)

                elif param_name in ['conversation_id', 'conv_id', 'session_id', 'chat_id']:
                    # Conversation ID parameter
                    args.append(str(conversation_id))

                elif param_name in ['context', 'ctx', 'metadata', 'extra']:
                    # Context parameter
                    args.append(context)

                else:
                    # Unknown parameter - try to infer from position and type
                    logger.warning(f"Unknown parameter '{param_name}' in process_message signature")
                    if len(args) == 0:
                        args.append(message)
                    elif len(args) == 1:
                        args.append(int(user_id) if isinstance(user_id, str) else user_id)
                    elif len(args) == 2:
                        args.append(str(conversation_id))
                    elif len(args) == 3:
                        args.append(context)

            # Call the method with detected arguments
            logger.debug(f"Calling process_message with {len(args)} args")
            return await self.process_message(*args, **kwargs)

        except Exception as e:
            logger.warning(f"Signature detection failed: {e}, falling back to legacy detection")
            # Fallback to the original simple detection
            return await self._legacy_process_message_call(message, user_id, conversation_id, context)

    async def _legacy_process_message_call(
        self,
        message: str,
        user_id: str,
        conversation_id: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Legacy signature detection for backward compatibility.
        """
        import inspect

        # Get the method signature to determine parameter order
        sig = inspect.signature(self.process_message)
        param_names = list(sig.parameters.keys())

        # Remove 'self' from parameter names
        if param_names and param_names[0] == 'self':
            param_names = param_names[1:]

        # Determine the correct parameter order based on the signature
        if len(param_names) >= 4:
            # Check if first parameter is 'message' (concierge style) or 'user_id' (marketing/analysis style)
            if param_names[0] == 'message':
                # Concierge agent style: (message, user_id, conversation_id, context)
                return await self.process_message(message, user_id, conversation_id, context)
            else:
                # Marketing/Analysis agent style: (user_id, message, conversation_id, context)
                # Convert user_id to int since these agents expect int
                user_id_int = int(user_id) if isinstance(user_id, str) else user_id
                return await self.process_message(user_id_int, message, conversation_id, context)
        else:
            # Fallback: try the most common signature
            try:
                user_id_int = int(user_id) if isinstance(user_id, str) else user_id
                return await self.process_message(user_id_int, message, conversation_id, context)
            except Exception:
                # If that fails, try the concierge style
                return await self.process_message(message, user_id, conversation_id, context)

    def _get_streaming_config(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get streaming configuration from YAML config, agent config, and context.

        Args:
            context: Request context

        Returns:
            Streaming configuration dictionary
        """
        # Start with global default configuration
        config = STREAMING_CONFIG.get("default", {
            "chunk_size": 30,
            "delay_ms": 30,
            "min_chunk_size": 10,
            "max_chunk_size": 100,
            "word_boundary": True,
            "adaptive_delay": True,
        }).copy()

        # Override with agent-specific config from YAML
        agent_name = getattr(self, 'name', None) or getattr(self, 'agent_id', None)
        if agent_name:
            agent_yaml_config = STREAMING_CONFIG.get("agents", {}).get(agent_name, {})
            config.update(agent_yaml_config)

        # Override with agent instance config if available
        agent_config = getattr(self, 'config', {}).get('streaming', {})
        config.update(agent_config)

        # Override with context-specific config if available
        context_config = context.get('streaming_config', {})
        config.update(context_config)

        # Apply content-type specific settings if specified
        content_type = context.get('content_type')
        if content_type:
            content_config = STREAMING_CONFIG.get("content_types", {}).get(content_type, {})
            config.update(content_config)

        return config

    async def _stream_response(
        self, 
        response_message: str, 
        response_metadata: Dict[str, Any],
        config: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream a response message in chunks.
        
        Args:
            response_message: The complete response message
            response_metadata: Response metadata
            config: Streaming configuration
            
        Yields:
            Streaming chunk data
        """
        if not response_message:
            yield {
                "type": "metadata",
                "metadata": response_metadata
            }
            return
        
        chunk_size = config.get("chunk_size", 30)
        delay_ms = config.get("delay_ms", 30)
        word_boundary = config.get("word_boundary", True)
        adaptive_delay = config.get("adaptive_delay", True)
        
        # Calculate adaptive delay based on message length
        if adaptive_delay:
            # Longer messages get shorter delays to maintain reasonable total time
            total_chars = len(response_message)
            if total_chars > 500:
                delay_ms = max(10, delay_ms // 2)
            elif total_chars < 100:
                delay_ms = min(100, delay_ms * 1.5)
        
        # Split message into chunks
        chunks = self._split_into_chunks(response_message, chunk_size, word_boundary)
        
        # Stream each chunk
        for i, chunk in enumerate(chunks):
            if chunk.strip():  # Only send non-empty chunks
                yield {
                    "type": "content",
                    "content": chunk
                }
                
                # Add delay between chunks (except for the last one)
                if i < len(chunks) - 1:
                    await asyncio.sleep(delay_ms / 1000.0)
        
        # Send final metadata
        final_metadata = {
            **response_metadata,
            "streaming": True,
            "total_chunks": len(chunks),
            "total_characters": len(response_message)
        }
        
        yield {
            "type": "metadata",
            "metadata": final_metadata
        }

    def _split_into_chunks(
        self,
        text: str,
        chunk_size: int,
        word_boundary: bool = True
    ) -> List[str]:
        """
        Split text into chunks for streaming with proper space preservation.

        Args:
            text: Text to split
            chunk_size: Target size for each chunk
            word_boundary: Whether to break on word boundaries

        Returns:
            List of text chunks that preserve spacing when concatenated
        """
        if not text:
            return []

        if not word_boundary:
            # Simple character-based chunking
            return [text[i:i + chunk_size] for i in range(0, len(text), chunk_size)]

        # Word-boundary aware chunking with proper space preservation
        chunks = []
        words = text.split(' ')  # Split on spaces to preserve them
        current_chunk = ""

        for i, word in enumerate(words):
            # Calculate what the chunk would be if we add this word
            if current_chunk:
                # Add space before word (except for first word in chunk)
                test_chunk = current_chunk + " " + word
            else:
                # First word in chunk, no leading space needed
                test_chunk = word

            if len(test_chunk) <= chunk_size:
                # Word fits in current chunk
                current_chunk = test_chunk
            else:
                # Word doesn't fit, need to start new chunk
                if current_chunk:
                    # Save current chunk and start new one
                    chunks.append(current_chunk)
                    # New chunk starts with current word
                    # Add leading space if this chunk will be concatenated with previous
                    if len(chunks) > 0:
                        current_chunk = " " + word
                    else:
                        current_chunk = word
                else:
                    # Current chunk is empty but word is too long
                    # Split the word itself
                    if len(word) > chunk_size:
                        # Word is longer than chunk size, split it
                        chunks.append(word[:chunk_size])
                        remaining = word[chunk_size:]
                        # Add space before remaining part if needed
                        if len(chunks) > 0:
                            current_chunk = " " + remaining
                        else:
                            current_chunk = remaining
                    else:
                        # Word fits but we need leading space
                        if len(chunks) > 0:
                            current_chunk = " " + word
                        else:
                            current_chunk = word

        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    def supports_streaming(self) -> bool:
        """
        Check if this agent supports streaming.
        
        Returns:
            True (all agents with this mixin support streaming)
        """
        return True

    def get_streaming_capabilities(self) -> Dict[str, Any]:
        """
        Get information about this agent's streaming capabilities.
        
        Returns:
            Dictionary describing streaming capabilities
        """
        return {
            "supports_streaming": True,
            "streaming_method": "universal_mixin",
            "configurable": True,
            "adaptive_delay": True,
            "word_boundary_aware": True
        }
