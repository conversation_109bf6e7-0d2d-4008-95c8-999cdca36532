"""
Comprehensive Testing Framework for MCP Tools.

This module provides unit tests, integration tests, and end-to-end tests
for all MCP tools with mock data, edge case testing, and automated test execution.
"""

import asyncio
import logging
import time
import json
import tempfile
import os
from typing import Dict, Any, List, Optional, Callable, Type, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import unittest
from unittest.mock import Mock, AsyncMock, patch
import pytest
import pandas as pd

from ..base import BaseMCPTool
from ..validation.input_validator import get_input_validator
from ..monitoring.performance_monitor import get_performance_monitor
from ..error_handling.robust_error_handler import get_error_handler
from ..security.access_control import get_access_controller, SecurityContext, PermissionLevel

logger = logging.getLogger(__name__)


class TestType(Enum):
    """Types of tests."""
    UNIT = "unit"
    INTEGRATION = "integration"
    END_TO_END = "end_to_end"
    PERFORMANCE = "performance"
    SECURITY = "security"
    STRESS = "stress"


class TestResult(Enum):
    """Test result status."""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class TestCase:
    """Individual test case definition."""
    test_id: str
    name: str
    description: str
    test_type: TestType
    tool_name: str
    input_data: Dict[str, Any]
    expected_output: Optional[Dict[str, Any]] = None
    expected_error: Optional[str] = None
    timeout_seconds: int = 30
    setup_function: Optional[Callable] = None
    teardown_function: Optional[Callable] = None
    mock_dependencies: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)


@dataclass
class TestExecution:
    """Test execution result."""
    test_case: TestCase
    result: TestResult
    execution_time: float
    output: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class TestSuite:
    """Collection of test cases."""
    suite_id: str
    name: str
    description: str
    test_cases: List[TestCase] = field(default_factory=list)
    setup_function: Optional[Callable] = None
    teardown_function: Optional[Callable] = None


class TestDataGenerator:
    """Generate realistic test data for validation and testing purposes only."""

    def __init__(self):
        """Initialize with security constraints."""
        self.max_rows = 1000  # Security limit
        self.max_memory_mb = 50  # Memory limit in MB

    def generate_csv_data(self, rows: int = 100, schema: Dict[str, str] = None) -> str:
        """Generate structured CSV data based on schema with memory management."""
        import io
        import sys

        # Security: Validate input parameters
        if rows <= 0 or rows > self.max_rows:
            raise ValueError(f"Invalid row count: {rows}. Must be between 1 and {self.max_rows}")

        # Use provided schema or default
        if schema is None:
            schema = {
                'id': 'int',
                'name': 'string',
                'value': 'float',
                'category': 'string',
                'created_date': 'date'
            }

        try:
            # Generate data based on schema
            data = {}
            for column, data_type in schema.items():
                data[column] = self._generate_column_data(data_type, rows)

            # Create DataFrame with memory monitoring
            df = pd.DataFrame(data)

            # Check memory usage
            memory_usage = df.memory_usage(deep=True).sum() / (1024 * 1024)  # MB
            if memory_usage > self.max_memory_mb:
                raise MemoryError(f"Generated data exceeds memory limit: {memory_usage:.2f}MB")

            # Convert to CSV
            csv_buffer = io.StringIO()
            df.to_csv(csv_buffer, index=False)
            csv_data = csv_buffer.getvalue()

            # Clean up to prevent memory leaks
            del df
            csv_buffer.close()

            return csv_data

        except Exception as e:
            raise RuntimeError(f"Error generating CSV data: {e}")

    def _generate_column_data(self, data_type: str, rows: int) -> List[Any]:
        """Generate column data based on type with proper validation."""
        import random
        from datetime import datetime, timedelta

        if data_type == 'int':
            return list(range(1, rows + 1))
        elif data_type == 'float':
            return [round(random.uniform(1.0, 1000.0), 2) for _ in range(rows)]
        elif data_type == 'string':
            return [f"Item_{i:04d}" for i in range(1, rows + 1)]
        elif data_type == 'date':
            base_date = datetime(2024, 1, 1)
            return [(base_date + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(rows)]
        else:
            raise ValueError(f"Unsupported data type: {data_type}")
    
    def generate_text_data(self, length: int = 1000, domain: str = "general") -> str:
        """Generate structured text data with domain-specific vocabulary."""
        if length <= 0 or length > 100000:  # Security limit
            raise ValueError(f"Invalid length: {length}. Must be between 1 and 100000")

        # Domain-specific vocabularies
        vocabularies = {
            "general": ["data", "analysis", "system", "process", "result", "information"],
            "technical": ["algorithm", "framework", "implementation", "optimization", "performance"],
            "business": ["strategy", "revenue", "customer", "market", "growth", "efficiency"]
        }

        words = vocabularies.get(domain, vocabularies["general"])

        # Generate structured sentences
        sentences = []
        words_per_sentence = 8
        num_sentences = min(length // (words_per_sentence * 5), 1000)  # Limit sentences

        for i in range(num_sentences):
            sentence_words = [words[j % len(words)] for j in range(i, i + words_per_sentence)]
            sentence = " ".join(sentence_words).capitalize() + "."
            sentences.append(sentence)

        return " ".join(sentences)

    def generate_json_data(self, schema: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate structured JSON data based on schema."""
        if schema is None:
            schema = {
                "id": {"type": "int", "range": [1, 1000]},
                "name": {"type": "string", "prefix": "Item"},
                "value": {"type": "float", "range": [0.0, 100.0]},
                "active": {"type": "boolean"}
            }

        try:
            result = {}
            for field, config in schema.items():
                result[field] = self._generate_field_value(config)

            return result

        except Exception as e:
            raise RuntimeError(f"Error generating JSON data: {e}")

    def _generate_field_value(self, config: Dict[str, Any]) -> Any:
        """Generate field value based on configuration."""
        import random

        field_type = config.get("type", "string")

        if field_type == "int":
            range_vals = config.get("range", [1, 100])
            return random.randint(range_vals[0], range_vals[1])
        elif field_type == "float":
            range_vals = config.get("range", [0.0, 100.0])
            return round(random.uniform(range_vals[0], range_vals[1]), 2)
        elif field_type == "string":
            prefix = config.get("prefix", "Item")
            return f"{prefix}_{random.randint(1, 9999):04d}"
        elif field_type == "boolean":
            return random.choice([True, False])
        else:
            return None


class ToolTestFramework:
    """
    Comprehensive testing framework for MCP tools.
    
    Features:
    - Automated test discovery and execution
    - Mock data generation
    - Performance testing
    - Security testing
    - Edge case testing
    - Test reporting and analytics
    - Continuous integration support
    """
    
    def __init__(self):
        """Initialize the test framework."""
        self.test_suites: Dict[str, TestSuite] = {}
        self.test_executions: List[TestExecution] = []
        self.test_data_generator = TestDataGenerator()
        
        # Test environment setup
        self.test_temp_dir = tempfile.mkdtemp(prefix="mcp_tool_tests_")
        
        logger.info(f"Test framework initialized with temp dir: {self.test_temp_dir}")
    
    def register_test_suite(self, test_suite: TestSuite):
        """Register a test suite."""
        self.test_suites[test_suite.suite_id] = test_suite
        logger.info(f"Registered test suite: {test_suite.name}")
    
    def create_standard_test_suite(self, tool: BaseMCPTool) -> TestSuite:
        """Create standard test suite for a tool."""
        suite_id = f"{tool.name}_standard_tests"
        suite = TestSuite(
            suite_id=suite_id,
            name=f"Standard Tests for {tool.name}",
            description=f"Comprehensive test suite for {tool.name} tool"
        )
        
        # Add basic functionality tests
        suite.test_cases.extend(self._create_basic_tests(tool))
        
        # Add edge case tests
        suite.test_cases.extend(self._create_edge_case_tests(tool))
        
        # Add error handling tests
        suite.test_cases.extend(self._create_error_tests(tool))
        
        # Add performance tests
        suite.test_cases.extend(self._create_performance_tests(tool))
        
        # Add security tests
        suite.test_cases.extend(self._create_security_tests(tool))
        
        return suite
    
    def _create_basic_tests(self, tool: BaseMCPTool) -> List[TestCase]:
        """Create basic functionality tests."""
        tests = []
        
        # Valid input test
        if tool.name in ["text_processing", "language_detection"]:
            tests.append(TestCase(
                test_id=f"{tool.name}_basic_valid",
                name="Basic Valid Input",
                description="Test with valid input data",
                test_type=TestType.UNIT,
                tool_name=tool.name,
                input_data={
                    "text": "This is a test sentence for processing.",
                    "operation": "summarize" if tool.name == "text_processing" else None
                },
                tags=["basic", "valid_input"]
            ))
        
        elif tool.name in ["data_access", "statistical_analysis"]:
            # Create test CSV file
            csv_data = self.mock_generator.generate_csv_data(50)
            test_file = os.path.join(self.test_temp_dir, f"test_data_{tool.name}.csv")
            with open(test_file, 'w') as f:
                f.write(csv_data)
            
            tests.append(TestCase(
                test_id=f"{tool.name}_basic_valid",
                name="Basic Valid File Input",
                description="Test with valid CSV file",
                test_type=TestType.UNIT,
                tool_name=tool.name,
                input_data={
                    "file_path": test_file,
                    "query": "Analyze the data distribution" if tool.name == "statistical_analysis" else None
                },
                tags=["basic", "valid_input", "file_based"]
            ))
        
        elif tool.name == "intent_detection":
            tests.append(TestCase(
                test_id=f"{tool.name}_basic_valid",
                name="Basic Intent Detection",
                description="Test intent detection with clear message",
                test_type=TestType.UNIT,
                tool_name=tool.name,
                input_data={
                    "message": "I want to analyze my sales data",
                    "enable_llm_fallback": True
                },
                tags=["basic", "valid_input"]
            ))
        
        return tests
    
    def _create_edge_case_tests(self, tool: BaseMCPTool) -> List[TestCase]:
        """Create edge case tests."""
        tests = []
        
        # Empty input test
        tests.append(TestCase(
            test_id=f"{tool.name}_edge_empty",
            name="Empty Input",
            description="Test with empty input",
            test_type=TestType.UNIT,
            tool_name=tool.name,
            input_data={},
            expected_error="validation",
            tags=["edge_case", "empty_input"]
        ))
        
        # Large input test
        if tool.name in ["text_processing", "language_detection"]:
            large_text = self.mock_generator.generate_text_data(10000)
            tests.append(TestCase(
                test_id=f"{tool.name}_edge_large",
                name="Large Input",
                description="Test with very large input",
                test_type=TestType.PERFORMANCE,
                tool_name=tool.name,
                input_data={"text": large_text},
                timeout_seconds=60,
                tags=["edge_case", "large_input", "performance"]
            ))
        
        # Invalid file path test
        if tool.name in ["data_access", "statistical_analysis"]:
            tests.append(TestCase(
                test_id=f"{tool.name}_edge_invalid_file",
                name="Invalid File Path",
                description="Test with non-existent file",
                test_type=TestType.UNIT,
                tool_name=tool.name,
                input_data={"file_path": "/nonexistent/file.csv"},
                expected_error="file_not_found",
                tags=["edge_case", "invalid_file"]
            ))
        
        return tests
    
    def _create_error_tests(self, tool: BaseMCPTool) -> List[TestCase]:
        """Create error handling tests."""
        tests = []
        
        # Invalid data type test
        tests.append(TestCase(
            test_id=f"{tool.name}_error_invalid_type",
            name="Invalid Data Type",
            description="Test with invalid data types",
            test_type=TestType.UNIT,
            tool_name=tool.name,
            input_data={"invalid_field": 12345},
            expected_error="validation",
            tags=["error_handling", "invalid_type"]
        ))
        
        # Missing required field test
        tests.append(TestCase(
            test_id=f"{tool.name}_error_missing_field",
            name="Missing Required Field",
            description="Test with missing required fields",
            test_type=TestType.UNIT,
            tool_name=tool.name,
            input_data={"incomplete": "data"},
            expected_error="validation",
            tags=["error_handling", "missing_field"]
        ))
        
        return tests
    
    def _create_performance_tests(self, tool: BaseMCPTool) -> List[TestCase]:
        """Create performance tests."""
        tests = []
        
        # Response time test
        tests.append(TestCase(
            test_id=f"{tool.name}_perf_response_time",
            name="Response Time Test",
            description="Measure tool response time",
            test_type=TestType.PERFORMANCE,
            tool_name=tool.name,
            input_data=self._get_standard_input(tool.name),
            timeout_seconds=10,
            tags=["performance", "response_time"]
        ))
        
        # Concurrent execution test
        tests.append(TestCase(
            test_id=f"{tool.name}_perf_concurrent",
            name="Concurrent Execution Test",
            description="Test concurrent tool execution",
            test_type=TestType.STRESS,
            tool_name=tool.name,
            input_data=self._get_standard_input(tool.name),
            tags=["performance", "concurrent", "stress"]
        ))
        
        return tests
    
    def _create_security_tests(self, tool: BaseMCPTool) -> List[TestCase]:
        """Create security tests."""
        tests = []
        
        # SQL injection test
        if tool.name in ["text_processing", "intent_detection"]:
            tests.append(TestCase(
                test_id=f"{tool.name}_security_sql_injection",
                name="SQL Injection Test",
                description="Test resistance to SQL injection",
                test_type=TestType.SECURITY,
                tool_name=tool.name,
                input_data={
                    "text": "'; DROP TABLE users; --",
                    "message": "'; DROP TABLE users; --"
                },
                expected_error="security_violation",
                tags=["security", "sql_injection"]
            ))
        
        # XSS test
        tests.append(TestCase(
            test_id=f"{tool.name}_security_xss",
            name="XSS Test",
            description="Test resistance to XSS attacks",
            test_type=TestType.SECURITY,
            tool_name=tool.name,
            input_data={
                "text": "<script>alert('xss')</script>",
                "message": "<script>alert('xss')</script>"
            },
            expected_error="security_violation",
            tags=["security", "xss"]
        ))
        
        # Path traversal test
        if tool.name in ["data_access"]:
            tests.append(TestCase(
                test_id=f"{tool.name}_security_path_traversal",
                name="Path Traversal Test",
                description="Test resistance to path traversal",
                test_type=TestType.SECURITY,
                tool_name=tool.name,
                input_data={"file_path": "../../etc/passwd"},
                expected_error="security_violation",
                tags=["security", "path_traversal"]
            ))
        
        return tests
    
    def _get_standard_input(self, tool_name: str) -> Dict[str, Any]:
        """Get standard input for a tool."""
        standard_inputs = {
            "text_processing": {"text": "Sample text for processing", "operation": "summarize"},
            "language_detection": {"text": "This is English text"},
            "intent_detection": {"message": "I want to analyze data"},
            "data_access": {"file_path": os.path.join(self.test_temp_dir, "sample.csv")},
            "statistical_analysis": {"file_path": os.path.join(self.test_temp_dir, "sample.csv"), "query": "Basic statistics"}
        }
        
        return standard_inputs.get(tool_name, {"test": "data"})
    
    async def run_test_case(self, test_case: TestCase, tool: BaseMCPTool) -> TestExecution:
        """Run a single test case."""
        start_time = time.time()
        
        try:
            # Setup
            if test_case.setup_function:
                await test_case.setup_function()
            
            # Apply mocks
            with patch.dict('sys.modules', test_case.mock_dependencies):
                # Execute tool
                if asyncio.iscoroutinefunction(tool.execute):
                    output = await asyncio.wait_for(
                        tool.execute(test_case.input_data),
                        timeout=test_case.timeout_seconds
                    )
                else:
                    output = await asyncio.wait_for(
                        asyncio.to_thread(tool.execute, test_case.input_data),
                        timeout=test_case.timeout_seconds
                    )
            
            execution_time = time.time() - start_time
            
            # Validate output
            if test_case.expected_error:
                if not output.get("isError"):
                    result = TestResult.FAILED
                    error_message = f"Expected error '{test_case.expected_error}' but got success"
                else:
                    result = TestResult.PASSED
                    error_message = None
            elif test_case.expected_output:
                if self._validate_output(output, test_case.expected_output):
                    result = TestResult.PASSED
                    error_message = None
                else:
                    result = TestResult.FAILED
                    error_message = "Output does not match expected result"
            else:
                # Just check for no errors
                if output.get("isError"):
                    result = TestResult.FAILED
                    error_message = f"Unexpected error: {output.get('content', [{}])[0].get('text', 'Unknown error')}"
                else:
                    result = TestResult.PASSED
                    error_message = None
            
            # Teardown
            if test_case.teardown_function:
                await test_case.teardown_function()
            
            return TestExecution(
                test_case=test_case,
                result=result,
                execution_time=execution_time,
                output=output,
                error_message=error_message,
                performance_metrics={
                    "execution_time": execution_time,
                    "memory_usage": 0,  # Would implement actual memory tracking
                    "cpu_usage": 0      # Would implement actual CPU tracking
                }
            )
            
        except asyncio.TimeoutError:
            return TestExecution(
                test_case=test_case,
                result=TestResult.FAILED,
                execution_time=time.time() - start_time,
                error_message=f"Test timed out after {test_case.timeout_seconds} seconds"
            )
        except Exception as e:
            return TestExecution(
                test_case=test_case,
                result=TestResult.ERROR,
                execution_time=time.time() - start_time,
                error_message=f"Test execution error: {str(e)}"
            )
    
    def _validate_output(self, actual: Dict[str, Any], expected: Dict[str, Any]) -> bool:
        """Validate actual output against expected output."""
        # Simple validation - could be made more sophisticated
        if expected.get("isError") != actual.get("isError"):
            return False
        
        if "content" in expected and "content" in actual:
            expected_content = expected["content"]
            actual_content = actual["content"]
            
            if len(expected_content) != len(actual_content):
                return False
            
            for exp_item, act_item in zip(expected_content, actual_content):
                if exp_item.get("type") != act_item.get("type"):
                    return False
        
        return True
    
    async def run_test_suite(self, suite_id: str, tool: BaseMCPTool) -> List[TestExecution]:
        """Run all tests in a test suite."""
        if suite_id not in self.test_suites:
            raise ValueError(f"Test suite {suite_id} not found")
        
        suite = self.test_suites[suite_id]
        executions = []
        
        logger.info(f"Running test suite: {suite.name}")
        
        # Suite setup
        if suite.setup_function:
            await suite.setup_function()
        
        try:
            # Run all test cases
            for test_case in suite.test_cases:
                logger.info(f"Running test: {test_case.name}")
                execution = await self.run_test_case(test_case, tool)
                executions.append(execution)
                self.test_executions.append(execution)
                
                # Log result
                status_emoji = "✅" if execution.result == TestResult.PASSED else "❌"
                logger.info(f"{status_emoji} {test_case.name}: {execution.result.value} ({execution.execution_time:.3f}s)")
        
        finally:
            # Suite teardown
            if suite.teardown_function:
                await suite.teardown_function()
        
        return executions
    
    async def run_all_tests(self, tools: List[BaseMCPTool]) -> Dict[str, List[TestExecution]]:
        """Run all tests for all tools."""
        all_results = {}
        
        for tool in tools:
            logger.info(f"Testing tool: {tool.name}")
            
            # Create and register standard test suite
            suite = self.create_standard_test_suite(tool)
            self.register_test_suite(suite)
            
            # Run tests
            results = await self.run_test_suite(suite.suite_id, tool)
            all_results[tool.name] = results
        
        return all_results
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        if not self.test_executions:
            return {"message": "No tests have been executed"}
        
        # Calculate statistics
        total_tests = len(self.test_executions)
        passed_tests = sum(1 for e in self.test_executions if e.result == TestResult.PASSED)
        failed_tests = sum(1 for e in self.test_executions if e.result == TestResult.FAILED)
        error_tests = sum(1 for e in self.test_executions if e.result == TestResult.ERROR)
        
        # Group by tool
        tool_results = {}
        for execution in self.test_executions:
            tool_name = execution.test_case.tool_name
            if tool_name not in tool_results:
                tool_results[tool_name] = []
            tool_results[tool_name].append(execution)
        
        # Group by test type
        type_results = {}
        for execution in self.test_executions:
            test_type = execution.test_case.test_type.value
            if test_type not in type_results:
                type_results[test_type] = {"passed": 0, "failed": 0, "error": 0}
            type_results[test_type][execution.result.value] += 1
        
        # Performance metrics
        execution_times = [e.execution_time for e in self.test_executions]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        
        return {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "errors": error_tests,
                "success_rate": (passed_tests / total_tests) * 100 if total_tests > 0 else 0,
                "avg_execution_time": avg_execution_time
            },
            "by_tool": {
                tool: {
                    "total": len(results),
                    "passed": sum(1 for r in results if r.result == TestResult.PASSED),
                    "failed": sum(1 for r in results if r.result == TestResult.FAILED),
                    "avg_time": sum(r.execution_time for r in results) / len(results) if results else 0
                }
                for tool, results in tool_results.items()
            },
            "by_type": type_results,
            "failed_tests": [
                {
                    "test_id": e.test_case.test_id,
                    "tool": e.test_case.tool_name,
                    "error": e.error_message,
                    "execution_time": e.execution_time
                }
                for e in self.test_executions
                if e.result in [TestResult.FAILED, TestResult.ERROR]
            ]
        }
    
    def cleanup(self):
        """Clean up test resources."""
        import shutil
        try:
            shutil.rmtree(self.test_temp_dir)
            logger.info("Test cleanup completed")
        except Exception as e:
            logger.warning(f"Test cleanup failed: {e}")


# Global test framework instance
_global_test_framework: Optional[ToolTestFramework] = None


def get_test_framework() -> ToolTestFramework:
    """Get global test framework instance."""
    global _global_test_framework
    if _global_test_framework is None:
        _global_test_framework = ToolTestFramework()
    return _global_test_framework
