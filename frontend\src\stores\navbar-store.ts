import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface NavbarState {
  isExpanded: boolean;
  isLocked: boolean;
  isHovered: boolean;
  setExpanded: (expanded: boolean) => void;
  setLocked: (locked: boolean) => void;
  setHovered: (hovered: boolean) => void;
  toggleExpanded: () => void;
  toggleLocked: () => void;
  shouldShowExpanded: () => boolean;
  getNavbarWidth: () => number;
}

export const useNavbarStore = create<NavbarState>()(
  persist(
    (set, get) => ({
      isExpanded: true,
      isLocked: false,
      isHovered: false,
      
      setExpanded: (expanded: boolean) => set({ isExpanded: expanded }),
      setLocked: (locked: boolean) => set({ isLocked: locked }),
      setHovered: (hovered: boolean) => set({ isHovered: hovered }),
      
      toggleExpanded: () => set((state) => ({ isExpanded: !state.isExpanded })),
      toggleLocked: () => set((state) => ({ isLocked: !state.isLocked })),
      
      shouldShowExpanded: () => {
        const state = get();
        return state.isExpanded || (state.isHovered && !state.isLocked);
      },
      
      getNavbarWidth: () => {
        const state = get();
        const shouldShow = state.isExpanded || (state.isHovered && !state.isLocked);
        return shouldShow ? 240 : 64;
      }
    }),
    {
      name: 'navbar-state',
      partialize: (state) => ({
        isExpanded: state.isExpanded,
        isLocked: state.isLocked,
      }),
    }
  )
);
