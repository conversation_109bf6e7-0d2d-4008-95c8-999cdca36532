/**
 * Export Dashboard Dialog
 * 
 * Dialog for exporting dashboards in various formats.
 * Features:
 * - Multiple export formats (PDF, PNG, Excel, JSON)
 * - Export options and settings
 * - Preview before export
 * - Batch export capabilities
 * - Custom styling options
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Download,
  FileText,
  Image,
  FileSpreadsheet,
  Code,
  Settings,
  Eye,
  Loader2,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ExportDashboardDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dashboardId: string;
  dashboardName: string;
}

interface ExportFormat {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  extension: string;
  options: string[];
}

const EXPORT_FORMATS: ExportFormat[] = [
  {
    id: 'pdf',
    name: 'PDF Document',
    description: 'High-quality PDF with all widgets and data',
    icon: FileText,
    extension: 'pdf',
    options: ['orientation', 'pageSize', 'includeData', 'includeCharts'],
  },
  {
    id: 'png',
    name: 'PNG Image',
    description: 'High-resolution image of the dashboard',
    icon: Image,
    extension: 'png',
    options: ['resolution', 'includeCharts', 'backgroundColor'],
  },
  {
    id: 'excel',
    name: 'Excel Workbook',
    description: 'Data export with multiple sheets',
    icon: FileSpreadsheet,
    extension: 'xlsx',
    options: ['includeCharts', 'separateSheets', 'includeMetadata'],
  },
  {
    id: 'json',
    name: 'JSON Data',
    description: 'Raw data and configuration export',
    icon: Code,
    extension: 'json',
    options: ['includeConfig', 'includeData', 'prettyFormat'],
  },
];

const EXPORT_OPTIONS = {
  orientation: { label: 'Orientation', values: ['portrait', 'landscape'] },
  pageSize: { label: 'Page Size', values: ['A4', 'A3', 'Letter', 'Legal'] },
  resolution: { label: 'Resolution', values: ['1920x1080', '2560x1440', '3840x2160'] },
  backgroundColor: { label: 'Background', values: ['white', 'transparent', 'theme'] },
};

export const ExportDashboardDialog: React.FC<ExportDashboardDialogProps> = ({
  open,
  onOpenChange,
  dashboardId,
  dashboardName,
}) => {
  const { toast } = useToast();
  
  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [exportOptions, setExportOptions] = useState<Record<string, any>>({
    includeData: true,
    includeCharts: true,
    includeMetadata: true,
    orientation: 'landscape',
    pageSize: 'A4',
    resolution: '1920x1080',
    backgroundColor: 'white',
    separateSheets: true,
    includeConfig: false,
    prettyFormat: true,
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportStatus, setExportStatus] = useState<'idle' | 'preparing' | 'exporting' | 'complete' | 'error'>('idle');

  const selectedFormatData = EXPORT_FORMATS.find(f => f.id === selectedFormat);

  const handleExport = async () => {
    if (!selectedFormatData) return;

    setIsExporting(true);
    setExportStatus('preparing');
    setExportProgress(0);

    try {
      // Simulate export progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      setExportStatus('exporting');

      const response = await fetch(`/api/dashboards/${dashboardId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          format: selectedFormat,
          options: exportOptions,
        }),
      });

      clearInterval(progressInterval);
      setExportProgress(100);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${dashboardName}.${selectedFormatData.extension}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        setExportStatus('complete');
        
        toast({
          title: "Export Complete",
          description: `Dashboard exported as ${selectedFormatData.name}.`,
        });

        setTimeout(() => {
          onOpenChange(false);
          setExportStatus('idle');
          setExportProgress(0);
        }, 2000);
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      setExportStatus('error');
      toast({
        title: "Export Failed",
        description: "Failed to export dashboard. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const updateOption = (key: string, value: any) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  const getStatusIcon = () => {
    switch (exportStatus) {
      case 'complete':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'preparing':
      case 'exporting':
        return <Loader2 className="h-5 w-5 animate-spin text-primary" />;
      default:
        return <Download className="h-5 w-5" />;
    }
  };

  const getStatusText = () => {
    switch (exportStatus) {
      case 'preparing':
        return 'Preparing export...';
      case 'exporting':
        return 'Exporting dashboard...';
      case 'complete':
        return 'Export complete!';
      case 'error':
        return 'Export failed';
      default:
        return 'Export Dashboard';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Download className="h-5 w-5" />
            <span>Export Dashboard</span>
          </DialogTitle>
          <DialogDescription>
            Export "{dashboardName}" in your preferred format.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="format" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="format">Format</TabsTrigger>
            <TabsTrigger value="options">Options</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="format" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {EXPORT_FORMATS.map((format) => (
                <Card
                  key={format.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    selectedFormat === format.id && "ring-2 ring-primary"
                  )}
                  onClick={() => setSelectedFormat(format.id)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center space-x-3">
                      <format.icon className="h-8 w-8 text-primary" />
                      <div>
                        <CardTitle className="text-sm">{format.name}</CardTitle>
                        <Badge variant="outline" className="text-xs">
                          .{format.extension}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      {format.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="options" className="space-y-4">
            {selectedFormatData && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Export Options</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Boolean Options */}
                    {selectedFormatData.options.includes('includeData') && (
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Include Data</p>
                          <p className="text-sm text-muted-foreground">
                            Export underlying data along with visualizations
                          </p>
                        </div>
                        <Switch
                          checked={exportOptions.includeData}
                          onCheckedChange={(checked) => updateOption('includeData', checked)}
                        />
                      </div>
                    )}

                    {selectedFormatData.options.includes('includeCharts') && (
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Include Charts</p>
                          <p className="text-sm text-muted-foreground">
                            Export chart visualizations and graphics
                          </p>
                        </div>
                        <Switch
                          checked={exportOptions.includeCharts}
                          onCheckedChange={(checked) => updateOption('includeCharts', checked)}
                        />
                      </div>
                    )}

                    {selectedFormatData.options.includes('includeMetadata') && (
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Include Metadata</p>
                          <p className="text-sm text-muted-foreground">
                            Export dashboard configuration and metadata
                          </p>
                        </div>
                        <Switch
                          checked={exportOptions.includeMetadata}
                          onCheckedChange={(checked) => updateOption('includeMetadata', checked)}
                        />
                      </div>
                    )}

                    {selectedFormatData.options.includes('separateSheets') && (
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Separate Sheets</p>
                          <p className="text-sm text-muted-foreground">
                            Create separate sheets for each section
                          </p>
                        </div>
                        <Switch
                          checked={exportOptions.separateSheets}
                          onCheckedChange={(checked) => updateOption('separateSheets', checked)}
                        />
                      </div>
                    )}

                    {selectedFormatData.options.includes('prettyFormat') && (
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Pretty Format</p>
                          <p className="text-sm text-muted-foreground">
                            Format JSON with proper indentation
                          </p>
                        </div>
                        <Switch
                          checked={exportOptions.prettyFormat}
                          onCheckedChange={(checked) => updateOption('prettyFormat', checked)}
                        />
                      </div>
                    )}

                    {selectedFormatData.options.includes('includeConfig') && (
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Include Configuration</p>
                          <p className="text-sm text-muted-foreground">
                            Export dashboard layout and widget configurations
                          </p>
                        </div>
                        <Switch
                          checked={exportOptions.includeConfig}
                          onCheckedChange={(checked) => updateOption('includeConfig', checked)}
                        />
                      </div>
                    )}

                    {/* Select Options */}
                    {Object.entries(EXPORT_OPTIONS).map(([key, option]) => {
                      if (!selectedFormatData.options.includes(key)) return null;
                      
                      return (
                        <div key={key} className="space-y-2">
                          <Label>{option.label}</Label>
                          <Select
                            value={exportOptions[key]}
                            onValueChange={(value) => updateOption(key, value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {option.values.map((value) => (
                                <SelectItem key={value} value={value}>
                                  {value.charAt(0).toUpperCase() + value.slice(1)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      );
                    })}
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Export Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Format</Label>
                    <p className="text-sm text-muted-foreground">
                      {selectedFormatData?.name} (.{selectedFormatData?.extension})
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">File Name</Label>
                    <p className="text-sm text-muted-foreground">
                      {dashboardName}.{selectedFormatData?.extension}
                    </p>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Options</Label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {Object.entries(exportOptions)
                      .filter(([key, value]) => value === true)
                      .map(([key]) => (
                        <Badge key={key} variant="secondary" className="text-xs">
                          {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                        </Badge>
                      ))}
                  </div>
                </div>

                {/* Export Progress */}
                {isExporting && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Export Progress</span>
                      <span className="text-sm text-muted-foreground">{exportProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${exportProgress}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Export Button */}
                <Button
                  onClick={handleExport}
                  disabled={isExporting}
                  className="w-full flex items-center space-x-2"
                  size="lg"
                >
                  {getStatusIcon()}
                  <span>{getStatusText()}</span>
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
