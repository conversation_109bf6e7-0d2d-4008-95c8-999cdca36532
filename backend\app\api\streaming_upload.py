"""
Streaming File Upload Implementation for Phase 1 Security Enhancement.

This module implements streaming file upload with comprehensive security validation
and resource cleanup as specified in the refactor.md Phase 1 requirements.
"""

import os
import logging
import tempfile
import magic
import hashlib
import aiofiles
from typing import Dict, Any, List, Optional, BinaryIO
from pathlib import Path
from contextlib import asynccontextmanager
from fastapi import UploadFile, HTTPException
from datetime import datetime

from ..performance.memory_manager import MemoryManager

logger = logging.getLogger(__name__)


class StreamingFileValidator:
    """
    Comprehensive file validation with streaming support and security checks.
    
    Implements Phase 1 file upload security enhancements with magic number checking,
    MIME type validation, and malicious file detection.
    """
    
    def __init__(self):
        """Initialize the streaming file validator."""
        # Allowed file types with their magic numbers
        self.allowed_types = {
            # Documents
            'application/pdf': [b'%PDF'],
            'application/msword': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
                b'PK\x03\x04', b'PK\x05\x06', b'PK\x07\x08'
            ],
            'text/plain': [b''],  # Text files can start with anything
            'text/csv': [b''],
            
            # Spreadsheets
            'application/vnd.ms-excel': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
                b'PK\x03\x04', b'PK\x05\x06', b'PK\x07\x08'
            ],
            
            # Images (for future use)
            'image/jpeg': [b'\xff\xd8\xff'],
            'image/png': [b'\x89PNG\r\n\x1a\n'],
            'image/gif': [b'GIF87a', b'GIF89a'],
        }
        
        # Dangerous file extensions
        self.dangerous_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
            '.jar', '.app', '.deb', '.pkg', '.dmg', '.msi', '.run', '.sh',
            '.ps1', '.psm1', '.psd1', '.ps1xml', '.psc1', '.psc2'
        }
        
        # Malicious patterns to check in file content
        self.malicious_patterns = [
            b'<script',
            b'javascript:',
            b'vbscript:',
            b'onload=',
            b'onerror=',
            b'eval(',
            b'document.write',
            b'window.location',
            b'<?php',
            b'<%',
            b'#!/bin/sh',
            b'#!/bin/bash',
            b'powershell',
            b'cmd.exe',
        ]
        
        # Maximum file sizes by type (in bytes)
        self.max_file_sizes = {
            'application/pdf': 50 * 1024 * 1024,  # 50MB
            'application/msword': 25 * 1024 * 1024,  # 25MB
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 25 * 1024 * 1024,
            'text/plain': 10 * 1024 * 1024,  # 10MB
            'text/csv': 100 * 1024 * 1024,  # 100MB for data files
            'application/vnd.ms-excel': 50 * 1024 * 1024,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 50 * 1024 * 1024,
        }
        
        logger.info("Streaming file validator initialized")
    
    async def validate_file_stream(self, upload_file: UploadFile, temp_path: str) -> Dict[str, Any]:
        """
        Validate uploaded file with streaming approach.
        
        Args:
            upload_file: FastAPI UploadFile object
            temp_path: Path to temporary file
            
        Returns:
            Validation result dictionary
        """
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "file_info": {
                "filename": upload_file.filename,
                "content_type": upload_file.content_type,
                "size": 0,
                "hash": None,
                "detected_type": None
            }
        }
        
        try:
            # Check file extension
            if upload_file.filename:
                file_ext = Path(upload_file.filename).suffix.lower()
                if file_ext in self.dangerous_extensions:
                    validation_result["is_valid"] = False
                    validation_result["errors"].append(f"Dangerous file extension: {file_ext}")
                    return validation_result
            
            # Stream file to temporary location while validating
            file_size = 0
            hash_sha256 = hashlib.sha256()
            first_chunk = None
            malicious_content_found = False
            
            async with aiofiles.open(temp_path, 'wb') as temp_file:
                while True:
                    chunk = await upload_file.read(8192)  # 8KB chunks
                    if not chunk:
                        break
                    
                    # Store first chunk for magic number validation
                    if first_chunk is None:
                        first_chunk = chunk
                    
                    # Check for malicious patterns in chunk
                    if not malicious_content_found:
                        chunk_lower = chunk.lower()
                        for pattern in self.malicious_patterns:
                            if pattern in chunk_lower:
                                malicious_content_found = True
                                validation_result["warnings"].append(f"Potentially malicious pattern detected: {pattern.decode('utf-8', errors='ignore')}")
                                break
                    
                    # Update hash and size
                    hash_sha256.update(chunk)
                    file_size += len(chunk)
                    
                    # Check file size limits
                    max_size = self.max_file_sizes.get(upload_file.content_type, 10 * 1024 * 1024)
                    if file_size > max_size:
                        validation_result["is_valid"] = False
                        validation_result["errors"].append(f"File too large: {file_size} bytes > {max_size} bytes")
                        return validation_result
                    
                    await temp_file.write(chunk)
            
            # Validate magic numbers
            if first_chunk and upload_file.content_type in self.allowed_types:
                magic_numbers = self.allowed_types[upload_file.content_type]
                if magic_numbers and magic_numbers != [b'']:  # Skip empty magic numbers for text files
                    magic_valid = any(first_chunk.startswith(magic) for magic in magic_numbers)
                    if not magic_valid:
                        validation_result["warnings"].append("File content doesn't match declared MIME type")
            
            # Detect actual file type using python-magic
            try:
                detected_type = magic.from_file(temp_path, mime=True)
                validation_result["file_info"]["detected_type"] = detected_type
                
                # Check if detected type matches declared type
                if detected_type != upload_file.content_type:
                    validation_result["warnings"].append(
                        f"MIME type mismatch: declared={upload_file.content_type}, detected={detected_type}"
                    )
            except Exception as e:
                logger.warning(f"Failed to detect file type: {e}")
            
            # Update file info
            validation_result["file_info"]["size"] = file_size
            validation_result["file_info"]["hash"] = hash_sha256.hexdigest()
            
            # Final validation checks
            if file_size == 0:
                validation_result["is_valid"] = False
                validation_result["errors"].append("Empty file")
            
            if upload_file.content_type not in self.allowed_types:
                validation_result["is_valid"] = False
                validation_result["errors"].append(f"Unsupported file type: {upload_file.content_type}")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating file stream: {e}")
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"Validation error: {str(e)}")
            return validation_result


class StreamingFileUploadManager:
    """
    Streaming file upload manager with resource cleanup and memory management.
    
    Implements Phase 1 file upload enhancements with streaming approach,
    comprehensive validation, and automatic resource cleanup.
    """
    
    def __init__(self, memory_manager: MemoryManager):
        """
        Initialize streaming file upload manager.
        
        Args:
            memory_manager: Memory manager instance for monitoring
        """
        self.memory_manager = memory_manager
        self.validator = StreamingFileValidator()
        self.temp_dir = tempfile.gettempdir()
        self.active_uploads: Dict[str, Dict[str, Any]] = {}
        
        logger.info("Streaming file upload manager initialized")
    
    @asynccontextmanager
    async def managed_upload(self, upload_file: UploadFile, user_id: str):
        """
        Context manager for managed file upload with automatic cleanup.
        
        Args:
            upload_file: FastAPI UploadFile object
            user_id: User identifier
            
        Yields:
            Tuple of (temp_file_path, validation_result)
        """
        upload_id = f"{user_id}_{datetime.now().timestamp()}"
        temp_file_path = None
        
        try:
            # Create temporary file
            temp_file_fd, temp_file_path = tempfile.mkstemp(
                suffix=f"_{upload_file.filename}",
                dir=self.temp_dir
            )
            os.close(temp_file_fd)  # Close file descriptor, we'll use aiofiles
            
            # Track active upload
            self.active_uploads[upload_id] = {
                "temp_path": temp_file_path,
                "filename": upload_file.filename,
                "user_id": user_id,
                "start_time": datetime.now()
            }
            
            # Monitor memory during upload
            async with self.memory_manager.monitor_memory(f"file_upload_{upload_file.filename}"):
                # Validate file stream
                validation_result = await self.validator.validate_file_stream(upload_file, temp_file_path)
                
                yield temp_file_path, validation_result
                
        except Exception as e:
            logger.error(f"Error in managed upload: {e}")
            raise
        finally:
            # Cleanup
            await self._cleanup_upload(upload_id, temp_file_path)
    
    async def _cleanup_upload(self, upload_id: str, temp_file_path: Optional[str]):
        """
        Clean up upload resources.
        
        Args:
            upload_id: Upload identifier
            temp_file_path: Path to temporary file
        """
        try:
            # Remove from active uploads
            if upload_id in self.active_uploads:
                del self.active_uploads[upload_id]
            
            # Remove temporary file
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                logger.debug(f"Cleaned up temporary file: {temp_file_path}")
                
        except Exception as e:
            logger.error(f"Error cleaning up upload {upload_id}: {e}")
    
    async def process_upload(self, upload_file: UploadFile, user_id: str, 
                           destination_dir: str) -> Dict[str, Any]:
        """
        Process file upload with streaming and validation.
        
        Args:
            upload_file: FastAPI UploadFile object
            user_id: User identifier
            destination_dir: Destination directory for the file
            
        Returns:
            Upload result dictionary
        """
        async with self.managed_upload(upload_file, user_id) as (temp_path, validation_result):
            if not validation_result["is_valid"]:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "message": "File validation failed",
                        "errors": validation_result["errors"],
                        "warnings": validation_result["warnings"]
                    }
                )
            
            # Create destination directory if it doesn't exist
            os.makedirs(destination_dir, exist_ok=True)
            
            # Generate safe filename
            safe_filename = self._generate_safe_filename(upload_file.filename, user_id)
            destination_path = os.path.join(destination_dir, safe_filename)
            
            # Move file from temp to destination
            os.rename(temp_path, destination_path)
            
            return {
                "success": True,
                "file_path": destination_path,
                "file_info": validation_result["file_info"],
                "warnings": validation_result["warnings"]
            }
    
    def _generate_safe_filename(self, original_filename: str, user_id: str) -> str:
        """
        Generate a safe filename for storage.
        
        Args:
            original_filename: Original filename
            user_id: User identifier
            
        Returns:
            Safe filename
        """
        # Sanitize filename
        safe_name = "".join(c for c in original_filename if c.isalnum() or c in '._-')
        
        # Add timestamp and user ID for uniqueness
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        name_parts = safe_name.rsplit('.', 1)
        
        if len(name_parts) == 2:
            name, ext = name_parts
            return f"{user_id}_{timestamp}_{name}.{ext}"
        else:
            return f"{user_id}_{timestamp}_{safe_name}"
    
    def get_upload_statistics(self) -> Dict[str, Any]:
        """Get upload statistics."""
        return {
            "active_uploads": len(self.active_uploads),
            "temp_directory": self.temp_dir,
            "validator_stats": {
                "allowed_types": len(self.validator.allowed_types),
                "dangerous_extensions": len(self.validator.dangerous_extensions),
                "malicious_patterns": len(self.validator.malicious_patterns)
            }
        }
