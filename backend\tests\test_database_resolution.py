#!/usr/bin/env python3
"""
Test script for database-based file resolution.
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from agents.tools.mcp.data_access import DataAccessTool

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_database_resolution():
    """Test database-based file resolution specifically."""
    
    # Initialize the tool
    tool = DataAccessTool()
    await tool._initialize({})
    
    # Test cases for database resolution
    test_cases = [
        {
            "name": "Test database resolution with file ID",
            "data_source": {"id": "eaf2c57f-c6c9-4bf6-ae03-6202cb36fecc"},
            "description": "Should find file via database lookup using file ID"
        },
        {
            "name": "Test database resolution with source_metadata",
            "data_source": {
                "source_metadata": {
                    "file_id": "eaf2c57f-c6c9-4bf6-ae03-6202cb36fecc",
                    "file_name": "mobile_sales_data"
                }
            },
            "description": "Should find file via database lookup using source_metadata"
        },
        {
            "name": "Test database resolution with data_source_id",
            "data_source": {
                "data_source_id": "some-data-source-id",
                "user_id": "test-user-123"
            },
            "description": "Should attempt to find file via data source ID"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n{'='*60}")
        print(f"TEST: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        print(f"Data source: {test_case['data_source']}")
        print(f"{'='*60}")
        
        try:
            # Test database resolution specifically
            file_path = await tool._resolve_file_path_from_database(test_case['data_source'])
            
            if file_path:
                print(f"✓ Database resolution found: {file_path}")
                
                # Test loading
                df = await tool._load_dataframe(file_path)
                if df is not None:
                    print(f"✓ Successfully loaded dataframe:")
                    print(f"  - Shape: {df.shape}")
                    print(f"  - Columns: {list(df.columns)}")
                else:
                    print("✗ Failed to load dataframe")
            else:
                print("✗ Database resolution failed")
                
        except Exception as e:
            print(f"✗ Error during test: {e}")
            logger.error(f"Test error: {e}", exc_info=True)

async def test_complete_flow():
    """Test the complete flow with database-first resolution."""
    
    # Initialize the tool
    tool = DataAccessTool()
    await tool._initialize({})
    
    print(f"\n{'='*60}")
    print("TEST: Complete flow with database-first resolution")
    print(f"{'='*60}")
    
    # Test with a file ID that should exist
    data_source = {"id": "eaf2c57f-c6c9-4bf6-ae03-6202cb36fecc"}
    
    try:
        file_path, df = await tool._find_and_load_data(data_source)
        
        if file_path and df is not None:
            print(f"✓ Complete flow successful:")
            print(f"  - File path: {file_path}")
            print(f"  - Shape: {df.shape}")
            print(f"  - Columns: {list(df.columns)}")
            print(f"  - Data source resolution method: Database-first approach")
        else:
            print("✗ Complete flow failed")
            
    except Exception as e:
        print(f"✗ Error during complete flow test: {e}")
        logger.error(f"Complete flow test error: {e}", exc_info=True)

async def main():
    """Main test function."""
    print("Testing Database-Based File Resolution")
    
    # Test database resolution specifically
    await test_database_resolution()
    
    # Test complete flow
    await test_complete_flow()
    
    print(f"\n{'='*60}")
    print("DATABASE RESOLUTION TEST COMPLETE")
    print(f"{'='*60}")

if __name__ == "__main__":
    asyncio.run(main())
