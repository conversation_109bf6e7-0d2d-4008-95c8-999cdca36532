"""
Business Context Detection Tool for MCP Integration.

This module provides intelligent business context detection from uploaded data
and user interactions to enable personalized marketing recommendations.
"""

import logging
import re
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from .base import BaseMCPTool
from ...marketing_agent.business_context import BusinessContextAnalyzer

logger = logging.getLogger(__name__)


class BusinessContextTool(BaseMCPTool):
    """Tool for detecting business context from data and interactions."""

    name = "detect_business_context"
    description = "Analyze business context from uploaded data and user interactions"

    def __init__(self):
        super().__init__()
        self.ai_analyzer = BusinessContextAnalyzer()

    class InputSchema(BaseModel):
        data_sources: Optional[List[str]] = Field(default=[], description="List of data source IDs to analyze")
        conversation_history: Optional[List[Dict[str, Any]]] = Field(default=[], description="Recent conversation messages")
        user_profile: Optional[Dict[str, Any]] = Field(default={}, description="User profile information")
        analysis_depth: str = Field(default="standard", description="Analysis depth: quick, standard, or comprehensive")
        use_ai_analysis: bool = Field(default=True, description="Whether to use AI-powered analysis")
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute business context detection."""
        try:
            data_sources = kwargs.get("data_sources", [])
            conversation_history = kwargs.get("conversation_history", [])
            user_profile = kwargs.get("user_profile", {})
            analysis_depth = kwargs.get("analysis_depth", "standard")
            use_ai_analysis = kwargs.get("use_ai_analysis", True)

            logger.info(f"Detecting business context with {len(data_sources)} data sources and {len(conversation_history)} conversation messages")

            # Use AI-powered analysis if enabled and comprehensive analysis is requested
            if use_ai_analysis and analysis_depth in ["standard", "comprehensive"]:
                try:
                    logger.info("Using AI-powered business context analysis")

                    # Prepare data for AI analysis
                    user_data = {
                        "data_sources": data_sources,
                        "conversation_history": conversation_history,
                        "user_profile": user_profile
                    }

                    # Get AI analysis
                    ai_context = await self.ai_analyzer.analyze_context(user_data)

                    # Convert AI analysis to expected format
                    business_context = {
                        "industry": ai_context.industry,
                        "business_type": ai_context.business_type,
                        "business_size": ai_context.business_size,
                        "target_market": ai_context.target_market,
                        "key_products": ai_context.key_products,
                        "marketing_challenges": ai_context.marketing_challenges,
                        "competitive_advantages": ai_context.competitive_advantages,
                        "current_marketing_channels": ai_context.current_marketing_channels,
                        "budget_indicators": ai_context.budget_indicators,
                        "geographic_focus": ai_context.geographic_focus,
                        "business_stage": ai_context.business_stage,
                        "confidence_score": ai_context.confidence_score
                    }

                    # Generate AI-enhanced recommendations
                    recommendations = self._generate_ai_enhanced_recommendations(business_context, ai_context)

                    return {
                        "success": True,
                        "business_context": business_context,
                        "recommendations": recommendations,
                        "metadata": {
                            "analysis_depth": analysis_depth,
                            "analysis_method": "ai_powered",
                            "data_sources_analyzed": len(data_sources),
                            "conversation_messages_analyzed": len(conversation_history),
                            "confidence_score": business_context["confidence_score"]
                        }
                    }

                except Exception as e:
                    logger.warning(f"AI analysis failed, falling back to pattern-based analysis: {e}")

            # Fallback to pattern-based analysis
            logger.info("Using pattern-based business context analysis")

            # Initialize context
            business_context = {
                "industry": None,
                "business_type": None,
                "business_size": None,
                "target_market": None,
                "key_products": [],
                "marketing_challenges": [],
                "competitive_advantages": [],
                "current_marketing_channels": [],
                "budget_indicators": None,
                "geographic_focus": None,
                "business_stage": None,
                "confidence_score": 0.0
            }

            # Analyze data sources
            if data_sources:
                data_context = await self._analyze_data_sources(data_sources)
                business_context.update(data_context)

            # Analyze conversation history
            if conversation_history:
                conversation_context = self._analyze_conversation_history(conversation_history)
                business_context = self._merge_context(business_context, conversation_context)

            # Analyze user profile
            if user_profile:
                profile_context = self._analyze_user_profile(user_profile)
                business_context = self._merge_context(business_context, profile_context)

            # Calculate confidence score
            business_context["confidence_score"] = self._calculate_confidence_score(business_context)

            # Generate recommendations based on context
            recommendations = self._generate_recommendations(business_context)

            return {
                "success": True,
                "business_context": business_context,
                "recommendations": recommendations,
                "metadata": {
                    "analysis_depth": analysis_depth,
                    "analysis_method": "pattern_based",
                    "data_sources_analyzed": len(data_sources),
                    "conversation_messages_analyzed": len(conversation_history),
                    "confidence_score": business_context["confidence_score"]
                }
            }
            
        except Exception as e:
            logger.error(f"Error detecting business context: {e}")
            return {
                "success": False,
                "error": str(e),
                "business_context": None,
                "recommendations": []
            }
    
    async def _analyze_data_sources(self, data_sources: List[str]) -> Dict[str, Any]:
        """Analyze uploaded data sources for business context."""
        context = {}

        try:
            # Use the existing DataAccessTool
            from .data_access import DataAccessTool

            data_tool = DataAccessTool()
            await data_tool.initialize({})

            for data_source_id in data_sources:
                try:
                    # Extract text content from the data source
                    text_result = await data_tool.execute({
                        "data_source": data_source_id,
                        "operation": "extract_text",
                        "params": {}
                    })

                    if not text_result.get("isError", False) and text_result.get("metadata"):
                        file_content = text_result["metadata"].get("full_text", "")

                        if file_content:
                            # Analyze file content for business context
                            file_analysis = self._analyze_file_content(file_content, data_source_id)
                            context = self._merge_context(context, file_analysis)

                except Exception as e:
                    logger.error(f"Error analyzing data source {data_source_id}: {e}")
                    continue

            return context

        except Exception as e:
            logger.error(f"Error analyzing data sources: {e}")
            return {}

    def _analyze_file_content(self, content: str, source_id: str) -> Dict[str, Any]:
        """Analyze file content for business context indicators."""
        context = {}
        content_lower = content.lower()

        try:
            logger.info(f"Analyzing content from source {source_id} ({len(content)} characters)")
            # Industry detection patterns
            industry_patterns = {
                "technology": [
                    "software", "saas", "platform", "api", "cloud", "digital", "tech",
                    "application", "system", "database", "algorithm", "artificial intelligence",
                    "machine learning", "data science", "cybersecurity", "blockchain"
                ],
                "healthcare": [
                    "healthcare", "medical", "patient", "clinic", "hospital", "health",
                    "pharmaceutical", "biotech", "medical device", "telemedicine",
                    "electronic health record", "clinical trial", "diagnosis", "treatment"
                ],
                "finance": [
                    "financial", "banking", "investment", "insurance", "fintech", "trading",
                    "portfolio", "asset management", "credit", "loan", "mortgage", "payment",
                    "cryptocurrency", "blockchain", "compliance", "regulatory"
                ],
                "retail": [
                    "retail", "ecommerce", "store", "shopping", "merchandise", "inventory",
                    "supply chain", "customer", "sales", "point of sale", "omnichannel",
                    "fashion", "apparel", "consumer goods", "marketplace"
                ],
                "education": [
                    "education", "learning", "student", "course", "training", "curriculum",
                    "university", "school", "academic", "e-learning", "lms", "edtech",
                    "certification", "degree", "instructor", "pedagogy"
                ],
                "manufacturing": [
                    "manufacturing", "production", "factory", "assembly", "quality control",
                    "supply chain", "logistics", "industrial", "automation", "lean",
                    "six sigma", "equipment", "machinery", "process optimization"
                ],
                "real_estate": [
                    "real estate", "property", "housing", "rental", "mortgage", "commercial",
                    "residential", "development", "construction", "leasing", "property management"
                ]
            }

            # Detect industry with confidence scoring
            industry_scores = {}
            for industry, keywords in industry_patterns.items():
                score = sum(1 for keyword in keywords if keyword in content_lower)
                if score > 0:
                    industry_scores[industry] = score

            if industry_scores:
                # Select industry with highest score
                best_industry = max(industry_scores, key=industry_scores.get)
                context["industry"] = best_industry
                context["industry_confidence"] = industry_scores[best_industry] / len(industry_patterns[best_industry])

            # Business type detection
            b2b_indicators = [
                "enterprise", "business to business", "b2b", "corporate", "professional services",
                "consulting", "solution", "implementation", "integration", "workflow"
            ]
            b2c_indicators = [
                "consumer", "customer", "b2c", "retail", "individual", "personal",
                "household", "family", "lifestyle", "entertainment"
            ]

            b2b_score = sum(1 for indicator in b2b_indicators if indicator in content_lower)
            b2c_score = sum(1 for indicator in b2c_indicators if indicator in content_lower)

            if b2b_score > b2c_score:
                context["business_type"] = "B2B"
            elif b2c_score > b2b_score:
                context["business_type"] = "B2C"

            # Business size indicators
            startup_indicators = [
                "startup", "new business", "founded", "early stage", "seed funding",
                "venture capital", "mvp", "minimum viable product", "bootstrap"
            ]
            small_business_indicators = [
                "small business", "local business", "family business", "independent",
                "boutique", "specialized", "niche market"
            ]
            enterprise_indicators = [
                "enterprise", "corporation", "multinational", "fortune", "global",
                "large scale", "enterprise level", "corporate", "conglomerate"
            ]

            if any(indicator in content_lower for indicator in startup_indicators):
                context["business_size"] = "startup"
            elif any(indicator in content_lower for indicator in enterprise_indicators):
                context["business_size"] = "enterprise"
            elif any(indicator in content_lower for indicator in small_business_indicators):
                context["business_size"] = "small"

            # Extract key products/services
            product_patterns = [
                r"(?:our|the|this)\s+(?:product|service|solution|platform|software|application)\s+(?:is|provides|offers|enables)\s+([^.]{10,100})",
                r"we\s+(?:offer|provide|deliver|sell)\s+([^.]{10,100})",
                r"(?:product|service|solution)\s+(?:features|includes|offers)\s+([^.]{10,100})"
            ]

            products = []
            for pattern in product_patterns:
                matches = re.findall(pattern, content_lower, re.IGNORECASE)
                products.extend([match.strip() for match in matches[:3]])  # Limit to 3 per pattern

            if products:
                context["key_products"] = list(set(products[:5]))  # Limit to 5 unique products

            # Target market detection
            target_patterns = [
                r"(?:target|serve|focus on|cater to)\s+([^.]{10,80})",
                r"(?:customers|clients|users)\s+(?:are|include|consist of)\s+([^.]{10,80})",
                r"(?:market|audience|demographic)\s+(?:is|includes|consists of)\s+([^.]{10,80})"
            ]

            targets = []
            for pattern in target_patterns:
                matches = re.findall(pattern, content_lower, re.IGNORECASE)
                targets.extend([match.strip() for match in matches[:2]])

            if targets:
                context["target_market"] = targets[0]  # Take the first/best match

            # Marketing challenges detection
            challenge_patterns = {
                "lead_generation": ["lead generation", "generate leads", "find customers", "prospect", "sales funnel"],
                "brand_awareness": ["brand awareness", "visibility", "recognition", "market presence", "brand building"],
                "customer_retention": ["retention", "churn", "loyalty", "repeat customers", "customer lifetime value"],
                "conversion": ["conversion", "sales conversion", "close rate", "funnel optimization"],
                "content_creation": ["content marketing", "content creation", "blog", "social media content"],
                "seo": ["seo", "search engine optimization", "organic traffic", "search rankings"],
                "paid_advertising": ["paid ads", "ppc", "advertising", "ad spend", "campaign management"]
            }

            detected_challenges = []
            for challenge, keywords in challenge_patterns.items():
                if any(keyword in content_lower for keyword in keywords):
                    detected_challenges.append(challenge.replace("_", " "))

            if detected_challenges:
                context["marketing_challenges"] = detected_challenges

            # Budget indicators
            budget_patterns = [
                r"budget\s+(?:of|is|around)\s+\$?([0-9,]+)",
                r"spend\s+\$?([0-9,]+)",
                r"investment\s+of\s+\$?([0-9,]+)"
            ]

            for pattern in budget_patterns:
                match = re.search(pattern, content_lower)
                if match:
                    budget_amount = match.group(1).replace(",", "")
                    try:
                        budget_num = int(budget_amount)
                        if budget_num < 10000:
                            context["budget_range"] = "small"
                        elif budget_num < 100000:
                            context["budget_range"] = "medium"
                        else:
                            context["budget_range"] = "large"
                    except ValueError:
                        pass
                    break

            # Geographic focus
            geo_patterns = [
                r"(?:located|based|operating)\s+in\s+([^.]{5,50})",
                r"(?:serve|target|focus on)\s+([^.]{5,50})\s+(?:market|region|area)",
                r"(?:us|usa|united states|north america|europe|asia|global|international|local|regional)"
            ]

            for pattern in geo_patterns:
                match = re.search(pattern, content_lower)
                if match:
                    context["geographic_focus"] = match.group(1).strip() if match.groups() else match.group(0)
                    break

            return context

        except Exception as e:
            logger.error(f"Error analyzing file content: {e}")
            return {}
    
    def _analyze_conversation_history(self, conversation_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze conversation history for business context clues."""
        context = {}

        try:
            if not conversation_history:
                return {}

            # Extract and clean conversation text
            conversation_texts = []
            user_messages = []

            for msg in conversation_history:
                content = msg.get("content", "")
                if content and isinstance(content, str):
                    conversation_texts.append(content)
                    # Separate user messages for more targeted analysis
                    if msg.get("role") == "user" or msg.get("sender") == "user":
                        user_messages.append(content)

            if not conversation_texts:
                return {}

            # Combine all text for analysis
            all_text = " ".join(conversation_texts)
            user_text = " ".join(user_messages) if user_messages else all_text

            # Enhanced industry detection with weighted scoring
            industry_patterns = {
                "technology": {
                    "primary": ["software", "saas", "platform", "api", "cloud", "tech", "digital transformation"],
                    "secondary": ["app", "system", "database", "algorithm", "automation", "ai", "ml"],
                    "context": ["development", "coding", "programming", "deployment", "integration"]
                },
                "healthcare": {
                    "primary": ["healthcare", "medical", "patient", "clinic", "hospital", "health"],
                    "secondary": ["pharmaceutical", "biotech", "telemedicine", "wellness", "treatment"],
                    "context": ["diagnosis", "therapy", "clinical", "medical device", "health record"]
                },
                "finance": {
                    "primary": ["financial", "banking", "investment", "insurance", "fintech"],
                    "secondary": ["trading", "portfolio", "credit", "loan", "payment", "wealth"],
                    "context": ["compliance", "regulatory", "risk management", "asset management"]
                },
                "retail": {
                    "primary": ["retail", "ecommerce", "store", "shopping", "merchandise"],
                    "secondary": ["inventory", "supply chain", "customer", "sales", "marketplace"],
                    "context": ["point of sale", "omnichannel", "fashion", "consumer goods"]
                },
                "education": {
                    "primary": ["education", "learning", "student", "course", "training"],
                    "secondary": ["university", "school", "academic", "curriculum", "certification"],
                    "context": ["e-learning", "lms", "edtech", "instructor", "degree"]
                },
                "manufacturing": {
                    "primary": ["manufacturing", "production", "factory", "assembly"],
                    "secondary": ["quality control", "logistics", "industrial", "automation"],
                    "context": ["lean", "six sigma", "equipment", "machinery", "process"]
                }
            }

            # Calculate industry scores
            industry_scores = {}
            text_lower = all_text.lower()

            for industry, patterns in industry_patterns.items():
                score = 0
                # Primary keywords get higher weight
                score += sum(3 for keyword in patterns["primary"] if keyword in text_lower)
                # Secondary keywords get medium weight
                score += sum(2 for keyword in patterns["secondary"] if keyword in text_lower)
                # Context keywords get lower weight
                score += sum(1 for keyword in patterns["context"] if keyword in text_lower)

                if score > 0:
                    industry_scores[industry] = score

            # Select industry with highest confidence
            if industry_scores:
                best_industry = max(industry_scores, key=industry_scores.get)
                max_possible_score = len(industry_patterns[best_industry]["primary"]) * 3 + \
                                  len(industry_patterns[best_industry]["secondary"]) * 2 + \
                                  len(industry_patterns[best_industry]["context"]) * 1
                confidence = min(1.0, industry_scores[best_industry] / max_possible_score)

                if confidence > 0.1:  # Only include if reasonably confident
                    context["industry"] = best_industry
                    context["industry_confidence"] = confidence

            # Enhanced business type detection
            b2b_patterns = [
                "enterprise", "business to business", "b2b", "corporate clients",
                "professional services", "consulting", "solution", "implementation",
                "workflow", "business process", "enterprise software", "business intelligence"
            ]
            b2c_patterns = [
                "consumer", "customer", "b2c", "retail", "individual", "personal",
                "household", "family", "lifestyle", "entertainment", "consumer goods",
                "end user", "general public"
            ]

            b2b_score = sum(2 if pattern in text_lower else 0 for pattern in b2b_patterns)
            b2c_score = sum(2 if pattern in text_lower else 0 for pattern in b2c_patterns)

            # Add context-based scoring
            if any(word in text_lower for word in ["clients", "businesses", "companies", "organizations"]):
                b2b_score += 1
            if any(word in text_lower for word in ["customers", "users", "consumers", "people"]):
                b2c_score += 1

            if b2b_score > b2c_score and b2b_score > 2:
                context["business_type"] = "B2B"
                context["business_type_confidence"] = min(1.0, b2b_score / 10)
            elif b2c_score > b2b_score and b2c_score > 2:
                context["business_type"] = "B2C"
                context["business_type_confidence"] = min(1.0, b2c_score / 10)

            # Business stage and size detection
            stage_patterns = {
                "startup": ["startup", "new business", "just started", "founding", "early stage",
                           "mvp", "seed funding", "venture capital", "bootstrap", "pre-revenue"],
                "growth": ["growing", "scaling", "expansion", "series a", "series b", "rapid growth",
                          "market traction", "scaling up", "growth stage"],
                "established": ["established", "mature", "stable", "profitable", "market leader",
                               "industry veteran", "well-established", "long-standing"],
                "enterprise": ["enterprise", "corporation", "multinational", "fortune 500",
                              "global company", "large corporation", "conglomerate"]
            }

            for stage, keywords in stage_patterns.items():
                if any(keyword in text_lower for keyword in keywords):
                    if stage == "enterprise":
                        context["business_size"] = "enterprise"
                    elif stage == "startup":
                        context["business_size"] = "startup"
                    else:
                        context["business_size"] = "medium"
                    context["business_stage"] = stage
                    break

            # Extract specific business goals and challenges from user messages
            goal_patterns = {
                "lead_generation": [
                    "generate leads", "find customers", "get more leads", "lead generation",
                    "prospect", "sales funnel", "customer acquisition", "new customers"
                ],
                "brand_awareness": [
                    "brand awareness", "increase visibility", "brand recognition", "market presence",
                    "brand building", "thought leadership", "industry recognition"
                ],
                "customer_retention": [
                    "retain customers", "customer loyalty", "reduce churn", "repeat customers",
                    "customer lifetime value", "retention rate", "customer satisfaction"
                ],
                "sales_conversion": [
                    "increase sales", "improve conversion", "close more deals", "sales optimization",
                    "conversion rate", "sales funnel", "revenue growth"
                ],
                "content_marketing": [
                    "content marketing", "blog posts", "social media", "content creation",
                    "content strategy", "thought leadership content", "educational content"
                ],
                "seo_optimization": [
                    "seo", "search engine optimization", "organic traffic", "search rankings",
                    "google rankings", "search visibility", "keyword optimization"
                ]
            }

            detected_goals = []
            user_text_lower = user_text.lower()

            for goal, patterns in goal_patterns.items():
                if any(pattern in user_text_lower for pattern in patterns):
                    detected_goals.append(goal.replace("_", " "))

            if detected_goals:
                context["marketing_goals"] = detected_goals

            # Extract mentioned tools, platforms, or technologies
            mentioned_tools = []
            tool_patterns = [
                r"(?:using|use|with|on)\s+(salesforce|hubspot|mailchimp|wordpress|shopify|linkedin|facebook|instagram|twitter|google ads|facebook ads)",
                r"(?:platform|tool|software|system)\s+(?:like|such as|including)\s+([a-zA-Z\s]{3,20})"
            ]

            for pattern in tool_patterns:
                matches = re.findall(pattern, user_text_lower, re.IGNORECASE)
                mentioned_tools.extend([match.strip() for match in matches if len(match.strip()) > 2])

            if mentioned_tools:
                context["current_tools"] = list(set(mentioned_tools[:5]))  # Limit and deduplicate

            # Extract budget or resource indicators
            budget_indicators = []
            if any(term in user_text_lower for term in ["limited budget", "tight budget", "small budget", "bootstrap"]):
                budget_indicators.append("limited")
            if any(term in user_text_lower for term in ["substantial budget", "good budget", "well-funded"]):
                budget_indicators.append("substantial")
            if any(term in user_text_lower for term in ["no budget", "free", "organic only"]):
                budget_indicators.append("minimal")

            if budget_indicators:
                context["budget_indicators"] = budget_indicators

            return context

        except Exception as e:
            logger.error(f"Error analyzing conversation history: {e}")
            return {}
    
    def _analyze_user_profile(self, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user profile for business context."""
        context = {}
        
        try:
            # Extract context from user profile fields
            if user_profile.get("company"):
                context["company_name"] = user_profile["company"]
            
            if user_profile.get("industry"):
                context["industry"] = user_profile["industry"]
            
            if user_profile.get("role"):
                role = user_profile["role"].lower()
                if any(term in role for term in ["founder", "ceo", "owner"]):
                    context["business_size"] = "startup"
                elif any(term in role for term in ["marketing", "growth", "digital"]):
                    context["marketing_challenges"] = ["lead generation", "brand awareness"]
            
            return context
            
        except Exception as e:
            logger.error(f"Error analyzing user profile: {e}")
            return {}
    
    def _merge_context(self, base_context: Dict[str, Any], new_context: Dict[str, Any]) -> Dict[str, Any]:
        """Merge two context dictionaries intelligently."""
        merged = base_context.copy()
        
        for key, value in new_context.items():
            if key not in merged or merged[key] is None:
                merged[key] = value
            elif isinstance(value, list) and isinstance(merged[key], list):
                # Merge lists and remove duplicates
                merged[key] = list(set(merged[key] + value))
            elif value and not merged[key]:
                # Override empty/None values
                merged[key] = value
        
        return merged
    
    def _calculate_confidence_score(self, context: Dict[str, Any]) -> float:
        """Calculate confidence score based on available context information."""
        total_fields = len(context)
        filled_fields = sum(1 for value in context.values() if value is not None and value != [])
        
        if total_fields == 0:
            return 0.0
        
        base_score = filled_fields / total_fields
        
        # Boost score for high-value fields
        high_value_fields = ["industry", "business_type", "target_market"]
        high_value_filled = sum(1 for field in high_value_fields if context.get(field))
        
        confidence_boost = (high_value_filled / len(high_value_fields)) * 0.3
        
        return min(1.0, base_score + confidence_boost)
    
    def _generate_recommendations(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate marketing recommendations based on business context."""
        recommendations = []
        
        try:
            industry = context.get("industry")
            business_size = context.get("business_size")
            challenges = context.get("marketing_challenges", [])
            
            # Industry-specific recommendations
            if industry == "technology":
                recommendations.extend([
                    {
                        "type": "content_strategy",
                        "title": "Technical Content Marketing",
                        "description": "Create technical blog posts and whitepapers to establish thought leadership",
                        "action": "blog_content",
                        "priority": "high"
                    },
                    {
                        "type": "channel_strategy",
                        "title": "LinkedIn B2B Strategy",
                        "description": "Leverage LinkedIn for B2B lead generation and networking",
                        "action": "social_media_content",
                        "priority": "medium"
                    }
                ])
            
            # Business size specific recommendations
            if business_size == "startup":
                recommendations.append({
                    "type": "strategy",
                    "title": "Lean Marketing Strategy",
                    "description": "Cost-effective marketing approach focused on quick wins and growth",
                    "action": "marketing_strategy",
                    "priority": "high"
                })
            
            # Challenge-specific recommendations
            if "lead generation" in challenges:
                recommendations.append({
                    "type": "lead_generation",
                    "title": "Lead Magnet Campaign",
                    "description": "Create valuable content offers to capture and nurture leads",
                    "action": "email_marketing",
                    "priority": "high"
                })
            
            if "brand awareness" in challenges:
                recommendations.append({
                    "type": "awareness",
                    "title": "Brand Awareness Campaign",
                    "description": "Multi-channel campaign to increase brand visibility and recognition",
                    "action": "campaign_strategy",
                    "priority": "medium"
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return []

    def _generate_ai_enhanced_recommendations(self, business_context: Dict[str, Any], ai_context) -> List[Dict[str, Any]]:
        """Generate enhanced recommendations using AI analysis results."""
        recommendations = []

        try:
            # Get base recommendations
            base_recommendations = self._generate_recommendations(business_context)
            recommendations.extend(base_recommendations)

            # Add AI-specific recommendations based on confidence and context
            if ai_context.confidence_score > 0.6:
                # High confidence - provide specific recommendations
                if ai_context.industry and ai_context.business_type:
                    recommendations.append({
                        "type": "ai_insight",
                        "title": f"AI-Powered {ai_context.industry} Strategy",
                        "description": f"Tailored marketing approach for {ai_context.business_type} businesses in {ai_context.industry}",
                        "action": "strategic_consultation",
                        "priority": "high",
                        "confidence": ai_context.confidence_score
                    })

                if ai_context.marketing_challenges:
                    for challenge in ai_context.marketing_challenges[:2]:  # Top 2 challenges
                        recommendations.append({
                            "type": "challenge_solution",
                            "title": f"Address {challenge.title()}",
                            "description": f"AI-identified solution for your {challenge} challenge",
                            "action": f"solve_{challenge.replace(' ', '_')}",
                            "priority": "medium",
                            "confidence": ai_context.confidence_score
                        })

            elif ai_context.confidence_score > 0.3:
                # Medium confidence - provide general recommendations
                recommendations.append({
                    "type": "ai_guidance",
                    "title": "AI-Assisted Marketing Analysis",
                    "description": "Let me analyze your business further to provide more targeted recommendations",
                    "action": "detailed_analysis",
                    "priority": "medium",
                    "confidence": ai_context.confidence_score
                })

            # Sort by priority and confidence
            recommendations.sort(key=lambda x: (
                {"high": 3, "medium": 2, "low": 1}.get(x.get("priority", "low"), 1),
                x.get("confidence", 0)
            ), reverse=True)

            return recommendations[:8]  # Limit to top 8 recommendations

        except Exception as e:
            logger.error(f"Error generating AI-enhanced recommendations: {e}")
            return self._generate_recommendations(business_context)
