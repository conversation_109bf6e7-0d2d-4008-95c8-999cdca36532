import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

interface UserContext {
  industry?: string;
  businessSize?: string;
  previousInteractions?: any[];
  uploadedData?: any[];
  conversationHistory?: any[];
}

interface SmartDefaults {
  brand_description?: string;
  target_audience?: string;
  products_services?: string;
  marketing_goals?: string;
  tone?: string;
  keywords?: string;
  suggested_topics?: string;
  competitive_landscape?: string;
  budget?: string;
  timeline?: string;
  platforms?: string;
}

interface BusinessContext {
  industry: string;
  businessType: string;
  targetMarket: string;
  keyProducts: string[];
  marketingChallenges: string[];
  competitiveAdvantages: string[];
}

export const useSmartFormDefaults = (contentType: string, userContext?: UserContext) => {
  const [smartDefaults, setSmartDefaults] = useState<SmartDefaults>({});
  const [businessContext, setBusinessContext] = useState<BusinessContext | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const { toast } = useToast();

  // Industry-specific defaults
  const getIndustryDefaults = (industry: string): Partial<SmartDefaults> => {
    const industryDefaults: Record<string, Partial<SmartDefaults>> = {
      technology: {
        tone: 'Professional',
        keywords: 'innovation, technology, digital transformation, software, automation',
        target_audience: 'Tech-savvy professionals, decision makers, early adopters',
        marketing_goals: 'Generate qualified leads, establish thought leadership, drive product adoption',
        platforms: 'LinkedIn, Twitter, industry publications, tech conferences'
      },
      healthcare: {
        tone: 'Professional',
        keywords: 'healthcare, medical, patient care, health outcomes, compliance',
        target_audience: 'Healthcare professionals, patients, healthcare administrators',
        marketing_goals: 'Build trust, educate patients, demonstrate expertise, ensure compliance',
        platforms: 'Medical journals, healthcare conferences, LinkedIn, patient portals'
      },
      retail: {
        tone: 'Friendly',
        keywords: 'shopping, deals, quality, customer service, lifestyle',
        target_audience: 'Consumers, shoppers, brand enthusiasts',
        marketing_goals: 'Drive sales, increase brand awareness, improve customer loyalty',
        platforms: 'Instagram, Facebook, TikTok, email marketing, influencer partnerships'
      },
      finance: {
        tone: 'Professional',
        keywords: 'financial services, investment, security, trust, expertise',
        target_audience: 'Investors, business owners, financial decision makers',
        marketing_goals: 'Build trust, demonstrate expertise, generate qualified leads',
        platforms: 'LinkedIn, financial publications, webinars, industry events'
      },
      education: {
        tone: 'Informative',
        keywords: 'education, learning, knowledge, skills, development',
        target_audience: 'Students, educators, parents, lifelong learners',
        marketing_goals: 'Increase enrollment, showcase outcomes, build reputation',
        platforms: 'Social media, educational conferences, content marketing, partnerships'
      }
    };

    return industryDefaults[industry.toLowerCase()] || {};
  };

  // Content type specific defaults
  const getContentTypeDefaults = (type: string): Partial<SmartDefaults> => {
    const contentDefaults: Record<string, Partial<SmartDefaults>> = {
      blog_content: {
        marketing_goals: 'Drive organic traffic, establish thought leadership, educate audience',
        suggested_topics: 'Industry trends, how-to guides, case studies, best practices',
        tone: 'Informative'
      },
      email_marketing: {
        marketing_goals: 'Nurture leads, increase engagement, drive conversions',
        suggested_topics: 'Welcome series, product updates, educational content, promotions',
        tone: 'Friendly'
      },
      social_media_content: {
        marketing_goals: 'Increase engagement, build community, drive brand awareness',
        suggested_topics: 'Behind-the-scenes, user-generated content, industry news, tips',
        tone: 'Conversational'
      },
      ad_copy: {
        marketing_goals: 'Drive conversions, increase click-through rates, generate leads',
        suggested_topics: 'Product benefits, special offers, problem solutions, testimonials',
        tone: 'Persuasive'
      },
      press_release: {
        marketing_goals: 'Generate media coverage, announce news, build credibility',
        suggested_topics: 'Product launches, partnerships, awards, company milestones',
        tone: 'Professional'
      },
      competitor_analysis: {
        marketing_goals: 'Identify opportunities, understand market position, inform strategy',
        suggested_topics: 'Competitive landscape, market gaps, positioning opportunities',
        tone: 'Analytical'
      }
    };

    return contentDefaults[type] || {};
  };

  // Analyze uploaded data for context using backend business context detection
  const analyzeUploadedData = async (dataFiles: any[]): Promise<Partial<SmartDefaults>> => {
    if (!dataFiles || dataFiles.length === 0) return {};

    try {
      // Call backend business context detection API
      const response = await fetch('/api/agents/detect-business-context', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          data_sources: dataFiles.map(file => file.id || file.name),
          analysis_depth: 'standard'
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.business_context) {
        const context = result.business_context;
        let defaults: Partial<SmartDefaults> = {};

        // Map business context to form defaults
        if (context.industry) {
          const industryDefaults = getIndustryDefaults(context.industry);
          defaults = { ...defaults, ...industryDefaults };
        }

        if (context.target_market) {
          defaults.target_audience = context.target_market;
        }

        if (context.key_products && context.key_products.length > 0) {
          defaults.products_services = context.key_products.join(', ');
        }

        if (context.marketing_challenges && context.marketing_challenges.length > 0) {
          defaults.marketing_goals = `Address key challenges: ${context.marketing_challenges.join(', ')}`;
        }

        if (context.business_size) {
          if (context.business_size === 'startup') {
            defaults.budget = 'Limited budget, focus on cost-effective channels and organic growth';
            defaults.timeline = '3-6 months for initial traction';
          } else if (context.business_size === 'enterprise') {
            defaults.budget = 'Substantial budget for comprehensive marketing initiatives';
            defaults.timeline = '6-12 months for strategic implementation';
          }
        }

        if (context.current_marketing_channels && context.current_marketing_channels.length > 0) {
          defaults.platforms = context.current_marketing_channels.join(', ');
        }

        // Add confidence indicator
        if (context.confidence_score > 0.7) {
          defaults.suggested_topics = `High-confidence recommendations based on your business data (${Math.round(context.confidence_score * 100)}% confidence)`;
        }

        return defaults;
      }

      return {};
    } catch (error) {
      console.error('Error analyzing uploaded data:', error);
      // Fallback to basic file analysis
      return analyzeFileTypes(dataFiles);
    }
  };

  // Fallback file type analysis
  const analyzeFileTypes = (dataFiles: any[]): Partial<SmartDefaults> => {
    const hasBusinessPlan = dataFiles.some(file =>
      file.name.toLowerCase().includes('business') ||
      file.name.toLowerCase().includes('plan')
    );

    const hasFinancialData = dataFiles.some(file =>
      file.name.toLowerCase().includes('financial') ||
      file.name.toLowerCase().includes('revenue') ||
      file.type === 'application/vnd.ms-excel'
    );

    const hasMarketingMaterials = dataFiles.some(file =>
      file.name.toLowerCase().includes('marketing') ||
      file.name.toLowerCase().includes('brand')
    );

    let defaults: Partial<SmartDefaults> = {};

    if (hasBusinessPlan) {
      defaults.brand_description = 'Business plan detected - we can extract your company overview';
      defaults.target_audience = 'Target market segments from your business documentation';
    }

    if (hasFinancialData) {
      defaults.budget = 'Financial data available for budget recommendations';
      defaults.marketing_goals = 'ROI-focused objectives based on your financial data';
    }

    if (hasMarketingMaterials) {
      defaults.tone = 'Consistent with your existing brand materials';
      defaults.keywords = 'Keywords from your current marketing content';
    }

    return defaults;
  };

  // Extract context from conversation history
  const extractConversationContext = (history: any[]): Partial<SmartDefaults> => {
    if (!history || history.length === 0) return {};

    try {
      // Look for previously mentioned business information
      const businessMentions = history.filter(msg => 
        msg.content && (
          msg.content.toLowerCase().includes('business') ||
          msg.content.toLowerCase().includes('company') ||
          msg.content.toLowerCase().includes('product')
        )
      );

      if (businessMentions.length > 0) {
        // Extract common themes and information
        const recentContent = businessMentions.slice(-3).map(msg => msg.content).join(' ');
        
        // Simple keyword extraction (in production, this would use NLP)
        const keywords = [];
        if (recentContent.includes('software') || recentContent.includes('tech')) {
          keywords.push('technology', 'software', 'innovation');
        }
        if (recentContent.includes('health') || recentContent.includes('medical')) {
          keywords.push('healthcare', 'medical', 'wellness');
        }
        if (recentContent.includes('retail') || recentContent.includes('store')) {
          keywords.push('retail', 'shopping', 'customer');
        }

        return {
          keywords: keywords.join(', '),
          suggested_topics: 'Topics based on your previous conversations and interests'
        };
      }

      return {};
    } catch (error) {
      console.error('Error extracting conversation context:', error);
      return {};
    }
  };

  // Main function to generate smart defaults
  const generateSmartDefaults = async () => {
    setIsAnalyzing(true);
    
    try {
      let defaults: SmartDefaults = {};

      // 1. Apply content type defaults
      const contentDefaults = getContentTypeDefaults(contentType);
      defaults = { ...defaults, ...contentDefaults };

      // 2. Apply industry defaults if available
      if (userContext?.industry) {
        const industryDefaults = getIndustryDefaults(userContext.industry);
        defaults = { ...defaults, ...industryDefaults };
      }

      // 3. Analyze uploaded data
      if (userContext?.uploadedData) {
        const dataDefaults = await analyzeUploadedData(userContext.uploadedData);
        defaults = { ...defaults, ...dataDefaults };
      }

      // 4. Extract conversation context
      if (userContext?.conversationHistory) {
        const conversationDefaults = extractConversationContext(userContext.conversationHistory);
        defaults = { ...defaults, ...conversationDefaults };
      }

      // 5. Apply business size specific adjustments
      if (userContext?.businessSize) {
        if (userContext.businessSize === 'startup') {
          defaults.budget = defaults.budget || 'Limited budget, focus on cost-effective channels';
          defaults.marketing_goals = defaults.marketing_goals || 'Build brand awareness, acquire first customers, establish market presence';
        } else if (userContext.businessSize === 'enterprise') {
          defaults.budget = defaults.budget || 'Substantial budget for comprehensive marketing initiatives';
          defaults.marketing_goals = defaults.marketing_goals || 'Scale existing success, enter new markets, maintain market leadership';
        }
      }

      setSmartDefaults(defaults);
      
      if (Object.keys(defaults).length > 0) {
        toast({
          title: "Smart defaults applied",
          description: `Pre-filled ${Object.keys(defaults).length} fields based on your context.`,
        });
      }

    } catch (error) {
      console.error('Error generating smart defaults:', error);
      toast({
        title: "Smart defaults unavailable",
        description: "Using standard defaults for the form.",
        variant: "default"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Detect business context from available data using AI-powered analysis
  const detectBusinessContext = async (): Promise<BusinessContext | null> => {
    if (!userContext?.uploadedData && !userContext?.conversationHistory) {
      return null;
    }

    try {
      // Call backend AI-powered business context detection
      const response = await fetch('/api/agents/detect-business-context', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          data_sources: userContext.uploadedData?.map(file => file.id || file.name) || [],
          conversation_history: userContext.conversationHistory || [],
          user_profile: {
            industry: userContext.industry,
            business_size: userContext.businessSize
          },
          analysis_depth: 'standard',
          include_recommendations: true
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.business_context) {
        const context = result.business_context;

        // Map backend response to frontend BusinessContext interface
        const businessContext: BusinessContext = {
          industry: context.industry || 'Unknown',
          businessType: context.business_type || 'Unknown',
          targetMarket: context.target_market || 'General market',
          keyProducts: context.key_products || [],
          marketingChallenges: context.marketing_challenges || [],
          competitiveAdvantages: context.competitive_advantages || []
        };

        setBusinessContext(businessContext);
        return businessContext;
      } else {
        console.warn('Business context detection returned no results');
        return null;
      }
    } catch (error) {
      console.error('Error detecting business context:', error);

      // Fallback to basic context if API fails
      const fallbackContext: BusinessContext = {
        industry: userContext.industry || 'Technology',
        businessType: 'Unknown',
        targetMarket: 'General market',
        keyProducts: [],
        marketingChallenges: ['Lead generation', 'Brand awareness'],
        competitiveAdvantages: []
      };

      setBusinessContext(fallbackContext);
      return fallbackContext;
    }
  };

  // Generate defaults when dependencies change
  useEffect(() => {
    if (contentType) {
      generateSmartDefaults();
      detectBusinessContext();
    }
  }, [contentType, userContext]);

  return {
    smartDefaults,
    businessContext,
    isAnalyzing,
    regenerateDefaults: generateSmartDefaults,
    detectBusinessContext
  };
};
