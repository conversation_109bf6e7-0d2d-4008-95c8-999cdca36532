"""
API Endpoints for Pricing, Subscriptions, Discounts, and Promotions.
"""
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel # Added BaseModel import

from ..services.pricing_service import PricingService, get_pricing_service
from ..models.pricing import (
    PricingTierCreate, PricingTierResponse, PricingTierBase,
    SubscriptionCreate, SubscriptionResponse,
    DiscountCreate, DiscountResponse, DiscountBase,
    PromotionCreate, PromotionResponse, PromotionBase
)
# Assuming you have auth utilities for current_user
from ..auth import get_current_active_user # Corrected import
# Pydantic model for User, if needed for response models or current_user type hint
from ..models.auth import User as UserSchema 

# Helper dependency for admin users
async def get_current_admin_user(current_user: UserSchema = Depends(get_current_active_user)):
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not authorized for this action")
    return current_user

router = APIRouter(prefix="/pricing", tags=["Pricing & Subscriptions"])

# --- Pricing Tier Endpoints ---
@router.post("/tiers", response_model=PricingTierResponse, status_code=201)
async def create_pricing_tier_endpoint(
    tier_data: PricingTierCreate,
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_admin_user) # Admin only
):
    """
    Create a new pricing tier. (Admin only)
    """
    return await service.create_pricing_tier(**tier_data.model_dump())

@router.get("/tiers/{tier_id}", response_model=PricingTierResponse)
async def get_pricing_tier_endpoint(
    tier_id: str,
    service: PricingService = Depends(get_pricing_service)
):
    """
    Get a specific pricing tier by ID.
    """
    tier = await service.get_pricing_tier(tier_id)
    if not tier:
        raise HTTPException(status_code=404, detail="Pricing tier not found")
    return tier

@router.get("/tiers", response_model=List[PricingTierResponse])
async def list_pricing_tiers_endpoint(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    service: PricingService = Depends(get_pricing_service)
):
    """
    List all pricing tiers. Filters by active status if provided.
    """
    return await service.get_all_pricing_tiers(skip=skip, limit=limit, is_active=is_active)

@router.put("/tiers/{tier_id}", response_model=PricingTierResponse)
async def update_pricing_tier_endpoint(
    tier_id: str,
    tier_update_data: PricingTierBase, # Using base for update, allows partial updates
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_admin_user) # Admin only
):
    """
    Update an existing pricing tier. (Admin only)
    """
    updated_tier = await service.update_pricing_tier(tier_id, tier_update_data.model_dump(exclude_unset=True))
    if not updated_tier:
        raise HTTPException(status_code=404, detail="Pricing tier not found")
    return updated_tier

# --- Subscription Endpoints ---
@router.post("/subscriptions", response_model=SubscriptionResponse, status_code=201)
async def create_subscription_endpoint(
    subscription_data: SubscriptionCreate,
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_active_user) # Authenticated user
):
    """
    Create a new subscription for the current user.
    """
    # Ensure user_id in subscription_data matches current_user.id or is admin
    if subscription_data.user_id != current_user.id and not current_user.is_superuser:
         raise HTTPException(status_code=403, detail="Cannot create subscription for another user.")

    subscription = await service.create_subscription(
        user_id=subscription_data.user_id,
        tier_id=subscription_data.pricing_tier_id,
        status=subscription_data.status,
        auto_renew=subscription_data.auto_renew
    )
    if not subscription:
        raise HTTPException(status_code=400, detail="Could not create subscription. Invalid user or tier.")
    return subscription

@router.get("/subscriptions/me", response_model=List[SubscriptionResponse])
async def get_my_subscriptions_endpoint(
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_active_user)
):
    """
    Get all subscriptions for the currently authenticated user.
    """
    return await service.get_user_subscriptions(user_id=current_user.id)

@router.get("/subscriptions/{subscription_id}", response_model=SubscriptionResponse)
async def get_subscription_endpoint(
    subscription_id: str,
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_active_user) 
):
    """
    Get a specific subscription by ID.
    Users can only access their own subscriptions unless they are admin.
    """
    subscription = await service.get_subscription(subscription_id)
    if not subscription:
        raise HTTPException(status_code=404, detail="Subscription not found")
    if subscription.user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not authorized to access this subscription")
    return subscription

@router.post("/subscriptions/{subscription_id}/cancel", response_model=SubscriptionResponse)
async def cancel_subscription_endpoint(
    subscription_id: str,
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_active_user)
):
    """
    Cancel a subscription.
    Users can only cancel their own subscriptions unless they are admin.
    """
    subscription = await service.get_subscription(subscription_id) # Check ownership first
    if not subscription:
        raise HTTPException(status_code=404, detail="Subscription not found")
    if subscription.user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Not authorized to cancel this subscription")

    cancelled_subscription = await service.cancel_subscription(subscription_id)
    if not cancelled_subscription:
        # This case should ideally be caught by the check above
        raise HTTPException(status_code=404, detail="Subscription not found or could not be cancelled.")
    return cancelled_subscription

# --- Discount Endpoints ---
@router.post("/discounts", response_model=DiscountResponse, status_code=201)
async def create_discount_endpoint(
    discount_data: DiscountCreate,
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_admin_user) # Admin only
):
    """
    Create a new discount code. (Admin only)
    """
    return await service.create_discount(**discount_data.model_dump())

@router.get("/discounts/{code}", response_model=DiscountResponse)
async def get_discount_by_code_endpoint(
    code: str,
    service: PricingService = Depends(get_pricing_service)
    # No auth needed, public can check discount validity
):
    """
    Get discount details by code.
    """
    discount = await service.get_discount_by_code(code)
    if not discount:
        raise HTTPException(status_code=404, detail="Discount code not found or inactive")
    return discount

# --- Promotion Endpoints ---
@router.post("/promotions", response_model=PromotionResponse, status_code=201)
async def create_promotion_endpoint(
    promotion_data: PromotionCreate,
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_admin_user) # Admin only
):
    """
    Create a new promotion. (Admin only)
    """
    return await service.create_promotion(**promotion_data.model_dump())

@router.get("/promotions/active", response_model=List[PromotionResponse])
async def get_active_promotions_endpoint(
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_active_user) # Optional: pass user for targeted promotions
):
    """
    Get all active promotions.
    Optionally, can be filtered for the current user.
    """
    return await service.get_active_promotions(user_id=current_user.id if current_user else None)


# --- Cart & Dynamic Pricing Endpoints ---
class CartTotalRequest(BaseModel):
    discount_code: Optional[str] = None

class CartTotalResponse(BaseModel):
    items: List[Dict[str, Any]]
    subtotal: float
    discount_applied: Optional[Dict[str, Any]] = None
    total_amount: float
    currency: str

@router.post("/cart/calculate-total", response_model=CartTotalResponse)
async def calculate_cart_total_endpoint(
    request_data: CartTotalRequest = Body(None), # Allow empty body if no discount code
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_active_user)
):
    """
    Calculate the total price for the current user's cart,
    optionally applying a discount code.
    """
    discount_code = request_data.discount_code if request_data else None
    cart_summary = await service.calculate_cart_total(user_id=current_user.id, discount_code=discount_code)
    return cart_summary

class DynamicPriceResponse(BaseModel):
    price: float
    currency: str
    original_price: float

@router.get("/personas/{persona_id}/dynamic-price", response_model=DynamicPriceResponse)
async def get_persona_dynamic_price_endpoint(
    persona_id: str,
    service: PricingService = Depends(get_pricing_service),
    current_user: UserSchema = Depends(get_current_active_user) # Context might be needed
):
    """
    Get the dynamically calculated price for a persona.
    (Currently a placeholder, will use ML model in future).
    """
    # user_context can be built from current_user or other sources
    user_context = {"user_id": current_user.id, "user_tier": "free"} # Example context
    price_info = await service.calculate_dynamic_price(persona_id=persona_id, user_context=user_context)
    return price_info
