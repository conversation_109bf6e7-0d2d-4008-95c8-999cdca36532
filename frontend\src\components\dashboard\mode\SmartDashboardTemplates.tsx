/**
 * Smart Dashboard Templates Component
 * 
 * Provides intelligent dashboard template recommendations based on user data,
 * industry, and use case. Features AI-powered template matching and customization.
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  <PERSON><PERSON><PERSON>,
  Brain,
  Target,
  TrendingUp,
  Bar<PERSON>hart3,
  <PERSON><PERSON>hart,
  Users,
  DollarSign,
  ShoppingCart,
  Calendar,
  Globe,
  Zap,
  Eye,
  Download,
  Star,
  CheckCircle,
  Lightbulb,
  Wand2,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SmartTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  industry: string[];
  use_cases: string[];
  data_types: string[];
  complexity: 'beginner' | 'intermediate' | 'advanced';
  ai_match_score: number;
  preview_image?: string;
  widgets: TemplateWidget[];
  layout_config: any;
  customization_options: string[];
  estimated_setup_time: string;
  user_rating: number;
  download_count: number;
}

interface TemplateWidget {
  type: 'chart' | 'kpi' | 'table' | 'gauge' | 'text';
  chart_type?: string;
  title: string;
  description: string;
  data_requirements: string[];
  position: { x: number; y: number; w: number; h: number };
}

interface UserProfile {
  industry?: string;
  role?: string;
  experience_level?: 'beginner' | 'intermediate' | 'advanced';
  data_sources?: string[];
  goals?: string[];
}

interface SmartDashboardTemplatesProps {
  className?: string;
  user_profile?: UserProfile;
  available_data_sources?: string[];
  on_template_select?: (template: SmartTemplate) => void;
  on_template_customize?: (template: SmartTemplate, customizations: any) => void;
}

export const SmartDashboardTemplates: React.FC<SmartDashboardTemplatesProps> = ({
  className,
  user_profile = {},
  available_data_sources = [],
  on_template_select,
  on_template_customize,
}) => {
  const [templates, set_templates] = useState<SmartTemplate[]>([]);
  const [filtered_templates, set_filtered_templates] = useState<SmartTemplate[]>([]);
  const [selected_template, set_selected_template] = useState<SmartTemplate | null>(null);
  const [show_customization, set_show_customization] = useState(false);
  const [ai_recommendations, set_ai_recommendations] = useState<SmartTemplate[]>([]);
  const [is_analyzing, set_is_analyzing] = useState(false);
  const [search_query, set_search_query] = useState('');
  const [selected_industry, set_selected_industry] = useState<string>('all');
  const [selected_complexity, set_selected_complexity] = useState<string>('all');

  // Load templates and generate AI recommendations
  useEffect(() => {
    load_smart_templates();
  }, []);

  useEffect(() => {
    if (templates.length > 0) {
      generate_ai_recommendations();
    }
  }, [templates, user_profile, available_data_sources]);

  useEffect(() => {
    filter_templates();
  }, [templates, search_query, selected_industry, selected_complexity]);

  const load_smart_templates = async () => {
    // In a real implementation, this would fetch from an API
    const mock_templates = get_mock_smart_templates();
    set_templates(mock_templates);
  };

  const generate_ai_recommendations = async () => {
    set_is_analyzing(true);
    try {
      // Simulate AI analysis
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const scored_templates = templates.map(template => ({
        ...template,
        ai_match_score: calculate_ai_match_score(template, user_profile, available_data_sources),
      }));

      const recommendations = scored_templates
        .filter(template => template.ai_match_score > 0.6)
        .sort((a, b) => b.ai_match_score - a.ai_match_score)
        .slice(0, 3);

      set_ai_recommendations(recommendations);
    } catch (error) {
      console.error('Error generating AI recommendations:', error);
    } finally {
      set_is_analyzing(false);
    }
  };

  const filter_templates = () => {
    let filtered = templates;

    if (search_query) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(search_query.toLowerCase()) ||
        template.description.toLowerCase().includes(search_query.toLowerCase()) ||
        template.use_cases.some(use_case => 
          use_case.toLowerCase().includes(search_query.toLowerCase())
        )
      );
    }

    if (selected_industry !== 'all') {
      filtered = filtered.filter(template =>
        template.industry.includes(selected_industry)
      );
    }

    if (selected_complexity !== 'all') {
      filtered = filtered.filter(template =>
        template.complexity === selected_complexity
      );
    }

    set_filtered_templates(filtered);
  };

  const handle_template_select = (template: SmartTemplate) => {
    set_selected_template(template);
    set_show_customization(true);
  };

  const handle_template_apply = (template: SmartTemplate, customizations?: any) => {
    if (customizations) {
      on_template_customize?.(template, customizations);
    } else {
      on_template_select?.(template);
    }
    set_show_customization(false);
    set_selected_template(null);
  };

  const get_category_icon = (category: string) => {
    const icons = {
      business: BarChart3,
      sales: TrendingUp,
      marketing: Target,
      finance: DollarSign,
      ecommerce: ShoppingCart,
      analytics: PieChart,
      operations: Users,
    };
    return icons[category as keyof typeof icons] || BarChart3;
  };

  const get_complexity_color = (complexity: string) => {
    const colors = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800',
    };
    return colors[complexity as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-6 w-6 text-purple-500" />
            <span>Smart Dashboard Templates</span>
            <Badge variant="secondary">AI-Powered</Badge>
          </CardTitle>
          <CardDescription>
            Intelligent template recommendations based on your data, industry, and goals.
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="recommendations">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="recommendations">AI Recommendations</TabsTrigger>
          <TabsTrigger value="browse">Browse All</TabsTrigger>
          <TabsTrigger value="custom">Custom Builder</TabsTrigger>
        </TabsList>

        {/* AI Recommendations Tab */}
        <TabsContent value="recommendations" className="space-y-4">
          {is_analyzing ? (
            <Card>
              <CardContent className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Brain className="h-12 w-12 text-purple-500 mx-auto mb-4 animate-pulse" />
                  <h3 className="text-lg font-medium mb-2">Analyzing Your Needs</h3>
                  <p className="text-muted-foreground">
                    AI is analyzing your data sources and profile to find the perfect templates...
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {ai_recommendations.length > 0 ? (
                <>
                  <div className="flex items-center space-x-2">
                    <Sparkles className="h-5 w-5 text-purple-500" />
                    <h3 className="text-lg font-semibold">Recommended for You</h3>
                    <Badge variant="outline">{ai_recommendations.length} matches</Badge>
                  </div>
                  
                  <div className="grid gap-6">
                    {ai_recommendations.map((template, index) => {
                      const CategoryIcon = get_category_icon(template.category);
                      return (
                        <Card key={template.id} className="border-l-4 border-l-purple-500">
                          <CardContent className="p-6">
                            <div className="flex items-start justify-between">
                              <div className="flex items-start space-x-4 flex-1">
                                <div className="p-3 bg-purple-50 rounded-lg">
                                  <CategoryIcon className="h-6 w-6 text-purple-500" />
                                </div>
                                <div className="flex-1 space-y-2">
                                  <div className="flex items-center space-x-2">
                                    <h4 className="text-lg font-semibold">{template.name}</h4>
                                    <Badge className="bg-purple-100 text-purple-800">
                                      {Math.round(template.ai_match_score * 100)}% match
                                    </Badge>
                                    {index === 0 && (
                                      <Badge variant="default" className="bg-gold text-white">
                                        <Star className="h-3 w-3 mr-1" />
                                        Best Match
                                      </Badge>
                                    )}
                                  </div>
                                  <p className="text-muted-foreground">{template.description}</p>
                                  
                                  <div className="flex flex-wrap gap-2">
                                    {template.use_cases.slice(0, 3).map((use_case, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs">
                                        {use_case}
                                      </Badge>
                                    ))}
                                  </div>
                                  
                                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                    <span>{template.widgets.length} widgets</span>
                                    <span>{template.estimated_setup_time}</span>
                                    <span className="flex items-center space-x-1">
                                      <Star className="h-3 w-3" />
                                      <span>{template.user_rating}</span>
                                    </span>
                                  </div>
                                </div>
                              </div>
                              
                              <div className="flex flex-col space-y-2">
                                <Button
                                  onClick={() => handle_template_select(template)}
                                  className="min-w-[120px]"
                                >
                                  <Wand2 className="h-4 w-4 mr-2" />
                                  Use Template
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {/* Show preview */}}
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  Preview
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </>
              ) : (
                <Card>
                  <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <Lightbulb className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Specific Recommendations</h3>
                      <p className="text-muted-foreground mb-4">
                        Browse all templates or provide more information about your goals.
                      </p>
                      <Button onClick={() => {/* Switch to browse tab */}}>
                        Browse All Templates
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        {/* Browse All Tab */}
        <TabsContent value="browse" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search templates..."
                    value={search_query}
                    onChange={(e) => set_search_query(e.target.value)}
                  />
                </div>
                <Select value={selected_industry} onValueChange={set_selected_industry}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Industries</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="sales">Sales</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                    <SelectItem value="ecommerce">E-commerce</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selected_complexity} onValueChange={set_selected_complexity}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Complexity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Template Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filtered_templates.map((template) => {
              const CategoryIcon = get_category_icon(template.category);
              return (
                <Card key={template.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        <CategoryIcon className="h-5 w-5 text-blue-500" />
                        <CardTitle className="text-lg">{template.name}</CardTitle>
                      </div>
                      <Badge className={cn("text-xs", get_complexity_color(template.complexity))}>
                        {template.complexity}
                      </Badge>
                    </div>
                    <CardDescription>{template.description}</CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Preview Area */}
                    <div className="h-32 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border-2 border-dashed border-blue-200 flex items-center justify-center">
                      <div className="text-center text-muted-foreground">
                        <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                        <p className="text-xs">{template.widgets.length} widgets</p>
                      </div>
                    </div>

                    {/* Template Info */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="flex items-center space-x-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span>{template.user_rating}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Download className="h-4 w-4 text-muted-foreground" />
                          <span>{template.download_count}</span>
                        </span>
                      </div>

                      <div className="flex flex-wrap gap-1">
                        {template.use_cases.slice(0, 2).map((use_case, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {use_case}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {/* Show preview */}}
                        className="flex-1"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Preview
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handle_template_select(template)}
                        className="flex-1"
                      >
                        <Zap className="h-4 w-4 mr-1" />
                        Use
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Custom Builder Tab */}
        <TabsContent value="custom" className="space-y-4">
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center">
                <Wand2 className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Custom Template Builder</h3>
                <p className="text-muted-foreground mb-4">
                  Create a custom template tailored to your specific needs.
                </p>
                <Button>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Start Building
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Template Customization Dialog */}
      {selected_template && (
        <Dialog open={show_customization} onOpenChange={set_show_customization}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Wand2 className="h-5 w-5 text-purple-500" />
                <span>Customize Template: {selected_template.name}</span>
              </DialogTitle>
              <DialogDescription>
                Customize this template to match your specific needs and data.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Template Overview */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="font-medium">Category</Label>
                  <p className="text-muted-foreground capitalize">{selected_template.category}</p>
                </div>
                <div>
                  <Label className="font-medium">Complexity</Label>
                  <p className="text-muted-foreground capitalize">{selected_template.complexity}</p>
                </div>
                <div>
                  <Label className="font-medium">Widgets</Label>
                  <p className="text-muted-foreground">{selected_template.widgets.length}</p>
                </div>
                <div>
                  <Label className="font-medium">Setup Time</Label>
                  <p className="text-muted-foreground">{selected_template.estimated_setup_time}</p>
                </div>
              </div>

              {/* Customization Options */}
              <div className="space-y-4">
                <Label className="font-medium">Customization Options</Label>
                <div className="grid grid-cols-2 gap-4">
                  {selected_template.customization_options.map((option, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">{option}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Preview */}
              <div className="h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <BarChart3 className="h-16 w-16 mx-auto mb-4" />
                  <p>Template Preview</p>
                  <p className="text-sm">Interactive preview coming soon</p>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => set_show_customization(false)}>
                Cancel
              </Button>
              <Button onClick={() => handle_template_apply(selected_template)}>
                <Zap className="h-4 w-4 mr-2" />
                Apply Template
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

// Calculate AI match score based on user profile and data sources
function calculate_ai_match_score(
  template: SmartTemplate,
  user_profile: UserProfile,
  available_data_sources: string[]
): number {
  let score = 0.5; // Base score

  // Industry match
  if (user_profile.industry && template.industry.includes(user_profile.industry)) {
    score += 0.3;
  }

  // Experience level match
  if (user_profile.experience_level === template.complexity) {
    score += 0.2;
  }

  // Data source compatibility
  const compatible_data_sources = template.data_types.filter(type =>
    available_data_sources.some(source => source.toLowerCase().includes(type.toLowerCase()))
  );
  if (compatible_data_sources.length > 0) {
    score += 0.2 * (compatible_data_sources.length / template.data_types.length);
  }

  // Goals alignment
  if (user_profile.goals) {
    const matching_goals = user_profile.goals.filter(goal =>
      template.use_cases.some(use_case => 
        use_case.toLowerCase().includes(goal.toLowerCase())
      )
    );
    if (matching_goals.length > 0) {
      score += 0.1 * (matching_goals.length / user_profile.goals.length);
    }
  }

  return Math.min(score, 1.0);
}

// Mock smart templates data
function get_mock_smart_templates(): SmartTemplate[] {
  return [
    {
      id: 'sales-performance',
      name: 'Sales Performance Dashboard',
      description: 'Comprehensive sales tracking with KPIs, trends, and team performance',
      category: 'sales',
      industry: ['sales', 'business', 'retail'],
      use_cases: ['sales tracking', 'performance monitoring', 'team management'],
      data_types: ['sales_data', 'customer_data', 'revenue'],
      complexity: 'intermediate',
      ai_match_score: 0.85,
      widgets: [
        {
          type: 'kpi',
          title: 'Total Revenue',
          description: 'Current month revenue',
          data_requirements: ['revenue'],
          position: { x: 0, y: 0, w: 3, h: 2 },
        },
        {
          type: 'chart',
          chart_type: 'line',
          title: 'Sales Trend',
          description: 'Monthly sales performance',
          data_requirements: ['date', 'sales_amount'],
          position: { x: 3, y: 0, w: 6, h: 4 },
        },
      ],
      layout_config: { columns: 12, rows: 8 },
      customization_options: ['Color scheme', 'Widget layout', 'Data filters', 'Time periods'],
      estimated_setup_time: '5 minutes',
      user_rating: 4.8,
      download_count: 1250,
    },
    // Add more mock templates...
  ];
}
