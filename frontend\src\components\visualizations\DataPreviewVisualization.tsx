import { VisualizationData } from '@/utils/visualization';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface DataPreviewVisualizationProps {
  visualization: VisualizationData;
}

export const DataPreviewVisualization = ({ visualization }: DataPreviewVisualizationProps) => {
  const { data } = visualization;
  const { preview_data, columns, description, metadata } = data;

  return (
    <Card className="shadow-lg border-gray-300 w-full bg-white">
      <CardHeader className="bg-gradient-to-r from-brand-500 to-brand-600 text-white border-b border-brand-700">
        <CardTitle className="text-white text-xl font-bold">{visualization.title || 'Data Preview'}</CardTitle>
        {visualization.description && <CardDescription className="text-brand-100">{visualization.description}</CardDescription>}
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="w-full border-b rounded-none px-4 bg-gray-100 border-gray-300">
            <TabsTrigger value="preview" className="data-[state=active]:bg-white data-[state=active]:text-brand-600 data-[state=active]:border-b-2 data-[state=active]:border-brand-500 font-semibold">
              📊 Data Preview
            </TabsTrigger>
            <TabsTrigger value="info" className="data-[state=active]:bg-white data-[state=active]:text-brand-600 data-[state=active]:border-b-2 data-[state=active]:border-brand-500 font-semibold">
              ℹ️ Data Info
            </TabsTrigger>
            {description && (
              <TabsTrigger value="description" className="data-[state=active]:bg-white data-[state=active]:text-brand-600 data-[state=active]:border-b-2 data-[state=active]:border-brand-500 font-semibold">
                📝 Description
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="preview" className="p-4">
            {preview_data && preview_data.length > 0 ? (
              <div className="overflow-x-auto w-full border border-gray-300 rounded-lg shadow-sm">
                <table className="w-full border-collapse min-w-full bg-white">
                  <thead>
                    <tr className="bg-gray-800 text-white">
                      {Object.keys(preview_data[0]).map((column, index) => (
                        <th key={index} className="text-left p-3 text-sm font-semibold border-r border-gray-600 last:border-r-0">
                          {column}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {preview_data.map((row, rowIndex) => (
                      <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white hover:bg-blue-50' : 'bg-gray-50 hover:bg-blue-50'}>
                        {Object.values(row).map((value, cellIndex) => (
                          <td key={cellIndex} className="p-3 text-sm text-gray-900 border-r border-gray-200 border-b border-gray-200 last:border-r-0">
                            <span className="font-medium">{String(value)}</span>
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : description ? (
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap bg-gray-100 p-4 rounded-lg text-sm border border-gray-300 text-gray-800">{description}</pre>
              </div>
            ) : (
              <div className="text-center p-8 text-gray-600 bg-gray-50 rounded-lg border border-gray-200">
                <div className="text-lg font-medium mb-2">No preview data available</div>
                <div className="text-sm">Upload a data file to see the preview</div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="info" className="p-4">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b border-gray-200 pb-2">Data Shape</h3>
                <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg text-sm">
                  {metadata?.shape ? (
                    <div className="flex gap-6">
                      <div className="bg-white px-3 py-2 rounded border">
                        <span className="text-gray-600">Rows:</span> <span className="font-bold text-blue-600">{metadata.shape[0].toLocaleString()}</span>
                      </div>
                      <div className="bg-white px-3 py-2 rounded border">
                        <span className="text-gray-600">Columns:</span> <span className="font-bold text-blue-600">{metadata.shape[1]}</span>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-600">Shape information not available</p>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b border-gray-200 pb-2">Columns</h3>
                <div className="bg-green-50 border border-green-200 p-4 rounded-lg text-sm">
                  {columns && columns.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {columns.map((column, index) => (
                        <div key={index} className="bg-white p-2 rounded border border-green-300 flex justify-between items-center">
                          <span className="font-medium text-gray-800">{column}</span>
                          {metadata?.dtypes && metadata.dtypes[column] && (
                            <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                              {metadata.dtypes[column]}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-600">Column information not available</p>
                  )}
                </div>
              </div>

              {metadata?.missing_values && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b border-gray-200 pb-2">Missing Values</h3>
                  <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg text-sm">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {Object.entries(metadata.missing_values).map(([column, count], index) => (
                        <div key={index} className="bg-white p-2 rounded border border-yellow-300 flex justify-between items-center">
                          <span className="font-medium text-gray-800">{column}</span>
                          <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded font-bold">
                            {count} missing
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {metadata?.description && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b border-gray-200 pb-2">Statistical Summary</h3>
                  <div className="overflow-x-auto bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <table className="w-full border-collapse text-sm bg-white rounded-lg overflow-hidden shadow-sm">
                      <thead>
                        <tr className="bg-purple-600 text-white">
                          <th className="text-left p-3 font-semibold border-r border-purple-500">Statistic</th>
                          {Object.keys(metadata.description).map((column, index) => (
                            <th key={index} className="text-left p-3 font-semibold border-r border-purple-500 last:border-r-0">{column}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {Object.keys(Object.values(metadata.description)[0] || {}).map((stat, statIndex) => (
                          <tr key={statIndex} className={statIndex % 2 === 0 ? 'bg-white hover:bg-purple-50' : 'bg-purple-50 hover:bg-purple-100'}>
                            <td className="p-3 font-semibold border-b border-r border-purple-200 bg-purple-100 text-purple-800">{stat}</td>
                            {Object.keys(metadata.description).map((column, colIndex) => (
                              <td key={colIndex} className="p-3 border-b border-r border-purple-200 last:border-r-0 text-gray-800 font-medium">
                                {typeof metadata.description[column][stat] === 'number'
                                  ? metadata.description[column][stat].toFixed(2)
                                  : metadata.description[column][stat] || 'N/A'}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          {description && (
            <TabsContent value="description" className="p-4">
              <div className="prose prose-sm max-w-none">
                <div className="bg-gray-100 border border-gray-300 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b border-gray-300 pb-2">Data Description</h3>
                  <pre className="whitespace-pre-wrap bg-white p-4 rounded-lg text-sm border border-gray-200 text-gray-800 font-mono leading-relaxed">{description}</pre>
                </div>
              </div>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
};
