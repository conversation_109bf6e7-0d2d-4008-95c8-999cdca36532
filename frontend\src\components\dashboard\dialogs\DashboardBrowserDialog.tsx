/**
 * Dashboard Browser Dialog
 * 
 * Modal dialog for browsing and selecting dashboards to open.
 * Features:
 * - Grid view of available dashboards
 * - Search and filter functionality
 * - Preview thumbnails
 * - Recent dashboards section
 * - Quick actions (duplicate, delete, share)
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Search,
  Grid,
  List,
  MoreVertical,
  Calendar,
  Users,
  Lock,
  Globe,
  Copy,
  Trash2,
  Share,
  Eye,
  Clock,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDashboardManagement } from '@/hooks/use-dashboard-management';
import { useToast } from '@/hooks/use-toast';

interface DashboardBrowserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDashboardSelect?: (dashboardId: string) => void;
}

export const DashboardBrowserDialog: React.FC<DashboardBrowserDialogProps> = ({
  open,
  onOpenChange,
  onDashboardSelect,
}) => {
  const { toast } = useToast();
  const { dashboards, switchDashboard, deleteDashboard, duplicateDashboard } = useDashboardManagement();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedTab, setSelectedTab] = useState('all');

  // Filter dashboards based on search and tab
  const filteredDashboards = dashboards.filter(dashboard => {
    const matchesSearch = dashboard.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         dashboard.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    switch (selectedTab) {
      case 'recent':
        // Show dashboards modified in the last 7 days
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return matchesSearch && new Date(dashboard.updated_at) > weekAgo;
      case 'shared':
        return matchesSearch && dashboard.is_public;
      case 'private':
        return matchesSearch && !dashboard.is_public;
      default:
        return matchesSearch;
    }
  });

  const handleDashboardOpen = async (dashboardId: string) => {
    try {
      await switchDashboard(dashboardId);
      onDashboardSelect?.(dashboardId);
      onOpenChange(false);
      
      toast({
        title: "Dashboard Opened",
        description: "Dashboard loaded successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to open dashboard.",
        variant: "destructive",
      });
    }
  };

  const handleDashboardDuplicate = async (dashboard: any) => {
    try {
      await duplicateDashboard(dashboard.id);
      toast({
        title: "Dashboard Duplicated",
        description: `"${dashboard.name}" has been duplicated.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate dashboard.",
        variant: "destructive",
      });
    }
  };

  const handleDashboardDelete = async (dashboard: any) => {
    if (window.confirm(`Are you sure you want to delete "${dashboard.name}"? This action cannot be undone.`)) {
      try {
        await deleteDashboard(dashboard.id);
        toast({
          title: "Dashboard Deleted",
          description: `"${dashboard.name}" has been deleted.`,
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete dashboard.",
          variant: "destructive",
        });
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const DashboardCard = ({ dashboard }: { dashboard: any }) => (
    <Card className="group hover:shadow-md transition-shadow cursor-pointer">
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-sm font-medium truncate">
              {dashboard.name}
            </CardTitle>
            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
              {dashboard.description || 'No description'}
            </p>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleDashboardOpen(dashboard.id)}>
                <Eye className="h-4 w-4 mr-2" />
                Open
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDashboardDuplicate(dashboard)}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share className="h-4 w-4 mr-2" />
                Share
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleDashboardDelete(dashboard)}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-2">
            {dashboard.is_public ? (
              <Globe className="h-3 w-3" />
            ) : (
              <Lock className="h-3 w-3" />
            )}
            <span>{dashboard.is_public ? 'Public' : 'Private'}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="h-3 w-3" />
            <span>{formatDate(dashboard.updated_at)}</span>
          </div>
        </div>
        <div className="mt-2 flex items-center justify-between">
          <div className="flex space-x-1">
            {dashboard.is_default && (
              <Badge variant="secondary" className="text-xs">Default</Badge>
            )}
            <Badge variant="outline" className="text-xs">
              {dashboard.section_count || 0} sections
            </Badge>
          </div>
          <Button
            size="sm"
            onClick={() => handleDashboardOpen(dashboard.id)}
            className="h-6 text-xs"
          >
            Open
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Browse Dashboards</DialogTitle>
          <DialogDescription>
            Select a dashboard to open from your collection.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and View Controls */}
          <div className="flex items-center justify-between">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search dashboards..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Tabs */}
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList>
              <TabsTrigger value="all">All ({dashboards.length})</TabsTrigger>
              <TabsTrigger value="recent">Recent</TabsTrigger>
              <TabsTrigger value="shared">Shared</TabsTrigger>
              <TabsTrigger value="private">Private</TabsTrigger>
            </TabsList>

            <TabsContent value={selectedTab} className="mt-4">
              <div className="max-h-96 overflow-y-auto">
                {filteredDashboards.length > 0 ? (
                  <div className={cn(
                    viewMode === 'grid' 
                      ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                      : "space-y-2"
                  )}>
                    {filteredDashboards.map((dashboard) => (
                      <DashboardCard key={dashboard.id} dashboard={dashboard} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Grid className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No dashboards found</p>
                    {searchQuery && (
                      <p className="text-sm">Try adjusting your search terms</p>
                    )}
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
