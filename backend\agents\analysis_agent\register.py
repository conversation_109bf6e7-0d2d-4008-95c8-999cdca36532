"""
Component registration for the analysis agent.

This module registers analysis components with the component registry.
"""

import logging
from agents.components.registry import ComponentRegistry
from .components import (
    AnalysisLLMComponent,
    AnalysisParserComponent,
    DataLoaderComponent,
    AnalysisExecutorComponent,
    SemanticLayerComponent,
    PandasAITrainingComponent
)

logger = logging.getLogger(__name__)


def register_analysis_components():
    """Register all analysis components with the registry."""
    # Register analysis LLM component
    ComponentRegistry.register("analysis_llm", AnalysisLLMComponent)
    logger.info("Registered AnalysisLLMComponent with component registry")

    # Register analysis parser component
    ComponentRegistry.register("analysis_parser", AnalysisParserComponent)
    logger.info("Registered AnalysisParserComponent with component registry")

    # Register data loader component
    ComponentRegistry.register("data_loader", DataLoaderComponent)
    logger.info("Registered DataLoaderComponent with component registry")

    # Register analysis executor component
    ComponentRegistry.register("analysis_executor", AnalysisExecutorComponent)
    logger.info("Registered AnalysisExecutorComponent with component registry")

    # Register semantic layer component
    ComponentRegistry.register("semantic_layer", SemanticLayerComponent)
    logger.info("Registered SemanticLayerComponent with component registry")

    # Register PandasAI training component
    ComponentRegistry.register("pandasai_training", PandasAITrainingComponent)
    logger.info("Registered PandasAITrainingComponent with component registry")

    # Log the registered components
    component_names = ComponentRegistry.list_registered_components()
    logger.info(f"Registered analysis components: {component_names}")
