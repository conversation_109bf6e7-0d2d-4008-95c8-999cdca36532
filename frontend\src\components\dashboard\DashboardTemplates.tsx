/**
 * Dashboard Templates Component
 * 
 * Provides pre-built dashboard templates for common use cases.
 * Users can select and apply templates to quickly set up dashboards.
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  BarChart3,
  TrendingUp,
  Users,
  Database,
  Activity,
  PieChart,
  LineChart,
  Table,
  Gauge,
  Eye,
  Zap,
} from 'lucide-react';
import { useDashboardManagement, useDashboardLayout } from '@/hooks/use-dashboard-management';
import { VisualizationType } from '@/types/dashboard-customization';

interface DashboardTemplate {
  id: string;
  name: string;
  description: string;
  category: 'analytics' | 'executive' | 'operational' | 'marketing';
  icon: React.ComponentType<any>;
  preview: string;
  sections: Array<{
    name: string;
    description: string;
    color?: string;
    icon?: string;
    widgets: Array<{
      title: string;
      type: VisualizationType;
      width: number;
      height: number;
      dataSource?: string;
      config?: Record<string, any>;
    }>;
  }>;
}

interface DashboardTemplatesProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTemplateApplied?: () => void;
}

const DASHBOARD_TEMPLATES: DashboardTemplate[] = [
  {
    id: 'analytics-overview',
    name: 'Analytics Overview',
    description: 'Comprehensive analytics dashboard with key metrics, trends, and user insights',
    category: 'analytics',
    icon: BarChart3,
    preview: '/templates/analytics-preview.png',
    sections: [
      {
        name: 'Key Metrics',
        description: 'Essential KPIs and performance indicators',
        color: '#3B82F6',
        icon: 'gauge',
        widgets: [
          {
            title: 'Total Users',
            type: VisualizationType.KPI,
            width: 3,
            height: 2,
            dataSource: 'platform-users',
            config: { valueColumn: 'total_platform_users', format: 'number' },
          },
          {
            title: 'Active Conversations',
            type: VisualizationType.KPI,
            width: 3,
            height: 2,
            dataSource: 'platform-metrics',
            config: { valueColumn: 'total_conversations', format: 'number' },
          },
          {
            title: 'Message Volume',
            type: VisualizationType.KPI,
            width: 3,
            height: 2,
            dataSource: 'platform-engagement',
            config: { valueColumn: 'current_messages', format: 'number' },
          },
          {
            title: 'Growth Rate',
            type: VisualizationType.GAUGE,
            width: 3,
            height: 2,
            dataSource: 'platform-users',
            config: { valueColumn: 'new_users_this_month', max: 1000 },
          },
        ],
      },
      {
        name: 'Trends & Analytics',
        description: 'Historical trends and detailed analytics',
        color: '#10B981',
        icon: 'chart',
        widgets: [
          {
            title: 'Message Trends',
            type: VisualizationType.CHART,
            width: 6,
            height: 4,
            dataSource: 'platform-engagement',
            config: {
              chartType: 'line',
              labelColumn: 'month',
              valueColumns: ['messages'],
            },
          },
          {
            title: 'User Segments',
            type: VisualizationType.CHART,
            width: 6,
            height: 4,
            dataSource: 'platform-users',
            config: {
              chartType: 'pie',
              labelColumn: 'segment',
              valueColumns: ['count'],
            },
          },
        ],
      },
    ],
  },
  {
    id: 'executive-summary',
    name: 'Executive Summary',
    description: 'High-level overview for executives with key business metrics',
    category: 'executive',
    icon: TrendingUp,
    preview: '/templates/executive-preview.png',
    sections: [
      {
        name: 'Business Performance',
        description: 'Key business performance indicators',
        color: '#8B5CF6',
        icon: 'gauge',
        widgets: [
          {
            title: 'Platform Health',
            type: VisualizationType.GAUGE,
            width: 4,
            height: 3,
            dataSource: 'platform-metrics',
            config: { valueColumn: 'active_users', max: 10000 },
          },
          {
            title: 'User Engagement',
            type: VisualizationType.GAUGE,
            width: 4,
            height: 3,
            dataSource: 'platform-engagement',
            config: { valueColumn: 'current_messages', max: 50000 },
          },
          {
            title: 'Growth Metrics',
            type: VisualizationType.GAUGE,
            width: 4,
            height: 3,
            dataSource: 'platform-users',
            config: { valueColumn: 'new_users_this_month', max: 1000 },
          },
        ],
      },
      {
        name: 'Strategic Insights',
        description: 'Strategic insights and recommendations',
        color: '#F59E0B',
        icon: 'chart',
        widgets: [
          {
            title: 'User Distribution',
            type: VisualizationType.CHART,
            width: 12,
            height: 4,
            dataSource: 'platform-users',
            config: {
              chartType: 'bar',
              labelColumn: 'segment',
              valueColumns: ['count', 'percentage'],
            },
          },
        ],
      },
    ],
  },
  {
    id: 'operational-monitoring',
    name: 'Operational Monitoring',
    description: 'Real-time operational metrics and system health monitoring',
    category: 'operational',
    icon: Activity,
    preview: '/templates/operational-preview.png',
    sections: [
      {
        name: 'System Health',
        description: 'Real-time system health and performance',
        color: '#EF4444',
        icon: 'gauge',
        widgets: [
          {
            title: 'Active Sessions',
            type: VisualizationType.KPI,
            width: 3,
            height: 2,
            dataSource: 'platform-metrics',
            config: { valueColumn: 'active_users', format: 'number' },
          },
          {
            title: 'Message Rate',
            type: VisualizationType.KPI,
            width: 3,
            height: 2,
            dataSource: 'platform-engagement',
            config: { valueColumn: 'current_messages', format: 'number' },
          },
          {
            title: 'Response Time',
            type: VisualizationType.GAUGE,
            width: 6,
            height: 2,
            dataSource: 'platform-metrics',
            config: { valueColumn: 'total_conversations', max: 100 },
          },
        ],
      },
      {
        name: 'Activity Monitoring',
        description: 'Detailed activity logs and monitoring',
        color: '#06B6D4',
        icon: 'table',
        widgets: [
          {
            title: 'Recent Activity',
            type: VisualizationType.TABLE,
            width: 12,
            height: 6,
            dataSource: 'platform-metrics',
            config: { pageSize: 20 },
          },
        ],
      },
    ],
  },
  {
    id: 'data-explorer',
    name: 'Data Explorer',
    description: 'Flexible data exploration dashboard for analyzing uploaded datasets',
    category: 'analytics',
    icon: Database,
    preview: '/templates/data-explorer-preview.png',
    sections: [
      {
        name: 'Data Overview',
        description: 'Overview of your data sources',
        color: '#84CC16',
        icon: 'table',
        widgets: [
          {
            title: 'Data Summary',
            type: VisualizationType.TABLE,
            width: 6,
            height: 4,
            config: { pageSize: 10 },
          },
          {
            title: 'Data Distribution',
            type: VisualizationType.CHART,
            width: 6,
            height: 4,
            config: {
              chartType: 'bar',
            },
          },
        ],
      },
      {
        name: 'Custom Analysis',
        description: 'Custom charts and analysis',
        color: '#EC4899',
        icon: 'chart',
        widgets: [
          {
            title: 'Custom Chart 1',
            type: VisualizationType.CHART,
            width: 6,
            height: 4,
            config: { chartType: 'line' },
          },
          {
            title: 'Custom Chart 2',
            type: VisualizationType.CHART,
            width: 6,
            height: 4,
            config: { chartType: 'pie' },
          },
        ],
      },
    ],
  },
];

export const DashboardTemplates: React.FC<DashboardTemplatesProps> = ({
  open,
  onOpenChange,
  onTemplateApplied,
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<DashboardTemplate | null>(null);
  const [dashboardName, setDashboardName] = useState('');
  const [isApplying, setIsApplying] = useState(false);

  const { createDashboard } = useDashboardManagement();
  const { createSection, createWidget } = useDashboardLayout();

  const handleTemplateSelect = (template: DashboardTemplate) => {
    setSelectedTemplate(template);
    setDashboardName(template.name);
  };

  const handleApplyTemplate = async () => {
    if (!selectedTemplate || !dashboardName.trim()) return;

    setIsApplying(true);
    try {
      // Create new dashboard
      const dashboard = await createDashboard({
        name: dashboardName,
        description: selectedTemplate.description,
        is_default: false,
      });

      // Create sections and widgets
      for (const sectionTemplate of selectedTemplate.sections) {
        const section = await createSection({
          name: sectionTemplate.name,
          description: sectionTemplate.description,
          color: sectionTemplate.color,
          icon: sectionTemplate.icon,
          layout_config: {
            columns: 12,
            rows: 8,
            grid_gap: 16,
            responsive: true,
          },
        });

        // Create widgets for this section
        for (const widgetTemplate of sectionTemplate.widgets) {
          await createWidget({
            section_id: section.id,
            title: widgetTemplate.title,
            widget_type: widgetTemplate.type,
            position_config: {
              x: 0,
              y: 0,
              w: widgetTemplate.width,
              h: widgetTemplate.height,
            },
            data_config: widgetTemplate.dataSource ? {
              dataSourceId: widgetTemplate.dataSource,
              ...widgetTemplate.config,
            } : undefined,
            visualization_config: widgetTemplate.config,
          });
        }
      }

      onTemplateApplied?.();
      onOpenChange(false);
      setSelectedTemplate(null);
      setDashboardName('');
    } catch (error) {
      console.error('Failed to apply template:', error);
    } finally {
      setIsApplying(false);
    }
  };

  const groupedTemplates = DASHBOARD_TEMPLATES.reduce((acc, template) => {
    if (!acc[template.category]) {
      acc[template.category] = [];
    }
    acc[template.category].push(template);
    return acc;
  }, {} as Record<string, DashboardTemplate[]>);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Dashboard Templates</DialogTitle>
          <DialogDescription>
            Choose from pre-built dashboard templates to quickly set up your analytics.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {Object.entries(groupedTemplates).map(([category, templates]) => (
            <div key={category} className="space-y-3">
              <h4 className="font-semibold capitalize">{category} Dashboards</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templates.map((template) => (
                  <Card
                    key={template.id}
                    className={`cursor-pointer transition-all ${
                      selectedTemplate?.id === template.id
                        ? 'border-primary bg-primary/5'
                        : 'hover:border-primary/50'
                    }`}
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center space-x-2">
                        <template.icon className="h-5 w-5 text-primary" />
                        <span>{template.name}</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        {template.description}
                      </p>
                      <div className="space-y-2">
                        <div className="text-xs font-medium">Includes:</div>
                        <div className="flex flex-wrap gap-1">
                          {template.sections.map((section, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {section.name} ({section.widgets.length} widgets)
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>

        {selectedTemplate && (
          <div className="space-y-4 pt-4 border-t">
            <div className="space-y-2">
              <Label htmlFor="dashboard-name">Dashboard Name</Label>
              <Input
                id="dashboard-name"
                value={dashboardName}
                onChange={(e) => setDashboardName(e.target.value)}
                placeholder="Enter dashboard name"
              />
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleApplyTemplate}
            disabled={!selectedTemplate || !dashboardName.trim() || isApplying}
          >
            {isApplying ? 'Creating...' : 'Create Dashboard'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
