import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ThumbsUp, ThumbsDown } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useMessageFeedback } from '@/hooks/use-feedback'; // Import the hook
import { Loader2 } from 'lucide-react'; // For loading state

// Define props based on usage in the snippet
interface MessageFeedbackProps {
  messageId: string;
  conversationId?: string; // Optional: pass if available for better context
  personaId?: string;    // Optional: pass if available
  // onFeedbackSubmitted prop can be kept if parent needs to know, or removed if all logic is internal
}

interface FeedbackDialogProps {
  messageId: string;
  initialRating: number;
  // onSubmit will be the function that calls the mutation from the hook
  onSubmit: (messageId: string, rating: number, feedbackText: string) => void;
  onClose: () => void;
}

// Basic FeedbackDialog (can be expanded or moved to its own file)
const FeedbackDialog: React.FC<FeedbackDialogProps> = ({ messageId, initialRating, onSubmit, onClose }) => {
  const [rating, setRating] = useState(initialRating);
  const [text, setText] = useState('');

  const handleSubmit = () => {
    onSubmit(messageId, rating, text);
    onClose();
  };

  return (
    <Dialog open={true} onOpenChange={(open) => !open && onClose()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Provide Feedback (Message ID: {messageId.substring(0,8)}...)</DialogTitle>
        </DialogHeader>
        <div className="py-4 space-y-4">
          <div>
            <Label htmlFor="rating">Rating (Current: {rating})</Label>
            {/* A proper star rating component would be better here */}
            <div className="flex gap-1 mt-1">
              {[1, 2, 3, 4, 5].map(star => (
                <Button 
                  key={star} 
                  variant={rating === star ? "default" : "outline"} 
                  size="sm"
                  onClick={() => setRating(star)}
                >
                  {star}
                </Button>
              ))}
            </div>
          </div>
          <div>
            <Label htmlFor="feedbackText">Additional Comments (Optional)</Label>
            <Textarea 
              id="feedbackText" 
              value={text} 
              onChange={(e) => setText(e.target.value)} 
              placeholder="Tell us more..."
            />
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline" onClick={onClose}>Cancel</Button>
          </DialogClose>
          <Button onClick={handleSubmit}>Submit Feedback</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};


export const MessageFeedback: React.FC<MessageFeedbackProps> = ({ messageId, conversationId, personaId }) => {
  const { mutate: submitFeedback, isPending: isLoading, isError, error } = useMessageFeedback();
  const [currentRating, setCurrentRating] = useState<number | null>(null);
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [feedbackDialogOpenForRating, setFeedbackDialogOpenForRating] = useState<number>(1);

  const handleRate = (rating: number) => {
    setCurrentRating(rating); // Optimistically update UI
    if (rating === 5) { // Thumbs up
      submitFeedback({ message_id: messageId, rating, conversation_id: conversationId, persona_id: personaId });
    } else { // Thumbs down or requires text
      setFeedbackDialogOpenForRating(rating);
      setShowFeedbackDialog(true);
    }
  };
  
  const handleDialogSubmit = (msgId: string, rating: number, feedbackText: string) => {
    submitFeedback({ message_id: msgId, rating, feedback_text: feedbackText, conversation_id: conversationId, persona_id: personaId });
    setShowFeedbackDialog(false);
    // currentRating will be updated by the successful mutation or if an error occurs, it can be reset
  };

  // Reset currentRating if submission fails for a thumbs up
  // This is a simple way; more complex logic might involve tracking submission status per rating
  React.useEffect(() => {
    if (isError && currentRating === 5) {
      setCurrentRating(null); 
    }
  }, [isError, currentRating]);

  return (
    <div className="flex items-center gap-1 mt-1">
      <Button 
        variant="ghost" 
        size="icon" 
        className={`h-7 w-7 ${currentRating === 5 ? 'text-green-500 bg-green-100/50' : 'text-muted-foreground hover:text-green-500 hover:bg-green-100/50'}`}
        onClick={() => handleRate(5)}
        title="Good response"
        disabled={isLoading && currentRating === 5}
      >
        {isLoading && currentRating === 5 ? <Loader2 className="h-4 w-4 animate-spin" /> : <ThumbsUp className="h-4 w-4" />}
      </Button>
      <Button 
        variant="ghost" 
        size="icon" 
        className={`h-7 w-7 ${currentRating !== null && currentRating < 3 && currentRating !== 5 ? 'text-red-500 bg-red-100/50' : 'text-muted-foreground hover:text-red-500 hover:bg-red-100/50'}`}
        onClick={() => handleRate(1)} // Default to 1 for thumbs down, dialog allows change
        title="Bad response"
        disabled={isLoading && currentRating === 1 && showFeedbackDialog} // Disable if dialog is for this rating and loading
      >
         {/* Show loader in dialog submit button instead if dialog is open */}
        <ThumbsDown className="h-4 w-4" />
      </Button>

      {showFeedbackDialog && (
        <FeedbackDialog
          messageId={messageId}
          initialRating={feedbackDialogOpenForRating}
          onSubmit={handleDialogSubmit}
          onClose={() => setShowFeedbackDialog(false)}
        />
      )}
      {isError && <p className="text-xs text-red-500 ml-1">Error submitting feedback.</p>}
    </div>
  );
};
