"""
Advanced query MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for executing advanced SQL-like queries on data.
"""

import logging
import os
import json
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
import re

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class AdvancedQueryTool(BaseMCPTool):
    """Tool for executing advanced SQL-like queries on data."""

    def __init__(self):
        """Initialize the advanced query tool."""
        super().__init__(
            name="advanced_query",
            description="Execute advanced SQL-like queries on data",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "query": {"type": "string"}
                },
                "required": ["file_path", "query"]
            },
            annotations={
                "title": "Advanced Query",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.data_dir = "data"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        if "data_dir" in config:
            self.data_dir = config["data_dir"]
            logger.info(f"Set data directory to: {self.data_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            file_path = arguments["file_path"]
            query = arguments["query"]

            # Check if the path is relative and prepend the data directory
            if not os.path.isabs(file_path):
                file_path = os.path.join(self.data_dir, file_path)

            # Check if the file exists
            if not os.path.exists(file_path):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }

            # Load the data
            if file_path.endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            # Parse and execute the query
            result_df = self._execute_query(df, query)
            
            if result_df is None:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Failed to execute query. Please check the query syntax."
                        }
                    ]
                }
            
            # Convert the result to a string representation
            result_str = result_df.to_string()
            
            # Create a preview of the result
            preview = result_df.head(10).to_dict(orient="records")
            
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Query executed successfully. Result:\n\n{result_str}"
                    }
                ],
                "metadata": {
                    "query": query,
                    "result_preview": preview,
                    "result_shape": result_df.shape
                }
            }
            
        except Exception as e:
            logger.error(f"Error executing advanced query: {str(e)}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error executing advanced query: {str(e)}"
                    }
                ]
            }
    
    def _execute_query(self, df: pd.DataFrame, query: str) -> Optional[pd.DataFrame]:
        """
        Execute a SQL-like query on a DataFrame.

        Args:
            df: The DataFrame to query
            query: The SQL-like query to execute

        Returns:
            The result DataFrame or None if the query could not be executed
        """
        # Make a copy of the DataFrame to avoid modifying the original
        result_df = df.copy()
        
        try:
            # Check for SELECT clause
            select_match = re.search(r'SELECT\s+(.*?)\s+FROM', query, re.IGNORECASE)
            if select_match:
                select_cols = select_match.group(1).strip()
                
                # Handle SELECT *
                if select_cols == '*':
                    pass  # Keep all columns
                else:
                    # Parse column list
                    cols = [col.strip() for col in select_cols.split(',')]
                    
                    # Handle aggregation functions
                    agg_funcs = {}
                    selected_cols = []
                    
                    for col in cols:
                        # Check for aggregation functions
                        agg_match = re.search(r'(COUNT|SUM|AVG|MIN|MAX)\((.*?)\)', col, re.IGNORECASE)
                        if agg_match:
                            func = agg_match.group(1).upper()
                            agg_col = agg_match.group(2).strip()
                            
                            if func == 'COUNT':
                                agg_funcs[col] = ('count', agg_col)
                            elif func == 'SUM':
                                agg_funcs[col] = ('sum', agg_col)
                            elif func == 'AVG':
                                agg_funcs[col] = ('mean', agg_col)
                            elif func == 'MIN':
                                agg_funcs[col] = ('min', agg_col)
                            elif func == 'MAX':
                                agg_funcs[col] = ('max', agg_col)
                        else:
                            selected_cols.append(col)
                    
                    # If we have aggregation functions but no GROUP BY, apply them to the entire DataFrame
                    if agg_funcs and not re.search(r'GROUP\s+BY', query, re.IGNORECASE):
                        agg_results = {}
                        for col_name, (func, col) in agg_funcs.items():
                            if func == 'count':
                                agg_results[col_name] = [df[col].count()]
                            elif func == 'sum':
                                agg_results[col_name] = [df[col].sum()]
                            elif func == 'mean':
                                agg_results[col_name] = [df[col].mean()]
                            elif func == 'min':
                                agg_results[col_name] = [df[col].min()]
                            elif func == 'max':
                                agg_results[col_name] = [df[col].max()]
                        
                        result_df = pd.DataFrame(agg_results)
                    else:
                        # Select columns
                        if selected_cols:
                            result_df = result_df[selected_cols]
            
            # Check for WHERE clause
            where_match = re.search(r'WHERE\s+(.*?)(?:\s+GROUP\s+BY|\s+ORDER\s+BY|\s*$)', query, re.IGNORECASE)
            if where_match:
                where_clause = where_match.group(1).strip()
                
                # Parse conditions
                conditions = []
                
                # Split by AND/OR
                parts = re.split(r'\s+(AND|OR)\s+', where_clause, flags=re.IGNORECASE)
                
                i = 0
                while i < len(parts):
                    condition = parts[i].strip()
                    
                    # Parse condition
                    condition_match = re.search(r'(.*?)\s*(=|!=|<>|>|<|>=|<=|LIKE|IN)\s*(.*)', condition, re.IGNORECASE)
                    if condition_match:
                        col = condition_match.group(1).strip()
                        op = condition_match.group(2).strip().upper()
                        val = condition_match.group(3).strip()
                        
                        # Remove quotes from string values
                        if (val.startswith("'") and val.endswith("'")) or (val.startswith('"') and val.endswith('"')):
                            val = val[1:-1]
                        
                        # Apply condition
                        if op == '=':
                            mask = result_df[col] == val
                        elif op in ['!=', '<>']:
                            mask = result_df[col] != val
                        elif op == '>':
                            mask = result_df[col] > float(val)
                        elif op == '<':
                            mask = result_df[col] < float(val)
                        elif op == '>=':
                            mask = result_df[col] >= float(val)
                        elif op == '<=':
                            mask = result_df[col] <= float(val)
                        elif op == 'LIKE':
                            # Convert SQL LIKE pattern to regex
                            pattern = val.replace('%', '.*').replace('_', '.')
                            mask = result_df[col].astype(str).str.match(f'^{pattern}$', case=False)
                        elif op == 'IN':
                            # Parse IN list
                            in_vals = [v.strip() for v in val.strip('()').split(',')]
                            mask = result_df[col].isin(in_vals)
                        else:
                            mask = pd.Series(True, index=result_df.index)
                        
                        conditions.append(mask)
                    else:
                        conditions.append(pd.Series(True, index=result_df.index))
                    
                    # Check if we have a logical operator
                    if i + 1 < len(parts):
                        logical_op = parts[i + 1].upper()
                        i += 2
                    else:
                        break
                
                # Apply conditions
                if conditions:
                    final_mask = conditions[0]
                    
                    i = 1
                    j = 1
                    while i < len(conditions) and j < len(parts):
                        if parts[j].upper() == 'AND':
                            final_mask = final_mask & conditions[i]
                        elif parts[j].upper() == 'OR':
                            final_mask = final_mask | conditions[i]
                        
                        i += 1
                        j += 2
                    
                    result_df = result_df[final_mask]
            
            # Check for GROUP BY clause
            group_match = re.search(r'GROUP\s+BY\s+(.*?)(?:\s+ORDER\s+BY|\s*$)', query, re.IGNORECASE)
            if group_match:
                group_cols = [col.strip() for col in group_match.group(1).split(',')]
                
                # Apply aggregation if we have aggregation functions
                if agg_funcs:
                    agg_dict = {}
                    for col_name, (func, col) in agg_funcs.items():
                        agg_dict[col] = func
                    
                    result_df = result_df.groupby(group_cols).agg(agg_dict).reset_index()
                else:
                    # If no aggregation functions, just group and count
                    result_df = result_df.groupby(group_cols).size().reset_index(name='count')
            
            # Check for ORDER BY clause
            order_match = re.search(r'ORDER\s+BY\s+(.*?)(?:\s+LIMIT|\s*$)', query, re.IGNORECASE)
            if order_match:
                order_cols = []
                ascending = []
                
                for col_spec in order_match.group(1).split(','):
                    col_spec = col_spec.strip()
                    
                    # Check for ASC/DESC
                    if ' ASC' in col_spec.upper():
                        col = col_spec.split(' ')[0].strip()
                        order_cols.append(col)
                        ascending.append(True)
                    elif ' DESC' in col_spec.upper():
                        col = col_spec.split(' ')[0].strip()
                        order_cols.append(col)
                        ascending.append(False)
                    else:
                        order_cols.append(col_spec)
                        ascending.append(True)
                
                result_df = result_df.sort_values(by=order_cols, ascending=ascending)
            
            # Check for LIMIT clause
            limit_match = re.search(r'LIMIT\s+(\d+)', query, re.IGNORECASE)
            if limit_match:
                limit = int(limit_match.group(1))
                result_df = result_df.head(limit)
            
            return result_df
            
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}", exc_info=True)
            return None
