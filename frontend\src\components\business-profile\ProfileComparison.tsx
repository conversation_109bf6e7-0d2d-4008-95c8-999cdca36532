import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowLeftRight, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Target, 
  Zap,
  MessageSquare,
  Clock,
  BarChart3
} from 'lucide-react';
import { BusinessProfile } from '@/lib/businessProfileApi';

interface ProfileComparisonProps {
  profiles: BusinessProfile[];
  activeProfile: BusinessProfile | null;
  onProfileSwitch: (profileId: string) => void;
  className?: string;
}

interface ComparisonMetric {
  key: string;
  label: string;
  icon: React.ComponentType<any>;
  format: (value: number) => string;
}

const COMPARISON_METRICS: ComparisonMetric[] = [
  {
    key: 'relevance',
    label: 'AI Response Relevance',
    icon: Target,
    format: (value) => `${value}%`
  },
  {
    key: 'satisfaction',
    label: 'User Satisfaction',
    icon: Users,
    format: (value) => `${value}%`
  },
  {
    key: 'effectiveness',
    label: 'Agent Effectiveness',
    icon: Zap,
    format: (value) => `${value}%`
  },
  {
    key: 'responseTime',
    label: 'Avg Response Time',
    icon: Clock,
    format: (value) => `${value}s`
  },
  {
    key: 'conversations',
    label: 'Total Conversations',
    icon: MessageSquare,
    format: (value) => value.toString()
  }
];

export const ProfileComparison: React.FC<ProfileComparisonProps> = ({
  profiles,
  activeProfile,
  onProfileSwitch,
  className
}) => {
  const [selectedProfile1, setSelectedProfile1] = useState<string>('');
  const [selectedProfile2, setSelectedProfile2] = useState<string>('');
  const [comparisonData, setComparisonData] = useState<Record<string, any>>({});

  useEffect(() => {
    if (profiles.length >= 2) {
      setSelectedProfile1(profiles[0].id);
      setSelectedProfile2(profiles[1].id);
    }
  }, [profiles]);

  useEffect(() => {
    if (selectedProfile1 && selectedProfile2) {
      loadComparisonData();
    }
  }, [selectedProfile1, selectedProfile2]);

  const loadComparisonData = () => {
    // Mock comparison data - in real implementation, this would come from API
    const profile1 = profiles.find(p => p.id === selectedProfile1);
    const profile2 = profiles.find(p => p.id === selectedProfile2);

    if (!profile1 || !profile2) return;

    const isProfile1Default = profile1.context_metadata?.is_default_profile;
    const isProfile2Default = profile2.context_metadata?.is_default_profile;

    setComparisonData({
      [selectedProfile1]: {
        relevance: isProfile1Default ? 65 : 87,
        satisfaction: isProfile1Default ? 72 : 91,
        effectiveness: isProfile1Default ? 58 : 84,
        responseTime: isProfile1Default ? 3.2 : 2.1,
        conversations: isProfile1Default ? 45 : 156
      },
      [selectedProfile2]: {
        relevance: isProfile2Default ? 65 : 87,
        satisfaction: isProfile2Default ? 72 : 91,
        effectiveness: isProfile2Default ? 58 : 84,
        responseTime: isProfile2Default ? 3.2 : 2.1,
        conversations: isProfile2Default ? 45 : 156
      }
    });
  };

  const getProfile = (profileId: string) => {
    return profiles.find(p => p.id === profileId);
  };

  const getMetricComparison = (metric: ComparisonMetric) => {
    const value1 = comparisonData[selectedProfile1]?.[metric.key] || 0;
    const value2 = comparisonData[selectedProfile2]?.[metric.key] || 0;
    
    let winner = 'tie';
    if (metric.key === 'responseTime') {
      // Lower is better for response time
      winner = value1 < value2 ? 'profile1' : value1 > value2 ? 'profile2' : 'tie';
    } else {
      // Higher is better for other metrics
      winner = value1 > value2 ? 'profile1' : value1 < value2 ? 'profile2' : 'tie';
    }

    return { value1, value2, winner };
  };

  const getWinnerBadge = (winner: string) => {
    if (winner === 'tie') return null;
    return (
      <Badge variant="default" className="text-xs">
        {winner === 'profile1' ? 'Winner' : 'Winner'}
      </Badge>
    );
  };

  if (profiles.length < 2) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Profile Comparison</h3>
          <p className="text-muted-foreground mb-4">
            Create at least 2 business profiles to compare their performance
          </p>
          <Button variant="outline">
            Create Another Profile
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Profile Comparison</h3>
          <p className="text-sm text-muted-foreground">
            Compare performance metrics across different business profiles
          </p>
        </div>
        <ArrowLeftRight className="h-5 w-5 text-muted-foreground" />
      </div>

      {/* Profile Selectors */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Profile A</CardTitle>
          </CardHeader>
          <CardContent>
            <Select value={selectedProfile1} onValueChange={setSelectedProfile1}>
              <SelectTrigger>
                <SelectValue placeholder="Select profile" />
              </SelectTrigger>
              <SelectContent>
                {profiles.map((profile) => (
                  <SelectItem key={profile.id} value={profile.id}>
                    <div className="flex items-center gap-2">
                      <span>{profile.name}</span>
                      {profile.context_metadata?.is_default_profile && (
                        <Badge variant="secondary" className="text-xs">Default</Badge>
                      )}
                      {profile.id === activeProfile?.id && (
                        <Badge variant="default" className="text-xs">Active</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Profile B</CardTitle>
          </CardHeader>
          <CardContent>
            <Select value={selectedProfile2} onValueChange={setSelectedProfile2}>
              <SelectTrigger>
                <SelectValue placeholder="Select profile" />
              </SelectTrigger>
              <SelectContent>
                {profiles.map((profile) => (
                  <SelectItem key={profile.id} value={profile.id}>
                    <div className="flex items-center gap-2">
                      <span>{profile.name}</span>
                      {profile.context_metadata?.is_default_profile && (
                        <Badge variant="secondary" className="text-xs">Default</Badge>
                      )}
                      {profile.id === activeProfile?.id && (
                        <Badge variant="default" className="text-xs">Active</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      </div>

      {/* Comparison Results */}
      {selectedProfile1 && selectedProfile2 && comparisonData[selectedProfile1] && (
        <Card>
          <CardHeader>
            <CardTitle>Performance Comparison</CardTitle>
            <CardDescription>
              Side-by-side comparison of key performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {COMPARISON_METRICS.map((metric) => {
                const comparison = getMetricComparison(metric);
                const profile1 = getProfile(selectedProfile1);
                const profile2 = getProfile(selectedProfile2);

                return (
                  <div key={metric.key} className="space-y-3">
                    <div className="flex items-center gap-2">
                      <metric.icon className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{metric.label}</span>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">
                            {profile1?.name}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">
                              {metric.format(comparison.value1)}
                            </span>
                            {comparison.winner === 'profile1' && getWinnerBadge('profile1')}
                          </div>
                        </div>
                        <Progress 
                          value={metric.key === 'responseTime' ? 100 - (comparison.value1 * 10) : comparison.value1} 
                          className="h-2"
                        />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">
                            {profile2?.name}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">
                              {metric.format(comparison.value2)}
                            </span>
                            {comparison.winner === 'profile2' && getWinnerBadge('profile2')}
                          </div>
                        </div>
                        <Progress 
                          value={metric.key === 'responseTime' ? 100 - (comparison.value2 * 10) : comparison.value2} 
                          className="h-2"
                        />
                      </div>
                    </div>
                    
                    {metric !== COMPARISON_METRICS[COMPARISON_METRICS.length - 1] && (
                      <Separator />
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      {selectedProfile1 && selectedProfile2 && (
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => onProfileSwitch(selectedProfile1)}
            disabled={activeProfile?.id === selectedProfile1}
          >
            Switch to {getProfile(selectedProfile1)?.name}
          </Button>
          <Button 
            variant="outline" 
            onClick={() => onProfileSwitch(selectedProfile2)}
            disabled={activeProfile?.id === selectedProfile2}
          >
            Switch to {getProfile(selectedProfile2)?.name}
          </Button>
        </div>
      )}
    </div>
  );
};
