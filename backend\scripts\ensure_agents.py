"""
<PERSON><PERSON><PERSON> to ensure all critical agents are registered.

This script is designed to be run at server startup to ensure that all critical agents
are properly registered in the agent registry, regardless of whether their configuration
files were successfully loaded.
"""

import os
import sys
import logging
import importlib
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import the agent modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def ensure_critical_agents():
    """
    Ensure all critical agents are registered in the agent registry.
    """
    try:
        # Import the agent registry
        from agents.registry import AgentRegistry
        
        # Import the agent classes
        from agents.composable import ComposableAgent
        from agents.classification.composable_agent_mcp import ComposableClassificationAgent
        from agents.marketing_agent.composable_agent import ComposableMarketingAgent
        from agents.analysis_agent.composable_agent import ComposableAnalysisAgent
        
        # Log the current state
        logger.info("Current registered personas: %s", AgentRegistry.list_registered_personas())
        
        # Register the critical agents if they're not already registered
        critical_agents = {
            "composable-ai": ComposableAgent,
            "composable-classifier-ai": ComposableClassificationAgent,
            "composable-marketing-ai": ComposableMarketingAgent,
            "composable-analysis-ai": ComposableAnalysisAgent
        }
        
        for persona_id, agent_class in critical_agents.items():
            if persona_id not in AgentRegistry.list_registered_personas():
                AgentRegistry.register(persona_id, agent_class)
                logger.info("Registered %s for persona ID '%s'", agent_class.__name__, persona_id)
            else:
                logger.info("Persona ID '%s' already registered", persona_id)
        
        # Log the updated state
        logger.info("Updated registered personas: %s", AgentRegistry.list_registered_personas())
        
        return True
    except Exception as e:
        logger.error("Error ensuring critical agents: %s", str(e), exc_info=True)
        return False

if __name__ == "__main__":
    success = ensure_critical_agents()
    if success:
        logger.info("Successfully ensured critical agents are registered")
    else:
        logger.error("Failed to ensure critical agents are registered")
        sys.exit(1)
