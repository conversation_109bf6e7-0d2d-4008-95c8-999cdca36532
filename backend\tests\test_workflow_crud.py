"""
Tests for workflow CRUD operations.
"""
import pytest
import uuid
from datetime import datetime
from sqlalchemy.orm import Session

from app.crud.workflow_crud import (
    create_workflow_execution,
    get_workflow_execution,
    update_workflow_execution,
    delete_workflow_execution,
    create_workflow_task_execution,
    get_workflow_task_execution,
    update_workflow_task_execution,
    delete_workflow_task_execution,
    get_workflow_tasks,
    get_workflows_by_status,
    get_tasks_by_status
)
from schemas.db_schemas import (
    WorkflowCreate,
    WorkflowUpdate,
    TaskCreate,
    TaskUpdate,
    WorkflowStatusEnum,
    TaskStatusEnum
)
from app.models.workflow import WorkflowExecution, WorkflowTaskExecution


class TestWorkflowCRUD:
    """Test workflow CRUD operations."""

    def test_create_workflow_execution(self, db_session: Session):
        """Test creating a workflow execution."""
        workflow_data = WorkflowCreate(
            name="Test Workflow",
            description="A test workflow",
            status=WorkflowStatusEnum.PENDING,
            user_id="test_user",
            session_id="test_session",
            context={"test": "data"},
            workflow_metadata={"version": "1.0"}
        )
        
        workflow = create_workflow_execution(db_session, workflow_data)
        
        assert workflow.name == "Test Workflow"
        assert workflow.description == "A test workflow"
        assert workflow.status == WorkflowStatusEnum.PENDING
        assert workflow.user_id == "test_user"
        assert workflow.session_id == "test_session"
        assert workflow.context == {"test": "data"}
        assert workflow.workflow_metadata == {"version": "1.0"}
        assert workflow.created_at is not None
        assert workflow.updated_at is not None

    def test_get_workflow_execution(self, db_session: Session):
        """Test retrieving a workflow execution."""
        # Create a workflow first
        workflow_data = WorkflowCreate(
            name="Test Workflow",
            status=WorkflowStatusEnum.PENDING
        )
        created_workflow = create_workflow_execution(db_session, workflow_data)
        
        # Retrieve the workflow
        retrieved_workflow = get_workflow_execution(db_session, created_workflow.id)
        
        assert retrieved_workflow is not None
        assert retrieved_workflow.id == created_workflow.id
        assert retrieved_workflow.name == "Test Workflow"

    def test_update_workflow_execution(self, db_session: Session):
        """Test updating a workflow execution."""
        # Create a workflow first
        workflow_data = WorkflowCreate(
            name="Test Workflow",
            status=WorkflowStatusEnum.PENDING
        )
        created_workflow = create_workflow_execution(db_session, workflow_data)
        
        # Update the workflow
        update_data = WorkflowUpdate(
            status=WorkflowStatusEnum.RUNNING,
            workflow_metadata={"updated": True}
        )
        updated_workflow = update_workflow_execution(
            db_session, created_workflow.id, update_data
        )
        
        assert updated_workflow.status == WorkflowStatusEnum.RUNNING
        assert updated_workflow.workflow_metadata == {"updated": True}
        assert updated_workflow.updated_at > created_workflow.updated_at

    def test_delete_workflow_execution(self, db_session: Session):
        """Test deleting a workflow execution."""
        # Create a workflow first
        workflow_data = WorkflowCreate(
            name="Test Workflow",
            status=WorkflowStatusEnum.PENDING
        )
        created_workflow = create_workflow_execution(db_session, workflow_data)
        
        # Delete the workflow
        result = delete_workflow_execution(db_session, created_workflow.id)
        assert result is True
        
        # Verify it's deleted
        retrieved_workflow = get_workflow_execution(db_session, created_workflow.id)
        assert retrieved_workflow is None

    def test_create_workflow_task_execution(self, db_session: Session):
        """Test creating a workflow task execution."""
        # Create a workflow first
        workflow_data = WorkflowCreate(
            name="Test Workflow",
            status=WorkflowStatusEnum.PENDING
        )
        workflow = create_workflow_execution(db_session, workflow_data)
        
        # Create a task
        task_data = TaskCreate(
            name="Test Task",
            agent_type="test_agent",
            input_data={"input": "test"},
            dependencies=["task1"],
            status=TaskStatusEnum.PENDING,
            task_metadata={"priority": "high"},
            workflow_id=workflow.id
        )
        
        task = create_workflow_task_execution(db_session, task_data, workflow.id)
        
        assert task.name == "Test Task"
        assert task.agent_type == "test_agent"
        assert task.input_data == {"input": "test"}
        assert task.dependencies == ["task1"]
        assert task.status == TaskStatusEnum.PENDING
        assert task.task_metadata == {"priority": "high"}
        assert task.workflow_id == workflow.id

    def test_get_workflow_tasks(self, db_session: Session):
        """Test retrieving tasks for a workflow."""
        # Create a workflow
        workflow_data = WorkflowCreate(
            name="Test Workflow",
            status=WorkflowStatusEnum.PENDING
        )
        workflow = create_workflow_execution(db_session, workflow_data)
        
        # Create multiple tasks
        for i in range(3):
            task_data = TaskCreate(
                name=f"Test Task {i}",
                agent_type="test_agent",
                input_data={"input": f"test{i}"},
                status=TaskStatusEnum.PENDING,
                workflow_id=workflow.id
            )
            create_workflow_task_execution(db_session, task_data, workflow.id)
        
        # Retrieve tasks
        tasks = get_workflow_tasks(db_session, workflow.id)
        
        assert len(tasks) == 3
        assert all(task.workflow_id == workflow.id for task in tasks)

    def test_get_workflows_by_status(self, db_session: Session):
        """Test retrieving workflows by status."""
        # Create workflows with different statuses
        for status in [WorkflowStatusEnum.PENDING, WorkflowStatusEnum.RUNNING, WorkflowStatusEnum.COMPLETED]:
            workflow_data = WorkflowCreate(
                name=f"Workflow {status.value}",
                status=status
            )
            create_workflow_execution(db_session, workflow_data)
        
        # Retrieve pending workflows
        pending_workflows = get_workflows_by_status(db_session, WorkflowStatusEnum.PENDING)
        assert len(pending_workflows) >= 1
        assert all(w.status == WorkflowStatusEnum.PENDING for w in pending_workflows)

    def test_get_tasks_by_status(self, db_session: Session):
        """Test retrieving tasks by status."""
        # Create a workflow
        workflow_data = WorkflowCreate(
            name="Test Workflow",
            status=WorkflowStatusEnum.PENDING
        )
        workflow = create_workflow_execution(db_session, workflow_data)
        
        # Create tasks with different statuses
        for status in [TaskStatusEnum.PENDING, TaskStatusEnum.RUNNING, TaskStatusEnum.COMPLETED]:
            task_data = TaskCreate(
                name=f"Task {status.value}",
                agent_type="test_agent",
                input_data={"input": "test"},
                status=status,
                workflow_id=workflow.id
            )
            create_workflow_task_execution(db_session, task_data, workflow.id)
        
        # Retrieve pending tasks
        pending_tasks = get_tasks_by_status(db_session, workflow.id, TaskStatusEnum.PENDING)
        assert len(pending_tasks) >= 1
        assert all(t.status == TaskStatusEnum.PENDING for t in pending_tasks)


@pytest.fixture
def db_session():
    """Create a test database session."""
    from app.database import SessionLocal, engine, Base
    
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()
        # Clean up tables
        Base.metadata.drop_all(bind=engine)
