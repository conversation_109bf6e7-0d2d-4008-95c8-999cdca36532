import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Settings,
  Database,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { MultiSelectDataSourceSelector } from './MultiSelectDataSourceSelector';
import { DataSourceAssignmentDisplay } from './DataSourceAssignmentDisplay';
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';
import { useDashboardManagement } from '@/hooks/use-dashboard-management';
import { useToast } from '@/hooks/use-toast';
import { DashboardDataSourceAssignment, DashboardDataSource } from '@/types/dashboard-customization';

interface DashboardDataSourceManagerProps {
  dashboardId: string;
  className?: string;
}

export const DashboardDataSourceManager: React.FC<DashboardDataSourceManagerProps> = ({
  dashboardId,
  className = '',
}) => {
  const { toast } = useToast();
  const [showMultiSelectDialog, setShowMultiSelectDialog] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Get dashboard data from store
  const {
    dashboards,
    refreshDashboardDataSources,
    addDashboardDataSource,
    updateDashboardDataSource,
    deleteDashboardDataSource
  } = useUnifiedDashboardStore();

  const { bulkAssignDataSources } = useDashboardManagement();

  // Find current dashboard
  const currentDashboard = dashboards.find(d => d.id === dashboardId);
  const dataSourceAssignments = currentDashboard?.data_source_assignments || [];

  console.log('Current dashboard:', currentDashboard);
  console.log('Data source assignments:', dataSourceAssignments);

  // Transform data source assignments to display format
  const transformDataSourceAssignments = (assignments: DashboardDataSourceAssignment[]): DashboardDataSource[] => {
    return assignments.map(assignment => ({
      id: assignment.id,
      name: assignment.alias || assignment.system_data_source?.name || 'Unknown Data Source',
      type: assignment.system_data_source?.type || 'unknown',
      description: assignment.system_data_source?.description,
      isActive: assignment.is_active,
      lastUpdated: assignment.updated_at,
      recordCount: assignment.system_data_source?.metadata?.record_count,
      refreshInterval: 300, // Default refresh interval
      alias: assignment.alias,
      connectionConfig: assignment.system_data_source?.metadata
    }));
  };

  const dataSources = transformDataSourceAssignments(dataSourceAssignments);

  // Handle multi-select data source assignment
  const handleMultiSelectDataSources = async (dataSourceIds: string[]) => {
    try {
      console.log('Bulk assigning data sources:', dataSourceIds);
      const result = await bulkAssignDataSources(dashboardId, dataSourceIds);
      console.log('Bulk assign result:', result);

      // Refresh data sources to show the new assignments
      console.log('Refreshing dashboard data sources...');
      await refreshDashboardDataSources(dashboardId);
      console.log('Refresh completed');

      toast({
        title: "Data Sources Added",
        description: `Successfully added ${dataSourceIds.length} data source${dataSourceIds.length !== 1 ? 's' : ''} to the dashboard.`,
      });
    } catch (error) {
      console.error('Error adding data sources:', error);
      toast({
        title: "Failed to Add Data Sources",
        description: error instanceof Error ? error.message : 'An error occurred while adding data sources.',
        variant: "destructive",
      });
    }
  };

  // Handle data source refresh
  const handleRefreshDataSource = async (dataSourceId: string) => {
    // This would typically trigger a data refresh for the specific data source
    // For now, we'll refresh the dashboard data sources
    await refreshDashboardDataSources(dashboardId);
  };

  // Handle data source removal
  const handleRemoveDataSource = async (dataSourceId: string) => {
    try {
      await deleteDashboardDataSource(dashboardId, dataSourceId);
      toast({
        title: "Data Source Removed",
        description: "Data source has been removed from the dashboard.",
      });
    } catch (error) {
      console.error('Error removing data source:', error);
      toast({
        title: "Failed to Remove Data Source",
        description: error instanceof Error ? error.message : 'An error occurred while removing the data source.',
        variant: "destructive",
      });
    }
  };

  // Handle refresh all data sources
  const handleRefreshAllDataSources = async () => {
    setIsRefreshing(true);
    try {
      await refreshDashboardDataSources(dashboardId);
      toast({
        title: "Data Sources Refreshed",
        description: "All data sources have been refreshed successfully.",
      });
    } catch (error) {
      console.error('Error refreshing data sources:', error);
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh data sources. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Get existing data source IDs for the multi-select dialog (use system_data_source_id)
  const existingDataSourceIds = dataSourceAssignments.map(assignment => assignment.system_data_source_id);

  if (!currentDashboard) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <AlertCircle className="h-6 w-6 text-muted-foreground mr-2" />
          <span>Dashboard not found</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Data Source Management</h2>
          <p className="text-muted-foreground">
            Manage data sources for "{currentDashboard.name}" dashboard
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleRefreshAllDataSources}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh All
          </Button>
          <Button onClick={() => setShowMultiSelectDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Data Sources
          </Button>
        </div>
      </div>

      {/* Dashboard Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Dashboard Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{dataSources.length}</div>
              <div className="text-sm text-muted-foreground">Data Sources</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {dataSources.filter(ds => ds.isActive).length}
              </div>
              <div className="text-sm text-muted-foreground">Active</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {dataSources.filter(ds => !ds.isActive).length}
              </div>
              <div className="text-sm text-muted-foreground">Inactive</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Sources Display */}
      <DataSourceAssignmentDisplay
        dataSources={dataSources}
        onAddDataSources={() => setShowMultiSelectDialog(true)}
        onRefreshDataSource={handleRefreshDataSource}
        onRemoveDataSource={handleRemoveDataSource}
      />

      {/* Multi-Select Dialog */}
      <MultiSelectDataSourceSelector
        open={showMultiSelectDialog}
        onOpenChange={setShowMultiSelectDialog}
        onDataSourcesSelected={handleMultiSelectDataSources}
        existingAssignments={existingDataSourceIds}
      />
    </div>
  );
};
