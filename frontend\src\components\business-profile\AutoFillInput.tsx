/**
 * Auto-fill Input Component
 * 
 * Provides file upload and URL input functionality for business profile auto-fill
 */

import React, { useState, useCallback } from 'react';
import { Upload, Link, X, FileText, AlertCircle, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  isSupportedFileType,
  getFileTypeDisplayName,
  isValidUrl,
  normalizeUrl,
  extractDomain
} from '@/lib/businessProfileAutoFillApi';

interface AutoFillInputProps {
  onFilesChange: (files: File[]) => void;
  onUrlsChange: (urls: string[]) => void;
  disabled?: boolean;
  maxFiles?: number;
  maxUrls?: number;
}

export const AutoFillInput: React.FC<AutoFillInputProps> = ({
  onFilesChange,
  onUrlsChange,
  disabled = false,
  maxFiles = 5,
  maxUrls = 3,
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [urls, setUrls] = useState<string[]>([]);
  const [urlInput, setUrlInput] = useState('');
  const [errors, setErrors] = useState<string[]>([]);

  // Handle file selection
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    const newErrors: string[] = [];

    // Validate files
    const validFiles = selectedFiles.filter(file => {
      if (!isSupportedFileType(file)) {
        newErrors.push(`${file.name}: Unsupported file type`);
        return false;
      }
      
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        newErrors.push(`${file.name}: File size exceeds 10MB limit`);
        return false;
      }
      
      return true;
    });

    // Check file limit
    const totalFiles = files.length + validFiles.length;
    if (totalFiles > maxFiles) {
      newErrors.push(`Maximum ${maxFiles} files allowed`);
      const allowedCount = maxFiles - files.length;
      validFiles.splice(allowedCount);
    }

    // Update state
    const updatedFiles = [...files, ...validFiles];
    setFiles(updatedFiles);
    setErrors(newErrors);
    onFilesChange(updatedFiles);

    // Clear input
    event.target.value = '';
  }, [files, maxFiles, onFilesChange]);

  // Handle file removal
  const handleFileRemove = useCallback((index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index);
    setFiles(updatedFiles);
    onFilesChange(updatedFiles);
  }, [files, onFilesChange]);

  // Handle URL addition
  const handleUrlAdd = useCallback(() => {
    if (!urlInput.trim()) return;

    const normalizedUrl = normalizeUrl(urlInput.trim());
    
    if (!isValidUrl(normalizedUrl)) {
      setErrors(prev => [...prev, 'Invalid URL format']);
      return;
    }

    if (urls.includes(normalizedUrl)) {
      setErrors(prev => [...prev, 'URL already added']);
      return;
    }

    if (urls.length >= maxUrls) {
      setErrors(prev => [...prev, `Maximum ${maxUrls} URLs allowed`]);
      return;
    }

    const updatedUrls = [...urls, normalizedUrl];
    setUrls(updatedUrls);
    setUrlInput('');
    onUrlsChange(updatedUrls);
    
    // Clear any previous errors
    setErrors([]);
  }, [urlInput, urls, maxUrls, onUrlsChange]);

  // Handle URL removal
  const handleUrlRemove = useCallback((index: number) => {
    const updatedUrls = urls.filter((_, i) => i !== index);
    setUrls(updatedUrls);
    onUrlsChange(updatedUrls);
  }, [urls, onUrlsChange]);

  // Handle URL input key press
  const handleUrlKeyPress = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleUrlAdd();
    }
  }, [handleUrlAdd]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Auto-fill Sources
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Upload documents or provide website URLs to automatically populate form fields
        </p>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Enhanced AI Processing:</strong> Documents are now processed using advanced AI embedding technology
            for more accurate field extraction and better business profile suggestions.
          </AlertDescription>
        </Alert>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* File Upload Section */}
        <div className="space-y-3">
          <Label htmlFor="file-upload" className="text-sm font-medium">
            Upload Documents
          </Label>
          <div className="flex items-center gap-3">
            <Input
              id="file-upload"
              type="file"
              multiple
              accept=".pdf,.docx,.doc,.txt,.csv,.xlsx,.xls"
              onChange={handleFileSelect}
              disabled={disabled || files.length >= maxFiles}
              className="flex-1"
            />
            <Badge variant="outline" className="text-xs">
              {files.length}/{maxFiles}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground">
            Supported formats: PDF, Word, Text, CSV, Excel (max 10MB each)
          </p>

          {/* File List */}
          {files.length > 0 && (
            <div className="space-y-2">
              {files.map((file, index) => (
                <div
                  key={`${file.name}-${index}`}
                  className="flex items-center justify-between p-2 bg-muted rounded-md"
                >
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">{file.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {getFileTypeDisplayName(file.name)} • {(file.size / 1024 / 1024).toFixed(1)} MB
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleFileRemove(index)}
                    disabled={disabled}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* URL Input Section */}
        <div className="space-y-3">
          <Label htmlFor="url-input" className="text-sm font-medium">
            Website URLs
          </Label>
          <div className="flex items-center gap-2">
            <div className="flex-1 flex items-center gap-2">
              <Link className="h-4 w-4 text-muted-foreground" />
              <Input
                id="url-input"
                type="url"
                placeholder="https://example.com"
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                onKeyPress={handleUrlKeyPress}
                disabled={disabled || urls.length >= maxUrls}
                className="flex-1"
              />
            </div>
            <Button
              onClick={handleUrlAdd}
              disabled={disabled || !urlInput.trim() || urls.length >= maxUrls}
              size="sm"
            >
              <Plus className="h-4 w-4" />
            </Button>
            <Badge variant="outline" className="text-xs">
              {urls.length}/{maxUrls}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground">
            Enter website URLs to extract business information
          </p>

          {/* URL List */}
          {urls.length > 0 && (
            <div className="space-y-2">
              {urls.map((url, index) => (
                <div
                  key={`${url}-${index}`}
                  className="flex items-center justify-between p-2 bg-muted rounded-md"
                >
                  <div className="flex items-center gap-2">
                    <Link className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">{extractDomain(url)}</p>
                      <p className="text-xs text-muted-foreground truncate max-w-xs">
                        {url}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleUrlRemove(index)}
                    disabled={disabled}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Error Display */}
        {errors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                {errors.map((error, index) => (
                  <div key={index}>{error}</div>
                ))}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearErrors}
                className="mt-2 h-auto p-0 text-xs"
              >
                Clear errors
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Summary */}
        {(files.length > 0 || urls.length > 0) && (
          <div className="p-3 bg-muted rounded-md">
            <p className="text-sm font-medium">Ready to process:</p>
            <div className="text-xs text-muted-foreground mt-1">
              {files.length > 0 && <span>{files.length} document{files.length !== 1 ? 's' : ''}</span>}
              {files.length > 0 && urls.length > 0 && <span> • </span>}
              {urls.length > 0 && <span>{urls.length} website{urls.length !== 1 ? 's' : ''}</span>}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
