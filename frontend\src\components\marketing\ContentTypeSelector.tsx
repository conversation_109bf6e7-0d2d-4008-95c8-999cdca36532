import React, { useState, useEffect, useMemo } from 'react';
import { Search, ChevronDown, Info, Sparkles } from 'lucide-react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// Content type definition interface
interface ContentTypeDefinition {
  id: string;
  name: string;
  description: string;
  category: 'strategic' | 'tactical' | 'operational' | 'creative';
  required_fields: string[];
  optional_fields: string[];
  supported_formats: string[];
  examples: string[];
  keywords: string[];
}

// Default content types (fallback if API fails)
const DEFAULT_CONTENT_TYPES: Record<string, ContentTypeDefinition> = {
  marketing_strategy: {
    id: 'marketing_strategy',
    name: 'Marketing Strategy',
    description: 'Comprehensive strategic marketing plan with market analysis, positioning, and implementation roadmap',
    category: 'strategic',
    required_fields: ['brand_description', 'target_audience', 'products_services', 'marketing_goals'],
    optional_fields: ['existing_content', 'keywords', 'suggested_topics', 'competitive_landscape'],
    supported_formats: ['report', 'presentation'],
    examples: ['Annual marketing strategy', 'Product launch strategy', 'Market entry strategy'],
    keywords: ['strategy', 'planning', 'market analysis', 'positioning', 'roadmap']
  },
  campaign_strategy: {
    id: 'campaign_strategy',
    name: 'Campaign Strategy',
    description: 'Creative campaign concepts with multi-channel approaches and execution plans',
    category: 'tactical',
    required_fields: ['brand_description', 'target_audience', 'marketing_goals'],
    optional_fields: ['products_services', 'keywords', 'suggested_topics', 'budget', 'timeline'],
    supported_formats: ['brief', 'presentation'],
    examples: ['Product launch campaign', 'Brand awareness campaign', 'Seasonal promotion'],
    keywords: ['campaign', 'creative', 'execution', 'channels', 'tactics']
  },
  social_media_content: {
    id: 'social_media_content',
    name: 'Social Media Content',
    description: 'Engaging social media posts and content strategy for various platforms',
    category: 'creative',
    required_fields: ['brand_description', 'target_audience'],
    optional_fields: ['products_services', 'existing_content', 'keywords', 'suggested_topics', 'platforms'],
    supported_formats: ['template', 'guide'],
    examples: ['Instagram posts', 'LinkedIn content', 'Twitter campaigns', 'Content calendar'],
    keywords: ['social media', 'posts', 'engagement', 'platforms', 'content']
  },
  seo_optimization: {
    id: 'seo_optimization',
    name: 'SEO Optimization',
    description: 'Search engine optimization strategy with keyword research and content recommendations',
    category: 'tactical',
    required_fields: ['brand_description', 'target_audience', 'keywords'],
    optional_fields: ['products_services', 'existing_content', 'competitive_landscape'],
    supported_formats: ['analysis', 'checklist'],
    examples: ['SEO audit', 'Keyword strategy', 'Content optimization', 'Technical SEO'],
    keywords: ['seo', 'keywords', 'optimization', 'search', 'ranking']
  },
  brand_positioning: {
    id: 'brand_positioning',
    name: 'Brand Positioning',
    description: 'Brand positioning and messaging framework with competitive differentiation',
    category: 'strategic',
    required_fields: ['brand_description', 'target_audience', 'competitive_landscape'],
    optional_fields: ['products_services', 'marketing_goals', 'keywords'],
    supported_formats: ['report', 'brief'],
    examples: ['Brand positioning statement', 'Messaging framework', 'Value proposition'],
    keywords: ['positioning', 'messaging', 'differentiation', 'value proposition']
  },
  content_calendar: {
    id: 'content_calendar',
    name: 'Content Calendar',
    description: 'Monthly content planning and scheduling with strategic themes',
    category: 'operational',
    required_fields: ['brand_description', 'target_audience', 'suggested_topics'],
    optional_fields: ['platforms', 'timeline', 'keywords', 'marketing_goals'],
    supported_formats: ['template', 'guide'],
    examples: ['Monthly content calendar', 'Editorial calendar', 'Social media schedule'],
    keywords: ['calendar', 'planning', 'scheduling', 'content', 'editorial']
  },
  blog_content: {
    id: 'blog_content',
    name: 'Blog Content',
    description: 'SEO-optimized blog posts and content strategy with engaging topics',
    category: 'creative',
    required_fields: ['brand_description', 'target_audience', 'suggested_topics'],
    optional_fields: ['products_services', 'keywords', 'existing_content', 'marketing_goals'],
    supported_formats: ['guide', 'template'],
    examples: ['How-to guides', 'Industry insights', 'Company updates', 'Thought leadership'],
    keywords: ['blog', 'content', 'seo', 'writing', 'articles']
  },
  email_marketing: {
    id: 'email_marketing',
    name: 'Email Marketing',
    description: 'Email campaigns, newsletters, and automation sequences for customer engagement',
    category: 'tactical',
    required_fields: ['brand_description', 'target_audience', 'marketing_goals'],
    optional_fields: ['products_services', 'existing_content', 'keywords', 'suggested_topics'],
    supported_formats: ['template', 'guide'],
    examples: ['Welcome series', 'Newsletter', 'Product announcements', 'Drip campaigns'],
    keywords: ['email', 'newsletter', 'automation', 'campaigns', 'nurturing']
  },
  ad_copy: {
    id: 'ad_copy',
    name: 'Ad Copy',
    description: 'Persuasive advertising copy for various platforms and campaign types',
    category: 'creative',
    required_fields: ['brand_description', 'target_audience', 'marketing_goals'],
    optional_fields: ['products_services', 'keywords', 'platforms', 'budget'],
    supported_formats: ['template', 'brief'],
    examples: ['Google Ads', 'Facebook ads', 'Display banners', 'Video ad scripts'],
    keywords: ['advertising', 'copy', 'ads', 'persuasive', 'conversion']
  },
  press_release: {
    id: 'press_release',
    name: 'Press Release',
    description: 'Professional press releases for announcements and media outreach',
    category: 'tactical',
    required_fields: ['brand_description', 'marketing_goals'],
    optional_fields: ['products_services', 'existing_content', 'keywords'],
    supported_formats: ['template', 'guide'],
    examples: ['Product launch', 'Company news', 'Partnership announcements', 'Awards'],
    keywords: ['press', 'media', 'announcement', 'news', 'pr']
  },
  competitor_analysis: {
    id: 'competitor_analysis',
    name: 'Competitor Analysis',
    description: 'Comprehensive analysis of competitors and market positioning',
    category: 'strategic',
    required_fields: ['brand_description', 'products_services', 'competitive_landscape'],
    optional_fields: ['target_audience', 'marketing_goals', 'keywords'],
    supported_formats: ['analysis', 'report'],
    examples: ['Market research', 'SWOT analysis', 'Competitive benchmarking', 'Gap analysis'],
    keywords: ['competitors', 'analysis', 'market', 'research', 'positioning']
  },
  audience_research: {
    id: 'audience_research',
    name: 'Audience Research',
    description: 'In-depth research and analysis of target audience behaviors and preferences',
    category: 'strategic',
    required_fields: ['brand_description', 'target_audience'],
    optional_fields: ['products_services', 'marketing_goals', 'existing_content'],
    supported_formats: ['analysis', 'report'],
    examples: ['Customer personas', 'Demographic analysis', 'Behavior studies', 'Survey insights'],
    keywords: ['audience', 'research', 'personas', 'demographics', 'behavior']
  },
  market_analysis: {
    id: 'market_analysis',
    name: 'Market Analysis',
    description: 'Market trends, opportunities, and strategic insights for business growth',
    category: 'strategic',
    required_fields: ['brand_description', 'products_services'],
    optional_fields: ['target_audience', 'competitive_landscape', 'marketing_goals'],
    supported_formats: ['analysis', 'report'],
    examples: ['Market trends', 'Industry analysis', 'Opportunity assessment', 'Growth strategies'],
    keywords: ['market', 'trends', 'analysis', 'opportunities', 'growth']
  },
  data_driven_analysis: {
    id: 'data_driven_analysis',
    name: 'Data-Driven Analysis',
    description: 'Marketing insights and recommendations based on your uploaded data',
    category: 'strategic',
    required_fields: ['brand_description'],
    optional_fields: ['target_audience', 'marketing_goals', 'existing_content'],
    supported_formats: ['analysis', 'report'],
    examples: ['Performance analysis', 'Customer insights', 'ROI analysis', 'Trend identification'],
    keywords: ['data', 'analytics', 'insights', 'performance', 'metrics']
  },
  data_driven_campaigns: {
    id: 'data_driven_campaigns',
    name: 'Data-Driven Campaigns',
    description: 'Campaign strategies and tactics based on your data insights',
    category: 'tactical',
    required_fields: ['brand_description', 'marketing_goals'],
    optional_fields: ['target_audience', 'products_services', 'budget', 'timeline'],
    supported_formats: ['brief', 'template'],
    examples: ['Personalized campaigns', 'Segmented targeting', 'Performance optimization', 'A/B test strategies'],
    keywords: ['campaigns', 'data', 'personalization', 'targeting', 'optimization']
  }
};

interface ContentTypeSelectorProps {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
}

export function ContentTypeSelector({ value, onChange, disabled = false, className }: ContentTypeSelectorProps) {
  const [open, setOpen] = useState(false);
  const [contentTypes, setContentTypes] = useState<Record<string, ContentTypeDefinition>>(DEFAULT_CONTENT_TYPES);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // Load content types from API
  useEffect(() => {
    const loadContentTypes = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/marketing/content-types');
        if (response.ok) {
          const data = await response.json();
          setContentTypes(data.content_types || DEFAULT_CONTENT_TYPES);
        }
      } catch (error) {
        console.error('Failed to load content types:', error);
        // Keep using default content types
      } finally {
        setLoading(false);
      }
    };

    loadContentTypes();
  }, []);

  // Filter and sort content types
  const filteredContentTypes = useMemo(() => {
    const types = Object.values(contentTypes);
    
    if (!searchValue) {
      return types;
    }

    const searchLower = searchValue.toLowerCase();
    return types.filter(type => 
      type.name.toLowerCase().includes(searchLower) ||
      type.description.toLowerCase().includes(searchLower) ||
      type.keywords.some(keyword => keyword.toLowerCase().includes(searchLower)) ||
      type.id.toLowerCase().includes(searchLower)
    );
  }, [contentTypes, searchValue]);

  // Group content types by category
  const groupedContentTypes = useMemo(() => {
    const groups: Record<string, ContentTypeDefinition[]> = {
      strategic: [],
      tactical: [],
      operational: [],
      creative: []
    };

    filteredContentTypes.forEach(type => {
      groups[type.category].push(type);
    });

    return groups;
  }, [filteredContentTypes]);

  const selectedContentType = contentTypes[value];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'strategic': return '🎯';
      case 'tactical': return '⚡';
      case 'operational': return '⚙️';
      case 'creative': return '🎨';
      default: return '📄';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'strategic': return 'bg-blue-100 text-blue-800';
      case 'tactical': return 'bg-green-100 text-green-800';
      case 'operational': return 'bg-orange-100 text-orange-800';
      case 'creative': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-auto p-3"
            disabled={disabled}
          >
            <div className="flex items-start space-x-3 text-left">
              <div className="text-2xl">
                {selectedContentType ? getCategoryIcon(selectedContentType.category) : '📄'}
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium">
                  {selectedContentType ? selectedContentType.name : 'Select content type...'}
                </div>
                {selectedContentType && (
                  <div className="text-sm text-muted-foreground mt-1 line-clamp-2">
                    {selectedContentType.description}
                  </div>
                )}
              </div>
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[500px] p-0" align="start">
          <Command>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <CommandInput
                placeholder="Search content types..."
                value={searchValue}
                onValueChange={setSearchValue}
                className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
            <CommandList className="max-h-[400px]">
              <CommandEmpty>
                <div className="text-center py-6">
                  <Sparkles className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">
                    No content types found. Try a different search term.
                  </p>
                </div>
              </CommandEmpty>
              
              {Object.entries(groupedContentTypes).map(([category, types]) => {
                if (types.length === 0) return null;
                
                return (
                  <CommandGroup key={category} heading={
                    <div className="flex items-center space-x-2">
                      <span>{getCategoryIcon(category)}</span>
                      <span className="capitalize font-medium">{category}</span>
                      <Badge variant="secondary" className={getCategoryColor(category)}>
                        {types.length}
                      </Badge>
                    </div>
                  }>
                    {types.map((type) => (
                      <CommandItem
                        key={type.id}
                        value={type.id}
                        onSelect={() => {
                          onChange(type.id);
                          setOpen(false);
                        }}
                        className="flex items-start space-x-3 p-3"
                      >
                        <div className="text-lg">{getCategoryIcon(type.category)}</div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium">{type.name}</div>
                          <div className="text-sm text-muted-foreground mt-1 line-clamp-2">
                            {type.description}
                          </div>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {type.examples.slice(0, 2).map((example, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {example}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        {value === type.id && (
                          <div className="text-primary">✓</div>
                        )}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                );
              })}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      
      {selectedContentType && (
        <div className="text-sm text-muted-foreground bg-muted/50 rounded-md p-3">
          <div className="flex items-center space-x-2 mb-2">
            <Info className="h-4 w-4" />
            <span className="font-medium">Content Type Details</span>
          </div>
          <div className="space-y-1">
            <div><strong>Category:</strong> {selectedContentType.category}</div>
            <div><strong>Formats:</strong> {selectedContentType.supported_formats.join(', ')}</div>
            <div><strong>Examples:</strong> {selectedContentType.examples.join(', ')}</div>
          </div>
        </div>
      )}
    </div>
  );
}
