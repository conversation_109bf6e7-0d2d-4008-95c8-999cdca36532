#!/usr/bin/env python3
"""
Chunking performance benchmarking script.

This script benchmarks different chunking strategies and provides
recommendations for optimal performance.
"""

import asyncio
import time
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any, List
import json

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.utils.adaptive_chunking import AdaptiveChunker, ContentType, ChunkingStrategy
from agents.utils.chunking_performance_manager import ChunkingPerformanceManager
from agents.utils.vector_service import VectorService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChunkingBenchmark:
    """Benchmarking suite for chunking strategies."""
    
    def __init__(self):
        """Initialize the benchmark suite."""
        self.adaptive_chunker = AdaptiveChunker()
        self.performance_manager = ChunkingPerformanceManager()
        self.vector_service = VectorService()
        self.results = []
    
    def create_test_documents(self) -> Dict[str, str]:
        """Create test documents of different types and sizes."""
        return {
            "technical_small": """
            # API Documentation
            
            ## Authentication
            The API uses OAuth 2.0 for authentication. You need to obtain an access token
            before making requests to protected endpoints.
            
            ## Endpoints
            
            ### GET /users
            Returns a list of users in the system.
            
            Parameters:
            - limit: Maximum number of users to return (default: 10)
            - offset: Number of users to skip (default: 0)
            
            Response:
            ```json
            {
                "users": [
                    {"id": 1, "name": "John Doe", "email": "<EMAIL>"}
                ],
                "total": 100
            }
            ```
            """,
            
            "narrative_medium": """
            The sun was setting over the horizon as Sarah walked along the beach, her mind
            wandering to the events of the past week. The conference had been a success,
            but she couldn't shake the feeling that something important had been overlooked.
            
            As she reflected on the presentations, she remembered Dr. Martinez's words about
            the importance of sustainable development in emerging markets. His research had
            shown that traditional approaches often failed to account for local cultural
            factors, leading to projects that looked good on paper but struggled in practice.
            
            The waves lapped gently at her feet as she considered the implications. Her own
            project in Southeast Asia had faced similar challenges. The community had been
            enthusiastic initially, but resistance grew as they realized the changes would
            affect their traditional way of life.
            
            She pulled out her phone and made a note to schedule a call with the local
            partners. Perhaps it was time to revisit the approach and find a more
            collaborative solution that honored both progress and tradition.
            """,
            
            "research_large": """
            # The Impact of Machine Learning on Healthcare Diagnostics: A Comprehensive Review
            
            ## Abstract
            
            This systematic review examines the current state and future potential of machine
            learning applications in healthcare diagnostics. We analyzed 247 peer-reviewed
            studies published between 2018 and 2023, focusing on diagnostic accuracy,
            implementation challenges, and clinical outcomes.
            
            ## Introduction
            
            Healthcare diagnostics has undergone significant transformation with the advent
            of artificial intelligence and machine learning technologies. Traditional
            diagnostic methods, while effective, often require extensive human expertise
            and can be subject to variability in interpretation.
            
            Machine learning algorithms, particularly deep learning models, have shown
            remarkable promise in medical image analysis, pattern recognition, and
            predictive modeling. These technologies offer the potential to improve
            diagnostic accuracy, reduce costs, and increase accessibility to quality
            healthcare services.
            
            ## Methodology
            
            We conducted a systematic literature review following PRISMA guidelines.
            Our search strategy included multiple databases: PubMed, IEEE Xplore,
            ACM Digital Library, and Google Scholar. The search terms included
            combinations of "machine learning," "artificial intelligence," "healthcare,"
            "diagnostics," "medical imaging," and related terms.
            
            Inclusion criteria:
            - Peer-reviewed articles published in English
            - Studies involving machine learning applications in medical diagnostics
            - Clinical validation or real-world implementation data
            - Publication date between January 2018 and December 2023
            
            Exclusion criteria:
            - Review articles without original research
            - Studies without clinical validation
            - Non-English publications
            - Conference abstracts without full papers
            
            ## Results
            
            Our analysis revealed several key findings across different medical specialties.
            In radiology, convolutional neural networks achieved diagnostic accuracy
            comparable to or exceeding that of experienced radiologists in specific tasks
            such as mammography screening and chest X-ray interpretation.
            
            Pathology applications showed particularly promising results, with deep learning
            models demonstrating high accuracy in histopathological image analysis for
            cancer detection. The sensitivity and specificity rates often exceeded 90%
            in controlled studies.
            
            However, implementation challenges remain significant. Issues include data
            quality and standardization, regulatory approval processes, integration with
            existing healthcare systems, and physician acceptance and training.
            """
        }
    
    async def benchmark_chunking_strategies(self, document: str, content_type: ContentType) -> Dict[str, Any]:
        """Benchmark different chunking strategies on a document."""
        strategies = {
            "adaptive": None,  # Will use adaptive chunking
            "small_chunks": ChunkingStrategy(
                chunk_size=400,
                chunk_overlap=80,
                separators=["\n\n", "\n", ".", " "],
                semantic_splitting=False
            ),
            "medium_chunks": ChunkingStrategy(
                chunk_size=800,
                chunk_overlap=160,
                separators=["\n\n", "\n", ".", " "],
                semantic_splitting=False
            ),
            "large_chunks": ChunkingStrategy(
                chunk_size=1200,
                chunk_overlap=240,
                separators=["\n\n", "\n", ".", " "],
                semantic_splitting=False
            ),
            "semantic_medium": ChunkingStrategy(
                chunk_size=800,
                chunk_overlap=160,
                separators=["\n\n", "\n", ".", " "],
                semantic_splitting=True
            )
        }
        
        results = {}
        
        for strategy_name, strategy in strategies.items():
            start_time = time.time()
            
            try:
                if strategy_name == "adaptive":
                    # Use adaptive chunking
                    chunks_with_metadata = self.adaptive_chunker.chunk_document(document)
                    chunks = [chunk for chunk, _ in chunks_with_metadata]
                    metadata_list = [metadata for _, metadata in chunks_with_metadata]
                else:
                    # Use specific strategy
                    chunks_with_metadata = self.adaptive_chunker.chunk_with_strategy(
                        document, strategy, content_type
                    )
                    chunks = [chunk for chunk, _ in chunks_with_metadata]
                    metadata_list = [metadata for _, metadata in chunks_with_metadata]
                
                processing_time = (time.time() - start_time) * 1000
                
                # Calculate metrics
                total_chars = sum(len(chunk) for chunk in chunks)
                avg_chunk_size = total_chars / len(chunks) if chunks else 0
                chunk_size_variance = sum((len(chunk) - avg_chunk_size) ** 2 for chunk in chunks) / len(chunks) if chunks else 0
                
                # Calculate semantic coherence (simplified)
                semantic_score = 0
                if metadata_list:
                    semantic_score = sum(meta.semantic_score for meta in metadata_list) / len(metadata_list)
                
                results[strategy_name] = {
                    "processing_time_ms": processing_time,
                    "chunk_count": len(chunks),
                    "total_characters": total_chars,
                    "avg_chunk_size": avg_chunk_size,
                    "chunk_size_variance": chunk_size_variance,
                    "semantic_score": semantic_score,
                    "chunks_preview": [chunk[:100] + "..." for chunk in chunks[:3]]
                }
                
            except Exception as e:
                logger.error(f"Error benchmarking strategy {strategy_name}: {e}")
                results[strategy_name] = {"error": str(e)}
        
        return results
    
    async def benchmark_embedding_performance(self, chunks: List[str]) -> Dict[str, Any]:
        """Benchmark embedding performance for different batch sizes."""
        batch_sizes = [1, 4, 8, 16, 32]
        results = {}
        
        for batch_size in batch_sizes:
            start_time = time.time()
            
            try:
                # Simulate embedding process (replace with actual embedding if needed)
                batches = [chunks[i:i + batch_size] for i in range(0, len(chunks), batch_size)]
                
                # Simulate processing time based on batch size
                processing_time = len(batches) * (50 + batch_size * 10)  # Simulated ms
                await asyncio.sleep(processing_time / 1000)  # Convert to seconds
                
                total_time = (time.time() - start_time) * 1000
                
                results[f"batch_{batch_size}"] = {
                    "batch_size": batch_size,
                    "total_batches": len(batches),
                    "processing_time_ms": total_time,
                    "chunks_per_second": len(chunks) / (total_time / 1000) if total_time > 0 else 0
                }
                
            except Exception as e:
                logger.error(f"Error benchmarking batch size {batch_size}: {e}")
                results[f"batch_{batch_size}"] = {"error": str(e)}
        
        return results
    
    async def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """Run comprehensive benchmarking suite."""
        logger.info("Starting comprehensive chunking benchmark...")
        
        test_documents = self.create_test_documents()
        content_types = {
            "technical_small": ContentType.TECHNICAL_DOCUMENT,
            "narrative_medium": ContentType.NARRATIVE_TEXT,
            "research_large": ContentType.RESEARCH_PAPER
        }
        
        benchmark_results = {}
        
        for doc_name, document in test_documents.items():
            logger.info(f"Benchmarking document: {doc_name}")
            content_type = content_types[doc_name]
            
            # Benchmark chunking strategies
            chunking_results = await self.benchmark_chunking_strategies(document, content_type)
            
            # Get best performing strategy for embedding benchmark
            best_strategy = min(chunking_results.keys(), 
                              key=lambda k: chunking_results[k].get("processing_time_ms", float('inf')))
            
            # Use adaptive chunking for embedding benchmark
            chunks_with_metadata = self.adaptive_chunker.chunk_document(document)
            chunks = [chunk for chunk, _ in chunks_with_metadata]
            
            # Benchmark embedding performance
            embedding_results = await self.benchmark_embedding_performance(chunks)
            
            benchmark_results[doc_name] = {
                "document_size": len(document),
                "content_type": content_type.value,
                "chunking_strategies": chunking_results,
                "best_chunking_strategy": best_strategy,
                "embedding_performance": embedding_results
            }
        
        # Generate recommendations
        recommendations = self.generate_recommendations(benchmark_results)
        
        return {
            "benchmark_results": benchmark_results,
            "recommendations": recommendations,
            "timestamp": time.time()
        }
    
    def generate_recommendations(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate performance recommendations based on benchmark results."""
        recommendations = {
            "optimal_strategies": {},
            "performance_insights": [],
            "configuration_suggestions": {}
        }
        
        for doc_type, result in results.items():
            chunking_results = result.get("chunking_strategies", {})
            
            # Find best strategy based on processing time and semantic score
            best_strategy = None
            best_score = float('-inf')
            
            for strategy, metrics in chunking_results.items():
                if "error" in metrics:
                    continue
                
                # Composite score: fast processing + good semantic coherence
                processing_score = 1000 / max(metrics.get("processing_time_ms", 1000), 1)
                semantic_score = metrics.get("semantic_score", 0) * 100
                composite_score = processing_score + semantic_score
                
                if composite_score > best_score:
                    best_score = composite_score
                    best_strategy = strategy
            
            recommendations["optimal_strategies"][doc_type] = best_strategy
        
        # General insights
        recommendations["performance_insights"] = [
            "Adaptive chunking generally provides the best balance of performance and quality",
            "Semantic splitting improves coherence but increases processing time",
            "Larger batch sizes improve throughput but may increase memory usage",
            "Content-specific strategies can improve performance by 20-40%"
        ]
        
        # Configuration suggestions
        recommendations["configuration_suggestions"] = {
            "speed_priority": {
                "profile": "speed_optimized",
                "chunk_size": 600,
                "semantic_splitting": False,
                "batch_size": 32
            },
            "quality_priority": {
                "profile": "quality_optimized",
                "chunk_size": 1200,
                "semantic_splitting": True,
                "batch_size": 16
            },
            "balanced": {
                "profile": "balanced",
                "chunk_size": 800,
                "semantic_splitting": True,
                "batch_size": 24
            }
        }
        
        return recommendations
    
    def save_results(self, results: Dict[str, Any], filename: str = "chunking_benchmark_results.json"):
        """Save benchmark results to file."""
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"Benchmark results saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving results: {e}")

async def main():
    """Main benchmark execution."""
    benchmark = ChunkingBenchmark()
    
    try:
        results = await benchmark.run_comprehensive_benchmark()
        
        # Print summary
        print("\n" + "="*60)
        print("CHUNKING PERFORMANCE BENCHMARK RESULTS")
        print("="*60)
        
        for doc_type, result in results["benchmark_results"].items():
            print(f"\n{doc_type.upper()}:")
            print(f"  Document size: {result['document_size']} characters")
            print(f"  Content type: {result['content_type']}")
            print(f"  Best strategy: {result['best_chunking_strategy']}")
        
        print(f"\nRECOMMENDATIONS:")
        for insight in results["recommendations"]["performance_insights"]:
            print(f"  • {insight}")
        
        # Save detailed results
        benchmark.save_results(results)
        
    except Exception as e:
        logger.error(f"Benchmark failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
