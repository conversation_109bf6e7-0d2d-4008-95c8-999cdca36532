import { useState, useEffect } from 'react';

/**
 * Custom hook for responsive design that detects if the current viewport matches a media query
 * 
 * @param query The media query to check against (e.g., "(max-width: 768px)")
 * @returns A boolean indicating whether the media query matches
 */
export function useMediaQuery(query: string): boolean {
  // Initialize with the current match state if window is available
  const getMatches = (): boolean => {
    // Check if window is defined (to avoid SSR issues)
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  };

  const [matches, setMatches] = useState<boolean>(getMatches());

  useEffect(() => {
    // Avoid running on the server
    if (typeof window === 'undefined') {
      return;
    }

    const mediaQuery = window.matchMedia(query);
    
    // Update the state initially
    setMatches(mediaQuery.matches);

    // Create a handler function for the change event
    const handler = (event: MediaQueryListEvent) => setMatches(event.matches);
    
    // Add the event listener
    mediaQuery.addEventListener('change', handler);
    
    // Clean up the event listener when the component unmounts
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
}
