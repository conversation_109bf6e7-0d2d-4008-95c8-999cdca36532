/**
 * Mode Transition Dialog Component
 * 
 * Provides a smooth transition experience when switching between dashboard modes,
 * with options to preserve data and understand the differences between modes.
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Sparkles, 
  Settings, 
  ArrowRight, 
  Shield, 
  Zap,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { DashboardMode } from '@/types/dashboard-mode';

interface ModeTransitionDialogProps {
  open: boolean;
  from_mode: DashboardMode;
  to_mode: DashboardMode;
  on_confirm: (preserve_data: boolean) => void;
  on_cancel: () => void;
  show_data_preservation?: boolean;
}

export const ModeTransitionDialog: React.FC<ModeTransitionDialogProps> = ({
  open,
  from_mode,
  to_mode,
  on_confirm,
  on_cancel,
  show_data_preservation = true,
}) => {
  const [preserve_data, set_preserve_data] = useState(true);
  const [understand_changes, set_understand_changes] = useState(false);

  const mode_config = {
    simple: {
      label: 'Simple Mode',
      description: 'AI-guided dashboard creation with conversational interface',
      icon: Sparkles,
      color: 'text-blue-500',
      bg_color: 'bg-blue-50',
      features: [
        'AI Assistant for guidance',
        'Template gallery with previews',
        'Guided step-by-step workflows',
        'Natural language widget creation',
        'Smart suggestions and recommendations',
        'One-click widget creation',
      ],
      benefits: [
        'Perfect for beginners and non-technical users',
        'Faster dashboard creation with AI help',
        'Reduced complexity and learning curve',
        'Conversational interface for easy interaction',
      ],
    },
    advanced: {
      label: 'Advanced Mode',
      description: 'Full technical control with ribbon toolbar and customization',
      icon: Settings,
      color: 'text-purple-500',
      bg_color: 'bg-purple-50',
      features: [
        'Complete ribbon toolbar with all options',
        'Technical controls and configurations',
        'Raw data access and custom queries',
        'Code editor for custom components',
        'Performance metrics and monitoring',
        'API access and integrations',
      ],
      benefits: [
        'Full control over dashboard customization',
        'Access to all technical features',
        'Custom visualizations and components',
        'Professional-grade dashboard creation',
      ],
    },
  };

  const from_config = mode_config[from_mode];
  const to_config = mode_config[to_mode];
  const FromIcon = from_config.icon;
  const ToIcon = to_config.icon;

  const handle_confirm = () => {
    if (understand_changes) {
      on_confirm(preserve_data);
    }
  };

  const changes_summary = to_mode === 'simple' 
    ? [
        'Interface will be simplified with AI guidance',
        'Advanced technical controls will be hidden',
        'AI assistant will become more prominent',
        'Template gallery will be available',
      ]
    : [
        'Full ribbon toolbar will be displayed',
        'All technical controls will be available',
        'AI assistant will provide technical guidance',
        'Advanced customization options will be enabled',
      ];

  return (
    <Dialog open={open} onOpenChange={(open) => !open && on_cancel()}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-orange-500" />
            <span>Switch Dashboard Mode</span>
          </DialogTitle>
          <DialogDescription>
            You're about to switch from {from_config.label} to {to_config.label}.
            Here's what will change:
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Mode Transition Visual */}
          <div className="flex items-center justify-center space-x-4 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className={cn("p-2 rounded-lg", from_config.bg_color)}>
                <FromIcon className={cn("h-5 w-5", from_config.color)} />
              </div>
              <div className="text-center">
                <p className="font-medium">{from_config.label}</p>
                <p className="text-xs text-muted-foreground">Current</p>
              </div>
            </div>
            
            <ArrowRight className="h-5 w-5 text-muted-foreground" />
            
            <div className="flex items-center space-x-2">
              <div className={cn("p-2 rounded-lg", to_config.bg_color)}>
                <ToIcon className={cn("h-5 w-5", to_config.color)} />
              </div>
              <div className="text-center">
                <p className="font-medium">{to_config.label}</p>
                <p className="text-xs text-muted-foreground">New</p>
              </div>
            </div>
          </div>

          {/* Changes Summary */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center space-x-2">
              <Info className="h-4 w-4 text-blue-500" />
              <span>What will change:</span>
            </h4>
            <ul className="space-y-2">
              {changes_summary.map((change, index) => (
                <li key={index} className="flex items-start space-x-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>{change}</span>
                </li>
              ))}
            </ul>
          </div>

          <Separator />

          {/* New Mode Features */}
          <div className="space-y-3">
            <h4 className="font-medium">Available in {to_config.label}:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {to_config.features.map((feature, index) => (
                <Badge key={index} variant="outline" className="justify-start">
                  {feature}
                </Badge>
              ))}
            </div>
          </div>

          {/* Data Preservation Option */}
          {show_data_preservation && (
            <div className="space-y-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="preserve-data"
                  checked={preserve_data}
                  onCheckedChange={(checked) => set_preserve_data(checked as boolean)}
                />
                <label htmlFor="preserve-data" className="text-sm font-medium cursor-pointer">
                  Preserve current dashboard data and settings
                </label>
              </div>
              <p className="text-xs text-muted-foreground ml-6">
                Your widgets, data sources, and customizations will be maintained during the mode switch.
              </p>
            </div>
          )}

          {/* Confirmation Checkbox */}
          <div className="flex items-start space-x-2 p-4 bg-amber-50 rounded-lg border border-amber-200">
            <Checkbox
              id="understand-changes"
              checked={understand_changes}
              onCheckedChange={(checked) => set_understand_changes(checked as boolean)}
            />
            <div className="space-y-1">
              <label htmlFor="understand-changes" className="text-sm font-medium cursor-pointer">
                I understand the interface changes
              </label>
              <p className="text-xs text-muted-foreground">
                Confirm that you understand how the dashboard interface will change in {to_config.label}.
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="space-x-2">
          <Button variant="outline" onClick={on_cancel}>
            Cancel
          </Button>
          <Button 
            onClick={handle_confirm}
            disabled={!understand_changes}
            className="min-w-[120px]"
          >
            <Zap className="h-4 w-4 mr-2" />
            Switch Mode
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Hook for managing mode transitions
export const useModeTransition = () => {
  const [is_transitioning, set_is_transitioning] = useState(false);
  const [transition_dialog, set_transition_dialog] = useState<{
    open: boolean;
    from_mode: DashboardMode;
    to_mode: DashboardMode;
    on_confirm: (preserve_data: boolean) => void;
  } | null>(null);

  const initiate_transition = (
    from_mode: DashboardMode,
    to_mode: DashboardMode,
    on_confirm: (preserve_data: boolean) => void
  ) => {
    set_transition_dialog({
      open: true,
      from_mode,
      to_mode,
      on_confirm: (preserve_data: boolean) => {
        set_is_transitioning(true);
        on_confirm(preserve_data);
        set_transition_dialog(null);
        
        // Reset transitioning state after animation
        setTimeout(() => {
          set_is_transitioning(false);
        }, 1000);
      },
    });
  };

  const cancel_transition = () => {
    set_transition_dialog(null);
  };

  return {
    is_transitioning,
    transition_dialog,
    initiate_transition,
    cancel_transition,
  };
};
