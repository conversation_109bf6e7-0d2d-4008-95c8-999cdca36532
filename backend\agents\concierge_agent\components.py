"""
Concierge agent components for the Datagenius agent system.

This module provides components specific to the concierge agent functionality,
including state tracking, workflow management, and specialized assistance.
"""

import logging
import time
import json
from datetime import datetime
from enum import Enum
from typing import Dict, Any, List, Optional, Tuple

from agents.components.base import AgentComponent

# Configure logging
logger = logging.getLogger(__name__)

# Define conversation stages for the concierge agent
class ConversationStage(Enum):
    """Enum representing the stages of a concierge conversation."""
    INITIAL = "initial"
    GREETING = "greeting"
    NEEDS_ASSESSMENT = "needs_assessment"
    PERSONA_RECOMMENDATION = "persona_recommendation"
    DATA_GUIDANCE = "data_guidance"
    DATA_ATTACHED = "data_attached"
    PERSONA_SELECTION = "persona_selection"
    HANDOFF = "handoff"
    FOLLOWUP = "followup"
    COMPLETED = "completed"

# Note: Most of the concierge agent's components are shared components
# that are already implemented in the agents/components directory:
# - PersonaRecommenderComponent
# - DataAttachmentAssistantComponent
# - ContextManagerComponent
# - PersonaRoutingComponent

# This file is a placeholder for any concierge-specific components
# that might be needed in the future.

class ConciergeStateTrackerComponent(AgentComponent):
    """
    Tracks the state of the concierge conversation to provide continuity.

    This component maintains a state machine for each conversation, tracking:
    - Current conversation stage
    - User needs and preferences
    - Recommended personas
    - Attached data files
    - Selected persona
    - Conversation history highlights

    It uses this information to provide context-aware assistance and
    ensure a smooth, continuous experience across multiple messages.
    """

    def __init__(self):
        """Initialize the ConciergeStateTrackerComponent."""
        super().__init__()
        self.states = {}  # Dictionary to store conversation states
        self.state_ttl = 3600  # Default TTL for states (1 hour)
        self.max_states = 1000  # Maximum number of states to store

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        self.state_ttl = config.get("state_ttl", 3600)  # TTL in seconds
        self.max_states = config.get("max_states", 1000)
        self.cleanup_interval = config.get("cleanup_interval", 300)  # 5 minutes
        self.last_cleanup = time.time()

        logger.info(f"ConciergeStateTrackerComponent '{self.name}' initialized with TTL {self.state_ttl}s")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the context to track conversation state.

        Args:
            context: Context dictionary containing request data.

        Returns:
            Updated context dictionary with state information.
        """
        # Periodically clean up expired states
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            await self._cleanup_expired_states()
            self.last_cleanup = current_time

        conversation_id = context.get("conversation_id")
        if not conversation_id:
            logger.warning("No conversation_id found in context")
            return context

        # Get current state or initialize new state
        current_state = self.states.get(conversation_id, self._create_initial_state())

        # Update timestamp
        current_state["timestamp"] = current_time

        # Update message count
        current_state["message_count"] = current_state.get("message_count", 0) + 1

        # Update state based on context and determine the current stage
        await self._update_state(current_state, context)

        # Store updated state
        self.states[conversation_id] = current_state

        # Add state to context
        context["concierge_state"] = current_state

        # Add suggestions based on the current state
        context = await self._add_state_based_suggestions(context, current_state)

        # Log state transition if it changed
        if current_state.get("_previous_stage") != current_state["stage"]:
            logger.info(f"Conversation {conversation_id} transitioned from "
                       f"{current_state.get('_previous_stage', 'None')} to {current_state['stage']}")

        return context

    def _create_initial_state(self) -> Dict[str, Any]:
        """
        Create an initial state for a new conversation.

        Returns:
            A dictionary with the initial state.
        """
        return {
            "stage": ConversationStage.INITIAL.value,
            "_previous_stage": None,
            "timestamp": time.time(),
            "message_count": 0,
            "user_needs": [],
            "recommended_personas": [],
            "data_attached": False,
            "attached_files": [],
            "selected_persona": None,
            "handoff_completed": False,
            "followup_needed": False,
            "highlights": []
        }

    async def _update_state(self, state: Dict[str, Any], context: Dict[str, Any]) -> None:
        """
        Update the state based on the current context.

        Args:
            state: The current state dictionary.
            context: The context dictionary.
        """
        # Save previous stage for transition logging
        state["_previous_stage"] = state["stage"]

        # Get relevant information from context
        metadata = context.get("metadata", {})
        message = context.get("message", "")
        attached_files = context.get("attached_files", [])

        # Check if this is the first message
        if state["message_count"] == 1:
            state["stage"] = ConversationStage.GREETING.value
            state["highlights"].append({
                "type": "greeting",
                "timestamp": time.time(),
                "message": "Conversation started"
            })
            return

        # Check for attached files
        if attached_files and not state["data_attached"]:
            state["stage"] = ConversationStage.DATA_ATTACHED.value
            state["data_attached"] = True
            state["attached_files"] = [
                {
                    "filename": f.get("filename", "Unknown file"),
                    "type": f.get("content_type", "Unknown type"),
                    "size": f.get("size", 0),
                    "timestamp": time.time()
                }
                for f in attached_files
            ]
            state["highlights"].append({
                "type": "data_attached",
                "timestamp": time.time(),
                "message": f"User attached {len(attached_files)} file(s)"
            })
            return

        # Check if persona recommendations were made
        if "persona_recommendations" in metadata:
            state["stage"] = ConversationStage.PERSONA_RECOMMENDATION.value
            state["recommended_personas"] = metadata.get("recommended_persona_ids", [])

            if state["recommended_personas"]:
                state["highlights"].append({
                    "type": "recommendation",
                    "timestamp": time.time(),
                    "message": f"Recommended personas: {', '.join(state['recommended_personas'])}"
                })
            return

        # Check if data guidance was given
        if "data_attachment_guidance" in metadata:
            state["stage"] = ConversationStage.DATA_GUIDANCE.value
            state["highlights"].append({
                "type": "data_guidance",
                "timestamp": time.time(),
                "message": "Provided data attachment guidance"
            })
            return

        # Check if routing was requested
        if "routing_result" in metadata and metadata["routing_result"].get("success", False):
            state["stage"] = ConversationStage.PERSONA_SELECTION.value
            state["selected_persona"] = metadata["routing_result"].get("persona_id")
            state["highlights"].append({
                "type": "selection",
                "timestamp": time.time(),
                "message": f"Selected persona: {state['selected_persona']}"
            })
            return

        # Check if handoff is being completed
        if state["selected_persona"] and not state["handoff_completed"]:
            if "handoff" in message.lower() or "transfer" in message.lower():
                state["stage"] = ConversationStage.HANDOFF.value
                state["handoff_completed"] = True
                state["highlights"].append({
                    "type": "handoff",
                    "timestamp": time.time(),
                    "message": f"Handed off to {state['selected_persona']}"
                })
                return

        # If we've already done a handoff and the user is back, it's a followup
        if state["handoff_completed"]:
            state["stage"] = ConversationStage.FOLLOWUP.value
            state["followup_needed"] = True
            return

        # If we're still in the initial stage after the greeting, move to needs assessment
        if state["stage"] == ConversationStage.GREETING.value:
            state["stage"] = ConversationStage.NEEDS_ASSESSMENT.value
            return

    async def _add_state_based_suggestions(self, context: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add suggestions to the context based on the current state.

        Args:
            context: The context dictionary.
            state: The current state dictionary.

        Returns:
            Updated context dictionary with suggestions.
        """
        suggestions = []

        # Add suggestions based on the current stage
        if state["stage"] == ConversationStage.NEEDS_ASSESSMENT.value:
            suggestions.append({
                "type": "next_step",
                "text": "Ask the user about their specific needs or goals"
            })

        elif state["stage"] == ConversationStage.PERSONA_RECOMMENDATION.value:
            suggestions.append({
                "type": "next_step",
                "text": "Encourage the user to select one of the recommended personas"
            })

        elif state["stage"] == ConversationStage.DATA_GUIDANCE.value:
            suggestions.append({
                "type": "next_step",
                "text": "Guide the user on how to attach their data files"
            })

        elif state["stage"] == ConversationStage.DATA_ATTACHED.value:
            suggestions.append({
                "type": "next_step",
                "text": "Recommend appropriate personas based on the attached files"
            })

        elif state["stage"] == ConversationStage.PERSONA_SELECTION.value:
            suggestions.append({
                "type": "next_step",
                "text": "Prepare to hand off the conversation to the selected persona"
            })

        elif state["stage"] == ConversationStage.FOLLOWUP.value:
            suggestions.append({
                "type": "next_step",
                "text": "Ask the user if they need further assistance or want to try another persona"
            })

        # Add the suggestions to the context
        if suggestions:
            context["metadata"] = context.get("metadata", {})
            context["metadata"]["concierge_suggestions"] = suggestions

        return context

    async def _cleanup_expired_states(self) -> None:
        """
        Clean up expired states to prevent memory leaks.
        """
        current_time = time.time()
        expired_keys = []

        # Find expired states
        for conversation_id, state in self.states.items():
            if current_time - state.get("timestamp", 0) > self.state_ttl:
                expired_keys.append(conversation_id)

        # Remove expired states
        for key in expired_keys:
            del self.states[key]

        # If we still have too many states, remove the oldest ones
        if len(self.states) > self.max_states:
            # Sort by timestamp
            sorted_states = sorted(
                self.states.items(),
                key=lambda x: x[1].get("timestamp", 0)
            )

            # Remove the oldest states
            for conversation_id, _ in sorted_states[:len(self.states) - self.max_states]:
                del self.states[conversation_id]

        logger.debug(f"Cleaned up {len(expired_keys)} expired states, {len(self.states)} remaining")

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["state_tracking", "conversation_continuity"])
