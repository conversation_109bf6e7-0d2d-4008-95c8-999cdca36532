/**
 * Global Error Handler
 * Handles unhandled errors and promise rejections
 */

interface ErrorReport {
  message: string;
  stack?: string;
  url?: string;
  line?: number;
  column?: number;
  timestamp: number;
  userAgent: string;
  type: 'javascript' | 'promise' | 'resource' | 'network';
}

class GlobalErrorHandler {
  private errorQueue: ErrorReport[] = [];
  private maxQueueSize = 50;
  private isOnline = navigator.onLine;

  constructor() {
    this.setupErrorHandlers();
    this.setupNetworkHandlers();
  }

  private setupErrorHandlers() {
    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename,
        line: event.lineno,
        column: event.colno,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        type: 'javascript'
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        type: 'promise'
      });
    });

    // Handle resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.handleError({
          message: `Failed to load resource: ${(event.target as any)?.src || (event.target as any)?.href}`,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          type: 'resource'
        });
      }
    }, true);
  }

  private setupNetworkHandlers() {
    // Monitor network status
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  private handleError(errorReport: ErrorReport) {
    // Filter out known non-critical errors
    if (this.shouldIgnoreError(errorReport)) {
      return;
    }

    // Log error to console in development
    if (import.meta.env.DEV) {
      console.error('Global error caught:', errorReport);
    }

    // Add to queue
    this.addToQueue(errorReport);

    // Try to send immediately if online
    if (this.isOnline) {
      this.flushErrorQueue();
    }

    // Show user notification for critical errors
    if (this.isCriticalError(errorReport)) {
      this.showErrorNotification(errorReport);
    }
  }

  private shouldIgnoreError(errorReport: ErrorReport): boolean {
    const ignoredPatterns = [
      // Browser extension errors
      /extension\//i,
      /chrome-extension:/i,
      /moz-extension:/i,
      
      // Third-party script errors
      /Script error/i,
      
      // Network errors that are handled elsewhere
      /NetworkError/i,
      /Failed to fetch/i,
      
      // React DevTools
      /react-devtools/i,
      
      // Non-critical warnings
      /ResizeObserver loop limit exceeded/i,
      /Non-passive event listener/i,
    ];

    return ignoredPatterns.some(pattern => 
      pattern.test(errorReport.message)
    );
  }

  private isCriticalError(errorReport: ErrorReport): boolean {
    const criticalPatterns = [
      /ChunkLoadError/i,
      /Loading chunk \d+ failed/i,
      /TypeError.*undefined/i,
      /ReferenceError/i,
      /SyntaxError/i,
    ];

    return criticalPatterns.some(pattern => 
      pattern.test(errorReport.message)
    );
  }

  private addToQueue(errorReport: ErrorReport) {
    this.errorQueue.push(errorReport);
    
    // Limit queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  private async flushErrorQueue() {
    if (this.errorQueue.length === 0) {
      return;
    }

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    try {
      // Send errors to monitoring service
      await this.sendErrorsToService(errors);
    } catch (error) {
      // If sending fails, add back to queue
      this.errorQueue.unshift(...errors);
      console.warn('Failed to send error reports:', error);
    }
  }

  private async sendErrorsToService(errors: ErrorReport[]) {
    // In a real application, you would send to your error monitoring service
    // For now, we'll just log them
    if (import.meta.env.DEV) {
      console.log('Would send error reports:', errors);
      return;
    }

    // Example implementation for a monitoring service
    try {
      const response = await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ errors }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      // Store in localStorage as fallback
      this.storeErrorsLocally(errors);
      throw error;
    }
  }

  private storeErrorsLocally(errors: ErrorReport[]) {
    try {
      const stored = localStorage.getItem('pending_error_reports');
      const existing = stored ? JSON.parse(stored) : [];
      const combined = [...existing, ...errors];
      
      // Limit stored errors
      const limited = combined.slice(-100);
      localStorage.setItem('pending_error_reports', JSON.stringify(limited));
    } catch (error) {
      console.warn('Failed to store errors locally:', error);
    }
  }

  private showErrorNotification(errorReport: ErrorReport) {
    // Create a user-friendly error notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm';
    notification.innerHTML = `
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="flex-1">
          <h4 class="font-semibold">Application Error</h4>
          <p class="text-sm mt-1">Something went wrong. Please refresh the page or try again later.</p>
        </div>
        <button onclick="this.parentElement.parentElement.remove()" class="text-red-200 hover:text-white">
          <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 10000);
  }

  // Public methods
  public reportError(error: Error, context?: Record<string, any>) {
    this.handleError({
      message: error.message,
      stack: error.stack,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      type: 'javascript',
      ...context
    });
  }

  public getErrorQueue(): ErrorReport[] {
    return [...this.errorQueue];
  }

  public clearErrorQueue(): void {
    this.errorQueue = [];
  }
}

// Create singleton instance
export const globalErrorHandler = new GlobalErrorHandler();

// Export for manual error reporting
export { GlobalErrorHandler, type ErrorReport };
