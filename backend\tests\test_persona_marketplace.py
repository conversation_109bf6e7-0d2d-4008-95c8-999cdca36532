#!/usr/bin/env python3
"""
Test script to verify the PersonaMarketplaceTool is working correctly.
This will help ensure the concierge agent can fetch real persona data.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_persona_marketplace_tool():
    """Test the PersonaMarketplaceTool functionality."""
    try:
        print("🧪 Testing PersonaMarketplaceTool...")
        
        # Import the tool
        from backend.agents.tools.mcp.persona_marketplace_tool import PersonaMarketplaceTool
        
        # Create and initialize the tool
        marketplace_tool = PersonaMarketplaceTool()
        await marketplace_tool._initialize({})
        print("✅ Tool initialized successfully")
        
        # Test 1: Get available personas
        print("\n📋 Test 1: Getting available personas...")
        result1 = await marketplace_tool.execute({
            "query_type": "available_personas"
        })
        
        if not result1.get("isError", False):
            metadata = result1.get("metadata", {})
            data = metadata.get("data", {})
            personas = data.get("available_personas", [])
            print(f"✅ Found {len(personas)} available personas")
            
            if personas:
                print("📝 Sample persona:")
                sample = personas[0]
                print(f"   Name: {sample.get('name', 'Unknown')}")
                print(f"   Description: {sample.get('description', 'No description')}")
                print(f"   Price: ${sample.get('price', 0):.2f}")
                print(f"   Capabilities: {', '.join(sample.get('capabilities', []))}")
        else:
            print(f"❌ Error getting available personas: {result1}")
        
        # Test 2: Get marketplace overview for a test user
        print("\n📋 Test 2: Getting marketplace overview for user...")
        result2 = await marketplace_tool.execute({
            "user_id": "1",  # Test user ID
            "query_type": "marketplace_overview"
        })
        
        if not result2.get("isError", False):
            metadata = result2.get("metadata", {})
            data = metadata.get("data", {})
            owned = data.get("owned_personas", [])
            free = data.get("free_personas", [])
            paid = data.get("paid_personas", [])
            
            print(f"✅ Marketplace overview:")
            print(f"   Owned personas: {len(owned)}")
            print(f"   Free personas: {len(free)}")
            print(f"   Paid personas: {len(paid)}")
        else:
            print(f"❌ Error getting marketplace overview: {result2}")
        
        # Test 3: Test the concierge agent's persona query handling
        print("\n📋 Test 3: Testing concierge agent persona query...")
        from backend.agents.concierge_agent.concierge import ConciergeAgent
        
        concierge = ConciergeAgent()
        await concierge._initialize({})
        
        # Test the persona query handler
        response = await concierge.handle_persona_query("what personas are available", "1")
        print("✅ Concierge persona query response:")
        print(f"   Response length: {len(response)} characters")
        print(f"   Contains 'Your Owned Personas': {'Your Owned Personas' in response}")
        print(f"   Contains 'Free Personas': {'Free Personas' in response}")
        print(f"   Contains 'Premium Personas': {'Premium Personas' in response}")
        
        print("\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_concierge_integration():
    """Test the full concierge integration with persona queries."""
    try:
        print("\n🔗 Testing full concierge integration...")
        
        from backend.agents.concierge_agent.concierge import ConciergeAgent
        
        concierge = ConciergeAgent()
        await concierge._initialize({})
        
        # Test various persona query phrases
        test_queries = [
            "what personas are available",
            "show me available agents",
            "list personas",
            "what can you do",
            "what options do I have"
        ]
        
        for query in test_queries:
            print(f"\n📝 Testing query: '{query}'")
            
            # Simulate the process_message call
            result = await concierge.process_message(
                message=query,
                user_id="1",
                conversation_id="test_conv",
                context={"conversation_history": []}
            )
            
            if result.get("success", False):
                response = result.get("message", "")
                metadata = result.get("metadata", {})
                
                print(f"✅ Query processed successfully")
                print(f"   Intent: {metadata.get('intent', 'unknown')}")
                print(f"   Response contains real data: {len(response) > 200}")
                print(f"   Contains marketplace sections: {'Your Owned Personas' in response}")
            else:
                print(f"❌ Query failed: {result}")
        
        print("\n🎉 Integration tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 Starting Persona Marketplace Tests\n")
        
        # Test the marketplace tool
        tool_success = await test_persona_marketplace_tool()
        
        if tool_success:
            # Test the concierge integration
            integration_success = await test_concierge_integration()
            
            if integration_success:
                print("\n✅ All tests passed! The concierge agent should now use real persona data instead of hallucinating.")
            else:
                print("\n❌ Integration tests failed.")
        else:
            print("\n❌ Marketplace tool tests failed.")
    
    asyncio.run(main())
