"""
Universal Context Injector for Cross-Agent Intelligence.

This component automatically injects cross-agent context into ALL AI personas
in the Datagenius system, enabling comprehensive cross-agent intelligence.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from ..base_component import BaseAgentComponent, Agent<PERSON>ontext
from .cross_agent_intelligence import CrossAgentIntelligence
from .agent_interaction_tracker import AgentInteractionTracker
from app.services.shared_context_repository import SharedContextRepository
from app.models.shared_context import CrossAgentContextRequest

logger = logging.getLogger(__name__)


class UniversalContextInjector(BaseAgentComponent):
    """
    Universal context injector that automatically provides cross-agent intelligence
    to ALL AI personas in the Datagenius system.
    
    This component:
    1. Automatically loads business profile context
    2. Injects relevant insights from other agents
    3. Provides collaboration opportunities
    4. Tracks interactions for future cross-agent learning
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("universal_context_injector", config)
        
        # Configuration
        self.enable_cross_agent_context = config.get("enable_cross_agent_context", True)
        self.enable_interaction_tracking = config.get("enable_interaction_tracking", True)
        self.max_context_age_hours = config.get("max_context_age_hours", 24)
        self.max_insights_per_agent = config.get("max_insights_per_agent", 15)
        self.max_interactions_per_agent = config.get("max_interactions_per_agent", 10)
        self.context_relevance_threshold = config.get("context_relevance_threshold", 0.6)
        
        # Agent persona configurations
        self.agent_personas = {
            "concierge-agent": {
                "role": "User guidance and coordination",
                "priorities": ["user_intent", "workflow", "coordination", "general"],
                "collaboration_style": "coordinator",
                "context_focus": ["user_guidance", "workflow_coordination", "agent_routing"]
            },
            "composable-marketing-ai": {
                "role": "Marketing strategy and campaign management",
                "priorities": ["marketing_strategy", "campaign_results", "audience_insights", "content_performance"],
                "collaboration_style": "strategist",
                "context_focus": ["marketing", "strategy", "campaigns", "audience"]
            },
            "composable-analysis-ai": {
                "role": "Data analysis and business intelligence",
                "priorities": ["data_insights", "trends", "patterns", "recommendations"],
                "collaboration_style": "analyst",
                "context_focus": ["analysis", "data", "insights", "trends"]
            },
            "composable-classifier-ai": {
                "role": "Data classification and organization",
                "priorities": ["categorization", "organization", "structure"],
                "collaboration_style": "organizer",
                "context_focus": ["classification", "organization", "structure"]
            },
            "data-assistant": {
                "role": "Data processing and management",
                "priorities": ["data_quality", "processing_results", "analysis_findings"],
                "collaboration_style": "processor",
                "context_focus": ["data_processing", "quality", "management"]
            },
            "text-processor": {
                "role": "Text analysis and processing",
                "priorities": ["text_insights", "summaries", "entities", "sentiment"],
                "collaboration_style": "analyzer",
                "context_focus": ["text_analysis", "summarization", "extraction"]
            }
        }
        
        # Cross-agent intelligence components
        self.cross_agent_intelligence = CrossAgentIntelligence(config.get("cross_agent_config", {}))
        self.interaction_tracker = AgentInteractionTracker(config.get("tracking_config", {}))
        
        # Context cache for performance
        self.context_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl_seconds = config.get("cache_ttl_seconds", 300)  # 5 minutes
    
    async def _initialize_component(self) -> None:
        """Initialize the universal context injector."""
        self.logger.info("Initializing UniversalContextInjector")
        
        try:
            # Initialize sub-components
            await self.cross_agent_intelligence.initialize()
            await self.interaction_tracker.initialize()
            
            self.logger.info("Universal context injector initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize some components: {e}")
    
    def get_required_fields(self) -> List[str]:
        """Return list of required context fields."""
        return ["user_id", "business_profile_id", "agent_id"]
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process universal context injection for any AI persona.
        
        Args:
            context: AgentContext containing agent and business profile information
            
        Returns:
            Updated AgentContext with comprehensive cross-agent intelligence
        """
        if not self.enable_cross_agent_context:
            return context
        
        user_id = context.user_id
        business_profile_id = context.get_field("business_profile_id")
        agent_id = context.get_field("agent_id", "unknown")
        operation = context.get_field("context_operation", "inject_context")
        
        if not business_profile_id:
            self.logger.warning(f"No business profile ID provided for agent {agent_id}")
            context.set_field("cross_agent_status", "no_profile")
            return context
        
        self.logger.info(f"Injecting universal context for agent {agent_id} in profile {business_profile_id}")
        
        try:
            if operation == "inject_context":
                return await self._inject_universal_context(context, business_profile_id, agent_id)
            elif operation == "track_interaction":
                return await self._track_agent_interaction(context, business_profile_id, agent_id)
            elif operation == "generate_insight":
                return await self._generate_agent_insight(context, business_profile_id, agent_id)
            elif operation == "get_collaboration_context":
                return await self._get_collaboration_context(context, business_profile_id, agent_id)
            else:
                context.add_error(self.name, f"Unknown context operation: {operation}")
                context.set_status("error")
                return context
                
        except Exception as e:
            self.logger.error(f"Error in universal context injection: {e}")
            context.add_error(self.name, f"Context injection error: {str(e)}")
            context.set_field("cross_agent_status", "error")
        
        return context
    
    async def _inject_universal_context(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Inject comprehensive cross-agent context for any agent."""
        
        # Check cache first
        cache_key = f"{business_profile_id}_{agent_id}"
        if self._is_cache_valid(cache_key):
            cached_context = self.context_cache[cache_key]["context"]
            context.set_field("cross_agent_context", cached_context)
            context.set_field("cross_agent_status", "loaded_from_cache")
            return context
        
        # Get agent persona configuration
        agent_config = self.agent_personas.get(agent_id, {
            "role": "AI Assistant",
            "priorities": ["general"],
            "collaboration_style": "helper",
            "context_focus": ["general"]
        })
        
        # Build comprehensive context
        universal_context = await self._build_universal_context(
            business_profile_id, agent_id, agent_config
        )
        
        # Cache the context
        self.context_cache[cache_key] = {
            "context": universal_context,
            "timestamp": datetime.now(),
            "ttl": self.cache_ttl_seconds
        }
        
        # Inject into agent context
        context.set_field("cross_agent_context", universal_context)
        context.set_field("agent_persona_config", agent_config)
        context.set_field("cross_agent_status", "loaded")
        
        # Add context summary for agent prompt
        context_summary = self._generate_context_summary_for_agent(universal_context, agent_config)
        context.set_field("cross_agent_summary", context_summary)
        
        self.logger.info(f"Injected universal context for {agent_id}: {len(universal_context.get('insights', []))} insights, {len(universal_context.get('interactions', []))} interactions")
        
        return context
    
    async def _build_universal_context(self, business_profile_id: str, agent_id: str, agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """Build comprehensive universal context for an agent."""
        
        try:
            from app.database import get_db
            db = next(get_db())
            repository = SharedContextRepository(db)
            
            # Get relevant insights from other agents
            insights = await repository.get_insights_for_agent(
                business_profile_id, agent_id, limit=self.max_insights_per_agent
            )
            
            # Filter insights by relevance to this agent
            relevant_insights = self._filter_insights_by_relevance(insights, agent_config)
            
            # Get recent interactions from other agents
            interactions = await repository.get_recent_interactions(
                business_profile_id, exclude_agent_id=agent_id, limit=self.max_interactions_per_agent
            )
            
            # Get shared contexts targeted at this agent
            shared_contexts = await repository.get_shared_context_for_agent(business_profile_id, agent_id)
            
            # Get agent activity summary
            activity_summary = await repository.get_agent_activity_summary(business_profile_id, days=7)
            
            # Build collaboration opportunities
            collaboration_opportunities = self._build_collaboration_opportunities(
                agent_id, agent_config, activity_summary
            )
            
            # Get business profile context (from business profile component)
            business_context = await self._get_business_profile_context(business_profile_id)
            
            universal_context = {
                "business_profile_context": business_context,
                "relevant_insights": [insight.model_dump() for insight in relevant_insights],
                "recent_interactions": [interaction.model_dump() for interaction in interactions],
                "shared_contexts": [context.model_dump() for context in shared_contexts],
                "collaboration_opportunities": collaboration_opportunities,
                "agent_activity_summary": activity_summary,
                "context_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "agent_id": agent_id,
                    "business_profile_id": business_profile_id,
                    "context_version": "1.0"
                }
            }
            
            return universal_context
            
        except Exception as e:
            self.logger.error(f"Error building universal context: {e}")
            return {
                "error": str(e),
                "business_profile_context": {},
                "relevant_insights": [],
                "recent_interactions": [],
                "shared_contexts": [],
                "collaboration_opportunities": [],
                "agent_activity_summary": {}
            }
    
    def _filter_insights_by_relevance(self, insights: List[Any], agent_config: Dict[str, Any]) -> List[Any]:
        """Filter insights based on agent's priorities and focus areas."""
        agent_priorities = agent_config.get("priorities", [])
        context_focus = agent_config.get("context_focus", [])
        
        relevant_insights = []
        
        for insight in insights:
            relevance_score = 0.0
            
            # Check insight type relevance
            if insight.insight_type in agent_priorities:
                relevance_score += 0.5
            
            # Check tag relevance
            for tag in insight.relevance_tags:
                if tag in context_focus or tag in agent_priorities:
                    relevance_score += 0.2
            
            # Check content relevance (simple keyword matching)
            content_lower = insight.content.lower()
            for focus_area in context_focus:
                if focus_area.replace("_", " ") in content_lower:
                    relevance_score += 0.1
            
            # Include if above threshold
            if relevance_score >= self.context_relevance_threshold:
                relevant_insights.append(insight)
        
        # Sort by relevance and recency
        relevant_insights.sort(key=lambda x: (x.confidence_score, x.created_at), reverse=True)
        
        return relevant_insights[:self.max_insights_per_agent]
    
    def _build_collaboration_opportunities(self, agent_id: str, agent_config: Dict[str, Any], 
                                         activity_summary: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Build collaboration opportunities for the agent."""
        opportunities = []
        
        collaboration_style = agent_config.get("collaboration_style", "helper")
        agent_priorities = agent_config.get("priorities", [])
        
        # Get active agents from activity summary
        active_agents = list(activity_summary.get("interactions_by_agent", {}).keys())
        
        # Define collaboration patterns
        collaboration_patterns = {
            ("strategist", "analyst"): "Strategy validation with data analysis",
            ("analyst", "strategist"): "Data-driven strategy development",
            ("coordinator", "analyst"): "Insight explanation and user guidance",
            ("coordinator", "strategist"): "Strategy communication and implementation",
            ("organizer", "analyst"): "Structured data analysis",
            ("processor", "analyst"): "Data preparation for analysis",
            ("analyzer", "strategist"): "Content analysis for marketing",
            ("analyzer", "analyst"): "Text insights for data analysis"
        }
        
        # Find collaboration opportunities with other active agents
        for other_agent_id in active_agents:
            if other_agent_id == agent_id:
                continue
            
            other_agent_config = self.agent_personas.get(other_agent_id, {})
            other_collaboration_style = other_agent_config.get("collaboration_style", "helper")
            
            # Check for collaboration patterns
            pattern_key = (collaboration_style, other_collaboration_style)
            if pattern_key in collaboration_patterns:
                opportunity = {
                    "partner_agent_id": other_agent_id,
                    "collaboration_type": pattern_key[0] + "_" + pattern_key[1],
                    "description": collaboration_patterns[pattern_key],
                    "suggested_workflow": f"{agent_id} → {other_agent_id}",
                    "confidence_score": 0.8,
                    "estimated_benefit": "High"
                }
                opportunities.append(opportunity)
        
        return opportunities
    
    async def _get_business_profile_context(self, business_profile_id: str) -> Dict[str, Any]:
        """Get business profile context for the agent."""
        try:
            from app.services.business_profile_service import BusinessProfileService
            from app.database import get_db
            
            db = next(get_db())
            service = BusinessProfileService(db)
            
            # Get profile with data sources
            profile_with_sources = service.get_profile_with_data_sources(business_profile_id, None)
            
            if profile_with_sources:
                return {
                    "profile_name": profile_with_sources.name,
                    "industry": profile_with_sources.industry,
                    "business_type": profile_with_sources.business_type,
                    "target_audience": profile_with_sources.target_audience,
                    "products_services": profile_with_sources.products_services,
                    "marketing_goals": profile_with_sources.marketing_goals,
                    "data_sources_count": len(profile_with_sources.data_source_assignments),
                    "data_source_roles": [assignment.role for assignment in profile_with_sources.data_source_assignments]
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"Error getting business profile context: {e}")
            return {}
    
    def _generate_context_summary_for_agent(self, universal_context: Dict[str, Any], agent_config: Dict[str, Any]) -> str:
        """Generate a context summary tailored for the specific agent."""
        agent_role = agent_config.get("role", "AI Assistant")
        
        summary_parts = []
        
        # Business context
        business_context = universal_context.get("business_profile_context", {})
        if business_context.get("profile_name"):
            summary_parts.append(f"Working with {business_context['profile_name']}")
            if business_context.get("industry"):
                summary_parts.append(f"in {business_context['industry']}")
        
        # Insights summary
        insights = universal_context.get("relevant_insights", [])
        if insights:
            insight_types = list(set([insight.get("insight_type", "general") for insight in insights]))
            summary_parts.append(f"Available insights: {', '.join(insight_types[:3])}")
        
        # Collaboration opportunities
        opportunities = universal_context.get("collaboration_opportunities", [])
        if opportunities:
            partner_agents = [opp.get("partner_agent_id", "") for opp in opportunities]
            summary_parts.append(f"Can collaborate with: {', '.join(partner_agents[:2])}")
        
        # Activity summary
        activity = universal_context.get("agent_activity_summary", {})
        if activity.get("total_interactions", 0) > 0:
            summary_parts.append(f"Recent activity: {activity['total_interactions']} interactions")
        
        if summary_parts:
            return f"Context for {agent_role}: " + ". ".join(summary_parts)
        else:
            return f"No additional context available for {agent_role}"
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached context is still valid."""
        if cache_key not in self.context_cache:
            return False
        
        cache_data = self.context_cache[cache_key]
        cache_age = (datetime.now() - cache_data["timestamp"]).total_seconds()
        
        return cache_age < cache_data["ttl"]
    
    async def _track_agent_interaction(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Track agent interaction using the interaction tracker."""
        # Delegate to interaction tracker
        context.set_field("tracking_operation", "track_interaction")
        return await self.interaction_tracker.process(context)
    
    async def _generate_agent_insight(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Generate agent insight using the interaction tracker."""
        # Delegate to interaction tracker
        context.set_field("tracking_operation", "generate_insight")
        return await self.interaction_tracker.process(context)
    
    async def _get_collaboration_context(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Get collaboration context using cross-agent intelligence."""
        # Delegate to cross-agent intelligence
        context.set_field("cross_agent_operation", "get_collaboration_opportunities")
        return await self.cross_agent_intelligence.process(context)
    
    def clear_cache(self, business_profile_id: str = None, agent_id: str = None) -> None:
        """Clear context cache."""
        if business_profile_id and agent_id:
            cache_key = f"{business_profile_id}_{agent_id}"
            self.context_cache.pop(cache_key, None)
        elif business_profile_id:
            keys_to_remove = [key for key in self.context_cache.keys() if key.startswith(f"{business_profile_id}_")]
            for key in keys_to_remove:
                del self.context_cache[key]
        else:
            self.context_cache.clear()
        
        self.logger.info(f"Cleared context cache for profile: {business_profile_id}, agent: {agent_id}")
    
    async def cleanup_expired_cache(self) -> None:
        """Clean up expired cache entries."""
        current_time = datetime.now()
        expired_keys = []
        
        for key, cache_data in self.context_cache.items():
            cache_age = (current_time - cache_data["timestamp"]).total_seconds()
            if cache_age >= cache_data["ttl"]:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.context_cache[key]
        
        if expired_keys:
            self.logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
