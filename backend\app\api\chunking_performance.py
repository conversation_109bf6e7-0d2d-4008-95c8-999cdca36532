"""
API endpoints for chunking performance monitoring and optimization.

This module provides REST API endpoints for monitoring chunking performance,
viewing metrics, managing alerts, and controlling optimization settings.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import logging

from agents.utils.chunking_monitor import get_chunking_monitor, ChunkingMonitor
from agents.utils.chunking_performance_manager import ChunkingPerformanceManager
from app.auth.admin import require_admin

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/chunking-performance", tags=["chunking-performance"])

# Pydantic models for API requests/responses
class PerformanceProfileUpdate(BaseModel):
    profile: str
    apply_immediately: bool = True

class ThresholdUpdate(BaseModel):
    metric_name: str
    warning_threshold: float
    critical_threshold: float

class MonitoringConfig(BaseModel):
    enabled: bool
    interval_seconds: int = 60

class OptimizationSettings(BaseModel):
    auto_optimize: bool
    optimization_interval_minutes: int = 30

@router.get("/dashboard")
async def get_performance_dashboard() -> Dict[str, Any]:
    """
    Get performance dashboard data.
    
    Returns comprehensive performance metrics, alerts, and trends.
    """
    try:
        monitor = get_chunking_monitor()
        dashboard_data = monitor.get_dashboard_data()
        
        return {
            "success": True,
            "data": dashboard_data
        }
    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting dashboard data: {str(e)}")

@router.get("/metrics")
async def get_current_metrics() -> Dict[str, Any]:
    """Get current performance metrics."""
    try:
        monitor = get_chunking_monitor()
        performance_manager = monitor.performance_manager
        
        stats = performance_manager.get_performance_stats()
        
        return {
            "success": True,
            "metrics": stats,
            "timestamp": "now"
        }
    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting metrics: {str(e)}")

@router.get("/alerts")
async def get_alerts(active_only: bool = True) -> Dict[str, Any]:
    """
    Get performance alerts.
    
    Args:
        active_only: If True, return only unresolved alerts
    """
    try:
        monitor = get_chunking_monitor()
        
        alerts = monitor.alerts
        if active_only:
            alerts = [alert for alert in alerts if not alert.resolved]
        
        # Convert alerts to dict format
        alert_data = []
        for alert in alerts:
            alert_data.append({
                "alert_type": alert.alert_type,
                "severity": alert.severity,
                "message": alert.message,
                "metric_value": alert.metric_value,
                "threshold": alert.threshold,
                "timestamp": alert.timestamp.isoformat(),
                "resolved": alert.resolved
            })
        
        return {
            "success": True,
            "alerts": alert_data,
            "total_count": len(alert_data)
        }
    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting alerts: {str(e)}")

@router.post("/alerts/{alert_index}/resolve")
async def resolve_alert(alert_index: int, admin_user=Depends(require_admin)) -> Dict[str, Any]:
    """
    Resolve a performance alert.
    
    Args:
        alert_index: Index of the alert to resolve
    """
    try:
        monitor = get_chunking_monitor()
        
        if 0 <= alert_index < len(monitor.alerts):
            monitor.alerts[alert_index].resolved = True
            logger.info(f"Alert {alert_index} resolved by admin user")
            
            return {
                "success": True,
                "message": f"Alert {alert_index} resolved"
            }
        else:
            raise HTTPException(status_code=404, detail="Alert not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resolving alert: {e}")
        raise HTTPException(status_code=500, detail=f"Error resolving alert: {str(e)}")

@router.get("/profiles")
async def get_performance_profiles() -> Dict[str, Any]:
    """Get available performance profiles and current profile."""
    try:
        monitor = get_chunking_monitor()
        performance_manager = monitor.performance_manager
        
        # Get available profiles from config
        config = performance_manager.config
        profiles = list(config.get("performance_profiles", {}).keys())
        current_profile = performance_manager.performance_profile
        
        return {
            "success": True,
            "available_profiles": profiles,
            "current_profile": current_profile,
            "profile_descriptions": {
                "speed_optimized": "Optimized for fast processing and low latency",
                "quality_optimized": "Optimized for high-quality embeddings and semantic understanding",
                "balanced": "Balanced performance and quality for general applications",
                "memory_optimized": "Optimized for low memory usage"
            }
        }
    except Exception as e:
        logger.error(f"Error getting profiles: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting profiles: {str(e)}")

@router.post("/profiles/update")
async def update_performance_profile(
    profile_update: PerformanceProfileUpdate,
    admin_user=Depends(require_admin)
) -> Dict[str, Any]:
    """
    Update the performance profile.
    
    Args:
        profile_update: New profile configuration
    """
    try:
        monitor = get_chunking_monitor()
        performance_manager = monitor.performance_manager
        
        # Validate profile exists
        available_profiles = list(performance_manager.config.get("performance_profiles", {}).keys())
        if profile_update.profile not in available_profiles:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid profile. Available profiles: {available_profiles}"
            )
        
        old_profile = performance_manager.performance_profile
        performance_manager.performance_profile = profile_update.profile
        
        logger.info(f"Performance profile updated from {old_profile} to {profile_update.profile} by admin")
        
        return {
            "success": True,
            "message": f"Performance profile updated to {profile_update.profile}",
            "old_profile": old_profile,
            "new_profile": profile_update.profile
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating profile: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating profile: {str(e)}")

@router.get("/monitoring/status")
async def get_monitoring_status() -> Dict[str, Any]:
    """Get current monitoring status."""
    try:
        monitor = get_chunking_monitor()
        
        return {
            "success": True,
            "monitoring_active": monitor.monitoring_active,
            "alerts_count": len([alert for alert in monitor.alerts if not alert.resolved]),
            "performance_history_count": len(monitor.performance_history),
            "optimization_history_count": len(monitor.optimization_history)
        }
    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting monitoring status: {str(e)}")

@router.post("/monitoring/start")
async def start_monitoring(
    config: MonitoringConfig,
    background_tasks: BackgroundTasks,
    admin_user=Depends(require_admin)
) -> Dict[str, Any]:
    """
    Start performance monitoring.
    
    Args:
        config: Monitoring configuration
    """
    try:
        monitor = get_chunking_monitor()
        
        if monitor.monitoring_active:
            return {
                "success": True,
                "message": "Monitoring is already active"
            }
        
        # Start monitoring in background
        background_tasks.add_task(monitor.start_monitoring, config.interval_seconds)
        
        logger.info(f"Performance monitoring started by admin with interval {config.interval_seconds}s")
        
        return {
            "success": True,
            "message": f"Performance monitoring started with {config.interval_seconds}s interval"
        }
    except Exception as e:
        logger.error(f"Error starting monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Error starting monitoring: {str(e)}")

@router.post("/monitoring/stop")
async def stop_monitoring(
    background_tasks: BackgroundTasks,
    admin_user=Depends(require_admin)
) -> Dict[str, Any]:
    """Stop performance monitoring."""
    try:
        monitor = get_chunking_monitor()
        
        if not monitor.monitoring_active:
            return {
                "success": True,
                "message": "Monitoring is not active"
            }
        
        # Stop monitoring in background
        background_tasks.add_task(monitor.stop_monitoring)
        
        logger.info("Performance monitoring stopped by admin")
        
        return {
            "success": True,
            "message": "Performance monitoring stopped"
        }
    except Exception as e:
        logger.error(f"Error stopping monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Error stopping monitoring: {str(e)}")

@router.post("/cache/clear")
async def clear_cache(admin_user=Depends(require_admin)) -> Dict[str, Any]:
    """Clear performance caches."""
    try:
        monitor = get_chunking_monitor()
        performance_manager = monitor.performance_manager
        
        # Clear caches
        performance_manager.clear_cache()
        
        logger.info("Performance caches cleared by admin")
        
        return {
            "success": True,
            "message": "Performance caches cleared"
        }
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail=f"Error clearing cache: {str(e)}")

@router.post("/optimize/manual")
async def manual_optimization(admin_user=Depends(require_admin)) -> Dict[str, Any]:
    """Trigger manual performance optimization."""
    try:
        monitor = get_chunking_monitor()
        performance_manager = monitor.performance_manager
        
        # Trigger optimization
        performance_manager.optimize_profile()
        
        logger.info("Manual performance optimization triggered by admin")
        
        return {
            "success": True,
            "message": "Manual optimization triggered",
            "current_profile": performance_manager.performance_profile
        }
    except Exception as e:
        logger.error(f"Error during manual optimization: {e}")
        raise HTTPException(status_code=500, detail=f"Error during manual optimization: {str(e)}")

@router.get("/report/generate")
async def generate_performance_report(admin_user=Depends(require_admin)) -> Dict[str, Any]:
    """Generate and save a performance report."""
    try:
        monitor = get_chunking_monitor()
        
        # Generate report filename
        from datetime import datetime
        filename = f"chunking_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Save report
        monitor.save_performance_report(filename)
        
        return {
            "success": True,
            "message": "Performance report generated",
            "filename": filename
        }
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating report: {str(e)}")

@router.get("/thresholds")
async def get_performance_thresholds() -> Dict[str, Any]:
    """Get current performance thresholds."""
    try:
        monitor = get_chunking_monitor()
        
        return {
            "success": True,
            "thresholds": monitor.thresholds
        }
    except Exception as e:
        logger.error(f"Error getting thresholds: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting thresholds: {str(e)}")

@router.post("/thresholds/update")
async def update_performance_thresholds(
    threshold_update: ThresholdUpdate,
    admin_user=Depends(require_admin)
) -> Dict[str, Any]:
    """
    Update performance thresholds.
    
    Args:
        threshold_update: New threshold configuration
    """
    try:
        monitor = get_chunking_monitor()
        
        metric_name = threshold_update.metric_name
        if metric_name not in monitor.thresholds:
            raise HTTPException(status_code=400, detail=f"Unknown metric: {metric_name}")
        
        # Update thresholds
        monitor.thresholds[metric_name] = {
            "warning": threshold_update.warning_threshold,
            "critical": threshold_update.critical_threshold
        }
        
        logger.info(f"Performance thresholds updated for {metric_name} by admin")
        
        return {
            "success": True,
            "message": f"Thresholds updated for {metric_name}",
            "new_thresholds": monitor.thresholds[metric_name]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating thresholds: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating thresholds: {str(e)}")
