"""
Pydantic schemas for database models, used for validation and serialization.
"""
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import uuid
import enum

# Re-using TaskStatus and WorkflowStatus enums from workflow_manager
# Ideally, these enums would be in a shared location if used by both DB and runtime logic.
# For now, let's assume they are accessible or redefined here if necessary.
# from backend.agents.orchestration.workflow_manager import TaskStatus, WorkflowStatus
# For simplicity in this step, I'll redefine them or use strings.
# In a real refactor, move enums to a shared location.

class TaskStatusEnum(str, enum.Enum): # Assuming enum is imported
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    WAITING = "waiting"

class WorkflowStatusEnum(str, enum.Enum): # Assuming enum is imported
    CREATED = "created"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

# --- Task Schemas ---
class TaskBase(BaseModel):
    name: str
    agent_type: str
    input_data: Dict[str, Any] = Field(default_factory=dict)
    dependencies: List[str] = Field(default_factory=list)
    status: TaskStatusEnum = TaskStatusEnum.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 300
    task_metadata: Dict[str, Any] = Field(default_factory=dict)
    workflow_id: uuid.UUID # Foreign key to Workflow

class TaskCreate(TaskBase):
    pass

class TaskUpdate(BaseModel):
    name: Optional[str] = None
    agent_type: Optional[str] = None
    input_data: Optional[Dict[str, Any]] = None
    dependencies: Optional[List[str]] = None
    status: Optional[TaskStatusEnum] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: Optional[int] = None
    max_retries: Optional[int] = None
    timeout_seconds: Optional[int] = None
    task_metadata: Optional[Dict[str, Any]] = None

class TaskInDB(TaskBase):
    id: uuid.UUID # Primary key

    class Config:
        from_attributes = True # Pydantic V2 style

# --- Workflow Schemas ---
class WorkflowBase(BaseModel):
    name: str
    description: Optional[str] = None
    status: WorkflowStatusEnum = WorkflowStatusEnum.CREATED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    user_id: Optional[str] = None # Assuming user_id can be string
    session_id: Optional[str] = None
    context: Dict[str, Any] = Field(default_factory=dict)
    workflow_metadata: Dict[str, Any] = Field(default_factory=dict)

class WorkflowCreate(WorkflowBase):
    pass

class WorkflowUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[WorkflowStatusEnum] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    workflow_metadata: Optional[Dict[str, Any]] = None

class WorkflowInDB(WorkflowBase):
    id: uuid.UUID # Primary key
    tasks: List[TaskInDB] = [] # Related tasks

    class Config:
        from_attributes = True # Pydantic V2 style
