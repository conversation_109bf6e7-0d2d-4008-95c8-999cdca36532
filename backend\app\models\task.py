"""
Task models for the Datagenius backend.

This module provides Pydantic models for task-related functionality.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field


class TaskBase(BaseModel):
    """Base model for task data."""
    task_type: str
    input_file_id: Optional[str] = None
    config: Optional[Dict[str, Any]] = None


class TaskCreate(TaskBase):
    """Model for creating a new task."""
    pass


class TaskResponse(TaskBase):
    """Model for task data returned to the client."""
    id: str
    user_id: int
    status: str
    message: Optional[str] = None
    result_file_path: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class TaskListResponse(BaseModel):
    """Model for task list response."""
    tasks: List[TaskResponse]


class TaskStatusUpdate(BaseModel):
    """Model for updating task status."""
    status: str
    message: Optional[str] = None
    result_file_path: Optional[str] = None
