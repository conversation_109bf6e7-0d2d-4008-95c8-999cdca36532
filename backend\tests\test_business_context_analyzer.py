"""
Tests for the Production Business Context Analyzer.

This module tests the AI-powered business context analysis functionality
to ensure it provides better results than mock implementations.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

from agents.marketing_agent.business_context import BusinessContextAnalyzer, BusinessContext
from agents.components.base_component import AgentContext


class TestBusinessContextAnalyzer:
    """Test suite for BusinessContextAnalyzer."""
    
    @pytest.fixture
    def analyzer(self):
        """Create a BusinessContextAnalyzer instance for testing."""
        return BusinessContextAnalyzer(llm_provider="groq")
    
    @pytest.fixture
    def sample_user_data(self):
        """Sample user data for testing."""
        return {
            "data_sources": ["doc1", "doc2"],
            "conversation_history": [
                {"role": "user", "content": "I run a SaaS company focused on small businesses"},
                {"role": "assistant", "content": "That's great! How can I help with your marketing?"},
                {"role": "user", "content": "We need help with lead generation and content marketing"}
            ],
            "user_profile": {
                "company": "TechStart Inc",
                "industry": "Technology",
                "role": "Founder"
            }
        }
    
    @pytest.fixture
    def mock_llm_processor(self):
        """Mock LLM processor for testing."""
        mock_processor = AsyncMock()
        mock_context = AgentContext()
        mock_context.set_field("llm_response", """
Industry: Technology
Business Type: B2B
Business Size: Startup
Target Market: Small businesses
Key Products: SaaS platform, Analytics tools
Marketing Challenges: Lead generation, Content marketing
Confidence Score: 0.8
        """)
        mock_context.set_status("success")
        mock_processor.process.return_value = mock_context
        return mock_processor
    
    @pytest.mark.asyncio
    async def test_analyze_context_success(self, analyzer, sample_user_data, mock_llm_processor):
        """Test successful business context analysis."""
        with patch.object(analyzer, 'llm_processor', mock_llm_processor):
            with patch.object(analyzer, '_analyze_data_sources', return_value={"industry": "Technology"}):
                result = await analyzer.analyze_context(sample_user_data)
                
                assert isinstance(result, BusinessContext)
                assert result.confidence_score > 0.0
                assert result.industry is not None
    
    @pytest.mark.asyncio
    async def test_analyze_context_with_data_sources(self, analyzer, sample_user_data):
        """Test context analysis with data sources."""
        with patch.object(analyzer, '_analyze_data_sources') as mock_analyze:
            mock_analyze.return_value = {
                "industry": "Technology",
                "business_type": "B2B",
                "key_products": ["Software platform"]
            }
            
            with patch.object(analyzer, 'llm_processor') as mock_processor:
                mock_context = AgentContext()
                mock_context.set_field("llm_response", "Industry: Technology\nConfidence: 0.7")
                mock_context.set_status("success")
                mock_processor.process.return_value = mock_context
                
                result = await analyzer.analyze_context(sample_user_data)
                
                mock_analyze.assert_called_once()
                assert result.confidence_score > 0.0
    
    @pytest.mark.asyncio
    async def test_analyze_context_with_conversation_history(self, analyzer, sample_user_data):
        """Test context analysis with conversation history."""
        with patch.object(analyzer, '_analyze_conversation_history') as mock_analyze:
            mock_analyze.return_value = {
                "marketing_challenges": ["lead generation", "content marketing"],
                "business_size": "startup"
            }
            
            with patch.object(analyzer, 'llm_processor') as mock_processor:
                mock_context = AgentContext()
                mock_context.set_field("llm_response", "Marketing Challenges: lead generation\nConfidence: 0.6")
                mock_context.set_status("success")
                mock_processor.process.return_value = mock_context
                
                result = await analyzer.analyze_context(sample_user_data)
                
                mock_analyze.assert_called_once()
                assert result.confidence_score > 0.0
    
    @pytest.mark.asyncio
    async def test_analyze_context_error_handling(self, analyzer, sample_user_data):
        """Test error handling in context analysis."""
        with patch.object(analyzer, 'llm_processor') as mock_processor:
            mock_processor.process.side_effect = Exception("LLM error")
            
            result = await analyzer.analyze_context(sample_user_data)
            
            assert isinstance(result, BusinessContext)
            assert result.confidence_score <= 0.1  # Low confidence due to error
            assert result.industry == "Unknown"
    
    @pytest.mark.asyncio
    async def test_analyze_empty_data(self, analyzer):
        """Test analysis with empty data."""
        empty_data = {
            "data_sources": [],
            "conversation_history": [],
            "user_profile": {}
        }
        
        result = await analyzer.analyze_context(empty_data)
        
        assert isinstance(result, BusinessContext)
        assert result.confidence_score == 0.0
    
    def test_parse_analysis_response(self, analyzer):
        """Test parsing of AI analysis responses."""
        response = """
        Industry: Technology
        Business Type: B2B SaaS
        Target Market: Small businesses
        - SMBs with 10-50 employees
        - Growing companies
        Marketing Challenges:
        - Lead generation
        - Content creation
        """
        
        result = analyzer._parse_analysis_response(response)
        
        assert "industry" in result
        assert result["industry"] == "Technology"
        assert "business_type" in result
        assert "target_market" in result
    
    def test_parse_business_context(self, analyzer):
        """Test parsing of business context from AI response."""
        response = """
        Industry: Healthcare
        Business Type: B2C
        Business Size: Medium
        Target Market: Individual patients
        Key Products: Telemedicine platform, Health monitoring
        Marketing Challenges: Patient acquisition, Trust building
        Confidence Score: 0.75
        """
        
        result = analyzer._parse_business_context(response)
        
        assert isinstance(result, BusinessContext)
        assert result.industry == "Healthcare"
        assert result.business_type == "B2C"
        assert result.confidence_score == 0.75
        assert len(result.key_products) > 0
        assert len(result.marketing_challenges) > 0
    
    def test_extract_confidence_score(self, analyzer):
        """Test confidence score extraction."""
        # Test explicit confidence score
        response1 = "Analysis complete. Confidence score: 0.85"
        score1 = analyzer._extract_confidence_score(response1)
        assert score1 == 0.85
        
        # Test confidence based on content length
        response2 = "Short response"
        score2 = analyzer._extract_confidence_score(response2)
        assert 0.0 <= score2 <= 1.0
        
        # Test long response
        response3 = "This is a very detailed analysis " * 20
        score3 = analyzer._extract_confidence_score(response3)
        assert score3 >= 0.5
    
    @pytest.mark.asyncio
    async def test_synthesize_context(self, analyzer):
        """Test context synthesis from multiple analyses."""
        analysis_results = {
            "data_analysis": {
                "industry": "Technology",
                "business_type": "B2B"
            },
            "conversation_analysis": {
                "marketing_challenges": ["lead generation"],
                "business_size": "startup"
            },
            "profile_analysis": {
                "industry": "Technology",  # Consistent with data analysis
                "target_market": "Small businesses"
            }
        }
        
        with patch.object(analyzer, 'llm_processor') as mock_processor:
            mock_context = AgentContext()
            mock_context.set_field("llm_response", """
            Industry: Technology
            Business Type: B2B
            Business Size: Startup
            Target Market: Small businesses
            Marketing Challenges: Lead generation
            Confidence Score: 0.8
            """)
            mock_context.set_status("success")
            mock_processor.process.return_value = mock_context
            
            result = await analyzer._synthesize_context(analysis_results)
            
            assert isinstance(result, BusinessContext)
            assert result.industry == "Technology"
            assert result.business_type == "B2B"
            assert result.confidence_score > 0.0
    
    @pytest.mark.asyncio
    async def test_comparison_with_mock_implementation(self, analyzer, sample_user_data):
        """Test that AI analysis provides better results than mock implementation."""
        # Mock implementation result (simplified)
        mock_result = BusinessContext(
            industry="Technology",
            business_type="B2B SaaS",
            target_market="Small to medium businesses",
            key_products=["Software platform", "Analytics tools"],
            marketing_challenges=["Lead generation", "Brand awareness", "Customer retention"],
            competitive_advantages=["Innovative features", "Excellent support", "Competitive pricing"],
            confidence_score=1.0  # Mock always returns high confidence
        )
        
        # AI analysis result
        with patch.object(analyzer, 'llm_processor') as mock_processor:
            mock_context = AgentContext()
            mock_context.set_field("llm_response", """
            Industry: Technology
            Business Type: B2B SaaS
            Business Size: Startup
            Target Market: Small businesses needing automation
            Key Products: SaaS platform for workflow automation
            Marketing Challenges: Lead generation, Content marketing
            Competitive Advantages: AI-powered features, Excellent customer support
            Confidence Score: 0.8
            """)
            mock_context.set_status("success")
            mock_processor.process.return_value = mock_context
            
            ai_result = await analyzer.analyze_context(sample_user_data)
            
            # AI result should be more specific and contextual
            assert ai_result.target_market != mock_result.target_market  # More specific
            assert len(ai_result.marketing_challenges) <= len(mock_result.marketing_challenges)  # More focused
            assert ai_result.confidence_score <= 1.0  # More realistic confidence
            
            # AI result should reflect actual conversation content
            assert "lead generation" in [challenge.lower() for challenge in ai_result.marketing_challenges]
