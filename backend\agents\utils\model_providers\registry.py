"""
Model provider registry for the Datagenius backend.

This module provides a registry for model providers, allowing providers to be
registered, retrieved, and instantiated by ID.
"""

import logging
import os
from typing import Dict, Type, Optional, List, Any, Union
from langchain.schema.language_model import BaseLanguageModel
from langchain_core.language_models.chat_models import BaseChatModel
from .base import ModelProvider
from .exceptions import ProviderNotFoundError, ProviderNotAvailableError

    
# Configure logging
logger = logging.getLogger(__name__)


class ModelProviderRegistry:
    """Registry for model providers."""

    _providers: Dict[str, Type[ModelProvider]] = {}
    _instances: Dict[str, ModelProvider] = {}
    _default_provider_id: Optional[str] = None

    @classmethod
    def register(cls, provider_id: str, provider_class: Type[ModelProvider]) -> None:
        """
        Register a provider class.

        Args:
            provider_id: ID of the provider
            provider_class: Provider class to register
        """
        cls._providers[provider_id] = provider_class
        logger.info(f"Registered model provider class {provider_class.__name__} with ID '{provider_id}'")

    @classmethod
    def get_provider_class(cls, provider_id: str) -> Optional[Type[ModelProvider]]:
        """
        Get a provider class by ID.

        Args:
            provider_id: ID of the provider

        Returns:
            Provider class if found, None otherwise
        """
        provider_class = cls._providers.get(provider_id)
        if provider_class is None:
            logger.warning(f"No model provider class found for ID '{provider_id}'")
        return provider_class

    @classmethod
    def list_registered_providers(cls) -> List[str]:
        """
        List all registered provider IDs.

        Returns:
            List of registered provider IDs
        """
        return list(cls._providers.keys())

    @classmethod
    async def get_provider(cls, provider_id: str, config: Optional[Dict[str, Any]] = None) -> ModelProvider:
        """
        Get a provider instance by ID, initializing it if necessary.

        Args:
            provider_id: ID of the provider
            config: Optional configuration for the provider

        Returns:
            Initialized provider instance

        Raises:
            ProviderNotFoundError: If the provider is not found
        """
        # Check if we already have an initialized instance
        if provider_id in cls._instances:
            return cls._instances[provider_id]

        # Get the provider class
        provider_class = cls.get_provider_class(provider_id)
        if provider_class is None:
            raise ProviderNotFoundError(f"No provider found with ID '{provider_id}'")

        # Create and initialize the provider
        provider = provider_class()
        if config is None:
            config = {}

        # Initialize the provider
        await provider.initialize(config)
        cls._instances[provider_id] = provider
        return provider

    @classmethod
    async def get_model(cls, 
                       provider_id: Optional[str] = None, 
                       model_id: Optional[str] = None, 
                       config: Optional[Dict[str, Any]] = None) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Get a model instance from a provider.

        Args:
            provider_id: ID of the provider (uses default if None)
            model_id: ID of the model (uses provider's default if None)
            config: Optional configuration for the model

        Returns:
            Initialized model instance

        Raises:
            ProviderNotFoundError: If the provider is not found
            ProviderNotAvailableError: If the provider is not available
        """
        # Use default provider if none specified
        if provider_id is None:
            provider_id = cls._default_provider_id
            if provider_id is None:
                # Try to find an available provider
                provider_id = await cls._find_available_provider()
                if provider_id is None:
                    raise ProviderNotFoundError("No provider ID specified and no default provider available")

        # Get the provider
        provider = await cls.get_provider(provider_id, config)

        # Check if the provider is available
        if not await provider.is_available():
            # Try to find another available provider
            fallback_provider_id = await cls._find_available_provider(exclude=[provider_id])
            if fallback_provider_id is None:
                raise ProviderNotAvailableError(f"Provider '{provider_id}' is not available and no fallback providers found")
            
            logger.warning(f"Provider '{provider_id}' is not available, falling back to '{fallback_provider_id}'")
            provider = await cls.get_provider(fallback_provider_id, config)

        # Get the model from the provider
        return await provider.get_model(model_id, config)

    @classmethod
    def set_default_provider(cls, provider_id: str) -> None:
        """
        Set the default provider.

        Args:
            provider_id: ID of the provider to set as default

        Raises:
            ProviderNotFoundError: If the provider is not found
        """
        if provider_id not in cls._providers:
            raise ProviderNotFoundError(f"No provider found with ID '{provider_id}'")
        
        cls._default_provider_id = provider_id
        logger.info(f"Set default provider to '{provider_id}'")

    @classmethod
    async def _find_available_provider(cls, exclude: Optional[List[str]] = None) -> Optional[str]:
        """
        Find an available provider.

        Args:
            exclude: List of provider IDs to exclude

        Returns:
            ID of an available provider, or None if none found
        """
        if exclude is None:
            exclude = []

        # Check environment variables for API keys to determine priority
        provider_priorities = []
        
        # Check for Groq API key
        if os.getenv("GROQ_API_KEY") and "groq" not in exclude:
            provider_priorities.append("groq")
            
        # Check for OpenAI API key
        if os.getenv("OPENAI_API_KEY") and "openai" not in exclude:
            provider_priorities.append("openai")
            
        # Check for Anthropic API key
        if os.getenv("ANTHROPIC_API_KEY") and "anthropic" not in exclude:
            provider_priorities.append("anthropic")

        # Check for Gemini API key
        if os.getenv("GEMINI_API_KEY") and "gemini" not in exclude:
            provider_priorities.append("gemini")

        # Check for OpenRouter API key
        if os.getenv("OPENROUTER_API_KEY") and "openrouter" not in exclude:
            provider_priorities.append("openrouter")

        # Check for Requesty API key (if applicable)
        # if os.getenv("REQUESTY_API_KEY") and "requesty" not in exclude:
        #     provider_priorities.append("requesty")

        # Add Ollama as a fallback
        if "ollama" not in exclude:
            provider_priorities.append("ollama")
            
        # Try each provider in priority order
        for provider_id in provider_priorities:
            if provider_id not in cls._providers:
                continue
                
            try:
                provider = await cls.get_provider(provider_id, {})
                if await provider.is_available():
                    return provider_id
            except Exception as e:
                logger.warning(f"Error checking availability of provider '{provider_id}': {str(e)}")
                
        # If no providers are available, return None
        return None
