/**
 * Comprehensive validation utilities for dashboard operations.
 * Provides centralized validation logic for all dashboard-related data.
 */

import { z } from 'zod';

// Dashboard validation schemas
export const DashboardCreateSchema = z.object({
  name: z.string()
    .min(1, 'Dashboard name is required')
    .max(100, 'Dashboard name must be less than 100 characters')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Dashboard name contains invalid characters'),
  description: z.string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  is_default: z.boolean().default(false),
  is_public: z.boolean().default(false),
  refresh_interval: z.number()
    .min(30, 'Refresh interval must be at least 30 seconds')
    .max(3600, 'Refresh interval must be less than 1 hour')
    .default(300),
  tags: z.array(z.string().max(50)).max(10, 'Maximum 10 tags allowed').default([]),
  theme_config: z.object({
    primary_color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format').optional(),
    background_color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format').optional(),
    text_color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format').optional(),
    border_color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format').optional(),
  }).default({}),
  layout_config: z.object({
    columns: z.number().min(6).max(24).default(12),
    rows: z.number().min(6).max(24).default(12),
    grid_gap: z.number().min(0).max(32).default(16),
    responsive: z.boolean().default(true),
  }).default({}),
});

export const DashboardUpdateSchema = DashboardCreateSchema.partial();

// Widget validation schemas
export const WidgetCreateSchema = z.object({
  section_id: z.string().uuid('Invalid section ID'),
  title: z.string()
    .min(1, 'Widget title is required')
    .max(100, 'Widget title must be less than 100 characters'),
  description: z.string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  widget_type: z.enum(['chart', 'table', 'kpi', 'text'], {
    errorMap: () => ({ message: 'Invalid widget type' })
  }),
  data_config: z.object({
    data_source_id: z.string().uuid('Invalid data source ID').optional(),
    query: z.string().max(5000, 'Query must be less than 5000 characters').optional(),
    x_field: z.string().max(100).optional(),
    y_field: z.string().max(100).optional(),
    filters: z.record(z.any()).optional(),
  }).default({}),
  visualization_config: z.object({
    chart_type: z.string().max(50).optional(),
    color_scheme: z.string().max(50).optional(),
    show_legend: z.boolean().default(true),
    show_grid: z.boolean().default(true),
    custom_options: z.record(z.any()).optional(),
  }).default({}),
  position_config: z.object({
    x: z.number().min(0).max(23),
    y: z.number().min(0),
    w: z.number().min(1).max(12),
    h: z.number().min(1).max(12),
  }),
  customization: z.record(z.any()).default({}),
  refresh_interval: z.number()
    .min(30, 'Refresh interval must be at least 30 seconds')
    .max(3600, 'Refresh interval must be less than 1 hour')
    .default(300),
  is_active: z.boolean().default(true),
});

export const WidgetUpdateSchema = WidgetCreateSchema.partial().omit({ section_id: true });

// Section validation schemas
export const SectionCreateSchema = z.object({
  dashboard_id: z.string().uuid('Invalid dashboard ID'),
  name: z.string()
    .min(1, 'Section name is required')
    .max(100, 'Section name must be less than 100 characters'),
  description: z.string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format').optional(),
  icon: z.string().max(50).optional(),
  layout_config: z.object({
    columns: z.number().min(1).max(12).default(12),
    rows: z.number().min(1).default(6),
    grid_gap: z.number().min(0).max(32).default(16),
  }).default({}),
  customization: z.record(z.any()).default({}),
  data_source_id: z.string().uuid('Invalid data source ID').optional(),
  position: z.number().min(0).default(0),
  is_active: z.boolean().default(true),
});

export const SectionUpdateSchema = SectionCreateSchema.partial().omit({ dashboard_id: true });

// Data source validation schemas
export const DataSourceCreateSchema = z.object({
  name: z.string()
    .min(1, 'Data source name is required')
    .max(100, 'Data source name must be less than 100 characters'),
  type: z.enum(['csv', 'json', 'api', 'database', 'file'], {
    errorMap: () => ({ message: 'Invalid data source type' })
  }),
  connection_config: z.object({
    url: z.string().url('Invalid URL').optional(),
    host: z.string().max(255).optional(),
    port: z.number().min(1).max(65535).optional(),
    database: z.string().max(100).optional(),
    username: z.string().max(100).optional(),
    password: z.string().max(255).optional(),
    file_path: z.string().max(500).optional(),
    api_key: z.string().max(255).optional(),
    headers: z.record(z.string()).optional(),
  }).default({}),
  schema_config: z.record(z.any()).default({}),
  is_active: z.boolean().default(true),
});

export const DataSourceUpdateSchema = DataSourceCreateSchema.partial();

// Validation result types
export interface ValidationResult<T = any> {
  success: boolean;
  data?: T;
  errors?: string[];
  fieldErrors?: Record<string, string[]>;
}

// Validation functions
export function validateDashboardCreate(data: unknown): ValidationResult {
  try {
    const result = DashboardCreateSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(e => e.message),
        fieldErrors: error.errors.reduce((acc, e) => {
          const field = e.path.join('.');
          if (!acc[field]) acc[field] = [];
          acc[field].push(e.message);
          return acc;
        }, {} as Record<string, string[]>),
      };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

export function validateDashboardUpdate(data: unknown): ValidationResult {
  try {
    const result = DashboardUpdateSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(e => e.message),
        fieldErrors: error.errors.reduce((acc, e) => {
          const field = e.path.join('.');
          if (!acc[field]) acc[field] = [];
          acc[field].push(e.message);
          return acc;
        }, {} as Record<string, string[]>),
      };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

export function validateWidgetCreate(data: unknown): ValidationResult {
  try {
    const result = WidgetCreateSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(e => e.message),
        fieldErrors: error.errors.reduce((acc, e) => {
          const field = e.path.join('.');
          if (!acc[field]) acc[field] = [];
          acc[field].push(e.message);
          return acc;
        }, {} as Record<string, string[]>),
      };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

export function validateWidgetUpdate(data: unknown): ValidationResult {
  try {
    const result = WidgetUpdateSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(e => e.message),
        fieldErrors: error.errors.reduce((acc, e) => {
          const field = e.path.join('.');
          if (!acc[field]) acc[field] = [];
          acc[field].push(e.message);
          return acc;
        }, {} as Record<string, string[]>),
      };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

export function validateSectionCreate(data: unknown): ValidationResult {
  try {
    const result = SectionCreateSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(e => e.message),
        fieldErrors: error.errors.reduce((acc, e) => {
          const field = e.path.join('.');
          if (!acc[field]) acc[field] = [];
          acc[field].push(e.message);
          return acc;
        }, {} as Record<string, string[]>),
      };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

export function validateSectionUpdate(data: unknown): ValidationResult {
  try {
    const result = SectionUpdateSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(e => e.message),
        fieldErrors: error.errors.reduce((acc, e) => {
          const field = e.path.join('.');
          if (!acc[field]) acc[field] = [];
          acc[field].push(e.message);
          return acc;
        }, {} as Record<string, string[]>),
      };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

export function validateDataSourceCreate(data: unknown): ValidationResult {
  try {
    const result = DataSourceCreateSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(e => e.message),
        fieldErrors: error.errors.reduce((acc, e) => {
          const field = e.path.join('.');
          if (!acc[field]) acc[field] = [];
          acc[field].push(e.message);
          return acc;
        }, {} as Record<string, string[]>),
      };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

export function validateDataSourceUpdate(data: unknown): ValidationResult {
  try {
    const result = DataSourceUpdateSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(e => e.message),
        fieldErrors: error.errors.reduce((acc, e) => {
          const field = e.path.join('.');
          if (!acc[field]) acc[field] = [];
          acc[field].push(e.message);
          return acc;
        }, {} as Record<string, string[]>),
      };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}

// Utility functions for common validations
export const ValidationUtils = {
  isValidUUID: (value: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(value);
  },

  isValidColor: (value: string): boolean => {
    const colorRegex = /^#[0-9A-Fa-f]{6}$/;
    return colorRegex.test(value);
  },

  isValidURL: (value: string): boolean => {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  },

  sanitizeString: (value: string, maxLength: number = 255): string => {
    return value.trim().slice(0, maxLength);
  },

  validateFileSize: (file: File, maxSizeMB: number = 10): boolean => {
    return file.size <= maxSizeMB * 1024 * 1024;
  },

  validateFileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type);
  },
};
