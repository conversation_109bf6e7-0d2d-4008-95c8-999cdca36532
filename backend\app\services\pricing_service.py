"""
Pricing Service for the Datagenius backend.

This module provides services for managing pricing tiers, subscriptions,
discounts, promotions, and dynamic pricing.
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func as sa_func # func is in sqlalchemy, not sqlalchemy.orm
from datetime import datetime, timedelta
import uuid # For generating unique IDs if needed for non-db entities
from fastapi import Depends # Added for dependency injection
import logging # Added logging

from ..models import pricing as pricing_models # SQLAlchemy models for pricing, subscription, etc.
from ..models.auth import User as UserModelDB # Corrected import for User model
from ..database import CartItem as CartItemModelDB # Corrected import for CartItem model
from ..database import Persona as PersonaModel # Import Persona model
from ..database import Purchase as PurchaseModel # Import Purchase model
from ..database import get_db # To get DB session

logger = logging.getLogger(__name__) # Initialize logger

class PricingService:
    def __init__(self, db: Session):
        self.db = db

    # Pricing Tier Management
    async def create_pricing_tier(self, name: str, base_price: float, currency: str = "USD",
                                  description: Optional[str] = None, billing_period: Optional[str] = "monthly",
                                  features: Optional[List[str]] = None, persona_id: Optional[str] = None,
                                  is_active: bool = True) -> pricing_models.PricingTier:
        tier_id = str(uuid.uuid4())
        db_tier = pricing_models.PricingTier(
            id=tier_id,
            name=name,
            base_price=base_price,
            currency=currency,
            description=description,
            billing_period=billing_period,
            features=features or [],
            persona_id=persona_id,
            is_active=is_active
        )
        self.db.add(db_tier)
        self.db.commit()
        self.db.refresh(db_tier)
        return db_tier

    async def get_pricing_tier(self, tier_id: str) -> Optional[pricing_models.PricingTier]:
        return self.db.query(pricing_models.PricingTier).filter(pricing_models.PricingTier.id == tier_id).first()

    async def get_all_pricing_tiers(self, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None) -> List[pricing_models.PricingTier]:
        query = self.db.query(pricing_models.PricingTier)
        if is_active is not None:
            query = query.filter(pricing_models.PricingTier.is_active == is_active)
        return query.offset(skip).limit(limit).all()

    async def update_pricing_tier(self, tier_id: str, updates: Dict[str, Any]) -> Optional[pricing_models.PricingTier]:
        db_tier = await self.get_pricing_tier(tier_id)
        if not db_tier:
            return None
        for key, value in updates.items():
            if hasattr(db_tier, key):
                setattr(db_tier, key, value)
        db_tier.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(db_tier)
        return db_tier

    # Subscription Management
    async def create_subscription(self, user_id: int, tier_id: str,
                                  status: str = "active", auto_renew: bool = True) -> Optional[pricing_models.Subscription]:
        user = self.db.query(UserModelDB).filter(UserModelDB.id == user_id).first()
        tier = await self.get_pricing_tier(tier_id)
        if not user or not tier:
            return None # Or raise an exception

        # Calculate next billing date based on tier.billing_period
        next_billing_date = None
        if tier.billing_period == "monthly":
            next_billing_date = datetime.utcnow() + timedelta(days=30)
        elif tier.billing_period == "yearly":
            next_billing_date = datetime.utcnow() + timedelta(days=365)

        sub_id = str(uuid.uuid4())
        db_subscription = pricing_models.Subscription(
            id=sub_id,
            user_id=user_id,
            pricing_tier_id=tier_id,
            status=status,
            auto_renew=auto_renew,
            next_billing_date=next_billing_date
        )
        self.db.add(db_subscription)
        self.db.commit()
        self.db.refresh(db_subscription)
        return db_subscription

    async def get_user_subscriptions(self, user_id: int) -> List[pricing_models.Subscription]:
        return self.db.query(pricing_models.Subscription).filter(pricing_models.Subscription.user_id == user_id).all()

    async def get_subscription(self, subscription_id: str) -> Optional[pricing_models.Subscription]:
        return self.db.query(pricing_models.Subscription).filter(pricing_models.Subscription.id == subscription_id).first()

    async def cancel_subscription(self, subscription_id: str) -> Optional[pricing_models.Subscription]:
        db_sub = await self.get_subscription(subscription_id)
        if db_sub:
            db_sub.status = "cancelled"
            db_sub.auto_renew = False
            # Potentially set end_date to current billing cycle end
            db_sub.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(db_sub)
        return db_sub

    # Discount Management
    async def create_discount(self, code: str, discount_type: str, value: float,
                              description: Optional[str] = None, max_uses: Optional[int] = None,
                              valid_until: Optional[datetime] = None, is_active: bool = True,
                              applicable_tier_ids: Optional[List[str]] = None) -> pricing_models.Discount:
        discount_id = str(uuid.uuid4())
        db_discount = pricing_models.Discount(
            id=discount_id,
            code=code,
            description=description,
            discount_type=discount_type,
            value=value,
            max_uses=max_uses,
            valid_until=valid_until,
            is_active=is_active,
            applicable_tier_ids=applicable_tier_ids or []
        )
        self.db.add(db_discount)
        self.db.commit()
        self.db.refresh(db_discount)
        return db_discount

    async def get_discount_by_code(self, code: str) -> Optional[pricing_models.Discount]:
        return self.db.query(pricing_models.Discount).filter(pricing_models.Discount.code == code, pricing_models.Discount.is_active == True).first()

    async def validate_and_apply_discount(self, code: str, tier_id: Optional[str] = None, user_id: Optional[int] = None) -> Optional[pricing_models.Discount]:
        discount = await self.get_discount_by_code(code)
        if not discount:
            return None # Invalid code
        if discount.valid_until and discount.valid_until < datetime.utcnow():
            return None # Expired
        if discount.max_uses is not None and discount.uses_count >= discount.max_uses:
            return None # Max uses reached
        if discount.applicable_tier_ids and tier_id not in discount.applicable_tier_ids:
            return None # Not applicable to this tier

        # Potentially add user-specific discount usage tracking here

        return discount

    # Promotion Management
    async def create_promotion(self, name: str, promotion_type: str,
                               description: Optional[str] = None, details: Optional[Dict[str, Any]] = None,
                               start_date: Optional[datetime] = None, end_date: Optional[datetime] = None,
                               is_active: bool = True, target_audience_criteria: Optional[Dict[str, Any]] = None) -> pricing_models.Promotion:
        promo_id = str(uuid.uuid4())
        db_promotion = pricing_models.Promotion(
            id=promo_id,
            name=name,
            description=description,
            promotion_type=promotion_type,
            details=details or {},
            start_date=start_date or datetime.utcnow(),
            end_date=end_date,
            is_active=is_active,
            target_audience_criteria=target_audience_criteria or {}
        )
        self.db.add(db_promotion)
        self.db.commit()
        self.db.refresh(db_promotion)
        return db_promotion

    async def get_active_promotions(self, user_id: Optional[int] = None) -> List[pricing_models.Promotion]:
        base_query = self.db.query(pricing_models.Promotion).filter(
            pricing_models.Promotion.is_active == True,
            (pricing_models.Promotion.start_date <= datetime.utcnow()),
            ((pricing_models.Promotion.end_date == None) | (pricing_models.Promotion.end_date >= datetime.utcnow()))
        )
        
        promotions = base_query.all()
        
        if not user_id:
            return [p for p in promotions if not p.target_audience_criteria] # Return only non-targeted promotions

        # If user_id is provided, filter based on criteria
        user = self.db.query(UserModelDB).filter(UserModelDB.id == user_id).first()
        if not user:
            return [p for p in promotions if not p.target_audience_criteria] # User not found, return non-targeted

        eligible_promotions = []
        for promo in promotions:
            if not promo.target_audience_criteria:
                eligible_promotions.append(promo)
                continue

            # Example criteria check (can be expanded)
            # This is a simplified check. A more robust system might involve a rules engine.
            criteria_met = True
            if "user_tier" in promo.target_audience_criteria:
                user_tier_name = "standard" # Default if no active subscription or tier name
                active_subscription = self.db.query(pricing_models.Subscription).filter(
                    pricing_models.Subscription.user_id == user_id,
                    pricing_models.Subscription.status == "active" # Assuming 'active' status
                ).first()
                if active_subscription and active_subscription.tier: # tier is the relationship to PricingTier
                    user_tier_name = active_subscription.tier.name.lower() # Get tier name, e.g., "premium"
                
                if user_tier_name != promo.target_audience_criteria["user_tier"].lower():
                    criteria_met = False
            
            if "min_purchase_count" in promo.target_audience_criteria:
                purchase_count = self.db.query(sa_func.count(PurchaseModel.id)).filter(
                    PurchaseModel.user_id == user_id,
                    PurchaseModel.payment_status == 'completed'
                ).scalar() or 0
                if purchase_count < promo.target_audience_criteria["min_purchase_count"]:
                    criteria_met = False
            
            if criteria_met:
                eligible_promotions.append(promo)
                
        return eligible_promotions

    # Enhanced Dynamic Pricing Engine
    async def calculate_dynamic_price(self, persona_id: str, user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Enhanced ML-based dynamic pricing with real-time adjustments.
        """
        logger.info(f"Calculating dynamic price for persona: {persona_id}, user_context: {user_context}")

        try:
            # Get base price from persona or pricing tier
            base_price, currency = await self._get_base_price(persona_id)

            # Apply dynamic pricing factors
            price_multiplier = await self._calculate_price_multiplier(persona_id, user_context)

            # Calculate final price
            dynamic_price = base_price * price_multiplier

            # Apply pricing constraints (min/max bounds)
            final_price = await self._apply_pricing_constraints(dynamic_price, base_price)

            return {
                "price": round(final_price, 2),
                "currency": currency,
                "original_price": round(base_price, 2),
                "discount_percentage": round((1 - price_multiplier) * 100, 1) if price_multiplier < 1 else 0,
                "pricing_factors": await self._get_pricing_factors_explanation(persona_id, user_context, price_multiplier)
            }

        except Exception as e:
            logger.error(f"Error calculating dynamic price: {e}")
            # Fallback to base price
            base_price, currency = await self._get_base_price(persona_id)
            return {
                "price": round(base_price, 2),
                "currency": currency,
                "original_price": round(base_price, 2),
                "discount_percentage": 0,
                "pricing_factors": ["Base pricing applied due to calculation error"]
            }

    async def _get_base_price(self, persona_id: str) -> tuple[float, str]:
        """Get base price for a persona."""
        try:
            # Try to get from pricing tier first
            persona_tier = self.db.query(pricing_models.PricingTier).filter(
                pricing_models.PricingTier.persona_id == persona_id,
                pricing_models.PricingTier.is_active == True
            ).first()

            if persona_tier:
                return float(persona_tier.base_price), persona_tier.currency

            # Fallback to persona model
            persona = self.db.query(PersonaModel).filter(PersonaModel.id == persona_id).first()
            if persona and persona.price:
                return float(persona.price), "USD"  # Default currency

            # Ultimate fallback
            return 10.0, "USD"

        except Exception as e:
            logger.error(f"Error getting base price: {e}")
            return 10.0, "USD"

    async def _calculate_price_multiplier(self, persona_id: str, user_context: Optional[Dict[str, Any]]) -> float:
        """Calculate dynamic price multiplier based on various factors."""
        multiplier = 1.0

        try:
            # Factor 1: User tier/loyalty (premium users get discounts)
            if user_context and user_context.get("user_tier") == "premium":
                multiplier *= 0.9  # 10% discount for premium users
            elif user_context and user_context.get("user_tier") == "enterprise":
                multiplier *= 0.85  # 15% discount for enterprise users

            # Factor 2: Persona popularity/demand
            demand_factor = await self._get_persona_demand_factor(persona_id)
            multiplier *= demand_factor

            # Factor 3: Time-based pricing (peak hours, weekends)
            time_factor = await self._get_time_based_factor()
            multiplier *= time_factor

            # Factor 4: User purchase history
            if user_context and user_context.get("user_id"):
                history_factor = await self._get_user_history_factor(user_context["user_id"])
                multiplier *= history_factor

            # Factor 5: Market conditions (competitor pricing, etc.)
            market_factor = await self._get_market_factor(persona_id)
            multiplier *= market_factor

            # Ensure multiplier stays within reasonable bounds
            multiplier = max(0.5, min(2.0, multiplier))  # 50% discount to 100% markup max

            return multiplier

        except Exception as e:
            logger.error(f"Error calculating price multiplier: {e}")
            return 1.0

    async def _get_persona_demand_factor(self, persona_id: str) -> float:
        """Calculate demand-based pricing factor."""
        try:
            # Get recent purchase count (last 7 days)
            week_ago = datetime.utcnow() - timedelta(days=7)
            recent_purchases = self.db.query(sa_func.count(PurchaseModel.id)).filter(
                # Assuming PurchaseModel has persona_id and status, adjust if not
                # PurchaseModel.persona_id == persona_id, # This might be on PurchasedItem
                PurchaseModel.payment_status == 'completed', # Corrected from Purchase.status
                PurchaseModel.created_at >= week_ago
            ).scalar() or 0
            # Note: persona_id is likely on PurchasedItem, not Purchase directly.
            # This logic might need adjustment if we need to count purchases of a specific persona.
            # For now, using general recent purchases as a proxy for demand.

            # High demand = higher price
            if recent_purchases > 50:
                return 1.2  # 20% markup for high demand
            elif recent_purchases > 20:
                return 1.1  # 10% markup for medium demand
            elif recent_purchases < 5:
                return 0.9  # 10% discount for low demand
            else:
                return 1.0  # Normal pricing

        except Exception as e:
            logger.error(f"Error calculating demand factor: {e}")
            return 1.0

    async def _get_time_based_factor(self) -> float:
        """Calculate time-based pricing factor."""
        try:
            from datetime import datetime

            now = datetime.utcnow()
            hour = now.hour
            weekday = now.weekday()

            # Peak hours (9 AM - 5 PM weekdays) get slight markup
            if weekday < 5 and 9 <= hour <= 17:
                return 1.05  # 5% markup during business hours

            # Weekend discount
            elif weekday >= 5:
                return 0.95  # 5% discount on weekends

            return 1.0

        except Exception as e:
            logger.error(f"Error calculating time factor: {e}")
            return 1.0

    async def _get_user_history_factor(self, user_id: int) -> float:
        """Calculate pricing factor based on user purchase history."""
        try:
            # Get user's total purchases
            total_purchases = self.db.query(sa_func.count(PurchaseModel.id)).filter(
                PurchaseModel.user_id == user_id,
                PurchaseModel.payment_status == 'completed' # Corrected from Purchase.status
            ).scalar() or 0

            # Loyal customers get better pricing
            if total_purchases > 10:
                return 0.9  # 10% discount for very loyal customers
            elif total_purchases > 5:
                return 0.95  # 5% discount for loyal customers
            elif total_purchases == 0:
                return 0.9  # 10% discount for first-time buyers

            return 1.0

        except Exception as e:
            logger.error(f"Error calculating user history factor: {e}")
            return 1.0

    async def _get_market_factor(self, persona_id: str) -> float:
        """Calculate market-based pricing factor."""
        try:
            # Placeholder for market analysis
            # In a real implementation, this would analyze competitor pricing,
            # market trends, economic indicators, etc.

            # Return neutral market factor when no real market data is available
            return 1.0

        except Exception as e:
            logger.error(f"Error calculating market factor: {e}")
            return 1.0

    async def _apply_pricing_constraints(self, calculated_price: float, base_price: float) -> float:
        """Apply pricing constraints to ensure reasonable bounds."""
        try:
            # Don't go below 50% of base price
            min_price = base_price * 0.5

            # Don't go above 200% of base price
            max_price = base_price * 2.0

            return max(min_price, min(max_price, calculated_price))

        except Exception as e:
            logger.error(f"Error applying pricing constraints: {e}")
            return base_price

    async def _get_pricing_factors_explanation(self, persona_id: str, user_context: Optional[Dict[str, Any]],
                                             multiplier: float) -> List[str]:
        """Generate explanation of pricing factors."""
        factors = []

        try:
            if user_context:
                if user_context.get("user_tier") == "premium":
                    factors.append("Premium member discount applied")
                elif user_context.get("user_tier") == "enterprise":
                    factors.append("Enterprise discount applied")

            if multiplier > 1.1:
                factors.append("High demand pricing")
            elif multiplier < 0.9:
                factors.append("Promotional pricing")

            if not factors:
                factors.append("Standard pricing")

            return factors

        except Exception as e:
            logger.error(f"Error generating pricing factors explanation: {e}")
            return ["Standard pricing"]
            # This section seems to be a leftover placeholder and can be removed
            # as the main calculate_dynamic_price already returns a more complete dict.
            # base_price = 10.0 
            # currency = "USD"
            # final_price = base_price 
            # return {"price": final_price, "currency": currency, "original_price": base_price}
            pass # This function is now primarily for explanation strings

    # Cart Price Calculation (incorporating discounts/promotions)
    async def calculate_cart_total(self, user_id: int, discount_code: Optional[str] = None) -> Dict[str, Any]:
        cart_items_db = self.db.query(CartItemModelDB).filter(CartItemModelDB.user_id == user_id).all()

        subtotal = 0.0
        items_details = []

        for item in cart_items_db:
            # Get dynamic/base price for the persona
            # This needs access to Persona model or a way to get persona price
            # For now, using the placeholder dynamic price logic
            price_info = await self.calculate_dynamic_price(item.persona_id, {"user_id": user_id})
            item_total_price = price_info["price"] * item.quantity
            subtotal += item_total_price
            items_details.append({
                "persona_id": item.persona_id,
                "quantity": item.quantity,
                "unit_price": price_info["price"],
                "total_price": item_total_price,
                "currency": price_info["currency"]
            })

        applied_discount_amount = 0.0
        final_total = subtotal
        applied_discount_info = None

        if discount_code:
            # Assuming a generic discount for now, tier_id might be relevant if cart items are tied to tiers
            discount = await self.validate_and_apply_discount(code=discount_code, user_id=user_id)
            if discount:
                if discount.discount_type == "percentage":
                    applied_discount_amount = (subtotal * discount.value) / 100
                elif discount.discount_type == "fixed_amount":
                    applied_discount_amount = discount.value

                final_total = max(0, subtotal - applied_discount_amount)
                applied_discount_info = {"code": discount.code, "amount": applied_discount_amount}

                # Increment discount usage count
                discount.uses_count = (discount.uses_count or 0) + 1
                self.db.commit()


        # Placeholder for applying promotions
        # active_promotions = await self.get_active_promotions(user_id=user_id)
        # For each promotion, check applicability and adjust final_total / items_details

        return {
            "items": items_details,
            "subtotal": subtotal,
            "discount_applied": applied_discount_info,
            "total_amount": final_total,
            "currency": items_details[0]["currency"] if items_details else "USD" # Default currency
        }

def get_pricing_service(db: Session = Depends(get_db)): # Assuming get_db is your dependency injector for DB session
    return PricingService(db)
