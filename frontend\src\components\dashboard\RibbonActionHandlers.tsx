/**
 * Ribbon Action Handlers
 * 
 * Comprehensive implementation of all ribbon toolbar action handlers.
 * Provides the actual functionality behind ribbon buttons including:
 * - Dashboard operations (open, save, new, recent)
 * - Data operations (connect, refresh, upload)
 * - Style operations (theme, layout)
 * - Share operations (share, export, embed)
 * - Admin operations (settings, preferences)
 */

import React, { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';
import { useDashboardManagement } from '@/hooks/use-dashboard-management';
import { useDashboardMode } from '@/stores/dashboard-mode-store';

// Modal dialogs
import { DashboardBrowserDialog } from './dialogs/DashboardBrowserDialog';
import { DashboardCreationWizard } from './dialogs/DashboardCreationWizard';
import { RecentDashboardsDropdown } from './dialogs/RecentDashboardsDropdown';
import { DataSourceConnectionWizard } from './dialogs/DataSourceConnectionWizard';
import { FileUploadDialog } from './dialogs/FileUploadDialog';
import { ShareDashboardDialog } from './dialogs/ShareDashboardDialog';
import { ExportDashboardDialog } from './dialogs/ExportDashboardDialog';
import { EmbedCodeDialog } from './dialogs/EmbedCodeDialog';
import { DashboardSettingsPanel } from './DashboardSettingsPanel';
import { UserPreferencesDialog } from './dialogs/UserPreferencesDialog';
import { ThemeSelector } from './dialogs/ThemeSelector';
import { LayoutSelector } from './dialogs/LayoutSelector';

// AI Assistant integration
import { RibbonAIIntegration, useRibbonAI } from './RibbonAIIntegration';

interface RibbonActionHandlersProps {
  onWidgetCreate?: (widget_config: any) => void;
  onSectionCreate?: (section_config: any) => void;
  onTemplateApply?: (template_id: string) => void;
  onDataConfigure?: (data_config: any) => void;
}

export const useRibbonActionHandlers = ({
  onWidgetCreate,
  onSectionCreate,
  onTemplateApply,
  onDataConfigure,
}: RibbonActionHandlersProps = {}) => {
  const { toast } = useToast();
  const { current_mode } = useDashboardMode();
  const {
    currentLayout,
    createDashboard,
    updateDashboard,
    isLoading,
    error
  } = useUnifiedDashboardStore();
  const {
    dashboards,
    activeDashboard,
    switchDashboard,
    refreshDashboards
  } = useDashboardManagement();

  // Dialog states
  const [showDashboardBrowser, setShowDashboardBrowser] = useState(false);
  const [showCreationWizard, setShowCreationWizard] = useState(false);
  const [showRecentDropdown, setShowRecentDropdown] = useState(false);
  const [showDataConnectionWizard, setShowDataConnectionWizard] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showEmbedDialog, setShowEmbedDialog] = useState(false);
  const [showSettingsPanel, setShowSettingsPanel] = useState(false);
  const [showPreferencesDialog, setShowPreferencesDialog] = useState(false);
  const [showThemeSelector, setShowThemeSelector] = useState(false);
  const [showLayoutSelector, setShowLayoutSelector] = useState(false);
  const [showAIAssistant, setShowAIAssistant] = useState(false);

  // Enhanced AI integration
  const { isAIOpen, aiContext, openAIWithContext, closeAI } = useRibbonAI();

  // Loading states for individual operations
  const [operationLoading, setOperationLoading] = useState<Record<string, boolean>>({});

  const setOperationState = useCallback((operation: string, loading: boolean) => {
    setOperationLoading(prev => ({ ...prev, [operation]: loading }));
  }, []);

  // Dashboard Operations
  const handleDashboardOpen = useCallback(async () => {
    try {
      setOperationState('dashboard_open', true);
      await refreshDashboards();
      setShowDashboardBrowser(true);
      
      toast({
        title: "Dashboard Browser",
        description: "Select a dashboard to open from your collection.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load dashboard browser.",
        variant: "destructive",
      });
    } finally {
      setOperationState('dashboard_open', false);
    }
  }, [refreshDashboards, toast, setOperationState]);

  const handleDashboardSave = useCallback(async () => {
    if (!activeDashboard || !currentLayout) {
      toast({
        title: "Nothing to Save",
        description: "No active dashboard to save.",
        variant: "destructive",
      });
      return;
    }

    try {
      setOperationState('dashboard_save', true);
      
      await updateDashboard(activeDashboard.id, {
        name: activeDashboard.name,
        description: activeDashboard.description,
        layout_config: currentLayout.dashboard?.layout_config,
        theme_config: currentLayout.dashboard?.theme_config,
      });

      toast({
        title: "Dashboard Saved",
        description: `"${activeDashboard.name}" has been saved successfully.`,
      });
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save dashboard changes.",
        variant: "destructive",
      });
    } finally {
      setOperationState('dashboard_save', false);
    }
  }, [activeDashboard, currentLayout, updateDashboard, toast, setOperationState]);

  const handleDashboardNew = useCallback(() => {
    setShowCreationWizard(true);
    toast({
      title: "Create New Dashboard",
      description: "Follow the wizard to create your new dashboard.",
    });
  }, [toast]);

  const handleDashboardRecent = useCallback(async () => {
    try {
      setOperationState('dashboard_recent', true);
      await refreshDashboards();
      setShowRecentDropdown(true);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load recent dashboards.",
        variant: "destructive",
      });
    } finally {
      setOperationState('dashboard_recent', false);
    }
  }, [refreshDashboards, toast, setOperationState]);

  // Data Operations
  const handleDataConnect = useCallback(() => {
    setShowDataConnectionWizard(true);
    toast({
      title: "Connect Data Source",
      description: "Configure a new data source for your dashboard.",
    });
  }, [toast]);

  const handleDataRefresh = useCallback(async () => {
    if (!currentLayout) {
      toast({
        title: "No Dashboard",
        description: "No active dashboard to refresh.",
        variant: "destructive",
      });
      return;
    }

    try {
      setOperationState('data_refresh', true);

      // Refresh all widgets in the current dashboard
      const refreshPromises = currentLayout.widgets.map(async (widget) => {
        // Trigger widget refresh via WebSocket or direct API call
        // This would integrate with the existing widget refresh system
        return fetch(`/api/dashboard-customization/widgets/${widget.id}/refresh`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        });
      });

      await Promise.all(refreshPromises);

      toast({
        title: "Data Refreshed",
        description: "All dashboard data has been refreshed successfully.",
      });
    } catch (error) {
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh dashboard data.",
        variant: "destructive",
      });
    } finally {
      setOperationState('data_refresh', false);
    }
  }, [currentLayout, toast, setOperationState]);

  const handleFileUpload = useCallback(() => {
    setShowFileUpload(true);
    toast({
      title: "Upload Files",
      description: "Upload data files to use in your dashboard.",
    });
  }, [toast]);

  // Style Operations
  const handleThemeChange = useCallback(() => {
    setShowThemeSelector(true);
  }, []);

  const handleLayoutChange = useCallback(() => {
    setShowLayoutSelector(true);
  }, []);

  // Share Operations
  const handleShareDashboard = useCallback(() => {
    if (!activeDashboard) {
      toast({
        title: "No Dashboard",
        description: "No active dashboard to share.",
        variant: "destructive",
      });
      return;
    }
    setShowShareDialog(true);
  }, [activeDashboard, toast]);

  const handleDashboardExport = useCallback(() => {
    if (!activeDashboard) {
      toast({
        title: "No Dashboard",
        description: "No active dashboard to export.",
        variant: "destructive",
      });
      return;
    }
    setShowExportDialog(true);
  }, [activeDashboard, toast]);

  const handleShareEmbed = useCallback(() => {
    if (!activeDashboard) {
      toast({
        title: "No Dashboard",
        description: "No active dashboard to embed.",
        variant: "destructive",
      });
      return;
    }
    setShowEmbedDialog(true);
  }, [activeDashboard, toast]);

  // Admin Operations
  const handleAdminSettings = useCallback(() => {
    setShowSettingsPanel(true);
  }, []);

  const handleAdminPreferences = useCallback(() => {
    setShowPreferencesDialog(true);
  }, []);

  // AI Assistant Operations
  const handleAICreate = useCallback(() => {
    openAIWithContext({
      action: 'create_widget',
      mode: current_mode,
      prompt: current_mode === 'simple'
        ? "I want to create a new widget for my dashboard"
        : "Help me create a custom widget with advanced configurations",
    });

    toast({
      title: current_mode === 'simple' ? "AI Assistant" : "AI Development Assistant",
      description: current_mode === 'simple'
        ? "Let me help you create widgets and sections with natural language."
        : "Advanced AI assistance for custom development and optimization.",
    });
  }, [current_mode, openAIWithContext, toast]);

  // Enhanced AI operations for different contexts
  const handleAIOptimize = useCallback(() => {
    openAIWithContext({
      action: 'optimize_dashboard',
      mode: current_mode,
      prompt: "Help me optimize my dashboard for better performance and user experience",
    });
  }, [current_mode, openAIWithContext]);

  const handleAIAnalyze = useCallback(() => {
    openAIWithContext({
      action: 'analyze_data',
      mode: current_mode,
      prompt: "Analyze my dashboard data and provide insights",
    });
  }, [current_mode, openAIWithContext]);

  const handleAISection = useCallback(() => {
    openAIWithContext({
      action: 'add_section',
      mode: current_mode,
      prompt: "Help me add a new section to organize my dashboard content",
    });
  }, [current_mode, openAIWithContext]);

  return {
    // Dashboard Operations
    handleDashboardOpen,
    handleDashboardSave,
    handleDashboardNew,
    handleDashboardRecent,

    // Data Operations
    handleDataConnect,
    handleDataRefresh,
    handleFileUpload,

    // Style Operations
    handleThemeChange,
    handleLayoutChange,

    // Share Operations
    handleShareDashboard,
    handleDashboardExport,
    handleShareEmbed,

    // Admin Operations
    handleAdminSettings,
    handleAdminPreferences,

    // AI Operations
    handleAICreate,
    handleAIOptimize,
    handleAIAnalyze,
    handleAISection,

    // Dialog states
    showDashboardBrowser,
    setShowDashboardBrowser,
    showCreationWizard,
    setShowCreationWizard,
    showRecentDropdown,
    setShowRecentDropdown,
    showDataConnectionWizard,
    setShowDataConnectionWizard,
    showFileUpload,
    setShowFileUpload,
    showShareDialog,
    setShowShareDialog,
    showExportDialog,
    setShowExportDialog,
    showEmbedDialog,
    setShowEmbedDialog,
    showSettingsPanel,
    setShowSettingsPanel,
    showPreferencesDialog,
    setShowPreferencesDialog,
    showThemeSelector,
    setShowThemeSelector,
    showLayoutSelector,
    setShowLayoutSelector,
    showAIAssistant,
    setShowAIAssistant,

    // Enhanced AI Integration
    isAIOpen,
    aiContext,
    closeAI,

    // Loading states
    operationLoading,
    isLoading,

    // Data
    dashboards,
    activeDashboard,
    currentLayout,
  };
};
