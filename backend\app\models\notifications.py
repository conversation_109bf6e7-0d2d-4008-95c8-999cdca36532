import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, Text, Integer, JSON # Added JSON
from ..database import Base
from ..utils.db_utils import get_utc_now

# SQLAlchemy Model
class NotificationModel(Base):
    __tablename__ = "notifications"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    type = Column(String(50), nullable=False, default="info")  # e.g., "info", "alert", "report_ready", "new_message"
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    is_read = Column(Boolean, default=False, nullable=False)
    link = Column(String(500), nullable=True)  # Optional URL for navigation
    notification_metadata = Column("metadata", JSON, nullable=True) # Renamed from metadata

    created_at = Column(DateTime(timezone=True), default=get_utc_now, nullable=False)
    read_at = Column(DateTime(timezone=True), nullable=True)

    # user = relationship("User") # Optional: if you need to access User object from Notification

# Pydantic Models
class NotificationBase(BaseModel):
    type: str = Field(default="info", description="Type of the notification")
    title: str = Field(..., description="Title of the notification")
    message: str = Field(..., description="Main content of the notification")
    link: Optional[str] = Field(None, description="Optional link associated with the notification")
    notification_metadata: Optional[Dict[str, Any]] = Field(None, alias="metadata", description="Additional metadata for the notification") # Renamed

class NotificationCreate(NotificationBase):
    user_id: int = Field(..., description="ID of the user to notify")

class NotificationUpdate(BaseModel):
    is_read: Optional[bool] = None
    # Add other updatable fields if necessary

class NotificationResponse(NotificationBase):
    id: str
    user_id: int
    is_read: bool
    created_at: datetime
    read_at: Optional[datetime] = None
    # Ensure notification_metadata is included if it's part of the response
    notification_metadata: Optional[Dict[str, Any]] = Field(None, alias="metadata")


    class Config:
        from_attributes = True
        populate_by_name = True # To allow alias "metadata" to work

class NotificationPreferences(BaseModel):
    user_id: int
    email_enabled: bool = Field(True, description="Enable email notifications")
    in_app_enabled: bool = Field(True, description="Enable in-app notifications")
    # Example: preferences for specific notification types
    report_ready_email: bool = Field(True)
    report_ready_in_app: bool = Field(True)
    new_message_email: bool = Field(False) # Typically users don't want email for every new message
    new_message_in_app: bool = Field(True)
    # Add more specific preferences as needed

# Could also have a SQLAlchemy model for NotificationPreferences if they need to be stored per user
# class UserNotificationPreferencesModel(Base):
#     __tablename__ = "user_notification_preferences"
#     user_id = Column(Integer, ForeignKey("users.id"), primary_key=True)
#     preferences = Column(JSON, nullable=False) # Stores NotificationPreferences Pydantic model as JSON
