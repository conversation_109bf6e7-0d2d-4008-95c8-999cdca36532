"""Utility functions for the Marketing Agent.

This module provides utility functions for the Marketing Agent.
"""

import os
import requests
import logging
from typing import List, Optional, Dict, Any
from langchain_groq import ChatGroq
from langchain_ollama import ChatOllama
#from langchain_openai import ChatOpenAI

# Import provider utilities
from agents.utils.provider_utils import get_provider_endpoint

# Configure logging
logger = logging.getLogger(__name__)

def fetch_models(provider: str, endpoint: str, api_key: Optional[str] = None) -> List[str]:
    """
    Fetch available models from the provider.

    Args:
        provider: The provider name (Groq, OpenAI, Ollama)
        endpoint: The API endpoint
        api_key: Optional API key for authenticated providers

    Returns:
        List of available model IDs
    """
    try:
        if provider == "Groq":
            url = f"{endpoint}/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                return [model['id'] for model in response.json()['data']]
            else:
                logger.error(f"Failed to fetch Groq models: {response.status_code} {response.text}")
                return []

        elif provider == "OpenAI":
            url = f"{endpoint}/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                return [model['id'] for model in response.json()['data']]
            else:
                logger.error(f"Failed to fetch OpenAI models: {response.status_code} {response.text}")
                return []

        elif provider == "Ollama":
            url = f"{endpoint}/api/tags"  # Ollama endpoint to list models
            response = requests.get(url)
            if response.status_code == 200:
                return [model['name'] for model in response.json()['models']]
            else:
                logger.error(f"Failed to fetch Ollama models: {response.status_code} {response.text}")
                return []
        else:
            logger.error(f"Unsupported provider: {provider}")
            return []
    except Exception as e:
        logger.error(f"Error fetching models: {str(e)}", exc_info=True)
        return []

def get_api_key(provider: str) -> str:
    """
    Get the API key for a provider from environment variables.

    Args:
        provider: The provider name (Groq, OpenAI)

    Returns:
        The API key or an empty string if not found
    """
    # Import here to avoid circular imports
    from agents.utils.provider_utils import get_api_key as get_provider_api_key

    # Convert provider name to provider ID
    provider_id = provider.lower()

    # Get the API key
    return get_provider_api_key(provider_id)


class ProviderHandler:
    @staticmethod
    def create_client(provider: str, model: str, api_key: Optional[str] = None, endpoint: Optional[str] = None) -> Any:
        """
        Create a language model client based on the provider.

        Args:
            provider: The provider name (Groq, OpenAI, Ollama)
            model: The model name
            api_key: The API key for authenticated providers (optional, will be loaded if not provided)
            endpoint: The API endpoint (optional, will be loaded if not provided)

        Returns:
            A language model client
        """
        # Convert provider name to lowercase for consistency
        provider_id = provider.lower()

        # Get API key if not provided
        if not api_key:
            api_key = get_api_key(provider)

        # Get endpoint if not provided
        if not endpoint:
            from agents.utils.provider_utils import get_provider_endpoint
            endpoint = get_provider_endpoint(provider_id)

        # Create client based on provider
        if provider.lower() == "groq":
            return ChatGroq(
                model=model,
                api_key=api_key,
                base_url=endpoint or "https://api.groq.com/openai/v1",
                temperature=0.7
            )
        elif provider.lower() == "openai":
            # Import here to avoid issues if package is not installed
            from langchain_openai import ChatOpenAI
            return ChatOpenAI(
                model=model,
                api_key=api_key,
                base_url=endpoint or "https://api.openai.com/v1",
                temperature=0.7
            )
        elif provider.lower() == "ollama":
            return ChatOllama(
                model=model,
                base_url=endpoint or "http://localhost:11434",
                temperature=0.7
            )
        else:
            raise ValueError(f"Unsupported provider: {provider}")


def convert_to_docx(content: str, title: str = "Generated Marketing Content") -> bytes:
    """
    Convert markdown content to a DOCX file.

    Args:
        content: The markdown content to convert
        title: The title to use for the document

    Returns:
        The DOCX file as bytes
    """
    try:
        from docx import Document
        from docx.shared import Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from io import BytesIO

        # Create a new Document
        doc = Document()

        # Add a title
        title_paragraph = doc.add_heading(level=1)
        title_run = title_paragraph.add_run(title)
        title_run.bold = True
        title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add the content
        for line in content.split('\n'):
            if line.startswith('# '):
                # Heading 1
                heading = doc.add_heading(line[2:], level=1)
                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
            elif line.startswith('## '):
                # Heading 2
                heading = doc.add_heading(line[3:], level=2)
                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
            elif line.startswith('### '):
                # Heading 3
                heading = doc.add_heading(line[4:], level=3)
                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
            elif line.startswith('- ') or line.startswith('* '):
                # Bullet point
                doc.add_paragraph(line[2:], style='List Bullet')
            elif line.startswith('1. ') or line.startswith('1) '):
                # Numbered list
                doc.add_paragraph(line[3:], style='List Number')
            elif line.strip() == '':
                # Empty line
                doc.add_paragraph()
            else:
                # Regular paragraph
                doc.add_paragraph(line)

        # Save the document to a BytesIO object
        docx_file = BytesIO()
        doc.save(docx_file)
        docx_file.seek(0)

        return docx_file.getvalue()
    except Exception as e:
        logger.error(f"Error converting to DOCX: {str(e)}", exc_info=True)
        raise