#!/usr/bin/env python3
"""
<PERSON>ript to verify that the system is configured to use Hugging Face embeddings exclusively.

This script checks the configuration and tests the embedding system.
"""

import sys
import os
import logging
from pathlib import Path

# Add backend to path
backend_root = Path(__file__).parent.parent
sys.path.insert(0, str(backend_root))

logger = logging.getLogger(__name__)


def test_embedding_config():
    """Test the embedding configuration."""
    print("🔧 Testing Embedding Configuration...")
    
    try:
        from agents.utils.embedding_config import get_embedding_config_manager, get_recommended_embedding_config
        
        # Test config manager
        manager = get_embedding_config_manager()
        print("✓ Embedding config manager loaded successfully")
        
        # Test providers
        providers = manager.get_available_providers()
        print(f"✓ Available providers: {[p['id'] for p in providers]}")
        
        # Check that only Hugging Face is available
        hf_providers = [p for p in providers if p['id'] == 'huggingface']
        if len(hf_providers) == 1:
            print("✓ Hugging Face provider is available")
        else:
            print("❌ Hugging Face provider not found")
            return False
        
        # Test models
        models = manager.get_available_models("huggingface")
        print(f"✓ Available Hugging Face models: {len(models)}")
        
        # Test recommendation
        config = get_recommended_embedding_config()
        if config.get("provider") == "huggingface":
            print(f"✓ Recommended config uses Hugging Face: {config['config']['model']}")
        else:
            print(f"❌ Recommended config uses wrong provider: {config.get('provider')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing embedding config: {e}")
        return False


def test_memory_service():
    """Test the memory service configuration."""
    print("\n🧠 Testing Memory Service Configuration...")
    
    try:
        from agents.utils.memory_service import MemoryService
        
        # Create memory service instance
        memory_service = MemoryService()
        
        # Test embedder config
        embedder_config = memory_service._get_embedder_config()
        
        if embedder_config.get("provider") == "huggingface":
            model = embedder_config.get("config", {}).get("model")
            print(f"✓ Memory service uses Hugging Face: {model}")
            return True
        else:
            print(f"❌ Memory service uses wrong provider: {embedder_config.get('provider')}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing memory service: {e}")
        return False


def test_dependencies():
    """Test that required dependencies are installed."""
    print("\n📦 Testing Dependencies...")
    
    required_packages = [
        "sentence_transformers",
        "torch",
        "transformers",
        "tokenizers"
    ]
    
    all_installed = True
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package} is installed")
        except ImportError:
            print(f"❌ {package} is NOT installed")
            all_installed = False
    
    return all_installed


def test_model_loading():
    """Test loading a small Hugging Face model."""
    print("\n🤖 Testing Model Loading...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        # Try to load a small model
        model_name = "sentence-transformers/all-MiniLM-L6-v2"
        print(f"Loading model: {model_name}")
        
        model = SentenceTransformer(model_name)
        print("✓ Model loaded successfully")
        
        # Test encoding
        test_text = "This is a test sentence."
        embedding = model.encode(test_text)
        print(f"✓ Text encoded successfully (dimension: {len(embedding)})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False


def main():
    """Main function to run all tests."""
    print("🚀 Verifying Hugging Face Embedding Configuration\n")
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Embedding Config", test_embedding_config),
        ("Memory Service", test_memory_service),
        ("Model Loading", test_model_loading),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📋 Test Summary:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print("="*50)
    
    if all_passed:
        print("🎉 All tests passed! Hugging Face embeddings are configured correctly.")
        print("\nYour system is ready to use free, local embedding models!")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        print("\nTo fix issues:")
        print("1. Run: python scripts/install_embedding_deps.py")
        print("2. Restart the backend")
        print("3. Run this script again")
        return 1


if __name__ == "__main__":
    sys.exit(main())
