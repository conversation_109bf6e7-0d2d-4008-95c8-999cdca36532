"""
Advanced Analytics Service for Phase 2.3 implementation.
Provides machine learning pipeline for data insights, automated pattern detection,
anomaly identification, predictive analytics, and forecasting.
"""

import logging
from typing import Dict, List, Any
import numpy as np
from datetime import timed<PERSON><PERSON>
from sqlalchemy.orm import Session
from app.database import get_db, get_utc_now, Message, Conversation, User
from collections import defaultdict

logger = logging.getLogger(__name__)


class AdvancedAnalyticsService:
    """Advanced analytics service with ML-powered insights and predictions."""

    def __init__(self, db: Session = None):
        """Initialize the advanced analytics service."""
        self.db = db or next(get_db())
        self._initialize_ml_models()
        logger.info("AdvancedAnalyticsService initialized")

    def _initialize_ml_models(self):
        """Initialize machine learning models for analytics."""
        try:
            # Try to import ML libraries
            from sklearn.ensemble import IsolationForest
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler
            from sklearn.linear_model import LinearRegression

            self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
            self.clustering_model = KMeans(n_clusters=3, random_state=42)
            self.scaler = StandardScaler()
            self.trend_predictor = LinearRegression()

            logger.info("ML models initialized successfully")
        except ImportError as e:
            logger.warning(f"Could not initialize ML models: {e}. Using statistical fallbacks.")
            self.anomaly_detector = None
            self.clustering_model = None
            self.scaler = None
            self.trend_predictor = None

    async def generate_data_insights(self, data_source_id: str = None, time_range: str = "30d") -> Dict[str, Any]:
        """
        Generate comprehensive data insights using ML pipeline.

        Args:
            data_source_id: Optional specific data source to analyze
            time_range: Time range for analysis (7d, 30d, 90d)

        Returns:
            Comprehensive insights including patterns, anomalies, and predictions
        """
        try:
            # Get data for analysis
            analysis_data = await self._prepare_analysis_data(data_source_id, time_range)

            if not analysis_data or len(analysis_data) < 10:
                return {
                    "insights": [],
                    "patterns": [],
                    "anomalies": [],
                    "predictions": {},
                    "confidence": 0.0,
                    "message": "Insufficient data for meaningful analysis"
                }

            # Generate insights
            insights = await self._extract_insights(analysis_data)
            patterns = await self._detect_patterns(analysis_data)
            anomalies = await self._detect_anomalies(analysis_data)
            predictions = await self._generate_predictions(analysis_data)

            # Calculate overall confidence
            confidence = self._calculate_confidence(analysis_data, insights, patterns)

            result = {
                "insights": insights,
                "patterns": patterns,
                "anomalies": anomalies,
                "predictions": predictions,
                "confidence": confidence,
                "data_points": len(analysis_data),
                "analysis_timestamp": get_utc_now().isoformat(),
                "time_range": time_range
            }

            logger.info(f"Generated insights with {len(insights)} insights, {len(patterns)} patterns, {len(anomalies)} anomalies")
            return result

        except Exception as e:
            logger.error(f"Error generating data insights: {e}")
            return {
                "insights": [],
                "patterns": [],
                "anomalies": [],
                "predictions": {},
                "confidence": 0.0,
                "error": str(e)
            }

    async def _prepare_analysis_data(self, data_source_id: str = None, time_range: str = "30d") -> List[Dict[str, Any]]:
        """Prepare data for analysis from various sources."""
        try:
            # Calculate time range
            days = int(time_range.replace('d', ''))
            start_date = get_utc_now() - timedelta(days=days)

            analysis_data = []

            # Get conversation and message data
            conversations = self.db.query(Conversation).filter(
                Conversation.created_at >= start_date
            ).all()

            for conv in conversations:
                messages = self.db.query(Message).filter(
                    Message.conversation_id == conv.id
                ).all()

                conv_data = {
                    "type": "conversation",
                    "id": conv.id,
                    "user_id": conv.user_id,
                    "created_at": conv.created_at,
                    "message_count": len(messages),
                    "total_length": sum(len(msg.content or "") for msg in messages),
                    "duration_minutes": self._calculate_conversation_duration(messages),
                    "persona_id": getattr(conv, 'persona_id', None)
                }
                analysis_data.append(conv_data)

            # Get user activity data
            users = self.db.query(User).all()
            for user in users:
                user_convs = [c for c in conversations if c.user_id == user.id]
                if user_convs:
                    user_data = {
                        "type": "user_activity",
                        "user_id": user.id,
                        "conversation_count": len(user_convs),
                        "avg_messages_per_conv": np.mean([
                            len(self.db.query(Message).filter(Message.conversation_id == c.id).all())
                            for c in user_convs
                        ]),
                        "total_activity_days": len(set(c.created_at.date() for c in user_convs)),
                        "last_activity": max(c.created_at for c in user_convs)
                    }
                    analysis_data.append(user_data)

            return analysis_data

        except Exception as e:
            logger.error(f"Error preparing analysis data: {e}")
            return []

    def _calculate_conversation_duration(self, messages: List[Message]) -> float:
        """Calculate conversation duration in minutes."""
        if len(messages) < 2:
            return 0.0

        start_time = min(msg.created_at for msg in messages)
        end_time = max(msg.created_at for msg in messages)
        duration = (end_time - start_time).total_seconds() / 60

        return round(duration, 2)

    async def _extract_insights(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract meaningful insights from the data using advanced analytics."""
        insights = []

        # Conversation insights with statistical analysis
        conv_data = [d for d in data if d["type"] == "conversation"]
        if conv_data:
            message_counts = [d["message_count"] for d in conv_data]
            durations = [d["duration_minutes"] for d in conv_data]
            lengths = [d["total_length"] for d in conv_data]

            avg_messages = np.mean(message_counts)
            avg_duration = np.mean(durations)
            avg_length = np.mean(lengths)

            # Calculate statistical measures
            msg_std = np.std(message_counts)
            duration_std = np.std(durations)

            # Engagement quality insight
            engagement_score = (avg_messages * avg_duration) / (avg_length / 100 + 1)  # Normalize by length

            insights.append({
                "type": "conversation_metrics",
                "title": "Conversation Engagement Analysis",
                "description": f"Average: {avg_messages:.1f} messages, {avg_duration:.1f} min duration, engagement score: {engagement_score:.2f}",
                "value": engagement_score,
                "trend": "improving" if engagement_score > 50 else "stable",
                "importance": "high",
                "confidence": min(0.9, len(conv_data) / 50),
                "details": {
                    "avg_messages": avg_messages,
                    "avg_duration": avg_duration,
                    "variability": msg_std / avg_messages if avg_messages > 0 else 0
                }
            })

            # Advanced peak activity analysis
            hourly_activity = defaultdict(int)
            daily_activity = defaultdict(int)

            for d in conv_data:
                hour = d["created_at"].hour
                day = d["created_at"].strftime("%A")
                hourly_activity[hour] += 1
                daily_activity[day] += 1

            # Find peak hour with statistical significance
            peak_hour = max(hourly_activity.items(), key=lambda x: x[1])[0]
            peak_count = hourly_activity[peak_hour]
            avg_hourly = sum(hourly_activity.values()) / len(hourly_activity)
            peak_significance = (peak_count - avg_hourly) / avg_hourly if avg_hourly > 0 else 0

            insights.append({
                "type": "usage_pattern",
                "title": "Peak Activity Analysis",
                "description": f"Peak at {peak_hour}:00 ({peak_significance*100:.1f}% above average)",
                "value": peak_hour,
                "trend": "consistent" if peak_significance > 0.3 else "variable",
                "importance": "medium" if peak_significance > 0.5 else "low",
                "confidence": min(0.8, peak_significance + 0.3),
                "details": {
                    "peak_hour": peak_hour,
                    "significance": peak_significance,
                    "daily_distribution": dict(daily_activity)
                }
            })

            # Conversation quality insights
            if len(conv_data) >= 10:
                # Identify high-quality conversations (top 25%)
                quality_threshold = np.percentile([d["message_count"] * d["duration_minutes"] for d in conv_data], 75)
                high_quality_convs = [d for d in conv_data if (d["message_count"] * d["duration_minutes"]) >= quality_threshold]

                if high_quality_convs:
                    quality_rate = len(high_quality_convs) / len(conv_data) * 100
                    avg_quality_duration = np.mean([d["duration_minutes"] for d in high_quality_convs])

                    insights.append({
                        "type": "quality_analysis",
                        "title": "High-Quality Conversation Insights",
                        "description": f"{quality_rate:.1f}% of conversations are high-quality (avg {avg_quality_duration:.1f} min)",
                        "value": quality_rate,
                        "trend": "positive" if quality_rate > 25 else "needs_improvement",
                        "importance": "high",
                        "confidence": 0.8,
                        "recommendation": "Focus on replicating patterns from high-quality conversations"
                    })

        # Enhanced user engagement insights
        user_data = [d for d in data if d["type"] == "user_activity"]
        if user_data:
            conversation_counts = [d["conversation_count"] for d in user_data]
            activity_days = [d["total_activity_days"] for d in user_data]

            # Segment users by engagement level
            power_users = len([d for d in user_data if d["conversation_count"] >= 5])
            regular_users = len([d for d in user_data if 2 <= d["conversation_count"] < 5])
            casual_users = len([d for d in user_data if d["conversation_count"] == 1])
            total_users = len(user_data)

            # Calculate retention indicators
            avg_activity_days = np.mean(activity_days)
            retention_score = avg_activity_days * (power_users + regular_users) / total_users

            insights.append({
                "type": "user_segmentation",
                "title": "User Engagement Segmentation",
                "description": f"Power: {power_users}, Regular: {regular_users}, Casual: {casual_users} users",
                "value": retention_score,
                "trend": "healthy" if power_users / total_users > 0.1 else "concerning",
                "importance": "high",
                "confidence": 0.9,
                "details": {
                    "power_users_pct": power_users / total_users * 100,
                    "regular_users_pct": regular_users / total_users * 100,
                    "casual_users_pct": casual_users / total_users * 100,
                    "avg_activity_days": avg_activity_days
                },
                "recommendation": "Focus on converting casual users to regular users"
            })

        return insights

    async def _detect_patterns(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect patterns in user behavior and system usage."""
        patterns = []

        try:
            conv_data = [d for d in data if d["type"] == "conversation"]
            if len(conv_data) < 5:
                return patterns

            # Daily usage pattern
            daily_counts = defaultdict(int)
            for d in conv_data:
                day = d["created_at"].strftime("%A")
                daily_counts[day] += 1

            max_day = max(daily_counts.items(), key=lambda x: x[1])
            min_day = min(daily_counts.items(), key=lambda x: x[1])

            patterns.append({
                "type": "temporal_pattern",
                "title": "Weekly Usage Pattern",
                "description": f"Peak usage on {max_day[0]} ({max_day[1]} conversations), lowest on {min_day[0]} ({min_day[1]} conversations)",
                "pattern_strength": 0.8,
                "actionable": True,
                "recommendation": f"Consider targeted engagement campaigns on {min_day[0]}"
            })

            # Message length patterns
            if self.clustering_model:
                message_lengths = np.array([[d["total_length"]] for d in conv_data])
                if len(message_lengths) > 3:
                    clusters = self.clustering_model.fit_predict(message_lengths)

                    patterns.append({
                        "type": "content_pattern",
                        "title": "Conversation Length Clusters",
                        "description": f"Identified {len(set(clusters))} distinct conversation length patterns",
                        "pattern_strength": 0.7,
                        "actionable": True,
                        "recommendation": "Tailor response strategies based on conversation length expectations"
                    })

        except Exception as e:
            logger.error(f"Error detecting patterns: {e}")

        return patterns

    async def _detect_anomalies(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect anomalies in the data using ML or statistical methods."""
        anomalies = []

        try:
            conv_data = [d for d in data if d["type"] == "conversation"]
            if len(conv_data) < 10:
                return anomalies

            # Prepare features for anomaly detection
            features = []
            for d in conv_data:
                features.append([
                    d["message_count"],
                    d["total_length"],
                    d["duration_minutes"]
                ])

            features_array = np.array(features)

            if self.anomaly_detector and self.scaler:
                # ML-based anomaly detection
                scaled_features = self.scaler.fit_transform(features_array)
                anomaly_labels = self.anomaly_detector.fit_predict(scaled_features)

                anomaly_indices = np.where(anomaly_labels == -1)[0]

                for idx in anomaly_indices:
                    conv = conv_data[idx]
                    anomalies.append({
                        "type": "conversation_anomaly",
                        "title": "Unusual Conversation Pattern",
                        "description": f"Conversation {conv['id']} shows unusual metrics",
                        "severity": "medium",
                        "details": {
                            "message_count": conv["message_count"],
                            "duration": conv["duration_minutes"],
                            "total_length": conv["total_length"]
                        },
                        "timestamp": conv["created_at"].isoformat()
                    })
            else:
                # Statistical anomaly detection (fallback)
                message_counts = [d["message_count"] for d in conv_data]
                mean_count = np.mean(message_counts)
                std_count = np.std(message_counts)

                for d in conv_data:
                    z_score = abs((d["message_count"] - mean_count) / std_count) if std_count > 0 else 0
                    if z_score > 2.5:  # More than 2.5 standard deviations
                        anomalies.append({
                            "type": "statistical_anomaly",
                            "title": "Unusual Message Count",
                            "description": f"Conversation has {d['message_count']} messages (z-score: {z_score:.2f})",
                            "severity": "low",
                            "timestamp": d["created_at"].isoformat()
                        })

        except Exception as e:
            logger.error(f"Error detecting anomalies: {e}")

        return anomalies

    async def _generate_predictions(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate predictions and forecasts."""
        predictions = {}

        try:
            conv_data = [d for d in data if d["type"] == "conversation"]
            if len(conv_data) < 7:
                return predictions

            # Daily conversation count prediction
            daily_counts = defaultdict(int)
            for d in conv_data:
                day = d["created_at"].date()
                daily_counts[day] += 1

            if len(daily_counts) >= 7:
                dates = sorted(daily_counts.keys())
                counts = [daily_counts[date] for date in dates]

                # Simple trend prediction
                if len(counts) > 1:
                    recent_avg = np.mean(counts[-7:])  # Last week average
                    overall_avg = np.mean(counts)
                    trend = (recent_avg - overall_avg) / overall_avg if overall_avg > 0 else 0

                    next_week_prediction = recent_avg * (1 + trend)

                    predictions["conversation_volume"] = {
                        "next_week_daily_avg": round(next_week_prediction, 1),
                        "trend_direction": "increasing" if trend > 0.05 else "decreasing" if trend < -0.05 else "stable",
                        "confidence": min(0.8, len(counts) / 30),  # Higher confidence with more data
                        "current_avg": round(recent_avg, 1)
                    }

            # User growth prediction
            user_data = [d for d in data if d["type"] == "user_activity"]
            if user_data:
                active_users = len([d for d in user_data if d["conversation_count"] > 1])
                total_users = len(user_data)

                predictions["user_engagement"] = {
                    "predicted_active_users_next_month": round(active_users * 1.1, 0),
                    "engagement_trend": "positive" if active_users / total_users > 0.3 else "needs_attention",
                    "confidence": 0.6
                }

        except Exception as e:
            logger.error(f"Error generating predictions: {e}")

        return predictions

    def _calculate_confidence(self, data: List[Dict[str, Any]], insights: List[Dict[str, Any]], patterns: List[Dict[str, Any]]) -> float:
        """Calculate overall confidence score for the analysis."""
        try:
            # Base confidence on data volume
            data_confidence = min(1.0, len(data) / 100)  # Full confidence at 100+ data points

            # Adjust based on insights quality
            insight_confidence = min(1.0, len(insights) / 5)  # Full confidence at 5+ insights

            # Adjust based on pattern detection
            pattern_confidence = min(1.0, len(patterns) / 3)  # Full confidence at 3+ patterns

            # Weighted average
            overall_confidence = (
                data_confidence * 0.5 +
                insight_confidence * 0.3 +
                pattern_confidence * 0.2
            )

            return round(overall_confidence, 2)

        except Exception:
            return 0.5  # Default moderate confidence
