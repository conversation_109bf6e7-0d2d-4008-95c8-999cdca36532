"""
Intelligent caching system for marketing agent.

This module provides comprehensive caching capabilities with support for
memory-based and Redis-based caching, intelligent invalidation, and
performance optimization.
"""

import os
import json
import hashlib
import logging
import asyncio
import time
from typing import Dict, Any, Optional, Union, List, Callable
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from collections import OrderedDict
import threading
from contextlib import asynccontextmanager

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Represents a cache entry with metadata."""
    key: str
    value: Any
    created_at: float
    expires_at: Optional[float] = None
    access_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    tags: List[str] = field(default_factory=list)
    size_bytes: int = 0
    
    def is_expired(self) -> bool:
        """Check if the cache entry is expired."""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at
    
    def touch(self) -> None:
        """Update access metadata."""
        self.access_count += 1
        self.last_accessed = time.time()


class CacheBackend(ABC):
    """Abstract base class for cache backends."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get a cache entry by key."""
        pass
    
    @abstractmethod
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None
    ) -> bool:
        """Set a cache entry."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete a cache entry."""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cache entries."""
        pass
    
    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        pass


class MemoryCacheBackend(CacheBackend):
    """Memory-based cache backend with LRU eviction."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = asyncio.Lock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0
        }
    
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get a cache entry by key."""
        async with self._lock:
            entry = self._cache.get(key)
            
            if entry is None:
                self._stats['misses'] += 1
                return None
            
            if entry.is_expired():
                del self._cache[key]
                self._stats['misses'] += 1
                return None
            
            # Move to end (most recently used)
            self._cache.move_to_end(key)
            entry.touch()
            self._stats['hits'] += 1
            
            return entry
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None
    ) -> bool:
        """Set a cache entry."""
        async with self._lock:
            try:
                # Calculate size
                size_bytes = len(json.dumps(value, default=str).encode('utf-8'))
                
                # Create cache entry
                expires_at = None
                if ttl is not None:
                    expires_at = time.time() + ttl
                elif self.default_ttl > 0:
                    expires_at = time.time() + self.default_ttl
                
                entry = CacheEntry(
                    key=key,
                    value=value,
                    created_at=time.time(),
                    expires_at=expires_at,
                    tags=tags or [],
                    size_bytes=size_bytes
                )
                
                # Evict if necessary
                while len(self._cache) >= self.max_size:
                    oldest_key = next(iter(self._cache))
                    del self._cache[oldest_key]
                    self._stats['evictions'] += 1
                
                self._cache[key] = entry
                self._cache.move_to_end(key)
                self._stats['sets'] += 1
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to set cache entry {key}: {e}")
                return False
    
    async def delete(self, key: str) -> bool:
        """Delete a cache entry."""
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats['deletes'] += 1
                return True
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries."""
        async with self._lock:
            self._cache.clear()
            return True
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        async with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                **self._stats,
                'size': len(self._cache),
                'max_size': self.max_size,
                'hit_rate': hit_rate,
                'memory_usage_bytes': sum(entry.size_bytes for entry in self._cache.values())
            }
    
    async def invalidate_by_tags(self, tags: List[str]) -> int:
        """Invalidate cache entries by tags."""
        async with self._lock:
            keys_to_delete = []
            
            for key, entry in self._cache.items():
                if any(tag in entry.tags for tag in tags):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                del self._cache[key]
            
            return len(keys_to_delete)


class RedisCacheBackend(CacheBackend):
    """Redis-based cache backend."""
    
    def __init__(self, redis_url: str, default_ttl: int = 3600):
        if not REDIS_AVAILABLE:
            raise ImportError("Redis is not available. Install redis package.")
        
        self.redis_url = redis_url
        self.default_ttl = default_ttl
        self._redis: Optional[redis.Redis] = None
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
    
    async def _get_redis(self) -> redis.Redis:
        """Get Redis connection."""
        if self._redis is None:
            self._redis = redis.from_url(self.redis_url)
        return self._redis
    
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get a cache entry by key."""
        try:
            r = await self._get_redis()
            data = await r.get(f"marketing_agent:{key}")
            
            if data is None:
                self._stats['misses'] += 1
                return None
            
            entry_data = json.loads(data)
            entry = CacheEntry(**entry_data)
            
            if entry.is_expired():
                await self.delete(key)
                self._stats['misses'] += 1
                return None
            
            entry.touch()
            self._stats['hits'] += 1
            
            # Update access metadata in Redis
            await r.set(
                f"marketing_agent:{key}",
                json.dumps(entry.__dict__, default=str),
                ex=int(entry.expires_at - time.time()) if entry.expires_at else self.default_ttl
            )
            
            return entry
            
        except Exception as e:
            logger.error(f"Redis get error for key {key}: {e}")
            self._stats['misses'] += 1
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None
    ) -> bool:
        """Set a cache entry."""
        try:
            r = await self._get_redis()
            
            # Calculate size
            size_bytes = len(json.dumps(value, default=str).encode('utf-8'))
            
            # Create cache entry
            expires_at = None
            if ttl is not None:
                expires_at = time.time() + ttl
            elif self.default_ttl > 0:
                expires_at = time.time() + self.default_ttl
            
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                expires_at=expires_at,
                tags=tags or [],
                size_bytes=size_bytes
            )
            
            # Store in Redis
            await r.set(
                f"marketing_agent:{key}",
                json.dumps(entry.__dict__, default=str),
                ex=ttl or self.default_ttl
            )
            
            # Store tags for invalidation
            if tags:
                for tag in tags:
                    await r.sadd(f"marketing_agent:tag:{tag}", key)
                    await r.expire(f"marketing_agent:tag:{tag}", ttl or self.default_ttl)
            
            self._stats['sets'] += 1
            return True
            
        except Exception as e:
            logger.error(f"Redis set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete a cache entry."""
        try:
            r = await self._get_redis()
            result = await r.delete(f"marketing_agent:{key}")
            
            if result > 0:
                self._stats['deletes'] += 1
                return True
            return False
            
        except Exception as e:
            logger.error(f"Redis delete error for key {key}: {e}")
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries."""
        try:
            r = await self._get_redis()
            keys = await r.keys("marketing_agent:*")
            
            if keys:
                await r.delete(*keys)
            
            return True
            
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            r = await self._get_redis()
            info = await r.info('memory')
            
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                **self._stats,
                'hit_rate': hit_rate,
                'redis_memory_usage': info.get('used_memory', 0),
                'redis_memory_human': info.get('used_memory_human', '0B')
            }
            
        except Exception as e:
            logger.error(f"Redis stats error: {e}")
            return self._stats
    
    async def invalidate_by_tags(self, tags: List[str]) -> int:
        """Invalidate cache entries by tags."""
        try:
            r = await self._get_redis()
            keys_to_delete = set()
            
            for tag in tags:
                tag_keys = await r.smembers(f"marketing_agent:tag:{tag}")
                keys_to_delete.update(tag_keys)
                await r.delete(f"marketing_agent:tag:{tag}")
            
            if keys_to_delete:
                prefixed_keys = [f"marketing_agent:{key.decode()}" for key in keys_to_delete]
                await r.delete(*prefixed_keys)
            
            return len(keys_to_delete)
            
        except Exception as e:
            logger.error(f"Redis tag invalidation error: {e}")
            return 0


class MarketingAgentCache:
    """Intelligent cache manager for marketing agent."""

    def __init__(
        self,
        backend: Optional[CacheBackend] = None,
        enable_compression: bool = True,
        enable_analytics: bool = True
    ):
        self.backend = backend or MemoryCacheBackend()
        self.enable_compression = enable_compression
        self.enable_analytics = enable_analytics
        self._key_generators: Dict[str, Callable] = {}
        self._invalidation_rules: Dict[str, List[str]] = {}

        # Register default key generators
        self._register_default_key_generators()
        self._register_default_invalidation_rules()

    def _register_default_key_generators(self) -> None:
        """Register default cache key generators."""
        self._key_generators.update({
            'marketing_strategy': self._generate_marketing_strategy_key,
            'campaign_strategy': self._generate_campaign_strategy_key,
            'social_media_content': self._generate_social_media_key,
            'seo_optimization': self._generate_seo_key,
            'conversation': self._generate_conversation_key
        })

    def _register_default_invalidation_rules(self) -> None:
        """Register default cache invalidation rules."""
        self._invalidation_rules.update({
            'user_data_updated': ['marketing_strategy', 'campaign_strategy', 'social_media_content'],
            'brand_info_changed': ['marketing_strategy', 'campaign_strategy'],
            'target_audience_changed': ['marketing_strategy', 'social_media_content'],
            'marketing_goals_changed': ['marketing_strategy', 'campaign_strategy'],
            'conversation_ended': ['conversation']
        })

    def _generate_marketing_strategy_key(self, context: Dict[str, Any]) -> str:
        """Generate cache key for marketing strategy."""
        key_data = {
            'type': 'marketing_strategy',
            'brand': context.get('brand_description', ''),
            'audience': context.get('target_audience', ''),
            'goals': context.get('marketing_goals', ''),
            'products': context.get('products_services', '')
        }
        return self._hash_key_data(key_data)

    def _generate_campaign_strategy_key(self, context: Dict[str, Any]) -> str:
        """Generate cache key for campaign strategy."""
        key_data = {
            'type': 'campaign_strategy',
            'brand': context.get('brand_description', ''),
            'audience': context.get('target_audience', ''),
            'goals': context.get('marketing_goals', ''),
            'budget': context.get('budget', ''),
            'timeline': context.get('timeline', '')
        }
        return self._hash_key_data(key_data)

    def _generate_social_media_key(self, context: Dict[str, Any]) -> str:
        """Generate cache key for social media content."""
        key_data = {
            'type': 'social_media_content',
            'brand': context.get('brand_description', ''),
            'audience': context.get('target_audience', ''),
            'platform': context.get('platform', ''),
            'content_type': context.get('content_type', ''),
            'tone': context.get('tone', '')
        }
        return self._hash_key_data(key_data)

    def _generate_seo_key(self, context: Dict[str, Any]) -> str:
        """Generate cache key for SEO optimization."""
        key_data = {
            'type': 'seo_optimization',
            'keywords': context.get('keywords', ''),
            'content_type': context.get('content_type', ''),
            'target_audience': context.get('target_audience', '')
        }
        return self._hash_key_data(key_data)

    def _generate_conversation_key(self, context: Dict[str, Any]) -> str:
        """Generate cache key for conversation responses."""
        key_data = {
            'type': 'conversation',
            'message': context.get('message', ''),
            'intent': context.get('intent', ''),
            'user_id': context.get('user_id', ''),
            'conversation_id': context.get('conversation_id', '')
        }
        return self._hash_key_data(key_data)

    def _hash_key_data(self, key_data: Dict[str, Any]) -> str:
        """Generate hash from key data."""
        # Sort keys for consistent hashing
        sorted_data = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.sha256(sorted_data.encode()).hexdigest()[:16]

    async def get(
        self,
        cache_type: str,
        context: Dict[str, Any],
        default: Any = None
    ) -> Any:
        """Get cached value."""
        try:
            # Generate cache key
            if cache_type not in self._key_generators:
                logger.warning(f"No key generator for cache type: {cache_type}")
                return default

            key = self._key_generators[cache_type](context)

            # Get from backend
            entry = await self.backend.get(key)

            if entry is None:
                return default

            # Log cache hit if analytics enabled
            if self.enable_analytics:
                logger.debug(f"Cache hit for {cache_type}: {key}")

            return entry.value

        except Exception as e:
            logger.error(f"Cache get error for {cache_type}: {e}")
            return default

    async def set(
        self,
        cache_type: str,
        context: Dict[str, Any],
        value: Any,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None
    ) -> bool:
        """Set cached value."""
        try:
            # Generate cache key
            if cache_type not in self._key_generators:
                logger.warning(f"No key generator for cache type: {cache_type}")
                return False

            key = self._key_generators[cache_type](context)

            # Add default tags
            cache_tags = tags or []
            cache_tags.extend([cache_type, f"user:{context.get('user_id', 'anonymous')}"])

            # Set in backend
            success = await self.backend.set(key, value, ttl, cache_tags)

            # Log cache set if analytics enabled
            if self.enable_analytics and success:
                logger.debug(f"Cache set for {cache_type}: {key}")

            return success

        except Exception as e:
            logger.error(f"Cache set error for {cache_type}: {e}")
            return False

    async def invalidate(self, cache_type: str, context: Dict[str, Any]) -> bool:
        """Invalidate specific cache entry."""
        try:
            if cache_type not in self._key_generators:
                return False

            key = self._key_generators[cache_type](context)
            return await self.backend.delete(key)

        except Exception as e:
            logger.error(f"Cache invalidation error for {cache_type}: {e}")
            return False

    async def invalidate_by_event(self, event: str, context: Dict[str, Any] = None) -> int:
        """Invalidate cache entries based on event."""
        try:
            if event not in self._invalidation_rules:
                logger.warning(f"No invalidation rules for event: {event}")
                return 0

            tags_to_invalidate = self._invalidation_rules[event].copy()

            # Add user-specific tags if context provided
            if context and context.get('user_id'):
                tags_to_invalidate.append(f"user:{context['user_id']}")

            # Invalidate by tags
            if hasattr(self.backend, 'invalidate_by_tags'):
                return await self.backend.invalidate_by_tags(tags_to_invalidate)
            else:
                # Fallback: clear all cache
                await self.backend.clear()
                return 1

        except Exception as e:
            logger.error(f"Cache event invalidation error for {event}: {e}")
            return 0

    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            backend_stats = await self.backend.get_stats()

            return {
                'backend_type': type(self.backend).__name__,
                'backend_stats': backend_stats,
                'key_generators': list(self._key_generators.keys()),
                'invalidation_rules': list(self._invalidation_rules.keys()),
                'compression_enabled': self.enable_compression,
                'analytics_enabled': self.enable_analytics
            }

        except Exception as e:
            logger.error(f"Cache stats error: {e}")
            return {}

    async def health_check(self) -> Dict[str, Any]:
        """Perform cache health check."""
        try:
            # Test basic operations
            test_key = "health_check"
            test_value = {"timestamp": time.time()}

            # Test set
            set_success = await self.backend.set(test_key, test_value, ttl=60)

            # Test get
            entry = await self.backend.get(test_key)
            get_success = entry is not None and entry.value == test_value

            # Test delete
            delete_success = await self.backend.delete(test_key)

            # Get stats
            stats = await self.backend.get_stats()

            return {
                'status': 'healthy' if all([set_success, get_success, delete_success]) else 'unhealthy',
                'operations': {
                    'set': set_success,
                    'get': get_success,
                    'delete': delete_success
                },
                'stats': stats
            }

        except Exception as e:
            logger.error(f"Cache health check error: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e)
            }


def create_cache_backend(config: Dict[str, Any]) -> CacheBackend:
    """Factory function to create cache backend based on configuration."""
    backend_type = config.get('backend', 'memory').lower()

    if backend_type == 'redis':
        if not REDIS_AVAILABLE:
            logger.warning("Redis not available, falling back to memory cache")
            return MemoryCacheBackend(
                max_size=config.get('max_size', 1000),
                default_ttl=config.get('ttl', 3600)
            )

        redis_url = config.get('redis_url') or os.getenv('REDIS_URL')
        if not redis_url:
            logger.warning("Redis URL not provided, falling back to memory cache")
            return MemoryCacheBackend(
                max_size=config.get('max_size', 1000),
                default_ttl=config.get('ttl', 3600)
            )

        return RedisCacheBackend(
            redis_url=redis_url,
            default_ttl=config.get('ttl', 3600)
        )

    else:  # memory backend
        return MemoryCacheBackend(
            max_size=config.get('max_size', 1000),
            default_ttl=config.get('ttl', 3600)
        )


# Cache decorator for easy function caching
def cache_result(
    cache_type: str,
    ttl: Optional[int] = None,
    tags: Optional[List[str]] = None,
    key_func: Optional[Callable] = None
):
    """Decorator to cache function results."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"

            # Try to get from cache
            cached_result = await cache.get(cache_type, {'cache_key': cache_key})
            if cached_result is not None:
                return cached_result

            # Execute function
            result = await func(*args, **kwargs)

            # Cache result
            await cache.set(cache_type, {'cache_key': cache_key}, result, ttl, tags)

            return result

        return wrapper
    return decorator


# Global cache instance
cache: Optional[MarketingAgentCache] = None


def initialize_cache(config: Dict[str, Any]) -> MarketingAgentCache:
    """Initialize global cache instance."""
    global cache

    backend = create_cache_backend(config)
    cache = MarketingAgentCache(
        backend=backend,
        enable_compression=config.get('enable_compression', True),
        enable_analytics=config.get('enable_analytics', True)
    )

    logger.info(f"Cache initialized with {type(backend).__name__} backend")
    return cache


def get_cache() -> Optional[MarketingAgentCache]:
    """Get global cache instance."""
    return cache
