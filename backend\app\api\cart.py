"""
Cart API endpoints for the Datagenius backend.

This module provides API endpoints for cart functionality.
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ..models.cart import CartResponse, AddToCartRequest, RemoveFromCartRequest, CartItemResponse
from ..models.auth import User
from ..database import (
    get_db, add_to_cart, get_cart_items, get_cart_item,
    update_cart_item_quantity, remove_from_cart, clear_cart
)
from ..auth import get_current_active_user

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/cart", tags=["Cart"])


@router.get("", response_model=CartResponse)
async def get_cart(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get the current user's cart.

    Args:
        db: Database session
        current_user: Current authenticated user

    Returns:
        Cart information
    """
    logger.info(f"User {current_user.id} requested cart")

    # Get cart items
    cart_items = get_cart_items(db, current_user.id)

    # Convert to response model
    cart_items_response = [CartItemResponse.model_validate(item, from_attributes=True) for item in cart_items]

    return {
        "items": cart_items_response,
        "total_items": sum(item.quantity for item in cart_items)
    }


@router.post("/add", response_model=CartItemResponse)
async def add_item_to_cart(
    request: AddToCartRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Add an item to the cart.

    Args:
        request: Add to cart request
        db: Database session
        current_user: Current authenticated user

    Returns:
        Added cart item
    """
    logger.info(f"User {current_user.id} adding item to cart: {request.persona_id}")

    # Add to cart
    cart_item = add_to_cart(db, current_user.id, request.persona_id, request.quantity)

    return CartItemResponse.model_validate(cart_item, from_attributes=True)


@router.put("/update/{cart_item_id}", response_model=CartItemResponse)
async def update_cart_item(
    cart_item_id: str,
    quantity: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update a cart item quantity.

    Args:
        cart_item_id: ID of the cart item
        quantity: New quantity
        db: Database session
        current_user: Current authenticated user

    Returns:
        Updated cart item
    """
    logger.info(f"User {current_user.id} updating cart item {cart_item_id} to quantity {quantity}")

    # Get cart item
    cart_item = get_cart_item(db, cart_item_id)
    if not cart_item:
        raise HTTPException(status_code=404, detail="Cart item not found")

    # Check if the cart item belongs to the user
    if cart_item.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this cart item")

    # Update quantity
    updated_item = update_cart_item_quantity(db, cart_item_id, quantity)

    return CartItemResponse.model_validate(updated_item, from_attributes=True)


@router.delete("/remove", response_model=dict)
async def remove_item_from_cart(
    request: RemoveFromCartRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Remove an item from the cart.

    Args:
        request: Remove from cart request
        db: Database session
        current_user: Current authenticated user

    Returns:
        Success message
    """
    logger.info(f"User {current_user.id} removing item from cart: {request.cart_item_id}")

    # Get cart item
    cart_item = get_cart_item(db, request.cart_item_id)
    if not cart_item:
        raise HTTPException(status_code=404, detail="Cart item not found")

    # Check if the cart item belongs to the user
    if cart_item.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to remove this cart item")

    # Remove from cart
    success = remove_from_cart(db, request.cart_item_id)

    if not success:
        raise HTTPException(status_code=500, detail="Failed to remove item from cart")

    return {"message": "Item removed from cart successfully"}


@router.delete("/clear", response_model=dict)
async def clear_user_cart(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Clear all items from the cart.

    Args:
        db: Database session
        current_user: Current authenticated user

    Returns:
        Success message
    """
    logger.info(f"User {current_user.id} clearing cart")

    # Clear cart
    success = clear_cart(db, current_user.id)

    if not success:
        raise HTTPException(status_code=500, detail="Failed to clear cart")

    return {"message": "Cart cleared successfully"}
