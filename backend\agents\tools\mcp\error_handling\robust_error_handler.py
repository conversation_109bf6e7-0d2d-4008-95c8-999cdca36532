"""
Robust Error Handling and Recovery System for MCP Tools.

This module provides comprehensive error handling with retry mechanisms,
graceful degradation, detailed error logging, and user-friendly error messages.
"""

import asyncio
import logging
import traceback
import time
from typing import Dict, Any, List, Optional, Callable, Union, Type
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
from functools import wraps

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    NETWORK = "network"
    TIMEOUT = "timeout"
    RATE_LIMIT = "rate_limit"
    RESOURCE = "resource"
    DATA = "data"
    CONFIGURATION = "configuration"
    EXTERNAL_SERVICE = "external_service"
    INTERNAL = "internal"
    USER_INPUT = "user_input"
    SYSTEM = "system"


class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""
    RETRY = "retry"
    FALLBACK = "fallback"
    GRACEFUL_DEGRADATION = "graceful_degradation"
    FAIL_FAST = "fail_fast"
    CIRCUIT_BREAKER = "circuit_breaker"
    BACKOFF = "backoff"


@dataclass
class ErrorContext:
    """Context information for error handling."""
    tool_name: str
    operation: str
    agent_identity: Optional[str] = None
    user_id: Optional[str] = None
    input_data: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.now)
    request_id: Optional[str] = None
    session_id: Optional[str] = None


@dataclass
class ErrorInfo:
    """Comprehensive error information."""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    user_message: str
    technical_details: str
    context: ErrorContext
    recovery_strategy: RecoveryStrategy
    retry_count: int = 0
    max_retries: int = 3
    backoff_delay: float = 1.0
    is_recoverable: bool = True
    suggested_actions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RecoveryResult:
    """Result of error recovery attempt."""
    success: bool
    result: Optional[Any] = None
    error: Optional[ErrorInfo] = None
    recovery_actions_taken: List[str] = field(default_factory=list)
    final_attempt: bool = False


class CircuitBreaker:
    """Circuit breaker for external service calls."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: Type[Exception] = Exception
    ):
        """
        Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time to wait before attempting recovery
            expected_exception: Exception type to monitor
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
    
    async def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        if self.state == "open":
            if self._should_attempt_reset():
                self.state = "half-open"
            else:
                raise Exception("Circuit breaker is open")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit should attempt reset."""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self):
        """Handle successful call."""
        self.failure_count = 0
        self.state = "closed"
    
    def _on_failure(self):
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"


class RobustErrorHandler:
    """
    Comprehensive error handling system for MCP tools.
    
    Features:
    - Intelligent error classification
    - Retry mechanisms with exponential backoff
    - Circuit breaker pattern
    - Graceful degradation
    - User-friendly error messages
    - Detailed error logging
    - Recovery strategies
    - Error analytics
    """
    
    def __init__(self):
        """Initialize the error handler."""
        self.error_history: List[ErrorInfo] = []
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.error_patterns: Dict[str, ErrorCategory] = self._initialize_error_patterns()
        self.recovery_strategies: Dict[ErrorCategory, RecoveryStrategy] = self._initialize_recovery_strategies()
        
        logger.info("Robust error handler initialized")
    
    def _initialize_error_patterns(self) -> Dict[str, ErrorCategory]:
        """Initialize error pattern matching."""
        return {
            # Network errors
            "connection": ErrorCategory.NETWORK,
            "timeout": ErrorCategory.TIMEOUT,
            "dns": ErrorCategory.NETWORK,
            "ssl": ErrorCategory.NETWORK,
            
            # Authentication/Authorization
            "unauthorized": ErrorCategory.AUTHENTICATION,
            "forbidden": ErrorCategory.AUTHORIZATION,
            "invalid.*key": ErrorCategory.AUTHENTICATION,
            "permission": ErrorCategory.AUTHORIZATION,
            
            # Rate limiting
            "rate.*limit": ErrorCategory.RATE_LIMIT,
            "quota.*exceeded": ErrorCategory.RATE_LIMIT,
            "too.*many.*requests": ErrorCategory.RATE_LIMIT,
            
            # Resource errors
            "memory": ErrorCategory.RESOURCE,
            "disk.*space": ErrorCategory.RESOURCE,
            "cpu": ErrorCategory.RESOURCE,
            
            # Data errors
            "invalid.*format": ErrorCategory.DATA,
            "parse.*error": ErrorCategory.DATA,
            "corrupt": ErrorCategory.DATA,
            "missing.*data": ErrorCategory.DATA,
            
            # Validation errors
            "validation": ErrorCategory.VALIDATION,
            "invalid.*input": ErrorCategory.VALIDATION,
            "required.*field": ErrorCategory.VALIDATION,
            
            # Configuration errors
            "config": ErrorCategory.CONFIGURATION,
            "setting": ErrorCategory.CONFIGURATION,
            "environment": ErrorCategory.CONFIGURATION,
        }
    
    def _initialize_recovery_strategies(self) -> Dict[ErrorCategory, RecoveryStrategy]:
        """Initialize recovery strategies for error categories."""
        return {
            ErrorCategory.NETWORK: RecoveryStrategy.RETRY,
            ErrorCategory.TIMEOUT: RecoveryStrategy.RETRY,
            ErrorCategory.RATE_LIMIT: RecoveryStrategy.BACKOFF,
            ErrorCategory.RESOURCE: RecoveryStrategy.GRACEFUL_DEGRADATION,
            ErrorCategory.EXTERNAL_SERVICE: RecoveryStrategy.CIRCUIT_BREAKER,
            ErrorCategory.DATA: RecoveryStrategy.FALLBACK,
            ErrorCategory.VALIDATION: RecoveryStrategy.FAIL_FAST,
            ErrorCategory.AUTHENTICATION: RecoveryStrategy.FAIL_FAST,
            ErrorCategory.AUTHORIZATION: RecoveryStrategy.FAIL_FAST,
            ErrorCategory.CONFIGURATION: RecoveryStrategy.FAIL_FAST,
            ErrorCategory.INTERNAL: RecoveryStrategy.RETRY,
            ErrorCategory.SYSTEM: RecoveryStrategy.GRACEFUL_DEGRADATION,
        }
    
    async def handle_error(
        self,
        error: Exception,
        context: ErrorContext,
        custom_recovery: Optional[Callable] = None
    ) -> RecoveryResult:
        """
        Handle an error with appropriate recovery strategy.
        
        Args:
            error: The exception that occurred
            context: Error context information
            custom_recovery: Custom recovery function
            
        Returns:
            Recovery result
        """
        # Create error info
        error_info = self._create_error_info(error, context)
        
        # Log error
        self._log_error(error_info)
        
        # Store in history
        self.error_history.append(error_info)
        
        # Attempt recovery
        recovery_result = await self._attempt_recovery(error_info, custom_recovery)
        
        return recovery_result
    
    def _create_error_info(self, error: Exception, context: ErrorContext) -> ErrorInfo:
        """Create comprehensive error information."""
        error_message = str(error)
        error_type = type(error).__name__
        
        # Classify error
        category = self._classify_error(error_message, error_type)
        severity = self._determine_severity(category, error)
        recovery_strategy = self.recovery_strategies.get(category, RecoveryStrategy.RETRY)
        
        # Generate user-friendly message
        user_message = self._generate_user_message(category, error_message)
        
        # Generate suggested actions
        suggested_actions = self._generate_suggested_actions(category, error_message)
        
        # Create error ID
        error_id = f"{context.tool_name}_{int(time.time())}_{hash(error_message) % 10000}"
        
        return ErrorInfo(
            error_id=error_id,
            category=category,
            severity=severity,
            message=error_message,
            user_message=user_message,
            technical_details=traceback.format_exc(),
            context=context,
            recovery_strategy=recovery_strategy,
            is_recoverable=self._is_recoverable(category, error),
            suggested_actions=suggested_actions,
            metadata={
                "error_type": error_type,
                "timestamp": datetime.now().isoformat(),
                "python_version": "3.x",
                "tool_version": "1.0"
            }
        )
    
    def _classify_error(self, error_message: str, error_type: str) -> ErrorCategory:
        """Classify error based on message and type."""
        error_text = f"{error_message} {error_type}".lower()
        
        # Check patterns
        for pattern, category in self.error_patterns.items():
            if pattern in error_text:
                return category
        
        # Check by exception type
        if "timeout" in error_type.lower():
            return ErrorCategory.TIMEOUT
        elif "connection" in error_type.lower():
            return ErrorCategory.NETWORK
        elif "validation" in error_type.lower():
            return ErrorCategory.VALIDATION
        elif "permission" in error_type.lower():
            return ErrorCategory.AUTHORIZATION
        
        return ErrorCategory.INTERNAL
    
    def _determine_severity(self, category: ErrorCategory, error: Exception) -> ErrorSeverity:
        """Determine error severity."""
        if category in [ErrorCategory.SYSTEM, ErrorCategory.CRITICAL]:
            return ErrorSeverity.CRITICAL
        elif category in [ErrorCategory.AUTHENTICATION, ErrorCategory.AUTHORIZATION, ErrorCategory.CONFIGURATION]:
            return ErrorSeverity.HIGH
        elif category in [ErrorCategory.NETWORK, ErrorCategory.EXTERNAL_SERVICE, ErrorCategory.RESOURCE]:
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _generate_user_message(self, category: ErrorCategory, error_message: str) -> str:
        """Generate user-friendly error message."""
        user_messages = {
            ErrorCategory.NETWORK: "Unable to connect to the service. Please check your internet connection and try again.",
            ErrorCategory.TIMEOUT: "The operation took too long to complete. Please try again.",
            ErrorCategory.RATE_LIMIT: "Too many requests have been made. Please wait a moment and try again.",
            ErrorCategory.AUTHENTICATION: "Authentication failed. Please check your API key or credentials.",
            ErrorCategory.AUTHORIZATION: "You don't have permission to perform this action.",
            ErrorCategory.VALIDATION: "The input data is invalid. Please check your input and try again.",
            ErrorCategory.DATA: "There was an issue with the data format. Please verify your data and try again.",
            ErrorCategory.RESOURCE: "System resources are currently limited. Please try again later.",
            ErrorCategory.CONFIGURATION: "There's a configuration issue. Please contact support.",
            ErrorCategory.EXTERNAL_SERVICE: "An external service is currently unavailable. Please try again later.",
        }
        
        return user_messages.get(category, "An unexpected error occurred. Please try again or contact support.")
    
    def _generate_suggested_actions(self, category: ErrorCategory, error_message: str) -> List[str]:
        """Generate suggested actions for error recovery."""
        actions = {
            ErrorCategory.NETWORK: [
                "Check your internet connection",
                "Verify the service URL is correct",
                "Try again in a few moments"
            ],
            ErrorCategory.TIMEOUT: [
                "Reduce the size of your request",
                "Try again with a simpler query",
                "Check if the service is experiencing high load"
            ],
            ErrorCategory.RATE_LIMIT: [
                "Wait before making another request",
                "Reduce the frequency of requests",
                "Consider upgrading your service plan"
            ],
            ErrorCategory.AUTHENTICATION: [
                "Verify your API key is correct",
                "Check if your credentials have expired",
                "Ensure you have the right permissions"
            ],
            ErrorCategory.VALIDATION: [
                "Check the format of your input data",
                "Verify all required fields are provided",
                "Review the documentation for correct usage"
            ],
            ErrorCategory.DATA: [
                "Verify your data file is not corrupted",
                "Check the data format is supported",
                "Try with a smaller dataset"
            ]
        }
        
        return actions.get(category, ["Try again", "Contact support if the issue persists"])
    
    def _is_recoverable(self, category: ErrorCategory, error: Exception) -> bool:
        """Determine if error is recoverable."""
        non_recoverable = [
            ErrorCategory.AUTHENTICATION,
            ErrorCategory.AUTHORIZATION,
            ErrorCategory.CONFIGURATION,
            ErrorCategory.VALIDATION
        ]
        
        return category not in non_recoverable
    
    async def _attempt_recovery(
        self,
        error_info: ErrorInfo,
        custom_recovery: Optional[Callable] = None
    ) -> RecoveryResult:
        """Attempt error recovery based on strategy."""
        recovery_actions = []
        
        try:
            if custom_recovery:
                # Try custom recovery first
                result = await custom_recovery(error_info)
                recovery_actions.append("custom_recovery")
                return RecoveryResult(
                    success=True,
                    result=result,
                    recovery_actions_taken=recovery_actions
                )
            
            # Apply strategy-based recovery
            if error_info.recovery_strategy == RecoveryStrategy.RETRY:
                return await self._retry_recovery(error_info, recovery_actions)
            elif error_info.recovery_strategy == RecoveryStrategy.BACKOFF:
                return await self._backoff_recovery(error_info, recovery_actions)
            elif error_info.recovery_strategy == RecoveryStrategy.CIRCUIT_BREAKER:
                return await self._circuit_breaker_recovery(error_info, recovery_actions)
            elif error_info.recovery_strategy == RecoveryStrategy.GRACEFUL_DEGRADATION:
                return await self._graceful_degradation_recovery(error_info, recovery_actions)
            elif error_info.recovery_strategy == RecoveryStrategy.FALLBACK:
                return await self._fallback_recovery(error_info, recovery_actions)
            else:
                # Fail fast
                return RecoveryResult(
                    success=False,
                    error=error_info,
                    recovery_actions_taken=["fail_fast"],
                    final_attempt=True
                )
                
        except Exception as recovery_error:
            logger.error(f"Recovery attempt failed: {recovery_error}")
            return RecoveryResult(
                success=False,
                error=error_info,
                recovery_actions_taken=recovery_actions,
                final_attempt=True
            )
    
    async def _retry_recovery(self, error_info: ErrorInfo, recovery_actions: List[str]) -> RecoveryResult:
        """Implement retry recovery strategy."""
        if error_info.retry_count >= error_info.max_retries:
            return RecoveryResult(
                success=False,
                error=error_info,
                recovery_actions_taken=recovery_actions + ["max_retries_reached"],
                final_attempt=True
            )
        
        # Exponential backoff
        delay = error_info.backoff_delay * (2 ** error_info.retry_count)
        await asyncio.sleep(min(delay, 60))  # Cap at 60 seconds
        
        error_info.retry_count += 1
        recovery_actions.append(f"retry_{error_info.retry_count}")
        
        return RecoveryResult(
            success=False,  # Indicates retry needed
            error=error_info,
            recovery_actions_taken=recovery_actions
        )
    
    async def _backoff_recovery(self, error_info: ErrorInfo, recovery_actions: List[str]) -> RecoveryResult:
        """Implement backoff recovery strategy."""
        # Longer delay for rate limiting
        delay = min(30 * (2 ** error_info.retry_count), 300)  # Cap at 5 minutes
        await asyncio.sleep(delay)
        
        recovery_actions.append(f"backoff_{delay}s")
        return await self._retry_recovery(error_info, recovery_actions)
    
    async def _circuit_breaker_recovery(self, error_info: ErrorInfo, recovery_actions: List[str]) -> RecoveryResult:
        """Implement circuit breaker recovery strategy."""
        service_key = f"{error_info.context.tool_name}_{error_info.category.value}"
        
        if service_key not in self.circuit_breakers:
            self.circuit_breakers[service_key] = CircuitBreaker()
        
        circuit_breaker = self.circuit_breakers[service_key]
        
        if circuit_breaker.state == "open":
            recovery_actions.append("circuit_breaker_open")
            return RecoveryResult(
                success=False,
                error=error_info,
                recovery_actions_taken=recovery_actions,
                final_attempt=True
            )
        
        recovery_actions.append("circuit_breaker_monitoring")
        return await self._retry_recovery(error_info, recovery_actions)
    
    async def _graceful_degradation_recovery(self, error_info: ErrorInfo, recovery_actions: List[str]) -> RecoveryResult:
        """Implement graceful degradation recovery strategy."""
        # Return a simplified or cached result
        degraded_result = {
            "content": [{
                "type": "text",
                "text": f"Service temporarily unavailable. {error_info.user_message}"
            }],
            "metadata": {
                "degraded_service": True,
                "error_category": error_info.category.value,
                "suggested_actions": error_info.suggested_actions
            }
        }
        
        recovery_actions.append("graceful_degradation")
        return RecoveryResult(
            success=True,
            result=degraded_result,
            recovery_actions_taken=recovery_actions
        )
    
    async def _fallback_recovery(self, error_info: ErrorInfo, recovery_actions: List[str]) -> RecoveryResult:
        """Implement fallback recovery strategy."""
        # Provide alternative functionality
        fallback_result = {
            "content": [{
                "type": "text",
                "text": f"Primary service unavailable. {error_info.user_message} Please try a simpler request."
            }],
            "metadata": {
                "fallback_mode": True,
                "original_error": error_info.message,
                "suggested_actions": error_info.suggested_actions
            }
        }
        
        recovery_actions.append("fallback_service")
        return RecoveryResult(
            success=True,
            result=fallback_result,
            recovery_actions_taken=recovery_actions
        )
    
    def _log_error(self, error_info: ErrorInfo):
        """Log error with appropriate level."""
        log_data = {
            "error_id": error_info.error_id,
            "category": error_info.category.value,
            "severity": error_info.severity.value,
            "tool": error_info.context.tool_name,
            "agent": error_info.context.agent_identity,
            "message": error_info.message,
            "recovery_strategy": error_info.recovery_strategy.value
        }
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(f"CRITICAL ERROR: {json.dumps(log_data)}")
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(f"HIGH SEVERITY ERROR: {json.dumps(log_data)}")
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(f"MEDIUM SEVERITY ERROR: {json.dumps(log_data)}")
        else:
            logger.info(f"LOW SEVERITY ERROR: {json.dumps(log_data)}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics and insights."""
        if not self.error_history:
            return {"total_errors": 0}
        
        # Calculate statistics
        total_errors = len(self.error_history)
        category_counts = {}
        severity_counts = {}
        
        for error in self.error_history:
            category_counts[error.category.value] = category_counts.get(error.category.value, 0) + 1
            severity_counts[error.severity.value] = severity_counts.get(error.severity.value, 0) + 1
        
        # Recent errors (last hour)
        recent_cutoff = datetime.now() - timedelta(hours=1)
        recent_errors = [e for e in self.error_history if e.context.timestamp >= recent_cutoff]
        
        return {
            "total_errors": total_errors,
            "recent_errors_1h": len(recent_errors),
            "error_categories": category_counts,
            "error_severities": severity_counts,
            "most_common_category": max(category_counts.items(), key=lambda x: x[1])[0] if category_counts else None,
            "circuit_breakers": {k: v.state for k, v in self.circuit_breakers.items()}
        }


# Global error handler instance
_global_error_handler: Optional[RobustErrorHandler] = None


def get_error_handler() -> RobustErrorHandler:
    """Get global error handler instance."""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = RobustErrorHandler()
    return _global_error_handler


def handle_tool_error(context: ErrorContext):
    """Decorator for handling tool errors."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            error_handler = get_error_handler()
            
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                recovery_result = await error_handler.handle_error(e, context)
                
                if recovery_result.success:
                    return recovery_result.result
                else:
                    # Return error response in MCP format
                    return {
                        "isError": True,
                        "content": [{
                            "type": "text",
                            "text": recovery_result.error.user_message
                        }],
                        "metadata": {
                            "error_id": recovery_result.error.error_id,
                            "category": recovery_result.error.category.value,
                            "severity": recovery_result.error.severity.value,
                            "suggested_actions": recovery_result.error.suggested_actions,
                            "recovery_actions_taken": recovery_result.recovery_actions_taken
                        }
                    }
        
        return wrapper
    return decorator
