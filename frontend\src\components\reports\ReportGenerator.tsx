
import { useState } from "react";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  BarChart4, 
  Line<PERSON>hart, 
  PieChart, 
  Table2, 
  Calendar, 
  ArrowRight, 
  Download, 
  Save
} from "lucide-react";
import { ReportPreview } from "./ReportPreview";
import { VisualizationSelector } from "./VisualizationSelector";
import { useToast } from "@/hooks/use-toast";

interface DataSource {
  id: string;
  name: string;
  type: string;
  icon: React.ComponentType<any>;
}

interface TimeRange {
  id: string;
  label: string;
}

export function ReportGenerator() {
  const { toast } = useToast();
  const [selectedSource, setSelectedSource] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>("last-30-days");
  const [selectedVisualizations, setSelectedVisualizations] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [reportGenerated, setReportGenerated] = useState(false);

  const dataSources: DataSource[] = [
    { id: "sales", name: "Sales Data", type: "Analytics", icon: BarChart4 },
    { id: "customers", name: "Customer Data", type: "CRM", icon: PieChart },
    { id: "marketing", name: "Marketing Campaigns", type: "Marketing", icon: LineChart },
    { id: "inventory", name: "Inventory Data", type: "Operations", icon: Table2 },
  ];
  
  const timeRanges: TimeRange[] = [
    { id: "last-7-days", label: "Last 7 Days" },
    { id: "last-30-days", label: "Last 30 Days" },
    { id: "last-90-days", label: "Last Quarter" },
    { id: "last-year", label: "Last Year" },
    { id: "custom", label: "Custom Range" },
  ];

  const handleGenerateReport = () => {
    if (!selectedSource) {
      toast({
        title: "Error",
        description: "Please select a data source",
        variant: "destructive",
      });
      return;
    }
    
    if (selectedVisualizations.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one visualization",
        variant: "destructive",
      });
      return;
    }
    
    setIsGenerating(true);
    
    // Simulate report generation
    setTimeout(() => {
      setIsGenerating(false);
      setReportGenerated(true);
      toast({
        title: "Report Generated",
        description: "Your report has been successfully created",
      });
    }, 2000);
  };
  
  const handleSaveReport = () => {
    toast({
      title: "Report Saved",
      description: "Report has been saved to your library",
    });
  };
  
  const handleDownloadReport = () => {
    toast({
      title: "Download Started",
      description: "Your report is being downloaded",
    });
  };
  
  const handleScheduleReport = () => {
    toast({
      title: "Schedule Report",
      description: "Opening scheduler for this report",
    });
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">1. Select Data Source</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          {dataSources.map((source) => (
            <div
              key={source.id}
              className={`
                p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md
                ${selectedSource === source.id ? "border-primary bg-primary/5" : "border-border hover:border-primary/50"}
              `}
              onClick={() => setSelectedSource(source.id)}
            >
              <div className="flex flex-col items-center text-center space-y-2">
                <source.icon className="h-10 w-10 text-primary" />
                <h3 className="font-medium">{source.name}</h3>
                <p className="text-xs text-muted-foreground">{source.type}</p>
              </div>
            </div>
          ))}
        </div>
      </Card>
      
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">2. Select Time Range</h2>
        <div className="flex items-center gap-4">
          <div className="w-full max-w-xs">
            <Select 
              value={selectedTimeRange} 
              onValueChange={setSelectedTimeRange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select time period" />
              </SelectTrigger>
              <SelectContent>
                {timeRanges.map((range) => (
                  <SelectItem key={range.id} value={range.id}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center">
            <Calendar className="h-5 w-5 text-muted-foreground mr-2" />
            <span className="text-sm text-muted-foreground">
              {selectedTimeRange === "last-7-days" && "May 23, 2023 - May 30, 2023"}
              {selectedTimeRange === "last-30-days" && "May 1, 2023 - May 30, 2023"}
              {selectedTimeRange === "last-90-days" && "Mar 1, 2023 - May 30, 2023"}
              {selectedTimeRange === "last-year" && "Jun 1, 2022 - May 30, 2023"}
              {selectedTimeRange === "custom" && "Select custom dates"}
            </span>
          </div>
        </div>
      </Card>
      
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">3. Select Visualizations</h2>
        <VisualizationSelector 
          selectedVisualizations={selectedVisualizations}
          setSelectedVisualizations={setSelectedVisualizations}
        />
      </Card>
      
      <div className="flex justify-end mb-6">
        <Button
          onClick={handleGenerateReport}
          disabled={!selectedSource || selectedVisualizations.length === 0 || isGenerating}
          className="gap-2"
        >
          {isGenerating ? "Generating..." : "Generate Report"}
          {!isGenerating && <ArrowRight className="h-4 w-4" />}
        </Button>
      </div>
      
      {reportGenerated && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Report Preview</h2>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="gap-2"
                  onClick={handleSaveReport}
                >
                  <Save className="h-4 w-4" />
                  Save
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="gap-2"
                  onClick={handleDownloadReport}
                >
                  <Download className="h-4 w-4" />
                  Download
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleScheduleReport}
                >
                  Schedule
                </Button>
              </div>
            </div>
            
            <ReportPreview 
              dataSource={selectedSource as string}
              timeRange={selectedTimeRange}
              visualizations={selectedVisualizations}
            />
          </Card>
        </motion.div>
      )}
    </div>
  );
}
