"""
Configuration loader for the Concierge Agent LLM analysis system.

This module loads and validates YAML configurations using Pydantic models.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, field_validator, ConfigDict
from .models import LLMAnalysisConfig

logger = logging.getLogger(__name__)


class PersonaDescription(BaseModel):
    """Model for persona description in configuration."""
    name: str = Field(..., description="Display name of the persona")
    description: str = Field(..., description="Detailed description of the persona")
    capabilities: List[str] = Field(..., description="List of persona capabilities")
    best_for: List[str] = Field(..., description="List of use cases this persona is best for")


class AnalysisPromptConfig(BaseModel):
    """Model for analysis prompt configuration."""
    system_message: str = Field(..., description="System message for the LLM")
    user_prompt_template: str = Field(..., description="Template for user prompts")

    @field_validator('system_message', 'user_prompt_template')
    @classmethod
    def validate_not_empty(cls, v):
        if not v.strip():
            raise ValueError("Prompt cannot be empty")
        return v


class IntentPromptConfig(BaseModel):
    """Model for intent-specific prompt configuration."""
    focus: str = Field(..., description="Focus area for this intent type")


class FallbackResponse(BaseModel):
    """Model for fallback response configuration."""
    intent_type: str = Field(..., description="Default intent type for fallback")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Default confidence score")
    reasoning: str = Field(..., description="Explanation for the fallback")


class AnalysisMode(BaseModel):
    """Model for analysis mode configuration."""
    temperature: float = Field(..., ge=0.0, le=2.0, description="LLM temperature")
    max_tokens: int = Field(..., gt=0, description="Maximum tokens for response")
    timeout: int = Field(..., gt=0, description="Timeout in seconds")
    description: str = Field(..., description="Description of this analysis mode")


class ValidationRules(BaseModel):
    """Model for validation rules configuration."""
    min_confidence: float = Field(..., ge=0.0, le=1.0, description="Minimum confidence threshold")
    max_personas: int = Field(..., gt=0, description="Maximum number of personas to suggest")
    required_reasoning_length: int = Field(..., gt=0, description="Minimum reasoning text length")
    max_follow_up_questions: int = Field(..., gt=0, description="Maximum follow-up questions")


class LLMAnalysisPromptConfig(BaseModel):
    """Complete configuration model for LLM analysis prompts."""
    analysis_prompt: AnalysisPromptConfig = Field(..., description="Main analysis prompt configuration")
    persona_descriptions: Dict[str, PersonaDescription] = Field(..., description="Persona descriptions")
    intent_prompts: Dict[str, IntentPromptConfig] = Field(..., description="Intent-specific prompts")
    fallback_responses: Dict[str, FallbackResponse] = Field(..., description="Fallback responses")
    analysis_modes: Dict[str, AnalysisMode] = Field(..., description="Analysis mode configurations")
    validation_rules: ValidationRules = Field(..., description="Validation rules")

    @field_validator('persona_descriptions')
    @classmethod
    def validate_persona_descriptions(cls, v):
        required_personas = [
            "concierge-agent", "composable-analyst", "composable-marketer",
            "composable-classifier", "data-assistant"
        ]

        for persona in required_personas:
            if persona not in v:
                logger.warning(f"Missing description for required persona: {persona}")

        return v

    @field_validator('intent_prompts')
    @classmethod
    def validate_intent_prompts(cls, v):
        required_intents = [
            "persona_request", "data_help", "analysis_request",
            "marketing_request", "classification_request", "general_question"
        ]

        for intent in required_intents:
            if intent not in v:
                logger.warning(f"Missing prompt configuration for intent: {intent}")

        return v


class ConfigLoader:
    """Loads and validates LLM analysis configuration from YAML files."""

    def __init__(self, config_dir: Optional[Path] = None):
        """
        Initialize the configuration loader.

        Args:
            config_dir: Directory containing configuration files
        """
        if config_dir is None:
            config_dir = Path(__file__).parent.parent / "configs"

        self.config_dir = Path(config_dir)
        self._prompt_config: Optional[LLMAnalysisPromptConfig] = None
        self._analysis_config: Optional[LLMAnalysisConfig] = None

    def load_prompt_config(self, filename: str = "llm_analysis_prompts.yaml") -> LLMAnalysisPromptConfig:
        """
        Load and validate prompt configuration from YAML.

        Args:
            filename: Name of the YAML configuration file

        Returns:
            Validated prompt configuration

        Raises:
            FileNotFoundError: If configuration file is not found
            ValueError: If configuration is invalid
        """
        config_path = self.config_dir / filename

        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            self._prompt_config = LLMAnalysisPromptConfig(**config_data)
            logger.info(f"Successfully loaded prompt configuration from {config_path}")
            return self._prompt_config

        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in configuration file: {e}")
        except Exception as e:
            raise ValueError(f"Error loading configuration: {e}")

    def load_analysis_config(self, config_data: Optional[Dict[str, Any]] = None) -> LLMAnalysisConfig:
        """
        Load and validate analysis configuration.

        Args:
            config_data: Configuration data (if None, uses defaults)

        Returns:
            Validated analysis configuration
        """
        if config_data is None:
            config_data = {}

        try:
            self._analysis_config = LLMAnalysisConfig(**config_data)
            logger.info("Successfully loaded analysis configuration")
            return self._analysis_config

        except Exception as e:
            raise ValueError(f"Error loading analysis configuration: {e}")

    def get_prompt_config(self) -> LLMAnalysisPromptConfig:
        """Get the loaded prompt configuration."""
        if self._prompt_config is None:
            self._prompt_config = self.load_prompt_config()
        return self._prompt_config

    def get_analysis_config(self) -> LLMAnalysisConfig:
        """Get the loaded analysis configuration."""
        if self._analysis_config is None:
            self._analysis_config = self.load_analysis_config()
        return self._analysis_config

    def get_persona_description(self, persona_id: str) -> Optional[PersonaDescription]:
        """
        Get description for a specific persona.

        Args:
            persona_id: ID of the persona

        Returns:
            Persona description or None if not found
        """
        config = self.get_prompt_config()
        return config.persona_descriptions.get(persona_id)

    def get_analysis_mode(self, mode: str = "quick") -> AnalysisMode:
        """
        Get configuration for a specific analysis mode.

        Args:
            mode: Analysis mode name

        Returns:
            Analysis mode configuration

        Raises:
            ValueError: If mode is not found
        """
        config = self.get_prompt_config()

        if mode not in config.analysis_modes:
            available_modes = list(config.analysis_modes.keys())
            raise ValueError(f"Unknown analysis mode '{mode}'. Available modes: {available_modes}")

        return config.analysis_modes[mode]

    def get_fallback_response(self, error_type: str = "llm_error") -> FallbackResponse:
        """
        Get fallback response for a specific error type.

        Args:
            error_type: Type of error

        Returns:
            Fallback response configuration
        """
        config = self.get_prompt_config()
        return config.fallback_responses.get(error_type, config.fallback_responses["llm_error"])

    def validate_response(self, response_data: Dict[str, Any]) -> bool:
        """
        Validate an LLM response against the configuration rules.

        Args:
            response_data: Response data to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            config = self.get_prompt_config()
            rules = config.validation_rules

            # Check confidence
            confidence = response_data.get("confidence", 0)
            if confidence < rules.min_confidence:
                logger.warning(f"Confidence {confidence} below minimum {rules.min_confidence}")
                return False

            # Check persona count
            personas = response_data.get("suggested_personas", [])
            if len(personas) > rules.max_personas:
                logger.warning(f"Too many personas suggested: {len(personas)} > {rules.max_personas}")
                return False

            # Check reasoning length
            reasoning = response_data.get("reasoning", "")
            if len(reasoning) < rules.required_reasoning_length:
                logger.warning(f"Reasoning too short: {len(reasoning)} < {rules.required_reasoning_length}")
                return False

            # Check follow-up questions
            questions = response_data.get("follow_up_questions", [])
            if len(questions) > rules.max_follow_up_questions:
                logger.warning(f"Too many follow-up questions: {len(questions)} > {rules.max_follow_up_questions}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating response: {e}")
            return False


# Global configuration loader instance
_config_loader: Optional[ConfigLoader] = None


def get_config_loader() -> ConfigLoader:
    """Get the global configuration loader instance."""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader
