"""
Shared Context Manager Component for Phase 2 Architecture Consolidation.

This component consolidates context management logic that was previously duplicated
across different agents, providing unified conversation history, state management,
and cross-persona context sharing capabilities.
"""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from ..base_component import BaseAgentComponent, AgentContext
from .business_profile_context import BusinessProfileContextComponent
from .universal_context_injector import UniversalContextInjector

logger = logging.getLogger(__name__)


class SharedContextManager(BaseAgentComponent):
    """
    Shared context manager component that consolidates context management logic
    across all agents with unified conversation history and state management.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("shared_context_manager", config)

        # Configuration
        self.max_history_length = config.get("max_history_length", 50)
        self.context_retention_hours = config.get("context_retention_hours", 24)
        self.enable_cross_persona_sharing = config.get("enable_cross_persona_sharing", True)
        self.enable_persistent_storage = config.get("enable_persistent_storage", True)
        self.enable_business_profile_context = config.get("enable_business_profile_context", True)
        self.enable_universal_context_injection = config.get("enable_universal_context_injection", True)

        # In-memory storage for contexts
        self.conversation_contexts = {}
        self.shared_contexts = {}
        self.user_sessions = {}

        # Context components
        if self.enable_business_profile_context:
            self.business_profile_context = BusinessProfileContextComponent(config.get("business_profile_config", {}))

        if self.enable_universal_context_injection:
            self.universal_context_injector = UniversalContextInjector(config.get("universal_context_config", {}))

        # Business profile context component
        if self.enable_business_profile_context:
            self.business_profile_context = BusinessProfileContextComponent(config.get("business_profile_config", {}))
    
    async def _initialize_component(self) -> None:
        """Initialize the context manager component."""
        self.logger.info("Initializing SharedContextManager")

        # Initialize context components
        if self.enable_business_profile_context:
            try:
                await self.business_profile_context.initialize()
                self.logger.info("Business profile context component initialized")
            except Exception as e:
                self.logger.warning(f"Failed to initialize business profile context: {e}")

        if self.enable_universal_context_injection:
            try:
                await self.universal_context_injector.initialize()
                self.logger.info("Universal context injector initialized")
            except Exception as e:
                self.logger.warning(f"Failed to initialize universal context injector: {e}")

        # Initialize any persistent storage connections if needed
        if self.enable_persistent_storage:
            try:
                # This would initialize database connections for persistent context storage
                self.logger.info("Persistent context storage initialized")
            except Exception as e:
                self.logger.warning(f"Failed to initialize persistent storage: {e}")
    
    def get_required_fields(self) -> List[str]:
        """Return list of required context fields."""
        return ["user_id"]
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process context management request.
        
        Args:
            context: AgentContext containing context management parameters
            
        Returns:
            Updated AgentContext with managed context information
        """
        user_id = context.user_id
        operation = context.get_field("operation", "get_context")
        
        self.logger.info(f"Processing context management operation: {operation} for user: {user_id}")
        
        if operation == "get_context":
            context = await self._get_conversation_context(context)

            # Load business profile context if enabled
            if self.enable_business_profile_context:
                context = await self.business_profile_context.process(context)

            # Inject universal cross-agent context if enabled
            if self.enable_universal_context_injection:
                context.set_field("context_operation", "inject_context")
                context = await self.universal_context_injector.process(context)

            return context
        elif operation == "update_context":
            return await self._update_conversation_context(context)
        elif operation == "share_context":
            return await self._share_context_across_personas(context)
        elif operation == "cleanup_context":
            return await self._cleanup_expired_contexts(context)
        elif operation == "track_interaction":
            # Track agent interaction for cross-agent learning
            if self.enable_universal_context_injection:
                context.set_field("context_operation", "track_interaction")
                return await self.universal_context_injector.process(context)
            return context
        elif operation == "generate_insight":
            # Generate insight from agent interaction
            if self.enable_universal_context_injection:
                context.set_field("context_operation", "generate_insight")
                return await self.universal_context_injector.process(context)
            return context
        elif operation == "get_collaboration_context":
            # Get collaboration opportunities
            if self.enable_universal_context_injection:
                context.set_field("context_operation", "get_collaboration_context")
                return await self.universal_context_injector.process(context)
            return context
        else:
            context.add_error(self.name, f"Unknown operation: {operation}")
            context.set_status("error")
            return context
    
    async def _get_conversation_context(self, context: AgentContext) -> AgentContext:
        """
        Get conversation context for a user.
        
        Args:
            context: AgentContext containing user and conversation information
            
        Returns:
            Updated AgentContext with conversation history and state
        """
        user_id = context.user_id
        conversation_id = context.get_field("conversation_id", f"default_{user_id}")
        persona_id = context.get_field("persona_id", "default")
        
        # Get or create conversation context
        context_key = f"{user_id}_{conversation_id}_{persona_id}"
        
        if context_key not in self.conversation_contexts:
            self.conversation_contexts[context_key] = {
                "user_id": user_id,
                "conversation_id": conversation_id,
                "persona_id": persona_id,
                "messages": [],
                "entities": {},
                "preferences": {},
                "session_data": {},
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        
        conversation_context = self.conversation_contexts[context_key]
        
        # Update context with conversation information
        context.set_field("conversation_history", conversation_context["messages"])
        context.set_field("extracted_entities", conversation_context["entities"])
        context.set_field("user_preferences", conversation_context["preferences"])
        context.set_field("session_data", conversation_context["session_data"])
        context.set_field("context_created_at", conversation_context["created_at"])
        context.set_field("context_updated_at", conversation_context["updated_at"])
        
        context.set_status("success")
        self.logger.info(f"Retrieved conversation context for {context_key}")
        
        return context
    
    async def _update_conversation_context(self, context: AgentContext) -> AgentContext:
        """
        Update conversation context with new information.
        
        Args:
            context: AgentContext containing updates to apply
            
        Returns:
            Updated AgentContext confirming the update
        """
        user_id = context.user_id
        conversation_id = context.get_field("conversation_id", f"default_{user_id}")
        persona_id = context.get_field("persona_id", "default")
        
        context_key = f"{user_id}_{conversation_id}_{persona_id}"
        
        # Ensure context exists
        if context_key not in self.conversation_contexts:
            await self._get_conversation_context(context)
        
        conversation_context = self.conversation_contexts[context_key]
        
        # Update messages
        new_message = context.get_field("new_message")
        if new_message:
            conversation_context["messages"].append({
                "content": new_message,
                "timestamp": datetime.now().isoformat(),
                "role": context.get_field("message_role", "user")
            })
            
            # Trim history if too long
            if len(conversation_context["messages"]) > self.max_history_length:
                conversation_context["messages"] = conversation_context["messages"][-self.max_history_length:]
        
        # Update entities
        new_entities = context.get_field("new_entities", {})
        if new_entities:
            conversation_context["entities"].update(new_entities)
        
        # Update preferences
        new_preferences = context.get_field("new_preferences", {})
        if new_preferences:
            conversation_context["preferences"].update(new_preferences)
        
        # Update session data
        new_session_data = context.get_field("new_session_data", {})
        if new_session_data:
            conversation_context["session_data"].update(new_session_data)
        
        # Update timestamp
        conversation_context["updated_at"] = datetime.now().isoformat()
        
        context.set_field("context_updated", True)
        context.set_status("success")
        
        self.logger.info(f"Updated conversation context for {context_key}")
        
        return context
    
    async def _share_context_across_personas(self, context: AgentContext) -> AgentContext:
        """
        Share context information across different personas.
        
        Args:
            context: AgentContext containing sharing parameters
            
        Returns:
            Updated AgentContext with sharing results
        """
        if not self.enable_cross_persona_sharing:
            context.add_error(self.name, "Cross-persona sharing is disabled")
            context.set_status("error")
            return context
        
        user_id = context.user_id
        source_persona = context.get_field("source_persona")
        target_persona = context.get_field("target_persona")
        shared_data = context.get_field("shared_data", {})
        
        if not source_persona or not target_persona:
            context.add_error(self.name, "Source and target personas must be specified")
            context.set_status("error")
            return context
        
        # Create shared context key
        shared_key = f"{user_id}_shared_{source_persona}_to_{target_persona}"
        
        # Store shared context
        self.shared_contexts[shared_key] = {
            "user_id": user_id,
            "source_persona": source_persona,
            "target_persona": target_persona,
            "shared_data": shared_data,
            "created_at": datetime.now().isoformat(),
            "expires_at": (datetime.now() + timedelta(hours=self.context_retention_hours)).isoformat()
        }
        
        context.set_field("sharing_completed", True)
        context.set_field("shared_context_key", shared_key)
        context.set_status("success")
        
        self.logger.info(f"Shared context from {source_persona} to {target_persona} for user {user_id}")
        
        return context
    
    async def _cleanup_expired_contexts(self, context: AgentContext) -> AgentContext:
        """
        Clean up expired contexts to manage memory usage.
        
        Args:
            context: AgentContext for cleanup operation
            
        Returns:
            Updated AgentContext with cleanup results
        """
        current_time = datetime.now()
        cleanup_count = 0
        
        # Clean up expired shared contexts
        expired_shared_keys = []
        for key, shared_context in self.shared_contexts.items():
            expires_at = datetime.fromisoformat(shared_context["expires_at"])
            if current_time > expires_at:
                expired_shared_keys.append(key)
        
        for key in expired_shared_keys:
            del self.shared_contexts[key]
            cleanup_count += 1
        
        # Clean up old conversation contexts (optional, based on configuration)
        if context.get_field("cleanup_conversations", False):
            expired_conversation_keys = []
            cutoff_time = current_time - timedelta(hours=self.context_retention_hours)
            
            for key, conv_context in self.conversation_contexts.items():
                updated_at = datetime.fromisoformat(conv_context["updated_at"])
                if updated_at < cutoff_time:
                    expired_conversation_keys.append(key)
            
            for key in expired_conversation_keys:
                del self.conversation_contexts[key]
                cleanup_count += 1
        
        context.set_field("contexts_cleaned", cleanup_count)
        context.set_status("success")
        
        self.logger.info(f"Cleaned up {cleanup_count} expired contexts")
        
        return context
    
    def get_context_statistics(self) -> Dict[str, Any]:
        """Get context management statistics."""
        return {
            "active_conversations": len(self.conversation_contexts),
            "shared_contexts": len(self.shared_contexts),
            "total_operations": self.metrics.success_count + self.metrics.error_count,
            "success_rate": self.metrics.get_success_rate(),
            "cross_persona_sharing_enabled": self.enable_cross_persona_sharing,
            "persistent_storage_enabled": self.enable_persistent_storage
        }
    
    async def get_shared_context(self, user_id: str, source_persona: str, target_persona: str) -> Optional[Dict[str, Any]]:
        """
        Get shared context between personas.
        
        Args:
            user_id: User identifier
            source_persona: Source persona identifier
            target_persona: Target persona identifier
            
        Returns:
            Shared context data or None if not found
        """
        shared_key = f"{user_id}_shared_{source_persona}_to_{target_persona}"
        
        if shared_key in self.shared_contexts:
            shared_context = self.shared_contexts[shared_key]
            
            # Check if expired
            expires_at = datetime.fromisoformat(shared_context["expires_at"])
            if datetime.now() > expires_at:
                del self.shared_contexts[shared_key]
                return None
            
            return shared_context["shared_data"]
        
        return None
