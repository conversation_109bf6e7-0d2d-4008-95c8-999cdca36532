"""Add dashboard system tables

Revision ID: fb59f25d232a
Revises: 850556735855
Create Date: 2025-06-23 13:55:46.286610

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fb59f25d232a'
down_revision = '850556735855'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dashboards',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('is_public', sa.<PERSON>(), nullable=True),
    sa.Column('layout_config', sa.<PERSON>(), nullable=True),
    sa.Column('theme_config', sa.JSO<PERSON>(), nullable=True),
    sa.Column('refresh_interval', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'is_default', name='unique_user_default_dashboard')
    )
    op.create_index(op.f('ix_dashboards_id'), 'dashboards', ['id'], unique=False)
    op.create_index(op.f('ix_dashboards_user_id'), 'dashboards', ['user_id'], unique=False)
    op.create_table('dashboard_data_sources',
    sa.Column('dashboard_id', sa.String(length=36), nullable=False),
    sa.Column('data_source_id', sa.String(length=36), nullable=False),
    sa.Column('alias', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['dashboard_id'], ['dashboards.id'], ),
    sa.ForeignKeyConstraint(['data_source_id'], ['data_sources.id'], ),
    sa.PrimaryKeyConstraint('dashboard_id', 'data_source_id')
    )
    op.add_column('dashboard_sections', sa.Column('dashboard_id', sa.String(length=36), nullable=False))
    op.create_index(op.f('ix_dashboard_sections_dashboard_id'), 'dashboard_sections', ['dashboard_id'], unique=False)
    op.create_foreign_key(None, 'dashboard_sections', 'dashboards', ['dashboard_id'], ['id'])
    op.add_column('data_sources', sa.Column('sync_status', sa.String(length=50), nullable=True))
    op.add_column('data_sources', sa.Column('last_sync', sa.DateTime(timezone=True), nullable=True))
    op.drop_index('ix_pricing_tiers_name', table_name='pricing_tiers')
    op.create_unique_constraint(None, 'pricing_tiers', ['name'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'pricing_tiers', type_='unique')
    op.create_index('ix_pricing_tiers_name', 'pricing_tiers', ['name'], unique=True)
    op.drop_column('data_sources', 'last_sync')
    op.drop_column('data_sources', 'sync_status')
    op.drop_constraint(None, 'dashboard_sections', type_='foreignkey')
    op.drop_index(op.f('ix_dashboard_sections_dashboard_id'), table_name='dashboard_sections')
    op.drop_column('dashboard_sections', 'dashboard_id')
    op.drop_table('dashboard_data_sources')
    op.drop_index(op.f('ix_dashboards_user_id'), table_name='dashboards')
    op.drop_index(op.f('ix_dashboards_id'), table_name='dashboards')
    op.drop_table('dashboards')
    # ### end Alembic commands ###
