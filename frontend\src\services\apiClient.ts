import axios from 'axios';

// Determine the base URL for the API
// In a real application, this would come from an environment variable
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'; // Default to local backend

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor to add JWT token to requests if available
apiClient.interceptors.request.use(
  (config) => {
    // In a real app, you'd get the token from localStorage, Zustand, Redux, etc.
    const token = localStorage.getItem('accessToken'); 
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Optional: Interceptor to handle token refresh or global error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    // Example: Token refresh logic
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        // const refreshToken = localStorage.getItem('refreshToken');
        // const { data } = await axios.post(`${API_BASE_URL}/auth/refresh-token`, { refreshToken });
        // localStorage.setItem('accessToken', data.access_token);
        // apiClient.defaults.headers.common['Authorization'] = `Bearer ${data.access_token}`;
        // return apiClient(originalRequest);
        console.warn("Simulated token refresh needed or redirect to login.");
        // For now, just re-throw or redirect to login
        // window.location.href = '/login'; 
      } catch (refreshError) {
        console.error("Token refresh failed:", refreshError);
        // Logout user or redirect to login
        // localStorage.removeItem('accessToken');
        // localStorage.removeItem('refreshToken');
        // window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    return Promise.reject(error);
  }
);

export default apiClient;
