# Enhanced Features Configuration for Datagenius
# Copy these settings to your .env file to enable enhanced marketing agent capabilities

# =============================================================================
# AI INTENT DETECTION
# =============================================================================
ENABLE_AI_INTENT_DETECTION=true
INTENT_CONFIDENCE_THRESHOLD=0.7
INTENT_CACHE_TTL=300

# =============================================================================
# ERROR HANDLING
# =============================================================================
ENABLE_ENHANCED_ERROR_HANDLING=true
USER_FRIENDLY_ERRORS=true
ERROR_TRACKING=true

# =============================================================================
# ANALYTICS
# =============================================================================
ENABLE_ANALYTICS=true
ANALYTICS_BACKEND=memory
TRACK_USER_INTERACTIONS=true
TRACK_PERFORMANCE_METRICS=true
ANALYTICS_RETENTION_DAYS=30
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=60

# =============================================================================
# INTELLIGENT CACHING
# =============================================================================
ENABLE_INTELLIGENT_CACHING=true
CACHE_BACKEND=memory
CACHE_TTL=3600
MAX_CACHE_SIZE=1000
CACHE_COMPRESSION=true
CACHE_KEY_PREFIX=datagenius:cache:

# =============================================================================
# PERFORMANCE MONITORING
# =============================================================================
ENABLE_PERFORMANCE_MONITORING=true
MONITORING_INTERVAL=60
HEALTH_CHECK_ENABLED=true
METRICS_RETENTION_HOURS=24

# Alert thresholds
ALERT_RESPONSE_TIME_MS=5000
ALERT_ERROR_RATE_PERCENT=5.0
ALERT_MEMORY_USAGE_PERCENT=80.0

# =============================================================================
# QUICK ACTION BUTTONS
# =============================================================================
ENABLE_QUICK_ACTION_BUTTONS=true

# =============================================================================
# CONVERSATIONAL FEATURES
# =============================================================================
ENABLE_CONVERSATIONAL_FOLLOW_UPS=true
CONTEXT_PRESERVATION=true
CONVERSATION_MEMORY_LIMIT=10

# =============================================================================
# REDIS CONFIGURATION (for advanced caching and analytics)
# =============================================================================
# Uncomment and configure these if you want to use Redis instead of memory
# CACHE_BACKEND=redis
# ANALYTICS_BACKEND=redis
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_DB=0
# REDIS_PASSWORD=

# =============================================================================
# ADVANCED FEATURES
# =============================================================================
# Enable these for production environments

# Database analytics (requires PostgreSQL)
# ANALYTICS_BACKEND=database

# Distributed caching
# CACHE_BACKEND=redis

# Advanced monitoring
# ENABLE_DISTRIBUTED_TRACING=true
# JAEGER_ENDPOINT=http://localhost:14268/api/traces

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Individual feature toggles for fine-grained control

# Intent Detection Features
ENABLE_CONTEXT_AWARE_INTENT=true
ENABLE_INTENT_CONFIDENCE_SCORING=true
ENABLE_INTENT_LEARNING=true

# Error Handling Features
ENABLE_ERROR_CATEGORIZATION=true
ENABLE_ERROR_SUGGESTIONS=true
ENABLE_ERROR_RECOVERY=true

# Analytics Features
ENABLE_USER_JOURNEY_TRACKING=true
ENABLE_PERFORMANCE_ANALYTICS=true
ENABLE_CONVERSATION_ANALYTICS=true
ENABLE_AGENT_PERFORMANCE_METRICS=true

# Caching Features
ENABLE_RESPONSE_CACHING=true
ENABLE_INTENT_CACHING=true
ENABLE_CONTEXT_CACHING=true

# UI Features
ENABLE_TYPING_INDICATORS=true
ENABLE_MESSAGE_REACTIONS=true
ENABLE_CONVERSATION_SUMMARIES=true
ENABLE_SMART_SUGGESTIONS=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# These settings are useful for development and testing

# Debug modes
DEBUG_INTENT_DETECTION=false
DEBUG_ERROR_HANDLING=false
DEBUG_ANALYTICS=false
DEBUG_CACHING=false

# Testing features
ENABLE_MOCK_RESPONSES=false
ENABLE_PERFORMANCE_SIMULATION=false
ENABLE_ERROR_SIMULATION=false

# Logging levels
INTENT_DETECTION_LOG_LEVEL=INFO
ERROR_HANDLING_LOG_LEVEL=INFO
ANALYTICS_LOG_LEVEL=INFO
CACHING_LOG_LEVEL=INFO

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# Security configurations for enhanced features

# Rate limiting
ENABLE_RATE_LIMITING=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST_SIZE=10

# Data privacy
ENABLE_DATA_ANONYMIZATION=true
ENABLE_PII_DETECTION=true
DATA_RETENTION_DAYS=90

# API security
ENABLE_API_KEY_ROTATION=true
API_KEY_ROTATION_DAYS=30

# =============================================================================
# INTEGRATION SETTINGS
# =============================================================================
# Settings for integrating with external services

# Webhook configurations
ENABLE_WEBHOOKS=false
WEBHOOK_SECRET_KEY=your-webhook-secret-key
WEBHOOK_TIMEOUT_SECONDS=30

# External analytics
ENABLE_EXTERNAL_ANALYTICS=false
GOOGLE_ANALYTICS_ID=
MIXPANEL_TOKEN=

# Notification services
ENABLE_SLACK_NOTIFICATIONS=false
SLACK_WEBHOOK_URL=
ENABLE_EMAIL_NOTIFICATIONS=false
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Advanced performance settings

# Thread pool sizes
INTENT_DETECTION_WORKERS=4
ANALYTICS_WORKERS=2
CACHE_WORKERS=2

# Memory limits
MAX_MEMORY_USAGE_MB=1024
MAX_CACHE_MEMORY_MB=256
MAX_ANALYTICS_MEMORY_MB=128

# Timeout settings
INTENT_DETECTION_TIMEOUT_SECONDS=10
CACHE_OPERATION_TIMEOUT_SECONDS=5
ANALYTICS_FLUSH_TIMEOUT_SECONDS=30

# Batch processing
ANALYTICS_BATCH_SIZE=100
CACHE_BATCH_SIZE=50
ERROR_BATCH_SIZE=25

# =============================================================================
# MONITORING AND OBSERVABILITY
# =============================================================================
# Settings for monitoring and observability

# Metrics collection
ENABLE_PROMETHEUS_METRICS=false
PROMETHEUS_PORT=9090

# Health checks
HEALTH_CHECK_INTERVAL_SECONDS=30
HEALTH_CHECK_TIMEOUT_SECONDS=10

# Logging
LOG_FORMAT=json
LOG_LEVEL=INFO
ENABLE_STRUCTURED_LOGGING=true

# Tracing
ENABLE_REQUEST_TRACING=true
TRACE_SAMPLING_RATE=0.1

# =============================================================================
# EXPERIMENTAL FEATURES
# =============================================================================
# Experimental features (use with caution in production)

ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_AI_RESPONSE_OPTIMIZATION=false
ENABLE_PREDICTIVE_CACHING=false
ENABLE_ADAPTIVE_INTENT_DETECTION=false
ENABLE_CONVERSATION_CLUSTERING=false
