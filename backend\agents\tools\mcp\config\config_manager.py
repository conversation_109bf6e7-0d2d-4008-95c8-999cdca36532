"""
Dynamic Configuration Manager for MCP Tools.

This module provides a comprehensive configuration management system for MCP tools,
supporting YAML-based configuration, agent-specific customizations, validation,
and runtime configuration updates.
"""

import logging
import yaml
import os
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from pydantic import BaseModel, Field, ValidationError, field_validator
from dataclasses import dataclass
import threading
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class GlobalConfig(BaseModel):
    """Global configuration settings for all MCP tools."""
    
    default_provider: str = Field(default="groq", description="Default AI provider")
    default_model: str = Field(default="llama-3.1-8b-instant", description="Default AI model")
    default_timeout: int = Field(default=30, ge=5, le=300, description="Default timeout in seconds")
    max_timeout: int = Field(default=120, ge=10, le=600, description="Maximum timeout in seconds")
    default_retries: int = Field(default=3, ge=1, le=10, description="Default retry attempts")
    max_retries: int = Field(default=5, ge=1, le=10, description="Maximum retry attempts")
    enable_caching: bool = Field(default=True, description="Enable result caching")
    default_cache_ttl: int = Field(default=300, ge=60, le=7200, description="Default cache TTL in seconds")
    max_cache_ttl: int = Field(default=3600, ge=300, le=86400, description="Maximum cache TTL in seconds")
    enable_input_validation: bool = Field(default=True, description="Enable input validation")
    enable_input_sanitization: bool = Field(default=True, description="Enable input sanitization")
    enable_performance_monitoring: bool = Field(default=True, description="Enable performance monitoring")
    enable_usage_analytics: bool = Field(default=True, description="Enable usage analytics")
    enable_rate_limiting: bool = Field(default=True, description="Enable rate limiting")
    enable_access_control: bool = Field(default=True, description="Enable access control")
    enable_audit_logging: bool = Field(default=True, description="Enable audit logging")


class AgentConfig(BaseModel):
    """Agent-specific configuration settings."""
    
    preferred_providers: List[str] = Field(default=["groq", "openai"], description="Preferred AI providers")
    preferred_models: List[str] = Field(default=["llama-3.1-8b-instant"], description="Preferred AI models")
    timeout_multiplier: float = Field(default=1.0, ge=0.1, le=5.0, description="Timeout multiplier")
    cache_ttl_multiplier: float = Field(default=1.0, ge=0.1, le=5.0, description="Cache TTL multiplier")
    analysis_depth: str = Field(default="moderate", description="Analysis depth level")
    explanation_level: str = Field(default="detailed", description="Explanation level")
    
    @field_validator('analysis_depth')
    @classmethod
    def validate_analysis_depth(cls, v):
        allowed_values = ["basic", "moderate", "advanced"]
        if v not in allowed_values:
            raise ValueError(f"analysis_depth must be one of {allowed_values}")
        return v

    @field_validator('explanation_level')
    @classmethod
    def validate_explanation_level(cls, v):
        allowed_values = ["accessible", "detailed", "business_focused", "structured", "educational"]
        if v not in allowed_values:
            raise ValueError(f"explanation_level must be one of {allowed_values}")
        return v


class ToolConfig(BaseModel):
    """Tool-specific configuration settings."""
    
    enable_agent_awareness: bool = Field(default=True, description="Enable agent awareness")
    timeout: Optional[int] = Field(default=None, ge=5, le=600, description="Tool-specific timeout")
    max_retries: Optional[int] = Field(default=None, ge=1, le=10, description="Tool-specific max retries")
    enable_caching: Optional[bool] = Field(default=None, description="Tool-specific caching setting")
    cache_ttl: Optional[int] = Field(default=None, ge=60, le=7200, description="Tool-specific cache TTL")
    custom_settings: Dict[str, Any] = Field(default_factory=dict, description="Custom tool settings")


class ValidationConfig(BaseModel):
    """Validation configuration settings."""
    
    required_fields: List[str] = Field(default_factory=list, description="Required fields")
    field_types: Dict[str, str] = Field(default_factory=dict, description="Field type definitions")
    field_constraints: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Field constraints")


class ErrorHandlingConfig(BaseModel):
    """Error handling configuration settings."""
    
    enable_graceful_degradation: bool = Field(default=True, description="Enable graceful degradation")
    enable_retry_logic: bool = Field(default=True, description="Enable retry logic")
    enable_fallback_providers: bool = Field(default=True, description="Enable fallback providers")
    retry_strategies: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Retry strategies")
    fallback_chains: Dict[str, List[str]] = Field(default_factory=dict, description="Fallback chains")
    error_categories: Dict[str, List[str]] = Field(default_factory=dict, description="Error categories")


class MonitoringConfig(BaseModel):
    """Performance monitoring configuration settings."""
    
    enable_metrics_collection: bool = Field(default=True, description="Enable metrics collection")
    enable_performance_tracking: bool = Field(default=True, description="Enable performance tracking")
    enable_usage_analytics: bool = Field(default=True, description="Enable usage analytics")
    metrics: List[str] = Field(default_factory=list, description="Metrics to collect")
    thresholds: Dict[str, Union[int, float]] = Field(default_factory=dict, description="Performance thresholds")


class SecurityConfig(BaseModel):
    """Security configuration settings."""
    
    enable_input_sanitization: bool = Field(default=True, description="Enable input sanitization")
    enable_output_filtering: bool = Field(default=True, description="Enable output filtering")
    enable_rate_limiting: bool = Field(default=True, description="Enable rate limiting")
    rate_limits: Dict[str, int] = Field(default_factory=dict, description="Rate limit settings")
    sanitization_rules: Dict[str, bool] = Field(default_factory=dict, description="Sanitization rules")
    access_control: Dict[str, bool] = Field(default_factory=dict, description="Access control settings")


class LoggingConfig(BaseModel):
    """Logging configuration settings."""
    
    level: str = Field(default="INFO", description="Logging level")
    enable_structured_logging: bool = Field(default=True, description="Enable structured logging")
    enable_audit_trail: bool = Field(default=True, description="Enable audit trail")
    log_categories: List[str] = Field(default_factory=list, description="Log categories")
    retention_policy: Dict[str, str] = Field(default_factory=dict, description="Log retention policy")


class MCPToolsConfig(BaseModel):
    """Complete MCP tools configuration."""
    
    global_config: GlobalConfig = Field(default_factory=GlobalConfig, alias="global")
    agents: Dict[str, AgentConfig] = Field(default_factory=dict, description="Agent configurations")
    tools: Dict[str, ToolConfig] = Field(default_factory=dict, description="Tool configurations")
    validation: ValidationConfig = Field(default_factory=ValidationConfig, description="Validation configuration")
    error_handling: ErrorHandlingConfig = Field(default_factory=ErrorHandlingConfig, description="Error handling configuration")
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig, description="Monitoring configuration")
    security: SecurityConfig = Field(default_factory=SecurityConfig, description="Security configuration")
    logging: LoggingConfig = Field(default_factory=LoggingConfig, description="Logging configuration")
    
    class Config:
        allow_population_by_field_name = True


@dataclass
class ConfigCache:
    """Configuration cache with TTL support."""
    
    config: MCPToolsConfig
    loaded_at: datetime
    ttl: timedelta
    
    def is_expired(self) -> bool:
        """Check if the cached configuration is expired."""
        return datetime.now() > self.loaded_at + self.ttl


class ConfigManager:
    """
    Dynamic configuration manager for MCP tools.
    
    Provides centralized configuration management with support for:
    - YAML-based configuration files
    - Agent-specific customizations
    - Runtime configuration updates
    - Configuration validation
    - Caching with TTL
    - Thread-safe operations
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """Singleton pattern implementation."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the configuration manager."""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._config_cache: Optional[ConfigCache] = None
        self._config_lock = threading.RLock()
        self._config_file_path = Path(__file__).parent / "tool_config.yaml"
        self._cache_ttl = timedelta(minutes=5)  # 5-minute cache TTL
        
        logger.info("ConfigManager initialized")
    
    def load_config(self, config_path: Optional[Union[str, Path]] = None, force_reload: bool = False) -> MCPToolsConfig:
        """
        Load configuration from YAML file with caching support.
        
        Args:
            config_path: Path to configuration file (optional)
            force_reload: Force reload even if cache is valid
            
        Returns:
            MCPToolsConfig: Loaded and validated configuration
        """
        with self._config_lock:
            # Use cached config if available and not expired
            if not force_reload and self._config_cache and not self._config_cache.is_expired():
                logger.debug("Using cached configuration")
                return self._config_cache.config
            
            # Determine config file path
            if config_path:
                config_file = Path(config_path)
            else:
                config_file = self._config_file_path
            
            try:
                # Load YAML configuration
                if not config_file.exists():
                    logger.warning(f"Configuration file not found: {config_file}. Using default configuration.")
                    config_data = {}
                else:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f) or {}
                    logger.info(f"Loaded configuration from: {config_file}")
                
                # Validate and create configuration object
                config = MCPToolsConfig(**config_data)
                
                # Cache the configuration
                self._config_cache = ConfigCache(
                    config=config,
                    loaded_at=datetime.now(),
                    ttl=self._cache_ttl
                )
                
                logger.info("Configuration loaded and cached successfully")
                return config
                
            except ValidationError as e:
                logger.error(f"Configuration validation error: {e}")
                raise
            except Exception as e:
                logger.error(f"Failed to load configuration: {e}")
                # Return default configuration on error
                return MCPToolsConfig()
    
    def get_global_config(self) -> GlobalConfig:
        """Get global configuration settings."""
        config = self.load_config()
        return config.global_config
    
    def get_agent_config(self, agent_identity: str) -> AgentConfig:
        """Get configuration for a specific agent."""
        config = self.load_config()
        return config.agents.get(agent_identity, AgentConfig())
    
    def get_tool_config(self, tool_name: str) -> ToolConfig:
        """Get configuration for a specific tool."""
        config = self.load_config()
        return config.tools.get(tool_name, ToolConfig())
    
    def get_validation_config(self) -> ValidationConfig:
        """Get validation configuration."""
        config = self.load_config()
        return config.validation
    
    def get_error_handling_config(self) -> ErrorHandlingConfig:
        """Get error handling configuration."""
        config = self.load_config()
        return config.error_handling
    
    def get_monitoring_config(self) -> MonitoringConfig:
        """Get monitoring configuration."""
        config = self.load_config()
        return config.monitoring
    
    def get_security_config(self) -> SecurityConfig:
        """Get security configuration."""
        config = self.load_config()
        return config.security
    
    def get_logging_config(self) -> LoggingConfig:
        """Get logging configuration."""
        config = self.load_config()
        return config.logging
    
    def reload_config(self) -> MCPToolsConfig:
        """Force reload configuration from file."""
        return self.load_config(force_reload=True)
    
    def clear_cache(self):
        """Clear the configuration cache."""
        with self._config_lock:
            self._config_cache = None
            logger.info("Configuration cache cleared")


# Global configuration manager instance
config_manager = ConfigManager()


def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance."""
    return config_manager
