"""
Time Series Analysis Module for MCP Tools.

This module provides comprehensive time series analysis capabilities including
trend analysis, seasonality detection, forecasting, and anomaly detection
for temporal data analysis.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import warnings

logger = logging.getLogger(__name__)


class TimeSeriesMethod(Enum):
    """Time series analysis methods."""
    TREND_ANALYSIS = "trend_analysis"
    SEASONALITY_DETECTION = "seasonality_detection"
    DECOMPOSITION = "decomposition"
    FORECASTING = "forecasting"
    ANOMALY_DETECTION = "anomaly_detection"
    STATIONARITY_TEST = "stationarity_test"
    AUTOCORRELATION = "autocorrelation"
    CHANGE_POINT_DETECTION = "change_point_detection"


class ForecastingModel(Enum):
    """Available forecasting models."""
    SIMPLE_EXPONENTIAL_SMOOTHING = "simple_exponential_smoothing"
    DOUBLE_EXPONENTIAL_SMOOTHING = "double_exponential_smoothing"
    TRIPLE_EXPONENTIAL_SMOOTHING = "triple_exponential_smoothing"
    ARIMA = "arima"
    SEASONAL_ARIMA = "seasonal_arima"
    LINEAR_TREND = "linear_trend"
    POLYNOMIAL_TREND = "polynomial_trend"
    MOVING_AVERAGE = "moving_average"


@dataclass
class TimeSeriesResult:
    """Container for time series analysis results."""
    method: TimeSeriesMethod
    original_data: pd.Series
    results: Dict[str, Any]
    forecast: Optional[pd.Series] = None
    confidence_intervals: Optional[Tuple[pd.Series, pd.Series]] = None
    decomposition: Optional[Dict[str, pd.Series]] = None
    anomalies: Optional[pd.Series] = None
    statistics: Dict[str, float] = None
    interpretation: str = ""
    recommendations: List[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []
        if self.statistics is None:
            self.statistics = {}
        if self.metadata is None:
            self.metadata = {}


class TimeSeriesAnalysis:
    """
    Comprehensive time series analysis with forecasting and anomaly detection.
    
    Features:
    - Trend and seasonality analysis
    - Multiple forecasting models
    - Anomaly detection in time series
    - Stationarity testing
    - Decomposition analysis
    - Change point detection
    - Agent-aware result formatting
    """
    
    def __init__(self):
        """Initialize the time series analysis module."""
        self.confidence_level = 0.95
        self.default_forecast_periods = 12
        
        # Import time series libraries with fallbacks
        try:
            from scipy import stats
            from scipy.signal import find_peaks
            self.scipy_available = True
            self.stats = stats
            self.find_peaks = find_peaks
        except ImportError:
            logger.warning("SciPy not available - some time series features will be limited")
            self.scipy_available = False
            self.stats = None
            self.find_peaks = None
        
        try:
            import statsmodels.api as sm
            from statsmodels.tsa.seasonal import seasonal_decompose
            from statsmodels.tsa.holtwinters import ExponentialSmoothing
            from statsmodels.tsa.arima.model import ARIMA
            from statsmodels.stats.diagnostic import acorr_ljungbox
            from statsmodels.tsa.stattools import adfuller, kpss
            
            self.statsmodels_available = True
            self.seasonal_decompose = seasonal_decompose
            self.ExponentialSmoothing = ExponentialSmoothing
            self.ARIMA = ARIMA
            self.acorr_ljungbox = acorr_ljungbox
            self.adfuller = adfuller
            self.kpss = kpss
            
        except ImportError:
            logger.warning("Statsmodels not available - advanced time series features will be limited")
            self.statsmodels_available = False

    def analyze_time_series(self, data: pd.Series, method: TimeSeriesMethod,
                           **kwargs) -> TimeSeriesResult:
        """
        Perform comprehensive time series analysis.
        
        Args:
            data: Time series data (pandas Series with datetime index)
            method: Analysis method to apply
            **kwargs: Additional parameters for specific methods
            
        Returns:
            TimeSeriesResult with comprehensive analysis results
        """
        # Validate input data
        if not isinstance(data.index, pd.DatetimeIndex):
            try:
                data.index = pd.to_datetime(data.index)
            except:
                raise ValueError("Data must have datetime index or convertible index")
        
        # Remove missing values
        data_clean = data.dropna()
        
        if len(data_clean) < 3:
            raise ValueError("Insufficient data for time series analysis (need at least 3 observations)")
        
        # Route to appropriate analysis method
        if method == TimeSeriesMethod.TREND_ANALYSIS:
            return self._analyze_trend(data_clean, **kwargs)
        elif method == TimeSeriesMethod.SEASONALITY_DETECTION:
            return self._detect_seasonality(data_clean, **kwargs)
        elif method == TimeSeriesMethod.DECOMPOSITION:
            return self._decompose_series(data_clean, **kwargs)
        elif method == TimeSeriesMethod.FORECASTING:
            return self._forecast_series(data_clean, **kwargs)
        elif method == TimeSeriesMethod.ANOMALY_DETECTION:
            return self._detect_anomalies(data_clean, **kwargs)
        elif method == TimeSeriesMethod.STATIONARITY_TEST:
            return self._test_stationarity(data_clean, **kwargs)
        elif method == TimeSeriesMethod.AUTOCORRELATION:
            return self._analyze_autocorrelation(data_clean, **kwargs)
        elif method == TimeSeriesMethod.CHANGE_POINT_DETECTION:
            return self._detect_change_points(data_clean, **kwargs)
        else:
            raise ValueError(f"Unsupported time series method: {method}")

    def _analyze_trend(self, data: pd.Series, **kwargs) -> TimeSeriesResult:
        """Analyze trend in time series data."""
        # Simple trend analysis using linear regression
        x = np.arange(len(data))
        y = data.values
        
        # Linear trend
        if self.scipy_available:
            slope, intercept, r_value, p_value, std_err = self.stats.linregress(x, y)
            trend_line = slope * x + intercept
            
            # Trend strength
            trend_strength = abs(r_value)
            
            # Trend direction
            if slope > 0:
                trend_direction = "increasing"
            elif slope < 0:
                trend_direction = "decreasing"
            else:
                trend_direction = "stable"
            
            # Statistical significance
            is_significant = p_value < 0.05
            
            results = {
                'slope': float(slope),
                'intercept': float(intercept),
                'r_squared': float(r_value ** 2),
                'p_value': float(p_value),
                'trend_strength': float(trend_strength),
                'trend_direction': trend_direction,
                'is_significant': is_significant,
                'trend_line': trend_line
            }
            
            # Generate interpretation
            if is_significant:
                if trend_strength > 0.7:
                    strength_desc = "strong"
                elif trend_strength > 0.4:
                    strength_desc = "moderate"
                else:
                    strength_desc = "weak"
                
                interpretation = f"The time series shows a {strength_desc} {trend_direction} trend " \
                               f"(R² = {r_value**2:.3f}, p = {p_value:.4f})."
            else:
                interpretation = f"No significant trend detected (p = {p_value:.4f}). " \
                               f"The series appears relatively stable over time."
            
        else:
            # Fallback without scipy
            mean_first_half = data[:len(data)//2].mean()
            mean_second_half = data[len(data)//2:].mean()
            
            if mean_second_half > mean_first_half * 1.1:
                trend_direction = "increasing"
            elif mean_second_half < mean_first_half * 0.9:
                trend_direction = "decreasing"
            else:
                trend_direction = "stable"
            
            results = {
                'trend_direction': trend_direction,
                'first_half_mean': float(mean_first_half),
                'second_half_mean': float(mean_second_half)
            }
            
            interpretation = f"Simple trend analysis suggests the series is {trend_direction}."
        
        # Recommendations
        recommendations = []
        if results.get('is_significant', False):
            if trend_direction == "increasing":
                recommendations.append("Consider trend-aware forecasting models")
                recommendations.append("Monitor for potential trend changes or saturation points")
            elif trend_direction == "decreasing":
                recommendations.append("Investigate causes of declining trend")
                recommendations.append("Consider intervention strategies if decline is problematic")
        else:
            recommendations.append("Series appears stationary - suitable for many forecasting methods")
            recommendations.append("Look for seasonal patterns or cyclical behavior")
        
        return TimeSeriesResult(
            method=TimeSeriesMethod.TREND_ANALYSIS,
            original_data=data,
            results=results,
            interpretation=interpretation,
            recommendations=recommendations,
            statistics={
                'data_points': len(data),
                'start_date': str(data.index[0]),
                'end_date': str(data.index[-1]),
                'mean_value': float(data.mean()),
                'std_value': float(data.std())
            }
        )

    def _detect_seasonality(self, data: pd.Series, **kwargs) -> TimeSeriesResult:
        """Detect seasonality in time series data."""
        period = kwargs.get('period', None)
        
        # Auto-detect period if not provided
        if period is None:
            period = self._auto_detect_period(data)
        
        results = {
            'detected_period': period,
            'has_seasonality': False,
            'seasonal_strength': 0.0
        }
        
        if period and period > 1 and len(data) >= 2 * period:
            # Calculate seasonal strength
            if self.statsmodels_available:
                try:
                    decomposition = self.seasonal_decompose(data, model='additive', period=period)
                    
                    # Calculate seasonal strength (variance of seasonal component / variance of residuals)
                    seasonal_var = decomposition.seasonal.var()
                    residual_var = decomposition.resid.dropna().var()
                    
                    if residual_var > 0:
                        seasonal_strength = seasonal_var / (seasonal_var + residual_var)
                        results['seasonal_strength'] = float(seasonal_strength)
                        results['has_seasonality'] = seasonal_strength > 0.1
                    
                    results['seasonal_component'] = decomposition.seasonal
                    
                except Exception as e:
                    logger.warning(f"Seasonal decomposition failed: {e}")
            
            # Simple seasonality test using autocorrelation
            if not results['has_seasonality'] and self.scipy_available:
                # Calculate autocorrelation at seasonal lag
                if len(data) > period:
                    seasonal_autocorr = data.autocorr(lag=period)
                    results['seasonal_autocorrelation'] = float(seasonal_autocorr)
                    results['has_seasonality'] = abs(seasonal_autocorr) > 0.3
        
        # Generate interpretation
        if results['has_seasonality']:
            strength = results.get('seasonal_strength', 0)
            if strength > 0.6:
                strength_desc = "strong"
            elif strength > 0.3:
                strength_desc = "moderate"
            else:
                strength_desc = "weak"
            
            interpretation = f"Detected {strength_desc} seasonality with period {period}. " \
                           f"Seasonal strength: {strength:.3f}."
        else:
            interpretation = "No significant seasonality detected in the time series."
        
        # Recommendations
        recommendations = []
        if results['has_seasonality']:
            recommendations.append("Use seasonal forecasting models (SARIMA, Holt-Winters)")
            recommendations.append("Consider seasonal adjustments for trend analysis")
            recommendations.append("Plan for seasonal variations in business operations")
        else:
            recommendations.append("Non-seasonal models may be appropriate")
            recommendations.append("Look for other cyclical patterns or external factors")
        
        return TimeSeriesResult(
            method=TimeSeriesMethod.SEASONALITY_DETECTION,
            original_data=data,
            results=results,
            interpretation=interpretation,
            recommendations=recommendations,
            statistics={
                'data_points': len(data),
                'detected_period': period,
                'frequency': str(data.index.freq) if data.index.freq else 'Unknown'
            }
        )

    def _auto_detect_period(self, data: pd.Series) -> Optional[int]:
        """Auto-detect seasonal period in time series."""
        if len(data) < 6:
            return None

        # Try common periods based on data frequency
        freq = data.index.freq
        if freq:
            freq_str = str(freq)
            if 'D' in freq_str:  # Daily data
                candidate_periods = [7, 30, 365]  # Weekly, monthly, yearly
            elif 'H' in freq_str:  # Hourly data
                candidate_periods = [24, 168, 8760]  # Daily, weekly, yearly
            elif 'M' in freq_str:  # Monthly data
                candidate_periods = [12]  # Yearly
            else:
                candidate_periods = [4, 12, 52]  # Quarterly, yearly, etc.
        else:
            # Default candidates
            candidate_periods = [4, 7, 12, 24, 52]

        # Test each candidate period
        best_period = None
        best_score = 0

        for period in candidate_periods:
            if len(data) >= 2 * period:
                try:
                    # Calculate autocorrelation at this lag
                    autocorr = abs(data.autocorr(lag=period))
                    if autocorr > best_score:
                        best_score = autocorr
                        best_period = period
                except:
                    continue

        return best_period if best_score > 0.3 else None

    def _detect_anomalies(self, data: pd.Series, **kwargs) -> TimeSeriesResult:
        """Detect anomalies in time series data."""
        method = kwargs.get('method', 'statistical')  # 'statistical', 'isolation_forest'
        threshold = kwargs.get('threshold', 3.0)  # Standard deviations for statistical method

        anomalies = pd.Series(index=data.index, data=False, dtype=bool)
        anomaly_scores = pd.Series(index=data.index, data=0.0)

        if method == 'statistical':
            # Statistical method using rolling statistics
            window = kwargs.get('window', min(30, len(data) // 4))

            if window >= 3:
                # Calculate rolling mean and std
                rolling_mean = data.rolling(window=window, center=True).mean()
                rolling_std = data.rolling(window=window, center=True).std()

                # Calculate z-scores
                z_scores = np.abs((data - rolling_mean) / rolling_std)
                anomaly_scores = z_scores.fillna(0)
                anomalies = z_scores > threshold
            else:
                # Simple global statistics
                mean_val = data.mean()
                std_val = data.std()
                z_scores = np.abs((data - mean_val) / std_val)
                anomaly_scores = z_scores
                anomalies = z_scores > threshold

        elif method == 'isolation_forest':
            try:
                from sklearn.ensemble import IsolationForest

                # Prepare features (value, time-based features)
                features = self._create_time_features(data)

                iso_forest = IsolationForest(contamination=kwargs.get('contamination', 0.1),
                                           random_state=42)
                anomaly_labels = iso_forest.fit_predict(features)
                anomalies = pd.Series(anomaly_labels == -1, index=data.index)
                anomaly_scores = pd.Series(-iso_forest.score_samples(features), index=data.index)

            except ImportError:
                logger.warning("Scikit-learn not available, falling back to statistical method")
                return self._detect_anomalies(data, method='statistical', **kwargs)

        # Calculate results
        n_anomalies = anomalies.sum()
        anomaly_rate = n_anomalies / len(data)

        results = {
            'method': method,
            'n_anomalies': int(n_anomalies),
            'anomaly_rate': float(anomaly_rate),
            'threshold': threshold if method == 'statistical' else None
        }

        # Generate interpretation
        if n_anomalies > 0:
            interpretation = f"Detected {n_anomalies} anomalies ({anomaly_rate:.1%} of data) using {method} method."
            if anomaly_rate > 0.1:
                interpretation += " High anomaly rate may indicate data quality issues or significant events."
            else:
                interpretation += " Anomaly rate is within normal range."
        else:
            interpretation = f"No anomalies detected using {method} method with current parameters."

        # Recommendations
        recommendations = []
        if n_anomalies > 0:
            recommendations.append("Investigate detected anomalies for data quality or significant events")
            recommendations.append("Consider removing or treating anomalies before forecasting")
        if anomaly_rate > 0.15:
            recommendations.append("High anomaly rate - review data collection process")
        recommendations.append("Adjust threshold or method parameters based on domain knowledge")

        return TimeSeriesResult(
            method=TimeSeriesMethod.ANOMALY_DETECTION,
            original_data=data,
            results=results,
            anomalies=anomalies,
            interpretation=interpretation,
            recommendations=recommendations,
            statistics={
                'anomaly_scores_mean': float(anomaly_scores.mean()),
                'anomaly_scores_max': float(anomaly_scores.max()),
                'method_used': method
            }
        )

    def _create_time_features(self, data: pd.Series) -> np.ndarray:
        """Create time-based features for anomaly detection."""
        features = []

        # Value feature
        features.append(data.values.reshape(-1, 1))

        # Time-based features
        if isinstance(data.index, pd.DatetimeIndex):
            # Hour of day, day of week, month, etc.
            hour_of_day = data.index.hour.values.reshape(-1, 1)
            day_of_week = data.index.dayofweek.values.reshape(-1, 1)
            month = data.index.month.values.reshape(-1, 1)

            features.extend([hour_of_day, day_of_week, month])

        # Lag features
        for lag in [1, 2, 3]:
            if len(data) > lag:
                lag_values = data.shift(lag).fillna(data.mean()).values.reshape(-1, 1)
                features.append(lag_values)

        return np.hstack(features)

    def _linear_trend_forecast(self, data: pd.Series, periods: int) -> Tuple[pd.Series, Tuple[pd.Series, pd.Series]]:
        """Generate forecast using linear trend."""
        if not self.scipy_available:
            # Simple fallback
            last_value = data.iloc[-1]
            trend = (data.iloc[-1] - data.iloc[0]) / len(data)

            forecast_index = pd.date_range(start=data.index[-1] + pd.Timedelta(days=1),
                                         periods=periods, freq=data.index.freq)
            forecast_values = [last_value + trend * (i + 1) for i in range(periods)]
            forecast = pd.Series(forecast_values, index=forecast_index)

            # Simple confidence intervals (±20% of last value)
            margin = abs(last_value) * 0.2
            lower = forecast - margin
            upper = forecast + margin

            return forecast, (lower, upper)

        # Linear regression forecast
        x = np.arange(len(data))
        y = data.values

        slope, intercept, _, _, _ = self.stats.linregress(x, y)

        # Generate forecast
        forecast_x = np.arange(len(data), len(data) + periods)
        forecast_values = slope * forecast_x + intercept

        # Create forecast index
        if data.index.freq:
            forecast_index = pd.date_range(start=data.index[-1] + data.index.freq,
                                         periods=periods, freq=data.index.freq)
        else:
            # Estimate frequency
            time_diff = (data.index[-1] - data.index[0]) / (len(data) - 1)
            forecast_index = pd.date_range(start=data.index[-1] + time_diff,
                                         periods=periods, freq=time_diff)

        forecast = pd.Series(forecast_values, index=forecast_index)

        # Calculate confidence intervals
        residuals = y - (slope * x + intercept)
        mse = np.mean(residuals ** 2)

        # Standard error of forecast
        se_forecast = np.sqrt(mse * (1 + 1/len(data) + (forecast_x - np.mean(x))**2 / np.sum((x - np.mean(x))**2)))

        # 95% confidence intervals
        t_critical = 1.96  # Approximate for large samples
        margin = t_critical * se_forecast

        lower = pd.Series(forecast_values - margin, index=forecast_index)
        upper = pd.Series(forecast_values + margin, index=forecast_index)

        return forecast, (lower, upper)

    def _simple_forecast(self, data: pd.Series, periods: int) -> Tuple[pd.Series, Tuple[pd.Series, pd.Series]]:
        """Simple forecast using last value or mean."""
        last_value = data.iloc[-1]

        # Create forecast index
        if data.index.freq:
            forecast_index = pd.date_range(start=data.index[-1] + data.index.freq,
                                         periods=periods, freq=data.index.freq)
        else:
            time_diff = (data.index[-1] - data.index[0]) / (len(data) - 1)
            forecast_index = pd.date_range(start=data.index[-1] + time_diff,
                                         periods=periods, freq=time_diff)

        # Simple forecast (last value)
        forecast = pd.Series([last_value] * periods, index=forecast_index)

        # Confidence intervals based on historical volatility
        std_dev = data.std()
        margin = 1.96 * std_dev  # 95% confidence interval

        lower = pd.Series([last_value - margin] * periods, index=forecast_index)
        upper = pd.Series([last_value + margin] * periods, index=forecast_index)

        return forecast, (lower, upper)

    def format_result_for_agent(self, result: TimeSeriesResult, agent_identity: str = "analyst") -> Dict[str, Any]:
        """
        Format time series results for agent-specific presentation.

        Args:
            result: TimeSeriesResult to format
            agent_identity: Agent identity for customized formatting

        Returns:
            Formatted result dictionary
        """
        # Base formatting
        formatted_result = {
            "method": result.method.value,
            "interpretation": result.interpretation,
            "recommendations": result.recommendations,
            "statistics": result.statistics
        }

        # Add method-specific results
        if result.results:
            formatted_result["results"] = result.results

        # Add forecast data if available
        if result.forecast is not None:
            formatted_result["forecast"] = {
                "values": result.forecast.tolist(),
                "dates": result.forecast.index.strftime('%Y-%m-%d').tolist(),
                "periods": len(result.forecast)
            }

            if result.confidence_intervals:
                lower, upper = result.confidence_intervals
                formatted_result["confidence_intervals"] = {
                    "lower": lower.tolist(),
                    "upper": upper.tolist()
                }

        # Add decomposition if available
        if result.decomposition:
            formatted_result["decomposition"] = {
                component: values.dropna().tolist()[:50]  # Limit size
                for component, values in result.decomposition.items()
                if component != 'observed'  # Skip original data
            }

        # Add anomalies if detected
        if result.anomalies is not None:
            anomaly_dates = result.anomalies[result.anomalies].index
            formatted_result["anomalies"] = {
                "count": len(anomaly_dates),
                "dates": anomaly_dates.strftime('%Y-%m-%d').tolist()[:20]  # Limit to first 20
            }

        # Agent-specific enhancements
        if agent_identity == "marketer":
            formatted_result["business_insights"] = self._add_business_time_series_insights(result)
            formatted_result["marketing_recommendations"] = self._add_marketing_time_series_recommendations(result)
        elif agent_identity == "concierge":
            formatted_result["simplified_explanation"] = self._add_simplified_time_series_explanation(result)
            formatted_result["user_friendly_summary"] = self._add_user_friendly_time_series_summary(result)
        elif agent_identity == "analyst":
            formatted_result["technical_details"] = self._add_technical_time_series_details(result)
            formatted_result["advanced_metrics"] = self._add_advanced_time_series_metrics(result)

        return formatted_result

    def _add_business_time_series_insights(self, result: TimeSeriesResult) -> List[str]:
        """Add business insights for marketing agents."""
        insights = []

        if result.method == TimeSeriesMethod.TREND_ANALYSIS:
            trend_direction = result.results.get('trend_direction', 'stable')
            if trend_direction == 'increasing':
                insights.append("Positive trend indicates growing business opportunity")
                insights.append("Consider scaling marketing efforts to capitalize on growth")
            elif trend_direction == 'decreasing':
                insights.append("Declining trend requires immediate attention and intervention")
                insights.append("Analyze market conditions and adjust strategy accordingly")
            else:
                insights.append("Stable trend suggests consistent market conditions")

        elif result.method == TimeSeriesMethod.SEASONALITY_DETECTION:
            if result.results.get('has_seasonality', False):
                period = result.results.get('detected_period', 0)
                insights.append(f"Seasonal patterns every {period} periods enable predictable planning")
                insights.append("Align marketing campaigns with seasonal peaks for maximum impact")
            else:
                insights.append("No seasonality detected - marketing can be consistent year-round")

        elif result.method == TimeSeriesMethod.FORECASTING:
            if result.forecast is not None:
                forecast_trend = "increasing" if result.forecast.iloc[-1] > result.forecast.iloc[0] else "decreasing"
                insights.append(f"Forecast shows {forecast_trend} trend for planning purposes")
                insights.append("Use predictions for budget allocation and resource planning")

        return insights

    def _add_marketing_time_series_recommendations(self, result: TimeSeriesResult) -> List[str]:
        """Add marketing-specific recommendations."""
        recommendations = []

        if result.method == TimeSeriesMethod.FORECASTING:
            recommendations.append("Use forecasts to plan marketing budget allocation")
            recommendations.append("Adjust campaign timing based on predicted peaks and valleys")

        if result.method == TimeSeriesMethod.SEASONALITY_DETECTION and result.results.get('has_seasonality'):
            recommendations.append("Develop seasonal marketing campaigns")
            recommendations.append("Prepare inventory and staffing for seasonal variations")

        if result.method == TimeSeriesMethod.ANOMALY_DETECTION:
            n_anomalies = result.results.get('n_anomalies', 0)
            if n_anomalies > 0:
                recommendations.append("Investigate anomalies for potential market disruptions")
                recommendations.append("Use anomaly insights to identify unexpected opportunities")

        return recommendations

    def _add_simplified_time_series_explanation(self, result: TimeSeriesResult) -> str:
        """Add simplified explanation for concierge agents."""
        method_explanations = {
            TimeSeriesMethod.TREND_ANALYSIS: "This analysis looked at whether your data is generally going up, down, or staying the same over time.",
            TimeSeriesMethod.SEASONALITY_DETECTION: "This analysis checked if your data has regular patterns that repeat at certain intervals.",
            TimeSeriesMethod.FORECASTING: "This analysis predicted what your data might look like in the future based on past patterns.",
            TimeSeriesMethod.ANOMALY_DETECTION: "This analysis found unusual data points that don't fit the normal pattern.",
            TimeSeriesMethod.DECOMPOSITION: "This analysis broke down your data into different components to understand what's driving the changes."
        }

        base_explanation = method_explanations.get(result.method, "This analysis examined patterns in your time-based data.")

        # Add context based on results
        if result.method == TimeSeriesMethod.TREND_ANALYSIS:
            trend = result.results.get('trend_direction', 'stable')
            base_explanation += f" The overall trend appears to be {trend}."

        elif result.method == TimeSeriesMethod.SEASONALITY_DETECTION:
            has_seasonality = result.results.get('has_seasonality', False)
            if has_seasonality:
                period = result.results.get('detected_period', 0)
                base_explanation += f" Regular patterns repeat every {period} time periods."
            else:
                base_explanation += " No regular repeating patterns were found."

        return base_explanation

    def _add_user_friendly_time_series_summary(self, result: TimeSeriesResult) -> Dict[str, Any]:
        """Add user-friendly summary for concierge agents."""
        summary = {
            "what_was_analyzed": result.method.value.replace('_', ' ').title(),
            "data_period": f"{result.statistics.get('start_date', 'Unknown')} to {result.statistics.get('end_date', 'Unknown')}",
            "data_points": result.statistics.get('data_points', 0)
        }

        if result.method == TimeSeriesMethod.FORECASTING and result.forecast is not None:
            summary["forecast_summary"] = {
                "periods_predicted": len(result.forecast),
                "next_value": float(result.forecast.iloc[0]) if len(result.forecast) > 0 else None,
                "forecast_direction": "up" if len(result.forecast) > 1 and result.forecast.iloc[-1] > result.forecast.iloc[0] else "down"
            }

        if result.method == TimeSeriesMethod.ANOMALY_DETECTION:
            summary["anomaly_summary"] = {
                "anomalies_found": result.results.get('n_anomalies', 0),
                "percentage_anomalous": f"{result.results.get('anomaly_rate', 0) * 100:.1f}%"
            }

        return summary

    def _add_technical_time_series_details(self, result: TimeSeriesResult) -> Dict[str, Any]:
        """Add technical details for analyst agents."""
        details = {
            "method_parameters": result.metadata,
            "data_characteristics": {
                "length": len(result.original_data),
                "frequency": str(result.original_data.index.freq) if result.original_data.index.freq else "Irregular",
                "missing_values": result.original_data.isnull().sum(),
                "data_range": {
                    "min": float(result.original_data.min()),
                    "max": float(result.original_data.max()),
                    "mean": float(result.original_data.mean()),
                    "std": float(result.original_data.std())
                }
            }
        }

        # Add method-specific technical details
        if result.method == TimeSeriesMethod.FORECASTING:
            details["forecast_metrics"] = result.results.get('accuracy_metrics', {})

        if result.decomposition:
            details["decomposition_variance"] = {
                component: float(values.var()) for component, values in result.decomposition.items()
                if hasattr(values, 'var')
            }

        return details

    def _add_advanced_time_series_metrics(self, result: TimeSeriesResult) -> Dict[str, Any]:
        """Add advanced metrics for analyst agents."""
        metrics = {}

        # Calculate additional statistical measures
        data = result.original_data

        # Autocorrelation at lag 1
        try:
            metrics["autocorrelation_lag1"] = float(data.autocorr(lag=1))
        except:
            metrics["autocorrelation_lag1"] = None

        # Coefficient of variation
        if data.mean() != 0:
            metrics["coefficient_of_variation"] = float(data.std() / abs(data.mean()))

        # Skewness and kurtosis
        try:
            if self.scipy_available:
                metrics["skewness"] = float(self.stats.skew(data.dropna()))
                metrics["kurtosis"] = float(self.stats.kurtosis(data.dropna()))
        except:
            pass

        # Data quality metrics
        metrics["data_quality"] = {
            "completeness": float(1 - data.isnull().sum() / len(data)),
            "consistency": float(1 - (data.diff().abs() > 3 * data.std()).sum() / len(data)),
            "outlier_rate": float((np.abs((data - data.mean()) / data.std()) > 3).sum() / len(data))
        }

        return metrics
