/**
 * File Upload Dialog
 * 
 * Dialog for uploading data files to use in dashboards.
 * Features:
 * - Drag and drop file upload
 * - Multiple file format support (CSV, Excel, PDF, DOCX)
 * - Upload progress tracking
 * - File validation and preview
 * - Automatic data source creation
 */

import React, { useState, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  FileText,
  File,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  Download,
  Eye,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface FileUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onFilesUploaded?: (fileIds: string[]) => void;
}

interface UploadFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  progress: number;
  error?: string;
  dataSourceId?: string;
}

const SUPPORTED_FORMATS = {
  'text/csv': { name: 'CSV', icon: FileText, color: 'bg-green-100 text-green-800' },
  'application/vnd.ms-excel': { name: 'Excel', icon: FileText, color: 'bg-blue-100 text-blue-800' },
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { name: 'Excel', icon: FileText, color: 'bg-blue-100 text-blue-800' },
  'application/pdf': { name: 'PDF', icon: File, color: 'bg-red-100 text-red-800' },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { name: 'Word', icon: File, color: 'bg-purple-100 text-purple-800' },
};

export const FileUploadDialog: React.FC<FileUploadDialogProps> = ({
  open,
  onOpenChange,
  onFilesUploaded,
}) => {
  const { toast } = useToast();
  
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!Object.keys(SUPPORTED_FORMATS).includes(file.type)) {
      return `Unsupported file type: ${file.type}`;
    }

    // Check file size (max 50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return `File too large. Maximum size is 50MB.`;
    }

    return null;
  };

  const handleFileSelect = useCallback((selectedFiles: FileList | null) => {
    if (!selectedFiles) return;

    const newFiles: UploadFile[] = [];
    
    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i];
      const error = validateFile(file);
      
      const uploadFile: UploadFile = {
        id: `${Date.now()}-${i}`,
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        status: error ? 'error' : 'pending',
        progress: 0,
        error,
      };
      
      newFiles.push(uploadFile);
    }

    setFiles(prev => [...prev, ...newFiles]);

    if (newFiles.some(f => f.status === 'error')) {
      toast({
        title: "File Validation Error",
        description: "Some files could not be added. Check the file list for details.",
        variant: "destructive",
      });
    }
  }, [toast]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const uploadFile = async (uploadFile: UploadFile): Promise<void> => {
    const formData = new FormData();
    formData.append('file', uploadFile.file);
    formData.append('name', uploadFile.name);
    formData.append('description', `Uploaded file: ${uploadFile.name}`);

    try {
      const response = await fetch('/api/data-sources/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Upload failed');
      }

      const result = await response.json();
      
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { ...f, status: 'completed', progress: 100, dataSourceId: result.id }
          : f
      ));

      return result.id;
    } catch (error) {
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { ...f, status: 'error', error: error instanceof Error ? error.message : 'Upload failed' }
          : f
      ));
      throw error;
    }
  };

  const handleUploadAll = async () => {
    const pendingFiles = files.filter(f => f.status === 'pending');
    if (pendingFiles.length === 0) return;

    setIsUploading(true);
    const uploadedIds: string[] = [];

    try {
      // Update status to uploading
      setFiles(prev => prev.map(f => 
        f.status === 'pending' ? { ...f, status: 'uploading' } : f
      ));

      // Upload files sequentially with progress simulation
      for (const file of pendingFiles) {
        try {
          // Simulate progress
          const progressInterval = setInterval(() => {
            setFiles(prev => prev.map(f => 
              f.id === file.id && f.progress < 90
                ? { ...f, progress: f.progress + 10 }
                : f
            ));
          }, 200);

          const dataSourceId = await uploadFile(file);
          clearInterval(progressInterval);
          
          if (dataSourceId) {
            uploadedIds.push(dataSourceId);
          }
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error);
        }
      }

      const successCount = files.filter(f => f.status === 'completed').length;
      const errorCount = files.filter(f => f.status === 'error').length;

      if (successCount > 0) {
        toast({
          title: "Upload Complete",
          description: `${successCount} file(s) uploaded successfully.${errorCount > 0 ? ` ${errorCount} file(s) failed.` : ''}`,
        });

        onFilesUploaded?.(uploadedIds);
      } else {
        toast({
          title: "Upload Failed",
          description: "No files were uploaded successfully.",
          variant: "destructive",
        });
      }
    } finally {
      setIsUploading(false);
    }
  };

  const getFileIcon = (type: string) => {
    const format = SUPPORTED_FORMATS[type as keyof typeof SUPPORTED_FORMATS];
    return format?.icon || File;
  };

  const getFileColor = (type: string) => {
    const format = SUPPORTED_FORMATS[type as keyof typeof SUPPORTED_FORMATS];
    return format?.color || 'bg-gray-100 text-gray-800';
  };

  const getFileTypeName = (type: string) => {
    const format = SUPPORTED_FORMATS[type as keyof typeof SUPPORTED_FORMATS];
    return format?.name || 'Unknown';
  };

  const completedFiles = files.filter(f => f.status === 'completed');
  const hasErrors = files.some(f => f.status === 'error');
  const canUpload = files.some(f => f.status === 'pending') && !isUploading;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Upload Data Files</DialogTitle>
          <DialogDescription>
            Upload CSV, Excel, PDF, or Word files to use as data sources in your dashboard.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Upload Area */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
              isDragOver 
                ? "border-primary bg-primary/5" 
                : "border-muted-foreground/25 hover:border-muted-foreground/50"
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <div className="space-y-2">
              <p className="text-lg font-medium">
                Drop files here or click to browse
              </p>
              <p className="text-sm text-muted-foreground">
                Supports CSV, Excel, PDF, and Word files up to 50MB
              </p>
            </div>
            <Input
              type="file"
              multiple
              accept=".csv,.xlsx,.xls,.pdf,.docx"
              onChange={(e) => handleFileSelect(e.target.files)}
              className="hidden"
              id="file-upload"
            />
            <Label htmlFor="file-upload" className="cursor-pointer">
              <Button variant="outline" className="mt-4" asChild>
                <span>Browse Files</span>
              </Button>
            </Label>
          </div>

          {/* File List */}
          {files.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Files ({files.length})</h3>
                <div className="flex space-x-2">
                  {canUpload && (
                    <Button onClick={handleUploadAll} disabled={isUploading}>
                      {isUploading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          Upload All
                        </>
                      )}
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    onClick={() => setFiles([])}
                    disabled={isUploading}
                  >
                    Clear All
                  </Button>
                </div>
              </div>

              <div className="max-h-60 overflow-y-auto space-y-2">
                {files.map((file) => {
                  const FileIcon = getFileIcon(file.type);
                  
                  return (
                    <Card key={file.id} className="p-3">
                      <div className="flex items-center space-x-3">
                        <FileIcon className="h-8 w-8 text-muted-foreground" />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <p className="text-sm font-medium truncate">{file.name}</p>
                            <Badge variant="outline" className={getFileColor(file.type)}>
                              {getFileTypeName(file.type)}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {formatFileSize(file.size)}
                          </p>
                          
                          {/* Progress Bar */}
                          {file.status === 'uploading' && (
                            <Progress value={file.progress} className="mt-2 h-1" />
                          )}
                          
                          {/* Error Message */}
                          {file.status === 'error' && file.error && (
                            <p className="text-xs text-destructive mt-1">{file.error}</p>
                          )}
                        </div>
                        
                        {/* Status Icon */}
                        <div className="flex items-center space-x-2">
                          {file.status === 'completed' && (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          )}
                          {file.status === 'error' && (
                            <AlertCircle className="h-5 w-5 text-destructive" />
                          )}
                          {file.status === 'uploading' && (
                            <Loader2 className="h-5 w-5 animate-spin text-primary" />
                          )}
                          
                          {/* Remove Button */}
                          {file.status !== 'uploading' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => removeFile(file.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>

              {/* Summary */}
              {completedFiles.length > 0 && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-800">
                      {completedFiles.length} file(s) uploaded successfully and available as data sources.
                    </span>
                  </div>
                </div>
              )}

              {hasErrors && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span className="text-sm text-red-800">
                      Some files failed to upload. Check the error messages above.
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Supported Formats */}
          <div className="text-xs text-muted-foreground">
            <p className="font-medium mb-1">Supported formats:</p>
            <p>CSV (.csv), Excel (.xlsx, .xls), PDF (.pdf), Word (.docx)</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
