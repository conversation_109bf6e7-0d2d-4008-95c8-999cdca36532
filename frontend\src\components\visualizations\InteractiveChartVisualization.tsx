import React, { useState, useRef, useEffect } from 'react';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Download,
  Maximize2,
  Minimize2,
  RefreshCw,
  Share2,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieIcon,
  <PERSON><PERSON><PERSON> as LineIcon,
  <PERSON>att<PERSON><PERSON><PERSON> as ScatterIcon,
  TrendingUp,
  ImageIcon,
  Zap
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface InteractiveChartData {
  chart_data: Array<Record<string, any>>;
  columns: string[];
  metadata: {
    x_axis?: string;
    y_axes?: string[];
    color_column?: string;
    data_type?: string;
    total_records?: number;
  };
  fallback_image?: string;
}

interface InteractiveChartVisualizationProps {
  title?: string;
  description?: string;
  data: InteractiveChartData;
  chartType: 'bar' | 'line' | 'pie' | 'scatter' | 'area' | 'heatmap';
  config?: {
    responsive?: boolean;
    interactive?: boolean;
    animation?: boolean;
  };
  className?: string;
  onRetry?: () => void;
}

const CHART_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
  '#06B6D4', '#F97316', '#84CC16', '#EC4899', '#6366F1'
];

const getChartIcon = (chartType: string) => {
  switch (chartType) {
    case 'pie': return PieIcon;
    case 'line': return LineIcon;
    case 'scatter': return ScatterIcon;
    case 'area': return TrendingUp;
    default: return BarChart3;
  }
};

export const InteractiveChartVisualization: React.FC<InteractiveChartVisualizationProps> = ({
  title = 'Interactive Data Visualization',
  description,
  data,
  chartType,
  config = {},
  className = '',
  onRetry
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showFallback, setShowFallback] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [processedData, setProcessedData] = useState<Array<Record<string, any>>>([]);
  const [isDataLarge, setIsDataLarge] = useState(false);
  const chartRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Debug logging
  console.log('🎨 InteractiveChartVisualization received data:', data);
  console.log('🎨 InteractiveChartVisualization chartType:', chartType);
  console.log('🎨 InteractiveChartVisualization title:', title);
  console.log('🎨 InteractiveChartVisualization config:', config);

  // 🚨 CRITICAL: Enhanced debugging for production fix
  console.log('🚨 CRITICAL DEBUG - Data structure analysis:');
  if (data) {
    console.log('  - data.chart_data:', data.chart_data);
    console.log('  - data.metadata:', data.metadata);
    console.log('  - data.image:', (data as any).image ? 'Present' : 'Not present');
    console.log('  - Full data object:', JSON.stringify(data, null, 2));
  }

  const ChartIcon = getChartIcon(chartType);

  // Performance optimization: Process and sample data
  useEffect(() => {
    console.log('🎨 InteractiveChartVisualization useEffect - data:', data);
    console.log('🎨 InteractiveChartVisualization useEffect - data.chart_data:', data?.chart_data);
    console.log('🎨 InteractiveChartVisualization useEffect - data keys:', Object.keys(data || {}));
    console.log('🎨 InteractiveChartVisualization useEffect - chartType:', chartType);
    console.log('🎨 InteractiveChartVisualization useEffect - title:', title);

    // 🚨 DEBUGGING: Enhanced logging for data structure analysis
    console.log('🚨 DEBUGGING - Enhanced data analysis:');
    console.log('🔍 data.chart_data type:', typeof data?.chart_data);
    console.log('🔍 data.chart_data isArray:', Array.isArray(data?.chart_data));
    console.log('🔍 data.chart_data length:', data?.chart_data?.length);
    console.log('🔍 data.chart_data first 2 items:', data?.chart_data?.slice(0, 2));
    console.log('🔍 data.fallback_image exists:', !!data?.fallback_image);
    console.log('🔍 data.metadata:', data?.metadata);

    // Check if we have valid chart data
    if (!data?.chart_data || !Array.isArray(data.chart_data) || data.chart_data.length === 0) {
      console.log('🎨 InteractiveChartVisualization: No valid chart_data found');
      console.log('🎨 Chart data:', data.chart_data);
      console.log('🎨 Has fallback image:', !!data.fallback_image);

      // Only show fallback if we have a fallback image AND no chart data
      if (data.fallback_image) {
        console.log('🎨 Using fallback image for visualization');
        setShowFallback(true);
        setProcessedData([]);
        setIsLoading(false);
        return;
      } else {
        console.log('🎨 No chart data and no fallback image available');
        setShowFallback(false);
        setProcessedData([]);
        setIsLoading(false);
        return;
      }
    }

    // We have valid chart data, so don't show fallback by default
    console.log('✅ Valid chart_data found, processing interactive chart');
    setShowFallback(false);

    setIsLoading(true);

    // Use setTimeout to avoid blocking the UI
    setTimeout(() => {
      let chartData = [...data.chart_data];
      const isLarge = chartData.length > 1000;
      setIsDataLarge(isLarge);

      // Sample data for performance if dataset is large
      if (isLarge && chartType !== 'pie') {
        // For non-pie charts, sample data points
        const sampleSize = 500;
        const step = Math.ceil(chartData.length / sampleSize);
        chartData = chartData.filter((_, index) => index % step === 0);

        toast({
          title: "Large Dataset Detected",
          description: `Showing ${chartData.length} of ${data.chart_data.length} data points for better performance.`,
        });
      } else if (isLarge && chartType === 'pie') {
        // For pie charts, show top categories and group others
        chartData = chartData
          .sort((a, b) => (b.value || 0) - (a.value || 0))
          .slice(0, 10);

        if (data.chart_data.length > 10) {
          const othersValue = data.chart_data
            .slice(10)
            .reduce((sum, item) => sum + (item.value || 0), 0);

          chartData.push({ name: 'Others', value: othersValue });
        }
      }

      console.log('🚨 CRITICAL - Setting processed data:');
      console.log('🔍 Final chartData length:', chartData.length);
      console.log('🔍 Final chartData sample:', chartData.slice(0, 2));
      console.log('🔍 About to call setProcessedData...');

      setProcessedData(chartData);
      setIsLoading(false);

      console.log('🚨 CRITICAL - Data processing complete');
      console.log('🔍 setProcessedData called with', chartData.length, 'items');
    }, 0);
  }, [data, chartType, toast]);

  // 🚨 DEBUGGING: Track state changes
  useEffect(() => {
    console.log('🚨 STATE CHANGE - showFallback:', showFallback);
  }, [showFallback]);

  useEffect(() => {
    console.log('🚨 STATE CHANGE - processedData length:', processedData?.length);
    console.log('🚨 STATE CHANGE - processedData sample:', processedData?.slice(0, 1));
  }, [processedData]);

  useEffect(() => {
    console.log('🚨 STATE CHANGE - isLoading:', isLoading);
  }, [isLoading]);

  const handleDownload = () => {
    try {
      // For interactive charts, we'll export as SVG or fall back to image
      if (data.fallback_image) {
        const link = document.createElement('a');
        link.href = data.fallback_image;
        link.download = `interactive-chart-${Date.now()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        toast({
          title: "Chart Downloaded",
          description: "The visualization has been saved to your downloads.",
        });
      } else {
        toast({
          title: "Download Not Available",
          description: "No downloadable version available for this chart.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Unable to download the chart. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleShare = async () => {
    try {
      if (navigator.share && data.fallback_image) {
        await navigator.share({
          title: title,
          text: description || 'Interactive data visualization',
          url: data.fallback_image
        });
      } else {
        // Copy chart data as JSON
        const chartDataJson = JSON.stringify(data.chart_data, null, 2);
        await navigator.clipboard.writeText(chartDataJson);
        toast({
          title: "Chart Data Copied",
          description: "Chart data has been copied to your clipboard as JSON.",
        });
      }
    } catch (error) {
      toast({
        title: "Share Failed",
        description: "Unable to share the chart. Please try again.",
        variant: "destructive",
      });
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const renderChart = () => {
    console.log('🎨 Rendering interactive chart:', {
      chartType,
      dataLength: processedData?.length,
      showFallback,
      hasChartData: !!data?.chart_data,
      hasFallbackImage: !!data?.fallback_image
    });

    // 🚨 CRITICAL DEBUGGING: Log the exact decision point
    console.log('🚨 CRITICAL - Render decision analysis:');
    console.log('🔍 showFallback state:', showFallback);
    console.log('🔍 data.fallback_image exists:', !!data.fallback_image);
    console.log('🔍 processedData length:', processedData?.length);
    console.log('🔍 processedData sample:', processedData?.slice(0, 2));
    console.log('🔍 Will render fallback?', showFallback && data.fallback_image);
    console.log('🔍 Will render interactive?', !showFallback && processedData && processedData.length > 0);

    // 🚨 CRITICAL FIX: Don't show fallback if we have valid chart data, even if processedData isn't ready yet
    const hasValidChartData = data?.chart_data && Array.isArray(data.chart_data) && data.chart_data.length > 0;

    if (showFallback && data.fallback_image && !hasValidChartData) {
      console.log('🎨 Rendering fallback image (user requested or no chart data)');
      return (
        <div className="relative">
          <img
            src={data.fallback_image}
            alt={title}
            className="w-full h-auto object-contain bg-white"
            onError={() => {
              toast({
                title: "Chart Error",
                description: "Unable to load chart visualization.",
                variant: "destructive",
              });
            }}
          />
          <div className="absolute top-2 left-2">
            <Badge variant="secondary" className="text-xs">
              Static Fallback
            </Badge>
          </div>
        </div>
      );
    }

    // 🚨 CRITICAL FIX: If we have valid chart data but processedData isn't ready, show loading
    if (hasValidChartData && (!processedData || processedData.length === 0) && isLoading) {
      console.log('🎨 Valid chart data exists, showing loading while processing...');
      return (
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-sm text-gray-600">Processing interactive chart...</p>
            <p className="text-xs text-gray-400 mt-1">{data.chart_data.length} data points</p>
          </div>
        </div>
      );
    }

    // If no data and no fallback, show error message
    if ((!processedData || processedData.length === 0) && !data.fallback_image && !hasValidChartData) {
      console.log('🎨 No data available for chart rendering');
      console.log('🚨 CRITICAL - No data condition triggered:');
      console.log('🔍 processedData:', processedData);
      console.log('🔍 processedData length:', processedData?.length);
      console.log('🔍 data.fallback_image:', !!data.fallback_image);
      console.log('🔍 hasValidChartData:', hasValidChartData);

      return (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="text-gray-500 mb-4">
            <ChartIcon className="h-12 w-12 mx-auto mb-2" />
            <p className="text-sm">No chart data available</p>
            <p className="text-xs text-red-500 mt-2">
              Debug: processedData={processedData?.length || 0}, fallback={!!data.fallback_image}
            </p>
          </div>
          {onRetry && (
            <Button variant="outline" size="sm" onClick={onRetry}>
              Retry
            </Button>
          )}
        </div>
      );
    }

    // 🚨 CRITICAL FIX: This condition should only trigger if we truly have no data
    if (!processedData || processedData.length === 0) {
      console.log('🎨 No processed data available - backend data pipeline issue');
      console.log('🚨 CRITICAL - Second no data condition triggered:');
      console.log('🔍 processedData:', processedData);
      console.log('🔍 processedData type:', typeof processedData);
      console.log('🔍 processedData length:', processedData?.length);
      console.log('🔍 hasValidChartData:', hasValidChartData);
      console.log('🔍 isLoading:', isLoading);
      console.log('🔍 This should NOT happen if data processing worked correctly');

      // If we have valid chart data but no processed data, something went wrong in processing
      if (hasValidChartData) {
        return (
          <div className="p-8 text-center">
            <BarChart3 className="h-12 w-12 text-orange-400 mx-auto mb-4" />
            <p className="text-orange-600 mb-2 font-medium">Data Processing Error</p>
            <p className="text-sm text-gray-500 mb-4">
              Chart data was received ({data.chart_data.length} items) but processing failed.
            </p>
            {onRetry && (
              <Button onClick={onRetry} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Processing
              </Button>
            )}
          </div>
        );
      }

      return (
        <div className="p-8 text-center">
          <BarChart3 className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-600 mb-2 font-medium">No Real Data Available</p>
          <p className="text-sm text-gray-500 mb-4">
            The backend did not provide structured chart data.
            {data.metadata?.data_type === 'backend_generated'
              ? 'Backend data was expected but not received properly.'
              : 'Interactive charts require structured data from the analysis.'}
          </p>
          {onRetry && (
            <Button onClick={onRetry} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Analysis
            </Button>
          )}
        </div>
      );
    }

    // Debug: Log sample data to understand structure
    console.log('🎨 Sample processedData item:', processedData[0]);
    console.log('🎨 All keys in first item:', Object.keys(processedData[0] || {}));

    const chartProps = {
      data: processedData,
      margin: { top: 20, right: 30, left: 20, bottom: 20 }
    };

    switch (chartType) {
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={400}>
              <PieChart>
              <Pie
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={120}
                fill="#8884d8"
                label
                data={processedData}
              >
                {processedData.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value, name, props) => [
                  value,
                  props.payload.original_name || name
                ]}
                labelFormatter={(label, payload) => {
                  if (payload && payload[0] && payload[0].payload.original_name) {
                    return `${label} (${payload[0].payload.original_name})`;
                  }
                  return label;
                }}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={processedData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis dataKey="name" stroke="#6B7280" fontSize={12} />
              <YAxis stroke="#6B7280" fontSize={12} />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#FFFFFF',
                  border: '1px solid #E5E7EB',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
                labelFormatter={(label, payload) => {
                  if (payload && payload[0] && payload[0].payload.original_name) {
                    return `${label} (${payload[0].payload.original_name})`;
                  }
                  return label;
                }}
              />
              <Legend />
              {data.metadata.y_axes?.map((yAxis, index) => (
                <Line
                  key={yAxis}
                  type="monotone"
                  dataKey={yAxis}
                  stroke={CHART_COLORS[index % CHART_COLORS.length]}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );

      case 'area':
        return (
          <AreaChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis dataKey="name" stroke="#6B7280" fontSize={12} />
            <YAxis stroke="#6B7280" fontSize={12} />
            <Tooltip
              contentStyle={{
                backgroundColor: '#FFFFFF',
                border: '1px solid #E5E7EB',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
              }}
              labelFormatter={(label, payload) => {
                if (payload && payload[0] && payload[0].payload.original_name) {
                  return `${label} (${payload[0].payload.original_name})`;
                }
                return label;
              }}
            />
            <Legend />
            {data.metadata.y_axes?.map((yAxis, index) => (
              <Area
                key={yAxis}
                type="monotone"
                dataKey={yAxis}
                stackId="1"
                stroke={CHART_COLORS[index % CHART_COLORS.length]}
                fill={CHART_COLORS[index % CHART_COLORS.length]}
                fillOpacity={0.6}
              />
            ))}
          </AreaChart>
        );

      case 'scatter':
        return (
          <ScatterChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis dataKey={data.metadata.x_axis || 'name'} stroke="#6B7280" fontSize={12} />
            <YAxis dataKey={data.metadata.y_axes?.[0] || 'value'} stroke="#6B7280" fontSize={12} />
            <Tooltip
              contentStyle={{
                backgroundColor: '#FFFFFF',
                border: '1px solid #E5E7EB',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
              }}
              labelFormatter={(label, payload) => {
                if (payload && payload[0] && payload[0].payload.original_name) {
                  return `${label} (${payload[0].payload.original_name})`;
                }
                return label;
              }}
            />
            <Scatter
              dataKey={data.metadata.y_axes?.[0] || 'value'}
              fill={CHART_COLORS[0]}
            />
          </ScatterChart>
        );

      default: // bar chart
        console.log('🎨 Rendering bar chart');
        console.log('🎨 chartProps:', chartProps);
        console.log('🎨 data.metadata.y_axes:', data.metadata.y_axes);
        console.log('🎨 Sample processedData item:', processedData[0]);

        // If no y_axes defined, try to infer from data
        let yAxes = data.metadata.y_axes;
        if (!yAxes || yAxes.length === 0) {
          // Get all keys except 'name' from the first data item
          const sampleItem = processedData[0];
          if (sampleItem) {
            yAxes = Object.keys(sampleItem).filter(key => key !== 'name');
            console.log('🎨 Inferred y_axes:', yAxes);
          }
        }

        try {
          // Validate that we have valid data for rendering
          if (!yAxes || yAxes.length === 0) {
            console.error('🎨 No y-axes available for bar chart');
            return (
              <div className="p-8 text-center">
                <BarChart3 className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
                <p className="text-yellow-600 mb-2">No Y-axis data found</p>
                <p className="text-sm text-gray-500">Available keys: {Object.keys(processedData[0] || {}).join(', ')}</p>
              </div>
            );
          }

          console.log('🎨 Creating BarChart with yAxes:', yAxes);
          console.log('🎨 Sample data for BarChart:', processedData.slice(0, 3));

          // Use real data with ResponsiveContainer for proper responsive behavior
          const barChart = (
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={processedData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis dataKey="name" stroke="#6B7280" fontSize={12} />
                <YAxis stroke="#6B7280" fontSize={12} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#FFFFFF',
                    border: '1px solid #E5E7EB',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                  }}
                  labelFormatter={(label, payload) => {
                    if (payload && payload[0] && payload[0].payload.original_name) {
                      return `${label} (${payload[0].payload.original_name})`;
                    }
                    return label;
                  }}
                />
                <Legend />
                {yAxes?.map((yAxis, index) => (
                  <Bar
                    key={yAxis}
                    dataKey={yAxis}
                    fill={CHART_COLORS[index % CHART_COLORS.length]}
                    radius={[2, 2, 0, 0]}
                  />
                ))}
              </BarChart>
            </ResponsiveContainer>
          );

          console.log('🎨 BarChart component created successfully');
          return barChart;
        } catch (error) {
          console.error('🎨 Error creating BarChart:', error);
          return (
            <div className="p-8 text-center">
              <BarChart3 className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <p className="text-red-600 mb-2">Chart Rendering Error</p>
              <p className="text-sm text-gray-500">{error.message}</p>
              <p className="text-xs text-gray-400 mt-2">Data: {JSON.stringify(processedData.slice(0, 2), null, 2)}</p>
            </div>
          );
        }
    }
  };

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4">
        <div className="relative w-full h-full max-w-6xl max-h-full bg-white rounded-lg p-4">
          <Button
            onClick={toggleFullscreen}
            className="absolute top-4 right-4 z-10"
            size="sm"
            variant="outline"
          >
            <Minimize2 className="h-4 w-4" />
          </Button>
          <div className="w-full h-full">
            <ResponsiveContainer width="100%" height="100%">
              {renderChart()}
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card className={cn("overflow-hidden shadow-lg border-gray-200 bg-white", className)}>
      <CardHeader className="pb-3 bg-gradient-to-r from-white to-gray-50 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-brand-100 rounded-lg">
              <ChartIcon className="h-5 w-5 text-brand-600" />
            </div>
            <div>
              <CardTitle className="text-lg text-brand-700 font-semibold flex items-center gap-2">
                {title}
                <Badge variant="outline" className="text-xs">
                  <Zap className="h-3 w-3 mr-1" />
                  Interactive
                </Badge>
              </CardTitle>
              {description && (
                <CardDescription className="text-gray-600 mt-1">
                  {description}
                </CardDescription>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {data.metadata.total_records && (
              <Badge variant="secondary" className="text-xs">
                {data.metadata.total_records} records
              </Badge>
            )}
            {isDataLarge && (
              <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                Sampled
              </Badge>
            )}
            <Badge variant="outline" className="text-xs">
              {chartType}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
            <div className="flex items-center gap-2 text-gray-500">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span className="text-sm">Loading interactive chart...</span>
            </div>
          </div>
        )}

        <div className="relative group h-96 bg-gray-50 border border-gray-200 rounded" style={{ minHeight: '384px' }}>
          <div className="absolute top-2 left-2 text-xs text-gray-500 z-20">
            Chart Container: {processedData?.length || 0} items | Type: {chartType}
          </div>

          {/* Debug: Visual indicator to confirm container is visible */}
          <div className="absolute top-2 right-2 text-xs text-blue-500 z-20 bg-white px-2 py-1 rounded">
            Container Active
          </div>

          {/* Interactive chart rendering with fixed dimensions */}
          <div className="w-full h-full flex items-center justify-center bg-white" style={{ minHeight: '400px' }}>
            {(() => {
              try {
                const chart = renderChart();
                console.log('📊 Interactive chart rendered successfully:', chartType);
                return chart;
              } catch (error) {
                console.error('🎨 Error rendering interactive chart:', error);
                return (
                  <div className="p-8 text-center">
                    <BarChart3 className="h-12 w-12 text-red-400 mx-auto mb-4" />
                    <p className="text-red-600 mb-2">Interactive Chart Error</p>
                    <p className="text-sm text-gray-500">{error.message}</p>
                  </div>
                );
              }
            })()}
          </div>
          
          {/* Overlay controls */}
          <div className="absolute top-2 right-16 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="flex gap-1 bg-white bg-opacity-90 rounded-md p-1 shadow-sm">
              <Button
                onClick={toggleFullscreen}
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
              <Button
                onClick={handleDownload}
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
              >
                <Download className="h-4 w-4" />
              </Button>
              <Button
                onClick={handleShare}
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
              >
                <Share2 className="h-4 w-4" />
              </Button>
              {data.fallback_image && (
                <Button
                  onClick={() => setShowFallback(!showFallback)}
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  title={showFallback ? "Show Interactive" : "Show Static"}
                >
                  <ImageIcon className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>

      {data.metadata && (
        <div className="px-4 py-2 bg-gray-50 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>
              Data: {data.metadata.x_axis} × {data.metadata.y_axes?.join(', ')}
            </span>
            <span>
              Type: {data.metadata.data_type || 'chart_data'}
            </span>
          </div>
        </div>
      )}
    </Card>
  );
};
