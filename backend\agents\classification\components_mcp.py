"""
Classification components for the Datagenius agent system.

This module provides components for text classification tasks using MCP tools.
"""

import logging
import json
from typing import Dict, Any, Optional, List

from agents.components.base import AgentComponent
from agents.components.mcp_server import MCPServerComponent

# Configure logging
logger = logging.getLogger(__name__)


class ClassificationParserComponent(AgentComponent):
    """Component for parsing classification requests."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing ClassificationParserComponent")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        message = context.get("message", "")
        ctx = context.get("context", {})

        # Parse the classification request
        classification_type, params = self._parse_classification_request(message, ctx)

        # Update the context with the parsed information
        context["classification_type"] = classification_type
        context["classification_params"] = params

        logger.info(f"Parsed classification request: type={classification_type}, params={params}")

        return context

    def _parse_classification_request(self, message: str, context: Optional[Dict[str, Any]]) -> tuple:
        """
        Parse the classification request from the message and context.

        Args:
            message: The user's message text
            context: Additional context information

        Returns:
            Tuple of (classification_type, parameters)
        """
        # Default to context if provided
        if context and "classification_type" in context:
            return context["classification_type"], context.get("params", {})

        # Parse the message to determine the classification type and parameters
        message_lower = message.lower()

        # Check for Hugging Face classification
        if any(term in message_lower for term in ["hugging face", "hf", "transformer", "bert", "roberta"]):
            # Extract parameters from the message
            params = {
                "classification_type": "huggingface"
            }

            # Look for file ID
            if "file_id" in message_lower:
                parts = message_lower.split("file_id")
                if len(parts) > 1:
                    file_id_part = parts[1].strip()
                    if file_id_part.startswith(":"):
                        file_id_part = file_id_part[1:].strip()
                    file_id = file_id_part.split()[0].strip()
                    params["file_id"] = file_id

            # Look for model name
            if "model" in message_lower:
                parts = message_lower.split("model")
                if len(parts) > 1:
                    model_part = parts[1].strip()
                    if model_part.startswith(":"):
                        model_part = model_part[1:].strip()
                    model_name = model_part.split()[0].strip()
                    params["hf_model_name"] = model_name

            return "text_classification", params

        # Check for LLM classification
        elif any(term in message_lower for term in ["llm", "language model", "gpt", "openai", "groq", "gemini"]):
            # Extract parameters from the message
            params = {
                "classification_type": "llm"
            }

            # Look for file ID
            if "file_id" in message_lower:
                parts = message_lower.split("file_id")
                if len(parts) > 1:
                    file_id_part = parts[1].strip()
                    if file_id_part.startswith(":"):
                        file_id_part = file_id_part[1:].strip()
                    file_id = file_id_part.split()[0].strip()
                    params["file_id"] = file_id

            # Look for provider
            for provider in ["openai", "groq", "gemini", "ollama", "anthropic"]:
                if provider in message_lower:
                    params["model_provider"] = provider
                    break
            
            # Default provider
            if "model_provider" not in params:
                params["model_provider"] = "groq"

            return "text_classification", params

        # Default to unknown
        return "unknown", {}


class MCPClassifierComponent(AgentComponent):
    """Component for classification using MCP tools."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing MCPClassifierComponent")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Check if this component should process the request
        classification_type = context.get("classification_type")
        if classification_type != "text_classification":
            return context

        params = context.get("classification_params", {})
        
        logger.info(f"Processing classification request with params: {params}")

        # Find MCP server component
        mcp_server = None
        for component in context.get("agent_components", []):
            if isinstance(component, MCPServerComponent):
                mcp_server = component
                break

        if not mcp_server:
            context["response"] = "MCP server component not found. Cannot execute classification tasks."
            context["metadata"] = {"error": "mcp_server_not_found"}
            return context

        # Check if required parameters are provided
        if "file_id" not in params:
            context["response"] = "Please provide a file ID for classification."
            context["metadata"] = {"error": "missing_file_id"}
            return context

        # For testing purposes, we'll use mock data
        file_id = params["file_id"]
        mock_filename = f"mock_file_{file_id}.csv"

        # Mock texts - in a real implementation, we would load these from the file
        texts = [
            "We need to improve our customer support response times.",
            "The new product launch was successful with positive feedback.",
            "Users are requesting more features in the mobile app."
        ]

        # Determine classification type
        sub_classification_type = params.get("classification_type", "llm")
        
        try:
            # Call the MCP text classification tool
            tool_args = {
                "texts": texts,
                "classification_type": sub_classification_type
            }
            
            if sub_classification_type == "llm":
                # Add LLM-specific parameters
                tool_args.update({
                    "model_provider": params.get("model_provider", "groq"),
                    "model_name": params.get("model_name", "llama3-70b-8192"),
                    "temperature": params.get("temperature", 0.1),
                    "hierarchy_levels": params.get("hierarchy_levels", ["theme", "category"]),
                    "categories": params.get("categories", [])
                })
            else:  # huggingface
                # Add Hugging Face-specific parameters
                tool_args.update({
                    "hf_model_name": params.get("hf_model_name", "facebook/bart-large-mnli"),
                    "threshold": params.get("threshold", 0.5),
                    "categories": params.get("categories", ["positive", "negative", "neutral"])
                })
            
            # Call the MCP tool
            tool_result = await mcp_server.call_tool("text_classification", tool_args)
            
            # Extract the result from the tool response
            if tool_result.get("isError", False):
                raise ValueError(tool_result.get("content", [{"text": "Unknown error"}])[0].get("text", "Unknown error"))
            
            # Get the classification results from metadata
            classification_results = tool_result.get("metadata", {}).get("results", [])
            
            # Create a response message
            if sub_classification_type == "llm":
                provider = tool_args["model_provider"]
                model = tool_args["model_name"]
                response = (f"I've classified the file '{mock_filename}' using the {provider.capitalize()} "
                           f"model '{model}'. The classification results show that the file contains "
                           f"content related to various themes and categories.")
            else:
                model = tool_args["hf_model_name"]
                response = (f"I've classified the file '{mock_filename}' using the Hugging Face "
                           f"model '{model}'. The classification results show that the file contains "
                           f"content related to various categories.")
            
            # Use the prompt template if available
            prompt_templates = context.get("agent_config", {}).get("system_prompts", {})
            if "hf_classification" in prompt_templates and sub_classification_type == "huggingface":
                from agents.utils.prompt_template import PromptTemplate
                template = PromptTemplate(prompt_templates["hf_classification"])
                
                # Format categories for the template
                categories_str = "\n".join([f"- {result.get('label', 'Unknown')}: {result.get('confidence', 0):.2f}" 
                                          for result in classification_results[:5]])
                
                response = template.format(categories=categories_str)
            
            # Update the context with the response
            context["response"] = response
            context["metadata"] = {
                "classification_type": sub_classification_type,
                "file_id": params["file_id"],
                "provider": tool_args.get("model_provider", "huggingface"),
                "model": tool_args.get("model_name", tool_args.get("hf_model_name", "")),
                "sample_results": classification_results[:10]  # Limit to 10 results
            }
            
        except Exception as e:
            logger.error(f"Error classifying texts: {str(e)}", exc_info=True)
            context["response"] = f"I encountered an error while trying to classify the file: {str(e)}"
            context["metadata"] = {
                "classification_type": sub_classification_type,
                "file_id": params["file_id"],
                "error": str(e)
            }
        
        return context


class ClassificationErrorHandlerComponent(AgentComponent):
    """Component for handling classification errors."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        logger.info("Initializing ClassificationErrorHandlerComponent")

    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request using this component.

        Args:
            context: Context dictionary containing request data

        Returns:
            Updated context dictionary
        """
        # Check if a response has already been generated
        if "response" in context:
            return context

        # Check if classification type is unknown
        classification_type = context.get("classification_type")
        if classification_type == "unknown":
            context["response"] = ("I'm not sure what kind of classification you're looking for. "
                                  "Please specify if you want to use Hugging Face or LLM classification, "
                                  "and provide the necessary parameters.")
            context["metadata"] = {"error": "unknown_classification_type"}

        return context
