"""
Tests for the Production Response Generator.

This module tests the AI-powered response generation functionality
to ensure it provides personalized, context-aware responses.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

from agents.marketing_agent.response_generator import ProductionResponseGenerator, ResponseContext
from agents.marketing_agent.business_context import BusinessContext
from agents.components.base_component import AgentContext


class TestProductionResponseGenerator:
    """Test suite for ProductionResponseGenerator."""
    
    @pytest.fixture
    def generator(self):
        """Create a ProductionResponseGenerator instance for testing."""
        return ProductionResponseGenerator(llm_provider="groq")
    
    @pytest.fixture
    def sample_business_context(self):
        """Sample business context for testing."""
        return BusinessContext(
            industry="Technology",
            business_type="B2B",
            business_size="startup",
            target_market="Small businesses",
            key_products=["SaaS platform", "Analytics tools"],
            marketing_challenges=["lead generation", "content marketing"],
            competitive_advantages=["AI-powered features", "Excellent support"],
            confidence_score=0.8
        )
    
    @pytest.fixture
    def sample_response_context(self, sample_business_context):
        """Sample response context for testing."""
        return ResponseContext(
            user_message="I need help with my marketing strategy",
            conversation_history=[
                {"role": "user", "content": "I run a tech startup"},
                {"role": "assistant", "content": "Great! How can I help?"},
                {"role": "user", "content": "I need help with my marketing strategy"}
            ],
            business_context=sample_business_context,
            user_profile={"role": "Founder", "company": "TechStart Inc"},
            intent_type="strategy",
            scenario="strategy_recommendation"
        )
    
    @pytest.fixture
    def mock_llm_processor(self):
        """Mock LLM processor for testing."""
        mock_processor = AsyncMock()
        mock_context = AgentContext()
        mock_context.set_field("llm_response", 
            "Based on your tech startup's focus on small businesses, I recommend starting with content marketing to establish thought leadership. Focus on creating valuable resources that address your target market's pain points with workflow automation.")
        mock_context.set_status("success")
        mock_processor.process.return_value = mock_context
        return mock_processor
    
    @pytest.mark.asyncio
    async def test_generate_response_success(self, generator, sample_response_context, mock_llm_processor):
        """Test successful response generation."""
        with patch.object(generator, 'llm_processor', mock_llm_processor):
            response = await generator.generate_response(sample_response_context)
            
            assert isinstance(response, str)
            assert len(response) > 0
            assert "tech startup" in response.lower() or "small businesses" in response.lower()
    
    @pytest.mark.asyncio
    async def test_generate_response_without_business_context(self, generator, mock_llm_processor):
        """Test response generation when business context needs to be analyzed."""
        context = ResponseContext(
            user_message="Help me with marketing",
            conversation_history=[{"role": "user", "content": "I need marketing help"}],
            scenario="assistance"
        )
        
        with patch.object(generator, 'llm_processor', mock_llm_processor):
            with patch.object(generator, '_analyze_business_context') as mock_analyze:
                mock_analyze.return_value = BusinessContext(
                    industry="Unknown",
                    confidence_score=0.3
                )
                
                response = await generator.generate_response(context)
                
                mock_analyze.assert_called_once()
                assert isinstance(response, str)
                assert len(response) > 0
    
    @pytest.mark.asyncio
    async def test_generate_response_error_handling(self, generator, sample_response_context):
        """Test error handling in response generation."""
        with patch.object(generator, 'llm_processor') as mock_processor:
            mock_processor.process.side_effect = Exception("LLM error")
            
            response = await generator.generate_response(sample_response_context)
            
            assert isinstance(response, str)
            assert len(response) > 0
            # Should return fallback response
            assert "marketing" in response.lower()
    
    def test_format_business_context(self, generator, sample_business_context):
        """Test business context formatting for prompts."""
        formatted = generator._format_business_context(sample_business_context)
        
        assert "Technology" in formatted
        assert "B2B" in formatted
        assert "startup" in formatted
        assert "Small businesses" in formatted
    
    def test_format_business_context_low_confidence(self, generator):
        """Test business context formatting with low confidence."""
        low_confidence_context = BusinessContext(
            industry="Unknown",
            confidence_score=0.1
        )
        
        formatted = generator._format_business_context(low_confidence_context)
        
        assert "Limited information available" in formatted
    
    def test_summarize_conversation(self, generator):
        """Test conversation history summarization."""
        conversation = [
            {"role": "user", "content": "I run a SaaS company"},
            {"role": "assistant", "content": "That's great!"},
            {"role": "user", "content": "We need help with lead generation"},
            {"role": "assistant", "content": "I can help with that"},
            {"role": "user", "content": "What's the best approach?"}
        ]
        
        summary = generator._summarize_conversation(conversation)
        
        assert "SaaS company" in summary
        assert "lead generation" in summary
        assert "best approach" in summary
    
    def test_format_user_profile(self, generator):
        """Test user profile formatting."""
        profile = {
            "role": "Marketing Manager",
            "company": "TechCorp",
            "industry": "Technology",
            "experience": "5 years"
        }
        
        formatted = generator._format_user_profile(profile)
        
        assert "Marketing Manager" in formatted
        assert "TechCorp" in formatted
        assert "Technology" in formatted
    
    def test_post_process_response(self, generator, sample_response_context):
        """Test response post-processing."""
        # Test long response truncation
        long_response = "This is a very long response. " * 50
        processed = generator._post_process_response(long_response, sample_response_context)
        
        assert len(processed) < len(long_response)
        assert processed.endswith('.')
        
        # Test normal response
        normal_response = "This is a normal response."
        processed_normal = generator._post_process_response(normal_response, sample_response_context)
        
        assert processed_normal == normal_response.strip()
    
    def test_get_fallback_response(self, generator):
        """Test fallback responses for different scenarios."""
        scenarios = ["greeting", "assistance", "clarification", "error_handling", "data_analysis", "strategy_recommendation"]
        
        for scenario in scenarios:
            fallback = generator._get_fallback_response(scenario)
            
            assert isinstance(fallback, str)
            assert len(fallback) > 0
            assert "marketing" in fallback.lower() or "help" in fallback.lower()
    
    @pytest.mark.asyncio
    async def test_different_scenarios(self, generator, sample_business_context, mock_llm_processor):
        """Test response generation for different scenarios."""
        scenarios = ["greeting", "assistance", "clarification", "error_handling", "data_analysis", "strategy_recommendation"]
        
        with patch.object(generator, 'llm_processor', mock_llm_processor):
            for scenario in scenarios:
                context = ResponseContext(
                    user_message=f"Test message for {scenario}",
                    business_context=sample_business_context,
                    scenario=scenario
                )
                
                response = await generator.generate_response(context)
                
                assert isinstance(response, str)
                assert len(response) > 0
    
    @pytest.mark.asyncio
    async def test_personalization_based_on_context(self, generator, mock_llm_processor):
        """Test that responses are personalized based on business context."""
        # Tech startup context
        tech_context = BusinessContext(
            industry="Technology",
            business_type="B2B",
            business_size="startup",
            target_market="Small businesses",
            marketing_challenges=["lead generation"],
            confidence_score=0.8
        )
        
        # Healthcare context
        healthcare_context = BusinessContext(
            industry="Healthcare",
            business_type="B2C",
            business_size="medium",
            target_market="Individual patients",
            marketing_challenges=["trust building"],
            confidence_score=0.8
        )
        
        with patch.object(generator, 'llm_processor', mock_llm_processor):
            # Generate responses for both contexts
            tech_response_context = ResponseContext(
                user_message="I need marketing help",
                business_context=tech_context,
                scenario="assistance"
            )
            
            healthcare_response_context = ResponseContext(
                user_message="I need marketing help",
                business_context=healthcare_context,
                scenario="assistance"
            )
            
            tech_response = await generator.generate_response(tech_response_context)
            healthcare_response = await generator.generate_response(healthcare_response_context)
            
            # Both should be valid responses
            assert isinstance(tech_response, str)
            assert isinstance(healthcare_response, str)
            assert len(tech_response) > 0
            assert len(healthcare_response) > 0
    
    @pytest.mark.asyncio
    async def test_comparison_with_hardcoded_responses(self, generator, sample_response_context, mock_llm_processor):
        """Test that AI responses are more contextual than hardcoded responses."""
        # Hardcoded response (what the old system might return)
        hardcoded_response = "I'm here to help with your marketing challenges! Whether it's content creation, strategy development, or campaign planning, I'm ready to assist. What specific area would you like to focus on?"
        
        # AI-generated response
        with patch.object(generator, 'llm_processor', mock_llm_processor):
            ai_response = await generator.generate_response(sample_response_context)
            
            # AI response should be different from hardcoded
            assert ai_response != hardcoded_response
            
            # AI response should be contextual (mention business context elements)
            context_elements = ["tech", "startup", "small business", "lead generation", "content marketing"]
            ai_mentions_context = any(element in ai_response.lower() for element in context_elements)
            hardcoded_mentions_context = any(element in hardcoded_response.lower() for element in context_elements)
            
            # AI response should be more contextual
            assert ai_mentions_context or len(ai_response) != len(hardcoded_response)
    
    @pytest.mark.asyncio
    async def test_response_quality_metrics(self, generator, sample_response_context, mock_llm_processor):
        """Test response quality metrics."""
        with patch.object(generator, 'llm_processor', mock_llm_processor):
            response = await generator.generate_response(sample_response_context)
            
            # Response should be appropriate length (not too short or too long)
            assert 50 <= len(response) <= 1000
            
            # Response should be coherent (no obvious truncation issues)
            assert not response.endswith("...")
            assert response.strip() == response  # No leading/trailing whitespace
            
            # Response should be professional (contains helpful language)
            helpful_indicators = ["help", "assist", "recommend", "suggest", "can", "will", "let's"]
            assert any(indicator in response.lower() for indicator in helpful_indicators)
