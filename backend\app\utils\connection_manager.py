import logging
from typing import Dict, Set
from fastapi import WebSocket

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {} # user_id to set of WebSockets
        self.logger = logging.getLogger(__name__)

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        self.active_connections[user_id].add(websocket)
        self.logger.info(f"User {user_id} connected to notification stream. Total connections for user: {len(self.active_connections[user_id])}")

    def disconnect(self, websocket: WebSocket, user_id: str):
        if user_id in self.active_connections:
            self.active_connections[user_id].remove(websocket)
            if not self.active_connections[user_id]: # No more connections for this user
                del self.active_connections[user_id]
            self.logger.info(f"User {user_id} disconnected from notification stream. Remaining connections for user: {len(self.active_connections.get(user_id, set()))}")

    async def send_personal_message(self, message: dict, user_id: str):
        if user_id in self.active_connections:
            disconnected_sockets = set()
            for websocket_conn in self.active_connections[user_id]: # Renamed websocket to websocket_conn to avoid conflict
                try:
                    await websocket_conn.send_json(message)
                except Exception as e: # Could be WebSocketClosed, RuntimeError if connection closed abruptly
                    self.logger.warning(f"Error sending message to user {user_id} via WebSocket: {e}. Marking for removal.")
                    disconnected_sockets.add(websocket_conn)
            
            # Clean up disconnected sockets
            for ws in disconnected_sockets:
                if user_id in self.active_connections and ws in self.active_connections[user_id]: # Check if still present
                    self.active_connections[user_id].remove(ws)
            if user_id in self.active_connections and not self.active_connections[user_id]: # Check if user_id still exists
                 del self.active_connections[user_id]

manager = ConnectionManager()
