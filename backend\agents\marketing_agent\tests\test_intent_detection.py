"""
Comprehensive tests for marketing agent intent detection.

This module provides thorough testing of the intent detection logic
to ensure proper classification of user messages.
"""

import pytest
import asyncio
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch

from backend.agents.marketing_agent.components import MarketingParserComponent
from backend.agents.marketing_agent.validation import intent_validator
from backend.agents.marketing_agent.exceptions import ValidationError


class TestIntentDetection:
    """Test suite for intent detection functionality."""
    
    @pytest.fixture
    def parser_component(self):
        """Create a parser component for testing."""
        component = MarketingParserComponent()
        # Initialize with test configuration
        asyncio.run(component._initialize({}))
        return component
    
    @pytest.fixture
    def sample_conversation_history(self):
        """Sample conversation history for testing."""
        return [
            {
                "sender": "user",
                "content": "I need help with marketing",
                "timestamp": "2024-01-01T10:00:00Z"
            },
            {
                "sender": "ai",
                "content": "I'd be happy to help with your marketing needs!",
                "timestamp": "2024-01-01T10:00:30Z",
                "metadata": {"generated_content": False}
            },
            {
                "sender": "ai",
                "content": "Here's a comprehensive marketing strategy...",
                "timestamp": "2024-01-01T10:01:00Z",
                "metadata": {"generated_content": True}
            }
        ]
    
    def test_explicit_content_generation_detection(self, parser_component):
        """Test detection of explicit content generation requests."""
        test_cases = [
            ("generate a marketing strategy", True),
            ("create a marketing plan", True),
            ("develop a campaign strategy", True),
            ("write marketing content", True),
            ("generate social media posts", True),
            ("create seo optimization", True),
            ("build a marketing strategy", True),
            ("what can you do", False),
            ("any other ideas", False),
            ("thanks for the help", False),
            ("how are you", False)
        ]
        
        for message, expected in test_cases:
            result = parser_component._is_explicit_marketing_request(message)
            assert result == expected, f"Failed for message: '{message}'"
    
    def test_follow_up_question_detection(self, parser_component):
        """Test detection of follow-up questions."""
        test_cases = [
            ("what else can be done", True),
            ("any other ideas", True),
            ("do you have any more recommendations", True),
            ("what about social media", True),
            ("can you suggest anything else", True),
            ("more suggestions please", True),
            ("what do you think", True),
            ("thanks", True),
            ("that's great", True),
            ("generate a new strategy", False),
            ("create marketing content", False),
            ("I need a different approach", False)
        ]
        
        for message, expected in test_cases:
            result = parser_component._is_follow_up_question(message)
            assert result == expected, f"Failed for message: '{message}'"
    
    def test_context_aware_intent_detection(self, parser_component, sample_conversation_history):
        """Test intent detection with conversation context."""
        context = {"conversation_history": sample_conversation_history}
        
        # Test regeneration request after generated content
        regeneration_messages = [
            "improve it",
            "make it better",
            "regenerate this",
            "try again"
        ]
        
        for message in regeneration_messages:
            result = parser_component._is_explicit_marketing_request(message, context)
            assert result == True, f"Failed to detect regeneration request: '{message}'"
    
    def test_form_data_detection(self, parser_component):
        """Test detection of form-based requests."""
        context_with_form = {
            "marketing_form_data": {
                "brand_description": "Tech startup",
                "target_audience": "Young professionals",
                "marketing_goals": "Increase brand awareness"
            }
        }
        
        # Any message with form data should be treated as explicit request
        result = parser_component._is_explicit_marketing_request(
            "help me with marketing", 
            context_with_form
        )
        assert result == True
    
    def test_action_button_detection(self, parser_component):
        """Test detection of action button selections."""
        action_buttons = [
            "marketing strategy",
            "campaign strategy", 
            "social media content",
            "seo optimization"
        ]
        
        for button in action_buttons:
            result = parser_component._is_explicit_marketing_request(button)
            assert result == True, f"Failed to detect action button: '{button}'"
    
    def test_edge_cases(self, parser_component):
        """Test edge cases and boundary conditions."""
        edge_cases = [
            ("", False),  # Empty message
            ("   ", False),  # Whitespace only
            ("a", False),  # Single character
            ("generate", False),  # Incomplete command
            ("marketing", False),  # Single keyword without context
            ("GENERATE A MARKETING STRATEGY", True),  # All caps
            ("Generate A Marketing Strategy", True),  # Mixed case
        ]
        
        for message, expected in edge_cases:
            result = parser_component._is_explicit_marketing_request(message)
            assert result == expected, f"Failed for edge case: '{message}'"
    
    def test_intent_validation(self):
        """Test intent validation functionality."""
        # Test valid intent classification
        result = intent_validator.validate_intent_classification(
            message="generate a marketing strategy",
            classified_intent="content_generation",
            confidence=0.95
        )
        assert result.is_valid == True
        assert len(result.issues) == 0
        
        # Test low confidence warning
        result = intent_validator.validate_intent_classification(
            message="maybe help with marketing",
            classified_intent="content_generation",
            confidence=0.5
        )
        assert result.is_valid == True
        assert len(result.issues) == 1
        assert result.issues[0].severity.value == "warning"
        
        # Test invalid intent type
        result = intent_validator.validate_intent_classification(
            message="test message",
            classified_intent="invalid_intent",
            confidence=0.8
        )
        assert result.is_valid == False
        assert any(issue.severity.value == "error" for issue in result.issues)
    
    @pytest.mark.asyncio
    async def test_component_processing_flow(self, parser_component):
        """Test the complete component processing flow."""
        # Test conversational message
        context = await parser_component.process({
            "message": "what else can you recommend",
            "conversation_history": []
        })
        
        assert context.get("is_conversational") == True
        assert context.get("skip_marketing_content_generation") == True
        
        # Test explicit content generation
        context = await parser_component.process({
            "message": "generate a marketing strategy",
            "conversation_history": []
        })
        
        assert context.get("is_conversational") != True
        assert context.get("skip_marketing_content_generation") != True
    
    def test_performance_with_large_context(self, parser_component):
        """Test performance with large conversation history."""
        # Create large conversation history
        large_history = []
        for i in range(100):
            large_history.extend([
                {
                    "sender": "user",
                    "content": f"Message {i}",
                    "timestamp": f"2024-01-01T{i:02d}:00:00Z"
                },
                {
                    "sender": "ai", 
                    "content": f"Response {i}",
                    "timestamp": f"2024-01-01T{i:02d}:00:30Z",
                    "metadata": {"generated_content": i % 5 == 0}
                }
            ])
        
        context = {"conversation_history": large_history}
        
        # Should still work efficiently
        import time
        start_time = time.time()
        result = parser_component._is_explicit_marketing_request(
            "improve it", 
            context
        )
        end_time = time.time()
        
        assert result == True
        assert end_time - start_time < 0.1  # Should complete in under 100ms
    
    def test_concurrent_processing(self, parser_component):
        """Test concurrent processing of multiple requests."""
        async def process_message(message):
            return await parser_component.process({
                "message": message,
                "conversation_history": []
            })
        
        async def run_concurrent_test():
            messages = [
                "generate marketing strategy",
                "what else can you do",
                "create campaign plan",
                "any other ideas",
                "develop social media content"
            ]
            
            # Process all messages concurrently
            tasks = [process_message(msg) for msg in messages]
            results = await asyncio.gather(*tasks)
            
            # Verify results
            assert len(results) == 5
            assert results[0].get("is_conversational") != True  # generate
            assert results[1].get("is_conversational") == True  # what else
            assert results[2].get("is_conversational") != True  # create
            assert results[3].get("is_conversational") == True  # any other
            assert results[4].get("is_conversational") != True  # develop
        
        asyncio.run(run_concurrent_test())


class TestIntentDetectionIntegration:
    """Integration tests for intent detection with other components."""
    
    @pytest.mark.asyncio
    async def test_integration_with_content_generator(self):
        """Test integration between intent detection and content generation."""
        # This would test the full flow from intent detection to content generation
        # Mock the content generator and verify proper routing
        pass
    
    @pytest.mark.asyncio
    async def test_integration_with_conversation_tool(self):
        """Test integration with conversation tool for follow-up questions."""
        # This would test the flow from intent detection to conversation handling
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
