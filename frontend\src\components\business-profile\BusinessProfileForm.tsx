/**
 * Business Profile Form Component
 * 
 * Form for creating and editing business profiles with marketing context fields.
 */

import React, { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Loader2, Building2, Target, Briefcase, Sparkles, ChevronDown, ChevronUp } from 'lucide-react';
import { BusinessProfileCreate, businessTypeOptions, businessSizeOptions, businessStageOptions } from '@/lib/businessProfileApi';
import { BusinessProfileAutoFill } from './BusinessProfileAutoFill';

// Form validation schema
const businessProfileSchema = z.object({
  name: z.string().min(1, 'Business name is required').max(255, 'Name must be less than 255 characters'),
  description: z.string().optional(),
  industry: z.string().optional(),
  business_type: z.enum(['B2B', 'B2C', 'B2B2C', 'marketplace', 'saas', 'ecommerce']).optional(),
  business_size: z.enum(['startup', 'small', 'medium', 'large', 'enterprise']).optional(),
  target_audience: z.string().optional(),
  products_services: z.string().optional(),
  marketing_goals: z.string().optional(),
  competitive_landscape: z.string().optional(),
  budget_indicators: z.string().optional(),
  geographic_focus: z.string().optional(),
  business_stage: z.enum(['idea', 'startup', 'growth', 'mature', 'enterprise']).optional(),

  // Marketing-specific fields (consolidated from marketing form)
  budget: z.string().optional(),
  timeline: z.string().optional(),
  platforms: z.string().optional(),
});

type BusinessProfileFormData = z.infer<typeof businessProfileSchema>;

interface BusinessProfileFormProps {
  initialData?: Partial<BusinessProfileFormData>;
  onSubmit: (data: BusinessProfileCreate) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  submitLabel?: string;
  title?: string;
  description?: string;
}

export const BusinessProfileForm: React.FC<BusinessProfileFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  submitLabel = 'Create Business Profile',
  title = 'Create Business Profile',
  description = 'Set up your business profile to provide context for AI agents'
}) => {
  const [showAutoFill, setShowAutoFill] = useState(false);
  const [autoFilledFields, setAutoFilledFields] = useState<Record<string, string>>({});

  const form = useForm<BusinessProfileFormData>({
    resolver: zodResolver(businessProfileSchema),
    defaultValues: {
      name: '',
      description: '',
      industry: '',
      business_type: undefined,
      business_size: undefined,
      target_audience: '',
      products_services: '',
      marketing_goals: '',
      competitive_landscape: '',
      budget_indicators: '',
      geographic_focus: '',
      business_stage: undefined,

      // Marketing-specific fields (consolidated from marketing form)
      budget: '',
      timeline: '',
      platforms: '',

      ...initialData,
    },
  });

  const handleSubmit = async (data: BusinessProfileFormData) => {
    try {
      await onSubmit(data as BusinessProfileCreate);
    } catch (error) {
      console.error('Error submitting business profile:', error);
    }
  };

  // Handle auto-fill
  const handleAutoFill = useCallback((fields: Record<string, string>) => {
    setAutoFilledFields(fields);

    // Update form values
    Object.entries(fields).forEach(([fieldName, value]) => {
      if (fieldName in form.getValues()) {
        form.setValue(fieldName as keyof BusinessProfileFormData, value as any);
      }
    });

    setShowAutoFill(false);
  }, [form]);

  // Check if field was auto-filled
  const isAutoFilled = useCallback((fieldName: string) => {
    return fieldName in autoFilledFields;
  }, [autoFilledFields]);

  // Auto-fill indicator component
  const AutoFillIndicator: React.FC<{ fieldName: string }> = ({ fieldName }) => {
    if (!isAutoFilled(fieldName)) return null;

    return (
      <Badge variant="secondary" className="ml-2 text-xs">
        <Sparkles className="h-3 w-3 mr-1" />
        Auto-filled
      </Badge>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              {title}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowAutoFill(!showAutoFill)}
            className="flex items-center gap-2"
          >
            <Sparkles className="h-4 w-4" />
            Auto-fill
            {showAutoFill ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Auto-fill Section */}
        <Collapsible open={showAutoFill} onOpenChange={setShowAutoFill}>
          <CollapsibleContent>
            <div className="mb-6 p-4 border rounded-lg bg-muted/50">
              <BusinessProfileAutoFill
                onAutoFill={handleAutoFill}
                onClose={() => setShowAutoFill(false)}
                disabled={isLoading}
              />
            </div>
          </CollapsibleContent>
        </Collapsible>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Basic Information
              </h3>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Business Name *
                      <AutoFillIndicator fieldName="name" />
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your business name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Description
                      <AutoFillIndicator fieldName="description" />
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Brief description of your business"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide a brief overview of what your business does
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="industry"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Industry
                        <AutoFillIndicator fieldName="industry" />
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Technology, Retail, Healthcare" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="geographic_focus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Geographic Focus</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Local, National, Global" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="business_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Business Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {businessTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="business_size"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Business Size</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select size" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {businessSizeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="business_stage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Business Stage</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select stage" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {businessStageOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Business Context */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Target className="h-4 w-4" />
                Business Context
              </h3>

              <FormField
                control={form.control}
                name="target_audience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Audience</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe your ideal customers and target market"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Who are your customers? What are their demographics, needs, and pain points?
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="products_services"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Products & Services</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe your main products or services"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      What do you offer? What makes your products/services unique?
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Marketing Context */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Briefcase className="h-4 w-4" />
                Marketing Context
              </h3>

              <FormField
                control={form.control}
                name="marketing_goals"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Marketing Goals</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="What are your marketing objectives and goals?"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      e.g., Increase brand awareness, generate leads, improve customer retention
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="competitive_landscape"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Competitive Landscape</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Describe your main competitors and market position"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="budget_indicators"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Indicators</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Marketing budget range or constraints"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        e.g., Small budget, Mid-range, Enterprise level
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Additional Marketing Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="budget"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Marketing Budget
                        <AutoFillIndicator fieldName="budget" />
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe budget constraints, allocations, or financial considerations"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        e.g., $10,000 monthly marketing budget, Limited budget for digital advertising
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="timeline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Timeline
                        <AutoFillIndicator fieldName="timeline" />
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Timeline constraints, deadlines, or scheduling requirements"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        e.g., Launch campaign by Q2, 6-month marketing timeline
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="platforms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Platforms & Channels
                      <AutoFillIndicator fieldName="platforms" />
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Specific platforms, channels, or mediums for content distribution"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      e.g., Facebook, Instagram, LinkedIn, Email marketing and SEO
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-3 pt-6 border-t">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {submitLabel}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
