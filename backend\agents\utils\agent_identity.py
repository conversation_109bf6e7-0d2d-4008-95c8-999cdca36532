"""
Dynamic Agent Identity Detection System

This module provides a production-ready, extensible system for detecting agent identities
from various sources (configuration, context, agent IDs) without hardcoding.
"""

import logging
import re
from typing import Dict, Any, Optional, List, Set
from dataclasses import dataclass
from pathlib import Path
import yaml
import os

logger = logging.getLogger(__name__)


@dataclass
class AgentIdentityInfo:
    """Information about an agent's identity."""
    identity: str
    name: str
    description: str
    keywords: List[str]
    capabilities: List[str]
    system_prompt: Optional[str] = None


class AgentIdentityRegistry:
    """
    Registry for managing agent identities dynamically.
    
    This class automatically discovers agent identities from:
    1. Agent configuration files (YAML)
    2. Agent class names and metadata
    3. Runtime registration
    """
    
    def __init__(self):
        self._identities: Dict[str, AgentIdentityInfo] = {}
        self._agent_id_to_identity: Dict[str, str] = {}
        self._keywords_to_identity: Dict[str, str] = {}
        self._initialized = False
    
    async def initialize(self, personas_dir: str = "backend/personas") -> None:
        """
        Initialize the registry by scanning persona configuration files.
        
        Args:
            personas_dir: Directory containing persona YAML files
        """
        if self._initialized:
            return
            
        logger.info("Initializing Agent Identity Registry")
        
        try:
            await self._scan_persona_configs(personas_dir)
            await self._build_keyword_mappings()
            self._initialized = True
            logger.info(f"Agent Identity Registry initialized with {len(self._identities)} identities")
        except Exception as e:
            logger.error(f"Failed to initialize Agent Identity Registry: {e}", exc_info=True)
            # Initialize with minimal fallback identities
            await self._initialize_fallback_identities()
    
    async def _scan_persona_configs(self, personas_dir: str) -> None:
        """Scan persona configuration files to extract identity information."""
        personas_path = Path(personas_dir)
        
        if not personas_path.exists():
            logger.warning(f"Personas directory not found: {personas_dir}")
            return
        
        for config_file in personas_path.glob("*.yaml"):
            try:
                await self._process_persona_config(config_file)
            except Exception as e:
                logger.error(f"Error processing persona config {config_file}: {e}")
    
    async def _process_persona_config(self, config_file: Path) -> None:
        """Process a single persona configuration file."""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if not config:
                return
            
            # Extract identity information from config
            agent_id = config.get("id", "")
            name = config.get("name", "")
            description = config.get("description", "")
            industry = config.get("industry", "")
            skills = config.get("skills", [])
            capabilities = config.get("capabilities", [])
            
            # Determine identity from various sources
            identity = self._extract_identity_from_config(config, agent_id, name, industry, skills)
            
            if identity and agent_id:
                # Create identity info
                identity_info = AgentIdentityInfo(
                    identity=identity,
                    name=name,
                    description=description,
                    keywords=self._extract_keywords(name, description, industry, skills),
                    capabilities=capabilities,
                    system_prompt=config.get("system_prompts", {}).get("default")
                )
                
                # Register the identity
                self._identities[identity] = identity_info
                self._agent_id_to_identity[agent_id] = identity
                
                logger.debug(f"Registered agent identity: {agent_id} -> {identity}")
                
        except Exception as e:
            logger.error(f"Error processing config file {config_file}: {e}")
    
    def _extract_identity_from_config(
        self, 
        config: Dict[str, Any], 
        agent_id: str, 
        name: str, 
        industry: str, 
        skills: List[str]
    ) -> str:
        """
        Extract agent identity from configuration using multiple strategies.
        
        Args:
            config: Full configuration dictionary
            agent_id: Agent ID
            name: Agent name
            industry: Agent industry
            skills: Agent skills list
            
        Returns:
            Detected identity string
        """
        # Strategy 1: Explicit identity in config
        if "identity" in config:
            return config["identity"].lower()
        
        # Strategy 2: Derive from industry
        if industry:
            industry_lower = industry.lower()
            if "data" in industry_lower or "analyt" in industry_lower:
                return "analyst"
            elif "marketing" in industry_lower:
                return "marketer"
            elif "classification" in industry_lower:
                return "classifier"
        
        # Strategy 3: Derive from agent name
        if name:
            name_lower = name.lower()
            if "analyst" in name_lower or "analysis" in name_lower:
                return "analyst"
            elif "market" in name_lower:
                return "marketer"
            elif "classif" in name_lower:
                return "classifier"
            elif "concierge" in name_lower:
                return "concierge"
        
        # Strategy 4: Derive from agent ID patterns
        if agent_id:
            agent_id_lower = agent_id.lower()
            if "analysis" in agent_id_lower or "analyst" in agent_id_lower:
                return "analyst"
            elif "marketing" in agent_id_lower or "marketer" in agent_id_lower:
                return "marketer"
            elif "classif" in agent_id_lower:
                return "classifier"
            elif "concierge" in agent_id_lower:
                return "concierge"
        
        # Strategy 5: Derive from skills/capabilities
        if skills:
            skills_text = " ".join(skills).lower()
            if any(keyword in skills_text for keyword in ["data analysis", "visualization", "statistical"]):
                return "analyst"
            elif any(keyword in skills_text for keyword in ["marketing", "content creation", "campaign"]):
                return "marketer"
            elif any(keyword in skills_text for keyword in ["classification", "categorization"]):
                return "classifier"
        
        # Default fallback
        return "assistant"
    
    def _extract_keywords(
        self, 
        name: str, 
        description: str, 
        industry: str, 
        skills: List[str]
    ) -> List[str]:
        """Extract keywords for identity matching."""
        keywords = []
        
        # Add words from name
        if name:
            keywords.extend(re.findall(r'\w+', name.lower()))
        
        # Add words from description
        if description:
            keywords.extend(re.findall(r'\w+', description.lower()))
        
        # Add industry
        if industry:
            keywords.extend(re.findall(r'\w+', industry.lower()))
        
        # Add skills
        for skill in skills:
            if skill:
                keywords.extend(re.findall(r'\w+', skill.lower()))
        
        # Remove duplicates and common words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        keywords = list(set(keyword for keyword in keywords if keyword not in stop_words and len(keyword) > 2))
        
        return keywords
    
    async def _build_keyword_mappings(self) -> None:
        """Build keyword to identity mappings for fuzzy matching."""
        for identity, info in self._identities.items():
            for keyword in info.keywords:
                if keyword not in self._keywords_to_identity:
                    self._keywords_to_identity[keyword] = identity
    
    async def _initialize_fallback_identities(self) -> None:
        """Initialize minimal fallback identities if config scanning fails."""
        fallback_identities = {
            "analyst": AgentIdentityInfo(
                identity="analyst",
                name="Data Analyst",
                description="Specialized in data analysis and visualization",
                keywords=["analysis", "data", "visualization", "statistics"],
                capabilities=["data_analysis", "data_visualization"]
            ),
            "marketer": AgentIdentityInfo(
                identity="marketer",
                name="Marketing Expert",
                description="Specialized in marketing and content creation",
                keywords=["marketing", "content", "campaign", "strategy"],
                capabilities=["content_creation", "marketing_strategy"]
            ),
            "classifier": AgentIdentityInfo(
                identity="classifier",
                name="Data Classifier",
                description="Specialized in data classification and organization",
                keywords=["classification", "categorization", "organization"],
                capabilities=["text_classification", "data_organization"]
            ),
            "concierge": AgentIdentityInfo(
                identity="concierge",
                name="Concierge Assistant",
                description="General assistant for navigation and guidance",
                keywords=["concierge", "assistant", "help", "guidance"],
                capabilities=["general_assistance", "navigation"]
            )
        }
        
        self._identities.update(fallback_identities)
        
        # Build basic ID mappings
        self._agent_id_to_identity.update({
            "composable-analysis-ai": "analyst",
            "composable-marketing-ai": "marketer",
            "composable-classifier-ai": "classifier",
            "concierge-agent": "concierge"
        })
        
        logger.info("Initialized fallback agent identities")
    
    def detect_identity(
        self, 
        agent_id: Optional[str] = None,
        agent_config: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
        intent_type: Optional[str] = None
    ) -> str:
        """
        Detect agent identity using multiple strategies.
        
        Args:
            agent_id: Agent ID to look up
            agent_config: Agent configuration dictionary
            context: User context containing agent information
            intent_type: Detected intent type
            
        Returns:
            Detected agent identity string
        """
        if not self._initialized:
            logger.warning("Agent Identity Registry not initialized, using fallback detection")
            return self._fallback_detect_identity(agent_id, context, intent_type)
        
        # Strategy 1: Direct agent ID lookup
        if agent_id and agent_id in self._agent_id_to_identity:
            return self._agent_id_to_identity[agent_id]
        
        # Strategy 2: Extract from agent config
        if agent_config:
            config_identity = self._extract_identity_from_config(
                agent_config,
                agent_config.get("id", ""),
                agent_config.get("name", ""),
                agent_config.get("industry", ""),
                agent_config.get("skills", [])
            )
            if config_identity != "assistant":  # Don't use generic fallback
                return config_identity
        
        # Strategy 3: Context-based detection
        if context:
            context_identity = self._detect_from_context(context)
            if context_identity:
                return context_identity
        
        # Strategy 4: Intent-based detection
        if intent_type:
            intent_identity = self._detect_from_intent(intent_type)
            if intent_identity:
                return intent_identity
        
        # Strategy 5: Fuzzy matching on agent_id
        if agent_id:
            fuzzy_identity = self._fuzzy_match_identity(agent_id)
            if fuzzy_identity:
                return fuzzy_identity
        
        # Default fallback
        return "concierge"

    def _detect_from_context(self, context: Dict[str, Any]) -> Optional[str]:
        """Detect identity from user context."""
        # Priority 1: Explicit agent identity
        if "agent_identity" in context:
            return context["agent_identity"]

        # Priority 2: Agent/Persona IDs
        for key in ["agent_id", "persona_id", "current_persona"]:
            if key in context and context[key] in self._agent_id_to_identity:
                return self._agent_id_to_identity[context[key]]

        # Priority 3: Legacy context flags (backward compatibility)
        if context.get("is_analysis_response", False):
            return "analyst"
        if context.get("is_marketing_agent", False):
            return "marketer"

        return None

    def _detect_from_intent(self, intent_type: str) -> Optional[str]:
        """Detect identity from intent type."""
        intent_mappings = {
            "analysis_request": "analyst",
            "data_analysis": "analyst",
            "visualization_request": "analyst",
            "marketing_advice": "marketer",
            "content_creation": "marketer",
            "campaign_planning": "marketer",
            "classification_request": "classifier",
            "text_classification": "classifier",
            "data_organization": "classifier",
            "greeting": None,  # Don't override based on greeting
            "general_question": None  # Don't override based on general questions
        }

        return intent_mappings.get(intent_type)

    def _fuzzy_match_identity(self, agent_id: str) -> Optional[str]:
        """Perform fuzzy matching on agent ID using keywords."""
        agent_id_lower = agent_id.lower()

        # Score each identity based on keyword matches
        scores = {}
        for identity, info in self._identities.items():
            score = 0
            for keyword in info.keywords:
                if keyword in agent_id_lower:
                    score += 1
            if score > 0:
                scores[identity] = score

        # Return identity with highest score
        if scores:
            return max(scores, key=scores.get)

        return None

    def _fallback_detect_identity(
        self,
        agent_id: Optional[str],
        context: Optional[Dict[str, Any]],
        intent_type: Optional[str]
    ) -> str:
        """Fallback detection when registry is not initialized."""
        # Simple pattern matching
        if agent_id:
            agent_id_lower = agent_id.lower()
            if "analysis" in agent_id_lower or "analyst" in agent_id_lower:
                return "analyst"
            elif "marketing" in agent_id_lower or "marketer" in agent_id_lower:
                return "marketer"
            elif "classif" in agent_id_lower:
                return "classifier"
            elif "concierge" in agent_id_lower:
                return "concierge"

        # Context-based fallback
        if context:
            if context.get("is_analysis_response", False) or intent_type == "analysis_request":
                return "analyst"
            if context.get("is_marketing_agent", False) or intent_type == "marketing_advice":
                return "marketer"

        return "concierge"

    def get_identity_info(self, identity: str) -> Optional[AgentIdentityInfo]:
        """Get detailed information about an identity."""
        return self._identities.get(identity)

    def get_system_prompt(self, identity: str) -> Optional[str]:
        """Get system prompt for an identity."""
        info = self._identities.get(identity)
        return info.system_prompt if info else None

    def list_identities(self) -> List[str]:
        """List all registered identities."""
        return list(self._identities.keys())

    def register_identity(
        self,
        agent_id: str,
        identity_info: AgentIdentityInfo
    ) -> None:
        """
        Register a new agent identity at runtime.

        Args:
            agent_id: Agent ID to register
            identity_info: Identity information
        """
        self._identities[identity_info.identity] = identity_info
        self._agent_id_to_identity[agent_id] = identity_info.identity

        # Update keyword mappings
        for keyword in identity_info.keywords:
            if keyword not in self._keywords_to_identity:
                self._keywords_to_identity[keyword] = identity_info.identity

        logger.info(f"Registered new agent identity: {agent_id} -> {identity_info.identity}")


# Global registry instance
_registry: Optional[AgentIdentityRegistry] = None


async def get_agent_identity_registry() -> AgentIdentityRegistry:
    """Get the global agent identity registry instance."""
    global _registry
    if _registry is None:
        _registry = AgentIdentityRegistry()
        await _registry.initialize()
    return _registry


async def detect_agent_identity(
    agent_id: Optional[str] = None,
    agent_config: Optional[Dict[str, Any]] = None,
    context: Optional[Dict[str, Any]] = None,
    intent_type: Optional[str] = None
) -> str:
    """
    Convenience function to detect agent identity.

    Args:
        agent_id: Agent ID to look up
        agent_config: Agent configuration dictionary
        context: User context containing agent information
        intent_type: Detected intent type

    Returns:
        Detected agent identity string
    """
    registry = await get_agent_identity_registry()
    return registry.detect_identity(agent_id, agent_config, context, intent_type)


async def get_agent_system_prompt(identity: str) -> Optional[str]:
    """
    Get system prompt for an agent identity.

    Args:
        identity: Agent identity string

    Returns:
        System prompt string or None
    """
    registry = await get_agent_identity_registry()
    return registry.get_system_prompt(identity)
