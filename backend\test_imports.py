#!/usr/bin/env python3
"""
Test script to verify all imports work correctly for the auto-fill feature.
"""

import sys
import traceback

def test_import(module_name, description):
    """Test importing a module and print result."""
    try:
        __import__(module_name)
        print(f"✅ {description}: {module_name}")
        return True
    except Exception as e:
        print(f"❌ {description}: {module_name} - {str(e)}")
        return False

def main():
    """Test all critical imports."""
    print("Testing Auto-fill Feature Imports...")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    # Test core dependencies
    tests = [
        ("app.services.document_processing_service", "Document Processing Service"),
        ("app.services.web_scraping_service", "Web Scraping Service"),
        ("app.services.ai_field_mapping_service", "AI Field Mapping Service"),
        ("app.api.business_profile_autofill", "Auto-fill API"),
        ("app.auth", "Authentication Module"),
        ("app.database", "Database Module"),
        ("beautifulsoup4", "BeautifulSoup4"),
        ("html2text", "HTML2Text"),
        ("requests", "Requests"),
        ("aiohttp", "AioHTTP"),
    ]
    
    for module, description in tests:
        total_count += 1
        if test_import(module, description):
            success_count += 1
    
    print("=" * 50)
    print(f"Import Test Results: {success_count}/{total_count} successful")
    
    if success_count == total_count:
        print("🎉 All imports successful! Auto-fill feature should work.")
        return 0
    else:
        print("⚠️  Some imports failed. Please install missing dependencies.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
