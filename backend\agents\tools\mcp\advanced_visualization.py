"""
Advanced visualization MCP tool using PandasAI.

This module provides an MCP-compatible tool for creating advanced visualizations
by interpreting natural language prompts using PandasAI.
"""

import logging
import os
import io
import base64
import pandas as pd
from typing import Dict, Any, Optional

# PandasAI imports
import pandasai as pai
from pandasai import Agent


from .base import BaseMCPTool
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)

# --- Reusing Helper Functions (Assume they are accessible or defined here/imported) ---
# If these are defined in a shared utility file, import them instead.
# For simplicity here, they are redefined.

def _try_load_dataframe(file_path: str) -> Optional[pd.DataFrame]:
    """Attempts to load a dataframe from the given path."""
    try:
        if file_path.lower().endswith(".csv"):
            df = pd.read_csv(file_path)
        elif file_path.lower().endswith((".xls", ".xlsx")):
            df = pd.read_excel(file_path)
        elif file_path.lower().endswith(".json"):
             df = pd.read_json(file_path)
        else:
            logger.warning(f"Unsupported file type for PandasAI visualization: {file_path}")
            return None
        if df.empty:
             logger.warning(f"Loaded dataframe from {file_path} is empty.")
             return None
        logger.info(f"Successfully loaded dataframe from {file_path} for PandasAI advanced visualization")
        return df
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        return None
    except pd.errors.EmptyDataError:
        logger.warning(f"File is empty: {file_path}")
        return None
    except Exception as e:
        logger.error(f"Error loading dataframe from {file_path}: {e}", exc_info=True)
        return None

def _get_pandasai_llm(provider: Optional[str], api_key: Optional[str], model: Optional[str] = None):
    """Instantiates a PandasAI LLM based on provider and API key."""
    from ..pandasai_v3.llm_providers import LLMProviderFactory

    provider = provider.lower() if provider else "openai"
    if not api_key:
        logger.error(f"API key missing for LLM provider: {provider}")
        raise ValueError(f"API key required for {provider}")

    try:
        return LLMProviderFactory.create_provider(provider=provider, api_key=api_key, model=model)
    except Exception as e:
        logger.error(f"Error instantiating LLM for {provider}: {e}", exc_info=True)
        raise ValueError(f"Could not instantiate LLM for {provider}: {e}")

# --- End Helper Functions ---


class AdvancedVisualizationTool(BaseMCPTool):
    """Tool for creating advanced visualizations using PandasAI based on natural language prompts."""

    def __init__(self):
        """Initialize the PandasAI advanced visualization tool."""
        super().__init__(
            # Match the 'type' used in the agent config
            name="advanced_visualization",
            description="Generates advanced data visualizations (e.g., heatmaps, 3D plots, network graphs, geospatial maps) based on a natural language prompt using PandasAI.",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV, Excel, JSON supported)."
                    },
                    "prompt": {
                        "type": "string",
                        "description": "Natural language prompt describing the desired advanced visualization (e.g., 'Heatmap of correlations', '3D scatter plot of X, Y, Z', 'Geospatial map showing locations colored by Value')."
                    },
                    "provider": {
                        "type": "string",
                        "description": "LLM provider for PandasAI (e.g., 'openai')."
                    },
                    "model": {
                        "type": "string",
                        "description": "Optional: Specific LLM model name."
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for the selected LLM provider."
                    }
                    # Removed specific plot params like plot_type, x_column, z_column etc.
                },
                "required": ["file_path", "prompt", "provider", "api_key"]
            },
            output_schema={ # Keep output schema consistent with basic visualization tool
                 "type": "object",
                 "properties": {
                     "tool_name": {"type": "string"},
                     "status": {"type": "string"},
                     "isError": {"type": "boolean"},
                     "content": {
                         "type": "array",
                         "items": {
                             "type": "object",
                             "properties": {
                                 "type": {"type": "string", "enum": ["text", "image"]},
                                 "text": {"type": "string"},
                                 "image": {
                                     "type": "object",
                                     "properties": {
                                         "url": {"type": "string", "format": "uri", "description": "Base64 encoded PNG image data URI"}
                                     }
                                 }
                             },
                             "required": ["type"]
                         }
                     },
                     "metadata": {"type": "object"}
                 }
            },
            annotations={
                "title": "Advanced Visualize (PandasAI)",
                "readOnlyHint": True,
                "openWorldHint": False # Relies on external LLM
            }
        )
        self.data_dir = "data" # Default data directory

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration (e.g., data directory).

        Args:
            config: Configuration dictionary for the tool
        """
        if "data_dir" in config:
            self.data_dir = config["data_dir"]
            logger.info(f"Set data directory to: {self.data_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with agent-aware advanced visualization capabilities.

        Args:
            arguments: Arguments for tool execution (file_path, prompt, provider, api_key, model?)

        Returns:
            Tool execution results in MCP format, potentially including a base64 image.
        """
        file_path_arg = arguments.get("file_path")
        prompt = arguments.get("prompt")
        provider = arguments.get("provider")
        api_key = arguments.get("api_key")
        model = arguments.get("model") # Optional

        # Agent context for dynamic identity detection
        user_context = arguments.get("context", {})
        agent_id = arguments.get("persona_id") or arguments.get("agent_id")

        # Detect agent identity for personalized advanced visualization
        agent_identity = await detect_agent_identity(
            agent_id=agent_id,
            context=user_context,
            intent_type="advanced_visualization"
        )

        logger.info(f"Detected agent identity: {agent_identity} for advanced visualization")

        # --- Input Validation ---
        if not file_path_arg or not prompt or not provider or not api_key:
             missing = [k for k, v in arguments.items() if k in ["file_path", "prompt", "provider", "api_key"] and not v]
             return {
                 "tool_name": self.name, "status": "error", "isError": True,
                 "content": [{"type": "text", "text": f"Error: Missing required arguments: {', '.join(missing)}"}],
                 "metadata": {"status": "error", "error_type": "missing_arguments"}
             }

        # --- Resolve File Path ---
        if not os.path.isabs(file_path_arg):
            file_path = os.path.join(self.data_dir, file_path_arg)
        else:
            file_path = file_path_arg

        # --- Load Data ---
        df = _try_load_dataframe(file_path)
        if df is None:
            return {
                "tool_name": self.name, "status": "error", "isError": True,
                "content": [{"type": "text", "text": f"Error: Could not load or data is empty in {file_path}."}],
                "metadata": {"file_path": file_path, "prompt": prompt, "status": "error", "error_type": "load_error"}
            }

        results_content = []
        results_metadata = {}
        is_error = False
        status = "success"

        try:
            # --- Instantiate PandasAI Agent ---
            llm = _get_pandasai_llm(provider, api_key, model)
            export_path = "exports/charts" # Same export path as basic viz
            os.makedirs(export_path, exist_ok=True)

            # Create agent with config
            config = {
                "llm": llm,
                "conversational": False,
                "verbose": False,
                "enforce_privacy": False,
                "save_charts": True,
                "save_charts_path": export_path
            }
            agent = Agent(df, config=config)

            # --- Enhance Prompt with Agent Style ---
            enhanced_prompt = await self._enhance_prompt_with_agent_style(prompt, agent_identity)

            # --- Run Query ---
            logger.info(f"Running PandasAI advanced visualization prompt: '{enhanced_prompt}'")
            result = agent.chat(enhanced_prompt)
            logger.info(f"PandasAI advanced visualization execution finished. Result: {result} (Type: {type(result)})")

            # --- Process Result (Identical logic to basic visualization tool) ---
            image_path = None
            if isinstance(result, str) and "exports/charts" in result and result.lower().endswith(".png"):
                 if os.path.exists(result):
                      image_path = result
                 else:
                      logger.warning(f"PandasAI returned image path '{result}' but file does not exist.")
                      results_content.append({"type": "text", "text": "Advanced chart generation was attempted, but the output file was not found."})
                      status = "partial_success"

            elif isinstance(result, str):
                 results_content.append({"type": "text", "text": f"PandasAI response: {result}"})
                 status = "success_text_response"

            elif result is None:
                 results_content.append({"type": "text", "text": "Advanced visualization request processed, but no specific chart was generated or returned."})
                 status = "success_no_output"

            else:
                 results_content.append({"type": "text", "text": f"PandasAI returned an unexpected result type: {type(result).__name__}. Result: {str(result)}"})
                 status = "unexpected_result"

            if image_path:
                try:
                    with open(image_path, "rb") as f:
                        img_bytes = f.read()
                    img_base64 = base64.b64encode(img_bytes).decode("utf-8")
                    results_content.append({
                        "type": "image",
                        "image": {"url": f"data:image/png;base64,{img_base64}"}
                    })
                    results_content.append({
                         "type": "text",
                         "text": f"Generated advanced visualization based on prompt: '{prompt}'"
                    })
                    results_metadata["image_path"] = image_path
                except Exception as img_e:
                    logger.error(f"Error reading or encoding image file {image_path}: {img_e}", exc_info=True)
                    results_content.append({"type": "text", "text": f"Error processing generated image file: {img_e}"})
                    status = "error"
                    is_error = True
                    results_metadata["error_type"] = "image_processing_error"


        except ValueError as e:
            logger.error(f"Configuration or instantiation error for PandasAI: {e}")
            results_content = [{"type": "text", "text": f"Error setting up visualization engine: {e}"}]
            results_metadata = {"status": "error", "error_type": "setup_error", "details": str(e)}
            is_error = True
            status = "error"
        except Exception as e:
            logger.error(f"Error during PandasAI advanced visualization execution: {e}", exc_info=True)
            error_detail = str(e)
            results_content = [{"type": "text", "text": f"An unexpected error occurred while generating the advanced visualization with PandasAI: {error_detail}"}]
            results_metadata = {"status": "error", "error_type": "pandasai_execution_error", "details": error_detail}
            is_error = True
            status = "error"

        if not results_content:
             results_content.append({"type": "text", "text": "No specific output generated."})
             if status == "success": status = "success_no_output"


        return {
            "tool_name": self.name,
            "status": status,
            "isError": is_error,
            "content": results_content,
            "metadata": {
                "file_path": file_path,
                "prompt": prompt,
                "implementation": "pandasai",
                "agent_identity": agent_identity,
                "agent_aware": True,
                **results_metadata
            }
        }

    async def _enhance_prompt_with_agent_style(self, prompt: str, agent_identity: str) -> str:
        """
        Enhance the advanced visualization prompt with agent-specific style and preferences.

        Args:
            prompt: Original visualization prompt
            agent_identity: Agent identity for customization

        Returns:
            Enhanced prompt with agent-specific context
        """
        try:
            # Get agent system prompt to extract visualization preferences
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract visualization style preferences from system prompt
            viz_style = await self._extract_advanced_visualization_style_from_prompt(system_prompt, agent_identity)

            # Enhance prompt based on agent style
            enhanced_prompt = self._apply_agent_style_to_advanced_visualization(prompt, viz_style, agent_identity)

            logger.info(f"Enhanced advanced visualization prompt for {agent_identity}: {enhanced_prompt[:100]}...")
            return enhanced_prompt

        except Exception as e:
            logger.warning(f"Failed to enhance prompt with agent style: {e}")
            return prompt  # Return original prompt if enhancement fails

    async def _extract_advanced_visualization_style_from_prompt(self, system_prompt: str, agent_identity: str) -> Dict[str, Any]:
        """Extract advanced visualization style preferences from agent system prompt."""
        style_preferences = {
            "complexity_level": "advanced",
            "color_scheme": "professional",
            "chart_style": "sophisticated",
            "explanation_level": "detailed",
            "focus_areas": [],
            "presentation_style": "analytical"
        }

        if not system_prompt:
            return self._get_default_advanced_visualization_style_for_agent(agent_identity)

        # Look for visualization-related patterns in the system prompt
        import re

        # Check for complexity level preferences
        if re.search(r"simple|basic|straightforward", system_prompt, re.IGNORECASE):
            style_preferences["complexity_level"] = "moderate"
        elif re.search(r"advanced|sophisticated|complex", system_prompt, re.IGNORECASE):
            style_preferences["complexity_level"] = "advanced"
        elif re.search(r"expert|cutting.edge|innovative", system_prompt, re.IGNORECASE):
            style_preferences["complexity_level"] = "expert"

        # Check for color scheme preferences
        if re.search(r"colorful|vibrant|bright", system_prompt, re.IGNORECASE):
            style_preferences["color_scheme"] = "vibrant"
        elif re.search(r"professional|corporate|business", system_prompt, re.IGNORECASE):
            style_preferences["color_scheme"] = "professional"
        elif re.search(r"minimal|clean|subtle", system_prompt, re.IGNORECASE):
            style_preferences["color_scheme"] = "minimal"

        # Check for chart style preferences
        if re.search(r"interactive|dynamic|engaging", system_prompt, re.IGNORECASE):
            style_preferences["chart_style"] = "interactive"
        elif re.search(r"detailed|comprehensive|thorough", system_prompt, re.IGNORECASE):
            style_preferences["chart_style"] = "detailed"
        elif re.search(r"elegant|refined|sophisticated", system_prompt, re.IGNORECASE):
            style_preferences["chart_style"] = "sophisticated"

        # Extract focus areas based on capabilities
        focus_areas = []
        if re.search(r"marketing|campaign|strategy", system_prompt, re.IGNORECASE):
            focus_areas.append("business_insights")
        if re.search(r"statistical|analysis|insights", system_prompt, re.IGNORECASE):
            focus_areas.append("statistical_depth")
        if re.search(r"classification|categorization", system_prompt, re.IGNORECASE):
            focus_areas.append("categorical_analysis")
        if re.search(r"patterns|trends|relationships", system_prompt, re.IGNORECASE):
            focus_areas.append("pattern_discovery")

        style_preferences["focus_areas"] = focus_areas

        return style_preferences

    def _get_default_advanced_visualization_style_for_agent(self, agent_identity: str) -> Dict[str, Any]:
        """Get default advanced visualization style for specific agent types."""
        default_styles = {
            "analyst": {
                "complexity_level": "expert",
                "color_scheme": "professional",
                "chart_style": "sophisticated",
                "explanation_level": "detailed",
                "focus_areas": ["statistical_depth", "pattern_discovery"],
                "presentation_style": "analytical"
            },
            "marketer": {
                "complexity_level": "advanced",
                "color_scheme": "vibrant",
                "chart_style": "engaging",
                "explanation_level": "business_focused",
                "focus_areas": ["business_insights", "performance_metrics"],
                "presentation_style": "business"
            },
            "classifier": {
                "complexity_level": "advanced",
                "color_scheme": "organized",
                "chart_style": "structured",
                "explanation_level": "clear",
                "focus_areas": ["categorical_analysis", "pattern_discovery"],
                "presentation_style": "organized"
            },
            "concierge": {
                "complexity_level": "moderate",
                "color_scheme": "friendly",
                "chart_style": "accessible",
                "explanation_level": "simple",
                "focus_areas": ["key_insights"],
                "presentation_style": "approachable"
            }
        }

        return default_styles.get(agent_identity, {
            "complexity_level": "advanced",
            "color_scheme": "professional",
            "chart_style": "sophisticated",
            "explanation_level": "detailed",
            "focus_areas": ["general_insights"],
            "presentation_style": "analytical"
        })

    def _apply_agent_style_to_advanced_visualization(self, prompt: str, style: Dict[str, Any], agent_identity: str) -> str:
        """Apply agent-specific style to the advanced visualization prompt."""
        enhanced_prompt = prompt

        # Add style-specific instructions based on agent identity
        style_instructions = []

        # Add complexity level instructions
        complexity_level = style.get("complexity_level", "advanced")
        if complexity_level == "expert":
            style_instructions.append("Create expert-level visualizations with advanced statistical overlays and multi-dimensional analysis.")
        elif complexity_level == "advanced":
            style_instructions.append("Use advanced visualization techniques with sophisticated chart types and detailed annotations.")
        elif complexity_level == "moderate":
            style_instructions.append("Create moderately complex visualizations that are informative yet accessible.")

        # Add color scheme instructions
        color_scheme = style.get("color_scheme", "professional")
        if color_scheme == "vibrant":
            style_instructions.append("Use vibrant, engaging colors that capture attention and convey energy.")
        elif color_scheme == "professional":
            style_instructions.append("Use professional, business-appropriate color schemes with good contrast.")
        elif color_scheme == "minimal":
            style_instructions.append("Use minimal, clean color palettes with subtle distinctions.")
        elif color_scheme == "organized":
            style_instructions.append("Use well-organized color coding that clearly distinguishes categories.")
        elif color_scheme == "friendly":
            style_instructions.append("Use warm, approachable colors that create a welcoming visual experience.")

        # Add chart style instructions
        chart_style = style.get("chart_style", "sophisticated")
        if chart_style == "interactive":
            style_instructions.append("Create interactive visualizations with hover effects and dynamic elements where possible.")
        elif chart_style == "detailed":
            style_instructions.append("Include detailed annotations, legends, and comprehensive labeling.")
        elif chart_style == "sophisticated":
            style_instructions.append("Use sophisticated chart designs with elegant styling and professional appearance.")
        elif chart_style == "structured":
            style_instructions.append("Create well-structured visualizations with clear organization and systematic layout.")
        elif chart_style == "accessible":
            style_instructions.append("Design accessible visualizations that are easy to understand for all audiences.")

        # Add explanation level instructions
        explanation_level = style.get("explanation_level", "detailed")
        if explanation_level == "detailed":
            style_instructions.append("Include detailed explanations of the visualization methodology and insights.")
        elif explanation_level == "business_focused":
            style_instructions.append("Focus explanations on business implications and actionable insights.")
        elif explanation_level == "clear":
            style_instructions.append("Provide clear, well-organized explanations of the visualization.")
        elif explanation_level == "simple":
            style_instructions.append("Use simple, accessible explanations that anyone can understand.")

        # Add focus area instructions
        focus_areas = style.get("focus_areas", [])
        if "business_insights" in focus_areas:
            style_instructions.append("Emphasize business insights and performance indicators in the visualization.")
        if "statistical_depth" in focus_areas:
            style_instructions.append("Include statistical depth with confidence intervals, significance tests, and error bars.")
        if "categorical_analysis" in focus_areas:
            style_instructions.append("Focus on categorical relationships and group comparisons.")
        if "pattern_discovery" in focus_areas:
            style_instructions.append("Highlight patterns, trends, and relationships in the data.")
        if "performance_metrics" in focus_areas:
            style_instructions.append("Emphasize key performance metrics and KPIs in the visualization.")

        # Add presentation style instructions
        presentation_style = style.get("presentation_style", "analytical")
        if presentation_style == "analytical":
            style_instructions.append("Present the visualization with analytical rigor and scientific precision.")
        elif presentation_style == "business":
            style_instructions.append("Present the visualization in a business-ready format suitable for executive presentations.")
        elif presentation_style == "organized":
            style_instructions.append("Organize the visualization presentation clearly and systematically.")
        elif presentation_style == "approachable":
            style_instructions.append("Make the visualization presentation approachable and user-friendly.")

        # Combine original prompt with style instructions
        if style_instructions:
            enhanced_prompt = f"{prompt}\n\nAdvanced Visualization Style Instructions:\n" + "\n".join([f"- {instruction}" for instruction in style_instructions])

        return enhanced_prompt

# Registration likely happens elsewhere for class-based tools in mcp/
