"""
Agent mixins for extending functionality.

This module provides reusable mixins that can be added to any agent
to extend their capabilities without requiring custom implementation.

Available Mixins:
- StreamingMixin: Provides streaming response capabilities
- ToolCompletionMixin: Universal tool completion and conversational state management
"""

from .streaming_mixin import StreamingMixin
from .tool_completion_mixin import ToolCompletionMixin

__all__ = ["StreamingMixin", "ToolCompletionMixin"]
