"""
Agent-Aware Tool Factory.

This module provides a factory system that automatically enhances existing MCP tools
with agent awareness capabilities. This ensures that even legacy tools can benefit
from the standardized agent identity integration without requiring manual updates.
"""

import logging
import inspect
from typing import Dict, Any, Type, Optional, List
from functools import wraps

from ..base import BaseMCPTool
from ..mixins.agent_aware_mixin import AgentAwareMixin
from ..enhanced_base import EnhancedMCPTool

logger = logging.getLogger(__name__)


class AgentAwareToolFactory:
    """
    Factory for creating agent-aware versions of existing MCP tools.
    
    This factory can:
    1. Automatically enhance existing tools with agent awareness
    2. Create wrapper classes that add agent capabilities
    3. Provide migration paths for legacy tools
    4. Ensure consistent agent integration across all tools
    """
    
    def __init__(self):
        """Initialize the agent-aware tool factory."""
        self.enhanced_tools_registry = {}
        self.tool_enhancement_cache = {}
    
    def enhance_tool_class(self, tool_class: Type[BaseMCPTool]) -> Type[EnhancedMCPTool]:
        """
        Enhance an existing tool class with agent awareness.
        
        Args:
            tool_class: Original tool class to enhance
            
        Returns:
            Enhanced tool class with agent awareness
        """
        # Check cache first
        class_name = tool_class.__name__
        if class_name in self.tool_enhancement_cache:
            return self.tool_enhancement_cache[class_name]
        
        # Check if already enhanced
        if issubclass(tool_class, EnhancedMCPTool):
            logger.info(f"Tool {class_name} is already enhanced")
            return tool_class
        
        # Create enhanced version
        enhanced_class = self._create_enhanced_class(tool_class)
        
        # Cache the result
        self.tool_enhancement_cache[class_name] = enhanced_class
        
        logger.info(f"Enhanced tool class: {class_name}")
        return enhanced_class
    
    def enhance_tool_instance(self, tool_instance: BaseMCPTool) -> EnhancedMCPTool:
        """
        Enhance an existing tool instance with agent awareness.
        
        Args:
            tool_instance: Original tool instance to enhance
            
        Returns:
            Enhanced tool instance with agent awareness
        """
        # Get enhanced class
        enhanced_class = self.enhance_tool_class(type(tool_instance))
        
        # Create enhanced instance with same configuration
        enhanced_instance = enhanced_class(
            name=tool_instance.name,
            description=tool_instance.description,
            input_schema=tool_instance.input_schema,
            annotations=getattr(tool_instance, 'annotations', None)
        )
        
        # Copy any additional attributes
        for attr_name in dir(tool_instance):
            if not attr_name.startswith('_') and not callable(getattr(tool_instance, attr_name)):
                try:
                    setattr(enhanced_instance, attr_name, getattr(tool_instance, attr_name))
                except:
                    pass
        
        logger.info(f"Enhanced tool instance: {tool_instance.name}")
        return enhanced_instance
    
    def _create_enhanced_class(self, original_class: Type[BaseMCPTool]) -> Type[EnhancedMCPTool]:
        """
        Create an enhanced version of the original tool class.
        
        Args:
            original_class: Original tool class
            
        Returns:
            Enhanced tool class
        """
        class_name = f"Enhanced{original_class.__name__}"
        
        # Get original execute method
        original_execute = original_class.execute
        
        # Create enhanced class
        class EnhancedToolClass(EnhancedMCPTool):
            """Dynamically created enhanced tool class."""
            
            def __init__(self, *args, **kwargs):
                # Extract initialization parameters
                if args:
                    # If positional args provided, use them
                    name = args[0] if len(args) > 0 else getattr(original_class, 'name', 'unknown')
                    description = args[1] if len(args) > 1 else getattr(original_class, 'description', '')
                    input_schema = args[2] if len(args) > 2 else getattr(original_class, 'input_schema', {})
                    annotations = args[3] if len(args) > 3 else getattr(original_class, 'annotations', None)
                else:
                    # Extract from kwargs or class attributes
                    name = kwargs.get('name', getattr(original_class, 'name', 'unknown'))
                    description = kwargs.get('description', getattr(original_class, 'description', ''))
                    input_schema = kwargs.get('input_schema', getattr(original_class, 'input_schema', {}))
                    annotations = kwargs.get('annotations', getattr(original_class, 'annotations', None))
                
                # Initialize enhanced base
                super().__init__(
                    name=name,
                    description=description,
                    input_schema=input_schema,
                    annotations=annotations
                )
                
                # Create original instance for delegation
                try:
                    self._original_instance = original_class()
                except:
                    self._original_instance = None
            
            async def _execute_with_agent_awareness(
                self, 
                arguments: Dict[str, Any], 
                agent_identity: str, 
                agent_capabilities: Dict[str, Any]
            ) -> Dict[str, Any]:
                """Execute with agent awareness using original logic."""
                try:
                    # Add agent context to arguments
                    enhanced_arguments = arguments.copy()
                    enhanced_arguments['agent_identity'] = agent_identity
                    enhanced_arguments['agent_capabilities'] = agent_capabilities
                    
                    # Execute original method
                    if self._original_instance:
                        result = await original_execute(self._original_instance, enhanced_arguments)
                    else:
                        # Fallback: create temporary instance
                        temp_instance = original_class()
                        result = await original_execute(temp_instance, enhanced_arguments)
                    
                    # Ensure result is in correct format
                    if not isinstance(result, dict):
                        result = {
                            "content": [{
                                "type": "text",
                                "text": str(result)
                            }]
                        }
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"Error in enhanced execution for {self.name}: {e}")
                    return {
                        "isError": True,
                        "content": [{
                            "type": "text",
                            "text": f"Error in enhanced execution: {str(e)}"
                        }]
                    }
        
        # Set class name and module
        EnhancedToolClass.__name__ = class_name
        EnhancedToolClass.__qualname__ = class_name
        EnhancedToolClass.__module__ = original_class.__module__
        
        return EnhancedToolClass
    
    def create_agent_aware_wrapper(self, tool_instance: BaseMCPTool) -> 'AgentAwareWrapper':
        """
        Create an agent-aware wrapper for an existing tool instance.
        
        Args:
            tool_instance: Original tool instance
            
        Returns:
            Agent-aware wrapper
        """
        return AgentAwareWrapper(tool_instance, self)
    
    def batch_enhance_tools(self, tools: List[BaseMCPTool]) -> List[EnhancedMCPTool]:
        """
        Enhance multiple tools in batch.
        
        Args:
            tools: List of tools to enhance
            
        Returns:
            List of enhanced tools
        """
        enhanced_tools = []
        
        for tool in tools:
            try:
                enhanced_tool = self.enhance_tool_instance(tool)
                enhanced_tools.append(enhanced_tool)
            except Exception as e:
                logger.error(f"Failed to enhance tool {tool.name}: {e}")
                # Keep original tool if enhancement fails
                enhanced_tools.append(tool)
        
        logger.info(f"Enhanced {len(enhanced_tools)} tools in batch")
        return enhanced_tools
    
    def get_enhancement_status(self) -> Dict[str, Any]:
        """
        Get status of tool enhancements.
        
        Returns:
            Enhancement status information
        """
        return {
            "enhanced_classes": len(self.tool_enhancement_cache),
            "registered_tools": len(self.enhanced_tools_registry),
            "cache_size": len(self.tool_enhancement_cache),
            "enhanced_class_names": list(self.tool_enhancement_cache.keys())
        }
    
    def clear_cache(self):
        """Clear the enhancement cache."""
        self.tool_enhancement_cache.clear()
        logger.info("Tool enhancement cache cleared")


class AgentAwareWrapper:
    """
    Wrapper that adds agent awareness to existing tool instances.
    
    This wrapper provides a non-invasive way to add agent capabilities
    to existing tools without modifying their original implementation.
    """
    
    def __init__(self, original_tool: BaseMCPTool, factory: AgentAwareToolFactory):
        """
        Initialize the agent-aware wrapper.
        
        Args:
            original_tool: Original tool instance
            factory: Tool factory for enhancement
        """
        self.original_tool = original_tool
        self.factory = factory
        self.agent_mixin = AgentAwareMixin()
        
        # Copy tool attributes
        self.name = original_tool.name
        self.description = original_tool.description
        self.input_schema = original_tool.input_schema
        self.annotations = getattr(original_tool, 'annotations', None)
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the wrapped tool with agent awareness.
        
        Args:
            arguments: Tool execution arguments
            
        Returns:
            Enhanced tool execution results
        """
        try:
            # Detect agent identity
            agent_identity = await self.agent_mixin.detect_agent_identity(arguments, self.name)
            
            # Get agent capabilities
            agent_capabilities = await self.agent_mixin.get_agent_capabilities(agent_identity)
            
            # Add agent context to arguments
            enhanced_arguments = arguments.copy()
            enhanced_arguments['agent_identity'] = agent_identity
            enhanced_arguments['agent_capabilities'] = agent_capabilities
            
            # Execute original tool
            result = await self.original_tool.execute(enhanced_arguments)
            
            # Enhance result with agent metadata
            enhanced_result = self.agent_mixin._enhance_result_with_agent_metadata(
                result, agent_identity, agent_capabilities
            )
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error in agent-aware wrapper for {self.name}: {e}")
            return {
                "isError": True,
                "content": [{
                    "type": "text",
                    "text": f"Error in agent-aware execution: {str(e)}"
                }],
                "metadata": {
                    "tool_name": self.name,
                    "wrapper_error": True,
                    "error_type": type(e).__name__
                }
            }
    
    def __getattr__(self, name):
        """Delegate attribute access to the original tool."""
        return getattr(self.original_tool, name)
    
    def __repr__(self) -> str:
        """String representation of the wrapper."""
        return f"AgentAwareWrapper({self.original_tool})"


# Global factory instance
agent_aware_factory = AgentAwareToolFactory()


def make_agent_aware(tool_class_or_instance):
    """
    Decorator/function to make a tool agent-aware.
    
    Can be used as:
    1. Class decorator: @make_agent_aware
    2. Function call: enhanced_tool = make_agent_aware(original_tool)
    
    Args:
        tool_class_or_instance: Tool class or instance to enhance
        
    Returns:
        Enhanced tool class or instance
    """
    if inspect.isclass(tool_class_or_instance):
        # Class enhancement
        return agent_aware_factory.enhance_tool_class(tool_class_or_instance)
    else:
        # Instance enhancement
        return agent_aware_factory.enhance_tool_instance(tool_class_or_instance)


def get_agent_aware_factory() -> AgentAwareToolFactory:
    """Get the global agent-aware tool factory."""
    return agent_aware_factory
