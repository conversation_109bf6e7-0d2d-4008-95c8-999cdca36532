/**
 * Test file for BusinessProfileSelector component
 * Tests the fix for cmdk iteration error
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { BusinessProfileSelector } from './BusinessProfileSelector';
import { businessProfileApi } from '@/lib/businessProfileApi';

// Mock the business profile API
jest.mock('@/lib/businessProfileApi');
const mockBusinessProfileApi = businessProfileApi as jest.Mocked<typeof businessProfileApi>;

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

describe('BusinessProfileSelector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle undefined API response gracefully', async () => {
    // Mock API to return undefined (simulating the error condition)
    mockBusinessProfileApi.listProfiles.mockResolvedValue(undefined as any);

    // Render the component
    const { container } = render(<BusinessProfileSelector />);

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading profiles...')).not.toBeInTheDocument();
    });

    // Component should render without throwing the cmdk iteration error
    expect(container).toBeInTheDocument();
    expect(screen.getByText('Select business profile')).toBeInTheDocument();
  });

  it('should handle dropdown click without errors', async () => {
    // Mock API to return valid profiles
    const mockProfiles = [
      {
        id: '1',
        user_id: 1,
        name: 'Test Profile',
        is_active: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      },
    ];

    mockBusinessProfileApi.listProfiles.mockResolvedValue({
      profiles: mockProfiles,
      total: 1,
      active_profile_id: '1',
    });

    // Render the component
    const { container } = render(<BusinessProfileSelector />);

    // Wait for profiles to load
    await waitFor(() => {
      expect(screen.getByText('Test Profile')).toBeInTheDocument();
    });

    // Click the dropdown button - should not throw errors
    const dropdownButton = screen.getByRole('combobox');
    expect(() => {
      dropdownButton.click();
    }).not.toThrow();
  });

  it('should handle malformed API response gracefully', async () => {
    // Mock API to return malformed response
    mockBusinessProfileApi.listProfiles.mockResolvedValue({ invalid: 'response' } as any);

    // Render the component
    const { container } = render(<BusinessProfileSelector />);

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading profiles...')).not.toBeInTheDocument();
    });

    // Component should render without throwing errors
    expect(container).toBeInTheDocument();
    expect(screen.getByText('Select business profile')).toBeInTheDocument();
  });

  it('should handle API error gracefully', async () => {
    // Mock API to throw an error
    mockBusinessProfileApi.listProfiles.mockRejectedValue(new Error('API Error'));

    // Render the component
    const { container } = render(<BusinessProfileSelector />);

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading profiles...')).not.toBeInTheDocument();
    });

    // Component should render without throwing errors
    expect(container).toBeInTheDocument();
    expect(screen.getByText('Select business profile')).toBeInTheDocument();
  });

  it('should render profiles correctly when API returns valid data', async () => {
    // Mock API to return valid profiles
    const mockProfiles = [
      {
        id: '1',
        user_id: 1,
        name: 'Test Profile',
        is_active: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      },
    ];

    mockBusinessProfileApi.listProfiles.mockResolvedValue({
      profiles: mockProfiles,
      total: 1,
      active_profile_id: '1',
    });

    // Render the component
    render(<BusinessProfileSelector />);

    // Wait for profiles to load
    await waitFor(() => {
      expect(screen.getByText('Test Profile')).toBeInTheDocument();
    });
  });
});
