/**
 * Service Worker for Phase 1 Performance Optimizations
 * 
 * Implements L1 browser caching strategy:
 * - Cache static assets (JS, CSS, images)
 * - Cache API responses with intelligent invalidation
 * - Implement stale-while-revalidate strategy
 * - Background sync for offline support
 */

const CACHE_NAME = 'datagenius-v1.0.0';
const STATIC_CACHE = 'datagenius-static-v1.0.0';
const API_CACHE = 'datagenius-api-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico'
];

// API endpoints to cache
const CACHEABLE_API_PATTERNS = [
  /\/api\/dashboards/,
  /\/api\/performance/,
  /\/api\/data-sources/,
  /\/api\/widgets/,
  /\/api\/auth\/me/
];

// API endpoints to never cache
const NEVER_CACHE_PATTERNS = [
  /\/api\/auth\/login/,
  /\/api\/auth\/logout/,
  /\/api\/auth\/refresh/,
  /\/ws\//,
  /real-time/
];

// Cache durations (in seconds)
const CACHE_DURATIONS = {
  static: 86400, // 24 hours
  api: 1800,     // 30 minutes
  images: 604800 // 7 days
};

self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE).then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      }),
      
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== STATIC_CACHE && 
                cacheName !== API_CACHE) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  // Handle different types of requests
  if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
  } else if (isApiRequest(request)) {
    event.respondWith(handleApiRequest(request));
  } else {
    event.respondWith(handleOtherRequest(request));
  }
});

/**
 * Check if request is for a static asset
 */
function isStaticAsset(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  return pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/) ||
         pathname === '/' ||
         pathname === '/index.html';
}

/**
 * Check if request is for API
 */
function isApiRequest(request) {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/');
}

/**
 * Handle static asset requests with cache-first strategy
 */
async function handleStaticAsset(request) {
  try {
    // Try cache first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      // Check if cache is still fresh
      const cacheDate = new Date(cachedResponse.headers.get('date') || 0);
      const now = new Date();
      const age = (now.getTime() - cacheDate.getTime()) / 1000;
      
      if (age < CACHE_DURATIONS.static) {
        return cachedResponse;
      }
    }
    
    // Fetch from network
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.error('Static asset fetch failed:', error);
    
    // Return cached version if available
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      return new Response('Offline', { status: 503 });
    }
    
    throw error;
  }
}

/**
 * Handle API requests with stale-while-revalidate strategy
 */
async function handleApiRequest(request) {
  const url = new URL(request.url);
  
  // Never cache certain endpoints
  if (NEVER_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    return fetch(request);
  }
  
  // Only cache certain API endpoints
  if (!CACHEABLE_API_PATTERNS.some(pattern => pattern.test(url.pathname))) {
    return fetch(request);
  }
  
  try {
    const cache = await caches.open(API_CACHE);
    const cachedResponse = await cache.match(request);
    
    // Start network request
    const networkPromise = fetch(request).then(async (networkResponse) => {
      if (networkResponse.ok) {
        // Clone and cache the response
        const responseClone = networkResponse.clone();
        await cache.put(request, responseClone);
      }
      return networkResponse;
    });
    
    // If we have a cached response, return it immediately
    if (cachedResponse) {
      // Check cache freshness
      const cacheDate = new Date(cachedResponse.headers.get('date') || 0);
      const now = new Date();
      const age = (now.getTime() - cacheDate.getTime()) / 1000;
      
      if (age < CACHE_DURATIONS.api) {
        // Cache is fresh, return it and update in background
        networkPromise.catch(console.error); // Update cache in background
        return cachedResponse;
      }
    }
    
    // Wait for network response
    return await networkPromise;
    
  } catch (error) {
    console.error('API request failed:', error);
    
    // Return cached version if available
    const cache = await caches.open(API_CACHE);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw error;
  }
}

/**
 * Handle other requests (navigation, etc.)
 */
async function handleOtherRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    // For navigation requests, try to return cached index.html
    if (request.mode === 'navigate') {
      const cache = await caches.open(STATIC_CACHE);
      const cachedIndex = await cache.match('/index.html');
      if (cachedIndex) {
        return cachedIndex;
      }
    }
    
    throw error;
  }
}

/**
 * Background sync for offline actions
 */
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  try {
    // Sync any pending actions when back online
    console.log('Background sync triggered');
    
    // You can implement specific sync logic here
    // For example, sync dashboard changes, user preferences, etc.
    
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

/**
 * Handle push notifications (future enhancement)
 */
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    
    event.waitUntil(
      self.registration.showNotification(data.title, {
        body: data.body,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'datagenius-notification'
      })
    );
  }
});

/**
 * Handle notification clicks
 */
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  event.waitUntil(
    clients.openWindow('/')
  );
});

/**
 * Message handling for cache management
 */
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    event.waitUntil(clearAllCaches());
  } else if (event.data && event.data.type === 'GET_CACHE_STATS') {
    event.waitUntil(getCacheStats().then(stats => {
      event.ports[0].postMessage(stats);
    }));
  }
});

async function clearAllCaches() {
  const cacheNames = await caches.keys();
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
  console.log('All caches cleared');
}

async function getCacheStats() {
  const cacheNames = await caches.keys();
  const stats = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    stats[cacheName] = keys.length;
  }
  
  return stats;
}
