"""
Task selector component for the Datagenius agent system.

This module provides a component for guiding users through task selection.
"""

import logging
import re
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent

logger = logging.getLogger(__name__)


class TaskSelectorComponent(AgentComponent):
    """Component for guiding users through task selection."""

    def __init__(self):
        """Initialize the task selector component."""
        super().__init__()
        self.tasks = {}
        self.task_descriptions = {}

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        # Register tasks from configuration
        if "tasks" in config:
            for task_config in config["tasks"]:
                task_id = task_config.get("id")
                task_name = task_config.get("name", task_id)
                task_description = task_config.get("description", "")

                if not task_id:
                    logger.warning("Task configuration missing 'id'")
                    continue

                # Register the task
                self.tasks[task_id] = {
                    "name": task_name,
                    "description": task_description,
                    "prompt_template": task_config.get("prompt_template"),
                    "required_data": task_config.get("required_data", []),
                    "metadata": task_config.get("metadata", {})
                }

                # Add to task descriptions for display
                self.task_descriptions[task_id] = f"{task_name}: {task_description}"

                logger.info(f"Registered task '{task_id}': {task_name}")
        else:
            # Default marketing tasks if none provided
            default_tasks = [
                {
                    "id": "marketing_strategy",
                    "name": "Marketing Strategy",
                    "description": "Create a comprehensive marketing strategy for your business"
                },
                {
                    "id": "campaign_strategy",
                    "name": "Campaign Strategy",
                    "description": "Develop campaign concepts and plans for specific initiatives"
                },
                {
                    "id": "social_media",
                    "name": "Social Media Content Strategy",
                    "description": "Create a social media content strategy and posting plan"
                },
                {
                    "id": "seo_optimization",
                    "name": "SEO Optimization Strategy",
                    "description": "Optimize your content and website for search engines"
                },
                {
                    "id": "post_composer",
                    "name": "Post Composer",
                    "description": "Generate engaging social media posts for your brand"
                }
            ]

            for task in default_tasks:
                task_id = task["id"]
                self.tasks[task_id] = task
                self.task_descriptions[task_id] = f"{task['name']}: {task['description']}"
                logger.info(f"Registered default task '{task_id}': {task['name']}")

        logger.info(f"Initialized task selector with {len(self.tasks)} tasks")

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        message_lower_strip = (context.message or "").lower().strip()
        component_specific_data = context.component_data.setdefault(self.name, {})

        # Check if this is the first message in the conversation
        # is_first_message should ideally be in initial_context or metadata
        is_first_message = context.metadata.get("is_first_message", False)
        if not context.conversation_id or not context.initial_context.get("conversation_history"): # Another way to infer first message
            is_first_message = True

        # If this is the first message, present task options
        if is_first_message:
            task_list = "\n".join([f"{i+1}. {desc}" for i, desc in enumerate(self.task_descriptions.values())])

            response_text = f"""
Welcome to the Marketing AI! I'm here to help you with your marketing needs.

Please select one of the following tasks:

{task_list}

You can select a task by number or name. For example, "1" or "Marketing Strategy".
"""
            context.response = response_text
            context.metadata["awaiting_task_selection"] = True
            return context

        # Check if we're awaiting task selection
        if context.metadata.get("awaiting_task_selection", False):
            selected_task_id = self._match_task(message_lower_strip)

            if selected_task_id:
                task = self.tasks[selected_task_id]

                component_specific_data["selected_task"] = {
                    "id": selected_task_id,
                    "name": task["name"],
                    "description": task["description"],
                    "required_data": task.get("required_data", [])
                }
                context.metadata["selected_task_id"] = selected_task_id # Also in metadata for other components

                response_text = ""
                if task.get("required_data"):
                    data_requirements = ", ".join(task["required_data"])
                    response_text = f"""
Great! You've selected: {task["name"]}

For this task, I'll need some information about your business. Please provide:
- {data_requirements}

You can also attach relevant files that contain this information, and I'll analyze them for you.
"""
                else:
                    response_text = f"""
Great! You've selected: {task["name"]}

Let me help you with that. If you have any relevant files or data, please attach them now, and I'll analyze them to provide better recommendations.

Otherwise, please tell me about your business, target audience, and goals so I can tailor my recommendations.
"""
                context.response = response_text
                context.metadata["awaiting_data"] = True
                context.metadata["awaiting_task_selection"] = False
                return context
            else:
                task_list = "\n".join([f"{i+1}. {desc}" for i, desc in enumerate(self.task_descriptions.values())])
                response_text = f"""
I'm not sure which task you're selecting. Please choose one of the following:

{task_list}

You can select a task by number or name.
"""
                context.response = response_text
                context.metadata["awaiting_task_selection"] = True # Keep awaiting
                return context

        # Check if we're awaiting data but the user has already selected a task
        if context.metadata.get("awaiting_data", False) and component_specific_data.get("selected_task"):
            # Pass through, let other components handle data provision/retrieval
            return context

        # If we get here, check if the message implies a task selection without explicit prompt
        selected_task_id = self._match_task(message_lower_strip)
        if selected_task_id:
            task = self.tasks[selected_task_id]

            component_specific_data["selected_task"] = {
                "id": selected_task_id,
                "name": task["name"],
                "description": task["description"],
                "required_data": task.get("required_data", [])
            }
            context.metadata["selected_task_id"] = selected_task_id

            context.response = f"""
I'll help you with: {task["name"]}

Let me analyze your request and any attached data to provide the best recommendations.
"""
            # Potentially set awaiting_data to true if not already handled
            if not context.metadata.get("awaiting_data"):
                 context.metadata["awaiting_data"] = True # Assume data might be needed or provided next
            return context

        # If no task selection is detected, just pass through
        return context

    def _match_task(self, message: str) -> Optional[str]:
        """
        Match a message to a task.

        Args:
            message: The message to match

        Returns:
            Task ID if matched, None otherwise
        """
        # Check if the message is a number
        if message.isdigit():
            task_index = int(message) - 1
            if 0 <= task_index < len(self.tasks):
                return list(self.tasks.keys())[task_index]

        # Check if the message contains a task name
        message_lower = message.lower()
        for task_id, task in self.tasks.items():
            task_name_lower = task["name"].lower()
            if task_name_lower in message_lower or task_id.lower() in message_lower:
                return task_id

        # No match found
        return None

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        return ["task_selection"] + [f"task:{task_id}" for task_id in self.tasks.keys()]
