"""
Component for managing hierarchical agent teams.
"""

import logging
import json
import time
import uuid
import re
import sys
from typing import Dict, Any, List, Optional, Tuple, Set
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent
from ..registry import AgentRegistry

logger = logging.getLogger(__name__)


class TeamRole:
    """Defines a role within a hierarchical agent team."""

    MANAGER = "manager"
    SPECIALIST = "specialist"
    ASSISTANT = "assistant"
    COORDINATOR = "coordinator"
    EXECUTOR = "executor"
    REVIEWER = "reviewer"
    FALLBACK = "fallback"


class TeamManagerComponent(AgentComponent):
    """
    Manages hierarchical agent teams, enabling complex workflows with specialized roles,
    delegation, oversight, and fallback mechanisms.
    """

    def __init__(self):
        """Initialize the TeamManagerComponent."""
        super().__init__()
        self.teams = {}  # Store team configurations
        self.active_teams = {}  # Track active teams by conversation
        # self.role_assignments = {} # This seems to be managed by RoleAssignmentComponent, consider removing if redundant
        self.task_registry = {}  # Track tasks assigned to team members

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"TeamManagerComponent '{self.name}' initialized.")
        self.agent_registry = AgentRegistry
        self.team_ttl = config.get("team_ttl", 3600)  # Default TTL: 1 hour
        self.max_team_size = config.get("max_team_size", 5)
        self.enable_auto_team_formation = config.get("enable_auto_team_formation", True)
        self.team_formation_threshold = config.get("team_formation_threshold", 0.7)

        self.team_templates = config.get("team_templates", {})
        if self.team_templates:
            logger.info(f"Loaded {len(self.team_templates)} team templates")

        self.role_capabilities = config.get("role_capabilities", {
            TeamRole.MANAGER: ["oversight", "delegation", "planning"],
            TeamRole.SPECIALIST: ["domain_expertise", "specialized_tasks"],
            TeamRole.ASSISTANT: ["support", "information_gathering"],
            TeamRole.COORDINATOR: ["coordination", "communication"],
            TeamRole.EXECUTOR: ["task_execution", "implementation"],
            TeamRole.REVIEWER: ["quality_control", "verification"],
            TeamRole.FALLBACK: ["error_handling", "recovery"]
        })

        self.persona_role_mappings = config.get("persona_role_mappings", {
            "concierge-agent": [TeamRole.MANAGER, TeamRole.COORDINATOR],
            "composable-analysis-ai": [TeamRole.SPECIALIST, TeamRole.EXECUTOR],
            "composable-marketing-ai": [TeamRole.SPECIALIST, TeamRole.EXECUTOR],
            "composable-classifier-ai": [TeamRole.SPECIALIST, TeamRole.ASSISTANT]
        })
        self._schedule_cleanup()

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        user_message = context.message or ""
        conversation_id = str(context.conversation_id)
        current_persona = context.agent_config.id if context.agent_config else "unknown"

        logger.debug(f"TeamManagerComponent processing for conversation {conversation_id}")

        component_specific_data = context.component_data.setdefault(self.name, {})
        team_data = component_specific_data.setdefault("team_info", { # Renamed from "team" to avoid conflict with context.team if it existed
            "id": None,
            "role": None,
            "tasks": [],
            "hierarchy": {},
            "members": []
        })

        active_team = self._get_active_team(conversation_id)
        if active_team:
            team_data["id"] = active_team["team_id"]
            team_data["role"] = self._get_persona_role(current_persona, active_team)
            team_data["tasks"] = self._get_persona_tasks(conversation_id, current_persona)
            team_data["hierarchy"] = active_team["hierarchy"]
            team_data["members"] = active_team["members"]
            context.metadata["active_team_details"] = { # More descriptive key
                "id": active_team["team_id"],
                "role": team_data["role"],
                "name": active_team["name"]
            }
            logger.debug(f"Context updated with active team {active_team['team_id']}")

        if self._is_team_formation_command(user_message):
            team_template, task_desc = self._extract_team_formation_info(user_message)
            if team_template:
                team_id = await self._form_team(conversation_id, current_persona, team_template, task_desc)
                newly_active_team = self._get_active_team(conversation_id)
                if newly_active_team:
                    team_data["id"] = newly_active_team["team_id"]
                    team_data["role"] = self._get_persona_role(current_persona, newly_active_team)
                    team_data["hierarchy"] = newly_active_team["hierarchy"]
                    team_data["members"] = newly_active_team["members"]

                    formation_msg = f"I've formed a team ('{newly_active_team['name']}') to help with: {task_desc}."
                    context.metadata["team_formation_result"] = {"success": True, "team_id": team_id, "team_name": newly_active_team["name"], "message": formation_msg}
                    context.response = formation_msg
                    logger.info(f"Formed team {team_id} ({newly_active_team['name']})")
                else:
                    msg = "I couldn't form a team with the specified parameters."
                    context.metadata["team_formation_result"] = {"success": False, "message": msg}
                    context.response = msg
                    context.add_error(self.name, "team_formation_failed", {"template": team_template, "task": task_desc})
                    logger.warning(f"Team formation failed for template {team_template}")
            else:
                context.response = "I could not find a suitable team template for your request."
                context.add_error(self.name, "team_template_not_found", {"user_message": user_message})
                logger.warning(f"Team formation command detected but no template found: {user_message}")

        elif self._is_task_assignment_command(user_message):
            target_persona, task_desc = self._extract_task_assignment_info(user_message)
            current_active_team = self._get_active_team(conversation_id) # re-fetch
            if target_persona and current_active_team:
                task_id = await self._assign_task(conversation_id, current_persona, target_persona, task_desc)
                if task_id:
                    msg = f"I've assigned the task '{task_desc[:30]}...' to {target_persona} (Task ID: {task_id})."
                    context.metadata["task_assignment_result"] = {"success": True, "task_id": task_id, "target_persona": target_persona, "message": msg}
                    context.response = msg
                    logger.info(f"Assigned task {task_id} to {target_persona}")
                else:
                    msg = f"I couldn't assign the task to {target_persona}. They might not be part of the team or no roles are available."
                    context.metadata["task_assignment_result"] = {"success": False, "target_persona": target_persona, "message": msg}
                    context.response = msg
                    context.add_error(self.name, "task_assignment_internal_fail", {"target": target_persona, "team_id": current_active_team["team_id"]})
                    logger.warning(f"Internal task assignment to {target_persona} failed.")
            else:
                msg = "I couldn't assign the task. Ensure a team is active and target persona is valid."
                context.metadata["task_assignment_result"] = {"success": False, "message": msg}
                context.response = msg
                context.add_error(self.name, "task_assignment_precondition_fail", {"target": target_persona, "team_active": bool(current_active_team)})
                logger.warning(f"Task assignment preconditions not met (target: {target_persona}, team_active: {bool(current_active_team)})")

        elif self._is_task_status_update_command(user_message):
            task_id, status, result_text = self._extract_task_status_update_info(user_message) # Renamed result
            current_active_team = self._get_active_team(conversation_id) # re-fetch
            if task_id and current_active_team:
                success = await self._update_task_status(conversation_id, current_persona, task_id, status, result_text)
                msg = f"Task {task_id} status updated to {status}." if success else f"Could not update status for task {task_id}."
                context.metadata["task_status_update_result"] = {"success": success, "task_id": task_id, "status": status, "message": msg}
                context.response = msg
                if not success:
                    context.add_error(self.name, "task_status_update_failed", {"task_id": task_id, "status": status})
                logger.info(f"Task {task_id} status update to {status}: Success - {success}")
            else:
                msg = "I couldn't update task status. Ensure team is active and task ID is valid."
                context.metadata["task_status_update_result"] = {"success": False, "message": msg}
                context.response = msg
                context.add_error(self.name, "task_status_update_precondition_fail", {"task_id": task_id, "team_active": bool(current_active_team)})
                logger.warning(f"Task status update preconditions not met (task_id: {task_id}, team_active: {bool(current_active_team)})")

        current_active_team = self._get_active_team(conversation_id) # re-fetch
        if self.enable_auto_team_formation and not current_active_team and \
           not self._is_team_formation_command(user_message) and \
           not self._is_task_assignment_command(user_message) and \
           not self._is_task_status_update_command(user_message): # Avoid triggering if other commands matched
            team_needed, template_name, reason = self._detect_team_need(user_message, context)
            if team_needed and template_name:
                suggestion_msg = f"This task might benefit from a team. Would you like to form a '{template_name}' team?"
                context.metadata["auto_team_suggestion"] = {"template": template_name, "reason": reason, "message": suggestion_msg}
                # context.response = suggestion_msg # Optionally set response to ask user
                logger.info(f"Auto team suggestion: {template_name} for reason: {reason}")

        return context

    def _is_team_formation_command(self, message: str) -> bool:
        formation_keywords = ["form a team", "create a team", "assemble a team", "put together a team", "organize a team", "build a team"]
        return any(keyword in message.lower() for keyword in formation_keywords)

    def _extract_team_formation_info(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        message_lower = message.lower()
        template_name = None
        for name_key in self.team_templates.keys(): # Renamed name to name_key
            if name_key.lower() in message_lower:
                template_name = name_key
                break
        if not template_name and self.team_templates:
            template_name = list(self.team_templates.keys())[0]

        task_description = message_lower # Default to full message
        if " for " in message_lower: # More specific split
            task_description = message_lower.split(" for ", 1)[1].strip()
        elif " to " in message_lower: # More specific split
             # Ensure "to" is not part of a persona name or template name before splitting
            if not any(f" {template.lower()} to " in message_lower for template in self.team_templates.keys()):
                 task_description = message_lower.split(" to ", 1)[1].strip()
        return template_name, task_description

    async def _form_team(self, conversation_id: str, initiator_persona: str, template_name: str, task_description: str) -> str:
        team_id = str(uuid.uuid4())
        template = self.team_templates.get(template_name, {
            "roles": {TeamRole.MANAGER: 1, TeamRole.SPECIALIST: 1}, # Simplified default
            "hierarchy": {TeamRole.MANAGER: [TeamRole.SPECIALIST]}
        })
        team = {
            "team_id": team_id, "name": f"{template_name.capitalize()} Team",
            "initiator": initiator_persona, "created_at": time.time(),
            "task_description": task_description, "members": [],
            "roles": template["roles"].copy(), "hierarchy": template["hierarchy"].copy(),
            "conversation_id": conversation_id
        }
        initiator_role = self._get_best_role_for_persona(initiator_persona, set(template["roles"].keys())) # Ensure set for available_roles
        team["members"].append({"persona_id": initiator_persona, "role": initiator_role, "joined_at": time.time()})
        if initiator_role in team["roles"]:
            team["roles"][initiator_role] -= 1
            if team["roles"][initiator_role] <= 0: del team["roles"][initiator_role]
        self.teams[team_id] = team
        self.active_teams.setdefault(conversation_id, []).append(team_id)
        logger.info(f"Formed team {team_id} for conversation {conversation_id}")
        return team_id

    def _get_best_role_for_persona(self, persona_id: str, available_roles: Set[str]) -> str:
        persona_roles = self.persona_role_mappings.get(persona_id, [])
        for role_option in persona_roles: # Renamed role
            if role_option in available_roles: return role_option
        if available_roles: return list(available_roles)[0] # Return first available if no affinity match
        return TeamRole.MANAGER # Fallback

    def _get_active_team(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        team_ids = self.active_teams.get(conversation_id, [])
        if not team_ids: return None
        return self.teams.get(team_ids[-1])

    def _get_persona_role(self, persona_id: str, team: Dict[str, Any]) -> Optional[str]:
        for member in team.get("members", []): # Added .get for safety
            if member.get("persona_id") == persona_id: return member.get("role")
        return None

    def _is_task_assignment_command(self, message: str) -> bool:
        assignment_keywords = ["assign task", "give task", "delegate task", "assign to", "give to", "delegate to"]
        return any(keyword in message.lower() for keyword in assignment_keywords)

    def _extract_task_assignment_info(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        message_lower = message.lower()
        target_persona = None
        # A more robust way to extract persona might be needed, e.g., checking against known personas
        # For now, simple keyword check
        persona_keywords = self.agent_registry.list_registered_personas() # Get actual persona IDs
        for pk in persona_keywords:
             if pk.lower() in message_lower: # Check if a known persona ID is in the message
                 target_persona = pk
                 break

        if not target_persona: # Fallback to general keywords if no specific ID found
            general_persona_terms = ["analyst", "marketer", "classifier", "concierge"]
            for term in general_persona_terms:
                if term in message_lower:
                    # This is ambiguous, ideally map term to a specific persona_id if possible
                    # For now, we'll use the term, but this might need refinement
                    target_persona = term
                    break

        if not target_persona: return None, None

        task_description = message_lower
        # Try to extract task description more cleanly
        split_keywords = [" to ", " the task ", " task ", " for "]
        for skw in split_keywords:
            if skw in message_lower:
                parts = message_lower.split(skw, 1)
                # Ensure the target_persona is not in the part we consider the task description
                if target_persona and target_persona.lower() not in parts[0].lower(): # Check if target is before the keyword
                     if len(parts) > 1 and parts[1].strip():
                        task_description = parts[1].strip()
                        break
        return target_persona, task_description


    async def _assign_task(self, conversation_id: str, assigner_persona: str, target_persona: str, task_description: str) -> str:
        task_id = str(uuid.uuid4())
        team = self._get_active_team(conversation_id)
        if not team:
            logger.warning(f"No active team for convo {conversation_id} to assign task.")
            return ""

        target_is_member = any(member.get("persona_id") == target_persona for member in team.get("members", []))
        if not target_is_member:
            available_roles = set(team.get("roles", {}).keys())
            if available_roles:
                target_role = self._get_best_role_for_persona(target_persona, available_roles)
                team.setdefault("members", []).append({"persona_id": target_persona, "role": target_role, "joined_at": time.time()})
                team_roles = team.setdefault("roles", {}) # Ensure roles dict exists
                if target_role in team_roles:
                    team_roles[target_role] -= 1
                    if team_roles[target_role] <= 0: del team_roles[target_role]
                logger.info(f"Added {target_persona} to team {team['team_id']} with role {target_role}")
            else:
                logger.warning(f"No available roles for {target_persona} in team {team['team_id']}")
                return ""

        task = {
            "task_id": task_id, "team_id": team["team_id"], "assigner": assigner_persona,
            "assignee": target_persona, "description": task_description, "status": "assigned",
            "created_at": time.time(), "updated_at": time.time(), "result": None,
            "conversation_id": conversation_id
        }
        self.task_registry.setdefault(conversation_id, {})[task_id] = task
        logger.info(f"Assigned task {task_id} to {target_persona}")
        return task_id

    def _get_persona_tasks(self, conversation_id: str, persona_id: str) -> List[Dict[str, Any]]:
        conversation_tasks = self.task_registry.get(conversation_id, {})
        return [task for task_id, task in conversation_tasks.items() if task.get("assignee") == persona_id]

    def _is_task_status_update_command(self, message: str) -> bool:
        status_keywords = ["task complete", "completed task", "finished task", "task status", "update task", "task update"]
        return any(keyword in message.lower() for keyword in status_keywords)

    def _extract_task_status_update_info(self, message: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        message_lower = message.lower()
        task_id_match = re.search(r"task\s+([a-f0-9-]+)", message_lower) # Look for UUID like task ID
        task_id = task_id_match.group(1) if task_id_match else None

        if not task_id: # Fallback if no UUID like ID found
            if "task " in message_lower: # simple split
                 parts = message_lower.split("task ", 1)[1].strip().split(" ",1)
                 if parts: task_id = parts[0]


        status = "in_progress"
        if any(kw in message_lower for kw in ["complete", "completed", "finished"]): status = "completed"
        elif any(kw in message_lower for kw in ["failed", "failure", "error"]): status = "failed"

        result_text = message_lower # Default to full message
        if "result: " in message_lower: result_text = message_lower.split("result: ", 1)[1].strip()
        elif "status: " in message_lower: result_text = message_lower.split("status: ",1)[1].strip() # if status is part of the message

        return task_id, status, result_text

    async def _update_task_status(self, conversation_id: str, updater_persona: str, task_id: str, status: str, result: Optional[str]) -> bool:
        if conversation_id not in self.task_registry or task_id not in self.task_registry[conversation_id]:
            logger.warning(f"Task {task_id} not found in convo {conversation_id} for update.")
            return False
        task = self.task_registry[conversation_id][task_id]
        if updater_persona != task.get("assignee") and updater_persona != task.get("assigner"):
            logger.warning(f"{updater_persona} cannot update task {task_id} (not assignee/assigner).")
            return False
        task["status"] = status
        task["updated_at"] = time.time()
        if result: task["result"] = result
        # self.task_registry[conversation_id][task_id] = task # task is a reference, already updated
        logger.info(f"Updated task {task_id} to {status}")
        return True

    def _detect_team_need(self, message: str, context: "AgentProcessingContext") -> Tuple[bool, Optional[str], Optional[str]]:
        # context argument is now AgentProcessingContext, but not used in this specific logic
        message_lower = message.lower()
        complex_keywords = ["complex", "complicated", "multi-step", "multiple", "comprehensive", "detailed", "collaborative", "team", "project", "campaign"]
        is_complex = any(keyword in message_lower for keyword in complex_keywords)

        domains = []
        if any(kw in message_lower for kw in ["analyze", "analysis", "data"]): domains.append("analysis")
        if any(kw in message_lower for kw in ["marketing", "campaign", "content"]): domains.append("marketing")
        if any(kw in message_lower for kw in ["classify", "categorize", "sort"]): domains.append("classification")

        if is_complex and len(domains) > 1: return True, "cross_functional", f"complex task spanning {', '.join(domains)}"
        if is_complex: return True, "specialized", f"complex {domains[0] if domains else 'task'}"
        if len(domains) > 1: return True, "collaborative", f"task spanning {', '.join(domains)}"
        return False, None, None

    def _schedule_cleanup(self) -> None:
        logger.info("Team cleanup scheduled")

    def get_capabilities(self) -> List[str]:
        return self.config.get("capabilities", ["team_management", "hierarchical_teams", "role_assignment", "task_delegation"])
