/**
 * Mobile Dashboard Optimization Component
 * 
 * Provides mobile-optimized dashboard interface with touch-friendly interactions,
 * gesture support, and responsive design patterns.
 */

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import {
  Smartphone,
  Tablet,
  Monitor,
  Menu,
  Plus,
  Settings,
  Search,
  Filter,
  MoreVertical,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  Maximize2,
  Minimize2,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Move,
  Grip,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDashboardMode } from '@/stores/dashboard-mode-store';

interface MobileGesture {
  type: 'swipe' | 'pinch' | 'tap' | 'long_press' | 'drag';
  direction?: 'left' | 'right' | 'up' | 'down';
  distance?: number;
  scale?: number;
  duration?: number;
}

interface TouchInteraction {
  start_x: number;
  start_y: number;
  current_x: number;
  current_y: number;
  start_time: number;
  is_active: boolean;
}

interface MobileDashboardOptimizationProps {
  className?: string;
  children?: React.ReactNode;
  widgets?: any[];
  on_widget_reorder?: (from_index: number, to_index: number) => void;
  on_widget_expand?: (widget_id: string) => void;
  on_gesture?: (gesture: MobileGesture) => void;
}

export const MobileDashboardOptimization: React.FC<MobileDashboardOptimizationProps> = ({
  className,
  children,
  widgets = [],
  on_widget_reorder,
  on_widget_expand,
  on_gesture,
}) => {
  const [device_type, set_device_type] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');
  const [is_landscape, set_is_landscape] = useState(false);
  const [zoom_level, set_zoom_level] = useState(1);
  const [expanded_widget, set_expanded_widget] = useState<string | null>(null);
  const [show_mobile_menu, set_show_mobile_menu] = useState(false);
  const [touch_interaction, set_touch_interaction] = useState<TouchInteraction | null>(null);
  const [dragging_widget, set_dragging_widget] = useState<string | null>(null);
  const container_ref = useRef<HTMLDivElement>(null);

  const { current_mode } = useDashboardMode();

  // Detect device type and orientation
  useEffect(() => {
    const detect_device = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      if (width < 768) {
        set_device_type('mobile');
      } else if (width < 1024) {
        set_device_type('tablet');
      } else {
        set_device_type('desktop');
      }
      
      set_is_landscape(width > height);
    };

    detect_device();
    window.addEventListener('resize', detect_device);
    window.addEventListener('orientationchange', detect_device);

    return () => {
      window.removeEventListener('resize', detect_device);
      window.removeEventListener('orientationchange', detect_device);
    };
  }, []);

  // Touch event handlers
  const handle_touch_start = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    set_touch_interaction({
      start_x: touch.clientX,
      start_y: touch.clientY,
      current_x: touch.clientX,
      current_y: touch.clientY,
      start_time: Date.now(),
      is_active: true,
    });
  };

  const handle_touch_move = (e: React.TouchEvent) => {
    if (!touch_interaction) return;

    const touch = e.touches[0];
    set_touch_interaction(prev => prev ? {
      ...prev,
      current_x: touch.clientX,
      current_y: touch.clientY,
    } : null);

    // Prevent default scrolling for custom gestures
    if (dragging_widget) {
      e.preventDefault();
    }
  };

  const handle_touch_end = (e: React.TouchEvent) => {
    if (!touch_interaction) return;

    const duration = Date.now() - touch_interaction.start_time;
    const distance_x = touch_interaction.current_x - touch_interaction.start_x;
    const distance_y = touch_interaction.current_y - touch_interaction.start_y;
    const distance = Math.sqrt(distance_x * distance_x + distance_y * distance_y);

    // Detect gesture type
    let gesture: MobileGesture | null = null;

    if (duration < 200 && distance < 10) {
      // Tap
      gesture = { type: 'tap' };
    } else if (duration > 500 && distance < 10) {
      // Long press
      gesture = { type: 'long_press', duration };
    } else if (distance > 50) {
      // Swipe
      const direction = Math.abs(distance_x) > Math.abs(distance_y)
        ? (distance_x > 0 ? 'right' : 'left')
        : (distance_y > 0 ? 'down' : 'up');
      
      gesture = { type: 'swipe', direction, distance };
    }

    if (gesture) {
      on_gesture?.(gesture);
      handle_gesture(gesture);
    }

    set_touch_interaction(null);
    set_dragging_widget(null);
  };

  const handle_gesture = (gesture: MobileGesture) => {
    switch (gesture.type) {
      case 'swipe':
        if (gesture.direction === 'left' && device_type === 'mobile') {
          // Show next widget or navigate
        } else if (gesture.direction === 'right' && device_type === 'mobile') {
          // Show previous widget or navigate back
        } else if (gesture.direction === 'up') {
          // Minimize current widget
          set_expanded_widget(null);
        } else if (gesture.direction === 'down') {
          // Show mobile menu
          set_show_mobile_menu(true);
        }
        break;
      case 'pinch':
        // Zoom in/out
        if (gesture.scale && gesture.scale > 1.1) {
          set_zoom_level(prev => Math.min(prev * 1.2, 3));
        } else if (gesture.scale && gesture.scale < 0.9) {
          set_zoom_level(prev => Math.max(prev * 0.8, 0.5));
        }
        break;
      case 'long_press':
        // Show context menu or enter edit mode
        break;
    }
  };

  const handle_widget_expand = (widget_id: string) => {
    set_expanded_widget(expanded_widget === widget_id ? null : widget_id);
    on_widget_expand?.(widget_id);
  };

  const get_responsive_grid_config = () => {
    switch (device_type) {
      case 'mobile':
        return {
          columns: is_landscape ? 2 : 1,
          gap: 'gap-3',
          padding: 'p-3',
          widget_height: is_landscape ? 'h-48' : 'h-64',
        };
      case 'tablet':
        return {
          columns: is_landscape ? 3 : 2,
          gap: 'gap-4',
          padding: 'p-4',
          widget_height: 'h-56',
        };
      default:
        return {
          columns: 4,
          gap: 'gap-6',
          padding: 'p-6',
          widget_height: 'h-64',
        };
    }
  };

  const grid_config = get_responsive_grid_config();

  const MobileHeader = () => (
    <div className="sticky top-0 z-50 bg-background border-b">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => set_show_mobile_menu(true)}
            className="md:hidden"
          >
            <Menu className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-semibold">Dashboard</h1>
          <Badge variant="secondary" className="text-xs">
            {current_mode === 'simple' ? 'Simple' : 'Advanced'}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Search className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="sm">
            <Filter className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreVertical className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );

  const MobileWidget = ({ widget, index }: { widget: any; index: number }) => {
    const is_expanded = expanded_widget === widget.id;
    
    return (
      <Card 
        className={cn(
          "touch-manipulation transition-all duration-200",
          is_expanded && "fixed inset-4 z-40 shadow-2xl",
          dragging_widget === widget.id && "opacity-50 scale-105"
        )}
        onTouchStart={handle_touch_start}
        onTouchMove={handle_touch_move}
        onTouchEnd={handle_touch_end}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium truncate">
              {widget.title || `Widget ${index + 1}`}
            </CardTitle>
            <div className="flex items-center space-x-1">
              {device_type === 'mobile' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handle_widget_expand(widget.id)}
                  className="h-6 w-6 p-0"
                >
                  {is_expanded ? (
                    <Minimize2 className="h-3 w-3" />
                  ) : (
                    <Maximize2 className="h-3 w-3" />
                  )}
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 cursor-grab active:cursor-grabbing"
                onTouchStart={() => set_dragging_widget(widget.id)}
              >
                <Grip className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className={cn(
          "p-3",
          is_expanded ? "h-[calc(100vh-12rem)] overflow-auto" : grid_config.widget_height
        )}>
          <div className="h-full bg-gradient-to-br from-blue-50 to-purple-50 rounded border flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="text-2xl mb-2">📊</div>
              <p className="text-sm">Widget Content</p>
              {is_expanded && (
                <p className="text-xs mt-1">Expanded View</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const MobileMenu = () => (
    <Sheet open={show_mobile_menu} onOpenChange={set_show_mobile_menu}>
      <SheetContent side="left" className="w-80">
        <SheetHeader>
          <SheetTitle>Dashboard Menu</SheetTitle>
          <SheetDescription>
            Access dashboard tools and settings
          </SheetDescription>
        </SheetHeader>
        
        <div className="space-y-4 mt-6">
          <Button className="w-full justify-start" variant="ghost">
            <Plus className="h-4 w-4 mr-2" />
            Add Widget
          </Button>
          <Button className="w-full justify-start" variant="ghost">
            <Settings className="h-4 w-4 mr-2" />
            Dashboard Settings
          </Button>
          <Button className="w-full justify-start" variant="ghost">
            <Filter className="h-4 w-4 mr-2" />
            Filter Data
          </Button>
          
          <Separator />
          
          <div className="space-y-2">
            <h4 className="text-sm font-medium">View Options</h4>
            <div className="flex items-center justify-between">
              <span className="text-sm">Zoom Level</span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => set_zoom_level(prev => Math.max(prev * 0.8, 0.5))}
                >
                  <ZoomOut className="h-3 w-3" />
                </Button>
                <span className="text-xs w-12 text-center">
                  {Math.round(zoom_level * 100)}%
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => set_zoom_level(prev => Math.min(prev * 1.2, 3))}
                >
                  <ZoomIn className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Device Info</h4>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              {device_type === 'mobile' && <Smartphone className="h-4 w-4" />}
              {device_type === 'tablet' && <Tablet className="h-4 w-4" />}
              {device_type === 'desktop' && <Monitor className="h-4 w-4" />}
              <span className="capitalize">{device_type}</span>
              <span>•</span>
              <span>{is_landscape ? 'Landscape' : 'Portrait'}</span>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );

  return (
    <div 
      ref={container_ref}
      className={cn("min-h-screen bg-background", className)}
      style={{ transform: `scale(${zoom_level})`, transformOrigin: 'top left' }}
    >
      {/* Mobile Header */}
      {device_type === 'mobile' && <MobileHeader />}
      
      {/* Main Content */}
      <div className={cn(
        "grid auto-rows-fr",
        `grid-cols-${grid_config.columns}`,
        grid_config.gap,
        grid_config.padding
      )}>
        {widgets.map((widget, index) => (
          <MobileWidget key={widget.id || index} widget={widget} index={index} />
        ))}
        
        {/* Add Widget Button */}
        {current_mode === 'simple' && (
          <Card className="border-dashed border-2 border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
            <CardContent className={cn(
              "flex items-center justify-center",
              grid_config.widget_height
            )}>
              <Button variant="ghost" className="h-full w-full flex flex-col items-center space-y-2">
                <Plus className="h-8 w-8 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Add Widget</span>
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
      
      {/* Mobile Menu */}
      <MobileMenu />
      
      {/* Touch Gesture Indicators */}
      {device_type === 'mobile' && (
        <div className="fixed bottom-4 left-4 right-4 z-30">
          <Card className="bg-background/80 backdrop-blur-sm">
            <CardContent className="p-3">
              <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <ChevronUp className="h-3 w-3" />
                  <span>Swipe up to minimize</span>
                </div>
                <div className="flex items-center space-x-1">
                  <ChevronDown className="h-3 w-3" />
                  <span>Swipe down for menu</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Expanded Widget Overlay */}
      {expanded_widget && (
        <div 
          className="fixed inset-0 bg-black/50 z-30"
          onClick={() => set_expanded_widget(null)}
        />
      )}
    </div>
  );
};

// Hook for mobile-specific optimizations
export const useMobileOptimization = () => {
  const [is_mobile, set_is_mobile] = useState(false);
  const [is_touch_device, set_is_touch_device] = useState(false);
  const [viewport_size, set_viewport_size] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const check_mobile = () => {
      const width = window.innerWidth;
      set_is_mobile(width < 768);
      set_is_touch_device('ontouchstart' in window);
      set_viewport_size({ width: window.innerWidth, height: window.innerHeight });
    };

    check_mobile();
    window.addEventListener('resize', check_mobile);
    
    return () => window.removeEventListener('resize', check_mobile);
  }, []);

  return {
    is_mobile,
    is_touch_device,
    viewport_size,
    is_small_screen: viewport_size.width < 640,
    is_landscape: viewport_size.width > viewport_size.height,
  };
};

// Mobile-optimized widget wrapper
export const MobileWidgetWrapper: React.FC<{
  children: React.ReactNode;
  widget_id: string;
  title?: string;
  expandable?: boolean;
  on_expand?: (id: string) => void;
}> = ({ children, widget_id, title, expandable = true, on_expand }) => {
  const { is_mobile } = useMobileOptimization();
  const [is_expanded, set_is_expanded] = useState(false);

  const handle_expand = () => {
    if (expandable) {
      set_is_expanded(!is_expanded);
      on_expand?.(widget_id);
    }
  };

  if (!is_mobile) {
    return <>{children}</>;
  }

  return (
    <div className={cn(
      "relative transition-all duration-200",
      is_expanded && "fixed inset-4 z-50 bg-background rounded-lg shadow-2xl"
    )}>
      {title && (
        <div className="flex items-center justify-between p-3 border-b">
          <h3 className="font-medium truncate">{title}</h3>
          {expandable && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handle_expand}
              className="h-6 w-6 p-0"
            >
              {is_expanded ? (
                <Minimize2 className="h-3 w-3" />
              ) : (
                <Maximize2 className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      )}
      <div className={cn(
        "p-3",
        is_expanded && "h-[calc(100vh-8rem)] overflow-auto"
      )}>
        {children}
      </div>
      {is_expanded && (
        <div 
          className="fixed inset-0 bg-black/50 -z-10"
          onClick={() => set_is_expanded(false)}
        />
      )}
    </div>
  );
};
