"""
Robust Error Handling and Recovery System for MCP Tools.

This module provides comprehensive error handling with retry mechanisms, graceful degradation,
detailed error logging, and user-friendly error messages across all MCP tools.
"""

import logging
import asyncio
import time
import traceback
from typing import Dict, Any, Optional, List, Callable, Union, Type
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import random
from functools import wraps

logger = logging.getLogger(__name__)


class ErrorCategory(Enum):
    """Categories of errors for different handling strategies."""
    
    TRANSIENT = "transient"  # Temporary errors that can be retried
    PERMANENT = "permanent"  # Permanent errors that should not be retried
    RATE_LIMIT = "rate_limit"  # Rate limiting errors
    AUTHENTICATION = "authentication"  # Authentication/authorization errors
    VALIDATION = "validation"  # Input validation errors
    NETWORK = "network"  # Network connectivity errors
    TIMEOUT = "timeout"  # Timeout errors
    RESOURCE = "resource"  # Resource exhaustion errors
    UNKNOWN = "unknown"  # Unknown/unclassified errors


class ErrorSeverity(Enum):
    """Error severity levels."""
    
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorInfo:
    """Detailed error information."""
    
    error_type: str
    error_message: str
    category: ErrorCategory
    severity: ErrorSeverity
    timestamp: datetime = field(default_factory=datetime.now)
    tool_name: Optional[str] = None
    agent_identity: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    retry_count: int = 0
    is_recoverable: bool = True
    user_message: Optional[str] = None


@dataclass
class RetryConfig:
    """Configuration for retry mechanisms."""
    
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    backoff_strategy: str = "exponential"  # exponential, linear, fixed
    retry_on_categories: List[ErrorCategory] = field(default_factory=lambda: [
        ErrorCategory.TRANSIENT, ErrorCategory.NETWORK, ErrorCategory.TIMEOUT, ErrorCategory.RATE_LIMIT
    ])


class ErrorClassifier:
    """Classifies errors into categories for appropriate handling."""
    
    # Error patterns for classification
    ERROR_PATTERNS = {
        ErrorCategory.TRANSIENT: [
            "connection reset", "connection timeout", "temporary failure",
            "service unavailable", "internal server error", "502", "503", "504"
        ],
        ErrorCategory.RATE_LIMIT: [
            "rate limit", "too many requests", "quota exceeded", "429"
        ],
        ErrorCategory.AUTHENTICATION: [
            "unauthorized", "invalid api key", "authentication failed", 
            "access denied", "401", "403"
        ],
        ErrorCategory.VALIDATION: [
            "validation error", "invalid input", "malformed request",
            "bad request", "400"
        ],
        ErrorCategory.NETWORK: [
            "network error", "connection failed", "dns resolution",
            "host unreachable", "connection refused"
        ],
        ErrorCategory.TIMEOUT: [
            "timeout", "timed out", "deadline exceeded"
        ],
        ErrorCategory.RESOURCE: [
            "out of memory", "disk full", "resource exhausted",
            "too large", "file size exceeded"
        ]
    }
    
    @classmethod
    def classify_error(cls, error: Exception, error_message: str = None) -> ErrorCategory:
        """
        Classify an error into a category.
        
        Args:
            error: The exception object
            error_message: Optional error message override
            
        Returns:
            ErrorCategory: The classified error category
        """
        message = error_message or str(error).lower()
        error_type = type(error).__name__.lower()
        
        # Check error patterns
        for category, patterns in cls.ERROR_PATTERNS.items():
            for pattern in patterns:
                if pattern in message or pattern in error_type:
                    return category
        
        # Check specific exception types
        if isinstance(error, (ConnectionError, OSError)):
            return ErrorCategory.NETWORK
        elif isinstance(error, TimeoutError):
            return ErrorCategory.TIMEOUT
        elif isinstance(error, ValueError):
            return ErrorCategory.VALIDATION
        elif isinstance(error, PermissionError):
            return ErrorCategory.AUTHENTICATION
        
        return ErrorCategory.UNKNOWN
    
    @classmethod
    def determine_severity(cls, category: ErrorCategory, error: Exception) -> ErrorSeverity:
        """
        Determine error severity based on category and error details.
        
        Args:
            category: Error category
            error: Exception object
            
        Returns:
            ErrorSeverity: The error severity level
        """
        severity_mapping = {
            ErrorCategory.CRITICAL: ErrorSeverity.CRITICAL,
            ErrorCategory.AUTHENTICATION: ErrorSeverity.HIGH,
            ErrorCategory.PERMANENT: ErrorSeverity.HIGH,
            ErrorCategory.RESOURCE: ErrorSeverity.MEDIUM,
            ErrorCategory.RATE_LIMIT: ErrorSeverity.MEDIUM,
            ErrorCategory.VALIDATION: ErrorSeverity.MEDIUM,
            ErrorCategory.NETWORK: ErrorSeverity.LOW,
            ErrorCategory.TIMEOUT: ErrorSeverity.LOW,
            ErrorCategory.TRANSIENT: ErrorSeverity.LOW,
            ErrorCategory.UNKNOWN: ErrorSeverity.MEDIUM
        }
        
        return severity_mapping.get(category, ErrorSeverity.MEDIUM)


class RetryStrategy:
    """Implements various retry strategies."""
    
    @staticmethod
    def exponential_backoff(attempt: int, config: RetryConfig) -> float:
        """Calculate delay using exponential backoff."""
        delay = config.base_delay * (config.exponential_base ** (attempt - 1))
        delay = min(delay, config.max_delay)
        
        if config.jitter:
            # Add random jitter (±25%)
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    @staticmethod
    def linear_backoff(attempt: int, config: RetryConfig) -> float:
        """Calculate delay using linear backoff."""
        delay = config.base_delay * attempt
        delay = min(delay, config.max_delay)
        
        if config.jitter:
            jitter_range = config.base_delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    @staticmethod
    def fixed_delay(attempt: int, config: RetryConfig) -> float:
        """Calculate delay using fixed delay."""
        delay = config.base_delay
        
        if config.jitter:
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    @classmethod
    def calculate_delay(cls, attempt: int, config: RetryConfig) -> float:
        """
        Calculate retry delay based on strategy.
        
        Args:
            attempt: Current attempt number (1-based)
            config: Retry configuration
            
        Returns:
            float: Delay in seconds
        """
        if config.backoff_strategy == "exponential":
            return cls.exponential_backoff(attempt, config)
        elif config.backoff_strategy == "linear":
            return cls.linear_backoff(attempt, config)
        elif config.backoff_strategy == "fixed":
            return cls.fixed_delay(attempt, config)
        else:
            return cls.exponential_backoff(attempt, config)


class GracefulDegradation:
    """Implements graceful degradation strategies."""
    
    @staticmethod
    def get_fallback_response(tool_name: str, error_info: ErrorInfo) -> Dict[str, Any]:
        """
        Get a fallback response when the primary operation fails.
        
        Args:
            tool_name: Name of the tool
            error_info: Error information
            
        Returns:
            Dict: Fallback response
        """
        fallback_responses = {
            "intent_detection": {
                "isError": False,
                "content": [{
                    "type": "text",
                    "text": "Unable to detect intent automatically. Please specify your request more clearly."
                }],
                "metadata": {"fallback": True, "confidence": 0.0}
            },
            "language_detection": {
                "isError": False,
                "content": [{
                    "type": "text",
                    "text": "Language detection unavailable. Assuming English."
                }],
                "metadata": {"fallback": True, "detected_language": "en", "confidence": 0.0}
            },
            "data_analysis": {
                "isError": False,
                "content": [{
                    "type": "text",
                    "text": "Data analysis service is temporarily unavailable. Please try again later or contact support."
                }],
                "metadata": {"fallback": True}
            }
        }
        
        # Get tool-specific fallback or generic fallback
        fallback = fallback_responses.get(tool_name, {
            "isError": False,
            "content": [{
                "type": "text",
                "text": f"The {tool_name} service is temporarily unavailable. Please try again later."
            }],
            "metadata": {"fallback": True}
        })
        
        # Add error context to metadata
        fallback["metadata"]["error_category"] = error_info.category.value
        fallback["metadata"]["error_severity"] = error_info.severity.value
        fallback["metadata"]["timestamp"] = error_info.timestamp.isoformat()
        
        return fallback


class ErrorHandler:
    """
    Comprehensive error handler for MCP tools.
    
    Provides retry mechanisms, graceful degradation, detailed logging,
    and user-friendly error messages.
    """
    
    def __init__(self, default_retry_config: Optional[RetryConfig] = None):
        """Initialize the error handler."""
        self.default_retry_config = default_retry_config or RetryConfig()
        self.classifier = ErrorClassifier()
        self.retry_strategy = RetryStrategy()
        self.graceful_degradation = GracefulDegradation()
        self.error_history: List[ErrorInfo] = []
        
    async def handle_with_retry(
        self,
        operation: Callable,
        tool_name: str,
        agent_identity: Optional[str] = None,
        retry_config: Optional[RetryConfig] = None,
        context: Optional[Dict[str, Any]] = None,
        *args,
        **kwargs
    ) -> Any:
        """
        Execute an operation with retry logic and error handling.
        
        Args:
            operation: The operation to execute
            tool_name: Name of the tool
            agent_identity: Agent identity for context
            retry_config: Retry configuration (optional)
            context: Additional context
            *args, **kwargs: Arguments for the operation
            
        Returns:
            Any: Operation result or fallback response
        """
        config = retry_config or self.default_retry_config
        context = context or {}
        
        last_error = None
        
        for attempt in range(1, config.max_attempts + 1):
            try:
                # Execute the operation
                if asyncio.iscoroutinefunction(operation):
                    result = await operation(*args, **kwargs)
                else:
                    result = operation(*args, **kwargs)
                
                # Log successful retry if this wasn't the first attempt
                if attempt > 1:
                    logger.info(f"Operation succeeded on attempt {attempt} for tool {tool_name}")
                
                return result
                
            except Exception as error:
                last_error = error
                
                # Classify the error
                category = self.classifier.classify_error(error)
                severity = self.classifier.determine_severity(category, error)
                
                # Create error info
                error_info = ErrorInfo(
                    error_type=type(error).__name__,
                    error_message=str(error),
                    category=category,
                    severity=severity,
                    tool_name=tool_name,
                    agent_identity=agent_identity,
                    context=context,
                    stack_trace=traceback.format_exc(),
                    retry_count=attempt,
                    is_recoverable=category in config.retry_on_categories
                )
                
                # Add to error history
                self.error_history.append(error_info)
                
                # Log the error
                self._log_error(error_info, attempt, config.max_attempts)
                
                # Check if we should retry
                if attempt >= config.max_attempts or category not in config.retry_on_categories:
                    break
                
                # Calculate and apply retry delay
                delay = self.retry_strategy.calculate_delay(attempt, config)
                if delay > 0:
                    logger.info(f"Retrying in {delay:.2f} seconds (attempt {attempt + 1}/{config.max_attempts})")
                    await asyncio.sleep(delay)
        
        # All retries exhausted, handle final error
        return await self._handle_final_error(last_error, tool_name, agent_identity, context)
    
    async def _handle_final_error(
        self,
        error: Exception,
        tool_name: str,
        agent_identity: Optional[str],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle the final error after all retries are exhausted.
        
        Args:
            error: The final error
            tool_name: Name of the tool
            agent_identity: Agent identity
            context: Error context
            
        Returns:
            Dict: Error response or fallback response
        """
        # Classify the final error
        category = self.classifier.classify_error(error)
        severity = self.classifier.determine_severity(category, error)
        
        # Create final error info
        error_info = ErrorInfo(
            error_type=type(error).__name__,
            error_message=str(error),
            category=category,
            severity=severity,
            tool_name=tool_name,
            agent_identity=agent_identity,
            context=context,
            stack_trace=traceback.format_exc(),
            is_recoverable=False,
            user_message=self._generate_user_friendly_message(category, tool_name)
        )
        
        # Log final error
        logger.error(f"Final error for tool {tool_name}: {error_info.error_message}")
        
        # Decide whether to return error or fallback
        if severity in [ErrorSeverity.LOW, ErrorSeverity.MEDIUM]:
            # Try graceful degradation
            return self.graceful_degradation.get_fallback_response(tool_name, error_info)
        else:
            # Return error response for high/critical severity
            return {
                "isError": True,
                "content": [{
                    "type": "text",
                    "text": error_info.user_message or f"An error occurred: {error_info.error_message}"
                }],
                "metadata": {
                    "error_type": error_info.error_type,
                    "error_category": error_info.category.value,
                    "error_severity": error_info.severity.value,
                    "timestamp": error_info.timestamp.isoformat(),
                    "tool_name": tool_name
                }
            }
    
    def _log_error(self, error_info: ErrorInfo, attempt: int, max_attempts: int):
        """Log error information with appropriate level."""
        log_message = (
            f"Error in {error_info.tool_name} (attempt {attempt}/{max_attempts}): "
            f"{error_info.error_message} [Category: {error_info.category.value}, "
            f"Severity: {error_info.severity.value}]"
        )
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def _generate_user_friendly_message(self, category: ErrorCategory, tool_name: str) -> str:
        """Generate user-friendly error messages."""
        messages = {
            ErrorCategory.NETWORK: f"Network connection issue. Please check your internet connection and try again.",
            ErrorCategory.TIMEOUT: f"The {tool_name} operation timed out. Please try again or contact support if the issue persists.",
            ErrorCategory.RATE_LIMIT: f"Too many requests. Please wait a moment before trying again.",
            ErrorCategory.AUTHENTICATION: f"Authentication failed. Please check your API credentials.",
            ErrorCategory.VALIDATION: f"Invalid input provided. Please check your request and try again.",
            ErrorCategory.RESOURCE: f"System resources are currently unavailable. Please try again later.",
            ErrorCategory.TRANSIENT: f"Temporary service issue. Please try again in a few moments.",
            ErrorCategory.PERMANENT: f"Unable to complete the {tool_name} operation. Please contact support.",
            ErrorCategory.UNKNOWN: f"An unexpected error occurred. Please try again or contact support if the issue persists."
        }
        
        return messages.get(category, f"An error occurred with the {tool_name} service.")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics from the error history."""
        if not self.error_history:
            return {"total_errors": 0}
        
        # Calculate statistics
        total_errors = len(self.error_history)
        categories = {}
        severities = {}
        tools = {}
        
        for error in self.error_history:
            # Count by category
            categories[error.category.value] = categories.get(error.category.value, 0) + 1
            
            # Count by severity
            severities[error.severity.value] = severities.get(error.severity.value, 0) + 1
            
            # Count by tool
            if error.tool_name:
                tools[error.tool_name] = tools.get(error.tool_name, 0) + 1
        
        return {
            "total_errors": total_errors,
            "by_category": categories,
            "by_severity": severities,
            "by_tool": tools,
            "recent_errors": [
                {
                    "timestamp": error.timestamp.isoformat(),
                    "tool": error.tool_name,
                    "category": error.category.value,
                    "severity": error.severity.value,
                    "message": error.error_message
                }
                for error in self.error_history[-10:]  # Last 10 errors
            ]
        }
    
    def clear_error_history(self):
        """Clear the error history."""
        self.error_history.clear()
        logger.info("Error history cleared")


# Global error handler instance
error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """Get the global error handler instance."""
    return error_handler


def with_error_handling(
    tool_name: str,
    retry_config: Optional[RetryConfig] = None,
    agent_identity: Optional[str] = None
):
    """
    Decorator for adding error handling to functions.
    
    Args:
        tool_name: Name of the tool
        retry_config: Retry configuration
        agent_identity: Agent identity
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await error_handler.handle_with_retry(
                func, tool_name, agent_identity, retry_config, None, *args, **kwargs
            )
        return wrapper
    return decorator
