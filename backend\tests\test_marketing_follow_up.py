#!/usr/bin/env python3
"""
Test script to verify marketing agent follow-up question handling.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.marketing_agent.composable_agent import ComposableMarketingAgent

async def test_follow_up():
    """Test marketing agent follow-up question handling."""
    print('🧪 Testing Marketing Agent Follow-up Question Handling...')
    
    try:
        agent = ComposableMarketingAgent()
        await agent.initialize({
            'provider': 'groq',
            'model': 'llama3-70b-8192',
            'temperature': 0.7
        })
        
        print('✅ Agent initialized')
        
        # Test 1: Generate marketing strategy first
        print('\n📝 Test 1: Generate marketing strategy')
        result1 = await agent.process_message(
            user_id=1,
            message='generate a marketing strategy for my tech startup',
            conversation_id='test-conversation-1',
            context={}
        )
        print(f'✅ Strategy generated: {len(result1["message"])} characters')
        print(f'✅ Metadata generated_content: {result1["metadata"].get("generated_content", False)}')
        
        # Test 2: Ask follow-up question
        print('\n📝 Test 2: Ask follow-up question')
        conversation_history = [
            {
                'sender': 'user',
                'content': 'generate a marketing strategy for my tech startup'
            },
            {
                'sender': 'ai', 
                'content': result1['message'],
                'metadata': result1['metadata']
            }
        ]
        
        result2 = await agent.process_message(
            user_id=1,
            message='what else can be done?',
            conversation_id='test-conversation-1',
            context={'conversation_history': conversation_history}
        )
        print(f'✅ Follow-up response: {len(result2["message"])} characters')
        print(f'✅ Is conversational: {result2["metadata"].get("conversational_response", False)}')
        print(f'✅ Generated content: {result2["metadata"].get("generated_content", False)}')
        
        # Check if it regenerated content (bad) or gave conversational advice (good)
        if result2['metadata'].get('generated_content', False):
            print('❌ PROBLEM: Follow-up question triggered content regeneration!')
            print(f'Response preview: {result2["message"][:200]}...')
            return False
        else:
            print('✅ SUCCESS: Follow-up question handled conversationally!')
            print(f'Response preview: {result2["message"][:200]}...')
            return True
            
    except Exception as e:
        print(f'❌ Error during test: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = asyncio.run(test_follow_up())
    if success:
        print('\n🎉 Test completed successfully!')
        sys.exit(0)
    else:
        print('\n💥 Test failed!')
        sys.exit(1)
