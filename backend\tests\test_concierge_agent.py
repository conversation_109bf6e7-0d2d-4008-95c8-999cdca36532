"""
Test module for the Concierge Agent.

This module contains comprehensive tests for the Concierge Agent functionality,
including intent recognition, persona recommendation, and conversation management.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from agents.concierge_agent.concierge import ConciergeAgent, UserIntent, ConversationContext


class TestConciergeAgent:
    """Test cases for the Concierge Agent."""

    @pytest.mark.asyncio
    async def test_agent_initialization(self, concierge_agent):
        """Test that the concierge agent initializes correctly."""
        assert concierge_agent is not None
        assert concierge_agent.recommendation_threshold == 0.7
        assert concierge_agent.max_recommendations == 3
        assert concierge_agent.consider_user_history is True
        assert len(concierge_agent.intent_patterns) > 0

    @pytest.mark.asyncio
    async def test_intent_recognition_analysis_request(self, concierge_agent):
        """Test intent recognition for analysis requests."""
        message = "I want to analyze my CSV data and create visualizations"
        user_context = {"user_id": "test_user"}

        intent = await concierge_agent.parse_user_intent(message, user_context)

        assert intent.intent_type == "analysis_request"
        assert intent.confidence > 0
        assert "composable-analyst" in intent.suggested_personas
        assert intent.requires_data is True

    @pytest.mark.asyncio
    async def test_intent_recognition_marketing_request(self, concierge_agent):
        """Test intent recognition for marketing requests."""
        message = "Help me create a marketing campaign for my product"
        user_context = {"user_id": "test_user"}

        intent = await concierge_agent.parse_user_intent(message, user_context)

        assert intent.intent_type == "marketing_request"
        assert intent.confidence > 0
        assert "composable-marketer" in intent.suggested_personas

    @pytest.mark.asyncio
    async def test_intent_recognition_classification_request(self, concierge_agent):
        """Test intent recognition for classification requests."""
        message = "I need to classify and organize my documents"
        user_context = {"user_id": "test_user"}

        intent = await concierge_agent.parse_user_intent(message, user_context)

        assert intent.intent_type == "classification_request"
        assert intent.confidence > 0
        assert "composable-classifier" in intent.suggested_personas

    @pytest.mark.asyncio
    async def test_entity_extraction(self, concierge_agent):
        """Test entity extraction from user messages."""
        message = "analyze my excel file for finance data"

        entities = concierge_agent._extract_entities(message)

        assert entities.get("file_type") == "excel"
        assert entities.get("industry") == "finance"
        assert entities.get("task_type") == "analysis"

    @pytest.mark.asyncio
    async def test_complexity_calculation(self, concierge_agent):
        """Test complexity score calculation."""
        simple_message = "hello"
        complex_message = "I need a comprehensive advanced analysis with multiple correlations and predictive modeling"

        simple_score = concierge_agent._calculate_complexity(simple_message, {})
        complex_score = concierge_agent._calculate_complexity(complex_message, {"task_type": "analysis"})

        assert simple_score < complex_score
        assert 0 <= simple_score <= 1
        assert 0 <= complex_score <= 1

    @pytest.mark.asyncio
    async def test_conversation_context_creation(self, concierge_agent):
        """Test conversation context creation and management."""
        user_id = "test_user"
        session_id = "test_session"

        context = await concierge_agent.get_conversation_context(user_id, session_id)

        assert context.user_id == user_id
        assert context.session_id == session_id
        assert len(context.conversation_history) == 0
        assert context.current_task is None

    @pytest.mark.asyncio
    async def test_conversation_context_update(self, concierge_agent):
        """Test conversation context updates."""
        user_id = "test_user"
        session_id = "test_session"

        context = await concierge_agent.get_conversation_context(user_id, session_id)

        intent = UserIntent(
            intent_type="analysis_request",
            confidence=0.8,
            entities={},
            suggested_personas=["composable-analyst"],
            requires_data=True,
            complexity_score=0.5
        )

        await concierge_agent.update_conversation_context(
            context, "test message", "test response", intent
        )

        assert len(context.conversation_history) == 1
        assert context.current_task == "analysis_request"
        assert context.conversation_history[0]["user_message"] == "test message"

    @pytest.mark.asyncio
    async def test_welcome_message_generation(self, concierge_agent):
        """Test welcome message generation."""
        user_context = {"user_name": "John", "is_returning_user": False}

        message = await concierge_agent.generate_welcome_message(user_context)

        assert "John" in message
        assert "Concierge" in message
        assert len(message) > 0

    @pytest.mark.asyncio
    async def test_persona_recommendation_generation(self, concierge_agent):
        """Test persona recommendation generation."""
        intent = UserIntent(
            intent_type="analysis_request",
            confidence=0.9,
            entities={"file_type": "csv"},
            suggested_personas=["composable-analyst"],
            requires_data=True,
            complexity_score=0.3
        )

        context = ConversationContext(
            user_id="test_user",
            session_id="test_session",
            conversation_history=[],
            user_preferences={},
            current_task=None,
            attached_data=[],
            last_interaction=datetime.now()
        )

        recommendation = await concierge_agent.generate_persona_recommendation(intent, context)

        assert "Composable Analyst" in recommendation
        assert "confident" in recommendation.lower()
        assert "💡" in recommendation  # Data attachment tip

    @pytest.mark.asyncio
    async def test_data_attachment_guidance(self, concierge_agent):
        """Test data attachment guidance generation."""
        guidance = await concierge_agent.generate_data_attachment_guidance("csv")

        assert "CSV" in guidance
        assert "📎" in guidance
        assert "Pro Tip" in guidance

    @pytest.mark.asyncio
    async def test_persona_handoff(self, concierge_agent):
        """Test persona handoff functionality."""
        context = ConversationContext(
            user_id="test_user",
            session_id="test_session",
            conversation_history=[],
            user_preferences={},
            current_task="analysis_request",
            attached_data=[],
            last_interaction=datetime.now()
        )

        with patch.object(concierge_agent.agent_registry, 'create_agent_instance') as mock_create:
            mock_agent = Mock()
            mock_create.return_value = mock_agent

            result = await concierge_agent.handle_persona_handoff(
                "composable-analyst", "analyze my data", context
            )

            assert result["success"] is True
            assert result["target_persona"] == "composable-analyst"
            assert "handoff_context" in result

    @pytest.mark.asyncio
    async def test_context_cleanup(self, concierge_agent):
        """Test old context cleanup."""
        # Create old context
        old_context = ConversationContext(
            user_id="old_user",
            session_id="old_session",
            conversation_history=[],
            user_preferences={},
            current_task=None,
            attached_data=[],
            last_interaction=datetime.now() - timedelta(hours=25)
        )

        # Create recent context
        recent_context = ConversationContext(
            user_id="recent_user",
            session_id="recent_session",
            conversation_history=[],
            user_preferences={},
            current_task=None,
            attached_data=[],
            last_interaction=datetime.now()
        )

        concierge_agent.conversation_contexts["old_user_old_session"] = old_context
        concierge_agent.conversation_contexts["recent_user_recent_session"] = recent_context

        await concierge_agent.cleanup_old_contexts(max_age_hours=24)

        assert "old_user_old_session" not in concierge_agent.conversation_contexts
        assert "recent_user_recent_session" in concierge_agent.conversation_contexts

    @pytest.mark.asyncio
    async def test_process_message_integration(self, concierge_agent):
        """Test the main message processing method."""
        message = "I want to analyze my sales data"
        user_context = {"user_id": "test_user", "user_name": "John"}
        session_id = "test_session"

        result = await concierge_agent.process_message(message, user_context, session_id)

        assert result["success"] is True
        assert "message" in result
        assert "metadata" in result
        assert result["metadata"]["intent"] == "analysis_request"
        assert len(result["metadata"]["suggested_personas"]) > 0

    @pytest.mark.asyncio
    async def test_error_handling(self, concierge_agent):
        """Test error handling in message processing."""
        # Test with invalid input that might cause errors
        with patch.object(concierge_agent, 'parse_user_intent', side_effect=Exception("Test error")):
            result = await concierge_agent.process_message("test", {}, "session")

            assert result["success"] is False
            assert "error" in result["metadata"]

    @pytest.mark.asyncio
    async def test_memory_efficiency(self, concierge_agent):
        """Test memory efficiency with conversation history limits."""
        context = await concierge_agent.get_conversation_context("test_user", "test_session")

        # Add more than 10 interactions
        for i in range(15):
            intent = UserIntent(
                intent_type="general_question",
                confidence=0.5,
                entities={},
                suggested_personas=[],
                requires_data=False,
                complexity_score=0.1
            )

            await concierge_agent.update_conversation_context(
                context, f"message {i}", f"response {i}", intent
            )

        # Should only keep last 10 interactions
        assert len(context.conversation_history) == 10
        assert context.conversation_history[0]["user_message"] == "message 5"
        assert context.conversation_history[-1]["user_message"] == "message 14"
