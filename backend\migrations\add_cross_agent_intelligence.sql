-- Migration: Add Cross-Agent Intelligence Tables
-- Description: Add tables for agent insights, interactions, and shared contexts for cross-agent intelligence
-- Date: 2025-01-11

-- Create agent_insights table
CREATE TABLE IF NOT EXISTS agent_insights (
    id VARCHAR(36) PRIMARY KEY,
    business_profile_id VARCHAR(36) NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,
    source_agent_id VARCHAR(100) NOT NULL,
    insight_type VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    insight_metadata JSONB DEFAULT '{}',
    relevance_tags JSONB DEFAULT '[]',
    confidence_score FLOAT DEFAULT 1.0,
    access_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for agent_insights
CREATE INDEX IF NOT EXISTS idx_agent_insights_profile_agent ON agent_insights(business_profile_id, source_agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_insights_type ON agent_insights(insight_type);
CREATE INDEX IF NOT EXISTS idx_agent_insights_created ON agent_insights(created_at);
CREATE INDEX IF NOT EXISTS idx_agent_insights_active ON agent_insights(business_profile_id, is_active) WHERE is_active = TRUE;

-- Create agent_interactions table
CREATE TABLE IF NOT EXISTS agent_interactions (
    id VARCHAR(36) PRIMARY KEY,
    business_profile_id VARCHAR(36) NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,
    agent_id VARCHAR(100) NOT NULL,
    user_message TEXT,
    agent_response TEXT,
    context_used JSONB DEFAULT '[]',
    tools_used JSONB DEFAULT '[]',
    insights_generated JSONB DEFAULT '[]',
    outcome VARCHAR(50) DEFAULT 'unknown',
    interaction_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for agent_interactions
CREATE INDEX IF NOT EXISTS idx_agent_interactions_profile_agent ON agent_interactions(business_profile_id, agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_interactions_created ON agent_interactions(created_at);
CREATE INDEX IF NOT EXISTS idx_agent_interactions_outcome ON agent_interactions(outcome);

-- Create shared_contexts table
CREATE TABLE IF NOT EXISTS shared_contexts (
    id VARCHAR(36) PRIMARY KEY,
    business_profile_id VARCHAR(36) NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,
    source_agent_id VARCHAR(100) NOT NULL,
    target_agent_id VARCHAR(100) NOT NULL,
    context_type VARCHAR(50) NOT NULL,
    context_data JSONB NOT NULL,
    context_metadata JSONB DEFAULT '{}',
    is_consumed BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for shared_contexts
CREATE INDEX IF NOT EXISTS idx_shared_contexts_target ON shared_contexts(business_profile_id, target_agent_id);
CREATE INDEX IF NOT EXISTS idx_shared_contexts_source ON shared_contexts(business_profile_id, source_agent_id);
CREATE INDEX IF NOT EXISTS idx_shared_contexts_expires ON shared_contexts(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_shared_contexts_unconsumed ON shared_contexts(business_profile_id, target_agent_id, is_consumed) WHERE is_consumed = FALSE;

-- Create triggers to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_agent_insights_updated_at()
RETURNS TRIGGER AS $func$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$func$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_agent_insights_updated_at
    BEFORE UPDATE ON agent_insights
    FOR EACH ROW
    EXECUTE FUNCTION update_agent_insights_updated_at();

CREATE OR REPLACE FUNCTION update_agent_interactions_updated_at()
RETURNS TRIGGER AS $func$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$func$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_agent_interactions_updated_at
    BEFORE UPDATE ON agent_interactions
    FOR EACH ROW
    EXECUTE FUNCTION update_agent_interactions_updated_at();

CREATE OR REPLACE FUNCTION update_shared_contexts_updated_at()
RETURNS TRIGGER AS $func$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$func$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_shared_contexts_updated_at
    BEFORE UPDATE ON shared_contexts
    FOR EACH ROW
    EXECUTE FUNCTION update_shared_contexts_updated_at();

-- Create function to automatically clean up expired shared contexts
CREATE OR REPLACE FUNCTION cleanup_expired_shared_contexts()
RETURNS INTEGER AS $func$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM shared_contexts
    WHERE expires_at IS NOT NULL AND expires_at < NOW();

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$func$ LANGUAGE plpgsql;

-- Create function to get cross-agent intelligence statistics
CREATE OR REPLACE FUNCTION get_cross_agent_stats(profile_id VARCHAR(36))
RETURNS TABLE(
    total_insights BIGINT,
    total_interactions BIGINT,
    active_agents BIGINT,
    recent_activity_7d BIGINT,
    top_insight_type TEXT,
    most_active_agent TEXT
) AS $func$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM agent_insights WHERE business_profile_id = profile_id AND is_active = TRUE) as total_insights,
        (SELECT COUNT(*) FROM agent_interactions WHERE business_profile_id = profile_id) as total_interactions,
        (SELECT COUNT(DISTINCT agent_id) FROM agent_interactions WHERE business_profile_id = profile_id) as active_agents,
        (SELECT COUNT(*) FROM agent_interactions WHERE business_profile_id = profile_id AND created_at >= NOW() - INTERVAL '7 days') as recent_activity_7d,
        (SELECT insight_type FROM agent_insights WHERE business_profile_id = profile_id AND is_active = TRUE GROUP BY insight_type ORDER BY COUNT(*) DESC LIMIT 1) as top_insight_type,
        (SELECT agent_id FROM agent_interactions WHERE business_profile_id = profile_id GROUP BY agent_id ORDER BY COUNT(*) DESC LIMIT 1) as most_active_agent;
END;
$func$ LANGUAGE plpgsql;

-- Create function to get agent collaboration patterns
CREATE OR REPLACE FUNCTION get_collaboration_patterns(profile_id VARCHAR(36))
RETURNS TABLE(
    source_agent VARCHAR(100),
    target_agent VARCHAR(100),
    collaboration_count BIGINT,
    last_collaboration TIMESTAMP WITH TIME ZONE
) AS $func$
BEGIN
    RETURN QUERY
    SELECT
        sc.source_agent_id as source_agent,
        sc.target_agent_id as target_agent,
        COUNT(*) as collaboration_count,
        MAX(sc.created_at) as last_collaboration
    FROM shared_contexts sc
    WHERE sc.business_profile_id = profile_id
    GROUP BY sc.source_agent_id, sc.target_agent_id
    ORDER BY collaboration_count DESC;
END;
$func$ LANGUAGE plpgsql;

-- Create view for agent performance metrics
CREATE OR REPLACE VIEW agent_performance_view AS
SELECT 
    ai.business_profile_id,
    ai.agent_id,
    COUNT(ai.id) as total_interactions,
    COUNT(CASE WHEN ai.outcome = 'success' THEN 1 END) as successful_interactions,
    ROUND(
        COUNT(CASE WHEN ai.outcome = 'success' THEN 1 END)::DECIMAL / 
        NULLIF(COUNT(ai.id), 0) * 100, 2
    ) as success_rate,
    COUNT(DISTINCT DATE(ai.created_at)) as active_days,
    AVG(LENGTH(ai.agent_response)) as avg_response_length,
    MAX(ai.created_at) as last_activity
FROM agent_interactions ai
GROUP BY ai.business_profile_id, ai.agent_id;

-- Create view for insight analytics
CREATE OR REPLACE VIEW insight_analytics_view AS
SELECT 
    ins.business_profile_id,
    ins.source_agent_id,
    ins.insight_type,
    COUNT(*) as insight_count,
    AVG(ins.confidence_score) as avg_confidence,
    SUM(ins.access_count) as total_accesses,
    MAX(ins.created_at) as latest_insight,
    COUNT(CASE WHEN ins.last_accessed_at IS NOT NULL THEN 1 END) as accessed_insights
FROM agent_insights ins
WHERE ins.is_active = TRUE
GROUP BY ins.business_profile_id, ins.source_agent_id, ins.insight_type;

-- Add constraints for data integrity
ALTER TABLE agent_insights ADD CONSTRAINT chk_confidence_score CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0);
ALTER TABLE agent_insights ADD CONSTRAINT chk_access_count CHECK (access_count >= 0);

-- Add constraint to prevent self-referencing shared contexts
ALTER TABLE shared_contexts ADD CONSTRAINT chk_no_self_sharing CHECK (source_agent_id != target_agent_id);

-- Create partial indexes for performance (without NOW() function to avoid immutability issues)
CREATE INDEX IF NOT EXISTS idx_agent_insights_active_profile ON agent_insights(business_profile_id, created_at)
WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_shared_contexts_unconsumed_profile ON shared_contexts(business_profile_id, target_agent_id, created_at)
WHERE is_consumed = FALSE;

-- Add comments to tables and columns
COMMENT ON TABLE agent_insights IS 'Stores insights generated by AI agents for cross-agent intelligence sharing';
COMMENT ON TABLE agent_interactions IS 'Records all agent interactions for learning and collaboration';
COMMENT ON TABLE shared_contexts IS 'Manages shared context between agents for collaboration';

COMMENT ON COLUMN agent_insights.confidence_score IS 'Confidence score for the insight (0.0 to 1.0)';
COMMENT ON COLUMN agent_insights.relevance_tags IS 'JSON array of tags for relevance matching';
COMMENT ON COLUMN agent_insights.access_count IS 'Number of times this insight has been accessed by other agents';

COMMENT ON COLUMN agent_interactions.context_used IS 'JSON array of context items used in the interaction';
COMMENT ON COLUMN agent_interactions.tools_used IS 'JSON array of tools used during the interaction';
COMMENT ON COLUMN agent_interactions.insights_generated IS 'JSON array of insight IDs generated from this interaction';

COMMENT ON COLUMN shared_contexts.context_data IS 'JSON object containing the shared context data';
COMMENT ON COLUMN shared_contexts.is_consumed IS 'Whether the target agent has consumed this shared context';
COMMENT ON COLUMN shared_contexts.expires_at IS 'When this shared context expires (NULL for no expiration)';

-- Insert sample data for testing (optional - remove in production)
/*
-- Sample insights
INSERT INTO agent_insights (id, business_profile_id, source_agent_id, insight_type, content, relevance_tags, confidence_score) VALUES
('sample-insight-1', 'sample-profile-uuid', 'composable-marketing-ai', 'marketing_strategy', 'Focus on digital marketing channels for better ROI', '["marketing", "digital", "roi"]', 0.9),
('sample-insight-2', 'sample-profile-uuid', 'composable-analysis-ai', 'data_insights', 'Customer acquisition cost has increased by 15% this quarter', '["customer", "acquisition", "cost", "trends"]', 0.85);

-- Sample interactions
INSERT INTO agent_interactions (id, business_profile_id, agent_id, user_message, agent_response, outcome) VALUES
('sample-interaction-1', 'sample-profile-uuid', 'composable-marketing-ai', 'Create a marketing strategy', 'Here is a comprehensive marketing strategy...', 'success'),
('sample-interaction-2', 'sample-profile-uuid', 'composable-analysis-ai', 'Analyze sales data', 'Based on the sales data analysis...', 'success');
*/

-- Create scheduled job to clean up expired data (PostgreSQL specific)
-- This would typically be set up as a cron job or scheduled task
-- SELECT cron.schedule('cleanup-expired-contexts', '0 2 * * *', 'SELECT cleanup_expired_shared_contexts();');
