import React, { useState } from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Sparkles,
  TrendingUp,
  Users,
  Target,
  Zap,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Play,
  FileText,
  BarChart3,
  Lightbulb,
  Gift
} from 'lucide-react';

interface OnboardingFlowProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (selectedActions: string[]) => void;
}

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  content: React.ReactNode;
  actions?: {
    id: string;
    title: string;
    description: string;
    actionType: string;
    recommended?: boolean;
  }[];
}

export const OnboardingFlow: React.FC<OnboardingFlowProps> = ({
  isOpen,
  onClose,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedActions, setSelectedActions] = useState<string[]>([]);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Composable Marketer!',
      description: 'Your AI-powered marketing assistant',
      icon: Sparkles,
      content: (
        <div className="text-center space-y-6">
          <div className="mx-auto w-20 h-20 bg-gradient-to-br from-brand-500 to-brand-600 rounded-full flex items-center justify-center">
            <Sparkles className="h-10 w-10 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Ready to accelerate your marketing success?
            </h3>
            <p className="text-gray-600">
              I'm here to help you create professional marketing content that drives real business results. 
              Let's get you started with a quick tour of what I can do for you.
            </p>
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span>Save 10+ hours per week</span>
            </div>
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span>Professional quality content</span>
            </div>
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span>Data-driven strategies</span>
            </div>
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span>Instant results</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'capabilities',
      title: 'What I Can Do For You',
      description: 'Explore my core capabilities',
      icon: Zap,
      content: (
        <div className="space-y-4">
          <div className="grid gap-4">
            <Card className="border-l-4 border-l-blue-500">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <TrendingUp className="h-6 w-6 text-blue-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Strategic Planning</h4>
                    <p className="text-sm text-gray-600">
                      Comprehensive marketing strategies, competitor analysis, and market research
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-l-4 border-l-green-500">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <FileText className="h-6 w-6 text-green-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Content Creation</h4>
                    <p className="text-sm text-gray-600">
                      Blog posts, email campaigns, social media content, and ad copy
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-l-4 border-l-purple-500">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <Target className="h-6 w-6 text-purple-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Campaign Management</h4>
                    <p className="text-sm text-gray-600">
                      Multi-channel campaigns, SEO optimization, and performance tracking
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-l-4 border-l-orange-500">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <BarChart3 className="h-6 w-6 text-orange-600 mt-1" />
                  <div>
                    <h4 className="font-semibold text-gray-900">Data Analysis</h4>
                    <p className="text-sm text-gray-600">
                      Audience insights, market trends, and performance optimization
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )
    },
    {
      id: 'quick-wins',
      title: 'Choose Your First Quick Win',
      description: 'Select what you\'d like to create first',
      icon: Gift,
      content: (
        <div className="space-y-4">
          <p className="text-gray-600 text-center">
            Let's start with something that will give you immediate value. 
            Choose one or more actions to get started:
          </p>
        </div>
      ),
      actions: [
        {
          id: 'marketing_strategy',
          title: 'Marketing Strategy',
          description: 'Get a comprehensive marketing plan tailored to your business',
          actionType: 'marketing_strategy',
          recommended: true
        },
        {
          id: 'social_media_content',
          title: 'Social Media Content',
          description: 'Create engaging posts for your social media channels',
          actionType: 'social_media_content'
        },
        {
          id: 'blog_content',
          title: 'Blog Content',
          description: 'Generate SEO-optimized blog posts that drive traffic',
          actionType: 'blog_content'
        },
        {
          id: 'competitor_analysis',
          title: 'Competitor Analysis',
          description: 'Understand your competitive landscape and opportunities',
          actionType: 'competitor_analysis'
        }
      ]
    },
    {
      id: 'tips',
      title: 'Pro Tips for Success',
      description: 'Make the most of your marketing assistant',
      icon: Lightbulb,
      content: (
        <div className="space-y-6">
          <div className="grid gap-4">
            <div className="flex items-start gap-3 p-4 bg-blue-50 rounded-lg">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-semibold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-semibold text-blue-900">Upload Your Data</h4>
                <p className="text-sm text-blue-800">
                  Upload business documents, brand guidelines, or competitor research for personalized recommendations
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-green-50 rounded-lg">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-green-600 font-semibold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-semibold text-green-900">Be Specific</h4>
                <p className="text-sm text-green-800">
                  The more details you provide about your business and goals, the better I can help you
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-purple-50 rounded-lg">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-purple-600 font-semibold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-semibold text-purple-900">Iterate and Improve</h4>
                <p className="text-sm text-purple-800">
                  Ask follow-up questions and request modifications to get exactly what you need
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-orange-50 rounded-lg">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-orange-600 font-semibold text-sm">4</span>
              </div>
              <div>
                <h4 className="font-semibold text-orange-900">Use Templates</h4>
                <p className="text-sm text-orange-800">
                  Browse our template gallery for quick-start options and industry-specific examples
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(selectedActions);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleActionToggle = (actionType: string) => {
    setSelectedActions(prev => 
      prev.includes(actionType) 
        ? prev.filter(a => a !== actionType)
        : [...prev, actionType]
    );
  };

  const isLastStep = currentStep === steps.length - 1;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <currentStepData.icon className="h-5 w-5 text-brand-600" />
            {currentStepData.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>Step {currentStep + 1} of {steps.length}</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Step Content */}
          <div className="min-h-[300px]">
            <p className="text-gray-600 mb-6">{currentStepData.description}</p>
            {currentStepData.content}
            
            {/* Actions for quick wins step */}
            {currentStepData.actions && (
              <div className="grid gap-3 mt-6">
                {currentStepData.actions.map((action) => (
                  <Card 
                    key={action.id}
                    className={`cursor-pointer transition-all border-2 ${
                      selectedActions.includes(action.actionType)
                        ? 'border-brand-500 bg-brand-50'
                        : 'border-gray-200 hover:border-brand-300'
                    }`}
                    onClick={() => handleActionToggle(action.actionType)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-gray-900">
                              {action.title}
                            </h4>
                            {action.recommended && (
                              <Badge className="bg-green-100 text-green-800">
                                Recommended
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">
                            {action.description}
                          </p>
                        </div>
                        <div className="ml-4">
                          {selectedActions.includes(action.actionType) ? (
                            <CheckCircle className="h-5 w-5 text-brand-600" />
                          ) : (
                            <div className="w-5 h-5 border-2 border-gray-300 rounded-full" />
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Navigation */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                Skip Tour
              </Button>
              
              <Button onClick={handleNext}>
                {isLastStep ? (
                  <>
                    Get Started
                    <Play className="h-4 w-4 ml-2" />
                  </>
                ) : (
                  <>
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
