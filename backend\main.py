# backend/main.py
"""
Main FastAPI application entry point (Transitional).

NOTE: This file is being phased out in favor of the new modular structure
defined in the app/ directory and server.py.

This file imports the FastAPI app instance from the new structure (`app.main`)
and runs it using uvicorn when executed directly. This provides backward
compatibility during the transition period.

Recommended way to run the application:
    python server.py
"""

import logging

# Import the 'app' instance from the new main application file
try:
    from app.main import app
    logger = logging.getLogger(__name__)
    logger.info("Successfully imported 'app' from app.main")
except ImportError as e:
    # Log a critical error if the import fails, as this file relies on it
    logging.basicConfig(level=logging.CRITICAL)
    logger = logging.getLogger(__name__)
    logger.critical(f"Failed to import 'app' from app.main: {e}", exc_info=True)
    # Define a dummy app to prevent NameError if uvicorn tries to run anyway
    from fastapi import FastAPI
    app = FastAPI(title="Import Error Placeholder")
    @app.get("/")
    def import_error_route():
        return {"error": "Failed to import application from app.main. Check logs."}

# This guard allows running the old main.py directly, which now uses the new app structure.
if __name__ == "__main__":
    import uvicorn
    logger.info("Running application using uvicorn via backend/main.py (transitional)...")
    # Run the imported app instance
    # Use host="0.0.0.0" to be accessible on the network, or "127.0.0.1" for local only
    # Reload=True is useful for development
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False) # Reload might be problematic here
