"""
Knowledge Graph MCP tool for the Datagenius backend.

This module provides an MCP tool for interacting with the knowledge graph,
including entity extraction, relationship extraction, and graph querying.
"""

import logging
import os
import json
from typing import Dict, Any, List, Optional
import uuid

from ..mcp.base import BaseMCPTool
from ...knowledge_graph.schema import (
    Entity, Relationship, KnowledgeGraph, EntityType, RelationshipType,
    Property, KnowledgeGraphQuery, KnowledgeGraphQueryResult
)
from ...knowledge_graph.store import KnowledgeGraphStore
from ...knowledge_graph.entity_extraction import EntityExtractor

logger = logging.getLogger(__name__)


class KnowledgeGraphTool(BaseMCPTool):
    """MCP tool for knowledge graph operations."""

    def __init__(self):
        """Initialize the knowledge graph tool."""
        super().__init__(
            name="knowledge_graph",
            description="Interact with the knowledge graph, including entity extraction, relationship extraction, and graph querying.",
            input_schema={
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": [
                            "extract_entities",
                            "extract_relationships",
                            "query_graph",
                            "create_graph",
                            "load_graph",
                            "save_graph",
                            "add_entity",
                            "add_relationship",
                            "get_entity",
                            "get_relationship",
                            "extract_subgraph",
                            "visualize_graph"
                        ],
                        "description": "The operation to perform on the knowledge graph."
                    },
                    "text": {
                        "type": "string",
                        "description": "Text to extract entities or relationships from."
                    },
                    "file_path": {
                        "type": "string",
                        "description": "Path to a file to extract entities or relationships from."
                    },
                    "graph_id": {
                        "type": "string",
                        "description": "ID of the knowledge graph to operate on."
                    },
                    "entity": {
                        "type": "object",
                        "description": "Entity to add to the knowledge graph."
                    },
                    "relationship": {
                        "type": "object",
                        "description": "Relationship to add to the knowledge graph."
                    },
                    "entity_id": {
                        "type": "string",
                        "description": "ID of an entity to retrieve or operate on."
                    },
                    "relationship_id": {
                        "type": "string",
                        "description": "ID of a relationship to retrieve or operate on."
                    },
                    "query": {
                        "type": "object",
                        "description": "Query to execute on the knowledge graph."
                    },
                    "entity_ids": {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "description": "IDs of entities to include in a subgraph."
                    },
                    "include_neighbors": {
                        "type": "boolean",
                        "description": "Whether to include neighbors of entities in a subgraph."
                    },
                    "use_embeddings": {
                        "type": "boolean",
                        "description": "Whether to generate embeddings for extracted entities."
                    },
                    "embeddings_model": {
                        "type": "string",
                        "description": "Name of the embeddings model to use."
                    }
                },
                "required": ["operation"]
            },
            annotations={
                "title": "Knowledge Graph",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.graph_store = None
        self.entity_extractor = None

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the knowledge graph tool.

        Args:
            config: Configuration dictionary.
        """
        # Set up the knowledge graph store
        graph_dir = config.get("graph_dir", "knowledge_graphs")
        self.graph_store = KnowledgeGraphStore(graph_dir=graph_dir)
        
        # Set up the entity extractor
        use_embeddings = config.get("use_embeddings", True)
        embeddings_model = config.get("embeddings_model", "all-MiniLM-L6-v2")
        self.entity_extractor = EntityExtractor(
            use_embeddings=use_embeddings,
            embeddings_model=embeddings_model
        )
        
        logger.info(f"Initialized knowledge graph tool with graph directory: {graph_dir}")

    async def execute(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the knowledge graph tool.

        Args:
            parameters: Parameters for the tool.

        Returns:
            Result of the operation.
        """
        operation = parameters.get("operation")
        
        if not self.graph_store or not self.entity_extractor:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Knowledge graph tool not initialized."
                    }
                ],
                "metadata": {
                    "error": "Knowledge graph tool not initialized."
                }
            }
        
        try:
            if operation == "extract_entities":
                return await self._extract_entities(parameters)
            elif operation == "extract_relationships":
                return await self._extract_relationships(parameters)
            elif operation == "query_graph":
                return await self._query_graph(parameters)
            elif operation == "create_graph":
                return await self._create_graph(parameters)
            elif operation == "load_graph":
                return await self._load_graph(parameters)
            elif operation == "save_graph":
                return await self._save_graph(parameters)
            elif operation == "add_entity":
                return await self._add_entity(parameters)
            elif operation == "add_relationship":
                return await self._add_relationship(parameters)
            elif operation == "get_entity":
                return await self._get_entity(parameters)
            elif operation == "get_relationship":
                return await self._get_relationship(parameters)
            elif operation == "extract_subgraph":
                return await self._extract_subgraph(parameters)
            elif operation == "visualize_graph":
                return await self._visualize_graph(parameters)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unknown operation: {operation}"
                        }
                    ],
                    "metadata": {
                        "error": f"Unknown operation: {operation}"
                    }
                }
        except Exception as e:
            logger.error(f"Error executing knowledge graph operation {operation}: {str(e)}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error executing knowledge graph operation {operation}: {str(e)}"
                    }
                ],
                "metadata": {
                    "error": str(e)
                }
            }

    async def _extract_entities(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract entities from text.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get text from parameters
        text = parameters.get("text")
        file_path = parameters.get("file_path")
        
        if not text and not file_path:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Either text or file_path must be provided."
                    }
                ],
                "metadata": {
                    "error": "Either text or file_path must be provided."
                }
            }
        
        # If file_path is provided, read the file
        if file_path and not text:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    text = f.read()
            except Exception as e:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Error reading file {file_path}: {str(e)}"
                        }
                    ],
                    "metadata": {
                        "error": str(e)
                    }
                }
        
        # Configure entity extractor
        use_embeddings = parameters.get("use_embeddings")
        embeddings_model = parameters.get("embeddings_model")
        
        if use_embeddings is not None or embeddings_model is not None:
            self.entity_extractor = EntityExtractor(
                use_embeddings=use_embeddings if use_embeddings is not None else True,
                embeddings_model=embeddings_model or "all-MiniLM-L6-v2"
            )
        
        # Extract entities
        source = parameters.get("graph_id") or file_path
        entities = await self.entity_extractor.extract_entities(text, source=source)
        
        # Format the result
        entity_dicts = [entity.model_dump() for entity in entities]
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Extracted {len(entities)} entities from the text."
                }
            ],
            "metadata": {
                "entities": entity_dicts
            }
        }

    async def _extract_relationships(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract relationships from text.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get text and entities from parameters
        text = parameters.get("text")
        file_path = parameters.get("file_path")
        entities = parameters.get("entities")
        
        if not text and not file_path:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Either text or file_path must be provided."
                    }
                ],
                "metadata": {
                    "error": "Either text or file_path must be provided."
                }
            }
        
        if not entities:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Entities must be provided."
                    }
                ],
                "metadata": {
                    "error": "Entities must be provided."
                }
            }
        
        # If file_path is provided, read the file
        if file_path and not text:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    text = f.read()
            except Exception as e:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Error reading file {file_path}: {str(e)}"
                        }
                    ],
                    "metadata": {
                        "error": str(e)
                    }
                }
        
        # Convert entity dictionaries to Entity objects
        entity_objects = []
        for entity_dict in entities:
            try:
                entity = Entity(**entity_dict)
                entity_objects.append(entity)
            except Exception as e:
                logger.warning(f"Error converting entity dictionary to Entity: {str(e)}")
        
        # Extract relationships
        relationships = await self.entity_extractor.extract_relationships(text, entity_objects)
        
        # Format the result
        relationship_dicts = [relationship.model_dump() for relationship in relationships]
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Extracted {len(relationships)} relationships from the text."
                }
            ],
            "metadata": {
                "relationships": relationship_dicts
            }
        }

    async def _query_graph(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Query the knowledge graph.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get query from parameters
        query_dict = parameters.get("query")
        
        if not query_dict:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Query must be provided."
                    }
                ],
                "metadata": {
                    "error": "Query must be provided."
                }
            }
        
        # Convert query dictionary to KnowledgeGraphQuery object
        try:
            query = KnowledgeGraphQuery(**query_dict)
        except Exception as e:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error converting query dictionary to KnowledgeGraphQuery: {str(e)}"
                    }
                ],
                "metadata": {
                    "error": str(e)
                }
            }
        
        # Execute the query
        result = self.graph_store.query(query)
        
        # Format the result
        result_dict = result.model_dump()
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Query executed successfully. Found {len(result.entities)} entities and {len(result.relationships)} relationships."
                }
            ],
            "metadata": {
                "result": result_dict
            }
        }

    async def _create_graph(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new knowledge graph.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get metadata from parameters
        metadata = parameters.get("metadata", {})
        
        # Create the graph
        graph_id = self.graph_store.create_graph(metadata=metadata)
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Created new knowledge graph with ID: {graph_id}"
                }
            ],
            "metadata": {
                "graph_id": graph_id
            }
        }

    async def _load_graph(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Load a knowledge graph from disk.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get graph ID from parameters
        graph_id = parameters.get("graph_id")
        
        if not graph_id:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Graph ID must be provided."
                    }
                ],
                "metadata": {
                    "error": "Graph ID must be provided."
                }
            }
        
        # Load the graph
        success = self.graph_store.load_graph(graph_id)
        
        if not success:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error loading graph {graph_id}."
                    }
                ],
                "metadata": {
                    "error": f"Error loading graph {graph_id}."
                }
            }
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Loaded knowledge graph {graph_id} with {len(self.graph_store.graph.nodes)} entities and {len(self.graph_store.graph.edges)} relationships."
                }
            ],
            "metadata": {
                "graph_id": graph_id,
                "entity_count": len(self.graph_store.graph.nodes),
                "relationship_count": len(self.graph_store.graph.edges)
            }
        }

    async def _save_graph(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Save the current knowledge graph to disk.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get graph ID from parameters
        graph_id = parameters.get("graph_id")
        
        # Save the graph
        success = self.graph_store.save_graph(graph_id)
        
        if not success:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error saving graph {graph_id or self.graph_store.current_graph_id}."
                    }
                ],
                "metadata": {
                    "error": f"Error saving graph {graph_id or self.graph_store.current_graph_id}."
                }
            }
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Saved knowledge graph {graph_id or self.graph_store.current_graph_id}."
                }
            ],
            "metadata": {
                "graph_id": graph_id or self.graph_store.current_graph_id
            }
        }

    async def _add_entity(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add an entity to the knowledge graph.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get entity from parameters
        entity_dict = parameters.get("entity")
        
        if not entity_dict:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Entity must be provided."
                    }
                ],
                "metadata": {
                    "error": "Entity must be provided."
                }
            }
        
        # Convert entity dictionary to Entity object
        try:
            entity = Entity(**entity_dict)
        except Exception as e:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error converting entity dictionary to Entity: {str(e)}"
                    }
                ],
                "metadata": {
                    "error": str(e)
                }
            }
        
        # Add the entity to the graph
        success = self.graph_store.add_entity(entity)
        
        if not success:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error adding entity {entity.id} to the graph."
                    }
                ],
                "metadata": {
                    "error": f"Error adding entity {entity.id} to the graph."
                }
            }
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Added entity {entity.id} to the graph."
                }
            ],
            "metadata": {
                "entity_id": entity.id
            }
        }

    async def _add_relationship(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a relationship to the knowledge graph.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get relationship from parameters
        relationship_dict = parameters.get("relationship")
        
        if not relationship_dict:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Relationship must be provided."
                    }
                ],
                "metadata": {
                    "error": "Relationship must be provided."
                }
            }
        
        # Convert relationship dictionary to Relationship object
        try:
            relationship = Relationship(**relationship_dict)
        except Exception as e:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error converting relationship dictionary to Relationship: {str(e)}"
                    }
                ],
                "metadata": {
                    "error": str(e)
                }
            }
        
        # Add the relationship to the graph
        success = self.graph_store.add_relationship(relationship)
        
        if not success:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error adding relationship {relationship.id} to the graph."
                    }
                ],
                "metadata": {
                    "error": f"Error adding relationship {relationship.id} to the graph."
                }
            }
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Added relationship {relationship.id} to the graph."
                }
            ],
            "metadata": {
                "relationship_id": relationship.id
            }
        }

    async def _get_entity(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get an entity from the knowledge graph.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get entity ID from parameters
        entity_id = parameters.get("entity_id")
        
        if not entity_id:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Entity ID must be provided."
                    }
                ],
                "metadata": {
                    "error": "Entity ID must be provided."
                }
            }
        
        # Get the entity
        entity = self.graph_store.get_entity(entity_id)
        
        if not entity:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Entity {entity_id} not found in the graph."
                    }
                ],
                "metadata": {
                    "error": f"Entity {entity_id} not found in the graph."
                }
            }
        
        # Format the result
        entity_dict = entity.model_dump()
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Retrieved entity {entity_id} from the graph."
                }
            ],
            "metadata": {
                "entity": entity_dict
            }
        }

    async def _get_relationship(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get a relationship from the knowledge graph.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get relationship ID from parameters
        relationship_id = parameters.get("relationship_id")
        
        if not relationship_id:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Relationship ID must be provided."
                    }
                ],
                "metadata": {
                    "error": "Relationship ID must be provided."
                }
            }
        
        # Get the relationship
        relationship = self.graph_store.get_relationship(relationship_id)
        
        if not relationship:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Relationship {relationship_id} not found in the graph."
                    }
                ],
                "metadata": {
                    "error": f"Relationship {relationship_id} not found in the graph."
                }
            }
        
        # Format the result
        relationship_dict = relationship.model_dump()
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Retrieved relationship {relationship_id} from the graph."
                }
            ],
            "metadata": {
                "relationship": relationship_dict
            }
        }

    async def _extract_subgraph(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract a subgraph from the knowledge graph.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get entity IDs and include_neighbors from parameters
        entity_ids = parameters.get("entity_ids")
        include_neighbors = parameters.get("include_neighbors", False)
        
        if not entity_ids:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Entity IDs must be provided."
                    }
                ],
                "metadata": {
                    "error": "Entity IDs must be provided."
                }
            }
        
        # Extract the subgraph
        subgraph = self.graph_store.extract_subgraph(entity_ids, include_neighbors=include_neighbors)
        
        # Format the result
        subgraph_dict = subgraph.model_dump()
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Extracted subgraph with {len(subgraph.entities)} entities and {len(subgraph.relationships)} relationships."
                }
            ],
            "metadata": {
                "subgraph": subgraph_dict
            }
        }

    async def _visualize_graph(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a visualization of the knowledge graph.

        Args:
            parameters: Parameters for the operation.

        Returns:
            Result of the operation.
        """
        # Get entity IDs and include_neighbors from parameters
        entity_ids = parameters.get("entity_ids")
        include_neighbors = parameters.get("include_neighbors", True)
        
        # Extract the subgraph if entity IDs are provided
        if entity_ids:
            subgraph = self.graph_store.extract_subgraph(entity_ids, include_neighbors=include_neighbors)
        else:
            # Use the full graph
            subgraph = KnowledgeGraph(
                entities={node_id: Entity(**node_data) for node_id, node_data in self.graph_store.graph.nodes(data=True)},
                relationships={edge_data.get("id"): Relationship(**edge_data) for _, _, edge_data in self.graph_store.graph.edges(data=True) if "id" in edge_data},
                metadata={"source_graph_id": self.graph_store.current_graph_id}
            )
        
        # Convert the subgraph to a format suitable for visualization
        nodes = []
        for entity_id, entity in subgraph.entities.items():
            nodes.append({
                "id": entity_id,
                "name": entity.name,
                "type": entity.type,
                "description": entity.description,
                "color": self._get_entity_color(entity.type)
            })
        
        links = []
        for relationship_id, relationship in subgraph.relationships.items():
            links.append({
                "id": relationship_id,
                "source": relationship.source_id,
                "target": relationship.target_id,
                "type": relationship.type,
                "value": relationship.weight or 1
            })
        
        # Create the visualization data
        visualization_data = {
            "type": "network",
            "title": "Knowledge Graph Visualization",
            "description": f"Visualization of knowledge graph with {len(nodes)} entities and {len(links)} relationships.",
            "data": {
                "nodes": nodes,
                "links": links
            }
        }
        
        return {
            "isError": False,
            "content": [
                {
                    "type": "text",
                    "text": f"Generated visualization of knowledge graph with {len(nodes)} entities and {len(links)} relationships."
                }
            ],
            "metadata": {
                "visualization": visualization_data
            }
        }

    def _get_entity_color(self, entity_type: EntityType) -> str:
        """
        Get a color for an entity type.

        Args:
            entity_type: Entity type.

        Returns:
            Color for the entity type.
        """
        color_map = {
            EntityType.CONCEPT: "#1f77b4",
            EntityType.PERSON: "#ff7f0e",
            EntityType.ORGANIZATION: "#2ca02c",
            EntityType.LOCATION: "#d62728",
            EntityType.EVENT: "#9467bd",
            EntityType.PRODUCT: "#8c564b",
            EntityType.DOCUMENT: "#e377c2",
            EntityType.TOPIC: "#7f7f7f",
            EntityType.CATEGORY: "#bcbd22",
            EntityType.CUSTOM: "#17becf"
        }
        
        return color_map.get(entity_type, "#1f77b4")
