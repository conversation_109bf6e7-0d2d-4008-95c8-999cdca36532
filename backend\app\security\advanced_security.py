"""
Advanced security system for Datagenius.

This module provides comprehensive security features including
threat detection, rate limiting, input validation, and security monitoring.
"""

import logging
import time
import hashlib
import re
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from functools import wraps
from ipaddress import ip_address, ip_network

from fastapi import HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from ..database import get_db
from ..models.auth import User
from ..monitoring.metrics import metrics

logger = logging.getLogger(__name__)


@dataclass
class SecurityEvent:
    """Represents a security event."""
    event_type: str
    severity: str  # low, medium, high, critical
    source_ip: str
    user_id: Optional[str]
    description: str
    timestamp: datetime
    metadata: Dict[str, Any]


class ThreatDetector:
    """
    Advanced threat detection system.
    """

    def __init__(self):
        """Initialize threat detector."""
        self.suspicious_patterns = {
            'sql_injection': [
                r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
                r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
                r"(--|#|/\*|\*/)",
                r"(\bxp_cmdshell\b|\bsp_executesql\b)"
            ],
            'xss': [
                r"<script[^>]*>.*?</script>",
                r"javascript:",
                r"on\w+\s*=",
                r"<iframe[^>]*>.*?</iframe>"
            ],
            'path_traversal': [
                r"\.\./",
                r"\.\.\\",
                r"/etc/passwd",
                r"/proc/",
                r"\\windows\\system32"
            ],
            'command_injection': [
                r"[;&|`$]",
                r"\b(cat|ls|pwd|whoami|id|uname)\b",
                r"\$\([^)]*\)",
                r"`[^`]*`"
            ]
        }
        
        self.blocked_ips: Set[str] = set()
        self.suspicious_ips: Dict[str, int] = {}
        self.security_events: List[SecurityEvent] = []
        self.max_events = 10000

    def detect_threats(self, input_data: str, source_ip: str, user_id: Optional[str] = None) -> List[str]:
        """
        Detect potential threats in input data.
        
        Args:
            input_data: Data to analyze
            source_ip: Source IP address
            user_id: User ID if authenticated
            
        Returns:
            List of detected threat types
        """
        threats = []
        
        for threat_type, patterns in self.suspicious_patterns.items():
            for pattern in patterns:
                if re.search(pattern, input_data, re.IGNORECASE):
                    threats.append(threat_type)
                    
                    # Log security event
                    self._log_security_event(
                        event_type=f"threat_detected_{threat_type}",
                        severity="high",
                        source_ip=source_ip,
                        user_id=user_id,
                        description=f"Detected {threat_type} pattern in input",
                        metadata={"pattern": pattern, "input_sample": input_data[:100]}
                    )
                    break
        
        return threats

    def is_ip_blocked(self, ip: str) -> bool:
        """Check if IP is blocked."""
        return ip in self.blocked_ips

    def block_ip(self, ip: str, reason: str):
        """Block an IP address."""
        self.blocked_ips.add(ip)
        self._log_security_event(
            event_type="ip_blocked",
            severity="high",
            source_ip=ip,
            user_id=None,
            description=f"IP blocked: {reason}",
            metadata={"reason": reason}
        )

    def mark_suspicious_ip(self, ip: str):
        """Mark IP as suspicious."""
        self.suspicious_ips[ip] = self.suspicious_ips.get(ip, 0) + 1
        
        # Auto-block after threshold
        if self.suspicious_ips[ip] >= 5:
            self.block_ip(ip, "Multiple suspicious activities")

    def _log_security_event(self, event_type: str, severity: str, source_ip: str, 
                           user_id: Optional[str], description: str, metadata: Dict[str, Any]):
        """Log a security event."""
        event = SecurityEvent(
            event_type=event_type,
            severity=severity,
            source_ip=source_ip,
            user_id=user_id,
            description=description,
            timestamp=datetime.now(),
            metadata=metadata
        )
        
        self.security_events.append(event)
        
        # Keep only recent events
        if len(self.security_events) > self.max_events:
            self.security_events = self.security_events[-self.max_events:]
        
        # Log to application logger
        logger.warning(f"Security Event [{severity}]: {description} from {source_ip}")

    def get_security_summary(self) -> Dict[str, Any]:
        """Get security summary."""
        recent_events = [e for e in self.security_events if e.timestamp > datetime.now() - timedelta(hours=24)]
        
        event_counts = {}
        for event in recent_events:
            event_counts[event.event_type] = event_counts.get(event.event_type, 0) + 1
        
        return {
            "total_events_24h": len(recent_events),
            "blocked_ips": len(self.blocked_ips),
            "suspicious_ips": len(self.suspicious_ips),
            "event_types": event_counts,
            "critical_events": len([e for e in recent_events if e.severity == "critical"])
        }


class RateLimiter:
    """
    Advanced rate limiting system with improved algorithm support.

    DEPRECATED: This Fixed Window implementation is vulnerable to burst attacks.
    Use ImprovedRateLimiter with Sliding Window Counter for better security.
    """

    def __init__(self, use_improved_algorithm: bool = True):
        """
        Initialize rate limiter.

        Args:
            use_improved_algorithm: Whether to use the improved sliding window algorithm
        """
        if use_improved_algorithm:
            # Use the new improved rate limiter
            from .improved_rate_limiter import improved_rate_limiter
            self._improved_limiter = improved_rate_limiter
            self._use_improved = True
            logger.info("Using improved sliding window rate limiter")
        else:
            # Legacy Fixed Window implementation (DEPRECATED)
            self._use_improved = False
            self.request_counts: Dict[str, Dict[str, Any]] = {}
            self.rate_limits = {
                "default": {"requests": 100, "window": 3600},  # 100 requests per hour
                "auth": {"requests": 10, "window": 300},       # 10 auth attempts per 5 minutes
                "api": {"requests": 1000, "window": 3600},     # 1000 API calls per hour
                "upload": {"requests": 20, "window": 3600}     # 20 uploads per hour
            }
            logger.warning("Using DEPRECATED Fixed Window rate limiter - vulnerable to burst attacks!")

    def is_rate_limited(self, identifier: str, limit_type: str = "default") -> bool:
        """
        Check if identifier is rate limited.

        Args:
            identifier: Unique identifier (IP, user ID, etc.)
            limit_type: Type of rate limit to apply

        Returns:
            True if rate limited
        """
        if self._use_improved:
            # Use improved sliding window algorithm
            return self._improved_limiter.is_rate_limited(identifier, limit_type)
        else:
            # Legacy Fixed Window implementation (DEPRECATED - VULNERABLE TO BURST ATTACKS)
            logger.warning(f"Using deprecated Fixed Window rate limiter for {identifier} - consider upgrading!")

            current_time = time.time()
            limit_config = self.rate_limits.get(limit_type, self.rate_limits["default"])

            if identifier not in self.request_counts:
                self.request_counts[identifier] = {}

            if limit_type not in self.request_counts[identifier]:
                self.request_counts[identifier][limit_type] = {
                    "count": 0,
                    "window_start": current_time
                }

            limit_data = self.request_counts[identifier][limit_type]

            # Reset window if expired - VULNERABLE: Allows burst at window boundaries
            if current_time - limit_data["window_start"] > limit_config["window"]:
                limit_data["count"] = 0
                limit_data["window_start"] = current_time

            # Check if limit exceeded
            if limit_data["count"] >= limit_config["requests"]:
                return True

            # Increment count
            limit_data["count"] += 1
            return False

    def get_rate_limit_info(self, identifier: str, limit_type: str = "default") -> Dict[str, Any]:
        """Get rate limit information for identifier."""
        if identifier not in self.request_counts or limit_type not in self.request_counts[identifier]:
            limit_config = self.rate_limits.get(limit_type, self.rate_limits["default"])
            return {
                "requests_made": 0,
                "requests_remaining": limit_config["requests"],
                "window_reset": time.time() + limit_config["window"]
            }
        
        limit_data = self.request_counts[identifier][limit_type]
        limit_config = self.rate_limits.get(limit_type, self.rate_limits["default"])
        
        return {
            "requests_made": limit_data["count"],
            "requests_remaining": max(0, limit_config["requests"] - limit_data["count"]),
            "window_reset": limit_data["window_start"] + limit_config["window"]
        }


class InputValidator:
    """
    Comprehensive input validation system.
    """

    def __init__(self):
        """Initialize input validator."""
        self.max_lengths = {
            "message": 10000,
            "filename": 255,
            "email": 254,
            "username": 50,
            "password": 128
        }

    def validate_input(self, input_data: Any, input_type: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Validate input data.
        
        Args:
            input_data: Data to validate
            input_type: Type of input
            context: Additional context for validation
            
        Returns:
            Validation result
        """
        errors = []
        warnings = []
        
        # Length validation
        if isinstance(input_data, str):
            max_length = self.max_lengths.get(input_type, 1000)
            if len(input_data) > max_length:
                errors.append(f"Input too long (max {max_length} characters)")
        
        # Type-specific validation
        if input_type == "email":
            if not self._validate_email(input_data):
                errors.append("Invalid email format")
        
        elif input_type == "password":
            password_issues = self._validate_password(input_data)
            errors.extend(password_issues)
        
        elif input_type == "filename":
            if not self._validate_filename(input_data):
                errors.append("Invalid filename")
        
        elif input_type == "message":
            # Check for suspicious content
            if isinstance(input_data, str):
                if len(input_data.strip()) == 0:
                    warnings.append("Empty message")
                
                # Check for excessive special characters
                special_char_ratio = sum(1 for c in input_data if not c.isalnum() and not c.isspace()) / len(input_data)
                if special_char_ratio > 0.3:
                    warnings.append("High ratio of special characters")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "sanitized_input": self._sanitize_input(input_data, input_type)
        }

    def _validate_email(self, email: str) -> bool:
        """Validate email format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def _validate_password(self, password: str) -> List[str]:
        """Validate password strength."""
        issues = []
        
        if len(password) < 8:
            issues.append("Password must be at least 8 characters long")
        
        if not re.search(r'[A-Z]', password):
            issues.append("Password must contain at least one uppercase letter")
        
        if not re.search(r'[a-z]', password):
            issues.append("Password must contain at least one lowercase letter")
        
        if not re.search(r'\d', password):
            issues.append("Password must contain at least one digit")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            issues.append("Password must contain at least one special character")
        
        return issues

    def _validate_filename(self, filename: str) -> bool:
        """Validate filename."""
        # Check for dangerous characters
        dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        return not any(char in filename for char in dangerous_chars)

    def _sanitize_input(self, input_data: Any, input_type: str) -> Any:
        """Sanitize input data."""
        if not isinstance(input_data, str):
            return input_data
        
        # Basic HTML escaping
        sanitized = input_data.replace('<', '&lt;').replace('>', '&gt;')
        
        # Remove null bytes
        sanitized = sanitized.replace('\x00', '')
        
        # Normalize whitespace
        sanitized = re.sub(r'\s+', ' ', sanitized).strip()
        
        return sanitized


class SecurityMiddleware:
    """
    Security middleware for FastAPI applications.
    """

    def __init__(self):
        """Initialize security middleware."""
        self.threat_detector = ThreatDetector()
        self.rate_limiter = RateLimiter()
        self.input_validator = InputValidator()

    async def __call__(self, request: Request, call_next):
        """Process request through security middleware."""
        start_time = time.time()
        client_ip = self._get_client_ip(request)
        
        # Check if IP is blocked
        if self.threat_detector.is_ip_blocked(client_ip):
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Rate limiting
        if self.rate_limiter.is_rate_limited(client_ip, "api"):
            self.threat_detector.mark_suspicious_ip(client_ip)
            raise HTTPException(status_code=429, detail="Rate limit exceeded")
        
        # Process request
        response = await call_next(request)
        
        # Record metrics
        duration = time.time() - start_time
        metrics.record_http_request(
            method=request.method,
            endpoint=str(request.url.path),
            status_code=response.status_code,
            duration=duration
        )
        
        return response

    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        # Check for forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"


# Global security instances
threat_detector = ThreatDetector()
rate_limiter = RateLimiter()
input_validator = InputValidator()
security_middleware = SecurityMiddleware()


def secure_endpoint(limit_type: str = "default", validate_input: bool = True):
    """
    Decorator for securing API endpoints.
    
    Args:
        limit_type: Type of rate limiting to apply
        validate_input: Whether to validate input data
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract request information
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if request:
                client_ip = security_middleware._get_client_ip(request)
                
                # Check rate limiting
                if rate_limiter.is_rate_limited(client_ip, limit_type):
                    threat_detector.mark_suspicious_ip(client_ip)
                    raise HTTPException(status_code=429, detail="Rate limit exceeded")
                
                # Validate input if enabled
                if validate_input and request.method in ["POST", "PUT", "PATCH"]:
                    try:
                        body = await request.body()
                        if body:
                            body_str = body.decode('utf-8')
                            threats = threat_detector.detect_threats(body_str, client_ip)
                            if threats:
                                threat_detector.mark_suspicious_ip(client_ip)
                                raise HTTPException(status_code=400, detail="Suspicious input detected")
                    except Exception as e:
                        logger.warning(f"Input validation error: {e}")
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def get_security_headers() -> Dict[str, str]:
    """Get security headers for responses."""
    return {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }


async def security_health_check() -> Dict[str, Any]:
    """Perform security health check."""
    return {
        "threat_detector": "operational",
        "rate_limiter": "operational",
        "input_validator": "operational",
        "security_summary": threat_detector.get_security_summary()
    }
