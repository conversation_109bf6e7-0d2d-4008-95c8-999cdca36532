"""
Business profile models for the Datagenius backend.

This module provides Pydantic models for business profile-related functionality.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
import uuid
import re

from pydantic import BaseModel, Field, field_validator, model_validator


class BusinessSize(str, Enum):
    """Enum for business sizes."""
    STARTUP = "startup"
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"
    ENTERPRISE = "enterprise"


class BusinessType(str, Enum):
    """Enum for business types."""
    B2B = "B2B"
    B2C = "B2C"
    B2B2C = "B2B2C"
    MARKETPLACE = "marketplace"
    SAAS = "saas"
    ECOMMERCE = "ecommerce"


class BusinessStage(str, Enum):
    """Enum for business stages."""
    IDEA = "idea"
    STARTUP = "startup"
    GROWTH = "growth"
    MATURE = "mature"
    ENTERPRISE = "enterprise"


class DataSourceRole(str, Enum):
    """Enum for data source roles in business context."""
    SALES_DATA = "sales_data"
    BUSINESS_DESCRIPTION = "business_description"
    MARKETING_MATERIALS = "marketing_materials"
    FINANCIAL_DATA = "financial_data"
    CUSTOMER_DATA = "customer_data"
    PRODUCT_DATA = "product_data"
    COMPETITOR_ANALYSIS = "competitor_analysis"
    WEBSITE_CONTENT = "website_content"
    SOCIAL_MEDIA = "social_media"
    OTHER = "other"


class BusinessProfileBase(BaseModel):
    """Base model for business profile data."""
    name: str = Field(..., min_length=1, max_length=255, description="Business profile name")
    description: Optional[str] = Field(None, max_length=2000, description="Business description")
    industry: Optional[str] = Field(None, max_length=100, description="Industry sector")
    business_type: Optional[BusinessType] = None
    business_size: Optional[BusinessSize] = None
    target_audience: Optional[str] = Field(None, max_length=1000, description="Target audience description")
    products_services: Optional[str] = Field(None, max_length=2000, description="Products and services offered")
    marketing_goals: Optional[str] = Field(None, max_length=1000, description="Marketing objectives")
    competitive_landscape: Optional[str] = Field(None, max_length=2000, description="Competitive analysis")
    budget_indicators: Optional[str] = Field(None, max_length=100, description="Budget range indicators")
    geographic_focus: Optional[str] = Field(None, max_length=255, description="Geographic market focus")
    business_stage: Optional[BusinessStage] = None

    # Marketing-specific fields (consolidated from marketing form)
    budget: Optional[str] = Field(None, max_length=1000, description="Budget constraints, allocations, or financial considerations")
    timeline: Optional[str] = Field(None, max_length=1000, description="Timeline constraints, deadlines, or scheduling requirements")
    platforms: Optional[str] = Field(None, max_length=1000, description="Specific platforms, channels, or mediums for content distribution")

    context_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional business context")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate business profile name."""
        if not v or not v.strip():
            raise ValueError('Business profile name cannot be empty')

        # Remove excessive whitespace
        cleaned_name = ' '.join(v.strip().split())

        # Check for potentially malicious content
        if any(char in cleaned_name for char in ['<', '>', '{', '}', '[', ']']):
            raise ValueError('Business profile name contains invalid characters')

        return cleaned_name

    @field_validator('context_metadata')
    @classmethod
    def validate_context_metadata(cls, v):
        """Validate context metadata structure."""
        if v is None:
            return v

        if not isinstance(v, dict):
            raise ValueError('Context metadata must be a dictionary')

        # Limit the size of metadata to prevent abuse
        if len(str(v)) > 10000:  # 10KB limit
            raise ValueError('Context metadata is too large')

        return v


class BusinessProfileCreate(BusinessProfileBase):
    """Model for creating a business profile."""
    pass


class BusinessProfileUpdate(BaseModel):
    """Model for updating a business profile."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    industry: Optional[str] = None
    business_type: Optional[BusinessType] = None
    business_size: Optional[BusinessSize] = None
    target_audience: Optional[str] = None
    products_services: Optional[str] = None
    marketing_goals: Optional[str] = None
    competitive_landscape: Optional[str] = None
    budget_indicators: Optional[str] = None
    geographic_focus: Optional[str] = None
    business_stage: Optional[BusinessStage] = None

    # Marketing-specific fields (consolidated from marketing form)
    budget: Optional[str] = None
    timeline: Optional[str] = None
    platforms: Optional[str] = None

    context_metadata: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class BusinessProfileDataSourceAssignment(BaseModel):
    """Model for business profile data source assignment."""
    id: str
    business_profile_id: str
    data_source_id: str
    role: Optional[DataSourceRole] = None
    priority: int = 1
    is_active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class BusinessProfileDataSourceAssignmentCreate(BaseModel):
    """Model for creating a business profile data source assignment."""
    data_source_id: str = Field(..., description="UUID of the data source to assign")
    role: Optional[DataSourceRole] = None
    priority: int = Field(1, ge=1, le=100, description="Priority order (1-100, lower = higher priority)")
    is_active: bool = True

    @field_validator('data_source_id')
    @classmethod
    def validate_data_source_id(cls, v):
        """Validate data source ID is a proper UUID."""
        try:
            uuid.UUID(v)
            return v
        except ValueError:
            raise ValueError('Data source ID must be a valid UUID')


class BusinessProfileDataSourceAssignmentUpdate(BaseModel):
    """Model for updating a business profile data source assignment."""
    role: Optional[DataSourceRole] = None
    priority: Optional[int] = Field(None, ge=1, le=100, description="Priority order (1-100)")
    is_active: Optional[bool] = None


class BusinessProfileResponse(BusinessProfileBase):
    """Model for business profile data returned to the client."""
    id: str
    user_id: int
    is_active: bool
    knowledge_graph_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    # Related data
    data_source_count: Optional[int] = 0
    dashboard_count: Optional[int] = 0

    class Config:
        from_attributes = True


class BusinessProfileWithDataSources(BusinessProfileResponse):
    """Model for business profile with associated data sources."""
    data_source_assignments: List[BusinessProfileDataSourceAssignment] = []


class BusinessProfileListResponse(BaseModel):
    """Response model for listing business profiles."""
    profiles: List[BusinessProfileResponse]
    total: int
    active_profile_id: Optional[str] = None


class BusinessProfileSwitchRequest(BaseModel):
    """Request model for switching active business profile."""
    profile_id: str = Field(..., description="UUID of the profile to activate")

    @field_validator('profile_id')
    @classmethod
    def validate_profile_id(cls, v):
        """Validate profile ID is a proper UUID."""
        try:
            uuid.UUID(v)
            return v
        except ValueError:
            raise ValueError('Profile ID must be a valid UUID')


class BusinessProfileContextAnalysis(BaseModel):
    """Model for business profile context analysis results."""
    profile_id: str
    analysis_results: Dict[str, Any]
    confidence_score: float = 0.0
    recommendations: List[str] = []
    detected_patterns: List[str] = []
    knowledge_graph_updates: Optional[Dict[str, Any]] = None
    created_at: datetime

    class Config:
        from_attributes = True


class BusinessProfileAnalysisRequest(BaseModel):
    """Request model for business profile analysis."""
    profile_id: str
    analysis_depth: str = Field(default="standard", description="Analysis depth: quick, standard, or comprehensive")
    include_recommendations: bool = True
    update_knowledge_graph: bool = True
