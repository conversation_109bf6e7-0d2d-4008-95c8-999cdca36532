"""
Composable classification agent for the Datagenius backend.

This module provides a composable implementation of the classification agent
that uses the component-based architecture.
"""

import logging
from typing import Dict, Any, Optional, List

from agents.composable import ComposableAgent
from .components import (
    ClassificationParserComponent,
    HuggingFaceClassifierComponent,
    LLMClassifierComponent,
    ClassificationErrorHandlerComponent
)

# Configure logging
logger = logging.getLogger(__name__)


class ComposableClassificationAgent(ComposableAgent):
    """Composable implementation of the classification agent."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the composable classification agent.

        Args:
            config: Configuration dictionary for the agent
        """
        logger.info("Initializing Composable Classification Agent")
        
        # Call the parent initialization to set up the base components
        await super()._initialize(config)
        
        # If no components were configured, set up the default components
        if not self.components:
            logger.info("No components configured, setting up default classification components")
            
            # Create and initialize the parser component
            parser_component = ClassificationParserComponent()
            await parser_component.initialize({
                "name": "classification_parser"
            })
            self.components.append(parser_component)
            
            # Create and initialize the HF classifier component
            hf_component = HuggingFaceClassifierComponent()
            await hf_component.initialize({
                "name": "hf_classifier"
            })
            self.components.append(hf_component)
            
            # Create and initialize the LLM classifier component
            llm_component = LLMClassifierComponent()
            await llm_component.initialize({
                "name": "llm_classifier"
            })
            self.components.append(llm_component)
            
            # Create and initialize the error handler component
            error_component = ClassificationErrorHandlerComponent()
            await error_component.initialize({
                "name": "error_handler"
            })
            self.components.append(error_component)
            
            logger.info(f"Initialized {len(self.components)} default classification components")

    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message using the agent's components.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """
        # Create initial context with agent components for inter-component communication
        ctx = {
            "user_id": user_id,
            "message": message,
            "conversation_id": conversation_id,
            "context": context or {},
            "agent_config": self.config,
            "agent_components": self.components,
            "response": "",
            "metadata": {}
        }
        
        # Process the message using the parent method
        return await super().process_message(user_id, message, conversation_id, context)
