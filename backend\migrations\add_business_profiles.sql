-- Migration: Add Business Profiles Tables
-- Description: Add business_profiles and business_profile_data_sources tables for business profile functionality
-- Date: 2025-01-11

-- Create business_profiles table
CREATE TABLE IF NOT EXISTS business_profiles (
    id VARCHAR(36) PRIMARY KEY CHECK (id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'),
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL CHECK (LENGTH(TRIM(name)) > 0),
    description TEXT CHECK (LENGTH(description) <= 2000),

    -- Business context fields with length constraints
    industry VARCHAR(100),
    business_type VARCHAR(50) CHECK (business_type IN ('B2B', 'B2C', 'B2B2C', 'marketplace', 'saas', 'ecommerce')),
    business_size VARCHAR(50) CHECK (business_size IN ('startup', 'small', 'medium', 'large', 'enterprise')),
    target_audience TEXT CHECK (LENGTH(target_audience) <= 1000),
    products_services TEXT CHECK (LENGTH(products_services) <= 2000),
    marketing_goals TEXT CHECK (LENGTH(marketing_goals) <= 1000),
    competitive_landscape TEXT CHECK (LENGTH(competitive_landscape) <= 2000),
    budget_indicators VARCHAR(100),
    geographic_focus VARCHAR(255),
    business_stage VARCHAR(50) CHECK (business_stage IN ('idea', 'startup', 'growth', 'mature', 'enterprise')),

    -- Profile management
    is_active BOOLEAN DEFAULT FALSE NOT NULL,
    knowledge_graph_id VARCHAR(36) CHECK (knowledge_graph_id IS NULL OR knowledge_graph_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'),
    context_metadata JSONB CHECK (pg_column_size(context_metadata) <= 10240), -- 10KB limit

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

    -- Constraints
    CONSTRAINT unique_user_profile_name UNIQUE (user_id, name)
);

-- Create comprehensive indexes for optimal query performance
CREATE INDEX IF NOT EXISTS idx_business_profiles_user_id ON business_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_business_profiles_active ON business_profiles(user_id, is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_business_profiles_created_at ON business_profiles(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_business_profiles_name ON business_profiles(user_id, name);
CREATE INDEX IF NOT EXISTS idx_business_profiles_industry ON business_profiles(industry) WHERE industry IS NOT NULL;

-- Create business_profile_data_sources association table
CREATE TABLE IF NOT EXISTS business_profile_data_sources (
    id VARCHAR(36) PRIMARY KEY CHECK (id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'),
    business_profile_id VARCHAR(36) NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,
    data_source_id VARCHAR(36) NOT NULL REFERENCES data_sources(id) ON DELETE CASCADE,

    -- Data source role in business context
    role VARCHAR(100) CHECK (role IN ('sales_data', 'business_description', 'marketing_materials', 'financial_data', 'customer_data', 'product_data', 'competitor_analysis', 'website_content', 'social_media', 'other')),
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 100),
    is_active BOOLEAN DEFAULT TRUE NOT NULL,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,

    -- Unique constraint
    CONSTRAINT unique_profile_data_source UNIQUE (business_profile_id, data_source_id)
);

-- Create comprehensive indexes for business_profile_data_sources
CREATE INDEX IF NOT EXISTS idx_bp_data_sources_profile_id ON business_profile_data_sources(business_profile_id);
CREATE INDEX IF NOT EXISTS idx_bp_data_sources_data_source_id ON business_profile_data_sources(data_source_id);
CREATE INDEX IF NOT EXISTS idx_bp_data_sources_active ON business_profile_data_sources(business_profile_id, is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_bp_data_sources_priority ON business_profile_data_sources(business_profile_id, priority, is_active);
CREATE INDEX IF NOT EXISTS idx_bp_data_sources_role ON business_profile_data_sources(business_profile_id, role) WHERE role IS NOT NULL;

-- Add business_profile_id column to dashboards table (optional scoping)
ALTER TABLE dashboards ADD COLUMN IF NOT EXISTS business_profile_id VARCHAR(36) REFERENCES business_profiles(id) ON DELETE SET NULL;

-- Create index for dashboard business profile scoping
CREATE INDEX IF NOT EXISTS idx_dashboards_business_profile_id ON dashboards(business_profile_id) WHERE business_profile_id IS NOT NULL;

-- Create trigger to update updated_at timestamp for business_profiles
CREATE OR REPLACE FUNCTION update_business_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_business_profiles_updated_at
    BEFORE UPDATE ON business_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_business_profiles_updated_at();

-- Create trigger to update updated_at timestamp for business_profile_data_sources
CREATE OR REPLACE FUNCTION update_business_profile_data_sources_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_business_profile_data_sources_updated_at
    BEFORE UPDATE ON business_profile_data_sources
    FOR EACH ROW
    EXECUTE FUNCTION update_business_profile_data_sources_updated_at();

-- Create function to ensure only one active profile per user
CREATE OR REPLACE FUNCTION ensure_single_active_profile()
RETURNS TRIGGER AS $$
BEGIN
    -- If setting a profile to active, deactivate all other profiles for the same user
    IF NEW.is_active = TRUE THEN
        UPDATE business_profiles 
        SET is_active = FALSE 
        WHERE user_id = NEW.user_id AND id != NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_ensure_single_active_profile
    BEFORE INSERT OR UPDATE ON business_profiles
    FOR EACH ROW
    WHEN (NEW.is_active = TRUE)
    EXECUTE FUNCTION ensure_single_active_profile();

-- Production-ready migration - no sample data included

-- Add comments to tables
COMMENT ON TABLE business_profiles IS 'Business profiles for organizing user data and context';
COMMENT ON TABLE business_profile_data_sources IS 'Association table linking business profiles to their data sources';

COMMENT ON COLUMN business_profiles.knowledge_graph_id IS 'Reference to the knowledge graph space for this business profile';
COMMENT ON COLUMN business_profiles.context_metadata IS 'Flexible JSON storage for additional business context';
COMMENT ON COLUMN business_profiles.is_active IS 'Indicates if this is the currently selected business profile for the user';

COMMENT ON COLUMN business_profile_data_sources.role IS 'The role of this data source in the business context (e.g., sales_data, marketing_materials)';
COMMENT ON COLUMN business_profile_data_sources.priority IS 'Priority order for context loading (lower numbers = higher priority)';
