"""
Context-Aware Greeting System for Composable Marketing Agent.

This module provides intelligent greeting generation that adapts based on user context,
data sources, conversation history, and business information.
"""

import logging
from typing import Dict, Any, List, Optional
from jinja2 import Template

logger = logging.getLogger(__name__)


class ContextAwareGreetingGenerator:
    """Generates personalized greetings based on user context and interaction history."""
    
    def __init__(self):
        """Initialize the greeting generator."""
        self.greeting_templates = {
            "first_time_with_data": """
Hello! I'm your Composable Marketer, and I can see you've uploaded some business data. Perfect! 
I can create highly personalized marketing strategies and content based on your specific information.

**🚀 Recommended for you:**
{% if industry_detected %}
[📊 {{ industry_detected }} Marketing Strategy](action:marketing_strategy) [🎯 Industry-Specific Campaign](action:campaign_strategy)
{% else %}
[📊 Data-Driven Marketing Strategy](action:data_driven_analysis) [🎯 Personalized Campaign](action:data_driven_campaigns)
{% endif %}

**✍️ Quick Content Creation:**
[📝 Blog Posts](action:blog_content) [📧 Email Campaigns](action:email_marketing) [🎨 Ad Copy](action:ad_copy)

**📈 Smart Analysis:**
[🔍 Competitor Analysis](action:competitor_analysis) [👥 Audience Research](action:audience_research)

💡 **I've analyzed your data and can provide targeted recommendations. What marketing goal would you like to achieve first?**
            """,
            
            "first_time_no_data": """
Hello! I'm your Composable Marketer, designed to accelerate your marketing success with professional content that drives real results.

**🚀 Quick Start Options:**
[📊 Marketing Strategy](action:marketing_strategy) [🎯 Campaign Plan](action:campaign_strategy) [📱 Social Media Content](action:social_media_content) [🔍 SEO Optimization](action:seo_optimization)

**✍️ Content Creation:**
[📝 Blog Posts](action:blog_content) [📧 Email Campaigns](action:email_marketing) [🎨 Ad Copy](action:ad_copy) [📄 Press Release](action:press_release)

**🎯 Get Started Faster:**
[📋 Browse Templates](action:template_gallery) [⚡ Quick Setup](action:business_setup) [💡 See Examples](action:show_examples)

💡 **Pro Tip:** Upload your business documents or brand guidelines for personalized recommendations that fit your specific needs.

What marketing challenge can I help you tackle today?
            """,
            
            "returning_with_data": """
Welcome back! I see you have data uploaded and we've worked together before. 
Let's continue building your marketing success.

**🎯 Continue where we left off:**
{% if recent_content_type %}
[🔄 Improve {{ recent_content_type }}](action:{{ recent_content_type }}) [📊 New Strategy](action:marketing_strategy)
{% endif %}
[📊 Analyze Your Data](action:data_driven_analysis) [🎯 Data-Driven Campaigns](action:data_driven_campaigns)

**✍️ Create New Content:**
[📝 Blog Posts](action:blog_content) [📧 Email Campaigns](action:email_marketing) [📱 Social Media](action:social_media_content)

{% if business_context %}
**Based on your {{ business_context.industry }} business:**
{% for suggestion in business_context.suggestions %}
[{{ suggestion.icon }} {{ suggestion.title }}](action:{{ suggestion.action }})
{% endfor %}
{% endif %}

What's your next marketing priority?
            """,
            
            "returning_no_data": """
Welcome back! Ready to create more great marketing content?

**🚀 Popular Actions:**
[📊 Marketing Strategy](action:marketing_strategy) [🎯 Campaign Plan](action:campaign_strategy) [📱 Social Media](action:social_media_content)

**✍️ Content Creation:**
[📝 Blog Posts](action:blog_content) [📧 Email Campaigns](action:email_marketing) [🎨 Ad Copy](action:ad_copy)

**📈 Analysis & Research:**
[🔍 Competitor Analysis](action:competitor_analysis) [👥 Audience Research](action:audience_research) [📊 Market Analysis](action:market_analysis)

{% if previous_success %}
💡 **Remember:** Last time we created {{ previous_success.content_type }} that you found helpful. Want to build on that success?
{% endif %}

What marketing goal can I help you achieve today?
            """
        }
    
    async def generate_greeting(self, context: Dict[str, Any]) -> str:
        """
        Generate a context-aware greeting based on user information.
        
        Args:
            context: User context including conversation history, data sources, etc.
            
        Returns:
            Personalized greeting message
        """
        try:
            # Determine user context
            is_first_conversation = context.get("is_first_conversation", True)
            has_data_source = context.get("has_data_source", False)
            conversation_history = context.get("conversation_history", [])
            user_info = context.get("user_info", {})
            
            # Analyze user's business context
            business_context = await self._analyze_business_context(context)
            
            # Detect industry from data if available
            industry_detected = await self._detect_industry(context) if has_data_source else None
            
            # Get recent content type from history
            recent_content_type = self._get_recent_content_type(conversation_history)
            
            # Determine greeting template
            if is_first_conversation:
                template_key = "first_time_with_data" if has_data_source else "first_time_no_data"
            else:
                template_key = "returning_with_data" if has_data_source else "returning_no_data"
            
            # Get template and render with context
            template_content = self.greeting_templates[template_key]
            template = Template(template_content)
            
            # Prepare template variables
            template_vars = {
                "is_first_conversation": is_first_conversation,
                "has_data_source": has_data_source,
                "industry_detected": industry_detected,
                "recent_content_type": recent_content_type,
                "business_context": business_context,
                "user_name": user_info.get("name", ""),
                "previous_success": self._get_previous_success(conversation_history)
            }
            
            # Render greeting
            greeting = template.render(**template_vars)
            
            # Add value proposition
            greeting += self._get_value_proposition(business_context)
            
            logger.info(f"Generated context-aware greeting for template: {template_key}")
            return greeting.strip()
            
        except Exception as e:
            logger.error(f"Error generating context-aware greeting: {e}")
            # Fallback to basic greeting
            return self._get_fallback_greeting()
    
    async def _analyze_business_context(self, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze business context from available data using business context detection tool."""
        try:
            # Import here to avoid circular imports
            from ..tools.mcp import AVAILABLE_TOOLS

            # Get business context detection tool
            context_tool_class = AVAILABLE_TOOLS.get("BusinessContextTool")
            context_tool = context_tool_class() if context_tool_class else None
            if not context_tool:
                logger.warning("Business context detection tool not available")
                return None

            # Prepare parameters for context detection
            detection_params = {
                "data_sources": context.get("data_sources", []),
                "conversation_history": context.get("conversation_history", []),
                "user_profile": context.get("user_info", {}),
                "analysis_depth": "standard"
            }

            # Execute business context detection
            result = await context_tool.execute(**detection_params)

            if result.get("success") and result.get("business_context"):
                business_context = result["business_context"]

                # Generate industry-specific suggestions
                suggestions = self._generate_industry_suggestions(business_context)

                return {
                    "industry": business_context.get("industry"),
                    "business_type": business_context.get("business_type"),
                    "business_size": business_context.get("business_size"),
                    "target_market": business_context.get("target_market"),
                    "marketing_challenges": business_context.get("marketing_challenges", []),
                    "confidence_score": business_context.get("confidence_score", 0.0),
                    "suggestions": suggestions
                }

            return None

        except Exception as e:
            logger.error(f"Error analyzing business context: {e}")
            return None

    def _generate_industry_suggestions(self, business_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate industry-specific action suggestions."""
        suggestions = []
        industry = business_context.get("industry")
        business_size = business_context.get("business_size")
        challenges = business_context.get("marketing_challenges", [])

        try:
            # Industry-specific suggestions
            if industry == "technology":
                suggestions.extend([
                    {
                        "icon": "📝",
                        "title": "Technical Content Strategy",
                        "action": "blog_content",
                        "description": "Create technical blog posts and whitepapers"
                    },
                    {
                        "icon": "🎯",
                        "title": "B2B LinkedIn Strategy",
                        "action": "social_media_content",
                        "description": "Professional networking and thought leadership"
                    }
                ])
            elif industry == "healthcare":
                suggestions.extend([
                    {
                        "icon": "🏥",
                        "title": "Trust-Building Content",
                        "action": "blog_content",
                        "description": "Educational content that builds patient trust"
                    },
                    {
                        "icon": "📋",
                        "title": "Compliance-Ready Marketing",
                        "action": "marketing_strategy",
                        "description": "HIPAA-compliant marketing strategies"
                    }
                ])
            elif industry == "retail":
                suggestions.extend([
                    {
                        "icon": "🛍️",
                        "title": "Social Commerce Strategy",
                        "action": "social_media_content",
                        "description": "Instagram and TikTok shopping content"
                    },
                    {
                        "icon": "📧",
                        "title": "Customer Retention Emails",
                        "action": "email_marketing",
                        "description": "Loyalty and repeat purchase campaigns"
                    }
                ])

            # Business size specific suggestions
            if business_size == "startup":
                suggestions.append({
                    "icon": "🚀",
                    "title": "Lean Marketing Strategy",
                    "action": "marketing_strategy",
                    "description": "Cost-effective growth strategies for startups"
                })
            elif business_size == "enterprise":
                suggestions.append({
                    "icon": "🏢",
                    "title": "Enterprise Campaign Strategy",
                    "action": "campaign_strategy",
                    "description": "Large-scale, multi-channel campaigns"
                })

            # Challenge-specific suggestions
            if "lead generation" in challenges:
                suggestions.append({
                    "icon": "🎯",
                    "title": "Lead Generation Campaign",
                    "action": "email_marketing",
                    "description": "Convert prospects into qualified leads"
                })

            if "brand awareness" in challenges:
                suggestions.append({
                    "icon": "📢",
                    "title": "Brand Awareness Strategy",
                    "action": "campaign_strategy",
                    "description": "Multi-channel brand visibility campaign"
                })

            return suggestions[:4]  # Limit to 4 suggestions

        except Exception as e:
            logger.error(f"Error generating industry suggestions: {e}")
            return []

    async def _detect_industry(self, context: Dict[str, Any]) -> Optional[str]:
        """Detect industry from uploaded data using business context analysis."""
        try:
            business_context = await self._analyze_business_context(context)
            if business_context and business_context.get("confidence_score", 0) > 0.3:
                return business_context.get("industry")
            return None
        except Exception as e:
            logger.error(f"Error detecting industry: {e}")
            return None
    
    def _get_recent_content_type(self, conversation_history: List[Dict[str, Any]]) -> Optional[str]:
        """Get the most recent content type from conversation history."""
        try:
            for message in reversed(conversation_history):
                metadata = message.get("metadata", {})
                if metadata.get("content_type"):
                    return metadata["content_type"]
            return None
        except Exception as e:
            logger.error(f"Error getting recent content type: {e}")
            return None
    
    def _get_previous_success(self, conversation_history: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Get information about previous successful interactions."""
        try:
            # Look for positive feedback or completed content generation
            for message in reversed(conversation_history):
                metadata = message.get("metadata", {})
                if metadata.get("user_feedback") == "positive":
                    return {
                        "content_type": metadata.get("content_type", "content"),
                        "timestamp": message.get("timestamp")
                    }
            return None
        except Exception as e:
            logger.error(f"Error getting previous success: {e}")
            return None
    
    def _get_value_proposition(self, business_context: Optional[Dict[str, Any]]) -> str:
        """Get relevant value proposition based on business context."""
        base_value = """

**What I help you achieve:**
• **Grow Your Business** - Strategies that drive measurable ROI and customer acquisition
• **Save Time** - Professional marketing content generated in minutes, not hours  
• **Beat Competitors** - Market positioning and competitive advantage insights
• **Engage Audiences** - Compelling content that converts prospects to customers
• **Optimize Performance** - Data-driven recommendations for better results"""
        
        if business_context and business_context.get("industry"):
            industry = business_context["industry"]
            industry_specific = {
                "Technology": "\n• **Scale Fast** - Growth strategies for tech companies and startups",
                "Healthcare": "\n• **Build Trust** - Compliant marketing that builds patient confidence",
                "Finance": "\n• **Establish Authority** - Thought leadership in financial services",
                "Retail": "\n• **Drive Sales** - E-commerce and retail marketing optimization"
            }
            base_value += industry_specific.get(industry, "")
        
        return base_value
    
    def _get_fallback_greeting(self) -> str:
        """Get fallback greeting if context analysis fails."""
        return """Hello! I'm your Composable Marketer, ready to help you create professional marketing content that drives results.

**🚀 Quick Start:**
[📊 Marketing Strategy](action:marketing_strategy) [🎯 Campaign Plan](action:campaign_strategy) [📱 Social Media](action:social_media_content)

What marketing challenge can I help you tackle today?"""
