/**
 * Enhanced Ribbon Toolbar Component
 *
 * Comprehensive Microsoft Office-style ribbon interface consolidating all dashboard functionality.
 * Features complete feature parity with DashboardRibbonToolbar plus advanced mode capabilities.
 *
 * Features:
 * - 7 main tabs: Dashboard, Data, Insert, Style, Analyze, Share, Admin
 * - Keyboard shortcuts with tooltips
 * - Responsive design for all screen sizes
 * - Accessibility compliance (ARIA labels, keyboard navigation)
 * - Quick Access toolbar for frequently used actions
 * - Contextual right-click menus
 * - Undo/redo functionality with history
 * - Command palette integration
 * - Advanced search capabilities
 * - User preference persistence
 */

import React, { useState, useCallback, useMemo, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// Removed unused imports: Input, Label, Switch
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
// Removed unused imports: Command, ContextMenu components
import {
  // Dashboard operations
  Save,
  FolderOpen,
  Download,
  Upload,
  Copy,
  Scissors,
  Clipboard,
  RotateCcw,
  RotateCw,
  FileText,
  Printer,
  LogOut,

  // Data operations
  Database,
  RefreshCw,
  Filter,
  Search,
  SortAsc,
  SortDesc,
  Eye,
  Trash2,

  // Insert/Widgets
  Plus,
  BarChart3,
  PieChart,
  LineChart,
  TrendingUp,
  Table,
  Type,
  Image,
  Calendar,
  MapPin,
  Activity,
  Layers,
  Move,

  // Style & Layout
  Palette,
  Layout,
  Grid,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline,
  Paintbrush,
  Rows,

  // Analyze
  Calculator,
  TrendingDown,
  Target,
  Zap,
  Brain,

  // Export & Share
  Share,
  Share2,
  Mail,
  Users,
  Lock,

  // Admin/Settings
  Settings,
  User,
  Key,
  Shield,
  Globe,
  ChevronDown,
  ChevronRight,
  Code,
  Terminal,
  Wrench,
  Languages,
  Bell,
  Monitor,
  Info,

  // Common UI
  MoreHorizontal,
  Star,
  Pin,
  Command as CommandIcon,
  HelpCircle,
  Maximize2,
  Minimize2,
  X,
  Wifi,
  WifiOff,
  Clock,

  // Interface Controls
  Bot,
  Sparkles,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useModeConfig, useDashboardMode } from '@/stores/dashboard-mode-store';
import { useTheme } from 'next-themes';
import { useHotkeys } from 'react-hotkeys-hook';

// Toolbar toggle component
import { InlineToolbarToggle } from '../../ToolbarToggleButton';

// Import advanced features from DashboardRibbonToolbar
// Note: RibbonTabsContent removed - functionality consolidated into EnhancedRibbonToolbar
import {
  useScreenReaderAnnouncements,
  useRibbonKeyboardNavigation,
  useAccessibilityPreferences,
  useResponsiveBreakpoints
} from '../../RibbonAccessibility';
import {
  useUndoRedo,
  useRibbonPreferences,
  useAdvancedSearch,
  RibbonContextMenu,
  CommandPalette,
} from '../../RibbonAdvancedFeatures';

// Mobile Ribbon Interface Component
const MobileRibbonInterface: React.FC<{
  onAddWidget?: (type: string) => void;
  onRefresh?: () => void;
  onSettings?: () => void;
  onExport?: (format: string) => void;
  onShare?: () => void;
  onFilterToggle?: () => void;
  onSortToggle?: () => void;
  onStyleChange?: (style: string) => void;
  onWidgetManagement?: () => void;
}> = ({
  onAddWidget,
  onRefresh,
  onSettings,
  onExport,
  onShare,
  onFilterToggle,
  onSortToggle,
  onStyleChange,
  onWidgetManagement,
}) => {
  return (
    <div className="sm:hidden ribbon-spacing-sm">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8 w-full justify-between"
          >
            <span className="flex items-center">
              <Monitor className="h-4 w-4 mr-2" />
              Dashboard Tools
            </span>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64">
          <DropdownMenuLabel>Dashboard Tools</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {/* Primary Actions */}
          <DropdownMenuItem onClick={() => onAddWidget?.('chart')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Widget
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onWidgetManagement}>
            <Grid className="h-4 w-4 mr-2" />
            Manage Widgets
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Dashboard
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* Data & Style Actions */}
          <DropdownMenuItem onClick={onFilterToggle}>
            <Filter className="h-4 w-4 mr-2" />
            Toggle Filters
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onSortToggle}>
            <SortAsc className="h-4 w-4 mr-2" />
            Sort Data
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onStyleChange?.('theme')}>
            <Palette className="h-4 w-4 mr-2" />
            Change Theme
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* Export & Share */}
          <DropdownMenuItem onClick={() => onExport?.('pdf')}>
            <Download className="h-4 w-4 mr-2" />
            Export as PDF
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onShare}>
            <Share2 className="h-4 w-4 mr-2" />
            Share Dashboard
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {/* Settings */}
          <DropdownMenuItem onClick={onSettings}>
            <Settings className="h-4 w-4 mr-2" />
            Dashboard Settings
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

// Enhanced interface with all DashboardRibbonToolbar props
interface EnhancedRibbonToolbarProps {
  // Dashboard Tab Props
  onOpen?: () => void;
  onSave?: () => void;
  onSaveAs?: () => void;
  onNew?: () => void;
  onRecent?: () => void;
  onExport?: (format?: string) => void;
  onPrint?: () => void;
  onLogout?: () => void;

  // Data Tab Props
  onDataSourceConnect?: () => void;
  onDataRefresh?: () => void;
  onQueryManagement?: () => void;
  onFilterToggle?: () => void;
  onSortToggle?: () => void;
  onDataPreview?: () => void;
  onDataCleaning?: () => void;

  // Insert/Widgets Tab Props
  onAddWidget?: (type?: string) => void;
  onAddSection?: () => void;
  onAddCustomHTML?: () => void;
  onWidgetSync?: () => void;
  onWidgetManagement?: () => void;

  // Style & Layout Tab Props
  onThemeChange?: (theme: string) => void;
  onLayoutChange?: (layout: string) => void;
  onStyleChange?: (style: string) => void;
  onTypographyChange?: (typography: any) => void;
  onAlignmentChange?: (alignment: string) => void;

  // Analyze Tab Props
  onDrillDown?: () => void;
  onGenerateInsights?: () => void;
  onTimeComparison?: (period: string) => void;
  onTrendAnalysis?: () => void;
  onStatisticalAnalysis?: () => void;
  onFormulaEditor?: () => void;
  onRawDataView?: () => void;

  // Export & Share Tab Props
  onShare?: () => void;
  onGenerateEmbedCode?: () => void;
  onPermissionManagement?: () => void;
  onScheduleReport?: () => void;
  onCollaboration?: () => void;

  // Admin/Settings Tab Props
  onSettings?: () => void;
  onUserPreferences?: () => void;
  onDataSourceConfig?: () => void;
  onAPIKeyManagement?: () => void;
  onAccessControl?: () => void;
  onLanguageChange?: (language: string) => void;

  // Interface Controls Props
  onModeToggle?: () => void;
  onAIAssistantToggle?: () => void;

  // Common Props
  onUndo?: () => void;
  onRedo?: () => void;
  onRefresh?: () => void;
  onViewChange?: (view: string) => void;

  // State Props
  widgetCount?: number;
  lastUpdated?: Date;
  isConnected?: boolean;
  canUndo?: boolean;
  canRedo?: boolean;
  currentTheme?: string;
  currentLayout?: string;
  availableDataSources?: any[];
  userPermissions?: {
    canEdit?: boolean;
    canShare?: boolean;
    canExport?: boolean;
    canManageUsers?: boolean;
    canAccessSettings?: boolean;
  };

  // UI Props
  dashboardSelector?: React.ReactNode;
  securityIndicator?: React.ReactNode;
  performanceMonitor?: React.ReactNode;
  className?: string;
  isCompact?: boolean;
  showQuickAccess?: boolean;
  quickAccessItems?: any[];

  // Legacy props for backward compatibility
  on_action?: (action: string, data?: any) => void;
  show_keyboard_shortcuts?: boolean;
  compact_mode?: boolean;
}

export const EnhancedRibbonToolbar: React.FC<EnhancedRibbonToolbarProps> = ({
  // Dashboard Tab Props
  onOpen,
  onSave,
  onSaveAs,
  onNew,
  onRecent,
  onExport,
  onPrint,
  onLogout,

  // Data Tab Props
  onDataSourceConnect,
  onDataRefresh,
  onQueryManagement,
  onFilterToggle,
  onSortToggle,
  onDataPreview,
  onDataCleaning,

  // Insert/Widgets Tab Props
  onAddWidget,
  onAddSection,
  onAddCustomHTML,
  onWidgetSync,
  onWidgetManagement,

  // Style & Layout Tab Props
  onThemeChange,
  onLayoutChange,
  onStyleChange,
  onTypographyChange,
  onAlignmentChange,

  // Analyze Tab Props
  onDrillDown,
  onGenerateInsights,
  onTimeComparison,
  onTrendAnalysis,
  onStatisticalAnalysis,
  onFormulaEditor,
  onRawDataView,

  // Export & Share Tab Props
  onShare,
  onGenerateEmbedCode,
  onPermissionManagement,
  onScheduleReport,
  onCollaboration,

  // Admin/Settings Tab Props
  onSettings,
  onUserPreferences,
  onDataSourceConfig,
  onAPIKeyManagement,
  onAccessControl,
  onLanguageChange,

  // Interface Controls Props
  onModeToggle,
  onAIAssistantToggle,

  // Common Props
  onUndo,
  onRedo,
  onRefresh,
  onViewChange,

  // State Props
  widgetCount = 0,
  lastUpdated,
  isConnected = false,
  canUndo = false,
  canRedo = false,
  currentTheme = 'light',
  currentLayout = 'grid',
  availableDataSources = [],
  userPermissions = {
    canEdit: true,
    canShare: true,
    canExport: true,
    canManageUsers: false,
    canAccessSettings: true,
  },

  // UI Props
  dashboardSelector,
  securityIndicator,
  performanceMonitor,
  className,
  isCompact = false,
  showQuickAccess = true,
  quickAccessItems = [],

  // Legacy props for backward compatibility
  on_action,
  show_keyboard_shortcuts = true,
  compact_mode = false,
}) => {
  // State management
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isQuickAccessCustomizing, setIsQuickAccessCustomizing] = useState(false);
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(new Set());
  const [isCommandPaletteOpen, setIsCommandPaletteOpen] = useState(false);

  const { advanced_config } = useModeConfig();
  const { current_mode, toggle_mode, can_switch } = useDashboardMode();

  // Define constants for enhanced functionality
  const timeComparisons = useMemo(() => [
    { period: 'yoy', label: 'Year over Year', description: 'Compare with same period last year' },
    { period: 'mom', label: 'Month over Month', description: 'Compare with previous month' },
    { period: 'wow', label: 'Week over Week', description: 'Compare with previous week' },
    { period: 'custom', label: 'Custom Period', description: 'Define custom comparison period' },
  ], []);

  const exportFormats = useMemo(() => [
    { format: 'pdf', label: 'PDF Document', icon: FileText, description: 'Portable document format' },
    { format: 'excel', label: 'Excel Spreadsheet', icon: Table, description: 'Microsoft Excel format' },
    { format: 'csv', label: 'CSV Data', icon: Database, description: 'Comma-separated values' },
    { format: 'image', label: 'Image (PNG)', icon: Image, description: 'High-quality image export' },
    { format: 'json', label: 'JSON Data', icon: Code, description: 'JavaScript Object Notation' },
  ], []);

  // Theme management
  const { theme, setTheme } = useTheme();

  // Accessibility features
  const { announceAction, AnnouncementRegion } = useScreenReaderAnnouncements();
  const { isHighContrast, isReducedMotion, screenReaderMode } = useAccessibilityPreferences();
  const { breakpoint, isMobile, isTablet, isDesktop } = useResponsiveBreakpoints();

  // Tab definitions for keyboard navigation
  const tabs = ['dashboard', 'data', 'insert', 'style', 'analyze', 'share', 'admin'];
  const ribbonRef = useRef<HTMLDivElement>(null);
  const { focusNext, focusPrevious } = useRibbonKeyboardNavigation(
    activeTab,
    setActiveTab,
    tabs
  );

  // Advanced features
  const { history, addAction, undo, redo, canUndo: canUndoHistory, canRedo: canRedoHistory } = useUndoRedo();
  const { preferences, updatePreferences } = useRibbonPreferences();

  // Enhanced keyboard shortcuts setup
  useHotkeys('ctrl+o', (e) => { e.preventDefault(); onOpen?.(); announceAction('Open dashboard'); }, { enableOnFormTags: false });
  useHotkeys('ctrl+s', (e) => { e.preventDefault(); onSave?.(); announceAction('Save dashboard'); }, { enableOnFormTags: false });
  useHotkeys('ctrl+shift+s', (e) => { e.preventDefault(); onSaveAs?.(); announceAction('Save As dialog opened'); }, { enableOnFormTags: false });
  useHotkeys('ctrl+n', (e) => { e.preventDefault(); onNew?.(); announceAction('New dashboard'); }, { enableOnFormTags: false });
  useHotkeys('ctrl+r', (e) => { e.preventDefault(); onRecent?.(); announceAction('Recent dashboards'); }, { enableOnFormTags: false });
  useHotkeys('ctrl+e', (e) => { e.preventDefault(); onExport?.('pdf'); announceAction('Export as PDF'); }, { enableOnFormTags: false });
  useHotkeys('ctrl+p', (e) => { e.preventDefault(); onPrint?.(); announceAction('Print dashboard'); }, { enableOnFormTags: false });
  useHotkeys('f5', (e) => { e.preventDefault(); onRefresh?.(); announceAction('Refreshing dashboard'); }, { enableOnFormTags: false });

  // Enhanced undo/redo with integrated system
  useHotkeys('ctrl+z', (e) => {
    e.preventDefault();
    if (canUndoHistory) {
      const action = undo();
      if (action) {
        announceAction(`Undid: ${action.description}`);
      }
    } else {
      announceAction('Nothing to undo');
    }
  }, { enableOnFormTags: false });

  useHotkeys('ctrl+y', (e) => {
    e.preventDefault();
    if (canRedoHistory) {
      const action = redo();
      if (action) {
        announceAction(`Redid: ${action.description}`);
      }
    } else {
      announceAction('Nothing to redo');
    }
  }, { enableOnFormTags: false });

  useHotkeys('ctrl+shift+w', (e) => { e.preventDefault(); onAddWidget?.('chart'); announceAction('Add widget dialog opened'); }, { enableOnFormTags: false });
  useHotkeys('ctrl+shift+f', (e) => { e.preventDefault(); onFilterToggle?.(); announceAction('Filters toggled'); }, { enableOnFormTags: false });
  useHotkeys('ctrl+shift+r', (e) => { e.preventDefault(); onDataRefresh?.(); announceAction('Data refresh started'); }, { enableOnFormTags: false });

  // Advanced search and command palette
  useHotkeys('ctrl+/', (e) => { e.preventDefault(); setIsSearchOpen(true); announceAction('Search opened'); }, { enableOnFormTags: false });
  useHotkeys('ctrl+shift+p', (e) => { e.preventDefault(); setIsCommandPaletteOpen(true); announceAction('Command palette opened'); }, { enableOnFormTags: false });

  // Legacy action handler for backward compatibility
  const handle_action = (action: string, data?: any) => {
    on_action?.(action, data);
    addAction({
      type: action,
      data,
      description: `Executed ${action}`,
      undo: () => {},
      redo: () => {}
    });
  };

  // Utility functions
  const formatLastUpdated = useCallback((date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}h ago`;
    return date.toLocaleDateString();
  }, []);

  const toggleGroupCollapse = useCallback((groupId: string) => {
    setCollapsedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  }, []);

  // Enhanced data definitions for comprehensive ribbon functionality
  const widgetTypes = useMemo(() => [
    { type: 'chart', icon: BarChart3, label: 'Bar Chart', category: 'Charts', description: 'Display data in vertical bars' },
    { type: 'line', icon: LineChart, label: 'Line Chart', category: 'Charts', description: 'Show trends over time' },
    { type: 'pie', icon: PieChart, label: 'Pie Chart', category: 'Charts', description: 'Display proportional data' },
    { type: 'table', icon: Table, label: 'Data Table', category: 'Data', description: 'Tabular data display' },
    { type: 'calendar', icon: Calendar, label: 'Calendar', category: 'Time', description: 'Date-based visualization' },
    { type: 'map', icon: MapPin, label: 'Map', category: 'Geographic', description: 'Location-based data' },
    { type: 'kpi', icon: Target, label: 'KPI Card', category: 'Metrics', description: 'Key performance indicators' },
    { type: 'gauge', icon: Activity, label: 'Gauge', category: 'Metrics', description: 'Progress indicators' },
    { type: 'text', icon: Type, label: 'Text Block', category: 'Content', description: 'Rich text content' },
    { type: 'image', icon: Image, label: 'Image', category: 'Content', description: 'Image display' },
  ], []);

  const themes = useMemo(() => [
    { id: 'light', label: 'Light', description: 'Clean light theme' },
    { id: 'dark', label: 'Dark', description: 'Modern dark theme' },
    { id: 'blue', label: 'Blue', description: 'Professional blue theme' },
    { id: 'green', label: 'Green', description: 'Nature-inspired green theme' },
    { id: 'purple', label: 'Purple', description: 'Creative purple theme' },
  ], []);

  const layouts = useMemo(() => [
    { layout: 'grid', label: 'Grid', icon: Grid, description: 'Structured grid layout' },
    { layout: 'masonry', label: 'Masonry', icon: Layers, description: 'Pinterest-style layout' },
    { layout: 'freeform', label: 'Freeform', icon: Move, description: 'Drag and drop positioning' },
    { layout: 'auto', label: 'Auto', icon: Zap, description: 'Automatic layout optimization' },
  ], []);

  const analysisTools = useMemo(() => [
    { id: 'insights', label: 'AI Insights', icon: Brain, action: onGenerateInsights },
    { id: 'trends', label: 'Trend Analysis', icon: TrendingUp, action: onTrendAnalysis },
    { id: 'statistics', label: 'Statistics', icon: Calculator, action: onStatisticalAnalysis },
    { id: 'formula', label: 'Formula Editor', icon: Calculator, action: onFormulaEditor },
    { id: 'rawdata', label: 'Raw Data', icon: Database, action: onRawDataView },
  ], [onGenerateInsights, onTrendAnalysis, onStatisticalAnalysis, onFormulaEditor, onRawDataView]);

  const RibbonButton = ({
    icon: Icon,
    label,
    shortcut,
    onClick,
    variant = 'ghost',
    size = 'sm',
    disabled = false,
    className: buttonClassName
  }: {
    icon: React.ComponentType<{ className?: string }>;
    label: string;
    shortcut?: string;
    onClick: () => void;
    variant?: 'ghost' | 'outline' | 'default';
    size?: 'sm' | 'default';
    disabled?: boolean;
    className?: string;
  }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            onClick={onClick}
            disabled={disabled}
            className={cn(
              "ribbon-button flex flex-col items-center justify-center space-y-1 h-14 min-w-[60px] px-2",
              buttonClassName
            )}
          >
            <Icon className="h-4 w-4" />
            <span className="text-xs font-normal leading-tight text-center whitespace-nowrap">{label}</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-center">
            <p className="font-medium">{label}</p>
            {shortcut && show_keyboard_shortcuts && (
              <p className="text-xs text-muted-foreground">{shortcut}</p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  // Enhanced RibbonGroup component with collapsible functionality - Optimized spacing
  const RibbonGroup = React.forwardRef<
    HTMLDivElement,
    {
      title: string;
      children: React.ReactNode;
      id?: string;
      collapsible?: boolean;
      className?: string;
      description?: string;
    }
  >(({ title, children, id, collapsible = false, className: groupClassName, description }, ref) => {
    const isCollapsed = id ? collapsedGroups.has(id) : false;

    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col items-center px-3 py-2 border-r border-slate-200 last:border-r-0 transition-all duration-200 h-full justify-center",
          (isCompact || compact_mode) && "px-1.5 py-1",
          groupClassName
        )}
      >
        {collapsible && id && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleGroupCollapse(id)}
            className="mb-0.5 ribbon-button-micro"
            aria-label={`${isCollapsed ? 'Expand' : 'Collapse'} ${title} group`}
          >
            {isCollapsed ? <ChevronRight className="h-2.5 w-2.5" /> : <ChevronDown className="h-2.5 w-2.5" />}
          </Button>
        )}

        {!isCollapsed && (
          <div className="flex gap-1 justify-center mb-1 flex-shrink-0">
            {children}
          </div>
        )}

        <div className="text-xs text-muted-foreground font-medium text-center flex-shrink-0">
          {title}
        </div>
      </div>
    );
  });

  return (
    <TooltipProvider>
      <div
        ref={ribbonRef}
        className={cn("border-b bg-background ribbon-toolbar-container h-full max-h-[160px] overflow-hidden", className)}
        role="toolbar"
        aria-label="Dashboard Ribbon Toolbar"
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
        {/* Tab Headers with Enhanced Status Information - Fixed height */}
        <div className="ribbon-tab-header flex items-center justify-between flex-shrink-0">
          <TabsList className="grid w-auto grid-cols-7 h-8">
            <TabsTrigger value="dashboard" className="text-xs px-2 py-1">Dashboard</TabsTrigger>
            <TabsTrigger value="data" className="text-xs px-2 py-1">Data</TabsTrigger>
            <TabsTrigger value="insert" className="text-xs px-2 py-1">Insert</TabsTrigger>
            <TabsTrigger value="style" className="text-xs px-2 py-1">Style</TabsTrigger>
            <TabsTrigger value="analyze" className="text-xs px-2 py-1">Analyze</TabsTrigger>
            <TabsTrigger value="share" className="text-xs px-2 py-1">Share</TabsTrigger>
            <TabsTrigger value="admin" className="text-xs px-2 py-1">Admin</TabsTrigger>
          </TabsList>

          {/* Enhanced Status Information from UnifiedHeader - Optimized spacing */}
          <div className="flex items-center space-x-2">
            {/* Dashboard Status Information - Compact layout */}
            <div className="hidden lg:flex items-center space-x-2 px-2 py-0.5 bg-slate-50/50 rounded border">
              {/* Connection Status */}
              <div className="flex items-center space-x-1">
                {isConnected ? (
                  <Wifi className="h-3 w-3 text-green-600" />
                ) : (
                  <WifiOff className="h-3 w-3 text-red-600" />
                )}
                <Badge
                  variant={isConnected ? "default" : "destructive"}
                  className="text-xs px-1 py-0"
                >
                  {isConnected ? "Live" : "Offline"}
                </Badge>
              </div>

              {/* Widget Count */}
              <div className="flex items-center space-x-1 text-xs text-slate-600">
                <Grid className="h-3 w-3" />
                <span className="font-medium">{widgetCount}</span>
                <span>widgets</span>
              </div>

              {/* Last Updated */}
              {lastUpdated && (
                <div className="flex items-center space-x-1 text-xs text-slate-600">
                  <Clock className="h-3 w-3" />
                  <span>{formatLastUpdated(lastUpdated)}</span>
                </div>
              )}

              {/* Security and Performance Indicators */}
              <div className="flex items-center space-x-1">
                {securityIndicator}
                {performanceMonitor}
              </div>
            </div>

            {/* Mode and Performance Badges - Compact */}
            <div className="flex items-center space-x-1">
              <Badge variant="secondary" className="text-xs px-1.5 py-0">Advanced Mode</Badge>
              {advanced_config.technical_controls.show_performance_metrics && (
                <Badge variant="outline" className="text-xs text-green-600 px-1.5 py-0">
                  Performance: Good
                </Badge>
              )}
            </div>

            {/* Toolbar Toggle Button - Matching Simple Mode */}
            <InlineToolbarToggle
              variant="chevron"
              className="h-8 w-8"
            />
          </div>
        </div>

        {/* Tab Content - Flexible height */}
        <div className="ribbon-tab-content flex-1 min-h-0 overflow-hidden">
          {/* Dashboard Tab - Enhanced with UnifiedHeader controls */}
          <TabsContent value="dashboard" className="mt-0 h-full p-2">
            <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
              <RibbonGroup title="Dashboard Operations">
                <RibbonButton
                  icon={FolderOpen}
                  label="Open"
                  shortcut="Ctrl+O"
                  onClick={() => {
                    if (onOpen) onOpen();
                    else handle_action('dashboard_open');
                  }}
                />
                <RibbonButton
                  icon={Save}
                  label="Save"
                  shortcut="Ctrl+S"
                  onClick={() => {
                    if (onSave) onSave();
                    else handle_action('dashboard_save');
                  }}
                />
                <RibbonButton
                  icon={Save}
                  label="Save As"
                  shortcut="Ctrl+Shift+S"
                  onClick={() => {
                    if (onSaveAs) onSaveAs();
                    else handle_action('dashboard_save_as');
                  }}
                />
                <RibbonButton
                  icon={Plus}
                  label="New"
                  shortcut="Ctrl+N"
                  onClick={() => {
                    if (onNew) onNew();
                    else handle_action('dashboard_new');
                  }}
                />
                <RibbonButton
                  icon={Clock}
                  label="Recent"
                  shortcut="Ctrl+R"
                  onClick={() => {
                    if (onRecent) onRecent();
                    else handle_action('dashboard_recent');
                  }}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              <RibbonGroup title="Export & Print">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Download}
                      label="Export"
                      onClick={() => {}}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel>Export Formats</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {exportFormats.map(({ format, label, icon: Icon, description }) => (
                      <DropdownMenuItem
                        key={format}
                        onClick={() => {
                          if (onExport) onExport(format);
                          else handle_action('dashboard_export', { format });
                        }}
                        className="cursor-pointer"
                      >
                        <Icon className="h-4 w-4 mr-2" />
                        <div className="flex flex-col">
                          <span>{label}</span>
                          <span className="text-xs text-muted-foreground">{description}</span>
                        </div>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
                <RibbonButton
                  icon={Printer}
                  label="Print"
                  shortcut="Ctrl+P"
                  onClick={() => {
                    if (onPrint) onPrint();
                    else handle_action('dashboard_print');
                  }}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              <RibbonGroup title="Templates">
                <Dialog>
                  <DialogTrigger asChild>
                    <RibbonButton
                      icon={Layout}
                      label="Templates"
                      onClick={() => {}}
                    />
                  </DialogTrigger>
                  <DialogContent className="max-w-6xl max-h-[90vh] overflow-auto">
                    <DialogHeader>
                      <DialogTitle className="flex items-center space-x-2">
                        <Layout className="h-5 w-5" />
                        <span>Dashboard Templates</span>
                      </DialogTitle>
                      <DialogDescription>
                        Choose from pre-built templates to quickly create professional dashboards
                      </DialogDescription>
                    </DialogHeader>
                    {/* Template content would go here */}
                  </DialogContent>
                </Dialog>
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              <RibbonGroup title="History">
                <RibbonButton
                  icon={RotateCcw}
                  label="Undo"
                  shortcut="Ctrl+Z"
                  onClick={() => {
                    if (onUndo) onUndo();
                    else handle_action('edit_undo');
                  }}
                  disabled={!canUndo && !canUndoHistory}
                />
                <RibbonButton
                  icon={RotateCw}
                  label="Redo"
                  shortcut="Ctrl+Y"
                  onClick={() => {
                    if (onRedo) onRedo();
                    else handle_action('edit_redo');
                  }}
                  disabled={!canRedo && !canRedoHistory}
                />
              </RibbonGroup>

              {advanced_config.customization.show_code_editor && (
                <>
                  <Separator orientation="vertical" className="h-12" />
                  <RibbonGroup title="Developer">
                    <RibbonButton
                      icon={Code}
                      label="Code Editor"
                      onClick={() => handle_action('dev_code_editor')}
                    />
                    <RibbonButton
                      icon={Terminal}
                      label="Console"
                      onClick={() => handle_action('dev_console')}
                    />
                  </RibbonGroup>
                </>
              )}
            </div>
          </TabsContent>

          {/* Data Tab */}
          <TabsContent value="data" className="mt-0 h-full p-2">
            <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
              <RibbonGroup title="Data Sources">
                <RibbonButton
                  icon={Database}
                  label="Connect"
                  onClick={() => handle_action('data_connect')}
                />
                <RibbonButton
                  icon={Upload}
                  label="Upload"
                  onClick={() => handle_action('data_upload')}
                />
                <RibbonButton
                  icon={RefreshCw}
                  label="Refresh"
                  shortcut="F5"
                  onClick={() => handle_action('data_refresh')}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              <RibbonGroup title="Data Operations">
                <RibbonButton
                  icon={Filter}
                  label="Filter"
                  onClick={() => handle_action('data_filter')}
                />
                <RibbonButton
                  icon={SortAsc}
                  label="Sort"
                  onClick={() => handle_action('data_sort')}
                />
                <RibbonButton
                  icon={Search}
                  label="Search"
                  shortcut="Ctrl+F"
                  onClick={() => handle_action('data_search')}
                />
              </RibbonGroup>

              {advanced_config.technical_controls.show_raw_data_access && (
                <>
                  <Separator orientation="vertical" className="h-12" />
                  <RibbonGroup title="Advanced Data">
                    <RibbonButton
                      icon={Terminal}
                      label="SQL Query"
                      onClick={() => handle_action('data_sql_query')}
                    />
                    <RibbonButton
                      icon={Wrench}
                      label="Transform"
                      onClick={() => handle_action('data_transform')}
                    />
                  </RibbonGroup>
                </>
              )}
            </div>
          </TabsContent>

          {/* Insert Tab - Enhanced with UnifiedHeader controls */}
          <TabsContent value="insert" className="mt-0 h-full p-2">
            <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
              <RibbonGroup title="Quick Actions">
                <RibbonButton
                  icon={Plus}
                  label="Add Widget"
                  shortcut="Ctrl+N"
                  onClick={() => {
                    if (onAddWidget) onAddWidget();
                    else handle_action('insert_widget');
                  }}
                  variant="default"
                  className="ribbon-button-primary"
                />
                <RibbonButton
                  icon={Plus}
                  label="Add Section"
                  onClick={() => {
                    if (onAddSection) onAddSection();
                    else handle_action('insert_section');
                  }}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              <RibbonGroup title="Charts">
                <RibbonButton
                  icon={BarChart3}
                  label="Bar Chart"
                  onClick={() => {
                    if (onAddWidget) onAddWidget('chart');
                    else handle_action('insert_chart', { type: 'bar' });
                  }}
                />
                <RibbonButton
                  icon={LineChart}
                  label="Line Chart"
                  onClick={() => {
                    if (onAddWidget) onAddWidget('line');
                    else handle_action('insert_chart', { type: 'line' });
                  }}
                />
                <RibbonButton
                  icon={PieChart}
                  label="Pie Chart"
                  onClick={() => {
                    if (onAddWidget) onAddWidget('pie');
                    else handle_action('insert_chart', { type: 'pie' });
                  }}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              <RibbonGroup title="Widgets">
                <RibbonButton
                  icon={TrendingUp}
                  label="KPI"
                  onClick={() => {
                    if (onAddWidget) onAddWidget('kpi');
                    else handle_action('insert_widget', { type: 'kpi' });
                  }}
                />
                <RibbonButton
                  icon={Table}
                  label="Table"
                  onClick={() => {
                    if (onAddWidget) onAddWidget('table');
                    else handle_action('insert_widget', { type: 'table' });
                  }}
                />
                <RibbonButton
                  icon={Target}
                  label="Gauge"
                  onClick={() => {
                    if (onAddWidget) onAddWidget('gauge');
                    else handle_action('insert_widget', { type: 'gauge' });
                  }}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              <RibbonGroup title="Management">
                <RibbonButton
                  icon={Settings}
                  label="Manage Widgets"
                  onClick={() => {
                    if (onWidgetManagement) onWidgetManagement();
                    else handle_action('widget_manage');
                  }}
                />
                <RibbonButton
                  icon={RefreshCw}
                  label="Sync Widgets"
                  onClick={() => {
                    if (onWidgetSync) onWidgetSync();
                    else handle_action('widget_sync');
                  }}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              <RibbonGroup title="Content">
                <RibbonButton
                  icon={Type}
                  label="Text"
                  onClick={() => {
                    if (onAddCustomHTML) onAddCustomHTML();
                    else handle_action('insert_text');
                  }}
                />
                <RibbonButton
                  icon={Image}
                  label="Image"
                  onClick={() => handle_action('insert_image')}
                />
              </RibbonGroup>

              {advanced_config.customization.enable_custom_components && (
                <>
                  <Separator orientation="vertical" className="h-12" />
                  <RibbonGroup title="Custom">
                    <RibbonButton
                      icon={Plus}
                      label="Component"
                      onClick={() => handle_action('insert_custom_component')}
                    />
                  </RibbonGroup>
                </>
              )}
            </div>
          </TabsContent>

          {/* Style Tab - Consolidated Visual Customization */}
          <TabsContent value="style" className="mt-0 h-full p-2">
            <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
              {/* Theme & Colors Group */}
              <RibbonGroup title="Theme & Colors">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Palette}
                      label="Theme"
                      onClick={() => {}}
                      variant="default"
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-48">
                    <DropdownMenuLabel>Themes</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuRadioGroup value="light" onValueChange={(value) => onThemeChange?.(value)}>
                      <DropdownMenuRadioItem value="light">
                        <div className="flex flex-col">
                          <span>Light</span>
                          <span className="text-xs text-slate-500">Clean and bright</span>
                        </div>
                      </DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="dark">
                        <div className="flex flex-col">
                          <span>Dark</span>
                          <span className="text-xs text-slate-500">Easy on the eyes</span>
                        </div>
                      </DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="system">
                        <div className="flex flex-col">
                          <span>System</span>
                          <span className="text-xs text-slate-500">Follow system preference</span>
                        </div>
                      </DropdownMenuRadioItem>
                    </DropdownMenuRadioGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onStyleChange?.('custom_theme')}>
                      <Settings className="h-4 w-4 mr-2" />
                      Custom Theme...
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <RibbonButton
                  icon={Paintbrush}
                  label="Colors"
                  onClick={() => onStyleChange?.('colors')}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* Typography Group */}
              <RibbonGroup title="Typography">
                <div className="flex items-center border border-slate-200 rounded">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ribbon-button-icon rounded-r-none border-0"
                        onClick={() => onTypographyChange?.({ style: 'bold' })}
                      >
                        <Bold className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Bold</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ribbon-button-icon rounded-none border-0 border-l border-slate-200"
                        onClick={() => onTypographyChange?.({ style: 'italic' })}
                      >
                        <Italic className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Italic</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ribbon-button-icon rounded-l-none border-0 border-l border-slate-200"
                        onClick={() => onTypographyChange?.({ style: 'underline' })}
                      >
                        <Underline className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Underline</TooltipContent>
                  </Tooltip>
                </div>

                <div className="flex items-center border border-slate-200 rounded">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ribbon-button-icon rounded-r-none border-0"
                        onClick={() => onAlignmentChange?.('left')}
                      >
                        <AlignLeft className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Align Left</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ribbon-button-icon rounded-none border-0 border-l border-slate-200"
                        onClick={() => onAlignmentChange?.('center')}
                      >
                        <AlignCenter className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Align Center</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ribbon-button-icon rounded-l-none border-0 border-l border-slate-200"
                        onClick={() => onAlignmentChange?.('right')}
                      >
                        <AlignRight className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Align Right</TooltipContent>
                  </Tooltip>
                </div>

                <RibbonButton
                  icon={Type}
                  label="Font"
                  onClick={() => onTypographyChange?.({ action: 'font_settings' })}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* Layout & Spacing Group */}
              <RibbonGroup title="Layout & Spacing">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Layout}
                      label="Layout"
                      onClick={() => {}}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel>Layout Types</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onLayoutChange?.('grid')}>
                      <Grid className="h-4 w-4 mr-2" />
                      <div className="flex flex-col">
                        <span>Grid</span>
                        <span className="text-xs text-slate-500">Structured grid layout</span>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onLayoutChange?.('masonry')}>
                      <Layout className="h-4 w-4 mr-2" />
                      <div className="flex flex-col">
                        <span>Masonry</span>
                        <span className="text-xs text-slate-500">Pinterest-style layout</span>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onLayoutChange?.('flex')}>
                      <Rows className="h-4 w-4 mr-2" />
                      <div className="flex flex-col">
                        <span>Flexible</span>
                        <span className="text-xs text-slate-500">Responsive flex layout</span>
                      </div>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      className="ribbon-button-secondary h-9 px-3 border border-slate-300 hover:border-slate-400 shadow-sm"
                      onClick={() => onStyleChange?.('spacing')}
                    >
                      <Move className="h-4 w-4 mr-2" />
                      Spacing
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Adjust spacing and margins</p>
                  </TooltipContent>
                </Tooltip>
              </RibbonGroup>
            </div>
          </TabsContent>



          {/* Analyze Tab - Data Analysis Tools */}
          <TabsContent value="analyze" className="mt-0 h-full p-2">
            <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
              {/* AI Insights Group */}
              <RibbonGroup
                title="AI Insights"
                id="analyze-ai"
                description="AI-powered data analysis"
              >
                <RibbonButton
                  icon={Brain}
                  label="Generate Insights"
                  onClick={onGenerateInsights}
                  variant="default"
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                />

                <RibbonButton
                  icon={TrendingUp}
                  label="Trends"
                  onClick={onTrendAnalysis}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* Time Analysis Group */}
              <RibbonGroup
                title="Time Analysis"
                id="analyze-time"
                description="Time-based comparisons"
              >
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Clock}
                      label="Compare"
                      onClick={() => {}}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel>Time Comparisons</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {timeComparisons.map(({ period, label, description }) => (
                      <DropdownMenuItem
                        key={period}
                        onClick={() => onTimeComparison?.(period)}
                        className="cursor-pointer"
                      >
                        <Clock className="h-4 w-4 mr-2" />
                        <div className="flex flex-col">
                          <span>{label}</span>
                          <span className="text-xs text-slate-500">{description}</span>
                        </div>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* Statistical Analysis Group */}
              <RibbonGroup
                title="Statistics"
                id="analyze-stats"
                description="Statistical analysis tools"
              >
                <RibbonButton
                  icon={Calculator}
                  label="Statistics"
                  onClick={onStatisticalAnalysis}
                />

                <RibbonButton
                  icon={Calculator}
                  label="Formulas"
                  onClick={onFormulaEditor}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* Data Exploration Group */}
              <RibbonGroup
                title="Explore"
                id="analyze-explore"
                description="Data exploration tools"
              >
                <RibbonButton
                  icon={Database}
                  label="Raw Data"
                  onClick={onRawDataView}
                />

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Target}
                      label="Drill"
                      onClick={() => {}}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel>Drill Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onDrillDown?.()}>
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Drill Down
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <TrendingDown className="h-4 w-4 mr-2" />
                      Drill Up
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Eye className="h-4 w-4 mr-2" />
                      Drill Through
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </RibbonGroup>
            </div>
          </TabsContent>

          {/* Export & Share Tab */}
          <TabsContent value="share" className="mt-0 h-full p-2">
            <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
              {/* Export Group */}
              <RibbonGroup
                title="Export"
                id="share-export"
                description="Export dashboard in various formats"
              >
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Download}
                      label="Export"
                      onClick={() => {}}
                      variant="default"
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56">
                    <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {exportFormats.map(({ format, label, icon: Icon, description }) => (
                      <DropdownMenuItem
                        key={format}
                        onClick={() => onExport?.(format)}
                        className="cursor-pointer"
                      >
                        <Icon className="h-4 w-4 mr-2" />
                        <div className="flex flex-col">
                          <span>{label}</span>
                          <span className="text-xs text-slate-500">{description}</span>
                        </div>
                      </DropdownMenuItem>
                    ))}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Settings className="h-4 w-4 mr-2" />
                      Export Settings...
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <RibbonButton
                  icon={Clock}
                  label="Schedule"
                  onClick={onScheduleReport}
                />

                <RibbonButton
                  icon={Printer}
                  label="Print"
                  onClick={() => onPrint?.()}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* Share Group */}
              <RibbonGroup
                title="Share"
                id="share-sharing"
                description="Share dashboard with others"
              >
                <RibbonButton
                  icon={Share}
                  label="Share Link"
                  onClick={() => onShare?.()}
                  variant="default"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                />

                <RibbonButton
                  icon={Code}
                  label="Embed"
                  onClick={() => onGenerateEmbedCode?.()}
                />

                <RibbonButton
                  icon={Users}
                  label="Collaborate"
                  onClick={() => onCollaboration?.()}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* Permissions Group */}
              <RibbonGroup
                title="Permissions"
                id="share-permissions"
                description="Manage access and permissions"
              >
                <RibbonButton
                  icon={Lock}
                  label="Access Control"
                  onClick={() => onPermissionManagement?.()}
                  disabled={!userPermissions.canShare}
                />

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Shield}
                      label="Security"
                      onClick={() => {}}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel>Security Options</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Key className="h-4 w-4 mr-2" />
                      Password Protection
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Calendar className="h-4 w-4 mr-2" />
                      Expiration Date
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Eye className="h-4 w-4 mr-2" />
                      View-Only Mode
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </RibbonGroup>
            </div>
          </TabsContent>

          {/* Admin/Settings Tab - Optimized Layout */}
          <TabsContent value="admin" className="mt-0 h-full p-2">
            <div className="flex items-center space-x-2 overflow-x-auto overflow-y-hidden h-full min-h-0">
              {/* User & Preferences Group - Optimized */}
              <RibbonGroup
                title="User"
                id="admin-user"
                description="User settings and preferences"
              >
                {/* User Profile - Following Dashboard tab pattern */}
                <RibbonButton
                  icon={User}
                  label="Profile"
                  onClick={onUserPreferences}
                />

                {/* Preferences Dropdown - Following Dashboard tab pattern */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Settings}
                      label="Preferences"
                      onClick={() => {}}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-48">
                    <DropdownMenuLabel>Preferences</DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    {/* Language Selection */}
                    <DropdownMenuItem onClick={() => {}}>
                      <Languages className="h-4 w-4 mr-2" />
                      Language Settings
                    </DropdownMenuItem>

                    {/* Notifications */}
                    <DropdownMenuItem onClick={() => {}}>
                      <Bell className="h-4 w-4 mr-2" />
                      Notifications
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />

                    {/* Quick Language Options */}
                    <DropdownMenuLabel className="text-xs">Quick Language</DropdownMenuLabel>
                    <DropdownMenuRadioGroup onValueChange={onLanguageChange}>
                      <DropdownMenuRadioItem value="en" className="text-xs">🇺🇸 English</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="es" className="text-xs">🇪🇸 Español</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="fr" className="text-xs">🇫🇷 Français</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="de" className="text-xs">🇩🇪 Deutsch</DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="zh" className="text-xs">🇨🇳 中文</DropdownMenuRadioItem>
                    </DropdownMenuRadioGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* Interface Controls Group - Streamlined */}
              <RibbonGroup
                title="Interface"
                id="admin-interface"
                description="Dashboard interface controls"
              >
                {/* Mode Toggle - Following Dashboard tab pattern */}
                <RibbonButton
                  icon={current_mode === 'simple' ? Settings : Sparkles}
                  label={current_mode === 'simple' ? 'Advanced' : 'Simple'}
                  onClick={() => {
                    if (onModeToggle) {
                      onModeToggle();
                    } else if (can_switch) {
                      toggle_mode();
                    }
                  }}
                  disabled={!can_switch}
                  variant={current_mode === 'advanced' ? 'default' : 'outline'}
                />

                {/* AI Assistant - Following Dashboard tab pattern */}
                <RibbonButton
                  icon={Bot}
                  label="AI Assistant"
                  onClick={onAIAssistantToggle}
                />
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* Data & Security Group - Consolidated */}
              <RibbonGroup
                title="Data & Security"
                id="admin-data-security"
                description="Data sources and security settings"
              >
                {/* Data Sources Dropdown - Following Dashboard tab pattern */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Database}
                      label="Data Sources"
                      onClick={() => {}}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-44">
                    <DropdownMenuLabel>Data Sources</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={onDataSourceConfig}>
                      <Database className="h-4 w-4 mr-2" />
                      Configure Sources
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={onAPIKeyManagement}>
                      <Key className="h-4 w-4 mr-2" />
                      API Keys
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Security Dropdown - Following Dashboard tab pattern */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={Shield}
                      label="Security"
                      onClick={() => {}}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-44">
                    <DropdownMenuLabel>Security</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={onAccessControl}
                      disabled={!userPermissions.canManageUsers}
                    >
                      <Shield className="h-4 w-4 mr-2" />
                      Access Control
                      {!userPermissions.canManageUsers && (
                        <Badge variant="secondary" className="ml-auto text-xs">No Access</Badge>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {}}>
                      <Monitor className="h-4 w-4 mr-2" />
                      Audit Log
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </RibbonGroup>

              <Separator orientation="vertical" className="h-12" />

              {/* System & Support Group - Consolidated */}
              <RibbonGroup
                title="System"
                id="admin-system"
                description="System settings and support"
              >
                {/* Primary Settings Button - Following Dashboard tab pattern */}
                <RibbonButton
                  icon={Settings}
                  label="Settings"
                  onClick={onSettings}
                />

                {/* Support & Info Dropdown - Following Dashboard tab pattern */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <RibbonButton
                      icon={HelpCircle}
                      label="Help"
                      onClick={() => {}}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-40">
                    <DropdownMenuLabel>Support</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => {}}>
                      <HelpCircle className="h-4 w-4 mr-2" />
                      Help & Docs
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {}}>
                      <Info className="h-4 w-4 mr-2" />
                      About
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => {}}>
                      <Globe className="h-4 w-4 mr-2" />
                      Support Center
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </RibbonGroup>
            </div>
          </TabsContent>
        </div>

        {/* Enhanced Mobile Interface */}
        <MobileRibbonInterface
          onAddWidget={onAddWidget}
          onRefresh={onRefresh}
          onSettings={onSettings}
          onExport={onExport}
          onShare={onShare}
          onFilterToggle={onFilterToggle}
          onSortToggle={onSortToggle}
          onStyleChange={onStyleChange}
          onWidgetManagement={onWidgetManagement}
        />

        {/* Command Palette */}
        <CommandPalette
          isOpen={isCommandPaletteOpen}
          onOpenChange={setIsCommandPaletteOpen}
          actions={[
            { id: 'add-widget', label: 'Add Widget', action: () => onAddWidget?.('chart'), tab: 'insert', shortcut: 'Ctrl+Shift+W' },
            { id: 'save', label: 'Save Dashboard', action: onSave, tab: 'dashboard', shortcut: 'Ctrl+S' },
            { id: 'new', label: 'New Dashboard', action: onNew, tab: 'dashboard', shortcut: 'Ctrl+N' },
            { id: 'recent', label: 'Recent Dashboards', action: onRecent, tab: 'dashboard', shortcut: 'Ctrl+R' },
            { id: 'export', label: 'Export Dashboard', action: () => onExport?.('pdf'), tab: 'dashboard', shortcut: 'Ctrl+E' },
            { id: 'refresh', label: 'Refresh Data', action: onRefresh, tab: 'data', shortcut: 'F5' },
            { id: 'filter', label: 'Toggle Filters', action: onFilterToggle, tab: 'data', shortcut: 'Ctrl+Shift+F' },
            { id: 'theme', label: 'Change Theme', action: () => onThemeChange?.(theme === 'dark' ? 'light' : 'dark'), tab: 'style' },
            { id: 'settings', label: 'Dashboard Settings', action: onSettings, tab: 'admin' },
            { id: 'share', label: 'Share Dashboard', action: () => onShare?.(), tab: 'share' },
          ].filter(action => action.action)}
          onExecuteAction={(action) => action.action?.()}
        />

        {/* Accessibility Announcements */}
        <AnnouncementRegion />
      </Tabs>
      </div>
    </TooltipProvider>
  );
};
