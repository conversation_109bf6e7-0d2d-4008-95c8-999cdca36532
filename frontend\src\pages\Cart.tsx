import { useState } from "react";
import { DashboardLayout } from "@/components/DashboardLayout";
import { useCart } from "@/contexts/CartContext";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Trash2, ShoppingCart, Plus, Minus, Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { personaApi } from "@/lib/api";
import { AIPersona } from "@/data/aiPersonas";

const Cart = () => {
  const { items, totalItems, removeFromCart, updateQuantity, clearCart, isLoading } = useCart();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [personas, setPersonas] = useState<Record<string, AIPersona>>({});
  const [isLoadingPersonas, setIsLoadingPersonas] = useState(true);

  // Fetch persona details for each item in the cart
  useState(() => {
    const fetchPersonas = async () => {
      setIsLoadingPersonas(true);
      try {
        // Get all personas
        const response = await personaApi.getPersonas();

        // Create a map of persona ID to persona details
        const personaMap: Record<string, AIPersona> = {};
        response.personas.forEach(persona => {
          personaMap[persona.id] = persona;
        });

        setPersonas(personaMap);
      } catch (error) {
        console.error("Failed to load personas:", error);
        toast({
          title: "Error",
          description: "Failed to load AI personas.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingPersonas(false);
      }
    };

    fetchPersonas();
  });

  // Calculate total price (in a real app, you would get prices from a database)
  // For now, we'll use a fixed price of $10 per persona
  const pricePerPersona = 10.0;
  const totalPrice = items.reduce((total, item) => total + (item.quantity * pricePerPersona), 0);

  const handleRemoveItem = async (itemId: string) => {
    await removeFromCart(itemId);
  };

  const handleUpdateQuantity = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    await updateQuantity(itemId, newQuantity);
  };

  const handleClearCart = async () => {
    await clearCart();
  };

  const handleCheckout = async () => {
    setIsCheckingOut(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'}/purchases/create`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          payment_method: 'credit_card',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create purchase');
      }

      const purchase = await response.json();

      // Clear the cart in the frontend state after successful checkout
      await clearCart();

      toast({
        title: "Purchase Successful",
        description: `Your purchase of ${totalItems} AI personas has been completed.`,
        variant: "default",
      });

      // Redirect to a success page or dashboard
      navigate("/dashboard");
    } catch (error) {
      console.error("Error during checkout:", error);
      toast({
        title: "Checkout Failed",
        description: "There was an error processing your purchase. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCheckingOut(false);
    }
  };

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        <div className="flex flex-col space-y-2">
          <h1 className="text-2xl font-bold">Your Cart</h1>
          <p className="text-muted-foreground">
            Review and checkout your AI personas
          </p>
        </div>

        {isLoading || isLoadingPersonas ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h3 className="text-lg font-medium">Loading your cart...</h3>
          </div>
        ) : items.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <ShoppingCart className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">Your cart is empty</h3>
            <p className="text-muted-foreground mt-2 mb-6">
              Add AI personas from the marketplace to get started
            </p>
            <Button onClick={() => navigate("/ai-marketplace")}>
              Browse Marketplace
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6">
            {/* Cart Items */}
            <div className="space-y-4">
              {items.map((item) => {
                const persona = personas[item.persona_id];
                return (
                  <Card key={item.id} className="overflow-hidden">
                    <div className="flex flex-col md:flex-row">
                      <div className="p-6 flex-1">
                        <div className="flex items-start">
                          <div className="h-12 w-12 rounded-full overflow-hidden bg-muted flex items-center justify-center mr-4">
                            {persona && (
                              <img
                                src={persona.imageUrl}
                                alt={persona.name}
                                className="h-full w-full object-cover"
                              />
                            )}
                          </div>
                          <div className="flex-1">
                            <h3 className="font-medium text-lg">
                              {persona ? persona.name : item.persona_id}
                            </h3>
                            {persona && (
                              <p className="text-sm text-muted-foreground">
                                {persona.industry} • {persona.skills.slice(0, 2).join(", ")}
                              </p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="font-medium">${pricePerPersona.toFixed(2)}</p>
                            <p className="text-sm text-muted-foreground">per unit</p>
                          </div>
                        </div>
                      </div>
                      <div className="bg-muted/20 p-6 flex items-center justify-between md:w-64">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <span className="w-8 text-center">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-500 hover:text-red-600 hover:bg-red-50"
                          onClick={() => handleRemoveItem(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>

            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Subtotal ({totalItems} items)</span>
                  <span>${totalPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>${totalPrice.toFixed(2)}</span>
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-4">
                <Button
                  className="w-full"
                  onClick={handleCheckout}
                  disabled={isCheckingOut}
                >
                  {isCheckingOut ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Checkout"
                  )}
                </Button>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleClearCart}
                  disabled={isCheckingOut}
                >
                  Clear Cart
                </Button>
              </CardFooter>
            </Card>
          </div>
        )}
      </motion.div>
    </DashboardLayout>
  );
};

export default Cart;
