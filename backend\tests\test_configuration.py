"""
Tests for the hierarchical configuration system.

This module tests the new configuration management system including
Pydantic validation, environment-specific configurations, and the
configuration manager.
"""

import os
import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, MagicMock

from app.settings.base import BaseConfig, EnvironmentConfig, LoggingConfig, MonitoringConfig
from app.settings.database import DatabaseConfig, RedisConfig
from app.settings.security import SecurityConfig
from app.settings.llm import LLMConfig, LLMProviderConfig
from app.settings.app import AppConfig, EmailConfig, FileConfig, VectorConfig
from app.settings.manager import ConfigurationManager


class TestBaseConfig:
    """Test the base configuration class."""
    
    def test_base_config_creation(self):
        """Test basic configuration creation."""
        config = EnvironmentConfig(environment="development", debug=True)
        assert config.environment == "development"
        assert config.debug is True
        assert config.is_development is True
        assert config.is_production is False
    
    def test_environment_validation(self):
        """Test environment validation."""
        with pytest.raises(ValueError, match="Environment must be one of"):
            EnvironmentConfig(environment="invalid")
    
    def test_from_env_method(self):
        """Test creating configuration from environment variables."""
        with patch.dict(os.environ, {
            "ENVIRONMENT": "production",
            "DEBUG": "false",
            "TESTING": "false"
        }):
            config = EnvironmentConfig(
                environment=os.getenv("ENVIRONMENT", "development"),
                debug=os.getenv("DEBUG", "false").lower() == "true",
                testing=os.getenv("TESTING", "false").lower() == "true"
            )
            assert config.environment == "production"
            assert config.debug is False
            assert config.is_production is True


class TestDatabaseConfig:
    """Test database configuration."""
    
    def test_database_config_creation(self):
        """Test database configuration creation."""
        config = DatabaseConfig(
            url="postgresql://user:pass@localhost:5432/db",
            pool_size=10,
            max_overflow=20
        )
        assert config.url == "postgresql://user:pass@localhost:5432/db"
        assert config.pool_size == 10
        assert config.max_overflow == 20
    
    def test_database_url_validation(self):
        """Test database URL validation."""
        with pytest.raises(ValueError, match="Database URL cannot be empty"):
            DatabaseConfig(url="")
        
        with pytest.raises(ValueError, match="Database URL must start with one of"):
            DatabaseConfig(url="invalid://localhost")
    
    def test_engine_kwargs_property(self):
        """Test engine kwargs generation."""
        config = DatabaseConfig(
            url="postgresql://user:pass@localhost:5432/db",
            pool_size=15,
            echo=True
        )
        kwargs = config.engine_kwargs
        assert kwargs["pool_size"] == 15
        assert kwargs["echo"] is True
        assert "connect_args" in kwargs
    
    def test_from_env_method(self):
        """Test creating database config from environment."""
        with patch.dict(os.environ, {
            "DATABASE_URL": "postgresql://test:test@localhost:5432/test",
            "DATABASE_ECHO": "true",
            "DATABASE_POOL_SIZE": "5"
        }):
            config = DatabaseConfig.from_env()
            assert config.url == "postgresql://test:test@localhost:5432/test"
            assert config.echo is True
            assert config.pool_size == 5


class TestSecurityConfig:
    """Test security configuration."""
    
    def test_security_config_creation(self):
        """Test security configuration creation."""
        config = SecurityConfig(
            jwt_secret_key="a-very-long-secret-key-for-testing-purposes-only",
            access_token_expire_minutes=30,
            max_upload_size=10485760
        )
        assert len(config.jwt_secret_key) >= 32
        assert config.access_token_expire_minutes == 30
        assert config.max_upload_size_mb == 10.0
    
    def test_jwt_secret_validation(self):
        """Test JWT secret key validation."""
        with pytest.raises(ValueError, match="JWT secret key must be at least 32 characters"):
            SecurityConfig(jwt_secret_key="short")
    
    def test_jwt_algorithm_validation(self):
        """Test JWT algorithm validation."""
        with pytest.raises(ValueError, match="JWT algorithm must be one of"):
            SecurityConfig(
                jwt_secret_key="a-very-long-secret-key-for-testing-purposes-only",
                jwt_algorithm="INVALID"
            )
    
    def test_file_types_validation(self):
        """Test file types validation."""
        config = SecurityConfig(
            jwt_secret_key="a-very-long-secret-key-for-testing-purposes-only",
            allowed_file_types=["csv", ".xlsx", "PDF"]
        )
        assert config.allowed_file_types == [".csv", ".xlsx", ".pdf"]
    
    def test_password_policy_description(self):
        """Test password policy description generation."""
        config = SecurityConfig(
            jwt_secret_key="a-very-long-secret-key-for-testing-purposes-only",
            min_password_length=8,
            require_uppercase=True,
            require_numbers=True
        )
        description = config.password_policy_description
        assert "at least 8 characters" in description
        assert "uppercase letters" in description
        assert "numbers" in description


class TestLLMConfig:
    """Test LLM configuration."""
    
    def test_llm_config_creation(self):
        """Test LLM configuration creation."""
        provider_config = LLMProviderConfig(
            name="test_provider",
            api_key="test-api-key",
            endpoint="https://api.test.com/v1",
            default_model="test-model"
        )
        
        config = LLMConfig(
            default_provider="test_provider",
            providers={"test_provider": provider_config}
        )
        
        assert config.default_provider == "test_provider"
        assert "test_provider" in config.enabled_providers
        assert config.get_provider_config("test_provider") == provider_config
    
    def test_provider_config_validation(self):
        """Test LLM provider configuration validation."""
        with pytest.raises(ValueError, match="API key cannot be empty"):
            LLMProviderConfig(
                name="test",
                api_key="",
                endpoint="https://api.test.com"
            )
        
        with pytest.raises(ValueError, match="Endpoint must be a valid HTTP/HTTPS URL"):
            LLMProviderConfig(
                name="test",
                api_key="test-key",
                endpoint="invalid-url"
            )
    
    def test_fallback_providers_validation(self):
        """Test fallback providers validation."""
        with pytest.raises(ValueError, match="Fallback providers list cannot be empty"):
            LLMConfig(fallback_providers=[])


class TestAppConfig:
    """Test main application configuration."""
    
    def test_app_config_creation(self):
        """Test application configuration creation."""
        config = AppConfig(
            name="Test App",
            version="1.0.0",
            database=DatabaseConfig(url="sqlite:///test.db"),
            security=SecurityConfig(jwt_secret_key="a-very-long-secret-key-for-testing-purposes-only")
        )
        
        assert config.name == "Test App"
        assert config.version == "1.0.0"
        assert config.database.url == "sqlite:///test.db"
    
    def test_config_summary(self):
        """Test configuration summary generation."""
        config = AppConfig(
            name="Test App",
            database=DatabaseConfig(url="postgresql://test:test@localhost:5432/test"),
            security=SecurityConfig(jwt_secret_key="a-very-long-secret-key-for-testing-purposes-only"),
            llm=LLMConfig(default_provider="groq")
        )
        
        summary = config.config_summary
        assert summary["app"]["name"] == "Test App"
        assert summary["database"]["url_scheme"] == "postgresql"
        assert summary["llm"]["default_provider"] == "groq"
    
    def test_from_yaml_file(self):
        """Test loading configuration from YAML file."""
        config_data = {
            "name": "YAML Test App",
            "version": "2.0.0",
            "database": {
                "url": "postgresql://yaml:test@localhost:5432/yaml",
                "pool_size": 5
            },
            "security": {
                "jwt_secret_key": "yaml-secret-key-for-testing-purposes-only",
                "access_token_expire_minutes": 60
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = AppConfig.from_yaml_file(temp_path)
            assert config.name == "YAML Test App"
            assert config.version == "2.0.0"
            assert config.database.pool_size == 5
            assert config.security.access_token_expire_minutes == 60
        finally:
            os.unlink(temp_path)


class TestConfigurationManager:
    """Test the configuration manager."""
    
    def test_configuration_manager_creation(self):
        """Test configuration manager creation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = ConfigurationManager(config_dir=temp_dir)
            assert manager.config_dir == Path(temp_dir)
            assert manager.environment == "development"  # Default
    
    def test_load_config_from_env(self):
        """Test loading configuration from environment variables."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.dict(os.environ, {
                "DATABASE_URL": "postgresql://test:test@localhost:5432/test",
                "JWT_SECRET_KEY": "test-secret-key-for-configuration-manager-testing",
                "APP_NAME": "Test Manager App"
            }):
                manager = ConfigurationManager(config_dir=temp_dir)
                config = manager.load_config()
                
                assert config.database.url == "postgresql://test:test@localhost:5432/test"
                assert config.security.jwt_secret_key == "test-secret-key-for-configuration-manager-testing"
                assert config.name == "Test Manager App"
    
    def test_load_config_with_yaml_file(self):
        """Test loading configuration with YAML file override."""
        config_data = {
            "name": "YAML Override App",
            "database": {
                "pool_size": 15
            }
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create environment-specific config file
            config_file = Path(temp_dir) / "development.yaml"
            with open(config_file, 'w') as f:
                yaml.dump(config_data, f)
            
            with patch.dict(os.environ, {
                "DATABASE_URL": "postgresql://env:test@localhost:5432/env",
                "JWT_SECRET_KEY": "env-secret-key-for-yaml-override-testing",
                "ENVIRONMENT": "development"
            }):
                manager = ConfigurationManager(config_dir=temp_dir)
                config = manager.load_config()
                
                # Environment variables should take precedence
                assert config.database.url == "postgresql://env:test@localhost:5432/env"
                assert config.security.jwt_secret_key == "env-secret-key-for-yaml-override-testing"
                
                # YAML values should be used where env vars are not set
                assert config.name == "YAML Override App"
                assert config.database.pool_size == 15
    
    def test_config_caching(self):
        """Test configuration caching."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = ConfigurationManager(config_dir=temp_dir)
            
            # Load config twice
            config1 = manager.load_config()
            config2 = manager.load_config()
            
            # Should return the same cached instance
            assert config1 is config2
    
    def test_config_reload(self):
        """Test configuration reloading."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = ConfigurationManager(config_dir=temp_dir)
            
            # Load initial config
            config1 = manager.load_config()
            
            # Reload config
            config2 = manager.reload_config()
            
            # Should be different instances
            assert config1 is not config2


@pytest.mark.integration
class TestConfigurationIntegration:
    """Integration tests for the configuration system."""
    
    def test_full_configuration_loading(self):
        """Test loading a complete configuration."""
        with patch.dict(os.environ, {
            "ENVIRONMENT": "testing",
            "DATABASE_URL": "postgresql://integration:test@localhost:5432/integration",
            "JWT_SECRET_KEY": "integration-test-secret-key-for-full-configuration-loading",
            "GROQ_API_KEY": "test-groq-key",
            "OPENAI_API_KEY": "test-openai-key",
            "EMAIL_ENABLED": "true",
            "EMAIL_SENDER": "<EMAIL>"
        }):
            config = AppConfig.from_env()
            
            # Verify all major components are configured
            assert config.environment.environment == "testing"
            assert config.database.url.startswith("postgresql://")
            assert len(config.security.jwt_secret_key) >= 32
            assert "groq" in config.llm.providers
            assert "openai" in config.llm.providers
            assert config.email.enabled is True
            assert config.email.sender == "<EMAIL>"
    
    def test_configuration_validation_errors(self):
        """Test configuration validation catches errors."""
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = ConfigurationManager(config_dir=temp_dir)
            
            # Test with invalid configuration
            with patch.dict(os.environ, {
                "DATABASE_URL": "",  # Invalid: empty URL
                "JWT_SECRET_KEY": "short"  # Invalid: too short
            }):
                # Should not raise exception but log warnings
                config = manager.load_config()
                # Basic config should still be created
                assert config is not None
