import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Info, Sparkles, ArrowRight, Settings } from 'lucide-react';
import { BusinessProfile } from '@/lib/businessProfileApi';

interface DefaultProfileNotificationProps {
  profile: BusinessProfile;
  onCustomizeProfile: () => void;
  onCreateNewProfile: () => void;
  className?: string;
}

export const DefaultProfileNotification: React.FC<DefaultProfileNotificationProps> = ({
  profile,
  onCustomizeProfile,
  onCreateNewProfile,
  className
}) => {
  // Check if this is a default profile
  const isDefaultProfile = profile.context_metadata?.is_default_profile === true;
  const needsCustomization = profile.context_metadata?.needs_customization === true;

  if (!isDefaultProfile && !needsCustomization) {
    return null;
  }

  return (
    <Card className={`border-amber-200 bg-amber-50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Info className="h-5 w-5 text-amber-600" />
          <CardTitle className="text-amber-800">
            {isDefaultProfile ? 'Using Default Profile' : 'Profile Needs Customization'}
          </CardTitle>
          <Badge variant="secondary" className="bg-amber-100 text-amber-700">
            {isDefaultProfile ? 'Default' : 'Incomplete'}
          </Badge>
        </div>
        <CardDescription className="text-amber-700">
          {isDefaultProfile 
            ? 'You\'re currently using a default profile. Customize it to get better AI recommendations tailored to your business.'
            : 'Your profile could use some customization to improve AI agent performance.'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="text-sm text-amber-700">
            <strong>Benefits of customizing your profile:</strong>
            <ul className="mt-1 ml-4 list-disc space-y-1">
              <li>More relevant AI responses and recommendations</li>
              <li>Better understanding of your business context</li>
              <li>Improved data analysis and insights</li>
              <li>Personalized agent interactions</li>
            </ul>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2">
            <Button 
              onClick={onCustomizeProfile}
              className="flex items-center gap-2 bg-amber-600 hover:bg-amber-700"
            >
              <Settings className="h-4 w-4" />
              Customize This Profile
              <ArrowRight className="h-4 w-4" />
            </Button>
            
            <Button 
              variant="outline" 
              onClick={onCreateNewProfile}
              className="flex items-center gap-2 border-amber-300 text-amber-700 hover:bg-amber-100"
            >
              <Sparkles className="h-4 w-4" />
              Create New Profile
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Compact version for smaller spaces
export const DefaultProfileBanner: React.FC<DefaultProfileNotificationProps> = ({
  profile,
  onCustomizeProfile,
  onCreateNewProfile,
  className
}) => {
  const isDefaultProfile = profile.context_metadata?.is_default_profile === true;
  const needsCustomization = profile.context_metadata?.needs_customization === true;

  if (!isDefaultProfile && !needsCustomization) {
    return null;
  }

  return (
    <Alert className={`border-amber-200 bg-amber-50 ${className}`}>
      <Info className="h-4 w-4 text-amber-600" />
      <AlertTitle className="text-amber-800">
        {isDefaultProfile ? 'Default Profile Active' : 'Profile Needs Updates'}
      </AlertTitle>
      <AlertDescription className="text-amber-700">
        <div className="flex items-center justify-between mt-2">
          <span className="text-sm">
            {isDefaultProfile 
              ? 'Customize your profile for better AI recommendations'
              : 'Update your profile to improve AI performance'
            }
          </span>
          <div className="flex gap-2">
            <Button 
              size="sm" 
              onClick={onCustomizeProfile}
              className="bg-amber-600 hover:bg-amber-700 text-xs"
            >
              Customize
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={onCreateNewProfile}
              className="border-amber-300 text-amber-700 hover:bg-amber-100 text-xs"
            >
              New Profile
            </Button>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
};
