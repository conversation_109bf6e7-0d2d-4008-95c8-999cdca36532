import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { 
  Settings, 
  Palette, 
  Database, 
  BarChart3, 
  RefreshCw,
  Eye,
  Code,
  Layers
} from 'lucide-react';
import { WidgetResponse, VisualizationType, DashboardDataSource } from '@/types/dashboard-customization';
import { useToast } from '@/hooks/use-toast';

interface WidgetEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  widget: WidgetResponse | null;
  onSave: (widgetId: string, updates: Partial<WidgetResponse>) => void;
  availableDataSources?: DashboardDataSource[];
  availableSections?: { id: string; name: string }[];
}

interface WidgetFormData {
  title: string;
  description?: string;
  widget_type: VisualizationType;
  section_id: string;
  data_config?: any;
  visualization_config?: any;
  position_config: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  customization?: any;
  refresh_interval: number;
  is_active: boolean;
}

const WIDGET_TYPES = [
  { value: VisualizationType.CHART, label: 'Chart', icon: BarChart3 },
  { value: VisualizationType.TABLE, label: 'Table', icon: Database },
  { value: VisualizationType.KPI, label: 'KPI', icon: Eye },
  { value: VisualizationType.TEXT, label: 'Text', icon: Code },
];

const CHART_TYPES = [
  'line', 'bar', 'area', 'pie', 'doughnut', 'scatter', 'radar', 'bubble'
];

const COLOR_SCHEMES = [
  { name: 'Default', colors: ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'] },
  { name: 'Blue', colors: ['#1f77b4', '#aec7e8', '#ff7f0e', '#ffbb78'] },
  { name: 'Green', colors: ['#2ca02c', '#98df8a', '#d62728', '#ff9896'] },
  { name: 'Purple', colors: ['#9467bd', '#c5b0d5', '#8c564b', '#c49c94'] },
];

export const WidgetEditor: React.FC<WidgetEditorProps> = ({
  open,
  onOpenChange,
  widget,
  onSave,
  availableDataSources = [],
  availableSections = [],
}) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<WidgetFormData>({
    title: '',
    widget_type: VisualizationType.CHART,
    section_id: '',
    position_config: { x: 0, y: 0, w: 4, h: 3 },
    refresh_interval: 300,
    is_active: true,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when widget changes
  useEffect(() => {
    if (widget) {
      setFormData({
        title: widget.title,
        description: widget.description,
        widget_type: widget.widget_type,
        section_id: widget.section_id,
        data_config: widget.data_config || {},
        visualization_config: widget.visualization_config || {},
        position_config: widget.position_config || { x: 0, y: 0, w: 4, h: 3 },
        customization: widget.customization || {},
        refresh_interval: widget.refresh_interval || 300,
        is_active: widget.is_active ?? true,
      });
    }
  }, [widget]);

  // Handle form field changes
  const handleFieldChange = (field: keyof WidgetFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle nested object changes
  const handleNestedChange = (parent: keyof WidgetFormData, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value,
      },
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!widget) return;

    setIsSubmitting(true);
    try {
      await onSave(widget.id, formData);
      onOpenChange(false);
      toast({
        title: "Widget Updated",
        description: "Widget has been updated successfully.",
      });
    } catch (error) {
      console.error('Error updating widget:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update widget. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle data source selection
  const handleDataSourceChange = (dataSourceId: string) => {
    const dataSource = availableDataSources.find(ds => ds.id === dataSourceId);
    if (dataSource) {
      handleNestedChange('data_config', 'data_source_id', dataSourceId);
      handleNestedChange('data_config', 'data_source_type', dataSource.type);
    }
  };

  if (!widget) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Edit Widget: {widget.title}</span>
          </DialogTitle>
          <DialogDescription>
            Configure widget settings, data sources, and visualization options.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="general" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="data">Data</TabsTrigger>
            <TabsTrigger value="visualization">Visualization</TabsTrigger>
            <TabsTrigger value="layout">Layout</TabsTrigger>
          </TabsList>

          {/* General Settings */}
          <TabsContent value="general" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Widget Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleFieldChange('title', e.target.value)}
                  placeholder="Enter widget title"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="widget-type">Widget Type</Label>
                <Select
                  value={formData.widget_type}
                  onValueChange={(value) => handleFieldChange('widget_type', value as VisualizationType)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {WIDGET_TYPES.map((type) => {
                      const Icon = type.icon;
                      return (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center space-x-2">
                            <Icon className="h-4 w-4" />
                            <span>{type.label}</span>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Enter widget description"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="section">Section</Label>
                <Select
                  value={formData.section_id}
                  onValueChange={(value) => handleFieldChange('section_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select section" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableSections.map((section) => (
                      <SelectItem key={section.id} value={section.id}>
                        {section.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="refresh-interval">Refresh Interval (seconds)</Label>
                <div className="space-y-2">
                  <Slider
                    value={[formData.refresh_interval]}
                    onValueChange={([value]) => handleFieldChange('refresh_interval', value)}
                    min={30}
                    max={3600}
                    step={30}
                    className="w-full"
                  />
                  <div className="text-sm text-muted-foreground text-center">
                    {Math.floor(formData.refresh_interval / 60)}m {formData.refresh_interval % 60}s
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="is-active"
                checked={formData.is_active}
                onCheckedChange={(checked) => handleFieldChange('is_active', checked)}
              />
              <Label htmlFor="is-active">Widget Active</Label>
            </div>
          </TabsContent>

          {/* Data Configuration */}
          <TabsContent value="data" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-4 w-4" />
                  <span>Data Source</span>
                </CardTitle>
                <CardDescription>
                  Select and configure the data source for this widget.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Data Source</Label>
                  <Select
                    value={formData.data_config?.data_source_id || ''}
                    onValueChange={handleDataSourceChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select data source" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableDataSources.map((dataSource) => (
                        <SelectItem key={dataSource.id} value={dataSource.id}>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{dataSource.type}</Badge>
                            <span>{dataSource.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {formData.data_config?.data_source_id && (
                  <Accordion type="single" collapsible>
                    <AccordionItem value="query-config">
                      <AccordionTrigger>Query Configuration</AccordionTrigger>
                      <AccordionContent className="space-y-4">
                        <div className="space-y-2">
                          <Label>Query/Filter</Label>
                          <Textarea
                            value={formData.data_config?.query || ''}
                            onChange={(e) => handleNestedChange('data_config', 'query', e.target.value)}
                            placeholder="Enter SQL query, filter expression, or data selection criteria"
                            rows={4}
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>X-Axis Field</Label>
                            <Input
                              value={formData.data_config?.x_field || ''}
                              onChange={(e) => handleNestedChange('data_config', 'x_field', e.target.value)}
                              placeholder="e.g., date, category"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Y-Axis Field</Label>
                            <Input
                              value={formData.data_config?.y_field || ''}
                              onChange={(e) => handleNestedChange('data_config', 'y_field', e.target.value)}
                              placeholder="e.g., value, count"
                            />
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Visualization Configuration */}
          <TabsContent value="visualization" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Palette className="h-4 w-4" />
                  <span>Visualization Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.widget_type === VisualizationType.CHART && (
                  <>
                    <div className="space-y-2">
                      <Label>Chart Type</Label>
                      <Select
                        value={formData.visualization_config?.chart_type || 'line'}
                        onValueChange={(value) => handleNestedChange('visualization_config', 'chart_type', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {CHART_TYPES.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.charAt(0).toUpperCase() + type.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Color Scheme</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {COLOR_SCHEMES.map((scheme) => (
                          <Button
                            key={scheme.name}
                            variant={
                              formData.visualization_config?.color_scheme === scheme.name
                                ? 'default'
                                : 'outline'
                            }
                            className="h-12 justify-start"
                            onClick={() => handleNestedChange('visualization_config', 'color_scheme', scheme.name)}
                          >
                            <div className="flex items-center space-x-2">
                              <div className="flex space-x-1">
                                {scheme.colors.map((color, index) => (
                                  <div
                                    key={index}
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: color }}
                                  />
                                ))}
                              </div>
                              <span>{scheme.name}</span>
                            </div>
                          </Button>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                <Accordion type="single" collapsible>
                  <AccordionItem value="advanced">
                    <AccordionTrigger>Advanced Options</AccordionTrigger>
                    <AccordionContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={formData.visualization_config?.show_legend ?? true}
                            onCheckedChange={(checked) => handleNestedChange('visualization_config', 'show_legend', checked)}
                          />
                          <Label>Show Legend</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={formData.visualization_config?.show_grid ?? true}
                            onCheckedChange={(checked) => handleNestedChange('visualization_config', 'show_grid', checked)}
                          />
                          <Label>Show Grid</Label>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Layout Configuration */}
          <TabsContent value="layout" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Layers className="h-4 w-4" />
                  <span>Position & Size</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label>X Position</Label>
                    <Input
                      type="number"
                      value={formData.position_config.x}
                      onChange={(e) => handleNestedChange('position_config', 'x', parseInt(e.target.value) || 0)}
                      min={0}
                      max={11}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Y Position</Label>
                    <Input
                      type="number"
                      value={formData.position_config.y}
                      onChange={(e) => handleNestedChange('position_config', 'y', parseInt(e.target.value) || 0)}
                      min={0}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Width</Label>
                    <Input
                      type="number"
                      value={formData.position_config.w}
                      onChange={(e) => handleNestedChange('position_config', 'w', parseInt(e.target.value) || 1)}
                      min={1}
                      max={12}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Height</Label>
                    <Input
                      type="number"
                      value={formData.position_config.h}
                      onChange={(e) => handleNestedChange('position_config', 'h', parseInt(e.target.value) || 1)}
                      min={1}
                      max={12}
                    />
                  </div>
                </div>

                <div className="p-4 bg-muted rounded-lg">
                  <div className="text-sm font-medium mb-2">Preview</div>
                  <div className="grid grid-cols-12 gap-1 h-24 bg-background rounded border">
                    {Array.from({ length: 12 * 6 }, (_, i) => {
                      const col = i % 12;
                      const row = Math.floor(i / 12);
                      const isInWidget = 
                        col >= formData.position_config.x &&
                        col < formData.position_config.x + formData.position_config.w &&
                        row >= formData.position_config.y &&
                        row < formData.position_config.y + formData.position_config.h;
                      
                      return (
                        <div
                          key={i}
                          className={`aspect-square rounded-sm ${
                            isInWidget ? 'bg-primary' : 'bg-muted'
                          }`}
                        />
                      );
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
