import React from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  BarChart3,
  Database,
  Zap,
  ArrowRight,
  Sparkles,
  Target,
  TrendingUp,
  Grid,
  FileText,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface EmptyStateAction {
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  primary?: boolean;
  badge?: string;
}

interface EnhancedEmptyStateProps {
  type: 'dashboard' | 'section' | 'widgets' | 'data';
  title?: string;
  description?: string;
  actions?: EmptyStateAction[];
  className?: string;
  showQuickStart?: boolean;
}

const emptyStateConfig = {
  dashboard: {
    title: "Welcome to Your Dashboard",
    description: "Create your first dashboard to start visualizing your data and insights.",
    icon: BarChart3,
    gradient: "from-blue-500 to-purple-600",
    quickStart: [
      { step: 1, title: "Create Dashboard", description: "Set up your first dashboard" },
      { step: 2, title: "Add Sections", description: "Organize your content" },
      { step: 3, title: "Add Widgets", description: "Visualize your data" }
    ]
  },
  section: {
    title: "No Sections Yet",
    description: "Sections help organize your dashboard content. Create your first section to get started.",
    icon: Grid,
    gradient: "from-green-500 to-teal-600",
    quickStart: [
      { step: 1, title: "Add Section", description: "Create a new section" },
      { step: 2, title: "Customize Layout", description: "Configure the grid" },
      { step: 3, title: "Add Widgets", description: "Start adding content" }
    ]
  },
  widgets: {
    title: "No Widgets in This Section",
    description: "Widgets display your data and insights. Add your first widget to bring this section to life.",
    icon: Sparkles,
    gradient: "from-orange-500 to-red-600",
    quickStart: [
      { step: 1, title: "Choose Widget Type", description: "Select chart, table, or KPI" },
      { step: 2, title: "Connect Data", description: "Link to your data source" },
      { step: 3, title: "Customize", description: "Style and configure" }
    ]
  },
  data: {
    title: "No Data Sources Connected",
    description: "Connect your data sources to start creating powerful visualizations and insights.",
    icon: Database,
    gradient: "from-purple-500 to-pink-600",
    quickStart: [
      { step: 1, title: "Upload Data", description: "CSV, Excel, or connect API" },
      { step: 2, title: "Configure Source", description: "Set up data mapping" },
      { step: 3, title: "Create Widgets", description: "Build visualizations" }
    ]
  }
};

export const EnhancedEmptyState: React.FC<EnhancedEmptyStateProps> = ({
  type,
  title,
  description,
  actions = [],
  className,
  showQuickStart = true
}) => {
  const config = emptyStateConfig[type];
  const IconComponent = config.icon;

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn("flex items-center justify-center min-h-[400px] py-8", className)}
    >
      <div className="max-w-2xl mx-auto text-center space-y-8">
        {/* Icon and Title */}
        <motion.div variants={itemVariants} className="space-y-4">
          <div className="relative">
            <div className={cn(
              "w-20 h-20 mx-auto rounded-full bg-gradient-to-br flex items-center justify-center shadow-lg",
              config.gradient
            )}>
              <IconComponent className="h-10 w-10 text-white" />
            </div>
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center"
            >
              <Sparkles className="h-3 w-3 text-yellow-800" />
            </motion.div>
          </div>
          
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
              {title || config.title}
            </h2>
            <p className="text-slate-600 dark:text-slate-400 max-w-md mx-auto">
              {description || config.description}
            </p>
          </div>
        </motion.div>

        {/* Quick Start Guide */}
        {showQuickStart && config.quickStart && (
          <motion.div variants={itemVariants}>
            <Card className="bg-slate-50 dark:bg-slate-800/50 border-dashed">
              <CardContent className="p-6">
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <Target className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    Quick Start Guide
                  </span>
                </div>
                <div className="flex items-center justify-center space-x-8">
                  {config.quickStart.map((step, index) => (
                    <div key={step.step} className="flex items-center space-x-4">
                      <div className="text-center">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                          {step.step}
                        </div>
                        <div className="space-y-1">
                          <div className="text-sm font-medium text-slate-900 dark:text-slate-100">
                            {step.title}
                          </div>
                          <div className="text-xs text-slate-500 dark:text-slate-400">
                            {step.description}
                          </div>
                        </div>
                      </div>
                      {index < config.quickStart.length - 1 && (
                        <ArrowRight className="h-4 w-4 text-slate-400" />
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Actions */}
        {actions.length > 0 && (
          <motion.div variants={itemVariants} className="space-y-4">
            <div className="flex flex-wrap items-center justify-center gap-3">
              {actions.map((action, index) => {
                const ActionIcon = action.icon;
                return (
                  <Button
                    key={index}
                    onClick={action.onClick}
                    variant={action.primary ? "default" : "outline"}
                    size="lg"
                    className={cn(
                      "group relative",
                      action.primary 
                        ? "btn-dashboard-primary hover-lift" 
                        : "btn-dashboard-secondary hover-lift"
                    )}
                  >
                    <ActionIcon className="h-4 w-4 mr-2" />
                    {action.label}
                    {action.badge && (
                      <Badge variant="secondary" className="ml-2">
                        {action.badge}
                      </Badge>
                    )}
                  </Button>
                );
              })}
            </div>
            
            {/* Action descriptions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
              {actions.map((action, index) => {
                const ActionIcon = action.icon;
                return (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="p-4 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-slate-100 dark:bg-slate-700 rounded-lg">
                        <ActionIcon className="h-4 w-4 text-slate-600 dark:text-slate-400" />
                      </div>
                      <div className="space-y-1">
                        <div className="font-medium text-sm text-slate-900 dark:text-slate-100">
                          {action.label}
                        </div>
                        <div className="text-xs text-slate-500 dark:text-slate-400">
                          {action.description}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};
