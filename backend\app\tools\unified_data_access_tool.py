"""
Unified Data Access Tool for Datagenius.
Provides consistent data access across all agents and dashboard widgets.
Supports multiple data source types with caching and error handling.
"""

import logging
import asyncio
import pandas as pd
from typing import Dict, Any, List, Optional, Union, Literal
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text
from pydantic import BaseModel, Field, ConfigDict
import json
import yaml
from pathlib import Path

from ..database import get_db
from ..models.dashboard_customization import DataSourceConfig
from ..services.data_integration_service import DataIntegrationService

logger = logging.getLogger(__name__)


class DataQuery(BaseModel):
    """Data query configuration."""
    model_config = ConfigDict(extra='forbid')
    
    source_id: str = Field(..., description="Data source identifier")
    query_type: Literal["sql", "filter", "aggregate", "search"] = Field(..., description="Type of query")
    query: str = Field(..., description="Query string or filter expression")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Query parameters")
    limit: Optional[int] = Field(None, ge=1, le=10000, description="Result limit")
    offset: Optional[int] = Field(None, ge=0, description="Result offset")
    cache_ttl: int = Field(300, ge=0, le=3600, description="Cache TTL in seconds")


class DataResult(BaseModel):
    """Data query result."""
    model_config = ConfigDict(extra='forbid')
    
    success: bool = Field(..., description="Query success status")
    data: Optional[List[Dict[str, Any]]] = Field(None, description="Result data")
    columns: Optional[List[str]] = Field(None, description="Column names")
    total_rows: int = Field(0, description="Total number of rows")
    execution_time: float = Field(0.0, description="Query execution time in seconds")
    cached: bool = Field(False, description="Whether result was cached")
    error: Optional[str] = Field(None, description="Error message if failed")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class UnifiedDataAccessTool:
    """Unified tool for accessing various data sources consistently."""
    
    def __init__(self, db: Session = None):
        """Initialize the unified data access tool."""
        self.db = db or next(get_db())
        self.data_integration_service = DataIntegrationService(self.db)
        self.cache = {}  # Simple in-memory cache (could be replaced with Redis)
        self.cache_timestamps = {}
        logger.info("Unified Data Access Tool initialized")
    
    async def execute_query(self, query: DataQuery) -> DataResult:
        """Execute a data query against the specified data source."""
        start_time = datetime.now()
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(query)
            cached_result = self._get_cached_result(cache_key, query.cache_ttl)
            if cached_result:
                return cached_result
            
            # Get data source configuration
            data_source = await self._get_data_source(query.source_id)
            if not data_source:
                return DataResult(
                    success=False,
                    error=f"Data source '{query.source_id}' not found",
                    execution_time=(datetime.now() - start_time).total_seconds()
                )
            
            # Execute query based on data source type
            result = await self._execute_query_by_type(data_source, query)
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            result.execution_time = execution_time
            
            # Cache successful results
            if result.success and query.cache_ttl > 0:
                self._cache_result(cache_key, result)
            
            logger.info(f"Query executed successfully in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return DataResult(
                success=False,
                error=str(e),
                execution_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def get_data_sources(self, user_id: int) -> List[Dict[str, Any]]:
        """Get available data sources for a user."""
        try:
            sources = await self.data_integration_service.get_user_data_sources(user_id)
            return [
                {
                    "id": source.id,
                    "name": source.name,
                    "type": source.type,
                    "description": source.description,
                    "is_active": source.is_active,
                    "sync_status": getattr(source, 'sync_status', 'unknown'),
                    "last_sync": getattr(source, 'last_sync', None)
                }
                for source in sources
            ]
        except Exception as e:
            logger.error(f"Error getting data sources: {e}")
            return []
    
    async def test_connection(self, source_id: str) -> Dict[str, Any]:
        """Test connection to a data source."""
        try:
            data_source = await self._get_data_source(source_id)
            if not data_source:
                return {"success": False, "error": "Data source not found"}
            
            # Simple test query
            test_query = DataQuery(
                source_id=source_id,
                query_type="sql" if data_source.type == "database" else "filter",
                query="SELECT 1" if data_source.type == "database" else "{}",
                limit=1,
                cache_ttl=0  # Don't cache test queries
            )
            
            result = await self.execute_query(test_query)
            return {
                "success": result.success,
                "error": result.error,
                "response_time": result.execution_time
            }
            
        except Exception as e:
            logger.error(f"Error testing connection: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_schema_info(self, source_id: str) -> Dict[str, Any]:
        """Get schema information for a data source."""
        try:
            data_source = await self._get_data_source(source_id)
            if not data_source:
                return {"success": False, "error": "Data source not found"}
            
            if data_source.type == "file":
                return await self._get_file_schema(data_source)
            elif data_source.type == "database":
                return await self._get_database_schema(data_source)
            else:
                return {"success": False, "error": f"Schema info not supported for type: {data_source.type}"}
                
        except Exception as e:
            logger.error(f"Error getting schema info: {e}")
            return {"success": False, "error": str(e)}
    
    async def _get_data_source(self, source_id: str):
        """Get data source by ID."""
        try:
            from ..database import DataSource
            return self.db.query(DataSource).filter(DataSource.id == source_id).first()
        except Exception as e:
            logger.error(f"Error getting data source: {e}")
            return None
    
    async def _execute_query_by_type(self, data_source, query: DataQuery) -> DataResult:
        """Execute query based on data source type."""
        if data_source.type == "file":
            return await self._execute_file_query(data_source, query)
        elif data_source.type == "database":
            return await self._execute_database_query(data_source, query)
        elif data_source.type == "api":
            return await self._execute_api_query(data_source, query)
        elif data_source.type == "mcp":
            return await self._execute_mcp_query(data_source, query)
        else:
            return DataResult(
                success=False,
                error=f"Unsupported data source type: {data_source.type}"
            )
    
    async def _execute_file_query(self, data_source, query: DataQuery) -> DataResult:
        """Execute query against file data source."""
        try:
            # Get file data using data integration service
            file_data = await self.data_integration_service.get_file_data(data_source.id)
            
            if not file_data:
                return DataResult(success=False, error="No data found in file")
            
            # Convert to DataFrame for easier querying
            df = pd.DataFrame(file_data)
            
            # Apply query based on type
            if query.query_type == "filter":
                df = self._apply_filter(df, query.query, query.parameters)
            elif query.query_type == "aggregate":
                df = self._apply_aggregation(df, query.query, query.parameters)
            elif query.query_type == "search":
                df = self._apply_search(df, query.query, query.parameters)
            
            # Apply limit and offset
            total_rows = len(df)
            if query.offset:
                df = df.iloc[query.offset:]
            if query.limit:
                df = df.head(query.limit)
            
            return DataResult(
                success=True,
                data=df.to_dict('records'),
                columns=df.columns.tolist(),
                total_rows=total_rows
            )
            
        except Exception as e:
            logger.error(f"Error executing file query: {e}")
            return DataResult(success=False, error=str(e))
    
    async def _execute_database_query(self, data_source, query: DataQuery) -> DataResult:
        """Execute query against database data source."""
        try:
            # This would require database connection setup
            # For now, return a placeholder
            return DataResult(
                success=False,
                error="Database queries not yet implemented"
            )
        except Exception as e:
            logger.error(f"Error executing database query: {e}")
            return DataResult(success=False, error=str(e))
    
    async def _execute_api_query(self, data_source, query: DataQuery) -> DataResult:
        """Execute query against API data source."""
        try:
            # This would require API client setup
            # For now, return a placeholder
            return DataResult(
                success=False,
                error="API queries not yet implemented"
            )
        except Exception as e:
            logger.error(f"Error executing API query: {e}")
            return DataResult(success=False, error=str(e))
    
    async def _execute_mcp_query(self, data_source, query: DataQuery) -> DataResult:
        """Execute query against MCP data source."""
        try:
            # This would integrate with MCP tools
            # For now, return a placeholder
            return DataResult(
                success=False,
                error="MCP queries not yet implemented"
            )
        except Exception as e:
            logger.error(f"Error executing MCP query: {e}")
            return DataResult(success=False, error=str(e))

    def _apply_filter(self, df: pd.DataFrame, filter_expr: str, parameters: Optional[Dict[str, Any]]) -> pd.DataFrame:
        """Apply filter expression to DataFrame."""
        try:
            if not filter_expr or filter_expr == "{}":
                return df

            # Parse filter expression (simplified JSON-based filtering)
            if filter_expr.startswith('{'):
                filters = json.loads(filter_expr)
                for column, condition in filters.items():
                    if column in df.columns:
                        if isinstance(condition, dict):
                            # Handle complex conditions like {"gt": 100, "lt": 200}
                            for op, value in condition.items():
                                if op == "gt":
                                    df = df[df[column] > value]
                                elif op == "lt":
                                    df = df[df[column] < value]
                                elif op == "eq":
                                    df = df[df[column] == value]
                                elif op == "contains":
                                    df = df[df[column].astype(str).str.contains(str(value), na=False)]
                        else:
                            # Simple equality filter
                            df = df[df[column] == condition]

            return df
        except Exception as e:
            logger.warning(f"Error applying filter: {e}")
            return df

    def _apply_aggregation(self, df: pd.DataFrame, agg_expr: str, parameters: Optional[Dict[str, Any]]) -> pd.DataFrame:
        """Apply aggregation to DataFrame."""
        try:
            if not agg_expr:
                return df

            # Parse aggregation expression
            agg_config = json.loads(agg_expr)
            group_by = agg_config.get('group_by', [])
            aggregations = agg_config.get('aggregations', {})

            if group_by and aggregations:
                result = df.groupby(group_by).agg(aggregations).reset_index()
                return result

            return df
        except Exception as e:
            logger.warning(f"Error applying aggregation: {e}")
            return df

    def _apply_search(self, df: pd.DataFrame, search_term: str, parameters: Optional[Dict[str, Any]]) -> pd.DataFrame:
        """Apply search to DataFrame."""
        try:
            if not search_term:
                return df

            # Search across all string columns
            mask = pd.Series([False] * len(df))
            for column in df.select_dtypes(include=['object']).columns:
                mask |= df[column].astype(str).str.contains(search_term, case=False, na=False)

            return df[mask]
        except Exception as e:
            logger.warning(f"Error applying search: {e}")
            return df

    async def _get_file_schema(self, data_source) -> Dict[str, Any]:
        """Get schema information for file data source."""
        try:
            file_data = await self.data_integration_service.get_file_data(data_source.id)
            if not file_data:
                return {"success": False, "error": "No data found"}

            df = pd.DataFrame(file_data)
            schema = {
                "success": True,
                "columns": [
                    {
                        "name": col,
                        "type": str(df[col].dtype),
                        "nullable": df[col].isnull().any(),
                        "unique_values": df[col].nunique() if df[col].nunique() < 100 else None
                    }
                    for col in df.columns
                ],
                "row_count": len(df),
                "memory_usage": df.memory_usage(deep=True).sum()
            }
            return schema
        except Exception as e:
            logger.error(f"Error getting file schema: {e}")
            return {"success": False, "error": str(e)}

    async def _get_database_schema(self, data_source) -> Dict[str, Any]:
        """Get schema information for database data source."""
        try:
            # Placeholder for database schema introspection
            return {
                "success": False,
                "error": "Database schema introspection not yet implemented"
            }
        except Exception as e:
            logger.error(f"Error getting database schema: {e}")
            return {"success": False, "error": str(e)}

    def _generate_cache_key(self, query: DataQuery) -> str:
        """Generate cache key for query."""
        key_data = {
            "source_id": query.source_id,
            "query_type": query.query_type,
            "query": query.query,
            "parameters": query.parameters,
            "limit": query.limit,
            "offset": query.offset
        }
        return f"query_{hash(json.dumps(key_data, sort_keys=True))}"

    def _get_cached_result(self, cache_key: str, ttl: int) -> Optional[DataResult]:
        """Get cached result if still valid."""
        if cache_key not in self.cache or cache_key not in self.cache_timestamps:
            return None

        cache_time = self.cache_timestamps[cache_key]
        if datetime.now() - cache_time > timedelta(seconds=ttl):
            # Cache expired
            del self.cache[cache_key]
            del self.cache_timestamps[cache_key]
            return None

        result = self.cache[cache_key]
        result.cached = True
        return result

    def _cache_result(self, cache_key: str, result: DataResult) -> None:
        """Cache query result."""
        self.cache[cache_key] = result
        self.cache_timestamps[cache_key] = datetime.now()

        # Simple cache cleanup (keep only last 100 entries)
        if len(self.cache) > 100:
            oldest_key = min(self.cache_timestamps.keys(), key=lambda k: self.cache_timestamps[k])
            del self.cache[oldest_key]
            del self.cache_timestamps[oldest_key]

    def clear_cache(self) -> None:
        """Clear all cached results."""
        self.cache.clear()
        self.cache_timestamps.clear()
        logger.info("Data access cache cleared")

    async def get_sample_data(self, source_id: str, limit: int = 10) -> DataResult:
        """Get sample data from a data source."""
        sample_query = DataQuery(
            source_id=source_id,
            query_type="filter",
            query="{}",  # No filter, just get raw data
            limit=limit,
            cache_ttl=60  # Cache samples for 1 minute
        )
        return await self.execute_query(sample_query)

    async def get_data_summary(self, source_id: str) -> Dict[str, Any]:
        """Get summary statistics for a data source."""
        try:
            # Get schema info
            schema_info = await self.get_schema_info(source_id)
            if not schema_info.get("success"):
                return schema_info

            # Get sample data for statistics
            sample_result = await self.get_sample_data(source_id, limit=1000)
            if not sample_result.success:
                return {"success": False, "error": "Could not get sample data"}

            # Calculate basic statistics
            df = pd.DataFrame(sample_result.data)
            numeric_columns = df.select_dtypes(include=['number']).columns

            summary = {
                "success": True,
                "total_rows": sample_result.total_rows,
                "columns": len(df.columns),
                "numeric_columns": len(numeric_columns),
                "statistics": {}
            }

            # Add statistics for numeric columns
            for col in numeric_columns:
                summary["statistics"][col] = {
                    "mean": float(df[col].mean()),
                    "median": float(df[col].median()),
                    "std": float(df[col].std()),
                    "min": float(df[col].min()),
                    "max": float(df[col].max()),
                    "null_count": int(df[col].isnull().sum())
                }

            return summary

        except Exception as e:
            logger.error(f"Error getting data summary: {e}")
            return {"success": False, "error": str(e)}
