
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Clock, 
  Calendar, 
  Mail, 
  Edit, 
  Trash, 
  Download, 
  PlusCircle 
} from "lucide-react";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

interface ScheduledReport {
  id: string;
  name: string;
  frequency: string;
  lastRun: string;
  nextRun: string;
  recipients: string[];
  status: "active" | "paused";
}

export function ScheduledReports() {
  const { toast } = useToast();
  const [isDialogO<PERSON>, setIsDialogO<PERSON>] = useState(false);
  const [reportName, setReportName] = useState("");
  const [frequency, setFrequency] = useState("weekly");
  const [recipients, setRecipients] = useState("");
  
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([
    {
      id: "1",
      name: "Weekly Sales Summary",
      frequency: "Weekly",
      lastRun: "May 24, 2023",
      nextRun: "May 31, 2023",
      recipients: ["<EMAIL>", "<EMAIL>"],
      status: "active",
    },
    {
      id: "2",
      name: "Monthly Performance Review",
      frequency: "Monthly",
      lastRun: "Apr 30, 2023",
      nextRun: "May 31, 2023",
      recipients: ["<EMAIL>"],
      status: "active",
    },
    {
      id: "3",
      name: "Quarterly Financial Report",
      frequency: "Quarterly",
      lastRun: "Mar 31, 2023",
      nextRun: "Jun 30, 2023",
      recipients: ["<EMAIL>", "<EMAIL>"],
      status: "active",
    },
  ]);

  const handleAddSchedule = () => {
    if (!reportName) {
      toast({
        title: "Error",
        description: "Please enter a report name",
        variant: "destructive",
      });
      return;
    }
    
    if (!recipients) {
      toast({
        title: "Error",
        description: "Please enter at least one recipient",
        variant: "destructive",
      });
      return;
    }
    
    const recipientsList = recipients.split(",").map(email => email.trim());
    
    const newReport: ScheduledReport = {
      id: (scheduledReports.length + 1).toString(),
      name: reportName,
      frequency: frequency.charAt(0).toUpperCase() + frequency.slice(1),
      lastRun: "Never",
      nextRun: "Jun 7, 2023",
      recipients: recipientsList,
      status: "active",
    };
    
    setScheduledReports([...scheduledReports, newReport]);
    setIsDialogOpen(false);
    setReportName("");
    setFrequency("weekly");
    setRecipients("");
    
    toast({
      title: "Schedule Created",
      description: "Your report has been scheduled successfully",
    });
  };
  
  const handleDeleteReport = (id: string) => {
    setScheduledReports(scheduledReports.filter(report => report.id !== id));
    toast({
      title: "Schedule Removed",
      description: "The scheduled report has been removed",
    });
  };
  
  const handleToggleStatus = (id: string) => {
    setScheduledReports(
      scheduledReports.map(report => 
        report.id === id 
          ? { ...report, status: report.status === "active" ? "paused" : "active" } 
          : report
      )
    );
    
    const report = scheduledReports.find(r => r.id === id);
    toast({
      title: report?.status === "active" ? "Schedule Paused" : "Schedule Activated",
      description: `The scheduled report has been ${report?.status === "active" ? "paused" : "activated"}`,
    });
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Scheduled Reports</h2>
          <p className="text-sm text-muted-foreground">
            Manage your automated report schedules
          </p>
        </div>
        <Button onClick={() => setIsDialogOpen(true)} className="gap-2">
          <PlusCircle className="h-4 w-4" />
          Schedule New Report
        </Button>
      </div>
      
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Report Name</TableHead>
                <TableHead>Frequency</TableHead>
                <TableHead>Last Run</TableHead>
                <TableHead>Next Run</TableHead>
                <TableHead>Recipients</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {scheduledReports.map((report) => (
                <TableRow key={report.id}>
                  <TableCell className="font-medium">{report.name}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="h-3 w-3 mr-2 text-muted-foreground" />
                      {report.frequency}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-2 text-muted-foreground" />
                      {report.lastRun}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-2 text-muted-foreground" />
                      {report.nextRun}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Mail className="h-3 w-3 mr-2 text-muted-foreground" />
                      {report.recipients.length} recipient{report.recipients.length !== 1 && 's'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      report.status === "active" 
                        ? "bg-green-100 text-green-800" 
                        : "bg-yellow-100 text-yellow-800"
                    }`}>
                      {report.status === "active" ? "Active" : "Paused"}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleToggleStatus(report.id)}
                        title={report.status === "active" ? "Pause" : "Activate"}
                      >
                        {report.status === "active" ? 
                          <span className="text-yellow-500">⏸</span> : 
                          <span className="text-green-500">▶</span>}
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        title="Edit"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        title="Download Latest"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteReport(report.id)}
                        className="text-destructive hover:text-destructive/80"
                        title="Delete"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Schedule New Report</DialogTitle>
            <DialogDescription>
              Set up a new automated report schedule. The report will be generated and sent automatically according to your settings.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="name" className="text-sm font-medium">
                Report Name
              </label>
              <Input
                id="name"
                placeholder="Weekly Sales Report"
                value={reportName}
                onChange={(e) => setReportName(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="frequency" className="text-sm font-medium">
                Frequency
              </label>
              <Select value={frequency} onValueChange={setFrequency}>
                <SelectTrigger>
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="recipients" className="text-sm font-medium">
                Recipients (comma separated)
              </label>
              <Input
                id="recipients"
                placeholder="<EMAIL>, <EMAIL>"
                value={recipients}
                onChange={(e) => setRecipients(e.target.value)}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddSchedule}>Create Schedule</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
