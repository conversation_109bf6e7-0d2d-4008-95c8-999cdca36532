# Embedding Models Configuration for Datagenius
# This file contains configuration for Hugging Face embedding models only

embedding_providers:
  huggingface:
    name: "Hugging Face"
    description: "Open-source embedding models from Hugging Face"
    requires_api_key: false
    models:
      - id: "BAAI/bge-small-en-v1.5"
        name: "BGE Small English v1.5"
        dimensions: 384
        description: "High-quality small English embedding model, good for general use"
        size: "133MB"
        languages: ["en"]
      - id: "BAAI/bge-base-en-v1.5"
        name: "BGE Base English v1.5"
        dimensions: 768
        description: "High-quality base English embedding model, better performance"
        size: "438MB"
        languages: ["en"]
      - id: "BAAI/bge-large-en-v1.5"
        name: "BGE Large English v1.5"
        dimensions: 1024
        description: "High-quality large English embedding model, best performance"
        size: "1.34GB"
        languages: ["en"]
      - id: "sentence-transformers/all-MiniLM-L6-v2"
        name: "All MiniLM L6 v2"
        dimensions: 384
        description: "Fast and efficient sentence transformer, good for general use"
        size: "91MB"
        languages: ["en"]
      - id: "sentence-transformers/all-mpnet-base-v2"
        name: "All MPNet Base v2"
        dimensions: 768
        description: "High-quality sentence transformer based on MPNet"
        size: "438MB"
        languages: ["en"]
      - id: "intfloat/e5-small-v2"
        name: "E5 Small v2"
        dimensions: 384
        description: "Efficient embedding model with good performance"
        size: "134MB"
        languages: ["en"]
      - id: "intfloat/e5-base-v2"
        name: "E5 Base v2"
        dimensions: 768
        description: "Balanced embedding model with good performance"
        size: "438MB"
        languages: ["en"]
      - id: "intfloat/e5-large-v2"
        name: "E5 Large v2"
        dimensions: 1024
        description: "Large embedding model with excellent performance"
        size: "1.34GB"
        languages: ["en"]
      - id: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
        name: "Paraphrase Multilingual MiniLM L12 v2"
        dimensions: 384
        description: "Multilingual sentence transformer supporting 50+ languages"
        size: "471MB"
        languages: ["multilingual"]

# Default configurations for different use cases
default_configs:
  fast_and_free:
    provider: "huggingface"
    model: "sentence-transformers/all-MiniLM-L6-v2"
    description: "Fast, free, and efficient for most use cases"

  high_quality:
    provider: "huggingface"
    model: "BAAI/bge-base-en-v1.5"
    description: "High quality embeddings"

  best_performance:
    provider: "huggingface"
    model: "BAAI/bge-large-en-v1.5"
    description: "Best embedding model performance"

  default:
    provider: "huggingface"
    model: "BAAI/bge-small-en-v1.5"
    description: "Default high-quality embedding model"

  multilingual:
    provider: "huggingface"
    model: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    description: "Support for multiple languages"

# Recommended models for different scenarios
recommendations:
  general_purpose:
    - "BAAI/bge-base-en-v1.5"
    - "sentence-transformers/all-mpnet-base-v2"
    - "BAAI/bge-small-en-v1.5"

  cost_conscious:
    - "sentence-transformers/all-MiniLM-L6-v2"
    - "BAAI/bge-small-en-v1.5"
    - "intfloat/e5-small-v2"

  high_performance:
    - "BAAI/bge-large-en-v1.5"
    - "intfloat/e5-large-v2"
    - "BAAI/bge-base-en-v1.5"

  multilingual:
    - "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"

# Installation requirements for Hugging Face models
requirements:
  huggingface:
    packages:
      - "sentence-transformers"
      - "torch"
      - "transformers"
    optional_packages:
      - "accelerate"  # For faster model loading
      - "bitsandbytes"  # For quantization
