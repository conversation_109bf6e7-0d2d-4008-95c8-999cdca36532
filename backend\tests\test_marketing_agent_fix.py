#!/usr/bin/env python3
"""
Test script to verify the marketing agent follow-up question fix.
This script tests that follow-up questions are handled conversationally
instead of regenerating marketing strategies.
"""

import asyncio
import logging
import sys
import os
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.marketing_agent.components import MarketingParserComponent, MCPContentGeneratorComponent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockMCPServer:
    """Mock MCP server for testing intent detection."""

    async def call_tool(self, tool_name: str, params: dict):
        """Mock tool call that simulates LLM intent detection."""
        if tool_name == "handle_conversation":
            message = params.get("message", "")

            # Simulate LLM intent detection based on message content
            if "generate" in message.lower() and ("strategy" in message.lower() or "campaign" in message.lower()):
                # Content generation request
                response = {
                    "intent": "content_generation",
                    "confidence": 0.9,
                    "reasoning": "Message contains explicit content generation keywords"
                }
            elif any(phrase in message.lower() for phrase in ["what else", "suggest", "social media"]):
                # Follow-up question
                response = {
                    "intent": "follow_up_question",
                    "confidence": 0.85,
                    "reasoning": "Message appears to be asking for additional suggestions"
                }
            else:
                # Default to conversational
                response = {
                    "intent": "conversational",
                    "confidence": 0.7,
                    "reasoning": "General conversational message"
                }

            return {
                "isError": False,
                "content": [{"type": "text", "text": json.dumps(response)}]
            }

        return {"isError": True, "content": [{"type": "text", "text": "Unknown tool"}]}

async def test_follow_up_question_handling():
    """Test that follow-up questions are handled conversationally."""
    
    logger.info("=== TESTING MARKETING AGENT FOLLOW-UP QUESTION HANDLING ===")
    
    # Initialize components
    parser = MarketingParserComponent()
    content_generator = MCPContentGeneratorComponent()
    mock_mcp_server = MockMCPServer()

    # Test case 1: Initial marketing form submission (should create task)
    logger.info("\n--- TEST 1: Initial marketing form submission ---")
    initial_context = {
        "message": "Generate a marketing strategy",
        "context": {
            "marketing_form_data": {
                "content_type": "marketing_strategy",
                "brand_description": "Tech startup",
                "target_audience": "Young professionals",
                "products_services": "Mobile app",
                "marketing_goals": "Increase user acquisition",
                "existing_content": "",
                "keywords": "mobile, app, productivity",
                "suggested_topics": "app features, user benefits",
                "tone": "Professional"
            }
        },
        "conversation_history": [],
        "agent_components": [mock_mcp_server]
    }
    
    # Process through parser
    result1 = await parser.process(initial_context.copy())
    logger.info(f"Parser result - Skip content generation: {result1.get('skip_marketing_content_generation', False)}")
    logger.info(f"Parser result - Has marketing task: {'marketing_task' in result1}")
    
    # Test case 2: Follow-up question (should NOT create task)
    logger.info("\n--- TEST 2: Follow-up question after strategy generation ---")
    followup_context = {
        "message": "What else can you suggest for social media?",
        "conversation_history": [
            {
                "sender": "user",
                "content": "Generate a marketing strategy",
                "metadata": {
                    "marketing_form_data": {
                        "content_type": "marketing_strategy",
                        "brand_description": "Tech startup",
                        "target_audience": "Young professionals"
                    }
                }
            },
            {
                "sender": "ai",
                "content": "Here's your marketing strategy...",
                "metadata": {
                    "generated_content": True,
                    "task_type": "marketing_strategy"
                }
            }
        ],
        "agent_components": [mock_mcp_server]
    }
    
    # Process through parser
    result2 = await parser.process(followup_context.copy())
    logger.info(f"Parser result - Skip content generation: {result2.get('skip_marketing_content_generation', False)}")
    logger.info(f"Parser result - Is conversational: {result2.get('is_conversational', False)}")
    logger.info(f"Parser result - Is follow-up question: {result2.get('is_follow_up_question', False)}")
    logger.info(f"Parser result - Has marketing task: {'marketing_task' in result2}")
    
    if 'marketing_task' in result2:
        task = result2['marketing_task']
        logger.info(f"Marketing task type: {task.get('task_type', 'unknown')}")
    
    # Process through content generator
    result3 = await content_generator.process(result2.copy())
    logger.info(f"Content generator result - Has response: {'response' in result3}")
    logger.info(f"Content generator result - Response type: {result3.get('metadata', {}).get('conversational_response', False)}")
    
    # Test case 3: Explicit content generation request (should create task)
    logger.info("\n--- TEST 3: Explicit content generation request ---")
    explicit_context = {
        "message": "Generate a new social media campaign strategy",
        "conversation_history": [
            {
                "sender": "ai",
                "content": "Here's your previous marketing strategy...",
                "metadata": {"generated_content": True}
            }
        ],
        "agent_components": [mock_mcp_server]
    }
    
    result4 = await parser.process(explicit_context.copy())
    logger.info(f"Parser result - Skip content generation: {result4.get('skip_marketing_content_generation', False)}")
    logger.info(f"Parser result - Detected marketing request: {result4.get('detected_marketing_request', False)}")
    logger.info(f"Parser result - Has marketing task: {'marketing_task' in result4}")
    
    # Summary
    logger.info("\n=== TEST SUMMARY ===")
    logger.info(f"Test 1 (Initial form): Creates task = {'marketing_task' in result1}")
    logger.info(f"Test 2 (Follow-up): Skips generation = {result2.get('skip_marketing_content_generation', False)}")
    logger.info(f"Test 2 (Follow-up): Is conversational = {result3.get('metadata', {}).get('conversational_response', False)}")
    logger.info(f"Test 3 (Explicit): Creates task = {'marketing_task' in result4}")
    
    # Verify the fix works
    success = (
        'marketing_task' in result1 and  # Initial form should create task
        result2.get('skip_marketing_content_generation', False) and  # Follow-up should skip
        result3.get('metadata', {}).get('conversational_response', False) and  # Should be conversational
        'marketing_task' in result4  # Explicit request should create task
    )
    
    if success:
        logger.info("✅ ALL TESTS PASSED - Follow-up question fix is working correctly!")
    else:
        logger.error("❌ TESTS FAILED - Follow-up question fix needs more work")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(test_follow_up_question_handling())
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        sys.exit(1)
