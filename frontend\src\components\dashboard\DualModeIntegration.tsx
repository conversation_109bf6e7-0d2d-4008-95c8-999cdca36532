/**
 * Dual Mode Integration Component
 * 
 * Provides a lightweight integration of the dual-mode system into existing dashboard components.
 * Shows mode indicator and provides basic mode switching without full wrapper.
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Sparkles,
  Settings,
  Zap,
  Bot,
  Eye,
  EyeOff,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDashboardMode } from '@/stores/dashboard-mode-store';
import { ModeIndicator } from './mode/ModeIndicator';

interface DualModeIntegrationProps {
  className?: string;
  show_mode_toggle?: boolean;
  show_ai_assistant?: boolean;
  compact?: boolean;
}

export const DualModeIntegration: React.FC<DualModeIntegrationProps> = ({
  className,
  show_mode_toggle = true,
  show_ai_assistant = true,
  compact = false,
}) => {
  const [show_assistant, set_show_assistant] = useState(false);
  const { current_mode, toggle_mode, can_switch } = useDashboardMode();

  const handle_mode_toggle = () => {
    if (can_switch) {
      toggle_mode();
    }
  };

  if (compact) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        {show_mode_toggle && (
          <ModeIndicator variant="badge-only" />
        )}
        {show_ai_assistant && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => set_show_assistant(!show_assistant)}
            className="h-8 w-8 p-0"
          >
            <Bot className="h-4 w-4" />
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Mode Status Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                {current_mode === 'simple' ? (
                  <Sparkles className="h-5 w-5 text-blue-500" />
                ) : (
                  <Settings className="h-5 w-5 text-purple-500" />
                )}
              </div>
              <div>
                <h3 className="font-semibold">
                  {current_mode === 'simple' ? 'Simple Mode' : 'Advanced Mode'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {current_mode === 'simple' 
                    ? 'AI-guided dashboard creation with conversational interface'
                    : 'Full technical control with ribbon toolbar and customization'
                  }
                </p>
              </div>
            </div>
            
            {show_mode_toggle && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handle_mode_toggle}
                  disabled={!can_switch}
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Switch to {current_mode === 'simple' ? 'Advanced' : 'Simple'}
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* AI Assistant Toggle */}
      {show_ai_assistant && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-50 rounded-lg">
                  <Bot className="h-5 w-5 text-green-500" />
                </div>
                <div>
                  <h3 className="font-semibold">AI Assistant</h3>
                  <p className="text-sm text-muted-foreground">
                    Get help with dashboard creation and data analysis
                  </p>
                </div>
              </div>
              
              <Button
                variant={show_assistant ? "default" : "outline"}
                size="sm"
                onClick={() => set_show_assistant(!show_assistant)}
              >
                {show_assistant ? (
                  <>
                    <EyeOff className="h-4 w-4 mr-2" />
                    Hide Assistant
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    Show Assistant
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Assistant Panel - Removed: Now using only FloatingAITrigger */}
      {show_assistant && show_ai_assistant && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-700 mb-2">
            <Bot className="h-4 w-4 inline mr-2" />
            AI Assistant is available via the floating button in the bottom-right corner.
          </p>
          <p className="text-xs text-blue-600">
            Click the AI assistant button to get help with widgets, data analysis, and dashboard optimization.
          </p>
        </div>
      )}
    </div>
  );
};

// Hook for easy integration into existing components
export const useDualModeIntegration = () => {
  const { current_mode, toggle_mode, can_switch } = useDashboardMode();
  const [ai_assistant_visible, set_ai_assistant_visible] = useState(false);

  return {
    current_mode,
    is_simple_mode: current_mode === 'simple',
    is_advanced_mode: current_mode === 'advanced',
    toggle_mode,
    can_switch_mode: can_switch,
    ai_assistant_visible,
    toggle_ai_assistant: () => set_ai_assistant_visible(!ai_assistant_visible),
    show_ai_assistant: () => set_ai_assistant_visible(true),
    hide_ai_assistant: () => set_ai_assistant_visible(false),
  };
};

// Simple mode indicator for existing headers
export const SimpleModeIndicator: React.FC<{ className?: string }> = ({ className }) => {
  const { current_mode } = useDashboardMode();
  
  return (
    <Badge 
      variant={current_mode === 'simple' ? 'default' : 'secondary'}
      className={cn("text-xs", className)}
    >
      {current_mode === 'simple' ? (
        <>
          <Sparkles className="h-3 w-3 mr-1" />
          Simple Mode
        </>
      ) : (
        <>
          <Settings className="h-3 w-3 mr-1" />
          Advanced Mode
        </>
      )}
    </Badge>
  );
};

// Quick mode toggle button for existing toolbars
export const QuickModeToggle: React.FC<{ className?: string }> = ({ className }) => {
  const { current_mode, toggle_mode, can_switch } = useDashboardMode();

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggle_mode}
      disabled={!can_switch}
      className={cn("text-xs", className)}
    >
      <Zap className="h-3 w-3 mr-1" />
      {current_mode === 'simple' ? 'Advanced' : 'Simple'}
    </Button>
  );
};

// Modern toggle switch for mode switching with on/off states
export const ModeToggleSwitch: React.FC<{
  className?: string;
  compact?: boolean;
}> = ({ className, compact = false }) => {
  const { current_mode, toggle_mode, can_switch } = useDashboardMode();

  // Advanced mode = ON (true), Simple mode = OFF (false)
  const isAdvancedMode = current_mode === 'advanced';

  if (compact) {
    return (
      <div className={cn("flex items-center space-x-2 mode-toggle-compact", className)}>
        {/* Compact Mode Label */}
        <div className="flex items-center space-x-1">
          {isAdvancedMode ? (
            <Settings className="h-3.5 w-3.5 text-slate-600" />
          ) : (
            <Sparkles className="h-3.5 w-3.5 text-slate-600" />
          )}
          <span className="text-xs font-medium text-slate-700">
            {isAdvancedMode ? 'Advanced Mode' : 'Simple'}
          </span>
        </div>

        {/* Compact Toggle Switch with Enhanced Visibility */}
        <div className="relative">
          <Switch
            checked={isAdvancedMode}
            onCheckedChange={toggle_mode}
            disabled={!can_switch}
            className="h-5 w-9 data-[state=checked]:bg-blue-600 data-[state=unchecked]:bg-slate-300"
          />
          {/* Custom thumb overlay for better visibility */}
          <div
            className={cn(
              "absolute top-0.5 left-0.5 h-4 w-4 rounded-full enhanced-switch-thumb pointer-events-none",
              isAdvancedMode
                ? "translate-x-4 checked"
                : "translate-x-0 unchecked"
            )}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      {/* Mode Label */}
      <div className="flex items-center space-x-2">
        {isAdvancedMode ? (
          <Settings className="h-4 w-4 text-slate-600" />
        ) : (
          <Sparkles className="h-4 w-4 text-slate-600" />
        )}
        <span className="text-sm font-medium text-slate-700">
          {isAdvancedMode ? 'Advanced Mode' : 'Simple'}
        </span>
      </div>

      {/* Toggle Switch with Enhanced Visibility */}
      <div className="relative">
        <Switch
          checked={isAdvancedMode}
          onCheckedChange={toggle_mode}
          disabled={!can_switch}
          className="data-[state=checked]:bg-blue-600 data-[state=unchecked]:bg-slate-300"
        />
        {/* Custom thumb overlay for better visibility */}
        <div
          className={cn(
            "absolute top-0.5 left-0.5 h-5 w-5 rounded-full enhanced-switch-thumb pointer-events-none",
            isAdvancedMode
              ? "translate-x-5 checked"
              : "translate-x-0 unchecked"
          )}
        />
      </div>
    </div>
  );
};
