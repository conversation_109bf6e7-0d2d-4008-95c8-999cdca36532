"""
PandasAI v3 query MCP tool.

This module provides an MCP-compatible tool for querying data using PandasAI v3.
It integrates with mem0ai for enhanced query capabilities.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional
import base64

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from ..chart_optimization import chart_enhancer
from ...utils.memory_service import MemoryService
from ...utils.vector_service import VectorService
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)

class PandasAIQueryTool(BaseMCPTool):
    """Tool for querying data using PandasAI v3."""

    def __init__(self):
        """Initialize the PandasAI query tool."""
        super().__init__(
            name="pandasai_query",
            description="Query data using natural language with PandasAI v3 and mem0ai integration",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "data_source": {
                        "type": ["object", "string"],
                        "description": "Data source information (can be an object with id/name or a string identifier)"
                    },
                    "query": {"type": "string"},
                    "api_key": {"type": "string"},
                    "provider": {"type": "string", "default": "openai"},
                    "model": {"type": "string"},
                    "user_id": {"type": "string"},
                    "persona_id": {"type": "string"},
                    "conversation_id": {"type": "string"},
                    "store_in_memory": {"type": "boolean", "default": True}
                },
                "required": ["query"]  # Removed api_key from required to allow fallbacks
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()
        self.memory_service = MemoryService()
        self.vector_service = VectorService()

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the PandasAI query tool with agent-aware capabilities."""
        file_path = arguments.get("file_path")
        data_source = arguments.get("data_source")
        query = arguments.get("query")
        api_key = arguments.get("api_key")
        provider = arguments.get("provider", "openai")

        # Detect agent identity for personalized querying
        agent_id = arguments.get("persona_id") or arguments.get("agent_id")
        context = arguments.get("context", {})
        agent_identity = await detect_agent_identity(
            agent_id=agent_id,
            context=context,
            intent_type="data_query"
        )

        logger.info(f"Detected agent identity: {agent_identity} for PandasAI query")

        # Get API key from environment if not provided
        logger.info(f"PandasAI query tool - api_key provided: {bool(api_key)}, provider: {provider}")

        if not api_key:
            # PandasAI requires PANDASAI_API_KEY
            api_key = os.getenv("PANDASAI_API_KEY")
            logger.info(f"Retrieved PANDASAI_API_KEY from environment: {bool(api_key)}")

            if not api_key:
                logger.error("No PANDASAI_API_KEY found in environment variables")
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "No PANDASAI_API_KEY found in environment variables. Please set PANDASAI_API_KEY to use PandasAI."}]
                }

            logger.info("Using PANDASAI_API_KEY from environment")

        # If we have a data_source but no file_path, use the data_access tool to get the file
        if data_source and not file_path:
            try:
                # Import the data access tool
                from .data_access import DataAccessTool

                # Create and initialize the tool
                data_tool = DataAccessTool()
                await data_tool.initialize({})

                # Call the tool to load the data
                data_result = await data_tool.execute({
                    "data_source": data_source,
                    "operation": "load",
                    "params": {"create_sample": True}
                })

                # Check if we got a valid result
                if not data_result.get("isError", False) and "metadata" in data_result:
                    # Extract the file path from the result
                    file_path = data_result["metadata"].get("file_path")
                    logger.info(f"Retrieved file path from data_access tool: {file_path}")
                else:
                    logger.error(f"Error retrieving file path from data_access tool: {data_result}")
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": "Could not access the data source. Please provide a valid file path or data source."}]
                    }
            except Exception as e:
                logger.error(f"Error using data_access tool: {e}", exc_info=True)
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error accessing data source: {str(e)}"}]
                }

        # Ensure we have a file path
        if not file_path:
            return {
                "isError": True,
                "content": [{"type": "text", "text": "No file path or valid data source provided"}]
            }

        # Check cache first
        cached_result = self.cache.get(file_path, query, provider)
        if cached_result:
            logger.info(f"Using cached result for query: {query}")
            return cached_result

        try:
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)

            # Load dataframe
            if not self.pandasai.load_dataframe(file_path):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error loading dataframe from {file_path}"}]
                }

            # Create agent with model if provided
            model = arguments.get("model")
            if not self.pandasai.create_agent(model=model):
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "Error creating PandasAI Agent"}]
                }

            # Enhance query with agent-specific query style
            enhanced_query = await self._enhance_query_with_agent_style(query, agent_identity)

            # Chat with agent using enhanced query
            result = self.pandasai.chat(enhanced_query)

            # Handle error
            if "error" in result:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error in PandasAI query: {result['error']}"}]
                }

            # Format response based on result type
            response = self._format_response(result, query, provider)

            # Cache the response
            self.cache.set(file_path, query, provider, response)

            # Store the query results in memory if requested
            store_in_memory = arguments.get("store_in_memory", True)
            if store_in_memory:
                user_id = arguments.get("user_id", "system")
                persona_id = arguments.get("persona_id", "unknown")
                conversation_id = arguments.get("conversation_id", "unknown")

                # Create metadata for the memory
                metadata = {
                    "type": "query",
                    "file_path": file_path,
                    "query": query,
                    "provider": provider,
                    "persona_id": persona_id,
                    "conversation_id": conversation_id,
                    "query_type": "pandasai"
                }

                # Format the content for memory storage
                if "content" in response:
                    content_text = ""
                    for content_item in response["content"]:
                        if content_item.get("type") == "text":
                            content_text += content_item.get("text", "") + "\n\n"

                    # Store in memory
                    if content_text:
                        try:
                            self.memory_service.add_memory(
                                content=f"Query on {os.path.basename(file_path)}: {query}\n\n{content_text}",
                                user_id=user_id,
                                metadata=metadata
                            )
                            logger.info(f"Stored query results in memory for user {user_id}")
                        except Exception as e:
                            logger.error(f"Error storing query results in memory: {e}")

            return response

        except Exception as e:
            logger.error(f"Error executing PandasAI query tool: {e}", exc_info=True)
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Error: {str(e)}"}]
            }

    def _format_response(self, result: Dict[str, Any], query: str, provider: str = "openai") -> Dict[str, Any]:
        """Format the response for the MCP tool."""
        if result["type"] == "dataframe":
            return {
                "isError": False,
                "content": [
                    {"type": "text", "text": f"Query results for: {query}"},
                    {"type": "table", "data": result["data"], "columns": result["columns"]}
                ]
            }
        elif result["type"] == "chart":
            # Use enhanced chart processing for better performance and UX
            try:
                image_path = result["image_path"]
                logger.info(f"Processing chart with enhanced optimization: {image_path}")

                if not os.path.exists(image_path):
                    logger.error(f"Chart image file does not exist: {image_path}")
                    return {
                        "isError": True,
                        "content": [{"type": "text", "text": "The visualization was generated but the image file could not be found."}]
                    }

                # Use the enhanced chart response system
                enhanced_response = chart_enhancer.enhance_chart_response(
                    image_path=image_path,
                    query=query,
                    provider=provider,
                    optimize=True  # Enable optimization for better performance
                )

                # If enhancement succeeded, return the enhanced response
                if not enhanced_response.get("isError", False):
                    logger.info("Successfully enhanced chart response with optimization")
                    return enhanced_response
                else:
                    # Fallback to basic processing if enhancement fails
                    logger.warning("Chart enhancement failed, using basic processing")
                    with open(image_path, "rb") as image_file:
                        encoded_image = base64.b64encode(image_file.read()).decode("utf-8")

                    return {
                        "isError": False,
                        "content": [
                            {"type": "text", "text": f"📊 Visualization for query: {query}"},
                            {"type": "image", "src": f"data:image/png;base64,{encoded_image}"}
                        ]
                    }
            except Exception as e:
                logger.error(f"Error reading chart image: {e}", exc_info=True)
                return {
                    "isError": False,
                    "content": [
                        {"type": "text", "text": f"Visualization for query: {query}"},
                        {"type": "text", "text": f"Error displaying chart: {str(e)}"}
                    ]
                }
        elif result["type"] == "number":
            return {
                "isError": False,
                "content": [
                    {"type": "text", "text": f"Query result for: {query}"},
                    {"type": "text", "text": str(result["value"])}
                ]
            }
        elif result["type"] == "text":
            return {
                "isError": False,
                "content": [{"type": "text", "text": result.get("text", str(result))}]
            }
        else:
            # Handle unknown types gracefully
            logger.warning(f"Unknown result type: {result.get('type', 'unknown')}")
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Type '{result.get('type', 'unknown')}' has not been implemented. Result: {str(result)}"}]
            }

    async def _enhance_query_with_agent_style(self, query: str, agent_identity: str) -> str:
        """Enhance the query with agent-specific query style and preferences."""
        try:
            # Get agent system prompt to extract query preferences
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract query style preferences from system prompt
            query_style = await self._extract_query_style_from_prompt(system_prompt, agent_identity)

            # Enhance query based on agent style
            enhanced_query = self._apply_agent_style_to_query(query, query_style, agent_identity)

            logger.info(f"Enhanced query for {agent_identity}: {enhanced_query[:100]}...")
            return enhanced_query

        except Exception as e:
            logger.warning(f"Failed to enhance query with agent style: {e}")
            return query  # Return original query if enhancement fails

    async def _extract_query_style_from_prompt(self, system_prompt: str, agent_identity: str) -> Dict[str, Any]:
        """Extract query style preferences from agent system prompt."""
        style_preferences = {
            "output_format": "detailed",
            "focus_areas": [],
            "data_presentation": "structured",
            "context_level": "moderate"
        }

        if not system_prompt:
            return self._get_default_query_style_for_agent(agent_identity)

        # Look for query-related patterns in the system prompt
        import re

        # Check for output format preferences
        if re.search(r"summary|brief|concise", system_prompt, re.IGNORECASE):
            style_preferences["output_format"] = "summary"
        elif re.search(r"detailed|comprehensive|thorough", system_prompt, re.IGNORECASE):
            style_preferences["output_format"] = "detailed"

        # Check for data presentation preferences
        if re.search(r"visual|chart|graph|plot", system_prompt, re.IGNORECASE):
            style_preferences["data_presentation"] = "visual"
        elif re.search(r"table|structured|organized", system_prompt, re.IGNORECASE):
            style_preferences["data_presentation"] = "structured"

        # Extract focus areas based on capabilities
        focus_areas = []
        if re.search(r"marketing|campaign|strategy", system_prompt, re.IGNORECASE):
            focus_areas.append("business_metrics")
        if re.search(r"statistical|analysis|insights", system_prompt, re.IGNORECASE):
            focus_areas.append("statistical_insights")
        if re.search(r"classification|categorization", system_prompt, re.IGNORECASE):
            focus_areas.append("data_organization")

        style_preferences["focus_areas"] = focus_areas

        return style_preferences

    def _get_default_query_style_for_agent(self, agent_identity: str) -> Dict[str, Any]:
        """Get default query style for specific agent types."""
        default_styles = {
            "analyst": {
                "output_format": "detailed",
                "focus_areas": ["statistical_insights", "data_patterns"],
                "data_presentation": "structured",
                "context_level": "advanced"
            },
            "marketer": {
                "output_format": "business_focused",
                "focus_areas": ["business_metrics", "performance_indicators"],
                "data_presentation": "visual",
                "context_level": "moderate"
            },
            "classifier": {
                "output_format": "organized",
                "focus_areas": ["data_organization", "categorization"],
                "data_presentation": "structured",
                "context_level": "moderate"
            },
            "concierge": {
                "output_format": "accessible",
                "focus_areas": ["key_insights"],
                "data_presentation": "visual",
                "context_level": "basic"
            }
        }

        return default_styles.get(agent_identity, {
            "output_format": "detailed",
            "focus_areas": ["general_insights"],
            "data_presentation": "structured",
            "context_level": "moderate"
        })

    def _apply_agent_style_to_query(self, query: str, style: Dict[str, Any], agent_identity: str) -> str:
        """Apply agent-specific style to the query."""
        enhanced_query = query

        # Add style-specific instructions based on agent identity
        style_instructions = []

        # Add output format instructions
        output_format = style.get("output_format", "detailed")
        if output_format == "business_focused":
            style_instructions.append("Focus on business implications and actionable insights.")
        elif output_format == "summary":
            style_instructions.append("Provide a concise summary of the key findings.")
        elif output_format == "accessible":
            style_instructions.append("Present results in an easy-to-understand format.")
        elif output_format == "organized":
            style_instructions.append("Organize results in a clear, structured manner.")

        # Add focus area instructions
        focus_areas = style.get("focus_areas", [])
        if "business_metrics" in focus_areas:
            style_instructions.append("Include relevant business metrics and KPIs.")
        if "statistical_insights" in focus_areas:
            style_instructions.append("Provide statistical context and significance.")
        if "data_organization" in focus_areas:
            style_instructions.append("Focus on data categorization and organization patterns.")

        # Add data presentation instructions
        data_presentation = style.get("data_presentation", "structured")
        if data_presentation == "visual":
            style_instructions.append("Create appropriate visualizations when possible.")
        elif data_presentation == "structured":
            style_instructions.append("Present data in well-organized tables or lists.")

        # Combine original query with style instructions
        if style_instructions:
            enhanced_query = f"{query}\n\nQuery Style Instructions:\n" + "\n".join([f"- {instruction}" for instruction in style_instructions])

        return enhanced_query
