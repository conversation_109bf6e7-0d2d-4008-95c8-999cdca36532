import * as React from "react"
import { type DialogProps } from "@radix-ui/react-dialog"
import { Search } from "lucide-react"

import { cn } from "@/lib/utils"
import { Dialog, DialogContent } from "@/components/ui/dialog"

// Custom Command implementation to replace cmdk
interface CommandProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: string
  onValueChange?: (value: string) => void
  filter?: (value: string, search: string) => number
  shouldFilter?: boolean
  loop?: boolean
}

const Command = React.forwardRef<HTMLDivElement, CommandProps>(
  ({ className, value, onValueChange, filter, shouldFilter = true, loop, children, ...props }, ref) => {
    const [internalValue, setInternalValue] = React.useState(value || "")
    const [search, setSearch] = React.useState("")

    const contextValue = React.useMemo(() => ({
      value: value !== undefined ? value : internalValue,
      onValueChange: onValueChange || setInternalValue,
      search,
      onSearchChange: setSearch,
      filter,
      shouldFilter,
      loop
    }), [value, internalValue, onValueChange, search, filter, shouldFilter, loop])

    return (
      <CommandContext.Provider value={contextValue}>
        <div
          ref={ref}
          className={cn(
            "flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",
            className
          )}
          {...props}
        >
          {children}
        </div>
      </CommandContext.Provider>
    )
  }
)
Command.displayName = "Command"

// Command Context
interface CommandContextValue {
  value: string
  onValueChange: (value: string) => void
  search: string
  onSearchChange: (search: string) => void
  filter?: (value: string, search: string) => number
  shouldFilter: boolean
  loop?: boolean
}

const CommandContext = React.createContext<CommandContextValue | null>(null)

const useCommand = () => {
  const context = React.useContext(CommandContext)
  if (!context) {
    throw new Error("useCommand must be used within a Command component")
  }
  return context
}

interface CommandDialogProps extends DialogProps {}

const CommandDialog = ({ children, ...props }: CommandDialogProps) => {
  return (
    <Dialog {...props}>
      <DialogContent className="overflow-hidden p-0 shadow-lg">
        <Command className="[&_[data-command-group-heading]]:px-2 [&_[data-command-group-heading]]:font-medium [&_[data-command-group-heading]]:text-muted-foreground [&_[data-command-group]:not([hidden])_~[data-command-group]]:pt-0 [&_[data-command-group]]:px-2 [&_[data-command-input-wrapper]_svg]:h-5 [&_[data-command-input-wrapper]_svg]:w-5 [&_[data-command-input]]:h-12 [&_[data-command-item]]:px-2 [&_[data-command-item]]:py-3 [&_[data-command-item]_svg]:h-5 [&_[data-command-item]_svg]:w-5">
          {children}
        </Command>
      </DialogContent>
    </Dialog>
  )
}

interface CommandInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onValueChange?: (value: string) => void
}

const CommandInput = React.forwardRef<HTMLInputElement, CommandInputProps>(
  ({ className, onValueChange, onChange, ...props }, ref) => {
    const command = useCommand()

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value
      command.onSearchChange(value)
      onValueChange?.(value)
      onChange?.(e)
    }

    return (
      <div className="flex items-center border-b px-3" data-command-input-wrapper="">
        <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
        <input
          ref={ref}
          className={cn(
            "flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
            className
          )}
          value={command.search}
          onChange={handleChange}
          data-command-input=""
          {...props}
        />
      </div>
    )
  }
)

CommandInput.displayName = "CommandInput"

const CommandList = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("max-h-[300px] overflow-y-auto overflow-x-hidden", className)}
        data-command-list=""
        role="listbox"
        {...props}
      >
        {children}
      </div>
    )
  }
)

CommandList.displayName = "CommandList"

const CommandEmpty = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("py-6 text-center text-sm", className)}
        data-command-empty=""
        {...props}
      />
    )
  }
)

CommandEmpty.displayName = "CommandEmpty"

interface CommandGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  heading?: React.ReactNode
}

const CommandGroup = React.forwardRef<HTMLDivElement, CommandGroupProps>(
  ({ className, heading, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "overflow-hidden p-1 text-foreground [&_[data-command-group-heading]]:px-2 [&_[data-command-group-heading]]:py-1.5 [&_[data-command-group-heading]]:text-xs [&_[data-command-group-heading]]:font-medium [&_[data-command-group-heading]]:text-muted-foreground",
          className
        )}
        data-command-group=""
        role="group"
        {...props}
      >
        {heading && (
          <div
            data-command-group-heading=""
            className="px-2 py-1.5 text-xs font-medium text-muted-foreground"
          >
            {heading}
          </div>
        )}
        {children}
      </div>
    )
  }
)

CommandGroup.displayName = "CommandGroup"

const CommandSeparator = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("-mx-1 h-px bg-border", className)}
        data-command-separator=""
        role="separator"
        {...props}
      />
    )
  }
)

CommandSeparator.displayName = "CommandSeparator"

interface CommandItemProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: string
  onSelect?: (value: string) => void
  disabled?: boolean
}

const CommandItem = React.forwardRef<HTMLDivElement, CommandItemProps>(
  ({ className, value, onSelect, disabled, children, onClick, ...props }, ref) => {
    const command = useCommand()
    const [selected, setSelected] = React.useState(false)

    const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
      if (disabled) return

      const itemValue = value || (typeof children === 'string' ? children : '')
      onSelect?.(itemValue)
      command.onValueChange(itemValue)
      onClick?.(e)
    }

    const handleMouseEnter = () => {
      if (!disabled) {
        setSelected(true)
      }
    }

    const handleMouseLeave = () => {
      setSelected(false)
    }

    return (
      <div
        ref={ref}
        className={cn(
          "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none",
          selected && "bg-accent text-accent-foreground",
          disabled && "pointer-events-none opacity-50",
          className
        )}
        data-command-item=""
        data-disabled={disabled}
        data-selected={selected}
        role="option"
        aria-selected={selected}
        aria-disabled={disabled}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        {...props}
      >
        {children}
      </div>
    )
  }
)

CommandItem.displayName = "CommandItem"

const CommandShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn(
        "ml-auto text-xs tracking-widest text-muted-foreground",
        className
      )}
      {...props}
    />
  )
}
CommandShortcut.displayName = "CommandShortcut"

export {
  Command,
  CommandDialog,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandShortcut,
  CommandSeparator,
}
