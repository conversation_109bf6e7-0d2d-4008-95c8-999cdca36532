"""
Tests for Crawl4AI integration in the business profile autofill feature.

This module contains tests to verify the Crawl4AI integration works correctly
and maintains backward compatibility with the existing system.
"""

import pytest
import asyncio
import os
from unittest.mock import Mock, patch, AsyncMock

# Test configuration
os.environ["CRAWL4AI_ENABLED"] = "true"
os.environ["CRAWL4AI_TIMEOUT"] = "10"
os.environ["CRAWL4AI_MAX_RETRIES"] = "1"

from app.services.enhanced_web_scraping_service import EnhancedWebScrapingService
from app.services.crawl4ai_web_service import Crawl4AIWebService, ExtractionResult
from app.config.crawl4ai_config import crawl4ai_config


class TestCrawl4AIConfiguration:
    """Test Crawl4AI configuration loading."""
    
    def test_config_loading(self):
        """Test that configuration loads correctly from environment."""
        assert crawl4ai_config.enabled == True
        assert crawl4ai_config.timeout_seconds == 10
        assert crawl4ai_config.max_retries == 1
        assert crawl4ai_config.min_confidence_score == 0.3
    
    def test_browser_args(self):
        """Test browser arguments configuration."""
        from app.config.crawl4ai_config import get_browser_args
        args = get_browser_args()
        assert "--no-sandbox" in args
        assert "--disable-dev-shm-usage" in args
        assert "--headless" not in args  # Should be handled by BrowserConfig


class TestCrawl4AIWebService:
    """Test the Crawl4AI web service implementation."""
    
    @pytest.fixture
    def service(self):
        """Create a Crawl4AI web service instance."""
        return Crawl4AIWebService()
    
    def test_service_initialization(self, service):
        """Test service initializes correctly."""
        assert service.fallback_service is not None
        assert service.browser_config is not None
    
    def test_confidence_score_calculation(self, service):
        """Test confidence score calculation logic."""
        # Mock crawl result
        mock_result = Mock()
        mock_result.metadata = {"og:title": "Test Company"}
        mock_result.markdown = "A" * 600  # Good content length
        
        # Test with good data
        good_data = {
            "business_name": "Test Company",
            "description": "A comprehensive business description",
            "industry": "Technology",
            "contact_email": "<EMAIL>"
        }
        
        score = service._calculate_confidence_score(good_data, mock_result)
        assert score > 0.5
        
        # Test with minimal data
        minimal_data = {"business_name": "Test"}
        score = service._calculate_confidence_score(minimal_data, mock_result)
        assert score < 0.5
    
    def test_quality_assessment(self, service):
        """Test result quality assessment."""
        # High quality result
        high_quality = ExtractionResult(
            data={"business_name": "Test Company", "description": "Good description"},
            confidence_score=0.8,
            extraction_method="crawl4ai_llm",
            processing_time=2.0
        )
        assert service._is_high_quality_result(high_quality) == True
        
        # Low quality result
        low_quality = ExtractionResult(
            data={"business_name": "Test"},
            confidence_score=0.1,
            extraction_method="crawl4ai_llm",
            processing_time=2.0
        )
        assert service._is_high_quality_result(low_quality) == False
        
        # Result with error
        error_result = ExtractionResult(
            data={},
            confidence_score=0.0,
            extraction_method="crawl4ai_llm",
            processing_time=2.0,
            error_message="Test error"
        )
        assert service._is_high_quality_result(error_result) == False
    
    def test_data_transformation(self, service):
        """Test data transformation from Crawl4AI format."""
        mock_result = Mock()
        mock_result.metadata = {"og:title": "Test"}
        mock_result.links = Mock()
        mock_result.links.external = [
            Mock(href="https://facebook.com/testcompany"),
            Mock(href="https://twitter.com/testcompany")
        ]
        mock_result.markdown = "Test content"
        
        extracted_data = {
            "business_name": "Test Company",
            "contact_email": "mailto:<EMAIL>",
            "contact_phone": "tel:+1234567890"
        }
        
        transformed = service._transform_crawl4ai_data(
            extracted_data, mock_result, "https://example.com"
        )
        
        assert transformed["source_url"] == "https://example.com"
        assert transformed["business_name"] == "Test Company"
        assert transformed["contact_email"] == "<EMAIL>"  # mailto: removed
        assert transformed["contact_phone"] == "+1234567890"  # tel: removed
        assert len(transformed["social_links"]) == 2


class TestEnhancedWebScrapingService:
    """Test the enhanced web scraping service."""
    
    @pytest.fixture
    def service(self):
        """Create an enhanced web scraping service instance."""
        return EnhancedWebScrapingService()
    
    def test_service_initialization(self, service):
        """Test service initializes correctly."""
        assert service.crawl4ai_service is not None
        assert service.legacy_service is not None
    
    @pytest.mark.asyncio
    async def test_context_manager(self, service):
        """Test async context manager functionality."""
        async with service as s:
            assert s is service
            # Should have initialized both services
    
    @pytest.mark.asyncio
    @patch('app.services.crawl4ai_web_service.Crawl4AIWebService.extract_business_info')
    async def test_successful_extraction(self, mock_extract, service):
        """Test successful business info extraction."""
        # Mock successful Crawl4AI extraction
        mock_extract.return_value = {
            "source_url": "https://example.com",
            "business_name": "Test Company",
            "description": "Test description",
            "extraction_method": "crawl4ai_enhanced"
        }
        
        result = await service.extract_business_info("https://example.com")
        
        assert result["business_name"] == "Test Company"
        assert result["extraction_method"] == "crawl4ai_enhanced"
        mock_extract.assert_called_once_with("https://example.com")
    
    @pytest.mark.asyncio
    @patch('app.services.crawl4ai_web_service.Crawl4AIWebService.extract_business_info')
    async def test_extraction_error_handling(self, mock_extract, service):
        """Test error handling in extraction."""
        # Mock extraction failure
        mock_extract.side_effect = Exception("Test error")
        
        result = await service.extract_business_info("https://example.com")
        
        assert result["extraction_method"] == "error"
        assert "error_message" in result
        assert result["business_name"] == ""


class TestBackwardCompatibility:
    """Test backward compatibility with existing API."""
    
    @pytest.mark.asyncio
    async def test_api_response_format(self):
        """Test that API response format is maintained."""
        service = EnhancedWebScrapingService()
        
        # Mock the extraction to return expected format
        with patch.object(service.crawl4ai_service, 'extract_business_info') as mock_extract:
            mock_extract.return_value = {
                "source_url": "https://example.com",
                "business_name": "Test Company",
                "description": "Test description",
                "industry": "Technology",
                "contact_info": {"emails": ["<EMAIL>"], "phones": [], "addresses": []},
                "social_links": [],
                "structured_data": {},
                "meta_data": {},
                "content_summary": "Test content",
                "extraction_timestamp": 1234567890,
                "extraction_method": "crawl4ai_enhanced"
            }
            
            result = await service.extract_business_info("https://example.com")
            
            # Verify all expected fields are present
            required_fields = [
                "source_url", "business_name", "description", "industry",
                "contact_info", "social_links", "structured_data", "meta_data",
                "content_summary", "extraction_timestamp"
            ]
            
            for field in required_fields:
                assert field in result, f"Missing required field: {field}"
    
    def test_legacy_method_delegation(self):
        """Test that legacy methods are properly delegated."""
        service = EnhancedWebScrapingService()
        
        # Test that legacy methods exist and delegate properly
        assert hasattr(service, '_infer_business_name')
        assert hasattr(service, '_infer_industry')
        assert hasattr(service, '_extract_title')
        assert hasattr(service, '_extract_description')


class TestIntegrationScenarios:
    """Test integration scenarios and edge cases."""
    
    @pytest.mark.asyncio
    async def test_crawl4ai_disabled_fallback(self):
        """Test fallback when Crawl4AI is disabled."""
        with patch('app.config.crawl4ai_config.crawl4ai_config') as mock_config:
            mock_config.enabled = False
            
            service = EnhancedWebScrapingService()
            
            # Should use fallback service
            with patch.object(service.legacy_service, 'extract_business_info') as mock_fallback:
                mock_fallback.return_value = {
                    "source_url": "https://example.com",
                    "business_name": "Fallback Company",
                    "extraction_method": "fallback_webscraping"
                }
                
                result = await service.extract_business_info("https://example.com")
                assert result["business_name"] == "Fallback Company"
    
    @pytest.mark.asyncio
    async def test_quality_threshold_fallback(self):
        """Test fallback when quality is below threshold."""
        service = EnhancedWebScrapingService()
        
        # Mock low-quality Crawl4AI result
        with patch.object(service.crawl4ai_service, 'extract_business_info') as mock_crawl4ai:
            mock_crawl4ai.return_value = {
                "source_url": "https://example.com",
                "business_name": "",  # Empty name = low quality
                "description": "",
                "extraction_method": "crawl4ai_enhanced"
            }
            
            # Mock fallback service
            with patch.object(service.legacy_service, 'extract_business_info') as mock_fallback:
                mock_fallback.return_value = {
                    "source_url": "https://example.com", 
                    "business_name": "Fallback Company",
                    "extraction_method": "fallback_webscraping"
                }
                
                result = await service.extract_business_info("https://example.com")
                # Should get fallback result due to quality check in Crawl4AI service
                # The actual behavior depends on the quality assessment logic


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
