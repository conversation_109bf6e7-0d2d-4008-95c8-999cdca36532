"""
Semantic layer manager for PandasAI v3.

This module provides utilities for creating and managing semantic layers in PandasAI v3.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, List, Optional

import pandasai as pai

logger = logging.getLogger(__name__)

class SemanticLayerManager:
    """Manager for PandasAI semantic layers."""
    
    def __init__(self, base_path: str = "data/semantic_layers"):
        """Initialize the semantic layer manager."""
        self.base_path = base_path
        os.makedirs(base_path, exist_ok=True)
        
    def create_layer(self, df: pd.DataFrame, name: str, 
                    description: str, columns: Optional[List[Dict[str, Any]]] = None) -> str:
        """Create a semantic layer for a dataframe."""
        try:
            # Generate path
            path = f"datagenius/{name}"
            
            # Create semantic layer
            layer = pai.create(
                path=path,
                df=df,
                description=description,
                columns=columns
            )
            
            logger.info(f"Created semantic layer: {path}")
            return path
        except Exception as e:
            logger.error(f"Error creating semantic layer: {e}", exc_info=True)
            return ""
            
    def load_layer(self, path: str):
        """Load a semantic layer."""
        try:
            layer = pai.load(path)
            logger.info(f"Loaded semantic layer: {path}")
            return layer
        except Exception as e:
            logger.error(f"Error loading semantic layer: {e}", exc_info=True)
            return None
            
    def push_layer(self, path: str) -> bool:
        """Push a semantic layer to the platform."""
        try:
            layer = self.load_layer(path)
            if layer:
                layer.push()
                logger.info(f"Pushed semantic layer: {path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error pushing semantic layer: {e}", exc_info=True)
            return False
            
    def infer_columns(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Infer column schemas from a dataframe."""
        columns = []
        for col in df.columns:
            col_type = str(df[col].dtype)
            
            # Map pandas dtypes to semantic layer types
            if col_type.startswith('int'):
                type_name = "integer"
            elif col_type.startswith('float'):
                type_name = "float"
            elif col_type.startswith('datetime'):
                type_name = "datetime"
            elif col_type.startswith('bool'):
                type_name = "boolean"
            else:
                type_name = "string"
                
            columns.append({
                "name": col,
                "type": type_name,
                "description": f"The {col} column"
            })
            
        return columns
