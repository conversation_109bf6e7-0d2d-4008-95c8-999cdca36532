import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Download,
  Maximize2,
  Minimize2,
  RefreshCw,
  Share2,
  ImageIcon,
  BarChart3,
  PieC<PERSON>,
  LineChart,
  ScatterChart,
  TrendingUp
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface ChartMetadata {
  chart_type?: string;
  file_size?: number;
  query?: string;
  timestamp?: string;
  provider?: string;
}

interface EnhancedChartVisualizationProps {
  title?: string;
  description?: string;
  data: {
    image: string;
    metadata?: ChartMetadata;
  };
  className?: string;
  onRetry?: () => void;
}

const getChartIcon = (chartType?: string) => {
  if (!chartType) return BarChart3;

  const type = chartType.toLowerCase();
  if (type.includes('pie')) return PieChart;
  if (type.includes('line')) return LineChart;
  if (type.includes('scatter')) return ScatterChart;
  if (type.includes('trend')) return TrendingUp;
  return BarChart3;
};

const formatFileSize = (bytes?: number): string => {
  if (!bytes) return '';
  const kb = bytes / 1024;
  if (kb < 1024) return `${kb.toFixed(1)} KB`;
  const mb = kb / 1024;
  return `${mb.toFixed(1)} MB`;
};

export const EnhancedChartVisualization: React.FC<EnhancedChartVisualizationProps> = ({
  title = 'Data Visualization',
  description,
  data,
  className = '',
  onRetry
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const { toast } = useToast();

  const ChartIcon = getChartIcon(data.metadata?.chart_type);

  useEffect(() => {
    if (data.image) {
      setIsLoading(false);
      setImageError(false);
    }
  }, [data.image]);

  const handleImageLoad = () => {
    setIsLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setImageError(true);
  };

  const handleDownload = () => {
    try {
      const link = document.createElement('a');
      link.href = data.image;
      link.download = `chart-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: "Chart Downloaded",
        description: "The visualization has been saved to your downloads.",
      });
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Unable to download the chart. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: title,
          text: description || 'Data visualization',
          url: data.image
        });
      } else {
        await navigator.clipboard.writeText(data.image);
        toast({
          title: "Copied to Clipboard",
          description: "Chart data URL has been copied to your clipboard.",
        });
      }
    } catch (error) {
      toast({
        title: "Share Failed",
        description: "Unable to share the chart. Please try again.",
        variant: "destructive",
      });
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4">
        <div className="relative max-w-full max-h-full">
          <Button
            onClick={toggleFullscreen}
            className="absolute top-4 right-4 z-10 bg-white text-black hover:bg-gray-100"
            size="sm"
          >
            <Minimize2 className="h-4 w-4" />
          </Button>
          <img
            src={data.image}
            alt={title}
            className="max-w-full max-h-full object-contain"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </div>
      </div>
    );
  }

  return (
    <Card className={cn("overflow-hidden shadow-lg border-gray-200 bg-white", className)}>
      <CardHeader className="pb-3 bg-gradient-to-r from-white to-gray-50 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-brand-100 rounded-lg">
              <ChartIcon className="h-5 w-5 text-brand-600" />
            </div>
            <div>
              <CardTitle className="text-lg text-brand-700 font-semibold">
                {title}
              </CardTitle>
              {description && (
                <CardDescription className="text-gray-600 mt-1">
                  {description}
                </CardDescription>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {data.metadata?.file_size && (
              <Badge variant="secondary" className="text-xs">
                {formatFileSize(data.metadata.file_size)}
              </Badge>
            )}
            {data.metadata?.provider && (
              <Badge variant="outline" className="text-xs">
                {data.metadata.provider}
              </Badge>
            )}
          </div>
        </div>

        {data.metadata?.query && (
          <div className="mt-2 p-2 bg-gray-50 rounded-md">
            <p className="text-sm text-gray-700 font-medium">Query:</p>
            <p className="text-sm text-gray-600 italic">"{data.metadata.query}"</p>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0 relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div className="flex items-center gap-2 text-gray-500">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span className="text-sm">Loading visualization...</span>
            </div>
          </div>
        )}

        {imageError ? (
          <div className="p-8 text-center">
            <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">Failed to load visualization</p>
            {onRetry && (
              <Button onClick={onRetry} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            )}
          </div>
        ) : (
          <div className="relative group">
            <img
              ref={imageRef}
              src={data.image}
              alt={title}
              className="w-full h-auto object-contain bg-white"
              onLoad={handleImageLoad}
              onError={handleImageError}
              style={{ display: isLoading ? 'none' : 'block' }}
            />
            
            {/* Overlay controls */}
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="flex gap-1 bg-white bg-opacity-90 rounded-md p-1 shadow-sm">
                <Button
                  onClick={toggleFullscreen}
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
                <Button
                  onClick={handleDownload}
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                >
                  <Download className="h-4 w-4" />
                </Button>
                <Button
                  onClick={handleShare}
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                >
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      {data.metadata?.timestamp && (
        <div className="px-4 py-2 bg-gray-50 border-t border-gray-100">
          <p className="text-xs text-gray-500">
            Generated: {new Date(data.metadata.timestamp).toLocaleString()}
          </p>
        </div>
      )}
    </Card>
  );
};
