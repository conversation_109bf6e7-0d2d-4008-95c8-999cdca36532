import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Save, 
  Cloud, 
  CloudOff, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface ConversationStateIndicatorProps {
  conversationId?: string;
  personaName?: string;
  isRestoring?: boolean;
  isSaving?: boolean;
  lastSaved?: Date;
  connectionStatus?: 'connecting' | 'connected' | 'disconnected' | 'error';
  hasUnsavedChanges?: boolean;
  onRetryConnection?: () => void;
  onManualSave?: () => void;
  className?: string;
}

export const ConversationStateIndicator: React.FC<ConversationStateIndicatorProps> = ({
  conversationId,
  personaName,
  isRestoring = false,
  isSaving = false,
  lastSaved,
  connectionStatus = 'connected',
  hasUnsavedChanges = false,
  onRetryConnection,
  onManualSave,
  className = ''
}) => {
  const formatLastSaved = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const getConnectionStatusConfig = () => {
    switch (connectionStatus) {
      case 'connecting':
        return {
          icon: RefreshCw,
          text: 'Connecting...',
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          iconClass: 'animate-spin'
        };
      case 'connected':
        return {
          icon: Wifi,
          text: 'Connected',
          color: 'bg-green-100 text-green-800 border-green-200',
          iconClass: ''
        };
      case 'disconnected':
        return {
          icon: WifiOff,
          text: 'Disconnected',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          iconClass: ''
        };
      case 'error':
        return {
          icon: AlertTriangle,
          text: 'Connection Error',
          color: 'bg-red-100 text-red-800 border-red-200',
          iconClass: ''
        };
    }
  };

  const connectionConfig = getConnectionStatusConfig();
  const ConnectionIcon = connectionConfig.icon;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Conversation Info */}
      {conversationId && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant="outline" className="text-xs">
              {personaName && (
                <span className="mr-1">{personaName}</span>
              )}
              <span className="opacity-60">
                {conversationId.substring(0, 8)}...
              </span>
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-xs">
              <div>Conversation ID: {conversationId}</div>
              {personaName && <div>Persona: {personaName}</div>}
            </div>
          </TooltipContent>
        </Tooltip>
      )}

      {/* Connection Status */}
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge variant="outline" className={connectionConfig.color}>
            <ConnectionIcon className={`h-3 w-3 mr-1 ${connectionConfig.iconClass}`} />
            {connectionConfig.text}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-xs">
            <div>Connection: {connectionConfig.text}</div>
            {connectionStatus === 'error' && onRetryConnection && (
              <Button
                variant="ghost"
                size="sm"
                className="mt-1 h-6 text-xs"
                onClick={onRetryConnection}
              >
                Retry Connection
              </Button>
            )}
          </div>
        </TooltipContent>
      </Tooltip>

      {/* Restoration Status */}
      <AnimatePresence>
        {isRestoring && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
              Restoring...
            </Badge>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Save Status */}
      <AnimatePresence>
        {(isSaving || hasUnsavedChanges) && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            {isSaving ? (
              <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                <Cloud className="h-3 w-3 mr-1 animate-pulse" />
                Saving...
              </Badge>
            ) : hasUnsavedChanges ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
                    <Clock className="h-3 w-3 mr-1" />
                    Unsaved
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-xs">
                    <div>You have unsaved changes</div>
                    {onManualSave && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="mt-1 h-6 text-xs"
                        onClick={onManualSave}
                      >
                        Save Now
                      </Button>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Last Saved */}
      {lastSaved && !isSaving && !hasUnsavedChanges && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
              <CheckCircle className="h-3 w-3 mr-1" />
              Saved
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-xs">
              Last saved: {formatLastSaved(lastSaved)}
            </div>
          </TooltipContent>
        </Tooltip>
      )}
    </div>
  );
};

interface ConversationRecoveryBannerProps {
  isVisible: boolean;
  conversationId?: string;
  personaName?: string;
  onRestore?: () => void;
  onDismiss?: () => void;
  onStartNew?: () => void;
}

export const ConversationRecoveryBanner: React.FC<ConversationRecoveryBannerProps> = ({
  isVisible,
  conversationId,
  personaName,
  onRestore,
  onDismiss,
  onStartNew
}) => {
  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="mb-4"
      >
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <RefreshCw className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-blue-900">
                  Previous Conversation Found
                </h4>
                <p className="text-sm text-blue-700 mt-1">
                  We found a previous conversation
                  {personaName && ` with ${personaName}`}
                  {conversationId && (
                    <span className="block text-xs text-blue-600 mt-1">
                      ID: {conversationId.substring(0, 8)}...
                    </span>
                  )}
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              {onRestore && (
                <Button
                  size="sm"
                  variant="default"
                  className="bg-blue-600 hover:bg-blue-700"
                  onClick={onRestore}
                >
                  Restore
                </Button>
              )}
              {onStartNew && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={onStartNew}
                >
                  Start New
                </Button>
              )}
              {onDismiss && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={onDismiss}
                >
                  Dismiss
                </Button>
              )}
            </div>
          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};
