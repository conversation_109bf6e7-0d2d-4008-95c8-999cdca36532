
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  <PERSON><PERSON><PERSON>,
  BarChart,
  AreaChart,
  PieChart,
  Table2,
  Gauge,
  Grid,
  Network,
  TreePine,
  Map,
  Type,
  Image,
} from "lucide-react";
import { VisualizationType, DashboardDataSourceAssignment, WidgetDataSourceConfig } from "@/types/dashboard-customization";
import { WidgetDataSourceSelector } from "./WidgetDataSourceSelector";

interface WidgetOption {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  type: VisualizationType;
  category: 'chart' | 'data' | 'kpi' | 'other';
}

interface WidgetFormData {
  title: string;
  type: VisualizationType;
  width: number;
  height: number;
  dataConfig?: WidgetDataSourceConfig;
  visualizationConfig?: Record<string, any>;
  customization?: Record<string, any>;
}

interface AddWidgetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onWidgetAdded: (widgetData: WidgetFormData) => void;
  availableDataSources?: DashboardDataSourceAssignment[];
  sectionId: string;
}

export function AddWidgetDialog({
  open,
  onOpenChange,
  onWidgetAdded,
  availableDataSources = [],
  sectionId,
}: AddWidgetDialogProps) {
  const [selectedWidget, setSelectedWidget] = useState<string | null>(null);
  const [formData, setFormData] = useState<WidgetFormData>({
    title: '',
    type: VisualizationType.CHART,
    width: 4,
    height: 3,
  });

  const widgetOptions: WidgetOption[] = [
    // Chart widgets
    {
      id: "line-chart",
      name: "Line Chart",
      description: "Visualize trends over time",
      icon: LineChart,
      type: VisualizationType.CHART,
      category: 'chart',
    },
    {
      id: "bar-chart",
      name: "Bar Chart",
      description: "Compare categories side by side",
      icon: BarChart,
      type: VisualizationType.CHART,
      category: 'chart',
    },
    {
      id: "area-chart",
      name: "Area Chart",
      description: "Show cumulative totals over time",
      icon: AreaChart,
      type: VisualizationType.CHART,
      category: 'chart',
    },
    {
      id: "pie-chart",
      name: "Pie Chart",
      description: "Display proportion between categories",
      icon: PieChart,
      type: VisualizationType.CHART,
      category: 'chart',
    },
    // Data widgets
    {
      id: "data-table",
      name: "Data Table",
      description: "Show detailed records in tabular format",
      icon: Table2,
      type: VisualizationType.TABLE,
      category: 'data',
    },
    {
      id: "heatmap",
      name: "Heatmap",
      description: "Visualize data density and patterns",
      icon: Grid,
      type: VisualizationType.HEATMAP,
      category: 'chart',
    },
    // KPI widgets
    {
      id: "kpi-metric",
      name: "KPI Metric",
      description: "Display key performance indicators",
      icon: Gauge,
      type: VisualizationType.KPI,
      category: 'kpi',
    },
    {
      id: "gauge-chart",
      name: "Gauge Chart",
      description: "Show progress towards a target",
      icon: Gauge,
      type: VisualizationType.GAUGE,
      category: 'kpi',
    },
    // Advanced widgets
    {
      id: "network-graph",
      name: "Network Graph",
      description: "Visualize relationships and connections",
      icon: Network,
      type: VisualizationType.NETWORK,
      category: 'other',
    },
    {
      id: "tree-map",
      name: "Tree Map",
      description: "Show hierarchical data structure",
      icon: TreePine,
      type: VisualizationType.TREE,
      category: 'chart',
    },
  ];

  const handleWidgetSelect = (widgetId: string) => {
    const selectedOption = widgetOptions.find(option => option.id === widgetId);
    if (selectedOption) {
      setSelectedWidget(widgetId);
      setFormData(prev => ({
        ...prev,
        title: prev.title || selectedOption.name,
        type: selectedOption.type,
      }));
    }
  };

  const handleFormChange = (updates: Partial<WidgetFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const handleAddWidget = () => {
    if (!selectedWidget || !formData.title.trim()) return;

    // Set default visualization config based on widget type
    const selectedOption = widgetOptions.find(option => option.id === selectedWidget);
    if (!selectedOption) return;

    const visualizationConfig: Record<string, any> = {};

    // Set chart-specific configurations
    if (selectedWidget.includes('line')) {
      visualizationConfig.chartType = 'line';
    } else if (selectedWidget.includes('bar')) {
      visualizationConfig.chartType = 'bar';
    } else if (selectedWidget.includes('pie')) {
      visualizationConfig.chartType = 'pie';
    } else if (selectedWidget.includes('area')) {
      visualizationConfig.chartType = 'area';
    }

    const widgetData: WidgetFormData = {
      ...formData,
      visualizationConfig,
    };

    onWidgetAdded(widgetData);
    onOpenChange(false);

    // Reset form
    setSelectedWidget(null);
    setFormData({
      title: '',
      type: VisualizationType.CHART,
      width: 4,
      height: 3,
    });
  };

  const groupedWidgets = widgetOptions.reduce((acc, widget) => {
    if (!acc[widget.category]) {
      acc[widget.category] = [];
    }
    acc[widget.category].push(widget);
    return acc;
  }, {} as Record<string, WidgetOption[]>);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Add Widget</DialogTitle>
          <DialogDescription>
            Create a new widget for your dashboard with custom data sources and visualization options.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="type" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="type">Widget Type</TabsTrigger>
            <TabsTrigger value="data" disabled={!selectedWidget}>Data Source</TabsTrigger>
            <TabsTrigger value="config" disabled={!selectedWidget}>Configuration</TabsTrigger>
          </TabsList>

          {/* Widget Type Selection */}
          <TabsContent value="type" className="space-y-4">
            {Object.entries(groupedWidgets).map(([category, widgets]) => (
              <div key={category} className="space-y-2">
                <h4 className="font-semibold capitalize">{category} Widgets</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {widgets.map((widget) => (
                    <div
                      key={widget.id}
                      className={`
                        p-3 rounded-lg border cursor-pointer transition-all
                        ${selectedWidget === widget.id ? "border-primary bg-primary/5" : "border-border hover:border-primary/50"}
                      `}
                      onClick={() => handleWidgetSelect(widget.id)}
                    >
                      <div className="flex flex-col items-center text-center space-y-2">
                        <widget.icon className="h-6 w-6 text-primary" />
                        <Label className="text-sm">{widget.name}</Label>
                        <p className="text-xs text-muted-foreground">{widget.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </TabsContent>

          {/* Data Source Configuration */}
          <TabsContent value="data" className="space-y-4">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Data Source</h4>
                {availableDataSources.length > 0 ? (
                  <>
                    <p className="text-sm text-muted-foreground mb-4">
                      Select from the data sources configured for this dashboard.
                    </p>
                    <WidgetDataSourceSelector
                      availableDataSources={availableDataSources}
                      value={formData.dataConfig}
                      onChange={(config) => handleFormChange({ dataConfig: config })}
                    />
                  </>
                ) : (
                  <div className="p-4 border border-dashed rounded-lg text-center">
                    <p className="text-sm text-muted-foreground mb-2">
                      No data sources configured for this dashboard.
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Add data sources in the Dashboard Settings → Data Sources tab to connect your widgets to data.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Widget Configuration */}
          <TabsContent value="config" className="space-y-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="widget-title">Widget Title</Label>
                  <Input
                    id="widget-title"
                    value={formData.title}
                    onChange={(e) => handleFormChange({ title: e.target.value })}
                    placeholder="Enter widget title"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Widget Type</Label>
                  <Input
                    value={widgetOptions.find(w => w.id === selectedWidget)?.name || ''}
                    disabled
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="widget-width">Width (Grid Units)</Label>
                  <Input
                    id="widget-width"
                    type="number"
                    min={1}
                    max={12}
                    value={formData.width}
                    onChange={(e) => handleFormChange({ width: parseInt(e.target.value) || 4 })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="widget-height">Height (Grid Units)</Label>
                  <Input
                    id="widget-height"
                    type="number"
                    min={1}
                    max={8}
                    value={formData.height}
                    onChange={(e) => handleFormChange({ height: parseInt(e.target.value) || 3 })}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              setSelectedWidget(null);
              setFormData({
                title: '',
                type: VisualizationType.CHART,
                width: 4,
                height: 3,
              });
              onOpenChange(false);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleAddWidget}
            disabled={!selectedWidget || !formData.title.trim()}
          >
            Add Widget
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
