"""
Tool Composition and Chaining System for MCP Tools.

This module provides a sophisticated system for chaining multiple MCP tools together,
allowing complex workflows where output from one tool becomes input for another,
with proper error propagation, rollback capabilities, and workflow optimization.
"""

import asyncio
import logging
import uuid
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json

from ..base import BaseMCPTool
from ..monitoring.performance_monitor import get_performance_monitor
from ..caching.intelligent_cache import get_cache

logger = logging.getLogger(__name__)


class ChainStepStatus(Enum):
    """Status of a chain step."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ROLLED_BACK = "rolled_back"


class ChainExecutionMode(Enum):
    """Chain execution modes."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    PIPELINE = "pipeline"


@dataclass
class ChainStep:
    """Represents a single step in a tool chain."""
    step_id: str
    tool: BaseMCPTool
    input_mapping: Dict[str, str] = field(default_factory=dict)
    output_mapping: Dict[str, str] = field(default_factory=dict)
    condition: Optional[Callable[[Dict[str, Any]], bool]] = None
    retry_count: int = 3
    timeout_seconds: Optional[int] = None
    rollback_function: Optional[Callable] = None
    dependencies: List[str] = field(default_factory=list)
    status: ChainStepStatus = ChainStepStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


@dataclass
class ChainResult:
    """Result of a tool chain execution."""
    chain_id: str
    success: bool
    steps_executed: int
    steps_failed: int
    total_execution_time: float
    results: Dict[str, Any]
    errors: List[str]
    step_results: Dict[str, Dict[str, Any]]
    metadata: Dict[str, Any] = field(default_factory=dict)


class ToolChain:
    """
    Tool composition and chaining system.
    
    Features:
    - Sequential and parallel execution
    - Conditional step execution
    - Input/output mapping between steps
    - Error handling and rollback
    - Retry mechanisms
    - Performance monitoring
    - Caching of intermediate results
    - Dependency management
    """
    
    def __init__(
        self,
        chain_id: Optional[str] = None,
        execution_mode: ChainExecutionMode = ChainExecutionMode.SEQUENTIAL,
        enable_caching: bool = True,
        enable_monitoring: bool = True,
        max_parallel_steps: int = 5
    ):
        """
        Initialize the tool chain.
        
        Args:
            chain_id: Unique identifier for the chain
            execution_mode: How to execute the chain steps
            enable_caching: Enable caching of intermediate results
            enable_monitoring: Enable performance monitoring
            max_parallel_steps: Maximum parallel steps for parallel execution
        """
        self.chain_id = chain_id or str(uuid.uuid4())
        self.execution_mode = execution_mode
        self.enable_caching = enable_caching
        self.enable_monitoring = enable_monitoring
        self.max_parallel_steps = max_parallel_steps
        
        self.steps: List[ChainStep] = []
        self.global_context: Dict[str, Any] = {}
        self.step_results: Dict[str, Dict[str, Any]] = {}
        
        # Monitoring and caching
        self.monitor = get_performance_monitor() if enable_monitoring else None
        self.cache = get_cache() if enable_caching else None
        
        logger.info(f"Tool chain initialized: {self.chain_id}")
    
    def add_step(
        self,
        tool: BaseMCPTool,
        step_id: Optional[str] = None,
        input_mapping: Optional[Dict[str, str]] = None,
        output_mapping: Optional[Dict[str, str]] = None,
        condition: Optional[Callable[[Dict[str, Any]], bool]] = None,
        retry_count: int = 3,
        timeout_seconds: Optional[int] = None,
        rollback_function: Optional[Callable] = None,
        dependencies: Optional[List[str]] = None
    ) -> str:
        """
        Add a step to the tool chain.
        
        Args:
            tool: MCP tool to execute
            step_id: Unique identifier for the step
            input_mapping: Mapping of chain context to tool input
            output_mapping: Mapping of tool output to chain context
            condition: Condition function to determine if step should execute
            retry_count: Number of retries on failure
            timeout_seconds: Timeout for step execution
            rollback_function: Function to call for rollback
            dependencies: List of step IDs this step depends on
            
        Returns:
            Step ID
        """
        step_id = step_id or f"step_{len(self.steps) + 1}"
        
        step = ChainStep(
            step_id=step_id,
            tool=tool,
            input_mapping=input_mapping or {},
            output_mapping=output_mapping or {},
            condition=condition,
            retry_count=retry_count,
            timeout_seconds=timeout_seconds,
            rollback_function=rollback_function,
            dependencies=dependencies or []
        )
        
        self.steps.append(step)
        logger.debug(f"Added step {step_id} to chain {self.chain_id}")
        
        return step_id
    
    def set_global_context(self, context: Dict[str, Any]):
        """Set global context for the chain."""
        self.global_context.update(context)
    
    async def execute(
        self,
        initial_input: Optional[Dict[str, Any]] = None,
        agent_identity: Optional[str] = None
    ) -> ChainResult:
        """
        Execute the tool chain.
        
        Args:
            initial_input: Initial input for the chain
            agent_identity: Agent identity for monitoring
            
        Returns:
            Chain execution result
        """
        start_time = datetime.now()
        
        # Initialize context
        if initial_input:
            self.global_context.update(initial_input)
        
        # Add agent identity to context
        if agent_identity:
            self.global_context["agent_identity"] = agent_identity
        
        logger.info(f"Starting chain execution: {self.chain_id}")
        
        try:
            if self.execution_mode == ChainExecutionMode.SEQUENTIAL:
                result = await self._execute_sequential()
            elif self.execution_mode == ChainExecutionMode.PARALLEL:
                result = await self._execute_parallel()
            elif self.execution_mode == ChainExecutionMode.CONDITIONAL:
                result = await self._execute_conditional()
            elif self.execution_mode == ChainExecutionMode.PIPELINE:
                result = await self._execute_pipeline()
            else:
                raise ValueError(f"Unsupported execution mode: {self.execution_mode}")
            
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()
            
            # Create chain result
            chain_result = ChainResult(
                chain_id=self.chain_id,
                success=result["success"],
                steps_executed=result["steps_executed"],
                steps_failed=result["steps_failed"],
                total_execution_time=total_time,
                results=self.global_context,
                errors=result["errors"],
                step_results=self.step_results,
                metadata={
                    "execution_mode": self.execution_mode.value,
                    "agent_identity": agent_identity,
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat()
                }
            )
            
            logger.info(f"Chain execution completed: {self.chain_id} - Success: {result['success']}")
            return chain_result
            
        except Exception as e:
            logger.error(f"Chain execution failed: {self.chain_id} - {str(e)}")
            
            # Attempt rollback
            await self._rollback_chain()
            
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()
            
            return ChainResult(
                chain_id=self.chain_id,
                success=False,
                steps_executed=0,
                steps_failed=len(self.steps),
                total_execution_time=total_time,
                results={},
                errors=[str(e)],
                step_results=self.step_results,
                metadata={
                    "execution_mode": self.execution_mode.value,
                    "agent_identity": agent_identity,
                    "error": str(e)
                }
            )
    
    async def _execute_sequential(self) -> Dict[str, Any]:
        """Execute steps sequentially."""
        steps_executed = 0
        steps_failed = 0
        errors = []
        
        for step in self.steps:
            try:
                # Check dependencies
                if not await self._check_dependencies(step):
                    step.status = ChainStepStatus.SKIPPED
                    continue
                
                # Check condition
                if step.condition and not step.condition(self.global_context):
                    step.status = ChainStepStatus.SKIPPED
                    continue
                
                # Execute step
                success = await self._execute_step(step)
                
                if success:
                    steps_executed += 1
                else:
                    steps_failed += 1
                    errors.append(step.error or f"Step {step.step_id} failed")
                    
                    # Stop on failure in sequential mode
                    break
                    
            except Exception as e:
                steps_failed += 1
                errors.append(f"Step {step.step_id} error: {str(e)}")
                break
        
        return {
            "success": steps_failed == 0,
            "steps_executed": steps_executed,
            "steps_failed": steps_failed,
            "errors": errors
        }
    
    async def _execute_parallel(self) -> Dict[str, Any]:
        """Execute steps in parallel where possible."""
        # Group steps by dependency level
        dependency_levels = self._calculate_dependency_levels()
        
        steps_executed = 0
        steps_failed = 0
        errors = []
        
        for level_steps in dependency_levels:
            # Execute steps at this level in parallel
            semaphore = asyncio.Semaphore(self.max_parallel_steps)
            
            async def execute_step_with_semaphore(step):
                async with semaphore:
                    return await self._execute_step(step)
            
            # Filter steps that should execute
            executable_steps = []
            for step in level_steps:
                if step.condition and not step.condition(self.global_context):
                    step.status = ChainStepStatus.SKIPPED
                    continue
                executable_steps.append(step)
            
            if executable_steps:
                # Execute in parallel
                results = await asyncio.gather(
                    *[execute_step_with_semaphore(step) for step in executable_steps],
                    return_exceptions=True
                )
                
                # Process results
                for step, result in zip(executable_steps, results):
                    if isinstance(result, Exception):
                        steps_failed += 1
                        errors.append(f"Step {step.step_id} error: {str(result)}")
                        step.status = ChainStepStatus.FAILED
                        step.error = str(result)
                    elif result:
                        steps_executed += 1
                    else:
                        steps_failed += 1
                        errors.append(step.error or f"Step {step.step_id} failed")
        
        return {
            "success": steps_failed == 0,
            "steps_executed": steps_executed,
            "steps_failed": steps_failed,
            "errors": errors
        }
    
    async def _execute_conditional(self) -> Dict[str, Any]:
        """Execute steps based on conditions."""
        steps_executed = 0
        steps_failed = 0
        errors = []
        
        for step in self.steps:
            try:
                # Check dependencies
                if not await self._check_dependencies(step):
                    step.status = ChainStepStatus.SKIPPED
                    continue
                
                # Check condition (required for conditional mode)
                if not step.condition or not step.condition(self.global_context):
                    step.status = ChainStepStatus.SKIPPED
                    continue
                
                # Execute step
                success = await self._execute_step(step)
                
                if success:
                    steps_executed += 1
                else:
                    steps_failed += 1
                    errors.append(step.error or f"Step {step.step_id} failed")
                    
            except Exception as e:
                steps_failed += 1
                errors.append(f"Step {step.step_id} error: {str(e)}")
        
        return {
            "success": steps_failed == 0,
            "steps_executed": steps_executed,
            "steps_failed": steps_failed,
            "errors": errors
        }
    
    async def _execute_pipeline(self) -> Dict[str, Any]:
        """Execute steps as a pipeline with streaming."""
        steps_executed = 0
        steps_failed = 0
        errors = []
        
        # Pipeline execution passes output directly to next step
        current_data = self.global_context.copy()
        
        for step in self.steps:
            try:
                # Check condition
                if step.condition and not step.condition(current_data):
                    step.status = ChainStepStatus.SKIPPED
                    continue
                
                # Prepare input for this step
                step_input = self._map_input(step, current_data)
                
                # Execute step with pipeline data
                step.status = ChainStepStatus.RUNNING
                step.start_time = datetime.now()
                
                result = await self._execute_tool_with_monitoring(step, step_input)
                
                step.end_time = datetime.now()
                step.execution_time = (step.end_time - step.start_time).total_seconds()
                
                if result.get("isError"):
                    step.status = ChainStepStatus.FAILED
                    step.error = result.get("content", [{}])[0].get("text", "Unknown error")
                    steps_failed += 1
                    errors.append(step.error)
                    break
                else:
                    step.status = ChainStepStatus.COMPLETED
                    step.result = result
                    steps_executed += 1
                    
                    # Update pipeline data for next step
                    current_data = self._map_output(step, result, current_data)
                    
            except Exception as e:
                step.status = ChainStepStatus.FAILED
                step.error = str(e)
                steps_failed += 1
                errors.append(f"Step {step.step_id} error: {str(e)}")
                break
        
        # Update global context with final pipeline result
        self.global_context.update(current_data)
        
        return {
            "success": steps_failed == 0,
            "steps_executed": steps_executed,
            "steps_failed": steps_failed,
            "errors": errors
        }
    
    async def _execute_step(self, step: ChainStep) -> bool:
        """Execute a single step with retry logic."""
        for attempt in range(step.retry_count + 1):
            try:
                step.status = ChainStepStatus.RUNNING
                step.start_time = datetime.now()
                
                # Prepare input
                step_input = self._map_input(step, self.global_context)
                
                # Execute with timeout
                if step.timeout_seconds:
                    result = await asyncio.wait_for(
                        self._execute_tool_with_monitoring(step, step_input),
                        timeout=step.timeout_seconds
                    )
                else:
                    result = await self._execute_tool_with_monitoring(step, step_input)
                
                step.end_time = datetime.now()
                step.execution_time = (step.end_time - step.start_time).total_seconds()
                
                # Check result
                if result.get("isError"):
                    step.status = ChainStepStatus.FAILED
                    step.error = result.get("content", [{}])[0].get("text", "Unknown error")
                    
                    if attempt < step.retry_count:
                        logger.warning(f"Step {step.step_id} failed, retrying ({attempt + 1}/{step.retry_count})")
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                        continue
                    
                    return False
                else:
                    step.status = ChainStepStatus.COMPLETED
                    step.result = result
                    
                    # Map output to global context
                    self.global_context.update(self._map_output(step, result, self.global_context))
                    
                    # Store step result
                    self.step_results[step.step_id] = result
                    
                    return True
                    
            except asyncio.TimeoutError:
                step.error = f"Step timed out after {step.timeout_seconds} seconds"
                if attempt < step.retry_count:
                    continue
                step.status = ChainStepStatus.FAILED
                return False
            except Exception as e:
                step.error = str(e)
                if attempt < step.retry_count:
                    logger.warning(f"Step {step.step_id} error, retrying ({attempt + 1}/{step.retry_count}): {e}")
                    await asyncio.sleep(2 ** attempt)
                    continue
                step.status = ChainStepStatus.FAILED
                return False
        
        return False

    async def _execute_tool_with_monitoring(self, step: ChainStep, step_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute tool with monitoring and caching."""
        # Check cache first
        if self.cache:
            cached_result = await self.cache.get(
                tool_name=step.tool.name,
                operation="chain_step",
                arguments=step_input,
                agent_identity=self.global_context.get("agent_identity")
            )

            if cached_result:
                logger.debug(f"Cache hit for step {step.step_id}")
                return cached_result

        # Execute with monitoring
        if self.monitor:
            async with self.monitor.monitor_execution(
                tool_name=step.tool.name,
                agent_identity=self.global_context.get("agent_identity"),
                input_data=step_input
            ):
                result = await step.tool.execute(step_input)
        else:
            result = await step.tool.execute(step_input)

        # Cache result
        if self.cache and not result.get("isError"):
            await self.cache.set(
                tool_name=step.tool.name,
                operation="chain_step",
                arguments=step_input,
                value=result,
                agent_identity=self.global_context.get("agent_identity")
            )

        return result

    def _map_input(self, step: ChainStep, context: Dict[str, Any]) -> Dict[str, Any]:
        """Map context data to step input."""
        step_input = {}

        # Apply input mapping
        for tool_param, context_key in step.input_mapping.items():
            if context_key in context:
                step_input[tool_param] = context[context_key]

        # Add global context items not explicitly mapped
        for key, value in context.items():
            if key not in step_input:
                step_input[key] = value

        return step_input

    def _map_output(self, step: ChainStep, result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Map step output to context."""
        output_data = {}

        # Apply output mapping
        for context_key, result_path in step.output_mapping.items():
            value = self._extract_value_by_path(result, result_path)
            if value is not None:
                output_data[context_key] = value

        # If no explicit mapping, use step_id as prefix
        if not step.output_mapping:
            output_data[f"{step.step_id}_result"] = result

        return output_data

    def _extract_value_by_path(self, data: Dict[str, Any], path: str) -> Any:
        """Extract value from nested dict using dot notation path."""
        try:
            keys = path.split('.')
            value = data
            for key in keys:
                if isinstance(value, dict):
                    value = value.get(key)
                elif isinstance(value, list) and key.isdigit():
                    value = value[int(key)]
                else:
                    return None
            return value
        except (KeyError, IndexError, ValueError):
            return None

    async def _check_dependencies(self, step: ChainStep) -> bool:
        """Check if step dependencies are satisfied."""
        for dep_id in step.dependencies:
            dep_step = next((s for s in self.steps if s.step_id == dep_id), None)
            if not dep_step or dep_step.status != ChainStepStatus.COMPLETED:
                return False
        return True

    def _calculate_dependency_levels(self) -> List[List[ChainStep]]:
        """Calculate dependency levels for parallel execution."""
        levels = []
        remaining_steps = self.steps.copy()
        completed_steps = set()

        while remaining_steps:
            current_level = []

            for step in remaining_steps[:]:
                # Check if all dependencies are completed
                if all(dep_id in completed_steps for dep_id in step.dependencies):
                    current_level.append(step)
                    remaining_steps.remove(step)

            if not current_level:
                # Circular dependency or invalid dependency
                logger.error("Circular dependency detected in tool chain")
                break

            levels.append(current_level)
            completed_steps.update(step.step_id for step in current_level)

        return levels

    async def _rollback_chain(self):
        """Rollback completed steps in reverse order."""
        logger.info(f"Starting rollback for chain {self.chain_id}")

        # Get completed steps in reverse order
        completed_steps = [s for s in reversed(self.steps) if s.status == ChainStepStatus.COMPLETED]

        for step in completed_steps:
            if step.rollback_function:
                try:
                    await step.rollback_function(step.result, self.global_context)
                    step.status = ChainStepStatus.ROLLED_BACK
                    logger.debug(f"Rolled back step {step.step_id}")
                except Exception as e:
                    logger.error(f"Rollback failed for step {step.step_id}: {e}")

    def get_step_status(self, step_id: str) -> Optional[ChainStepStatus]:
        """Get status of a specific step."""
        step = next((s for s in self.steps if s.step_id == step_id), None)
        return step.status if step else None

    def get_chain_summary(self) -> Dict[str, Any]:
        """Get summary of chain configuration and status."""
        return {
            "chain_id": self.chain_id,
            "execution_mode": self.execution_mode.value,
            "total_steps": len(self.steps),
            "step_statuses": {
                step.step_id: {
                    "status": step.status.value,
                    "tool": step.tool.name,
                    "dependencies": step.dependencies,
                    "execution_time": step.execution_time,
                    "error": step.error
                }
                for step in self.steps
            },
            "global_context_keys": list(self.global_context.keys()),
            "enable_caching": self.enable_caching,
            "enable_monitoring": self.enable_monitoring
        }


class ChainBuilder:
    """Builder class for creating tool chains with fluent interface."""

    def __init__(self, chain_id: Optional[str] = None):
        """Initialize chain builder."""
        self.chain = ToolChain(chain_id=chain_id)

    def sequential(self) -> 'ChainBuilder':
        """Set execution mode to sequential."""
        self.chain.execution_mode = ChainExecutionMode.SEQUENTIAL
        return self

    def parallel(self, max_parallel: int = 5) -> 'ChainBuilder':
        """Set execution mode to parallel."""
        self.chain.execution_mode = ChainExecutionMode.PARALLEL
        self.chain.max_parallel_steps = max_parallel
        return self

    def conditional(self) -> 'ChainBuilder':
        """Set execution mode to conditional."""
        self.chain.execution_mode = ChainExecutionMode.CONDITIONAL
        return self

    def pipeline(self) -> 'ChainBuilder':
        """Set execution mode to pipeline."""
        self.chain.execution_mode = ChainExecutionMode.PIPELINE
        return self

    def add_tool(
        self,
        tool: BaseMCPTool,
        step_id: Optional[str] = None,
        input_mapping: Optional[Dict[str, str]] = None,
        output_mapping: Optional[Dict[str, str]] = None,
        condition: Optional[Callable[[Dict[str, Any]], bool]] = None,
        retry_count: int = 3,
        timeout_seconds: Optional[int] = None,
        dependencies: Optional[List[str]] = None
    ) -> 'ChainBuilder':
        """Add a tool to the chain."""
        self.chain.add_step(
            tool=tool,
            step_id=step_id,
            input_mapping=input_mapping,
            output_mapping=output_mapping,
            condition=condition,
            retry_count=retry_count,
            timeout_seconds=timeout_seconds,
            dependencies=dependencies
        )
        return self

    def with_context(self, context: Dict[str, Any]) -> 'ChainBuilder':
        """Set global context for the chain."""
        self.chain.set_global_context(context)
        return self

    def enable_caching(self, enable: bool = True) -> 'ChainBuilder':
        """Enable or disable caching."""
        self.chain.enable_caching = enable
        return self

    def enable_monitoring(self, enable: bool = True) -> 'ChainBuilder':
        """Enable or disable monitoring."""
        self.chain.enable_monitoring = enable
        return self

    def build(self) -> ToolChain:
        """Build and return the tool chain."""
        return self.chain


# Utility functions for common chain patterns
def create_data_analysis_chain(
    data_access_tool: BaseMCPTool,
    analysis_tool: BaseMCPTool,
    visualization_tool: BaseMCPTool
) -> ToolChain:
    """Create a common data analysis chain."""
    return (ChainBuilder()
            .sequential()
            .add_tool(
                tool=data_access_tool,
                step_id="data_access",
                output_mapping={"data": "content.0.text"}
            )
            .add_tool(
                tool=analysis_tool,
                step_id="analysis",
                input_mapping={"data": "data"},
                output_mapping={"analysis_result": "content.0.text"},
                dependencies=["data_access"]
            )
            .add_tool(
                tool=visualization_tool,
                step_id="visualization",
                input_mapping={"analysis_data": "analysis_result"},
                dependencies=["analysis"]
            )
            .build())


def create_content_processing_chain(
    text_processing_tool: BaseMCPTool,
    classification_tool: BaseMCPTool,
    sentiment_tool: BaseMCPTool
) -> ToolChain:
    """Create a content processing chain."""
    return (ChainBuilder()
            .parallel(max_parallel=3)
            .add_tool(
                tool=text_processing_tool,
                step_id="text_processing",
                output_mapping={"processed_text": "content.0.text"}
            )
            .add_tool(
                tool=classification_tool,
                step_id="classification",
                input_mapping={"text": "processed_text"},
                dependencies=["text_processing"]
            )
            .add_tool(
                tool=sentiment_tool,
                step_id="sentiment",
                input_mapping={"text": "processed_text"},
                dependencies=["text_processing"]
            )
            .build())


def create_marketing_workflow_chain(
    data_tool: BaseMCPTool,
    analysis_tool: BaseMCPTool,
    strategy_tool: BaseMCPTool
) -> ToolChain:
    """Create a marketing workflow chain."""
    return (ChainBuilder()
            .pipeline()
            .add_tool(
                tool=data_tool,
                step_id="data_collection",
                output_mapping={"market_data": "content.0.text"}
            )
            .add_tool(
                tool=analysis_tool,
                step_id="market_analysis",
                input_mapping={"data": "market_data"},
                output_mapping={"insights": "content.0.text"}
            )
            .add_tool(
                tool=strategy_tool,
                step_id="strategy_generation",
                input_mapping={"analysis": "insights"}
            )
            .build())
