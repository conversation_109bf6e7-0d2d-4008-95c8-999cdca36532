import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  MessageSquare, 
  ThumbsUp, 
  Clock,
  Target,
  Zap,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { BusinessProfile } from '@/lib/businessProfileApi';

interface ProfileAnalyticsProps {
  profile: BusinessProfile;
  className?: string;
}

interface AnalyticsMetric {
  label: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ComponentType<any>;
}

interface ConversationMetric {
  date: string;
  conversations: number;
  satisfaction: number;
  responseTime: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const ProfileAnalytics: React.FC<ProfileAnalyticsProps> = ({
  profile,
  className
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [metrics, setMetrics] = useState<AnalyticsMetric[]>([]);
  const [conversationData, setConversationData] = useState<ConversationMetric[]>([]);
  const [agentPerformance, setAgentPerformance] = useState<any[]>([]);

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    loadAnalytics();
  }, [profile.id]);

  const loadAnalytics = async () => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      // Mock metrics data
      setMetrics([
        {
          label: 'AI Response Relevance',
          value: profile.context_metadata?.is_default_profile ? 65 : 87,
          change: profile.context_metadata?.is_default_profile ? -5 : 12,
          trend: profile.context_metadata?.is_default_profile ? 'down' : 'up',
          icon: Target
        },
        {
          label: 'User Satisfaction',
          value: profile.context_metadata?.is_default_profile ? 72 : 91,
          change: profile.context_metadata?.is_default_profile ? -8 : 15,
          trend: profile.context_metadata?.is_default_profile ? 'down' : 'up',
          icon: ThumbsUp
        },
        {
          label: 'Agent Effectiveness',
          value: profile.context_metadata?.is_default_profile ? 58 : 84,
          change: profile.context_metadata?.is_default_profile ? -12 : 18,
          trend: profile.context_metadata?.is_default_profile ? 'down' : 'up',
          icon: Zap
        },
        {
          label: 'Avg Response Time',
          value: profile.context_metadata?.is_default_profile ? 3.2 : 2.1,
          change: profile.context_metadata?.is_default_profile ? 15 : -20,
          trend: profile.context_metadata?.is_default_profile ? 'down' : 'up',
          icon: Clock
        }
      ]);

      // Mock conversation data
      setConversationData([
        { date: '2024-01-01', conversations: 12, satisfaction: 4.2, responseTime: 2.5 },
        { date: '2024-01-02', conversations: 18, satisfaction: 4.5, responseTime: 2.1 },
        { date: '2024-01-03', conversations: 15, satisfaction: 4.3, responseTime: 2.3 },
        { date: '2024-01-04', conversations: 22, satisfaction: 4.7, responseTime: 1.9 },
        { date: '2024-01-05', conversations: 19, satisfaction: 4.4, responseTime: 2.2 },
        { date: '2024-01-06', conversations: 25, satisfaction: 4.8, responseTime: 1.8 },
        { date: '2024-01-07', conversations: 21, satisfaction: 4.6, responseTime: 2.0 }
      ]);

      // Mock agent performance data
      setAgentPerformance([
        { name: 'Concierge Agent', performance: 92, interactions: 156 },
        { name: 'Marketing AI', performance: 88, interactions: 89 },
        { name: 'Data Analyst', performance: 85, interactions: 67 },
        { name: 'Research AI', performance: 90, interactions: 45 }
      ]);

      setIsLoading(false);
    }, 1000);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Profile Analytics</h3>
          <p className="text-sm text-muted-foreground">
            How your business profile impacts AI agent performance
          </p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={loadAnalytics}
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {metric.label}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className={`text-2xl font-bold ${getScoreColor(metric.value)}`}>
                      {metric.label.includes('Time') ? `${metric.value}s` : `${metric.value}%`}
                    </span>
                    <Badge variant={getScoreBadgeVariant(metric.value)} className="text-xs">
                      {metric.change > 0 ? '+' : ''}{metric.change}%
                    </Badge>
                  </div>
                </div>
                <div className="p-2 bg-muted rounded-lg">
                  <metric.icon className="h-5 w-5 text-muted-foreground" />
                </div>
              </div>
              <Progress
                value={metric.value}
                className="mt-3"
                indicatorClassName={
                  metric.value >= 80 ? 'bg-green-500' :
                  metric.value >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                }
              />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="agents">Agent Performance</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Conversation Trends</CardTitle>
              <CardDescription>
                Daily conversation volume and satisfaction scores
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={conversationData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="conversations" 
                    stroke="#8884d8" 
                    strokeWidth={2}
                    name="Conversations"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="satisfaction" 
                    stroke="#82ca9d" 
                    strokeWidth={2}
                    name="Satisfaction"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Agent Performance</CardTitle>
              <CardDescription>
                Performance scores by AI agent
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={agentPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="performance" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Profile Completeness Impact</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Profile Information</span>
                    <Badge variant={profile.context_metadata?.is_default_profile ? 'destructive' : 'default'}>
                      {profile.context_metadata?.is_default_profile ? 'Basic' : 'Complete'}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {profile.context_metadata?.is_default_profile 
                      ? 'Your default profile provides basic context. Customizing it could improve AI performance by up to 35%.'
                      : 'Your detailed profile helps AI agents provide more relevant and accurate responses.'
                    }
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.context_metadata?.is_default_profile ? (
                    <>
                      <div className="text-sm">• Add specific industry details</div>
                      <div className="text-sm">• Define target audience</div>
                      <div className="text-sm">• Specify business goals</div>
                      <div className="text-sm">• Upload relevant documents</div>
                    </>
                  ) : (
                    <>
                      <div className="text-sm">• Keep profile information updated</div>
                      <div className="text-sm">• Add more data sources</div>
                      <div className="text-sm">• Provide feedback on AI responses</div>
                      <div className="text-sm">• Use multiple AI agents</div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
