"""
Tool Usage Analytics and Insights System.

This module provides comprehensive analytics for tool usage patterns, success rates,
user preferences, and performance insights to guide future tool development and optimization.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, Counter
from enum import Enum
import json
import statistics

logger = logging.getLogger(__name__)


class AnalyticsTimeframe(Enum):
    """Time frames for analytics aggregation."""
    HOUR = "hour"
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"


class UsagePattern(Enum):
    """Common usage patterns."""
    FREQUENT = "frequent"
    OCCASIONAL = "occasional"
    RARE = "rare"
    POWER_USER = "power_user"
    NEW_USER = "new_user"


@dataclass
class ToolUsageEvent:
    """Individual tool usage event."""
    tool_name: str
    agent_identity: str
    user_id: Optional[str]
    timestamp: datetime
    execution_time: float
    success: bool
    input_size: int
    output_size: int
    error_type: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ToolAnalytics:
    """Analytics data for a specific tool."""
    tool_name: str
    total_usage: int
    unique_users: int
    unique_agents: int
    success_rate: float
    avg_execution_time: float
    total_data_processed: int
    peak_usage_hour: int
    most_common_errors: List[Tuple[str, int]]
    usage_trend: str  # "increasing", "decreasing", "stable"
    user_satisfaction_score: float
    agent_preferences: Dict[str, int]
    
    @property
    def popularity_score(self) -> float:
        """Calculate popularity score based on usage and success."""
        return (self.total_usage * self.success_rate * self.user_satisfaction_score) / 100


@dataclass
class UserAnalytics:
    """Analytics data for a specific user."""
    user_id: str
    total_tool_usage: int
    unique_tools_used: int
    favorite_tools: List[Tuple[str, int]]
    preferred_agents: List[Tuple[str, int]]
    usage_pattern: UsagePattern
    avg_session_duration: float
    success_rate: float
    most_active_hours: List[int]
    tool_expertise_level: Dict[str, str]  # tool -> expertise level


@dataclass
class AgentAnalytics:
    """Analytics data for a specific agent."""
    agent_identity: str
    total_usage: int
    unique_users: int
    tools_used: Dict[str, int]
    success_rate: float
    avg_response_time: float
    user_satisfaction: float
    specialization_score: Dict[str, float]  # domain -> score
    efficiency_rating: float


class UsageAnalytics:
    """
    Comprehensive usage analytics system for MCP tools.
    
    Features:
    - Real-time usage tracking
    - User behavior analysis
    - Tool popularity metrics
    - Agent performance analytics
    - Trend analysis and forecasting
    - Usage pattern recognition
    - Performance optimization insights
    - User satisfaction tracking
    """
    
    def __init__(self, retention_days: int = 90):
        """
        Initialize the usage analytics system.
        
        Args:
            retention_days: Number of days to retain detailed analytics data
        """
        self.retention_days = retention_days
        
        # Event storage
        self.usage_events: List[ToolUsageEvent] = []
        self.max_events = 100000  # Limit memory usage
        
        # Aggregated analytics
        self.tool_analytics: Dict[str, ToolAnalytics] = {}
        self.user_analytics: Dict[str, UserAnalytics] = {}
        self.agent_analytics: Dict[str, AgentAnalytics] = {}
        
        # Real-time metrics
        self.hourly_usage: Dict[str, Dict[int, int]] = defaultdict(lambda: defaultdict(int))
        self.daily_usage: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        
        # Trend tracking
        self.usage_trends: Dict[str, List[Tuple[datetime, int]]] = defaultdict(list)
        
        # User satisfaction tracking
        self.satisfaction_scores: Dict[str, List[float]] = defaultdict(list)
        
        logger.info("Usage analytics system initialized")
    
    async def record_usage_event(
        self,
        tool_name: str,
        agent_identity: str,
        execution_time: float,
        success: bool,
        user_id: Optional[str] = None,
        input_size: int = 0,
        output_size: int = 0,
        error_type: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        """
        Record a tool usage event.
        
        Args:
            tool_name: Name of the tool used
            agent_identity: Agent that used the tool
            execution_time: Time taken to execute
            success: Whether execution was successful
            user_id: User identifier
            input_size: Size of input data
            output_size: Size of output data
            error_type: Type of error if failed
            context: Additional context information
        """
        event = ToolUsageEvent(
            tool_name=tool_name,
            agent_identity=agent_identity,
            user_id=user_id,
            timestamp=datetime.now(),
            execution_time=execution_time,
            success=success,
            input_size=input_size,
            output_size=output_size,
            error_type=error_type,
            context=context or {}
        )
        
        # Add to events list
        self.usage_events.append(event)
        
        # Limit memory usage
        if len(self.usage_events) > self.max_events:
            self.usage_events = self.usage_events[-self.max_events:]
        
        # Update real-time metrics
        await self._update_real_time_metrics(event)
        
        # Update aggregated analytics periodically
        if len(self.usage_events) % 100 == 0:
            await self._update_aggregated_analytics()
    
    async def _update_real_time_metrics(self, event: ToolUsageEvent):
        """Update real-time usage metrics."""
        hour = event.timestamp.hour
        day_key = event.timestamp.strftime("%Y-%m-%d")
        
        # Update hourly usage
        self.hourly_usage[event.tool_name][hour] += 1
        
        # Update daily usage
        self.daily_usage[event.tool_name][day_key] += 1
        
        # Update usage trends
        self.usage_trends[event.tool_name].append((event.timestamp, 1))
        
        # Limit trend data
        cutoff_time = datetime.now() - timedelta(days=30)
        self.usage_trends[event.tool_name] = [
            (ts, count) for ts, count in self.usage_trends[event.tool_name]
            if ts >= cutoff_time
        ]
    
    async def _update_aggregated_analytics(self):
        """Update aggregated analytics from recent events."""
        # Clean old events
        cutoff_time = datetime.now() - timedelta(days=self.retention_days)
        self.usage_events = [e for e in self.usage_events if e.timestamp >= cutoff_time]
        
        # Update tool analytics
        await self._calculate_tool_analytics()
        
        # Update user analytics
        await self._calculate_user_analytics()
        
        # Update agent analytics
        await self._calculate_agent_analytics()
    
    async def _calculate_tool_analytics(self):
        """Calculate analytics for each tool."""
        tool_events = defaultdict(list)
        
        # Group events by tool
        for event in self.usage_events:
            tool_events[event.tool_name].append(event)
        
        # Calculate analytics for each tool
        for tool_name, events in tool_events.items():
            if not events:
                continue
            
            successful_events = [e for e in events if e.success]
            unique_users = len(set(e.user_id for e in events if e.user_id))
            unique_agents = len(set(e.agent_identity for e in events))
            
            # Calculate error distribution
            error_counter = Counter(e.error_type for e in events if e.error_type)
            most_common_errors = error_counter.most_common(5)
            
            # Calculate peak usage hour
            hour_usage = Counter(e.timestamp.hour for e in events)
            peak_hour = hour_usage.most_common(1)[0][0] if hour_usage else 0
            
            # Calculate usage trend
            trend = self._calculate_usage_trend(tool_name)
            
            # Calculate agent preferences
            agent_usage = Counter(e.agent_identity for e in events)
            
            # Calculate satisfaction score (placeholder - would integrate with feedback system)
            satisfaction = statistics.mean(self.satisfaction_scores.get(tool_name, [4.0]))
            
            self.tool_analytics[tool_name] = ToolAnalytics(
                tool_name=tool_name,
                total_usage=len(events),
                unique_users=unique_users,
                unique_agents=unique_agents,
                success_rate=len(successful_events) / len(events),
                avg_execution_time=statistics.mean(e.execution_time for e in events),
                total_data_processed=sum(e.input_size + e.output_size for e in events),
                peak_usage_hour=peak_hour,
                most_common_errors=most_common_errors,
                usage_trend=trend,
                user_satisfaction_score=satisfaction,
                agent_preferences=dict(agent_usage)
            )
    
    async def _calculate_user_analytics(self):
        """Calculate analytics for each user."""
        user_events = defaultdict(list)
        
        # Group events by user
        for event in self.usage_events:
            if event.user_id:
                user_events[event.user_id].append(event)
        
        # Calculate analytics for each user
        for user_id, events in user_events.items():
            if not events:
                continue
            
            successful_events = [e for e in events if e.success]
            tool_usage = Counter(e.tool_name for e in events)
            agent_usage = Counter(e.agent_identity for e in events)
            
            # Calculate usage pattern
            usage_pattern = self._determine_usage_pattern(events)
            
            # Calculate session duration (approximate)
            session_durations = self._calculate_session_durations(events)
            avg_session = statistics.mean(session_durations) if session_durations else 0
            
            # Calculate most active hours
            hour_usage = Counter(e.timestamp.hour for e in events)
            most_active_hours = [hour for hour, _ in hour_usage.most_common(3)]
            
            # Calculate tool expertise
            tool_expertise = self._calculate_tool_expertise(user_id, events)
            
            self.user_analytics[user_id] = UserAnalytics(
                user_id=user_id,
                total_tool_usage=len(events),
                unique_tools_used=len(set(e.tool_name for e in events)),
                favorite_tools=tool_usage.most_common(5),
                preferred_agents=agent_usage.most_common(3),
                usage_pattern=usage_pattern,
                avg_session_duration=avg_session,
                success_rate=len(successful_events) / len(events),
                most_active_hours=most_active_hours,
                tool_expertise_level=tool_expertise
            )
    
    async def _calculate_agent_analytics(self):
        """Calculate analytics for each agent."""
        agent_events = defaultdict(list)
        
        # Group events by agent
        for event in self.usage_events:
            agent_events[event.agent_identity].append(event)
        
        # Calculate analytics for each agent
        for agent_identity, events in agent_events.items():
            if not events:
                continue
            
            successful_events = [e for e in events if e.success]
            unique_users = len(set(e.user_id for e in events if e.user_id))
            tool_usage = Counter(e.tool_name for e in events)
            
            # Calculate specialization scores
            specialization = self._calculate_agent_specialization(events)
            
            # Calculate efficiency rating
            efficiency = self._calculate_efficiency_rating(events)
            
            # Calculate user satisfaction
            satisfaction = statistics.mean(self.satisfaction_scores.get(agent_identity, [4.0]))
            
            self.agent_analytics[agent_identity] = AgentAnalytics(
                agent_identity=agent_identity,
                total_usage=len(events),
                unique_users=unique_users,
                tools_used=dict(tool_usage),
                success_rate=len(successful_events) / len(events),
                avg_response_time=statistics.mean(e.execution_time for e in events),
                user_satisfaction=satisfaction,
                specialization_score=specialization,
                efficiency_rating=efficiency
            )
    
    def _calculate_usage_trend(self, tool_name: str) -> str:
        """Calculate usage trend for a tool."""
        trends = self.usage_trends.get(tool_name, [])
        if len(trends) < 10:
            return "stable"
        
        # Calculate trend over last 7 days vs previous 7 days
        now = datetime.now()
        recent_week = now - timedelta(days=7)
        previous_week = now - timedelta(days=14)
        
        recent_usage = sum(1 for ts, _ in trends if ts >= recent_week)
        previous_usage = sum(1 for ts, _ in trends if previous_week <= ts < recent_week)
        
        if previous_usage == 0:
            return "stable"
        
        change_ratio = recent_usage / previous_usage
        
        if change_ratio > 1.2:
            return "increasing"
        elif change_ratio < 0.8:
            return "decreasing"
        else:
            return "stable"
    
    def _determine_usage_pattern(self, events: List[ToolUsageEvent]) -> UsagePattern:
        """Determine user usage pattern."""
        total_events = len(events)
        unique_days = len(set(e.timestamp.date() for e in events))
        
        if total_events >= 100 and unique_days >= 20:
            return UsagePattern.POWER_USER
        elif total_events >= 50 and unique_days >= 10:
            return UsagePattern.FREQUENT
        elif total_events >= 10 and unique_days >= 3:
            return UsagePattern.OCCASIONAL
        elif total_events < 5:
            return UsagePattern.NEW_USER
        else:
            return UsagePattern.RARE
    
    def _calculate_session_durations(self, events: List[ToolUsageEvent]) -> List[float]:
        """Calculate session durations from events."""
        # Group events by day and calculate session durations
        daily_events = defaultdict(list)
        for event in events:
            daily_events[event.timestamp.date()].append(event)
        
        session_durations = []
        for day_events in daily_events.values():
            if len(day_events) < 2:
                continue
            
            day_events.sort(key=lambda e: e.timestamp)
            session_start = day_events[0].timestamp
            session_end = day_events[-1].timestamp
            duration = (session_end - session_start).total_seconds() / 60  # minutes
            
            if duration > 0:
                session_durations.append(duration)
        
        return session_durations
    
    def _calculate_tool_expertise(self, user_id: str, events: List[ToolUsageEvent]) -> Dict[str, str]:
        """Calculate user expertise level for each tool."""
        tool_events = defaultdict(list)
        for event in events:
            tool_events[event.tool_name].append(event)
        
        expertise = {}
        for tool_name, tool_events_list in tool_events.items():
            usage_count = len(tool_events_list)
            success_rate = sum(1 for e in tool_events_list if e.success) / usage_count
            
            if usage_count >= 50 and success_rate >= 0.9:
                expertise[tool_name] = "expert"
            elif usage_count >= 20 and success_rate >= 0.8:
                expertise[tool_name] = "advanced"
            elif usage_count >= 10 and success_rate >= 0.7:
                expertise[tool_name] = "intermediate"
            elif usage_count >= 5:
                expertise[tool_name] = "beginner"
            else:
                expertise[tool_name] = "novice"
        
        return expertise
    
    def _calculate_agent_specialization(self, events: List[ToolUsageEvent]) -> Dict[str, float]:
        """Calculate agent specialization scores by domain."""
        # Map tools to domains (simplified)
        tool_domains = {
            "data_access": "data",
            "data_visualization": "data",
            "statistical_analysis": "analytics",
            "pandasai_analysis": "analytics",
            "text_processing": "nlp",
            "text_classification": "nlp",
            "sentiment_analysis": "nlp",
            "marketing_strategy_generation": "marketing",
            "intent_detection": "ai",
            "conversation_tool": "ai"
        }
        
        domain_usage = defaultdict(int)
        total_usage = len(events)
        
        for event in events:
            domain = tool_domains.get(event.tool_name, "general")
            domain_usage[domain] += 1
        
        # Calculate specialization scores
        specialization = {}
        for domain, usage in domain_usage.items():
            specialization[domain] = usage / total_usage
        
        return specialization
    
    def _calculate_efficiency_rating(self, events: List[ToolUsageEvent]) -> float:
        """Calculate agent efficiency rating."""
        if not events:
            return 0.0
        
        successful_events = [e for e in events if e.success]
        success_rate = len(successful_events) / len(events)
        
        # Calculate average execution time relative to tool benchmarks
        avg_execution_time = statistics.mean(e.execution_time for e in events)
        
        # Simplified efficiency calculation
        # In practice, this would compare against tool-specific benchmarks
        time_efficiency = max(0, 1 - (avg_execution_time / 10))  # Assume 10s is baseline
        
        return (success_rate * 0.7 + time_efficiency * 0.3) * 5  # Scale to 0-5

    async def record_user_satisfaction(self, tool_or_agent: str, score: float):
        """Record user satisfaction score for a tool or agent."""
        if 0 <= score <= 5:
            self.satisfaction_scores[tool_or_agent].append(score)
            # Keep only recent scores
            if len(self.satisfaction_scores[tool_or_agent]) > 100:
                self.satisfaction_scores[tool_or_agent] = self.satisfaction_scores[tool_or_agent][-100:]

    def get_tool_analytics(self, tool_name: Optional[str] = None) -> Dict[str, Any]:
        """Get analytics for specific tool or all tools."""
        if tool_name:
            analytics = self.tool_analytics.get(tool_name)
            if analytics:
                return {
                    "tool_name": analytics.tool_name,
                    "total_usage": analytics.total_usage,
                    "unique_users": analytics.unique_users,
                    "unique_agents": analytics.unique_agents,
                    "success_rate": round(analytics.success_rate, 3),
                    "avg_execution_time": round(analytics.avg_execution_time, 3),
                    "total_data_processed_mb": round(analytics.total_data_processed / (1024 * 1024), 2),
                    "peak_usage_hour": analytics.peak_usage_hour,
                    "most_common_errors": analytics.most_common_errors,
                    "usage_trend": analytics.usage_trend,
                    "user_satisfaction_score": round(analytics.user_satisfaction_score, 2),
                    "popularity_score": round(analytics.popularity_score, 2),
                    "agent_preferences": analytics.agent_preferences
                }
            else:
                return {"error": f"No analytics found for tool: {tool_name}"}
        else:
            return {
                tool_name: {
                    "total_usage": analytics.total_usage,
                    "success_rate": round(analytics.success_rate, 3),
                    "popularity_score": round(analytics.popularity_score, 2),
                    "usage_trend": analytics.usage_trend,
                    "user_satisfaction": round(analytics.user_satisfaction_score, 2)
                }
                for tool_name, analytics in self.tool_analytics.items()
            }

    def get_user_analytics(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get analytics for specific user or all users."""
        if user_id:
            analytics = self.user_analytics.get(user_id)
            if analytics:
                return {
                    "user_id": analytics.user_id,
                    "total_tool_usage": analytics.total_tool_usage,
                    "unique_tools_used": analytics.unique_tools_used,
                    "favorite_tools": analytics.favorite_tools,
                    "preferred_agents": analytics.preferred_agents,
                    "usage_pattern": analytics.usage_pattern.value,
                    "avg_session_duration_minutes": round(analytics.avg_session_duration, 1),
                    "success_rate": round(analytics.success_rate, 3),
                    "most_active_hours": analytics.most_active_hours,
                    "tool_expertise_level": analytics.tool_expertise_level
                }
            else:
                return {"error": f"No analytics found for user: {user_id}"}
        else:
            return {
                user_id: {
                    "total_usage": analytics.total_tool_usage,
                    "usage_pattern": analytics.usage_pattern.value,
                    "success_rate": round(analytics.success_rate, 3),
                    "favorite_tools": analytics.favorite_tools[:3]
                }
                for user_id, analytics in self.user_analytics.items()
            }

    def get_agent_analytics(self, agent_identity: Optional[str] = None) -> Dict[str, Any]:
        """Get analytics for specific agent or all agents."""
        if agent_identity:
            analytics = self.agent_analytics.get(agent_identity)
            if analytics:
                return {
                    "agent_identity": analytics.agent_identity,
                    "total_usage": analytics.total_usage,
                    "unique_users": analytics.unique_users,
                    "tools_used": analytics.tools_used,
                    "success_rate": round(analytics.success_rate, 3),
                    "avg_response_time": round(analytics.avg_response_time, 3),
                    "user_satisfaction": round(analytics.user_satisfaction, 2),
                    "specialization_score": {k: round(v, 3) for k, v in analytics.specialization_score.items()},
                    "efficiency_rating": round(analytics.efficiency_rating, 2)
                }
            else:
                return {"error": f"No analytics found for agent: {agent_identity}"}
        else:
            return {
                agent_identity: {
                    "total_usage": analytics.total_usage,
                    "success_rate": round(analytics.success_rate, 3),
                    "efficiency_rating": round(analytics.efficiency_rating, 2),
                    "user_satisfaction": round(analytics.user_satisfaction, 2),
                    "top_specialization": max(analytics.specialization_score.items(), key=lambda x: x[1])[0] if analytics.specialization_score else "general"
                }
                for agent_identity, analytics in self.agent_analytics.items()
            }

    def get_usage_insights(self, timeframe: AnalyticsTimeframe = AnalyticsTimeframe.WEEK) -> Dict[str, Any]:
        """Get comprehensive usage insights and recommendations."""
        insights = {
            "timestamp": datetime.now().isoformat(),
            "timeframe": timeframe.value,
            "summary": {},
            "top_performers": {},
            "trends": {},
            "recommendations": [],
            "alerts": []
        }

        # Calculate summary metrics
        total_events = len(self.usage_events)
        successful_events = sum(1 for e in self.usage_events if e.success)
        unique_tools = len(set(e.tool_name for e in self.usage_events))
        unique_users = len(set(e.user_id for e in self.usage_events if e.user_id))
        unique_agents = len(set(e.agent_identity for e in self.usage_events))

        insights["summary"] = {
            "total_tool_executions": total_events,
            "overall_success_rate": round(successful_events / total_events, 3) if total_events > 0 else 0,
            "unique_tools_used": unique_tools,
            "unique_users": unique_users,
            "unique_agents": unique_agents,
            "avg_executions_per_user": round(total_events / unique_users, 1) if unique_users > 0 else 0
        }

        # Top performers
        if self.tool_analytics:
            top_tools = sorted(self.tool_analytics.values(), key=lambda x: x.popularity_score, reverse=True)[:5]
            insights["top_performers"]["tools"] = [
                {"name": t.tool_name, "popularity_score": round(t.popularity_score, 2), "usage": t.total_usage}
                for t in top_tools
            ]

        if self.agent_analytics:
            top_agents = sorted(self.agent_analytics.values(), key=lambda x: x.efficiency_rating, reverse=True)[:5]
            insights["top_performers"]["agents"] = [
                {"identity": a.agent_identity, "efficiency_rating": round(a.efficiency_rating, 2), "usage": a.total_usage}
                for a in top_agents
            ]

        # Usage trends
        insights["trends"] = {
            "increasing_tools": [name for name, analytics in self.tool_analytics.items() if analytics.usage_trend == "increasing"],
            "decreasing_tools": [name for name, analytics in self.tool_analytics.items() if analytics.usage_trend == "decreasing"],
            "peak_usage_hours": self._get_peak_usage_hours()
        }

        # Generate recommendations
        insights["recommendations"] = self._generate_recommendations()

        # Generate alerts
        insights["alerts"] = self._generate_alerts()

        return insights

    def _get_peak_usage_hours(self) -> List[int]:
        """Get peak usage hours across all tools."""
        hour_usage = defaultdict(int)
        for tool_usage in self.hourly_usage.values():
            for hour, count in tool_usage.items():
                hour_usage[hour] += count

        if not hour_usage:
            return []

        # Return top 3 peak hours
        return [hour for hour, _ in sorted(hour_usage.items(), key=lambda x: x[1], reverse=True)[:3]]

    def _generate_recommendations(self) -> List[Dict[str, Any]]:
        """Generate optimization recommendations based on analytics."""
        recommendations = []

        # Tool performance recommendations
        for tool_name, analytics in self.tool_analytics.items():
            if analytics.success_rate < 0.9:
                recommendations.append({
                    "type": "performance",
                    "priority": "high",
                    "target": tool_name,
                    "issue": f"Low success rate ({analytics.success_rate:.1%})",
                    "recommendation": "Investigate error patterns and improve error handling",
                    "impact": "Improve user experience and tool reliability"
                })

            if analytics.avg_execution_time > 10:
                recommendations.append({
                    "type": "performance",
                    "priority": "medium",
                    "target": tool_name,
                    "issue": f"Slow execution time ({analytics.avg_execution_time:.1f}s)",
                    "recommendation": "Optimize tool performance or implement caching",
                    "impact": "Reduce user wait times and improve efficiency"
                })

            if analytics.user_satisfaction_score < 3.5:
                recommendations.append({
                    "type": "user_experience",
                    "priority": "high",
                    "target": tool_name,
                    "issue": f"Low user satisfaction ({analytics.user_satisfaction_score:.1f}/5)",
                    "recommendation": "Gather user feedback and improve tool functionality",
                    "impact": "Increase user adoption and satisfaction"
                })

        # Usage pattern recommendations
        low_usage_tools = [name for name, analytics in self.tool_analytics.items() if analytics.total_usage < 10]
        if low_usage_tools:
            recommendations.append({
                "type": "adoption",
                "priority": "medium",
                "target": "multiple_tools",
                "issue": f"Low usage tools: {', '.join(low_usage_tools[:3])}",
                "recommendation": "Improve tool discoverability and documentation",
                "impact": "Increase tool adoption and utilization"
            })

        return recommendations

    def _generate_alerts(self) -> List[Dict[str, Any]]:
        """Generate alerts for critical issues."""
        alerts = []

        # Critical performance alerts
        for tool_name, analytics in self.tool_analytics.items():
            if analytics.success_rate < 0.5:
                alerts.append({
                    "type": "critical",
                    "severity": "high",
                    "target": tool_name,
                    "message": f"Critical: {tool_name} has very low success rate ({analytics.success_rate:.1%})",
                    "action_required": "Immediate investigation needed"
                })

            if analytics.usage_trend == "decreasing" and analytics.total_usage > 50:
                alerts.append({
                    "type": "trend",
                    "severity": "medium",
                    "target": tool_name,
                    "message": f"Warning: {tool_name} usage is decreasing",
                    "action_required": "Investigate user feedback and tool performance"
                })

        return alerts

    def export_analytics_report(self, file_path: str, include_raw_data: bool = False):
        """Export comprehensive analytics report to file."""
        report = {
            "export_timestamp": datetime.now().isoformat(),
            "retention_days": self.retention_days,
            "summary": {
                "total_events": len(self.usage_events),
                "total_tools": len(self.tool_analytics),
                "total_users": len(self.user_analytics),
                "total_agents": len(self.agent_analytics)
            },
            "tool_analytics": {name: self.get_tool_analytics(name) for name in self.tool_analytics.keys()},
            "agent_analytics": {name: self.get_agent_analytics(name) for name in self.agent_analytics.keys()},
            "usage_insights": self.get_usage_insights()
        }

        if include_raw_data:
            report["raw_events"] = [
                {
                    "tool_name": e.tool_name,
                    "agent_identity": e.agent_identity,
                    "user_id": e.user_id,
                    "timestamp": e.timestamp.isoformat(),
                    "execution_time": e.execution_time,
                    "success": e.success,
                    "error_type": e.error_type
                }
                for e in self.usage_events[-1000:]  # Last 1000 events
            ]

        with open(file_path, 'w') as f:
            json.dump(report, f, indent=2)

        logger.info(f"Analytics report exported to {file_path}")

    async def cleanup_old_data(self):
        """Clean up old analytics data."""
        cutoff_time = datetime.now() - timedelta(days=self.retention_days)

        # Clean events
        initial_count = len(self.usage_events)
        self.usage_events = [e for e in self.usage_events if e.timestamp >= cutoff_time]
        cleaned_count = initial_count - len(self.usage_events)

        # Clean trend data
        for tool_name in self.usage_trends:
            self.usage_trends[tool_name] = [
                (ts, count) for ts, count in self.usage_trends[tool_name]
                if ts >= cutoff_time
            ]

        logger.info(f"Cleaned up {cleaned_count} old analytics events")


# Global analytics instance
_global_analytics: Optional[UsageAnalytics] = None


def get_usage_analytics() -> UsageAnalytics:
    """Get global usage analytics instance."""
    global _global_analytics
    if _global_analytics is None:
        _global_analytics = UsageAnalytics()
    return _global_analytics


async def track_tool_usage(
    tool_name: str,
    agent_identity: str,
    execution_time: float,
    success: bool,
    user_id: Optional[str] = None,
    input_size: int = 0,
    output_size: int = 0,
    error_type: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
):
    """Convenience function to track tool usage."""
    analytics = get_usage_analytics()
    await analytics.record_usage_event(
        tool_name=tool_name,
        agent_identity=agent_identity,
        execution_time=execution_time,
        success=success,
        user_id=user_id,
        input_size=input_size,
        output_size=output_size,
        error_type=error_type,
        context=context
    )
