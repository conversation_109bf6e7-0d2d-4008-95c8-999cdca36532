<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Persona Switching UI Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .persona-button {
            transition: all 0.2s ease-in-out;
        }
        .persona-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">Enhanced Persona Switching UI</h1>
        <p class="text-gray-600 mb-8">This preview shows how the concierge agent's persona recommendations will appear in the chat interface.</p>
        
        <!-- Chat Message Container -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="flex items-start gap-3 mb-4">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                    C
                </div>
                <div class="flex-1">
                    <div class="font-medium text-gray-800 mb-2">Datagenius Concierge</div>
                    <div class="text-gray-700 mb-4">
                        <h2 class="text-lg font-semibold mb-2">🎯 Your Owned Personas</h2>
                        <p class="mb-3">Click any persona below to start chatting with them:</p>
                        
                        <h2 class="text-lg font-semibold mb-2">🆓 Free Personas Available</h2>
                        <p class="mb-3">These personas are free to use - click to start chatting:</p>
                        
                        <h2 class="text-lg font-semibold mb-2">💎 Premium Personas Available</h2>
                        <p class="mb-3">Purchase these personas from the marketplace to unlock advanced capabilities:</p>
                        
                        <hr class="my-4">
                        <p><strong>Summary:</strong> You own 1 persona(s). 2 free and 3 premium personas are available.</p>
                        <p class="mt-2">💡 <strong>Tip:</strong> Tell me what you want to accomplish, and I'll recommend the best persona for your needs!</p>
                    </div>
                    
                    <!-- Interactive Persona Buttons -->
                    <div class="space-y-4">
                        <!-- Owned Personas Section -->
                        <div class="space-y-2">
                            <div class="text-sm font-medium text-green-700 bg-green-50 px-3 py-2 rounded-lg">
                                🎯 Your Owned Personas (1)
                            </div>
                            <button class="persona-button w-full text-left p-4 border-2 border-green-200 rounded-lg hover:border-green-300 hover:bg-green-50">
                                <div class="flex flex-col w-full">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="font-semibold text-green-800">Datagenius Concierge</span>
                                        <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">OWNED</span>
                                    </div>
                                    <span class="text-sm text-gray-600 mb-2">Your personal AI assistant for guidance and recommendations</span>
                                    <span class="text-xs text-gray-500">
                                        Capabilities: User guidance, persona recommendation, workflow coordination
                                    </span>
                                </div>
                            </button>
                        </div>

                        <!-- Free Personas Section -->
                        <div class="space-y-2">
                            <div class="text-sm font-medium text-blue-700 bg-blue-50 px-3 py-2 rounded-lg">
                                🆓 Free Personas Available (2)
                            </div>
                            <button class="persona-button w-full text-left p-4 border-2 border-blue-200 rounded-lg hover:border-blue-300 hover:bg-blue-50">
                                <div class="flex flex-col w-full">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="font-semibold text-blue-800">Composable Analysis Agent</span>
                                        <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">FREE</span>
                                    </div>
                                    <span class="text-sm text-gray-600 mb-2">Advanced data analysis and visualization</span>
                                    <span class="text-xs text-gray-500 mb-1">
                                        Capabilities: Statistical analysis, data visualization, report generation
                                    </span>
                                    <span class="text-xs text-gray-500">
                                        ⭐ 4.6/5.0 (89 reviews)
                                    </span>
                                </div>
                            </button>
                            <button class="persona-button w-full text-left p-4 border-2 border-blue-200 rounded-lg hover:border-blue-300 hover:bg-blue-50">
                                <div class="flex flex-col w-full">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="font-semibold text-blue-800">Composable Classifier</span>
                                        <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">FREE</span>
                                    </div>
                                    <span class="text-sm text-gray-600 mb-2">AI assistant for text classification tasks</span>
                                    <span class="text-xs text-gray-500 mb-1">
                                        Capabilities: Text classification, document analysis, content categorization
                                    </span>
                                    <span class="text-xs text-gray-500">
                                        ⭐ 4.7/5.0 (95 reviews)
                                    </span>
                                </div>
                            </button>
                        </div>

                        <!-- Paid Personas Section -->
                        <div class="space-y-2">
                            <div class="text-sm font-medium text-purple-700 bg-purple-50 px-3 py-2 rounded-lg">
                                💎 Premium Personas Available (3)
                            </div>
                            <button class="persona-button w-full text-left p-4 border-2 border-purple-200 rounded-lg hover:border-purple-300 hover:bg-purple-50">
                                <div class="flex flex-col w-full">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="font-semibold text-purple-800">Marketing Content Agent</span>
                                        <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">$29.00</span>
                                    </div>
                                    <span class="text-sm text-gray-600 mb-2">Professional marketing content creation</span>
                                    <span class="text-xs text-gray-500 mb-1">
                                        Capabilities: Content writing, social media, campaign planning
                                    </span>
                                    <span class="text-xs text-gray-500">
                                        ⭐ 4.9/5.0 (234 reviews)
                                    </span>
                                </div>
                            </button>
                            <button class="persona-button w-full text-left p-4 border-2 border-purple-200 rounded-lg hover:border-purple-300 hover:bg-purple-50">
                                <div class="flex flex-col w-full">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="font-semibold text-purple-800">Business Intelligence Agent</span>
                                        <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">$49.00</span>
                                    </div>
                                    <span class="text-sm text-gray-600 mb-2">Advanced business analytics and insights</span>
                                    <span class="text-xs text-gray-500 mb-1">
                                        Capabilities: Business analysis, KPI tracking, strategic planning
                                    </span>
                                    <span class="text-xs text-gray-500">
                                        ⭐ 4.8/5.0 (156 reviews)
                                    </span>
                                </div>
                            </button>
                            <button class="persona-button w-full text-left p-4 border-2 border-purple-200 rounded-lg hover:border-purple-300 hover:bg-purple-50">
                                <div class="flex flex-col w-full">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="font-semibold text-purple-800">Research Assistant Agent</span>
                                        <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">$39.00</span>
                                    </div>
                                    <span class="text-sm text-gray-600 mb-2">Comprehensive research and documentation</span>
                                    <span class="text-xs text-gray-500 mb-1">
                                        Capabilities: Research synthesis, citation management, report writing
                                    </span>
                                    <span class="text-xs text-gray-500">
                                        ⭐ 4.7/5.0 (178 reviews)
                                    </span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features List -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">✨ Enhanced Features</h2>
            <ul class="space-y-3 text-gray-700">
                <li class="flex items-start gap-3">
                    <span class="text-green-500 font-bold">✓</span>
                    <span><strong>Interactive Buttons:</strong> Click any persona to instantly switch and start chatting</span>
                </li>
                <li class="flex items-start gap-3">
                    <span class="text-green-500 font-bold">✓</span>
                    <span><strong>Category Grouping:</strong> Clear separation between owned, free, and paid personas</span>
                </li>
                <li class="flex items-start gap-3">
                    <span class="text-green-500 font-bold">✓</span>
                    <span><strong>Rich Information:</strong> Capabilities, ratings, reviews, and pricing displayed</span>
                </li>
                <li class="flex items-start gap-3">
                    <span class="text-green-500 font-bold">✓</span>
                    <span><strong>Smart Actions:</strong> Owned/free personas switch immediately, paid ones redirect to marketplace</span>
                </li>
                <li class="flex items-start gap-3">
                    <span class="text-green-500 font-bold">✓</span>
                    <span><strong>Visual Feedback:</strong> Color-coded categories and hover effects for better UX</span>
                </li>
                <li class="flex items-start gap-3">
                    <span class="text-green-500 font-bold">✓</span>
                    <span><strong>Mobile Responsive:</strong> Works seamlessly on all device sizes</span>
                </li>
            </ul>
        </div>

        <div class="mt-8 text-center text-gray-500">
            <p>This enhanced UI replaces the previous text-only persona listings with interactive, categorized buttons.</p>
        </div>
    </div>

    <script>
        // Add click handlers for demonstration
        document.querySelectorAll('.persona-button').forEach(button => {
            button.addEventListener('click', function() {
                const personaName = this.querySelector('.font-semibold').textContent;
                const category = this.querySelector('.text-xs.bg-green-100, .text-xs.bg-blue-100, .text-xs.bg-purple-100').textContent;
                
                if (category.includes('$')) {
                    alert(`Redirecting to marketplace to purchase ${personaName} for ${category}`);
                } else {
                    alert(`Switching to ${personaName}... Starting new conversation!`);
                }
            });
        });
    </script>
</body>
</html>
