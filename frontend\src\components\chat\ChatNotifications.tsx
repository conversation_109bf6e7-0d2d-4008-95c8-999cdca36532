import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  AlertCircle, 
  Info, 
  X, 
  Wifi, 
  WifiOff, 
  RefreshCw,
  Save,
  Cloud,
  AlertTriangle
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export type NotificationType = 'success' | 'error' | 'warning' | 'info' | 'connection' | 'save';

export interface ChatNotification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number; // in milliseconds, 0 for persistent
  action?: {
    label: string;
    onClick: () => void;
  };
  metadata?: Record<string, any>;
}

interface ChatNotificationsProps {
  notifications: ChatNotification[];
  onDismiss: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center';
  className?: string;
}

export const ChatNotifications: React.FC<ChatNotificationsProps> = ({
  notifications,
  onDismiss,
  position = 'top-right',
  className = ''
}) => {
  const [visibleNotifications, setVisibleNotifications] = useState<ChatNotification[]>([]);

  useEffect(() => {
    setVisibleNotifications(notifications);

    // Auto-dismiss notifications with duration
    notifications.forEach(notification => {
      if (notification.duration && notification.duration > 0) {
        setTimeout(() => {
          onDismiss(notification.id);
        }, notification.duration);
      }
    });
  }, [notifications, onDismiss]);

  const getNotificationConfig = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircle,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          iconColor: 'text-green-600'
        };
      case 'error':
        return {
          icon: AlertCircle,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-600'
        };
      case 'warning':
        return {
          icon: AlertTriangle,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-600'
        };
      case 'info':
        return {
          icon: Info,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600'
        };
      case 'connection':
        return {
          icon: Wifi,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-800',
          iconColor: 'text-gray-600'
        };
      case 'save':
        return {
          icon: Save,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600'
        };
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2';
    }
  };

  return (
    <div className={`fixed z-50 ${getPositionClasses()} ${className}`}>
      <div className="space-y-2 max-w-sm">
        <AnimatePresence>
          {visibleNotifications.map((notification) => {
            const config = getNotificationConfig(notification.type);
            const Icon = config.icon;

            return (
              <motion.div
                key={notification.id}
                initial={{ opacity: 0, y: -20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -20, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                <Card className={`p-4 shadow-lg ${config.bgColor} ${config.borderColor}`}>
                  <div className="flex items-start gap-3">
                    <Icon className={`h-5 w-5 mt-0.5 flex-shrink-0 ${config.iconColor}`} />
                    <div className="flex-1 min-w-0">
                      <h4 className={`font-medium ${config.textColor}`}>
                        {notification.title}
                      </h4>
                      {notification.message && (
                        <p className={`text-sm mt-1 ${config.textColor} opacity-90`}>
                          {notification.message}
                        </p>
                      )}
                      {notification.action && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`mt-2 h-6 text-xs ${config.textColor} hover:bg-white/50`}
                          onClick={notification.action.onClick}
                        >
                          {notification.action.label}
                        </Button>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`h-6 w-6 p-0 ${config.textColor} hover:bg-white/50`}
                      onClick={() => onDismiss(notification.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Hook for managing chat notifications
export const useChatNotifications = () => {
  const [notifications, setNotifications] = useState<ChatNotification[]>([]);

  const addNotification = (notification: Omit<ChatNotification, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification: ChatNotification = {
      ...notification,
      id,
      duration: notification.duration ?? 5000 // Default 5 seconds
    };
    
    setNotifications(prev => [...prev, newNotification]);
    return id;
  };

  const dismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  // Convenience methods
  const showSuccess = (title: string, message?: string, duration?: number) => {
    return addNotification({ type: 'success', title, message, duration });
  };

  const showError = (title: string, message?: string, duration?: number) => {
    return addNotification({ type: 'error', title, message, duration: duration ?? 0 }); // Errors persist by default
  };

  const showWarning = (title: string, message?: string, duration?: number) => {
    return addNotification({ type: 'warning', title, message, duration });
  };

  const showInfo = (title: string, message?: string, duration?: number) => {
    return addNotification({ type: 'info', title, message, duration });
  };

  const showConnectionStatus = (isConnected: boolean, message?: string) => {
    return addNotification({
      type: 'connection',
      title: isConnected ? 'Connected' : 'Disconnected',
      message: message || (isConnected ? 'Chat server connection restored' : 'Lost connection to chat server'),
      duration: isConnected ? 3000 : 0
    });
  };

  const showSaveStatus = (isSaving: boolean, message?: string) => {
    return addNotification({
      type: 'save',
      title: isSaving ? 'Saving...' : 'Saved',
      message: message || (isSaving ? 'Saving conversation...' : 'Conversation saved successfully'),
      duration: isSaving ? 0 : 2000
    });
  };

  return {
    notifications,
    addNotification,
    dismissNotification,
    clearAll,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConnectionStatus,
    showSaveStatus
  };
};

// Inline notification component for specific contexts
interface InlineNotificationProps {
  type: NotificationType;
  title: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  onDismiss?: () => void;
  className?: string;
}

export const InlineNotification: React.FC<InlineNotificationProps> = ({
  type,
  title,
  message,
  action,
  onDismiss,
  className = ''
}) => {
  const config = getNotificationConfig(type);
  const Icon = config.icon;

  const getNotificationConfig = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircle,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          iconColor: 'text-green-600'
        };
      case 'error':
        return {
          icon: AlertCircle,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-600'
        };
      case 'warning':
        return {
          icon: AlertTriangle,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-600'
        };
      case 'info':
        return {
          icon: Info,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600'
        };
      case 'connection':
        return {
          icon: Wifi,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-800',
          iconColor: 'text-gray-600'
        };
      case 'save':
        return {
          icon: Save,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600'
        };
    }
  };

  return (
    <Card className={`p-3 ${config.bgColor} ${config.borderColor} ${className}`}>
      <div className="flex items-start gap-3">
        <Icon className={`h-4 w-4 mt-0.5 flex-shrink-0 ${config.iconColor}`} />
        <div className="flex-1 min-w-0">
          <h4 className={`text-sm font-medium ${config.textColor}`}>
            {title}
          </h4>
          {message && (
            <p className={`text-xs mt-1 ${config.textColor} opacity-90`}>
              {message}
            </p>
          )}
          {action && (
            <Button
              variant="ghost"
              size="sm"
              className={`mt-2 h-6 text-xs ${config.textColor} hover:bg-white/50`}
              onClick={action.onClick}
            >
              {action.label}
            </Button>
          )}
        </div>
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            className={`h-5 w-5 p-0 ${config.textColor} hover:bg-white/50`}
            onClick={onDismiss}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </Card>
  );
};
