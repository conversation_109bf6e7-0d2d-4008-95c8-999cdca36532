"""
Analytics Widget Service for integrating analysis agent capabilities with dashboard widgets.
Provides data cleaning, visualization, sentiment analysis, and other analytics features.
"""

import logging
import asyncio
import pandas as pd
from typing import Dict, Any, List, Optional, Union, Literal
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field, ConfigDict

from ..database import get_db
from ..tools.unified_data_access_tool import UnifiedDataAccessTool, DataQuery, DataResult
from ..models.dashboard_customization import DashboardWidget, VisualizationType

logger = logging.getLogger(__name__)


class AnalyticsRequest(BaseModel):
    """Request for analytics processing."""
    model_config = ConfigDict(extra='forbid')
    
    widget_id: str = Field(..., description="Widget ID")
    analysis_type: Literal["cleaning", "visualization", "sentiment", "statistical", "predictive"] = Field(..., description="Type of analysis")
    data_source_id: str = Field(..., description="Data source ID")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Analysis parameters")
    cache_results: bool = Field(True, description="Whether to cache results")


class AnalyticsResult(BaseModel):
    """Result of analytics processing."""
    model_config = ConfigDict(extra='forbid')
    
    success: bool = Field(..., description="Processing success status")
    analysis_type: str = Field(..., description="Type of analysis performed")
    data: Optional[Dict[str, Any]] = Field(None, description="Processed data")
    visualization_config: Optional[Dict[str, Any]] = Field(None, description="Visualization configuration")
    insights: Optional[List[str]] = Field(None, description="Generated insights")
    recommendations: Optional[List[str]] = Field(None, description="Recommendations")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Processing metadata")
    error: Optional[str] = Field(None, description="Error message if failed")
    execution_time: float = Field(0.0, description="Processing time in seconds")


class DataCleaningConfig(BaseModel):
    """Configuration for data cleaning operations."""
    model_config = ConfigDict(extra='forbid')
    
    remove_duplicates: bool = Field(True, description="Remove duplicate rows")
    handle_missing_values: Literal["drop", "fill_mean", "fill_median", "fill_mode", "forward_fill"] = Field("drop", description="How to handle missing values")
    outlier_detection: bool = Field(True, description="Detect and handle outliers")
    outlier_method: Literal["iqr", "zscore", "isolation_forest"] = Field("iqr", description="Outlier detection method")
    normalize_text: bool = Field(True, description="Normalize text columns")
    standardize_dates: bool = Field(True, description="Standardize date formats")


class VisualizationConfig(BaseModel):
    """Configuration for visualization generation."""
    model_config = ConfigDict(extra='forbid')
    
    chart_type: VisualizationType = Field(..., description="Type of visualization")
    x_column: Optional[str] = Field(None, description="X-axis column")
    y_column: Optional[str] = Field(None, description="Y-axis column")
    color_column: Optional[str] = Field(None, description="Color grouping column")
    size_column: Optional[str] = Field(None, description="Size column for scatter plots")
    aggregation: Optional[Literal["sum", "mean", "count", "max", "min"]] = Field(None, description="Data aggregation method")
    group_by: Optional[List[str]] = Field(None, description="Columns to group by")
    filters: Optional[Dict[str, Any]] = Field(None, description="Data filters")


class AnalyticsWidgetService:
    """Service for integrating analytics capabilities with dashboard widgets."""
    
    def __init__(self, db: Session = None):
        """Initialize the analytics widget service."""
        self.db = db or next(get_db())
        self.data_access_tool = UnifiedDataAccessTool(self.db)
        self.cache = {}  # Simple in-memory cache
        self.cache_timestamps = {}
        logger.info("Analytics Widget Service initialized")
    
    async def process_analytics_request(self, request: AnalyticsRequest, user_id: int) -> AnalyticsResult:
        """Process an analytics request for a widget."""
        start_time = datetime.now()
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(request)
            if request.cache_results:
                cached_result = self._get_cached_result(cache_key)
                if cached_result:
                    return cached_result
            
            # Get widget configuration
            widget = self._get_widget(request.widget_id, user_id)
            if not widget:
                return AnalyticsResult(
                    success=False,
                    analysis_type=request.analysis_type,
                    error=f"Widget {request.widget_id} not found",
                    execution_time=(datetime.now() - start_time).total_seconds()
                )
            
            # Get data from source
            data_result = await self._get_widget_data(request.data_source_id, widget, user_id)
            if not data_result.success:
                return AnalyticsResult(
                    success=False,
                    analysis_type=request.analysis_type,
                    error=f"Failed to get data: {data_result.error}",
                    execution_time=(datetime.now() - start_time).total_seconds()
                )
            
            # Process based on analysis type
            result = await self._process_by_type(request, data_result, widget)
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            result.execution_time = execution_time
            
            # Cache successful results
            if result.success and request.cache_results:
                self._cache_result(cache_key, result)
            
            logger.info(f"Analytics processing completed in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error processing analytics request: {e}")
            return AnalyticsResult(
                success=False,
                analysis_type=request.analysis_type,
                error=str(e),
                execution_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def clean_data(self, data: List[Dict[str, Any]], config: DataCleaningConfig) -> Dict[str, Any]:
        """Clean and preprocess data."""
        try:
            df = pd.DataFrame(data)
            original_shape = df.shape
            cleaning_steps = []
            
            # Remove duplicates
            if config.remove_duplicates:
                before_count = len(df)
                df = df.drop_duplicates()
                after_count = len(df)
                if before_count != after_count:
                    cleaning_steps.append(f"Removed {before_count - after_count} duplicate rows")
            
            # Handle missing values
            if config.handle_missing_values != "drop":
                missing_before = df.isnull().sum().sum()
                if missing_before > 0:
                    if config.handle_missing_values == "fill_mean":
                        df = df.fillna(df.mean(numeric_only=True))
                    elif config.handle_missing_values == "fill_median":
                        df = df.fillna(df.median(numeric_only=True))
                    elif config.handle_missing_values == "fill_mode":
                        df = df.fillna(df.mode().iloc[0])
                    elif config.handle_missing_values == "forward_fill":
                        df = df.fillna(method='ffill')
                    
                    missing_after = df.isnull().sum().sum()
                    cleaning_steps.append(f"Filled {missing_before - missing_after} missing values using {config.handle_missing_values}")
            else:
                before_count = len(df)
                df = df.dropna()
                after_count = len(df)
                if before_count != after_count:
                    cleaning_steps.append(f"Dropped {before_count - after_count} rows with missing values")
            
            # Outlier detection
            if config.outlier_detection:
                outliers_removed = 0
                numeric_columns = df.select_dtypes(include=['number']).columns
                
                for col in numeric_columns:
                    before_count = len(df)
                    
                    if config.outlier_method == "iqr":
                        Q1 = df[col].quantile(0.25)
                        Q3 = df[col].quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - 1.5 * IQR
                        upper_bound = Q3 + 1.5 * IQR
                        df = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]
                    elif config.outlier_method == "zscore":
                        z_scores = abs((df[col] - df[col].mean()) / df[col].std())
                        df = df[z_scores < 3]
                    
                    after_count = len(df)
                    outliers_removed += before_count - after_count
                
                if outliers_removed > 0:
                    cleaning_steps.append(f"Removed {outliers_removed} outliers using {config.outlier_method} method")
            
            # Text normalization
            if config.normalize_text:
                text_columns = df.select_dtypes(include=['object']).columns
                for col in text_columns:
                    df[col] = df[col].astype(str).str.strip().str.lower()
                if len(text_columns) > 0:
                    cleaning_steps.append(f"Normalized {len(text_columns)} text columns")
            
            # Date standardization
            if config.standardize_dates:
                date_columns = df.select_dtypes(include=['datetime64']).columns
                for col in date_columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
                if len(date_columns) > 0:
                    cleaning_steps.append(f"Standardized {len(date_columns)} date columns")
            
            final_shape = df.shape
            
            return {
                "cleaned_data": df.to_dict('records'),
                "original_shape": original_shape,
                "final_shape": final_shape,
                "cleaning_steps": cleaning_steps,
                "data_quality_score": self._calculate_data_quality_score(df)
            }
            
        except Exception as e:
            logger.error(f"Error cleaning data: {e}")
            return {"error": str(e)}
    
    async def generate_visualization_config(self, data: List[Dict[str, Any]], config: VisualizationConfig) -> Dict[str, Any]:
        """Generate visualization configuration based on data and requirements."""
        try:
            df = pd.DataFrame(data)
            
            # Auto-detect best columns if not specified
            if not config.x_column or not config.y_column:
                numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
                categorical_columns = df.select_dtypes(include=['object']).columns.tolist()
                
                if not config.x_column:
                    config.x_column = categorical_columns[0] if categorical_columns else numeric_columns[0] if numeric_columns else df.columns[0]
                
                if not config.y_column and len(numeric_columns) > 0:
                    config.y_column = numeric_columns[0] if config.x_column != numeric_columns[0] else (numeric_columns[1] if len(numeric_columns) > 1 else numeric_columns[0])
            
            # Apply filters if specified
            if config.filters:
                for column, filter_value in config.filters.items():
                    if column in df.columns:
                        if isinstance(filter_value, dict):
                            for op, value in filter_value.items():
                                if op == "gt":
                                    df = df[df[column] > value]
                                elif op == "lt":
                                    df = df[df[column] < value]
                                elif op == "eq":
                                    df = df[df[column] == value]
                        else:
                            df = df[df[column] == filter_value]
            
            # Apply grouping and aggregation
            if config.group_by and config.aggregation:
                agg_dict = {config.y_column: config.aggregation}
                df = df.groupby(config.group_by).agg(agg_dict).reset_index()
            
            # Generate chart-specific configuration
            chart_config = {
                "type": config.chart_type,
                "data": df.to_dict('records'),
                "mapping": {
                    "x": config.x_column,
                    "y": config.y_column,
                },
                "options": {
                    "responsive": True,
                    "maintainAspectRatio": False,
                }
            }
            
            # Add color mapping if specified
            if config.color_column and config.color_column in df.columns:
                chart_config["mapping"]["color"] = config.color_column
            
            # Add size mapping for scatter plots
            if config.chart_type == "scatter" and config.size_column and config.size_column in df.columns:
                chart_config["mapping"]["size"] = config.size_column
            
            # Chart-specific configurations
            if config.chart_type in ["line", "area"]:
                chart_config["options"]["scales"] = {
                    "x": {"type": "category"},
                    "y": {"beginAtZero": True}
                }
            elif config.chart_type in ["bar", "column"]:
                chart_config["options"]["scales"] = {
                    "y": {"beginAtZero": True}
                }
            
            return chart_config
            
        except Exception as e:
            logger.error(f"Error generating visualization config: {e}")
            return {"error": str(e)}

    async def perform_sentiment_analysis(self, data: List[Dict[str, Any]], text_column: str) -> Dict[str, Any]:
        """Perform sentiment analysis on text data."""
        try:
            df = pd.DataFrame(data)

            if text_column not in df.columns:
                return {"error": f"Column '{text_column}' not found in data"}

            # Mock sentiment analysis (in production, integrate with actual sentiment analysis service)
            import random

            sentiments = []
            sentiment_scores = []

            for text in df[text_column]:
                # Mock sentiment classification
                sentiment = random.choice(['positive', 'negative', 'neutral'])
                score = random.uniform(-1, 1)  # -1 (very negative) to 1 (very positive)

                sentiments.append(sentiment)
                sentiment_scores.append(score)

            df['sentiment'] = sentiments
            df['sentiment_score'] = sentiment_scores

            # Calculate summary statistics
            sentiment_distribution = df['sentiment'].value_counts().to_dict()
            average_sentiment = df['sentiment_score'].mean()

            insights = []
            if average_sentiment > 0.2:
                insights.append("Overall sentiment is positive")
            elif average_sentiment < -0.2:
                insights.append("Overall sentiment is negative")
            else:
                insights.append("Overall sentiment is neutral")

            most_common_sentiment = max(sentiment_distribution, key=sentiment_distribution.get)
            insights.append(f"Most common sentiment: {most_common_sentiment} ({sentiment_distribution[most_common_sentiment]} occurrences)")

            return {
                "analyzed_data": df.to_dict('records'),
                "sentiment_distribution": sentiment_distribution,
                "average_sentiment_score": average_sentiment,
                "insights": insights,
                "total_analyzed": len(df)
            }

        except Exception as e:
            logger.error(f"Error performing sentiment analysis: {e}")
            return {"error": str(e)}

    async def generate_statistical_insights(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate statistical insights from data."""
        try:
            df = pd.DataFrame(data)
            numeric_columns = df.select_dtypes(include=['number']).columns

            if len(numeric_columns) == 0:
                return {"error": "No numeric columns found for statistical analysis"}

            insights = []
            statistics = {}

            for col in numeric_columns:
                col_stats = {
                    "mean": float(df[col].mean()),
                    "median": float(df[col].median()),
                    "std": float(df[col].std()),
                    "min": float(df[col].min()),
                    "max": float(df[col].max()),
                    "count": int(df[col].count()),
                    "null_count": int(df[col].isnull().sum())
                }

                statistics[col] = col_stats

                # Generate insights
                if col_stats["std"] > col_stats["mean"]:
                    insights.append(f"{col} shows high variability (std > mean)")

                if col_stats["null_count"] > 0:
                    null_percentage = (col_stats["null_count"] / len(df)) * 100
                    insights.append(f"{col} has {null_percentage:.1f}% missing values")

            # Correlation analysis
            correlations = {}
            if len(numeric_columns) > 1:
                corr_matrix = df[numeric_columns].corr()
                for i, col1 in enumerate(numeric_columns):
                    for j, col2 in enumerate(numeric_columns):
                        if i < j:  # Avoid duplicates
                            corr_value = corr_matrix.loc[col1, col2]
                            if abs(corr_value) > 0.7:
                                correlations[f"{col1}_vs_{col2}"] = float(corr_value)
                                strength = "strong" if abs(corr_value) > 0.8 else "moderate"
                                direction = "positive" if corr_value > 0 else "negative"
                                insights.append(f"{strength.capitalize()} {direction} correlation between {col1} and {col2} ({corr_value:.2f})")

            return {
                "statistics": statistics,
                "correlations": correlations,
                "insights": insights,
                "data_shape": df.shape,
                "numeric_columns": len(numeric_columns)
            }

        except Exception as e:
            logger.error(f"Error generating statistical insights: {e}")
            return {"error": str(e)}

    async def _process_by_type(self, request: AnalyticsRequest, data_result: DataResult, widget: DashboardWidget) -> AnalyticsResult:
        """Process analytics request based on type."""
        try:
            if request.analysis_type == "cleaning":
                config = DataCleaningConfig(**(request.parameters or {}))
                result_data = await self.clean_data(data_result.data, config)

                return AnalyticsResult(
                    success=True,
                    analysis_type=request.analysis_type,
                    data=result_data,
                    insights=result_data.get("cleaning_steps", []),
                    metadata={"data_quality_score": result_data.get("data_quality_score")}
                )

            elif request.analysis_type == "visualization":
                config = VisualizationConfig(**(request.parameters or {}))
                viz_config = await self.generate_visualization_config(data_result.data, config)

                return AnalyticsResult(
                    success=True,
                    analysis_type=request.analysis_type,
                    visualization_config=viz_config,
                    insights=[f"Generated {config.chart_type} visualization"],
                    metadata={"chart_type": config.chart_type}
                )

            elif request.analysis_type == "sentiment":
                text_column = request.parameters.get("text_column") if request.parameters else None
                if not text_column:
                    return AnalyticsResult(
                        success=False,
                        analysis_type=request.analysis_type,
                        error="text_column parameter is required for sentiment analysis"
                    )

                result_data = await self.perform_sentiment_analysis(data_result.data, text_column)

                return AnalyticsResult(
                    success=True,
                    analysis_type=request.analysis_type,
                    data=result_data,
                    insights=result_data.get("insights", []),
                    metadata={"sentiment_distribution": result_data.get("sentiment_distribution")}
                )

            elif request.analysis_type == "statistical":
                result_data = await self.generate_statistical_insights(data_result.data)

                return AnalyticsResult(
                    success=True,
                    analysis_type=request.analysis_type,
                    data=result_data,
                    insights=result_data.get("insights", []),
                    metadata={"statistics": result_data.get("statistics")}
                )

            else:
                return AnalyticsResult(
                    success=False,
                    analysis_type=request.analysis_type,
                    error=f"Unsupported analysis type: {request.analysis_type}"
                )

        except Exception as e:
            logger.error(f"Error processing {request.analysis_type} analysis: {e}")
            return AnalyticsResult(
                success=False,
                analysis_type=request.analysis_type,
                error=str(e)
            )

    async def _get_widget_data(self, data_source_id: str, widget: DashboardWidget, user_id: int) -> DataResult:
        """Get data for a widget from its data source."""
        try:
            # Create a data query based on widget configuration
            query = DataQuery(
                source_id=data_source_id,
                query_type="filter",
                query="{}",  # Default to no filter
                limit=1000,  # Reasonable limit for analytics
                cache_ttl=300
            )

            # Apply widget-specific data configuration if available
            if widget.data_config:
                if "query" in widget.data_config:
                    query.query = widget.data_config["query"]
                if "query_type" in widget.data_config:
                    query.query_type = widget.data_config["query_type"]
                if "limit" in widget.data_config:
                    query.limit = widget.data_config["limit"]

            return await self.data_access_tool.execute_query(query)

        except Exception as e:
            logger.error(f"Error getting widget data: {e}")
            return DataResult(success=False, error=str(e), total_rows=0, execution_time=0.0, cached=False)

    def _get_widget(self, widget_id: str, user_id: int) -> Optional[DashboardWidget]:
        """Get widget by ID and user."""
        try:
            return self.db.query(DashboardWidget).filter(
                DashboardWidget.id == widget_id,
                DashboardWidget.user_id == user_id
            ).first()
        except Exception as e:
            logger.error(f"Error getting widget: {e}")
            return None

    def _calculate_data_quality_score(self, df: pd.DataFrame) -> float:
        """Calculate a data quality score (0-1)."""
        try:
            total_cells = df.size
            missing_cells = df.isnull().sum().sum()
            duplicate_rows = df.duplicated().sum()

            # Simple quality score calculation
            missing_penalty = (missing_cells / total_cells) * 0.5
            duplicate_penalty = (duplicate_rows / len(df)) * 0.3

            quality_score = max(0.0, 1.0 - missing_penalty - duplicate_penalty)
            return round(quality_score, 3)

        except Exception:
            return 0.5  # Default score if calculation fails

    def _generate_cache_key(self, request: AnalyticsRequest) -> str:
        """Generate cache key for analytics request."""
        import hashlib
        import json

        key_data = {
            "widget_id": request.widget_id,
            "analysis_type": request.analysis_type,
            "data_source_id": request.data_source_id,
            "parameters": request.parameters
        }

        key_string = json.dumps(key_data, sort_keys=True)
        return f"analytics_{hashlib.md5(key_string.encode()).hexdigest()}"

    def _get_cached_result(self, cache_key: str) -> Optional[AnalyticsResult]:
        """Get cached analytics result if still valid."""
        if cache_key not in self.cache or cache_key not in self.cache_timestamps:
            return None

        cache_time = self.cache_timestamps[cache_key]
        if datetime.now() - cache_time > timedelta(minutes=15):  # 15-minute cache
            del self.cache[cache_key]
            del self.cache_timestamps[cache_key]
            return None

        return self.cache[cache_key]

    def _cache_result(self, cache_key: str, result: AnalyticsResult) -> None:
        """Cache analytics result."""
        self.cache[cache_key] = result
        self.cache_timestamps[cache_key] = datetime.now()

        # Simple cache cleanup (keep only last 50 entries)
        if len(self.cache) > 50:
            oldest_key = min(self.cache_timestamps.keys(), key=lambda k: self.cache_timestamps[k])
            del self.cache[oldest_key]
            del self.cache_timestamps[oldest_key]

    def clear_cache(self) -> None:
        """Clear analytics cache."""
        self.cache.clear()
        self.cache_timestamps.clear()
        logger.info("Analytics cache cleared")
