"""
Agent-based architecture for the Datagenius backend.

This package contains the implementation of the agent-based architecture
for the Datagenius backend, including the base agent interface, agent registry,
and specific agent implementations.
"""

import logging
import os
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

# Import component system first to ensure components are registered
logger.info("Importing component system")
from . import components

# Import tools
logger.info("Importing tools")
from . import tools

# Import agent modules to ensure their components are registered
logger.info("Importing agent modules")
from . import analysis_agent
from . import classification
from . import marketing_agent
from . import concierge_agent

# Import composable agent base class
logger.info("Importing composable agent base class")
from .composable import ComposableAgent

# Import registry after components are registered
logger.info("Importing agent registry")
from .registry import AgentRegistry

# Define paths
backend_dir = Path(__file__).parent.parent
personas_dir = os.path.join(backend_dir, "personas")
agent_configs_dir = os.path.join(Path(__file__).parent, "configs") # Path to agent implementation configs

# Import composable agent implementations first
from .classification.composable_agent import ComposableClassificationAgent
from .marketing_agent.composable_agent import ComposableMarketingAgent
from .analysis_agent.composable_agent import ComposableAnalysisAgent
from .concierge_agent.concierge import ConciergeAgent

# Load configurations from the personas directory (for metadata, etc.)
try:
    # Note: This call might populate _configurations in AgentRegistry, but not _registry if format differs
    AgentRegistry.load_configurations(personas_dir)
    logger.info(f"Loaded persona metadata configurations from {personas_dir}")
except Exception as e:
    logger.error(f"Error loading persona metadata configurations from {personas_dir}: {e}")

# Load agent implementation configurations from the agents/configs directory
try:
    AgentRegistry.load_configurations(agent_configs_dir)
    logger.info(f"Loaded agent implementation configurations from {agent_configs_dir}")
except Exception as e:
    logger.error(f"Error loading agent implementation configurations from {agent_configs_dir}: {e}")
    logger.info("Will proceed with manual agent registration fallback")

# Ensure critical agents are always registered, regardless of YAML loading
logger.info("Ensuring critical agents are registered")

# Register composable agents if they're not already registered
if "composable-ai" not in AgentRegistry.list_registered_personas():
    AgentRegistry.register("composable-ai", ComposableAgent)
    logger.info("Registered ComposableAgent")

if "composable-classifier-ai" not in AgentRegistry.list_registered_personas():
    AgentRegistry.register("composable-classifier-ai", ComposableClassificationAgent)
    logger.info("Registered ComposableClassificationAgent")

if "composable-marketing-ai" not in AgentRegistry.list_registered_personas():
    AgentRegistry.register("composable-marketing-ai", ComposableMarketingAgent)
    logger.info("Registered ComposableMarketingAgent")

if "composable-analysis-ai" not in AgentRegistry.list_registered_personas():
    AgentRegistry.register("composable-analysis-ai", ComposableAnalysisAgent)
    logger.info("Registered ComposableAnalysisAgent")

if "concierge-agent" not in AgentRegistry.list_registered_personas():
    AgentRegistry.register("concierge-agent", ConciergeAgent)
    logger.info("Registered ConciergeAgent")

logger.info("Critical agents registration complete")

# Log the registered personas
persona_ids = AgentRegistry.list_registered_personas()
logger.info(f"Registered personas: {persona_ids}")
