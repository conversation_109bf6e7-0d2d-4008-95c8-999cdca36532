"""
Production-Ready Data Generator for Testing and Development.

This module replaces mock data generators with realistic data generation
based on real-world patterns and industry standards.
"""

import logging
import random
import json
import csv
import io
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import uuid

logger = logging.getLogger(__name__)


class DataType(Enum):
    """Supported data types for generation."""
    BUSINESS_DATA = "business_data"
    CUSTOMER_DATA = "customer_data"
    SALES_DATA = "sales_data"
    MARKETING_DATA = "marketing_data"
    FINANCIAL_DATA = "financial_data"
    PRODUCT_DATA = "product_data"
    ANALYTICS_DATA = "analytics_data"


@dataclass
class DataGenerationConfig:
    """Configuration for data generation."""
    data_type: DataType
    rows: int = 100
    include_nulls: bool = True
    null_percentage: float = 0.05
    date_range_days: int = 365
    industry: str = "technology"
    business_size: str = "medium"
    locale: str = "en_US"


class ProductionDataGenerator:
    """Generate realistic data for testing and development."""
    
    def __init__(self):
        self.industries = {
            "technology": {
                "companies": ["TechCorp", "DataSoft", "CloudSys", "AIVentures", "DevTools"],
                "products": ["Software Platform", "Mobile App", "API Service", "Analytics Tool", "Cloud Solution"],
                "keywords": ["innovation", "digital", "automation", "scalable", "efficient"]
            },
            "healthcare": {
                "companies": ["HealthPlus", "MedCare", "WellnessCorp", "LifeScience", "CareFirst"],
                "products": ["Medical Device", "Health App", "Diagnostic Tool", "Treatment Plan", "Wellness Program"],
                "keywords": ["patient care", "health outcomes", "medical", "wellness", "treatment"]
            },
            "retail": {
                "companies": ["ShopMart", "RetailPro", "StyleCo", "MarketPlace", "BrandStore"],
                "products": ["Consumer Goods", "Fashion Items", "Electronics", "Home Decor", "Accessories"],
                "keywords": ["quality", "style", "value", "customer satisfaction", "trendy"]
            },
            "finance": {
                "companies": ["FinanceFirst", "InvestCorp", "BankPro", "WealthMgmt", "CreditSolutions"],
                "products": ["Investment Service", "Banking Solution", "Credit Product", "Insurance Plan", "Financial Tool"],
                "keywords": ["secure", "profitable", "reliable", "growth", "financial freedom"]
            }
        }
        
        self.business_sizes = {
            "startup": {"employees": (1, 10), "revenue": (0, 100000)},
            "small": {"employees": (11, 50), "revenue": (100000, 1000000)},
            "medium": {"employees": (51, 250), "revenue": (1000000, ********)},
            "large": {"employees": (251, 1000), "revenue": (********, *********)},
            "enterprise": {"employees": (1001, 10000), "revenue": (*********, *********0)}
        }
    
    def generate_data(
        self, 
        config: DataGenerationConfig,
        format_type: str = "csv"
    ) -> Union[str, Dict[str, Any], List[Dict[str, Any]]]:
        """
        Generate realistic data based on configuration.
        
        Args:
            config: Data generation configuration
            format_type: Output format (csv, json, dict)
            
        Returns:
            Generated data in specified format
        """
        try:
            # Generate data based on type
            if config.data_type == DataType.BUSINESS_DATA:
                data = self._generate_business_data(config)
            elif config.data_type == DataType.CUSTOMER_DATA:
                data = self._generate_customer_data(config)
            elif config.data_type == DataType.SALES_DATA:
                data = self._generate_sales_data(config)
            elif config.data_type == DataType.MARKETING_DATA:
                data = self._generate_marketing_data(config)
            elif config.data_type == DataType.FINANCIAL_DATA:
                data = self._generate_financial_data(config)
            elif config.data_type == DataType.PRODUCT_DATA:
                data = self._generate_product_data(config)
            elif config.data_type == DataType.ANALYTICS_DATA:
                data = self._generate_analytics_data(config)
            else:
                raise ValueError(f"Unsupported data type: {config.data_type}")
            
            # Apply null values if configured
            if config.include_nulls:
                data = self._apply_null_values(data, config.null_percentage)
            
            # Format output
            if format_type == "csv":
                return self._to_csv(data)
            elif format_type == "json":
                return json.dumps(data, indent=2, default=str)
            else:
                return data
                
        except Exception as e:
            logger.error(f"Error generating data: {e}")
            raise
    
    def _generate_business_data(self, config: DataGenerationConfig) -> List[Dict[str, Any]]:
        """Generate realistic business data."""
        industry_data = self.industries.get(config.industry, self.industries["technology"])
        size_data = self.business_sizes.get(config.business_size, self.business_sizes["medium"])
        
        data = []
        for i in range(config.rows):
            company_name = f"{random.choice(industry_data['companies'])} {random.randint(1, 999)}"
            
            record = {
                "company_id": str(uuid.uuid4()),
                "company_name": company_name,
                "industry": config.industry,
                "business_size": config.business_size,
                "employees": random.randint(*size_data["employees"]),
                "annual_revenue": random.randint(*size_data["revenue"]),
                "founded_year": random.randint(1990, 2023),
                "headquarters": random.choice([
                    "San Francisco, CA", "New York, NY", "Austin, TX", "Seattle, WA",
                    "Boston, MA", "Chicago, IL", "Los Angeles, CA", "Denver, CO"
                ]),
                "website": f"https://www.{company_name.lower().replace(' ', '')}.com",
                "description": f"Leading {config.industry} company specializing in {random.choice(industry_data['products']).lower()}",
                "primary_product": random.choice(industry_data["products"]),
                "target_market": random.choice([
                    "B2B Enterprise", "B2B SMB", "B2C Consumer", "B2B2C Marketplace"
                ]),
                "marketing_budget": random.randint(10000, 1000000),
                "customer_count": random.randint(100, 100000),
                "growth_rate": round(random.uniform(-10, 50), 2),
                "created_date": self._random_date(config.date_range_days),
                "last_updated": datetime.now().isoformat()
            }
            data.append(record)
        
        return data
    
    def _generate_customer_data(self, config: DataGenerationConfig) -> List[Dict[str, Any]]:
        """Generate realistic customer data."""
        first_names = ["John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa", "James", "Maria"]
        last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez"]
        
        data = []
        for i in range(config.rows):
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            
            record = {
                "customer_id": str(uuid.uuid4()),
                "first_name": first_name,
                "last_name": last_name,
                "email": f"{first_name.lower()}.{last_name.lower()}@email.com",
                "phone": f"+1-{random.randint(200, 999)}-{random.randint(200, 999)}-{random.randint(1000, 9999)}",
                "age": random.randint(18, 80),
                "gender": random.choice(["Male", "Female", "Other", "Prefer not to say"]),
                "location": random.choice([
                    "New York, NY", "Los Angeles, CA", "Chicago, IL", "Houston, TX",
                    "Phoenix, AZ", "Philadelphia, PA", "San Antonio, TX", "San Diego, CA"
                ]),
                "customer_since": self._random_date(config.date_range_days),
                "total_purchases": random.randint(1, 50),
                "total_spent": round(random.uniform(50, 5000), 2),
                "average_order_value": round(random.uniform(25, 500), 2),
                "last_purchase_date": self._random_date(90),
                "customer_segment": random.choice(["High Value", "Regular", "New", "At Risk", "Churned"]),
                "preferred_channel": random.choice(["Email", "SMS", "Phone", "Social Media", "Direct Mail"]),
                "satisfaction_score": random.randint(1, 10),
                "referral_source": random.choice([
                    "Organic Search", "Social Media", "Email Marketing", "Referral", 
                    "Paid Advertising", "Direct", "Content Marketing"
                ])
            }
            data.append(record)
        
        return data
    
    def _generate_sales_data(self, config: DataGenerationConfig) -> List[Dict[str, Any]]:
        """Generate realistic sales data."""
        products = self.industries.get(config.industry, self.industries["technology"])["products"]
        
        data = []
        for i in range(config.rows):
            sale_date = self._random_date(config.date_range_days)
            
            record = {
                "sale_id": str(uuid.uuid4()),
                "customer_id": str(uuid.uuid4()),
                "product_name": random.choice(products),
                "product_category": random.choice(["Premium", "Standard", "Basic", "Enterprise"]),
                "quantity": random.randint(1, 10),
                "unit_price": round(random.uniform(10, 1000), 2),
                "total_amount": 0,  # Will be calculated
                "discount_percentage": random.choice([0, 5, 10, 15, 20, 25]),
                "sale_date": sale_date,
                "sales_rep": random.choice([
                    "Alice Johnson", "Bob Smith", "Carol Davis", "David Wilson", "Eva Brown"
                ]),
                "sales_channel": random.choice([
                    "Online", "In-Store", "Phone", "Email", "Partner", "Direct Sales"
                ]),
                "payment_method": random.choice([
                    "Credit Card", "Debit Card", "PayPal", "Bank Transfer", "Cash", "Check"
                ]),
                "order_status": random.choice([
                    "Completed", "Pending", "Shipped", "Delivered", "Cancelled", "Refunded"
                ]),
                "region": random.choice(["North", "South", "East", "West", "Central"]),
                "lead_source": random.choice([
                    "Website", "Social Media", "Email Campaign", "Referral", "Trade Show", "Cold Call"
                ])
            }
            
            # Calculate total amount
            subtotal = record["quantity"] * record["unit_price"]
            discount_amount = subtotal * (record["discount_percentage"] / 100)
            record["total_amount"] = round(subtotal - discount_amount, 2)
            
            data.append(record)
        
        return data
    
    def _generate_marketing_data(self, config: DataGenerationConfig) -> List[Dict[str, Any]]:
        """Generate realistic marketing campaign data."""
        campaign_types = ["Email", "Social Media", "PPC", "Content Marketing", "SEO", "Display Ads", "Influencer"]
        
        data = []
        for i in range(config.rows):
            campaign_start = self._random_date(config.date_range_days)
            campaign_end = campaign_start + timedelta(days=random.randint(7, 90))
            
            impressions = random.randint(1000, 1000000)
            clicks = random.randint(10, impressions // 10)
            conversions = random.randint(1, clicks // 10)
            
            record = {
                "campaign_id": str(uuid.uuid4()),
                "campaign_name": f"{random.choice(campaign_types)} Campaign {i+1}",
                "campaign_type": random.choice(campaign_types),
                "start_date": campaign_start,
                "end_date": campaign_end,
                "budget": round(random.uniform(1000, 100000), 2),
                "spend": round(random.uniform(500, 95000), 2),
                "impressions": impressions,
                "clicks": clicks,
                "conversions": conversions,
                "click_through_rate": round((clicks / impressions) * 100, 2),
                "conversion_rate": round((conversions / clicks) * 100, 2),
                "cost_per_click": round(random.uniform(0.5, 10), 2),
                "cost_per_conversion": round(random.uniform(10, 200), 2),
                "return_on_ad_spend": round(random.uniform(1.5, 8), 2),
                "target_audience": random.choice([
                    "18-24 Tech Enthusiasts", "25-34 Professionals", "35-44 Decision Makers",
                    "45-54 Executives", "Small Business Owners", "Enterprise Buyers"
                ]),
                "platform": random.choice([
                    "Google Ads", "Facebook", "LinkedIn", "Twitter", "Instagram", 
                    "YouTube", "TikTok", "Email", "Website"
                ]),
                "objective": random.choice([
                    "Brand Awareness", "Lead Generation", "Sales", "Traffic", 
                    "Engagement", "App Downloads", "Video Views"
                ])
            }
            data.append(record)
        
        return data
    
    def _generate_financial_data(self, config: DataGenerationConfig) -> List[Dict[str, Any]]:
        """Generate realistic financial data."""
        data = []
        for i in range(config.rows):
            transaction_date = self._random_date(config.date_range_days)
            
            record = {
                "transaction_id": str(uuid.uuid4()),
                "account_id": f"ACC-{random.randint(100000, 999999)}",
                "transaction_date": transaction_date,
                "transaction_type": random.choice([
                    "Revenue", "Expense", "Investment", "Refund", "Fee", "Tax", "Salary"
                ]),
                "category": random.choice([
                    "Marketing", "Sales", "Operations", "R&D", "HR", "Legal", "IT", "Finance"
                ]),
                "amount": round(random.uniform(-50000, 100000), 2),
                "currency": "USD",
                "description": f"Transaction for {random.choice(['Q1', 'Q2', 'Q3', 'Q4'])} operations",
                "vendor": random.choice([
                    "Google", "Microsoft", "Salesforce", "Adobe", "AWS", "Slack", "Zoom", "HubSpot"
                ]),
                "payment_method": random.choice([
                    "Bank Transfer", "Credit Card", "Check", "ACH", "Wire Transfer"
                ]),
                "approval_status": random.choice(["Approved", "Pending", "Rejected"]),
                "department": random.choice([
                    "Marketing", "Sales", "Engineering", "HR", "Finance", "Operations"
                ])
            }
            data.append(record)
        
        return data
    
    def _generate_product_data(self, config: DataGenerationConfig) -> List[Dict[str, Any]]:
        """Generate realistic product data."""
        industry_data = self.industries.get(config.industry, self.industries["technology"])
        
        data = []
        for i in range(config.rows):
            record = {
                "product_id": str(uuid.uuid4()),
                "product_name": f"{random.choice(industry_data['products'])} {i+1}",
                "category": random.choice(["Premium", "Standard", "Basic", "Enterprise", "Starter"]),
                "price": round(random.uniform(9.99, 999.99), 2),
                "cost": round(random.uniform(5, 500), 2),
                "inventory_count": random.randint(0, 1000),
                "units_sold": random.randint(0, 10000),
                "rating": round(random.uniform(3.0, 5.0), 1),
                "review_count": random.randint(0, 1000),
                "launch_date": self._random_date(config.date_range_days),
                "status": random.choice(["Active", "Discontinued", "Coming Soon", "Out of Stock"]),
                "features": random.sample(industry_data["keywords"], 3),
                "target_market": random.choice([
                    "Enterprise", "SMB", "Consumer", "Developer", "Professional"
                ]),
                "competitor_products": random.randint(2, 10),
                "market_share": round(random.uniform(1, 25), 2)
            }
            data.append(record)
        
        return data
    
    def _generate_analytics_data(self, config: DataGenerationConfig) -> List[Dict[str, Any]]:
        """Generate realistic web analytics data."""
        data = []
        for i in range(config.rows):
            date = self._random_date(config.date_range_days)
            
            record = {
                "date": date,
                "page_views": random.randint(100, 10000),
                "unique_visitors": random.randint(50, 5000),
                "sessions": random.randint(75, 7500),
                "bounce_rate": round(random.uniform(20, 80), 2),
                "avg_session_duration": random.randint(30, 600),
                "pages_per_session": round(random.uniform(1.5, 8), 2),
                "new_visitors": round(random.uniform(20, 70), 2),
                "returning_visitors": round(random.uniform(30, 80), 2),
                "mobile_traffic": round(random.uniform(40, 80), 2),
                "desktop_traffic": round(random.uniform(20, 60), 2),
                "organic_traffic": round(random.uniform(30, 70), 2),
                "paid_traffic": round(random.uniform(10, 40), 2),
                "social_traffic": round(random.uniform(5, 25), 2),
                "direct_traffic": round(random.uniform(15, 35), 2),
                "conversion_rate": round(random.uniform(1, 10), 2),
                "revenue": round(random.uniform(100, 10000), 2)
            }
            data.append(record)
        
        return data
    
    def _apply_null_values(self, data: List[Dict[str, Any]], null_percentage: float) -> List[Dict[str, Any]]:
        """Apply null values to simulate real-world data quality issues."""
        if null_percentage <= 0:
            return data
        
        for record in data:
            for key in record.keys():
                if random.random() < null_percentage:
                    record[key] = None
        
        return data
    
    def _random_date(self, days_back: int) -> datetime:
        """Generate random date within specified range."""
        start_date = datetime.now() - timedelta(days=days_back)
        random_days = random.randint(0, days_back)
        return start_date + timedelta(days=random_days)
    
    def _to_csv(self, data: List[Dict[str, Any]]) -> str:
        """Convert data to CSV format."""
        if not data:
            return ""
        
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
        
        return output.getvalue()


# Global instance
production_data_generator = ProductionDataGenerator()
