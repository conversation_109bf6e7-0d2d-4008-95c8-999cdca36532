/**
 * Integration test suite for the hierarchical dashboard data source system
 * Tests the integration between dashboard-level data source management and 
 * the centralized data integration system.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  SystemDataSource,
  DashboardDataSourceAssignment,
  DashboardDataSourceAssignmentCreate,
  Dashboard
} from '@/types/dashboard-customization';

// Mock the API modules
vi.mock('@/lib/dataSourceApi', () => ({
  dataSourceApi: {
    getDataSources: vi.fn(),
    createFileDataSource: vi.fn(),
    createDatabaseDataSource: vi.fn(),
    createApiDataSource: vi.fn(),
    createMcpDataSource: vi.fn(),
  }
}));

vi.mock('@/lib/api', () => ({
  fileApi: {
    uploadFile: vi.fn(),
  }
}));

describe('Integrated Data Source System', () => {
  let mockSystemDataSources: SystemDataSource[];
  let mockDashboard: Dashboard;

  beforeEach(() => {
    // Setup mock system data sources (from Data Integration page)
    mockSystemDataSources = [
      {
        id: 'sys-ds-1',
        name: 'Customer Database',
        type: 'database',
        description: 'Main customer database',
        is_active: true,
        metadata: { host: 'localhost', database: 'customers' },
        source_metadata: { db_type: 'postgresql' },
        user_id: 1,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      },
      {
        id: 'sys-ds-2',
        name: 'Sales Data CSV',
        type: 'file',
        description: 'Uploaded sales data file',
        is_active: true,
        metadata: { file_size: 1024000, num_rows: 5000 },
        source_metadata: { file_id: 'file-123' },
        user_id: 1,
        created_at: '2024-01-02T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
      },
      {
        id: 'sys-ds-3',
        name: 'Analytics API',
        type: 'api',
        description: 'External analytics API',
        is_active: true,
        metadata: { endpoint: 'https://api.analytics.com' },
        source_metadata: { api_type: 'rest' },
        user_id: 1,
        created_at: '2024-01-03T00:00:00Z',
        updated_at: '2024-01-03T00:00:00Z',
      },
      {
        id: 'sys-ds-4',
        name: 'Inactive Source',
        type: 'file',
        description: 'Inactive data source',
        is_active: false,
        metadata: {},
        source_metadata: {},
        user_id: 1,
        created_at: '2024-01-04T00:00:00Z',
        updated_at: '2024-01-04T00:00:00Z',
      }
    ];

    // Setup mock dashboard with data source assignments
    mockDashboard = {
      id: 'dashboard-1',
      name: 'Sales Dashboard',
      description: 'Dashboard for sales analytics',
      is_default: false,
      is_public: false,
      layout_config: {},
      theme_config: {},
      refresh_interval: 300,
      tags: ['sales', 'analytics'],
      data_source_assignments: [
        {
          id: 'dsa-1',
          dashboard_id: 'dashboard-1',
          system_data_source_id: 'sys-ds-1',
          alias: 'Customer DB',
          is_active: true,
          created_at: '2024-01-05T00:00:00Z',
          updated_at: '2024-01-05T00:00:00Z',
          system_data_source: mockSystemDataSources[0],
        },
        {
          id: 'dsa-2',
          dashboard_id: 'dashboard-1',
          system_data_source_id: 'sys-ds-2',
          alias: undefined, // No alias, uses original name
          is_active: true,
          created_at: '2024-01-06T00:00:00Z',
          updated_at: '2024-01-06T00:00:00Z',
          system_data_source: mockSystemDataSources[1],
        }
      ],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-06T00:00:00Z',
      section_count: 2,
      widget_count: 4,
    };
  });

  describe('System Data Source Integration', () => {
    it('should distinguish between system-level and dashboard-level data sources', () => {
      // System data sources exist independently
      expect(mockSystemDataSources).toHaveLength(4);
      expect(mockSystemDataSources[0].id).toBe('sys-ds-1');
      
      // Dashboard assignments reference system data sources
      expect(mockDashboard.data_source_assignments).toHaveLength(2);
      expect(mockDashboard.data_source_assignments![0].system_data_source_id).toBe('sys-ds-1');
      expect(mockDashboard.data_source_assignments![1].system_data_source_id).toBe('sys-ds-2');
    });

    it('should support data source aliases at dashboard level', () => {
      const assignment1 = mockDashboard.data_source_assignments![0];
      const assignment2 = mockDashboard.data_source_assignments![1];
      
      // First assignment has an alias
      expect(assignment1.alias).toBe('Customer DB');
      expect(assignment1.system_data_source?.name).toBe('Customer Database');
      
      // Second assignment uses original name (no alias)
      expect(assignment2.alias).toBeUndefined();
      expect(assignment2.system_data_source?.name).toBe('Sales Data CSV');
    });

    it('should filter out inactive system data sources', () => {
      const activeSystemDataSources = mockSystemDataSources.filter(ds => ds.is_active);
      expect(activeSystemDataSources).toHaveLength(3);
      expect(activeSystemDataSources.map(ds => ds.id)).not.toContain('sys-ds-4');
    });

    it('should prevent duplicate assignments of the same system data source', () => {
      const existingAssignments = mockDashboard.data_source_assignments!.map(
        assignment => assignment.system_data_source_id
      );
      
      expect(existingAssignments).toEqual(['sys-ds-1', 'sys-ds-2']);
      
      // Available system data sources should exclude already assigned ones
      const availableDataSources = mockSystemDataSources.filter(
        ds => ds.is_active && !existingAssignments.includes(ds.id)
      );
      
      expect(availableDataSources).toHaveLength(1);
      expect(availableDataSources[0].id).toBe('sys-ds-3');
    });
  });

  describe('Data Source Assignment Management', () => {
    it('should create valid data source assignments', () => {
      const newAssignment: DashboardDataSourceAssignmentCreate = {
        system_data_source_id: 'sys-ds-3',
        alias: 'External Analytics',
        is_active: true,
      };

      expect(newAssignment.system_data_source_id).toBe('sys-ds-3');
      expect(newAssignment.alias).toBe('External Analytics');
      expect(newAssignment.is_active).toBe(true);
    });

    it('should support assignment updates', () => {
      const assignment = mockDashboard.data_source_assignments![0];
      const originalAlias = assignment.alias;
      
      // Update alias
      const updatedAssignment = {
        ...assignment,
        alias: 'Updated Customer Database',
        updated_at: new Date().toISOString(),
      };

      expect(updatedAssignment.alias).toBe('Updated Customer Database');
      expect(updatedAssignment.alias).not.toBe(originalAlias);
      expect(updatedAssignment.system_data_source_id).toBe(assignment.system_data_source_id);
    });

    it('should maintain referential integrity with system data sources', () => {
      const assignment = mockDashboard.data_source_assignments![0];
      const systemDataSource = mockSystemDataSources.find(
        ds => ds.id === assignment.system_data_source_id
      );

      expect(systemDataSource).toBeDefined();
      expect(systemDataSource!.id).toBe(assignment.system_data_source_id);
      expect(assignment.system_data_source).toEqual(systemDataSource);
    });
  });

  describe('File Upload Integration', () => {
    it('should support file upload workflow', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      
      // Mock file upload response
      const mockUploadResponse = {
        id: 'file-456',
        filename: 'test.csv',
        file_size: 1024,
        num_rows: 100,
        columns: ['col1', 'col2'],
      };

      // Mock data source creation response
      const mockDataSourceResponse: SystemDataSource = {
        id: 'sys-ds-5',
        name: 'test.csv',
        type: 'file',
        description: 'Uploaded file: test.csv',
        is_active: true,
        metadata: mockUploadResponse,
        source_metadata: { file_id: 'file-456' },
        user_id: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Simulate the upload workflow
      expect(mockFile.name).toBe('test.csv');
      expect(mockUploadResponse.id).toBe('file-456');
      expect(mockDataSourceResponse.source_metadata.file_id).toBe('file-456');
      expect(mockDataSourceResponse.type).toBe('file');
    });

    it('should auto-create data source assignment after file upload', () => {
      const newSystemDataSource: SystemDataSource = {
        id: 'sys-ds-5',
        name: 'uploaded-file.csv',
        type: 'file',
        description: 'Newly uploaded file',
        is_active: true,
        metadata: {},
        source_metadata: { file_id: 'file-789' },
        user_id: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const autoAssignment: DashboardDataSourceAssignmentCreate = {
        system_data_source_id: newSystemDataSource.id,
        alias: newSystemDataSource.name,
        is_active: true,
      };

      expect(autoAssignment.system_data_source_id).toBe('sys-ds-5');
      expect(autoAssignment.alias).toBe('uploaded-file.csv');
    });
  });

  describe('Widget Data Source Configuration', () => {
    it('should reference dashboard data source assignments in widget config', () => {
      const widgetDataConfig = {
        dashboard_data_source_assignment_id: 'dsa-1',
        query: 'SELECT * FROM customers WHERE active = true',
        filters: { status: 'active' },
        limit: 100,
      };

      expect(widgetDataConfig.dashboard_data_source_assignment_id).toBe('dsa-1');
      
      // Verify the assignment exists
      const assignment = mockDashboard.data_source_assignments!.find(
        a => a.id === widgetDataConfig.dashboard_data_source_assignment_id
      );
      
      expect(assignment).toBeDefined();
      expect(assignment!.system_data_source?.type).toBe('database');
    });

    it('should support different data source types in widget configuration', () => {
      const databaseWidgetConfig = {
        dashboard_data_source_assignment_id: 'dsa-1', // Database source
        query: 'SELECT COUNT(*) FROM customers',
        aggregation: 'count',
      };

      const fileWidgetConfig = {
        dashboard_data_source_assignment_id: 'dsa-2', // File source
        filters: { date_range: '30d' },
        groupBy: ['category'],
        limit: 50,
      };

      expect(databaseWidgetConfig.query).toContain('SELECT');
      expect(fileWidgetConfig.groupBy).toEqual(['category']);
    });
  });

  describe('Data Consistency and Validation', () => {
    it('should maintain consistency between system and dashboard data sources', () => {
      // All dashboard assignments should reference valid system data sources
      const systemDataSourceIds = mockSystemDataSources.map(ds => ds.id);
      const assignmentSourceIds = mockDashboard.data_source_assignments!.map(
        assignment => assignment.system_data_source_id
      );

      assignmentSourceIds.forEach(id => {
        expect(systemDataSourceIds).toContain(id);
      });
    });

    it('should handle system data source deletion gracefully', () => {
      // If a system data source is deleted, assignments should be marked as invalid
      const deletedSystemDataSourceId = 'sys-ds-1';
      const remainingSystemDataSources = mockSystemDataSources.filter(
        ds => ds.id !== deletedSystemDataSourceId
      );

      const affectedAssignments = mockDashboard.data_source_assignments!.filter(
        assignment => assignment.system_data_source_id === deletedSystemDataSourceId
      );

      expect(affectedAssignments).toHaveLength(1);
      expect(remainingSystemDataSources.map(ds => ds.id)).not.toContain(deletedSystemDataSourceId);
    });

    it('should validate data source types and configurations', () => {
      const databaseSource = mockSystemDataSources.find(ds => ds.type === 'database');
      const fileSource = mockSystemDataSources.find(ds => ds.type === 'file');
      const apiSource = mockSystemDataSources.find(ds => ds.type === 'api');

      expect(databaseSource?.source_metadata).toHaveProperty('db_type');
      expect(fileSource?.source_metadata).toHaveProperty('file_id');
      expect(apiSource?.source_metadata).toHaveProperty('api_type');
    });
  });
});
