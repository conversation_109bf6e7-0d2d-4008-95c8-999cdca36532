"""
Configuration settings for the Datagenius backend.

This module provides configuration settings for the Datagenius backend,
using a hierarchical configuration system with Pydantic validation.

For backward compatibility, this module exposes configuration values
as module-level variables. The new hierarchical configuration system
is available through the settings package.
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# Import the new configuration system
try:
    from .settings.manager import get_config

    # Load the hierarchical configuration
    _app_config = get_config()

    # Expose configuration values for backward compatibility
    # Database configuration
    DATABASE_URL = _app_config.database.url
    DATABASE_ECHO = _app_config.database.echo

    # JWT configuration
    JWT_SECRET_KEY = _app_config.security.jwt_secret_key
    JWT_ALGORITHM = _app_config.security.jwt_algorithm
    ACCESS_TOKEN_EXPIRE_MINUTES = _app_config.security.access_token_expire_minutes
    REFRESH_TOKEN_EXPIRE_DAYS = _app_config.security.refresh_token_expire_days

    # Security configuration
    MAX_REFRESH_COUNT = _app_config.security.max_refresh_count
    MAX_CONCURRENT_SESSIONS = _app_config.security.max_concurrent_sessions
    ENFORCE_IP_VALIDATION = _app_config.security.enforce_ip_validation
    IP_CHANGE_LOCKOUT = _app_config.security.ip_change_lockout

    # Redis configuration
    REDIS_URL = _app_config.redis.url

    # File upload configuration
    UPLOAD_DIR = _app_config.files.upload_dir
    MAX_UPLOAD_SIZE = _app_config.security.max_upload_size

    logger.info("Using hierarchical configuration system")

except ImportError as e:
    logger.warning(f"Failed to import hierarchical configuration: {e}")
    logger.warning("Falling back to environment variable configuration")

    # Fallback to environment variables for backward compatibility
    DATABASE_URL = os.getenv("DATABASE_URL")
    DATABASE_ECHO = os.getenv("DATABASE_ECHO", "false").lower() == "true"

    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-for-development-only")
    JWT_ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

    MAX_REFRESH_COUNT = int(os.getenv("MAX_REFRESH_COUNT", "100"))
    MAX_CONCURRENT_SESSIONS = int(os.getenv("MAX_CONCURRENT_SESSIONS", "5"))
    ENFORCE_IP_VALIDATION = os.getenv("ENFORCE_IP_VALIDATION", "false").lower() == "true"
    IP_CHANGE_LOCKOUT = os.getenv("IP_CHANGE_LOCKOUT", "false").lower() == "true"

    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    UPLOAD_DIR = os.getenv("UPLOAD_DIR", "temp_uploads")
    MAX_UPLOAD_SIZE = int(os.getenv("MAX_UPLOAD_SIZE", "10485760"))

try:
    # LLM provider configuration from hierarchical config
    _llm_providers = _app_config.llm.providers

    GROQ_API_KEY = _llm_providers.get("groq", type("", (), {"api_key": ""})).api_key
    OPENAI_API_KEY = _llm_providers.get("openai", type("", (), {"api_key": ""})).api_key
    GEMINI_API_KEY = _llm_providers.get("gemini", type("", (), {"api_key": ""})).api_key
    OPENROUTER_API_KEY = _llm_providers.get("openrouter", type("", (), {"api_key": ""})).api_key

    # Google OAuth configuration
    GOOGLE_CLIENT_ID = _app_config.google_client_id
    GOOGLE_CLIENT_SECRET = _app_config.google_client_secret
    GOOGLE_REDIRECT_URI = _app_config.google_redirect_uri

    # LLM endpoint configuration
    GROQ_ENDPOINT = _llm_providers.get("groq", type("", (), {"endpoint": "https://api.groq.com/openai/v1"})).endpoint
    OPENAI_ENDPOINT = _llm_providers.get("openai", type("", (), {"endpoint": "https://api.openai.com/v1"})).endpoint
    GEMINI_ENDPOINT = _llm_providers.get("gemini", type("", (), {"endpoint": "https://generativelanguage.googleapis.com"})).endpoint
    OPENROUTER_ENDPOINT = _llm_providers.get("openrouter", type("", (), {"endpoint": "https://openrouter.ai/api/v1"})).endpoint
    OLLAMA_ENDPOINT = _llm_providers.get("ollama", type("", (), {"endpoint": "http://localhost:11434"})).endpoint
    REQUESTY_ENDPOINT = _llm_providers.get("requesty", type("", (), {"endpoint": "https://router.requesty.ai/v1"})).endpoint

except (NameError, AttributeError):
    # Fallback to environment variables
    GROQ_API_KEY = os.getenv("GROQ_API_KEY", "")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "")

    GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID", "")
    GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET", "")
    GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:5173/auth/google/callback")

    GROQ_ENDPOINT = os.getenv("GROQ_ENDPOINT", "https://api.groq.com/openai/v1")
    OPENAI_ENDPOINT = os.getenv("OPENAI_ENDPOINT", "https://api.openai.com/v1")
    GEMINI_ENDPOINT = os.getenv("GEMINI_ENDPOINT", "https://generativelanguage.googleapis.com")
    OPENROUTER_ENDPOINT = os.getenv("OPENROUTER_ENDPOINT", "https://openrouter.ai/api/v1")
    OLLAMA_ENDPOINT = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
    REQUESTY_ENDPOINT = os.getenv("REQUESTY_ENDPOINT", "https://router.requesty.ai/v1")

try:
    # mem0ai configuration from hierarchical config
    MEM0_API_KEY = _app_config.vector.mem0_api_key
    MEM0_ENDPOINT = _app_config.vector.mem0_endpoint
    MEM0_SELF_HOSTED = _app_config.vector.mem0_self_hosted
    MEM0_DEFAULT_TTL = _app_config.vector.mem0_default_ttl
    MEM0_MAX_MEMORIES = _app_config.vector.mem0_max_memories
    MEM0_MEMORY_THRESHOLD = _app_config.vector.mem0_memory_threshold

    # Qdrant configuration
    QDRANT_HOST = _app_config.vector.qdrant_host
    QDRANT_PORT = _app_config.vector.qdrant_port

    # Adaptive Chunking Configuration
    CHUNKING_PERFORMANCE_PROFILE = _app_config.files.chunking_performance_profile
    CHUNKING_USE_ADAPTIVE = _app_config.files.chunking_use_adaptive
    CHUNKING_ENABLE_CACHING = _app_config.files.chunking_enable_caching
    CHUNKING_BATCH_SIZE = _app_config.files.chunking_batch_size
    CHUNKING_PARALLEL_WORKERS = _app_config.files.chunking_parallel_workers

    # Frontend URL for redirects
    FRONTEND_URL = _app_config.frontend_url

    # Email configuration
    EMAIL_ENABLED = _app_config.email.enabled
    EMAIL_SENDER = _app_config.email.sender
    EMAIL_SMTP_SERVER = _app_config.email.smtp_server
    EMAIL_SMTP_PORT = _app_config.email.smtp_port
    EMAIL_SMTP_USER = _app_config.email.smtp_user
    EMAIL_SMTP_PASSWORD = _app_config.email.smtp_password
    EMAIL_USE_TLS = _app_config.email.use_tls

    # CORS configuration
    CORS_ORIGINS = _app_config.security.cors_origins

    # Debug configuration
    DEBUG = _app_config.debug

    # Application configuration
    APP_NAME = _app_config.name
    APP_VERSION = _app_config.version
    APP_DESCRIPTION = _app_config.description

except (NameError, AttributeError):
    # Fallback to environment variables
    MEM0_API_KEY = os.getenv("MEM0_API_KEY", "")
    MEM0_ENDPOINT = os.getenv("MEM0_ENDPOINT", "")
    MEM0_SELF_HOSTED = os.getenv("MEM0_SELF_HOSTED", "false").lower() == "true"
    MEM0_DEFAULT_TTL = int(os.getenv("MEM0_DEFAULT_TTL", "2592000"))
    MEM0_MAX_MEMORIES = int(os.getenv("MEM0_MAX_MEMORIES", "1000"))
    MEM0_MEMORY_THRESHOLD = float(os.getenv("MEM0_MEMORY_THRESHOLD", "0.7"))

    QDRANT_HOST = os.getenv("QDRANT_HOST", "localhost")
    QDRANT_PORT = int(os.getenv("QDRANT_PORT", "6333"))

    CHUNKING_PERFORMANCE_PROFILE = os.getenv("CHUNKING_PERFORMANCE_PROFILE", "balanced")
    CHUNKING_USE_ADAPTIVE = os.getenv("CHUNKING_USE_ADAPTIVE", "true").lower() == "true"
    CHUNKING_ENABLE_CACHING = os.getenv("CHUNKING_ENABLE_CACHING", "true").lower() == "true"
    CHUNKING_BATCH_SIZE = int(os.getenv("CHUNKING_BATCH_SIZE", "16"))
    CHUNKING_PARALLEL_WORKERS = int(os.getenv("CHUNKING_PARALLEL_WORKERS", "3"))

    FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:5173")

    EMAIL_ENABLED = os.getenv("EMAIL_ENABLED", "false").lower() == "true"
    EMAIL_SENDER = os.getenv("EMAIL_SENDER", "<EMAIL>")
    EMAIL_SMTP_SERVER = os.getenv("EMAIL_SMTP_SERVER", "smtp.example.com")
    EMAIL_SMTP_PORT = int(os.getenv("EMAIL_SMTP_PORT", "587"))
    EMAIL_SMTP_USER = os.getenv("EMAIL_SMTP_USER", "")
    EMAIL_SMTP_PASSWORD = os.getenv("EMAIL_SMTP_PASSWORD", "")
    EMAIL_USE_TLS = os.getenv("EMAIL_USE_TLS", "true").lower() == "true"

    CORS_ORIGINS = os.getenv("CORS_ORIGINS", "*").split(",")

    DEBUG = os.getenv("DEBUG", "false").lower() == "true"

    APP_NAME = os.getenv("APP_NAME", "Datagenius")
    APP_VERSION = os.getenv("APP_VERSION", "1.0.0")
    APP_DESCRIPTION = os.getenv("APP_DESCRIPTION", "AI-powered data analysis platform")

# Log configuration settings
try:
    # Use hierarchical config summary if available
    config_summary = _app_config.config_summary
    logger.info("Loaded hierarchical configuration settings:")
    logger.info(f"App: {config_summary['app']}")
    logger.info(f"Database: {config_summary['database']}")
    logger.info(f"Security: {config_summary['security']}")
    logger.info(f"LLM: {config_summary['llm']}")

except (NameError, AttributeError):
    # Fallback to individual variable logging
    logger.info("Loaded configuration settings from environment variables:")
    logger.info(f"DATABASE_URL: {DATABASE_URL.split('://')[0] if DATABASE_URL else 'Not configured'}://*****")
    logger.info(f"DATABASE_ECHO: {DATABASE_ECHO}")
    logger.info(f"JWT_ALGORITHM: {JWT_ALGORITHM}")
    logger.info(f"ACCESS_TOKEN_EXPIRE_MINUTES: {ACCESS_TOKEN_EXPIRE_MINUTES}")
    logger.info(f"REFRESH_TOKEN_EXPIRE_DAYS: {REFRESH_TOKEN_EXPIRE_DAYS}")
    logger.info(f"UPLOAD_DIR: {UPLOAD_DIR}")
    logger.info(f"MAX_UPLOAD_SIZE: {MAX_UPLOAD_SIZE}")
    logger.info(f"EMAIL_ENABLED: {EMAIL_ENABLED}")
    logger.info(f"DEBUG: {DEBUG}")
    logger.info(f"APP_NAME: {APP_NAME}")
    logger.info(f"APP_VERSION: {APP_VERSION}")
    logger.info(f"GOOGLE_CLIENT_ID: {'Configured' if GOOGLE_CLIENT_ID else 'Not configured'}")
    logger.info(f"GOOGLE_REDIRECT_URI: {GOOGLE_REDIRECT_URI}")
    logger.info(f"MEM0_API_KEY: {'Configured' if MEM0_API_KEY else 'Not configured'}")
    logger.info(f"MEM0_SELF_HOSTED: {MEM0_SELF_HOSTED}")
    logger.info(f"MEM0_DEFAULT_TTL: {MEM0_DEFAULT_TTL}")
    logger.info(f"CHUNKING_PERFORMANCE_PROFILE: {CHUNKING_PERFORMANCE_PROFILE}")
    logger.info(f"CHUNKING_USE_ADAPTIVE: {CHUNKING_USE_ADAPTIVE}")
    logger.info(f"CHUNKING_ENABLE_CACHING: {CHUNKING_ENABLE_CACHING}")
    logger.info(f"CHUNKING_BATCH_SIZE: {CHUNKING_BATCH_SIZE}")
    logger.info(f"CHUNKING_PARALLEL_WORKERS: {CHUNKING_PARALLEL_WORKERS}")


# Export the hierarchical configuration for direct access
try:
    app_config = _app_config
except NameError:
    app_config = None


def get_settings():
    """
    Get configuration settings for backward compatibility with tests.

    Returns:
        Configuration object with all settings as attributes
    """
    class Settings:
        def __init__(self):
            # Database configuration
            self.database_url = DATABASE_URL
            self.database_echo = DATABASE_ECHO

            # JWT configuration
            self.jwt_secret_key = JWT_SECRET_KEY
            self.jwt_algorithm = JWT_ALGORITHM
            self.access_token_expire_minutes = ACCESS_TOKEN_EXPIRE_MINUTES
            self.refresh_token_expire_days = REFRESH_TOKEN_EXPIRE_DAYS

            # Security configuration
            self.max_refresh_count = MAX_REFRESH_COUNT
            self.max_concurrent_sessions = MAX_CONCURRENT_SESSIONS
            self.enforce_ip_validation = ENFORCE_IP_VALIDATION
            self.ip_change_lockout = IP_CHANGE_LOCKOUT

            # Redis configuration
            self.redis_url = REDIS_URL

            # File upload configuration
            self.upload_dir = UPLOAD_DIR
            self.max_upload_size = MAX_UPLOAD_SIZE

            # LLM provider configuration
            self.groq_api_key = GROQ_API_KEY
            self.openai_api_key = OPENAI_API_KEY
            self.gemini_api_key = GEMINI_API_KEY
            self.openrouter_api_key = OPENROUTER_API_KEY

            # Google OAuth configuration
            self.google_client_id = GOOGLE_CLIENT_ID
            self.google_client_secret = GOOGLE_CLIENT_SECRET
            self.google_redirect_uri = GOOGLE_REDIRECT_URI

            # LLM endpoint configuration
            self.groq_endpoint = GROQ_ENDPOINT
            self.openai_endpoint = OPENAI_ENDPOINT
            self.gemini_endpoint = GEMINI_ENDPOINT
            self.openrouter_endpoint = OPENROUTER_ENDPOINT
            self.ollama_endpoint = OLLAMA_ENDPOINT
            self.requesty_endpoint = REQUESTY_ENDPOINT

            # mem0ai configuration
            self.mem0_api_key = MEM0_API_KEY
            self.mem0_endpoint = MEM0_ENDPOINT
            self.mem0_self_hosted = MEM0_SELF_HOSTED
            self.mem0_default_ttl = MEM0_DEFAULT_TTL
            self.mem0_max_memories = MEM0_MAX_MEMORIES
            self.mem0_memory_threshold = MEM0_MEMORY_THRESHOLD

            # Qdrant configuration
            self.qdrant_host = QDRANT_HOST
            self.qdrant_port = QDRANT_PORT

            # Chunking configuration
            self.chunking_performance_profile = CHUNKING_PERFORMANCE_PROFILE
            self.chunking_use_adaptive = CHUNKING_USE_ADAPTIVE
            self.chunking_enable_caching = CHUNKING_ENABLE_CACHING
            self.chunking_batch_size = CHUNKING_BATCH_SIZE
            self.chunking_parallel_workers = CHUNKING_PARALLEL_WORKERS

            # Frontend URL
            self.frontend_url = FRONTEND_URL

            # Email configuration
            self.email_enabled = EMAIL_ENABLED
            self.email_sender = EMAIL_SENDER
            self.email_smtp_server = EMAIL_SMTP_SERVER
            self.email_smtp_port = EMAIL_SMTP_PORT
            self.email_smtp_user = EMAIL_SMTP_USER
            self.email_smtp_password = EMAIL_SMTP_PASSWORD
            self.email_use_tls = EMAIL_USE_TLS

            # CORS configuration
            self.cors_origins = CORS_ORIGINS

            # Debug configuration
            self.debug = DEBUG

            # Application configuration
            self.app_name = APP_NAME
            self.app_version = APP_VERSION
            self.app_description = APP_DESCRIPTION

    return Settings()
