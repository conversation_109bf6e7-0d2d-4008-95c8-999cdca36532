/**
 * Technical Controls Panel Component
 * 
 * Provides advanced technical controls for power users in Advanced Mode.
 * Features raw data access, custom queries, performance metrics, and code editor.
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Terminal,
  Code,
  Database,
  Activity,
  Settings,
  Play,
  Save,
  Download,
  Upload,
  RefreshCw,
  Eye,
  EyeOff,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  MemoryStick,
  Cpu,
  HardDrive,
  Network,
  Wrench,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useModeConfig } from '@/stores/dashboard-mode-store';

interface TechnicalControlsPanelProps {
  className?: string;
  on_query_execute?: (query: string, type: 'sql' | 'javascript' | 'python') => void;
  on_performance_action?: (action: string) => void;
  on_data_access?: (action: string, params?: any) => void;
}

export const TechnicalControlsPanel: React.FC<TechnicalControlsPanelProps> = ({
  className,
  on_query_execute,
  on_performance_action,
  on_data_access,
}) => {
  const [active_tab, set_active_tab] = useState('query');
  const [sql_query, set_sql_query] = useState('');
  const [javascript_code, set_javascript_code] = useState('');
  const [python_code, set_python_code] = useState('');
  const [show_raw_data, set_show_raw_data] = useState(false);
  const [performance_monitoring, set_performance_monitoring] = useState(true);
  const [query_results, set_query_results] = useState<any>(null);
  const [is_executing, set_is_executing] = useState(false);

  const { advanced_config } = useModeConfig();

  const handle_execute_query = async (type: 'sql' | 'javascript' | 'python') => {
    set_is_executing(true);
    try {
      let query = '';
      switch (type) {
        case 'sql':
          query = sql_query;
          break;
        case 'javascript':
          query = javascript_code;
          break;
        case 'python':
          query = python_code;
          break;
      }
      
      on_query_execute?.(query, type);
      
      // Simulate execution results
      await new Promise(resolve => setTimeout(resolve, 1000));
      set_query_results({
        type,
        query,
        rows_affected: 42,
        execution_time: '0.23s',
        status: 'success',
      });
    } catch (error) {
      console.error('Query execution error:', error);
    } finally {
      set_is_executing(false);
    }
  };

  const performance_metrics = {
    cpu_usage: 23,
    memory_usage: 45,
    disk_io: 12,
    network_io: 8,
    query_performance: 'Good',
    cache_hit_ratio: 87,
    active_connections: 15,
    avg_response_time: '0.12s',
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Wrench className="h-5 w-5 text-purple-500" />
            <span>Technical Controls</span>
            <Badge variant="secondary">Advanced Mode</Badge>
          </CardTitle>
          <CardDescription>
            Advanced tools for power users: custom queries, raw data access, and performance monitoring.
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs value={active_tab} onValueChange={set_active_tab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="query">Query Editor</TabsTrigger>
          <TabsTrigger value="data">Raw Data</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Query Editor Tab */}
        <TabsContent value="query" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="h-5 w-5" />
                <span>Custom Query Editor</span>
              </CardTitle>
              <CardDescription>
                Execute custom SQL queries, JavaScript, or Python code against your data.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Tabs defaultValue="sql">
                <TabsList>
                  <TabsTrigger value="sql">SQL</TabsTrigger>
                  <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                  <TabsTrigger value="python">Python</TabsTrigger>
                </TabsList>

                <TabsContent value="sql" className="space-y-4">
                  <div className="space-y-2">
                    <Label>SQL Query</Label>
                    <Textarea
                      value={sql_query}
                      onChange={(e) => set_sql_query(e.target.value)}
                      placeholder="SELECT * FROM dashboard_data WHERE..."
                      className="font-mono text-sm min-h-[200px]"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => handle_execute_query('sql')}
                      disabled={!sql_query.trim() || is_executing}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      {is_executing ? 'Executing...' : 'Execute SQL'}
                    </Button>
                    <Button variant="outline" onClick={() => set_sql_query('')}>
                      Clear
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="javascript" className="space-y-4">
                  <div className="space-y-2">
                    <Label>JavaScript Code</Label>
                    <Textarea
                      value={javascript_code}
                      onChange={(e) => set_javascript_code(e.target.value)}
                      placeholder="// Process dashboard data
const result = data.filter(item => item.value > 100);
return result;"
                      className="font-mono text-sm min-h-[200px]"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => handle_execute_query('javascript')}
                      disabled={!javascript_code.trim() || is_executing}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      {is_executing ? 'Executing...' : 'Execute JS'}
                    </Button>
                    <Button variant="outline" onClick={() => set_javascript_code('')}>
                      Clear
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="python" className="space-y-4">
                  <div className="space-y-2">
                    <Label>Python Code</Label>
                    <Textarea
                      value={python_code}
                      onChange={(e) => set_python_code(e.target.value)}
                      placeholder="# Analyze dashboard data with pandas
import pandas as pd
df = pd.DataFrame(data)
result = df.groupby('category').sum()
return result"
                      className="font-mono text-sm min-h-[200px]"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => handle_execute_query('python')}
                      disabled={!python_code.trim() || is_executing}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      {is_executing ? 'Executing...' : 'Execute Python'}
                    </Button>
                    <Button variant="outline" onClick={() => set_python_code('')}>
                      Clear
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>

              {/* Query Results */}
              {query_results && (
                <Card className="mt-4">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <span>Query Results</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Query Type:</span>
                        <Badge variant="outline">{query_results.type.toUpperCase()}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Execution Time:</span>
                        <span>{query_results.execution_time}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Rows Affected:</span>
                        <span>{query_results.rows_affected}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Status:</span>
                        <Badge variant="default" className="bg-green-500">
                          {query_results.status}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Raw Data Tab */}
        <TabsContent value="data" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>Raw Data Access</span>
                <Switch
                  checked={show_raw_data}
                  onCheckedChange={set_show_raw_data}
                />
              </CardTitle>
              <CardDescription>
                Direct access to underlying data sources and structures.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <Button onClick={() => on_data_access?.('export_raw')}>
                  <Download className="h-4 w-4 mr-2" />
                  Export Raw Data
                </Button>
                <Button variant="outline" onClick={() => on_data_access?.('refresh_schema')}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Schema
                </Button>
                <Button variant="outline" onClick={() => on_data_access?.('view_metadata')}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Metadata
                </Button>
              </div>

              {show_raw_data && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Data Structure</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-64">
                      <pre className="text-xs font-mono">
{`{
  "tables": [
    {
      "name": "sales_data",
      "columns": [
        {"name": "id", "type": "integer", "nullable": false},
        {"name": "date", "type": "date", "nullable": false},
        {"name": "amount", "type": "decimal", "nullable": false},
        {"name": "customer_id", "type": "integer", "nullable": true}
      ],
      "row_count": 15420
    },
    {
      "name": "customers",
      "columns": [
        {"name": "id", "type": "integer", "nullable": false},
        {"name": "name", "type": "varchar", "nullable": false},
        {"name": "email", "type": "varchar", "nullable": true}
      ],
      "row_count": 3847
    }
  ]
}`}
                      </pre>
                    </ScrollArea>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Performance Monitoring</span>
                <Switch
                  checked={performance_monitoring}
                  onCheckedChange={set_performance_monitoring}
                />
              </CardTitle>
              <CardDescription>
                Real-time performance metrics and system monitoring.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {performance_monitoring && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <Cpu className="h-4 w-4 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium">CPU Usage</p>
                          <p className="text-2xl font-bold">{performance_metrics.cpu_usage}%</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <MemoryStick className="h-4 w-4 text-green-500" />
                        <div>
                          <p className="text-sm font-medium">Memory</p>
                          <p className="text-2xl font-bold">{performance_metrics.memory_usage}%</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <HardDrive className="h-4 w-4 text-orange-500" />
                        <div>
                          <p className="text-sm font-medium">Disk I/O</p>
                          <p className="text-2xl font-bold">{performance_metrics.disk_io}%</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <Network className="h-4 w-4 text-purple-500" />
                        <div>
                          <p className="text-sm font-medium">Network</p>
                          <p className="text-2xl font-bold">{performance_metrics.network_io}%</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Query Performance</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Status:</span>
                      <Badge variant="default" className="bg-green-500">
                        {performance_metrics.query_performance}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Cache Hit Ratio:</span>
                      <span className="text-sm font-medium">{performance_metrics.cache_hit_ratio}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Avg Response Time:</span>
                      <span className="text-sm font-medium">{performance_metrics.avg_response_time}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">System Status</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Active Connections:</span>
                      <span className="text-sm font-medium">{performance_metrics.active_connections}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">System Health:</span>
                      <Badge variant="default" className="bg-green-500">Healthy</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Last Updated:</span>
                      <span className="text-sm font-medium">2s ago</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex space-x-2">
                <Button onClick={() => on_performance_action?.('optimize')}>
                  <Zap className="h-4 w-4 mr-2" />
                  Optimize Performance
                </Button>
                <Button variant="outline" onClick={() => on_performance_action?.('clear_cache')}>
                  Clear Cache
                </Button>
                <Button variant="outline" onClick={() => on_performance_action?.('export_metrics')}>
                  <Download className="h-4 w-4 mr-2" />
                  Export Metrics
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Advanced Settings</span>
              </CardTitle>
              <CardDescription>
                Configure advanced technical options and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Enable Raw Data Access</Label>
                    <p className="text-xs text-muted-foreground">Allow direct access to underlying data structures</p>
                  </div>
                  <Switch
                    checked={advanced_config.technical_controls.show_raw_data_access}
                    onCheckedChange={(checked) => {
                      // Update config
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Custom Query Execution</Label>
                    <p className="text-xs text-muted-foreground">Enable SQL, JavaScript, and Python query execution</p>
                  </div>
                  <Switch
                    checked={advanced_config.technical_controls.enable_custom_queries}
                    onCheckedChange={(checked) => {
                      // Update config
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Performance Monitoring</Label>
                    <p className="text-xs text-muted-foreground">Show real-time performance metrics</p>
                  </div>
                  <Switch
                    checked={advanced_config.technical_controls.show_performance_metrics}
                    onCheckedChange={(checked) => {
                      // Update config
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Code Editor</Label>
                    <p className="text-xs text-muted-foreground">Enable advanced code editing capabilities</p>
                  </div>
                  <Switch
                    checked={advanced_config.customization.show_code_editor}
                    onCheckedChange={(checked) => {
                      // Update config
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">API Access</Label>
                    <p className="text-xs text-muted-foreground">Enable programmatic API access</p>
                  </div>
                  <Switch
                    checked={advanced_config.customization.enable_api_access}
                    onCheckedChange={(checked) => {
                      // Update config
                    }}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <Label className="text-sm font-medium">Query Timeout (seconds)</Label>
                <Input
                  type="number"
                  defaultValue="30"
                  className="w-32"
                />
              </div>

              <div className="space-y-4">
                <Label className="text-sm font-medium">Cache TTL (minutes)</Label>
                <Input
                  type="number"
                  defaultValue="15"
                  className="w-32"
                />
              </div>

              <div className="flex space-x-2">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </Button>
                <Button variant="outline">
                  Reset to Defaults
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
