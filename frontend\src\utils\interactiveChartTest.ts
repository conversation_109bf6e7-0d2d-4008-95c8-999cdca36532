/**
 * Test utilities for interactive chart system validation
 */

import { processVisualizationData } from './visualization';

// Sample test data for different chart types
export const sampleInteractiveChartData = {
  bar_chart: {
    type: "interactive_chart",
    chart_type: "bar",
    title: "Sales by Region",
    description: "Interactive bar chart showing sales data",
    data: {
      chart_data: [
        { name: "North", sales: 4000, profit: 2400 },
        { name: "South", sales: 3000, profit: 1398 },
        { name: "East", sales: 2000, profit: 9800 },
        { name: "West", sales: 2780, profit: 3908 }
      ],
      columns: ["name", "sales", "profit"],
      metadata: {
        x_axis: "name",
        y_axes: ["sales", "profit"],
        data_type: "standard_chart",
        total_records: 4
      }
    },
    config: {
      responsive: true,
      interactive: true,
      animation: true
    }
  },

  line_chart: {
    type: "interactive_chart",
    chart_type: "line",
    title: "Revenue Trend",
    description: "Interactive line chart showing revenue over time",
    data: {
      chart_data: [
        { name: "<PERSON>", revenue: 4000, expenses: 2400 },
        { name: "Feb", revenue: 3000, expenses: 1398 },
        { name: "Mar", revenue: 2000, expenses: 9800 },
        { name: "Apr", revenue: 2780, expenses: 3908 },
        { name: "May", revenue: 1890, expenses: 4800 },
        { name: "Jun", revenue: 2390, expenses: 3800 }
      ],
      columns: ["name", "revenue", "expenses"],
      metadata: {
        x_axis: "name",
        y_axes: ["revenue", "expenses"],
        data_type: "time_series",
        total_records: 6
      }
    },
    config: {
      responsive: true,
      interactive: true,
      animation: true
    }
  },

  pie_chart: {
    type: "interactive_chart",
    chart_type: "pie",
    title: "Market Share",
    description: "Interactive pie chart showing market distribution",
    data: {
      chart_data: [
        { name: "Product A", value: 400 },
        { name: "Product B", value: 300 },
        { name: "Product C", value: 300 },
        { name: "Product D", value: 200 }
      ],
      columns: ["name", "value"],
      metadata: {
        x_axis: "name",
        y_axes: ["value"],
        data_type: "categorical_numeric",
        total_records: 4
      }
    },
    config: {
      responsive: true,
      interactive: true,
      animation: true
    }
  },

  large_dataset: {
    type: "interactive_chart",
    chart_type: "bar",
    title: "Large Dataset Test",
    description: "Test chart with large dataset for performance validation",
    data: {
      chart_data: Array.from({ length: 2000 }, (_, i) => ({
        name: `Item ${i + 1}`,
        value: Math.floor(Math.random() * 1000) + 100
      })),
      columns: ["name", "value"],
      metadata: {
        x_axis: "name",
        y_axes: ["value"],
        data_type: "large_dataset",
        total_records: 2000
      }
    },
    config: {
      responsive: true,
      interactive: true,
      animation: true
    }
  }
};

// Sample backend response with interactive chart data
export const sampleBackendResponse = {
  content: [
    { type: "text", text: "📊 Generated visualization for: Show sales by region" },
    {
      type: "image",
      src: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      metadata: {}
    }
  ],
  metadata: {
    file_path: "/test/data.csv",
    prompt: "Show sales by region",
    provider: "groq",
    visualization: {
      type: "chart",
      title: "Visualization for: Show sales by region",
      description: "Generated using groq provider",
      data: {
        image: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
        interactive_chart: sampleInteractiveChartData.bar_chart
      },
      config: {
        type: "enhanced_chart",
        options: {
          responsive: true,
          optimized: true,
          interactive: true
        }
      }
    }
  }
};

/**
 * Test function to validate interactive chart data processing
 */
export function testInteractiveChartProcessing(): boolean {
  console.log('🧪 Testing interactive chart data processing...');
  
  try {
    // Test processing of backend response with interactive chart data
    const processedVisualization = processVisualizationData(sampleBackendResponse.metadata);
    
    if (!processedVisualization) {
      console.error('❌ Failed to process visualization data');
      return false;
    }

    if (processedVisualization.type !== 'interactive_chart') {
      console.error('❌ Expected interactive_chart type, got:', processedVisualization.type);
      return false;
    }

    if (!processedVisualization.data.chart_data) {
      console.error('❌ Missing chart_data in processed visualization');
      return false;
    }

    if (!Array.isArray(processedVisualization.data.chart_data)) {
      console.error('❌ chart_data is not an array');
      return false;
    }

    console.log('✅ Interactive chart data processing test passed');
    console.log('📊 Processed visualization:', processedVisualization);
    return true;

  } catch (error) {
    console.error('❌ Error during interactive chart processing test:', error);
    return false;
  }
}

/**
 * Test function to validate chart data formats
 */
export function testChartDataFormats(): boolean {
  console.log('🧪 Testing chart data formats...');
  
  const testCases = [
    { name: 'Bar Chart', data: sampleInteractiveChartData.bar_chart },
    { name: 'Line Chart', data: sampleInteractiveChartData.line_chart },
    { name: 'Pie Chart', data: sampleInteractiveChartData.pie_chart },
    { name: 'Large Dataset', data: sampleInteractiveChartData.large_dataset }
  ];

  for (const testCase of testCases) {
    try {
      const { data } = testCase;
      
      // Validate required fields
      if (!data.type || !data.chart_type || !data.data) {
        console.error(`❌ ${testCase.name}: Missing required fields`);
        return false;
      }

      if (!data.data.chart_data || !Array.isArray(data.data.chart_data)) {
        console.error(`❌ ${testCase.name}: Invalid chart_data`);
        return false;
      }

      if (!data.data.metadata) {
        console.error(`❌ ${testCase.name}: Missing metadata`);
        return false;
      }

      console.log(`✅ ${testCase.name}: Format validation passed`);

    } catch (error) {
      console.error(`❌ ${testCase.name}: Validation error:`, error);
      return false;
    }
  }

  console.log('✅ All chart data format tests passed');
  return true;
}

/**
 * Test function to validate performance with large datasets
 */
export function testLargeDatasetPerformance(): boolean {
  console.log('🧪 Testing large dataset performance...');
  
  try {
    const startTime = performance.now();
    
    // Simulate processing large dataset
    const largeData = sampleInteractiveChartData.large_dataset;
    const chartData = largeData.data.chart_data;
    
    // Test data sampling (similar to what the component does)
    const sampleSize = 500;
    const step = Math.ceil(chartData.length / sampleSize);
    const sampledData = chartData.filter((_, index) => index % step === 0);
    
    const endTime = performance.now();
    const processingTime = endTime - startTime;
    
    console.log(`📊 Original data points: ${chartData.length}`);
    console.log(`📊 Sampled data points: ${sampledData.length}`);
    console.log(`⏱️ Processing time: ${processingTime.toFixed(2)}ms`);
    
    if (processingTime > 100) {
      console.warn('⚠️ Processing time is high, consider further optimization');
    }
    
    if (sampledData.length === 0) {
      console.error('❌ Data sampling resulted in empty dataset');
      return false;
    }
    
    console.log('✅ Large dataset performance test passed');
    return true;

  } catch (error) {
    console.error('❌ Error during performance test:', error);
    return false;
  }
}

/**
 * Run all interactive chart tests
 */
export function runAllInteractiveChartTests(): boolean {
  console.log('🚀 Running all interactive chart tests...');
  
  const tests = [
    testInteractiveChartProcessing,
    testChartDataFormats,
    testLargeDatasetPerformance
  ];
  
  let allPassed = true;
  
  for (const test of tests) {
    if (!test()) {
      allPassed = false;
    }
  }
  
  if (allPassed) {
    console.log('🎉 All interactive chart tests passed!');
  } else {
    console.log('❌ Some interactive chart tests failed');
  }
  
  return allPassed;
}

// Export for use in development/testing
export default {
  sampleInteractiveChartData,
  sampleBackendResponse,
  testInteractiveChartProcessing,
  testChartDataFormats,
  testLargeDatasetPerformance,
  runAllInteractiveChartTests
};
