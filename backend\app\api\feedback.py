import logging
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import uuid # For generating IDs if not done by model default_factory in all cases

from ..services.feedback_service import FeedbackService # Changed
from ..models.feedback import ( # Changed
    UserFeedbackResponse, 
    FeedbackSurveyResponse, 
    SurveyResponseResponse, 
    FeedbackAnalytics, NPSAnalytics, FeedbackAlert
)
from ..database import get_db # Changed
# Assuming Pydantic schemas for request bodies might be defined elsewhere,
# or we use the ones from models.feedback if they are suitable for creation/update.
# For example, if UserFeedbackSchema is used for creation:
# from ..models.feedback import UserFeedbackCreateSchema (hypothetical)

from ..api.admin import get_current_active_user, get_current_admin_user # Changed
from ..models.auth import User as AuthUser # Changed For current_user type hint
# or define simple Pydantic models here for request bodies.

# For simplicity, let's define Pydantic request models here if they differ from SQLAlchemy models
# or if we need specific fields for creation.
from pydantic import BaseModel
from datetime import datetime

class MessageFeedbackCreate(BaseModel):
    message_id: str
    conversation_id: Optional[str] = None
    persona_id: Optional[str] = None
    rating: int
    feedback_text: Optional[str] = None

class ConversationFeedbackCreate(BaseModel):
    conversation_id: str
    persona_id: Optional[str] = None
    rating: int
    feedback_tags: Optional[List[str]] = None
    feedback_text: Optional[str] = None

class PersonaReviewCreate(BaseModel):
    persona_id: str
    rating: int
    review_text: str

class SurveyCreate(BaseModel): # For creating a survey
    title: str
    description: Optional[str] = None
    survey_type: str # e.g., "satisfaction", "nps"
    questions: List[Dict[str, Any]] # Define question structure as needed
    target_audience: Optional[Dict[str, Any]] = None
    is_active: bool = True
    expires_at: Optional[datetime] = None

class SurveyResponseCreate(BaseModel): # For submitting a response
    survey_id: str
    responses: Dict[str, Any] # e.g. {"question_id_1": "answer", "question_id_2": 4}
    completion_time_seconds: Optional[int] = None

# Removed dummy auth functions, using real ones via Depends

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/feedback", tags=["feedback"])

# Dependency to get FeedbackService instance
def get_feedback_service(db: Session = Depends(get_db)) -> FeedbackService:
    return FeedbackService(db=db)


# --- Feedback Collection Endpoints ---
@router.post("/message", response_model=UserFeedbackResponse)
async def submit_message_feedback(
    feedback_data: MessageFeedbackCreate,
    current_user: AuthUser = Depends(get_current_active_user), # Changed
    service: FeedbackService = Depends(get_feedback_service)
):
    return await service.collect_message_feedback(
        user_id=current_user.id, # Changed
        message_id=feedback_data.message_id,
        conversation_id=feedback_data.conversation_id,
        persona_id=feedback_data.persona_id,
        rating=feedback_data.rating,
        feedback_text=feedback_data.feedback_text
    )

@router.post("/conversation", response_model=UserFeedbackResponse)
async def submit_conversation_feedback(
    feedback_data: ConversationFeedbackCreate,
    current_user: AuthUser = Depends(get_current_active_user), # Changed
    service: FeedbackService = Depends(get_feedback_service)
):
    return await service.collect_conversation_feedback(
        user_id=current_user.id, # Changed
        conversation_id=feedback_data.conversation_id,
        persona_id=feedback_data.persona_id,
        rating=feedback_data.rating,
        feedback_tags=feedback_data.feedback_tags,
        feedback_text=feedback_data.feedback_text
    )

@router.post("/persona_review", response_model=UserFeedbackResponse)
async def submit_persona_review(
    review_data: PersonaReviewCreate,
    current_user: AuthUser = Depends(get_current_active_user), # Changed
    service: FeedbackService = Depends(get_feedback_service)
):
    return await service.collect_persona_review(
        user_id=current_user.id, # Changed
        persona_id=review_data.persona_id,
        rating=review_data.rating,
        review_text=review_data.review_text
    )

# --- Survey Endpoints ---
@router.post("/surveys", response_model=FeedbackSurveyResponse, status_code=201)
async def create_new_survey(
    survey_data: SurveyCreate, # Using Pydantic model for request body
    current_user: AuthUser = Depends(get_current_admin_user), # Changed, assuming admin only
    service: FeedbackService = Depends(get_feedback_service)
):
    # Ensure admin access (get_current_admin_user should handle this)
    return await service.create_survey(survey_data.model_dump())


@router.post("/surveys/trigger/{trigger_event}", response_model=Optional[FeedbackSurveyResponse])
async def trigger_satisfaction_survey(
    trigger_event: str,
    current_user: AuthUser = Depends(get_current_active_user), # Changed
    service: FeedbackService = Depends(get_feedback_service)
):
    """Endpoint to trigger a survey, e.g., post-conversation."""
    survey = await service.create_satisfaction_survey(trigger_event, current_user.id) # Changed
    if not survey:
        raise HTTPException(status_code=404, detail=f"No survey configuration for trigger: {trigger_event}")
    return survey

@router.get("/surveys/active", response_model=List[FeedbackSurveyResponse])
async def get_active_surveys(
    current_user: AuthUser = Depends(get_current_active_user), # Changed
    service: FeedbackService = Depends(get_feedback_service)
):
    """Get active surveys targeted for the current user."""
    return await service.get_active_surveys_for_user(current_user.id) # Changed

@router.get("/surveys/{survey_id}", response_model=FeedbackSurveyResponse)
async def get_survey_details_route(
    survey_id: str,
    service: FeedbackService = Depends(get_feedback_service)
):
    survey = await service.get_survey_details(survey_id)
    if not survey:
        raise HTTPException(status_code=404, detail="Survey not found")
    return survey

@router.post("/surveys/responses", response_model=SurveyResponseResponse, status_code=201)
async def submit_survey_response_route(
    response_data: SurveyResponseCreate,
    current_user: AuthUser = Depends(get_current_active_user), # Changed
    service: FeedbackService = Depends(get_feedback_service)
):
    return await service.submit_survey_response(
        survey_id=response_data.survey_id,
        user_id=current_user.id, # Changed
        responses=response_data.responses,
        completion_time_seconds=response_data.completion_time_seconds
    )

# --- Admin Analytics Endpoints ---
@router.get("/analytics/trends", response_model=FeedbackAnalytics)
async def get_feedback_trends_analytics(
    time_period: str = Query("week", enum=["day", "week", "month"]),
    current_user: AuthUser = Depends(get_current_admin_user), # Changed & uncommented
    service: FeedbackService = Depends(get_feedback_service)
):
    # get_current_admin_user dependency should handle admin access check
    return await service.analyze_feedback_trends(time_period)

@router.get("/analytics/nps", response_model=NPSAnalytics)
async def get_nps_score_analytics(
    time_period: str = Query("month", enum=["week", "month", "quarter"]),
    current_user: AuthUser = Depends(get_current_admin_user), # Changed & uncommented
    service: FeedbackService = Depends(get_feedback_service)
):
    # get_current_admin_user dependency should handle admin access check
    return await service.calculate_nps_analytics(time_period)

@router.get("/alerts", response_model=List[FeedbackAlert])
async def get_admin_feedback_alerts(
    threshold_rating: int = Query(2, ge=1, le=5),
    current_user: AuthUser = Depends(get_current_admin_user), # Changed & uncommented
    service: FeedbackService = Depends(get_feedback_service)
):
    # get_current_admin_user dependency should handle admin access check
    return await service.get_negative_feedback_alerts(threshold_rating)
