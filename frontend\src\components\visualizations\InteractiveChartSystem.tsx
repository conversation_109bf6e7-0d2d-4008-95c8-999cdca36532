import React, { useState, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Scatter<PERSON>hart,
  TrendingUp,
  Settings,
  Palette,
  Download,
  RefreshCw,
  Zap
} from 'lucide-react';
import { EnhancedChartVisualization } from './EnhancedChartVisualization';
import { useToast } from '@/hooks/use-toast';

interface ChartRequest {
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'heatmap' | 'custom';
  query: string;
  style?: 'default' | 'minimal' | 'colorful' | 'professional';
  size?: 'small' | 'medium' | 'large';
}

interface InteractiveChartSystemProps {
  dataSource: string;
  onChartRequest: (request: ChartRequest) => Promise<void>;
  currentChart?: {
    title: string;
    description?: string;
    data: {
      image: string;
      metadata?: any;
    };
  };
  isGenerating?: boolean;
  className?: string;
}

const chartTypes = [
  { 
    id: 'bar' as const, 
    name: 'Bar Chart', 
    icon: BarChart3, 
    description: 'Compare categories',
    examples: ['Sales by region', 'Count by category', 'Performance metrics']
  },
  { 
    id: 'line' as const, 
    name: 'Line Chart', 
    icon: LineChart, 
    description: 'Show trends over time',
    examples: ['Revenue over time', 'Growth trends', 'Performance tracking']
  },
  { 
    id: 'pie' as const, 
    name: 'Pie Chart', 
    icon: PieChart, 
    description: 'Show proportions',
    examples: ['Market share', 'Budget allocation', 'Category distribution']
  },
  {
    id: 'scatter' as const,
    name: 'Scatter Plot',
    icon: ScatterChart,
    description: 'Find correlations',
    examples: ['Price vs quality', 'Age vs income', 'Performance correlation']
  },
  { 
    id: 'heatmap' as const, 
    name: 'Heatmap', 
    icon: TrendingUp, 
    description: 'Show intensity patterns',
    examples: ['Correlation matrix', 'Activity patterns', 'Geographic data']
  }
];

const chartStyles = [
  { id: 'default', name: 'Default', description: 'Standard styling' },
  { id: 'minimal', name: 'Minimal', description: 'Clean and simple' },
  { id: 'colorful', name: 'Colorful', description: 'Vibrant colors' },
  { id: 'professional', name: 'Professional', description: 'Business ready' }
];

export const InteractiveChartSystem: React.FC<InteractiveChartSystemProps> = ({
  dataSource,
  onChartRequest,
  currentChart,
  isGenerating = false,
  className = ''
}) => {
  const [selectedType, setSelectedType] = useState<ChartRequest['type']>('bar');
  const [selectedStyle, setSelectedStyle] = useState<ChartRequest['style']>('default');
  const [customQuery, setCustomQuery] = useState('');
  const [activeTab, setActiveTab] = useState('quick');
  const { toast } = useToast();

  const handleQuickChart = useCallback(async (type: ChartRequest['type'], example: string) => {
    try {
      await onChartRequest({
        type,
        query: example,
        style: selectedStyle,
        size: 'medium'
      });
    } catch (error) {
      toast({
        title: "Chart Generation Failed",
        description: "Unable to generate the chart. Please try again.",
        variant: "destructive",
      });
    }
  }, [onChartRequest, selectedStyle, toast]);

  const handleCustomChart = useCallback(async () => {
    if (!customQuery.trim()) {
      toast({
        title: "Query Required",
        description: "Please enter a description for your chart.",
        variant: "destructive",
      });
      return;
    }

    try {
      await onChartRequest({
        type: selectedType,
        query: customQuery,
        style: selectedStyle,
        size: 'medium'
      });
    } catch (error) {
      toast({
        title: "Chart Generation Failed",
        description: "Unable to generate the chart. Please try again.",
        variant: "destructive",
      });
    }
  }, [customQuery, selectedType, selectedStyle, onChartRequest, toast]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Current Chart Display */}
      {currentChart && (
        <EnhancedChartVisualization
          title={currentChart.title}
          description={currentChart.description}
          data={currentChart.data}
          className="mb-6"
        />
      )}

      {/* Chart Generation Interface */}
      <Card className="border-2 border-dashed border-gray-200 bg-gray-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-brand-600" />
            Interactive Chart Generator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="quick">Quick Charts</TabsTrigger>
              <TabsTrigger value="custom">Custom Chart</TabsTrigger>
            </TabsList>

            <TabsContent value="quick" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {chartTypes.map((chartType) => {
                  const Icon = chartType.icon;
                  return (
                    <Card key={chartType.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-2">
                        <div className="flex items-center gap-2">
                          <Icon className="h-5 w-5 text-brand-600" />
                          <CardTitle className="text-sm">{chartType.name}</CardTitle>
                        </div>
                        <p className="text-xs text-gray-600">{chartType.description}</p>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        {chartType.examples.map((example, index) => (
                          <Button
                            key={index}
                            variant="outline"
                            size="sm"
                            className="w-full text-left justify-start h-auto py-2 px-3"
                            onClick={() => handleQuickChart(chartType.id, example)}
                            disabled={isGenerating}
                          >
                            <span className="text-xs">{example}</span>
                          </Button>
                        ))}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="custom" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Chart Type Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Chart Type</label>
                  <div className="grid grid-cols-2 gap-2">
                    {chartTypes.map((chartType) => {
                      const Icon = chartType.icon;
                      return (
                        <Button
                          key={chartType.id}
                          variant={selectedType === chartType.id ? "default" : "outline"}
                          size="sm"
                          className="flex items-center gap-2 h-auto py-2"
                          onClick={() => setSelectedType(chartType.id)}
                        >
                          <Icon className="h-4 w-4" />
                          <span className="text-xs">{chartType.name}</span>
                        </Button>
                      );
                    })}
                  </div>
                </div>

                {/* Style Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Style</label>
                  <div className="grid grid-cols-2 gap-2">
                    {chartStyles.map((style) => (
                      <Button
                        key={style.id}
                        variant={selectedStyle === style.id ? "default" : "outline"}
                        size="sm"
                        className="flex items-center gap-2 h-auto py-2"
                        onClick={() => setSelectedStyle(style.id as ChartRequest['style'])}
                      >
                        <Palette className="h-4 w-4" />
                        <span className="text-xs">{style.name}</span>
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Custom Query Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Chart Description</label>
                <textarea
                  value={customQuery}
                  onChange={(e) => setCustomQuery(e.target.value)}
                  placeholder="Describe what you want to visualize... (e.g., 'Show the correlation between price and sales volume')"
                  className="w-full p-3 border border-gray-300 rounded-md resize-none h-20 text-sm"
                  disabled={isGenerating}
                />
              </div>

              <Button
                onClick={handleCustomChart}
                disabled={isGenerating || !customQuery.trim()}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Generating Chart...
                  </>
                ) : (
                  <>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Generate Chart
                  </>
                )}
              </Button>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Data Source Info */}
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <Badge variant="outline" className="text-xs">
          Data Source: {dataSource}
        </Badge>
        <span>•</span>
        <span>Charts are generated using AI-powered analysis</span>
      </div>
    </div>
  );
};
