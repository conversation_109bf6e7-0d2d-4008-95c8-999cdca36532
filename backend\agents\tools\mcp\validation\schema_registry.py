"""
MCP Schema Registry for Phase 2 Validation Standardization.

This module implements the extensible schema registry system as specified
in the refactor.md Phase 2 requirements, providing automatic schema discovery
and registration for MCP tools with agent awareness.
"""

import logging
import inspect
import importlib
import os
from typing import Dict, Any, Type, Optional, List, Set
from pathlib import Path
from pydantic import BaseModel, create_model
from ..base import BaseMCPTool

logger = logging.getLogger(__name__)


class MCPSchemaRegistry:
    """
    Extensible schema registry for MCP tools with auto-discovery capabilities.
    
    This registry automatically discovers and registers validation schemas for
    MCP tools, making the system extensible for new tools without manual configuration.
    """
    
    def __init__(self):
        """Initialize the schema registry."""
        self.schemas: Dict[str, Type[BaseModel]] = {}
        self.tool_classes: Dict[str, Type[BaseMCPTool]] = {}
        self.auto_discovery_enabled = True
        self.discovery_paths: List[str] = []
        self.registered_tools: Set[str] = set()
        
        # Agent awareness patterns for validation
        self.agent_awareness_patterns = {
            "required_imports": [
                "from ...utils.agent_identity import detect_agent_identity",
                "import logging"
            ],
            "agent_detection_pattern": r"agent_identity = await detect_agent_identity\(",
            "agent_logging_pattern": r"logger\.info\(f[\"']Detected agent identity: \{agent_identity\}",
            "agent_metadata_pattern": r'"agent_identity": agent_identity',
            "agent_aware_flag_pattern": r'"agent_aware": True'
        }
        
        self.logger = logging.getLogger(f"{__name__}.MCPSchemaRegistry")
        self.logger.info("Initialized MCP Schema Registry")
    
    def register_schema(self, tool_name: str, schema_class: Type[BaseModel]) -> None:
        """
        Register a validation schema for a tool.
        
        Args:
            tool_name: Name of the tool
            schema_class: Pydantic schema class for validation
        """
        self.schemas[tool_name] = schema_class
        self.registered_tools.add(tool_name)
        self.logger.info(f"Registered schema for tool: {tool_name}")
    
    def register_tool_class(self, tool_name: str, tool_class: Type[BaseMCPTool]) -> None:
        """
        Register a tool class for schema inference.
        
        Args:
            tool_name: Name of the tool
            tool_class: Tool class for schema inference
        """
        self.tool_classes[tool_name] = tool_class
        self.logger.info(f"Registered tool class: {tool_name}")
    
    def get_schema(self, tool_name: str) -> Optional[Type[BaseModel]]:
        """
        Get validation schema for a tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Pydantic schema class or None if not found
        """
        # Check if schema is already registered
        if tool_name in self.schemas:
            return self.schemas[tool_name]
        
        # Try auto-discovery if enabled
        if self.auto_discovery_enabled:
            discovered_schema = self._auto_discover_schema(tool_name)
            if discovered_schema:
                self.register_schema(tool_name, discovered_schema)
                return discovered_schema
        
        return None
    
    def _auto_discover_schema(self, tool_name: str) -> Optional[Type[BaseModel]]:
        """
        Automatically discover schema for a tool.
        
        Args:
            tool_name: Name of the tool to discover schema for
            
        Returns:
            Discovered schema class or None
        """
        try:
            # Try to find the tool class
            tool_class = self._find_tool_class(tool_name)
            if tool_class:
                return self._infer_schema_from_tool(tool_class)
            
            # Try to find existing schema definition
            schema_class = self._find_existing_schema(tool_name)
            if schema_class:
                return schema_class
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Failed to auto-discover schema for {tool_name}: {e}")
            return None
    
    def _find_tool_class(self, tool_name: str) -> Optional[Type[BaseMCPTool]]:
        """
        Find tool class by name.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool class or None if not found
        """
        # Check registered tool classes first
        if tool_name in self.tool_classes:
            return self.tool_classes[tool_name]
        
        # Try to discover from file system
        return self._discover_tool_from_filesystem(tool_name)
    
    def _discover_tool_from_filesystem(self, tool_name: str) -> Optional[Type[BaseMCPTool]]:
        """
        Discover tool class from filesystem.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool class or None if not found
        """
        try:
            # Get the MCP tools directory
            mcp_tools_dir = Path(__file__).parent.parent
            
            # Look for tool file
            tool_file_patterns = [
                f"{tool_name}.py",
                f"{tool_name}_tool.py",
                f"mcp_{tool_name}.py"
            ]
            
            for pattern in tool_file_patterns:
                tool_file = mcp_tools_dir / pattern
                if tool_file.exists():
                    # Try to import and find the tool class
                    module_name = f"backend.agents.tools.mcp.{pattern[:-3]}"
                    try:
                        module = importlib.import_module(module_name)
                        
                        # Look for classes that inherit from BaseMCPTool
                        for name, obj in inspect.getmembers(module, inspect.isclass):
                            if (issubclass(obj, BaseMCPTool) and 
                                obj != BaseMCPTool and
                                name.lower().replace("tool", "").replace("mcp", "") == tool_name.lower()):
                                return obj
                                
                    except ImportError as e:
                        self.logger.warning(f"Failed to import module {module_name}: {e}")
                        continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error discovering tool from filesystem: {e}")
            return None
    
    def _infer_schema_from_tool(self, tool_class: Type[BaseMCPTool]) -> Optional[Type[BaseModel]]:
        """
        Infer validation schema from tool class.
        
        Args:
            tool_class: Tool class to analyze
            
        Returns:
            Inferred schema class or None
        """
        try:
            # Get the execute method signature
            execute_method = getattr(tool_class, 'execute', None)
            if not execute_method:
                return None
            
            signature = inspect.signature(execute_method)
            
            # Build schema fields from method parameters
            schema_fields = {}
            
            for param_name, param in signature.parameters.items():
                if param_name in ['self', 'arguments']:
                    continue
                
                # Determine field type and constraints
                field_type = param.annotation if param.annotation != inspect.Parameter.empty else str
                default_value = param.default if param.default != inspect.Parameter.empty else ...
                
                schema_fields[param_name] = (field_type, default_value)
            
            # Add common MCP tool fields (ensure all values are hashable)
            common_fields = {
                'persona_id': (Optional[str], None),
                'agent_id': (Optional[str], None),
                'context': (Dict[str, Any], None),  # Use None instead of {} to avoid unhashable type
                'provider': (Optional[str], None),
                'model': (Optional[str], None)
            }

            # Only add fields that don't already exist and have valid types
            for field_name, field_spec in common_fields.items():
                if field_name not in schema_fields:
                    schema_fields[field_name] = field_spec

            # Validate schema_fields to ensure all keys are strings and values are tuples
            validated_fields = {}
            for field_name, field_spec in schema_fields.items():
                if isinstance(field_name, str) and isinstance(field_spec, tuple) and len(field_spec) == 2:
                    validated_fields[field_name] = field_spec
                else:
                    self.logger.warning(f"Skipping invalid field {field_name}: {field_spec}")

            # Create dynamic schema class with model configuration to avoid namespace conflicts
            schema_class_name = f"{tool_class.__name__}Schema"

            # Create model config to avoid protected namespace warnings
            model_config = {
                'protected_namespaces': ()  # Disable protected namespace warnings
            }

            schema_class = create_model(
                schema_class_name,
                __config__=type('Config', (), model_config),
                **validated_fields
            )
            
            return schema_class
            
        except Exception as e:
            self.logger.error(f"Failed to infer schema from tool class: {e}")
            return None
    
    def _find_existing_schema(self, tool_name: str) -> Optional[Type[BaseModel]]:
        """
        Find existing schema definition.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Existing schema class or None
        """
        try:
            # Try to import from validation module
            from .input_validator import (
                IntentDetectionInputSchema,
                LanguageDetectionInputSchema,
                MarketingToolInputSchema,
                DataAnalysisInputSchema,
                ConversationToolInputSchema
            )
            
            # Map tool names to existing schemas
            schema_mapping = {
                'intent_detection': IntentDetectionInputSchema,
                'intent_analysis': IntentDetectionInputSchema,
                'language_detection': LanguageDetectionInputSchema,
                'generate_blog_content': MarketingToolInputSchema,
                'generate_email_marketing': MarketingToolInputSchema,
                'generate_ad_copy': MarketingToolInputSchema,
                'data_analysis': DataAnalysisInputSchema,
                'pandasai_analysis': DataAnalysisInputSchema,
                'handle_conversation': ConversationToolInputSchema
            }
            
            return schema_mapping.get(tool_name)
            
        except ImportError as e:
            self.logger.warning(f"Failed to import existing schemas: {e}")
            return None
    
    def validate_tool_agent_awareness(self, tool_name: str) -> Dict[str, Any]:
        """
        Validate that a tool follows agent awareness patterns.
        
        Args:
            tool_name: Name of the tool to validate
            
        Returns:
            Validation results dictionary
        """
        try:
            tool_class = self._find_tool_class(tool_name)
            if not tool_class:
                return {
                    "valid": False,
                    "error": f"Tool class not found: {tool_name}"
                }
            
            # Get tool source file
            tool_file = inspect.getfile(tool_class)
            with open(tool_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check agent awareness patterns
            validation_result = {
                "valid": True,
                "patterns_found": {},
                "patterns_missing": [],
                "issues": [],
                "recommendations": []
            }
            
            # Check required imports
            for required_import in self.agent_awareness_patterns["required_imports"]:
                if required_import in content:
                    validation_result["patterns_found"][f"import_{required_import.split()[-1]}"] = True
                else:
                    validation_result["patterns_missing"].append(required_import)
                    validation_result["issues"].append(f"Missing import: {required_import}")
                    validation_result["valid"] = False
            
            # Check agent detection pattern
            import re
            if re.search(self.agent_awareness_patterns["agent_detection_pattern"], content):
                validation_result["patterns_found"]["agent_detection"] = True
            else:
                validation_result["patterns_missing"].append("agent_detection")
                validation_result["issues"].append("Missing agent identity detection")
                validation_result["valid"] = False
            
            # Check agent metadata pattern
            if re.search(self.agent_awareness_patterns["agent_metadata_pattern"], content):
                validation_result["patterns_found"]["agent_metadata"] = True
            else:
                validation_result["patterns_missing"].append("agent_metadata")
                validation_result["issues"].append("Missing agent_identity in metadata")
                validation_result["valid"] = False
            
            if not validation_result["valid"]:
                validation_result["recommendations"].extend([
                    "Add missing agent-related imports",
                    "Implement agent identity detection",
                    "Add agent metadata to responses"
                ])
            
            return validation_result
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Validation failed: {str(e)}"
            }
    
    def discover_all_tools(self) -> Dict[str, Type[BaseModel]]:
        """
        Discover and register schemas for all available tools.
        
        Returns:
            Dictionary of discovered schemas
        """
        discovered_schemas = {}
        
        try:
            # Get MCP tools directory
            mcp_tools_dir = Path(__file__).parent.parent
            
            # Scan for Python files
            for py_file in mcp_tools_dir.glob("*.py"):
                if py_file.name.startswith("__") or py_file.name == "base.py":
                    continue
                
                tool_name = py_file.stem
                schema = self.get_schema(tool_name)
                if schema:
                    discovered_schemas[tool_name] = schema
            
            self.logger.info(f"Discovered {len(discovered_schemas)} tool schemas")
            return discovered_schemas
            
        except Exception as e:
            self.logger.error(f"Failed to discover all tools: {e}")
            return {}
    
    def get_registry_status(self) -> Dict[str, Any]:
        """
        Get registry status and statistics.
        
        Returns:
            Registry status dictionary
        """
        return {
            "registered_schemas": len(self.schemas),
            "registered_tool_classes": len(self.tool_classes),
            "auto_discovery_enabled": self.auto_discovery_enabled,
            "discovery_paths": self.discovery_paths,
            "registered_tools": list(self.registered_tools)
        }
