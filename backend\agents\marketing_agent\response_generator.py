"""
Production Response Generator for Marketing Agent.

This module provides AI-powered response generation that uses business context
and conversation history to create personalized, intelligent responses.
"""

import logging
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from .business_context import BusinessContextAnalyzer, BusinessContext
from ..components.shared.shared_llm_processor import SharedLLMProcessor
from ..components.base_component import AgentContext

logger = logging.getLogger(__name__)


class ResponseContext(BaseModel):
    """Context information for response generation."""
    user_message: str
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list)
    business_context: Optional[BusinessContext] = None
    user_profile: Dict[str, Any] = Field(default_factory=dict)
    intent_type: str = "general"
    scenario: str = "assistance"
    data_sources: List[str] = Field(default_factory=list)


class ProductionResponseGenerator:
    """
    Production-ready response generator that creates personalized responses
    using business context analysis and conversation history.
    """
    
    def __init__(self, llm_provider: str = "groq"):
        """
        Initialize the response generator.
        
        Args:
            llm_provider: LLM provider to use for response generation
        """
        self.llm_provider = llm_provider
        self.business_analyzer = BusinessContextAnalyzer(llm_provider)
        self.llm_processor = SharedLLMProcessor({
            "fallback_providers": [llm_provider, "openai", "google"],
            "default_temperature": 0.7,
            "max_tokens": 1000
        })
        
        # Response templates for different scenarios
        self.response_templates = {
            "greeting": self._get_greeting_template(),
            "assistance": self._get_assistance_template(),
            "clarification": self._get_clarification_template(),
            "error_handling": self._get_error_template(),
            "data_analysis": self._get_data_analysis_template(),
            "strategy_recommendation": self._get_strategy_template()
        }
    
    async def generate_response(self, context: ResponseContext) -> str:
        """
        Generate a personalized response based on context.
        
        Args:
            context: ResponseContext containing all relevant information
            
        Returns:
            Generated response string
        """
        try:
            logger.info(f"Generating response for scenario: {context.scenario}")
            
            # Analyze business context if not provided
            if not context.business_context:
                context.business_context = await self._analyze_business_context(context)
            
            # Select appropriate response template
            template = self.response_templates.get(context.scenario, self.response_templates["assistance"])
            
            # Generate personalized response
            response = await self._generate_personalized_response(template, context)
            
            logger.info("Response generated successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return self._get_fallback_response(context.scenario)
    
    async def _analyze_business_context(self, context: ResponseContext) -> BusinessContext:
        """Analyze business context from available information."""
        try:
            user_data = {
                "conversation_history": context.conversation_history,
                "user_profile": context.user_profile,
                "data_sources": context.data_sources
            }
            
            return await self.business_analyzer.analyze_context(user_data)
            
        except Exception as e:
            logger.error(f"Error analyzing business context: {e}")
            return BusinessContext(confidence_score=0.1)
    
    async def _generate_personalized_response(self, template: str, context: ResponseContext) -> str:
        """Generate personalized response using AI."""
        try:
            # Prepare context information for the prompt
            business_info = self._format_business_context(context.business_context)
            conversation_summary = self._summarize_conversation(context.conversation_history)
            
            # Create personalized prompt
            prompt = template.format(
                user_message=context.user_message,
                business_context=business_info,
                conversation_summary=conversation_summary,
                user_profile=self._format_user_profile(context.user_profile),
                intent_type=context.intent_type,
                scenario=context.scenario
            )
            
            # Generate response using LLM
            agent_context = AgentContext()
            agent_context.set_field("prompt", prompt)
            agent_context.set_field("agent_identity", {
                "role": "marketing_expert",
                "personality": "enthusiastic_professional",
                "expertise": ["digital_marketing", "content_strategy", "lead_generation"]
            })
            
            result_context = await self.llm_processor.process(agent_context)
            
            if result_context.get_status() == "success":
                response = result_context.get_field("llm_response", "")
                return self._post_process_response(response, context)
            
            return self._get_fallback_response(context.scenario)
            
        except Exception as e:
            logger.error(f"Error generating personalized response: {e}")
            return self._get_fallback_response(context.scenario)
    
    def _format_business_context(self, business_context: Optional[BusinessContext]) -> str:
        """Format business context for prompt inclusion."""
        if not business_context or business_context.confidence_score < 0.2:
            return "Business context: Limited information available"
        
        context_parts = []
        
        if business_context.industry:
            context_parts.append(f"Industry: {business_context.industry}")
        
        if business_context.business_type:
            context_parts.append(f"Business Type: {business_context.business_type}")
        
        if business_context.business_size:
            context_parts.append(f"Business Size: {business_context.business_size}")
        
        if business_context.target_market:
            context_parts.append(f"Target Market: {business_context.target_market}")
        
        if business_context.marketing_challenges:
            challenges = ", ".join(business_context.marketing_challenges[:3])
            context_parts.append(f"Marketing Challenges: {challenges}")
        
        if business_context.key_products:
            products = ", ".join(business_context.key_products[:3])
            context_parts.append(f"Key Products/Services: {products}")
        
        return "Business context: " + "; ".join(context_parts) if context_parts else "Business context: General business"
    
    def _summarize_conversation(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Summarize recent conversation history."""
        if not conversation_history:
            return "No previous conversation"
        
        recent_messages = conversation_history[-5:]  # Last 5 messages
        user_messages = [
            msg.get("content", "") for msg in recent_messages 
            if msg.get("role") == "user" or msg.get("sender") == "user"
        ]
        
        if not user_messages:
            return "No recent user messages"
        
        return f"Recent topics: {'; '.join(user_messages[:3])}"
    
    def _format_user_profile(self, user_profile: Dict[str, Any]) -> str:
        """Format user profile information."""
        if not user_profile:
            return "User profile: Not available"
        
        profile_parts = []
        for key, value in user_profile.items():
            if value and key in ["role", "company", "industry", "experience"]:
                profile_parts.append(f"{key}: {value}")
        
        return "User profile: " + "; ".join(profile_parts) if profile_parts else "User profile: General user"
    
    def _post_process_response(self, response: str, context: ResponseContext) -> str:
        """Post-process the generated response."""
        # Clean up the response
        response = response.strip()
        
        # Ensure appropriate length
        if len(response) > 1000:
            # Truncate at sentence boundary
            sentences = response.split('. ')
            truncated = []
            current_length = 0
            
            for sentence in sentences:
                if current_length + len(sentence) > 800:
                    break
                truncated.append(sentence)
                current_length += len(sentence)
            
            response = '. '.join(truncated)
            if not response.endswith('.'):
                response += '.'
        
        return response
    
    def _get_fallback_response(self, scenario: str) -> str:
        """Get fallback response for different scenarios."""
        fallbacks = {
            "greeting": "Hello! I'm your marketing AI assistant. I'm here to help you with all your marketing needs. What would you like to work on today?",
            "assistance": "I'm here to help with your marketing challenges! Whether it's content creation, strategy development, or campaign planning, I'm ready to assist. What specific area would you like to focus on?",
            "clarification": "I'd be happy to help clarify that for you. Could you provide a bit more detail about what you're looking for?",
            "error_handling": "I apologize for the technical issue. Let me help you in a different way. What marketing challenge can I assist you with?",
            "data_analysis": "I can help analyze your data for marketing insights. Please share more details about what you'd like to explore.",
            "strategy_recommendation": "I'd love to help you develop a marketing strategy. Let's start by understanding your business goals and target audience."
        }
        
        return fallbacks.get(scenario, fallbacks["assistance"])

    def _get_greeting_template(self) -> str:
        """Get greeting response template."""
        return """
You are an expert marketing AI assistant. Generate a warm, professional greeting response.

User's message: "{user_message}"
{business_context}
{user_profile}

Create a personalized greeting that:
1. Welcomes the user warmly and professionally
2. Acknowledges their business context if available
3. Highlights your marketing expertise relevant to their industry/business
4. Offers specific help based on their context
5. Asks an engaging question to start the conversation

Keep the tone enthusiastic but professional. Make it 2-3 sentences maximum.
"""

    def _get_assistance_template(self) -> str:
        """Get general assistance response template."""
        return """
You are an expert marketing AI assistant ready to provide helpful guidance.

User's message: "{user_message}"
{business_context}
{conversation_summary}
{user_profile}

Generate a helpful response that:
1. Directly addresses their request or question
2. Provides specific, actionable marketing advice
3. References their business context when relevant
4. Offers concrete next steps or recommendations
5. Maintains an enthusiastic, professional tone

Focus on practical marketing solutions. Keep response concise but comprehensive.
"""

    def _get_clarification_template(self) -> str:
        """Get clarification request template."""
        return """
You are an expert marketing AI assistant seeking to better understand the user's needs.

User's message: "{user_message}"
{business_context}
{conversation_summary}

The user's request needs clarification. Generate a response that:
1. Acknowledges their request professionally
2. Explains what additional information would be helpful
3. Asks specific, relevant questions based on their business context
4. Offers examples or options to guide their response
5. Maintains a helpful, encouraging tone

Ask 2-3 specific questions maximum to gather the needed information.
"""

    def _get_error_template(self) -> str:
        """Get error handling response template."""
        return """
You are an expert marketing AI assistant handling a technical issue gracefully.

Context: There was a technical issue with the previous request.
User's message: "{user_message}"
{business_context}

Generate a professional error recovery response that:
1. Acknowledges the issue without going into technical details
2. Apologizes briefly and professionally
3. Offers alternative ways to help with their marketing needs
4. Redirects to actionable marketing assistance
5. Maintains confidence and professionalism

Keep it brief and focus on moving forward positively.
"""

    def _get_data_analysis_template(self) -> str:
        """Get data analysis response template."""
        return """
You are an expert marketing AI assistant specializing in data-driven insights.

User's message: "{user_message}"
{business_context}
Data available: User has uploaded data sources for analysis

Generate a response that:
1. Acknowledges their data and analysis needs
2. Explains how you can help extract marketing insights
3. Suggests specific types of analysis relevant to their business
4. Offers to identify opportunities, trends, or recommendations
5. Asks what specific insights they're most interested in

Focus on the marketing value of data analysis. Be specific about capabilities.
"""

    def _get_strategy_template(self) -> str:
        """Get strategy recommendation response template."""
        return """
You are an expert marketing strategist providing strategic guidance.

User's message: "{user_message}"
{business_context}
{conversation_summary}

Generate a strategic marketing response that:
1. Addresses their strategic marketing needs
2. Provides industry-specific strategic recommendations
3. Considers their business size, type, and challenges
4. Offers a structured approach to their marketing goals
5. Suggests prioritized next steps

Focus on high-level strategy while being actionable. Consider their business context.
"""
