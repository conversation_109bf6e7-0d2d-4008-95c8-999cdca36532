# Universal Streaming Configuration for All Agents
# This configuration is used by the StreamingMixin to provide consistent streaming behavior

# Default streaming settings
default:
  chunk_size: 30              # Characters per chunk
  delay_ms: 30                # Milliseconds between chunks
  min_chunk_size: 10          # Minimum chunk size
  max_chunk_size: 100         # Maximum chunk size
  word_boundary: true         # Break on word boundaries
  adaptive_delay: true        # Adjust delay based on content length

# Agent-specific streaming configurations
agents:
  # Concierge agent - faster streaming for quick responses
  concierge-agent:
    chunk_size: 25
    delay_ms: 25
    adaptive_delay: true
    word_boundary: true
    
  # Marketing agent - moderate streaming for professional content
  composable-marketing-ai:
    chunk_size: 40
    delay_ms: 35
    adaptive_delay: true
    word_boundary: true
    
  # Analysis agent - slower streaming for complex data
  composable-analysis-ai:
    chunk_size: 50
    delay_ms: 40
    adaptive_delay: true
    word_boundary: true
    
  # Data scientist agent - technical content streaming
  composable-data-scientist-ai:
    chunk_size: 45
    delay_ms: 35
    adaptive_delay: true
    word_boundary: true

# Content-type specific streaming settings
content_types:
  # Short responses (< 100 characters)
  short:
    chunk_size: 20
    delay_ms: 20
    
  # Medium responses (100-500 characters)
  medium:
    chunk_size: 30
    delay_ms: 30
    
  # Long responses (> 500 characters)
  long:
    chunk_size: 50
    delay_ms: 25
    adaptive_delay: true
    
  # Code responses
  code:
    chunk_size: 80
    delay_ms: 20
    word_boundary: false  # Don't break code on word boundaries
    
  # Technical documentation
  technical:
    chunk_size: 60
    delay_ms: 35
    word_boundary: true

# Advanced streaming features
features:
  # Enable typing indicators
  typing_indicators: true
  
  # Enable streaming progress
  progress_tracking: true
  
  # Enable chunk optimization
  chunk_optimization: true
  
  # Enable error recovery
  error_recovery: true
  
  # Maximum streaming time (seconds)
  max_streaming_time: 30
  
  # Fallback to non-streaming if errors occur
  fallback_on_error: true
