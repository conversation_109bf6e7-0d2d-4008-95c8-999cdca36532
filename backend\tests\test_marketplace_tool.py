#!/usr/bin/env python3
"""
Test script for the enhanced persona marketplace tool.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_marketplace_tool():
    """Test the persona marketplace tool functionality."""
    try:
        from backend.agents.tools.mcp.persona_marketplace_tool import PersonaMarketplaceTool
        
        print("🧪 Testing Persona Marketplace Tool...")
        
        # Create tool instance
        tool = PersonaMarketplaceTool()
        
        # Test user_id (using a test user ID)
        test_user_id = "1"
        
        print(f"\n1. Testing user_personas query for user {test_user_id}...")
        result = await tool.execute({
            "user_id": test_user_id,
            "query_type": "user_personas"
        })
        
        if result.get("isError"):
            print(f"❌ Error: {result}")
        else:
            data = result.get("metadata", {}).get("data", {})
            user_personas = data.get("user_personas", [])
            print(f"✅ User owns {len(user_personas)} personas:")
            for persona in user_personas:
                print(f"   - {persona.get('name', 'Unknown')} (ID: {persona.get('id', 'Unknown')})")
        
        print(f"\n2. Testing available_personas query...")
        result = await tool.execute({
            "user_id": test_user_id,
            "query_type": "available_personas"
        })
        
        if result.get("isError"):
            print(f"❌ Error: {result}")
        else:
            data = result.get("metadata", {}).get("data", {})
            available_personas = data.get("available_personas", [])
            free_personas = data.get("free_personas", [])
            paid_personas = data.get("paid_personas", [])
            print(f"✅ Marketplace has {len(available_personas)} total personas:")
            print(f"   - {len(free_personas)} free personas")
            print(f"   - {len(paid_personas)} paid personas")
        
        print(f"\n3. Testing recommendations query...")
        result = await tool.execute({
            "user_id": test_user_id,
            "query_type": "recommendations",
            "intent_type": "data_analysis",
            "user_requirements": "I want to analyze my sales data"
        })
        
        if result.get("isError"):
            print(f"❌ Error: {result}")
        else:
            data = result.get("metadata", {}).get("data", {})
            owned_recs = data.get("owned_recommendations", [])
            purchase_recs = data.get("purchase_recommendations", [])
            print(f"✅ Recommendations for data analysis:")
            print(f"   - {len(owned_recs)} owned personas recommended")
            print(f"   - {len(purchase_recs)} purchase personas recommended")
        
        print(f"\n4. Testing marketplace_overview query...")
        result = await tool.execute({
            "user_id": test_user_id,
            "query_type": "marketplace_overview"
        })
        
        if result.get("isError"):
            print(f"❌ Error: {result}")
        else:
            data = result.get("metadata", {}).get("data", {})
            owned_count = data.get("owned_count", 0)
            free_count = data.get("free_count", 0)
            paid_count = data.get("paid_count", 0)
            total_count = data.get("total_count", 0)
            print(f"✅ Marketplace overview:")
            print(f"   - {owned_count} owned personas")
            print(f"   - {free_count} free personas available")
            print(f"   - {paid_count} paid personas available")
            print(f"   - {total_count} total personas")
        
        print(f"\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_marketplace_tool())
