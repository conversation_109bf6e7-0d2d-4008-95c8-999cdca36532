/**
 * Theme Selector Dialog
 * 
 * Dialog for selecting and customizing dashboard themes.
 * Features:
 * - Predefined theme options
 * - Custom color picker
 * - Live preview
 * - Theme import/export
 * - Dark/light mode variants
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Palette,
  Check,
  Download,
  Upload,
  Paintbrush,
  Sun,
  Moon,
  Monitor,
  Eye,
  Save,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';

interface ThemeSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dashboardId?: string;
}

interface Theme {
  id: string;
  name: string;
  description: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    accent: string;
  };
  category: 'light' | 'dark' | 'custom';
  isDefault?: boolean;
}

const PREDEFINED_THEMES: Theme[] = [
  {
    id: 'default-light',
    name: 'Default Light',
    description: 'Clean and professional light theme',
    colors: {
      primary: '#3B82F6',
      secondary: '#10B981',
      background: '#FFFFFF',
      surface: '#F9FAFB',
      text: '#1F2937',
      textSecondary: '#6B7280',
      border: '#E5E7EB',
      accent: '#8B5CF6',
    },
    category: 'light',
    isDefault: true,
  },
  {
    id: 'default-dark',
    name: 'Default Dark',
    description: 'Modern dark theme for low-light environments',
    colors: {
      primary: '#60A5FA',
      secondary: '#34D399',
      background: '#111827',
      surface: '#1F2937',
      text: '#F9FAFB',
      textSecondary: '#D1D5DB',
      border: '#374151',
      accent: '#A78BFA',
    },
    category: 'dark',
  },
  {
    id: 'ocean',
    name: 'Ocean Blue',
    description: 'Calming blue tones inspired by the ocean',
    colors: {
      primary: '#0EA5E9',
      secondary: '#06B6D4',
      background: '#F0F9FF',
      surface: '#E0F2FE',
      text: '#0C4A6E',
      textSecondary: '#0369A1',
      border: '#BAE6FD',
      accent: '#7C3AED',
    },
    category: 'light',
  },
  {
    id: 'forest',
    name: 'Forest Green',
    description: 'Natural green theme for environmental data',
    colors: {
      primary: '#059669',
      secondary: '#10B981',
      background: '#F0FDF4',
      surface: '#DCFCE7',
      text: '#14532D',
      textSecondary: '#166534',
      border: '#BBF7D0',
      accent: '#F59E0B',
    },
    category: 'light',
  },
  {
    id: 'sunset',
    name: 'Sunset Orange',
    description: 'Warm orange and red tones',
    colors: {
      primary: '#EA580C',
      secondary: '#F59E0B',
      background: '#FFF7ED',
      surface: '#FFEDD5',
      text: '#9A3412',
      textSecondary: '#C2410C',
      border: '#FED7AA',
      accent: '#DC2626',
    },
    category: 'light',
  },
  {
    id: 'midnight',
    name: 'Midnight Purple',
    description: 'Deep purple theme for creative dashboards',
    colors: {
      primary: '#8B5CF6',
      secondary: '#A78BFA',
      background: '#1E1B4B',
      surface: '#312E81',
      text: '#F3F4F6',
      textSecondary: '#C7D2FE',
      border: '#4C1D95',
      accent: '#F59E0B',
    },
    category: 'dark',
  },
];

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  open,
  onOpenChange,
  dashboardId,
}) => {
  const { toast } = useToast();
  const { currentLayout, updateDashboard } = useUnifiedDashboardStore();
  
  const [selectedTheme, setSelectedTheme] = useState<Theme | null>(null);
  const [customTheme, setCustomTheme] = useState<Theme>({
    id: 'custom',
    name: 'Custom Theme',
    description: 'Your custom theme',
    colors: {
      primary: '#3B82F6',
      secondary: '#10B981',
      background: '#FFFFFF',
      surface: '#F9FAFB',
      text: '#1F2937',
      textSecondary: '#6B7280',
      border: '#E5E7EB',
      accent: '#8B5CF6',
    },
    category: 'custom',
  });
  const [activeTab, setActiveTab] = useState('predefined');
  const [isApplying, setIsApplying] = useState(false);

  // Load current theme
  useEffect(() => {
    if (open && currentLayout?.dashboard?.theme_config) {
      const currentThemeConfig = currentLayout.dashboard.theme_config;
      
      // Try to match with predefined themes
      const matchedTheme = PREDEFINED_THEMES.find(theme => 
        theme.colors.primary === currentThemeConfig.primary_color &&
        theme.colors.secondary === currentThemeConfig.secondary_color
      );
      
      if (matchedTheme) {
        setSelectedTheme(matchedTheme);
      } else {
        // Create custom theme from current config
        setCustomTheme(prev => ({
          ...prev,
          colors: {
            primary: currentThemeConfig.primary_color || prev.colors.primary,
            secondary: currentThemeConfig.secondary_color || prev.colors.secondary,
            background: currentThemeConfig.background_color || prev.colors.background,
            surface: prev.colors.surface,
            text: currentThemeConfig.text_color || prev.colors.text,
            textSecondary: prev.colors.textSecondary,
            border: prev.colors.border,
            accent: prev.colors.accent,
          },
        }));
        setSelectedTheme(customTheme);
        setActiveTab('custom');
      }
    }
  }, [open, currentLayout]);

  const handleThemeSelect = (theme: Theme) => {
    setSelectedTheme(theme);
  };

  const handleCustomColorChange = (colorKey: keyof Theme['colors'], value: string) => {
    setCustomTheme(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorKey]: value,
      },
    }));
    
    if (activeTab === 'custom') {
      setSelectedTheme({
        ...customTheme,
        colors: {
          ...customTheme.colors,
          [colorKey]: value,
        },
      });
    }
  };

  const handleApplyTheme = async () => {
    if (!selectedTheme || !dashboardId) {
      toast({
        title: "No Theme Selected",
        description: "Please select a theme to apply.",
        variant: "destructive",
      });
      return;
    }

    setIsApplying(true);
    try {
      const themeConfig = {
        primary_color: selectedTheme.colors.primary,
        secondary_color: selectedTheme.colors.secondary,
        background_color: selectedTheme.colors.background,
        text_color: selectedTheme.colors.text,
        theme_name: selectedTheme.id,
      };

      await updateDashboard(dashboardId, {
        theme_config: themeConfig,
      });

      toast({
        title: "Theme Applied",
        description: `"${selectedTheme.name}" theme has been applied to your dashboard.`,
      });

      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to apply theme. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsApplying(false);
    }
  };

  const handleExportTheme = () => {
    if (!selectedTheme) return;

    const themeData = JSON.stringify(selectedTheme, null, 2);
    const blob = new Blob([themeData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${selectedTheme.name.toLowerCase().replace(/\s+/g, '-')}-theme.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast({
      title: "Theme Exported",
      description: "Theme has been exported as JSON file.",
    });
  };

  const handleImportTheme = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const themeData = JSON.parse(e.target?.result as string);
        setCustomTheme(themeData);
        setSelectedTheme(themeData);
        setActiveTab('custom');
        
        toast({
          title: "Theme Imported",
          description: "Custom theme has been imported successfully.",
        });
      } catch (error) {
        toast({
          title: "Import Error",
          description: "Invalid theme file format.",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);
  };

  const ThemePreview = ({ theme }: { theme: Theme }) => (
    <div className="space-y-2">
      <div 
        className="w-full h-20 rounded-lg border-2 p-3 transition-all"
        style={{ 
          backgroundColor: theme.colors.background,
          borderColor: theme.colors.border,
          color: theme.colors.text,
        }}
      >
        <div className="flex items-center justify-between h-full">
          <div className="space-y-1">
            <div 
              className="w-16 h-2 rounded"
              style={{ backgroundColor: theme.colors.primary }}
            />
            <div 
              className="w-12 h-1 rounded"
              style={{ backgroundColor: theme.colors.textSecondary }}
            />
          </div>
          <div className="flex space-x-1">
            <div 
              className="w-3 h-3 rounded"
              style={{ backgroundColor: theme.colors.secondary }}
            />
            <div 
              className="w-3 h-3 rounded"
              style={{ backgroundColor: theme.colors.accent }}
            />
          </div>
        </div>
      </div>
      <div className="flex space-x-1">
        {Object.entries(theme.colors).slice(0, 4).map(([key, color]) => (
          <div
            key={key}
            className="w-4 h-4 rounded border"
            style={{ backgroundColor: color }}
            title={key}
          />
        ))}
      </div>
    </div>
  );

  const ColorInput = ({ 
    label, 
    colorKey, 
    value 
  }: { 
    label: string; 
    colorKey: keyof Theme['colors']; 
    value: string; 
  }) => (
    <div className="space-y-2">
      <Label className="text-sm">{label}</Label>
      <div className="flex space-x-2">
        <Input
          type="color"
          value={value}
          onChange={(e) => handleCustomColorChange(colorKey, e.target.value)}
          className="w-12 h-8 p-1 border rounded"
        />
        <Input
          type="text"
          value={value}
          onChange={(e) => handleCustomColorChange(colorKey, e.target.value)}
          className="flex-1 text-sm font-mono"
          placeholder="#000000"
        />
      </div>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Palette className="h-5 w-5" />
            <span>Select Theme</span>
          </DialogTitle>
          <DialogDescription>
            Choose a theme for your dashboard or create a custom one.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="predefined">Predefined</TabsTrigger>
            <TabsTrigger value="custom">Custom</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="predefined" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
              {PREDEFINED_THEMES.map((theme) => (
                <Card
                  key={theme.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    selectedTheme?.id === theme.id && "ring-2 ring-primary"
                  )}
                  onClick={() => handleThemeSelect(theme)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-sm">{theme.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {theme.category}
                          </Badge>
                          {theme.isDefault && (
                            <Badge variant="secondary" className="text-xs">Default</Badge>
                          )}
                        </div>
                      </div>
                      {selectedTheme?.id === theme.id && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-xs text-muted-foreground">
                      {theme.description}
                    </p>
                    <ThemePreview theme={theme} />
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="custom" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Custom Colors</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <ColorInput 
                      label="Primary Color" 
                      colorKey="primary" 
                      value={customTheme.colors.primary} 
                    />
                    <ColorInput 
                      label="Secondary Color" 
                      colorKey="secondary" 
                      value={customTheme.colors.secondary} 
                    />
                    <ColorInput 
                      label="Background Color" 
                      colorKey="background" 
                      value={customTheme.colors.background} 
                    />
                    <ColorInput 
                      label="Text Color" 
                      colorKey="text" 
                      value={customTheme.colors.text} 
                    />
                    <ColorInput 
                      label="Border Color" 
                      colorKey="border" 
                      value={customTheme.colors.border} 
                    />
                    <ColorInput 
                      label="Accent Color" 
                      colorKey="accent" 
                      value={customTheme.colors.accent} 
                    />
                  </CardContent>
                </Card>

                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={handleExportTheme}
                    className="flex items-center space-x-2"
                  >
                    <Download className="h-4 w-4" />
                    <span>Export</span>
                  </Button>
                  <div>
                    <Input
                      type="file"
                      accept=".json"
                      onChange={handleImportTheme}
                      className="hidden"
                      id="theme-import"
                    />
                    <Label htmlFor="theme-import">
                      <Button variant="outline" asChild className="flex items-center space-x-2">
                        <span>
                          <Upload className="h-4 w-4" />
                          <span>Import</span>
                        </span>
                      </Button>
                    </Label>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Preview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ThemePreview theme={customTheme} />
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            {selectedTheme && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Theme Preview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Theme Name</Label>
                      <p className="text-sm text-muted-foreground">{selectedTheme.name}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Category</Label>
                      <p className="text-sm text-muted-foreground">{selectedTheme.category}</p>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-sm text-muted-foreground">{selectedTheme.description}</p>
                  </div>
                  <ThemePreview theme={selectedTheme} />
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex items-center justify-between pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleApplyTheme}
            disabled={!selectedTheme || isApplying}
            className="flex items-center space-x-2"
          >
            {isApplying ? (
              <>
                <Save className="h-4 w-4 animate-pulse" />
                <span>Applying...</span>
              </>
            ) : (
              <>
                <Paintbrush className="h-4 w-4" />
                <span>Apply Theme</span>
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
