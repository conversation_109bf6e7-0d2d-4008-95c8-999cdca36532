#!/usr/bin/env python3
"""
Installation script for Crawl4AI and its dependencies.

This script handles the installation of Crawl4AI and ensures proper setup
for the Datagenius business profile autofill enhancement.
"""

import subprocess
import sys
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Run a command and handle errors."""
    logger.info(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"Success: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed: {description}")
        logger.error(f"Error: {e.stderr}")
        return False

def install_crawl4ai():
    """Install Crawl4AI and its dependencies."""
    logger.info("Starting Crawl4AI installation...")
    
    # Install Crawl4AI
    if not run_command("pip install crawl4ai", "Installing Crawl4AI"):
        return False
    
    # Run post-installation setup
    if not run_command("crawl4ai-setup", "Running Crawl4AI setup"):
        logger.warning("Crawl4AI setup failed, trying manual browser installation...")
        
        # Try manual browser installation
        if not run_command("python -m playwright install chromium", "Installing Chromium browser"):
            logger.error("Failed to install browser dependencies")
            return False
    
    # Verify installation
    try:
        import crawl4ai
        logger.info(f"Crawl4AI successfully installed, version: {crawl4ai.__version__ if hasattr(crawl4ai, '__version__') else 'unknown'}")
        return True
    except ImportError as e:
        logger.error(f"Crawl4AI installation verification failed: {e}")
        return False

def check_system_requirements():
    """Check if system meets requirements for Crawl4AI."""
    logger.info("Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    # Check if we're in a virtual environment (recommended)
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.warning("Not running in a virtual environment - this is not recommended")
    
    return True

def main():
    """Main installation function."""
    logger.info("Crawl4AI Installation Script for Datagenius")
    logger.info("=" * 50)
    
    if not check_system_requirements():
        logger.error("System requirements not met")
        sys.exit(1)
    
    if install_crawl4ai():
        logger.info("Crawl4AI installation completed successfully!")
        logger.info("You can now use the enhanced web scraping features in Datagenius")
        
        # Print configuration information
        logger.info("\nConfiguration:")
        logger.info("- Set CRAWL4AI_ENABLED=true to enable enhanced scraping")
        logger.info("- Set CRAWL4AI_TIMEOUT=30 to configure timeout (seconds)")
        logger.info("- Set CRAWL4AI_MAX_RETRIES=2 to configure retry attempts")
        logger.info("- See backend/app/config/crawl4ai_config.py for more options")
        
    else:
        logger.error("Crawl4AI installation failed")
        logger.error("The system will fall back to the existing web scraping service")
        sys.exit(1)

if __name__ == "__main__":
    main()
