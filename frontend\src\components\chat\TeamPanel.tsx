import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  UserCircle, 
  Briefcase, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle,
  Network,
  Layers,
  GitBranch
} from 'lucide-react';
import { useConcierge } from '@/contexts/ConciergeContext';

interface TeamPanelProps {
  className?: string;
}

export const TeamPanel: React.FC<TeamPanelProps> = ({
  className = ''
}) => {
  const { conciergeState } = useConcierge();

  if (!conciergeState) return null;

  const { 
    teamId, 
    teamMembers, 
    teamHierarchy, 
    assignedRole, 
    roleCapabilities, 
    activeTasks,
    routingStrategy,
    fallbackChain
  } = conciergeState;

  const hasTeamData = 
    teamId || 
    teamMembers.length > 0 || 
    Object.keys(teamHierarchy).length > 0 || 
    assignedRole || 
    roleCapabilities.length > 0 || 
    activeTasks.length > 0 ||
    routingStrategy ||
    fallbackChain.length > 0;

  if (!hasTeamData) {
    return (
      <Card className={`${className} border-dashed border-gray-300`}>
        <CardContent className="p-4">
          <div className="flex flex-col items-center justify-center text-center p-4 text-gray-500">
            <Users className="h-8 w-8 mb-2 text-gray-400" />
            <h3 className="text-sm font-medium mb-1">No Team Activity</h3>
            <p className="text-xs">
              Team information will appear here when you form a team or are assigned a role.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="p-4">
        <h3 className="text-sm font-medium mb-3 flex items-center">
          <Users className="h-4 w-4 mr-1 text-brand-500" />
          Team & Role Information
        </h3>

        {/* Team Information */}
        {teamId && (
          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-500 mb-2">Team</h4>
            <div className="bg-blue-50 p-2 rounded-md text-xs">
              <div className="flex justify-between items-start mb-1">
                <div className="font-medium text-blue-700 flex items-center">
                  <Users className="h-3 w-3 mr-1" />
                  Team ID: {teamId.substring(0, 8)}...
                </div>
                <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-200">
                  Active
                </Badge>
              </div>
              {teamMembers.length > 0 && (
                <div className="mt-2">
                  <p className="text-xs text-blue-600 mb-1">Members:</p>
                  <div className="flex flex-wrap gap-1">
                    {teamMembers.map((member, index) => (
                      <Badge 
                        key={`member-${index}`}
                        variant="outline" 
                        className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1"
                      >
                        <UserCircle className="h-3 w-3" />
                        {member.persona_id} ({member.role})
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Role Information */}
        {assignedRole && (
          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-500 mb-2">Assigned Role</h4>
            <div className="bg-purple-50 p-2 rounded-md text-xs">
              <div className="flex justify-between items-start mb-1">
                <div className="font-medium text-purple-700 flex items-center">
                  <Briefcase className="h-3 w-3 mr-1" />
                  {assignedRole}
                </div>
              </div>
              {roleCapabilities.length > 0 && (
                <div className="mt-2">
                  <p className="text-xs text-purple-600 mb-1">Capabilities:</p>
                  <div className="flex flex-wrap gap-1">
                    {roleCapabilities.map((capability, index) => (
                      <Badge 
                        key={`capability-${index}`}
                        variant="outline" 
                        className="bg-purple-50 text-purple-700 border-purple-200"
                      >
                        {capability}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Active Tasks */}
        {activeTasks.length > 0 && (
          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-500 mb-2">Active Tasks</h4>
            <div className="space-y-2">
              {activeTasks.map((task, index) => (
                <motion.div 
                  key={`task-${index}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-amber-50 p-2 rounded-md text-xs"
                >
                  <div className="flex justify-between items-start mb-1">
                    <div className="font-medium text-amber-700 flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      Task: {task.task_id.substring(0, 8)}...
                    </div>
                    <Badge 
                      variant="outline" 
                      className={`
                        ${task.status === 'assigned' ? 'bg-blue-100 text-blue-700 border-blue-200' : ''}
                        ${task.status === 'in_progress' ? 'bg-yellow-100 text-yellow-700 border-yellow-200' : ''}
                        ${task.status === 'completed' ? 'bg-green-100 text-green-700 border-green-200' : ''}
                        ${task.status === 'failed' ? 'bg-red-100 text-red-700 border-red-200' : ''}
                      `}
                    >
                      {task.status}
                    </Badge>
                  </div>
                  <p className="text-amber-600">{task.description}</p>
                  <p className="text-amber-500 mt-1 text-xs">Assignee: {task.assignee}</p>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Routing Information */}
        {routingStrategy && (
          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-500 mb-2">Routing Strategy</h4>
            <div className="bg-green-50 p-2 rounded-md text-xs">
              <div className="flex justify-between items-start mb-1">
                <div className="font-medium text-green-700 flex items-center">
                  <Network className="h-3 w-3 mr-1" />
                  {routingStrategy}
                </div>
              </div>
              {fallbackChain.length > 0 && (
                <div className="mt-2">
                  <p className="text-xs text-green-600 mb-1">Fallback Chain:</p>
                  <div className="flex items-center gap-1">
                    {fallbackChain.map((persona, index) => (
                      <React.Fragment key={`fallback-${index}`}>
                        <Badge 
                          variant="outline" 
                          className="bg-green-50 text-green-700 border-green-200"
                        >
                          {persona}
                        </Badge>
                        {index < fallbackChain.length - 1 && (
                          <GitBranch className="h-3 w-3 text-green-400 rotate-90" />
                        )}
                      </React.Fragment>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Team Hierarchy */}
        {Object.keys(teamHierarchy).length > 0 && (
          <div>
            <h4 className="text-xs font-medium text-gray-500 mb-2">Team Hierarchy</h4>
            <div className="bg-indigo-50 p-2 rounded-md text-xs">
              <div className="flex justify-between items-start mb-1">
                <div className="font-medium text-indigo-700 flex items-center">
                  <Layers className="h-3 w-3 mr-1" />
                  Hierarchy
                </div>
              </div>
              <div className="mt-2 space-y-2">
                {Object.entries(teamHierarchy).map(([role, subordinates], index) => (
                  <div key={`hierarchy-${index}`} className="ml-2">
                    <div className="flex items-center">
                      <Badge 
                        variant="outline" 
                        className="bg-indigo-100 text-indigo-700 border-indigo-200"
                      >
                        {role}
                      </Badge>
                    </div>
                    {subordinates.length > 0 && (
                      <div className="ml-4 mt-1 flex flex-wrap gap-1">
                        {subordinates.map((subordinate, subIndex) => (
                          <Badge 
                            key={`sub-${subIndex}`}
                            variant="outline" 
                            className="bg-indigo-50 text-indigo-600 border-indigo-100"
                          >
                            {subordinate}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
