"""
Marketing agent implementation.

This package contains the implementation of the Marketing Agent,
which is responsible for marketing content generation tasks.
This agent uses the Model Context Protocol (MCP) for standardized tool integration.
"""

import logging

# Configure logging
logger = logging.getLogger(__name__)

# Register marketing components
from .register import register_marketing_components
register_marketing_components()

# Import the components for easy access
from .components import (
    MarketingParserComponent,
    MCPContentGeneratorComponent
)

# Import the agent for easy access
from .composable_agent import ComposableMarketingAgent

# Export classes for easier imports
__all__ = [
    "MarketingParserComponent",
    "MCPContentGeneratorComponent",
    "ComposableMarketingAgent"
]

logger.info("Initialized marketing agent package")
