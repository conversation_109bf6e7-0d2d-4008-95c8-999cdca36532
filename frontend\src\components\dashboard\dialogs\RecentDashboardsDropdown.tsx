/**
 * Recent Dashboards Dropdown
 * 
 * Dropdown component showing recently accessed dashboards.
 * Features:
 * - List of recently opened dashboards
 * - Quick access to switch dashboards
 * - Last modified timestamps
 * - Pin/unpin functionality
 * - Clear recent history option
 */

import React, { useState, useEffect } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Clock,
  Pin,
  PinOff,
  Trash2,
  Globe,
  Lock,
  Calendar,
  MoreHorizontal,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDashboardManagement } from '@/hooks/use-dashboard-management';
import { useToast } from '@/hooks/use-toast';

interface RecentDashboardsDropdownProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trigger?: React.ReactNode;
  onDashboardSelect?: (dashboardId: string) => void;
}

interface RecentDashboard {
  id: string;
  name: string;
  description?: string;
  lastAccessed: string;
  isPinned: boolean;
  isPublic: boolean;
  isDefault: boolean;
  sectionCount: number;
  widgetCount: number;
}

export const RecentDashboardsDropdown: React.FC<RecentDashboardsDropdownProps> = ({
  open,
  onOpenChange,
  trigger,
  onDashboardSelect,
}) => {
  const { toast } = useToast();
  const { dashboards, switchDashboard, activeDashboard } = useDashboardManagement();
  
  const [recentDashboards, setRecentDashboards] = useState<RecentDashboard[]>([]);
  const [pinnedDashboards, setPinnedDashboards] = useState<string[]>([]);

  // Load recent dashboards from localStorage and merge with current dashboards
  useEffect(() => {
    const loadRecentDashboards = () => {
      try {
        const recentIds = JSON.parse(localStorage.getItem('recentDashboards') || '[]');
        const pinned = JSON.parse(localStorage.getItem('pinnedDashboards') || '[]');
        setPinnedDashboards(pinned);

        // Create recent dashboards list with access timestamps
        const recentWithData = recentIds
          .map((item: any) => {
            const dashboard = dashboards.find(d => d.id === item.id);
            if (!dashboard) return null;
            
            return {
              ...dashboard,
              lastAccessed: item.lastAccessed,
              isPinned: pinned.includes(dashboard.id),
              sectionCount: dashboard.section_count || 0,
              widgetCount: dashboard.widget_count || 0,
            };
          })
          .filter(Boolean)
          .slice(0, 10); // Limit to 10 recent items

        setRecentDashboards(recentWithData);
      } catch (error) {
        console.error('Error loading recent dashboards:', error);
      }
    };

    if (dashboards.length > 0) {
      loadRecentDashboards();
    }
  }, [dashboards]);

  const handleDashboardOpen = async (dashboardId: string) => {
    try {
      await switchDashboard(dashboardId);
      
      // Update recent dashboards list
      updateRecentDashboards(dashboardId);
      
      onDashboardSelect?.(dashboardId);
      onOpenChange(false);
      
      toast({
        title: "Dashboard Opened",
        description: "Dashboard loaded successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to open dashboard.",
        variant: "destructive",
      });
    }
  };

  const updateRecentDashboards = (dashboardId: string) => {
    try {
      const recent = JSON.parse(localStorage.getItem('recentDashboards') || '[]');
      const updated = [
        { id: dashboardId, lastAccessed: new Date().toISOString() },
        ...recent.filter((item: any) => item.id !== dashboardId)
      ].slice(0, 10);
      
      localStorage.setItem('recentDashboards', JSON.stringify(updated));
    } catch (error) {
      console.error('Error updating recent dashboards:', error);
    }
  };

  const handlePinToggle = (dashboardId: string) => {
    try {
      const pinned = [...pinnedDashboards];
      const index = pinned.indexOf(dashboardId);
      
      if (index > -1) {
        pinned.splice(index, 1);
        toast({
          title: "Dashboard Unpinned",
          description: "Dashboard removed from pinned list.",
        });
      } else {
        pinned.push(dashboardId);
        toast({
          title: "Dashboard Pinned",
          description: "Dashboard added to pinned list.",
        });
      }
      
      setPinnedDashboards(pinned);
      localStorage.setItem('pinnedDashboards', JSON.stringify(pinned));
      
      // Update the recent dashboards state
      setRecentDashboards(prev => 
        prev.map(dashboard => 
          dashboard.id === dashboardId 
            ? { ...dashboard, isPinned: !dashboard.isPinned }
            : dashboard
        )
      );
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update pin status.",
        variant: "destructive",
      });
    }
  };

  const handleClearRecent = () => {
    try {
      localStorage.removeItem('recentDashboards');
      setRecentDashboards([]);
      toast({
        title: "Recent History Cleared",
        description: "Recent dashboards list has been cleared.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clear recent history.",
        variant: "destructive",
      });
    }
  };

  const formatLastAccessed = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInHours < 168) { // 7 days
      return `${Math.floor(diffInHours / 24)}d ago`;
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }
  };

  // Separate pinned and recent dashboards
  const pinnedItems = recentDashboards.filter(d => d.isPinned);
  const recentItems = recentDashboards.filter(d => !d.isPinned);

  const DashboardItem = ({ dashboard }: { dashboard: RecentDashboard }) => (
    <DropdownMenuItem
      className="flex items-center justify-between p-3 cursor-pointer"
      onClick={() => handleDashboardOpen(dashboard.id)}
    >
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <span className="font-medium truncate">{dashboard.name}</span>
          {dashboard.isDefault && (
            <Badge variant="secondary" className="text-xs">Default</Badge>
          )}
          {dashboard.id === activeDashboard?.id && (
            <Badge variant="default" className="text-xs">Active</Badge>
          )}
        </div>
        <div className="flex items-center space-x-3 mt-1 text-xs text-muted-foreground">
          <div className="flex items-center space-x-1">
            <Clock className="h-3 w-3" />
            <span>{formatLastAccessed(dashboard.lastAccessed)}</span>
          </div>
          <div className="flex items-center space-x-1">
            {dashboard.isPublic ? (
              <Globe className="h-3 w-3" />
            ) : (
              <Lock className="h-3 w-3" />
            )}
            <span>{dashboard.isPublic ? 'Public' : 'Private'}</span>
          </div>
          <span>{dashboard.sectionCount} sections</span>
        </div>
      </div>
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0 ml-2"
        onClick={(e) => {
          e.stopPropagation();
          handlePinToggle(dashboard.id);
        }}
      >
        {dashboard.isPinned ? (
          <PinOff className="h-3 w-3" />
        ) : (
          <Pin className="h-3 w-3" />
        )}
      </Button>
    </DropdownMenuItem>
  );

  return (
    <DropdownMenu open={open} onOpenChange={onOpenChange}>
      {trigger && (
        <DropdownMenuTrigger asChild>
          {trigger}
        </DropdownMenuTrigger>
      )}
      <DropdownMenuContent className="w-80" align="start">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Recent Dashboards</span>
          {recentDashboards.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={handleClearRecent}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          )}
        </DropdownMenuLabel>
        
        {recentDashboards.length === 0 ? (
          <div className="p-4 text-center text-sm text-muted-foreground">
            <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No recent dashboards</p>
            <p className="text-xs">Dashboards you open will appear here</p>
          </div>
        ) : (
          <>
            {/* Pinned Dashboards */}
            {pinnedItems.length > 0 && (
              <>
                <DropdownMenuLabel className="text-xs text-muted-foreground">
                  Pinned
                </DropdownMenuLabel>
                {pinnedItems.map((dashboard) => (
                  <DashboardItem key={`pinned-${dashboard.id}`} dashboard={dashboard} />
                ))}
                {recentItems.length > 0 && <DropdownMenuSeparator />}
              </>
            )}
            
            {/* Recent Dashboards */}
            {recentItems.length > 0 && (
              <>
                {pinnedItems.length > 0 && (
                  <DropdownMenuLabel className="text-xs text-muted-foreground">
                    Recent
                  </DropdownMenuLabel>
                )}
                {recentItems.map((dashboard) => (
                  <DashboardItem key={`recent-${dashboard.id}`} dashboard={dashboard} />
                ))}
              </>
            )}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
