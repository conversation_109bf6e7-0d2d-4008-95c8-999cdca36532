"""
Component system for the Datagenius agent architecture.

This package contains the implementation of the component-based architecture
for Datagenius agents, including the base component interface, component registry,
and specific component implementations.
"""

import logging
from .base import AgentComponent
from .registry import ComponentRegistry
from .essential_tools import ensure_essential_tools, create_mcp_server_with_essential_tools

# Configure logging
logger = logging.getLogger(__name__)

# Import Phase 2 shared components
from .shared import SharedLLMProcessor, SharedDataRetriever, SharedContextManager
from .base_component import BaseAgentComponent, AgentContext, ComponentMetrics

# Export classes for easier imports
__all__ = [
    "AgentComponent",
    "ComponentRegistry",
    "ensure_essential_tools",
    "create_mcp_server_with_essential_tools",
    # Phase 2 exports
    "BaseAgentComponent",
    "AgentContext",
    "ComponentMetrics",
    "SharedLLMProcessor",
    "SharedDataRetriever",
    "SharedContextManager"
]

# Log before component registration
logger.info("About to register components")

# Register components
from .register import register_components
register_components()

# Log the component system initialization
logger.info("Initialized agent component system")
logger.info(f"Registered components: {ComponentRegistry.list_registered_components()}")
