import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Database, 
  Wifi, 
  MemoryStick, 
  RefreshCw, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PerformanceMetrics {
  optimization_status: {
    cache_system: boolean;
    database_optimization: boolean;
    websocket_optimization: boolean;
    monitoring_enabled: boolean;
  };
  cache_stats: {
    hit_rate_percent: number;
    l1_hit_rate_percent: number;
    l2_hit_rate_percent: number;
    memory_cache_size: number;
    total_requests: number;
    redis_available: boolean;
  };
  database_stats: {
    active_connections: number;
    avg_query_time: number;
    slow_queries: number;
    query_count: number;
  };
  websocket_stats: {
    active_connections: number;
    error_rate: number;
    total_connections: number;
    reconnections: number;
  };
  health_score: number;
  interpretations: {
    cache: string;
    database: string;
    websocket: string;
    overall: string;
  };
}

export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();

  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/performance/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setMetrics(data.data);
      } else {
        throw new Error('Failed to fetch performance metrics');
      }
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
      toast({
        title: "Error",
        description: "Failed to fetch performance metrics",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchMetrics();
  };

  const initializeOptimizations = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/performance/initialize', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Success",
          description: "Performance optimizations initialized successfully",
          variant: "default"
        });
        await fetchMetrics();
      } else {
        throw new Error('Failed to initialize optimizations');
      }
    } catch (error) {
      console.error('Error initializing optimizations:', error);
      toast({
        title: "Error",
        description: "Failed to initialize performance optimizations",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    );
  };

  if (loading && !metrics) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        <span>Loading performance metrics...</span>
      </div>
    );
  }

  if (!metrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Performance Metrics Unavailable</h3>
            <p className="text-gray-600 mb-4">Unable to load performance metrics</p>
            <Button onClick={initializeOptimizations}>
              Initialize Optimizations
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Monitor</h2>
          <p className="text-gray-600">Phase 1 optimization metrics and status</p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={initializeOptimizations}>
            <Activity className="h-4 w-4 mr-2" />
            Initialize
          </Button>
        </div>
      </div>

      {/* Health Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Overall Health Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Health Score</span>
                <span className={`text-2xl font-bold ${getHealthColor(metrics.health_score)}`}>
                  {metrics.health_score.toFixed(1)}%
                </span>
              </div>
              <Progress value={metrics.health_score} className="h-2" />
            </div>
            <div className="text-sm text-gray-600">
              {metrics.interpretations.overall}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Optimization Status */}
      <Card>
        <CardHeader>
          <CardTitle>Optimization Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              {getStatusIcon(metrics.optimization_status.cache_system)}
              <span className="text-sm">Cache System</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(metrics.optimization_status.database_optimization)}
              <span className="text-sm">Database</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(metrics.optimization_status.websocket_optimization)}
              <span className="text-sm">WebSocket</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(metrics.optimization_status.monitoring_enabled)}
              <span className="text-sm">Monitoring</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <Tabs defaultValue="cache" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="cache">Cache Performance</TabsTrigger>
          <TabsTrigger value="database">Database Performance</TabsTrigger>
          <TabsTrigger value="websocket">WebSocket Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="cache" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MemoryStick className="h-5 w-5" />
                Cache Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {metrics.cache_stats.hit_rate_percent.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">Overall Hit Rate</div>
                  <div className="text-xs text-gray-500">Target: >85%</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {metrics.cache_stats.l1_hit_rate_percent.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">L1 Memory Hit Rate</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {metrics.cache_stats.l2_hit_rate_percent.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">L2 Redis Hit Rate</div>
                </div>
              </div>
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-700">{metrics.interpretations.cache}</div>
              </div>
              <div className="mt-4 flex items-center gap-4 text-sm">
                <span>Memory Cache: {metrics.cache_stats.memory_cache_size} entries</span>
                <span>Total Requests: {metrics.cache_stats.total_requests}</span>
                <Badge variant={metrics.cache_stats.redis_available ? "default" : "destructive"}>
                  Redis: {metrics.cache_stats.redis_available ? "Available" : "Unavailable"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="database" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {(metrics.database_stats.avg_query_time * 1000).toFixed(0)}ms
                  </div>
                  <div className="text-sm text-gray-600">Avg Query Time</div>
                  <div className="text-xs text-gray-500">Target: <100ms</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {metrics.database_stats.active_connections}
                  </div>
                  <div className="text-sm text-gray-600">Active Connections</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {metrics.database_stats.slow_queries}
                  </div>
                  <div className="text-sm text-gray-600">Slow Queries</div>
                </div>
              </div>
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-700">{metrics.interpretations.database}</div>
              </div>
              <div className="mt-4 text-sm text-gray-600">
                Total Queries: {metrics.database_stats.query_count}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="websocket" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5" />
                WebSocket Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {metrics.websocket_stats.active_connections}
                  </div>
                  <div className="text-sm text-gray-600">Active Connections</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">
                    {(metrics.websocket_stats.error_rate * 100).toFixed(2)}%
                  </div>
                  <div className="text-sm text-gray-600">Error Rate</div>
                  <div className="text-xs text-gray-500">Target: <5%</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">
                    {metrics.websocket_stats.reconnections}
                  </div>
                  <div className="text-sm text-gray-600">Reconnections</div>
                </div>
              </div>
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-700">{metrics.interpretations.websocket}</div>
              </div>
              <div className="mt-4 text-sm text-gray-600">
                Total Connections: {metrics.websocket_stats.total_connections}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
