"""
Composable agent implementation for the Datagenius backend.

This module provides a composable agent implementation that can be built from
reusable components.
"""

import logging
import sys
from typing import Dict, Any, Optional, List
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import agent schemas using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, PersonaConfig = import_agent_schemas()

from .base import BaseAgent
from .components import ComponentRegistry

logger = logging.getLogger(__name__)


class ComposableAgent(BaseAgent):
    """Agent composed of reusable components."""

    def __init__(self):
        """Initialize the composable agent."""
        super().__init__()
        self.components = []

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the composable agent.

        Args:
            config: Configuration dictionary for the agent
        """
        logger.info("Initializing Composable Agent")

        # Ensure the agent has a name based on its class if not set in config
        if self.name == "base-agent":
            # Get the class name and convert it to kebab-case for consistency
            class_name = self.__class__.__name__
            # Convert CamelCase to kebab-case (e.g., ComposableMarketingAgent -> composable-marketing-agent)
            kebab_name = ''.join(['-' + c.lower() if c.isupper() else c for c in class_name]).lstrip('-')
            self.name = kebab_name
            logger.info(f"Set agent name from class: {self.name}")

        # Initialize components from configuration
        if "components" in config:
            for component_config in config["components"]:
                component_name = component_config.get("type")
                if not component_name:
                    logger.warning("Component configuration missing 'type' field")
                    continue

                logger.info(f"Creating component of type '{component_name}'")
                logger.info(f"Available component types: {ComponentRegistry.list_registered_components()}")
                component_class = ComponentRegistry.get_component_class(component_name)
                if component_class:
                    try:
                        component = component_class()
                        await component.initialize(component_config)
                        self.components.append(component)
                        logger.info(f"Added component: {component.name}")
                    except Exception as e:
                        logger.error(f"Error initializing component '{component_name}': {e}", exc_info=True)
                else:
                    logger.warning(f"Component type '{component_name}' not found in registry")

        logger.info(f"Initialized {len(self.components)} components")

        # Ensure essential tools are available in the MCP server component
        await self._ensure_essential_tools()

    async def _ensure_essential_tools(self) -> None:
        """
        Ensure that the MCP server component has all essential tools.
        """
        from .components.mcp_server import MCPServerComponent
        from .components.essential_tools import ensure_essential_tools

        # Find the MCP server component
        mcp_server = next((comp for comp in self.components if isinstance(comp, MCPServerComponent)), None)
        if mcp_server:
            logger.info("Found MCP server component, ensuring essential tools")
            await ensure_essential_tools(mcp_server)
        else:
            logger.warning("No MCP server component found, cannot ensure essential tools")

    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message using the agent's components.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """

        if not self.components:
            logger.warning("No components configured for this agent")
            return {
                "message": "This agent has no components configured. Please check the agent configuration.",
                "metadata": {
                    "error": "no_components_configured"
                }
            }

        # Create initial context using the Pydantic model
        ctx = AgentProcessingContext(
            user_id=user_id,
            message=message,
            conversation_id=conversation_id,
            initial_context=context or {},
            agent_config=PersonaConfig(**self.config) # Validate and structure agent's own config
            # agent_components is not part of AgentProcessingContext by default
        )

        # Check if we need to process a file for the persona (only for initial uploads, not follow-up questions)
        # Accessing initial_context from the typed ctx object
        should_process_file_for_persona = (
            ctx.initial_context.get("send_file_to_persona") and
            ctx.initial_context.get("data_source") and
            (not message or message.strip() == "" or len(message.strip()) < 10)  # Only for initial uploads or very short messages
        )

        if should_process_file_for_persona:
            mcp_server = next((comp for comp in self.components if comp.__class__.__name__ == "MCPServerComponent"), None)

            if mcp_server:
                logger.info("Found MCP server component, processing file for initial persona introduction")
                try:
                    result = await mcp_server.call_tool("data_access", {
                        "operation": "send_to_persona",
                        "data_source": ctx.initial_context.get("data_source"),
                        "params": {"sample_size": 10, "include_table": True}
                    })

                    if not result.get("isError", False):
                        logger.info("Successfully processed file for persona")
                        ctx.file_processed = True
                        ctx.file_data = result

                        if not message or message.strip() == "":
                            text_content = [item.get("text", "") for item in result.get("content", []) if item.get("type") == "text"]
                            if text_content:
                                ctx.response = "\n".join(text_content)
                    else:
                        logger.error(f"Error processing file: {result}")
                        error_message = "I couldn't process the attached file. Please make sure it's a valid data file."
                        if result.get("content") and len(result["content"]) > 0:
                            error_message = result["content"][0].get("text", error_message)
                        ctx.response = error_message
                        ctx.metadata["file_error"] = True # Keep metadata for simple flags
                except Exception as e:
                    logger.error(f"Error calling data_access tool: {e}")
                    ctx.response = "I encountered an error while processing the attached file."
                    ctx.metadata["file_error"] = True # Keep metadata for simple flags
        elif ctx.initial_context.get("send_file_to_persona"):
            # For follow-up questions, check if we have file context from conversation history
            logger.info("Checking for file context in follow-up message for base composable agent")

            # Look for data source in current context or conversation history
            data_source = ctx.initial_context.get("data_source")
            if not data_source and message and len(message.strip()) > 10:
                conversation_history = ctx.initial_context.get("conversation_history", [])
                for msg in reversed(conversation_history):
                    msg_metadata = msg.get("metadata", {})
                    if msg_metadata.get("data_source"):
                        data_source = msg_metadata["data_source"]
                        logger.info(f"Base composable agent: Found file context from conversation history: {data_source}")
                        # Update the context with the found data source
                        # Create a mutable copy of initial_context
                        updated_initial_context = ctx.initial_context.copy()
                        updated_initial_context["data_source"] = data_source
                        ctx.initial_context = updated_initial_context
                        break

            # Skip initial file processing and clear the flag
            logger.info("Skipping initial file processing for follow-up question - using conversation context instead")
            # Note: We can't modify the initial_context directly in the base class,
            # so derived classes should handle this if needed

        # Process context through each component
        for component in self.components:
            try:
                logger.debug(f"Processing with component: {component.name}")
                # Components will now receive and return the AgentProcessingContext object
                processed_ctx = await component.process(ctx)
                if not isinstance(processed_ctx, AgentProcessingContext):
                    logger.error(f"Component {component.name} did not return an AgentProcessingContext. Returned: {type(processed_ctx)}")
                    # Potentially revert to old ctx or handle error more gracefully
                    ctx.add_error(component.name, "Component returned invalid context type.")
                else:
                    ctx = processed_ctx # Update context with the one returned by the component
            except Exception as e:
                logger.error(f"Error in component {component.name}: {e}", exc_info=True)
                ctx.add_error(component.name, str(e)) # Use the new add_error method

        # Ensure we have a response
        if not ctx.response:
            logger.warning("No response generated by components")
            ctx.response = "I'm sorry, I wasn't able to process your request properly."

        return {
            "message": ctx.response,
            "metadata": {**ctx.metadata, "errors": [err.model_dump() for err in ctx.errors]} # Serialize errors for output
        }

    async def get_capabilities(self) -> List[str]:
        """
        Return a list of capabilities this agent supports.

        Returns:
            List of capability strings
        """
        # Combine capabilities from all components
        capabilities = set(self.capabilities)
        for component in self.components:
            capabilities.update(component.get_capabilities())
        return list(capabilities)
