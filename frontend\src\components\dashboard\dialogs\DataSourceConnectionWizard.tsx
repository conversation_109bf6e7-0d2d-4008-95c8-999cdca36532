/**
 * Data Source Connection Wizard
 * 
 * Multi-step wizard for connecting new data sources to dashboards.
 * Features:
 * - Data source type selection
 * - Connection configuration
 * - Authentication setup
 * - Test connection
 * - Data preview
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ChevronLeft,
  ChevronRight,
  Check,
  Database,
  FileText,
  Globe,
  Upload,
  Key,
  TestTube,
  Eye,
  AlertCircle,
  CheckCircle,
  Loader2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface DataSourceConnectionWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDataSourceCreated?: (dataSourceId: string) => void;
}

interface DataSourceType {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  category: string;
  requiresAuth: boolean;
  fields: Array<{
    name: string;
    label: string;
    type: 'text' | 'password' | 'number' | 'url' | 'file';
    required: boolean;
    placeholder?: string;
  }>;
}

const DATA_SOURCE_TYPES: DataSourceType[] = [
  {
    id: 'csv',
    name: 'CSV File',
    description: 'Upload and connect CSV files',
    icon: FileText,
    category: 'File',
    requiresAuth: false,
    fields: [
      { name: 'name', label: 'Data Source Name', type: 'text', required: true, placeholder: 'My CSV Data' },
      { name: 'file', label: 'CSV File', type: 'file', required: true },
      { name: 'description', label: 'Description', type: 'text', required: false, placeholder: 'Optional description' },
    ],
  },
  {
    id: 'excel',
    name: 'Excel File',
    description: 'Upload and connect Excel files',
    icon: FileText,
    category: 'File',
    requiresAuth: false,
    fields: [
      { name: 'name', label: 'Data Source Name', type: 'text', required: true, placeholder: 'My Excel Data' },
      { name: 'file', label: 'Excel File', type: 'file', required: true },
      { name: 'sheet', label: 'Sheet Name', type: 'text', required: false, placeholder: 'Sheet1' },
      { name: 'description', label: 'Description', type: 'text', required: false, placeholder: 'Optional description' },
    ],
  },
  {
    id: 'postgresql',
    name: 'PostgreSQL',
    description: 'Connect to PostgreSQL database',
    icon: Database,
    category: 'Database',
    requiresAuth: true,
    fields: [
      { name: 'name', label: 'Data Source Name', type: 'text', required: true, placeholder: 'My PostgreSQL DB' },
      { name: 'host', label: 'Host', type: 'text', required: true, placeholder: 'localhost' },
      { name: 'port', label: 'Port', type: 'number', required: true, placeholder: '5432' },
      { name: 'database', label: 'Database', type: 'text', required: true, placeholder: 'mydb' },
      { name: 'username', label: 'Username', type: 'text', required: true, placeholder: 'user' },
      { name: 'password', label: 'Password', type: 'password', required: true },
    ],
  },
  {
    id: 'api',
    name: 'REST API',
    description: 'Connect to REST API endpoints',
    icon: Globe,
    category: 'API',
    requiresAuth: true,
    fields: [
      { name: 'name', label: 'Data Source Name', type: 'text', required: true, placeholder: 'My API' },
      { name: 'url', label: 'API URL', type: 'url', required: true, placeholder: 'https://api.example.com/data' },
      { name: 'method', label: 'HTTP Method', type: 'text', required: true, placeholder: 'GET' },
      { name: 'headers', label: 'Headers (JSON)', type: 'text', required: false, placeholder: '{"Authorization": "Bearer token"}' },
      { name: 'apiKey', label: 'API Key', type: 'password', required: false },
    ],
  },
];

export const DataSourceConnectionWizard: React.FC<DataSourceConnectionWizardProps> = ({
  open,
  onOpenChange,
  onDataSourceCreated,
}) => {
  const { toast } = useToast();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isCreating, setIsCreating] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  
  // Form data
  const [formData, setFormData] = useState({
    type: '',
    config: {} as Record<string, any>,
  });

  const steps = [
    { id: 'type', title: 'Select Type', description: 'Choose your data source type' },
    { id: 'configure', title: 'Configure', description: 'Set up connection details' },
    { id: 'test', title: 'Test Connection', description: 'Verify the connection works' },
    { id: 'preview', title: 'Preview', description: 'Preview your data' },
  ];

  const selectedType = DATA_SOURCE_TYPES.find(t => t.id === formData.type);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleTestConnection = async () => {
    if (!selectedType) return;

    setIsTesting(true);
    setTestResult(null);

    try {
      // Simulate API call to test connection
      const response = await fetch('/api/data-sources/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          type: formData.type,
          config: formData.config,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setTestResult({ success: true, message: 'Connection successful!' });
        toast({
          title: "Connection Test Passed",
          description: "Your data source connection is working correctly.",
        });
      } else {
        setTestResult({ success: false, message: result.message || 'Connection failed' });
        toast({
          title: "Connection Test Failed",
          description: result.message || "Please check your configuration.",
          variant: "destructive",
        });
      }
    } catch (error) {
      setTestResult({ success: false, message: 'Network error occurred' });
      toast({
        title: "Connection Test Failed",
        description: "Network error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleCreateDataSource = async () => {
    if (!selectedType) return;

    setIsCreating(true);
    try {
      const response = await fetch('/api/data-sources', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          name: formData.config.name,
          type: formData.type,
          description: formData.config.description || '',
          config: formData.config,
          is_active: true,
        }),
      });

      if (response.ok) {
        const newDataSource = await response.json();
        
        toast({
          title: "Data Source Created",
          description: `"${formData.config.name}" has been created successfully.`,
        });

        onDataSourceCreated?.(newDataSource.id);
        onOpenChange(false);
        
        // Reset form
        setFormData({ type: '', config: {} });
        setCurrentStep(0);
        setTestResult(null);
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create data source');
      }
    } catch (error) {
      toast({
        title: "Creation Failed",
        description: error instanceof Error ? error.message : "Failed to create data source.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Type selection
        return formData.type !== '';
      case 1: // Configuration
        if (!selectedType) return false;
        return selectedType.fields
          .filter(field => field.required)
          .every(field => formData.config[field.name]);
      case 2: // Test connection
        return testResult?.success === true;
      case 3: // Preview
        return true;
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Type Selection
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {DATA_SOURCE_TYPES.map((type) => (
                <Card
                  key={type.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    formData.type === type.id && "ring-2 ring-primary"
                  )}
                  onClick={() => setFormData({ ...formData, type: type.id })}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center space-x-3">
                      <type.icon className="h-8 w-8 text-primary" />
                      <div>
                        <CardTitle className="text-sm">{type.name}</CardTitle>
                        <Badge variant="outline" className="text-xs">
                          {type.category}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      {type.description}
                    </p>
                    {type.requiresAuth && (
                      <div className="flex items-center space-x-1 mt-2">
                        <Key className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">Requires authentication</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      case 1: // Configuration
        return (
          <div className="space-y-4">
            {selectedType && (
              <div className="space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Selected Data Source</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-3">
                      <selectedType.icon className="h-6 w-6 text-primary" />
                      <div>
                        <p className="font-medium">{selectedType.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {selectedType.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="space-y-4">
                  {selectedType.fields.map((field) => (
                    <div key={field.name} className="space-y-2">
                      <Label htmlFor={field.name}>
                        {field.label}
                        {field.required && <span className="text-destructive ml-1">*</span>}
                      </Label>
                      {field.type === 'file' ? (
                        <Input
                          id={field.name}
                          type="file"
                          accept={selectedType.id === 'csv' ? '.csv' : '.xlsx,.xls'}
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setFormData({
                                ...formData,
                                config: { ...formData.config, [field.name]: file }
                              });
                            }
                          }}
                        />
                      ) : (
                        <Input
                          id={field.name}
                          type={field.type}
                          placeholder={field.placeholder}
                          value={formData.config[field.name] || ''}
                          onChange={(e) => setFormData({
                            ...formData,
                            config: { ...formData.config, [field.name]: e.target.value }
                          })}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 2: // Test Connection
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Test Connection</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Test your data source connection to ensure it's configured correctly.
                </p>
                
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={handleTestConnection}
                    disabled={isTesting}
                    className="flex items-center space-x-2"
                  >
                    {isTesting ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <TestTube className="h-4 w-4" />
                    )}
                    <span>{isTesting ? 'Testing...' : 'Test Connection'}</span>
                  </Button>
                </div>

                {testResult && (
                  <div className={cn(
                    "flex items-center space-x-2 p-3 rounded-md",
                    testResult.success 
                      ? "bg-green-50 text-green-700 border border-green-200"
                      : "bg-red-50 text-red-700 border border-red-200"
                  )}>
                    {testResult.success ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <span className="text-sm">{testResult.message}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );

      case 3: // Preview
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Data Source Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Name</Label>
                    <p className="text-sm text-muted-foreground">{formData.config.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Type</Label>
                    <p className="text-sm text-muted-foreground">{selectedType?.name}</p>
                  </div>
                </div>
                {formData.config.description && (
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-sm text-muted-foreground">{formData.config.description}</p>
                  </div>
                )}
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Connection test passed</span>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Connect Data Source</DialogTitle>
          <DialogDescription>
            Add a new data source to your dashboard.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
                    index <= currentStep
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                  )}
                >
                  {index < currentStep ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    index + 1
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "w-12 h-0.5 mx-2",
                      index < currentStep ? "bg-primary" : "bg-muted"
                    )}
                  />
                )}
              </div>
            ))}
          </div>

          {/* Step Content */}
          <div className="min-h-[400px]">
            <div className="mb-4">
              <h3 className="text-lg font-semibold">{steps[currentStep].title}</h3>
              <p className="text-sm text-muted-foreground">
                {steps[currentStep].description}
              </p>
            </div>
            <div className="max-h-80 overflow-y-auto">
              {renderStepContent()}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            {currentStep === steps.length - 1 ? (
              <Button
                onClick={handleCreateDataSource}
                disabled={!canProceed() || isCreating}
              >
                {isCreating ? 'Creating...' : 'Create Data Source'}
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                disabled={!canProceed()}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
