/**
 * Floating AI Assistant Trigger Component
 * 
 * A floating action button that triggers the AI assistant modal.
 * Features:
 * - Animated entrance/exit
 * - Notification badges for new messages
 * - Contextual positioning
 * - Accessibility support
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Bot,
  MessageCircle,
  Sparkles,
  Zap,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDashboardMode } from '@/stores/dashboard-mode-store';
import { FloatingAIAssistant } from './FloatingAIAssistant';

interface FloatingAITriggerProps {
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  onWidgetCreate?: (widget_config: any) => void;
  onSectionCreate?: (section_config: any) => void;
  onTemplateApply?: (template_id: string) => void;
  onDataConfigure?: (data_config: any) => void;
  onDataAnalyze?: (analysis_request: any) => void;
  onDashboardOptimize?: (optimization_params: any) => void;
  onCodeGenerate?: (code_request: any) => void;
  showNotifications?: boolean;
  disabled?: boolean;
}

export const FloatingAITrigger: React.FC<FloatingAITriggerProps> = ({
  className,
  position = 'bottom-right',
  onWidgetCreate,
  onSectionCreate,
  onTemplateApply,
  onDataConfigure,
  onDataAnalyze,
  onDashboardOptimize,
  onCodeGenerate,
  showNotifications = true,
  disabled = false,
}) => {
  const { current_mode } = useDashboardMode();
  const [isOpen, setIsOpen] = useState(false);
  const [hasNewMessages, setHasNewMessages] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  // Auto-hide on scroll (optional)
  useEffect(() => {
    let scrollTimeout: NodeJS.Timeout;
    
    const handleScroll = () => {
      setIsVisible(false);
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        setIsVisible(true);
      }, 150);
    };

    // Uncomment to enable auto-hide on scroll
    // window.addEventListener('scroll', handleScroll);
    // return () => {
    //   window.removeEventListener('scroll', handleScroll);
    //   clearTimeout(scrollTimeout);
    // };
  }, []);

  // Position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-6 left-6';
      case 'top-right':
        return 'top-6 right-6';
      case 'top-left':
        return 'top-6 left-6';
      case 'bottom-right':
      default:
        return 'bottom-6 right-6';
    }
  };

  // Get appropriate icon and styling based on mode
  const getModeConfig = () => {
    if (current_mode === 'simple') {
      return {
        icon: Bot,
        color: 'bg-blue-600 hover:bg-blue-700',
        label: 'AI Assistant',
        description: 'Get help building your dashboard',
      };
    } else {
      return {
        icon: Zap,
        color: 'bg-purple-600 hover:bg-purple-700',
        label: 'Datagenius Pro',
        description: 'Advanced AI assistance',
      };
    }
  };

  const modeConfig = getModeConfig();
  const IconComponent = modeConfig.icon;

  const handleToggle = () => {
    if (disabled) return;
    console.log('FloatingAI: Toggle clicked, current state:', isOpen);
    setIsOpen(!isOpen);
    if (hasNewMessages) {
      setHasNewMessages(false);
    }
  };

  if (!isVisible) return null;

  return (
    <TooltipProvider>
      <div className={cn("fixed z-[9998]", getPositionClasses(), className)}>
        {/* Main trigger button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={handleToggle}
              disabled={disabled}
              className={cn(
                "relative h-14 w-14 rounded-full transition-all duration-300 ease-in-out",
                "transform hover:scale-110 active:scale-95",
                modeConfig.color,
                "text-white border-2 border-white/20",
                isHovered && "shadow-xl",
                disabled && "opacity-50 cursor-not-allowed"
              )}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <IconComponent className="h-6 w-6" />
              
              {/* Notification badge */}
              {showNotifications && hasNewMessages && (
                <Badge
                  variant="destructive"
                  className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                >
                  !
                </Badge>
              )}
              
              {/* Pulse animation for new messages */}
              {hasNewMessages && (
                <div className="absolute inset-0 rounded-full bg-red-400 animate-ping opacity-75" />
              )}
              
              {/* Sparkle effect for simple mode */}
              {current_mode === 'simple' && isHovered && (
                <Sparkles className="absolute -top-2 -right-2 h-4 w-4 text-yellow-400 animate-pulse" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left" className="max-w-xs">
            <div className="text-center">
              <p className="font-medium">{modeConfig.label}</p>
              <p className="text-xs text-muted-foreground mt-1">
                {modeConfig.description}
              </p>
              {disabled && (
                <p className="text-xs text-red-400 mt-1">
                  Currently unavailable
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>

        {/* Quick action buttons (shown on hover) */}
        {isHovered && !disabled && !isOpen && (
          <div className="absolute bottom-16 right-0 space-y-2 animate-in slide-in-from-bottom-2 duration-200">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="secondary"
                  size="sm"
                  className="h-10 w-10 rounded-full shadow-md"
                  onClick={() => {
                    setIsOpen(true);
                    // Auto-focus on widget creation
                  }}
                >
                  <MessageCircle className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Quick Chat</p>
              </TooltipContent>
            </Tooltip>
          </div>
        )}

        {/* Floating AI Assistant Modal */}
        <FloatingAIAssistant
          isOpen={isOpen}
          onOpenChange={setIsOpen}
          onWidgetCreate={onWidgetCreate}
          onSectionCreate={onSectionCreate}
          onTemplateApply={onTemplateApply}
          onDataConfigure={onDataConfigure}
          onDataAnalyze={onDataAnalyze}
          onDashboardOptimize={onDashboardOptimize}
          onCodeGenerate={onCodeGenerate}
        />
      </div>
    </TooltipProvider>
  );
};

// Hook for managing AI assistant state across components
export const useFloatingAI = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [hasNewMessages, setHasNewMessages] = useState(false);

  const openAssistant = () => setIsOpen(true);
  const closeAssistant = () => setIsOpen(false);
  const toggleAssistant = () => setIsOpen(!isOpen);
  
  const markMessagesAsRead = () => setHasNewMessages(false);
  const notifyNewMessage = () => setHasNewMessages(true);

  return {
    isOpen,
    hasNewMessages,
    openAssistant,
    closeAssistant,
    toggleAssistant,
    markMessagesAsRead,
    notifyNewMessage,
  };
};

// Context for sharing AI assistant state
export const FloatingAIContext = React.createContext<{
  isOpen: boolean;
  hasNewMessages: boolean;
  openAssistant: () => void;
  closeAssistant: () => void;
  toggleAssistant: () => void;
  markMessagesAsRead: () => void;
  notifyNewMessage: () => void;
} | null>(null);

export const FloatingAIProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const aiState = useFloatingAI();
  
  return (
    <FloatingAIContext.Provider value={aiState}>
      {children}
    </FloatingAIContext.Provider>
  );
};

export const useFloatingAIContext = () => {
  const context = React.useContext(FloatingAIContext);
  if (!context) {
    throw new Error('useFloatingAIContext must be used within a FloatingAIProvider');
  }
  return context;
};
