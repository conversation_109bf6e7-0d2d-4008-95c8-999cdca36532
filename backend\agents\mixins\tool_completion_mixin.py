"""
Universal Tool Completion Mixin for AI Agents

This mixin provides standardized tool completion handling and conversational state management
that can be used by all AI agents in the Datagenius platform.

Key Features:
- Automatic tool-to-conversational mode transition
- Intelligent request analysis
- Tool completion state detection
- Conversational state persistence
- Backward compatibility with existing agents
"""

import time
import logging
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class ToolCompletionMixin(ABC):
    """
    Universal mixin for tool completion and conversational state management.
    
    This mixin provides standardized functionality for:
    1. Detecting tool completion states
    2. Transitioning from tool mode to conversational mode
    3. Intelligent request analysis
    4. Conversational state persistence
    
    Usage:
        class MyAgent(ToolCompletionMixin, BaseAgent):
            def get_agent_type(self) -> str:
                return "my_agent"
            
            def get_tool_indicators(self) -> List[str]:
                return ["my_form_data", "my_tool_trigger"]
    """
    
    @abstractmethod
    def get_agent_type(self) -> str:
        """
        Return the agent type identifier.
        
        Returns:
            String identifier for the agent (e.g., "marketing", "analysis", "concierge")
        """
        pass
    
    @abstractmethod
    def get_tool_indicators(self) -> List[str]:
        """
        Return list of context keys that indicate tool-triggered requests.
        
        Returns:
            List of context keys that indicate this agent's tools are being triggered
        """
        pass
    
    def get_conversational_flags(self) -> List[str]:
        """
        Return list of context keys that indicate conversational mode.
        
        Can be overridden by agents with custom conversational flags.
        
        Returns:
            List of context keys that indicate conversational mode
        """
        return [
            "skip_content_generation",
            "is_conversational", 
            "content_generation_completed",
            "tool_completed",
            "auto_conversational_mode"
        ]
    
    def analyze_request_intelligently(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Intelligently analyze the request to determine the appropriate processing mode.
        
        This method provides a comprehensive analysis that distinguishes between:
        1. Tool-triggered requests (button clicks, form submissions)
        2. Conversational follow-ups after tool calls
        3. New conversational messages
        4. Regeneration requests
        
        Args:
            context: The processing context
            
        Returns:
            Dictionary with analysis results and processing recommendations
        """
        message = context.get("message", "")
        agent_type = self.get_agent_type()
        
        logger.info(f"🔍 {agent_type.upper()}: Analyzing request intelligently")
        
        # Step 1: Check for explicit tool triggers (highest priority)
        is_tool_triggered = self._is_tool_triggered_request(context)
        if is_tool_triggered:
            return {
                "should_use_tools": True,
                "should_be_conversational": False,
                "is_follow_up": False,
                "is_tool_call": True,
                "confidence": 0.95,
                "reasoning": f"Tool-triggered request detected for {agent_type} agent",
                "trigger_type": "tool",
                "agent_type": agent_type
            }
        
        # Step 2: Check for tool completion state (high priority for conversational mode)
        if self._is_in_tool_completion_state(context):
            return {
                "should_use_tools": False,
                "should_be_conversational": True,
                "is_follow_up": True,
                "is_tool_call": False,
                "confidence": 0.98,
                "reasoning": f"Tool completion state detected for {agent_type} - automatically returning to conversational mode",
                "trigger_type": "tool_completion",
                "agent_type": agent_type
            }
        
        # Step 3: Check conversation history for recent tool calls
        conversation_history = context.get("conversation_history", [])
        recent_tool_call = self._find_recent_tool_call_in_history(conversation_history)
        
        # Step 4: Analyze message content for follow-up patterns
        is_follow_up_message = self._is_follow_up_message_pattern(message)
        
        # Step 5: Make decision based on analysis
        if recent_tool_call and is_follow_up_message:
            return {
                "should_use_tools": False,
                "should_be_conversational": True,
                "is_follow_up": True,
                "is_tool_call": False,
                "confidence": 0.85,
                "reasoning": f"Follow-up message detected after recent {agent_type} tool call",
                "trigger_type": "follow_up",
                "recent_tool_call": recent_tool_call,
                "agent_type": agent_type
            }
        elif recent_tool_call and not self._is_explicit_new_tool_request(message):
            return {
                "should_use_tools": False,
                "should_be_conversational": True,
                "is_follow_up": True,
                "is_tool_call": False,
                "confidence": 0.75,
                "reasoning": f"Message after recent {agent_type} tool call, treating as conversational",
                "trigger_type": "conversational_after_tool",
                "recent_tool_call": recent_tool_call,
                "agent_type": agent_type
            }
        else:
            # No recent tool call or explicit new tool request
            return {
                "should_use_tools": False,
                "should_be_conversational": True,
                "is_follow_up": False,
                "is_tool_call": False,
                "confidence": 0.70,
                "reasoning": f"No tool triggers or recent tool calls detected for {agent_type}, treating as conversational",
                "trigger_type": "conversational",
                "agent_type": agent_type
            }
    
    def _is_tool_triggered_request(self, context: Dict[str, Any]) -> bool:
        """
        Determine if this is a tool-triggered request for this agent.
        
        Args:
            context: Context dictionary
            
        Returns:
            True if this is a tool-triggered request
        """
        tool_indicators = self.get_tool_indicators()
        agent_type = self.get_agent_type()
        
        # Check for agent-specific tool indicators
        has_tool_data = any(
            context.get(indicator) is not None or
            context.get("metadata", {}).get(indicator) is not None or
            context.get("context", {}).get(indicator) is not None
            for indicator in tool_indicators
        )
        
        # Check for regeneration flag
        is_regeneration = (
            context.get("is_regeneration", False) or
            context.get("metadata", {}).get("is_regeneration", False)
        )
        
        # Check for explicit tool call indicators
        has_tool_indicators = (
            context.get("tool_call", False) or
            context.get("button_triggered", False) or
            context.get("form_submission", False)
        )
        
        result = has_tool_data or is_regeneration or has_tool_indicators
        
        if result:
            logger.info(f"🔧 {agent_type.upper()}: Tool-triggered request detected - "
                       f"tool_data={has_tool_data}, regeneration={is_regeneration}, "
                       f"tool_indicators={has_tool_indicators}")
        
        return result
    
    def _is_in_tool_completion_state(self, context: Dict[str, Any]) -> bool:
        """
        Check if the context indicates we're in a tool completion state.
        
        Args:
            context: Context dictionary
            
        Returns:
            True if in tool completion state
        """
        agent_type = self.get_agent_type()
        
        # CRITICAL: First, ensure there are no active tool triggers
        if self._is_tool_triggered_request(context):
            logger.info(f"🔧 {agent_type.upper()}: Tool triggers detected - not in completion state")
            return False
        
        # Check for explicit tool completion flags in current context
        conversational_flags = self.get_conversational_flags()
        if any(context.get(flag, False) for flag in conversational_flags):
            logger.info(f"✅ {agent_type.upper()}: Tool completion state detected from context flags")
            return True
        
        # Check metadata for tool completion
        metadata = context.get("metadata", {})
        if (metadata.get("tool_completion_reset", False) or
            metadata.get("conversational_state_restored", False)):
            logger.info(f"✅ {agent_type.upper()}: Tool completion state detected from metadata")
            return True
        
        # Check conversation history for recent tool completion
        conversation_history = context.get("conversation_history", [])
        message = context.get("message", "")
        
        if (conversation_history and len(conversation_history) > 0 and
            self._is_follow_up_message_pattern(message)):
            
            # Check the last AI message for tool completion indicators
            for msg in reversed(conversation_history):
                if msg.get("sender") == "ai":
                    msg_metadata = msg.get("metadata", {}) or {}
                    
                    # Check for tool completion metadata
                    if (msg_metadata.get("tool_execution", {}).get("status") == "completed" or
                        msg_metadata.get("conversational_state", {}).get("tool_completed", False) or
                        msg_metadata.get("conversational_state", {}).get("auto_return_to_conversational", False) or
                        msg_metadata.get("tool_completion_reset", False)):
                        logger.info(f"✅ {agent_type.upper()}: Tool completion state detected from conversation history")
                        return True
                    
                    # Only check the most recent AI message
                    break
        
        return False

    def _find_recent_tool_call_in_history(self, conversation_history: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Find the most recent tool call in conversation history."""
        if not conversation_history:
            return None

        agent_type = self.get_agent_type()

        # Look through last 5 messages for tool calls
        recent_messages = conversation_history[-5:] if len(conversation_history) > 5 else conversation_history

        for msg in reversed(recent_messages):
            if msg.get("sender") == "ai":
                metadata = msg.get("metadata", {}) or {}

                # Check for tool call indicators
                if (metadata.get("generated_content", False) or
                    metadata.get("tool_call_result", False) or
                    metadata.get("conversational_state", {}).get("content_generated", False) or
                    metadata.get("content_generation_completed", False) or
                    metadata.get("tool_execution", {}).get("status") == "completed"):

                    return {
                        "timestamp": metadata.get("timestamp", time.time()),
                        "tool_name": metadata.get("tool_name", f"{agent_type}_tool"),
                        "content_type": metadata.get("content_type", metadata.get("task_type", "unknown")),
                        "agent_type": agent_type,
                        "messages_ago": len([m for m in reversed(recent_messages)
                                           if m.get("sender") == "user" and
                                           conversation_history.index(m) > conversation_history.index(msg)])
                    }
        return None

    def _is_follow_up_message_pattern(self, message: str) -> bool:
        """Check if the message matches follow-up question patterns."""
        if not message:
            return False

        message_lower = message.lower().strip()

        # Strong follow-up indicators
        strong_patterns = [
            "what else", "any other", "more ideas", "additional", "furthermore",
            "what about", "how about", "any more", "other ways", "what other",
            "anything else", "more suggestions", "other options", "also",
            "can you also", "do you have more", "any additional", "tell me more",
            "explain more", "elaborate", "expand on", "go deeper", "more details"
        ]

        # Weak follow-up indicators (need more context)
        weak_patterns = [
            "can you", "do you", "what", "how", "why", "when", "where", "which"
        ]

        # Check for strong patterns first
        if any(pattern in message_lower for pattern in strong_patterns):
            return True

        # Check for weak patterns with additional context
        if any(pattern in message_lower for pattern in weak_patterns):
            # Additional checks for weak patterns
            if len(message.split()) <= 10:  # Short questions are more likely follow-ups
                return True

        return False

    def _is_explicit_new_tool_request(self, message: str) -> bool:
        """Check if the message is an explicit request for new tool execution."""
        if not message:
            return False

        message_lower = message.lower().strip()
        agent_type = self.get_agent_type()

        # Generic new request patterns
        explicit_patterns = [
            "create a new", "generate a new", "make a new", "develop a new",
            "create another", "generate another", "make another", "develop another",
            "start over", "from scratch", "completely new", "different approach"
        ]

        # Agent-specific patterns can be added by subclasses
        agent_specific_patterns = self._get_agent_specific_new_request_patterns()

        all_patterns = explicit_patterns + agent_specific_patterns
        return any(pattern in message_lower for pattern in all_patterns)

    def _get_agent_specific_new_request_patterns(self) -> List[str]:
        """
        Get agent-specific patterns for new tool requests.

        Can be overridden by specific agents to add their own patterns.

        Returns:
            List of agent-specific patterns
        """
        return []

    def reset_context_for_conversational_mode(self, context: Dict[str, Any]) -> None:
        """
        Reset context state after tool completion to enable conversational follow-ups.

        This method implements the automatic tool-to-conversation transition by:
        1. Clearing tool execution flags and state
        2. Preserving ALL routing and persona information
        3. Setting conversational mode flags
        4. Adding tool completion metadata for UI indicators

        Args:
            context: Context dictionary to reset
        """
        agent_type = self.get_agent_type()
        logger.info(f"🔄 {agent_type.upper()}: Resetting context state for conversational mode after tool execution")

        # Preserve critical routing information
        preserved_keys = {
            "current_persona": context.get("current_persona"),
            "persona_id": context.get("persona_id"),
            "agent_id": context.get("agent_id"),
            "user_id": context.get("user_id"),
            "conversation_id": context.get("conversation_id"),
            "message": context.get("message"),
            "conversation_history": context.get("conversation_history"),
            "agent_components": context.get("agent_components"),
            "response": context.get("response"),
            "metadata": context.get("metadata", {})
        }

        # Set default persona information if not present
        if not preserved_keys["current_persona"]:
            preserved_keys["current_persona"] = f"composable-{agent_type}-ai"
            preserved_keys["persona_id"] = f"composable-{agent_type}-ai"
            preserved_keys["agent_id"] = f"composable-{agent_type}-ai"
            logger.info(f"🔄 {agent_type.upper()}: Set default persona information")

        logger.info(f"🔄 {agent_type.upper()}: Preserved routing keys - "
                   f"current_persona={preserved_keys['current_persona']}, "
                   f"persona_id={preserved_keys['persona_id']}, "
                   f"agent_id={preserved_keys['agent_id']}")

        # Mark tool data as used to prevent regeneration
        tool_indicators = self.get_tool_indicators()
        for indicator in tool_indicators:
            if indicator in context:
                context[f"{indicator}_used"] = True
                logger.info(f"🔄 {agent_type.upper()}: Marked {indicator} as used to prevent regeneration")

        # Update preserved metadata
        preserved_keys["metadata"].update({
            "tool_execution": {
                "tool_name": f"generate_{agent_type}_content",
                "status": "completed",
                "completion_time": time.time(),
                "result_type": "content_generation"
            },
            "tool_completion_reset": True,
            "auto_conversational_mode": True,
            "tool_completion_timestamp": time.time(),
            "next_message_mode": "conversational"
        })

        # Set conversational mode flags for next message processing
        conversational_flags = self.get_conversational_flags()
        for flag in conversational_flags:
            context[flag] = True

        # Set additional conversational state
        context["is_follow_up_question"] = False
        context["content_generation_completed"] = True

        # Restore preserved keys
        for key, value in preserved_keys.items():
            if value is not None:
                context[key] = value

        # Clear any cached analysis that might influence next message processing
        context.pop("llm_analysis", None)
        context.pop("llm_intent", None)
        context.pop("request_analysis", None)

        logger.info(f"✅ {agent_type.upper()}: Context reset complete - next messages will be conversational")
        logger.info(f"🔄 {agent_type.upper()}: Conversational flags set for {len(conversational_flags)} flags")

    def should_skip_tool_execution(self, context: Dict[str, Any]) -> bool:
        """
        Determine if tool execution should be skipped based on conversational state.

        Args:
            context: Context dictionary

        Returns:
            True if tool execution should be skipped
        """
        agent_type = self.get_agent_type()

        # Check if we're in conversational mode
        conversational_flags = self.get_conversational_flags()
        is_conversational = any(context.get(flag, False) for flag in conversational_flags)

        if is_conversational:
            logger.info(f"🗣️ {agent_type.upper()}: Skipping tool execution - in conversational mode")
            return True

        # Check if this is a follow-up after tool completion
        analysis = self.analyze_request_intelligently(context)
        if analysis["should_be_conversational"]:
            logger.info(f"🗣️ {agent_type.upper()}: Skipping tool execution - intelligent analysis suggests conversational mode")
            return True

        return False

    def prepare_conversational_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare context for conversational response generation.

        Args:
            context: Original context

        Returns:
            Enhanced context for conversational responses
        """
        agent_type = self.get_agent_type()

        # Enhanced user context for conversational responses
        user_context = {
            f"is_{agent_type}_agent": True,
            "persona": agent_type,
            "conversational_response": True,
            f"{agent_type}_expertise": True,
            "is_follow_up_question": context.get("is_follow_up_question", False),
            f"{agent_type}_conversation_context": context.get(f"{agent_type}_conversation_context", False)
        }

        return {
            "message": context.get("message", ""),
            "conversation_history": context.get("conversation_history", []),
            "user_context": user_context,
            "intent_type": f"{agent_type}_advice" if context.get("is_follow_up_question") else "general_question",
            "confidence": 0.9 if context.get("is_follow_up_question") else 0.8,
            "is_continuing_conversation": len(context.get("conversation_history", [])) > 0,
            "temperature": 0.7
        }

    def add_conversational_state_metadata(self, context: Dict[str, Any], task_info: Optional[Dict[str, Any]] = None) -> None:
        """
        Add conversational state metadata that will be saved to database.

        Args:
            context: Context dictionary to update
            task_info: Optional task information for metadata
        """
        agent_type = self.get_agent_type()

        if "metadata" not in context:
            context["metadata"] = {}

        # Add conversational state flags that will be saved to database
        context["metadata"]["conversational_state"] = {
            "content_generated": True,
            "next_message_should_be_conversational": True,
            "content_type": task_info.get("task_type", "unknown") if task_info else "unknown",
            "generation_timestamp": time.time(),
            "tool_completed": True,
            "auto_return_to_conversational": True,
            "agent_type": agent_type
        }

        logger.info(f"📝 {agent_type.upper()}: Added conversational state metadata to context")
