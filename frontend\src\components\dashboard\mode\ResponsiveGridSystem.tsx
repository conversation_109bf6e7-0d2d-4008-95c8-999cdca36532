/**
 * Responsive Grid System Component
 * 
 * Enhanced responsive grid system with breakpoint-aware layouts,
 * touch-friendly interactions, and mobile-first design patterns.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';

interface GridBreakpoint {
  name: string;
  min_width: number;
  columns: number;
  gap: string;
  padding: string;
}

interface GridItem {
  id: string;
  content: React.ReactNode;
  span?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  order?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  height?: 'auto' | 'fixed' | 'fill';
  min_height?: string;
  max_height?: string;
}

interface ResponsiveGridSystemProps {
  className?: string;
  items: GridItem[];
  breakpoints?: GridBreakpoint[];
  enable_reordering?: boolean;
  enable_resizing?: boolean;
  on_item_reorder?: (from_index: number, to_index: number) => void;
  on_item_resize?: (item_id: string, new_span: any) => void;
  on_layout_change?: (layout: any) => void;
}

const DEFAULT_BREAKPOINTS: GridBreakpoint[] = [
  { name: 'xs', min_width: 0, columns: 1, gap: 'gap-3', padding: 'p-3' },
  { name: 'sm', min_width: 640, columns: 2, gap: 'gap-4', padding: 'p-4' },
  { name: 'md', min_width: 768, columns: 3, gap: 'gap-4', padding: 'p-4' },
  { name: 'lg', min_width: 1024, columns: 4, gap: 'gap-6', padding: 'p-6' },
  { name: 'xl', min_width: 1280, columns: 6, gap: 'gap-6', padding: 'p-6' },
];

export const ResponsiveGridSystem: React.FC<ResponsiveGridSystemProps> = ({
  className,
  items,
  breakpoints = DEFAULT_BREAKPOINTS,
  enable_reordering = false,
  enable_resizing = false,
  on_item_reorder,
  on_item_resize,
  on_layout_change,
}) => {
  const [current_breakpoint, set_current_breakpoint] = useState<GridBreakpoint>(breakpoints[0]);
  const [viewport_width, set_viewport_width] = useState(0);
  const [dragging_item, set_dragging_item] = useState<string | null>(null);
  const [drag_over_index, set_drag_over_index] = useState<number | null>(null);
  const [touch_start, set_touch_start] = useState<{ x: number; y: number } | null>(null);
  const grid_ref = useRef<HTMLDivElement>(null);

  // Update breakpoint based on viewport width
  useEffect(() => {
    const update_breakpoint = () => {
      const width = window.innerWidth;
      set_viewport_width(width);
      
      // Find the largest breakpoint that fits
      const matching_breakpoint = breakpoints
        .slice()
        .reverse()
        .find(bp => width >= bp.min_width) || breakpoints[0];
      
      set_current_breakpoint(matching_breakpoint);
    };

    update_breakpoint();
    window.addEventListener('resize', update_breakpoint);
    
    return () => window.removeEventListener('resize', update_breakpoint);
  }, [breakpoints]);

  // Get responsive span for current breakpoint
  const get_item_span = useCallback((item: GridItem): number => {
    const span = item.span || {};
    const breakpoint_name = current_breakpoint.name as keyof typeof span;
    return span[breakpoint_name] || 1;
  }, [current_breakpoint]);

  // Get responsive order for current breakpoint
  const get_item_order = useCallback((item: GridItem): number => {
    const order = item.order || {};
    const breakpoint_name = current_breakpoint.name as keyof typeof order;
    return order[breakpoint_name] || 0;
  }, [current_breakpoint]);

  // Sort items by responsive order
  const sorted_items = [...items].sort((a, b) => {
    const order_a = get_item_order(a);
    const order_b = get_item_order(b);
    return order_a - order_b;
  });

  // Touch event handlers for mobile reordering
  const handle_touch_start = (e: React.TouchEvent, item_id: string) => {
    if (!enable_reordering) return;
    
    const touch = e.touches[0];
    set_touch_start({ x: touch.clientX, y: touch.clientY });
    set_dragging_item(item_id);
  };

  const handle_touch_move = (e: React.TouchEvent) => {
    if (!dragging_item || !touch_start) return;
    
    const touch = e.touches[0];
    const distance = Math.sqrt(
      Math.pow(touch.clientX - touch_start.x, 2) + 
      Math.pow(touch.clientY - touch_start.y, 2)
    );
    
    // Start drag if moved enough distance
    if (distance > 10) {
      e.preventDefault();
      
      // Find the element under the touch point
      const element_under = document.elementFromPoint(touch.clientX, touch.clientY);
      const grid_item = element_under?.closest('[data-grid-item]');
      
      if (grid_item) {
        const index = parseInt(grid_item.getAttribute('data-index') || '0');
        set_drag_over_index(index);
      }
    }
  };

  const handle_touch_end = () => {
    if (dragging_item && drag_over_index !== null) {
      const from_index = sorted_items.findIndex(item => item.id === dragging_item);
      if (from_index !== -1 && from_index !== drag_over_index) {
        on_item_reorder?.(from_index, drag_over_index);
      }
    }
    
    set_dragging_item(null);
    set_drag_over_index(null);
    set_touch_start(null);
  };

  // Mouse event handlers for desktop reordering
  const handle_drag_start = (e: React.DragEvent, item_id: string) => {
    if (!enable_reordering) return;
    
    set_dragging_item(item_id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handle_drag_over = (e: React.DragEvent, index: number) => {
    if (!enable_reordering) return;
    
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    set_drag_over_index(index);
  };

  const handle_drop = (e: React.DragEvent, index: number) => {
    if (!enable_reordering || !dragging_item) return;
    
    e.preventDefault();
    const from_index = sorted_items.findIndex(item => item.id === dragging_item);
    
    if (from_index !== -1 && from_index !== index) {
      on_item_reorder?.(from_index, index);
    }
    
    set_dragging_item(null);
    set_drag_over_index(null);
  };

  // Generate grid CSS classes
  const get_grid_classes = () => {
    const base_classes = [
      'grid',
      'auto-rows-fr',
      current_breakpoint.gap,
      current_breakpoint.padding,
    ];

    // Add responsive grid columns
    base_classes.push(`grid-cols-${current_breakpoint.columns}`);

    return cn(base_classes);
  };

  // Generate item CSS classes
  const get_item_classes = (item: GridItem, index: number) => {
    const span = get_item_span(item);
    const is_dragging = dragging_item === item.id;
    const is_drag_target = drag_over_index === index;
    
    const classes = [
      'transition-all',
      'duration-200',
      'ease-in-out',
    ];

    // Column span
    if (span > 1) {
      classes.push(`col-span-${Math.min(span, current_breakpoint.columns)}`);
    }

    // Height classes
    switch (item.height) {
      case 'fixed':
        classes.push('h-64');
        break;
      case 'fill':
        classes.push('h-full');
        break;
      default:
        classes.push('h-auto');
    }

    // Min/max height
    if (item.min_height) {
      classes.push(`min-h-[${item.min_height}]`);
    }
    if (item.max_height) {
      classes.push(`max-h-[${item.max_height}]`);
    }

    // Drag states
    if (is_dragging) {
      classes.push('opacity-50', 'scale-105', 'z-50');
    }
    if (is_drag_target && dragging_item && dragging_item !== item.id) {
      classes.push('ring-2', 'ring-blue-500', 'ring-opacity-50');
    }

    // Touch-friendly sizing for mobile
    if (viewport_width < 768) {
      classes.push('touch-manipulation');
    }

    return cn(classes);
  };

  // Notify layout changes
  useEffect(() => {
    const layout = {
      breakpoint: current_breakpoint.name,
      viewport_width,
      columns: current_breakpoint.columns,
      items: sorted_items.map((item, index) => ({
        id: item.id,
        index,
        span: get_item_span(item),
        order: get_item_order(item),
      })),
    };
    
    on_layout_change?.(layout);
  }, [current_breakpoint, viewport_width, sorted_items, get_item_span, get_item_order, on_layout_change]);

  return (
    <div
      ref={grid_ref}
      className={cn(get_grid_classes(), className)}
      onTouchMove={handle_touch_move}
      onTouchEnd={handle_touch_end}
    >
      {sorted_items.map((item, index) => (
        <div
          key={item.id}
          data-grid-item
          data-index={index}
          className={get_item_classes(item, index)}
          draggable={enable_reordering}
          onDragStart={(e) => handle_drag_start(e, item.id)}
          onDragOver={(e) => handle_drag_over(e, index)}
          onDrop={(e) => handle_drop(e, index)}
          onTouchStart={(e) => handle_touch_start(e, item.id)}
        >
          {/* Resize handles for desktop */}
          {enable_resizing && viewport_width >= 768 && (
            <div className="absolute top-0 right-0 w-4 h-4 cursor-se-resize opacity-0 hover:opacity-100 transition-opacity">
              <div className="w-full h-full bg-blue-500 rounded-bl-lg" />
            </div>
          )}
          
          {/* Drag handle for mobile */}
          {enable_reordering && viewport_width < 768 && (
            <div className="absolute top-2 right-2 w-6 h-6 cursor-grab active:cursor-grabbing opacity-50 hover:opacity-100 transition-opacity">
              <div className="w-full h-full bg-gray-400 rounded" style={{
                backgroundImage: `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='2' stroke-dasharray='2%2c2' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e")`,
              }} />
            </div>
          )}
          
          {item.content}
        </div>
      ))}
    </div>
  );
};

// Hook for responsive grid utilities
export const useResponsiveGrid = (breakpoints: GridBreakpoint[] = DEFAULT_BREAKPOINTS) => {
  const [current_breakpoint, set_current_breakpoint] = useState<GridBreakpoint>(breakpoints[0]);
  const [viewport_width, set_viewport_width] = useState(0);
  const [is_mobile, set_is_mobile] = useState(false);
  const [is_tablet, set_is_tablet] = useState(false);

  useEffect(() => {
    const update_breakpoint = () => {
      const width = window.innerWidth;
      set_viewport_width(width);
      set_is_mobile(width < 768);
      set_is_tablet(width >= 768 && width < 1024);
      
      const matching_breakpoint = breakpoints
        .slice()
        .reverse()
        .find(bp => width >= bp.min_width) || breakpoints[0];
      
      set_current_breakpoint(matching_breakpoint);
    };

    update_breakpoint();
    window.addEventListener('resize', update_breakpoint);
    
    return () => window.removeEventListener('resize', update_breakpoint);
  }, [breakpoints]);

  const get_responsive_span = (spans: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number }) => {
    const breakpoint_name = current_breakpoint.name as keyof typeof spans;
    return spans[breakpoint_name] || spans.xs || 1;
  };

  const get_responsive_value = <T,>(values: { xs?: T; sm?: T; md?: T; lg?: T; xl?: T }, fallback: T): T => {
    const breakpoint_name = current_breakpoint.name as keyof typeof values;
    return values[breakpoint_name] || values.xs || fallback;
  };

  return {
    current_breakpoint,
    viewport_width,
    is_mobile,
    is_tablet,
    is_desktop: !is_mobile && !is_tablet,
    get_responsive_span,
    get_responsive_value,
    columns: current_breakpoint.columns,
    gap: current_breakpoint.gap,
    padding: current_breakpoint.padding,
  };
};

// Utility component for responsive containers
export const ResponsiveContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
  max_width?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  center?: boolean;
}> = ({ children, className, max_width = 'full', center = true }) => {
  const max_width_classes = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
  };

  return (
    <div className={cn(
      'w-full',
      max_width_classes[max_width],
      center && 'mx-auto',
      'px-4 sm:px-6 lg:px-8',
      className
    )}>
      {children}
    </div>
  );
};

// Utility component for responsive spacing
export const ResponsiveSpacing: React.FC<{
  children: React.ReactNode;
  className?: string;
  spacing?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
  };
}> = ({ children, className, spacing = {} }) => {
  const { get_responsive_value } = useResponsiveGrid();
  const current_spacing = get_responsive_value(spacing, 'space-y-4');

  return (
    <div className={cn(current_spacing, className)}>
      {children}
    </div>
  );
};
