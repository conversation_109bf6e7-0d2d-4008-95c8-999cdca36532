/* Enhanced Markdown Styling for AI Agent Responses */

.markdown-content {
  line-height: 1.6;
}

/* Headings */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.3;
}

.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child,
.markdown-content h4:first-child,
.markdown-content h5:first-child,
.markdown-content h6:first-child {
  margin-top: 0;
}

/* Paragraphs */
.markdown-content p {
  margin-bottom: 1em;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

/* Lists */
.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content li {
  margin-bottom: 0.25em;
  line-height: 1.5;
}

.markdown-content li > p {
  margin-bottom: 0.5em;
}

.markdown-content li:last-child {
  margin-bottom: 0;
}

/* Nested lists */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

.markdown-content ul ul {
  list-style-type: circle;
}

.markdown-content ul ul ul {
  list-style-type: square;
}

/* Code */
.markdown-content code {
  background-color: #f1f5f9;
  color: #1e293b;
  padding: 0.125em 0.375em;
  border-radius: 0.25em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
  font-weight: 500;
  border: 1px solid #cbd5e1;
}

.markdown-content pre {
  background-color: #f8fafc;
  color: #1e293b;
  padding: 1.25em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin-bottom: 1em;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  border: none;
  color: inherit;
}

/* Blockquotes */
.markdown-content blockquote {
  border-left: 4px solid rgba(0, 0, 0, 0.1);
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  color: rgba(0, 0, 0, 0.7);
}

/* Links */
.markdown-content a {
  color: #3b82f6;
  text-decoration: underline;
  text-decoration-color: rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.markdown-content a:hover {
  color: #1d4ed8;
  text-decoration-color: #1d4ed8;
}

/* Tables */
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
  border: 2px solid #374151;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  background-color: #ffffff;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #374151;
  padding: 0.875rem;
  text-align: left;
  vertical-align: top;
}

.markdown-content th {
  background-color: #1f2937;
  font-weight: 700;
  color: #ffffff;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid #374151;
}

.markdown-content td {
  color: #111827;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: #ffffff;
}

.markdown-content tbody tr:nth-child(even) {
  background-color: #f8fafc;
}

.markdown-content tbody tr:nth-child(even) td {
  background-color: #f8fafc;
}

.markdown-content tbody tr:hover {
  background-color: #e0f2fe;
}

.markdown-content tbody tr:hover td {
  background-color: #e0f2fe;
}

/* Responsive table wrapper */
.markdown-content .table-responsive {
  overflow-x: auto;
  margin-bottom: 1em;
}

.markdown-content .table-responsive table {
  margin-bottom: 0;
  min-width: 600px;
}

/* Horizontal rules */
.markdown-content hr {
  border: none;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin: 2em 0;
}

/* Strong and emphasis */
.markdown-content strong {
  font-weight: 600;
}

.markdown-content em {
  font-style: italic;
}

/* Special styling for AI message content in dark theme (user messages) */
.bg-brand-500 .markdown-content code {
  background-color: rgba(255, 255, 255, 0.9);
  color: #1e293b;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-weight: 500;
}

.bg-brand-500 .markdown-content pre {
  background-color: rgba(255, 255, 255, 0.95);
  color: #1e293b;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.bg-brand-500 .markdown-content blockquote {
  border-left-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

.bg-brand-500 .markdown-content a {
  color: #93c5fd;
}

.bg-brand-500 .markdown-content a:hover {
  color: #dbeafe;
}

.bg-brand-500 .markdown-content table {
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 0.95);
}

.bg-brand-500 .markdown-content th,
.bg-brand-500 .markdown-content td {
  border-color: rgba(255, 255, 255, 0.4);
}

.bg-brand-500 .markdown-content th {
  background-color: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  font-weight: 700;
}

.bg-brand-500 .markdown-content td {
  color: #111827;
  background-color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
}

.bg-brand-500 .markdown-content tbody tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.9);
}

.bg-brand-500 .markdown-content tbody tr:nth-child(even) td {
  background-color: rgba(255, 255, 255, 0.9);
}

.bg-brand-500 .markdown-content tbody tr:hover {
  background-color: rgba(255, 255, 255, 1);
}

.bg-brand-500 .markdown-content tbody tr:hover td {
  background-color: rgba(255, 255, 255, 1);
}

.bg-brand-500 .markdown-content hr {
  border-top-color: rgba(255, 255, 255, 0.2);
}

/* Ensure proper spacing for action buttons */
.markdown-content .inline {
  display: inline;
}

.markdown-content .inline p {
  display: inline;
  margin: 0;
}

/* Improve readability with better spacing */
.markdown-content > *:first-child {
  margin-top: 0;
}

.markdown-content > *:last-child {
  margin-bottom: 0;
}
