/**
 * createReactSyncApp
 *
 * This function creates a synchronized React application instance.
 * It's used for managing state synchronization between components,
 * particularly in the context of the concierge workflow visualization.
 */

// Define the types for the function
interface SyncAppOptions {
  element?: HTMLElement | null;
  initialState?: Record<string, any>;
  onSync?: (state: Record<string, any>) => void;
  debug?: boolean;
  rootElement?: HTMLElement | null; // For backward compatibility
  syncInterval?: number; // For backward compatibility
}

interface SyncAppInstance {
  render: (element: HTMLElement) => void;
  unmount: () => void;
  getState: () => Record<string, any>;
  setState: (state: Record<string, any>) => void;
  sync: () => void;
  // For backward compatibility
  mount: (element: HTMLElement) => void;
  subscribe: (callback: (state: Record<string, any>) => void) => () => void;
}

/**
 * Creates a synchronized React application
 * This function is used by the workflow visualization components
 */
export function createReactSyncApp(options: SyncAppOptions = {}): SyncAppInstance {
  const {
    element = null,
    rootElement = null, // For backward compatibility
    initialState = {},
    onSync = () => {},
    debug = false,
    syncInterval = 1000 // For backward compatibility
  } = options;

  // Internal state
  let currentState = { ...initialState };
  let isMounted = false;
  let subscribers: ((state: Record<string, any>) => void)[] = [onSync]; // Include onSync as a subscriber
  let syncTimer: number | null = null;

  // Debug logging
  const log = (message: string, data?: any) => {
    if (debug) {
      console.log(`[ReactSyncApp] ${message}`, data || '');
    }
  };

  // Notify all subscribers of state changes
  const notifySubscribers = () => {
    subscribers.forEach(callback => callback(currentState));
  };

  // Start sync timer (for backward compatibility)
  const startSyncTimer = () => {
    if (syncTimer !== null) return;

    syncTimer = window.setInterval(() => {
      log('Syncing state via timer');
      notifySubscribers();
    }, syncInterval);
  };

  // Stop sync timer (for backward compatibility)
  const stopSyncTimer = () => {
    if (syncTimer === null) return;

    window.clearInterval(syncTimer);
    syncTimer = null;
  };

  // Render the app to a DOM element
  const render = (targetElement: HTMLElement) => {
    if (isMounted) {
      log('App is already mounted');
      return;
    }

    isMounted = true;
    log('App mounted to element', targetElement);

    // Initial sync
    sync();
    startSyncTimer(); // For backward compatibility
  };

  // Mount alias for backward compatibility
  const mount = (targetElement: HTMLElement) => {
    render(targetElement);
  };

  // Unmount the app
  const unmount = () => {
    if (!isMounted) {
      log('App is not mounted');
      return;
    }

    isMounted = false;
    stopSyncTimer(); // For backward compatibility
    log('App unmounted');
  };

  // Get the current state
  const getState = (): Record<string, any> => {
    return { ...currentState };
  };

  // Set a new state
  const setState = (newState: Record<string, any>) => {
    currentState = { ...currentState, ...newState };
    log('State updated', currentState);

    // Trigger sync after state update
    sync();
  };

  // Sync the state with any listeners
  const sync = () => {
    if (isMounted) {
      log('Syncing state', currentState);
      notifySubscribers();
    }
  };

  // Subscribe to state changes (for backward compatibility)
  const subscribe = (callback: (state: Record<string, any>) => void) => {
    subscribers.push(callback);
    log('New subscriber added');

    // Return unsubscribe function
    return () => {
      subscribers = subscribers.filter(cb => cb !== callback);
      log('Subscriber removed');
    };
  };

  // Initialize if element is provided
  if (element || rootElement) {
    render(element || rootElement);
  }

  // Return the public API
  return {
    render,
    mount, // Alias for backward compatibility
    unmount,
    getState,
    setState,
    sync,
    subscribe // For backward compatibility
  };
}

// Export as both named and default export for compatibility
export default createReactSyncApp;
