#!/usr/bin/env python3
"""
Script to install dependencies for Hugging Face embeddings.

This script checks for and installs the required packages for using
Hugging Face embedding models with mem0ai.
"""

import subprocess
import sys
import logging
from typing import List, Dict

logger = logging.getLogger(__name__)


def check_package_installed(package_name: str) -> bool:
    """Check if a package is installed."""
    try:
        __import__(package_name.replace('-', '_'))
        return True
    except ImportError:
        return False


def install_package(package_name: str, version: str = None) -> bool:
    """Install a package using pip."""
    try:
        cmd = [sys.executable, "-m", "pip", "install"]
        if version:
            cmd.append(f"{package_name}=={version}")
        else:
            cmd.append(package_name)
        
        print(f"Installing {package_name}...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ Successfully installed {package_name}")
            return True
        else:
            print(f"✗ Failed to install {package_name}: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Error installing {package_name}: {e}")
        return False


def main():
    """Main function to install embedding dependencies."""
    print("🔧 Installing Hugging Face embedding dependencies...")
    
    # Required packages for Hugging Face embeddings
    required_packages = {
        "sentence-transformers": None,  # Latest version
        "torch": None,  # Latest version
        "transformers": None,  # Latest version
        "tokenizers": None,  # Latest version
    }
    
    # Optional packages for better performance
    optional_packages = {
        "accelerate": None,  # For faster model loading
        "safetensors": None,  # For safer model loading
    }
    
    # Check and install required packages
    print("\n📦 Checking required packages...")
    failed_required = []
    
    for package, version in required_packages.items():
        if check_package_installed(package):
            print(f"✓ {package} is already installed")
        else:
            if not install_package(package, version):
                failed_required.append(package)
    
    # Check and install optional packages
    print("\n🎯 Checking optional packages...")
    failed_optional = []
    
    for package, version in optional_packages.items():
        if check_package_installed(package):
            print(f"✓ {package} is already installed")
        else:
            if not install_package(package, version):
                failed_optional.append(package)
    
    # Summary
    print("\n📋 Installation Summary:")
    
    if not failed_required:
        print("✅ All required packages installed successfully!")
    else:
        print(f"❌ Failed to install required packages: {', '.join(failed_required)}")
    
    if failed_optional:
        print(f"⚠️  Failed to install optional packages: {', '.join(failed_optional)}")
        print("   (These are not critical for basic functionality)")
    
    # Test installation
    print("\n🧪 Testing installation...")
    try:
        import sentence_transformers
        print("✓ sentence-transformers import successful")
        
        # Try to load a small model to verify everything works
        from sentence_transformers import SentenceTransformer
        print("✓ SentenceTransformer class import successful")
        
        print("\n🎉 Installation completed successfully!")
        print("\nYou can now use Hugging Face embedding models with the following configuration:")
        print("""
{
    "embedding_provider": "huggingface",
    "embedding_model": "BAAI/bge-small-en-v1.5"
}
        """)
        
    except ImportError as e:
        print(f"❌ Installation test failed: {e}")
        print("Please check the installation and try again.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
