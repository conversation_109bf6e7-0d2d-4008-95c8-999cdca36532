
import React, { useState, useEffect } from 'react';
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
// Select components removed - metrics view selectors removed
// Chart imports removed - metrics dashboard functionality removed
import {
  Users,
  Activity,
  RefreshCw,
  Download,
  Plus,
  Settings,
  Move,
  Copy,
  Trash2,
  Upload,
  Eye,
  EyeOff,
  Palette,
  Grid,
  BarChart3,
  Table,
  Gauge,
  Network,
  TreePine,
  Map,
  Type,
  Image,
  ChevronDown,
  Edit
} from 'lucide-react';
// Metrics dashboard hooks removed
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';
import { useDashboardManagement } from '@/hooks/use-dashboard-management';
import { useWidgetDataUpdates } from '@/hooks/use-dashboard-websocket';
import { SectionResponse, WidgetResponse, VisualizationType, DashboardDataSource } from '@/types/dashboard-customization';
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { DragDropWidgetGrid } from './dashboard/DragDropWidgetGrid';
import { WidgetEditor } from './dashboard/WidgetEditor';
import { SectionCustomizer } from './dashboard/SectionCustomizer';
import { WidgetCustomizer } from './dashboard/WidgetCustomizer';
import { WidgetInsights } from './dashboard/WidgetInsights';
import { DashboardErrorBoundary } from './dashboard/DashboardErrorBoundary';

import { DashboardSettingsPanel } from './dashboard/DashboardSettingsPanel';
import { WidgetManagementPanel } from './dashboard/WidgetManagementPanel';
import { AddWidgetDialog } from './dashboard/AddWidgetDialog';
import { EnhancedEmptyState } from './dashboard/EnhancedEmptyState';
import { ResponsiveDashboardGrid } from './dashboard/ResponsiveDashboardGrid';



// Chart colors removed - metrics dashboard functionality removed

const WIDGET_ICONS = {
  chart: BarChart3,
  table: Table,
  kpi: Gauge,
  gauge: Gauge,
  heatmap: Grid,
  network: Network,
  tree: TreePine,
  map: Map,
  text: Type,
  image: Image,
};

interface DashboardProps {
  className?: string;
}

export const Dashboard: React.FC<DashboardProps> = ({ className }) => {
  // Metrics view removed - only customizable dashboard remains
  const [showSectionCustomizer, setShowSectionCustomizer] = useState(false);
  const [showWidgetCustomizer, setShowWidgetCustomizer] = useState(false);
  const [showWidgetEditor, setShowWidgetEditor] = useState(false);
  const [showWidgetInsights, setShowWidgetInsights] = useState(false);
  const [selectedWidget, setSelectedWidget] = useState<WidgetResponse | null>(null);
  const [selectedSection, setSelectedSection] = useState<SectionResponse | null>(null);
  const [isEditingMode, setIsEditingMode] = useState(false);
  const [useDragDrop, setUseDragDrop] = useState(true);
  const [showDeleteSectionDialog, setShowDeleteSectionDialog] = useState(false);
  const [sectionToDelete, setSectionToDelete] = useState<SectionResponse | null>(null);
  const [showDashboardSettings, setShowDashboardSettings] = useState(false);
  const [showWidgetManagement, setShowWidgetManagement] = useState(false);
  const [showAddWidgetDialog, setShowAddWidgetDialog] = useState(false);
  const [selectedSectionForWidget, setSelectedSectionForWidget] = useState<string | null>(null);
  const [showSectionSelector, setShowSectionSelector] = useState(false);
  const { toast } = useToast();

  // Metrics dashboard hooks removed

  // Use unified dashboard store for customizable view
  const {
    currentLayout,
    isLoading: isDashboardLoading,
    error: dashboardError,
    createSection,
    updateSection,
    deleteSection,
    createWidget,
    updateWidget,
    deleteWidget,
    moveWidget,
    createDashboard: createDashboardInStore
  } = useUnifiedDashboardStore();

  // Dashboard management hook
  const {
    dashboards: managedDashboards,
    activeDashboard,
  } = useDashboardManagement();

  // Define isLoadingLayout for component usage
  const isLoadingLayout = isDashboardLoading;

  const {
    refreshWidget,
  } = useWidgetDataUpdates(
    currentLayout?.widgets.map(w => w.id) || []
  );

  // Handle dashboard errors
  useEffect(() => {
    if (dashboardError) {
      toast({
        title: "Dashboard Error",
        description: dashboardError.message,
        variant: "destructive",
      });
    }
  }, [dashboardError, toast]);

  const handleCreateFirstDashboard = async () => {
    try {
      await createDashboardInStore({
        name: "My Dashboard",
        description: "Your first dashboard for data visualization and analytics",
        is_default: true,
        is_public: false,
        layout_config: {
          columns: 12,
          rows: 12,
          grid_gap: 16,
          responsive: true,
        },
        theme_config: {
          primary_color: "#3B82F6",
          secondary_color: "#10B981",
          background_color: "#F9FAFB",
          text_color: "#1F2937"
        },
        refresh_interval: 300,
        tags: ['default']
      });

      toast({
        title: "Dashboard Created",
        description: "Your first dashboard has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create dashboard.",
        variant: "destructive",
      });
    }
  };

  const handleAddSection = async () => {
    if (!activeDashboard) return;

    try {
      await createSection({
        dashboard_id: activeDashboard.id,
        name: `Section ${(currentLayout?.sections.length || 0) + 1}`,
        description: 'New dashboard section',
        color: '#10B981',
        icon: 'Grid',
        layout_config: {
          columns: 12,
          rows: 12,
          grid_gap: 16,
          responsive: true,
        },
        customization: {
          background_color: '#FFFFFF',
          border_color: '#E5E7EB',
        },
      });

      toast({
        title: "Section Added",
        description: "New dashboard section has been created.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create section.",
        variant: "destructive",
      });
    }
  };

  const handleAddWidget = (sectionId: string) => {
    setSelectedSectionForWidget(sectionId);
    setShowAddWidgetDialog(true);
  };

  const handleMainAddWidget = () => {
    if (!currentLayout?.sections || currentLayout.sections.length === 0) {
      toast({
        title: "No Sections Available",
        description: "Please create a section first before adding widgets.",
        variant: "destructive",
      });
      return;
    }

    if (currentLayout.sections.length === 1) {
      // If only one section, add widget directly to it
      handleAddWidget(currentLayout.sections[0].id);
    } else {
      // If multiple sections, show section selector
      setShowSectionSelector(true);
    }
  };

  const handleWidgetAdded = async (widgetData: any) => {
    if (!selectedSectionForWidget) return;

    try {
      await createWidget({
        section_id: selectedSectionForWidget,
        title: widgetData.title,
        widget_type: widgetData.type as VisualizationType,
        data_config: widgetData.dataConfig,
        visualization_config: widgetData.visualizationConfig,
        position_config: {
          x: 0,
          y: 0,
          w: widgetData.width || 4,
          h: widgetData.height || 4,
        },
        customization: widgetData.customization,
        refresh_interval: 300,
      });

      toast({
        title: "Widget Added",
        description: "New widget has been created with data source configuration.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create widget.",
        variant: "destructive",
      });
    }
  };

  const handleWidgetCustomize = (widget: WidgetResponse) => {
    setSelectedWidget(widget);
    setShowWidgetCustomizer(true);
  };

  const handleWidgetEdit = (widget: WidgetResponse) => {
    setSelectedWidget(widget);
    setShowWidgetEditor(true);
  };

  const handleWidgetInsights = (widget: WidgetResponse) => {
    setSelectedWidget(widget);
    setShowWidgetInsights(true);
  };

  const handleSectionCustomize = (section: SectionResponse) => {
    setSelectedSection(section);
    setShowSectionCustomizer(true);
  };

  // Handle section deletion with confirmation
  const handleDeleteSection = (section: SectionResponse) => {
    setSectionToDelete(section);
    setShowDeleteSectionDialog(true);
  };

  const confirmDeleteSection = async () => {
    if (!sectionToDelete) return;

    try {
      await deleteSection(sectionToDelete.id);
      setShowDeleteSectionDialog(false);
      setSectionToDelete(null);
      toast({
        title: "Section Deleted",
        description: `"${sectionToDelete.name}" section has been deleted.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete section.",
        variant: "destructive",
      });
    }
  };

  // Handle widget duplication
  const handleWidgetDuplicate = async (widget: WidgetResponse) => {
    try {
      await createWidget({
        section_id: widget.section_id,
        title: `${widget.title} (Copy)`,
        widget_type: widget.widget_type as VisualizationType,
        data_config: widget.data_config,
        visualization_config: widget.visualization_config,
        position_config: {
          x: (widget.position_config?.x || 0) + 1,
          y: (widget.position_config?.y || 0) + 1,
          w: widget.position_config?.w || 4,
          h: widget.position_config?.h || 3,
        },
        customization: widget.customization,
        refresh_interval: widget.refresh_interval,
      });

      toast({
        title: "Widget Duplicated",
        description: "Widget has been duplicated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate widget.",
        variant: "destructive",
      });
    }
  };

  // Handle widget resize
  const handleWidgetResize = async (widgetId: string, newSize: { w: number; h: number }) => {
    try {
      await updateWidget(widgetId, {
        position_config: {
          ...currentLayout?.widgets.find(w => w.id === widgetId)?.position_config,
          w: newSize.w,
          h: newSize.h,
        },
      });

      toast({
        title: "Widget Resized",
        description: "Widget size has been updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to resize widget.",
        variant: "destructive",
      });
    }
  };

  // Handle widget move with drag and drop
  const handleWidgetMove = async (widgetId: string, targetSectionId: string, newPosition: number) => {
    try {
      // Calculate new position based on the drop index
      const targetSection = currentLayout?.sections.find(s => s.id === targetSectionId);
      const targetWidgets = currentLayout?.widgets.filter(w => w.section_id === targetSectionId) || [];

      // Calculate grid position
      const gridColumns = targetSection?.layout_config?.columns || 12;
      const x = (newPosition % gridColumns) * 2; // Spread widgets out
      const y = Math.floor(newPosition / (gridColumns / 2)) * 2;

      await moveWidget(widgetId, targetSectionId, {
        x,
        y,
        w: 4,
        h: 3,
      });

      toast({
        title: "Widget Moved",
        description: "Widget has been moved successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to move widget.",
        variant: "destructive",
      });
    }
  };

  if (isDashboardLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span className="text-sm text-gray-500">
              {managedDashboards.length === 0 ? 'Setting up your dashboard...' : 'Loading...'}
            </span>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/3"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const handleDashboardError = (error: Error, errorInfo: any) => {
    console.error('Dashboard Error:', error, errorInfo);
    toast({
      title: "Dashboard Error",
      description: error.message,
      variant: "destructive",
    });
  };

  return (
      <div className="h-full flex flex-col w-full" role="main" aria-label="Dashboard Application">

      {/* Dashboard Content */}
      <DashboardErrorBoundary onError={handleDashboardError}>
        <main className="flex-1 flex flex-col" role="main" aria-label="Dashboard Content">
            {currentLayout && currentLayout.sections.length > 0 ? (
              <Tabs
                defaultValue={currentLayout.sections[0]?.id}
                className="flex-1 flex flex-col"
                aria-label="Dashboard Sections"
              >
                <div className="border-b flex-shrink-0" role="tablist" aria-label="Section Navigation">
                  <TabsList className="h-auto p-2 bg-transparent">
                    {currentLayout.sections.map((section) => (
                      <TabsTrigger
                        key={section.id}
                        value={section.id}
                        className="relative group data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                        style={{ borderColor: section.color }}
                        role="tab"
                        aria-label={`${section.name} section with ${currentLayout.widgets.filter(w => w.section_id === section.id).length} widgets`}
                      >
                        <div className="flex items-center space-x-2">
                          {section.icon && (
                            <div className="h-4 w-4" style={{ color: section.color }}>
                              {React.createElement(WIDGET_ICONS[section.icon as keyof typeof WIDGET_ICONS] || Grid)}
                            </div>
                          )}
                          <span>{section.name}</span>
                          <Badge variant="outline" className="ml-2">
                            {currentLayout.widgets.filter(w => w.section_id === section.id).length}
                          </Badge>
                        </div>

                        {/* Section Actions */}
                        <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="flex space-x-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSectionCustomize(section);
                              }}
                            >
                              <Settings className="h-3 w-3" />
                            </Button>
                            {currentLayout.sections.length > 1 && (
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-6 w-6 p-0 text-destructive"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteSection(section);
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>

                {/* Section Content */}
                {currentLayout.sections.map((section) => (
                  <TabsContent
                    key={section.id}
                    value={section.id}
                    className="flex-1 overflow-hidden"
                    role="tabpanel"
                    aria-labelledby={`tab-${section.id}`}
                    aria-label={`${section.name} section content`}
                  >
                    <div className="h-full flex flex-col">
                      {/* Section Header */}
                      <div className="flex items-center justify-between mb-3 flex-shrink-0 pt-2" role="region" aria-label="Section Controls">
                        <div>
                          <h2 className="text-lg font-semibold">{section.name}</h2>
                          {section.description && (
                            <p className="text-sm text-muted-foreground">{section.description}</p>
                          )}
                        </div>

                        <div className="flex items-center space-x-2" role="group" aria-label="Section Actions">
                          <Button
                            variant={isEditingMode ? "default" : "outline"}
                            size="sm"
                            onClick={() => setIsEditingMode(!isEditingMode)}
                            aria-label={isEditingMode ? "Exit edit mode" : "Enter edit mode"}
                            aria-pressed={isEditingMode}
                          >
                            <Edit className="h-4 w-4 mr-2" aria-hidden="true" />
                            {isEditingMode ? "Exit Edit" : "Edit Mode"}
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setUseDragDrop(!useDragDrop)}
                            aria-label={useDragDrop ? "Switch to grid view" : "Switch to drag and drop view"}
                            aria-pressed={useDragDrop}
                          >
                            <Move className="h-4 w-4 mr-2" aria-hidden="true" />
                            {useDragDrop ? "Grid View" : "Drag & Drop"}
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSectionCustomize(section)}
                            aria-label={`Customize ${section.name} section`}
                          >
                            <Palette className="h-4 w-4 mr-2" aria-hidden="true" />
                            Customize
                          </Button>

                          {/* Section-specific Add Widget Button */}
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleAddWidget(section.id)}
                              aria-label={`Add widget to ${section.name} section`}
                              className="text-muted-foreground hover:text-foreground"
                            >
                              <Plus className="h-4 w-4" aria-hidden="true" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Enhanced Dashboard Grid - Scrolling handled by parent container */}
                      <div className="flex-1">
                        {useDragDrop ? (
                          <DragDropWidgetGrid
                            widgets={currentLayout.widgets}
                            sections={currentLayout.sections}
                            currentSectionId={section.id}
                            onWidgetUpdate={updateWidget}
                            onWidgetRemove={deleteWidget}
                            onWidgetMove={handleWidgetMove}
                            onWidgetDuplicate={handleWidgetDuplicate}
                            onWidgetCustomize={handleWidgetEdit}
                            onWidgetResize={handleWidgetResize}
                            isEditing={isEditingMode}
                            onWidgetRefresh={refreshWidget}
                          />
                        ) : (
                          <ResponsiveDashboardGrid
                            widgets={currentLayout.widgets.filter(w => w.section_id === section.id)}
                            layoutConfig={section.layout_config}
                            onWidgetUpdate={updateWidget}
                            onWidgetRemove={deleteWidget}
                            onWidgetPositionUpdate={(id, position) => updateWidget(id, { position_config: position })}
                            onWidgetCustomize={handleWidgetCustomize}
                            onWidgetInsights={handleWidgetInsights}
                            onWidgetMove={(widgetId, targetSectionId) => moveWidget(widgetId, targetSectionId, { x: 0, y: 0, w: 4, h: 3 })}
                            onAddWidget={() => handleAddWidget(section.id)}
                            availableSections={currentLayout.sections.filter(s => s.id !== section.id)}
                            dataSourceAssignments={currentLayout.dashboard?.data_source_assignments || []}
                            isLoading={isLoadingLayout}
                          />
                        )}
                      </div>
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            ) : (
              <div className="flex-1">
                {managedDashboards.length === 0 ? (
                  <EnhancedEmptyState
                    type="dashboard"
                    title="Setting up your dashboard..."
                    description="We're creating your first dashboard automatically. This will only take a moment."
                    showQuickStart={false}
                  />
                ) : (
                  <EnhancedEmptyState
                    type="section"
                    actions={[
                      {
                        label: "Create Your First Section",
                        description: "Sections help organize your widgets by topic or data source",
                        icon: Plus,
                        onClick: handleAddSection,
                        primary: true,
                        badge: "Start Here"
                      }
                    ]}
                  />
                )}
              </div>
            )}
          </main>
      </DashboardErrorBoundary>

      {/* Customization Modals */}
      {showSectionCustomizer && selectedSection && (
        <SectionCustomizer
          section={selectedSection}
          onUpdate={(updates) => {
            updateSection(selectedSection.id, updates);
            setShowSectionCustomizer(false);
          }}
          onClose={() => setShowSectionCustomizer(false)}
        />
      )}

      {showWidgetCustomizer && selectedWidget && (
        <WidgetCustomizer
          widget={selectedWidget}
          onUpdate={(updates) => {
            updateWidget(selectedWidget.id, updates);
            setShowWidgetCustomizer(false);
          }}
          onClose={() => setShowWidgetCustomizer(false)}
        />
      )}

      {showWidgetEditor && selectedWidget && (
        <WidgetEditor
          open={showWidgetEditor}
          onOpenChange={setShowWidgetEditor}
          widget={selectedWidget}
          onSave={(widgetId, updates) => {
            updateWidget(widgetId, updates);
            setShowWidgetEditor(false);
          }}
          availableDataSources={
            currentLayout?.dashboard?.data_source_assignments
              ?.map(d => d.system_data_source)
              .filter(Boolean)
              .map(ds => ({
                id: ds!.id,
                name: ds!.name,
                type: ds!.type,
                description: ds!.description,
                isActive: ds!.is_active,
                lastUpdated: ds!.updated_at,
                refreshInterval: 300, // Default value, can be customized
              })) as DashboardDataSource[] || []
          }
          availableSections={currentLayout?.sections.map(s => ({ id: s.id, name: s.name })) || []}
        />
      )}

      {showWidgetInsights && selectedWidget && (
        <WidgetInsights
          widget={selectedWidget}
          onClose={() => setShowWidgetInsights(false)}
        />
      )}

      {/* Delete Section Confirmation Dialog */}
      <AlertDialog open={showDeleteSectionDialog} onOpenChange={setShowDeleteSectionDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Section</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the section "{sectionToDelete?.name}"?
              This action cannot be undone. All widgets in this section will also be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteSection}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Section
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dashboard Settings Panel */}
      <DashboardSettingsPanel
        open={showDashboardSettings}
        onOpenChange={setShowDashboardSettings}
      />

      {/* Widget Management Panel */}
      <WidgetManagementPanel
        open={showWidgetManagement}
        onOpenChange={setShowWidgetManagement}
      />

      {/* Add Widget Dialog */}
      {selectedSectionForWidget && (
        <AddWidgetDialog
          open={showAddWidgetDialog}
          onOpenChange={setShowAddWidgetDialog}
          onWidgetAdded={handleWidgetAdded}
          availableDataSources={currentLayout?.dashboard?.data_source_assignments || []}
          sectionId={selectedSectionForWidget}
        />
      )}

      {/* Section Selector Dialog */}
      <Dialog open={showSectionSelector} onOpenChange={setShowSectionSelector}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Select Section</DialogTitle>
            <DialogDescription>
              Choose which section to add the widget to.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-3 py-4">
            {currentLayout?.sections.map((section) => (
              <Button
                key={section.id}
                variant="outline"
                className="justify-start h-auto p-4"
                onClick={() => {
                  handleAddWidget(section.id);
                  setShowSectionSelector(false);
                }}
              >
                <div className="flex items-center space-x-3">
                  {section.icon && (
                    <div className="h-5 w-5" style={{ color: section.color }}>
                      {React.createElement(WIDGET_ICONS[section.icon as keyof typeof WIDGET_ICONS] || Grid)}
                    </div>
                  )}
                  <div className="text-left">
                    <div className="font-medium">{section.name}</div>
                    {section.description && (
                      <div className="text-sm text-muted-foreground">{section.description}</div>
                    )}
                  </div>
                  <Badge variant="outline" className="ml-auto">
                    {currentLayout.widgets.filter(w => w.section_id === section.id).length} widgets
                  </Badge>
                </div>
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
      </div>
  );
};
