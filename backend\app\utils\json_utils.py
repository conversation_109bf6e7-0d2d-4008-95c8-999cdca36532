"""
JSON utility functions for the Datagenius backend.

This module provides utility functions for working with JSON data.
"""

import logging
import json
import sys
import math
import numpy as np
import pandas as pd
from typing import Any, Dict, List, Union, Set

logger = logging.getLogger(__name__)


def serialize_for_db(obj: Any) -> Any:
    """
    Serialize Pydantic models and other objects for database JSON storage.

    Args:
        obj: Object to serialize (can be Pydantic model, dict, list, etc.)

    Returns:
        JSON-serializable object (dict, list, primitive types)
    """
    if obj is None:
        return None

    # Handle Pydantic models
    if hasattr(obj, 'model_dump'):
        return obj.model_dump()

    # Handle dictionaries
    if isinstance(obj, dict):
        return {k: serialize_for_db(v) for k, v in obj.items()}

    # Handle lists and tuples
    if isinstance(obj, (list, tuple)):
        return [serialize_for_db(item) for item in obj]

    # Return primitive types as-is
    if isinstance(obj, (str, int, float, bool)):
        return obj

    # For other types, try to convert to string
    logger.warning(f"Converting non-serializable object of type {type(obj).__name__} to string")
    return str(obj)


class NaNSafeJSONEncoder(json.JSONEncoder):
    """
    Custom JSON encoder that handles NaN, Infinity, and other special values.
    """
    def default(self, obj):
        # Explicitly handle common NaN types first
        if isinstance(obj, float) and math.isnan(obj):
            return None
        # Check for numpy.nan specifically. isinstance(np.nan, np.floating) is True.
        # np.isnan() is the correct check for numpy NaNs.
        if isinstance(obj, (np.floating, np.float64)) and np.isnan(obj):
            return None

        # Handle pandas NA, which is distinct from np.nan or float('nan')
        if obj is pd.NA:
            return None

        # Handle standard Python float Infinity
        if isinstance(obj, float) and math.isinf(obj):
            return str(obj) # Or handle as error/specific value

        # Handle numpy float Infinity
        if isinstance(obj, (np.floating, np.float64)) and np.isinf(obj):
            return str(obj)

        # Convert other numpy numeric types to standard Python types
        if isinstance(obj, (np.floating, np.float64)): # Non-NaN, Non-Inf numpy floats
            return float(obj)
        if isinstance(obj, (np.integer, np.int64)):
            return int(obj)

        # Broader check for anything pandas considers NA (includes pd.NaT, etc.)
        # This should ideally be redundant if specific types are handled above, but acts as a fallback.
        if pd.isna(obj): # This check should come after more specific ones.
            return None

        # Handle numpy arrays: convert to list.
        # Elements will be recursively processed by this default method.
        # e.g., np.nan in a float array becomes float('nan') in the list, then handled by the float rule.
        if isinstance(obj, np.ndarray):
            return obj.tolist()

        # Handle pandas DataFrame/Series: convert to dict/list.
        # Elements will be recursively processed.
        if isinstance(obj, pd.DataFrame):
            return obj.to_dict(orient="records")
        if isinstance(obj, pd.Series):
            return obj.to_dict()

        # Let the base class handle everything else (strings, bools, basic lists/dicts etc.)
        return super().default(obj)

def ensure_serializable(obj: Any, _seen: Set = None) -> Any:
    """
    Ensure an object is JSON serializable.

    Args:
        obj: The object to make serializable
        _seen: Set of object ids already processed (used to detect circular references)

    Returns:
        A JSON serializable version of the object
    """
    # Initialize the set of seen objects if this is the top-level call
    if _seen is None:
        _seen = set()

    # Handle None
    if obj is None:
        return None

    # Handle primitive types
    if isinstance(obj, (str, int, bool)):
        return obj

    # Handle float, including NaN and Infinity
    if isinstance(obj, float):
        if math.isnan(obj):
            return None  # Convert NaN to None
        elif math.isinf(obj):
            return str(obj)  # Convert Infinity to string
        return obj

    # Handle numpy types
    if isinstance(obj, (np.integer, np.int64)):
        return int(obj)
    if isinstance(obj, (np.floating, np.float64)):
        if np.isnan(obj):
            return None  # Convert NaN to None
        elif np.isinf(obj):
            return str(obj)  # Convert Infinity to string
        return float(obj)

    # Handle pandas NA/NaT
    try:
        # Check if it's a scalar value that can be checked with pd.isna
        if pd.api.types.is_scalar(obj) and pd.isna(obj):
            return None
    except Exception:
        # If there's any error, just continue with the rest of the function
        pass

    # Check for circular references
    obj_id = id(obj)
    if obj_id in _seen:
        return "<circular reference>"

    # Add this object to the set of seen objects
    _seen.add(obj_id)

    try:
        # Handle dictionaries
        if isinstance(obj, dict):
            result = {}
            for k, v in obj.items():
                # Convert non-string/non-primitive keys to strings
                if not isinstance(k, (str, int, float, bool, type(None))):
                    str_key = str(k)
                    logger.warning(f"Converting non-serializable dictionary key of type {type(k).__name__} to string: {str_key}")
                    result[str_key] = ensure_serializable(v, _seen)
                else:
                    result[k] = ensure_serializable(v, _seen)
            return result

        # Handle lists and tuples
        if isinstance(obj, (list, tuple)):
            return [ensure_serializable(item, _seen) for item in obj]

        # Handle Pydantic models
        if hasattr(obj, 'model_dump'):
            logger.info(f"Converting Pydantic model {type(obj).__name__} to dict")
            return ensure_serializable(obj.model_dump(), _seen)

        # Handle pandas DataFrame
        if isinstance(obj, pd.DataFrame):
            logger.info(f"Converting pandas DataFrame of shape {obj.shape} to dict")
            return ensure_serializable(obj.to_dict(orient="records"), _seen)

        # Handle pandas Series
        if isinstance(obj, pd.Series):
            logger.info(f"Converting pandas Series of length {len(obj)} to dict")
            return ensure_serializable(obj.to_dict(), _seen)

        # Handle numpy arrays
        if isinstance(obj, np.ndarray):
            logger.info(f"Converting numpy array of shape {obj.shape} to list")
            return ensure_serializable(obj.tolist(), _seen)

        # Handle objects with __dict__
        if hasattr(obj, '__dict__'):
            # For objects with __dict__, convert to dict
            logger.warning(f"Converting object of type {type(obj).__name__} to dict")
            return ensure_serializable(obj.__dict__, _seen)

        # For any other type, convert to string
        logger.warning(f"Converting non-serializable type {type(obj).__name__} to string")
        return str(obj)
    finally:
        # Remove this object from the set of seen objects when we're done with it
        # This allows the same object to appear multiple times in non-circular structures
        _seen.remove(obj_id)

def sanitize_json(data: Any) -> Any:
    """
    Sanitize any data to ensure it's JSON serializable, converting NaNs to None.
    Uses strict NaN checking during intermediate serialization steps.

    Args:
        data: The data to sanitize

    Returns:
        Sanitized Python object (e.g., dict, list) that can be safely serialized to JSON
        where NaNs are converted to None.
    """
    if data is None:
        return None

    try:
        # Attempt to dump with custom encoder AND strict NaN handling (allow_nan=False).
        # If NaNSafeJSONEncoder.default correctly returns None for all NaNs,
        # then no raw NaNs will reach the superclass encoder part, and allow_nan=False
        # will not raise an error for NaNs handled by our encoder.
        # If a NaN slips through default() and reaches super().default(),
        # allow_nan=False will cause json.dumps to raise ValueError.
        json_str = json.dumps(data, cls=NaNSafeJSONEncoder, allow_nan=False)
        # Parse the JSON string (which should now have 'null' for NaNs) back into a Python object.
        return json.loads(json_str)
    except ValueError as ve: # Specifically catch ValueError, likely from allow_nan=False
        logger.warning(
            f"NaN or Inf likely encountered by json.dumps(allow_nan=False) "
            f"even after/during NaNSafeJSONEncoder processing: {str(ve)}. "
            f"Falling back to ensure_serializable."
        )
        # Fallback path: use ensure_serializable, which also aims to convert NaNs to None.
        sanitized_by_ensure = ensure_serializable(data)
        try:
            # Final check: can this structure now be dumped without allowing NaNs?
            # If sanitized_by_ensure still has NaNs (float('nan') or np.nan), this will raise ValueError.
            # This step ensures that the returned object is truly free of Python NaNs.
            json.dumps(sanitized_by_ensure, allow_nan=False)
            return sanitized_by_ensure # Return the Python object, not a JSON string
        except ValueError as ve2:
            logger.error(
                f"ensure_serializable output still contains NaN/Inf "
                f"when checked with json.dumps(allow_nan=False): {str(ve2)}. "
                f"Returning an error structure."
            )
            # Last resort: the data could not be fully cleaned.
            return {
                "error": "Data could not be fully sanitized from NaN/Inf values for JSON.",
                "original_type": str(type(data)),
                "sanitization_error_details": str(ve2)
            }
    except Exception as e: # Catch other exceptions during the initial dumps/loads
        logger.warning(
            f"Failed to serialize/deserialize with NaNSafeJSONEncoder "
            f"(other exception type: {type(e).__name__}): {str(e)}. "
            f"Falling back to ensure_serializable."
        )
        # Fallback path for other errors (e.g., TypeErrors not related to NaN directly)
        sanitized_by_ensure = ensure_serializable(data)
        try:
            # Check again with strict NaN handling
            json.dumps(sanitized_by_ensure, allow_nan=False)
            return sanitized_by_ensure
        except ValueError as ve2: # If NaNs were introduced or missed by ensure_serializable
            logger.error(
                f"ensure_serializable output (after other initial exception) "
                f"still contains NaN/Inf: {str(ve2)}. "
                f"Returning an error structure."
            )
            return {
                "error": "Data could not be fully sanitized from NaN/Inf values (after other initial exception).",
                "original_type": str(type(data)),
                "sanitization_error_details": str(ve2)
            }
        except Exception as e2: # If ensure_serializable output has other JSON issues
             logger.error(
                f"ensure_serializable output (after other initial exception) "
                f"has other serialization issues: {str(e2)}. "
                f"Returning an error structure."
            )
             return {
                "error": "Data could not be fully sanitized for JSON (after other initial exception).",
                "original_type": str(type(data)),
                "sanitization_error_details": str(e2)
            }


def sanitize_metadata(metadata: Union[Dict[str, Any], None]) -> Union[Dict[str, Any], None]:
    """
    Sanitize metadata to ensure it's JSON serializable.

    Args:
        metadata: The metadata to sanitize

    Returns:
        Sanitized metadata
    """
    # Simply use our sanitize_json function
    return sanitize_json(metadata)
