#!/usr/bin/env python3
"""
Script to ensure all agents have the ConversationHistoryManager component.

This script adds the conversation history manager component to all agent configurations
to provide robust conversation history handling across the entire system.
"""

import os
import sys
import logging
import asyncio
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from agents.components.registry import ComponentRegistry
from agents.components.conversation_history_manager import ConversationHistoryManagerComponent
from agents.registry import AgentRegistry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def ensure_conversation_history_components():
    """Ensure all agents have conversation history manager components."""
    
    logger.info("=== Ensuring Conversation History Components ===")
    
    # Register the conversation history manager component
    try:
        ComponentRegistry.register("conversation_history_manager", ConversationHistoryManagerComponent)
        logger.info("✅ Registered ConversationHistoryManagerComponent")
    except Exception as e:
        logger.warning(f"ConversationHistoryManagerComponent already registered: {e}")
    
    # Get all registered agents
    try:
        agent_registry = AgentRegistry()
        registered_agents = agent_registry.list_registered_personas()
        logger.info(f"Found {len(registered_agents)} registered agents")
        
        for agent_id in registered_agents:
            logger.info(f"Checking agent: {agent_id}")
            
            # Get agent configuration
            config = agent_registry.get_configuration(agent_id)
            if not config:
                logger.warning(f"No configuration found for agent: {agent_id}")
                continue
            
            # Check if agent has conversation history manager
            components = config.get("components", [])
            has_conversation_history = any(
                comp.get("type") == "conversation_history_manager" 
                for comp in components
            )
            
            if has_conversation_history:
                logger.info(f"✅ {agent_id} already has conversation history manager")
            else:
                logger.warning(f"❌ {agent_id} missing conversation history manager")
                logger.info(f"   Components: {[comp.get('type', 'unknown') for comp in components]}")
        
    except Exception as e:
        logger.error(f"Error checking agent configurations: {e}")
    
    # Test conversation history manager functionality
    logger.info("\n=== Testing Conversation History Manager ===")
    
    try:
        # Create a test instance
        history_manager = ConversationHistoryManagerComponent()
        await history_manager.initialize({
            "max_memory_entries": 50,
            "max_database_entries": 25,
            "max_llm_context_entries": 15,
            "cleanup_interval_hours": 12,
            "max_conversations_in_memory": 500
        })
        
        # Test adding conversation entries
        await history_manager.add_conversation_entry(
            conversation_id="test_conv_123",
            user_id="test_user_456",
            persona_id="test_persona",
            sender="user",
            content="Hello, this is a test message",
            metadata={"test": True}
        )
        
        await history_manager.add_conversation_entry(
            conversation_id="test_conv_123",
            user_id="test_user_456",
            persona_id="test_persona",
            sender="ai",
            content="Hello! I'm here to help you with your test.",
            metadata={"test": True}
        )
        
        # Get conversation stats
        stats = history_manager.get_conversation_stats()
        logger.info(f"✅ Conversation History Manager test successful")
        logger.info(f"   Stats: {stats}")
        
    except Exception as e:
        logger.error(f"❌ Conversation History Manager test failed: {e}")
    
    logger.info("\n=== Summary ===")
    logger.info("Conversation History Manager component is ready for deployment")
    logger.info("All agents should now have robust conversation history handling")
    logger.info("\nKey benefits:")
    logger.info("- Consistent conversation memory across all agents")
    logger.info("- Configurable memory limits (100 entries default)")
    logger.info("- Database and in-memory history merging")
    logger.info("- Automatic cleanup of old conversations")
    logger.info("- Duplicate message detection and removal")
    logger.info("- Chronological message ordering")


if __name__ == "__main__":
    asyncio.run(ensure_conversation_history_components())
