2025-04-21 14:15:07,517 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 14:15:07,518 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnBVkE5kXkvc_GIS7KAYttNKPnnFn-Bgsb9PHScjgpd5RqD6_fMlf2hVpBOsdasdQ', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 14:15:07,519 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 14:15:07,990 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 14:15:07,994 - asyncio - ERROR - Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 162, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-04-21 14:15:08,007 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 14:15:08,019 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 14:15:35,259 - app.api.hf - ERROR - Error reading rules from DB for model ID '1': 'Label' is an invalid keyword argument for HFRule
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hf.py", line 92, in get_hf_rules_db
    rule = HFRule(
  File "<string>", line 4, in __init__
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\state.py", line 571, in _initialize_instance
    with util.safe_reraise():
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\state.py", line 569, in _initialize_instance
    manager.original_init(*mixed[1:], **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\decl_base.py", line 2175, in _declarative_constructor
    raise TypeError(
TypeError: 'Label' is an invalid keyword argument for HFRule
2025-04-21 14:26:32,264 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 14:26:32,266 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlmtFTGtnn13qlIKJ6y9PV2N_cS_PdIoqfkyhCoL67CjfMKoMAw9HImW8g_abQJ4jg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 14:26:32,267 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 14:26:32,743 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 14:26:32,771 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 14:26:39,927 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 14:29:09,914 - app.database - ERROR - Rule 1 is missing required field 'label'
2025-04-21 14:29:09,916 - app.database - ERROR - Rule 2 is missing required field 'label'
2025-04-21 14:29:09,918 - app.database - ERROR - Rule 3 is missing required field 'label'
2025-04-21 14:29:09,919 - app.database - ERROR - Rule 4 is missing required field 'label'
2025-04-21 14:29:09,921 - app.database - ERROR - Rule 5 is missing required field 'label'
2025-04-21 14:29:09,924 - app.database - ERROR - Rule 6 is missing required field 'label'
2025-04-21 14:29:09,926 - app.database - ERROR - Rule 7 is missing required field 'label'
2025-04-21 14:29:09,928 - app.database - ERROR - Rule 8 is missing required field 'label'
2025-04-21 14:29:09,930 - app.database - ERROR - Rule 9 is missing required field 'label'
2025-04-21 14:29:09,932 - app.database - ERROR - Rule 10 is missing required field 'label'
2025-04-21 14:29:09,933 - app.database - ERROR - Rule 11 is missing required field 'label'
2025-04-21 14:29:09,935 - app.database - ERROR - Rule 12 is missing required field 'label'
2025-04-21 14:29:09,937 - app.database - ERROR - Error creating HFRule object from data {'Label': 'Category: KCB Group', 'Keywords': 'kcb, growth, time, believe, lionhearted, https, forpeopleforbetter', 'confidence_threshold': 0.5, 'model_id': 2}: 'Label' is an invalid keyword argument for HFRule
2025-04-21 14:29:09,938 - app.database - ERROR - Error bulk creating rules: 'Label' is an invalid keyword argument for HFRule
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\database.py", line 527, in bulk_create_hf_rules
    rule = HFRule(**data)
  File "<string>", line 4, in __init__
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\state.py", line 571, in _initialize_instance
    with util.safe_reraise():
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\state.py", line 569, in _initialize_instance
    manager.original_init(*mixed[1:], **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\decl_base.py", line 2175, in _declarative_constructor
    raise TypeError(
TypeError: 'Label' is an invalid keyword argument for HFRule
2025-04-21 14:29:09,946 - app.database - ERROR - Rules data that caused the error: [{'Label': 'Category: KCB Group', 'Keywords': 'kcb, growth, time, believe, lionhearted, https, forpeopleforbetter', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Category: Real Estate and Investment', 'Keywords': 'kcbgroup, growth, https, kcb, time, believe, lionhearted', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Segment: Banking Services', 'Keywords': 'time, believe, lionhearted, forpeopleforbetter, growth, https, place', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Segment: Chess Sponsorship', 'Keywords': 'forpeopleforbetter, place, kcb, growth, https, believe, lionhearted', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Segment: Investment Advice', 'Keywords': 'kcbgroup, growth, https, kcb, time, believe, lionhearted', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Segment: Partnerships and Collaborations', 'Keywords': 'kcb, kcbgroup, time, believe, lionhearted, forpeopleforbetter, growth', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Subsegment: ATM Services', 'Keywords': 'https, kcbgroup, kcb, time, believe, lionhearted, forpeopleforbetter', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Subsegment: Population Growth and Shelter', 'Keywords': 'growth, kcbgroup, https, kcb, time, believe, lionhearted', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Subsegment: Real Estate Investment', 'Keywords': 'kcbgroup, https, kcb, time, believe, lionhearted, forpeopleforbetter', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Subsegment: Tournament Participation', 'Keywords': 'forpeopleforbetter, place, kcb, growth, https, believe, lionhearted', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Subsegment: University Partnerships', 'Keywords': 'kcb, kcbgroup, time, believe, lionhearted, forpeopleforbetter, growth', 'confidence_threshold': 0.5, 'model_id': 2}, {'Label': 'Theme: Banking and Finance', 'Keywords': 'kcb, kcbgroup, time, believe, lionhearted, forpeopleforbetter, growth', 'confidence_threshold': 0.5, 'model_id': 2}]
2025-04-21 14:29:09,949 - app.database - ERROR - Rolling back transaction and returning empty list
2025-04-21 14:30:11,900 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 14:30:11,902 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnBFlI-TbAeChWtGG4-c-S3ttDrHGM_PAavRRVjmpj7Yuvrw1FaT31gcVvTgovkEw', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 14:30:11,904 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 14:30:12,824 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 14:30:12,836 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 14:30:12,870 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:06:46,763 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 15:06:46,765 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnaE0NHNhUaNvwoVK6yG0m5WvrQYQ8Xsx8y836IJDsQhHB0sq5_aCbRF6TRmHwcAQ', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 15:06:46,766 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 15:06:47,237 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 15:06:48,427 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:06:48,439 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:09:58,468 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:10:19,527 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:10:36,718 - app.tasks.hf - ERROR - HF Classification Task 404ad5db-1e5d-492a-ac45-52f6231f2abf failed: Instance <HFModel at 0x210b0389e40> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 314, in run_hf_classification_task
    logger.info(f"Found HF model '{model_name}' (ID: {db_model.id}) in database. Deserializing...")
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\attributes.py", line 566, in __get__
    return self.impl.get(state, dict_)  # type: ignore[no-any-return]
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\attributes.py", line 1086, in get
    value = self._fire_loader_callables(state, key, passive)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\attributes.py", line 1116, in _fire_loader_callables
    return state._load_expired(state, passive)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\state.py", line 803, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\loading.py", line 1603, in load_scalar_attributes
    raise orm_exc.DetachedInstanceError(
sqlalchemy.orm.exc.DetachedInstanceError: Instance <HFModel at 0x210b0389e40> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-04-21 15:13:56,362 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 15:13:56,364 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qllIH7yPMY71l0UToPIxcESWmr16hbMPTlao8OT8rKnxo59N191F-t3VdS4aebtnjA', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 15:13:56,365 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 15:13:56,843 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 15:13:58,014 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:13:58,032 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:15:22,836 - app.tasks.hf - ERROR - HF Classification Task a538764d-02d7-4298-9d80-47ef14c8b4b2 failed: Instance <HFModel at 0x2b8c23acbe0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 323, in run_hf_classification_task
    model_id = db_model.id
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\attributes.py", line 566, in __get__
    return self.impl.get(state, dict_)  # type: ignore[no-any-return]
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\attributes.py", line 1086, in get
    value = self._fire_loader_callables(state, key, passive)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\attributes.py", line 1116, in _fire_loader_callables
    return state._load_expired(state, passive)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\state.py", line 803, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\sqlalchemy\orm\loading.py", line 1603, in load_scalar_attributes
    raise orm_exc.DetachedInstanceError(
sqlalchemy.orm.exc.DetachedInstanceError: Instance <HFModel at 0x2b8c23acbe0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-04-21 15:18:02,127 - root - ERROR - HF Deserialize Error: Failed during deserialization for model 'ai_sample3': Deserialized tokenizer is not of the expected type.
2025-04-21 15:18:02,129 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\hf_classifier.py", line 622, in deserialize_hf_model_artifacts
    raise TypeError("Deserialized tokenizer is not of the expected type.")
TypeError: Deserialized tokenizer is not of the expected type.

2025-04-21 15:18:02,134 - app.tasks.hf - ERROR - Failed to load model after multiple attempts: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 380, in run_hf_classification_task
    raise RuntimeError(f"Failed to deserialize model artifacts from database for model '{model_name}' (ID: {model_id})")
RuntimeError: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
2025-04-21 15:18:02,148 - app.tasks.hf - ERROR - HF Classification Task 9b633853-139d-41ca-b31f-55f95e593bd5 failed: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 380, in run_hf_classification_task
    raise RuntimeError(f"Failed to deserialize model artifacts from database for model '{model_name}' (ID: {model_id})")
RuntimeError: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
2025-04-21 15:21:47,738 - root - ERROR - HF Deserialize Error: Failed during deserialization for model 'ai_sample3': Deserialized tokenizer is not of the expected type.
2025-04-21 15:21:47,744 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\hf_classifier.py", line 622, in deserialize_hf_model_artifacts
    tokenizer = pickle.loads(tokenizer_bytes)
TypeError: Deserialized tokenizer is not of the expected type.

2025-04-21 15:21:47,765 - app.tasks.hf - ERROR - Failed to load model after multiple attempts: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 380, in run_hf_classification_task
    raise RuntimeError(f"Failed to deserialize model artifacts from database for model '{model_name}' (ID: {model_id})")
RuntimeError: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
2025-04-21 15:21:47,777 - app.tasks.hf - ERROR - HF Classification Task b5908630-0b25-4a79-b8c7-041759ff4973 failed: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 380, in run_hf_classification_task
    raise RuntimeError(f"Failed to deserialize model artifacts from database for model '{model_name}' (ID: {model_id})")
RuntimeError: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
2025-04-21 15:22:10,230 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 15:22:10,231 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnhuvYLnScRtymMXnViyd8VfTBoxhhz8xxhFTra7ApLHkh6gWshM65WENomNKVXDA', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 15:22:10,233 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 15:22:10,719 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 15:22:10,742 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:22:10,760 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:22:23,846 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:22:23,860 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:23:37,297 - root - ERROR - HF Deserialize Error: Failed during deserialization for model 'ai_sample3': Deserialized tokenizer is not of the expected type.
2025-04-21 15:23:37,300 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\hf_classifier.py", line 622, in deserialize_hf_model_artifacts
    tokenizer = pickle.loads(tokenizer_bytes)
TypeError: Deserialized tokenizer is not of the expected type.

2025-04-21 15:23:37,305 - app.tasks.hf - ERROR - Failed to load model after multiple attempts: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 380, in run_hf_classification_task
    raise RuntimeError(f"Failed to deserialize model artifacts from database for model '{model_name}' (ID: {model_id})")
RuntimeError: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
2025-04-21 15:23:37,317 - app.tasks.hf - ERROR - HF Classification Task b891b2ca-3348-4783-8af0-d0e4328ffc6f failed: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 380, in run_hf_classification_task
    raise RuntimeError(f"Failed to deserialize model artifacts from database for model '{model_name}' (ID: {model_id})")
RuntimeError: Failed to deserialize model artifacts from database for model 'ai_sample3' (ID: 3)
2025-04-21 15:27:49,294 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 15:27:49,296 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlm7AnVOVyqMLdcJPl126VcA-UplogCPbjJtVy_ytJlUpw11b4mrK0bzCQ4iX6mm7A', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 15:27:49,297 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 15:27:49,840 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 15:27:49,856 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:28:09,859 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:30:37,578 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:30:37,590 - app.api.hierarchy - ERROR - Error fetching hierarchy configurations: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\api\hierarchy.py", line 45, in get_hierarchy_configs
    configs_response.append(HierarchyConfigResponse.from_orm(default_config_db))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 1443, in from_orm
    return cls.model_validate(obj)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\pydantic\main.py", line 703, in model_validate
    return cls.__pydantic_validator__.validate_python(
pydantic_core._pydantic_core.ValidationError: 2 validation errors for HierarchyConfigResponse
created_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
updated_at
  Input should be a valid string [type=string_type, input_value=datetime.datetime(2025, 4, 21, 10, 12, 11, 533012), input_type=datetime]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-04-21 15:50:24,267 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 15:50:24,268 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlkVVgLNAnUj6DhIUakzMgKmEFrUCJ8P_BjNioNUGoPe5SqptPSasZuDk2S7phYdPA', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 15:50:24,270 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 15:50:24,748 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 16:11:50,679 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 16:11:50,681 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlkB1glsZ_1EgK_6bBwYlFFIaP9mWS0JTPUyeGUyzAdVcRcQ_LTiGexlXf1Nrhh0Zg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 16:11:50,682 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 16:11:51,162 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 19:13:29,654 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 19:13:29,655 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlkpEKwaIs4PrhKmVK1JUL1_7mtueaU_peGZcTt1ztkyZ1WF3LIGzUImnam8FU68JA', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 19:13:29,656 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 19:13:30,221 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 19:30:12,196 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 19:30:12,197 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnlWnEKRbwup91UDl4TH0-Kou7k6HDgOjHPC9YKTkPVcCqmEMkeiRffkK2JeYU4Wg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 19:30:12,198 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 19:30:12,751 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 19:33:49,148 - root - ERROR - Timeout: Request to https://api.groq.com/openai/v1 timed out.
2025-04-21 20:02:48,799 - app.setup_db - ERROR - Alembic command failed: 
2025-04-21 20:30:44,702 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 20:30:44,703 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlmJ1kHZ0J9yBfAIPL73QXEed3T3FOpMaerqrTy-ZOrb3uvk2ZUjvdj3eJ4GTJN0LQ', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 20:30:44,704 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 20:30:45,283 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 20:56:21,666 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 20:56:21,667 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlkLo2LTUxAu8SNvDWqkAYSU_uJLT85d3moRi1DKjr_g0toMDuyJWpue-yootFIqDg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 20:56:21,668 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 20:56:22,298 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 21:09:40,016 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 21:09:40,017 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnAkDolnCMlNgOmURAFCKdMhEJ2dCHSSlWAe-1v18R227zUUD0R-MUg_viDRXrA5g', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 21:09:40,017 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 21:09:41,000 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 21:11:14,712 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 21:11:14,713 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlm29hXnug6XeybI4bbiUIBoI-MFG4HKxTZmYDd2C7fMZ636qbpb9rhnGzW8Egdrrg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 21:11:14,714 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 21:11:15,292 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 21:18:04,486 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 21:18:04,486 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnOlkSKMzyq4noW_YjOmr2Kq99lTklEwN3DonbQWJfb6ntAQPi1i7r2_6I_t6FiZQ', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 21:18:04,487 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 21:18:05,065 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 21:19:26,497 - root - ERROR - LLM Suggestion: OutputFixingParser also failed: Error code: 400 - {'error': {'message': 'Please reduce the length of the messages or completion.', 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}
2025-04-21 21:19:26,498 - root - ERROR - Raw AI Response that failed parsing:
2025-04-21 21:19:26,498 - root - ERROR - {
  "$schema": "http://json-schema.org/draft-07-json-schema.json",
  "type": "object",
  "title": "AISuggestionTheme",
  "description": "Structure for the AI to generate the hierarchy.",
  "properties": {
    "themes": {
      "description": "The complete hierarchical structure.",
      "type": "array",
      "items": {
        "$ref": "#/definitions/AISuggestionTheme"
      },
      "title": "Themes",
      "type": "array"
    }
  },
  "definitions": {
    "AISuggestionCategory": {
      "description": "A category within a theme.",
      "type": "object",
      "properties": {
        "name": {
          "description": "Name of the category.",
          "type": "string",
          "title": "Name",
          "type": "string"
        },
        "categories": {
          "description": "List of subcategories within this category.",
          "type": "array",
          "items": {
            "$ref": "#/definitions/AISuggestionCategory"
          },
          "title": "Subcategories",
          "type": "array"
        }
      },
      "title": "Category",
      "type": "object"
    },
    "AISuggestionTheme": {
      "description": "A theme within the hierarchy.",
      "type": "object",
      "properties": {
        "name": {
          "description": "Name of the theme.",
          "type": "string",
          "title": "Name",
          "type": "string"
        },
        "categories": {
          "description": "List of sub-themes within this theme.",
          "type": "array",
          "items": {
            "$ref": "#/definitions/AISuggestionTheme"
          },
          "title": "Sub-Themes",
          "type": "array"
        }
      },
      "title": "Theme",
      "type": "object"
    }
  },
  "definitions": {
    "AISuggestionCategory": {
      "description": "A category within the hierarchy.",
      "type": "object",
      "properties": {
        "name": {
          "description": "Name of the category.",
          "type": "string",
          "title": "Name",
          "type": "string"
        },
        "categories": {
          "description": "List of subcategories within this category.",
          "type": "array",
          "items": {
            "$ref": "#/definitions/AISuggestionCategory"
          },
          "title": "Subcategories",
          "type": "array"
      },
      "title": "Category",
      "type": "object"
    }
  }
}
```

Based on the provided text data, the hierarchical structure can be defined as follows:

```json
{
  "$schema": "http://json-schema.org/draft-07",
  "title": "Hierarchy",
  "type": "object",
  "properties": {
    "Themes": {
      "description": "Main topics of the text data",
      "type": "array",
      "items": {
        "$ref": "#/definitions/AISuggestionTheme"
     },
    "Segments": {
      "description": "Specific topics within each theme",
      "type": "array",
      "items": {
        "$ref": "#/definitions/AISuggestionCategory"
    },
    "Categories": {
      "description": "Specific topics within each segment",
     "type": "array",
     "items": {
        "$ref": "#/definitions/AISuggestionSubCategory"
    },
    "Subcategories": {
        "description": "Specific topics within each category",
      "type": "array",
     "items": {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
2025-04-21 21:27:18,313 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 21:27:18,314 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qln8CG8iMumw9vNEdTHvtjXQhR7nrfnXXCbqlJtqu4ly8HAZd5_yVIKMUdqddFUFXw', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 21:27:18,315 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 21:27:18,892 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 21:28:33,146 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 21:28:33,147 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlmNA99zONsf5o9Hc6Gr10_QWwuhC1UW8kVoiPbxzyvr9uiQis8kvI0gFqV05JS9jg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 21:28:33,148 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 21:28:33,720 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 21:36:17,471 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 21:36:17,472 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qllokaxB9aDNb0AXU-rlOvjcPiELGfQOjubHfqqx9EeMfv5rkc_93Md1ZC2JxWnPbw', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 21:36:17,473 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 21:36:18,087 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 21:43:59,239 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 21:43:59,240 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlk0t6qVijKuQEjoE3BLdN706wE7sRNNRLHkEcnnzCLuseefUdR1bUFR27qbbM-jZg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 21:43:59,240 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 21:44:00,260 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 21:44:00,264 - asyncio - ERROR - Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 162, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-04-21 22:09:55,967 - app.redis_client - ERROR - Failed to store refresh token: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:09:59,581 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 22:09:59,582 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlkgC73wc6AYcV7PXOsX0clnOtoZn5sRphjWOLkT8CMgKYKe58dnfzdlPfEb03R_tA', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 22:09:59,583 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 22:10:00,211 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 22:10:05,337 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:10:09,351 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:10:22,146 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:10:50,161 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:11:19,311 - app.redis_client - ERROR - Failed to store refresh token: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:11:22,794 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 22:11:22,795 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlkCj7hvmpDwDfs34Qv6NSXc8ZCO9PsXXftp9zvW-LnmNcT2kxvo-ptcKEPT62AmAw', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 22:11:22,796 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 22:11:23,607 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 22:11:28,779 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:11:33,490 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:13:16,140 - app.redis_client - ERROR - Failed to store refresh token: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:13:21,738 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:13:26,384 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-21 22:13:43,575 - root - ERROR - Connection Error: Could not reach endpoint http://localhost:11434. Is the service running?
2025-04-21 22:19:24,614 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 22:19:24,615 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qllR4QvfxA9S49trXPXkbmtzU8bRMbwE7gI191KG4unKPmigTT239H9AbYoyI1lIXA', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 22:19:24,615 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 22:19:25,190 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 22:41:37,575 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 22:41:37,576 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnCCmMvIIs2v8G4OktZHdPQC2_VnyAip6fIw269503oH7qB0VswHwOAL9Zhg3pWDQ', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 22:41:37,577 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 22:41:38,153 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 22:47:20,621 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 22:47:20,622 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlmTXn_zsmo3B7Z2x8eLjnmswOxWbfhtEgZESwD3eGwoHk69xLOdIXoXLYKwnKDMJw', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 22:47:20,623 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 22:47:21,206 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 22:50:22,647 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 22:50:22,648 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qllGlPdARD_DIzI4stuI9HYrUmgmoHREM8MQWmqA-1qIZfpqFEBxajk-TNHCxBYf9g', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 22:50:22,649 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 22:50:23,219 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 22:56:04,593 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 22:56:04,594 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qllgQzZTz2kFGxicMkP4k8sspjWUi67wGXri9yaF4UcSEhlWe9teZzsHkhQuBoLN6g', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 22:56:04,595 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 22:56:05,166 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 22:58:49,234 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 22:58:49,234 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlmUfStv0AxX443ik8yzHqw4rz5VVeH2X5XvlRaNQi8FIYWi6zauPrxYMivsHDiePw', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 22:58:49,235 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 22:58:49,831 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-21 23:33:30,013 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-21 23:33:30,014 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlk8WuLV-Y-0S6pqTlGc9Ctk2bUXIgk8aHuuSBP7EfFlSsbmLj6Vdvb0sT0RKk0CEg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-21 23:33:30,015 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-21 23:33:30,583 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 01:14:55,296 - root - ERROR - HF Serialize Error: Failed to serialize model artifacts: name 'uuid' is not defined
2025-04-22 01:14:55,299 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\hf_classifier.py", line 491, in serialize_hf_model_artifacts
    model_id = str(uuid.uuid4())
NameError: name 'uuid' is not defined

2025-04-22 01:14:55,301 - app.tasks.hf - ERROR - HF Training Task 4b2b5705-cfd5-41a3-b235-c04bcc1dac0a failed: Failed to serialize trained model artifacts.
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 164, in run_hf_training_task
    raise RuntimeError("Failed to serialize trained model artifacts.")
RuntimeError: Failed to serialize trained model artifacts.
2025-04-22 01:20:01,654 - root - ERROR - HF Serialize Error: Failed to serialize model artifacts: name 'uuid' is not defined
2025-04-22 01:20:01,657 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\hf_classifier.py", line 491, in serialize_hf_model_artifacts
    model_id = str(uuid.uuid4())
NameError: name 'uuid' is not defined

2025-04-22 01:20:01,659 - app.tasks.hf - ERROR - HF Training Task 71a83733-98c3-4c37-bd0c-954a412ea77f failed: Failed to serialize trained model artifacts.
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 164, in run_hf_training_task
    raise RuntimeError("Failed to serialize trained model artifacts.")
RuntimeError: Failed to serialize trained model artifacts.
2025-04-22 01:34:31,067 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 01:34:31,068 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qln4epFbrXDilt_F6ipDJQi30mG8RknoNDn4fIoLSb2KA2rzcCxOCouWEWFPaz8b0w', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 01:34:31,069 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 01:34:31,646 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 01:36:10,868 - root - ERROR - HF Serialize Error: Failed to serialize model artifacts: name 'uuid' is not defined
2025-04-22 01:36:10,871 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\hf_classifier.py", line 491, in serialize_hf_model_artifacts
    # Generate a unique ID for this model's artifacts
NameError: name 'uuid' is not defined

2025-04-22 01:36:10,873 - app.tasks.hf - ERROR - HF Training Task 9b761251-3aa5-4f87-8795-3bd2f2cb8eb2 failed: Failed to serialize trained model artifacts.
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\hf.py", line 164, in run_hf_training_task
    raise RuntimeError("Failed to serialize trained model artifacts.")
RuntimeError: Failed to serialize trained model artifacts.
2025-04-22 01:38:31,929 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 01:38:31,929 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnLC-BiPX15bH1oxKsdfqFF11OpBqOv8Mn2WHCncJMJtBOnERpCq4aMNj2zaX9PkA', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 01:38:31,930 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 01:38:32,525 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 02:20:19,351 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 02:20:19,352 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qllgTf0rQx3dIUqSWo2MORQqg-d_IFIVRKPyL7xic7XpXigH9j7UlMbmA3pXwl-ouQ', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 02:20:19,353 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 02:20:19,924 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 02:49:08,533 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 02:49:08,534 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qllLXltFuZfvoZJatmtgSYKW59fv-Lr9iGIJCjXT66Qn3MQE7ryHJGQqW0RRFHvGRw', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 02:49:08,535 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 02:49:09,108 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 03:11:56,186 - root - ERROR - LLM Batch Error (Rows 1-10): Error code: 429 - {'error': {'message': 'Rate limit reached for model `allam-2-7b` in organization `org_01j69b6f7me4rbeg0vw7hjm903` service tier `on_demand` on tokens per minute (TPM): Limit 6000, Used 7887, Requested 826. Please try again in 27.136999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-04-22 03:11:56,189 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\llm_classifier.py", line 493, in classify_texts_with_llm
    batch_responses = categorization_chain.batch(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\base.py", line 789, in batch
    return cast("list[Output]", list(executor.map(invoke, inputs, configs)))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 621, in result_iterator
    yield _result_or_cancel(fs.pop())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 319, in _result_or_cancel
    return fut.result(timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 458, in result
    return self.__get_result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 403, in __get_result
    raise self._exception
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\config.py", line 555, in _wrapped_fn
    return contexts.pop().run(fn, *args)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\base.py", line 782, in invoke
    return self.invoke(input, config, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\base.py", line 170, in invoke
    raise e
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\base.py", line 160, in invoke
    self._call(inputs, run_manager=run_manager)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\llm.py", line 126, in _call
    response = self.generate([inputs], run_manager=run_manager)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\llm.py", line 138, in generate
    return self.llm.generate_prompt(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 937, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 759, in generate
    self._generate_with_cache(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 1002, in _generate_with_cache
    result = self._generate(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_groq\chat_models.py", line 498, in _generate
    response = self.client.create(messages=message_dicts, **params)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\resources\chat\completions.py", line 322, in create
    return self._post(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1225, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 917, in request
    return self._request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1005, in _request
    return self._retry_request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1054, in _retry_request
    return self._request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1005, in _request
    return self._retry_request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1054, in _retry_request
    return self._request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1020, in _request
    raise self._make_status_error_from_response(err.response) from None
groq.RateLimitError: Error code: 429 - {'error': {'message': 'Rate limit reached for model `allam-2-7b` in organization `org_01j69b6f7me4rbeg0vw7hjm903` service tier `on_demand` on tokens per minute (TPM): Limit 6000, Used 7887, Requested 826. Please try again in 27.136999999s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}

2025-04-22 03:12:39,602 - root - ERROR - LLM Batch Error (Rows 11-20): Error code: 429 - {'error': {'message': 'Rate limit reached for model `allam-2-7b` in organization `org_01j69b6f7me4rbeg0vw7hjm903` service tier `on_demand` on tokens per minute (TPM): Limit 6000, Used 6317, Requested 806. Please try again in 11.237s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}
2025-04-22 03:12:39,604 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\llm_classifier.py", line 493, in classify_texts_with_llm
    batch_responses = categorization_chain.batch(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\base.py", line 789, in batch
    return cast("list[Output]", list(executor.map(invoke, inputs, configs)))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 621, in result_iterator
    yield _result_or_cancel(fs.pop())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 319, in _result_or_cancel
    return fut.result(timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 458, in result
    return self.__get_result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 403, in __get_result
    raise self._exception
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\config.py", line 555, in _wrapped_fn
    return contexts.pop().run(fn, *args)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\base.py", line 782, in invoke
    return self.invoke(input, config, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\base.py", line 170, in invoke
    raise e
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\base.py", line 160, in invoke
    self._call(inputs, run_manager=run_manager)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\llm.py", line 126, in _call
    response = self.generate([inputs], run_manager=run_manager)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\llm.py", line 138, in generate
    return self.llm.generate_prompt(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 937, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 759, in generate
    self._generate_with_cache(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 1002, in _generate_with_cache
    result = self._generate(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_groq\chat_models.py", line 498, in _generate
    response = self.client.create(messages=message_dicts, **params)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\resources\chat\completions.py", line 322, in create
    return self._post(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1225, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 917, in request
    return self._request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1005, in _request
    return self._retry_request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1054, in _retry_request
    return self._request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1005, in _request
    return self._retry_request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1054, in _retry_request
    return self._request(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\groq\_base_client.py", line 1020, in _request
    raise self._make_status_error_from_response(err.response) from None
groq.RateLimitError: Error code: 429 - {'error': {'message': 'Rate limit reached for model `allam-2-7b` in organization `org_01j69b6f7me4rbeg0vw7hjm903` service tier `on_demand` on tokens per minute (TPM): Limit 6000, Used 6317, Requested 806. Please try again in 11.237s. Need more tokens? Upgrade to Dev Tier today at https://console.groq.com/settings/billing', 'type': 'tokens', 'code': 'rate_limit_exceeded'}}

2025-04-22 03:21:03,986 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 03:21:03,987 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlmtyGZwgv8AiwOjgEuXFv1HOwkbrEymCfW7FD_sCQk9BMIXgm9NRjGQB_Qi4C8WGg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 03:21:03,988 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 03:21:04,294 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 03:22:01,407 - root - ERROR - LLM Suggestion: OutputFixingParser also failed: Error code: 400 - {'error': {'message': 'Please reduce the length of the messages or completion.', 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}
2025-04-22 03:22:01,408 - root - ERROR - Raw AI Response that failed parsing:
2025-04-22 03:22:01,408 - root - ERROR - To structure the data provided, we can define a JSON schema using the provided definitions for AISuggestionCategory, AISuggestionSegment, and AISuggestionTheme. Here is the JSON output based on the sample text data:

```json
{
  "themes": [
    {
      "name": "Chess",
      "categories": [
        {
          "name": "KCB Chess Club",
          "segments": [
            {
              "name": "Waridi Rapid Open Chess Tournament",
              "subsegments": [
                {
                  "name": "First Place",
                  "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "First Place"]
                },
                {
                  "name": "Chess Tournament",
                  "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
                }
              ]
            },
            {
              "name": "Chess",
              "segments": [
                {
                  "name": "Chess Tournament",
                  "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
               },
              {
                 "name": "KCB Chess Club",
                 "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
              }
            ]
          },
          {
            "name": "Chess",
            "segments": [
              {
                "name": "Waridi Rapid Open Chess Tournament",
               "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
            },
           {
              "name": "KCB Chess Club",
              "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
        }
      ]
          },
          {
            "name": "Chess",
            "segments": [
              {
                "name": "Waridi Rapid Open Chess Tournament",
              "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
        },
        {
          "name": "KCB Chess Club",
          "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
      }
    ]
      },
     {
        "name": "Chess",
        "segments": [
          {
            "name": "Waridi Rapid Open Chess Tournament",
          "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
     },
    {
        "name": "KCB Chess Club",
        "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
    }
  ]
     },
  {
      "name": "Chess",
      "segments": [
           {
               "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
    },
  {
           "keywords": ["Chess", "KCB", "Waridi Rapid Open Chess Tournament", "Chess Tournament"]
    }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
2025-04-22 03:22:21,913 - app.tasks.llm - ERROR - Task 197590cc-be21-4cc6-be1c-f7c241230161 failed: local variable 'hierarchy_levels' referenced before assignment
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\llm.py", line 114, in run_llm_classification_task
    df_results = classify_texts_with_llm( # Use directly imported function
  File "E:\Programming\AI projects\classyweb\backend\app\llm_classifier.py", line 447, in classify_texts_with_llm
    level_names = [level.capitalize() for level in hierarchy_levels]
UnboundLocalError: local variable 'hierarchy_levels' referenced before assignment
2025-04-22 03:24:24,716 - app.tasks.llm - ERROR - Task eda13fbd-ae24-4952-8d44-93c204b64923 failed: local variable 'hierarchy_levels' referenced before assignment
Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\tasks\llm.py", line 114, in run_llm_classification_task
    df_results = classify_texts_with_llm( # Use directly imported function
  File "E:\Programming\AI projects\classyweb\backend\app\llm_classifier.py", line 447, in classify_texts_with_llm
    # Note: Using OutputFixingParser in batch mode can be slow if many items fail parsing.
UnboundLocalError: local variable 'hierarchy_levels' referenced before assignment
2025-04-22 03:26:02,846 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 03:26:02,848 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlmySFnHlltI5kD9bRNaRmlEv_7pLiwFNYJbNMqEWB4bPtDhw1d4fWWqHDx3odn-3A', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 03:26:02,848 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 03:26:03,774 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 03:35:56,200 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 03:35:56,201 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlm6Ma5IIVqWAcFlqSyvl-PBsOJFjjEC5EarychI-PLDIbn5GBieHNcIwQnLOr8j2w', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 03:35:56,202 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 03:35:56,514 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 03:42:57,498 - root - ERROR - LLM Batch Error (Rows 1-10): 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-04-22 03:42:57,513 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\llm_classifier.py", line 661, in classify_texts_with_llm
    batch_responses = categorization_chain.batch(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\base.py", line 789, in batch
    return cast("list[Output]", list(executor.map(invoke, inputs, configs)))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 621, in result_iterator
    yield _result_or_cancel(fs.pop())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 319, in _result_or_cancel
    return fut.result(timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 458, in result
    return self.__get_result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 403, in __get_result
    raise self._exception
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\config.py", line 555, in _wrapped_fn
    return contexts.pop().run(fn, *args)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\base.py", line 782, in invoke
    return self.invoke(input, config, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\base.py", line 170, in invoke
    raise e
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\base.py", line 160, in invoke
    self._call(inputs, run_manager=run_manager)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\llm.py", line 126, in _call
    response = self.generate([inputs], run_manager=run_manager)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\llm.py", line 138, in generate
    return self.llm.generate_prompt(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 937, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 759, in generate
    self._generate_with_cache(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 1002, in _generate_with_cache
    result = self._generate(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_google_genai\chat_models.py", line 1089, in _generate
    response: GenerateContentResponse = _chat_with_retry(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_google_genai\chat_models.py", line 206, in _chat_with_retry
    return _chat_with_retry(**kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 338, in wrapped_f
    return copy(f, *args, **kw)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 477, in __call__
    do = self.iter(retry_state=retry_state)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 420, in exc_check
    raise retry_exc.reraise()
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 187, in reraise
    raise self.last_attempt.result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 451, in result
    return self.__get_result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 403, in __get_result
    raise self._exception
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 480, in __call__
    result = fn(*args, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_google_genai\chat_models.py", line 204, in _chat_with_retry
    raise e
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_google_genai\chat_models.py", line 188, in _chat_with_retry
    return generation_method(**kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 867, in generate_content
    response = rpc(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\retry\retry_unary.py", line 293, in retry_wrapped_func
    return retry_target(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\retry\retry_unary.py", line 153, in retry_target
    _retry_error_helper(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\retry\retry_base.py", line 212, in _retry_error_helper
    raise final_exc from source_exc
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\retry\retry_unary.py", line 144, in retry_target
    result = target()
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.NotFound: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.

2025-04-22 03:43:02,974 - root - ERROR - LLM Batch Error (Rows 11-20): 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-04-22 03:43:02,976 - root - ERROR - Traceback (most recent call last):
  File "E:\Programming\AI projects\classyweb\backend\app\llm_classifier.py", line 661, in classify_texts_with_llm
    batch_responses = categorization_chain.batch(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\base.py", line 789, in batch
    return cast("list[Output]", list(executor.map(invoke, inputs, configs)))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 621, in result_iterator
    yield _result_or_cancel(fs.pop())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 319, in _result_or_cancel
    return fut.result(timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 458, in result
    return self.__get_result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 403, in __get_result
    raise self._exception
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\config.py", line 555, in _wrapped_fn
    return contexts.pop().run(fn, *args)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\runnables\base.py", line 782, in invoke
    return self.invoke(input, config, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\base.py", line 170, in invoke
    raise e
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\base.py", line 160, in invoke
    self._call(inputs, run_manager=run_manager)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\llm.py", line 126, in _call
    response = self.generate([inputs], run_manager=run_manager)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain\chains\llm.py", line 138, in generate
    return self.llm.generate_prompt(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 937, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 759, in generate
    self._generate_with_cache(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_core\language_models\chat_models.py", line 1002, in _generate_with_cache
    result = self._generate(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_google_genai\chat_models.py", line 1089, in _generate
    response: GenerateContentResponse = _chat_with_retry(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_google_genai\chat_models.py", line 206, in _chat_with_retry
    return _chat_with_retry(**kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 338, in wrapped_f
    return copy(f, *args, **kw)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 477, in __call__
    do = self.iter(retry_state=retry_state)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 420, in exc_check
    raise retry_exc.reraise()
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 187, in reraise
    raise self.last_attempt.result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 451, in result
    return self.__get_result()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\concurrent\futures\_base.py", line 403, in __get_result
    raise self._exception
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\tenacity\__init__.py", line 480, in __call__
    result = fn(*args, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_google_genai\chat_models.py", line 204, in _chat_with_retry
    raise e
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\langchain_google_genai\chat_models.py", line 188, in _chat_with_retry
    return generation_method(**kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 867, in generate_content
    response = rpc(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\retry\retry_unary.py", line 293, in retry_wrapped_func
    return retry_target(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\retry\retry_unary.py", line 153, in retry_target
    _retry_error_helper(
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\retry\retry_base.py", line 212, in _retry_error_helper
    raise final_exc from source_exc
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\retry\retry_unary.py", line 144, in retry_target
    result = target()
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "E:\Programming\AI projects\classyweb\backend\classyweb_env\lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.NotFound: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.

2025-04-22 03:49:10,260 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 03:49:10,261 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnJPVwsa_pJVFhDLcpnkclC7j-euqG3Xq4shUwkYBZP-8oWDNc7KmTwJcwFP0LKgg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 03:49:10,262 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 03:49:10,569 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 04:04:06,548 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 04:04:06,549 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnJPVwsa_pJVFhDLcpnkclC7j-euqG3Xq4shUwkYBZP-8oWDNc7KmTwJcwFP0LKgg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 04:04:06,549 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 04:04:06,854 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 04:04:11,774 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 04:04:11,775 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qlnJPVwsa_pJVFhDLcpnkclC7j-euqG3Xq4shUwkYBZP-8oWDNc7KmTwJcwFP0LKgg', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 04:04:11,776 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 04:04:12,083 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 04:04:28,482 - app.api.auth - ERROR - Google OAuth error response: {
  "error": "invalid_grant",
  "error_description": "Bad Request"
}
2025-04-22 04:04:28,483 - app.api.auth - ERROR - Full token request data: {'code': '4/0Ab_5qllXOeP7mpIu5wwIjWJ1-vzCmEFkqv1gnIYogc6mKuI4JOlDxUbmxvHiurwJo4bw0w', 'client_id': '************-pilf1vc3ofm31omevmvljfjuievh3a21.apps.googleusercontent.com', 'client_secret': '***REDACTED***', 'redirect_uri': 'http://localhost:5173/auth/google/callback', 'grant_type': 'authorization_code'}
2025-04-22 04:04:28,484 - app.api.auth - ERROR - Request headers: {'Content-Type': 'application/x-www-form-urlencoded'}
2025-04-22 04:04:28,889 - app.api.auth - ERROR - This may be caused by:
1. The authorization code has expired (they typically expire after a few minutes)
2. The code has already been used
3. There's a time synchronization issue between your server and Google's servers
4. The redirect URI doesn't match what's registered in Google Cloud Console
Server timezone: E. Africa Standard Time (UTC+3), DST inactive

Client timezone: Africa/Nairobi (UTC+03:00), DST: inactive
2025-04-22 14:56:43,432 - app.redis_client - ERROR - Failed to store refresh token: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:56:47,458 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:56:52,080 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:56:57,148 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:57:26,454 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:57:30,467 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:57:34,488 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:57:38,497 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:59:00,177 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:59:05,474 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 14:59:35,475 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
2025-04-22 15:00:06,667 - app.redis_client - ERROR - Failed to check if token is blacklisted: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
