import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Home, 
  Copy,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  retryCount: number;
}

export class DashboardErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Dashboard Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error to monitoring service (if available)
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService = async (error: Error, errorInfo: ErrorInfo) => {
    // Enhanced error logging with better data collection
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem('userId'),
        sessionId: sessionStorage.getItem('sessionId'),
        dashboardId: localStorage.getItem('activeDashboardId'),
        errorBoundary: 'DashboardErrorBoundary',
        retryCount: this.state.retryCount,
        severity: this.getErrorSeverity(error),
        category: this.getErrorCategory(error),
        // Additional context
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        memory: (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize
        } : null,
        connectionType: (navigator as any).connection?.effectiveType || 'unknown'
      };

      // Send to backend error logging endpoint
      try {
        await fetch('/api/errors/log', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(errorData)
        });
      } catch (apiError) {
        // Fallback to local storage if API fails
        const storedErrors = JSON.parse(localStorage.getItem('errorLogs') || '[]');
        storedErrors.push(errorData);
        // Keep only last 10 errors to prevent storage overflow
        if (storedErrors.length > 10) {
          storedErrors.shift();
        }
        localStorage.setItem('errorLogs', JSON.stringify(storedErrors));
      }

      console.error('Dashboard Error Logged:', errorData);
    } catch (loggingError) {
      console.error('Failed to log error to service:', loggingError);
    }
  };

  private handleRetry = async () => {
    if (this.state.retryCount < this.maxRetries) {
      // Clear any cached data that might be causing issues
      try {
        // Clear dashboard cache
        sessionStorage.removeItem('dashboardCache');
        sessionStorage.removeItem('widgetCache');

        // Clear any stale API cache
        if ('caches' in window) {
          const cacheNames = await caches.keys();
          await Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
          );
        }
      } catch (cacheError) {
        console.warn('Failed to clear cache:', cacheError);
      }

      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        showDetails: false,
        retryCount: prevState.retryCount + 1,
      }));
    } else {
      // Max retries reached, offer more recovery options
      const shouldReload = window.confirm(
        'Maximum retry attempts reached. Would you like to reload the page? This will reset your current session.'
      );

      if (shouldReload) {
        window.location.reload();
      } else {
        // Offer to go to a safe page
        window.location.href = '/dashboard';
      }
    }
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails,
    }));
  };

  private copyErrorDetails = () => {
    const { error, errorInfo } = this.state;
    const errorText = `
Error: ${error?.message}
Stack: ${error?.stack}
Component Stack: ${errorInfo?.componentStack}
Timestamp: ${new Date().toISOString()}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      // Could show a toast here
      console.log('Error details copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy error details:', err);
    });
  };

  private getErrorSeverity = (error: Error): 'low' | 'medium' | 'high' => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'medium';
    }
    
    if (message.includes('chunk') || message.includes('loading')) {
      return 'low';
    }
    
    return 'high';
  };

  private getErrorCategory = (error: Error): string => {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('network') || message.includes('fetch')) {
      return 'Network Error';
    }

    if (message.includes('chunk') || message.includes('loading')) {
      return 'Loading Error';
    }

    if (stack.includes('websocket')) {
      return 'WebSocket Error';
    }

    if (stack.includes('dashboard') || stack.includes('widget')) {
      return 'Dashboard Error';
    }

    if (message.includes('permission') || message.includes('unauthorized')) {
      return 'Permission Error';
    }

    if (message.includes('timeout')) {
      return 'Timeout Error';
    }

    return 'Application Error';
  };

  private getErrorSuggestions = (error: Error): string[] => {
    const message = error.message.toLowerCase();
    const suggestions: string[] = [];

    if (message.includes('network') || message.includes('fetch')) {
      suggestions.push('Check your internet connection');
      suggestions.push('Verify the server is accessible');
      suggestions.push('Try refreshing the page');
    } else if (message.includes('chunk') || message.includes('loading')) {
      suggestions.push('Clear your browser cache');
      suggestions.push('Try refreshing the page');
      suggestions.push('Check if you have sufficient storage space');
    } else if (message.includes('permission') || message.includes('unauthorized')) {
      suggestions.push('Check your login status');
      suggestions.push('Verify you have the required permissions');
      suggestions.push('Try logging out and back in');
    } else if (message.includes('timeout')) {
      suggestions.push('The operation took too long to complete');
      suggestions.push('Try again with a smaller dataset');
      suggestions.push('Check your network connection');
    } else {
      suggestions.push('Try refreshing the page');
      suggestions.push('Clear your browser cache');
      suggestions.push('Contact support if the issue persists');
    }

    return suggestions;
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, showDetails, retryCount } = this.state;
      const severity = error ? this.getErrorSeverity(error) : 'high';
      const category = error ? this.getErrorCategory(error) : 'Application Error';
      const canRetry = retryCount < this.maxRetries;

      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-destructive/10 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-xl">Dashboard Error</CardTitle>
                  <CardDescription>
                    Something went wrong while loading the dashboard
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge 
                    variant={severity === 'high' ? 'destructive' : severity === 'medium' ? 'default' : 'secondary'}
                  >
                    {severity.toUpperCase()}
                  </Badge>
                  <Badge variant="outline">{category}</Badge>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Alert>
                <Bug className="h-4 w-4" />
                <AlertTitle>Error Details</AlertTitle>
                <AlertDescription>
                  {error?.message || 'An unexpected error occurred'}
                </AlertDescription>
              </Alert>

              {/* Error Suggestions */}
              {error && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Suggested Solutions</AlertTitle>
                  <AlertDescription>
                    <ul className="list-disc list-inside space-y-1 mt-2">
                      {this.getErrorSuggestions(error).map((suggestion, index) => (
                        <li key={index} className="text-sm">{suggestion}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {retryCount > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Retry Attempt</AlertTitle>
                  <AlertDescription>
                    This is retry attempt {retryCount} of {this.maxRetries}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex flex-col sm:flex-row gap-3">
                {canRetry ? (
                  <Button onClick={this.handleRetry} className="flex-1">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                ) : (
                  <Button onClick={this.handleRetry} className="flex-1">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reload Page
                  </Button>
                )}
                
                <Button variant="outline" onClick={this.handleGoHome} className="flex-1">
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>

              <Separator />

              <div className="space-y-3">
                <Button
                  variant="ghost"
                  onClick={this.toggleDetails}
                  className="w-full justify-between"
                >
                  <span>Technical Details</span>
                  {showDetails ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>

                {showDetails && (
                  <div className="space-y-3">
                    <div className="p-3 bg-muted rounded-lg">
                      <div className="text-sm font-medium mb-2">Error Message</div>
                      <code className="text-xs break-all">{error?.message}</code>
                    </div>

                    {error?.stack && (
                      <div className="p-3 bg-muted rounded-lg">
                        <div className="text-sm font-medium mb-2">Stack Trace</div>
                        <pre className="text-xs overflow-auto max-h-32 whitespace-pre-wrap">
                          {error.stack}
                        </pre>
                      </div>
                    )}

                    {errorInfo?.componentStack && (
                      <div className="p-3 bg-muted rounded-lg">
                        <div className="text-sm font-medium mb-2">Component Stack</div>
                        <pre className="text-xs overflow-auto max-h-32 whitespace-pre-wrap">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    )}

                    <Button
                      variant="outline"
                      onClick={this.copyErrorDetails}
                      className="w-full"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy Error Details
                    </Button>
                  </div>
                )}
              </div>

              <div className="text-xs text-muted-foreground text-center">
                Error ID: {Date.now().toString(36)} • 
                Time: {new Date().toLocaleString()}
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export const withDashboardErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <DashboardErrorBoundary fallback={fallback}>
      <Component {...props} />
    </DashboardErrorBoundary>
  );

  WrappedComponent.displayName = `withDashboardErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};
