"""
Document processing service for extracting business information from uploaded files.

This module provides functionality to process various document formats
and extract business information for auto-filling business profile forms.
"""

import logging
import os
import tempfile
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import asyncio
from dataclasses import dataclass

# Document processing imports
try:
    import pypdf as PyPDF2  # Use pypdf instead of deprecated PyPDF2
except ImportError:
    import PyPDF2  # Fallback to PyPDF2 if pypdf not available
from docx import Document
import pandas as pd

# Import existing services
from agents.utils.vector_service import VectorService
from agents.utils.memory_service import MemoryService
from agents.utils.knowledge_graph_service import KnowledgeGraphService

logger = logging.getLogger(__name__)

@dataclass
class ProcessedDocument:
    """Container for processed document content."""
    filename: str
    file_type: str
    content: str
    metadata: Dict[str, Any]
    extracted_entities: Dict[str, Any]
    structured_data: Dict[str, Any]
    confidence_score: float

class DocumentProcessingService:
    """Service for processing documents and extracting business information."""
    
    def __init__(self):
        self.vector_service = VectorService()
        self.memory_service = MemoryService()
        self.kg_service = KnowledgeGraphService()
        
        # Supported file types
        self.supported_types = {
            '.pdf': self._process_pdf,
            '.docx': self._process_docx,
            '.doc': self._process_doc,
            '.txt': self._process_txt,
            '.csv': self._process_csv,
            '.xlsx': self._process_excel,
            '.xls': self._process_excel
        }
        
        # Business information patterns
        self.business_patterns = {
            'company_indicators': [
                'company', 'corporation', 'corp', 'inc', 'llc', 'ltd',
                'business', 'enterprise', 'organization', 'firm'
            ],
            'industry_indicators': [
                'industry', 'sector', 'market', 'field', 'domain',
                'specializes in', 'focuses on', 'provides', 'offers'
            ],
            'service_indicators': [
                'services', 'products', 'solutions', 'offerings',
                'specialties', 'capabilities', 'expertise'
            ],
            'contact_indicators': [
                'contact', 'phone', 'email', 'address', 'location',
                'headquarters', 'office', 'reach us'
            ],
            # Marketing-specific patterns (consolidated from marketing form)
            'marketing_indicators': [
                'marketing', 'advertising', 'promotion', 'campaign',
                'brand', 'branding', 'awareness', 'lead generation'
            ],
            'budget_indicators': [
                'budget', 'cost', 'price', 'investment', 'spend',
                'financial', 'allocation', 'funding', 'expense'
            ],
            'timeline_indicators': [
                'timeline', 'deadline', 'schedule', 'launch',
                'timeframe', 'duration', 'phase', 'milestone'
            ],
            'platform_indicators': [
                'platform', 'channel', 'social media', 'facebook',
                'instagram', 'linkedin', 'twitter', 'email marketing',
                'seo', 'digital', 'online', 'website'
            ],
            'competitive_indicators': [
                'competitor', 'competition', 'competitive advantage',
                'market position', 'differentiation', 'unique selling'
            ]
        }

    async def process_document(self, file_path: str, filename: str) -> ProcessedDocument:
        """
        Process a document and extract business information.
        
        Args:
            file_path: Path to the uploaded file
            filename: Original filename
            
        Returns:
            ProcessedDocument with extracted information
        """
        try:
            # Determine file type
            file_extension = Path(filename).suffix.lower()
            
            if file_extension not in self.supported_types:
                raise ValueError(f"Unsupported file type: {file_extension}")
            
            logger.info(f"Processing document: {filename} ({file_extension})")
            
            # Process the document based on type
            processor = self.supported_types[file_extension]
            content, metadata = await processor(file_path)
            
            # Extract entities and structured data
            extracted_entities = await self._extract_entities(content)
            structured_data = await self._extract_structured_data(content, extracted_entities)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(content, extracted_entities)
            
            return ProcessedDocument(
                filename=filename,
                file_type=file_extension,
                content=content,
                metadata=metadata,
                extracted_entities=extracted_entities,
                structured_data=structured_data,
                confidence_score=confidence_score
            )
            
        except Exception as e:
            logger.error(f"Error processing document {filename}: {e}")
            raise

    async def _process_pdf(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Process PDF file."""
        content = ""
        metadata = {}
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Extract metadata
                if pdf_reader.metadata:
                    metadata = {
                        'title': pdf_reader.metadata.get('/Title', ''),
                        'author': pdf_reader.metadata.get('/Author', ''),
                        'subject': pdf_reader.metadata.get('/Subject', ''),
                        'creator': pdf_reader.metadata.get('/Creator', ''),
                        'pages': len(pdf_reader.pages)
                    }
                
                # Extract text from all pages
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            content += f"\n--- Page {page_num + 1} ---\n{page_text}"
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num + 1}: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"Error processing PDF: {e}")
            raise
        
        return content.strip(), metadata

    async def _process_docx(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Process DOCX file."""
        try:
            doc = Document(file_path)
            
            # Extract metadata
            metadata = {
                'title': doc.core_properties.title or '',
                'author': doc.core_properties.author or '',
                'subject': doc.core_properties.subject or '',
                'created': str(doc.core_properties.created) if doc.core_properties.created else '',
                'paragraphs': len(doc.paragraphs)
            }
            
            # Extract text from paragraphs
            content = ""
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content += " | ".join(row_text) + "\n"
            
            return content.strip(), metadata
            
        except Exception as e:
            logger.error(f"Error processing DOCX: {e}")
            raise

    async def _process_doc(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Process DOC file (legacy format)."""
        # For DOC files, we'll try to use python-docx2txt or similar
        # For now, return basic processing
        try:
            # This is a simplified implementation
            # In production, you might want to use python-docx2txt or antiword
            with open(file_path, 'rb') as file:
                content = file.read().decode('utf-8', errors='ignore')
            
            metadata = {
                'file_size': os.path.getsize(file_path),
                'format': 'DOC (legacy)'
            }
            
            return content, metadata
            
        except Exception as e:
            logger.error(f"Error processing DOC: {e}")
            # Fallback to basic text extraction
            return "Unable to extract text from DOC file", {}

    async def _process_txt(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Process TXT file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            metadata = {
                'file_size': os.path.getsize(file_path),
                'lines': len(content.split('\n')),
                'characters': len(content)
            }
            
            return content, metadata
            
        except UnicodeDecodeError:
            # Try different encodings
            encodings = ['latin-1', 'cp1252', 'iso-8859-1']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                    
                    metadata = {
                        'file_size': os.path.getsize(file_path),
                        'encoding': encoding,
                        'lines': len(content.split('\n'))
                    }
                    
                    return content, metadata
                except UnicodeDecodeError:
                    continue
            
            raise ValueError("Unable to decode text file with any supported encoding")

    async def _process_csv(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Process CSV file."""
        try:
            df = pd.read_csv(file_path)
            
            # Convert to text representation
            content = f"CSV Data Summary:\n"
            content += f"Columns: {', '.join(df.columns.tolist())}\n"
            content += f"Shape: {df.shape[0]} rows, {df.shape[1]} columns\n\n"
            
            # Add first few rows as text
            content += "Sample Data:\n"
            content += df.head(10).to_string(index=False)
            
            # Add column descriptions if available
            content += "\n\nColumn Statistics:\n"
            content += df.describe(include='all').to_string()
            
            metadata = {
                'rows': df.shape[0],
                'columns': df.shape[1],
                'column_names': df.columns.tolist(),
                'data_types': df.dtypes.to_dict()
            }
            
            return content, metadata
            
        except Exception as e:
            logger.error(f"Error processing CSV: {e}")
            raise

    async def _process_excel(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Process Excel file."""
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(file_path)
            content = ""
            metadata = {
                'sheets': excel_file.sheet_names,
                'total_sheets': len(excel_file.sheet_names)
            }
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                content += f"\n--- Sheet: {sheet_name} ---\n"
                content += f"Columns: {', '.join(df.columns.tolist())}\n"
                content += f"Shape: {df.shape[0]} rows, {df.shape[1]} columns\n"
                
                # Add sample data
                if not df.empty:
                    content += "Sample Data:\n"
                    content += df.head(5).to_string(index=False) + "\n"
                
                metadata[f'sheet_{sheet_name}'] = {
                    'rows': df.shape[0],
                    'columns': df.shape[1],
                    'column_names': df.columns.tolist()
                }
            
            return content.strip(), metadata
            
        except Exception as e:
            logger.error(f"Error processing Excel: {e}")
            raise

    async def _extract_entities(self, content: str) -> Dict[str, Any]:
        """Extract business entities from document content."""
        entities = {
            'companies': [],
            'industries': [],
            'services': [],
            'contacts': [],
            'locations': [],
            'dates': [],
            'financial_info': []
        }
        
        # Simple pattern-based extraction
        import re
        
        # Extract company names (basic patterns)
        company_patterns = [
            r'\b[A-Z][a-zA-Z\s&]+(?:Inc|LLC|Corp|Corporation|Company|Ltd|Limited)\b',
            r'\b[A-Z][a-zA-Z\s&]+ (?:Inc|LLC|Corp|Corporation|Company|Ltd|Limited)\b'
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, content)
            entities['companies'].extend(matches)
        
        # Extract email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, content)
        entities['contacts'].extend([{'type': 'email', 'value': email} for email in emails])
        
        # Extract phone numbers
        phone_pattern = r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
        phones = re.findall(phone_pattern, content)
        entities['contacts'].extend([{'type': 'phone', 'value': '-'.join(phone[1:])} for phone in phones if phone[1]])
        
        # Extract potential addresses
        address_pattern = r'\d+\s+[A-Za-z\s,]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Drive|Dr|Lane|Ln|Way|Court|Ct)\b[^.]*'
        addresses = re.findall(address_pattern, content)
        entities['locations'].extend(addresses)
        
        # Remove duplicates
        for key in entities:
            if isinstance(entities[key], list):
                entities[key] = list(set(str(item) for item in entities[key]))
        
        return entities

    async def _extract_structured_data(self, content: str, entities: Dict[str, Any]) -> Dict[str, Any]:
        """Extract structured business data from content and entities."""
        structured_data = {
            'business_name': '',
            'description': '',
            'industry': '',
            'services': [],
            'contact_info': {},
            'location': '',
            'key_information': []
        }
        
        # Extract business name from companies
        if entities.get('companies'):
            structured_data['business_name'] = entities['companies'][0]
        
        # Extract contact information
        contact_info = {}
        for contact in entities.get('contacts', []):
            if isinstance(contact, dict):
                contact_type = contact.get('type')
                if contact_type not in contact_info:
                    contact_info[contact_type] = []
                contact_info[contact_type].append(contact.get('value'))
        structured_data['contact_info'] = contact_info
        
        # Extract location
        if entities.get('locations'):
            structured_data['location'] = entities['locations'][0]
        
        # Extract key sentences that might contain business information
        sentences = content.split('.')
        key_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20:  # Filter out very short sentences
                # Check if sentence contains business-related keywords
                for category, keywords in self.business_patterns.items():
                    if any(keyword.lower() in sentence.lower() for keyword in keywords):
                        key_sentences.append(sentence)
                        break
        
        structured_data['key_information'] = key_sentences[:10]  # Limit to top 10
        
        return structured_data

    def _calculate_confidence_score(self, content: str, entities: Dict[str, Any]) -> float:
        """Calculate confidence score for extracted information."""
        score = 0.0
        
        # Base score for having content
        if content and len(content) > 100:
            score += 0.2
        
        # Score for having company information
        if entities.get('companies'):
            score += 0.3
        
        # Score for having contact information
        if entities.get('contacts'):
            score += 0.2
        
        # Score for having location information
        if entities.get('locations'):
            score += 0.1
        
        # Score for business-related keywords
        business_keywords = sum(len(keywords) for keywords in self.business_patterns.values())
        found_keywords = 0
        
        content_lower = content.lower()
        for keywords in self.business_patterns.values():
            for keyword in keywords:
                if keyword.lower() in content_lower:
                    found_keywords += 1
        
        keyword_ratio = found_keywords / business_keywords if business_keywords > 0 else 0
        score += keyword_ratio * 0.2
        
        return min(score, 1.0)  # Cap at 1.0

    async def extract_business_info(self, file_path: str, filename: str) -> Dict[str, Any]:
        """
        Extract business information from a document file.
        
        Args:
            file_path: Path to the uploaded file
            filename: Original filename
            
        Returns:
            Dictionary with extracted business information
        """
        try:
            processed_doc = await self.process_document(file_path, filename)
            
            # Structure the information for business profile auto-fill
            business_info = {
                'source_file': filename,
                'file_type': processed_doc.file_type,
                'business_name': processed_doc.structured_data.get('business_name', ''),
                'description': processed_doc.structured_data.get('description', ''),
                'industry': processed_doc.structured_data.get('industry', ''),
                'services': processed_doc.structured_data.get('services', []),
                'contact_info': processed_doc.structured_data.get('contact_info', {}),
                'location': processed_doc.structured_data.get('location', ''),
                'key_information': processed_doc.structured_data.get('key_information', []),
                'extracted_entities': processed_doc.extracted_entities,
                'confidence_score': processed_doc.confidence_score,
                'metadata': processed_doc.metadata,
                'content_summary': processed_doc.content[:1000] if processed_doc.content else "",
                'extraction_timestamp': asyncio.get_event_loop().time()
            }
            
            return business_info
            
        except Exception as e:
            logger.error(f"Error extracting business info from {filename}: {e}")
            raise
