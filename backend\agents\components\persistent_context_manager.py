"""
Persistent Context Manager using mem0 for maintaining file context and conversation history.

This component leverages mem0's memory capabilities to ensure that uploaded files
and conversation context persist throughout the entire conversation, enabling
agents to answer follow-up questions without losing context.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from ..utils.memory_service import MemoryService
from .base import AgentComponent

logger = logging.getLogger(__name__)


class PersistentContextManager(AgentComponent):
    """
    Manages persistent context using mem0 for file uploads and conversation history.

    This component ensures that:
    1. Uploaded files remain accessible throughout the conversation
    2. Conversation history is maintained and searchable
    3. Context is preserved across different agent interactions
    4. File metadata and data source information persists
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the persistent context manager."""
        super().__init__(config)
        self.memory_service = MemoryService()
        self.enable_file_persistence = config.get("enable_file_persistence", True)
        self.enable_conversation_memory = config.get("enable_conversation_memory", True)
        self.context_retention_limit = config.get("context_retention_limit", 10)
        self.file_context_ttl = config.get("file_context_ttl", 86400)  # 24 hours

        logger.info(f"Initialized PersistentContextManager with file_persistence={self.enable_file_persistence}, conversation_memory={self.enable_conversation_memory}")

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process context to maintain persistent file and conversation context.

        Args:
            context: The current AgentProcessingContext

        Returns:
            Enhanced context with persistent memory integration
        """
        user_id = str(context.user_id)
        conversation_id = str(context.conversation_id)

        # Initialize component data
        component_data = context.component_data.setdefault(self.name, {})

        # 0. Process conversation history from orchestrator
        await self._process_conversation_history(context, user_id, conversation_id, component_data)

        # 1. Handle file context persistence
        if self.enable_file_persistence:
            await self._handle_file_context(context, user_id, conversation_id, component_data)

        # 2. Handle conversation memory
        if self.enable_conversation_memory:
            await self._handle_conversation_memory(context, user_id, conversation_id, component_data)

        # 3. Retrieve and inject persistent context
        await self._inject_persistent_context(context, user_id, conversation_id, component_data)

        return context

    async def _process_conversation_history(self, context: "AgentProcessingContext", user_id: str,
                                          conversation_id: str, component_data: Dict[str, Any]) -> None:
        """Process conversation history from orchestrator and make it available to components."""
        try:
            # Extract conversation history from initial context
            conversation_history = context.initial_context.get("conversation_history", [])
            conversation_context = context.initial_context.get("conversation_context", {})

            if conversation_history:
                logger.info(f"PersistentContextManager: Processing {len(conversation_history)} conversation messages")

                # Store conversation history in component data for easy access
                component_data["conversation_history"] = conversation_history
                component_data["conversation_context"] = conversation_context

                # Add conversation summary to context metadata
                context.metadata["conversation_summary"] = {
                    "total_messages": len(conversation_history),
                    "last_message_timestamp": conversation_history[-1].get("timestamp") if conversation_history else None,
                    "conversation_id": conversation_id,
                    "has_data_context": any(
                        msg.get("metadata", {}).get("data_source") for msg in conversation_history
                    )
                }

                # Extract data source context from conversation history if not already present
                if "data_source" not in context.initial_context:
                    for msg in reversed(conversation_history):  # Start from most recent
                        msg_metadata = msg.get("metadata", {})
                        if msg_metadata.get("data_source"):
                            context.initial_context["data_source"] = msg_metadata["data_source"]
                            logger.info(f"PersistentContextManager: Extracted data source from conversation history: {msg_metadata['data_source'].get('name', 'unknown')}")
                            break

                # Create a formatted conversation context for LLM consumption
                formatted_history = self._format_conversation_for_llm(conversation_history)
                component_data["formatted_conversation_history"] = formatted_history

                logger.info(f"PersistentContextManager: Successfully processed conversation history")
            else:
                logger.info("PersistentContextManager: No conversation history found in initial context")
                component_data["conversation_history"] = []
                component_data["conversation_context"] = {}

        except Exception as e:
            logger.error(f"PersistentContextManager: Error processing conversation history: {e}")

    def _format_conversation_for_llm(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Format conversation history for LLM consumption."""
        if not conversation_history:
            return ""

        formatted_lines = ["### Previous Conversation:"]

        # Limit to last 10 messages to avoid context overflow
        recent_messages = conversation_history[-10:] if len(conversation_history) > 10 else conversation_history

        for msg in recent_messages:
            sender = msg.get("sender", "unknown")
            content = msg.get("content", "")
            timestamp = msg.get("timestamp", "")

            # Skip processing messages
            if content == "Processing your request...":
                continue

            # Format timestamp
            time_str = ""
            if timestamp:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = f" [{dt.strftime('%H:%M')}]"
                except:
                    pass

            # Format based on sender
            if sender == "user":
                formatted_lines.append(f"User{time_str}: {content}")
            elif sender == "ai":
                # Check for generated content
                if msg.get("metadata", {}).get("generated_content", False):
                    formatted_lines.append(f"AI{time_str}: [Generated content based on user request]")
                else:
                    # Truncate long AI responses
                    truncated_content = content[:200] + "..." if len(content) > 200 else content
                    formatted_lines.append(f"AI{time_str}: {truncated_content}")

        formatted_lines.append("### End Previous Conversation\n")
        return "\n".join(formatted_lines)

    async def _handle_file_context(self, context: "AgentProcessingContext", user_id: str,
                                 conversation_id: str, component_data: Dict[str, Any]) -> None:
        """Handle file context persistence in memory."""
        try:
            # Check for new file uploads in current context
            data_source = context.initial_context.get("data_source")
            if data_source and isinstance(data_source, dict):
                # Extract file information
                file_info = {
                    "id": data_source.get("id"),
                    "name": data_source.get("name"),
                    "type": data_source.get("type"),
                    "file_path": data_source.get("file_path"),
                    "metadata": data_source.get("metadata", {}),
                    "source_metadata": data_source.get("source_metadata", {})
                }

                # Add additional file details if available
                if "columns" in data_source.get("metadata", {}):
                    file_info["columns"] = data_source["metadata"]["columns"]
                if "shape" in data_source.get("metadata", {}):
                    file_info["shape"] = data_source["metadata"]["shape"]

                # Store file context in memory
                memory_metadata = {
                    "timestamp": time.time(),
                    "persona_id": context.agent_config.id if context.agent_config else "unknown"
                }

                result = self.memory_service.add_file_context(
                    file_info=file_info,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    metadata=memory_metadata
                )

                if result:
                    component_data["last_file_stored"] = file_info
                    logger.info(f"Stored file context in memory: {file_info.get('name')}")
                else:
                    logger.warning(f"Failed to store file context in memory: {file_info.get('name')}")

            # Check for attached files in context
            attached_files = context.initial_context.get("attached_files", [])
            for file_data in attached_files:
                if isinstance(file_data, dict) and file_data.get("id"):
                    memory_metadata = {
                        "timestamp": time.time(),
                        "persona_id": context.agent_config.id if context.agent_config else "unknown"
                    }

                    self.memory_service.add_file_context(
                        file_info=file_data,
                        user_id=user_id,
                        conversation_id=conversation_id,
                        metadata=memory_metadata
                    )
                    logger.debug(f"Stored attached file context: {file_data.get('name')}")

        except Exception as e:
            logger.error(f"Error handling file context: {e}")

    async def _handle_conversation_memory(self, context: "AgentProcessingContext", user_id: str,
                                        conversation_id: str, component_data: Dict[str, Any]) -> None:
        """Handle conversation memory storage."""
        try:
            # Store current interaction in memory
            if context.message:
                # Create a summary of the current interaction
                interaction_summary = f"User message: {context.message[:200]}..."
                if len(context.message) <= 200:
                    interaction_summary = f"User message: {context.message}"

                # Add context about any data operations or analysis
                if "data_source" in context.initial_context:
                    data_source = context.initial_context["data_source"]
                    interaction_summary += f" [Context: User working with file '{data_source.get('name', 'unknown')}']"

                memory_metadata = {
                    "timestamp": time.time(),
                    "persona_id": context.agent_config.id if context.agent_config else "unknown",
                    "message_type": "user_interaction"
                }

                self.memory_service.add_conversation_context(
                    context_summary=interaction_summary,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    metadata=memory_metadata
                )

                component_data["last_interaction_stored"] = time.time()

        except Exception as e:
            logger.error(f"Error handling conversation memory: {e}")

    async def _inject_persistent_context(self, context: "AgentProcessingContext", user_id: str,
                                       conversation_id: str, component_data: Dict[str, Any]) -> None:
        """Inject persistent context from memory into current context."""
        try:
            # 1. Retrieve file context for this conversation
            file_context_results = self.memory_service.search_file_context(
                user_id=user_id,
                conversation_id=conversation_id,
                limit=5
            )

            # 2. Check for cached file data specifically
            cached_file_memories = self.memory_service.search_memories(
                query="cached file data",
                user_id=user_id,
                limit=3,
                metadata_filter={
                    "conversation_id": conversation_id,
                    "type": "cached_file_data"
                }
            )

            # 3. Search for relevant conversation memories
            conversation_memories = self.memory_service.search_memories(
                query=context.message or "conversation context",
                user_id=user_id,
                limit=5,
                metadata_filter={
                    "conversation_id": conversation_id,
                    "type": "conversation_context"
                }
            )

            # 3. Inject cached file data into context if available
            if cached_file_memories.get("results"):
                latest_cached_file = cached_file_memories["results"][0]
                cached_content = latest_cached_file.get("content", "")

                # Try to extract file metadata from the cached memory
                try:
                    # The content should contain file information
                    if "file_id" in cached_content and "file_name" in cached_content:
                        cached_file_data = {
                            "file_id": cached_content.get("file_id"),
                            "file_name": cached_content.get("file_name"),
                            "file_path": cached_content.get("file_path"),
                            "shape": cached_content.get("shape"),
                            "columns": cached_content.get("columns"),
                            "dtypes": cached_content.get("dtypes"),
                            "cached_at": cached_content.get("cached_at")
                        }

                        # Inject cached file data into context
                        context.initial_context["cached_file_data"] = cached_file_data
                        component_data["cached_file_injected"] = True
                        logger.info(f"Injected cached file data: {cached_file_data['file_name']}")
                except Exception as e:
                    logger.warning(f"Failed to parse cached file data: {e}")

            # 4. Inject file context into current context
            if file_context_results.get("results"):
                persistent_files = []
                for file_memory in file_context_results["results"]:
                    metadata = file_memory.get("metadata", {})
                    if metadata.get("file_id") and metadata.get("file_name"):
                        file_info = {
                            "id": metadata.get("file_id"),
                            "name": metadata.get("file_name"),
                            "type": "file",
                            "source": "memory",
                            "memory_content": file_memory.get("memory", "")
                        }
                        persistent_files.append(file_info)

                if persistent_files:
                    # Add to context if not already present
                    if "data_source" not in context.initial_context and persistent_files:
                        # Use the most recent file as the primary data source
                        context.initial_context["data_source"] = persistent_files[0]
                        logger.info(f"Injected persistent file context: {persistent_files[0]['name']}")

                    # Add all persistent files to attached_files
                    existing_attached = context.initial_context.get("attached_files", [])
                    context.initial_context["attached_files"] = existing_attached + persistent_files

                    component_data["persistent_files_injected"] = len(persistent_files)

            # 4. Inject conversation context
            if conversation_memories.get("results"):
                conversation_context = []
                for memory in conversation_memories["results"]:
                    conversation_context.append({
                        "content": memory.get("memory", ""),
                        "timestamp": memory.get("metadata", {}).get("timestamp", 0),
                        "type": "memory"
                    })

                if conversation_context:
                    context.initial_context["persistent_conversation_context"] = conversation_context
                    component_data["conversation_memories_injected"] = len(conversation_context)
                    logger.debug(f"Injected {len(conversation_context)} conversation memories")

        except Exception as e:
            logger.error(f"Error injecting persistent context: {e}")
