"""
Shared Context models for cross-agent intelligence system.

This module provides Pydantic models for managing shared context,
agent insights, and interactions across AI personas.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum

from pydantic import BaseModel, Field


class InsightType(str, Enum):
    """Enum for agent insight types."""
    MARKETING_STRATEGY = "marketing_strategy"
    DATA_INSIGHTS = "data_insights"
    CAMPAIGN_RESULTS = "campaign_results"
    AUDIENCE_INSIGHTS = "audience_insights"
    CONTENT_PERFORMANCE = "content_performance"
    TRENDS = "trends"
    PATTERNS = "patterns"
    RECOMMENDATIONS = "recommendations"
    CATEGORIZATION = "categorization"
    ORGANIZATION = "organization"
    DATA_QUALITY = "data_quality"
    PROCESSING_RESULTS = "processing_results"
    TEXT_INSIGHTS = "text_insights"
    SUMMARIES = "summaries"
    ENTITIES = "entities"
    SENTIMENT = "sentiment"
    USER_INTENT = "user_intent"
    WORKFLOW = "workflow"
    COORDINATION = "coordination"
    GENERAL = "general"


class InteractionOutcome(str, Enum):
    """Enum for interaction outcomes."""
    SUCCESS = "success"
    ERROR = "error"
    PARTIAL = "partial"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
    UNKNOWN = "unknown"


class ContextType(str, Enum):
    """Enum for shared context types."""
    HANDOFF = "handoff"
    COLLABORATION = "collaboration"
    INSIGHT_SHARING = "insight_sharing"
    WORKFLOW_COORDINATION = "workflow_coordination"
    DATA_SHARING = "data_sharing"


# Agent Insight Models
class AgentInsightBase(BaseModel):
    """Base model for agent insights."""
    source_agent_id: str = Field(..., description="ID of the agent that generated the insight")
    insight_type: InsightType = Field(..., description="Type of insight")
    content: str = Field(..., min_length=1, description="Content of the insight")
    insight_metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    relevance_tags: Optional[List[str]] = Field(default_factory=list, description="Tags for relevance matching")
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0, description="Confidence score for the insight")


class AgentInsightCreate(AgentInsightBase):
    """Model for creating agent insights."""
    pass


class AgentInsightUpdate(BaseModel):
    """Model for updating agent insights."""
    content: Optional[str] = Field(None, min_length=1)
    insight_metadata: Optional[Dict[str, Any]] = None
    relevance_tags: Optional[List[str]] = None
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    is_active: Optional[bool] = None


class AgentInsightResponse(AgentInsightBase):
    """Model for agent insight responses."""
    id: str
    business_profile_id: str
    access_count: int = 0
    is_active: bool = True
    created_at: datetime
    updated_at: datetime
    last_accessed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Agent Interaction Models
class AgentInteractionBase(BaseModel):
    """Base model for agent interactions."""
    agent_id: str = Field(..., description="ID of the agent")
    user_message: Optional[str] = Field(None, description="User message that triggered the interaction")
    agent_response: Optional[str] = Field(None, description="Agent's response")
    context_used: Optional[List[str]] = Field(default_factory=list, description="Context items used")
    tools_used: Optional[List[str]] = Field(default_factory=list, description="Tools used in interaction")
    insights_generated: Optional[List[str]] = Field(default_factory=list, description="Insights generated")
    outcome: InteractionOutcome = Field(default=InteractionOutcome.UNKNOWN, description="Interaction outcome")
    interaction_metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")


class AgentInteractionCreate(AgentInteractionBase):
    """Model for creating agent interactions."""
    pass


class AgentInteractionUpdate(BaseModel):
    """Model for updating agent interactions."""
    agent_response: Optional[str] = None
    context_used: Optional[List[str]] = None
    tools_used: Optional[List[str]] = None
    insights_generated: Optional[List[str]] = None
    outcome: Optional[InteractionOutcome] = None
    interaction_metadata: Optional[Dict[str, Any]] = None


class AgentInteractionResponse(AgentInteractionBase):
    """Model for agent interaction responses."""
    id: str
    business_profile_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Shared Context Models
class SharedContextBase(BaseModel):
    """Base model for shared context."""
    source_agent_id: str = Field(..., description="ID of the source agent")
    target_agent_id: str = Field(..., description="ID of the target agent")
    context_type: ContextType = Field(..., description="Type of shared context")
    context_data: Dict[str, Any] = Field(..., description="Context data to share")
    context_metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    expires_at: Optional[datetime] = Field(None, description="When the context expires")


class SharedContextCreate(SharedContextBase):
    """Model for creating shared context."""
    pass


class SharedContextUpdate(BaseModel):
    """Model for updating shared context."""
    context_data: Optional[Dict[str, Any]] = None
    context_metadata: Optional[Dict[str, Any]] = None
    is_consumed: Optional[bool] = None
    expires_at: Optional[datetime] = None


class SharedContextResponse(SharedContextBase):
    """Model for shared context responses."""
    id: str
    business_profile_id: str
    is_consumed: bool = False
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Cross-Agent Intelligence Models
class CrossAgentContextRequest(BaseModel):
    """Request model for cross-agent context."""
    agent_id: str = Field(..., description="Requesting agent ID")
    business_profile_id: str = Field(..., description="Business profile ID")
    context_types: Optional[List[str]] = Field(default_factory=list, description="Specific context types to retrieve")
    max_insights: int = Field(default=20, ge=1, le=100, description="Maximum number of insights to retrieve")
    max_interactions: int = Field(default=10, ge=1, le=50, description="Maximum number of interactions to retrieve")


class CrossAgentContextResponse(BaseModel):
    """Response model for cross-agent context."""
    insights: List[AgentInsightResponse] = Field(default_factory=list)
    interactions: List[AgentInteractionResponse] = Field(default_factory=list)
    shared_contexts: List[SharedContextResponse] = Field(default_factory=list)
    collaboration_opportunities: List[Dict[str, Any]] = Field(default_factory=list)
    agent_capabilities: Dict[str, List[str]] = Field(default_factory=dict)
    context_summary: str = ""


class AgentCollaborationOpportunity(BaseModel):
    """Model for agent collaboration opportunities."""
    partner_agent_id: str
    collaboration_type: str
    description: str
    suggested_workflow: str
    confidence_score: float = Field(ge=0.0, le=1.0)
    estimated_benefit: str


class CrossAgentIntelligenceStats(BaseModel):
    """Statistics model for cross-agent intelligence."""
    business_profile_id: str
    total_insights: int = 0
    total_interactions: int = 0
    active_agents: int = 0
    insight_types: Dict[str, int] = Field(default_factory=dict)
    agent_activity: Dict[str, int] = Field(default_factory=dict)
    collaboration_count: int = 0
    average_insight_confidence: float = 0.0
    most_active_agent: Optional[str] = None
    most_valuable_insight_type: Optional[str] = None


class AgentInsightBatch(BaseModel):
    """Model for batch operations on insights."""
    insights: List[AgentInsightCreate]
    business_profile_id: str


class AgentInteractionBatch(BaseModel):
    """Model for batch operations on interactions."""
    interactions: List[AgentInteractionCreate]
    business_profile_id: str


# Search and Filter Models
class InsightSearchRequest(BaseModel):
    """Request model for searching insights."""
    business_profile_id: str
    query: Optional[str] = None
    insight_types: Optional[List[InsightType]] = None
    source_agents: Optional[List[str]] = None
    min_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    tags: Optional[List[str]] = None
    limit: int = Field(default=50, ge=1, le=200)
    offset: int = Field(default=0, ge=0)


class InteractionSearchRequest(BaseModel):
    """Request model for searching interactions."""
    business_profile_id: str
    agent_ids: Optional[List[str]] = None
    outcomes: Optional[List[InteractionOutcome]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    has_insights: Optional[bool] = None
    tools_used: Optional[List[str]] = None
    limit: int = Field(default=50, ge=1, le=200)
    offset: int = Field(default=0, ge=0)


# Analytics Models
class AgentPerformanceMetrics(BaseModel):
    """Performance metrics for an agent."""
    agent_id: str
    business_profile_id: str
    total_interactions: int = 0
    successful_interactions: int = 0
    insights_generated: int = 0
    average_response_quality: float = 0.0
    collaboration_count: int = 0
    tools_usage: Dict[str, int] = Field(default_factory=dict)
    performance_trend: str = "stable"  # improving, stable, declining


class BusinessProfileIntelligenceReport(BaseModel):
    """Comprehensive intelligence report for a business profile."""
    business_profile_id: str
    report_period_days: int
    agent_performance: List[AgentPerformanceMetrics] = Field(default_factory=list)
    top_insights: List[AgentInsightResponse] = Field(default_factory=list)
    collaboration_patterns: Dict[str, Any] = Field(default_factory=dict)
    knowledge_growth: Dict[str, int] = Field(default_factory=dict)
    recommendations: List[str] = Field(default_factory=list)
    generated_at: datetime = Field(default_factory=datetime.utcnow)
