#!/usr/bin/env python3
"""
Installation script for auto-fill feature dependencies.
"""

import subprocess
import sys

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """Install all required packages for auto-fill feature."""
    print("Installing Auto-fill Feature Dependencies...")
    print("=" * 50)
    
    # Required packages for auto-fill feature
    packages = [
        "beautifulsoup4",
        "lxml", 
        "html2text",
        "aiofiles",
        "pypdf",  # Modern replacement for PyPDF2
        "python-docx",
        "aiohttp",
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package in packages:
        print(f"Installing {package}...")
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"Installation Results: {success_count}/{total_count} successful")
    
    if success_count == total_count:
        print("🎉 All dependencies installed successfully!")
        print("You can now start the backend server.")
        return 0
    else:
        print("⚠️  Some installations failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
