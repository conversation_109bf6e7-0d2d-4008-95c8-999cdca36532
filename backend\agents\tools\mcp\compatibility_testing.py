"""
Compatibility Testing Framework for MCP Tools.

This module provides comprehensive compatibility testing to ensure all tools
work correctly with different agent types, data formats, and configuration
combinations with automated regression testing.
"""

import logging
import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import traceback

logger = logging.getLogger(__name__)


class TestResult(Enum):
    """Test result status."""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class TestCategory(Enum):
    """Test categories."""
    AGENT_COMPATIBILITY = "agent_compatibility"
    DATA_FORMAT_COMPATIBILITY = "data_format_compatibility"
    CONFIGURATION_COMPATIBILITY = "configuration_compatibility"
    PERFORMANCE_REGRESSION = "performance_regression"
    INTEGRATION_TEST = "integration_test"
    STRESS_TEST = "stress_test"


@dataclass
class TestCase:
    """Individual test case definition."""
    name: str
    description: str
    category: TestCategory
    tool_name: str
    agent_type: str
    input_data: Dict[str, Any]
    expected_output: Optional[Dict[str, Any]] = None
    timeout_seconds: int = 30
    performance_threshold: Optional[float] = None
    prerequisites: List[str] = None
    
    def __post_init__(self):
        if self.prerequisites is None:
            self.prerequisites = []


@dataclass
class TestExecution:
    """Test execution result."""
    test_case: TestCase
    result: TestResult
    execution_time: float
    output: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    stack_trace: Optional[str] = None
    performance_metrics: Dict[str, float] = None
    timestamp: str = ""
    
    def __post_init__(self):
        if self.performance_metrics is None:
            self.performance_metrics = {}
        if not self.timestamp:
            self.timestamp = time.strftime("%Y-%m-%d %H:%M:%S")


@dataclass
class TestSuite:
    """Collection of test cases."""
    name: str
    description: str
    test_cases: List[TestCase]
    setup_function: Optional[Callable] = None
    teardown_function: Optional[Callable] = None


class CompatibilityTestFramework:
    """
    Comprehensive compatibility testing framework for MCP tools.
    
    Features:
    - Agent compatibility testing
    - Data format compatibility testing
    - Configuration compatibility testing
    - Performance regression testing
    - Automated test discovery
    - Detailed reporting and analytics
    - Continuous integration support
    """
    
    def __init__(self, tools_registry: Dict[str, Any] = None):
        """Initialize the compatibility testing framework."""
        self.tools_registry = tools_registry or {}
        self.test_suites = {}
        self.test_results = []
        self.agent_types = ["analyst", "marketer", "concierge", "composable_analyst"]
        self.data_formats = ["csv", "json", "xlsx", "txt", "pdf"]
        self.performance_baselines = {}
        
        # Validation data for testing
        self.validation_data = self._create_validation_data()

    def _create_validation_data(self) -> Dict[str, Any]:
        """Create structured validation data for testing tool compatibility."""
        import tempfile
        import os

        # Create temporary files for testing
        temp_dir = tempfile.mkdtemp(prefix="mcp_validation_")

        try:
            # Create a temporary CSV file
            csv_path = os.path.join(temp_dir, "validation_data.csv")
            with open(csv_path, 'w', encoding='utf-8') as f:
                f.write("id,name,value\n1,Test Item 1,100\n2,Test Item 2,200\n")

            return {
                "text_data": "Validation text for testing text processing capabilities.",
                "csv_file_path": csv_path,
                "json_data": {"validation": True, "test_id": 1},
                "numerical_data": [1, 2, 3, 4, 5],
                "query": "SELECT id, name FROM validation_table LIMIT 5",
                "analysis_description": "Validate tool compatibility and data processing",
                "temp_dir": temp_dir  # Store for cleanup
            }
        except Exception as e:
            # Clean up on error
            import shutil
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            raise RuntimeError(f"Error creating validation data: {e}")

    def discover_test_cases(self) -> List[TestCase]:
        """Automatically discover test cases based on available tools."""
        test_cases = []
        
        for tool_name, tool_info in self.tools_registry.items():
            # Generate agent compatibility tests
            for agent_type in self.agent_types:
                test_case = self._create_agent_compatibility_test(tool_name, tool_info, agent_type)
                if test_case:
                    test_cases.append(test_case)
            
            # Generate data format compatibility tests
            test_cases.extend(self._create_data_format_tests(tool_name, tool_info))
            
            # Generate configuration compatibility tests
            test_cases.extend(self._create_configuration_tests(tool_name, tool_info))
            
            # Generate performance regression tests
            performance_test = self._create_performance_test(tool_name, tool_info)
            if performance_test:
                test_cases.append(performance_test)
        
        return test_cases

    def _create_agent_compatibility_test(self, tool_name: str, tool_info: Dict[str, Any], 
                                       agent_type: str) -> Optional[TestCase]:
        """Create agent compatibility test case."""
        input_schema = tool_info.get('input_schema', {})
        if not input_schema or 'properties' not in input_schema:
            return None
        
        # Generate test input based on schema
        test_input = self._generate_test_input(input_schema)
        
        return TestCase(
            name=f"{tool_name}_agent_compatibility_{agent_type}",
            description=f"Test {tool_name} compatibility with {agent_type} agent",
            category=TestCategory.AGENT_COMPATIBILITY,
            tool_name=tool_name,
            agent_type=agent_type,
            input_data=test_input,
            timeout_seconds=30
        )

    def _create_data_format_tests(self, tool_name: str, tool_info: Dict[str, Any]) -> List[TestCase]:
        """Create data format compatibility tests."""
        test_cases = []
        input_schema = tool_info.get('input_schema', {})
        
        if not input_schema or 'properties' not in input_schema:
            return test_cases
        
        # Check if tool accepts file paths or data
        properties = input_schema['properties']
        
        for prop_name, prop_schema in properties.items():
            if 'file' in prop_name.lower() or 'path' in prop_name.lower():
                # Test different file formats
                for data_format in self.data_formats:
                    test_input = self._generate_test_input(input_schema)
                    test_input[prop_name] = f"/tmp/test_file.{data_format}"
                    
                    test_case = TestCase(
                        name=f"{tool_name}_data_format_{data_format}",
                        description=f"Test {tool_name} with {data_format} format",
                        category=TestCategory.DATA_FORMAT_COMPATIBILITY,
                        tool_name=tool_name,
                        agent_type="analyst",  # Default agent
                        input_data=test_input,
                        timeout_seconds=45
                    )
                    test_cases.append(test_case)
        
        return test_cases

    def _create_configuration_tests(self, tool_name: str, tool_info: Dict[str, Any]) -> List[TestCase]:
        """Create configuration compatibility tests."""
        test_cases = []
        input_schema = tool_info.get('input_schema', {})
        
        if not input_schema or 'properties' not in input_schema:
            return test_cases
        
        # Test with minimal required parameters
        required_params = input_schema.get('required', [])
        if required_params:
            minimal_input = {}
            for param in required_params:
                if param in input_schema['properties']:
                    minimal_input[param] = self._generate_example_value(input_schema['properties'][param])
            
            test_case = TestCase(
                name=f"{tool_name}_minimal_config",
                description=f"Test {tool_name} with minimal configuration",
                category=TestCategory.CONFIGURATION_COMPATIBILITY,
                tool_name=tool_name,
                agent_type="analyst",
                input_data=minimal_input,
                timeout_seconds=30
            )
            test_cases.append(test_case)
        
        # Test with maximum parameters
        full_input = self._generate_test_input(input_schema)
        test_case = TestCase(
            name=f"{tool_name}_full_config",
            description=f"Test {tool_name} with full configuration",
            category=TestCategory.CONFIGURATION_COMPATIBILITY,
            tool_name=tool_name,
            agent_type="analyst",
            input_data=full_input,
            timeout_seconds=60
        )
        test_cases.append(test_case)
        
        return test_cases

    def _create_performance_test(self, tool_name: str, tool_info: Dict[str, Any]) -> Optional[TestCase]:
        """Create performance regression test."""
        input_schema = tool_info.get('input_schema', {})
        if not input_schema:
            return None
        
        test_input = self._generate_test_input(input_schema)
        
        # Set performance threshold based on tool type
        threshold = self._get_performance_threshold(tool_name)
        
        return TestCase(
            name=f"{tool_name}_performance_regression",
            description=f"Test {tool_name} performance regression",
            category=TestCategory.PERFORMANCE_REGRESSION,
            tool_name=tool_name,
            agent_type="analyst",
            input_data=test_input,
            timeout_seconds=120,
            performance_threshold=threshold
        )

    def _generate_test_input(self, input_schema: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test input based on schema."""
        test_input = {}
        
        if 'properties' not in input_schema:
            return test_input
        
        for prop_name, prop_schema in input_schema['properties'].items():
            test_input[prop_name] = self._generate_example_value(prop_schema)
        
        return test_input

    def _generate_example_value(self, schema: Dict[str, Any]) -> Any:
        """Generate example value based on schema."""
        schema_type = schema.get('type', 'string')
        
        # Use validation data if available
        if 'file' in schema.get('description', '').lower():
            return self.validation_data.get('csv_file_path', '/tmp/validation.csv')
        elif 'text' in schema.get('description', '').lower():
            return self.validation_data.get('text_data', 'Validation text')
        elif 'query' in schema.get('description', '').lower():
            return self.validation_data.get('query', 'SELECT * FROM validation_table')
        elif 'analysis' in schema.get('description', '').lower():
            return self.validation_data.get('analysis_description', 'Validation analysis')
        
        # Generate based on type
        if schema_type == 'string':
            if 'enum' in schema:
                return schema['enum'][0]
            return "test_string"
        elif schema_type == 'integer':
            return 42
        elif schema_type == 'number':
            return 3.14
        elif schema_type == 'boolean':
            return True
        elif schema_type == 'array':
            return ["test_item"]
        elif schema_type == 'object':
            return {"key": "value"}
        
        return "test_value"

    def _get_performance_threshold(self, tool_name: str) -> float:
        """Get performance threshold for tool."""
        # Default thresholds based on tool type
        tool_name_lower = tool_name.lower()
        
        if any(keyword in tool_name_lower for keyword in ['ml', 'machine', 'learning', 'statistical']):
            return 10.0  # 10 seconds for ML tools
        elif any(keyword in tool_name_lower for keyword in ['data', 'access', 'query']):
            return 5.0   # 5 seconds for data access
        elif any(keyword in tool_name_lower for keyword in ['text', 'processing']):
            return 2.0   # 2 seconds for text processing
        else:
            return 3.0   # 3 seconds default

    async def run_test_case(self, test_case: TestCase) -> TestExecution:
        """Execute a single test case."""
        start_time = time.time()
        
        try:
            # Get the tool instance
            tool_instance = await self._get_tool_instance(test_case.tool_name)
            if not tool_instance:
                return TestExecution(
                    test_case=test_case,
                    result=TestResult.ERROR,
                    execution_time=0,
                    error_message=f"Tool {test_case.tool_name} not found"
                )
            
            # Execute the tool with timeout
            try:
                output = await asyncio.wait_for(
                    self._execute_tool(tool_instance, test_case.input_data, test_case.agent_type),
                    timeout=test_case.timeout_seconds
                )
                
                execution_time = time.time() - start_time
                
                # Check performance threshold
                if test_case.performance_threshold and execution_time > test_case.performance_threshold:
                    return TestExecution(
                        test_case=test_case,
                        result=TestResult.FAILED,
                        execution_time=execution_time,
                        output=output,
                        error_message=f"Performance threshold exceeded: {execution_time:.2f}s > {test_case.performance_threshold}s",
                        performance_metrics={"execution_time": execution_time}
                    )
                
                # Validate output
                if self._validate_output(output, test_case):
                    return TestExecution(
                        test_case=test_case,
                        result=TestResult.PASSED,
                        execution_time=execution_time,
                        output=output,
                        performance_metrics={"execution_time": execution_time}
                    )
                else:
                    return TestExecution(
                        test_case=test_case,
                        result=TestResult.FAILED,
                        execution_time=execution_time,
                        output=output,
                        error_message="Output validation failed"
                    )
                    
            except asyncio.TimeoutError:
                return TestExecution(
                    test_case=test_case,
                    result=TestResult.FAILED,
                    execution_time=test_case.timeout_seconds,
                    error_message=f"Test timed out after {test_case.timeout_seconds} seconds"
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            return TestExecution(
                test_case=test_case,
                result=TestResult.ERROR,
                execution_time=execution_time,
                error_message=str(e),
                stack_trace=traceback.format_exc()
            )

    async def _get_tool_instance(self, tool_name: str):
        """Get tool instance for testing."""
        # This would need to be implemented based on your tool loading mechanism
        # For now, return a mock tool
        return MockTool(tool_name)

    async def _execute_tool(self, tool_instance, input_data: Dict[str, Any], agent_type: str) -> Dict[str, Any]:
        """Execute tool with given input and agent context."""
        # Add agent context to input
        enhanced_input = {
            **input_data,
            "agent_identity": agent_type
        }
        
        # Execute the tool
        if hasattr(tool_instance, 'execute'):
            result = await tool_instance.execute(enhanced_input)
        else:
            result = {"status": "success", "message": "Mock execution"}
        
        return result

    def _validate_output(self, output: Dict[str, Any], test_case: TestCase) -> bool:
        """Validate tool output."""
        if not output:
            return False
        
        # Basic validation - check for error indicators
        if output.get('isError', False):
            return False
        
        # Check for required fields based on test category
        if test_case.category == TestCategory.AGENT_COMPATIBILITY:
            # Agent compatibility tests should return formatted results
            return 'content' in output or 'result' in output
        
        # Default validation - any non-error output is considered valid
        return True


class MockTool:
    """Mock tool for testing purposes."""
    
    def __init__(self, name: str):
        self.name = name
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Mock execute method."""
        await asyncio.sleep(0.1)  # Simulate processing time
        
        return {
            "status": "success",
            "tool": self.name,
            "input_received": input_data,
            "result": "Mock execution completed successfully"
        }


class TestRunner:
    """Test runner for executing test suites."""

    def __init__(self, framework: CompatibilityTestFramework):
        self.framework = framework
        self.results = []

    async def run_test_suite(self, test_suite: TestSuite) -> List[TestExecution]:
        """Run all tests in a test suite."""
        results = []

        # Run setup if provided
        if test_suite.setup_function:
            try:
                await test_suite.setup_function()
            except Exception as e:
                logger.error(f"Setup failed for test suite {test_suite.name}: {e}")
                return results

        # Run all test cases
        for test_case in test_suite.test_cases:
            logger.info(f"Running test: {test_case.name}")
            result = await self.framework.run_test_case(test_case)
            results.append(result)
            self.results.append(result)

        # Run teardown if provided
        if test_suite.teardown_function:
            try:
                await test_suite.teardown_function()
            except Exception as e:
                logger.warning(f"Teardown failed for test suite {test_suite.name}: {e}")

        return results

    async def run_all_tests(self, test_cases: List[TestCase]) -> List[TestExecution]:
        """Run all provided test cases."""
        results = []

        for test_case in test_cases:
            logger.info(f"Running test: {test_case.name}")
            result = await self.framework.run_test_case(test_case)
            results.append(result)
            self.results.append(result)

        return results

    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        if not self.results:
            return {"error": "No test results available"}

        # Calculate summary statistics
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.result == TestResult.PASSED)
        failed_tests = sum(1 for r in self.results if r.result == TestResult.FAILED)
        error_tests = sum(1 for r in self.results if r.result == TestResult.ERROR)
        skipped_tests = sum(1 for r in self.results if r.result == TestResult.SKIPPED)

        # Calculate performance metrics
        execution_times = [r.execution_time for r in self.results if r.execution_time > 0]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        max_execution_time = max(execution_times) if execution_times else 0

        # Group results by category
        results_by_category = {}
        for result in self.results:
            category = result.test_case.category.value
            if category not in results_by_category:
                results_by_category[category] = []
            results_by_category[category].append(result)

        # Group results by tool
        results_by_tool = {}
        for result in self.results:
            tool = result.test_case.tool_name
            if tool not in results_by_tool:
                results_by_tool[tool] = []
            results_by_tool[tool].append(result)

        # Group results by agent
        results_by_agent = {}
        for result in self.results:
            agent = result.test_case.agent_type
            if agent not in results_by_agent:
                results_by_agent[agent] = []
            results_by_agent[agent].append(result)

        report = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "errors": error_tests,
                "skipped": skipped_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                "avg_execution_time": avg_execution_time,
                "max_execution_time": max_execution_time
            },
            "category_breakdown": {
                category: {
                    "total": len(results),
                    "passed": sum(1 for r in results if r.result == TestResult.PASSED),
                    "failed": sum(1 for r in results if r.result == TestResult.FAILED),
                    "errors": sum(1 for r in results if r.result == TestResult.ERROR)
                }
                for category, results in results_by_category.items()
            },
            "tool_breakdown": {
                tool: {
                    "total": len(results),
                    "passed": sum(1 for r in results if r.result == TestResult.PASSED),
                    "failed": sum(1 for r in results if r.result == TestResult.FAILED),
                    "avg_time": sum(r.execution_time for r in results) / len(results)
                }
                for tool, results in results_by_tool.items()
            },
            "agent_breakdown": {
                agent: {
                    "total": len(results),
                    "passed": sum(1 for r in results if r.result == TestResult.PASSED),
                    "failed": sum(1 for r in results if r.result == TestResult.FAILED)
                }
                for agent, results in results_by_agent.items()
            },
            "failed_tests": [
                {
                    "name": r.test_case.name,
                    "tool": r.test_case.tool_name,
                    "agent": r.test_case.agent_type,
                    "category": r.test_case.category.value,
                    "error": r.error_message,
                    "execution_time": r.execution_time
                }
                for r in self.results if r.result in [TestResult.FAILED, TestResult.ERROR]
            ],
            "performance_issues": [
                {
                    "name": r.test_case.name,
                    "tool": r.test_case.tool_name,
                    "execution_time": r.execution_time,
                    "threshold": r.test_case.performance_threshold
                }
                for r in self.results
                if r.test_case.performance_threshold and r.execution_time > r.test_case.performance_threshold
            ]
        }

        return report

    def save_report(self, report: Dict[str, Any], output_path: str = "test_report.json"):
        """Save test report to file."""
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        logger.info(f"Test report saved to {output_path}")

    def print_summary(self):
        """Print test summary to console."""
        if not self.results:
            print("No test results available")
            return

        report = self.generate_report()
        summary = report["summary"]

        print("\n" + "="*60)
        print("COMPATIBILITY TEST RESULTS SUMMARY")
        print("="*60)
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed']} ({summary['success_rate']:.1f}%)")
        print(f"Failed: {summary['failed']}")
        print(f"Errors: {summary['errors']}")
        print(f"Skipped: {summary['skipped']}")
        print(f"Average Execution Time: {summary['avg_execution_time']:.2f}s")
        print(f"Max Execution Time: {summary['max_execution_time']:.2f}s")

        # Print failed tests
        if report["failed_tests"]:
            print("\nFAILED TESTS:")
            print("-" * 40)
            for failed_test in report["failed_tests"][:10]:  # Show first 10
                print(f"• {failed_test['name']} ({failed_test['tool']}) - {failed_test['error']}")

        # Print performance issues
        if report["performance_issues"]:
            print("\nPERFORMANCE ISSUES:")
            print("-" * 40)
            for perf_issue in report["performance_issues"][:5]:  # Show first 5
                print(f"• {perf_issue['name']} - {perf_issue['execution_time']:.2f}s (threshold: {perf_issue['threshold']}s)")

        print("="*60)


class ContinuousIntegrationRunner:
    """CI/CD integration for automated testing."""

    def __init__(self, framework: CompatibilityTestFramework):
        self.framework = framework
        self.baseline_file = "test_baselines.json"

    def load_baselines(self) -> Dict[str, Any]:
        """Load performance baselines from file."""
        try:
            with open(self.baseline_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}

    def save_baselines(self, baselines: Dict[str, Any]):
        """Save performance baselines to file."""
        with open(self.baseline_file, 'w') as f:
            json.dump(baselines, f, indent=2)

    async def run_regression_tests(self) -> bool:
        """Run regression tests and compare with baselines."""
        # Discover and run tests
        test_cases = self.framework.discover_test_cases()
        runner = TestRunner(self.framework)
        results = await runner.run_all_tests(test_cases)

        # Load baselines
        baselines = self.load_baselines()

        # Check for regressions
        regressions = []
        new_baselines = {}

        for result in results:
            test_name = result.test_case.name
            current_time = result.execution_time

            if test_name in baselines:
                baseline_time = baselines[test_name]
                # Consider it a regression if 50% slower than baseline
                if current_time > baseline_time * 1.5:
                    regressions.append({
                        "test": test_name,
                        "baseline": baseline_time,
                        "current": current_time,
                        "regression": ((current_time - baseline_time) / baseline_time) * 100
                    })

            # Update baseline with current time (for successful tests)
            if result.result == TestResult.PASSED:
                new_baselines[test_name] = current_time

        # Save updated baselines
        self.save_baselines(new_baselines)

        # Generate CI report
        ci_report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_tests": len(results),
            "passed": sum(1 for r in results if r.result == TestResult.PASSED),
            "failed": sum(1 for r in results if r.result == TestResult.FAILED),
            "regressions": regressions,
            "success": len(regressions) == 0 and all(r.result == TestResult.PASSED for r in results)
        }

        # Save CI report
        with open("ci_test_report.json", 'w') as f:
            json.dump(ci_report, f, indent=2)

        # Print results
        if ci_report["success"]:
            print("✅ All tests passed - No regressions detected")
        else:
            print("❌ Test failures or regressions detected")
            if regressions:
                print(f"Performance regressions: {len(regressions)}")
                for reg in regressions:
                    print(f"  • {reg['test']}: {reg['regression']:.1f}% slower")

        return ci_report["success"]
