"""
Shared LLM Processor Component for Phase 2 Architecture Consolidation.

This component consolidates LLM processing logic that was previously duplicated
across different agents, providing a unified interface for LLM interactions
with fallback chains and provider management.
"""

import logging
from typing import Dict, Any, List, Optional
from ..base_component import BaseAgentComponent, AgentContext
from ...utils.production_provider_manager import ProductionProviderManager
from ...utils.agent_identity import detect_agent_identity

logger = logging.getLogger(__name__)


class LLMProviderFactory:
    """Factory for creating LLM provider instances."""
    
    def __init__(self):
        self.provider_manager = ProductionProviderManager()
        self._providers = {}
    
    async def get_provider(self, provider_name: str):
        """Get or create a provider instance."""
        if provider_name not in self._providers:
            self._providers[provider_name] = await self.provider_manager.get_provider(provider_name)
        return self._providers[provider_name]


class SharedLLMProcessor(BaseAgentComponent):
    """
    Shared LLM processor component that consolidates LLM interaction logic
    across all agents with standardized fallback chains and error handling.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("shared_llm_processor", config)
        self.provider_factory = LLMProviderFactory()
        self.fallback_chain = config.get("fallback_providers", ["groq", "openai", "google"])
        self.default_model_map = config.get("default_models", {
            "groq": "llama3-70b-8192",
            "openai": "gpt-4",
            "google": "gemini-pro"
        })
        self.default_temperature = config.get("default_temperature", 0.7)
        self.max_tokens = config.get("max_tokens", 4000)
    
    async def _initialize_component(self) -> None:
        """Initialize the LLM processor component."""
        self.logger.info("Initializing SharedLLMProcessor")
        # Pre-warm provider connections if needed
        try:
            for provider in self.fallback_chain[:1]:  # Just the first provider
                await self.provider_factory.get_provider(provider)
                self.logger.info(f"Pre-warmed provider: {provider}")
        except Exception as e:
            self.logger.warning(f"Failed to pre-warm providers: {e}")
    
    def get_required_fields(self) -> List[str]:
        """Return list of required context fields."""
        return ["prompt"]
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process LLM request with fallback chain support.
        
        Args:
            context: AgentContext containing prompt and optional parameters
            
        Returns:
            Updated AgentContext with LLM response
        """
        prompt = context.get_field("prompt")
        agent_identity = context.get_field("agent_identity", {})
        user_id = context.user_id
        
        # Detect agent identity if not provided
        if not agent_identity:
            try:
                agent_identity = await detect_agent_identity(
                    context.get_field("persona_id", ""),
                    context.get_field("agent_id", ""),
                    context.get_field("context", {})
                )
                context.set_field("agent_identity", agent_identity)
                self.logger.info(f"Detected agent identity: {agent_identity}")
            except Exception as e:
                self.logger.warning(f"Failed to detect agent identity: {e}")
                agent_identity = {"name": "assistant", "role": "helpful assistant"}
        
        # Get provider preferences
        preferred_provider = context.get_field("provider")
        preferred_model = context.get_field("model")
        temperature = context.get_field("temperature", self.default_temperature)
        max_tokens = context.get_field("max_tokens", self.max_tokens)
        
        # Build fallback chain with preferred provider first
        providers_to_try = []
        if preferred_provider and preferred_provider in self.fallback_chain:
            providers_to_try.append(preferred_provider)
            providers_to_try.extend([p for p in self.fallback_chain if p != preferred_provider])
        else:
            providers_to_try = self.fallback_chain.copy()
        
        # Try each provider in the fallback chain
        last_error = None
        for provider in providers_to_try:
            try:
                self.logger.info(f"Attempting LLM request with provider: {provider}")
                
                # Get provider instance
                llm_provider = await self.provider_factory.get_provider(provider)
                
                # Determine model to use
                model = preferred_model or self.default_model_map.get(provider)
                
                # Prepare request parameters
                request_params = {
                    "prompt": prompt,
                    "agent_identity": agent_identity,
                    "user_id": user_id,
                    "temperature": temperature,
                    "max_tokens": max_tokens
                }
                
                if model:
                    request_params["model"] = model
                
                # Make the request
                response = await self._make_llm_request(llm_provider, request_params)
                
                # Success - update context and return
                context.set_field("llm_response", response)
                context.set_field("provider_used", provider)
                context.set_field("model_used", model)
                context.set_status("success")
                
                self.metrics.record_success(provider)
                self.logger.info(f"Successfully generated response using {provider}")
                
                return context
                
            except Exception as e:
                last_error = e
                self.logger.warning(f"Provider {provider} failed: {str(e)}")
                self.metrics.record_provider_failure(provider)
                continue
        
        # All providers failed
        error_message = f"All LLM providers failed. Last error: {str(last_error)}"
        context.set_status("error")
        context.add_error(self.name, error_message)
        self.logger.error(error_message)
        
        return context
    
    async def _make_llm_request(self, provider, params: Dict[str, Any]) -> str:
        """
        Make an LLM request to the specified provider.
        
        Args:
            provider: LLM provider instance
            params: Request parameters
            
        Returns:
            Generated response text
        """
        # This is a simplified interface - actual implementation would depend
        # on the specific provider interface
        if hasattr(provider, 'generate_response'):
            return await provider.generate_response(
                params["prompt"],
                agent_identity=params.get("agent_identity"),
                user_id=params.get("user_id"),
                temperature=params.get("temperature", 0.7),
                max_tokens=params.get("max_tokens", 4000),
                model=params.get("model")
            )
        elif hasattr(provider, 'chat_completion'):
            return await provider.chat_completion(
                messages=[{"role": "user", "content": params["prompt"]}],
                temperature=params.get("temperature", 0.7),
                max_tokens=params.get("max_tokens", 4000),
                model=params.get("model")
            )
        else:
            raise ValueError(f"Provider does not support expected interface")
    
    async def generate_streaming_response(self, context: AgentContext) -> AgentContext:
        """
        Generate a streaming LLM response.
        
        Args:
            context: AgentContext containing prompt and streaming parameters
            
        Returns:
            Updated AgentContext with streaming response setup
        """
        # This would implement streaming functionality
        # For now, fall back to regular processing
        self.logger.info("Streaming requested, falling back to regular processing")
        return await self.process(context)
    
    def get_supported_providers(self) -> List[str]:
        """Get list of supported providers."""
        return self.fallback_chain.copy()
    
    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all providers."""
        status = {}
        for provider in self.fallback_chain:
            provider_metrics = {
                "successes": self.metrics.provider_successes.get(provider, 0),
                "failures": self.metrics.provider_failures.get(provider, 0)
            }
            total = provider_metrics["successes"] + provider_metrics["failures"]
            provider_metrics["success_rate"] = (
                provider_metrics["successes"] / total if total > 0 else 0.0
            )
            status[provider] = provider_metrics
        return status
