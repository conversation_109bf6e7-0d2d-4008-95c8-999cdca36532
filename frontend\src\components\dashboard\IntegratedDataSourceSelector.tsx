import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Plus, 
  Upload, 
  Database, 
  FileText, 
  Globe, 
  Server,
  Search,
  Filter,
  RefreshCw,
  CheckCircle2,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { SystemDataSource, DashboardDataSourceAssignmentCreate } from '@/types/dashboard-customization';
import { dataSourceApi } from '@/lib/dataSourceApi';
import { fileApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface IntegratedDataSourceSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDataSourceSelected: (assignment: DashboardDataSourceAssignmentCreate) => void;
  onFileUploaded?: (file: any) => void;
  existingAssignments?: string[]; // IDs of already assigned data sources
}

const DATA_SOURCE_ICONS = {
  file: FileText,
  database: Database,
  api: Globe,
  mcp: Server,
};

const DATA_SOURCE_TYPE_LABELS = {
  file: 'File Upload',
  database: 'Database',
  api: 'API',
  mcp: 'MCP Server',
};

export const IntegratedDataSourceSelector: React.FC<IntegratedDataSourceSelectorProps> = ({
  open,
  onOpenChange,
  onDataSourceSelected,
  onFileUploaded,
  existingAssignments = [],
}) => {
  const { toast } = useToast();
  
  // State for existing data sources
  const [systemDataSources, setSystemDataSources] = useState<SystemDataSource[]>([]);
  const [filteredDataSources, setFilteredDataSources] = useState<SystemDataSource[]>([]);
  const [isLoadingDataSources, setIsLoadingDataSources] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  
  // State for file upload
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  
  // State for selection
  const [selectedDataSource, setSelectedDataSource] = useState<SystemDataSource | null>(null);
  const [alias, setAlias] = useState('');

  // Load system data sources
  const loadSystemDataSources = useCallback(async () => {
    setIsLoadingDataSources(true);
    try {
      const response = await dataSourceApi.getDataSources();
      setSystemDataSources(response.data_sources || []);
    } catch (error) {
      console.error('Error loading data sources:', error);
      toast({
        title: "Error",
        description: "Failed to load data sources. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingDataSources(false);
    }
  }, [toast]);

  // Filter data sources based on search and type
  useEffect(() => {
    let filtered = systemDataSources.filter(ds => ds.is_active);
    
    // Exclude already assigned data sources
    filtered = filtered.filter(ds => !existingAssignments.includes(ds.id));
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(ds => 
        ds.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ds.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(ds => ds.type === typeFilter);
    }
    
    setFilteredDataSources(filtered);
  }, [systemDataSources, searchTerm, typeFilter, existingAssignments]);

  // Load data sources when dialog opens
  useEffect(() => {
    if (open) {
      loadSystemDataSources();
      setSearchTerm('');
      setTypeFilter('all');
      setSelectedDataSource(null);
      setAlias('');
    }
  }, [open, loadSystemDataSources]);

  // Handle file upload
  const handleFileUpload = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 300);

      // Upload the file
      const uploadedFile = await fileApi.uploadFile(file);
      
      // Clear progress interval and set to 100%
      clearInterval(progressInterval);
      setUploadProgress(100);

      // Trigger callback if provided
      if (onFileUploaded) {
        onFileUploaded(uploadedFile);
      }

      // Create a file data source
      const fileDataSource = await dataSourceApi.createFileDataSource({
        name: file.name,
        type: 'file',
        description: `Uploaded file: ${file.name}`,
        file_id: uploadedFile.id,
      });

      // Reload data sources to include the new one
      await loadSystemDataSources();

      // Auto-select the newly created data source
      setSelectedDataSource(fileDataSource);
      setAlias(fileDataSource.name);

      toast({
        title: "File Uploaded",
        description: `${file.name} has been uploaded and processed successfully.`,
      });

    } catch (error) {
      console.error('Error uploading file:', error);
      toast({
        title: "Upload Failed",
        description: "Failed to upload file. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // Handle drag and drop
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files[0]);
    }
  };

  // Handle data source selection
  const handleSelectDataSource = () => {
    if (!selectedDataSource) return;

    const assignment: DashboardDataSourceAssignmentCreate = {
      system_data_source_id: selectedDataSource.id,
      alias: alias.trim() || undefined,
      is_active: true,
    };

    onDataSourceSelected(assignment);
    onOpenChange(false);
  };

  const formatDataSourceType = (type: string) => {
    return DATA_SOURCE_TYPE_LABELS[type as keyof typeof DATA_SOURCE_TYPE_LABELS] || type;
  };

  const getDataSourceIcon = (type: string) => {
    return DATA_SOURCE_ICONS[type as keyof typeof DATA_SOURCE_ICONS] || Database;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Add Data Source to Dashboard</DialogTitle>
          <DialogDescription>
            Select an existing data source or upload a new file to add to your dashboard.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="existing" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="existing">Existing Data Sources</TabsTrigger>
            <TabsTrigger value="upload">Upload New File</TabsTrigger>
          </TabsList>

          {/* Existing Data Sources Tab */}
          <TabsContent value="existing" className="space-y-4">
            {/* Search and Filter */}
            <div className="flex items-center space-x-2">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search data sources..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="file">Files</SelectItem>
                  <SelectItem value="database">Databases</SelectItem>
                  <SelectItem value="api">APIs</SelectItem>
                  <SelectItem value="mcp">MCP Servers</SelectItem>
                </SelectContent>
              </Select>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={loadSystemDataSources}
                disabled={isLoadingDataSources}
              >
                <RefreshCw className={`h-4 w-4 ${isLoadingDataSources ? 'animate-spin' : ''}`} />
              </Button>
            </div>

            {/* Data Sources List */}
            <div className="space-y-2 max-h-[400px] overflow-y-auto">
              {isLoadingDataSources ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading data sources...</span>
                </div>
              ) : filteredDataSources.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-8">
                    <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                    <h4 className="text-lg font-semibold mb-2">No Data Sources Available</h4>
                    <p className="text-sm text-muted-foreground text-center">
                      {systemDataSources.length === 0 
                        ? "No data sources have been configured yet. Upload a file or configure a data source on the Data Integration page."
                        : "All available data sources are already assigned to this dashboard."
                      }
                    </p>
                  </CardContent>
                </Card>
              ) : (
                filteredDataSources.map((dataSource) => {
                  const Icon = getDataSourceIcon(dataSource.type);
                  const isSelected = selectedDataSource?.id === dataSource.id;
                  
                  return (
                    <Card 
                      key={dataSource.id} 
                      className={`cursor-pointer transition-colors ${
                        isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
                      }`}
                      onClick={() => {
                        setSelectedDataSource(dataSource);
                        setAlias(dataSource.name);
                      }}
                    >
                      <CardContent className="flex items-center justify-between p-4">
                        <div className="flex items-center space-x-3">
                          <Icon className="h-8 w-8 text-muted-foreground" />
                          <div>
                            <div className="flex items-center space-x-2">
                              <h4 className="font-semibold">{dataSource.name}</h4>
                              <Badge variant="outline">
                                {formatDataSourceType(dataSource.type)}
                              </Badge>
                            </div>
                            {dataSource.description && (
                              <p className="text-sm text-muted-foreground">{dataSource.description}</p>
                            )}
                            <div className="text-xs text-muted-foreground mt-1">
                              Created {new Date(dataSource.created_at).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        {isSelected && (
                          <CheckCircle2 className="h-5 w-5 text-primary" />
                        )}
                      </CardContent>
                    </Card>
                  );
                })
              )}
            </div>

            {/* Alias Input */}
            {selectedDataSource && (
              <div className="space-y-2">
                <Label htmlFor="alias">Alias (optional)</Label>
                <Input
                  id="alias"
                  value={alias}
                  onChange={(e) => setAlias(e.target.value)}
                  placeholder="Enter a custom name for this data source in your dashboard"
                />
                <p className="text-xs text-muted-foreground">
                  You can give this data source a custom name within your dashboard
                </p>
              </div>
            )}
          </TabsContent>

          {/* File Upload Tab */}
          <TabsContent value="upload" className="space-y-4">
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
              } ${isUploading ? 'pointer-events-none opacity-50' : ''}`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              {isUploading ? (
                <div className="space-y-4">
                  <Loader2 className="h-12 w-12 animate-spin mx-auto text-primary" />
                  <div>
                    <h4 className="text-lg font-semibold">Uploading File...</h4>
                    <p className="text-sm text-muted-foreground">Processing and creating data source</p>
                  </div>
                  <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
                </div>
              ) : (
                <div className="space-y-4">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div>
                    <h4 className="text-lg font-semibold">Upload Data File</h4>
                    <p className="text-sm text-muted-foreground">
                      Drag and drop a file here, or click to browse
                    </p>
                    <p className="text-xs text-muted-foreground mt-2">
                      Supports CSV, Excel, PDF, DOC, DOCX files
                    </p>
                  </div>
                  <input
                    type="file"
                    id="file-upload"
                    className="hidden"
                    accept=".csv,.xlsx,.xls,.pdf,.doc,.docx"
                    onChange={handleFileInputChange}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => document.getElementById('file-upload')?.click()}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Choose File
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSelectDataSource}
            disabled={!selectedDataSource || isUploading}
          >
            Add to Dashboard
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
