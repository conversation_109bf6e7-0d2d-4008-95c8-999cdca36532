import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { DashboardLayout } from '../DashboardLayout';
import { LeftNavbar } from '../LeftNavbar';
import { useNavbarStore } from '@/stores/navbar-store';

// Mock the navbar store
jest.mock('@/stores/navbar-store');
const mockUseNavbarStore = useNavbarStore as jest.MockedFunction<typeof useNavbarStore>;

// Mock other dependencies
jest.mock('@/stores/dashboard-mode-store', () => ({
  useDashboardMode: () => ({ current_mode: 'advanced' })
}));

jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({ user: null })
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, className, style, ...props }: any) => (
      <div className={className} style={style} {...props}>
        {children}
      </div>
    ),
    header: ({ children, className, style, ...props }: any) => (
      <header className={className} style={style} {...props}>
        {children}
      </header>
    )
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe('Layout Positioning', () => {
  beforeEach(() => {
    // Reset mock before each test
    mockUseNavbarStore.mockReset();
  });

  it('should position header behind navbar with correct z-index', () => {
    mockUseNavbarStore.mockReturnValue({
      isExpanded: true,
      isLocked: false,
      isHovered: false,
      setExpanded: jest.fn(),
      setLocked: jest.fn(),
      setHovered: jest.fn(),
      toggleExpanded: jest.fn(),
      toggleLocked: jest.fn(),
      shouldShowExpanded: () => true,
      getNavbarWidth: () => 240,
    });

    render(
      <TestWrapper>
        <div className="relative">
          <LeftNavbar />
          <DashboardLayout title="Test Dashboard">
            <div>Test Content</div>
          </DashboardLayout>
        </div>
      </TestWrapper>
    );

    // Check that navbar has higher z-index than header
    const navbar = document.querySelector('.z-40');
    const header = document.querySelector('.z-20');
    
    expect(navbar).toBeInTheDocument();
    expect(header).toBeInTheDocument();
  });

  it('should position content with correct margin for expanded navbar', () => {
    const mockStore = {
      isExpanded: true,
      isLocked: false,
      isHovered: false,
      setExpanded: jest.fn(),
      setLocked: jest.fn(),
      setHovered: jest.fn(),
      toggleExpanded: jest.fn(),
      toggleLocked: jest.fn(),
      shouldShowExpanded: () => true,
      getNavbarWidth: () => 240,
    };

    mockUseNavbarStore.mockReturnValue(mockStore);

    render(
      <TestWrapper>
        <DashboardLayout title="Test Dashboard">
          <div>Test Content</div>
        </DashboardLayout>
      </TestWrapper>
    );

    // Check that main content has correct margin for expanded navbar
    const mainContent = document.querySelector('.main-content-with-navbar');
    expect(mainContent).toHaveClass('main-content-with-navbar');
    expect(mainContent).toHaveClass('navbar-expanded');
  });

  it('should position content with correct margin for collapsed navbar', () => {
    const mockStore = {
      isExpanded: false,
      isLocked: false,
      isHovered: false,
      setExpanded: jest.fn(),
      setLocked: jest.fn(),
      setHovered: jest.fn(),
      toggleExpanded: jest.fn(),
      toggleLocked: jest.fn(),
      shouldShowExpanded: () => false,
      getNavbarWidth: () => 64,
    };

    mockUseNavbarStore.mockReturnValue(mockStore);

    render(
      <TestWrapper>
        <DashboardLayout title="Test Dashboard">
          <div>Test Content</div>
        </DashboardLayout>
      </TestWrapper>
    );

    // Check that main content has correct margin for collapsed navbar
    const mainContent = document.querySelector('.main-content-with-navbar');
    expect(mainContent).toHaveClass('main-content-with-navbar');
    expect(mainContent).not.toHaveClass('navbar-expanded');
  });
});
