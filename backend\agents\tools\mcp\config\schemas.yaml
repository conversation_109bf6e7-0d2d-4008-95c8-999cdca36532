# MCP Tool Validation Schemas Configuration
# This file defines validation schemas for MCP tools
# Schemas defined here will be automatically loaded and applied

schemas:
  # Example: Custom tool schema
  my_custom_tool:
    schema_type: "dynamic"
    base_schema: "BaseInputSchema"
    enabled: true
    priority: 100
    fields:
      input_text:
        type: "str"
        description: "Text input for processing"
        required: true
        min_length: 1
        max_length: 10000
      processing_mode:
        type: "str"
        description: "Mode of processing"
        required: false
        default: "standard"
      options:
        type: "dict"
        description: "Additional processing options"
        required: false
        default: {}

  # Example: File-based tool
  custom_file_processor:
    schema_type: "dynamic"
    base_schema: "FileInputSchema"
    enabled: true
    priority: 90
    fields:
      processing_type:
        type: "str"
        description: "Type of file processing"
        required: true
      output_format:
        type: "str"
        description: "Desired output format"
        required: false
        default: "json"
      include_metadata:
        type: "bool"
        description: "Whether to include file metadata"
        required: false
        default: true

  # Example: Analysis tool
  advanced_analyzer:
    schema_type: "dynamic"
    base_schema: "PandasAIInputSchema"
    enabled: true
    priority: 80
    fields:
      analysis_depth:
        type: "str"
        description: "Depth of analysis"
        required: false
        default: "standard"
      include_visualizations:
        type: "bool"
        description: "Whether to include charts"
        required: false
        default: true
      max_results:
        type: "int"
        description: "Maximum number of results"
        required: false
        default: 100
        ge: 1
        le: 1000

  # Example: Conversation tool variant
  specialized_conversation:
    schema_type: "dynamic"
    base_schema: "BaseInputSchema"
    enabled: true
    priority: 70
    fields:
      message:
        type: "str"
        description: "User message"
        required: true
        min_length: 1
        max_length: 5000
      conversation_type:
        type: "str"
        description: "Type of conversation"
        required: false
        default: "general"
      context_depth:
        type: "int"
        description: "How much context to consider"
        required: false
        default: 5
        ge: 1
        le: 20
      response_style:
        type: "str"
        description: "Style of response"
        required: false
        default: "professional"

  # Example: Code execution variant
  secure_code_executor:
    schema_type: "dynamic"
    base_schema: "BaseInputSchema"
    enabled: true
    priority: 60
    fields:
      code:
        type: "str"
        description: "Code to execute"
        required: true
        min_length: 1
        max_length: 50000
      language:
        type: "str"
        description: "Programming language"
        required: false
        default: "python"
      security_level:
        type: "str"
        description: "Security level for execution"
        required: false
        default: "standard"
      timeout_seconds:
        type: "int"
        description: "Execution timeout"
        required: false
        default: 30
        ge: 1
        le: 300
      allowed_imports:
        type: "list"
        description: "List of allowed imports"
        required: false
        default: []

# Global configuration
global:
  auto_discovery: true
  enable_inference: true
  cache_schemas: true
  validation_mode: "strict"  # strict, lenient, disabled
  
# Schema inheritance rules
inheritance:
  # Tools ending with these suffixes inherit from specific base schemas
  file_tools:
    suffix: ["_file", "_document", "_upload"]
    base_schema: "FileInputSchema"
  
  query_tools:
    suffix: ["_query", "_search", "_find"]
    base_schema: "QueryInputSchema"
    
  analysis_tools:
    suffix: ["_analysis", "_analyze", "_stats"]
    base_schema: "PandasAIInputSchema"
    
  conversation_tools:
    suffix: ["_chat", "_conversation", "_talk"]
    base_schema: "ConversationInputSchema"

# Field type mappings for common patterns
field_patterns:
  # Common field names and their expected types
  text_fields: ["text", "content", "message", "description", "prompt"]
  file_fields: ["file_path", "document_path", "upload_path"]
  numeric_fields: ["count", "limit", "max", "min", "threshold"]
  boolean_fields: ["enabled", "include", "show", "hide", "allow"]
  
# Validation rules
validation:
  # Maximum field lengths by type
  max_lengths:
    text: 50000
    message: 10000
    description: 2000
    prompt: 5000
    file_path: 500
    
  # Default values by type
  defaults:
    temperature: 0.7
    max_tokens: 1000
    timeout: 30
    retries: 3
