"""
Quick Actions API endpoints for the Datagenius backend.

This module provides API endpoints for quick action buttons and specialized tools.
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..models.auth import User
from ..database import get_db
from ..auth import get_current_active_user

# Import enhanced features configuration
try:
    from backend.config.enhanced_features import get_enhanced_config, get_quick_actions
    ENHANCED_CONFIG_AVAILABLE = True
except ImportError:
    ENHANCED_CONFIG_AVAILABLE = False

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/quick-actions", tags=["Quick Actions"])


class QuickAction(BaseModel):
    """Quick action model."""
    id: str = Field(..., description="Unique identifier for the action")
    label: str = Field(..., description="Display label for the action")
    description: str = Field(..., description="Description of what the action does")
    icon: str = Field(..., description="Icon identifier for the action")
    category: str = Field(..., description="Category of the action")
    enabled: bool = Field(True, description="Whether the action is enabled")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class QuickActionsResponse(BaseModel):
    """Response model for quick actions."""
    actions: List[QuickAction] = Field(..., description="List of available quick actions")
    categories: List[str] = Field(..., description="Available categories")
    enabled: bool = Field(..., description="Whether quick actions are enabled")


class ExecuteQuickActionRequest(BaseModel):
    """Request model for executing a quick action."""
    action_id: str = Field(..., description="ID of the action to execute")
    conversation_id: str = Field(..., description="ID of the conversation")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context for the action")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class ExecuteQuickActionResponse(BaseModel):
    """Response model for quick action execution."""
    success: bool = Field(..., description="Whether the action was executed successfully")
    message: str = Field(..., description="Result message")
    action_id: str = Field(..., description="ID of the executed action")
    conversation_id: str = Field(..., description="ID of the conversation")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Response metadata")


@router.get("/", response_model=QuickActionsResponse)
async def get_quick_actions_endpoint(
    persona_id: Optional[str] = None,
    category: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get available quick actions for the current user.
    
    Args:
        persona_id: Optional persona ID to filter actions
        category: Optional category to filter actions
        
    Returns:
        List of available quick actions
    """
    logger.info(f"User {current_user.id} requesting quick actions")
    
    if not ENHANCED_CONFIG_AVAILABLE:
        logger.warning("Enhanced configuration not available, returning empty actions")
        return QuickActionsResponse(
            actions=[],
            categories=[],
            enabled=False
        )
    
    try:
        config = get_enhanced_config()
        
        if not config.enable_quick_action_buttons:
            logger.info("Quick action buttons are disabled")
            return QuickActionsResponse(
                actions=[],
                categories=[],
                enabled=False
            )
        
        # Get configured quick actions
        configured_actions = get_quick_actions()
        
        # Convert to QuickAction models
        actions = []
        categories = set()
        
        for action_config in configured_actions:
            # Filter by persona if specified
            if persona_id and action_config.get("persona_id") and action_config["persona_id"] != persona_id:
                continue
                
            # Filter by category if specified
            if category and action_config.get("category") != category:
                continue
            
            action = QuickAction(
                id=action_config["id"],
                label=action_config["label"],
                description=action_config["description"],
                icon=action_config["icon"],
                category=action_config["category"],
                enabled=action_config.get("enabled", True),
                metadata=action_config.get("metadata")
            )
            
            actions.append(action)
            categories.add(action_config["category"])
        
        logger.info(f"Returning {len(actions)} quick actions for user {current_user.id}")
        
        return QuickActionsResponse(
            actions=actions,
            categories=sorted(list(categories)),
            enabled=True
        )
        
    except Exception as e:
        logger.error(f"Error getting quick actions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get quick actions")


@router.post("/execute", response_model=ExecuteQuickActionResponse)
async def execute_quick_action(
    request: ExecuteQuickActionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Execute a quick action.
    
    Args:
        request: Quick action execution request
        
    Returns:
        Execution result
    """
    logger.info(f"User {current_user.id} executing quick action {request.action_id}")
    
    try:
        # Import chat functionality for message sending
        from ..api.chat import send_message_to_agent
        from ..models.chat import SendMessageRequest
        
        # Get the conversation to verify access
        from ..database import get_conversation
        conversation = get_conversation(db, request.conversation_id)
        
        if not conversation or conversation.user_id != current_user.id:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        # Map quick action to message content
        action_messages = {
            "marketing_strategy": "Generate a comprehensive marketing strategy",
            "campaign_strategy": "Create a targeted campaign strategy",
            "social_media_content": "Generate engaging social media content",
            "seo_optimization": "Provide SEO optimization recommendations",
            "content_analysis": "Analyze the provided marketing content",
            # Enhanced content creation actions
            "blog_content": "Generate engaging blog content",
            "email_marketing": "Create email marketing campaigns",
            "ad_copy": "Generate compelling advertising copy",
            "press_release": "Create a professional press release",
            # Analysis and research actions
            "competitor_analysis": "Analyze competitors and market positioning",
            "audience_research": "Research target audience and create personas",
            "market_analysis": "Analyze market trends and opportunities",
            # Template gallery and setup actions
            "template_gallery": "Show me the template gallery",
            "business_setup": "Help me set up my business profile",
            "show_examples": "Show me marketing examples",
            "business_setup_basic": "Continue with basic business setup",
            "business_setup_goals": "Continue to marketing goals setup",
            "business_setup_audience": "Continue to audience setup",
            "business_setup_complete": "Complete my business setup",
            # Data-driven actions
            "data_driven_analysis": "Analyze my data for marketing insights",
            "data_driven_campaigns": "Create data-driven marketing campaigns"
        }
        
        message_content = action_messages.get(
            request.action_id,
            f"Execute action: {request.action_id}"
        )
        
        # Create enhanced context for the action
        enhanced_context = request.context.copy() if request.context else {}
        enhanced_context["quick_action"] = {
            "action_id": request.action_id,
            "triggered_by": "quick_action_button",
            "user_id": str(current_user.id)
        }
        
        # Add metadata if provided
        if request.metadata:
            enhanced_context["action_metadata"] = request.metadata
        
        # Create send message request
        send_request = SendMessageRequest(
            conversation_id=request.conversation_id,
            message=message_content,
            context=enhanced_context
        )
        
        # Execute the action by sending a message
        response = await send_message_to_agent(send_request, db, current_user)
        
        logger.info(f"Quick action {request.action_id} executed successfully")
        
        return ExecuteQuickActionResponse(
            success=True,
            message="Quick action executed successfully",
            action_id=request.action_id,
            conversation_id=request.conversation_id,
            metadata={
                "user_message_id": response.user_message.id,
                "ai_message_id": response.ai_message.id,
                "execution_time": "immediate"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing quick action {request.action_id}: {e}")
        return ExecuteQuickActionResponse(
            success=False,
            message=f"Failed to execute action: {str(e)}",
            action_id=request.action_id,
            conversation_id=request.conversation_id,
            metadata={"error": str(e)}
        )


@router.get("/categories")
async def get_quick_action_categories(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get available quick action categories.
    
    Returns:
        List of available categories
    """
    logger.info(f"User {current_user.id} requesting quick action categories")
    
    if not ENHANCED_CONFIG_AVAILABLE:
        return {"categories": [], "enabled": False}
    
    try:
        config = get_enhanced_config()
        
        if not config.enable_quick_action_buttons:
            return {"categories": [], "enabled": False}
        
        configured_actions = get_quick_actions()
        categories = list(set(action["category"] for action in configured_actions))
        
        return {
            "categories": sorted(categories),
            "enabled": True,
            "total_actions": len(configured_actions)
        }
        
    except Exception as e:
        logger.error(f"Error getting quick action categories: {e}")
        raise HTTPException(status_code=500, detail="Failed to get categories")


@router.get("/persona/{persona_id}")
async def get_persona_quick_actions(
    persona_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get quick actions specific to a persona.
    
    Args:
        persona_id: ID of the persona
        
    Returns:
        Persona-specific quick actions
    """
    logger.info(f"User {current_user.id} requesting quick actions for persona {persona_id}")
    
    # For marketing agent, return specialized actions
    if persona_id == "marketing_agent":
        marketing_actions = [
            {
                "id": "marketing_strategy",
                "label": "Create Marketing Strategy",
                "description": "Generate a comprehensive marketing strategy based on your business data",
                "icon": "strategy",
                "category": "strategy",
                "priority": 1
            },
            {
                "id": "campaign_strategy",
                "label": "Create Campaign Strategy", 
                "description": "Develop a targeted campaign plan for specific goals",
                "icon": "campaign",
                "category": "campaign",
                "priority": 2
            },
            {
                "id": "social_media_content",
                "label": "Social Media Content",
                "description": "Create engaging social media posts and content",
                "icon": "social",
                "category": "content",
                "priority": 3
            },
            {
                "id": "seo_optimization",
                "label": "SEO Optimization",
                "description": "Optimize your content for search engines",
                "icon": "seo",
                "category": "seo",
                "priority": 4
            }
        ]
        
        return {
            "persona_id": persona_id,
            "actions": marketing_actions,
            "enabled": True,
            "specialized": True
        }
    
    # For other personas, return general actions
    return await get_quick_actions_endpoint(persona_id=persona_id, current_user=current_user)


@router.get("/health")
async def quick_actions_health():
    """
    Health check endpoint for quick actions service.
    
    Returns:
        Health status
    """
    return {
        "status": "healthy",
        "enhanced_config_available": ENHANCED_CONFIG_AVAILABLE,
        "timestamp": "2024-01-01T00:00:00Z"
    }
