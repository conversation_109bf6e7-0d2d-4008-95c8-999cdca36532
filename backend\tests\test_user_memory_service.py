#!/usr/bin/env python3
"""
Test script to verify that the memory service can load user settings from the database.
"""

import logging
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.utils.memory_service import MemoryService

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_user_memory_service():
    """Test memory service with user settings."""
    logger.info("Testing memory service with user settings...")
    
    # Test with a user ID (assuming user 2 exists from your screenshot)
    test_user_id = 2
    
    try:
        # Get memory service instance with user settings
        memory_service = MemoryService.get_instance_with_user_id(test_user_id)
        
        if memory_service.initialized:
            logger.info("✅ Memory service initialized successfully with user settings")
            
            # Check if user settings were loaded
            if hasattr(memory_service, '_current_user_settings'):
                settings = memory_service._current_user_settings
                logger.info(f"📋 User settings loaded: {settings}")
                
                provider = settings.get('memory_service_provider', 'Not set')
                model = settings.get('memory_service_model', 'Not set')
                logger.info(f"🤖 Memory service provider: {provider}")
                logger.info(f"🧠 Memory service model: {model}")
            else:
                logger.warning("⚠️ No user settings found in memory service")
                
        else:
            logger.error("❌ Memory service failed to initialize")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing memory service: {e}", exc_info=True)
        return False
    
    return True

if __name__ == "__main__":
    logger.info("🧪 Testing Memory Service with User Settings")
    logger.info("=" * 50)
    
    success = test_user_memory_service()
    
    if success:
        logger.info("🎉 All tests passed!")
    else:
        logger.error("💥 Tests failed!")
        sys.exit(1)
