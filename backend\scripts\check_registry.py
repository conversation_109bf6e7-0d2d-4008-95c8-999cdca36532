"""
Check the agent registry for registered personas.

This script checks the agent registry to see what personas are registered.
"""

import os
import sys
import logging

# Add the parent directory to sys.path to allow importing from app
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_registry():
    """
    Check the agent registry for registered personas.
    """
    try:
        # Import the agent registry
        from agents.registry import AgentRegistry
        
        # Get all registered personas
        registered_personas = AgentRegistry.list_registered_personas()
        logger.info(f"Registered personas: {registered_personas}")
        
        # Check for specific persona
        persona_id = "marketing-ai"
        if persona_id in registered_personas:
            logger.info(f"Persona {persona_id} is registered")
            
            # Get the agent class
            agent_class = AgentRegistry.get_agent_class(persona_id)
            logger.info(f"Agent class for {persona_id}: {agent_class}")
            
            # Get the configuration
            config = AgentRegistry.get_configuration(persona_id)
            logger.info(f"Configuration for {persona_id}: {config}")
        else:
            logger.error(f"Persona {persona_id} is not registered")
            
        # Check persona files
        personas_dir = os.path.join(parent_dir, "personas")
        if os.path.exists(personas_dir):
            persona_files = os.listdir(personas_dir)
            logger.info(f"Persona files in {personas_dir}: {persona_files}")
        else:
            logger.error(f"Personas directory not found: {personas_dir}")
            
        # Check agent modules
        agents_dir = os.path.join(parent_dir, "agents")
        if os.path.exists(agents_dir):
            agent_files = [f for f in os.listdir(agents_dir) if f.endswith(".py") or os.path.isdir(os.path.join(agents_dir, f))]
            logger.info(f"Agent files in {agents_dir}: {agent_files}")
            
            # Check for marketing agent specifically
            marketing_dir = os.path.join(agents_dir, "marketing_agent")
            if os.path.exists(marketing_dir) and os.path.isdir(marketing_dir):
                marketing_files = os.listdir(marketing_dir)
                logger.info(f"Marketing agent files: {marketing_files}")
            else:
                logger.warning(f"Marketing agent directory not found: {marketing_dir}")
        else:
            logger.error(f"Agents directory not found: {agents_dir}")
            
    except Exception as e:
        logger.error(f"Error checking registry: {str(e)}", exc_info=True)

if __name__ == "__main__":
    check_registry()
