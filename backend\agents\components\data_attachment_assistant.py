"""
Component to assist users with attaching or selecting data sources.

This component provides intelligent guidance on how to attach different types of data,
explains file format requirements, and helps users prepare their data for analysis.
"""

import logging
import re
import sys
from typing import Dict, Any, List, Set, Optional
import time # Added for timestamp
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent
# Might need access to data source management services later
# from backend.app.services.data_source_service import DataSourceService

logger = logging.getLogger(__name__)


class DataAttachmentAssistantComponent(AgentComponent):
    """
    Guides users on how to attach files or select existing data sources.
    """

    def __init__(self):
        """Initialize the DataAttachmentAssistantComponent."""
        super().__init__()
        # self.data_source_service = DataSourceService() # Placeholder for service interaction

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        # Get supported file types from config
        self.supported_file_types = config.get("supported_file_types", [
            "csv", "xlsx", "pdf", "docx", "txt"
        ])

        # Define file type groups for more targeted assistance
        self.file_type_groups = {
            "tabular": ["csv", "xlsx", "xls", "tsv"],
            "document": ["pdf", "docx", "doc", "txt", "rtf"],
            "image": ["jpg", "jpeg", "png", "gif", "bmp"],
            "audio": ["mp3", "wav", "ogg", "flac"],
            "video": ["mp4", "avi", "mov", "wmv"]
        }

        # Define file type descriptions and requirements
        self.file_type_info = {
            "csv": {
                "description": "Comma-separated values file, ideal for tabular data",
                "requirements": "Should have a header row and consistent columns",
                "size_limit": "Up to 50MB",
                "example_usage": "Data analysis, visualization, machine learning"
            },
            "xlsx": {
                "description": "Microsoft Excel spreadsheet",
                "requirements": "Well-structured data with clear headers",
                "size_limit": "Up to 50MB",
                "example_usage": "Financial analysis, data visualization, reporting"
            },
            "pdf": {
                "description": "Portable Document Format file",
                "requirements": "Text should be selectable (not scanned images)",
                "size_limit": "Up to 100MB",
                "example_usage": "Document analysis, text extraction, summarization"
            },
            "docx": {
                "description": "Microsoft Word document",
                "requirements": "Well-formatted text content",
                "size_limit": "Up to 50MB",
                "example_usage": "Text analysis, content extraction, summarization"
            },
            "txt": {
                "description": "Plain text file",
                "requirements": "UTF-8 encoding recommended",
                "size_limit": "Up to 50MB",
                "example_usage": "Text analysis, simple data processing"
            }
        }

        # Define keywords that indicate data attachment needs
        self.data_keywords = [
            "attach", "upload", "file", "data", "connect", "source", "dataset",
            "spreadsheet", "excel", "csv", "document", "pdf", "word", "docx",
            "import", "load", "analyze", "process", "work with"
        ]

        logger.info(f"DataAttachmentAssistantComponent '{self.name}' initialized with {len(self.supported_file_types)} supported file types.")

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process the user message to detect if data attachment assistance is needed.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object, potentially with guidance on data attachment.
        """
        user_message = context.message or ""
        user_id = str(context.user_id)
        conversation_id = str(context.conversation_id)

        logger.debug(f"DataAttachmentAssistantComponent processing message: {user_message}")

        # Check if there are already attached files in the context
        attached_files = context.initial_context.get("attached_files", [])

        # Check if this is a request for data attachment help
        message_lower = user_message.lower()

        # Detect if the user is asking about specific file types
        mentioned_file_types = self._detect_file_types(message_lower)

        # Detect if the user needs data attachment assistance
        needs_assistance = self._needs_data_assistance(message_lower)

        # If the user already has attached files, they might need help using them
        if attached_files:
            # The helper will modify context directly (metadata and response)
            await self._provide_attached_files_guidance(context, attached_files)
            return context

        # If the user mentioned specific file types, provide targeted guidance
        elif mentioned_file_types:
            await self._provide_file_type_guidance(context, mentioned_file_types)
            return context

        # If the user needs general data attachment assistance
        elif needs_assistance:
            # Provide general guidance on how to attach data
            guidance_text = (
                "\nIt looks like you might need to work with data. "
                "You can upload files directly in the chat interface by clicking the attachment button or dragging and dropping files. "
                f"I support these file types: {', '.join(self.supported_file_types)}.\n\n"
                "What kind of data are you looking to analyze?"
            )

            # Add guidance to metadata and set response
            context.metadata["data_attachment_guidance"] = guidance_text
            context.response = guidance_text # Set the response for the user

            # Store in metadata for future reference
            if conversation_id: # conversation_id is now a string
                context.metadata["data_guidance_provided"] = True
                context.metadata["data_guidance_timestamp"] = time.time() # Use current time

            logger.info(f"Provided general data attachment guidance to user {user_id}.")
        else:
            logger.debug("No data attachment assistance needed.")

        return context

    def _detect_file_types(self, message: str) -> Set[str]:
        """
        Detect mentions of specific file types in the message.

        Args:
            message: The user message.

        Returns:
            Set of detected file types.
        """
        mentioned_types = set()

        # Check for explicit file extensions
        for file_type in self.supported_file_types:
            # Look for mentions like ".csv", "csv file", etc.
            patterns = [
                f"\\.{file_type}\\b",
                f"\\b{file_type} files?\\b",
                f"\\b{file_type} data\\b"
            ]

            if any(re.search(pattern, message) for pattern in patterns):
                mentioned_types.add(file_type)

        # Check for file type groups
        for group, types in self.file_type_groups.items():
            if group in message or f"{group} files" in message or f"{group} data" in message:
                # Add all supported file types from this group
                for file_type in types:
                    if file_type in self.supported_file_types:
                        mentioned_types.add(file_type)

        return mentioned_types

    def _needs_data_assistance(self, message: str) -> bool:
        """
        Determine if the user needs data attachment assistance.

        Args:
            message: The user message.

        Returns:
            True if assistance is needed, False otherwise.
        """
        # Check for keywords that indicate data attachment needs
        return any(keyword in message for keyword in self.data_keywords)

    async def _provide_file_type_guidance(self, context: "AgentProcessingContext", file_types: Set[str]) -> None:
        """
        Provide guidance for specific file types. Modifies context directly.

        Args:
            context: The AgentProcessingContext object.
            file_types: Set of file types to provide guidance for.
        """
        guidance_text = "\nHere's information about the file types you mentioned:\n\n"

        for file_type in file_types:
            info = self.file_type_info.get(file_type, {})
            if info:
                guidance_text += f"**{file_type.upper()}**\n"
                guidance_text += f"- Description: {info.get('description', 'No description available')}\n"
                guidance_text += f"- Requirements: {info.get('requirements', 'No specific requirements')}\n"
                guidance_text += f"- Size Limit: {info.get('size_limit', 'Standard limits apply')}\n"
                guidance_text += f"- Example Usage: {info.get('example_usage', 'Various data analysis tasks')}\n\n"

        guidance_text += "To upload a file, click the attachment button in the chat interface or drag and drop your file."

        # Add guidance to metadata and set response
        context.metadata["data_attachment_guidance"] = guidance_text
        context.response = guidance_text

        logger.info(f"Provided specific file type guidance for: {', '.join(file_types)}")


    async def _provide_attached_files_guidance(self, context: "AgentProcessingContext", attached_files: List[Dict[str, Any]]) -> None:
        """
        Provide guidance for already attached files. Modifies context directly.

        Args:
            context: The AgentProcessingContext object.
            attached_files: List of attached file information.
        """
        file_count = len(attached_files)
        file_names = [f.get('filename', 'Unknown file') for f in attached_files]

        guidance_text = f"\nI see you have {file_count} file{'s' if file_count > 1 else ''} attached: "
        guidance_text += ", ".join(file_names) + ".\n\n"

        # Add specific guidance based on file types
        file_extensions = [f.get('filename', '').split('.')[-1].lower() for f in attached_files if '.' in f.get('filename', '')]

        if any(ext in self.file_type_groups['tabular'] for ext in file_extensions):
            guidance_text += (
                "For tabular data files (CSV, Excel), you can ask me to:\n"
                "- Analyze the data structure and provide a summary\n"
                "- Create visualizations like charts or graphs\n"
                "- Perform statistical analysis\n"
                "- Filter or query the data\n"
                "- Clean the data by handling missing values or duplicates\n\n"
            )

        if any(ext in self.file_type_groups['document'] for ext in file_extensions):
            guidance_text += (
                "For document files (PDF, Word, Text), you can ask me to:\n"
                "- Extract and summarize key information\n"
                "- Analyze the content for insights\n"
                "- Answer questions about the document\n"
                "- Extract structured data if available\n\n"
            )

        guidance_text += "What would you like to do with your attached file(s)?"

        # Add guidance to metadata and set response
        context.metadata["data_attachment_guidance"] = guidance_text
        context.response = guidance_text

        logger.info(f"Provided guidance for {file_count} attached files")


    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["data_attachment_assistance"])
