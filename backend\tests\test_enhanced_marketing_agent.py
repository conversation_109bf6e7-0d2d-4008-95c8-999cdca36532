"""
Comprehensive tests for enhanced marketing agent features.

This module tests all the new enhancements including context-aware greetings,
new action types, template system, and business context detection.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any, List

# Import the components to test
from backend.agents.tools.mcp.business_context_detection import BusinessContextTool
from backend.agents.tools.mcp.template_gallery_tools import (
    TemplateGalleryTool, BusinessSetupTool, ShowExamplesTool, 
    TemplateRepository, MarketingTemplate
)
from backend.agents.tools.mcp.enhanced_marketing_tools import (
    BlogContentTool, EmailMarketingTool, AdCopyTool, PressReleaseTool,
    CompetitorAnalysisTool, AudienceResearchTool, MarketAnalysisTool
)
from backend.agents.marketing_agent.context_aware_greeting import ContextAwareGreetingGenerator


class TestBusinessContextDetection:
    """Test business context detection functionality."""
    
    @pytest.fixture
    def context_tool(self):
        return BusinessContextTool()
    
    @pytest.mark.asyncio
    async def test_basic_context_detection(self, context_tool):
        """Test basic business context detection."""
        params = {
            "conversation_history": [
                {
                    "role": "user",
                    "content": "I run a technology startup that develops SaaS platforms for small businesses"
                }
            ],
            "user_profile": {
                "company": "TechCorp",
                "industry": "technology"
            },
            "analysis_depth": "standard"
        }
        
        result = await context_tool.execute(**params)
        
        assert result["success"] is True
        assert result["business_context"] is not None
        
        context = result["business_context"]
        assert context.get("industry") == "technology"
        assert context.get("business_size") == "startup"
        assert "confidence_score" in context
        assert isinstance(context["confidence_score"], float)
    
    @pytest.mark.asyncio
    async def test_conversation_analysis(self, context_tool):
        """Test conversation history analysis."""
        conversation = [
            {"role": "user", "content": "We're a healthcare company looking to improve patient engagement"},
            {"role": "assistant", "content": "I can help with healthcare marketing strategies"},
            {"role": "user", "content": "We need help with lead generation and brand awareness"}
        ]
        
        result = await context_tool.execute(conversation_history=conversation)
        
        assert result["success"] is True
        context = result["business_context"]
        assert context.get("industry") == "healthcare"
        assert "lead generation" in context.get("marketing_challenges", [])
        assert "brand awareness" in context.get("marketing_challenges", [])
    
    def test_file_content_analysis(self, context_tool):
        """Test file content analysis for business context."""
        content = """
        Our company is a leading technology firm specializing in artificial intelligence 
        and machine learning solutions for enterprise clients. We provide B2B software 
        platforms that help businesses automate their workflows and improve efficiency.
        Our target market includes Fortune 500 companies and growing enterprises.
        """
        
        result = context_tool._analyze_file_content(content, "test_file.txt")
        
        assert result.get("industry") == "technology"
        assert result.get("business_type") == "B2B"
        assert "software" in result.get("key_products", [])


class TestTemplateGallery:
    """Test template gallery functionality."""
    
    @pytest.fixture
    def template_tool(self):
        return TemplateGalleryTool()
    
    @pytest.fixture
    def template_repo(self):
        return TemplateRepository()
    
    def test_template_loading(self, template_repo):
        """Test template loading from repository."""
        templates = template_repo.load_templates()
        
        assert len(templates) > 0
        assert all(isinstance(t, MarketingTemplate) for t in templates)
        
        # Check required fields
        for template in templates:
            assert template.id is not None
            assert template.name is not None
            assert template.action_type is not None
            assert template.category is not None
    
    def test_template_filtering(self, template_repo):
        """Test template filtering functionality."""
        # Filter by category
        strategy_templates = template_repo.filter_templates(category="strategy")
        assert all(t.category == "strategy" for t in strategy_templates)
        
        # Filter by industry
        tech_templates = template_repo.filter_templates(industry="technology")
        assert all(t.industry in ["technology", "all"] for t in tech_templates)
        
        # Search functionality
        search_results = template_repo.filter_templates(search_term="email")
        assert len(search_results) > 0
        assert all("email" in t.name.lower() or "email" in t.description.lower() 
                  for t in search_results)
    
    @pytest.mark.asyncio
    async def test_template_gallery_execution(self, template_tool):
        """Test template gallery tool execution."""
        result = await template_tool.execute(category="all", sort_by="popular")
        
        assert result["success"] is True
        assert "content" in result
        assert "templates" in result
        assert len(result["templates"]) > 0
        
        # Check metadata
        metadata = result["metadata"]
        assert metadata["tool_type"] == "template_gallery"
        assert "total_templates" in metadata


class TestEnhancedMarketingTools:
    """Test enhanced marketing tools."""
    
    @pytest.mark.asyncio
    async def test_blog_content_tool(self):
        """Test blog content generation tool."""
        tool = BlogContentTool()
        
        with patch.object(tool, 'execute', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "content": "Generated blog content strategy",
                "metadata": {"content_type": "blog_content"}
            }
            
            result = await tool.execute(
                topic="AI in Marketing",
                target_audience="Marketing professionals",
                keywords="AI, marketing automation, personalization"
            )
            
            assert result["success"] is True
            assert "content" in result
    
    @pytest.mark.asyncio
    async def test_email_marketing_tool(self):
        """Test email marketing tool."""
        tool = EmailMarketingTool()
        
        params = {
            "email_type": "welcome_series",
            "subject_line": "Welcome to our community",
            "target_audience": "New subscribers",
            "campaign_goal": "Onboard new users"
        }
        
        with patch.object(tool, 'execute', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "content": "Generated email marketing campaign",
                "metadata": {"content_type": "email_marketing"}
            }
            
            result = await tool.execute(**params)
            assert result["success"] is True
    
    @pytest.mark.asyncio
    async def test_competitor_analysis_tool(self):
        """Test competitor analysis tool."""
        tool = CompetitorAnalysisTool()
        
        params = {
            "industry": "technology",
            "competitors": "Company A, Company B, Company C",
            "analysis_focus": "pricing, marketing strategies, positioning",
            "company_info": "SaaS startup in project management space"
        }
        
        with patch.object(tool, 'execute', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "content": "Generated competitor analysis",
                "metadata": {"content_type": "competitor_analysis"}
            }
            
            result = await tool.execute(**params)
            assert result["success"] is True


class TestContextAwareGreeting:
    """Test context-aware greeting generation."""
    
    @pytest.fixture
    def greeting_generator(self):
        return ContextAwareGreetingGenerator()
    
    @pytest.mark.asyncio
    async def test_first_time_greeting_with_data(self, greeting_generator):
        """Test greeting for first-time user with data."""
        context = {
            "is_first_conversation": True,
            "has_data_source": True,
            "data_sources": ["business_plan.pdf"],
            "user_info": {"name": "John Doe"}
        }
        
        with patch.object(greeting_generator, '_analyze_business_context', 
                         new_callable=AsyncMock) as mock_analyze:
            mock_analyze.return_value = {
                "industry": "technology",
                "business_size": "startup",
                "confidence_score": 0.8,
                "suggestions": []
            }
            
            greeting = await greeting_generator.generate_greeting(context)
            
            assert "data uploaded" in greeting.lower()
            assert "personalized" in greeting.lower()
            assert len(greeting) > 100  # Ensure substantial content
    
    @pytest.mark.asyncio
    async def test_returning_user_greeting(self, greeting_generator):
        """Test greeting for returning user."""
        context = {
            "is_first_conversation": False,
            "has_data_source": False,
            "conversation_history": [
                {"content": "I need help with social media marketing", "timestamp": "2024-01-01"}
            ]
        }
        
        greeting = await greeting_generator.generate_greeting(context)
        
        assert "welcome back" in greeting.lower()
        assert len(greeting) > 50


class TestBusinessSetupTool:
    """Test business setup tool."""
    
    @pytest.fixture
    def setup_tool(self):
        return BusinessSetupTool()
    
    @pytest.mark.asyncio
    async def test_setup_steps(self, setup_tool):
        """Test all business setup steps."""
        steps = ["start", "basic", "goals", "audience", "complete"]
        
        for step in steps:
            result = await setup_tool.execute(step=step)
            
            assert result["success"] is True
            assert "content" in result
            assert len(result["content"]) > 100
            
            metadata = result["metadata"]
            assert metadata["tool_type"] == "business_setup"
            assert metadata["step"] == step


class TestIntegrationScenarios:
    """Test integration scenarios combining multiple components."""
    
    @pytest.mark.asyncio
    async def test_complete_user_journey(self):
        """Test complete user journey from greeting to content generation."""
        # 1. Generate context-aware greeting
        greeting_generator = ContextAwareGreetingGenerator()
        context = {
            "is_first_conversation": True,
            "has_data_source": True,
            "data_sources": ["business_plan.pdf"]
        }
        
        with patch.object(greeting_generator, '_analyze_business_context', 
                         new_callable=AsyncMock) as mock_analyze:
            mock_analyze.return_value = {
                "industry": "technology",
                "business_size": "startup",
                "confidence_score": 0.8,
                "suggestions": [
                    {"title": "Tech Content Strategy", "action": "blog_content"}
                ]
            }
            
            greeting = await greeting_generator.generate_greeting(context)
            assert len(greeting) > 100
        
        # 2. Browse templates
        template_tool = TemplateGalleryTool()
        template_result = await template_tool.execute(category="content")
        assert template_result["success"] is True
        
        # 3. Generate content using enhanced tools
        blog_tool = BlogContentTool()
        with patch.object(blog_tool, 'execute', new_callable=AsyncMock) as mock_blog:
            mock_blog.return_value = {"success": True, "content": "Blog strategy"}
            
            blog_result = await blog_tool.execute(
                topic="Technology trends",
                target_audience="Tech professionals"
            )
            assert blog_result["success"] is True
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling across components."""
        # Test business context detection with invalid data
        context_tool = BusinessContextTool()
        result = await context_tool.execute(data_sources=["invalid_file"])
        
        # Should handle gracefully
        assert "success" in result
        
        # Test template gallery with invalid parameters
        template_tool = TemplateGalleryTool()
        result = await template_tool.execute(category="invalid_category")
        
        # Should return empty results gracefully
        assert result["success"] is True


if __name__ == "__main__":
    # Run basic tests
    pytest.main([__file__, "-v"])
