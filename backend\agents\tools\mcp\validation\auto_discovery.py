"""
Auto-Discovery Engine for MCP Tool Validation.

This module implements the automatic schema discovery system that can detect
and register new MCP tools without manual configuration, as specified in
the refactor.md Phase 2 requirements.
"""

import logging
import ast
import inspect
import importlib
import os
from typing import Dict, Any, Type, Optional, List, Set, Tuple
from pathlib import Path
from pydantic import BaseModel, Field, create_model
from ..base import BaseMCPTool
from .schema_registry import MCPSchemaRegistry

logger = logging.getLogger(__name__)


class ToolDiscoveryEngine:
    """
    Automatic discovery engine for MCP tools and their validation schemas.
    
    This engine scans the codebase for MCP tools, analyzes their structure,
    and automatically generates appropriate validation schemas.
    """
    
    def __init__(self, registry: MCPSchemaRegistry):
        """
        Initialize the discovery engine.
        
        Args:
            registry: Schema registry to register discovered tools
        """
        self.registry = registry
        self.logger = logging.getLogger(f"{__name__}.ToolDiscoveryEngine")
        
        # Discovery configuration
        self.scan_paths = [
            Path(__file__).parent.parent,  # MCP tools directory
        ]
        self.excluded_files = {
            "__init__.py", "base.py", "enhanced_base.py", 
            "register.py", "registry.py"
        }
        self.excluded_directories = {
            "__pycache__", "validation", "config", "examples",
            "testing", "migration", "monitoring"
        }
        
        # Tool analysis patterns
        self.tool_class_patterns = [
            r"class\s+(\w+Tool)\s*\(",
            r"class\s+(\w+)\s*\(.*BaseMCPTool",
            r"class\s+(MCP\w+)\s*\("
        ]
        
        self.logger.info("Initialized Tool Discovery Engine")
    
    def discover_all_tools(self) -> Dict[str, Dict[str, Any]]:
        """
        Discover all MCP tools in the configured scan paths.
        
        Returns:
            Dictionary of discovered tools with their metadata
        """
        discovered_tools = {}
        
        for scan_path in self.scan_paths:
            if not scan_path.exists():
                self.logger.warning(f"Scan path does not exist: {scan_path}")
                continue
            
            self.logger.info(f"Scanning path: {scan_path}")
            path_tools = self._scan_directory(scan_path)
            discovered_tools.update(path_tools)
        
        self.logger.info(f"Discovered {len(discovered_tools)} tools total")
        return discovered_tools
    
    def _scan_directory(self, directory: Path) -> Dict[str, Dict[str, Any]]:
        """
        Scan a directory for MCP tools.
        
        Args:
            directory: Directory to scan
            
        Returns:
            Dictionary of discovered tools in this directory
        """
        tools = {}
        
        try:
            for item in directory.iterdir():
                if item.is_file() and item.suffix == ".py":
                    if item.name in self.excluded_files:
                        continue
                    
                    tool_info = self._analyze_python_file(item)
                    if tool_info:
                        tools.update(tool_info)
                
                elif item.is_dir() and item.name not in self.excluded_directories:
                    # Recursively scan subdirectories
                    subdir_tools = self._scan_directory(item)
                    tools.update(subdir_tools)
        
        except Exception as e:
            self.logger.error(f"Error scanning directory {directory}: {e}")
        
        return tools
    
    def _analyze_python_file(self, file_path: Path) -> Optional[Dict[str, Dict[str, Any]]]:
        """
        Analyze a Python file for MCP tools.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            Dictionary of tools found in the file
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse the AST
            tree = ast.parse(content)
            
            # Find tool classes
            tools = {}
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    tool_info = self._analyze_class_node(node, file_path, content)
                    if tool_info:
                        tool_name = tool_info["name"]
                        tools[tool_name] = tool_info
            
            if tools:
                self.logger.debug(f"Found {len(tools)} tools in {file_path.name}")
            
            return tools if tools else None
            
        except Exception as e:
            self.logger.warning(f"Failed to analyze file {file_path}: {e}")
            return None
    
    def _analyze_class_node(self, node: ast.ClassDef, file_path: Path, content: str) -> Optional[Dict[str, Any]]:
        """
        Analyze a class node to determine if it's an MCP tool.
        
        Args:
            node: AST class node
            file_path: Path to the source file
            content: File content
            
        Returns:
            Tool information dictionary or None
        """
        try:
            # Check if class inherits from BaseMCPTool
            is_mcp_tool = False
            for base in node.bases:
                if isinstance(base, ast.Name) and base.id == "BaseMCPTool":
                    is_mcp_tool = True
                    break
                elif isinstance(base, ast.Attribute) and base.attr == "BaseMCPTool":
                    is_mcp_tool = True
                    break
            
            if not is_mcp_tool:
                return None
            
            # Extract tool information
            tool_info = {
                "name": self._derive_tool_name(node.name, file_path),
                "class_name": node.name,
                "file_path": str(file_path),
                "module_path": self._get_module_path(file_path),
                "methods": [],
                "execute_method": None,
                "schema_fields": {},
                "agent_aware": self._check_agent_awareness(content)
            }
            
            # Analyze methods
            for item in node.body:
                if isinstance(item, ast.FunctionDef):
                    method_info = self._analyze_method(item)
                    tool_info["methods"].append(method_info)
                    
                    if item.name == "execute":
                        tool_info["execute_method"] = method_info
                        tool_info["schema_fields"] = self._extract_schema_fields(item)
            
            return tool_info
            
        except Exception as e:
            self.logger.warning(f"Failed to analyze class node {node.name}: {e}")
            return None
    
    def _derive_tool_name(self, class_name: str, file_path: Path) -> str:
        """
        Derive tool name from class name and file path.
        
        Args:
            class_name: Name of the tool class
            file_path: Path to the source file
            
        Returns:
            Derived tool name
        """
        # Remove common suffixes
        name = class_name.lower()
        for suffix in ["tool", "mcp", "agent"]:
            if name.endswith(suffix):
                name = name[:-len(suffix)]
        
        # If name is empty, use file name
        if not name:
            name = file_path.stem.lower()
            for suffix in ["tool", "mcp", "_tool", "_mcp"]:
                if name.endswith(suffix):
                    name = name[:-len(suffix)]
        
        return name
    
    def _get_module_path(self, file_path: Path) -> str:
        """
        Get the module import path for a file.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            Module import path
        """
        try:
            # Convert file path to module path
            parts = file_path.parts
            
            # Find the backend directory
            backend_index = -1
            for i, part in enumerate(parts):
                if part == "backend":
                    backend_index = i
                    break
            
            if backend_index >= 0:
                module_parts = parts[backend_index:]
                module_path = ".".join(module_parts[:-1]) + "." + file_path.stem
                return module_path
            
            return str(file_path.stem)
            
        except Exception as e:
            self.logger.warning(f"Failed to get module path for {file_path}: {e}")
            return str(file_path.stem)
    
    def _analyze_method(self, node: ast.FunctionDef) -> Dict[str, Any]:
        """
        Analyze a method node.
        
        Args:
            node: AST function node
            
        Returns:
            Method information dictionary
        """
        return {
            "name": node.name,
            "args": [arg.arg for arg in node.args.args if arg.arg != "self"],
            "is_async": isinstance(node, ast.AsyncFunctionDef),
            "has_docstring": (
                len(node.body) > 0 and 
                isinstance(node.body[0], ast.Expr) and 
                isinstance(node.body[0].value, ast.Constant)
            )
        }
    
    def _extract_schema_fields(self, execute_node: ast.FunctionDef) -> Dict[str, Any]:
        """
        Extract schema fields from execute method.

        Args:
            execute_node: AST node for execute method

        Returns:
            Dictionary of schema fields
        """
        schema_fields = {}

        try:
            # Look for arguments parameter usage
            for node in ast.walk(execute_node):
                if isinstance(node, ast.Subscript):
                    # Look for arguments.get() calls
                    if (isinstance(node.value, ast.Attribute) and
                        isinstance(node.value.value, ast.Name) and
                        node.value.value.id == "arguments" and
                        node.value.attr == "get"):

                        # This is arguments.get() - extract the key
                        if isinstance(node.slice, ast.Constant):
                            field_name = node.slice.value
                            # Ensure field_name is a string (hashable)
                            if isinstance(field_name, str):
                                schema_fields[field_name] = {
                                    "type": "str",  # Default type
                                    "required": False
                                }

            # Add common MCP tool fields (ensure all keys are strings)
            common_fields = {
                "persona_id": {"type": "Optional[str]", "required": False},
                "agent_id": {"type": "Optional[str]", "required": False},
                "context": {"type": "Dict[str, Any]", "required": False},
                "provider": {"type": "Optional[str]", "required": False},
                "model": {"type": "Optional[str]", "required": False}
            }

            for field_name, field_info in common_fields.items():
                if field_name not in schema_fields:
                    schema_fields[field_name] = field_info

        except Exception as e:
            self.logger.warning(f"Failed to extract schema fields: {e}")

        return schema_fields
    
    def _check_agent_awareness(self, content: str) -> bool:
        """
        Check if tool implements agent awareness patterns.
        
        Args:
            content: File content to analyze
            
        Returns:
            True if agent awareness patterns are found
        """
        patterns = [
            "detect_agent_identity",
            "agent_identity",
            '"agent_aware": True'
        ]
        
        return any(pattern in content for pattern in patterns)
    
    def generate_schema_for_tool(self, tool_info: Dict[str, Any]) -> Optional[Type[BaseModel]]:
        """
        Generate a Pydantic schema for a discovered tool.
        
        Args:
            tool_info: Tool information dictionary
            
        Returns:
            Generated Pydantic schema class
        """
        try:
            schema_fields = {}
            
            # Convert schema field information to Pydantic fields
            for field_name, field_info in tool_info.get("schema_fields", {}).items():
                field_type = self._convert_type_string(field_info.get("type", "str"))
                required = field_info.get("required", False)
                
                if required:
                    schema_fields[field_name] = (field_type, ...)
                else:
                    schema_fields[field_name] = (field_type, None)
            
            # Create the schema class with model configuration to avoid namespace conflicts
            schema_class_name = f"{tool_info['class_name']}Schema"

            # Create model config to avoid protected namespace warnings
            model_config = {
                'protected_namespaces': ()  # Disable protected namespace warnings
            }

            schema_class = create_model(
                schema_class_name,
                __config__=type('Config', (), model_config),
                **schema_fields
            )
            
            return schema_class
            
        except Exception as e:
            self.logger.error(f"Failed to generate schema for tool {tool_info['name']}: {e}")
            return None
    
    def _convert_type_string(self, type_string: str) -> type:
        """
        Convert type string to Python type.
        
        Args:
            type_string: String representation of type
            
        Returns:
            Python type
        """
        type_mapping = {
            "str": str,
            "int": int,
            "float": float,
            "bool": bool,
            "list": list,
            "dict": dict,
            "Dict[str, Any]": Dict[str, Any],
            "Optional[str]": Optional[str],
            "List[str]": List[str]
        }
        
        return type_mapping.get(type_string, str)
    
    def register_discovered_tools(self, tools: Dict[str, Dict[str, Any]]) -> int:
        """
        Register discovered tools with the schema registry.
        
        Args:
            tools: Dictionary of discovered tools
            
        Returns:
            Number of tools successfully registered
        """
        registered_count = 0
        
        for tool_name, tool_info in tools.items():
            try:
                # Generate schema
                schema = self.generate_schema_for_tool(tool_info)
                if schema:
                    self.registry.register_schema(tool_name, schema)
                    registered_count += 1
                    self.logger.info(f"Registered schema for discovered tool: {tool_name}")
                else:
                    self.logger.warning(f"Failed to generate schema for tool: {tool_name}")
                    
            except Exception as e:
                self.logger.error(f"Failed to register tool {tool_name}: {e}")
        
        return registered_count
    
    def run_full_discovery(self) -> Dict[str, Any]:
        """
        Run full discovery process and register all found tools.
        
        Returns:
            Discovery results summary
        """
        self.logger.info("Starting full tool discovery process")
        
        # Discover all tools
        discovered_tools = self.discover_all_tools()
        
        # Register discovered tools
        registered_count = self.register_discovered_tools(discovered_tools)
        
        # Validate agent awareness
        agent_aware_count = 0
        for tool_name, tool_info in discovered_tools.items():
            if tool_info.get("agent_aware", False):
                agent_aware_count += 1
        
        results = {
            "total_discovered": len(discovered_tools),
            "successfully_registered": registered_count,
            "agent_aware_tools": agent_aware_count,
            "discovery_paths": [str(path) for path in self.scan_paths],
            "tools": list(discovered_tools.keys())
        }
        
        self.logger.info(f"Discovery complete: {results}")
        return results
