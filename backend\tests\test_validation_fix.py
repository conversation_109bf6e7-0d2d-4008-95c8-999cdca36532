#!/usr/bin/env python3
"""
Test to verify that the pandasai_visualization validation fix is working.
"""

import asyncio
import logging
import os
import pandas as pd
from agents.tools.mcp.validation.input_validator import validate_tool_input

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_validation_fix():
    """Test that the pandasai_visualization tool validation is working."""
    logger.info("Testing pandasai_visualization validation fix...")
    
    # Create test data
    test_file_path = "temp_uploads/test_data.csv"
    os.makedirs("temp_uploads", exist_ok=True)
    
    # Create sample data
    data = {
        "Department": ["Engineering", "Marketing", "Sales", "HR", "Finance"],
        "Average_Salary": [95000, 65000, 70000, 60000, 75000],
        "Employee_Count": [50, 20, 30, 15, 25]
    }
    df = pd.DataFrame(data)
    df.to_csv(test_file_path, index=False)
    
    # Test the validation with the new schema
    test_input = {
        "data_source": {
            "id": "test-id",
            "name": "test_data.csv",
            "file_path": test_file_path
        },
        "file_path": test_file_path,
        "query": "Create a bar chart showing average salary by department",
        "prompt": "Create a bar chart showing average salary by department",
        "api_key": "test_key",
        "provider": "groq"
    }
    
    logger.info("Testing validation with new schema...")
    validation_result = validate_tool_input("pandasai_visualization", test_input)
    
    if validation_result.is_valid:
        logger.info("✅ Validation passed!")
        logger.info(f"Sanitized data keys: {list(validation_result.sanitized_data.keys())}")
        logger.info(f"Query field: {validation_result.sanitized_data.get('query')}")
        logger.info(f"File path field: {validation_result.sanitized_data.get('file_path')}")
    else:
        logger.error("❌ Validation failed!")
        logger.error(f"Validation errors: {validation_result.errors}")
        for error in validation_result.errors:
            logger.error(f"  - {error}")
    
    # Test with minimal input (just query and file_path)
    logger.info("\nTesting with minimal input...")
    minimal_input = {
        "query": "Create a visualization",
        "file_path": test_file_path
    }
    
    minimal_validation = validate_tool_input("pandasai_visualization", minimal_input)
    
    if minimal_validation.is_valid:
        logger.info("✅ Minimal validation passed!")
    else:
        logger.error("❌ Minimal validation failed!")
        logger.error(f"Minimal validation errors: {minimal_validation.errors}")
    
    # Test with data_source only
    logger.info("\nTesting with data_source only...")
    data_source_input = {
        "data_source": test_file_path,
        "query": "Create a visualization"
    }
    
    data_source_validation = validate_tool_input("pandasai_visualization", data_source_input)
    
    if data_source_validation.is_valid:
        logger.info("✅ Data source validation passed!")
    else:
        logger.error("❌ Data source validation failed!")
        logger.error(f"Data source validation errors: {data_source_validation.errors}")
    
    # Clean up
    try:
        os.remove(test_file_path)
        logger.info("Cleaned up test file")
    except:
        pass
    
    return validation_result.is_valid

if __name__ == "__main__":
    asyncio.run(test_validation_fix())
