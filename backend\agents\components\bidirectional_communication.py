"""
Component for enabling bidirectional communication between personas.
"""

import logging
import json
import time
import sys
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent
from ..registry import AgentRegistry

logger = logging.getLogger(__name__)


class BidirectionalCommunicationComponent(AgentComponent):
    """
    Enables bidirectional communication between personas, allowing specialized personas
    to communicate back to the concierge or other personas.
    """

    def __init__(self):
        """Initialize the BidirectionalCommunicationComponent."""
        super().__init__()
        self.message_store = {}  # In-memory store for simplicity; could be replaced with Redis
        self.callback_registry = {}  # Store callback information

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"BidirectionalCommunicationComponent '{self.name}' initialized.")
        self.message_ttl = config.get("message_ttl", 3600)  # Default TTL: 1 hour
        self.agent_registry = AgentRegistry
        self.enable_auto_callbacks = config.get("enable_auto_callbacks", True)
        self.callback_threshold = config.get("callback_threshold", 0.7)

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process the context to enable bidirectional communication.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object with bidirectional communication capabilities.
        """
        user_message = context.message or ""
        conversation_id = str(context.conversation_id) # Ensure string
        current_persona = context.agent_config.id if context.agent_config else "unknown"

        logger.debug(f"BidirectionalCommunicationComponent processing for conversation {conversation_id}")

        # Initialize bidirectional communication context in component_data if not present
        component_data = context.component_data.setdefault(self.name, {})
        bidirectional_data = component_data.setdefault("bidirectional", {
            "callbacks": [],
            "messages": [],
            "pending_requests": []
        })

        # Check for callback commands
        if self._is_callback_command(user_message):
            # Extract callback target from command
            callback_target, callback_reason = self._extract_callback_info(user_message)
            if callback_target:
                # Register callback
                await self._register_callback(conversation_id, current_persona, callback_target, callback_reason)

                # Add callback information to metadata
                context.metadata["callback_request"] = {
                    "target_persona": callback_target,
                    "reason": callback_reason,
                    "message": f"I'll request assistance from the {callback_target} persona."
                }
                context.response = f"I'll request assistance from the {callback_target} persona."


                logger.info(f"Callback request initiated to {callback_target} for conversation {conversation_id}")
            else:
                logger.warning(f"Callback command detected but no target persona found: {user_message}")

        # Check for pending callbacks for this persona
        pending_callbacks = await self._check_pending_callbacks(conversation_id, current_persona)
        if pending_callbacks:
            # Add pending callbacks to component_data
            bidirectional_data["pending_callbacks"] = pending_callbacks

            # Add callback information to metadata
            context.metadata["pending_callbacks"] = {
                "count": len(pending_callbacks),
                "sources": [cb["source_persona"] for cb in pending_callbacks]
            }
            # Potentially set context.response to inform user about pending callbacks

            logger.info(f"Found {len(pending_callbacks)} pending callbacks for {current_persona}")

        # Check if this is a response to a callback
        if self._is_callback_response(user_message):
            # Extract callback response information
            response_target, response_message_content = self._extract_callback_response(user_message) # Renamed response_message
            if response_target:
                # Send the response
                await self._send_callback_response(conversation_id, current_persona, response_target, response_message_content)

                # Add response information to metadata
                context.metadata["callback_response_sent_to"] = { # Changed key for clarity
                    "target_persona": response_target,
                    "message": f"I've sent your response to the {response_target} persona."
                }
                context.response = f"I've sent your response to the {response_target} persona."


                logger.info(f"Callback response sent to {response_target} for conversation {conversation_id}")

        # Check for automatic callback detection
        if self.enable_auto_callbacks and not self._is_callback_command(user_message) and not self._is_callback_response(user_message):
            # Pass AgentProcessingContext to _detect_callback_need
            callback_needed, target_persona_suggestion, reason_suggestion = self._detect_callback_need(user_message, context) # Renamed vars
            if callback_needed:
                # Add automatic callback suggestion to metadata
                context.metadata["auto_callback_suggestion"] = {
                    "target_persona": target_persona_suggestion,
                    "reason": reason_suggestion,
                    "message": f"It seems you might need assistance from the {target_persona_suggestion} persona. Would you like me to request their help?"
                }
                # Potentially set context.response to ask the user
                # context.response = f"It seems you might need assistance from the {target_persona_suggestion} persona. Would you like me to request their help?"


                logger.info(f"Automatic callback suggestion for {target_persona_suggestion} in conversation {conversation_id}")

        return context

    def _is_callback_command(self, message: str) -> bool:
        """
        Determine if the message is a command to request a callback from another persona.

        Args:
            message: The user message.

        Returns:
            True if this is a callback command, False otherwise.
        """
        # Simple keyword-based detection
        callback_keywords = [
            "ask for help from", "request assistance from", "consult with",
            "get input from", "collaborate with", "need help from",
            "check with", "verify with", "confirm with"
        ]

        return any(keyword in message.lower() for keyword in callback_keywords)

    def _extract_callback_info(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract the callback target and reason from a callback command.

        Args:
            message: The user message.

        Returns:
            A tuple containing the target persona and the reason for the callback.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Map keywords to consistent kebab-case persona IDs
        persona_mapping = {
            "analyst": "composable-analysis-ai",
            "analysis": "composable-analysis-ai",
            "marketer": "composable-marketing-ai",
            "marketing": "composable-marketing-ai",
            "classifier": "composable-classifier-ai",
            "classification": "composable-classifier-ai",
            "concierge": "concierge-agent"
        }
        target_persona = None

        for keyword, persona_id_val in persona_mapping.items(): # Renamed persona_id
            if keyword in message_lower:
                target_persona = persona_id_val
                break

        if not target_persona:
            return None, None

        # Extract reason (simple heuristic - everything after "because" or "for")
        reason = None
        if "because" in message_lower:
            reason = message_lower.split("because", 1)[1].strip()
        elif "for" in message_lower:
            reason = message_lower.split("for", 1)[1].strip()
        else:
            # Default reason
            reason = f"assistance with {message_lower}"

        return target_persona, reason

    async def _register_callback(self, conversation_id: str, source_persona: str, target_persona: str, reason: str) -> None:
        """
        Register a callback request.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The persona requesting the callback.
            target_persona: The persona to call back.
            reason: The reason for the callback.
        """
        callback_info = {
            "conversation_id": conversation_id,
            "source_persona": source_persona,
            "target_persona": target_persona,
            "reason": reason,
            "timestamp": time.time(),
            "status": "pending"
        }

        # Store the callback request
        key = f"{conversation_id}:{target_persona}:callbacks"
        if key not in self.callback_registry:
            self.callback_registry[key] = []

        self.callback_registry[key].append(callback_info)
        logger.debug(f"Registered callback request: {key}")

    async def _check_pending_callbacks(self, conversation_id: str, current_persona: str) -> List[Dict[str, Any]]:
        """
        Check for pending callbacks for a persona.

        Args:
            conversation_id: The ID of the conversation.
            current_persona: The current persona.

        Returns:
            A list of pending callback requests.
        """
        key = f"{conversation_id}:{current_persona}:callbacks"
        pending_callbacks = self.callback_registry.get(key, [])

        # Filter for pending callbacks only
        pending_callbacks = [cb for cb in pending_callbacks if cb["status"] == "pending"]

        if pending_callbacks:
            # Update status to "seen"
            for callback_item in pending_callbacks: # Renamed callback
                callback_item["status"] = "seen"

            # Update the registry
            self.callback_registry[key] = pending_callbacks # This should be fine as pending_callbacks is a filtered list of dicts
            logger.debug(f"Found {len(pending_callbacks)} pending callbacks for {key}")

        return pending_callbacks

    def _is_callback_response(self, message: str) -> bool:
        """
        Determine if the message is a response to a callback request.

        Args:
            message: The user message.

        Returns:
            True if this is a callback response, False otherwise.
        """
        # Simple keyword-based detection
        response_keywords = [
            "respond to", "reply to", "answer", "tell", "inform",
            "let know", "send back to", "report to", "update"
        ]

        return any(keyword in message.lower() for keyword in response_keywords)

    def _extract_callback_response(self, message: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract the target persona and response message from a callback response.

        Args:
            message: The user message.

        Returns:
            A tuple containing the target persona and the response message.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()

        # Map keywords to consistent kebab-case persona IDs
        persona_mapping = {
            "analyst": "composable-analysis-ai",
            "analysis": "composable-analysis-ai",
            "marketer": "composable-marketing-ai",
            "marketing": "composable-marketing-ai",
            "classifier": "composable-classifier-ai",
            "classification": "composable-classifier-ai",
            "concierge": "concierge-agent"
        }
        target_persona = None

        for keyword, persona_id_val in persona_mapping.items(): # Renamed persona_id
            if keyword in message_lower:
                target_persona = persona_id_val
                break

        if not target_persona:
            return None, None

        # Extract response message (simple heuristic - everything after "that" or ":")
        response_message_content = None # Renamed response_message
        if "that" in message_lower:
            response_message_content = message_lower.split("that", 1)[1].strip()
        elif ":" in message_lower:
            response_message_content = message_lower.split(":", 1)[1].strip()
        else:
            # Default response
            response_message_content = message_lower

        return target_persona, response_message_content

    async def _send_callback_response(self, conversation_id: str, source_persona: str, target_persona: str, response_content: str) -> None: # Renamed response
        """
        Send a response to a callback request.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The persona sending the response.
            target_persona: The persona to receive the response.
            response_content: The response message.
        """
        response_info = {
            "conversation_id": conversation_id,
            "source_persona": source_persona,
            "target_persona": target_persona,
            "response": response_content, # Use renamed arg
            "timestamp": time.time()
        }

        # Store the response
        key = f"{conversation_id}:{target_persona}:responses"
        if key not in self.message_store:
            self.message_store[key] = []

        self.message_store[key].append(response_info)
        logger.debug(f"Sent callback response: {key}")

        # Update the callback status
        callback_key = f"{conversation_id}:{source_persona}:callbacks"
        if callback_key in self.callback_registry:
            callbacks = self.callback_registry[callback_key]
            for callback_item in callbacks: # Renamed callback
                if callback_item["source_persona"] == target_persona and callback_item["status"] in ["pending", "seen"]:
                    callback_item["status"] = "responded"

            # Update the registry
            self.callback_registry[callback_key] = callbacks

    def _detect_callback_need(self, message: str, context: "AgentProcessingContext") -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Detect if a callback to another persona is needed based on the message content.

        Args:
            message: The user message.
            context: The current AgentProcessingContext.

        Returns:
            A tuple containing a boolean indicating if a callback is needed, the target persona, and the reason.
        """
        # Disabled hardcoded pattern matching - let the concierge agent handle persona recommendations
        # through LLM-based understanding instead of rigid keyword matching
        return False, None, None

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", ["bidirectional_communication"])
