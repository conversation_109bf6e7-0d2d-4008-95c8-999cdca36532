"""
Business Profile Service for managing business profiles and their data sources.

This service handles CRUD operations for business profiles and integrates with
the knowledge graph system for context management.
"""

import logging
import uuid
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..database import BusinessProfile, BusinessProfileDataSource, DataSource
from ..models.business_profile import (
    BusinessProfileCreate,
    BusinessProfileUpdate,
    BusinessProfileResponse,
    BusinessProfileWithDataSources,
    BusinessProfileListResponse,
    BusinessProfileDataSourceAssignmentCreate,
    BusinessProfileDataSourceAssignmentUpdate,
    BusinessProfileDataSourceAssignment
)
from agents.utils.knowledge_graph_service import KnowledgeGraphService
from .business_profile_template_service import get_business_profile_template_service

logger = logging.getLogger(__name__)


class BusinessProfileService:
    """Service for managing business profiles."""
    
    def __init__(self, db: Session):
        self.db = db
        self.kg_service = KnowledgeGraphService()
    
    async def create_profile(self, user_id: int, profile_data: BusinessProfileCreate) -> BusinessProfileResponse:
        """Create a new business profile."""
        try:
            # Try to create knowledge graph for this profile
            kg_id = None
            try:
                kg_id = self.kg_service.create_graph(
                    name=f"Business Profile: {profile_data.name}",
                    description=f"Knowledge graph for {profile_data.name} business profile",
                    metadata={
                        "profile_type": "business",
                        "user_id": user_id,
                        "industry": profile_data.industry,
                        "business_type": profile_data.business_type
                    }
                )
                logger.info(f"Created knowledge graph {kg_id} for business profile: {profile_data.name}")
            except Exception as kg_error:
                logger.warning(f"Failed to create knowledge graph for business profile '{profile_data.name}': {kg_error}")
                logger.info("Business profile will be created without knowledge graph integration")
                kg_id = None
            
            # Create business profile
            profile = BusinessProfile(
                id=str(uuid.uuid4()),
                user_id=user_id,
                name=profile_data.name,
                description=profile_data.description,
                industry=profile_data.industry,
                business_type=profile_data.business_type.value if profile_data.business_type else None,
                business_size=profile_data.business_size.value if profile_data.business_size else None,
                target_audience=profile_data.target_audience,
                products_services=profile_data.products_services,
                marketing_goals=profile_data.marketing_goals,
                competitive_landscape=profile_data.competitive_landscape,
                budget_indicators=profile_data.budget_indicators,
                geographic_focus=profile_data.geographic_focus,
                business_stage=profile_data.business_stage.value if profile_data.business_stage else None,
                knowledge_graph_id=kg_id,
                context_metadata=profile_data.context_metadata or {},
                is_active=False  # Will be set active if it's the first profile
            )
            
            # Check if this is the user's first profile
            existing_profiles = self.db.query(BusinessProfile).filter(
                BusinessProfile.user_id == user_id
            ).count()
            
            if existing_profiles == 0:
                profile.is_active = True
            
            self.db.add(profile)
            self.db.commit()
            self.db.refresh(profile)
            
            logger.info(f"Created business profile {profile.id} for user {user_id}")
            
            return BusinessProfileResponse.model_validate(profile)
            
        except Exception as e:
            logger.error(f"Error creating business profile: {e}")
            self.db.rollback()
            raise
    
    def get_profile(self, profile_id: str, user_id: int) -> Optional[BusinessProfileResponse]:
        """Get a business profile by ID."""
        profile = self.db.query(BusinessProfile).filter(
            and_(
                BusinessProfile.id == profile_id,
                BusinessProfile.user_id == user_id
            )
        ).first()
        
        if not profile:
            return None
        
        return BusinessProfileResponse.model_validate(profile)
    
    def get_profile_with_data_sources(self, profile_id: str, user_id: int) -> Optional[BusinessProfileWithDataSources]:
        """Get a business profile with its data source assignments."""
        profile = self.db.query(BusinessProfile).filter(
            and_(
                BusinessProfile.id == profile_id,
                BusinessProfile.user_id == user_id
            )
        ).first()
        
        if not profile:
            return None
        
        # Get data source assignments
        assignments = self.db.query(BusinessProfileDataSource).filter(
            BusinessProfileDataSource.business_profile_id == profile_id
        ).all()
        
        profile_dict = BusinessProfileResponse.model_validate(profile).model_dump()
        profile_dict['data_source_assignments'] = [
            BusinessProfileDataSourceAssignment.model_validate(assignment) 
            for assignment in assignments
        ]
        
        return BusinessProfileWithDataSources.model_validate(profile_dict)
    
    def list_profiles(self, user_id: int) -> BusinessProfileListResponse:
        """
        List all business profiles for a user with optimized queries.

        This method uses efficient bulk queries to avoid N+1 problems.
        """
        # Get all profiles for the user
        profiles = self.db.query(BusinessProfile).filter(
            BusinessProfile.user_id == user_id
        ).order_by(BusinessProfile.created_at.desc()).all()

        if not profiles:
            return BusinessProfileListResponse(
                profiles=[],
                total=0,
                active_profile_id=None
            )

        # Get profile IDs for bulk queries
        profile_ids = [profile.id for profile in profiles]

        # Bulk query for data source counts
        from sqlalchemy import func
        data_source_counts = dict(
            self.db.query(
                BusinessProfileDataSource.business_profile_id,
                func.count(BusinessProfileDataSource.id)
            ).filter(
                BusinessProfileDataSource.business_profile_id.in_(profile_ids)
            ).group_by(BusinessProfileDataSource.business_profile_id).all()
        )

        # Bulk query for dashboard counts
        dashboard_counts = {}
        try:
            from ..models.dashboard_customization import Dashboard
            dashboard_counts = dict(
                self.db.query(
                    Dashboard.business_profile_id,
                    func.count(Dashboard.id)
                ).filter(
                    Dashboard.business_profile_id.in_(profile_ids)
                ).group_by(Dashboard.business_profile_id).all()
            )
        except Exception as e:
            logger.warning(f"Could not count dashboards: {type(e).__name__}: {e}")

        # Find active profile
        active_profile_id = None
        for profile in profiles:
            if profile.is_active:
                active_profile_id = profile.id
                break

        # Build response objects with validation
        profile_responses = []
        for profile in profiles:
            try:
                # Validate profile has required fields
                if not profile.id or not profile.name:
                    logger.warning(f"Skipping profile with missing required fields: id={profile.id}, name={profile.name}")
                    continue

                profile_dict = BusinessProfileResponse.model_validate(profile).model_dump()
                profile_dict['data_source_count'] = data_source_counts.get(profile.id, 0)
                profile_dict['dashboard_count'] = dashboard_counts.get(profile.id, 0)

                # Ensure all required fields are present and valid
                validated_profile = BusinessProfileResponse.model_validate(profile_dict)
                profile_responses.append(validated_profile)

            except Exception as e:
                logger.error(f"Error processing profile {profile.id}: {e}")
                # Skip invalid profiles rather than failing the entire request
                continue

        return BusinessProfileListResponse(
            profiles=profile_responses,
            total=len(profile_responses),
            active_profile_id=active_profile_id
        )
    
    def update_profile(self, profile_id: str, user_id: int, update_data: BusinessProfileUpdate) -> Optional[BusinessProfileResponse]:
        """Update a business profile."""
        profile = self.db.query(BusinessProfile).filter(
            and_(
                BusinessProfile.id == profile_id,
                BusinessProfile.user_id == user_id
            )
        ).first()
        
        if not profile:
            return None
        
        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            if hasattr(profile, field):
                if field in ['business_type', 'business_size', 'business_stage'] and value:
                    setattr(profile, field, value.value if hasattr(value, 'value') else value)
                else:
                    setattr(profile, field, value)
        
        self.db.commit()
        self.db.refresh(profile)
        
        logger.info(f"Updated business profile {profile_id}")
        
        return BusinessProfileResponse.model_validate(profile)
    
    async def delete_profile(self, profile_id: str, user_id: int) -> bool:
        """
        Delete a business profile and all associated data.

        This method ensures proper cleanup of all related resources including
        knowledge graphs, data source assignments, and dashboards.
        """
        try:
            # Get profile with lock to prevent concurrent modifications
            profile = self.db.query(BusinessProfile).filter(
                and_(
                    BusinessProfile.id == profile_id,
                    BusinessProfile.user_id == user_id
                )
            ).with_for_update().first()

            if not profile:
                logger.warning(f"Profile {profile_id} not found or access denied for user {user_id}")
                return False

            # Delete knowledge graph if exists
            if profile.knowledge_graph_id:
                try:
                    success = self.kg_service.delete_graph(profile.knowledge_graph_id)
                    if success:
                        logger.info(f"Successfully deleted knowledge graph {profile.knowledge_graph_id}")
                    else:
                        logger.warning(f"Knowledge graph {profile.knowledge_graph_id} deletion returned False")
                except Exception as kg_error:
                    logger.error(f"Failed to delete knowledge graph {profile.knowledge_graph_id}: {type(kg_error).__name__}")
                    # Continue with profile deletion even if KG deletion fails

            # Delete the profile (cascading deletes will handle related data)
            self.db.delete(profile)
            self.db.commit()

            logger.info(f"Deleted business profile {profile_id} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting business profile {profile_id} for user {user_id}: {type(e).__name__}")
            self.db.rollback()
            return False
    
    def switch_active_profile(self, user_id: int, profile_id: str) -> bool:
        """
        Switch the active business profile for a user.

        This method uses a single atomic transaction to prevent race conditions
        and ensures data consistency.
        """
        try:
            # First verify the profile exists and belongs to the user
            target_profile = self.db.query(BusinessProfile).filter(
                and_(
                    BusinessProfile.id == profile_id,
                    BusinessProfile.user_id == user_id
                )
            ).with_for_update().first()  # Lock the row to prevent race conditions

            if not target_profile:
                logger.warning(f"Profile {profile_id} not found or access denied for user {user_id}")
                return False

            # Deactivate all profiles for the user in a single query
            self.db.query(BusinessProfile).filter(
                BusinessProfile.user_id == user_id
            ).update({"is_active": False}, synchronize_session=False)

            # Activate the selected profile
            target_profile.is_active = True

            # Commit the transaction
            self.db.commit()

            logger.info(f"Switched active profile to {profile_id} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error switching active profile for user {user_id}: {type(e).__name__}")
            self.db.rollback()
            return False
    
    def get_active_profile(self, user_id: int) -> Optional[BusinessProfileResponse]:
        """Get the active business profile for a user."""
        profile = self.db.query(BusinessProfile).filter(
            and_(
                BusinessProfile.user_id == user_id,
                BusinessProfile.is_active == True
            )
        ).first()

        if not profile:
            return None

        return BusinessProfileResponse.model_validate(profile)

    def assign_data_source(self, profile_id: str, user_id: int, assignment_data: BusinessProfileDataSourceAssignmentCreate) -> Optional[BusinessProfileDataSourceAssignment]:
        """Assign a data source to a business profile."""
        # Verify profile ownership
        profile = self.db.query(BusinessProfile).filter(
            and_(
                BusinessProfile.id == profile_id,
                BusinessProfile.user_id == user_id
            )
        ).first()

        if not profile:
            return None

        # Verify data source ownership
        data_source = self.db.query(DataSource).filter(
            and_(
                DataSource.id == assignment_data.data_source_id,
                DataSource.user_id == user_id
            )
        ).first()

        if not data_source:
            return None

        # Check if assignment already exists
        existing = self.db.query(BusinessProfileDataSource).filter(
            and_(
                BusinessProfileDataSource.business_profile_id == profile_id,
                BusinessProfileDataSource.data_source_id == assignment_data.data_source_id
            )
        ).first()

        if existing:
            return BusinessProfileDataSourceAssignment.model_validate(existing)

        # Create new assignment
        assignment = BusinessProfileDataSource(
            id=str(uuid.uuid4()),
            business_profile_id=profile_id,
            data_source_id=assignment_data.data_source_id,
            role=assignment_data.role.value if assignment_data.role else None,
            priority=assignment_data.priority,
            is_active=assignment_data.is_active
        )

        self.db.add(assignment)
        self.db.commit()
        self.db.refresh(assignment)

        logger.info(f"Assigned data source {assignment_data.data_source_id} to profile {profile_id}")

        return BusinessProfileDataSourceAssignment.model_validate(assignment)

    def unassign_data_source(self, profile_id: str, user_id: int, data_source_id: str) -> bool:
        """Remove a data source assignment from a business profile."""
        # Verify profile ownership
        profile = self.db.query(BusinessProfile).filter(
            and_(
                BusinessProfile.id == profile_id,
                BusinessProfile.user_id == user_id
            )
        ).first()

        if not profile:
            return False

        # Find and delete assignment
        assignment = self.db.query(BusinessProfileDataSource).filter(
            and_(
                BusinessProfileDataSource.business_profile_id == profile_id,
                BusinessProfileDataSource.data_source_id == data_source_id
            )
        ).first()

        if not assignment:
            return False

        self.db.delete(assignment)
        self.db.commit()

        logger.info(f"Unassigned data source {data_source_id} from profile {profile_id}")
        return True

    def update_data_source_assignment(self, profile_id: str, user_id: int, data_source_id: str, update_data: BusinessProfileDataSourceAssignmentUpdate) -> Optional[BusinessProfileDataSourceAssignment]:
        """Update a data source assignment."""
        # Verify profile ownership
        profile = self.db.query(BusinessProfile).filter(
            and_(
                BusinessProfile.id == profile_id,
                BusinessProfile.user_id == user_id
            )
        ).first()

        if not profile:
            return None

        # Find assignment
        assignment = self.db.query(BusinessProfileDataSource).filter(
            and_(
                BusinessProfileDataSource.business_profile_id == profile_id,
                BusinessProfileDataSource.data_source_id == data_source_id
            )
        ).first()

        if not assignment:
            return None

        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            if hasattr(assignment, field):
                if field == 'role' and value:
                    setattr(assignment, field, value.value if hasattr(value, 'value') else value)
                else:
                    setattr(assignment, field, value)

        self.db.commit()
        self.db.refresh(assignment)

        logger.info(f"Updated data source assignment {data_source_id} for profile {profile_id}")

        return BusinessProfileDataSourceAssignment.model_validate(assignment)

    async def create_default_profile(self, user_id: int, industry: str = "General") -> BusinessProfileResponse:
        """
        Create a default/guest profile for a user.

        Args:
            user_id: User ID
            industry: Industry for template selection

        Returns:
            Created default profile
        """
        try:
            # Get template service
            template_service = get_business_profile_template_service()
            template = template_service.get_template(industry)

            # Create default profile data
            profile_data = BusinessProfileCreate(
                name=f"Default Profile",
                description="Default business profile for getting started with Datagenius",
                industry=industry,
                business_type=template.get('business_type', 'B2B'),
                business_size=template.get('business_size', 'small'),
                target_audience=template.get('target_audience', 'Business professionals'),
                products_services=template.get('products_services', 'Professional services'),
                marketing_goals=template.get('marketing_goals', 'Build brand awareness'),
                competitive_landscape=template.get('competitive_landscape', 'Competitive market'),
                budget_indicators=template.get('budget_indicators', '$5K-25K monthly'),
                geographic_focus=template.get('geographic_focus', 'Regional'),
                business_stage=template.get('business_stage', 'growth'),
                platforms=template.get('platforms', 'LinkedIn, networking events'),
                context_metadata={
                    "is_default_profile": True,
                    "created_from_template": industry,
                    "needs_customization": True
                }
            )

            # Create the profile
            profile = await self.create_profile(user_id, profile_data)

            logger.info(f"Created default profile for user {user_id} with industry {industry}")
            return profile

        except Exception as e:
            logger.error(f"Error creating default profile for user {user_id}: {e}")
            raise

    def is_default_profile(self, profile: BusinessProfile) -> bool:
        """
        Check if a profile is a default/guest profile.

        Args:
            profile: Business profile to check

        Returns:
            True if it's a default profile
        """
        if not profile.context_metadata:
            return False

        return profile.context_metadata.get("is_default_profile", False)

    def needs_customization(self, profile: BusinessProfile) -> bool:
        """
        Check if a profile needs customization.

        Args:
            profile: Business profile to check

        Returns:
            True if the profile needs customization
        """
        if not profile.context_metadata:
            return True

        return profile.context_metadata.get("needs_customization", True)


def get_business_profile_service(db: Session) -> BusinessProfileService:
    """Get business profile service instance."""
    return BusinessProfileService(db)
