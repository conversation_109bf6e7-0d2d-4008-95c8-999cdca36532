/**
 * Test suite for the hierarchical dashboard data source architecture
 * Validates that data sources are properly managed at the dashboard level
 * and inherited by widgets.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  Dashboard, 
  DashboardDataSource, 
  DashboardDataSourceCreate,
  WidgetDataSourceConfig,
  WidgetCreate 
} from '@/types/dashboard-customization';

describe('Hierarchical Dashboard Data Source Architecture', () => {
  let mockDashboard: Dashboard;
  let mockDataSources: DashboardDataSource[];

  beforeEach(() => {
    // Setup mock dashboard with data sources
    mockDataSources = [
      {
        id: 'ds-1',
        name: 'Sales Database',
        type: 'database',
        description: 'Main sales database connection',
        connectionConfig: {
          host: 'localhost',
          database: 'sales_db',
          table: 'transactions'
        },
        isActive: true,
        refreshInterval: 300,
        lastUpdated: '2024-01-01T00:00:00Z',
        recordCount: 1500
      },
      {
        id: 'ds-2',
        name: 'Analytics API',
        type: 'api',
        description: 'External analytics API',
        connectionConfig: {
          endpoint: 'https://api.analytics.com/v1',
          apiKey: 'test-key'
        },
        isActive: true,
        refreshInterval: 600,
        lastUpdated: '2024-01-01T00:00:00Z',
        recordCount: 850
      },
      {
        id: 'ds-3',
        name: 'Inactive Source',
        type: 'file',
        description: 'Inactive data source',
        connectionConfig: {},
        isActive: false,
        refreshInterval: 300,
        lastUpdated: '2024-01-01T00:00:00Z'
      }
    ];

    mockDashboard = {
      id: 'dashboard-1',
      name: 'Test Dashboard',
      description: 'Test dashboard for data source architecture',
      is_default: false,
      is_public: false,
      layout_config: {},
      theme_config: {},
      refresh_interval: 300,
      tags: ['test'],
      data_sources: mockDataSources,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      section_count: 1,
      widget_count: 2
    };
  });

  describe('Dashboard Data Source Management', () => {
    it('should have data sources configured at dashboard level', () => {
      expect(mockDashboard.data_sources).toBeDefined();
      expect(mockDashboard.data_sources).toHaveLength(3);
      expect(mockDashboard.data_sources![0].name).toBe('Sales Database');
    });

    it('should filter active data sources for widget selection', () => {
      const activeDataSources = mockDashboard.data_sources!.filter(ds => ds.isActive);
      expect(activeDataSources).toHaveLength(2);
      expect(activeDataSources.map(ds => ds.name)).toEqual(['Sales Database', 'Analytics API']);
    });

    it('should validate data source creation schema', () => {
      const newDataSource: DashboardDataSourceCreate = {
        name: 'New CSV File',
        type: 'file',
        description: 'Uploaded CSV file',
        connectionConfig: {
          fileName: 'data.csv',
          delimiter: ','
        },
        refreshInterval: 300
      };

      expect(newDataSource.name).toBe('New CSV File');
      expect(newDataSource.type).toBe('file');
      expect(newDataSource.connectionConfig.fileName).toBe('data.csv');
    });
  });

  describe('Widget Data Source Configuration', () => {
    it('should reference dashboard data sources in widget config', () => {
      const widgetDataConfig: WidgetDataSourceConfig = {
        dashboardDataSourceId: 'ds-1',
        query: 'SELECT * FROM transactions WHERE date > ?',
        filters: {
          date_range: '30d',
          status: 'completed'
        },
        aggregation: 'sum',
        group_by: ['category'],
        sort_by: 'amount',
        limit: 100
      };

      expect(widgetDataConfig.dashboardDataSourceId).toBe('ds-1');
      expect(widgetDataConfig.query).toContain('SELECT');
      expect(widgetDataConfig.filters?.date_range).toBe('30d');
      expect(widgetDataConfig.limit).toBe(100);
    });

    it('should create widget with dashboard data source reference', () => {
      const widgetCreate: WidgetCreate = {
        section_id: 'section-1',
        title: 'Sales Chart',
        widget_type: 'chart' as any,
        data_config: {
          dashboardDataSourceId: 'ds-1',
          query: 'SELECT category, SUM(amount) FROM transactions GROUP BY category',
          aggregation: 'sum',
          group_by: ['category'],
          limit: 10
        },
        position_config: {
          x: 0,
          y: 0,
          w: 6,
          h: 4
        },
        refresh_interval: 300
      };

      expect(widgetCreate.data_config?.dashboardDataSourceId).toBe('ds-1');
      expect(widgetCreate.title).toBe('Sales Chart');
    });

    it('should not allow widgets to configure new data sources independently', () => {
      // This test ensures that widgets can only reference existing dashboard data sources
      const availableDataSourceIds = mockDashboard.data_sources!
        .filter(ds => ds.isActive)
        .map(ds => ds.id);

      const validWidgetConfig: WidgetDataSourceConfig = {
        dashboardDataSourceId: 'ds-1', // Valid - exists in dashboard
        limit: 50
      };

      const invalidWidgetConfig = {
        dashboardDataSourceId: 'ds-999', // Invalid - doesn't exist in dashboard
        limit: 50
      };

      expect(availableDataSourceIds).toContain(validWidgetConfig.dashboardDataSourceId);
      expect(availableDataSourceIds).not.toContain(invalidWidgetConfig.dashboardDataSourceId);
    });
  });

  describe('Data Source Inheritance and Consistency', () => {
    it('should ensure widgets inherit from dashboard data sources', () => {
      const dashboardDataSourceIds = mockDashboard.data_sources!.map(ds => ds.id);
      
      // Simulate multiple widgets using dashboard data sources
      const widget1Config: WidgetDataSourceConfig = {
        dashboardDataSourceId: 'ds-1',
        aggregation: 'sum'
      };
      
      const widget2Config: WidgetDataSourceConfig = {
        dashboardDataSourceId: 'ds-2',
        aggregation: 'avg'
      };

      expect(dashboardDataSourceIds).toContain(widget1Config.dashboardDataSourceId);
      expect(dashboardDataSourceIds).toContain(widget2Config.dashboardDataSourceId);
    });

    it('should maintain data source consistency across sections', () => {
      // Multiple sections can use the same dashboard data sources
      const section1Widget: WidgetDataSourceConfig = {
        dashboardDataSourceId: 'ds-1',
        query: 'SELECT * FROM transactions WHERE category = "electronics"'
      };

      const section2Widget: WidgetDataSourceConfig = {
        dashboardDataSourceId: 'ds-1',
        query: 'SELECT * FROM transactions WHERE category = "clothing"'
      };

      // Both widgets reference the same dashboard data source but with different queries
      expect(section1Widget.dashboardDataSourceId).toBe(section2Widget.dashboardDataSourceId);
      expect(section1Widget.query).not.toBe(section2Widget.query);
    });

    it('should support multiple data sources per dashboard', () => {
      expect(mockDashboard.data_sources).toHaveLength(3);
      
      const dataSourceTypes = mockDashboard.data_sources!.map(ds => ds.type);
      expect(dataSourceTypes).toContain('database');
      expect(dataSourceTypes).toContain('api');
      expect(dataSourceTypes).toContain('file');
    });
  });

  describe('Data Source Configuration Validation', () => {
    it('should validate connection configurations by type', () => {
      const databaseSource = mockDashboard.data_sources!.find(ds => ds.type === 'database');
      const apiSource = mockDashboard.data_sources!.find(ds => ds.type === 'api');

      expect(databaseSource?.connectionConfig.host).toBeDefined();
      expect(databaseSource?.connectionConfig.database).toBeDefined();
      
      expect(apiSource?.connectionConfig.endpoint).toBeDefined();
      expect(apiSource?.connectionConfig.apiKey).toBeDefined();
    });

    it('should handle refresh intervals appropriately', () => {
      const refreshIntervals = mockDashboard.data_sources!.map(ds => ds.refreshInterval);
      
      // All refresh intervals should be within valid range (30-3600 seconds)
      refreshIntervals.forEach(interval => {
        expect(interval).toBeGreaterThanOrEqual(30);
        expect(interval).toBeLessThanOrEqual(3600);
      });
    });
  });
});
