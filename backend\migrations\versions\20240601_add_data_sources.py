"""Add data sources table

Revision ID: 20240601_add_data_sources
Revises: 20250422_update_hfmodel_storage
Create Date: 2024-06-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20240601_add_data_sources'
# Make this a standalone migration
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if data_sources table exists
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)
    tables = inspector.get_table_names()

    # Only create data_sources table if it doesn't exist
    if 'data_sources' not in tables:
        # Create data_sources table
        op.create_table('data_sources',
            sa.Column('id', sa.String(length=36), nullable=False),
            sa.Column('name', sa.String(length=255), nullable=False),
            sa.Column('type', sa.String(length=20), nullable=False),
            sa.Column('description', sa.Text(), nullable=True),
            sa.Column('is_active', sa.<PERSON>(), nullable=True),
            sa.Column('source_metadata', sa.JSON(), nullable=True),
            sa.Column('user_id', sa.Integer(), nullable=False),
            sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
            sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
            sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_data_sources_id'), 'data_sources', ['id'], unique=False)


def downgrade() -> None:
    # Drop data_sources table
    op.drop_index(op.f('ix_data_sources_id'), table_name='data_sources')
    op.drop_table('data_sources')
