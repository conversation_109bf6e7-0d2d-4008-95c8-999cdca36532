"""
User Knowledge Graph MCP tool using mem0ai for the Datagenius backend.

This tool provides the concierge agent with access to user-specific knowledge graphs
stored in mem0ai, enabling personalized experiences and user detail retention.
"""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base import BaseMCPTool
from ...utils.memory_service import MemoryService
from ...utils.knowledge_graph_service import KnowledgeGraphService

logger = logging.getLogger(__name__)


class UserKnowledgeGraphTool(BaseMCPTool):
    """
    MCP tool for accessing and managing user knowledge graphs using mem0ai.

    This tool enables the concierge agent to:
    - Retrieve user preferences and details
    - Store new information about users
    - Query user interaction history
    - Access user's data patterns and interests
    - Maintain personalized context across sessions
    """

    def __init__(self):
        """Initialize the user knowledge graph tool."""
        super().__init__()
        self.name = "get_user_knowledge_graph"
        self.description = "Access user knowledge graph and memory for personalized interactions"

        # Initialize services
        self.memory_service = None
        self.kg_service = None

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary
        """
        try:
            # Initialize memory and knowledge graph services
            self.memory_service = MemoryService()
            self.kg_service = KnowledgeGraphService()

            logger.info("UserKnowledgeGraphTool initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize UserKnowledgeGraphTool: {e}")
            raise

    def get_schema(self) -> Dict[str, Any]:
        """
        Get the JSON schema for this tool.

        Returns:
            JSON schema for the tool parameters
        """
        return {
            "type": "object",
            "properties": {
                "user_id": {
                    "type": "string",
                    "description": "The ID of the user to query knowledge graph for"
                },
                "query_type": {
                    "type": "string",
                    "enum": [
                        "user_profile",
                        "preferences",
                        "interaction_history",
                        "data_patterns",
                        "interests",
                        "goals",
                        "context_summary",
                        "add_memory",
                        "search_memories"
                    ],
                    "description": "Type of knowledge graph query to perform"
                },
                "query": {
                    "type": "string",
                    "description": "Specific query or search term (for search_memories and add_memory)"
                },
                "memory_content": {
                    "type": "string",
                    "description": "Content to add to user memory (for add_memory)"
                },
                "metadata": {
                    "type": "object",
                    "description": "Additional metadata for memory operations",
                    "properties": {
                        "category": {"type": "string"},
                        "importance": {"type": "number"},
                        "tags": {"type": "array", "items": {"type": "string"}}
                    }
                },
                "limit": {
                    "type": "integer",
                    "default": 10,
                    "description": "Maximum number of results to return"
                }
            },
            "required": ["user_id", "query_type"]
        }

    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the user knowledge graph query.

        Args:
            params: Parameters for the knowledge graph query

        Returns:
            Dictionary containing query results and metadata
        """
        try:
            user_id = params.get("user_id")
            query_type = params.get("query_type")
            query = params.get("query", "")
            memory_content = params.get("memory_content", "")
            metadata = params.get("metadata", {})
            limit = params.get("limit", 10)

            if not user_id:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "User ID is required"}]
                }

            logger.info(f"Executing user knowledge graph query: {query_type} for user {user_id}")

            # Route to appropriate query handler
            if query_type == "user_profile":
                result = await self._get_user_profile(user_id, limit)
            elif query_type == "preferences":
                result = await self._get_user_preferences(user_id, limit)
            elif query_type == "interaction_history":
                result = await self._get_interaction_history(user_id, limit)
            elif query_type == "data_patterns":
                result = await self._get_data_patterns(user_id, limit)
            elif query_type == "interests":
                result = await self._get_user_interests(user_id, limit)
            elif query_type == "goals":
                result = await self._get_user_goals(user_id, limit)
            elif query_type == "context_summary":
                result = await self._get_context_summary(user_id, limit)
            elif query_type == "add_memory":
                result = await self._add_user_memory(user_id, memory_content, metadata)
            elif query_type == "search_memories":
                result = await self._search_user_memories(user_id, query, limit)
            else:
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Unknown query type: {query_type}"}]
                }

            return {
                "isError": False,
                "content": [{"type": "text", "text": json.dumps(result, indent=2)}],
                "metadata": {
                    "user_id": user_id,
                    "query_type": query_type,
                    "timestamp": datetime.now().isoformat(),
                    "results_count": len(result.get("data", {}).get("results", []))
                }
            }

        except Exception as e:
            logger.error(f"Error executing user knowledge graph query: {e}")
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Error: {str(e)}"}]
            }

    async def _get_user_profile(self, user_id: str, limit: int) -> Dict[str, Any]:
        """Get comprehensive user profile information."""
        try:
            # Search for profile-related memories
            profile_memories = self.memory_service.search_memories(
                query="user profile personal information name preferences",
                user_id=user_id,
                limit=limit,
                metadata_filter={"category": "profile"}
            )

            return {
                "description": f"User profile information for {user_id}",
                "data": {
                    "user_id": user_id,
                    "profile_memories": profile_memories.get("results", []),
                    "total_memories": len(profile_memories.get("results", [])),
                    "last_updated": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error getting user profile: {e}")
            return {"description": "Error retrieving user profile", "data": {"error": str(e)}}

    async def _get_user_preferences(self, user_id: str, limit: int) -> Dict[str, Any]:
        """Get user preferences and settings."""
        try:
            # Search for preference-related memories
            preference_memories = self.memory_service.search_memories(
                query="preferences settings likes dislikes favorite",
                user_id=user_id,
                limit=limit,
                metadata_filter={"category": "preference"}
            )

            return {
                "description": f"User preferences for {user_id}",
                "data": {
                    "user_id": user_id,
                    "preferences": preference_memories.get("results", []),
                    "total_preferences": len(preference_memories.get("results", [])),
                    "last_updated": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error getting user preferences: {e}")
            return {"description": "Error retrieving user preferences", "data": {"error": str(e)}}

    async def _get_interaction_history(self, user_id: str, limit: int) -> Dict[str, Any]:
        """Get user interaction history and patterns."""
        try:
            # Search for interaction-related memories
            interaction_memories = self.memory_service.search_memories(
                query="interaction conversation session activity usage",
                user_id=user_id,
                limit=limit,
                metadata_filter={"category": "interaction"}
            )

            return {
                "description": f"Interaction history for {user_id}",
                "data": {
                    "user_id": user_id,
                    "interactions": interaction_memories.get("results", []),
                    "total_interactions": len(interaction_memories.get("results", [])),
                    "last_updated": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error getting interaction history: {e}")
            return {"description": "Error retrieving interaction history", "data": {"error": str(e)}}

    async def _get_data_patterns(self, user_id: str, limit: int) -> Dict[str, Any]:
        """Get user data usage patterns and analytics preferences."""
        try:
            # Search for data-related memories
            data_memories = self.memory_service.search_memories(
                query="data analysis patterns usage analytics files datasets",
                user_id=user_id,
                limit=limit,
                metadata_filter={"category": "data_pattern"}
            )

            return {
                "description": f"Data patterns for {user_id}",
                "data": {
                    "user_id": user_id,
                    "data_patterns": data_memories.get("results", []),
                    "total_patterns": len(data_memories.get("results", [])),
                    "last_updated": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error getting data patterns: {e}")
            return {"description": "Error retrieving data patterns", "data": {"error": str(e)}}

    async def _get_user_interests(self, user_id: str, limit: int) -> Dict[str, Any]:
        """Get user interests and topics of focus."""
        try:
            # Search for interest-related memories
            interest_memories = self.memory_service.search_memories(
                query="interests topics focus areas expertise hobbies",
                user_id=user_id,
                limit=limit,
                metadata_filter={"category": "interest"}
            )

            return {
                "description": f"User interests for {user_id}",
                "data": {
                    "user_id": user_id,
                    "interests": interest_memories.get("results", []),
                    "total_interests": len(interest_memories.get("results", [])),
                    "last_updated": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error getting user interests: {e}")
            return {"description": "Error retrieving user interests", "data": {"error": str(e)}}

    async def _get_user_goals(self, user_id: str, limit: int) -> Dict[str, Any]:
        """Get user goals and objectives."""
        try:
            # Search for goal-related memories
            goal_memories = self.memory_service.search_memories(
                query="goals objectives targets aims plans projects",
                user_id=user_id,
                limit=limit,
                metadata_filter={"category": "goal"}
            )

            return {
                "description": f"User goals for {user_id}",
                "data": {
                    "user_id": user_id,
                    "goals": goal_memories.get("results", []),
                    "total_goals": len(goal_memories.get("results", [])),
                    "last_updated": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error getting user goals: {e}")
            return {"description": "Error retrieving user goals", "data": {"error": str(e)}}

    async def _get_context_summary(self, user_id: str, limit: int) -> Dict[str, Any]:
        """Get a comprehensive context summary for the user."""
        try:
            # Get recent memories across all categories
            recent_memories = self.memory_service.search_memories(
                query="recent context summary overview",
                user_id=user_id,
                limit=limit
            )

            # Categorize memories
            categorized = {
                "profile": [],
                "preferences": [],
                "interactions": [],
                "data_patterns": [],
                "interests": [],
                "goals": [],
                "other": []
            }

            for memory in recent_memories.get("results", []):
                category = memory.get("metadata", {}).get("category", "other")
                if category in categorized:
                    categorized[category].append(memory)
                else:
                    categorized["other"].append(memory)

            return {
                "description": f"Context summary for {user_id}",
                "data": {
                    "user_id": user_id,
                    "summary": categorized,
                    "total_memories": len(recent_memories.get("results", [])),
                    "categories": {k: len(v) for k, v in categorized.items()},
                    "last_updated": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error getting context summary: {e}")
            return {"description": "Error retrieving context summary", "data": {"error": str(e)}}

    async def _add_user_memory(self, user_id: str, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Add a new memory for the user."""
        try:
            if not content:
                return {"description": "Error: Memory content is required", "data": {"error": "No content provided"}}

            # Add timestamp and user context to metadata
            enhanced_metadata = {
                "timestamp": datetime.now().isoformat(),
                "source": "concierge_agent",
                **metadata
            }

            # Add memory using mem0ai
            result = self.memory_service.add_memory(
                content=content,
                user_id=user_id,
                metadata=enhanced_metadata
            )

            return {
                "description": f"Added memory for {user_id}",
                "data": {
                    "user_id": user_id,
                    "memory_id": result.get("id") if result else None,
                    "content": content,
                    "metadata": enhanced_metadata,
                    "success": bool(result),
                    "timestamp": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error adding user memory: {e}")
            return {"description": "Error adding user memory", "data": {"error": str(e)}}

    async def _search_user_memories(self, user_id: str, query: str, limit: int) -> Dict[str, Any]:
        """Search user memories with a specific query."""
        try:
            if not query:
                return {"description": "Error: Search query is required", "data": {"error": "No query provided"}}

            # Search memories using mem0ai
            results = self.memory_service.search_memories(
                query=query,
                user_id=user_id,
                limit=limit
            )

            return {
                "description": f"Search results for '{query}' for {user_id}",
                "data": {
                    "user_id": user_id,
                    "query": query,
                    "results": results.get("results", []),
                    "total_results": len(results.get("results", [])),
                    "timestamp": datetime.now().isoformat()
                }
            }
        except Exception as e:
            logger.error(f"Error searching user memories: {e}")
            return {"description": "Error searching user memories", "data": {"error": str(e)}}
