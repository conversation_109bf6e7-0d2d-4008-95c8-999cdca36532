import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Target, 
  Users, 
  Zap, 
  FileText, 
  Mail, 
  PenTool,
  Search,
  BarChart3,
  Eye,
  ArrowRight,
  ArrowLeft,
  CheckCircle
} from 'lucide-react';

interface GuidedWorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectAction: (actionType: string, config?: any) => void;
}

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  actions: {
    id: string;
    title: string;
    description: string;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    timeEstimate: string;
    actionType: string;
  }[];
}

const workflowSteps: WorkflowStep[] = [
  {
    id: 'strategy',
    title: 'Strategy & Planning',
    description: 'Build the foundation of your marketing success',
    icon: TrendingUp,
    actions: [
      {
        id: 'marketing_strategy',
        title: 'Marketing Strategy',
        description: 'Comprehensive plan for your marketing goals',
        difficulty: 'intermediate',
        timeEstimate: '15-20 min',
        actionType: 'marketing_strategy'
      },
      {
        id: 'competitor_analysis',
        title: 'Competitor Analysis',
        description: 'Understand your competitive landscape',
        difficulty: 'beginner',
        timeEstimate: '10-15 min',
        actionType: 'competitor_analysis'
      },
      {
        id: 'audience_research',
        title: 'Audience Research',
        description: 'Define and understand your target customers',
        difficulty: 'beginner',
        timeEstimate: '10-15 min',
        actionType: 'audience_research'
      }
    ]
  },
  {
    id: 'content',
    title: 'Content Creation',
    description: 'Create engaging content that converts',
    icon: PenTool,
    actions: [
      {
        id: 'blog_content',
        title: 'Blog Posts',
        description: 'SEO-optimized articles that drive traffic',
        difficulty: 'beginner',
        timeEstimate: '10-15 min',
        actionType: 'blog_content'
      },
      {
        id: 'social_media_content',
        title: 'Social Media Content',
        description: 'Engaging posts for all platforms',
        difficulty: 'beginner',
        timeEstimate: '5-10 min',
        actionType: 'social_media_content'
      },
      {
        id: 'email_marketing',
        title: 'Email Campaigns',
        description: 'High-converting email sequences',
        difficulty: 'intermediate',
        timeEstimate: '15-20 min',
        actionType: 'email_marketing'
      },
      {
        id: 'ad_copy',
        title: 'Ad Copy',
        description: 'Persuasive advertising copy',
        difficulty: 'intermediate',
        timeEstimate: '10-15 min',
        actionType: 'ad_copy'
      }
    ]
  },
  {
    id: 'campaigns',
    title: 'Campaign Execution',
    description: 'Launch targeted marketing campaigns',
    icon: Target,
    actions: [
      {
        id: 'campaign_strategy',
        title: 'Campaign Planning',
        description: 'Multi-channel campaign strategies',
        difficulty: 'advanced',
        timeEstimate: '20-30 min',
        actionType: 'campaign_strategy'
      },
      {
        id: 'seo_optimization',
        title: 'SEO Strategy',
        description: 'Improve your search rankings',
        difficulty: 'intermediate',
        timeEstimate: '15-20 min',
        actionType: 'seo_optimization'
      },
      {
        id: 'press_release',
        title: 'Press Release',
        description: 'Professional PR announcements',
        difficulty: 'intermediate',
        timeEstimate: '10-15 min',
        actionType: 'press_release'
      }
    ]
  }
];

export const GuidedWorkflowModal: React.FC<GuidedWorkflowModalProps> = ({
  isOpen,
  onClose,
  onSelectAction
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedActions, setSelectedActions] = useState<string[]>([]);

  const handleActionSelect = (actionType: string) => {
    onSelectAction(actionType);
    onClose();
  };

  const handleStepSelect = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const currentWorkflowStep = workflowSteps[currentStep];
  const progress = ((currentStep + 1) / workflowSteps.length) * 100;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-brand-600" />
            Marketing Workflow Guide
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>Step {currentStep + 1} of {workflowSteps.length}</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Step Navigation */}
          <div className="flex gap-2 overflow-x-auto pb-2">
            {workflowSteps.map((step, index) => {
              const StepIcon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = selectedActions.some(action => 
                step.actions.some(stepAction => stepAction.actionType === action)
              );

              return (
                <Button
                  key={step.id}
                  variant={isActive ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleStepSelect(index)}
                  className={`flex items-center gap-2 whitespace-nowrap ${
                    isCompleted ? 'border-green-500 bg-green-50' : ''
                  }`}
                >
                  {isCompleted ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <StepIcon className="h-4 w-4" />
                  )}
                  {step.title}
                </Button>
              );
            })}
          </div>

          {/* Current Step Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <currentWorkflowStep.icon className="h-5 w-5 text-brand-600" />
                {currentWorkflowStep.title}
              </CardTitle>
              <CardDescription>
                {currentWorkflowStep.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {currentWorkflowStep.actions.map((action) => (
                  <Card 
                    key={action.id} 
                    className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-brand-300"
                    onClick={() => handleActionSelect(action.actionType)}
                  >
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <h4 className="font-semibold text-gray-900">
                            {action.title}
                          </h4>
                          <Badge className={getDifficultyColor(action.difficulty)}>
                            {action.difficulty}
                          </Badge>
                        </div>
                        
                        <p className="text-sm text-gray-600">
                          {action.description}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            ⏱️ {action.timeEstimate}
                          </span>
                          <Button size="sm" variant="ghost">
                            Get Started
                            <ArrowRight className="h-3 w-3 ml-1" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
              disabled={currentStep === 0}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                Close Guide
              </Button>
              
              <Button
                onClick={() => setCurrentStep(Math.min(workflowSteps.length - 1, currentStep + 1))}
                disabled={currentStep === workflowSteps.length - 1}
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>

          {/* Quick Tips */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Eye className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">
                    💡 Pro Tip
                  </h4>
                  <p className="text-sm text-blue-800">
                    Start with strategy and research before creating content. 
                    Understanding your audience and competition will make your content more effective.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
