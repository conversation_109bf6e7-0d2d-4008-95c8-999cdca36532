"""
Business Profile Context Component for Phase 2 Architecture.

This component automatically loads business profile context for agents,
providing seamless access to business data without manual attachment.
"""

import logging
import asyncio
import time
from typing import Dict, Any, List, Optional
from threading import RLock
from ..base_component import BaseAgentComponent, AgentContext
from agents.utils.vector_service import VectorService
from agents.utils.memory_service import MemoryService

logger = logging.getLogger(__name__)


class BusinessProfileContextComponent(BaseAgentComponent):
    """
    Business profile context component that automatically loads business profile
    data and makes it available to all agents without manual data attachment.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("business_profile_context", config)
        
        # Configuration
        self.auto_load_profile = config.get("auto_load_profile", True)
        self.include_data_sources = config.get("include_data_sources", True)
        self.include_knowledge_graph = config.get("include_knowledge_graph", True)
        self.max_data_sources = config.get("max_data_sources", 10)
        self.context_priority_order = config.get("context_priority_order", [
            "business_description", "sales_data", "marketing_materials", 
            "financial_data", "customer_data", "product_data"
        ])
        
        # Services
        self.vector_service = VectorService()
        self.memory_service = MemoryService()
        
        # Thread-safe cache for business profile data
        self.profile_cache = {}
        self.cache_ttl = config.get("cache_ttl", 300)  # 5 minutes
        self.cache_lock = RLock()  # Thread-safe cache access
    
    async def _initialize_component(self) -> None:
        """Initialize the business profile context component."""
        self.logger.info("Initializing BusinessProfileContextComponent")
        
        try:
            # Initialize vector and memory services
            await self.vector_service.initialize()
            await self.memory_service.initialize()
            self.logger.info("Business profile context services initialized")
        except Exception as e:
            self.logger.warning(f"Failed to initialize some services: {e}")
    
    def get_required_fields(self) -> List[str]:
        """Return list of required context fields."""
        return ["user_id"]
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process context to load business profile information.
        
        Args:
            context: AgentContext containing user information
            
        Returns:
            Updated AgentContext with business profile data
        """
        if not self.auto_load_profile:
            return context
        
        user_id = context.user_id
        self.logger.info(f"Loading business profile context for user: {user_id}")
        
        try:
            # Get active business profile
            profile_data = await self._get_active_business_profile(user_id)
            
            if not profile_data:
                self.logger.info(f"No active business profile found for user {user_id}")
                context.set_field("business_profile_status", "no_active_profile")
                return context
            
            # Load business profile context
            business_context = await self._load_business_context(profile_data)
            
            # Add business context to agent context
            context.set_field("business_profile", profile_data)
            context.set_field("business_context", business_context)
            context.set_field("business_profile_status", "loaded")
            
            # Load associated data sources if enabled
            if self.include_data_sources:
                data_sources_context = await self._load_data_sources_context(profile_data["id"], user_id)
                context.set_field("business_data_sources", data_sources_context)
            
            # Load knowledge graph context if enabled
            if self.include_knowledge_graph and profile_data.get("knowledge_graph_id"):
                kg_context = await self._load_knowledge_graph_context(profile_data["knowledge_graph_id"])
                context.set_field("business_knowledge_graph", kg_context)
            
            self.logger.info(f"Successfully loaded business profile context for user {user_id}")
            
        except Exception as e:
            self.logger.error(f"Error loading business profile context: {e}")
            context.add_error(self.name, f"Failed to load business profile context: {str(e)}")
            context.set_field("business_profile_status", "error")
        
        return context
    
    async def _get_active_business_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get the active business profile for a user."""
        try:
            # Check cache first (thread-safe)
            cache_key = f"profile_{user_id}"
            with self.cache_lock:
                if cache_key in self.profile_cache:
                    cached_data, timestamp = self.profile_cache[cache_key]
                    if time.time() - timestamp < self.cache_ttl:
                        self.logger.debug(f"Using cached profile for user {user_id}")
                        return cached_data
                    else:
                        # Remove expired cache entry
                        del self.profile_cache[cache_key]
            
            # Import here to avoid circular imports
            from app.services.business_profile_service import BusinessProfileService
            from app.database import get_db
            
            # Get database session
            db = next(get_db())
            service = BusinessProfileService(db)
            
            # Get active profile
            profile = service.get_active_profile(int(user_id))
            
            profile_data = profile.model_dump() if profile else None

            # Cache the result (thread-safe)
            with self.cache_lock:
                self.profile_cache[cache_key] = (profile_data, time.time())

                # Clean up old cache entries to prevent memory leaks
                current_time = time.time()
                expired_keys = [
                    key for key, (_, timestamp) in self.profile_cache.items()
                    if current_time - timestamp > self.cache_ttl
                ]
                for key in expired_keys:
                    del self.profile_cache[key]

            return profile_data

        except Exception as e:
            self.logger.error(f"Error getting active business profile for user {user_id}: {type(e).__name__}")
            return None
    
    async def _load_business_context(self, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """Load structured business context from profile data."""
        business_context = {
            "profile_id": profile_data["id"],
            "profile_name": profile_data["name"],
            "industry": profile_data.get("industry"),
            "business_type": profile_data.get("business_type"),
            "business_size": profile_data.get("business_size"),
            "target_audience": profile_data.get("target_audience"),
            "products_services": profile_data.get("products_services"),
            "marketing_goals": profile_data.get("marketing_goals"),
            "competitive_landscape": profile_data.get("competitive_landscape"),
            "budget_indicators": profile_data.get("budget_indicators"),
            "geographic_focus": profile_data.get("geographic_focus"),
            "business_stage": profile_data.get("business_stage"),
            "context_metadata": profile_data.get("context_metadata", {})
        }
        
        # Create a structured business summary for agents
        business_summary = self._create_business_summary(business_context)
        business_context["business_summary"] = business_summary
        
        return business_context
    
    def _create_business_summary(self, context: Dict[str, Any]) -> str:
        """Create a natural language business summary for agents."""
        summary_parts = []
        
        if context.get("profile_name"):
            summary_parts.append(f"Business Profile: {context['profile_name']}")
        
        if context.get("industry"):
            summary_parts.append(f"Industry: {context['industry']}")
        
        if context.get("business_type"):
            summary_parts.append(f"Business Type: {context['business_type']}")
        
        if context.get("target_audience"):
            summary_parts.append(f"Target Audience: {context['target_audience']}")
        
        if context.get("products_services"):
            summary_parts.append(f"Products/Services: {context['products_services']}")
        
        if context.get("marketing_goals"):
            summary_parts.append(f"Marketing Goals: {context['marketing_goals']}")
        
        return "\n".join(summary_parts) if summary_parts else "No business context available"
    
    async def _load_data_sources_context(self, profile_id: str, user_id: str) -> Dict[str, Any]:
        """Load context from associated data sources."""
        try:
            # Import here to avoid circular imports
            from app.services.business_profile_service import BusinessProfileService
            from app.database import get_db
            
            # Get database session
            db = next(get_db())
            service = BusinessProfileService(db)
            
            # Get profile with data sources
            profile_with_sources = service.get_profile_with_data_sources(profile_id, int(user_id))
            
            if not profile_with_sources or not profile_with_sources.data_source_assignments:
                return {"data_sources": [], "summary": "No data sources attached"}
            
            # Sort data sources by priority and role
            assignments = sorted(
                profile_with_sources.data_source_assignments,
                key=lambda x: (x.priority, self.context_priority_order.index(x.role) if x.role in self.context_priority_order else 999)
            )
            
            # Limit number of data sources
            assignments = assignments[:self.max_data_sources]
            
            data_sources_info = []
            for assignment in assignments:
                if assignment.is_active:
                    data_sources_info.append({
                        "id": assignment.data_source_id,
                        "role": assignment.role,
                        "priority": assignment.priority
                    })
            
            return {
                "data_sources": data_sources_info,
                "summary": f"{len(data_sources_info)} data sources available for business context",
                "total_sources": len(profile_with_sources.data_source_assignments)
            }
            
        except Exception as e:
            self.logger.error(f"Error loading data sources context: {e}")
            return {"data_sources": [], "summary": "Error loading data sources", "error": str(e)}
    
    async def _load_knowledge_graph_context(self, kg_id: str) -> Dict[str, Any]:
        """Load context from the business profile's knowledge graph."""
        try:
            from ...utils.knowledge_graph_service import KnowledgeGraphService

            kg_service = KnowledgeGraphService()

            # Query the knowledge graph for entities and relationships
            query_result = await kg_service.query_graph(
                query="Get all entities and relationships",
                user_id=None,  # System query
                limit=100
            )

            return {
                "knowledge_graph_id": kg_id,
                "summary": f"Knowledge graph loaded with {query_result.get('count', 0)} items",
                "entities_count": len(query_result.get('entities', [])),
                "relationships_count": len(query_result.get('relationships', [])),
                "entities": query_result.get('entities', [])[:10],  # First 10 for context
                "relationships": query_result.get('relationships', [])[:10]
            }

        except Exception as e:
            self.logger.error(f"Error loading knowledge graph context: {type(e).__name__}")
            return {
                "knowledge_graph_id": kg_id,
                "summary": "Knowledge graph unavailable",
                "entities_count": 0,
                "relationships_count": 0,
                "error": "Service unavailable"
            }
    
    def clear_cache(self, user_id: Optional[str] = None):
        """Clear the profile cache for a specific user or all users."""
        if user_id:
            cache_key = f"profile_{user_id}"
            self.profile_cache.pop(cache_key, None)
        else:
            self.profile_cache.clear()
        
        self.logger.info(f"Cleared business profile cache for user: {user_id or 'all users'}")
    
    async def refresh_business_context(self, context: AgentContext) -> AgentContext:
        """Force refresh of business context for the current user."""
        user_id = context.user_id
        
        # Clear cache for this user
        self.clear_cache(user_id)
        
        # Reload context
        return await self.process(context)
