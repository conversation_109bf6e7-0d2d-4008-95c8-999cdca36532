"""
Admin models for the Datagenius backend.

This module provides Pydantic models for admin-specific functionality.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field


class AdminActivityLogBase(BaseModel):
    """Base model for admin activity log data."""
    action: str
    entity_type: str
    entity_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class AdminActivityLogCreate(AdminActivityLogBase):
    """Model for creating a new admin activity log."""
    admin_id: int


class AdminActivityLogResponse(AdminActivityLogBase):
    """Model for admin activity log data returned to the client."""
    id: int
    admin_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class AdminActivityLogListResponse(BaseModel):
    """Model for admin activity log list response."""
    logs: List[AdminActivityLogResponse]
    total: int


class AdminUserUpdateRequest(BaseModel):
    """Model for admin updating a user."""
    email: Optional[str] = None
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    is_superuser: Optional[bool] = None


class AdminPersonaUpdateRequest(BaseModel):
    """Model for admin updating a persona."""
    name: Optional[str] = None
    description: Optional[str] = None
    industry: Optional[str] = None
    skills: Optional[List[str]] = None
    rating: Optional[float] = None
    review_count: Optional[int] = None
    image_url: Optional[str] = None
    price: Optional[float] = None
    provider: Optional[str] = None
    model: Optional[str] = None
    is_active: Optional[bool] = None
    age_restriction: Optional[int] = Field(None, ge=0)
    content_filters: Optional[Dict[str, Any]] = None


class AdminPersonaCreateRequest(BaseModel):
    """Model for admin creating a new persona."""
    id: str
    name: str
    description: str
    industry: str
    skills: List[str]
    rating: float = 4.5
    review_count: int = 0
    image_url: str = "/placeholder.svg"
    price: float = 10.0
    provider: str = "groq"
    model: Optional[str] = None
    is_active: bool = True
    age_restriction: int = Field(0, ge=0)
    content_filters: Optional[Dict[str, Any]] = None


class AdminDashboardStats(BaseModel):
    """Model for admin dashboard statistics."""
    total_users: int
    active_users: int
    total_personas: int
    active_personas: int
    total_purchases: int
    total_revenue: float
    recent_purchases: int
    recent_revenue: float


class AdminUserListResponse(BaseModel):
    """Model for admin user list response."""
    users: List[Any]  # Will use the User model from auth.py
    total: int


class AdminAnalyticsTimeframe(BaseModel):
    """Model for analytics timeframe."""
    start_date: datetime
    end_date: datetime


class AdminAnalyticsRequest(BaseModel):
    """Model for admin analytics request."""
    timeframe: AdminAnalyticsTimeframe
    group_by: Optional[str] = None  # "day", "week", "month"


class AdminAnalyticsDataPoint(BaseModel):
    """Model for analytics data point."""
    date: str
    value: float


class AdminAnalyticsResponse(BaseModel):
    """Model for admin analytics response."""
    data: List[AdminAnalyticsDataPoint]
    total: float
    average: float
    min: float
    max: float
