import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface ToolbarVisibilityState {
  // Simple Mode toolbar visibility
  isSimpleToolbarVisible: boolean;
  
  // Advanced Mode ribbon toolbar visibility
  isAdvancedRibbonVisible: boolean;
  
  // Actions
  toggleSimpleToolbar: () => void;
  toggleAdvancedRibbon: () => void;
  setSimpleToolbarVisible: (visible: boolean) => void;
  setAdvancedRibbonVisible: (visible: boolean) => void;
  
  // Utility methods
  getToolbarVisibility: (mode: 'simple' | 'advanced') => boolean;
  setToolbarVisibility: (mode: 'simple' | 'advanced', visible: boolean) => void;
}

export const useToolbarVisibilityStore = create<ToolbarVisibilityState>()(
  persist(
    (set, get) => ({
      // Default states - both toolbars visible by default
      isSimpleToolbarVisible: true,
      isAdvancedRibbonVisible: true,
      
      // Toggle actions
      toggleSimpleToolbar: () => 
        set((state) => ({ 
          isSimpleToolbarVisible: !state.isSimpleToolbarVisible 
        })),
      
      toggleAdvancedRibbon: () => 
        set((state) => ({ 
          isAdvancedRibbonVisible: !state.isAdvancedRibbonVisible 
        })),
      
      // Direct setters
      setSimpleToolbarVisible: (visible: boolean) => 
        set({ isSimpleToolbarVisible: visible }),
      
      setAdvancedRibbonVisible: (visible: boolean) => 
        set({ isAdvancedRibbonVisible: visible }),
      
      // Utility methods
      getToolbarVisibility: (mode: 'simple' | 'advanced') => {
        const state = get();
        return mode === 'simple' 
          ? state.isSimpleToolbarVisible 
          : state.isAdvancedRibbonVisible;
      },
      
      setToolbarVisibility: (mode: 'simple' | 'advanced', visible: boolean) => {
        if (mode === 'simple') {
          set({ isSimpleToolbarVisible: visible });
        } else {
          set({ isAdvancedRibbonVisible: visible });
        }
      },
    }),
    {
      name: 'toolbar-visibility-state',
      partialize: (state) => ({
        isSimpleToolbarVisible: state.isSimpleToolbarVisible,
        isAdvancedRibbonVisible: state.isAdvancedRibbonVisible,
      }),
    }
  )
);
