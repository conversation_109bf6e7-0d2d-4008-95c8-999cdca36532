"""
Groq model provider for the Datagenius backend.

This module provides a model provider implementation for Groq.
"""

import logging
import requests
from typing import Dict, Any, List, Union, Optional

# Import LangChain models with compatibility for different versions
try:
    # Try newer LangChain structure
    from langchain_core.language_models.base import BaseLanguageModel
    from langchain_core.language_models.chat_models import BaseChatModel
except ImportError:
    try:
        # Try older LangChain structure
        from langchain.schema.language_model import BaseLanguageModel
        from langchain.chat_models.base import BaseChatModel
    except ImportError:
        # Fallback to even older structure
        from langchain.base_language import BaseLanguageModel
        from langchain.chat_models.base import BaseChatModel

from .base import BaseModelProvider
from .exceptions import ModelInitializationError, ModelNotFoundError
from .config import get_provider_config

# Configure logging
logger = logging.getLogger(__name__)


class GroqProvider(BaseModelProvider):
    """Model provider implementation for Groq."""

    @property
    def provider_id(self) -> str:
        """Get the provider ID."""
        return "groq"

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        return "Groq"

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the provider with configuration.

        Args:
            config: Configuration dictionary for the provider
        """
        await super().initialize(config)

        # Get provider configuration
        provider_config = get_provider_config("groq")

        # Set default model if not specified
        if not self._default_model_id:
            self._default_model_id = provider_config.get("default_model", "llama3-70b-8192")

        # Set default endpoint if not specified
        if not self._endpoint:
            self._endpoint = provider_config.get("endpoint", "https://api.groq.com/openai/v1")

        # Set API key from configuration if not specified
        if not self._api_key:
            self._api_key = provider_config.get("api_key", "")

        # Check if we have an API key
        if not self._api_key:
            logger.warning("No Groq API key provided")

    async def _initialize_model(self, model_id: str, config: Dict[str, Any]) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Initialize a model instance.

        Args:
            model_id: ID of the model to initialize
            config: Configuration for the model

        Returns:
            Initialized model instance

        Raises:
            ModelInitializationError: If there's an error initializing the model
            ModelNotFoundError: If the model is not found
        """
        try:
            # Check if the model exists
            models = await self.list_models()
            model_exists = any(model["id"] == model_id for model in models)

            if not model_exists:
                # If the model doesn't exist but is a known model, we'll try anyway
                known_models = ["llama3-70b-8192", "llama3-8b-8192", "mixtral-8x7b-32768", "gemma-7b-it"]
                if model_id not in known_models:
                    raise ModelNotFoundError(f"Model '{model_id}' not found in Groq")

            # Import here to avoid hard dependencies
            try:
                from langchain_groq import ChatGroq
            except ImportError:
                logger.warning("langchain-groq not installed, attempting to install...")
                import subprocess
                subprocess.check_call(["pip", "install", "langchain-groq"])
                from langchain_groq import ChatGroq

            # Initialize the model
            try:
                # Try with the newer parameter name (groq_api_key)
                model = ChatGroq(
                    temperature=config.get("temperature", 0.7),
                    model_name=model_id,
                    groq_api_key=self._api_key
                )
            except TypeError:
                # Fall back to the older parameter name (api_key)
                model = ChatGroq(
                    temperature=config.get("temperature", 0.7),
                    model_name=model_id,
                    api_key=self._api_key
                )

            logger.info(f"Initialized Groq model '{model_id}'")
            return model

        except ImportError as e:
            raise ModelInitializationError(f"Error importing Groq: {str(e)}")
        except Exception as e:
            raise ModelInitializationError(f"Error initializing Groq model '{model_id}': {str(e)}")

    async def _fetch_models(self) -> List[Dict[str, Any]]:
        """
        Fetch available models from Groq.

        Returns:
            List of model metadata dictionaries
        """
        if not self._api_key:
            # Return a static list of known models if no API key
            return [
                {
                    "id": "llama3-70b-8192",
                    "name": "Llama 3 70B",
                    "description": "Meta's Llama 3 70B model",
                    "context_length": 8192,
                    "provider": "groq"
                },
                {
                    "id": "llama3-8b-8192",
                    "name": "Llama 3 8B",
                    "description": "Meta's Llama 3 8B model",
                    "context_length": 8192,
                    "provider": "groq"
                },
                {
                    "id": "mixtral-8x7b-32768",
                    "name": "Mixtral 8x7B",
                    "description": "Mistral AI's Mixtral 8x7B model",
                    "context_length": 32768,
                    "provider": "groq"
                },
                {
                    "id": "gemma-7b-it",
                    "name": "Gemma 7B",
                    "description": "Google's Gemma 7B model",
                    "context_length": 8192,
                    "provider": "groq"
                }
            ]

        try:
            # Make request to Groq API
            headers = {
                "Authorization": f"Bearer {self._api_key}"
            }

            response = requests.get(f"{self._endpoint}/models", headers=headers, timeout=10)
            response.raise_for_status()

            # Parse response
            data = response.json()
            models = data.get("data", [])

            # Format models
            formatted_models = []
            for model in models:
                model_id = model.get("id", "")

                # Get display name
                display_name = model.get("name", model_id)
                if display_name == model_id:
                    # Try to make a nicer display name
                    parts = model_id.split("-")
                    if len(parts) > 1:
                        display_name = " ".join(part.capitalize() for part in parts[:-1])
                        if parts[-1].isdigit():
                            display_name += f" {parts[-1]}"

                # Get description
                description = model.get("description", "")

                # Get context window (Groq uses context_window instead of context_length)
                context_length = model.get("context_window", 0)

                formatted_models.append({
                    "id": model_id,
                    "name": display_name,
                    "description": description,
                    "created": model.get("created", 0),
                    "context_length": context_length,
                    "provider": "groq"
                })

            # Sort models by creation date (newest first)
            formatted_models.sort(key=lambda x: x.get("created", 0), reverse=True)
            return formatted_models

        except Exception as e:
            logger.error(f"Error fetching Groq models: {str(e)}", exc_info=True)
            # Return a static list of known models as a fallback
            return [
                {
                    "id": "llama3-70b-8192",
                    "name": "Llama 3 70B",
                    "description": "Meta's Llama 3 70B model",
                    "context_length": 8192,
                    "provider": "groq"
                },
                {
                    "id": "llama3-8b-8192",
                    "name": "Llama 3 8B",
                    "description": "Meta's Llama 3 8B model",
                    "context_length": 8192,
                    "provider": "groq"
                }
            ]
