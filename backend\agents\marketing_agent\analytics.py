"""
Analytics and feedback system for marketing agent.

This module provides comprehensive analytics capabilities including
user interaction tracking, performance metrics, and feedback collection.
"""

import os
import json
import time
import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import uuid

logger = logging.getLogger(__name__)


class EventType(str, Enum):
    """Types of analytics events."""
    USER_MESSAGE = "user_message"
    AGENT_RESPONSE = "agent_response"
    CONTENT_GENERATED = "content_generated"
    CONVERSATION_STARTED = "conversation_started"
    CONVERSATION_ENDED = "conversation_ended"
    ERROR_OCCURRED = "error_occurred"
    FEEDBACK_SUBMITTED = "feedback_submitted"
    CACHE_HIT = "cache_hit"
    CACHE_MISS = "cache_miss"


class FeedbackType(str, Enum):
    """Types of user feedback."""
    RATING = "rating"
    THUMBS_UP_DOWN = "thumbs_up_down"
    TEXT_FEEDBACK = "text_feedback"
    BUG_REPORT = "bug_report"
    FEATURE_REQUEST = "feature_request"


@dataclass
class AnalyticsEvent:
    """Represents an analytics event."""
    event_id: str
    event_type: EventType
    timestamp: float
    user_id: Optional[str] = None
    conversation_id: Optional[str] = None
    session_id: Optional[str] = None
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'timestamp': self.timestamp,
            'user_id': self.user_id,
            'conversation_id': self.conversation_id,
            'session_id': self.session_id,
            'data': self.data,
            'metadata': self.metadata
        }


@dataclass
class FeedbackEntry:
    """Represents user feedback."""
    feedback_id: str
    feedback_type: FeedbackType
    timestamp: float
    user_id: Optional[str] = None
    conversation_id: Optional[str] = None
    message_id: Optional[str] = None
    rating: Optional[float] = None
    text: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'feedback_id': self.feedback_id,
            'feedback_type': self.feedback_type.value,
            'timestamp': self.timestamp,
            'user_id': self.user_id,
            'conversation_id': self.conversation_id,
            'message_id': self.message_id,
            'rating': self.rating,
            'text': self.text,
            'metadata': self.metadata
        }


class AnalyticsCollector:
    """Collects and processes analytics events."""
    
    def __init__(
        self,
        buffer_size: int = 1000,
        flush_interval: int = 300,  # 5 minutes
        enable_persistence: bool = True,
        storage_path: Optional[str] = None
    ):
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        self.enable_persistence = enable_persistence
        self.storage_path = storage_path or "data/analytics"
        
        # Event buffer
        self._event_buffer: deque = deque(maxlen=buffer_size)
        self._feedback_buffer: deque = deque(maxlen=buffer_size)
        
        # Statistics
        self._event_counts = defaultdict(int)
        self._user_sessions = defaultdict(dict)
        self._conversation_metrics = defaultdict(dict)
        
        # Background tasks
        self._flush_task: Optional[asyncio.Task] = None
        self._is_running = False
        
        # Ensure storage directory exists
        if self.enable_persistence:
            os.makedirs(self.storage_path, exist_ok=True)
    
    async def start(self) -> None:
        """Start analytics collection."""
        if self._is_running:
            return
        
        self._is_running = True
        
        if self.enable_persistence:
            self._flush_task = asyncio.create_task(self._flush_loop())
        
        logger.info("Analytics collector started")
    
    async def stop(self) -> None:
        """Stop analytics collection."""
        self._is_running = False
        
        if self._flush_task:
            self._flush_task.cancel()
            try:
                await self._flush_task
            except asyncio.CancelledError:
                pass
        
        # Final flush
        if self.enable_persistence:
            await self._flush_events()
            await self._flush_feedback()
        
        logger.info("Analytics collector stopped")
    
    def track_event(
        self,
        event_type: EventType,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Track an analytics event."""
        event = AnalyticsEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            timestamp=time.time(),
            user_id=user_id,
            conversation_id=conversation_id,
            session_id=session_id,
            data=data or {},
            metadata=metadata or {}
        )
        
        self._event_buffer.append(event)
        self._event_counts[event_type.value] += 1
        
        # Update session tracking
        if user_id and session_id:
            self._update_session_tracking(user_id, session_id, event)
        
        # Update conversation metrics
        if conversation_id:
            self._update_conversation_metrics(conversation_id, event)
        
        logger.debug(f"Tracked event: {event_type.value}")
        return event.event_id
    
    def track_user_message(
        self,
        message: str,
        user_id: str,
        conversation_id: str,
        session_id: Optional[str] = None,
        intent: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Track user message event."""
        data = {
            'message_length': len(message),
            'intent': intent,
            'has_form_data': 'marketing_form_data' in (metadata or {})
        }
        
        return self.track_event(
            EventType.USER_MESSAGE,
            user_id=user_id,
            conversation_id=conversation_id,
            session_id=session_id,
            data=data,
            metadata=metadata
        )
    
    def track_agent_response(
        self,
        response: Dict[str, Any],
        user_id: str,
        conversation_id: str,
        session_id: Optional[str] = None,
        processing_time: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Track agent response event."""
        data = {
            'response_length': len(response.get('content', '')),
            'processing_time': processing_time,
            'response_type': response.get('metadata', {}).get('response_type'),
            'generated_content': response.get('metadata', {}).get('generated_content', False),
            'conversational_response': response.get('metadata', {}).get('conversational_response', False)
        }
        
        return self.track_event(
            EventType.AGENT_RESPONSE,
            user_id=user_id,
            conversation_id=conversation_id,
            session_id=session_id,
            data=data,
            metadata=metadata
        )
    
    def track_content_generation(
        self,
        content_type: str,
        user_id: str,
        conversation_id: str,
        session_id: Optional[str] = None,
        generation_time: Optional[float] = None,
        content_length: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Track content generation event."""
        data = {
            'content_type': content_type,
            'generation_time': generation_time,
            'content_length': content_length
        }
        
        return self.track_event(
            EventType.CONTENT_GENERATED,
            user_id=user_id,
            conversation_id=conversation_id,
            session_id=session_id,
            data=data,
            metadata=metadata
        )
    
    def track_error(
        self,
        error: Exception,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Track error event."""
        data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context or {}
        }
        
        return self.track_event(
            EventType.ERROR_OCCURRED,
            user_id=user_id,
            conversation_id=conversation_id,
            session_id=session_id,
            data=data
        )
    
    def submit_feedback(
        self,
        feedback_type: FeedbackType,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        message_id: Optional[str] = None,
        rating: Optional[float] = None,
        text: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Submit user feedback."""
        feedback = FeedbackEntry(
            feedback_id=str(uuid.uuid4()),
            feedback_type=feedback_type,
            timestamp=time.time(),
            user_id=user_id,
            conversation_id=conversation_id,
            message_id=message_id,
            rating=rating,
            text=text,
            metadata=metadata or {}
        )
        
        self._feedback_buffer.append(feedback)
        
        # Track feedback submission as event
        self.track_event(
            EventType.FEEDBACK_SUBMITTED,
            user_id=user_id,
            conversation_id=conversation_id,
            data={
                'feedback_type': feedback_type.value,
                'rating': rating,
                'has_text': text is not None
            }
        )
        
        logger.debug(f"Feedback submitted: {feedback_type.value}")
        return feedback.feedback_id
    
    def _update_session_tracking(
        self,
        user_id: str,
        session_id: str,
        event: AnalyticsEvent
    ) -> None:
        """Update session tracking data."""
        if user_id not in self._user_sessions:
            self._user_sessions[user_id] = {}
        
        if session_id not in self._user_sessions[user_id]:
            self._user_sessions[user_id][session_id] = {
                'start_time': event.timestamp,
                'last_activity': event.timestamp,
                'event_count': 0,
                'conversations': set()
            }
        
        session = self._user_sessions[user_id][session_id]
        session['last_activity'] = event.timestamp
        session['event_count'] += 1
        
        if event.conversation_id:
            session['conversations'].add(event.conversation_id)
    
    def _update_conversation_metrics(
        self,
        conversation_id: str,
        event: AnalyticsEvent
    ) -> None:
        """Update conversation metrics."""
        if conversation_id not in self._conversation_metrics:
            self._conversation_metrics[conversation_id] = {
                'start_time': event.timestamp,
                'last_activity': event.timestamp,
                'message_count': 0,
                'user_messages': 0,
                'agent_responses': 0,
                'content_generations': 0,
                'errors': 0
            }
        
        metrics = self._conversation_metrics[conversation_id]
        metrics['last_activity'] = event.timestamp
        
        if event.event_type == EventType.USER_MESSAGE:
            metrics['user_messages'] += 1
            metrics['message_count'] += 1
        elif event.event_type == EventType.AGENT_RESPONSE:
            metrics['agent_responses'] += 1
            metrics['message_count'] += 1
        elif event.event_type == EventType.CONTENT_GENERATED:
            metrics['content_generations'] += 1
        elif event.event_type == EventType.ERROR_OCCURRED:
            metrics['errors'] += 1
    
    async def _flush_loop(self) -> None:
        """Background loop to flush events to storage."""
        while self._is_running:
            try:
                await asyncio.sleep(self.flush_interval)
                await self._flush_events()
                await self._flush_feedback()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in analytics flush loop: {e}")
    
    async def _flush_events(self) -> None:
        """Flush events to storage."""
        if not self._event_buffer:
            return
        
        try:
            # Get events to flush
            events_to_flush = list(self._event_buffer)
            self._event_buffer.clear()
            
            # Write to file
            filename = f"events_{int(time.time())}.jsonl"
            filepath = os.path.join(self.storage_path, filename)
            
            with open(filepath, 'w') as f:
                for event in events_to_flush:
                    f.write(json.dumps(event.to_dict()) + '\n')
            
            logger.debug(f"Flushed {len(events_to_flush)} events to {filepath}")
            
        except Exception as e:
            logger.error(f"Error flushing events: {e}")
    
    async def _flush_feedback(self) -> None:
        """Flush feedback to storage."""
        if not self._feedback_buffer:
            return
        
        try:
            # Get feedback to flush
            feedback_to_flush = list(self._feedback_buffer)
            self._feedback_buffer.clear()
            
            # Write to file
            filename = f"feedback_{int(time.time())}.jsonl"
            filepath = os.path.join(self.storage_path, filename)
            
            with open(filepath, 'w') as f:
                for feedback in feedback_to_flush:
                    f.write(json.dumps(feedback.to_dict()) + '\n')
            
            logger.debug(f"Flushed {len(feedback_to_flush)} feedback entries to {filepath}")
            
        except Exception as e:
            logger.error(f"Error flushing feedback: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get analytics statistics."""
        return {
            'event_counts': dict(self._event_counts),
            'total_events': sum(self._event_counts.values()),
            'active_sessions': len(self._user_sessions),
            'active_conversations': len(self._conversation_metrics),
            'buffer_sizes': {
                'events': len(self._event_buffer),
                'feedback': len(self._feedback_buffer)
            }
        }
    
    def get_conversation_metrics(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get metrics for a specific conversation."""
        return self._conversation_metrics.get(conversation_id)
    
    def get_user_session_data(self, user_id: str) -> Dict[str, Any]:
        """Get session data for a specific user."""
        return self._user_sessions.get(user_id, {})


# Global analytics collector instance
analytics_collector: Optional[AnalyticsCollector] = None


def initialize_analytics(config: Dict[str, Any]) -> AnalyticsCollector:
    """Initialize global analytics collector."""
    global analytics_collector
    
    analytics_collector = AnalyticsCollector(
        buffer_size=config.get('buffer_size', 1000),
        flush_interval=config.get('flush_interval', 300),
        enable_persistence=config.get('enable_persistence', True),
        storage_path=config.get('storage_path')
    )
    
    logger.info("Analytics collector initialized")
    return analytics_collector


def get_analytics() -> Optional[AnalyticsCollector]:
    """Get global analytics collector instance."""
    return analytics_collector


# Decorator for automatic event tracking
def track_function_call(event_type: EventType, extract_context: Optional[callable] = None):
    """Decorator to automatically track function calls."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # Extract context if function provided
            context = {}
            if extract_context:
                try:
                    context = extract_context(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"Error extracting context for tracking: {e}")
            
            try:
                result = await func(*args, **kwargs)
                
                # Track successful execution
                if analytics_collector:
                    processing_time = time.time() - start_time
                    analytics_collector.track_event(
                        event_type,
                        user_id=context.get('user_id'),
                        conversation_id=context.get('conversation_id'),
                        session_id=context.get('session_id'),
                        data={
                            'function_name': func.__name__,
                            'processing_time': processing_time,
                            'success': True
                        },
                        metadata=context
                    )
                
                return result
                
            except Exception as e:
                # Track error
                if analytics_collector:
                    processing_time = time.time() - start_time
                    analytics_collector.track_error(
                        e,
                        user_id=context.get('user_id'),
                        conversation_id=context.get('conversation_id'),
                        session_id=context.get('session_id'),
                        context={
                            'function_name': func.__name__,
                            'processing_time': processing_time
                        }
                    )
                
                raise
        
        return wrapper
    return decorator
