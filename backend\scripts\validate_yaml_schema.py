import argparse
import os
import sys
import glob
import json
import yaml # Needed for loading YAML schema if provided as YAML
from jsonschema import validate, ValidationError

# Add the backend directory to the Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(script_dir)
sys.path.insert(0, backend_dir)

try:
    # We need load_yaml to load the data files and potentially the schema file
    # We need yaml_to_schema if the schema itself is provided in YAML format
    from app.utils.yaml_utils import load_yaml, yaml_to_schema
except ImportError as e:
    print(f"Error importing yaml_utils: {e}")
    print("Ensure the script is run from a context where 'backend' is accessible or in the Python path.")
    sys.exit(1)

def load_schema(schema_path: str) -> dict:
    """Loads a schema from a JSON or YAML file."""
    if not os.path.isfile(schema_path):
        raise FileNotFoundError(f"Schema file not found: {schema_path}")

    try:
        if schema_path.lower().endswith(".json"):
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema = json.load(f)
                print(f"Loaded JSON schema from: {schema_path}")
                return schema
        elif schema_path.lower().endswith((".yaml", ".yml")):
            # Use load_yaml first, then pass to yaml_to_schema (which currently just loads)
            # This aligns with the plan's intention, even if yaml_to_schema needs refinement
            raw_yaml_data = load_yaml(schema_path)
            schema = yaml_to_schema(yaml.dump(raw_yaml_data)) # Pass string to yaml_to_schema
            print(f"Loaded YAML schema from: {schema_path}")
            return schema
        else:
            raise ValueError(f"Unsupported schema file extension: {schema_path}. Use .json, .yaml, or .yml.")
    except Exception as e:
        raise Exception(f"Error loading or parsing schema file {schema_path}: {e}")


def main():
    parser = argparse.ArgumentParser(description="Validate YAML data files against a JSON or YAML schema.")
    parser.add_argument("--schema-file", required=True, help="Path to the schema file (JSON or YAML).")

    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--data-file", help="Path to a single YAML data file to validate.")
    group.add_argument("--data-directory", help="Directory containing YAML data files to validate.")

    parser.add_argument("--pattern", default="*.yaml", help="Glob pattern for data files if --data-directory is used (e.g., '*.yaml'). Default is '*.yaml'.")
    parser.add_argument("--recursive", action="store_true", default=True, help="Search data directory recursively. Default is True.")

    args = parser.parse_args()

    try:
        schema = load_schema(args.schema_file)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

    data_files_to_validate = []
    if args.data_file:
        if not os.path.isfile(args.data_file):
            print(f"Error: Data file not found: {args.data_file}")
            sys.exit(1)
        if not args.data_file.lower().endswith((".yaml", ".yml")):
             print(f"Warning: Specified data file '{args.data_file}' does not have a .yaml or .yml extension.")
        data_files_to_validate.append(args.data_file)
    elif args.data_directory:
        if not os.path.isdir(args.data_directory):
            print(f"Error: Data directory not found: {args.data_directory}")
            sys.exit(1)
        search_pattern = os.path.join(args.data_directory, '**', args.pattern) if args.recursive else os.path.join(args.data_directory, args.pattern)
        print(f"Searching for data files in '{args.data_directory}' with pattern '{args.pattern}' (Recursive: {args.recursive})")
        found_files = glob.glob(search_pattern, recursive=args.recursive)
        data_files_to_validate = [f for f in found_files if os.path.isfile(f) and f.lower().endswith(('.yaml', '.yml'))]
        if not data_files_to_validate:
             print("No matching YAML files found to validate.")
             sys.exit(0)


    print("-" * 30)
    print(f"Validating {len(data_files_to_validate)} data file(s) against schema: {args.schema_file}")
    print("-" * 30)

    valid_count = 0
    invalid_count = 0

    for data_path in data_files_to_validate:
        print(f"Validating: {data_path}")
        try:
            data = load_yaml(data_path)
            validate(instance=data, schema=schema)
            print("  Status: VALID")
            valid_count += 1
        except ValidationError as e:
            print(f"  Status: INVALID")
            print(f"  Error: {e.message}")
            # print(f"  Path: {list(e.path)}") # Can be verbose
            # print(f"  Schema Path: {list(e.schema_path)}") # Can be verbose
            invalid_count += 1
        except Exception as e:
            print(f"  Status: ERROR loading or processing file")
            print(f"  Error: {e}")
            invalid_count += 1 # Count as invalid if loading fails

    print("-" * 30)
    print("Validation finished.")
    print(f"Valid files: {valid_count}")
    print(f"Invalid/Error files: {invalid_count}")
    print("-" * 30)

    if invalid_count > 0:
        sys.exit(1) # Exit with error code if any files failed validation

if __name__ == "__main__":
    main()
