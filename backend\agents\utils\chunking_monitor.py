"""
Performance monitoring and optimization for chunking operations.

This module provides real-time monitoring, alerting, and automatic
optimization for chunking and embedding performance.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import os
from pathlib import Path

from .chunking_performance_manager import ChunkingPerformanceManager, PerformanceMetrics

logger = logging.getLogger(__name__)

@dataclass
class PerformanceAlert:
    """Performance alert information."""
    alert_type: str
    severity: str  # "warning", "critical"
    message: str
    metric_value: float
    threshold: float
    timestamp: datetime
    resolved: bool = False

class ChunkingMonitor:
    """
    Real-time performance monitor for chunking operations.
    
    Features:
    - Real-time performance tracking
    - Automatic threshold alerting
    - Performance trend analysis
    - Automatic optimization recommendations
    - Performance dashboard data
    """
    
    def __init__(self, performance_manager: ChunkingPerformanceManager = None):
        """Initialize the chunking monitor."""
        self.performance_manager = performance_manager or ChunkingPerformanceManager()
        self.alerts = []
        self.monitoring_active = False
        self.monitoring_task = None
        self.performance_history = []
        self.optimization_history = []
        
        # Performance thresholds (can be configured)
        self.thresholds = {
            "processing_time_ms": {"warning": 2000, "critical": 5000},
            "memory_usage_mb": {"warning": 512, "critical": 1024},
            "cache_hit_rate": {"warning": 0.5, "critical": 0.3},  # Lower is worse
            "error_rate": {"warning": 0.05, "critical": 0.1},
            "throughput_ops_per_sec": {"warning": 1.0, "critical": 0.5}  # Lower is worse
        }
        
        logger.info("Chunking performance monitor initialized")
    
    async def start_monitoring(self, interval_seconds: int = 60):
        """
        Start continuous performance monitoring.
        
        Args:
            interval_seconds: Monitoring interval in seconds
        """
        if self.monitoring_active:
            logger.warning("Monitoring is already active")
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(
            self._monitoring_loop(interval_seconds)
        )
        logger.info(f"Started chunking performance monitoring (interval: {interval_seconds}s)")
    
    async def stop_monitoring(self):
        """Stop continuous performance monitoring."""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Stopped chunking performance monitoring")
    
    async def _monitoring_loop(self, interval_seconds: int):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                await self._collect_performance_metrics()
                await self._check_performance_thresholds()
                await self._analyze_performance_trends()
                await self._auto_optimize_if_needed()
                
                await asyncio.sleep(interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(interval_seconds)
    
    async def _collect_performance_metrics(self):
        """Collect current performance metrics."""
        try:
            # Get performance stats from the performance manager
            stats = self.performance_manager.get_performance_stats()
            
            if stats:
                # Create performance snapshot
                snapshot = {
                    "timestamp": datetime.now(),
                    "processing_time_ms": stats.get("average_duration_ms", 0),
                    "memory_usage_mb": stats.get("average_memory_usage_mb", 0),
                    "cache_hit_rate": stats.get("average_cache_hit_rate", 0),
                    "total_operations": stats.get("total_operations", 0),
                    "current_profile": stats.get("current_profile", "unknown"),
                    "cache_size": stats.get("cache_size", 0)
                }
                
                self.performance_history.append(snapshot)
                
                # Keep only recent history (last 24 hours worth of data)
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.performance_history = [
                    h for h in self.performance_history 
                    if h["timestamp"] > cutoff_time
                ]
                
        except Exception as e:
            logger.error(f"Error collecting performance metrics: {e}")
    
    async def _check_performance_thresholds(self):
        """Check performance metrics against thresholds and generate alerts."""
        if not self.performance_history:
            return
        
        latest_metrics = self.performance_history[-1]
        
        for metric_name, thresholds in self.thresholds.items():
            if metric_name not in latest_metrics:
                continue
            
            metric_value = latest_metrics[metric_name]
            
            # Handle metrics where lower values are worse
            if metric_name in ["cache_hit_rate", "throughput_ops_per_sec"]:
                if metric_value < thresholds["critical"]:
                    await self._create_alert(
                        "performance_degradation",
                        "critical",
                        f"{metric_name} is critically low: {metric_value:.2f}",
                        metric_value,
                        thresholds["critical"]
                    )
                elif metric_value < thresholds["warning"]:
                    await self._create_alert(
                        "performance_degradation",
                        "warning",
                        f"{metric_name} is below warning threshold: {metric_value:.2f}",
                        metric_value,
                        thresholds["warning"]
                    )
            else:
                # Handle metrics where higher values are worse
                if metric_value > thresholds["critical"]:
                    await self._create_alert(
                        "performance_degradation",
                        "critical",
                        f"{metric_name} is critically high: {metric_value:.2f}",
                        metric_value,
                        thresholds["critical"]
                    )
                elif metric_value > thresholds["warning"]:
                    await self._create_alert(
                        "performance_degradation",
                        "warning",
                        f"{metric_name} exceeds warning threshold: {metric_value:.2f}",
                        metric_value,
                        thresholds["warning"]
                    )
    
    async def _create_alert(self, alert_type: str, severity: str, message: str, 
                          metric_value: float, threshold: float):
        """Create a performance alert."""
        # Check if similar alert already exists and is unresolved
        existing_alert = next(
            (alert for alert in self.alerts 
             if alert.alert_type == alert_type and 
             alert.severity == severity and 
             not alert.resolved and
             (datetime.now() - alert.timestamp).seconds < 300),  # Within 5 minutes
            None
        )
        
        if existing_alert:
            return  # Don't create duplicate alerts
        
        alert = PerformanceAlert(
            alert_type=alert_type,
            severity=severity,
            message=message,
            metric_value=metric_value,
            threshold=threshold,
            timestamp=datetime.now()
        )
        
        self.alerts.append(alert)
        
        # Log the alert
        log_level = logging.CRITICAL if severity == "critical" else logging.WARNING
        logger.log(log_level, f"PERFORMANCE ALERT [{severity.upper()}]: {message}")
        
        # Keep only recent alerts
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.alerts = [alert for alert in self.alerts if alert.timestamp > cutoff_time]
    
    async def _analyze_performance_trends(self):
        """Analyze performance trends and identify patterns."""
        if len(self.performance_history) < 10:
            return  # Need more data for trend analysis
        
        # Analyze recent trend (last 10 data points)
        recent_metrics = self.performance_history[-10:]
        
        # Calculate trend for processing time
        processing_times = [m["processing_time_ms"] for m in recent_metrics]
        if len(processing_times) >= 2:
            trend = (processing_times[-1] - processing_times[0]) / len(processing_times)
            
            if trend > 100:  # Processing time increasing by >100ms per measurement
                await self._create_alert(
                    "performance_trend",
                    "warning",
                    f"Processing time trending upward: +{trend:.1f}ms per measurement",
                    trend,
                    100
                )
    
    async def _auto_optimize_if_needed(self):
        """Automatically optimize performance if conditions are met."""
        if not self.performance_history:
            return
        
        latest_metrics = self.performance_history[-1]
        
        # Check if optimization is needed
        needs_optimization = (
            latest_metrics["processing_time_ms"] > self.thresholds["processing_time_ms"]["warning"] or
            latest_metrics["memory_usage_mb"] > self.thresholds["memory_usage_mb"]["warning"] or
            latest_metrics["cache_hit_rate"] < self.thresholds["cache_hit_rate"]["warning"]
        )
        
        if needs_optimization:
            # Check if we haven't optimized recently (avoid thrashing)
            recent_optimization = any(
                opt["timestamp"] > datetime.now() - timedelta(minutes=30)
                for opt in self.optimization_history
            )
            
            if not recent_optimization:
                await self._perform_auto_optimization(latest_metrics)
    
    async def _perform_auto_optimization(self, metrics: Dict[str, Any]):
        """Perform automatic optimization based on current metrics."""
        optimization_actions = []
        
        try:
            # Optimize performance profile
            current_profile = metrics.get("current_profile", "balanced")
            
            if metrics["processing_time_ms"] > 3000:
                # Switch to speed-optimized profile
                if current_profile != "speed_optimized":
                    self.performance_manager.performance_profile = "speed_optimized"
                    optimization_actions.append("switched_to_speed_optimized")
            
            elif metrics["memory_usage_mb"] > 800:
                # Switch to memory-optimized profile
                if current_profile != "memory_optimized":
                    self.performance_manager.performance_profile = "memory_optimized"
                    optimization_actions.append("switched_to_memory_optimized")
            
            # Clear cache if hit rate is very low
            if metrics["cache_hit_rate"] < 0.2 and metrics["cache_size"] > 1000:
                self.performance_manager.clear_cache()
                optimization_actions.append("cleared_cache")
            
            # Record optimization
            optimization_record = {
                "timestamp": datetime.now(),
                "trigger_metrics": metrics,
                "actions": optimization_actions,
                "previous_profile": current_profile,
                "new_profile": self.performance_manager.performance_profile
            }
            
            self.optimization_history.append(optimization_record)
            
            if optimization_actions:
                logger.info(f"Auto-optimization performed: {', '.join(optimization_actions)}")
            
        except Exception as e:
            logger.error(f"Error during auto-optimization: {e}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get performance dashboard data."""
        # Calculate summary statistics
        recent_metrics = self.performance_history[-10:] if self.performance_history else []
        
        if recent_metrics:
            avg_processing_time = sum(m["processing_time_ms"] for m in recent_metrics) / len(recent_metrics)
            avg_memory_usage = sum(m["memory_usage_mb"] for m in recent_metrics) / len(recent_metrics)
            avg_cache_hit_rate = sum(m["cache_hit_rate"] for m in recent_metrics) / len(recent_metrics)
        else:
            avg_processing_time = avg_memory_usage = avg_cache_hit_rate = 0
        
        # Get active alerts
        active_alerts = [alert for alert in self.alerts if not alert.resolved]
        
        return {
            "monitoring_active": self.monitoring_active,
            "current_metrics": {
                "avg_processing_time_ms": avg_processing_time,
                "avg_memory_usage_mb": avg_memory_usage,
                "avg_cache_hit_rate": avg_cache_hit_rate,
                "total_operations": recent_metrics[-1]["total_operations"] if recent_metrics else 0,
                "current_profile": recent_metrics[-1]["current_profile"] if recent_metrics else "unknown"
            },
            "alerts": {
                "active_count": len(active_alerts),
                "critical_count": len([a for a in active_alerts if a.severity == "critical"]),
                "warning_count": len([a for a in active_alerts if a.severity == "warning"]),
                "recent_alerts": [asdict(alert) for alert in active_alerts[-5:]]
            },
            "optimization": {
                "recent_optimizations": len(self.optimization_history),
                "last_optimization": self.optimization_history[-1]["timestamp"].isoformat() if self.optimization_history else None
            },
            "performance_history": [
                {**metrics, "timestamp": metrics["timestamp"].isoformat()}
                for metrics in self.performance_history[-50:]  # Last 50 data points
            ]
        }
    
    def save_performance_report(self, filename: str = None):
        """Save performance report to file."""
        if filename is None:
            filename = f"chunking_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            "generated_at": datetime.now().isoformat(),
            "monitoring_period": {
                "start": self.performance_history[0]["timestamp"].isoformat() if self.performance_history else None,
                "end": self.performance_history[-1]["timestamp"].isoformat() if self.performance_history else None,
                "data_points": len(self.performance_history)
            },
            "dashboard_data": self.get_dashboard_data(),
            "all_alerts": [asdict(alert) for alert in self.alerts],
            "optimization_history": [
                {**opt, "timestamp": opt["timestamp"].isoformat()}
                for opt in self.optimization_history
            ]
        }
        
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            logger.info(f"Performance report saved to: {filename}")
        except Exception as e:
            logger.error(f"Error saving performance report: {e}")

# Global monitor instance
_global_monitor = None

def get_chunking_monitor() -> ChunkingMonitor:
    """Get the global chunking monitor instance."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = ChunkingMonitor()
    return _global_monitor

async def start_global_monitoring(interval_seconds: int = 60):
    """Start global chunking performance monitoring."""
    monitor = get_chunking_monitor()
    await monitor.start_monitoring(interval_seconds)

async def stop_global_monitoring():
    """Stop global chunking performance monitoring."""
    monitor = get_chunking_monitor()
    await monitor.stop_monitoring()
