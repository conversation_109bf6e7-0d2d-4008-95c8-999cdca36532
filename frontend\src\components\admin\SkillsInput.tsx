import React, { useState, useRef, KeyboardEvent } from 'react';
import { X, Plus } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface SkillsInputProps {
  skills: string[];
  onChange: (skills: string[]) => void;
  placeholder?: string;
  className?: string;
}

const SkillsInput: React.FC<SkillsInputProps> = ({
  skills,
  onChange,
  placeholder = "Add a skill...",
  className = "",
}) => {
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const addSkill = (skill: string) => {
    const trimmedSkill = skill.trim();
    if (trimmedSkill && !skills.includes(trimmedSkill)) {
      onChange([...skills, trimmedSkill]);
    }
    setInputValue('');
  };

  const removeSkill = (skillToRemove: string) => {
    onChange(skills.filter(skill => skill !== skillToRemove));
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addSkill(inputValue);
    } else if (e.key === 'Backspace' && !inputValue && skills.length > 0) {
      removeSkill(skills[skills.length - 1]);
    }
  };

  const handleContainerClick = () => {
    inputRef.current?.focus();
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    const pastedSkills = pastedText.split(/,|\n/).map(skill => skill.trim()).filter(Boolean);
    
    const newSkills = [...skills];
    pastedSkills.forEach(skill => {
      if (!newSkills.includes(skill)) {
        newSkills.push(skill);
      }
    });
    
    onChange(newSkills);
  };

  return (
    <div 
      ref={containerRef}
      className={`flex flex-wrap gap-2 p-2 border rounded-md bg-white min-h-[42px] focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 ${className}`}
      onClick={handleContainerClick}
    >
      {skills.map((skill, index) => (
        <div 
          key={index} 
          className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
        >
          <span>{skill}</span>
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              removeSkill(skill);
            }}
            className="text-blue-600 hover:text-blue-800 focus:outline-none"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
      ))}
      <div className="flex-1 min-w-[120px]">
        <Input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          placeholder={skills.length === 0 ? placeholder : ""}
          className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-7 text-sm"
        />
      </div>
      {inputValue && (
        <button
          type="button"
          onClick={() => addSkill(inputValue)}
          className="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200"
        >
          <Plus className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

export default SkillsInput;
