import React, { useState, useEffect, useMemo, useRef } from 'react'; // Import useEffect and useMemo
import { useForm, useWatch } from 'react-hook-form'; // Import useWatch
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import axios from 'axios';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';

import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Info, Plus, X, Loader2, RefreshCw, Upload, Paperclip } from 'lucide-react'; // Import Loader2 and RefreshCw
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { fileApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { DataSource } from '@/lib/dataSourceApi'; // Import DataSource type
import { loadYamlSchemaAsZod } from '@/utils/schema-utils';
import { ContentTypeSelector } from './ContentTypeSelector';
import { BlogTopicSelector } from './BlogTopicSelector';

// Define a flexible schema for extensible content types
const placeholderSchema = z.object({
  content_type: z.string().min(3, "Content type must be at least 3 characters").max(50, "Content type must be at most 50 characters"),
  document_format: z.enum(['report', 'presentation', 'brief', 'checklist', 'template', 'guide', 'analysis']).default('report'),
  brand_description: z.string().min(1, "Brand description is required"),
  target_audience: z.string().optional(),
  products_services: z.string().optional(),
  marketing_goals: z.string().optional(),
  existing_content: z.string().optional(),
  keywords: z.string().optional(),
  suggested_topics: z.string().optional(),
  tone: z.string().default("Professional"),
  competitive_landscape: z.string().optional(),
  budget: z.string().optional(),
  timeline: z.string().optional(),
  platforms: z.string().optional(),
  // Additional fields for specific content types
  campaign_objective: z.string().optional(),
  ad_type: z.string().optional(),
  platform: z.string().optional(),
  character_limit: z.number().optional(),
  announcement_type: z.string().optional(),
  headline: z.string().optional(),
  key_details: z.string().optional(),
  target_media: z.string().optional(),
  product_service: z.string().optional(),
  email_type: z.string().optional(),
  blog_topic: z.string().optional(),
  analysis_focus: z.string().optional(),
});

// We'll use this as our form schema
let formSchema = placeholderSchema;

// Load the YAML schema
(async () => {
  try {
    // In a production environment, this would be a proper URL
    // For development, we're using a relative path
    const loadedSchema = await loadYamlSchemaAsZod('/src/schemas/marketing-content-form.yaml');
    // Use the loaded schema if it's compatible, otherwise fall back to placeholder
    if (loadedSchema && typeof loadedSchema.parse === 'function') {
      formSchema = loadedSchema as typeof placeholderSchema;
      console.log('Marketing content form schema loaded from YAML');
    } else {
      console.log('Loaded schema is not compatible, using placeholder schema');
    }
  } catch (error) {
    console.error('Error loading YAML schema:', error);
    console.warn('Using placeholder schema instead');
    // Continue using the placeholder schema
  }
})();

export type MarketingContentFormData = z.infer<typeof formSchema> & {
  attachedFile?: {
    name: string;
    size: number;
    type: string;
  } | null;
};

interface MarketingContentFormProps {
  onSubmit: (data: MarketingContentFormData) => void;
  defaultValues?: Partial<MarketingContentFormData>;
  isLoading?: boolean;
  dataSourceId?: string; // Changed back to dataSourceId
  onClose?: () => void; // Add onClose prop
  onFileUpload?: () => void; // Callback to trigger file upload
  attachedFile?: File | null; // External attached file
  onRemoveFile?: () => void; // Callback to remove file
  isUploading?: boolean; // External uploading state
}

// Removed local type definitions, will use imported Provider and ProviderModel


export function MarketingContentForm({
  onSubmit,
  defaultValues,
  isLoading = false,
  dataSourceId, // Use dataSourceId again
  onClose,
  onFileUpload,
  attachedFile: externalAttachedFile,
  onRemoveFile,
  isUploading: externalIsUploading = false,
}: MarketingContentFormProps) {
  const { toast } = useToast();
  const [contentType, setContentType] = useState<string>(defaultValues?.content_type || 'marketing_strategy');
  // Provider/model selection removed - now uses concierge agent configuration

  // File upload state - use external state if provided, otherwise internal
  const [internalAttachedFile, setInternalAttachedFile] = useState<File | null>(null);
  const [internalIsUploading, setInternalIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Use external file state if provided, otherwise use internal state
  const attachedFile = externalAttachedFile !== undefined ? externalAttachedFile : internalAttachedFile;
  const isUploading = externalIsUploading || internalIsUploading;
  const [isLoadingDocumentData, setIsLoadingDocumentData] = useState(false); // State for autofill loading

  // Initialize form with default values
  const form = useForm<MarketingContentFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content_type: defaultValues?.content_type || 'marketing_strategy',
      document_format: defaultValues?.document_format || 'report',
      brand_description: defaultValues?.brand_description || '',
      target_audience: defaultValues?.target_audience || '',
      products_services: defaultValues?.products_services || '',
      marketing_goals: defaultValues?.marketing_goals || '',
      existing_content: defaultValues?.existing_content || '',
      keywords: defaultValues?.keywords || '',
      suggested_topics: defaultValues?.suggested_topics || '',
      tone: defaultValues?.tone || 'Professional',
      competitive_landscape: defaultValues?.competitive_landscape || '',
      budget: defaultValues?.budget || '',
      timeline: defaultValues?.timeline || '',
      platforms: defaultValues?.platforms || '',
      // Additional fields for specific content types
      campaign_objective: defaultValues?.campaign_objective || '',
      ad_type: defaultValues?.ad_type || '',
      platform: defaultValues?.platform || '',
      character_limit: defaultValues?.character_limit || undefined,
      announcement_type: defaultValues?.announcement_type || '',
      headline: defaultValues?.headline || '',
      key_details: defaultValues?.key_details || '',
      target_media: defaultValues?.target_media || '',
      product_service: defaultValues?.product_service || '',
      email_type: defaultValues?.email_type || '',
      blog_topic: defaultValues?.blog_topic || '',
      analysis_focus: defaultValues?.analysis_focus || '',
    },
  });

  // Provider selection removed - using concierge agent configuration

  // Provider fetching removed - using concierge agent configuration

  // Function to handle autofill action
  const handleAutofill = async () => {
    // Check if we have either a data source ID or an attached file
    if (!dataSourceId && !attachedFile) {
      toast({
        title: "No Data Source or File",
        description: "Please attach a data source or upload a file first.",
        variant: "default",
      });
      return;
    }

    // If we have an attached file but no dataSourceId, we need to upload it first
    if (attachedFile && !dataSourceId) {
      try {
        toast({
          title: "Uploading File",
          description: `Uploading ${attachedFile.name} for analysis...`,
        });

        const uploadedFile = await fileApi.uploadFile(attachedFile);

        // Use the uploaded file ID for autofill
        await performAutofill(uploadedFile.id);
        return;
      } catch (error) {
        console.error("Error uploading file for autofill:", error);
        toast({
          title: "Upload Failed",
          description: `Failed to upload ${attachedFile.name}. Please try again.`,
          variant: "destructive",
        });
        return;
      }
    }

    // Use existing dataSourceId
    await performAutofill(dataSourceId);
  };

  // Separate function to perform the actual autofill
  const performAutofill = async (fileId: string) => {
    setIsLoadingDocumentData(true);
    try {
      // Get the API base URL from environment or default to localhost
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      const token = localStorage.getItem('token');

      if (!token) {
        toast({
          title: "Authentication Error",
          description: "You need to be logged in to fetch document data.",
          variant: "destructive",
        });
        return;
      }

      console.log(`Querying document with file_id: ${fileId}`);

      // Using concierge agent configuration for AI provider/model
      console.log('Using concierge agent AI configuration for document query');

      const response = await axios.post(
        `${API_BASE_URL}/document-query`,
        {
          file_id: fileId,
          query_type: "marketing_fields"
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.data && response.data.results) {
        const fields = response.data.results;

        // Update form values with document data
        if (fields.brand_description) {
          form.setValue('brand_description', fields.brand_description);
        }

        if (fields.target_audience) {
          form.setValue('target_audience', fields.target_audience);
        }

        if (fields.products_services) {
          form.setValue('products_services', fields.products_services);
        }

        if (fields.marketing_goals) {
          form.setValue('marketing_goals', fields.marketing_goals);
        }

        if (fields.existing_content) {
          form.setValue('existing_content', fields.existing_content);
        }

        if (fields.keywords) {
          form.setValue('keywords', fields.keywords);
        }

        if (fields.suggested_topics) {
          form.setValue('suggested_topics', fields.suggested_topics);
        }

        // Set values for new extended fields
        if (fields.competitive_landscape) {
          form.setValue('competitive_landscape', fields.competitive_landscape);
        }

        if (fields.budget) {
          form.setValue('budget', fields.budget);
        }

        if (fields.timeline) {
          form.setValue('timeline', fields.timeline);
        }

        if (fields.platforms) {
          form.setValue('platforms', fields.platforms);
        }

        toast({
          title: "Form Autofilled",
          description: "Marketing form fields have been filled based on your document.",
        });
      } else {
         toast({
            title: "No Data Found",
            description: "Could not extract relevant information from the document.",
            variant: "default", // Changed from warning
          });
      }
    } catch (error: any) {
      console.error("Failed to fetch document data:", error);

      // Check if it's a 404 error (file not found)
      if (error.response && error.response.status === 404) {
        const errorDetail = error.response.data?.detail || "The attached file could not be found.";
        toast({
          title: "File Not Found",
          description: errorDetail,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to extract data from your document. Please fill the form manually.",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoadingDocumentData(false);
    }
  };

  // Models fetching removed - using concierge agent configuration

  // Handle content type change
  const handleContentTypeChange = (value: string) => {
    setContentType(value);
    form.setValue('content_type', value as any);

    // Auto-populate suggested topics for blog content
    if (value === 'blog_content' && !form.getValues('suggested_topics')) {
      const sampleTopics = generateSampleBlogTopics(form.getValues('brand_description'), form.getValues('products_services'));
      form.setValue('suggested_topics', sampleTopics);
    }
  };

  // Generate sample blog topics based on brand and products (this would be replaced by actual AI in production)
  const generateSampleBlogTopics = (_brandDescription?: string, _productsServices?: string): string => {
    const topics = [
      'How to get started with our products',
      'Industry trends and insights',
      'Customer success stories',
      'Behind the scenes content',
      'Tips and best practices',
      'Product updates and announcements',
      'Thought leadership articles',
      'Educational content for beginners',
      'Advanced techniques and strategies',
      'Common challenges and solutions'
    ];

    // In a real implementation, this would use AI to generate topics based on brand and products
    // For now, we return generic topics that work for most businesses
    return topics.join(', ');
  };

  // File upload handlers
  const handleFileUpload = () => {
    if (onFileUpload) {
      // Use external file upload handler if provided
      onFileUpload();
    } else {
      // Use internal file upload
      fileInputRef.current?.click();
    }
  };

  const handleFileSelected = async (event: React.ChangeEvent<HTMLInputElement>) => {
    // Only handle internal file selection if no external handler is provided
    if (onFileUpload) return;

    const file = event.target.files?.[0];
    if (!file) return;

    setInternalAttachedFile(file);
    setInternalIsUploading(true);

    try {
      // Upload the file
      await fileApi.uploadFile(file);

      toast({
        title: "File Uploaded",
        description: `${file.name} has been uploaded successfully and can be used for content generation.`,
      });

      // Optionally auto-fill form fields based on file content
      // This could be enhanced to analyze the file and extract relevant information

    } catch (error) {
      console.error("Error uploading file:", error);
      toast({
        title: "Upload Failed",
        description: `Failed to upload ${file.name}. Please try again.`,
        variant: "destructive",
      });
      setInternalAttachedFile(null);
    } finally {
      setInternalIsUploading(false);
    }
  };

  const handleRemoveFile = () => {
    if (onRemoveFile) {
      // Use external remove handler if provided
      onRemoveFile();
    } else {
      // Use internal remove
      setInternalAttachedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Get form field descriptions based on content type
  const getFieldDescriptions = () => {
    switch (contentType) {
      case 'marketing_strategy':
        return {
          brand_description: "Describe your brand's identity, values, and positioning",
          target_audience: "Describe your target audience demographics, behaviors, and needs",
          products_services: "List and describe your main products or services",
          marketing_goals: "Outline your marketing objectives (e.g., increase brand awareness, generate leads)",
          existing_content: "Describe any existing marketing materials or campaigns",
          keywords: "List important keywords for your brand and industry",
          suggested_topics: "Suggest topics you'd like included in the strategy",
        };
      case 'campaign_strategy':
        return {
          brand_description: "Describe your brand's identity and values",
          target_audience: "Describe the specific audience for this campaign",
          products_services: "Describe the specific products/services featured in this campaign",
          marketing_goals: "Outline the specific goals for this campaign",
          existing_content: "Describe any existing campaign materials or previous campaigns",
          keywords: "List important keywords for this campaign",
          suggested_topics: "Suggest campaign themes or concepts",
        };
      case 'social_media_content':
        return {
          brand_description: "Describe your brand's identity and social media presence",
          target_audience: "Describe your social media audience",
          products_services: "Describe products/services to feature in social content",
          marketing_goals: "Outline your social media objectives",
          existing_content: "Describe your current social media content",
          keywords: "List hashtags and keywords for social media",
          suggested_topics: "Suggest topics for social media posts",
        };
      case 'seo_optimization':
        return {
          brand_description: "Describe your brand and website",
          target_audience: "Describe your target audience for SEO",
          products_services: "Describe products/services to optimize for search",
          marketing_goals: "Outline your SEO objectives",
          existing_content: "Describe your current website content and SEO status",
          keywords: "List target keywords for SEO (very important for this content type)",
          suggested_topics: "Suggest topics for SEO content",
        };
      case 'post_composer':
        return {
          brand_description: "Describe your brand's voice and style",
          target_audience: "Describe the audience for this post",
          products_services: "Describe products/services to feature in this post",
          marketing_goals: "What do you want to achieve with this post?",
          existing_content: "Describe any existing posts or content to reference",
          keywords: "List keywords or hashtags to include",
          suggested_topics: "Suggest specific topics for this post",
        };
      case 'brand_positioning':
        return {
          brand_description: "Describe your brand's current identity and values",
          target_audience: "Describe your target audience for positioning",
          products_services: "Describe your products/services to position",
          marketing_goals: "Outline your positioning objectives",
          existing_content: "Describe current brand messaging and content",
          keywords: "List brand-related keywords and terms",
          suggested_topics: "Suggest positioning themes or concepts",
        };
      case 'content_calendar':
        return {
          brand_description: "Describe your brand's content style and voice",
          target_audience: "Describe your content audience",
          products_services: "Describe products/services to feature in content",
          marketing_goals: "Outline your content marketing objectives",
          existing_content: "Describe your current content themes and formats",
          keywords: "List content keywords and hashtags",
          suggested_topics: "Suggest content themes and topics for the calendar",
        };
      case 'competitive_analysis':
        return {
          brand_description: "Describe your brand and market position",
          target_audience: "Describe your target market segment",
          products_services: "Describe your products/services for comparison",
          marketing_goals: "Outline what you want to learn from competitor analysis",
          existing_content: "Describe your current competitive intelligence",
          keywords: "List competitor names and industry keywords",
          suggested_topics: "Suggest areas of competitive analysis to focus on",
        };
      case 'email_marketing':
        return {
          brand_description: "Describe your brand's email communication style",
          target_audience: "Describe your email subscribers and prospects",
          products_services: "Describe products/services to promote via email",
          marketing_goals: "Outline your email marketing objectives",
          existing_content: "Describe your current email campaigns and templates",
          keywords: "List email subject line keywords and CTAs",
          suggested_topics: "Suggest email campaign themes and content ideas",
        };
      case 'blog_content':
        return {
          brand_description: "Describe your brand's content voice and expertise",
          target_audience: "Describe your blog readers and their interests",
          products_services: "Describe products/services to feature in blog content",
          marketing_goals: "Outline your content marketing objectives",
          existing_content: "Describe your current blog content and themes",
          keywords: "List SEO keywords and topics for blog posts",
          suggested_topics: "Suggest specific blog post topics and themes",
        };
      case 'ad_copy':
        return {
          brand_description: "Describe your brand's advertising voice and style",
          target_audience: "Describe your target audience for ads",
          products_services: "Describe products/services to advertise",
          marketing_goals: "Outline your advertising objectives",
          existing_content: "Describe your current advertising campaigns",
          keywords: "List key advertising keywords and phrases",
          suggested_topics: "Suggest ad themes and messaging angles",
        };
      case 'press_release':
        return {
          brand_description: "Describe your company and brand for media",
          target_audience: "Describe your target media and audience",
          products_services: "Describe what you're announcing",
          marketing_goals: "Outline your PR objectives",
          existing_content: "Describe previous press releases or media coverage",
          keywords: "List key terms and industry keywords",
          suggested_topics: "Suggest newsworthy angles and topics",
        };
      case 'competitor_analysis':
        return {
          brand_description: "Describe your brand and market position",
          target_audience: "Describe your target market segment",
          products_services: "Describe your products/services for comparison",
          marketing_goals: "Outline what you want to learn from competitor analysis",
          existing_content: "Describe your current competitive intelligence",
          keywords: "List competitor names and industry keywords",
          suggested_topics: "Suggest areas of competitive analysis to focus on",
        };
      case 'audience_research':
        return {
          brand_description: "Describe your brand and current market understanding",
          target_audience: "Describe your current understanding of your audience",
          products_services: "Describe your products/services for audience analysis",
          marketing_goals: "Outline what you want to learn about your audience",
          existing_content: "Describe your current audience data and insights",
          keywords: "List audience-related keywords and demographics",
          suggested_topics: "Suggest areas of audience research to focus on",
        };
      case 'market_analysis':
        return {
          brand_description: "Describe your brand and market position",
          target_audience: "Describe your target market segment",
          products_services: "Describe your products/services for market analysis",
          marketing_goals: "Outline what you want to learn about the market",
          existing_content: "Describe your current market intelligence",
          keywords: "List market-related keywords and industry terms",
          suggested_topics: "Suggest areas of market analysis to focus on",
        };
      default:
        return {
          brand_description: "Describe your brand, its identity, values, and positioning",
          target_audience: "Describe your target audience, their demographics, behaviors, and needs",
          products_services: "Describe your products or services in detail",
          marketing_goals: "Outline your marketing objectives and what you want to achieve",
          existing_content: "Describe any existing marketing materials, campaigns, or content",
          keywords: "List important keywords, terms, and phrases relevant to your business",
          suggested_topics: "Suggest specific topics, themes, or areas you'd like to focus on",
          competitive_landscape: "Information about competitors, market position, and industry landscape",
          budget: "Budget constraints, allocations, or financial considerations",
          timeline: "Timeline constraints, deadlines, or scheduling requirements",
          platforms: "Specific platforms, channels, or mediums for content distribution",
        };
    }
  };

  const fieldDescriptions = getFieldDescriptions();

  // Get content type description
  const getContentTypeDescription = (type: string) => {
    switch (type) {
      case 'marketing_strategy':
        return 'A comprehensive marketing strategy including market analysis, target audience, competitive positioning, and tactical recommendations.';
      case 'campaign_strategy':
        return 'A focused campaign strategy for a specific marketing initiative, including concept, messaging, channels, and timeline.';
      case 'social_media_content':
        return 'Social media content plan with post ideas, content calendar, and platform-specific recommendations.';
      case 'seo_optimization':
        return 'SEO strategy including keyword analysis, on-page optimization recommendations, and content suggestions.';
      case 'post_composer':
        return 'Compose social media posts for specific platforms with appropriate messaging and hashtags.';
      case 'blog_content':
        return 'Blog content strategy with post ideas, SEO optimization, and content calendar recommendations.';
      case 'email_marketing':
        return 'Email marketing campaigns including templates, sequences, and automation strategies.';
      case 'ad_copy':
        return 'Persuasive advertising copy for various platforms including headlines, descriptions, and call-to-actions.';
      case 'press_release':
        return 'Professional press releases for announcements, product launches, and company news.';
      case 'competitor_analysis':
        return 'Comprehensive analysis of competitors including strategies, positioning, and market opportunities.';
      case 'audience_research':
        return 'Detailed audience research including demographics, behaviors, preferences, and insights.';
      case 'market_analysis':
        return 'Market analysis including trends, opportunities, threats, and strategic recommendations.';
      default:
        return '';
    }
  };

  // Removed console.log

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create Marketing Content</CardTitle>
        <CardDescription>
          Generate professional marketing content tailored to your needs
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="content_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content Type</FormLabel>
                  <FormControl>
                    <ContentTypeSelector
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                        handleContentTypeChange(value);
                      }}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Content Type Description */}
            {contentType && (
              <div className="bg-muted/50 p-4 rounded-lg border">
                <h4 className="font-medium text-sm mb-2">What you'll get:</h4>
                <p className="text-sm text-muted-foreground">
                  {getContentTypeDescription(contentType)}
                </p>
              </div>
            )}

            {/* Document Format Selector */}
            <FormField
              control={form.control}
              name="document_format"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Document Format</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select format" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="report">Report</SelectItem>
                      <SelectItem value="presentation">Presentation</SelectItem>
                      <SelectItem value="brief">Brief</SelectItem>
                      <SelectItem value="checklist">Checklist</SelectItem>
                      <SelectItem value="template">Template</SelectItem>
                      <SelectItem value="guide">Guide</SelectItem>
                      <SelectItem value="analysis">Analysis</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose the format for your marketing content output
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* File Upload Section */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Supporting Documents</h3>
                  <p className="text-xs text-gray-500">Upload brand guidelines, existing content, or reference materials</p>
                </div>
                {!attachedFile && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleFileUpload}
                    disabled={isUploading}
                    className="flex items-center gap-2"
                  >
                    {isUploading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                    {isUploading ? 'Uploading...' : 'Upload File'}
                  </Button>
                )}
              </div>

              {/* Show attached file if present */}
              {attachedFile && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Paperclip className="h-4 w-4 text-blue-600" />
                    <div>
                      <span className="text-sm font-medium text-blue-700">
                        {attachedFile.name}
                      </span>
                      <span className="text-xs text-blue-600 ml-2">
                        ({Math.round(attachedFile.size / 1024)} KB)
                      </span>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 text-blue-600 hover:bg-blue-100"
                    onClick={handleRemoveFile}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* Hidden file input */}
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileSelected}
              className="hidden"
              accept=".pdf,.doc,.docx,.txt,.csv,.xlsx,.xls"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="brand_description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your brand..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.brand_description}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="target_audience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Audience</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your target audience..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.target_audience}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="products_services"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Products/Services</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your products or services..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.products_services}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="marketing_goals"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Marketing Goals</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your marketing goals..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.marketing_goals}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {contentType === 'social_media_content' && (
              <FormField
                control={form.control}
                name="existing_content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Existing Social Media Content</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your existing social media content..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.existing_content}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {contentType === 'seo_optimization' && (
              <FormField
                control={form.control}
                name="keywords"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SEO Keywords</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter your target keywords, one per line..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.keywords}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {contentType === 'post_composer' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="suggested_topics"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Post Topic</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="What should this post be about?"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {fieldDescriptions.suggested_topics}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="keywords"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hashtags & Keywords</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter hashtags and keywords to include..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {fieldDescriptions.keywords}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Show existing content field for relevant content types */}
            {!['competitor_analysis', 'audience_research', 'market_analysis', 'data_driven_analysis'].includes(contentType) && (
              <FormField
                control={form.control}
                name="existing_content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Existing Content</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe any existing content or materials..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.existing_content}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Show keywords field for SEO and content-focused types */}
            {['seo_optimization', 'blog_content', 'social_media_content', 'ad_copy'].includes(contentType) && (
              <FormField
                control={form.control}
                name="keywords"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Keywords</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter relevant keywords..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.keywords}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Show suggested topics for content creation types (except blog_content which has its own selector) */}
            {['social_media_content', 'email_marketing', 'content_calendar'].includes(contentType) && (
              <FormField
                control={form.control}
                name="suggested_topics"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Suggested Topics</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Suggest topics or themes..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {fieldDescriptions.suggested_topics}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Show suggested topics input for blog content (used by BlogTopicSelector) */}
            {contentType === 'blog_content' && (
              <FormField
                control={form.control}
                name="suggested_topics"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>Suggested Topics (AI Input)</FormLabel>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newTopics = generateSampleBlogTopics(form.getValues('brand_description'), form.getValues('products_services'));
                          form.setValue('suggested_topics', newTopics);
                        }}
                        className="text-xs"
                      >
                        Generate Topics
                      </Button>
                    </div>
                    <FormControl>
                      <Textarea
                        placeholder="AI will populate this with suggested blog topics..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      This field will be auto-populated by AI with suggested blog topics that you can then select from below
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Additional fields for extended content types */}
            {['marketing_strategy', 'competitor_analysis', 'brand_positioning'].includes(contentType) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="competitive_landscape"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Competitive Landscape</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe your competitors and market landscape..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {fieldDescriptions.competitive_landscape || "Information about competitors, market position, and industry landscape"}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {['marketing_strategy', 'campaign_strategy'].includes(contentType) && (
                  <FormField
                    control={form.control}
                    name="budget"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe budget constraints or considerations..."
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {fieldDescriptions.budget || "Budget constraints, allocations, or financial considerations"}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            )}

            {/* Show timeline and platforms for campaign and strategy types */}
            {['marketing_strategy', 'campaign_strategy', 'social_media_content', 'content_calendar'].includes(contentType) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="timeline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Timeline</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe timeline, deadlines, or scheduling..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {fieldDescriptions.timeline || "Timeline constraints, deadlines, or scheduling requirements"}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="platforms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Platforms</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Specify platforms or channels to focus on..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {fieldDescriptions.platforms || "Specific platforms, channels, or mediums for content distribution"}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Conditional fields based on content type */}
            {(contentType === 'ad_copy' || contentType === 'campaign_strategy') && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="campaign_objective"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Campaign Objective</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="e.g., Increase brand awareness, Generate leads, Drive sales..."
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Specific objective for this campaign
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {contentType === 'ad_copy' && (
                  <FormField
                    control={form.control}
                    name="ad_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ad Type</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Display ad, Search ad, Social media ad..."
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Type of advertisement
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            )}

            {contentType === 'press_release' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="announcement_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Announcement Type</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Product launch, Partnership, Award..."
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Type of announcement
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="headline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Headline</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Main headline for the press release"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Main headline or title
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {contentType === 'email_marketing' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="email_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Type</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Newsletter, Promotional, Welcome series..."
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Type of email campaign
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {contentType === 'blog_content' && (
              <div className="grid grid-cols-1 gap-6">
                <BlogTopicSelector
                  value={form.watch('blog_topic') || ''}
                  onChange={(value: string) => {
                    try {
                      form.setValue('blog_topic', value || '');
                    } catch (error) {
                      console.error('Error setting blog topic value:', error);
                    }
                  }}
                  suggestedTopics={form.watch('suggested_topics') || ''}
                />
              </div>
            )}

            {(contentType === 'competitor_analysis' || contentType === 'audience_research' || contentType === 'market_analysis') && (
              <div className="grid grid-cols-1 gap-6">
                <FormField
                  control={form.control}
                  name="analysis_focus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Analysis Focus</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Specific areas to focus on in the analysis..."
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Specific focus areas for the analysis
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormField
                control={form.control}
                name="tone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tone</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select tone" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Professional">Professional</SelectItem>
                        <SelectItem value="Casual">Casual</SelectItem>
                        <SelectItem value="Friendly">Friendly</SelectItem>
                        <SelectItem value="Authoritative">Authoritative</SelectItem>
                        <SelectItem value="Enthusiastic">Enthusiastic</SelectItem>
                        <SelectItem value="Humorous">Humorous</SelectItem>
                        <SelectItem value="Formal">Formal</SelectItem>
                        <SelectItem value="Inspirational">Inspirational</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the tone for your content
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* AI Provider/Model controls removed - now using concierge agent configuration */}
            </div>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-between items-center"> {/* Added items-center */}
        {/* Left side buttons */}
        <div className="flex gap-2">
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
          <Button variant="outline" onClick={() => {
            form.reset();
            // Also clear attached file if using external file state
            if (onRemoveFile && attachedFile) {
              onRemoveFile();
            }
          }}>
            Reset
          </Button>
          {/* Autofill Button */}
          <Button
            variant="outline"
            onClick={handleAutofill}
            disabled={(!dataSourceId && !attachedFile) || isLoadingDocumentData}
            title={(!dataSourceId && !attachedFile) ? "Attach a data source or upload a file to enable autofill" : ""}
          >
            {isLoadingDocumentData ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Autofill from Source
          </Button>
        </div>

        {/* Right side button */}
        <div className="flex gap-2">
          {/* Removed loading indicator from here */}
          <Button
            onClick={form.handleSubmit((data) => {
              // Include attached file information in the submission
              const submissionData = {
                ...data,
                attachedFile: attachedFile ? {
                  name: attachedFile.name,
                  size: attachedFile.size,
                  type: attachedFile.type
                } : null
              };
              onSubmit(submissionData);
            })}
            disabled={isLoading || isLoadingDocumentData} // Also disable if autofilling
          >
            {isLoading ? <><Loader2 className="h-4 w-4 animate-spin mr-2" /> Generating...</> : 'Generate Content'}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
