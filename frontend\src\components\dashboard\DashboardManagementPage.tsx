import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Plus, 
  Search, 
  MoreVertical, 
  Settings, 
  Copy, 
  Trash2, 
  Download, 
  Upload,
  Eye,
  Edit,
  Star,
  StarOff,
  Calendar,
  BarChart3,
  Users,
  RefreshCw
} from 'lucide-react';
import { DashboardResponse } from '@/types/dashboard-customization';
import { DashboardSettingsDialog } from './DashboardSettingsDialog';
import { DashboardDataSourceManager } from './DashboardDataSourceManager';
import { useDashboardManagement } from '@/hooks/use-dashboard-management';
import { useToast } from '@/hooks/use-toast';

interface DashboardManagementPageProps {
  className?: string;
}

export const DashboardManagementPage: React.FC<DashboardManagementPageProps> = ({
  className = '',
}) => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDashboard, setSelectedDashboard] = useState<DashboardResponse | null>(null);
  const [showSettingsDialog, setShowSettingsDialog] = useState(false);
  const [showDataSourceManager, setShowDataSourceManager] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const {
    dashboards,
    activeDashboard,
    isLoading,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    setActiveDashboard,
    refreshDashboards,
  } = useDashboardManagement();

  // Filter dashboards based on search term
  const filteredDashboards = dashboards.filter(dashboard =>
    dashboard.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dashboard.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dashboard.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handle dashboard creation
  const handleCreateDashboard = async () => {
    try {
      await createDashboard({
        name: `New Dashboard ${dashboards.length + 1}`,
        description: 'A new dashboard for data visualization',
        is_default: false,
        is_public: false,
        refresh_interval: 300,
        tags: ['new'],
        theme_config: {},
        layout_config: {
          columns: 12,
          rows: 12,
          grid_gap: 16,
          responsive: true,
        },
      });

      toast({
        title: "Dashboard Created",
        description: "New dashboard has been created successfully.",
      });
    } catch (error) {
      console.error('Error creating dashboard:', error);
      toast({
        title: "Creation Failed",
        description: "Failed to create dashboard. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle dashboard settings
  const handleDashboardSettings = (dashboard: DashboardResponse) => {
    setSelectedDashboard(dashboard);
    setShowSettingsDialog(true);
  };

  // Handle data source management
  const handleDataSourceManagement = (dashboard: DashboardResponse) => {
    setSelectedDashboard(dashboard);
    setShowDataSourceManager(true);
  };

  // Handle dashboard duplication
  const handleDuplicateDashboard = async (dashboardId: string, name?: string) => {
    try {
      // This would typically call the duplicate API endpoint
      const originalDashboard = dashboards.find(d => d.id === dashboardId);
      if (originalDashboard) {
        await createDashboard({
          ...originalDashboard,
          name: name || `${originalDashboard.name} (Copy)`,
          is_default: false,
        });

        toast({
          title: "Dashboard Duplicated",
          description: "Dashboard has been duplicated successfully.",
        });
      }
    } catch (error) {
      console.error('Error duplicating dashboard:', error);
      toast({
        title: "Duplication Failed",
        description: "Failed to duplicate dashboard. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle dashboard export
  const handleExportDashboard = (dashboardId: string) => {
    const dashboard = dashboards.find(d => d.id === dashboardId);
    if (dashboard) {
      const dataStr = JSON.stringify(dashboard, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${dashboard.name.replace(/\s+/g, '_')}_dashboard.json`;
      link.click();
      URL.revokeObjectURL(url);

      toast({
        title: "Dashboard Exported",
        description: "Dashboard has been exported successfully.",
      });
    }
  };

  // Handle dashboard import
  const handleImportDashboard = (file: File) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const dashboardData = JSON.parse(e.target?.result as string);
        await createDashboard({
          ...dashboardData,
          name: `${dashboardData.name} (Imported)`,
          is_default: false,
        });

        toast({
          title: "Dashboard Imported",
          description: "Dashboard has been imported successfully.",
        });
      } catch (error) {
        console.error('Error importing dashboard:', error);
        toast({
          title: "Import Failed",
          description: "Failed to import dashboard. Please check the file format.",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);
  };

  // Handle set as default
  const handleSetAsDefault = async (dashboardId: string) => {
    try {
      await updateDashboard(dashboardId, { is_default: true });
      // Update other dashboards to not be default
      await Promise.all(
        dashboards
          .filter(d => d.id !== dashboardId && d.is_default)
          .map(d => updateDashboard(d.id, { is_default: false }))
      );

      toast({
        title: "Default Dashboard Set",
        description: "Dashboard has been set as the default.",
      });
    } catch (error) {
      console.error('Error setting default dashboard:', error);
      toast({
        title: "Update Failed",
        description: "Failed to set default dashboard. Please try again.",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard Management</h1>
          <p className="text-muted-foreground">
            Create, configure, and manage your dashboards
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={refreshDashboards}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleCreateDashboard}>
            <Plus className="h-4 w-4 mr-2" />
            Create Dashboard
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="data-sources">Data Sources</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Search and Filters */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search dashboards..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Dashboard Grid */}
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded"></div>
                      <div className="h-3 bg-muted rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredDashboards.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Dashboards Found</h3>
                <p className="text-muted-foreground text-center mb-4">
                  {searchTerm ? 'No dashboards match your search criteria.' : 'Create your first dashboard to get started.'}
                </p>
                {!searchTerm && (
                  <Button onClick={handleCreateDashboard}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Dashboard
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDashboards.map((dashboard) => (
                <Card key={dashboard.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-lg truncate flex items-center space-x-2">
                          <span>{dashboard.name}</span>
                          {dashboard.is_default && (
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          )}
                        </CardTitle>
                        <CardDescription className="line-clamp-2">
                          {dashboard.description || 'No description provided'}
                        </CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => setActiveDashboard(dashboard.id)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Dashboard
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDashboardSettings(dashboard)}>
                            <Settings className="h-4 w-4 mr-2" />
                            Settings
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDataSourceManagement(dashboard)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Manage Data Sources
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleDuplicateDashboard(dashboard.id)}>
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleExportDashboard(dashboard.id)}>
                            <Download className="h-4 w-4 mr-2" />
                            Export
                          </DropdownMenuItem>
                          {!dashboard.is_default && (
                            <DropdownMenuItem onClick={() => handleSetAsDefault(dashboard.id)}>
                              <Star className="h-4 w-4 mr-2" />
                              Set as Default
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => deleteDashboard(dashboard.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Sections:</span>
                        <Badge variant="outline">{dashboard.section_count || 0}</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Widgets:</span>
                        <Badge variant="outline">{dashboard.widget_count || 0}</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Updated:</span>
                        <span className="text-xs">{formatDate(dashboard.updated_at)}</span>
                      </div>
                      {dashboard.tags && dashboard.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {dashboard.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {dashboard.tags.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{dashboard.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Data Sources Tab */}
        <TabsContent value="data-sources" className="space-y-4">
          {selectedDashboard ? (
            <DashboardDataSourceManager dashboardId={selectedDashboard.id} />
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Users className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Select a Dashboard</h3>
                <p className="text-muted-foreground text-center">
                  Choose a dashboard from the overview tab to manage its data sources.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Dashboards</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboards.length}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboards.filter(d => d.is_public).length} public
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Sections</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboards.reduce((sum, d) => sum + (d.section_count || 0), 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all dashboards
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Widgets</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboards.reduce((sum, d) => sum + (d.widget_count || 0), 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all dashboards
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Default Dashboard</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm font-bold truncate">
                  {dashboards.find(d => d.is_default)?.name || 'None set'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Current default
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Settings Dialog */}
      {selectedDashboard && (
        <DashboardSettingsDialog
          open={showSettingsDialog}
          onOpenChange={setShowSettingsDialog}
          dashboard={selectedDashboard}
          onUpdate={updateDashboard}
          onDelete={deleteDashboard}
          onDuplicate={handleDuplicateDashboard}
          onExport={handleExportDashboard}
          onImport={handleImportDashboard}
        />
      )}
    </div>
  );
};
