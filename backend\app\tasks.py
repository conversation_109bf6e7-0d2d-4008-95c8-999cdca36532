from celery import Celery
from celery.schedules import crontab
import logging

from . import config # Changed to import the config module
from .database import SessionLocal # Changed to relative import
# Import models and services needed for the task
# from backend.app.models.reports import ReportScheduleModel # (Will be needed later)
# from backend.app.services.reports_service import ReportGenerator # (Will be needed later)

logger = logging.getLogger(__name__)

# Initialize Celery
# The broker URL should come from your application's configuration
# Example: 'redis://localhost:6379/0'
# The backend URL is for storing task results, also typically Redis or another DB.
celery_app = Celery(
    "tasks",
    broker=config.REDIS_URL, # Use config.REDIS_URL
    backend=config.REDIS_URL, # Use config.REDIS_URL
    include=['backend.app.tasks'] # Points to this module for task discovery
)

# Optional Celery configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],  # Ignore other content
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    # Optional: configure a beat schedule if you want Celery Beat to manage schedules
    # beat_schedule = {
    #     'run-every-minute-example': {
    #         'task': 'backend.app.tasks.example_task',
    #         'schedule': crontab(minute='*/1'), # Runs every minute
    #     },
    # }
)

@celery_app.task(name="backend.app.tasks.example_task")
def example_task(x, y):
    logger.info(f"Running example task with {x} and {y}")
    return x + y

@celery_app.task(name="backend.app.tasks.generate_scheduled_report_task")
async def generate_scheduled_report_task(schedule_id: str):
    """
    Celery task to generate a report based on a schedule ID.
    """
    logger.info(f"Starting report generation for schedule_id: {schedule_id}")
    db = None
    try:
        db = SessionLocal()
        # Dynamically import here to avoid circular dependencies at module load time
        from ..services.reports_service import ReportGenerator # Changed
        from ..models.reports import ReportScheduleModel # Changed
        from croniter import croniter
        from datetime import datetime, timezone

        report_generator = ReportGenerator(db)
        
        schedule = db.query(ReportScheduleModel).filter(ReportScheduleModel.id == schedule_id).first()

        if not schedule:
            logger.error(f"Report schedule with ID {schedule_id} not found.")
            return
        
        if not schedule.is_active:
            logger.info(f"Report schedule {schedule_id} is not active. Skipping.")
            return

        logger.info(f"Generating report for schedule: {schedule.id}, name: {schedule.report_config.report_name}")
        
        # Generate the report
        # The ReportGenerator's generate_report method expects a Pydantic ReportConfig
        # It will also handle persisting the report to the database.
        generated_report_pydantic = await report_generator.generate_report(schedule.report_config)

        # Persist the generated report (ReportModel) - This should be handled by generate_report
        # This part needs to be implemented in ReportGenerator or here.
        # For now, let's assume generate_report_sync handles saving or returns data to save.
        # Example:
        # report_model_instance = ReportModel(...)
        # db.add(report_model_instance)
        # db.commit()
        
        logger.info(f"Report {generated_report_pydantic.report_id} generated successfully for schedule {schedule_id}.")

        # Update schedule's last_run_status and next_run_time
        schedule.last_run_status = "success"
        now_utc = datetime.now(timezone.utc)
        try:
            iter_schedule = croniter(schedule.cron_expression, now_utc)
            schedule.next_run_time = iter_schedule.get_next(datetime)
        except Exception as e:
            logger.error(f"Error calculating next run time for schedule {schedule_id}: {e}")
            schedule.next_run_time = None # Or handle error appropriately
            schedule.is_active = False # Deactivate if cron is invalid
        
        db.commit()
        logger.info(f"Schedule {schedule_id} updated. Next run: {schedule.next_run_time}")

    except Exception as e:
        logger.error(f"Error generating report for schedule {schedule_id}: {e}", exc_info=True)
        if db and schedule: # Check if schedule was fetched
            schedule.last_run_status = "failed"
            db.commit()
    finally:
        if db:
            db.close()

# Note: To run Celery worker:
# celery -A backend.app.tasks.celery_app worker -l info
# To run Celery Beat (for scheduled tasks defined in beat_schedule):
# celery -A backend.app.tasks.celery_app beat -l info

# It's often better to manage schedules dynamically (e.g., via APScheduler or by querying DB)
# rather than hardcoding in beat_schedule, especially if schedules are user-defined.
# The current approach will be to trigger tasks from the application logic when a schedule is due.
