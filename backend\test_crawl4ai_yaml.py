#!/usr/bin/env python3
"""
Quick test script to verify Crawl4AI YAML parsing functionality.
"""

import sys
import os
import asyncio
import yaml

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.crawl4ai_web_service import Crawl4AIWebService

def test_yaml_parsing():
    """Test the YAML parsing functionality."""
    service = Crawl4AIWebService()

    # Test JSON content (now primary format)
    json_content = """
{
    "business_name": "KCB Group",
    "description": "Leading financial services provider in East Africa",
    "industry": "Financial Services",
    "services": ["Banking", "Insurance", "Investment"],
    "contact_email": "<EMAIL>",
    "contact_phone": "+254-20-3270000",
    "address": "Kencom House, Moi Avenue, Nairobi",
    "website": "https://ke.kcbgroup.com",
    "social_media": {
        "facebook": "https://facebook.com/kcbgroup",
        "twitter": "https://twitter.com/kcbgroup"
    },
    "founded_year": "1896",
    "employee_count": "5000+"
}
"""

    print("Testing JSON parsing (primary)...")
    result = service._parse_extracted_content_as_yaml(json_content, "https://test.com")

    print("Parsed result:")
    print(yaml.dump(result, default_flow_style=False, indent=2))

    # Test JSON with markdown formatting
    json_with_markdown = """```json
{
    "business_name": "Test Company",
    "description": "Test description",
    "industry": "Technology"
}
```"""

    print("\nTesting JSON with markdown formatting...")
    result2 = service._parse_extracted_content_as_yaml(json_with_markdown, "https://test2.com")

    print("Parsed result:")
    print(yaml.dump(result2, default_flow_style=False, indent=2))

    # Test YAML fallback
    yaml_content = """
business_name: "YAML Company"
description: "YAML description"
industry: "Technology"
"""

    print("\nTesting YAML fallback...")
    result3 = service._parse_extracted_content_as_yaml(yaml_content, "https://test3.com")

    print("Parsed result:")
    print(yaml.dump(result3, default_flow_style=False, indent=2))

    # Test malformed content
    print("\nTesting malformed content...")
    result4 = service._parse_extracted_content_as_yaml("This is not valid YAML or JSON", "https://test4.com")

    print("Parsed result:")
    print(yaml.dump(result4, default_flow_style=False, indent=2))

    # Test confidence scoring
    print("\nTesting confidence scoring...")
    confidence = service._calculate_confidence_score(result, None)
    print(f"Confidence score for good data: {confidence}")

    confidence_empty = service._calculate_confidence_score({}, None)
    print(f"Confidence score for empty data: {confidence_empty}")

    confidence_raw = service._calculate_confidence_score({"raw_content": "some text"}, None)
    print(f"Confidence score for raw content: {confidence_raw}")

if __name__ == "__main__":
    test_yaml_parsing()
