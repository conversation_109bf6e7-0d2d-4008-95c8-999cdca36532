import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { WidgetResponse, DataSourceConfig } from '@/types/dashboard-customization';
import { dataSourceApi, DataSource } from '@/lib/dataSourceApi'; // Assuming DataSource type is exported
import { useToast } from '@/hooks/use-toast';

interface WidgetCustomizerProps {
  widget: EnhancedDashboardWidget | null;
  onUpdate: (updates: Partial<EnhancedDashboardWidget>) => void;
  onClose: () => void;
}

export const WidgetCustomizer: React.FC<WidgetCustomizerProps> = ({ widget, onUpdate, onClose }) => {
  const [title, setTitle] = useState('');
  const [selectedDataSourceId, setSelectedDataSourceId] = useState<string | undefined>(undefined);
  const [availableDataSources, setAvailableDataSources] = useState<DataSource[]>([]);
  const [isLoadingDataSources, setIsLoadingDataSources] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (widget) {
      setTitle(widget.title || '');
      setSelectedDataSourceId(widget.dataSource?.id);
    }
  }, [widget]);

  useEffect(() => {
    const fetchDataSources = async () => {
      setIsLoadingDataSources(true);
      try {
        const response = await dataSourceApi.getDataSources();
        setAvailableDataSources(response.data_sources || []);
      } catch (error) {
        console.error('Failed to fetch data sources:', error);
        toast({
          title: 'Error',
          description: 'Could not load data sources.',
          variant: 'destructive',
        });
        setAvailableDataSources([]);
      }
      setIsLoadingDataSources(false);
    };
    fetchDataSources();
  }, [toast]);

  const handleSave = () => {
    if (!widget) return;

    const updates: Partial<EnhancedDashboardWidget> = {
      title,
    };

    if (selectedDataSourceId) {
      const selectedDS = availableDataSources.find(ds => ds.id === selectedDataSourceId);
      if (selectedDS) {
        updates.dataSource = {
          id: selectedDS.id,
          name: selectedDS.name,
          type: selectedDS.type,
          // filters can be added here if UI for them exists
        };
      }
    } else {
      updates.dataSource = undefined; // Or null, depending on how you want to clear it
    }
    
    // Potentially clear widget data if data source changes, or let backend handle it
    // updates.data = {}; // Or fetch new data based on new source

    onUpdate(updates);
    onClose();
  };

  if (!widget) {
    return null;
  }

  return (
    <Dialog open={!!widget} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Customize Widget: {widget.title}</DialogTitle>
          <DialogDescription>
            Modify the widget's settings and connect it to a data source.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="widget-title" className="text-right">
              Title
            </Label>
            <Input
              id="widget-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="data-source" className="text-right">
              Data Source
            </Label>
            <Select
              value={selectedDataSourceId}
              onValueChange={setSelectedDataSourceId}
              disabled={isLoadingDataSources}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder={isLoadingDataSources ? "Loading..." : "Select a data source"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None (Use sample or no data)</SelectItem>
                {availableDataSources.map((ds) => (
                  <SelectItem key={ds.id} value={ds.id}>
                    {ds.name} ({ds.type})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {/* Add more customization fields here, e.g., refresh interval, chart type specific options */}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
