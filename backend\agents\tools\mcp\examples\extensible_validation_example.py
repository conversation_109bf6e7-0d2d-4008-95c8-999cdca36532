"""
Example demonstrating the extensible validation system for MCP tools.

This example shows various ways to create and register validation schemas
for new MCP tools, making the system automatically extensible.
"""

import logging
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

# Import the extensible validation components
from ..validation.schema_decorators import (
    auto_register_schema, 
    with_validation_schema, 
    schema_field,
    create_tool_schema,
    SchemaBuilder
)
from ..validation.input_validator import BaseInputSchema
from ..base import BaseMCPTool

logger = logging.getLogger(__name__)


# Method 1: Using decorator with custom schema
class CustomAnalysisSchema(BaseInputSchema):
    """Custom schema for analysis tool."""
    
    data_source: str = Field(description="Source of data to analyze")
    analysis_type: str = Field(description="Type of analysis to perform")
    depth: Optional[str] = Field(default="standard", description="Analysis depth")
    include_charts: Optional[bool] = Field(default=True, description="Include visualizations")
    max_results: Optional[int] = Field(default=100, ge=1, le=1000, description="Maximum results")


@with_validation_schema(CustomAnalysisSchema)
class CustomAnalysisTool(BaseMCPTool):
    """Example tool with custom validation schema."""
    
    def __init__(self):
        super().__init__(
            name="custom_analysis",
            description="Perform custom data analysis",
            input_schema={
                "type": "object",
                "properties": {
                    "data_source": {"type": "string"},
                    "analysis_type": {"type": "string"},
                    "depth": {"type": "string"},
                    "include_charts": {"type": "boolean"},
                    "max_results": {"type": "integer"}
                },
                "required": ["data_source", "analysis_type"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the analysis."""
        # Tool implementation here
        return {
            "content": [{
                "type": "text",
                "text": f"Analysis completed for {arguments.get('data_source')}"
            }]
        }


# Method 2: Using auto-registration decorator (infers schema)
@auto_register_schema()
class SmartTextProcessor(BaseMCPTool):
    """Tool that automatically gets its schema inferred."""
    
    def __init__(self):
        super().__init__(
            name="smart_text_processor",
            description="Process text with smart algorithms",
            input_schema={
                "type": "object",
                "properties": {
                    "text": {"type": "string", "description": "Text to process"},
                    "operation": {
                        "type": "string", 
                        "enum": ["summarize", "analyze", "extract", "transform"],
                        "description": "Processing operation"
                    },
                    "language": {"type": "string", "default": "auto", "description": "Text language"},
                    "options": {"type": "object", "description": "Additional options"}
                },
                "required": ["text", "operation"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute text processing."""
        return {
            "content": [{
                "type": "text", 
                "text": f"Processed text with {arguments.get('operation')}"
            }]
        }


# Method 3: Using SchemaBuilder for dynamic creation
def create_dynamic_tool_schema():
    """Example of creating a schema dynamically."""
    
    builder = SchemaBuilder()
    
    # Add fields
    builder.add_field("input_data", str, "Input data for processing", min_length=1)
    builder.add_field("format", str, "Output format", default="json")
    builder.add_field("validate_input", bool, "Whether to validate input", default=True)
    builder.add_field("timeout", int, "Processing timeout", default=30, ge=1, le=300)
    
    # Add custom validator
    def validate_format(cls, v):
        allowed_formats = ["json", "xml", "csv", "text"]
        if v not in allowed_formats:
            raise ValueError(f"Format must be one of {allowed_formats}")
        return v
    
    builder.add_validator("format", validate_format)
    
    # Build the schema
    return builder.build("DynamicProcessorSchema")


# Method 4: Using create_tool_schema helper
def register_batch_processor():
    """Example of registering a tool schema using helper function."""
    
    schema = create_tool_schema("batch_processor", {
        'files': schema_field(List[str], "List of files to process"),
        'operation': schema_field(str, "Batch operation to perform"),
        'parallel': schema_field(bool, "Process files in parallel", default=True),
        'max_workers': schema_field(int, "Maximum worker threads", default=4, ge=1, le=16),
        'output_dir': schema_field(Optional[str], "Output directory", default=None)
    })
    
    return schema


# Method 5: Runtime registration
class RuntimeTool(BaseMCPTool):
    """Tool registered at runtime."""
    
    def __init__(self):
        super().__init__(
            name="runtime_tool",
            description="Tool registered at runtime",
            input_schema={
                "type": "object",
                "properties": {
                    "action": {"type": "string"},
                    "parameters": {"type": "object"}
                },
                "required": ["action"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        return {"content": [{"type": "text", "text": "Runtime tool executed"}]}


def register_runtime_tool():
    """Register a tool at runtime."""
    from ..validation.input_validator import get_input_validator
    
    # Create the tool
    tool = RuntimeTool()
    
    # Get validator and register schema
    validator = get_input_validator()
    success = validator.register_tool_schema("runtime_tool", RuntimeTool)
    
    if success:
        logger.info("Successfully registered runtime tool schema")
    else:
        logger.warning("Failed to register runtime tool schema")


# Method 6: Configuration-based registration
def create_config_based_schema():
    """Example of schema that would be loaded from configuration."""
    
    # This would typically be in schemas.yaml:
    config_example = {
        "advanced_processor": {
            "schema_type": "dynamic",
            "base_schema": "BaseInputSchema",
            "enabled": True,
            "priority": 50,
            "fields": {
                "input_source": {
                    "type": "str",
                    "description": "Source of input data",
                    "required": True
                },
                "processing_mode": {
                    "type": "str", 
                    "description": "Mode of processing",
                    "required": False,
                    "default": "standard"
                },
                "advanced_options": {
                    "type": "dict",
                    "description": "Advanced processing options",
                    "required": False,
                    "default": {}
                }
            }
        }
    }
    
    return config_example


# Example usage and testing
def demonstrate_extensible_validation():
    """Demonstrate the extensible validation system."""
    
    logger.info("=== Extensible Validation System Demo ===")
    
    # 1. Test auto-discovery
    from ..validation.input_validator import get_input_validator
    validator = get_input_validator()
    
    # 2. Show discovery stats
    stats = validator.get_discovery_stats()
    logger.info(f"Discovery stats: {stats}")
    
    # 3. List all registered tools
    tools = validator.get_all_registered_tools()
    logger.info(f"Registered tools: {len(tools)} tools")
    
    # 4. Test schema inference
    test_data = {
        "data_source": "test.csv",
        "analysis_type": "statistical",
        "depth": "deep",
        "include_charts": True,
        "max_results": 50
    }
    
    result = validator.validate_input("custom_analysis", test_data)
    logger.info(f"Validation result: {result.is_valid}")
    
    # 5. Register runtime tool
    register_runtime_tool()
    
    # 6. Refresh discovery
    validator.refresh_discovery()
    
    logger.info("=== Demo completed ===")


# Integration with existing tools
def upgrade_existing_tool():
    """Example of upgrading an existing tool with validation."""
    
    # For existing tools, you can add validation retroactively:
    from ..validation.schema_decorators import register_tool_schema
    
    # Create schema for existing tool
    class ExistingToolSchema(BaseInputSchema):
        query: str = Field(description="Search query")
        filters: Optional[Dict[str, Any]] = Field(default={}, description="Search filters")
        limit: Optional[int] = Field(default=10, ge=1, le=100, description="Result limit")
    
    # Register it
    register_tool_schema("existing_search_tool", ExistingToolSchema)
    
    logger.info("Upgraded existing tool with validation schema")


if __name__ == "__main__":
    # Run the demonstration
    logging.basicConfig(level=logging.INFO)
    demonstrate_extensible_validation()
