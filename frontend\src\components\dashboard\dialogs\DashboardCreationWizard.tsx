/**
 * Dashboard Creation Wizard
 * 
 * Multi-step wizard for creating new dashboards with guided setup.
 * Features:
 * - Step-by-step dashboard configuration
 * - Template selection
 * - Basic settings (name, description, privacy)
 * - Layout and theme configuration
 * - Data source integration
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ChevronLeft,
  ChevronRight,
  Check,
  BarChart3,
  Pie<PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Activity,
  Users,
  DollarSign,
  TrendingUp,
  Grid,
  Layout,
  Palette,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';
import { useToast } from '@/hooks/use-toast';

interface DashboardCreationWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDashboardCreated?: (dashboardId: string) => void;
}

interface DashboardTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  category: string;
  sections: string[];
  widgets: string[];
}

const DASHBOARD_TEMPLATES: DashboardTemplate[] = [
  {
    id: 'analytics',
    name: 'Analytics Dashboard',
    description: 'Track website traffic, user engagement, and conversion metrics',
    icon: BarChart3,
    category: 'Business',
    sections: ['Overview', 'Traffic', 'Conversions'],
    widgets: ['Page Views', 'Unique Visitors', 'Bounce Rate', 'Conversion Rate'],
  },
  {
    id: 'sales',
    name: 'Sales Dashboard',
    description: 'Monitor sales performance, revenue, and team metrics',
    icon: DollarSign,
    category: 'Sales',
    sections: ['Revenue', 'Performance', 'Team'],
    widgets: ['Total Revenue', 'Sales by Region', 'Top Products', 'Team Performance'],
  },
  {
    id: 'operations',
    name: 'Operations Dashboard',
    description: 'Track operational metrics, system health, and performance',
    icon: Activity,
    category: 'Operations',
    sections: ['System Health', 'Performance', 'Alerts'],
    widgets: ['System Status', 'Response Time', 'Error Rate', 'Active Alerts'],
  },
  {
    id: 'marketing',
    name: 'Marketing Dashboard',
    description: 'Monitor campaigns, leads, and marketing ROI',
    icon: TrendingUp,
    category: 'Marketing',
    sections: ['Campaigns', 'Leads', 'ROI'],
    widgets: ['Campaign Performance', 'Lead Generation', 'Cost per Lead', 'ROI'],
  },
  {
    id: 'blank',
    name: 'Blank Dashboard',
    description: 'Start with a clean slate and build your own dashboard',
    icon: Grid,
    category: 'Custom',
    sections: [],
    widgets: [],
  },
];

const THEMES = [
  { id: 'default', name: 'Default', primary: '#3B82F6', secondary: '#10B981' },
  { id: 'dark', name: 'Dark', primary: '#6366F1', secondary: '#8B5CF6' },
  { id: 'green', name: 'Green', primary: '#10B981', secondary: '#059669' },
  { id: 'purple', name: 'Purple', primary: '#8B5CF6', secondary: '#7C3AED' },
  { id: 'orange', name: 'Orange', primary: '#F59E0B', secondary: '#D97706' },
];

export const DashboardCreationWizard: React.FC<DashboardCreationWizardProps> = ({
  open,
  onOpenChange,
  onDashboardCreated,
}) => {
  const { toast } = useToast();
  const { createDashboard } = useUnifiedDashboardStore();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [isCreating, setIsCreating] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: false,
    template: '',
    theme: 'default',
    layout: 'grid',
  });

  const steps = [
    { id: 'template', title: 'Choose Template', description: 'Select a template to get started' },
    { id: 'details', title: 'Dashboard Details', description: 'Configure basic settings' },
    { id: 'appearance', title: 'Appearance', description: 'Customize theme and layout' },
    { id: 'review', title: 'Review', description: 'Review and create dashboard' },
  ];

  const selectedTemplate = DASHBOARD_TEMPLATES.find(t => t.id === formData.template);
  const selectedTheme = THEMES.find(t => t.id === formData.theme);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCreateDashboard = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Dashboard name is required.",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const dashboardData = {
        name: formData.name,
        description: formData.description,
        is_public: formData.isPublic,
        is_default: false,
        layout_config: {
          columns: 12,
          rows: 12,
          grid_gap: 16,
          responsive: true,
          layout_type: formData.layout,
        },
        theme_config: {
          primary_color: selectedTheme?.primary || '#3B82F6',
          secondary_color: selectedTheme?.secondary || '#10B981',
          background_color: '#F9FAFB',
          text_color: '#1F2937',
          theme_name: formData.theme,
        },
        refresh_interval: 300,
        tags: selectedTemplate ? [selectedTemplate.category.toLowerCase()] : ['custom'],
      };

      const newDashboard = await createDashboard(dashboardData);
      
      toast({
        title: "Dashboard Created",
        description: `"${formData.name}" has been created successfully.`,
      });

      onDashboardCreated?.(newDashboard.id);
      onOpenChange(false);
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        isPublic: false,
        template: '',
        theme: 'default',
        layout: 'grid',
      });
      setCurrentStep(0);
    } catch (error) {
      toast({
        title: "Creation Failed",
        description: "Failed to create dashboard. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Template selection
        return formData.template !== '';
      case 1: // Details
        return formData.name.trim() !== '';
      case 2: // Appearance
        return true;
      case 3: // Review
        return true;
      default:
        return false;
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Template Selection
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {DASHBOARD_TEMPLATES.map((template) => (
                <Card
                  key={template.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    formData.template === template.id && "ring-2 ring-primary"
                  )}
                  onClick={() => setFormData({ ...formData, template: template.id })}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center space-x-3">
                      <template.icon className="h-8 w-8 text-primary" />
                      <div>
                        <CardTitle className="text-sm">{template.name}</CardTitle>
                        <Badge variant="outline" className="text-xs">
                          {template.category}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">
                      {template.description}
                    </p>
                    {template.sections.length > 0 && (
                      <div>
                        <p className="text-xs font-medium mb-1">Includes:</p>
                        <div className="flex flex-wrap gap-1">
                          {template.sections.slice(0, 3).map((section) => (
                            <Badge key={section} variant="secondary" className="text-xs">
                              {section}
                            </Badge>
                          ))}
                          {template.sections.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{template.sections.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      case 1: // Dashboard Details
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Dashboard Name *</Label>
              <Input
                id="name"
                placeholder="Enter dashboard name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe what this dashboard will track"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="public"
                checked={formData.isPublic}
                onCheckedChange={(checked) => setFormData({ ...formData, isPublic: checked })}
              />
              <Label htmlFor="public">Make this dashboard public</Label>
            </div>
            {selectedTemplate && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Selected Template</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-3">
                    <selectedTemplate.icon className="h-6 w-6 text-primary" />
                    <div>
                      <p className="font-medium">{selectedTemplate.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {selectedTemplate.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );

      case 2: // Appearance
        return (
          <div className="space-y-6">
            <div className="space-y-3">
              <Label>Theme</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {THEMES.map((theme) => (
                  <Card
                    key={theme.id}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-md",
                      formData.theme === theme.id && "ring-2 ring-primary"
                    )}
                    onClick={() => setFormData({ ...formData, theme: theme.id })}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: theme.primary }}
                          />
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: theme.secondary }}
                          />
                        </div>
                        <span className="text-sm font-medium">{theme.name}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            <div className="space-y-3">
              <Label>Layout Style</Label>
              <Select value={formData.layout} onValueChange={(value) => setFormData({ ...formData, layout: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="grid">Grid Layout</SelectItem>
                  <SelectItem value="masonry">Masonry Layout</SelectItem>
                  <SelectItem value="fixed">Fixed Layout</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 3: // Review
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Review Dashboard Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Name</Label>
                    <p className="text-sm text-muted-foreground">{formData.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Privacy</Label>
                    <p className="text-sm text-muted-foreground">
                      {formData.isPublic ? 'Public' : 'Private'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Template</Label>
                    <p className="text-sm text-muted-foreground">
                      {selectedTemplate?.name || 'None'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Theme</Label>
                    <p className="text-sm text-muted-foreground">
                      {selectedTheme?.name || 'Default'}
                    </p>
                  </div>
                </div>
                {formData.description && (
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-sm text-muted-foreground">{formData.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Create New Dashboard</DialogTitle>
          <DialogDescription>
            Follow the steps to create your new dashboard.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
                    index <= currentStep
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                  )}
                >
                  {index < currentStep ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    index + 1
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "w-12 h-0.5 mx-2",
                      index < currentStep ? "bg-primary" : "bg-muted"
                    )}
                  />
                )}
              </div>
            ))}
          </div>

          {/* Step Content */}
          <div className="min-h-[400px]">
            <div className="mb-4">
              <h3 className="text-lg font-semibold">{steps[currentStep].title}</h3>
              <p className="text-sm text-muted-foreground">
                {steps[currentStep].description}
              </p>
            </div>
            <div className="max-h-80 overflow-y-auto">
              {renderStepContent()}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            {currentStep === steps.length - 1 ? (
              <Button
                onClick={handleCreateDashboard}
                disabled={!canProceed() || isCreating}
              >
                {isCreating ? 'Creating...' : 'Create Dashboard'}
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                disabled={!canProceed()}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
