import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BlogTopicSelector } from './BlogTopicSelector';

describe('BlogTopicSelector', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('renders without crashing', () => {
    render(
      <BlogTopicSelector
        value=""
        onChange={mockOnChange}
        suggestedTopics=""
      />
    );
    
    expect(screen.getByText('Select blog topics...')).toBeInTheDocument();
  });

  it('handles suggested topics correctly', () => {
    const suggestedTopics = "AI Technology, Machine Learning, Data Science";

    render(
      <BlogTopicSelector
        value=""
        onChange={mockOnChange}
        suggestedTopics={suggestedTopics}
      />
    );

    expect(screen.getByText('3 AI-suggested topics available. Select multiple topics or add your own.')).toBeInTheDocument();
  });

  it('parses numbered bold topics correctly', () => {
    const suggestedTopics = `Based on the provided context, here are seven content topics relevant for DataGent's marketing strategy:

1. **The Importance of Conversational NLP in Modern AI Solutions**
   - Highlight DataGent's true conversational NLP interface and its benefits.

2. **Predictive Modeling: Built-In vs. Add-On**
   - Discuss the advantages of DataGent's built-in predictive modeling capabilities.

3. **Establishing Industry Standards for AI-Generated Insights**
   - Explore DataGent's role in setting industry standards and the importance of this effort.`;

    render(
      <BlogTopicSelector
        value=""
        onChange={mockOnChange}
        suggestedTopics={suggestedTopics}
      />
    );

    expect(screen.getByText('3 AI-suggested topics available. Select multiple topics or add your own.')).toBeInTheDocument();
  });

  it('handles empty suggested topics gracefully', () => {
    render(
      <BlogTopicSelector
        value=""
        onChange={mockOnChange}
        suggestedTopics=""
      />
    );
    
    expect(screen.getByText('Add custom blog topics or let AI suggest topics in the \'Suggested Topics\' field above.')).toBeInTheDocument();
  });

  it('handles invalid suggested topics gracefully', () => {
    render(
      <BlogTopicSelector
        value=""
        onChange={mockOnChange}
        suggestedTopics={null as any}
      />
    );
    
    // Should not crash and should show fallback message
    expect(screen.getByText('Add custom blog topics or let AI suggest topics in the \'Suggested Topics\' field above.')).toBeInTheDocument();
  });

  it('can add custom topics', () => {
    render(
      <BlogTopicSelector
        value=""
        onChange={mockOnChange}
        suggestedTopics=""
      />
    );
    
    const input = screen.getByPlaceholderText('Add custom topic...');
    const addButton = screen.getByRole('button', { name: /plus/i });
    
    fireEvent.change(input, { target: { value: 'Custom Topic' } });
    fireEvent.click(addButton);
    
    expect(mockOnChange).toHaveBeenCalledWith('Custom Topic');
  });

  it('extracts clean topic titles from formatted content', () => {
    // Test the parsing logic directly by checking console output
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    const complexSuggestedTopics = `Based on the provided context, here are seven content topics:

1. **The Importance of Conversational NLP in Modern AI Solutions**
   - Highlight DataGent's true conversational NLP interface and its benefits.

2. **Predictive Modeling: Built-In vs. Add-On**
   - Discuss the advantages of DataGent's built-in predictive modeling capabilities.`;

    render(
      <BlogTopicSelector
        value=""
        onChange={mockOnChange}
        suggestedTopics={complexSuggestedTopics}
      />
    );

    // Check that the parsing extracted clean topic titles
    const logCalls = consoleSpy.mock.calls;
    const parsedTopicsCall = logCalls.find(call =>
      call[0] === 'BlogTopicSelector - Parsed topics:' && Array.isArray(call[1])
    );

    if (parsedTopicsCall) {
      const topics = parsedTopicsCall[1] as string[];
      expect(topics).toContain('The Importance of Conversational NLP in Modern AI Solutions');
      expect(topics).toContain('Predictive Modeling: Built-In vs. Add-On');
    }

    consoleSpy.mockRestore();
  });
});
