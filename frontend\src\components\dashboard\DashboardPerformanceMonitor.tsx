import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Activity, 
  Clock, 
  MemoryStick, 
  AlertTriangle, 
  CheckCircle2,
  TrendingUp,
  TrendingDown,
  Zap,
  Database,
  Cpu,
  Wifi,
  X
} from 'lucide-react';
import { useDashboardPerformance, PerformanceAlert } from '@/hooks/use-dashboard-performance';

interface DashboardPerformanceMonitorProps {
  className?: string;
  showInline?: boolean;
  enableAlerts?: boolean;
}

export const DashboardPerformanceMonitor: React.FC<DashboardPerformanceMonitorProps> = ({
  className = '',
  showInline = false,
  enableAlerts = true,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const {
    metrics,
    alerts,
    isMonitoring,
    thresholds,
    startMonitoring,
    stopMonitoring,
    clearAlerts,
    resetMetrics,
    getPerformanceSummary,
  } = useDashboardPerformance({}, enableAlerts);

  const summary = getPerformanceSummary();

  // Auto-start monitoring
  useEffect(() => {
    if (!isMonitoring) {
      startMonitoring();
    }
    
    return () => {
      stopMonitoring();
    };
  }, [isMonitoring, startMonitoring, stopMonitoring]);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getStatusColor = (value: number, threshold: number) => {
    if (value <= threshold) return 'text-green-600';
    if (value <= threshold * 1.5) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (value: number, threshold: number) => {
    if (value <= threshold) return <CheckCircle2 className="h-4 w-4 text-green-600" />;
    if (value <= threshold * 1.5) return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    return <AlertTriangle className="h-4 w-4 text-red-600" />;
  };

  const getProgressValue = (value: number, threshold: number) => {
    return Math.min((value / threshold) * 100, 100);
  };

  const PerformanceContent = () => (
    <div className="space-y-4">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center space-x-2">
                  <Activity className="h-4 w-4" />
                  <span>Overall Status</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Badge 
                    variant={
                      summary.overall === 'good' ? 'default' : 
                      summary.overall === 'warning' ? 'secondary' : 'destructive'
                    }
                  >
                    {summary.overall.toUpperCase()}
                  </Badge>
                  {summary.overall === 'good' ? (
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  )}
                </div>
                <div className="text-xs text-muted-foreground mt-2">
                  {summary.issues.length === 0 ? 'No issues detected' : `${summary.issues.length} issues found`}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center space-x-2">
                  <Zap className="h-4 w-4" />
                  <span>Monitoring</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Badge variant={isMonitoring ? 'default' : 'secondary'}>
                    {isMonitoring ? 'ACTIVE' : 'INACTIVE'}
                  </Badge>
                  <div className={`h-2 w-2 rounded-full ${isMonitoring ? 'bg-green-500' : 'bg-gray-400'}`} />
                </div>
                <div className="text-xs text-muted-foreground mt-2">
                  Last update: {metrics.lastUpdate.toLocaleTimeString()}
                </div>
              </CardContent>
            </Card>
          </div>

          {summary.issues.length > 0 && (
            <Card className="border-yellow-200">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-yellow-700">Issues Detected</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="text-sm space-y-1">
                  {summary.issues.map((issue, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <AlertTriangle className="h-3 w-3 text-yellow-600" />
                      <span>{issue}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {summary.recommendations.length > 0 && (
            <Card className="border-blue-200">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-blue-700">Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="text-sm space-y-1">
                  {summary.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <TrendingUp className="h-3 w-3 text-blue-600" />
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span>Render Time</span>
                  </div>
                  {getStatusIcon(metrics.renderTime, thresholds.maxRenderTime)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className={getStatusColor(metrics.renderTime, thresholds.maxRenderTime)}>
                      {formatTime(metrics.renderTime)}
                    </span>
                    <span className="text-muted-foreground">
                      Target: {formatTime(thresholds.maxRenderTime)}
                    </span>
                  </div>
                  <Progress 
                    value={getProgressValue(metrics.renderTime, thresholds.maxRenderTime)}
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Database className="h-4 w-4" />
                    <span>Data Load Time</span>
                  </div>
                  {getStatusIcon(metrics.dataLoadTime, thresholds.maxDataLoadTime)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className={getStatusColor(metrics.dataLoadTime, thresholds.maxDataLoadTime)}>
                      {formatTime(metrics.dataLoadTime)}
                    </span>
                    <span className="text-muted-foreground">
                      Target: {formatTime(thresholds.maxDataLoadTime)}
                    </span>
                  </div>
                  <Progress 
                    value={getProgressValue(metrics.dataLoadTime, thresholds.maxDataLoadTime)}
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MemoryStick className="h-4 w-4" />
                    <span>Memory Usage</span>
                  </div>
                  {getStatusIcon(metrics.memoryUsage, thresholds.maxMemoryUsage)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className={getStatusColor(metrics.memoryUsage, thresholds.maxMemoryUsage)}>
                      {formatBytes(metrics.memoryUsage)}
                    </span>
                    <span className="text-muted-foreground">
                      Target: {formatBytes(thresholds.maxMemoryUsage)}
                    </span>
                  </div>
                  <Progress 
                    value={getProgressValue(metrics.memoryUsage, thresholds.maxMemoryUsage)}
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center space-x-2">
                    <Cpu className="h-4 w-4" />
                    <span>Components</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.componentCount}</div>
                  <div className="text-xs text-muted-foreground">Active widgets</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4" />
                    <span>Errors</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${getStatusColor(metrics.errorCount, thresholds.maxErrorCount)}`}>
                    {metrics.errorCount}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Max: {thresholds.maxErrorCount}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium">
              Performance Alerts ({alerts.length})
            </div>
            {alerts.length > 0 && (
              <Button variant="outline" size="sm" onClick={clearAlerts}>
                Clear All
              </Button>
            )}
          </div>

          {alerts.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center">
                  <CheckCircle2 className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-sm font-medium">No alerts</div>
                  <div className="text-xs text-muted-foreground">Performance is within normal ranges</div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-2">
              {alerts.map((alert, index) => (
                <Card key={index} className={`border-l-4 ${
                  alert.type === 'error' ? 'border-l-red-500' : 'border-l-yellow-500'
                }`}>
                  <CardContent className="p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Badge variant={alert.type === 'error' ? 'destructive' : 'secondary'}>
                            {alert.type.toUpperCase()}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {alert.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        <div className="text-sm mt-1">{alert.message}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <div className="flex justify-between">
        <Button variant="outline" onClick={resetMetrics}>
          Reset Metrics
        </Button>
        <Button 
          variant="outline" 
          onClick={isMonitoring ? stopMonitoring : startMonitoring}
        >
          {isMonitoring ? 'Stop' : 'Start'} Monitoring
        </Button>
      </div>
    </div>
  );

  if (showInline) {
    return (
      <div className={className}>
        <PerformanceContent />
      </div>
    );
  }

  return (
    <TooltipProvider>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" className={className}>
                <Activity className="h-4 w-4 mr-2" />
                Performance
                {alerts.length > 0 && (
                  <Badge variant="destructive" className="ml-2 h-4 w-4 p-0 text-xs">
                    {alerts.length}
                  </Badge>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Dashboard Performance Monitor</p>
              <p className="text-xs">Status: {summary.overall}</p>
            </TooltipContent>
          </Tooltip>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Performance Monitor</span>
            </DialogTitle>
            <DialogDescription>
              Monitor dashboard performance metrics and receive alerts for issues.
            </DialogDescription>
          </DialogHeader>
          <PerformanceContent />
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
};
