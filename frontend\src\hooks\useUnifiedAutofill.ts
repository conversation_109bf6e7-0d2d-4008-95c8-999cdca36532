/**
 * Unified Autofill Hook
 * 
 * Provides standardized loading states, error handling, and user feedback patterns
 * for autofill functionality across marketing and business profile forms.
 */

import { useState, useCallback } from 'react';
import { unifiedAutofillService, AutofillResult, AutofillOptions } from '@/services/unifiedAutofillService';

export interface AutofillState {
  isLoading: boolean;
  error: string | null;
  progress: number;
  progressMessage: string;
  lastResult: AutofillResult | null;
}

export interface UseUnifiedAutofillOptions {
  showToasts?: boolean;
  onSuccess?: (data: Record<string, any>, source: string) => void;
  onError?: (error: string) => void;
  onProgress?: (message: string, progress: number) => void;
}

export interface UseUnifiedAutofillReturn {
  state: AutofillState;
  autofillFromMarketingDocument: (fileId: string) => Promise<AutofillResult>;
  autofillFromBusinessDocument: (file: File) => Promise<AutofillResult>;
  autofillFromUrl: (url: string) => Promise<AutofillResult>;
  autofillFromMultipleSources: (files?: File[], urls?: string[]) => Promise<AutofillResult>;
  uploadFileAndAutofill: (file: File) => Promise<AutofillResult>;
  reset: () => void;
  clearError: () => void;
}

export function useUnifiedAutofill(options: UseUnifiedAutofillOptions = {}): UseUnifiedAutofillReturn {
  const { showToasts = true, onSuccess, onError, onProgress } = options;

  const [state, setState] = useState<AutofillState>({
    isLoading: false,
    error: null,
    progress: 0,
    progressMessage: '',
    lastResult: null,
  });

  const updateState = useCallback((updates: Partial<AutofillState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const reset = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      progress: 0,
      progressMessage: '',
      lastResult: null,
    });
  }, []);

  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  const createAutofillOptions = useCallback((): AutofillOptions => ({
    showToasts,
    onProgress: (message: string, progress?: number) => {
      updateState({
        progressMessage: message,
        progress: progress || 0,
      });
      if (onProgress) onProgress(message, progress || 0);
    },
    onError: (error: string) => {
      updateState({
        isLoading: false,
        error,
        progress: 0,
        progressMessage: '',
      });
      if (onError) onError(error);
    },
    onSuccess: (data: Record<string, any>) => {
      updateState({
        isLoading: false,
        error: null,
        progress: 100,
        progressMessage: 'Complete!',
      });
      // onSuccess will be called in the individual methods with source info
    },
  }), [showToasts, onProgress, onError, updateState]);

  const executeAutofill = useCallback(async (
    autofillFn: () => Promise<AutofillResult>,
    source: string
  ): Promise<AutofillResult> => {
    updateState({
      isLoading: true,
      error: null,
      progress: 0,
      progressMessage: 'Starting...',
    });

    try {
      const result = await autofillFn();
      
      updateState({
        isLoading: false,
        lastResult: result,
        progress: result.success ? 100 : 0,
        progressMessage: result.success ? 'Complete!' : 'Failed',
      });

      if (result.success && result.data && onSuccess) {
        onSuccess(result.data, source);
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      updateState({
        isLoading: false,
        error: errorMessage,
        progress: 0,
        progressMessage: 'Failed',
        lastResult: { success: false, error: errorMessage },
      });
      
      if (onError) onError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [updateState, onSuccess, onError]);

  const autofillFromMarketingDocument = useCallback(async (fileId: string): Promise<AutofillResult> => {
    return executeAutofill(
      () => unifiedAutofillService.autofillFromMarketingDocument(fileId, createAutofillOptions()),
      'marketing_document'
    );
  }, [executeAutofill, createAutofillOptions]);

  const autofillFromBusinessDocument = useCallback(async (file: File): Promise<AutofillResult> => {
    return executeAutofill(
      () => unifiedAutofillService.autofillFromBusinessDocument(file, createAutofillOptions()),
      'business_document'
    );
  }, [executeAutofill, createAutofillOptions]);

  const autofillFromUrl = useCallback(async (url: string): Promise<AutofillResult> => {
    return executeAutofill(
      () => unifiedAutofillService.autofillFromUrl(url, createAutofillOptions()),
      'website'
    );
  }, [executeAutofill, createAutofillOptions]);

  const autofillFromMultipleSources = useCallback(async (
    files?: File[],
    urls?: string[]
  ): Promise<AutofillResult> => {
    return executeAutofill(
      () => unifiedAutofillService.autofillFromMultipleSources(files, urls, createAutofillOptions()),
      'multiple_sources'
    );
  }, [executeAutofill, createAutofillOptions]);

  const uploadFileAndAutofill = useCallback(async (file: File): Promise<AutofillResult> => {
    return executeAutofill(
      () => unifiedAutofillService.uploadFileAndAutofill(file, createAutofillOptions()),
      'uploaded_file'
    );
  }, [executeAutofill, createAutofillOptions]);

  return {
    state,
    autofillFromMarketingDocument,
    autofillFromBusinessDocument,
    autofillFromUrl,
    autofillFromMultipleSources,
    uploadFileAndAutofill,
    reset,
    clearError,
  };
}

/**
 * Utility hook for simple autofill operations with minimal setup
 */
export function useSimpleAutofill() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const executeAutofill = useCallback(async (
    operation: () => Promise<AutofillResult>
  ): Promise<Record<string, any> | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await operation();
      
      if (result.success && result.data) {
        return result.data;
      } else {
        setError(result.error || 'Autofill failed');
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const autofillFromDocument = useCallback(async (fileId: string): Promise<Record<string, any> | null> => {
    return executeAutofill(() => 
      unifiedAutofillService.autofillFromMarketingDocument(fileId, { showToasts: false })
    );
  }, [executeAutofill]);

  const autofillFromFile = useCallback(async (file: File): Promise<Record<string, any> | null> => {
    return executeAutofill(() => 
      unifiedAutofillService.autofillFromBusinessDocument(file, { showToasts: false })
    );
  }, [executeAutofill]);

  const autofillFromUrl = useCallback(async (url: string): Promise<Record<string, any> | null> => {
    return executeAutofill(() => 
      unifiedAutofillService.autofillFromUrl(url, { showToasts: false })
    );
  }, [executeAutofill]);

  return {
    isLoading,
    error,
    autofillFromDocument,
    autofillFromFile,
    autofillFromUrl,
    clearError: () => setError(null),
  };
}

/**
 * Progress indicator component props for autofill operations
 */
export interface AutofillProgressProps {
  isLoading: boolean;
  progress: number;
  message: string;
  error?: string | null;
}

/**
 * Utility function to get user-friendly autofill source names
 */
export function getAutofillSourceName(source: string): string {
  const sourceNames: Record<string, string> = {
    'marketing_document': 'Marketing Document',
    'business_document': 'Business Document',
    'website': 'Website',
    'multiple_sources': 'Multiple Sources',
    'uploaded_file': 'Uploaded File',
  };
  
  return sourceNames[source] || 'Unknown Source';
}

/**
 * Utility function to determine if autofill is available for given inputs
 */
export function canAutofill(dataSourceId?: string, attachedFile?: File | null, urls?: string[]): boolean {
  return !!(dataSourceId || attachedFile || (urls && urls.length > 0));
}
