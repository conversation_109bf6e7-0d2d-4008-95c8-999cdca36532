import React, { useEffect, useRef } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { toast } from '@/hooks/use-toast';

// Keyboard shortcuts for dashboard navigation
export const useDashboardKeyboardShortcuts = ({
  onAddWidget,
  onAddSection,
  onRefresh,
  onSettings,
  onToggleEditMode,
  onExport
}: {
  onAddWidget?: () => void;
  onAddSection?: () => void;
  onRefresh?: () => void;
  onSettings?: () => void;
  onToggleEditMode?: () => void;
  onExport?: () => void;
}) => {
  // Widget management shortcuts
  useHotkeys('ctrl+shift+w', (e) => {
    e.preventDefault();
    onAddWidget?.();
    toast({
      title: "Keyboard Shortcut",
      description: "Add Widget dialog opened (Ctrl+Shift+W)",
    });
  }, { enableOnFormTags: false });

  // Section management shortcuts
  useHotkeys('ctrl+shift+s', (e) => {
    e.preventDefault();
    onAddSection?.();
    toast({
      title: "Keyboard Shortcut",
      description: "Add Section triggered (Ctrl+Shift+S)",
    });
  }, { enableOnFormTags: false });

  // Refresh shortcut
  useHotkeys('ctrl+r', (e) => {
    e.preventDefault();
    onRefresh?.();
    toast({
      title: "Keyboard Shortcut",
      description: "Dashboard refreshed (Ctrl+R)",
    });
  }, { enableOnFormTags: false });

  // Settings shortcut
  useHotkeys('ctrl+comma', (e) => {
    e.preventDefault();
    onSettings?.();
    toast({
      title: "Keyboard Shortcut",
      description: "Settings opened (Ctrl+,)",
    });
  }, { enableOnFormTags: false });

  // Edit mode toggle
  useHotkeys('ctrl+e', (e) => {
    e.preventDefault();
    onToggleEditMode?.();
    toast({
      title: "Keyboard Shortcut",
      description: "Edit mode toggled (Ctrl+E)",
    });
  }, { enableOnFormTags: false });

  // Export shortcut
  useHotkeys('ctrl+shift+e', (e) => {
    e.preventDefault();
    onExport?.();
    toast({
      title: "Keyboard Shortcut",
      description: "Export initiated (Ctrl+Shift+E)",
    });
  }, { enableOnFormTags: false });

  // Help shortcut
  useHotkeys('ctrl+shift+h', (e) => {
    e.preventDefault();
    toast({
      title: "Dashboard Keyboard Shortcuts",
      description: (
        <div className="space-y-2 text-sm">
          <div><kbd>Ctrl+Shift+W</kbd> - Add Widget</div>
          <div><kbd>Ctrl+Shift+S</kbd> - Add Section</div>
          <div><kbd>Ctrl+R</kbd> - Refresh Dashboard</div>
          <div><kbd>Ctrl+,</kbd> - Open Settings</div>
          <div><kbd>Ctrl+E</kbd> - Toggle Edit Mode</div>
          <div><kbd>Ctrl+Shift+E</kbd> - Export Dashboard</div>
          <div><kbd>Ctrl+Shift+H</kbd> - Show Help</div>
        </div>
      ),
    });
  }, { enableOnFormTags: false });
};

// Focus management for dashboard components
export const useFocusManagement = () => {
  const focusableElementsRef = useRef<HTMLElement[]>([]);
  const currentFocusIndexRef = useRef(0);

  const updateFocusableElements = () => {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ');

    focusableElementsRef.current = Array.from(
      document.querySelectorAll(focusableSelectors)
    ) as HTMLElement[];
  };

  const focusNext = () => {
    updateFocusableElements();
    const elements = focusableElementsRef.current;
    if (elements.length === 0) return;

    currentFocusIndexRef.current = (currentFocusIndexRef.current + 1) % elements.length;
    elements[currentFocusIndexRef.current]?.focus();
  };

  const focusPrevious = () => {
    updateFocusableElements();
    const elements = focusableElementsRef.current;
    if (elements.length === 0) return;

    currentFocusIndexRef.current = currentFocusIndexRef.current === 0 
      ? elements.length - 1 
      : currentFocusIndexRef.current - 1;
    elements[currentFocusIndexRef.current]?.focus();
  };

  // Tab navigation enhancement
  useHotkeys('tab', (e) => {
    if (!e.shiftKey) {
      e.preventDefault();
      focusNext();
    }
  }, { enableOnFormTags: true });

  useHotkeys('shift+tab', (e) => {
    e.preventDefault();
    focusPrevious();
  }, { enableOnFormTags: true });

  return { updateFocusableElements, focusNext, focusPrevious };
};

// Screen reader announcements
export const useScreenReaderAnnouncements = () => {
  const announceRef = useRef<HTMLDivElement>(null);

  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!announceRef.current) return;

    announceRef.current.setAttribute('aria-live', priority);
    announceRef.current.textContent = message;

    // Clear after announcement
    setTimeout(() => {
      if (announceRef.current) {
        announceRef.current.textContent = '';
      }
    }, 1000);
  };

  const ScreenReaderAnnouncer = () => (
    <div
      ref={announceRef}
      aria-live="polite"
      aria-atomic="true"
      className="sr-only"
    />
  );

  return { announce, ScreenReaderAnnouncer };
};

// High contrast mode detection and support
export const useHighContrastMode = () => {
  const [isHighContrast, setIsHighContrast] = React.useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setIsHighContrast(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setIsHighContrast(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return isHighContrast;
};

// Reduced motion detection
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

// ARIA live region for dynamic content updates
export const LiveRegion: React.FC<{
  message: string;
  priority?: 'polite' | 'assertive';
  className?: string;
}> = ({ message, priority = 'polite', className = 'sr-only' }) => (
  <div
    aria-live={priority}
    aria-atomic="true"
    className={className}
  >
    {message}
  </div>
);

// Skip navigation link
export const SkipNavigation: React.FC = () => (
  <a
    href="#main-content"
    className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50"
  >
    Skip to main content
  </a>
);

// Accessible widget wrapper with proper ARIA attributes
export const AccessibleWidgetWrapper: React.FC<{
  children: React.ReactNode;
  title: string;
  description?: string;
  isLoading?: boolean;
  hasError?: boolean;
  className?: string;
}> = ({ children, title, description, isLoading, hasError, className }) => (
  <div
    role="region"
    aria-labelledby={`widget-title-${title.replace(/\s+/g, '-').toLowerCase()}`}
    aria-describedby={description ? `widget-desc-${title.replace(/\s+/g, '-').toLowerCase()}` : undefined}
    aria-busy={isLoading}
    aria-invalid={hasError}
    className={className}
  >
    <h3
      id={`widget-title-${title.replace(/\s+/g, '-').toLowerCase()}`}
      className="sr-only"
    >
      {title}
    </h3>
    {description && (
      <p
        id={`widget-desc-${title.replace(/\s+/g, '-').toLowerCase()}`}
        className="sr-only"
      >
        {description}
      </p>
    )}
    {children}
  </div>
);
