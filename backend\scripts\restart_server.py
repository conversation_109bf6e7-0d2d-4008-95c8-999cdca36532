"""
<PERSON><PERSON><PERSON> to restart the server.

This script stops any running server processes and starts a new one.
"""

import os
import sys
import subprocess
import time
import logging
import signal
import psutil

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_server_process():
    """Find the server process if it's running."""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info.get('cmdline', [])
            if cmdline and 'uvicorn' in ' '.join(cmdline) and 'app.main:app' in ' '.join(cmdline):
                return proc
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return None

def stop_server():
    """Stop the server if it's running."""
    server_proc = find_server_process()
    if server_proc:
        logger.info(f"Found server process with PID {server_proc.pid}, stopping...")
        try:
            server_proc.terminate()
            gone, alive = psutil.wait_procs([server_proc], timeout=5)
            if alive:
                logger.warning("Server process didn't terminate gracefully, killing...")
                server_proc.kill()
            logger.info("Server process stopped")
            return True
        except Exception as e:
            logger.error(f"Error stopping server process: {e}")
            return False
    else:
        logger.info("No server process found")
        return True

def start_server():
    """Start the server."""
    logger.info("Starting server...")
    try:
        # Change to the backend directory
        os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        # Start the server
        server_proc = subprocess.Popen(
            ["python", "server.py", "--host", "0.0.0.0", "--port", "8000"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit to make sure the server starts
        time.sleep(2)
        
        # Check if the server is running
        if server_proc.poll() is None:
            logger.info(f"Server started with PID {server_proc.pid}")
            return True
        else:
            stdout, stderr = server_proc.communicate()
            logger.error(f"Server failed to start: {stderr}")
            return False
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        return False

def restart_server():
    """Restart the server."""
    logger.info("Restarting server...")
    if stop_server():
        if start_server():
            logger.info("Server restarted successfully")
            return True
        else:
            logger.error("Failed to start server")
            return False
    else:
        logger.error("Failed to stop server")
        return False

if __name__ == "__main__":
    success = restart_server()
    if success:
        logger.info("Script completed successfully")
    else:
        logger.error("Script failed")
        sys.exit(1)
