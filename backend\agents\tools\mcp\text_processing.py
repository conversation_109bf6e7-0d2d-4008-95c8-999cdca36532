"""
Text processing MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for processing text.
"""

import logging
from typing import Dict, Any, List

from .base import BaseMCPTool
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class TextProcessingTool(BaseMCPTool):
    """Tool for processing text."""

    def __init__(self):
        """Initialize the text processing tool."""
        super().__init__(
            name="process_text",
            description="Process text using various operations",
            input_schema={
                "type": "object",
                "properties": {
                    "text": {"type": "string"},
                    "operation": {
                        "type": "string",
                        "enum": [
                            "count_words",
                            "count_chars",
                            "extract_emails",
                            "extract_urls",
                            "extract_entities",
                            "summarize",
                            "sentiment",
                            "keyword_extraction",
                            "readability_analysis",
                            "advanced_summarization",
                            "sentiment_detailed",
                            "text_classification",
                            "language_style_analysis",
                            "content_quality_assessment",
                            "topic_modeling",
                            "text_similarity",
                            "named_entity_recognition"
                        ]
                    },
                    "params": {"type": "object"}
                },
                "required": ["text", "operation"]
            },
            annotations={
                "title": "Process Text",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        logger.info("Initialized text processing tool")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the text processing tool with agent-aware capabilities.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            text = arguments["text"]
            operation = arguments["operation"]
            params = arguments.get("params", {})

            # Agent context for dynamic identity detection
            user_context = arguments.get("context", {})
            agent_id = arguments.get("persona_id") or arguments.get("agent_id")

            # Detect agent identity for personalized text processing
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=user_context,
                intent_type="text_processing"
            )

            logger.info(f"Detected agent identity: {agent_identity} for text processing operation: {operation}")

            if not text:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Text is empty"
                        }
                    ]
                }

            # Perform the operation with agent-aware processing
            result = await self._process_text_with_agent_awareness(
                text, operation, params, agent_identity
            )

            return {
                "content": [
                    {
                        "type": "text",
                        "text": result
                    }
                ],
                "metadata": {
                    "operation": operation,
                    "agent_identity": agent_identity,
                    "agent_aware": True,
                    "text_length": len(text),
                    "processing_params": params
                }
            }
        except Exception as e:
            logger.error(f"Error processing text: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error processing text: {str(e)}"
                    }
                ]
            }

    async def _process_text_with_agent_awareness(
        self,
        text: str,
        operation: str,
        params: Dict[str, Any],
        agent_identity: str
    ) -> str:
        """
        Process text with agent-aware capabilities.

        Args:
            text: Text to process
            operation: Processing operation
            params: Operation parameters
            agent_identity: Agent identity for customization

        Returns:
            Processed text result
        """
        # Get agent-specific processing style
        processing_style = await self._get_agent_processing_style(agent_identity, operation)

        # Perform the operation with agent-aware enhancements
        if operation == "count_words":
            words = text.split()
            word_count = len(words)
            result = self._format_result_for_agent(
                f"Word count: {word_count}",
                processing_style,
                {"count": word_count, "operation": "word_count"}
            )

        elif operation == "count_chars":
            char_count = len(text)
            result = self._format_result_for_agent(
                f"Character count: {char_count}",
                processing_style,
                {"count": char_count, "operation": "char_count"}
            )

        elif operation == "extract_emails":
            import re
            email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
            emails = re.findall(email_pattern, text)
            if emails:
                base_result = "Extracted emails:\n" + "\n".join(emails)
            else:
                base_result = "No emails found in the text."
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"emails_found": len(emails), "operation": "email_extraction"}
            )

        elif operation == "extract_urls":
            import re
            url_pattern = r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+'
            urls = re.findall(url_pattern, text)
            if urls:
                base_result = "Extracted URLs:\n" + "\n".join(urls)
            else:
                base_result = "No URLs found in the text."
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"urls_found": len(urls), "operation": "url_extraction"}
            )

        elif operation == "extract_entities":
            import re
            # Simple entity extraction using regex patterns
            patterns = {
                "dates": r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{1,2} (?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{2,4}',
                "phone_numbers": r'\+?[\d\s()-]{10,}',
                "money": r'\$\d+(?:\.\d{2})?|\d+(?:\.\d{2})? (?:dollars|USD)',
            }

            entities = {}
            for entity_type, pattern in patterns.items():
                matches = re.findall(pattern, text)
                if matches:
                    entities[entity_type] = matches

            if entities:
                base_result = "Extracted entities:\n"
                for entity_type, matches in entities.items():
                    base_result += f"\n{entity_type.capitalize()}:\n"
                    for match in matches:
                        base_result += f"- {match}\n"
            else:
                base_result = "No entities found in the text."

            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"entities_found": sum(len(matches) for matches in entities.values()), "operation": "entity_extraction"}
            )

        elif operation == "summarize":
            import re
            # Simple summarization by extracting the first few sentences
            sentences = re.split(r'(?<=[.!?])\s+', text)
            num_sentences = params.get("num_sentences", 3)
            summary = " ".join(sentences[:min(num_sentences, len(sentences))])
            base_result = f"Summary:\n{summary}"
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"sentences_used": min(num_sentences, len(sentences)), "operation": "summarization"}
            )

        elif operation == "sentiment":
            import re
            # Simple sentiment analysis using keyword counting
            positive_words = ["good", "great", "excellent", "positive", "happy", "love", "like", "best"]
            negative_words = ["bad", "terrible", "negative", "sad", "hate", "dislike", "worst"]

            words = re.findall(r'\b\w+\b', text.lower())
            positive_count = sum(1 for word in words if word in positive_words)
            negative_count = sum(1 for word in words if word in negative_words)

            if positive_count > negative_count:
                sentiment = "positive"
            elif negative_count > positive_count:
                sentiment = "negative"
            else:
                sentiment = "neutral"

            base_result = f"Sentiment analysis:\n"
            base_result += f"- Sentiment: {sentiment}\n"
            base_result += f"- Positive words: {positive_count}\n"
            base_result += f"- Negative words: {negative_count}"

            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"sentiment": sentiment, "positive_count": positive_count, "negative_count": negative_count, "operation": "sentiment_analysis"}
            )

        elif operation == "keyword_extraction":
            keywords = await self._extract_keywords(text, params)
            base_result = f"Extracted keywords:\n" + "\n".join([f"- {kw['word']} (score: {kw['score']:.2f})" for kw in keywords])
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"keywords_found": len(keywords), "operation": "keyword_extraction"}
            )

        elif operation == "readability_analysis":
            readability = await self._analyze_readability(text)
            base_result = f"Readability analysis:\n"
            base_result += f"- Reading level: {readability['level']}\n"
            base_result += f"- Flesch score: {readability['flesch_score']:.1f}\n"
            base_result += f"- Average sentence length: {readability['avg_sentence_length']:.1f} words\n"
            base_result += f"- Complex words: {readability['complex_words']} ({readability['complex_word_percentage']:.1f}%)"
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"readability": readability, "operation": "readability_analysis"}
            )

        elif operation == "advanced_summarization":
            summary = await self._advanced_summarization(text, params)
            base_result = f"Advanced summary:\n{summary['summary']}\n\nKey points:\n" + "\n".join([f"- {point}" for point in summary['key_points']])
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"summary_length": len(summary['summary']), "key_points_count": len(summary['key_points']), "operation": "advanced_summarization"}
            )

        elif operation == "sentiment_detailed":
            sentiment_analysis = await self._detailed_sentiment_analysis(text)
            base_result = f"Detailed sentiment analysis:\n"
            base_result += f"- Overall sentiment: {sentiment_analysis['overall']} ({sentiment_analysis['confidence']:.2f})\n"
            base_result += f"- Emotions detected: {', '.join(sentiment_analysis['emotions'])}\n"
            base_result += f"- Sentiment distribution: {sentiment_analysis['distribution']}"
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"sentiment_analysis": sentiment_analysis, "operation": "detailed_sentiment_analysis"}
            )

        elif operation == "text_classification":
            classification = await self._classify_text(text, params)
            base_result = f"Text classification:\n"
            base_result += f"- Primary category: {classification['primary_category']} ({classification['confidence']:.2f})\n"
            base_result += f"- All categories: {classification['all_categories']}"
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"classification": classification, "operation": "text_classification"}
            )

        elif operation == "language_style_analysis":
            style_analysis = await self._analyze_language_style(text)
            base_result = f"Language style analysis:\n"
            base_result += f"- Writing style: {style_analysis['style']}\n"
            base_result += f"- Formality level: {style_analysis['formality']}\n"
            base_result += f"- Tone: {style_analysis['tone']}\n"
            base_result += f"- Complexity: {style_analysis['complexity']}"
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"style_analysis": style_analysis, "operation": "language_style_analysis"}
            )

        elif operation == "content_quality_assessment":
            quality = await self._assess_content_quality(text)
            base_result = f"Content quality assessment:\n"
            base_result += f"- Overall quality score: {quality['overall_score']:.1f}/10\n"
            base_result += f"- Clarity: {quality['clarity']:.1f}/10\n"
            base_result += f"- Coherence: {quality['coherence']:.1f}/10\n"
            base_result += f"- Engagement: {quality['engagement']:.1f}/10\n"
            base_result += f"- Suggestions: {', '.join(quality['suggestions'])}"
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"quality_assessment": quality, "operation": "content_quality_assessment"}
            )

        elif operation == "topic_modeling":
            topics = await self._extract_topics(text, params)
            base_result = f"Topic modeling results:\n"
            for i, topic in enumerate(topics, 1):
                base_result += f"Topic {i}: {topic['name']} (weight: {topic['weight']:.2f})\n"
                base_result += f"  Keywords: {', '.join(topic['keywords'])}\n"
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"topics_found": len(topics), "operation": "topic_modeling"}
            )

        elif operation == "text_similarity":
            reference_text = params.get("reference_text", "")
            if not reference_text:
                result = "Error: reference_text parameter required for similarity analysis"
            else:
                similarity = await self._calculate_text_similarity(text, reference_text)
                base_result = f"Text similarity analysis:\n"
                base_result += f"- Similarity score: {similarity['score']:.2f}\n"
                base_result += f"- Common words: {similarity['common_words']}\n"
                base_result += f"- Unique words in text 1: {similarity['unique_text1']}\n"
                base_result += f"- Unique words in text 2: {similarity['unique_text2']}"
                result = self._format_result_for_agent(
                    base_result,
                    processing_style,
                    {"similarity": similarity, "operation": "text_similarity"}
                )

        elif operation == "named_entity_recognition":
            entities = await self._advanced_entity_recognition(text)
            base_result = f"Named entity recognition:\n"
            for entity_type, entity_list in entities.items():
                if entity_list:
                    base_result += f"\n{entity_type.upper()}:\n"
                    for entity in entity_list:
                        base_result += f"- {entity['text']} (confidence: {entity['confidence']:.2f})\n"
            result = self._format_result_for_agent(
                base_result,
                processing_style,
                {"entities": entities, "operation": "named_entity_recognition"}
            )

        else:
            result = f"Unsupported operation: {operation}"

        return result

    async def _get_agent_processing_style(self, agent_identity: str, operation: str) -> Dict[str, Any]:
        """
        Get agent-specific processing style preferences.

        Args:
            agent_identity: Agent identity
            operation: Processing operation

        Returns:
            Processing style preferences
        """
        try:
            # Get agent system prompt to extract processing preferences
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract processing style from system prompt
            style_preferences = await self._extract_processing_style_from_prompt(system_prompt, agent_identity)

            return style_preferences

        except Exception as e:
            logger.warning(f"Failed to get agent processing style: {e}")
            return self._get_default_processing_style_for_agent(agent_identity)

    async def _extract_processing_style_from_prompt(self, system_prompt: str, agent_identity: str) -> Dict[str, Any]:
        """Extract processing style preferences from agent system prompt."""
        style_preferences = {
            "output_format": "detailed",
            "explanation_level": "moderate",
            "focus_areas": [],
            "presentation_style": "professional"
        }

        if not system_prompt:
            return self._get_default_processing_style_for_agent(agent_identity)

        # Look for processing-related patterns in the system prompt
        import re

        # Check for output format preferences
        if re.search(r"concise|brief|summary", system_prompt, re.IGNORECASE):
            style_preferences["output_format"] = "concise"
        elif re.search(r"detailed|comprehensive|thorough", system_prompt, re.IGNORECASE):
            style_preferences["output_format"] = "detailed"

        # Check for explanation level
        if re.search(r"educational|explain|teaching", system_prompt, re.IGNORECASE):
            style_preferences["explanation_level"] = "educational"
        elif re.search(r"technical|advanced", system_prompt, re.IGNORECASE):
            style_preferences["explanation_level"] = "technical"
        elif re.search(r"simple|basic|accessible", system_prompt, re.IGNORECASE):
            style_preferences["explanation_level"] = "simple"

        # Extract focus areas based on capabilities
        focus_areas = []
        if re.search(r"marketing|campaign|strategy", system_prompt, re.IGNORECASE):
            focus_areas.append("business_context")
        if re.search(r"analysis|analytical|insights", system_prompt, re.IGNORECASE):
            focus_areas.append("analytical_insights")
        if re.search(r"classification|categorization", system_prompt, re.IGNORECASE):
            focus_areas.append("structured_output")

        style_preferences["focus_areas"] = focus_areas

        return style_preferences

    def _get_default_processing_style_for_agent(self, agent_identity: str) -> Dict[str, Any]:
        """Get default processing style for specific agent types."""
        default_styles = {
            "analyst": {
                "output_format": "detailed",
                "explanation_level": "technical",
                "focus_areas": ["analytical_insights", "statistical_context"],
                "presentation_style": "analytical"
            },
            "marketer": {
                "output_format": "business_focused",
                "explanation_level": "moderate",
                "focus_areas": ["business_context", "actionable_insights"],
                "presentation_style": "business"
            },
            "classifier": {
                "output_format": "structured",
                "explanation_level": "clear",
                "focus_areas": ["structured_output", "categorization"],
                "presentation_style": "organized"
            },
            "concierge": {
                "output_format": "accessible",
                "explanation_level": "simple",
                "focus_areas": ["user_friendly"],
                "presentation_style": "friendly"
            }
        }

        return default_styles.get(agent_identity, {
            "output_format": "detailed",
            "explanation_level": "moderate",
            "focus_areas": ["general"],
            "presentation_style": "professional"
        })

    def _format_result_for_agent(
        self,
        base_result: str,
        style: Dict[str, Any],
        metadata: Dict[str, Any]
    ) -> str:
        """
        Format processing result based on agent style preferences.

        Args:
            base_result: Base processing result
            style: Agent style preferences
            metadata: Result metadata

        Returns:
            Formatted result
        """
        # Add style-specific enhancements
        enhancements = []

        # Add explanation based on agent style
        explanation_level = style.get("explanation_level", "moderate")
        if explanation_level == "educational":
            enhancements.append(self._add_educational_context(metadata))
        elif explanation_level == "technical":
            enhancements.append(self._add_technical_context(metadata))
        elif explanation_level == "simple":
            enhancements.append(self._add_simple_context(metadata))

        # Add focus area enhancements
        focus_areas = style.get("focus_areas", [])
        if "business_context" in focus_areas:
            enhancements.append(self._add_business_context(metadata))
        if "analytical_insights" in focus_areas:
            enhancements.append(self._add_analytical_insights(metadata))
        if "structured_output" in focus_areas:
            # Already structured, no additional formatting needed
            pass

        # Combine base result with enhancements
        if enhancements:
            enhanced_result = base_result + "\n\n" + "\n".join(filter(None, enhancements))
        else:
            enhanced_result = base_result

        return enhanced_result

    def _add_educational_context(self, metadata: Dict[str, Any]) -> str:
        """Add educational context to the result."""
        operation = metadata.get("operation", "")
        if operation == "word_count":
            return "💡 Word count is useful for assessing document length and readability."
        elif operation == "sentiment_analysis":
            return "💡 Sentiment analysis helps understand the emotional tone of text content."
        elif operation == "email_extraction":
            return "💡 Email extraction is commonly used for contact discovery and data mining."
        return ""

    def _add_technical_context(self, metadata: Dict[str, Any]) -> str:
        """Add technical context to the result."""
        operation = metadata.get("operation", "")
        if operation == "sentiment_analysis":
            return "🔧 Technical note: This uses basic keyword matching. For production use, consider advanced NLP models."
        elif operation == "entity_extraction":
            return "🔧 Technical note: This uses regex patterns. Consider NER models for better accuracy."
        return ""

    def _add_simple_context(self, metadata: Dict[str, Any]) -> str:
        """Add simple, user-friendly context to the result."""
        operation = metadata.get("operation", "")
        if operation == "word_count":
            count = metadata.get("count", 0)
            if count > 1000:
                return "📝 This is a long document - great for detailed content!"
            elif count > 100:
                return "📝 This is a medium-length text - good for most purposes."
            else:
                return "📝 This is a short text - perfect for quick reading."
        return ""

    def _add_business_context(self, metadata: Dict[str, Any]) -> str:
        """Add business context to the result."""
        operation = metadata.get("operation", "")
        if operation == "sentiment_analysis":
            sentiment = metadata.get("sentiment", "")
            if sentiment == "positive":
                return "📈 Business insight: Positive sentiment can boost brand perception and customer engagement."
            elif sentiment == "negative":
                return "📉 Business insight: Negative sentiment may require attention to improve customer satisfaction."
        elif operation == "email_extraction":
            count = metadata.get("emails_found", 0)
            if count > 0:
                return f"💼 Business insight: Found {count} email contact(s) - valuable for lead generation and outreach."
        return ""

    def _add_analytical_insights(self, metadata: Dict[str, Any]) -> str:
        """Add analytical insights to the result."""
        operation = metadata.get("operation", "")
        if operation == "word_count":
            count = metadata.get("count", 0)
            return f"📊 Analysis: Text density is {count} words. Optimal range for most content is 300-800 words."
        elif operation == "sentiment_analysis":
            pos_count = metadata.get("positive_count", 0)
            neg_count = metadata.get("negative_count", 0)
            total_sentiment_words = pos_count + neg_count
            if total_sentiment_words > 0:
                pos_ratio = pos_count / total_sentiment_words
                return f"📊 Analysis: Sentiment ratio is {pos_ratio:.2%} positive, indicating overall tone tendency."
        return ""

    async def _extract_keywords(self, text: str, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract keywords from text using frequency analysis and TF-IDF."""
        import re
        from collections import Counter

        # Simple keyword extraction using word frequency
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())

        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'this', 'that', 'these', 'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
            'might', 'can', 'cannot', 'not', 'no', 'yes', 'all', 'any', 'some', 'many', 'much',
            'more', 'most', 'other', 'another', 'such', 'what', 'which', 'who', 'when', 'where',
            'why', 'how', 'here', 'there', 'now', 'then', 'than', 'so', 'very', 'just', 'only'
        }

        filtered_words = [word for word in words if word not in stop_words and len(word) > 3]
        word_freq = Counter(filtered_words)

        # Calculate simple TF score
        total_words = len(filtered_words)
        keywords = []

        max_keywords = params.get("max_keywords", 10)
        for word, freq in word_freq.most_common(max_keywords):
            score = freq / total_words
            keywords.append({
                "word": word,
                "frequency": freq,
                "score": score
            })

        return keywords

    async def _analyze_readability(self, text: str) -> Dict[str, Any]:
        """Analyze text readability using various metrics."""
        import re

        # Count sentences, words, and syllables
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        sentence_count = len(sentences)

        words = re.findall(r'\b\w+\b', text)
        word_count = len(words)

        # Simple syllable counting
        def count_syllables(word):
            word = word.lower()
            vowels = 'aeiouy'
            syllable_count = 0
            prev_was_vowel = False

            for char in word:
                is_vowel = char in vowels
                if is_vowel and not prev_was_vowel:
                    syllable_count += 1
                prev_was_vowel = is_vowel

            # Handle silent e
            if word.endswith('e') and syllable_count > 1:
                syllable_count -= 1

            return max(1, syllable_count)

        syllable_count = sum(count_syllables(word) for word in words)

        # Calculate metrics
        avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0
        avg_syllables_per_word = syllable_count / word_count if word_count > 0 else 0

        # Flesch Reading Ease Score
        flesch_score = 206.835 - (1.015 * avg_sentence_length) - (84.6 * avg_syllables_per_word)

        # Complex words (3+ syllables)
        complex_words = sum(1 for word in words if count_syllables(word) >= 3)
        complex_word_percentage = (complex_words / word_count * 100) if word_count > 0 else 0

        # Determine reading level
        if flesch_score >= 90:
            level = "Very Easy"
        elif flesch_score >= 80:
            level = "Easy"
        elif flesch_score >= 70:
            level = "Fairly Easy"
        elif flesch_score >= 60:
            level = "Standard"
        elif flesch_score >= 50:
            level = "Fairly Difficult"
        elif flesch_score >= 30:
            level = "Difficult"
        else:
            level = "Very Difficult"

        return {
            "flesch_score": flesch_score,
            "level": level,
            "avg_sentence_length": avg_sentence_length,
            "avg_syllables_per_word": avg_syllables_per_word,
            "complex_words": complex_words,
            "complex_word_percentage": complex_word_percentage,
            "sentence_count": sentence_count,
            "word_count": word_count,
            "syllable_count": syllable_count
        }

    async def _advanced_summarization(self, text: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Perform advanced text summarization with key points extraction."""
        import re

        # Split into sentences
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        if not sentences:
            return {"summary": "No content to summarize", "key_points": []}

        # Simple extractive summarization
        summary_length = params.get("summary_length", 3)
        summary_length = min(summary_length, len(sentences))

        # Score sentences based on word frequency
        words = re.findall(r'\b\w+\b', text.lower())
        word_freq = {}
        for word in words:
            if len(word) > 3:  # Skip short words
                word_freq[word] = word_freq.get(word, 0) + 1

        # Score sentences
        sentence_scores = []
        for i, sentence in enumerate(sentences):
            sentence_words = re.findall(r'\b\w+\b', sentence.lower())
            score = sum(word_freq.get(word, 0) for word in sentence_words if len(word) > 3)
            sentence_scores.append((score, i, sentence))

        # Get top sentences for summary
        sentence_scores.sort(reverse=True)
        top_sentences = sorted(sentence_scores[:summary_length], key=lambda x: x[1])
        summary = ". ".join([sentence for _, _, sentence in top_sentences]) + "."

        # Extract key points (top scoring sentences)
        key_points = [sentence.strip() for _, _, sentence in sentence_scores[:5]]

        return {
            "summary": summary,
            "key_points": key_points,
            "original_sentences": len(sentences),
            "summary_sentences": summary_length
        }

    async def _detailed_sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """Perform detailed sentiment analysis with emotion detection."""
        import re

        # Enhanced sentiment word lists
        sentiment_words = {
            "positive": ["excellent", "amazing", "wonderful", "fantastic", "great", "good", "love", "like",
                        "happy", "joy", "pleased", "satisfied", "delighted", "thrilled", "excited"],
            "negative": ["terrible", "awful", "horrible", "bad", "hate", "dislike", "sad", "angry",
                        "frustrated", "disappointed", "upset", "annoyed", "disgusted"],
            "neutral": ["okay", "fine", "average", "normal", "standard", "typical", "usual"]
        }

        emotion_words = {
            "joy": ["happy", "joyful", "cheerful", "delighted", "elated", "ecstatic"],
            "anger": ["angry", "furious", "mad", "irritated", "annoyed", "rage"],
            "sadness": ["sad", "depressed", "melancholy", "sorrowful", "grief"],
            "fear": ["afraid", "scared", "terrified", "anxious", "worried", "nervous"],
            "surprise": ["surprised", "amazed", "astonished", "shocked", "stunned"],
            "disgust": ["disgusted", "revolted", "repulsed", "sickened"]
        }

        words = re.findall(r'\b\w+\b', text.lower())

        # Count sentiment words
        sentiment_counts = {}
        for sentiment, word_list in sentiment_words.items():
            sentiment_counts[sentiment] = sum(1 for word in words if word in word_list)

        # Determine overall sentiment
        total_sentiment = sum(sentiment_counts.values())
        if total_sentiment == 0:
            overall = "neutral"
            confidence = 0.5
        else:
            max_sentiment = max(sentiment_counts, key=sentiment_counts.get)
            overall = max_sentiment
            confidence = sentiment_counts[max_sentiment] / total_sentiment

        # Detect emotions
        detected_emotions = []
        for emotion, word_list in emotion_words.items():
            emotion_count = sum(1 for word in words if word in word_list)
            if emotion_count > 0:
                detected_emotions.append(emotion)

        return {
            "overall": overall,
            "confidence": confidence,
            "distribution": sentiment_counts,
            "emotions": detected_emotions,
            "total_sentiment_words": total_sentiment
        }

    async def _classify_text(self, text: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Classify text into predefined categories."""
        import re

        # Predefined categories with keywords
        categories = {
            "business": ["business", "company", "corporate", "enterprise", "commercial", "industry",
                        "market", "sales", "revenue", "profit", "strategy", "management"],
            "technology": ["technology", "software", "computer", "digital", "internet", "data",
                          "algorithm", "programming", "artificial", "intelligence", "machine", "learning"],
            "science": ["science", "research", "study", "experiment", "analysis", "hypothesis",
                       "theory", "discovery", "scientific", "laboratory"],
            "education": ["education", "learning", "teaching", "school", "university", "student",
                         "teacher", "academic", "curriculum", "knowledge"],
            "health": ["health", "medical", "medicine", "doctor", "patient", "treatment", "therapy",
                      "disease", "wellness", "fitness"],
            "entertainment": ["entertainment", "movie", "music", "game", "sport", "fun", "leisure",
                             "recreation", "hobby", "celebrity"],
            "news": ["news", "report", "journalist", "media", "press", "breaking", "update",
                    "current", "events", "politics"]
        }

        words = re.findall(r'\b\w+\b', text.lower())

        # Score each category
        category_scores = {}
        for category, keywords in categories.items():
            score = sum(1 for word in words if word in keywords)
            if score > 0:
                category_scores[category] = score / len(words)  # Normalize by text length

        if not category_scores:
            return {
                "primary_category": "general",
                "confidence": 0.1,
                "all_categories": {"general": 0.1}
            }

        primary_category = max(category_scores, key=category_scores.get)
        confidence = category_scores[primary_category]

        return {
            "primary_category": primary_category,
            "confidence": confidence,
            "all_categories": category_scores
        }

    async def _analyze_language_style(self, text: str) -> Dict[str, Any]:
        """Analyze the language style and characteristics of the text."""
        import re

        words = re.findall(r'\b\w+\b', text)
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        # Calculate metrics
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
        avg_sentence_length = len(words) / len(sentences) if sentences else 0

        # Analyze formality
        formal_indicators = ["therefore", "however", "furthermore", "consequently", "nevertheless",
                           "moreover", "thus", "hence", "accordingly", "subsequently"]
        informal_indicators = ["yeah", "okay", "cool", "awesome", "gonna", "wanna", "kinda",
                             "sorta", "really", "pretty", "super", "totally"]

        formal_count = sum(1 for word in words if word.lower() in formal_indicators)
        informal_count = sum(1 for word in words if word.lower() in informal_indicators)

        if formal_count > informal_count:
            formality = "formal"
        elif informal_count > formal_count:
            formality = "informal"
        else:
            formality = "neutral"

        # Analyze tone
        positive_tone = ["excellent", "great", "wonderful", "amazing", "fantastic"]
        negative_tone = ["terrible", "awful", "horrible", "disappointing", "frustrating"]

        positive_count = sum(1 for word in words if word.lower() in positive_tone)
        negative_count = sum(1 for word in words if word.lower() in negative_tone)

        if positive_count > negative_count:
            tone = "positive"
        elif negative_count > positive_count:
            tone = "negative"
        else:
            tone = "neutral"

        # Determine complexity
        if avg_word_length > 6 and avg_sentence_length > 20:
            complexity = "high"
        elif avg_word_length < 4 and avg_sentence_length < 10:
            complexity = "low"
        else:
            complexity = "medium"

        # Determine writing style
        if formality == "formal" and complexity == "high":
            style = "academic"
        elif formality == "informal" and complexity == "low":
            style = "conversational"
        elif tone == "positive" and formality == "informal":
            style = "friendly"
        elif complexity == "high":
            style = "technical"
        else:
            style = "standard"

        return {
            "style": style,
            "formality": formality,
            "tone": tone,
            "complexity": complexity,
            "avg_word_length": avg_word_length,
            "avg_sentence_length": avg_sentence_length,
            "formal_indicators": formal_count,
            "informal_indicators": informal_count
        }

    async def _assess_content_quality(self, text: str) -> Dict[str, Any]:
        """Assess the overall quality of the content."""
        import re

        words = re.findall(r'\b\w+\b', text)
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        # Clarity assessment
        avg_sentence_length = len(words) / len(sentences) if sentences else 0
        clarity_score = 10 - min(9, max(0, (avg_sentence_length - 15) / 5))  # Optimal around 15 words

        # Coherence assessment (simple transition word check)
        transition_words = ["however", "therefore", "furthermore", "moreover", "consequently",
                           "additionally", "meanwhile", "subsequently", "nevertheless", "thus"]
        transition_count = sum(1 for word in words if word.lower() in transition_words)
        coherence_score = min(10, (transition_count / len(sentences)) * 20) if sentences else 5

        # Engagement assessment
        engaging_words = ["amazing", "incredible", "fascinating", "remarkable", "outstanding",
                         "exceptional", "extraordinary", "compelling", "captivating"]
        question_count = text.count('?')
        exclamation_count = text.count('!')
        engaging_count = sum(1 for word in words if word.lower() in engaging_words)

        engagement_score = min(10, (engaging_count + question_count + exclamation_count) / len(sentences) * 10) if sentences else 3

        # Overall score
        overall_score = (clarity_score + coherence_score + engagement_score) / 3

        # Generate suggestions
        suggestions = []
        if clarity_score < 6:
            suggestions.append("Consider shorter sentences for better clarity")
        if coherence_score < 5:
            suggestions.append("Add more transition words to improve flow")
        if engagement_score < 4:
            suggestions.append("Use more engaging language and varied sentence types")
        if len(words) < 50:
            suggestions.append("Content might be too brief for comprehensive analysis")

        return {
            "overall_score": overall_score,
            "clarity": clarity_score,
            "coherence": coherence_score,
            "engagement": engagement_score,
            "suggestions": suggestions,
            "word_count": len(words),
            "sentence_count": len(sentences)
        }

    async def _extract_topics(self, text: str, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract topics from text using keyword clustering."""
        import re
        from collections import Counter

        # Extract keywords first
        keywords = await self._extract_keywords(text, {"max_keywords": 20})

        if not keywords:
            return []

        # Simple topic clustering based on word co-occurrence
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())

        # Group related keywords into topics
        topic_groups = []
        used_keywords = set()

        for keyword in keywords:
            if keyword["word"] in used_keywords:
                continue

            # Find related words (simple approach)
            related_words = [keyword["word"]]
            used_keywords.add(keyword["word"])

            # Look for words that appear near this keyword
            for other_keyword in keywords:
                if other_keyword["word"] not in used_keywords:
                    # Simple co-occurrence check
                    if self._words_cooccur(keyword["word"], other_keyword["word"], text):
                        related_words.append(other_keyword["word"])
                        used_keywords.add(other_keyword["word"])

            if len(related_words) >= 1:  # At least one word for a topic
                topic_name = related_words[0].title()  # Use first word as topic name
                topic_groups.append({
                    "name": topic_name,
                    "keywords": related_words,
                    "weight": sum(kw["score"] for kw in keywords if kw["word"] in related_words)
                })

        # Sort by weight and return top topics
        topic_groups.sort(key=lambda x: x["weight"], reverse=True)
        max_topics = params.get("max_topics", 5)
        return topic_groups[:max_topics]

    def _words_cooccur(self, word1: str, word2: str, text: str, window: int = 10) -> bool:
        """Check if two words co-occur within a window."""
        import re

        words = re.findall(r'\b\w+\b', text.lower())

        for i, word in enumerate(words):
            if word == word1:
                # Check window around this word
                start = max(0, i - window)
                end = min(len(words), i + window + 1)
                if word2 in words[start:end]:
                    return True
        return False

    async def _calculate_text_similarity(self, text1: str, text2: str) -> Dict[str, Any]:
        """Calculate similarity between two texts."""
        import re
        from collections import Counter

        # Tokenize both texts
        words1 = set(re.findall(r'\b\w+\b', text1.lower()))
        words2 = set(re.findall(r'\b\w+\b', text2.lower()))

        # Calculate Jaccard similarity
        intersection = words1.intersection(words2)
        union = words1.union(words2)

        jaccard_score = len(intersection) / len(union) if union else 0

        # Calculate cosine similarity using word frequencies
        all_words = list(union)
        freq1 = Counter(re.findall(r'\b\w+\b', text1.lower()))
        freq2 = Counter(re.findall(r'\b\w+\b', text2.lower()))

        # Create vectors
        vec1 = [freq1.get(word, 0) for word in all_words]
        vec2 = [freq2.get(word, 0) for word in all_words]

        # Calculate cosine similarity
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        magnitude1 = sum(a * a for a in vec1) ** 0.5
        magnitude2 = sum(b * b for b in vec2) ** 0.5

        cosine_score = dot_product / (magnitude1 * magnitude2) if magnitude1 * magnitude2 > 0 else 0

        # Use average of Jaccard and cosine for final score
        final_score = (jaccard_score + cosine_score) / 2

        return {
            "score": final_score,
            "jaccard_similarity": jaccard_score,
            "cosine_similarity": cosine_score,
            "common_words": len(intersection),
            "unique_text1": len(words1 - words2),
            "unique_text2": len(words2 - words1),
            "total_unique_words": len(union)
        }

    async def _advanced_entity_recognition(self, text: str) -> Dict[str, List[Dict[str, Any]]]:
        """Perform advanced named entity recognition."""
        import re

        # Enhanced entity patterns
        patterns = {
            "person": [
                r'\b[A-Z][a-z]+ [A-Z][a-z]+\b',  # First Last
                r'\b(?:Mr|Mrs|Ms|Dr|Prof)\. [A-Z][a-z]+\b'  # Title Name
            ],
            "organization": [
                r'\b[A-Z][a-z]+ (?:Inc|Corp|LLC|Ltd|Company|Corporation)\b',
                r'\b[A-Z][A-Z]+ [A-Z][a-z]+\b'  # Acronym + word
            ],
            "location": [
                r'\b[A-Z][a-z]+, [A-Z][A-Z]\b',  # City, State
                r'\b[A-Z][a-z]+ [A-Z][a-z]+\b'   # Two capitalized words
            ],
            "date": [
                r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',
                r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2},? \d{4}\b',
                r'\b\d{1,2} (?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{4}\b'
            ],
            "money": [
                r'\$\d+(?:,\d{3})*(?:\.\d{2})?\b',
                r'\b\d+(?:,\d{3})*(?:\.\d{2})? (?:dollars|USD|euros|EUR)\b'
            ],
            "phone": [
                r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
                r'\(\d{3}\)\s?\d{3}[-.]?\d{4}\b'
            ],
            "email": [
                r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b'
            ],
            "url": [
                r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+',
                r'www\.(?:[-\w.]|(?:%[\da-fA-F]{2}))+'
            ]
        }

        entities = {}

        for entity_type, pattern_list in patterns.items():
            entities[entity_type] = []

            for pattern in pattern_list:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    entity_text = match.group()

                    # Simple confidence scoring based on pattern specificity
                    confidence = 0.8 if entity_type in ["email", "url", "phone"] else 0.6

                    entities[entity_type].append({
                        "text": entity_text,
                        "start": match.start(),
                        "end": match.end(),
                        "confidence": confidence
                    })

        # Remove duplicates and sort by confidence
        for entity_type in entities:
            seen = set()
            unique_entities = []
            for entity in entities[entity_type]:
                if entity["text"] not in seen:
                    seen.add(entity["text"])
                    unique_entities.append(entity)

            entities[entity_type] = sorted(unique_entities, key=lambda x: x["confidence"], reverse=True)

        return entities
