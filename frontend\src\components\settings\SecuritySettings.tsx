import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export const SecuritySettings = () => {
  const { updateIdleTimeout } = useAuth();
  const { toast } = useToast();
  const [idleTimeout, setIdleTimeout] = useState(30); // Default 30 minutes

  const handleTimeoutChange = (value: number[]) => {
    setIdleTimeout(value[0]);
  };

  const saveSettings = () => {
    updateIdleTimeout(idleTimeout);
    toast({
      title: 'Settings Saved',
      description: `Idle timeout set to ${idleTimeout} minutes.`,
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Security Settings</CardTitle>
        <CardDescription>
          Configure security settings for your account
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="idle-timeout">Session Timeout</Label>
            <div className="flex items-center gap-4 mt-2">
              <Slider
                id="idle-timeout"
                min={5}
                max={120}
                step={5}
                value={[idleTimeout]}
                onValueChange={handleTimeoutChange}
                className="flex-1"
              />
              <div className="w-16 flex items-center gap-1">
                <Input
                  type="number"
                  value={idleTimeout}
                  onChange={(e) => setIdleTimeout(Number(e.target.value))}
                  min={5}
                  max={120}
                  className="h-8"
                />
                <span className="text-sm text-muted-foreground">min</span>
              </div>
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              You will be automatically logged out after {idleTimeout} minutes of inactivity
            </p>
          </div>
        </div>
        <Button onClick={saveSettings}>Save Settings</Button>
      </CardContent>
    </Card>
  );
};
