/**
 * Embed Code Dialog
 * 
 * Dialog for generating embed codes for dashboards.
 * Features:
 * - Multiple embed options (iframe, JavaScript, React component)
 * - Customizable embed settings
 * - Responsive embed codes
 * - Security options
 * - Preview functionality
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Code,
  Copy,
  Eye,
  Settings,
  Shield,
  Smartphone,
  Tablet,
  Monitor,
  Check,
  ExternalLink,
  Loader2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface EmbedCodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dashboardId: string;
  dashboardName: string;
}

interface EmbedSettings {
  type: 'iframe' | 'javascript' | 'react';
  width: string;
  height: string;
  responsive: boolean;
  showTitle: boolean;
  showControls: boolean;
  allowFullscreen: boolean;
  theme: 'light' | 'dark' | 'auto';
  autoRefresh: boolean;
  refreshInterval: number;
  allowInteraction: boolean;
  showWatermark: boolean;
}

const DEFAULT_SETTINGS: EmbedSettings = {
  type: 'iframe',
  width: '100%',
  height: '600px',
  responsive: true,
  showTitle: true,
  showControls: true,
  allowFullscreen: true,
  theme: 'auto',
  autoRefresh: false,
  refreshInterval: 300,
  allowInteraction: true,
  showWatermark: false,
};

export const EmbedCodeDialog: React.FC<EmbedCodeDialogProps> = ({
  open,
  onOpenChange,
  dashboardId,
  dashboardName,
}) => {
  const { toast } = useToast();
  
  const [settings, setSettings] = useState<EmbedSettings>(DEFAULT_SETTINGS);
  const [embedCode, setEmbedCode] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewDevice, setPreviewDevice] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [copied, setCopied] = useState(false);

  // Generate embed code when settings change
  useEffect(() => {
    if (open) {
      generateEmbedCode();
    }
  }, [open, settings, dashboardId]);

  const updateSetting = (key: keyof EmbedSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const generateEmbedCode = async () => {
    setIsGenerating(true);
    try {
      // Simulate API call to generate embed code
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const baseUrl = window.location.origin;
      const embedUrl = `${baseUrl}/embed/dashboard/${dashboardId}`;
      
      // Add query parameters based on settings
      const params = new URLSearchParams();
      if (!settings.showTitle) params.append('hideTitle', 'true');
      if (!settings.showControls) params.append('hideControls', 'true');
      if (settings.theme !== 'auto') params.append('theme', settings.theme);
      if (settings.autoRefresh) params.append('refresh', settings.refreshInterval.toString());
      if (!settings.allowInteraction) params.append('readonly', 'true');
      if (settings.showWatermark) params.append('watermark', 'true');

      const fullUrl = params.toString() ? `${embedUrl}?${params.toString()}` : embedUrl;

      let code = '';
      
      switch (settings.type) {
        case 'iframe':
          code = generateIframeCode(fullUrl);
          break;
        case 'javascript':
          code = generateJavaScriptCode(fullUrl);
          break;
        case 'react':
          code = generateReactCode(fullUrl);
          break;
      }
      
      setEmbedCode(code);
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate embed code.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const generateIframeCode = (url: string): string => {
    const width = settings.responsive ? '100%' : settings.width;
    const height = settings.height;
    
    return `<iframe
  src="${url}"
  width="${width}"
  height="${height}"
  frameborder="0"
  ${settings.allowFullscreen ? 'allowfullscreen' : ''}
  ${settings.responsive ? 'style="max-width: 100%; height: auto;"' : ''}
  title="${dashboardName} Dashboard"
></iframe>`;
  };

  const generateJavaScriptCode = (url: string): string => {
    return `<div id="datagenius-dashboard-${dashboardId}"></div>
<script>
  (function() {
    var iframe = document.createElement('iframe');
    iframe.src = '${url}';
    iframe.width = '${settings.responsive ? '100%' : settings.width}';
    iframe.height = '${settings.height}';
    iframe.frameBorder = '0';
    ${settings.allowFullscreen ? "iframe.allowFullscreen = true;" : ''}
    iframe.title = '${dashboardName} Dashboard';
    
    ${settings.responsive ? `
    iframe.style.maxWidth = '100%';
    iframe.style.height = 'auto';
    ` : ''}
    
    document.getElementById('datagenius-dashboard-${dashboardId}').appendChild(iframe);
  })();
</script>`;
  };

  const generateReactCode = (url: string): string => {
    return `import React from 'react';

const DatageniusDashboard = () => {
  return (
    <iframe
      src="${url}"
      width="${settings.responsive ? '100%' : settings.width}"
      height="${settings.height}"
      frameBorder="0"
      ${settings.allowFullscreen ? 'allowFullScreen' : ''}
      title="${dashboardName} Dashboard"
      ${settings.responsive ? 'style={{ maxWidth: "100%", height: "auto" }}' : ''}
    />
  );
};

export default DatageniusDashboard;`;
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(embedCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      
      toast({
        title: "Code Copied",
        description: "Embed code has been copied to clipboard.",
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy embed code.",
        variant: "destructive",
      });
    }
  };

  const handlePreview = () => {
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    if (previewWindow) {
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Dashboard Preview</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body style="margin: 0; padding: 20px; font-family: Arial, sans-serif;">
          <h2>Preview: ${dashboardName}</h2>
          ${embedCode}
        </body>
        </html>
      `);
      previewWindow.document.close();
    }
  };

  const getPreviewWidth = () => {
    switch (previewDevice) {
      case 'mobile': return '375px';
      case 'tablet': return '768px';
      case 'desktop': return '100%';
      default: return '100%';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Code className="h-5 w-5" />
            <span>Embed Dashboard</span>
          </DialogTitle>
          <DialogDescription>
            Generate embed code for "{dashboardName}" to use on your website.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="settings" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="code">Code</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="settings" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Embed Type</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Code Type</Label>
                      <Select
                        value={settings.type}
                        onValueChange={(value: any) => updateSetting('type', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="iframe">HTML iframe</SelectItem>
                          <SelectItem value="javascript">JavaScript</SelectItem>
                          <SelectItem value="react">React Component</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Width</Label>
                        <Input
                          value={settings.width}
                          onChange={(e) => updateSetting('width', e.target.value)}
                          placeholder="100% or 800px"
                          disabled={settings.responsive}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Height</Label>
                        <Input
                          value={settings.height}
                          onChange={(e) => updateSetting('height', e.target.value)}
                          placeholder="600px"
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Responsive</p>
                        <p className="text-sm text-muted-foreground">
                          Automatically adjust to container width
                        </p>
                      </div>
                      <Switch
                        checked={settings.responsive}
                        onCheckedChange={(checked) => updateSetting('responsive', checked)}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Display Options</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Show Title</p>
                        <p className="text-sm text-muted-foreground">
                          Display dashboard title
                        </p>
                      </div>
                      <Switch
                        checked={settings.showTitle}
                        onCheckedChange={(checked) => updateSetting('showTitle', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Show Controls</p>
                        <p className="text-sm text-muted-foreground">
                          Display dashboard controls and filters
                        </p>
                      </div>
                      <Switch
                        checked={settings.showControls}
                        onCheckedChange={(checked) => updateSetting('showControls', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Allow Fullscreen</p>
                        <p className="text-sm text-muted-foreground">
                          Enable fullscreen viewing
                        </p>
                      </div>
                      <Switch
                        checked={settings.allowFullscreen}
                        onCheckedChange={(checked) => updateSetting('allowFullscreen', checked)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Theme</Label>
                      <Select
                        value={settings.theme}
                        onValueChange={(value: any) => updateSetting('theme', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="auto">Auto</SelectItem>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Behavior</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Auto Refresh</p>
                        <p className="text-sm text-muted-foreground">
                          Automatically refresh dashboard data
                        </p>
                      </div>
                      <Switch
                        checked={settings.autoRefresh}
                        onCheckedChange={(checked) => updateSetting('autoRefresh', checked)}
                      />
                    </div>

                    {settings.autoRefresh && (
                      <div className="space-y-2">
                        <Label>Refresh Interval (seconds)</Label>
                        <Select
                          value={settings.refreshInterval.toString()}
                          onValueChange={(value) => updateSetting('refreshInterval', parseInt(value))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="60">1 minute</SelectItem>
                            <SelectItem value="300">5 minutes</SelectItem>
                            <SelectItem value="600">10 minutes</SelectItem>
                            <SelectItem value="1800">30 minutes</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Allow Interaction</p>
                        <p className="text-sm text-muted-foreground">
                          Users can interact with dashboard elements
                        </p>
                      </div>
                      <Switch
                        checked={settings.allowInteraction}
                        onCheckedChange={(checked) => updateSetting('allowInteraction', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Show Watermark</p>
                        <p className="text-sm text-muted-foreground">
                          Display "Powered by Datagenius" watermark
                        </p>
                      </div>
                      <Switch
                        checked={settings.showWatermark}
                        onCheckedChange={(checked) => updateSetting('showWatermark', checked)}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center space-x-2">
                      <Shield className="h-5 w-5" />
                      <span>Security</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-sm text-blue-800">
                        <strong>Note:</strong> Embedded dashboards inherit the security settings 
                        of the original dashboard. Make sure your dashboard is properly configured 
                        for public access if needed.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="code" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">{settings.type.toUpperCase()}</Badge>
                  <span className="text-sm text-muted-foreground">
                    {settings.responsive ? 'Responsive' : `${settings.width} × ${settings.height}`}
                  </span>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" onClick={handlePreview}>
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                  <Button onClick={handleCopyCode}>
                    {copied ? (
                      <Check className="h-4 w-4 mr-2" />
                    ) : (
                      <Copy className="h-4 w-4 mr-2" />
                    )}
                    {copied ? 'Copied' : 'Copy Code'}
                  </Button>
                </div>
              </div>

              <Card>
                <CardContent className="p-0">
                  {isGenerating ? (
                    <div className="flex items-center justify-center p-8">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Generating embed code...</span>
                    </div>
                  ) : (
                    <Textarea
                      value={embedCode}
                      readOnly
                      className="min-h-[300px] font-mono text-sm border-0 resize-none"
                      placeholder="Embed code will appear here..."
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Preview</h3>
                <div className="flex space-x-1">
                  <Button
                    variant={previewDevice === 'mobile' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewDevice('mobile')}
                  >
                    <Smartphone className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewDevice === 'tablet' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewDevice('tablet')}
                  >
                    <Tablet className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewDevice === 'desktop' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewDevice('desktop')}
                  >
                    <Monitor className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <Card>
                <CardContent className="p-4">
                  <div 
                    className="mx-auto border rounded"
                    style={{ 
                      width: getPreviewWidth(),
                      maxWidth: '100%',
                      height: settings.height,
                      backgroundColor: '#f5f5f5',
                    }}
                  >
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <Monitor className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>Dashboard Preview</p>
                        <p className="text-sm">
                          {dashboardName}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
