import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { X } from 'lucide-react';
import { DashboardCreate, DashboardDataSourceAssignment, DashboardDataSourceAssignmentCreate, DashboardDataSourceAssignmentUpdate } from '@/types/dashboard-customization';
import { DashboardCreationDataSourceManager } from './DashboardCreationDataSourceManager';

interface DashboardCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (dashboard: DashboardCreate) => Promise<void>;
}

export const DashboardCreateDialog: React.FC<DashboardCreateDialogProps> = ({
  open,
  onOpenChange,
  onSubmit
}) => {
  const [formData, setFormData] = useState<DashboardCreate>({
    name: '',
    description: '',
    is_default: false,
    is_public: false,
    layout_config: {
      columns: 12,
      rows: 12,
      grid_gap: 16,
      margin: [10, 10],
      container_padding: [10, 10],
      auto_size: true
    },
    theme_config: {
      primary_color: '#3B82F6',
      secondary_color: '#10B981',
      background_color: '#F9FAFB',
      text_color: '#1F2937',
      border_color: '#E5E7EB',
      accent_color: '#F59E0B',
      success_color: '#10B981',
      warning_color: '#F59E0B',
      error_color: '#EF4444',
      font_family: 'Inter, system-ui, sans-serif',
      font_size_base: 14,
      border_radius: 8,
      shadow_level: 'md'
    },
    refresh_interval: 300,
    tags: []
  });

  const [tagInput, setTagInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dataSourceAssignments, setDataSourceAssignments] = useState<DashboardDataSourceAssignment[]>([]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Include data source assignments in the submission
      const dashboardData = {
        ...formData,
        data_source_assignments: dataSourceAssignments.map(assignment => ({
          system_data_source_id: assignment.system_data_source_id,
          alias: assignment.alias,
          is_active: assignment.is_active,
        }))
      };

      await onSubmit(dashboardData);

      // Reset form
      setFormData({
        name: '',
        description: '',
        is_default: false,
        is_public: false,
        layout_config: formData.layout_config,
        theme_config: formData.theme_config,
        refresh_interval: 300,
        tags: []
      });
      setTagInput('');
      setDataSourceAssignments([]);
    } catch (error) {
      console.error('Failed to create dashboard:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }));
  };

  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Data source assignment management handlers
  const handleAddDataSourceAssignment = (assignment: DashboardDataSourceAssignmentCreate) => {
    const newAssignment: DashboardDataSourceAssignment = {
      id: `temp-${Date.now()}`, // Temporary ID for UI
      dashboard_id: 'temp', // Will be set when dashboard is created
      ...assignment,
      is_active: assignment.is_active ?? true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    setDataSourceAssignments(prev => [...prev, newAssignment]);
  };

  const handleUpdateDataSourceAssignment = (id: string, updates: DashboardDataSourceAssignmentUpdate) => {
    setDataSourceAssignments(prev => prev.map(assignment =>
      assignment.id === id ? { ...assignment, ...updates, updated_at: new Date().toISOString() } : assignment
    ));
  };

  const handleDeleteDataSourceAssignment = (id: string) => {
    setDataSourceAssignments(prev => prev.filter(assignment => assignment.id !== id));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Create New Dashboard</DialogTitle>
          <DialogDescription>
            Create a new dashboard to organize your data visualizations and analytics.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">Basic Settings</TabsTrigger>
            <TabsTrigger value="data-sources">Data Sources</TabsTrigger>
          </TabsList>

          <form onSubmit={handleSubmit} className="space-y-4">
            <TabsContent value="basic" className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Dashboard Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter dashboard name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what this dashboard will show"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags?.map((tag) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleRemoveTag(tag)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                id="tags"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleTagInputKeyPress}
                placeholder="Add tags (press Enter)"
                className="flex-1"
              />
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleAddTag}
                disabled={!tagInput.trim()}
              >
                Add
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="refresh_interval">Refresh Interval (seconds)</Label>
            <Input
              id="refresh_interval"
              type="number"
              min="30"
              max="3600"
              value={formData.refresh_interval}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                refresh_interval: parseInt(e.target.value) || 300 
              }))}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Label htmlFor="is_default">Set as Default Dashboard</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_default"
                  checked={formData.is_default}
                  onCheckedChange={(checked) => setFormData(prev => ({ 
                    ...prev, 
                    is_default: checked 
                  }))}
                />
                <span className="text-sm text-muted-foreground">
                  This will be your default dashboard
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Label htmlFor="is_public">Make Public</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_public"
                  checked={formData.is_public}
                  onCheckedChange={(checked) => setFormData(prev => ({ 
                    ...prev, 
                    is_public: checked 
                  }))}
                />
                <span className="text-sm text-muted-foreground">
                  Other users can view this dashboard
                </span>
              </div>
            </div>
          </div>
            </TabsContent>

            <TabsContent value="data-sources" className="space-y-4">
              <DashboardCreationDataSourceManager
                dataSourceAssignments={dataSourceAssignments}
                onAddDataSourceAssignment={handleAddDataSourceAssignment}
                onUpdateDataSourceAssignment={handleUpdateDataSourceAssignment}
                onDeleteDataSourceAssignment={handleDeleteDataSourceAssignment}
              />
            </TabsContent>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!formData.name.trim() || isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Create Dashboard'}
            </Button>
          </DialogFooter>
        </form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
