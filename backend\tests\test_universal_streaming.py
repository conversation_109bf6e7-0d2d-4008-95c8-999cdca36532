#!/usr/bin/env python3
"""
Test script to verify universal streaming functionality across all agents.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.concierge_agent.concierge import ConciergeAgent
from agents.marketing_agent.composable_agent import ComposableMarketingAgent
from agents.analysis_agent.composable_agent import ComposableAnalysisAgent

async def test_agent_streaming(agent_class, agent_name, test_message):
    """Test streaming functionality for a specific agent."""
    print(f'\n🧪 Testing {agent_name} Streaming...')
    
    try:
        agent = agent_class()
        await agent.initialize({
            'provider': 'groq',
            'model': 'llama3-70b-8192',
            'temperature': 0.7
        })
        
        print(f'✅ {agent_name} initialized')
        
        # Test streaming functionality
        chunk_count = 0
        total_content = ""
        
        async for chunk in agent.process_streaming_message(
            message=test_message,
            user_id='1',
            conversation_id=f'test-{agent_name.lower()}-streaming',
            context={}
        ):
            chunk_count += 1
            chunk_type = chunk.get('type', 'unknown')
            content = chunk.get('content', '')
            
            if content:
                total_content += content
                content_preview = content[:30].replace('\n', ' ') if content else 'No content'
                print(f'  Chunk {chunk_count}: {chunk_type} - "{content_preview}..."')
            else:
                print(f'  Chunk {chunk_count}: {chunk_type} - metadata')
            
            if chunk_count > 25:  # Prevent infinite loops
                break
        
        print(f'✅ {agent_name} streaming completed with {chunk_count} chunks')
        print(f'✅ Total content length: {len(total_content)} characters')
        
        return True
            
    except Exception as e:
        print(f'❌ Error testing {agent_name}: {e}')
        import traceback
        traceback.print_exc()
        return False

async def test_all_agents():
    """Test streaming functionality across all agents."""
    print('🚀 Testing Universal Streaming Mixin Across All Agents...')
    
    agents_to_test = [
        (ConciergeAgent, "Concierge Agent", "hello, what can you help me with?"),
        (ComposableMarketingAgent, "Marketing Agent", "help me with marketing strategy"),
        (ComposableAnalysisAgent, "Analysis Agent", "analyze some data for me"),
    ]
    
    results = []
    
    for agent_class, agent_name, test_message in agents_to_test:
        success = await test_agent_streaming(agent_class, agent_name, test_message)
        results.append((agent_name, success))
    
    # Summary
    print('\n📊 Test Results Summary:')
    all_passed = True
    for agent_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f'  {agent_name}: {status}')
        if not success:
            all_passed = False
    
    if all_passed:
        print('\n🎉 All agents support universal streaming!')
        return True
    else:
        print('\n💥 Some agents failed streaming tests!')
        return False

if __name__ == '__main__':
    success = asyncio.run(test_all_agents())
    if success:
        print('\n✨ Universal streaming mixin is fully extensible and compatible!')
        sys.exit(0)
    else:
        print('\n🔧 Universal streaming mixin needs improvements!')
        sys.exit(1)
