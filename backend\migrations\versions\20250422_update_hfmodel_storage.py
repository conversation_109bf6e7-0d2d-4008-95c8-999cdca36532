"""Update HFModel storage to use file paths instead of binary data

Revision ID: 20250422_update_hfmodel_storage
Revises: 0795255204dc
Create Date: 2025-04-22 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite


# revision identifiers, used by Alembic.
revision = '20250422_update_hfmodel_storage'
down_revision = '0795255204dc'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if columns exist before adding or dropping them
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)

    # Check if the table exists
    tables = inspector.get_table_names()
    if 'hf_models' in tables:
        columns = [col['name'] for col in inspector.get_columns('hf_models')]

        # Add new columns if they don't exist
        if 'model_path' not in columns:
            op.add_column('hf_models', sa.Column('model_path', sa.String(255), nullable=True))
        if 'tokenizer_path' not in columns:
            op.add_column('hf_models', sa.Column('tokenizer_path', sa.String(255), nullable=True))

        # Drop old columns if they exist
        # Note: We're not migrating data here as that would be handled by the application
        if 'model_data' in columns:
            op.drop_column('hf_models', 'model_data')
        if 'tokenizer_data' in columns:
            op.drop_column('hf_models', 'tokenizer_data')


def downgrade() -> None:
    # Check if columns exist before adding or dropping them
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)

    # Check if the table exists
    tables = inspector.get_table_names()
    if 'hf_models' in tables:
        columns = [col['name'] for col in inspector.get_columns('hf_models')]

        # Add old columns back if they don't exist
        if 'model_data' not in columns:
            op.add_column('hf_models', sa.Column('model_data', sa.Text(), nullable=True))
        if 'tokenizer_data' not in columns:
            op.add_column('hf_models', sa.Column('tokenizer_data', sa.Text(), nullable=True))

        # Drop new columns if they exist
        if 'model_path' in columns:
            op.drop_column('hf_models', 'model_path')
        if 'tokenizer_path' in columns:
            op.drop_column('hf_models', 'tokenizer_path')
