"""
Configuration manager for hierarchical configuration loading.

This module provides a configuration manager that can load configurations
from multiple sources (environment variables, YAML files) and merge them
according to priority.
"""

import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any, Union
from functools import lru_cache

from .app import AppConfig

logger = logging.getLogger(__name__)


class ConfigurationManager:
    """
    Configuration manager for loading and managing application configuration.
    
    The manager loads configuration in the following priority order:
    1. Environment variables (highest priority)
    2. Environment-specific YAML file (e.g., production.yaml)
    3. Default configuration values (lowest priority)
    """
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_dir: Directory containing configuration files
        """
        self.config_dir = Path(config_dir) if config_dir else self._get_default_config_dir()
        self._config_cache: Optional[AppConfig] = None
        self._environment = os.getenv("ENVIRONMENT", "development")
        
        logger.info(f"Configuration manager initialized with config_dir: {self.config_dir}")
        logger.info(f"Environment: {self._environment}")
    
    def _get_default_config_dir(self) -> Path:
        """Get the default configuration directory."""
        # Look for config directory relative to this file
        current_dir = Path(__file__).parent.parent.parent  # Go up to backend/
        config_dir = current_dir / "config"
        
        if not config_dir.exists():
            # Create config directory if it doesn't exist
            config_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created config directory: {config_dir}")
        
        return config_dir
    
    @lru_cache(maxsize=1)
    def load_config(self, force_reload: bool = False) -> AppConfig:
        """
        Load the application configuration.
        
        Args:
            force_reload: Force reload even if cached
            
        Returns:
            Application configuration
        """
        if self._config_cache is not None and not force_reload:
            return self._config_cache
        
        logger.info("Loading application configuration...")
        
        try:
            # Start with environment-based configuration
            config = AppConfig.from_env()
            logger.info("Loaded base configuration from environment variables")
            
            # Load environment-specific YAML file if it exists
            env_config_file = self.config_dir / f"{self._environment}.yaml"
            if env_config_file.exists():
                logger.info(f"Loading environment-specific config from: {env_config_file}")
                env_config = AppConfig.from_yaml_file(env_config_file)
                
                # Merge environment config with base config
                # Environment variables take precedence over YAML
                config = self._merge_configs(env_config, config)
                logger.info("Merged environment-specific configuration")
            else:
                logger.warning(f"Environment-specific config file not found: {env_config_file}")
            
            # Validate the final configuration
            self._validate_config(config)
            
            # Cache the configuration
            self._config_cache = config
            
            logger.info("Configuration loaded successfully")
            logger.debug(f"Configuration summary: {config.config_summary}")
            
            return config
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Return a basic configuration with environment variables only
            logger.warning("Falling back to environment-only configuration")
            return AppConfig.from_env()
    
    def _merge_configs(self, base_config: AppConfig, override_config: AppConfig) -> AppConfig:
        """
        Merge two configurations with override taking precedence.
        
        Args:
            base_config: Base configuration (lower priority)
            override_config: Override configuration (higher priority)
            
        Returns:
            Merged configuration
        """
        # Convert to dictionaries for merging
        base_dict = base_config.model_dump()
        override_dict = override_config.model_dump()
        
        # Deep merge the dictionaries
        merged_dict = self._deep_merge_dicts(base_dict, override_dict)
        
        # Create new configuration from merged data
        return AppConfig(**merged_dict)
    
    def _deep_merge_dicts(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deep merge two dictionaries.
        
        Args:
            base: Base dictionary
            override: Override dictionary
            
        Returns:
            Merged dictionary
        """
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge_dicts(result[key], value)
            else:
                # Override takes precedence, but only if the value is not None or empty string
                if value is not None and value != "":
                    result[key] = value
        
        return result
    
    def _validate_config(self, config: AppConfig) -> None:
        """
        Validate the configuration.
        
        Args:
            config: Configuration to validate
            
        Raises:
            ValueError: If configuration is invalid
        """
        # Check required fields
        if not config.database.url:
            raise ValueError("Database URL is required")
        
        if not config.security.jwt_secret_key:
            raise ValueError("JWT secret key is required")
        
        # Validate environment-specific requirements
        if config.environment.is_production:
            if config.security.jwt_secret_key == "your-secret-key-for-development-only":
                raise ValueError("Production environment requires a secure JWT secret key")
            
            if not config.security.enforce_https:
                logger.warning("HTTPS is not enforced in production environment")
            
            if config.database.echo:
                logger.warning("Database query logging is enabled in production")
        
        # Validate LLM configuration
        if not config.llm.enabled_providers:
            logger.warning("No LLM providers are enabled")
        
        logger.info("Configuration validation passed")
    
    def get_config(self) -> AppConfig:
        """Get the current configuration (cached)."""
        return self.load_config()
    
    def reload_config(self) -> AppConfig:
        """Reload the configuration from sources."""
        # Clear cache
        self._config_cache = None
        self.load_config.cache_clear()
        
        return self.load_config(force_reload=True)
    
    def save_config_template(self, environment: str = "development") -> Path:
        """
        Save a configuration template for the specified environment.
        
        Args:
            environment: Environment name
            
        Returns:
            Path to the saved template file
        """
        template_file = self.config_dir / f"{environment}.yaml.template"
        
        # Create a default configuration
        default_config = AppConfig.from_env()
        
        # Save as YAML template
        default_config.save_to_yaml(template_file)
        
        logger.info(f"Configuration template saved to: {template_file}")
        return template_file
    
    @property
    def environment(self) -> str:
        """Get the current environment."""
        return self._environment
    
    @property
    def config_file_path(self) -> Path:
        """Get the path to the current environment's config file."""
        return self.config_dir / f"{self._environment}.yaml"


# Global configuration manager instance
_config_manager: Optional[ConfigurationManager] = None


def get_config_manager() -> ConfigurationManager:
    """Get the global configuration manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigurationManager()
    return _config_manager


def get_config() -> AppConfig:
    """Get the current application configuration."""
    return get_config_manager().get_config()


def reload_config() -> AppConfig:
    """Reload the application configuration."""
    return get_config_manager().reload_config()
