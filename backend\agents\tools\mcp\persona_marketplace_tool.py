"""
Persona marketplace tool for the Datagenius concierge agent.

This module provides an MCP-compatible tool for accessing marketplace information,
user purchases, and persona capabilities to enable effective recommendations.
"""

import logging
from typing import Dict, Any, List, Optional

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class PersonaMarketplaceTool(BaseMCPTool):
    """Tool for accessing persona marketplace and user purchase information."""

    def __init__(self):
        """Initialize the persona marketplace tool."""
        super().__init__(
            name="get_persona_marketplace_info",
            description="Get information about available personas, user purchases, and capabilities",
            input_schema={
                "type": "object",
                "properties": {
                    "user_id": {
                        "type": "string",
                        "description": "The user ID to get purchase information for"
                    },
                    "query_type": {
                        "type": "string",
                        "enum": [
                            "user_personas",      # Get personas the user owns
                            "available_personas", # Get all available personas in marketplace
                            "persona_capabilities", # Get capabilities of specific personas
                            "recommendations",    # Get persona recommendations based on intent
                            "marketplace_overview" # Get overview of all personas with ownership status
                        ],
                        "description": "Type of marketplace query to perform"
                    },
                    "persona_ids": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Specific persona IDs to get information about"
                    },
                    "intent_type": {
                        "type": "string",
                        "description": "User intent type for recommendations"
                    },
                    "user_requirements": {
                        "type": "string",
                        "description": "User requirements or task description for recommendations"
                    }
                },
                "required": ["user_id", "query_type"]
            },
            annotations={
                "title": "Get Persona Marketplace Info",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            user_id = arguments["user_id"]
            query_type = arguments["query_type"]
            persona_ids = arguments.get("persona_ids", [])
            intent_type = arguments.get("intent_type")
            user_requirements = arguments.get("user_requirements", "")

            logger.info(f"Persona marketplace query: {query_type} for user {user_id}")

            if query_type == "user_personas":
                result = await self._get_user_personas(user_id)
            elif query_type == "available_personas":
                result = await self._get_available_personas()
            elif query_type == "persona_capabilities":
                result = await self._get_persona_capabilities(persona_ids)
            elif query_type == "recommendations":
                result = await self._get_persona_recommendations(user_id, intent_type, user_requirements)
            elif query_type == "marketplace_overview":
                result = await self._get_marketplace_overview(user_id)
            else:
                raise ValueError(f"Unknown query type: {query_type}")

            return {
                "content": [
                    {
                        "type": "text",
                        "text": result["description"]
                    }
                ],
                "metadata": {
                    "query_type": query_type,
                    "user_id": user_id,
                    "data": result["data"]
                }
            }

        except Exception as e:
            logger.error(f"Error in PersonaMarketplaceTool execute: {str(e)}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error accessing marketplace information: {str(e)}"
                    }
                ],
                "metadata": {
                    "error_type": e.__class__.__name__,
                    "error_details": str(e),
                    "component": "PersonaMarketplaceTool"
                }
            }

    async def _get_user_personas(self, user_id: str) -> Dict[str, Any]:
        """Get personas that the user has purchased/owns."""
        try:
            # Import database functions and services
            from app.database import get_db
            from app.services.persona_service import persona_service
            from sqlalchemy.orm import Session

            # Get database session
            db_gen = get_db()
            db: Session = next(db_gen)

            try:
                # Convert user_id to int (database expects integer user IDs)
                user_id_int = int(user_id)

                # Get purchased persona IDs from database
                purchased_persona_ids = persona_service.get_user_purchased_personas(db, user_id_int)
                logger.info(f"User {user_id} has purchased personas: {purchased_persona_ids}")

                # Get detailed information for each purchased persona
                user_personas = []
                for persona_id in purchased_persona_ids:
                    persona_info = await self._get_persona_info(persona_id)
                    if persona_info:
                        # Mark as owned
                        persona_info["is_owned"] = True
                        user_personas.append(persona_info)

                description = f"User owns {len(user_personas)} persona(s)"
                if user_personas:
                    description += ": " + ", ".join([p["name"] for p in user_personas])

                return {
                    "description": description,
                    "data": {
                        "user_personas": user_personas,
                        "purchased_persona_ids": purchased_persona_ids,
                        "count": len(user_personas)
                    }
                }

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error getting user personas: {e}")
            # Return empty result on error - no hardcoded fallbacks
            return {
                "description": "Error retrieving user personas",
                "data": {
                    "user_personas": [],
                    "purchased_persona_ids": [],
                    "count": 0,
                    "error": str(e)
                }
            }

    async def _get_available_personas(self) -> Dict[str, Any]:
        """Get all available personas in the marketplace."""
        try:
            # Import agent registry and database functions
            from agents.registry import AgentRegistry
            from app.database import get_db, get_personas
            from sqlalchemy.orm import Session

            # Get database session
            db_gen = get_db()
            db: Session = next(db_gen)

            try:
                # Get all registered personas from agent registry
                registered_personas = AgentRegistry.list_registered_personas()

                # Get personas from database for pricing information
                db_personas = get_personas(db, skip=0, limit=1000)
                db_persona_dict = {p.id: p for p in db_personas}

                persona_details = []

                for persona_id in registered_personas:
                    persona_info = await self._get_persona_info(persona_id)
                    if persona_info:
                        # Add database information if available
                        if persona_id in db_persona_dict:
                            db_persona = db_persona_dict[persona_id]
                            persona_info.update({
                                "price": db_persona.price,
                                "is_active": db_persona.is_active,
                                "industry": db_persona.industry,
                                "rating": db_persona.rating,
                                "review_count": db_persona.review_count
                            })

                        # Mark as available for purchase
                        persona_info["is_owned"] = False
                        persona_details.append(persona_info)

                # Separate free and paid personas
                free_personas = [p for p in persona_details if p.get("price", 0) == 0]
                paid_personas = [p for p in persona_details if p.get("price", 0) > 0]

                description = f"Marketplace has {len(persona_details)} available persona(s): {len(free_personas)} free, {len(paid_personas)} paid"

                return {
                    "description": description,
                    "data": {
                        "available_personas": persona_details,
                        "free_personas": free_personas,
                        "paid_personas": paid_personas,
                        "count": len(persona_details),
                        "free_count": len(free_personas),
                        "paid_count": len(paid_personas)
                    }
                }

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error getting available personas: {e}")
            # Return empty result on error - no hardcoded fallbacks
            return {
                "description": "Error retrieving available personas",
                "data": {
                    "available_personas": [],
                    "free_personas": [],
                    "paid_personas": [],
                    "count": 0,
                    "free_count": 0,
                    "paid_count": 0,
                    "error": str(e)
                }
            }

    async def _get_persona_capabilities(self, persona_ids: List[str]) -> Dict[str, Any]:
        """Get detailed capabilities of specific personas."""
        try:
            persona_capabilities = []

            for persona_id in persona_ids:
                persona_info = await self._get_persona_info(persona_id)
                if persona_info:
                    persona_capabilities.append(persona_info)

            description = f"Retrieved capabilities for {len(persona_capabilities)} persona(s)"

            return {
                "description": description,
                "data": {
                    "persona_capabilities": persona_capabilities,
                    "count": len(persona_capabilities)
                }
            }

        except Exception as e:
            logger.error(f"Error getting persona capabilities: {e}")
            return {
                "description": "Error retrieving persona capabilities",
                "data": {
                    "persona_capabilities": [],
                    "count": 0,
                    "error": str(e)
                }
            }

    async def _get_persona_recommendations(self, user_id: str, intent_type: str, user_requirements: str) -> Dict[str, Any]:
        """Get persona recommendations based on user intent and requirements."""
        try:
            # Get user's owned personas first
            user_personas_result = await self._get_user_personas(user_id)
            purchased_persona_ids = user_personas_result["data"]["purchased_persona_ids"]

            # Get all available personas
            available_personas_result = await self._get_available_personas()
            available_personas = available_personas_result["data"]["available_personas"]

            # Dynamic recommendation logic based on intent type and capabilities
            matching_personas = []

            # Define intent to capability mappings dynamically
            intent_capability_keywords = {
                "data_analysis": ["data", "analysis", "visualization", "statistics", "pandas", "chart", "composable"],
                "content_creation": ["marketing", "content", "campaign", "strategy", "seo", "creative"],
                "general_question": []  # Will use user_requirements for matching
            }

            # Get relevant capability keywords for this intent
            relevant_keywords = intent_capability_keywords.get(intent_type, [])

            # If no specific intent keywords, extract from user requirements
            if not relevant_keywords and user_requirements:
                user_req_lower = user_requirements.lower()
                # Check for data-related keywords
                if any(keyword in user_req_lower for keyword in ["data", "analysis", "chart", "graph", "csv", "excel", "statistics"]):
                    relevant_keywords = intent_capability_keywords["data_analysis"]
                # Check for marketing-related keywords
                elif any(keyword in user_req_lower for keyword in ["marketing", "content", "campaign", "strategy", "seo"]):
                    relevant_keywords = intent_capability_keywords["marketing_request"]

            # Find personas with matching capabilities
            for persona in available_personas:
                persona_capabilities = persona.get("capabilities", [])
                persona_description = persona.get("description", "").lower()
                persona_name = persona.get("name", "").lower()

                # Check if any relevant keywords match persona capabilities, description, or name
                if relevant_keywords:
                    capability_match = any(
                        keyword in " ".join(persona_capabilities).lower() or
                        keyword in persona_description or
                        keyword in persona_name
                        for keyword in relevant_keywords
                    )
                    if capability_match:
                        matching_personas.append(persona)
                else:
                    # If no specific keywords, include all non-concierge personas
                    if persona.get("id") != "concierge-agent":
                        matching_personas.append(persona)

            # Separate owned vs. available for purchase
            owned_recommendations = []
            purchase_recommendations = []

            for persona in matching_personas:
                if persona["id"] in purchased_persona_ids:
                    persona["is_owned"] = True
                    owned_recommendations.append(persona)
                else:
                    persona["is_owned"] = False
                    purchase_recommendations.append(persona)

            # Create comprehensive description
            description_parts = []
            if owned_recommendations:
                description_parts.append(f"{len(owned_recommendations)} recommended persona(s) you already own")
            if purchase_recommendations:
                description_parts.append(f"{len(purchase_recommendations)} recommended persona(s) available for purchase")

            if not description_parts:
                description = "No specific persona recommendations found for your request"
            else:
                description = "Found " + " and ".join(description_parts)

            return {
                "description": description,
                "data": {
                    "owned_recommendations": owned_recommendations,
                    "purchase_recommendations": purchase_recommendations,
                    "all_matching_personas": matching_personas,
                    "owned_count": len(owned_recommendations),
                    "purchase_count": len(purchase_recommendations),
                    "total_matches": len(matching_personas),
                    "user_purchased_ids": purchased_persona_ids
                }
            }

        except Exception as e:
            logger.error(f"Error getting persona recommendations: {e}")
            return {
                "description": "Error generating persona recommendations",
                "data": {
                    "owned_recommendations": [],
                    "purchase_recommendations": [],
                    "all_matching_personas": [],
                    "owned_count": 0,
                    "purchase_count": 0,
                    "total_matches": 0,
                    "user_purchased_ids": [],
                    "error": str(e)
                }
            }

    async def _get_marketplace_overview(self, user_id: str) -> Dict[str, Any]:
        """Get overview of all personas with ownership status for the user."""
        try:
            # Get user's owned personas
            user_personas_result = await self._get_user_personas(user_id)
            purchased_persona_ids = user_personas_result["data"]["purchased_persona_ids"]

            # Get all available personas
            available_personas_result = await self._get_available_personas()
            all_personas = available_personas_result["data"]["available_personas"]

            # Categorize personas by ownership and price
            owned_personas = []
            free_personas = []
            paid_personas = []

            for persona in all_personas:
                persona_copy = persona.copy()
                if persona["id"] in purchased_persona_ids:
                    persona_copy["is_owned"] = True
                    owned_personas.append(persona_copy)
                else:
                    persona_copy["is_owned"] = False
                    if persona.get("price", 0) == 0:
                        free_personas.append(persona_copy)
                    else:
                        paid_personas.append(persona_copy)

            description = f"Marketplace overview: {len(owned_personas)} owned, {len(free_personas)} free available, {len(paid_personas)} paid available"

            return {
                "description": description,
                "data": {
                    "owned_personas": owned_personas,
                    "free_personas": free_personas,
                    "paid_personas": paid_personas,
                    "all_personas": all_personas,
                    "owned_count": len(owned_personas),
                    "free_count": len(free_personas),
                    "paid_count": len(paid_personas),
                    "total_count": len(all_personas),
                    "user_purchased_ids": purchased_persona_ids
                }
            }

        except Exception as e:
            logger.error(f"Error getting marketplace overview: {e}")
            return {
                "description": "Error retrieving marketplace overview",
                "data": {
                    "owned_personas": [],
                    "free_personas": [],
                    "paid_personas": [],
                    "all_personas": [],
                    "owned_count": 0,
                    "free_count": 0,
                    "paid_count": 0,
                    "total_count": 0,
                    "user_purchased_ids": [],
                    "error": str(e)
                }
            }

    async def _get_persona_info(self, persona_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific persona."""
        try:
            # Import agent registry
            from agents.registry import AgentRegistry

            # Get configuration from registry
            config = AgentRegistry.get_configuration(persona_id)
            if config:
                persona_info = {
                    "id": persona_id,
                    "name": config.get("name", persona_id.replace("-", " ").title()),
                    "description": config.get("description", "AI assistant for specialized tasks"),
                    "capabilities": config.get("capabilities", []),
                    "category": config.get("category", "general"),
                    "price": config.get("price", 0),
                    "skills": config.get("skills", []),
                    "industry": config.get("industry", "Technology"),
                    "rating": config.get("rating", 4.5),
                    "review_count": config.get("review_count", 0),
                    "is_active": config.get("is_active", True),
                    "version": config.get("version", "1.0.0")
                }

                # Add purchase status (will be updated by calling methods)
                persona_info["is_owned"] = False

                return persona_info

            # No fallback - return None if not found in registry
            logger.warning(f"Persona {persona_id} not found in agent registry")
            return None

        except Exception as e:
            logger.error(f"Error getting persona info for {persona_id}: {e}")
            return None
