
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Clock, 
  Download, 
  Eye, 
  Share2, 
  Trash, 
  Bar<PERSON>hart4, 
  <PERSON><PERSON><PERSON>, 
  Pie<PERSON><PERSON> 
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface SavedReport {
  id: string;
  name: string;
  description: string;
  created: string;
  type: "line" | "bar" | "pie" | "mixed";
  thumbnail: React.ComponentType<any>;
}

export function SavedReports() {
  const { toast } = useToast();
  
  const savedReports: SavedReport[] = [
    {
      id: "1",
      name: "Q1 Sales Performance",
      description: "Quarterly sales report showing performance metrics across all product lines",
      created: "Apr 12, 2023",
      type: "bar",
      thumbnail: BarChart4,
    },
    {
      id: "2",
      name: "Customer Acquisition Trends",
      description: "Monthly breakdown of new customer acquisition channels and conversion rates",
      created: "May 3, 2023",
      type: "line",
      thumbnail: Line<PERSON><PERSON>,
    },
    {
      id: "3",
      name: "Product Category Breakdown",
      description: "Analysis of sales distribution across different product categories",
      created: "May 18, 2023",
      type: "pie",
      thumbnail: <PERSON><PERSON><PERSON>,
    },
    {
      id: "4",
      name: "Marketing Campaign ROI",
      description: "Comparison of ROI across different marketing campaigns and channels",
      created: "May 24, 2023",
      type: "mixed",
      thumbnail: BarChart4,
    },
  ];
  
  const handleViewReport = (id: string) => {
    toast({
      title: "Opening Report",
      description: "Loading the selected report for viewing",
    });
  };
  
  const handleDownloadReport = (id: string) => {
    toast({
      title: "Download Started",
      description: "Your report is being downloaded",
    });
  };
  
  const handleShareReport = (id: string) => {
    toast({
      title: "Share Report",
      description: "Share options opened for this report",
    });
  };
  
  const handleDeleteReport = (id: string) => {
    toast({
      title: "Report Deleted",
      description: "The report has been removed from your saved reports",
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold">Saved Reports</h2>
        <p className="text-sm text-muted-foreground">
          Access your previously generated reports
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {savedReports.map((report) => (
          <Card key={report.id} className="overflow-hidden">
            <div className="bg-primary/5 p-6 flex justify-center items-center border-b">
              <report.thumbnail className="h-16 w-16 text-primary" />
            </div>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">{report.name}</h3>
                  <p className="text-sm text-muted-foreground">{report.description}</p>
                </div>
                
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  Created on {report.created}
                </div>
                
                <div className="flex justify-between">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="gap-1"
                    onClick={() => handleViewReport(report.id)}
                  >
                    <Eye className="h-3 w-3" />
                    View
                  </Button>
                  
                  <div className="flex gap-1">
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8"
                      onClick={() => handleDownloadReport(report.id)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8"
                      onClick={() => handleShareReport(report.id)}
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 text-destructive hover:text-destructive/80"
                      onClick={() => handleDeleteReport(report.id)}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
