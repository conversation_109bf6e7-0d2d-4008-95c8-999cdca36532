"""
Data storytelling MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for generating data storytelling insights
using PandasAI.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional

# PandasAI imports
import pandasai as pai

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import ErrorHandler
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class DataStorytellingTool(BaseMCPTool):
    """Tool for generating data storytelling insights using PandasAI."""

    def __init__(self):
        """Initialize the data storytelling tool."""
        super().__init__(
            name="data_storytelling",
            description="Generates a narrative explanation (data story) about a specific topic using insights derived from the data via PandasAI.",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV, Excel, JSON supported)."
                    },
                    "topic": {
                        "type": "string",
                        "description": "The main topic or question the data story should address (e.g., 'sales trends', 'customer segment differences')."
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for the LLM provider."
                    },
                    "provider": {
                        "type": "string",
                        "description": "LLM provider to use (e.g., openai, groq, anthropic).",
                        "default": "openai"
                    },
                    "model": {
                        "type": "string",
                        "description": "Model name to use for the analysis."
                    }
                },
                "required": ["file_path", "topic", "api_key"]
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # No additional initialization needed
        pass

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the data storytelling tool with agent-aware narrative capabilities.

        Args:
            arguments: Arguments for tool execution (following the inputSchema)

        Returns:
            Tool execution results in MCP format
        """
        file_path = arguments["file_path"]
        topic = arguments["topic"]
        api_key = arguments["api_key"]
        provider = arguments.get("provider", "openai")
        model = arguments.get("model")

        # Detect agent identity for personalized storytelling
        agent_id = arguments.get("persona_id") or arguments.get("agent_id")
        context = arguments.get("context", {})
        agent_identity = await detect_agent_identity(
            agent_id=agent_id,
            context=context,
            intent_type="data_storytelling"
        )

        logger.info(f"Detected agent identity: {agent_identity} for data storytelling")

        logger.info(f"PandasAI data storytelling requested for {file_path} with topic: {topic}")

        # Check if we have a cached response
        cache_key = f"{file_path}:{topic}:{provider}:{model}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            logger.info(f"Using cached result for data storytelling: {cache_key}")
            return cached_result

        # Input validation
        if not provider or not api_key:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Error: LLM Provider and API Key must be provided."
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "topic": topic,
                    "status": "error",
                    "error_type": "config_error"
                }
            }

        # Load the data
        try:
            if file_path.lower().endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.lower().endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.lower().endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            if df.empty:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"The dataframe loaded from {file_path} is empty."
                        }
                    ]
                }
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error loading data from {file_path}: {str(e)}"
                    }
                ]
            }

        # Generate the story
        try:
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)

            # Create agent with the dataframe
            if not self.pandasai.create_agent(df=df, model=model):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Error creating PandasAI Agent"
                        }
                    ]
                }

            # Craft an agent-aware prompt guiding PandasAI towards storytelling
            prompt = await self._create_agent_aware_storytelling_prompt(topic, agent_identity)

            # Run the analysis using PandasAI wrapper
            result = self.pandasai.chat(prompt)

            # Process the result
            if isinstance(result, str):
                results_text = result
                results_metadata = {"status": "success", "result_type": "string"}
            elif result is None:
                results_text = "The storytelling analysis was processed, but did not produce a narrative."
                results_metadata = {"status": "success", "result_type": "None"}
            else:
                # Handle other potential return types (though string is expected)
                results_text = f"Storytelling analysis returned an unexpected result type: {type(result).__name__}. Result: {str(result)}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__)}

            response = {
                "content": [
                    {
                        "type": "text",
                        "text": results_text
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "topic": topic,
                    "implementation": "pandasai",
                    **results_metadata
                }
            }

            # Cache the response
            self.cache.set(cache_key, response)
            return response

        except Exception as e:
            error_handler = ErrorHandler()
            error_info = error_handler.handle_error(e, context={
                "operation": "data_storytelling",
                "file_path": file_path,
                "topic": topic
            })

            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": error_info["message"]
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "topic": topic,
                    "error_type": error_info["error_type"],
                    "details": error_info["details"]
                }
            }

    async def _create_agent_aware_storytelling_prompt(self, topic: str, agent_identity: str) -> str:
        """Create an agent-aware storytelling prompt based on agent capabilities and style."""
        try:
            # Get agent system prompt to extract storytelling preferences
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract storytelling style preferences from system prompt
            storytelling_style = await self._extract_storytelling_style_from_prompt(system_prompt, agent_identity)

            # Create enhanced prompt based on agent style
            enhanced_prompt = self._apply_agent_style_to_storytelling(topic, storytelling_style, agent_identity)

            logger.info(f"Enhanced storytelling prompt for {agent_identity}: {enhanced_prompt[:100]}...")
            return enhanced_prompt

        except Exception as e:
            logger.warning(f"Failed to enhance storytelling prompt with agent style: {e}")
            # Return basic prompt if enhancement fails
            return f"Analyze the dataframe and tell a story about '{topic}'. Focus on key insights, trends, or interesting patterns related to this topic. Explain your findings clearly."

    async def _extract_storytelling_style_from_prompt(self, system_prompt: str, agent_identity: str) -> Dict[str, Any]:
        """Extract storytelling style preferences from agent system prompt."""
        style_preferences = {
            "narrative_style": "informative",
            "tone": "professional",
            "focus_areas": [],
            "explanation_depth": "moderate",
            "audience_level": "general"
        }

        if not system_prompt:
            return self._get_default_storytelling_style_for_agent(agent_identity)

        # Look for storytelling-related patterns in the system prompt
        import re

        # Check for narrative style preferences
        if re.search(r"conversational|friendly|approachable", system_prompt, re.IGNORECASE):
            style_preferences["narrative_style"] = "conversational"
        elif re.search(r"analytical|technical|detailed", system_prompt, re.IGNORECASE):
            style_preferences["narrative_style"] = "analytical"
        elif re.search(r"engaging|compelling|story", system_prompt, re.IGNORECASE):
            style_preferences["narrative_style"] = "engaging"

        # Check for tone preferences
        if re.search(r"professional|business|corporate", system_prompt, re.IGNORECASE):
            style_preferences["tone"] = "professional"
        elif re.search(r"friendly|warm|approachable", system_prompt, re.IGNORECASE):
            style_preferences["tone"] = "friendly"
        elif re.search(r"educational|teaching|learning", system_prompt, re.IGNORECASE):
            style_preferences["tone"] = "educational"

        # Check for explanation depth
        if re.search(r"detailed|comprehensive|thorough", system_prompt, re.IGNORECASE):
            style_preferences["explanation_depth"] = "detailed"
        elif re.search(r"concise|brief|summary", system_prompt, re.IGNORECASE):
            style_preferences["explanation_depth"] = "concise"

        # Extract focus areas based on capabilities
        focus_areas = []
        if re.search(r"marketing|campaign|strategy", system_prompt, re.IGNORECASE):
            focus_areas.append("business_impact")
        if re.search(r"statistical|analysis|insights", system_prompt, re.IGNORECASE):
            focus_areas.append("statistical_insights")
        if re.search(r"classification|categorization", system_prompt, re.IGNORECASE):
            focus_areas.append("data_patterns")
        if re.search(r"educational|explain|teaching", system_prompt, re.IGNORECASE):
            focus_areas.append("educational_context")

        style_preferences["focus_areas"] = focus_areas

        return style_preferences

    def _get_default_storytelling_style_for_agent(self, agent_identity: str) -> Dict[str, Any]:
        """Get default storytelling style for specific agent types."""
        default_styles = {
            "analyst": {
                "narrative_style": "analytical",
                "tone": "professional",
                "focus_areas": ["statistical_insights", "data_patterns"],
                "explanation_depth": "detailed",
                "audience_level": "technical"
            },
            "marketer": {
                "narrative_style": "engaging",
                "tone": "business_focused",
                "focus_areas": ["business_impact", "actionable_insights"],
                "explanation_depth": "moderate",
                "audience_level": "business"
            },
            "classifier": {
                "narrative_style": "structured",
                "tone": "organized",
                "focus_areas": ["data_patterns", "categorization_insights"],
                "explanation_depth": "moderate",
                "audience_level": "technical"
            },
            "concierge": {
                "narrative_style": "conversational",
                "tone": "friendly",
                "focus_areas": ["key_insights", "educational_context"],
                "explanation_depth": "accessible",
                "audience_level": "general"
            }
        }

        return default_styles.get(agent_identity, {
            "narrative_style": "informative",
            "tone": "professional",
            "focus_areas": ["general_insights"],
            "explanation_depth": "moderate",
            "audience_level": "general"
        })

    def _apply_agent_style_to_storytelling(self, topic: str, style: Dict[str, Any], agent_identity: str) -> str:
        """Apply agent-specific style to the storytelling prompt."""

        # Base prompt
        base_prompt = f"Analyze the dataframe and tell a compelling data story about '{topic}'."

        # Add style-specific instructions based on agent identity
        style_instructions = []

        # Add narrative style instructions
        narrative_style = style.get("narrative_style", "informative")
        if narrative_style == "conversational":
            style_instructions.append("Use a conversational, friendly narrative style that engages the reader.")
        elif narrative_style == "analytical":
            style_instructions.append("Use an analytical narrative style with logical flow and evidence-based conclusions.")
        elif narrative_style == "engaging":
            style_instructions.append("Create an engaging, compelling narrative that captures attention and tells a memorable story.")
        elif narrative_style == "structured":
            style_instructions.append("Use a well-structured narrative with clear organization and systematic presentation.")

        # Add tone instructions
        tone = style.get("tone", "professional")
        if tone == "business_focused":
            style_instructions.append("Maintain a business-focused tone that emphasizes practical implications and actionable insights.")
        elif tone == "friendly":
            style_instructions.append("Use a warm, friendly tone that makes the data accessible and approachable.")
        elif tone == "educational":
            style_instructions.append("Adopt an educational tone that explains concepts clearly and helps readers learn.")
        elif tone == "organized":
            style_instructions.append("Use an organized, systematic tone that presents information clearly and logically.")

        # Add explanation depth instructions
        explanation_depth = style.get("explanation_depth", "moderate")
        if explanation_depth == "detailed":
            style_instructions.append("Provide detailed explanations of the analysis methods, statistical significance, and underlying patterns.")
        elif explanation_depth == "concise":
            style_instructions.append("Keep explanations concise while still covering the key insights and findings.")
        elif explanation_depth == "accessible":
            style_instructions.append("Make explanations accessible to a general audience, avoiding technical jargon.")

        # Add focus area instructions
        focus_areas = style.get("focus_areas", [])
        if "business_impact" in focus_areas:
            style_instructions.append("Emphasize the business impact and practical implications of the findings.")
        if "statistical_insights" in focus_areas:
            style_instructions.append("Include statistical insights, significance tests, and confidence intervals where relevant.")
        if "data_patterns" in focus_areas:
            style_instructions.append("Highlight interesting data patterns, trends, and relationships.")
        if "educational_context" in focus_areas:
            style_instructions.append("Provide educational context that helps readers understand the broader implications.")
        if "actionable_insights" in focus_areas:
            style_instructions.append("Focus on actionable insights that can inform decision-making.")

        # Add audience level instructions
        audience_level = style.get("audience_level", "general")
        if audience_level == "technical":
            style_instructions.append("Tailor the narrative for a technical audience familiar with data analysis concepts.")
        elif audience_level == "business":
            style_instructions.append("Tailor the narrative for a business audience focused on strategic implications.")
        elif audience_level == "general":
            style_instructions.append("Make the narrative accessible to a general audience without specialized knowledge.")

        # Combine base prompt with style instructions
        if style_instructions:
            enhanced_prompt = f"{base_prompt}\n\nStorytelling Style Instructions:\n" + "\n".join([f"- {instruction}" for instruction in style_instructions])
        else:
            enhanced_prompt = base_prompt

        return enhanced_prompt
