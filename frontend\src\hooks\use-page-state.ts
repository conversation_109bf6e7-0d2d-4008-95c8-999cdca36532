import { useEffect, useCallback, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { 
  usePageStateStore, 
  PageStates, 
  DataChatState,
  SettingsState,
  DataIntegrationState,
  AIMarketplaceState,
  ReportsState,
  MainDashboardState
} from '@/stores/page-state-store';

// Map route paths to page state keys
const routeToPageMap: Record<string, keyof PageStates> = {
  '/data-chat': 'dataChat',
  '/settings': 'settings',
  '/data-integration': 'dataIntegration',
  '/ai-marketplace': 'aiMarketplace',
  '/reports': 'reports',
  '/dashboard': 'mainDashboard',
};

// Generic hook for any page state
export function usePageState<K extends keyof PageStates>(
  pageKey: K,
  initialState?: Partial<PageStates[K]>
) {
  const store = usePageStateStore();
  const location = useLocation();
  const isInitializedRef = useRef(false);
  
  // Get current state
  const currentState = store.getPageState(pageKey);
  
  // Set state function with error handling (stable reference)
  const setState = useCallback(
    (state: Partial<PageStates[K]>) => {
      try {
        store.setPageState(pageKey, state);
      } catch (error) {
        console.warn(`Failed to save page state for ${pageKey}:`, error);
      }
    },
    [pageKey] // Removed store from dependencies to prevent loops
  );
  
  // Clear state function
  const clearState = useCallback(() => {
    store.clearPageState(pageKey);
  }, [pageKey]); // Removed store from dependencies to prevent loops
  
  // Initialize state on mount if provided
  useEffect(() => {
    try {
      if (!isInitializedRef.current && initialState && !currentState) {
        setState(initialState);
        isInitializedRef.current = true;
      }
    } catch (error) {
      console.warn(`Failed to initialize page state for ${pageKey}:`, error);
    }
  }, [initialState, currentState, setState, pageKey]);
  
  // Update last visited when component mounts (only once per route change)
  useEffect(() => {
    const currentPageKey = routeToPageMap[location.pathname];
    if (currentPageKey === pageKey) {
      store.updateLastVisited(pageKey);
    }
  }, [location.pathname, pageKey]); // Removed store from dependencies to prevent loops
  
  return {
    state: currentState,
    setState,
    clearState,
    hasState: !!currentState,
  };
}

// Specific hooks for each page type
export function useDataChatState(initialState?: Partial<DataChatState>) {
  return usePageState('dataChat', initialState);
}

export function useSettingsState(initialState?: Partial<SettingsState>) {
  return usePageState('settings', initialState);
}

export function useDataIntegrationState(initialState?: Partial<DataIntegrationState>) {
  return usePageState('dataIntegration', initialState);
}

export function useAIMarketplaceState(initialState?: Partial<AIMarketplaceState>) {
  return usePageState('aiMarketplace', initialState);
}

export function useReportsState(initialState?: Partial<ReportsState>) {
  return usePageState('reports', initialState);
}

export function useMainDashboardState(initialState?: Partial<MainDashboardState>) {
  return usePageState('mainDashboard', initialState);
}

// Hook for automatic state persistence based on current route
export function useAutoPageState() {
  const location = useLocation();
  const store = usePageStateStore();
  
  const currentPageKey = routeToPageMap[location.pathname];
  
  // Auto-save state function that can be called by components
  const saveState = useCallback(
    (state: any) => {
      if (currentPageKey) {
        store.setPageState(currentPageKey, state);
      }
    },
    [currentPageKey] // Remove store from dependencies to prevent loops
  );

  // Get current page state
  const getState = useCallback(() => {
    if (currentPageKey) {
      return store.getPageState(currentPageKey);
    }
    return undefined;
  }, [currentPageKey]); // Remove store from dependencies to prevent loops

  // Clear current page state
  const clearState = useCallback(() => {
    if (currentPageKey) {
      store.clearPageState(currentPageKey);
    }
  }, [currentPageKey]); // Remove store from dependencies to prevent loops

  // Update last visited
  useEffect(() => {
    if (currentPageKey) {
      store.updateLastVisited(currentPageKey);
    }
  }, [currentPageKey]); // Remove store from dependencies to prevent loops
  
  return {
    pageKey: currentPageKey,
    saveState,
    getState,
    clearState,
    hasState: currentPageKey ? !!store.getPageState(currentPageKey) : false,
  };
}

// Hook for state cleanup and maintenance
export function usePageStateCleanup() {
  const store = usePageStateStore();
  
  const cleanupExpiredStates = useCallback(
    (maxAge?: number) => {
      store.cleanupExpiredStates(maxAge);
    },
    [] // Remove store from dependencies to prevent loops
  );

  const clearAllStates = useCallback(() => {
    store.clearAllStates();
  }, []); // Remove store from dependencies to prevent loops
  
  // Auto cleanup on app start
  useEffect(() => {
    try {
      // Clean up states older than 7 days on app initialization
      cleanupExpiredStates();
    } catch (error) {
      console.warn('Failed to cleanup expired page states:', error);
    }
  }, [cleanupExpiredStates]);
  
  return {
    cleanupExpiredStates,
    clearAllStates,
  };
}

// Hook for debugging page states
export function usePageStateDebug() {
  const store = usePageStateStore();
  
  return {
    allStates: store.pageStates,
    lastVisited: store.lastVisited,
    logStates: () => {
      console.log('Page States:', store.pageStates);
      console.log('Last Visited:', store.lastVisited);
    },
  };
}
