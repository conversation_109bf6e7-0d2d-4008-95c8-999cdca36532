"""
Enhanced base agent interface for the Datagenius backend.

This module defines an enhanced base agent interface that all agents should implement,
with improved error handling, logging, and support for composable architecture.
"""

import logging
import traceback
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple, Union

from .utils.prompt_template import PromptTemplate
from .mixins.streaming_mixin import StreamingMixin

logger = logging.getLogger(__name__)


class EnhancedBaseAgent(StreamingMixin, ABC):
    """Enhanced base class for all agents in the system."""

    def __init__(self):
        """Initialize the enhanced base agent."""
        self.config = {}
        self.prompt_templates = {}
        self.capabilities = []
        self.name = self.__class__.__name__

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the agent with configuration.

        Args:
            config: Configuration dictionary for the agent
        """
        try:
            self.config = config
            
            # Set agent name from config if available
            self.name = config.get("name", self.__class__.__name__)
            
            # Load prompt templates from configuration
            if "system_prompts" in config:
                for name, template in config["system_prompts"].items():
                    self.prompt_templates[name] = PromptTemplate(template)
                    logger.debug(f"Loaded prompt template '{name}'")

            # Load capabilities from configuration
            if "capabilities" in config:
                self.capabilities = config["capabilities"]
                logger.debug(f"Loaded capabilities: {self.capabilities}")

            # Additional initialization should be implemented by subclasses
            await self._initialize(config)
            
            logger.info(f"Successfully initialized agent: {self.name}")
        except Exception as e:
            logger.error(f"Error initializing agent {self.name}: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to initialize agent {self.name}: {str(e)}")

    @abstractmethod
    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Additional initialization for subclasses.

        Args:
            config: Configuration dictionary for the agent
        """
        pass

    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message and return a response.

        This method provides standard error handling and logging around the
        actual message processing implemented by subclasses.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """
        if context is None:
            context = {}
            
        logger.info(f"Processing message for user {user_id} in conversation {conversation_id}")
        logger.debug(f"Message: {message[:100]}{'...' if len(message) > 100 else ''}")
        
        try:
            # Call the subclass implementation
            result = await self._process_message(user_id, message, conversation_id, context)
            
            # Ensure the result has the expected structure
            if not isinstance(result, dict):
                logger.warning(f"Agent {self.name} returned non-dict result: {result}")
                result = {"message": str(result), "metadata": {}}
                
            if "message" not in result:
                logger.warning(f"Agent {self.name} returned result without 'message' field")
                result["message"] = "No response generated"
                
            if "metadata" not in result:
                result["metadata"] = {}
                
            # Add agent info to metadata
            result["metadata"]["agent_name"] = self.name
            result["metadata"]["agent_type"] = self.__class__.__name__
            
            logger.info(f"Successfully processed message for user {user_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}", exc_info=True)
            
            # Create a user-friendly error message
            error_message = f"I encountered an error while processing your request: {str(e)}"
            
            # Include stack trace in debug mode
            if logger.isEnabledFor(logging.DEBUG):
                stack_trace = traceback.format_exc()
                logger.debug(f"Stack trace: {stack_trace}")
            
            return {
                "message": error_message,
                "metadata": {
                    "error": True,
                    "error_type": e.__class__.__name__,
                    "error_message": str(e)
                }
            }

    @abstractmethod
    async def _process_message(self,
                              user_id: int,
                              message: str,
                              conversation_id: str,
                              context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a user message and return a response (implementation).

        This method should be implemented by subclasses to provide the actual
        message processing logic.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """
        pass

    async def get_capabilities(self) -> List[str]:
        """
        Return a list of capabilities this agent supports.

        Returns:
            List of capability strings
        """
        return self.capabilities

    def get_prompt(self, name: str, **kwargs) -> str:
        """
        Get a formatted prompt by name.

        Args:
            name: Name of the prompt template
            **kwargs: Variables to substitute in the template

        Returns:
            Formatted prompt
        """
        if name not in self.prompt_templates:
            logger.warning(f"Prompt template '{name}' not found")
            return f"Prompt template '{name}' not found"

        try:
            return self.prompt_templates[name].format(**kwargs)
        except KeyError as e:
            logger.error(f"Missing key in prompt template '{name}': {e}")
            return f"Error formatting prompt template '{name}': missing key {e}"
        except Exception as e:
            logger.error(f"Error formatting prompt template '{name}': {e}")
            return f"Error formatting prompt template '{name}': {e}"

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value by key.

        Args:
            key: Configuration key
            default: Default value if key is not found

        Returns:
            Configuration value
        """
        return self.config.get(key, default)
