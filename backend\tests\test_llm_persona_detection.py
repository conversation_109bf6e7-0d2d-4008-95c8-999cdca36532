#!/usr/bin/env python3
"""
Test script to verify LLM-based persona intent detection works correctly.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_llm_persona_detection():
    """Test the LLM-based persona detection system."""
    print("🧪 Testing LLM-based Persona Intent Detection")
    print("=" * 50)
    
    try:
        from backend.agents.concierge_agent.concierge import ConciergeAgent
        
        # Initialize the concierge agent
        concierge = ConciergeAgent()
        await concierge._initialize({})
        
        # Test cases for different types of persona requests
        test_cases = [
            {
                "message": "what personas are available",
                "expected_intent": "persona_query",
                "description": "General persona listing query"
            },
            {
                "message": "show me all available agents",
                "expected_intent": "persona_query", 
                "description": "Agent listing request"
            },
            {
                "message": "what's the best persona for marketing",
                "expected_intent": "persona_request",
                "description": "Specific marketing recommendation"
            },
            {
                "message": "recommend a persona for data analysis",
                "expected_intent": "persona_request",
                "description": "Data analysis recommendation"
            },
            {
                "message": "which agent should I use for classification tasks",
                "expected_intent": "persona_request",
                "description": "Classification task recommendation"
            },
            {
                "message": "hello there",
                "expected_intent": "general_question",
                "description": "General greeting"
            },
            {
                "message": "how do I upload a CSV file",
                "expected_intent": "data_help",
                "description": "Data help request"
            }
        ]
        
        print(f"Testing {len(test_cases)} different message types...\n")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"Test {i}: {test_case['description']}")
            print(f"Message: '{test_case['message']}'")
            
            try:
                # Parse intent using LLM
                user_context = {
                    "is_continuing_conversation": False,
                    "conversation_state": None
                }
                
                intent = await concierge.parse_user_intent(test_case['message'], user_context)
                
                print(f"Detected Intent: {intent.intent_type}")
                print(f"Confidence: {intent.confidence:.2f}")
                print(f"Expected: {test_case['expected_intent']}")
                
                # Check if intent matches expectation
                if intent.intent_type == test_case['expected_intent']:
                    print("✅ PASS - Intent correctly detected")
                else:
                    print("❌ FAIL - Intent mismatch")
                
                print("-" * 40)
                
            except Exception as e:
                print(f"❌ ERROR: {e}")
                print("-" * 40)
        
        print("\n🎯 Testing complete!")
        print("\nKey improvements with LLM-based detection:")
        print("• No hardcoded keywords - more flexible and robust")
        print("• Better understanding of user intent and context")
        print("• Distinguishes between browsing (persona_query) and recommendations (persona_request)")
        print("• Can handle variations in language and phrasing")
        print("• Learns from conversation context")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_llm_persona_detection())
