import { Button } from "@/components/ui/button";
import { FcGoogle } from "react-icons/fc";

interface GoogleLoginButtonProps {
  onClick: () => void;
  isLoading?: boolean;
}

export const GoogleLoginButton = ({ onClick, isLoading = false }: GoogleLoginButtonProps) => {
  return (
    <Button
      variant="outline"
      type="button"
      className="w-full flex items-center justify-center gap-2"
      onClick={onClick}
      disabled={isLoading}
    >
      <FcGoogle className="h-5 w-5" />
      {isLoading ? "Connecting..." : "Continue with Google"}
    </Button>
  );
};
