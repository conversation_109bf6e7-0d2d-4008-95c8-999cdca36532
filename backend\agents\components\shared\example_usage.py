"""
Example usage of Phase 2 Shared Components.

This module demonstrates how to use the new shared components in agent implementations,
showing the standardized patterns for LLM processing, data retrieval, and context management.
"""

import logging
from typing import Dict, Any
from ..base_component import BaseAgentComponent, AgentContext
from .shared_llm_processor import SharedLLMProcessor
from .shared_data_retriever import SharedDataRetriever
from .shared_context_manager import SharedContextManager

logger = logging.getLogger(__name__)


class ExampleAgentComponent(BaseAgentComponent):
    """
    Example agent component demonstrating Phase 2 shared component usage.
    
    This shows how to compose an agent using the standardized shared components
    instead of implementing duplicate logic.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("example_agent", config)
        
        # Initialize shared components
        self.llm_processor = SharedLLMProcessor(config.get("llm_config", {}))
        self.data_retriever = SharedDataRetriever(config.get("data_config", {}))
        self.context_manager = SharedContextManager(config.get("context_config", {}))
    
    async def _initialize_component(self) -> None:
        """Initialize all shared components."""
        await self.llm_processor.initialize()
        await self.data_retriever.initialize()
        await self.context_manager.initialize()
        self.logger.info("Example agent component initialized with shared components")
    
    def get_required_fields(self) -> list[str]:
        """Return required fields for this component."""
        return ["user_message", "user_id"]
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process request using shared components.
        
        This demonstrates the standardized flow:
        1. Manage context and conversation history
        2. Retrieve relevant data if needed
        3. Process with LLM
        4. Update context with results
        """
        user_message = context.get_field("user_message")
        
        # Step 1: Get conversation context
        context.set_field("operation", "get_context")
        context.set_field("conversation_id", f"example_{context.user_id}")
        context.set_field("persona_id", "example_agent")
        
        context = await self.context_manager.execute_with_monitoring(context)
        
        # Step 2: Retrieve relevant data if query requires it
        if self._requires_data_retrieval(user_message):
            context.set_field("query", user_message)
            context.set_field("search_types", ["vector", "memory"])
            
            context = await self.data_retriever.execute_with_monitoring(context)
            
            # Use retrieved data in prompt
            retrieved_data = context.get_field("retrieved_data", [])
            if retrieved_data:
                data_context = "\n".join([item.get("content", "") for item in retrieved_data[:3]])
                context.set_field("data_context", data_context)
        
        # Step 3: Generate LLM response
        prompt = self._build_prompt(context)
        context.set_field("prompt", prompt)
        context.set_field("agent_identity", {
            "name": "Example Agent",
            "role": "helpful assistant demonstrating Phase 2 architecture"
        })
        
        context = await self.llm_processor.execute_with_monitoring(context)
        
        # Step 4: Update conversation context
        if context.get_field("llm_response"):
            context.set_field("operation", "update_context")
            context.set_field("new_message", user_message)
            context.set_field("message_role", "user")
            
            await self.context_manager.execute_with_monitoring(context)
            
            # Add assistant response to context
            context.set_field("new_message", context.get_field("llm_response"))
            context.set_field("message_role", "assistant")
            
            await self.context_manager.execute_with_monitoring(context)
        
        return context
    
    def _requires_data_retrieval(self, message: str) -> bool:
        """
        Determine if message requires data retrieval.
        
        Args:
            message: User message
            
        Returns:
            True if data retrieval is needed
        """
        data_keywords = [
            "analyze", "data", "chart", "graph", "report", "statistics",
            "find", "search", "lookup", "information", "document"
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in data_keywords)
    
    def _build_prompt(self, context: AgentContext) -> str:
        """
        Build prompt for LLM processing.
        
        Args:
            context: Current agent context
            
        Returns:
            Formatted prompt string
        """
        user_message = context.get_field("user_message")
        conversation_history = context.get_field("conversation_history", [])
        data_context = context.get_field("data_context", "")
        
        prompt_parts = []
        
        # Add system context
        prompt_parts.append("You are a helpful assistant demonstrating Phase 2 architecture.")
        
        # Add conversation history if available
        if conversation_history:
            prompt_parts.append("\nConversation History:")
            for msg in conversation_history[-3:]:  # Last 3 messages
                role = msg.get("role", "user")
                content = msg.get("content", "")
                prompt_parts.append(f"{role.title()}: {content}")
        
        # Add data context if available
        if data_context:
            prompt_parts.append(f"\nRelevant Data:\n{data_context}")
        
        # Add current user message
        prompt_parts.append(f"\nUser: {user_message}")
        prompt_parts.append("\nAssistant:")
        
        return "\n".join(prompt_parts)


async def example_usage():
    """
    Example function showing how to use the shared components.
    """
    # Configuration for shared components
    config = {
        "llm_config": {
            "fallback_providers": ["groq", "openai"],
            "default_temperature": 0.7
        },
        "data_config": {
            "max_results": 5,
            "enable_vector_search": True,
            "enable_memory_search": True
        },
        "context_config": {
            "max_history_length": 20,
            "enable_cross_persona_sharing": True
        }
    }
    
    # Create example agent component
    agent = ExampleAgentComponent(config)
    
    # Create context for processing
    context = AgentContext(
        user_id="example_user_123",
        message="Hello, can you help me analyze my sales data?"
    )
    context.set_field("user_message", "Hello, can you help me analyze my sales data?")
    
    # Process the request
    try:
        result_context = await agent.execute_with_monitoring(context)
        
        if result_context.status == "success":
            response = result_context.get_field("llm_response")
            print(f"Agent Response: {response}")
            
            # Get component metrics
            llm_metrics = agent.llm_processor.get_metrics()
            data_metrics = agent.data_retriever.get_metrics()
            context_metrics = agent.context_manager.get_metrics()
            
            print(f"LLM Processor Success Rate: {llm_metrics.get_success_rate():.2%}")
            print(f"Data Retriever Success Rate: {data_metrics.get_success_rate():.2%}")
            print(f"Context Manager Success Rate: {context_metrics.get_success_rate():.2%}")
        else:
            print(f"Processing failed: {result_context.errors}")
            
    except Exception as e:
        logger.error(f"Example usage failed: {e}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())
