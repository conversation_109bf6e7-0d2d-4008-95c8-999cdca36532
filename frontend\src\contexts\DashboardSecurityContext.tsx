import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useSecurityValidation, SecurityConfig } from '@/hooks/use-security-validation';
import { useToast } from '@/hooks/use-toast';

interface SecurityPermissions {
  canCreateDashboard: boolean;
  canEditDashboard: boolean;
  canDeleteDashboard: boolean;
  canCreateWidget: boolean;
  canEditWidget: boolean;
  canDeleteWidget: boolean;
  canAccessDataSources: boolean;
  canManagePermissions: boolean;
  canViewAuditLogs: boolean;
}

interface SecurityPolicy {
  enforceInputValidation: boolean;
  requireCSRFTokens: boolean;
  enableAuditLogging: boolean;
  maxDashboardsPerUser: number;
  maxWidgetsPerDashboard: number;
  sessionTimeoutMinutes: number;
  requireReauthForSensitiveOps: boolean;
}

interface SecurityAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
}

interface DashboardSecurityContextType {
  // Permissions
  permissions: SecurityPermissions;
  updatePermissions: (newPermissions: Partial<SecurityPermissions>) => void;
  
  // Security policy
  policy: SecurityPolicy;
  updatePolicy: (newPolicy: Partial<SecurityPolicy>) => void;
  
  // Input validation
  validateInput: (input: string, showToasts?: boolean) => any;
  validateForm: (formData: Record<string, any>) => any;
  sanitizeHTML: (input: string) => string;
  
  // CSRF protection
  csrfToken: string;
  refreshCSRFToken: () => void;
  
  // Security alerts
  alerts: SecurityAlert[];
  addAlert: (alert: Omit<SecurityAlert, 'id' | 'timestamp' | 'acknowledged'>) => void;
  acknowledgeAlert: (alertId: string) => void;
  clearAlerts: () => void;
  
  // Audit logging
  logSecurityEvent: (event: string, details?: any) => void;
  
  // Session management
  sessionExpiry: Date | null;
  extendSession: () => void;
  isSessionValid: boolean;
  
  // Security status
  securityScore: number;
  getSecurityReport: () => any;
}

const DashboardSecurityContext = createContext<DashboardSecurityContextType | undefined>(undefined);

interface DashboardSecurityProviderProps {
  children: ReactNode;
  initialPermissions?: Partial<SecurityPermissions>;
  initialPolicy?: Partial<SecurityPolicy>;
  securityConfig?: Partial<SecurityConfig>;
}

const DEFAULT_PERMISSIONS: SecurityPermissions = {
  canCreateDashboard: true,
  canEditDashboard: true,
  canDeleteDashboard: true,
  canCreateWidget: true,
  canEditWidget: true,
  canDeleteWidget: true,
  canAccessDataSources: true,
  canManagePermissions: false,
  canViewAuditLogs: false,
};

const DEFAULT_POLICY: SecurityPolicy = {
  enforceInputValidation: true,
  requireCSRFTokens: true,
  enableAuditLogging: true,
  maxDashboardsPerUser: 50,
  maxWidgetsPerDashboard: 50,
  sessionTimeoutMinutes: 480, // 8 hours
  requireReauthForSensitiveOps: true,
};

export const DashboardSecurityProvider: React.FC<DashboardSecurityProviderProps> = ({
  children,
  initialPermissions = {},
  initialPolicy = {},
  securityConfig = {},
}) => {
  const { toast } = useToast();
  const [permissions, setPermissions] = useState<SecurityPermissions>({
    ...DEFAULT_PERMISSIONS,
    ...initialPermissions,
  });
  
  const [policy, setPolicy] = useState<SecurityPolicy>({
    ...DEFAULT_POLICY,
    ...initialPolicy,
  });
  
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);
  const [auditLog, setAuditLog] = useState<Array<{
    timestamp: Date;
    event: string;
    details: any;
  }>>([]);

  const {
    validateInput,
    validateForm,
    sanitizeHTML,
    generateCSRFToken,
    getSecurityReport,
  } = useSecurityValidation(securityConfig);

  const [csrfToken, setCSRFToken] = useState<string>(() => generateCSRFToken());

  // Initialize session expiry
  useEffect(() => {
    const expiry = new Date();
    expiry.setMinutes(expiry.getMinutes() + policy.sessionTimeoutMinutes);
    setSessionExpiry(expiry);
  }, [policy.sessionTimeoutMinutes]);

  // Check session validity
  const isSessionValid = sessionExpiry ? new Date() < sessionExpiry : false;

  // Session timeout warning
  useEffect(() => {
    if (!sessionExpiry) return;

    const warningTime = new Date(sessionExpiry.getTime() - 5 * 60 * 1000); // 5 minutes before expiry
    const now = new Date();

    if (now < warningTime) {
      const timeoutId = setTimeout(() => {
        addAlert({
          type: 'warning',
          title: 'Session Expiring Soon',
          message: 'Your session will expire in 5 minutes. Please save your work.',
        });
      }, warningTime.getTime() - now.getTime());

      return () => clearTimeout(timeoutId);
    }
  }, [sessionExpiry]);

  // Auto-logout on session expiry
  useEffect(() => {
    if (sessionExpiry && !isSessionValid) {
      addAlert({
        type: 'error',
        title: 'Session Expired',
        message: 'Your session has expired. Please log in again.',
      });
      
      // In a real app, this would trigger logout
      console.warn('Session expired - user should be logged out');
    }
  }, [isSessionValid, sessionExpiry]);

  const updatePermissions = (newPermissions: Partial<SecurityPermissions>) => {
    setPermissions(prev => ({ ...prev, ...newPermissions }));
    logSecurityEvent('permissions_updated', newPermissions);
  };

  const updatePolicy = (newPolicy: Partial<SecurityPolicy>) => {
    setPolicy(prev => ({ ...prev, ...newPolicy }));
    logSecurityEvent('security_policy_updated', newPolicy);
  };

  const refreshCSRFToken = () => {
    const newToken = generateCSRFToken();
    setCSRFToken(newToken);
    logSecurityEvent('csrf_token_refreshed');
  };

  const addAlert = (alert: Omit<SecurityAlert, 'id' | 'timestamp' | 'acknowledged'>) => {
    const newAlert: SecurityAlert = {
      ...alert,
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      acknowledged: false,
    };
    
    setAlerts(prev => [...prev.slice(-19), newAlert]); // Keep last 20 alerts
    
    // Show toast for high-priority alerts
    if (alert.type === 'error') {
      toast({
        title: alert.title,
        description: alert.message,
        variant: 'destructive',
      });
    } else if (alert.type === 'warning') {
      toast({
        title: alert.title,
        description: alert.message,
      });
    }
  };

  const acknowledgeAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  };

  const clearAlerts = () => {
    setAlerts([]);
  };

  const logSecurityEvent = (event: string, details?: any) => {
    if (!policy.enableAuditLogging) return;

    const logEntry = {
      timestamp: new Date(),
      event,
      details: details || {},
    };
    
    setAuditLog(prev => [...prev.slice(-999), logEntry]); // Keep last 1000 entries
    
    // In production, this would be sent to a security logging service
    console.log('Security Event:', logEntry);
  };

  const extendSession = () => {
    const newExpiry = new Date();
    newExpiry.setMinutes(newExpiry.getMinutes() + policy.sessionTimeoutMinutes);
    setSessionExpiry(newExpiry);
    logSecurityEvent('session_extended');
  };

  // Enhanced validation functions that respect policy
  const enhancedValidateInput = (input: string, showToasts: boolean = true) => {
    if (!policy.enforceInputValidation) {
      return { isValid: true, sanitizedValue: input, threats: [], riskLevel: 'low' };
    }
    
    const result = validateInput(input, showToasts);
    
    if (!result.isValid) {
      logSecurityEvent('input_validation_failed', {
        threats: result.threats,
        riskLevel: result.riskLevel,
      });
    }
    
    return result;
  };

  const enhancedValidateForm = (formData: Record<string, any>) => {
    if (!policy.enforceInputValidation) {
      return { isValid: true, sanitizedData: formData, threats: {} };
    }
    
    const result = validateForm(formData);
    
    if (!result.isValid) {
      logSecurityEvent('form_validation_failed', {
        threats: result.threats,
      });
    }
    
    return result;
  };

  // Security score calculation
  const securityScore = (() => {
    let score = 100;
    
    // Deduct points for disabled security features
    if (!policy.enforceInputValidation) score -= 20;
    if (!policy.requireCSRFTokens) score -= 15;
    if (!policy.enableAuditLogging) score -= 10;
    if (!policy.requireReauthForSensitiveOps) score -= 15;
    
    // Deduct points for security alerts
    const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged);
    score -= unacknowledgedAlerts.length * 5;
    
    // Deduct points for session issues
    if (!isSessionValid) score -= 30;
    
    return Math.max(score, 0);
  })();

  const contextValue: DashboardSecurityContextType = {
    permissions,
    updatePermissions,
    policy,
    updatePolicy,
    validateInput: enhancedValidateInput,
    validateForm: enhancedValidateForm,
    sanitizeHTML,
    csrfToken,
    refreshCSRFToken,
    alerts,
    addAlert,
    acknowledgeAlert,
    clearAlerts,
    logSecurityEvent,
    sessionExpiry,
    extendSession,
    isSessionValid,
    securityScore,
    getSecurityReport,
  };

  return (
    <DashboardSecurityContext.Provider value={contextValue}>
      {children}
    </DashboardSecurityContext.Provider>
  );
};

export const useDashboardSecurity = (): DashboardSecurityContextType => {
  const context = useContext(DashboardSecurityContext);
  if (context === undefined) {
    throw new Error('useDashboardSecurity must be used within a DashboardSecurityProvider');
  }
  return context;
};

// Higher-order component for security-aware components
export const withDashboardSecurity = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const WrappedComponent = (props: P) => (
    <DashboardSecurityProvider>
      <Component {...props} />
    </DashboardSecurityProvider>
  );

  WrappedComponent.displayName = `withDashboardSecurity(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};
