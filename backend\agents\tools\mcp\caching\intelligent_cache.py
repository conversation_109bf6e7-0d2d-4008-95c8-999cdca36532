"""
Intelligent Caching System for MCP Tools.

This module provides a sophisticated caching system with multiple cache layers,
intelligent invalidation, and performance optimization for all MCP tools.
"""

import asyncio
import hashlib
import json
import logging
import time
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import pickle
import redis
from functools import wraps

logger = logging.getLogger(__name__)


class CacheLevel(Enum):
    """Cache levels for different types of data."""
    MEMORY = "memory"
    REDIS = "redis"
    PERSISTENT = "persistent"


class CacheStrategy(Enum):
    """Cache strategies for different use cases."""
    LRU = "lru"
    LFU = "lfu"
    TTL = "ttl"
    ADAPTIVE = "adaptive"


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl: Optional[int] = None
    size_bytes: int = 0
    agent_identity: Optional[str] = None
    tool_name: Optional[str] = None
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        if self.ttl is None:
            return False
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    def update_access(self):
        """Update access metadata."""
        self.last_accessed = datetime.now()
        self.access_count += 1


class IntelligentCache:
    """
    Intelligent multi-level caching system for MCP tools.
    
    Features:
    - Multi-level caching (memory, Redis, persistent)
    - Intelligent cache strategies (LRU, LFU, TTL, adaptive)
    - Agent-aware caching with personalized cache keys
    - Performance monitoring and optimization
    - Automatic cache warming and preloading
    - Cache analytics and insights
    """
    
    def __init__(
        self,
        memory_size_mb: int = 100,
        redis_url: Optional[str] = None,
        default_ttl: int = 3600,
        strategy: CacheStrategy = CacheStrategy.ADAPTIVE
    ):
        """
        Initialize the intelligent cache system.
        
        Args:
            memory_size_mb: Maximum memory cache size in MB
            redis_url: Redis connection URL for distributed caching
            default_ttl: Default TTL in seconds
            strategy: Default cache strategy
        """
        self.memory_size_mb = memory_size_mb
        self.memory_size_bytes = memory_size_mb * 1024 * 1024
        self.default_ttl = default_ttl
        self.strategy = strategy
        
        # Memory cache
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.memory_usage = 0
        
        # Redis cache
        self.redis_client = None
        if redis_url:
            try:
                self.redis_client = redis.from_url(redis_url)
                self.redis_client.ping()
                logger.info("Redis cache initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Redis cache: {e}")
        
        # Cache statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "memory_usage": 0,
            "redis_usage": 0
        }
        
        # Cache warming tasks
        self.warming_tasks: Dict[str, asyncio.Task] = {}
        
        logger.info(f"Intelligent cache initialized with {memory_size_mb}MB memory")
    
    def _generate_cache_key(
        self,
        tool_name: str,
        operation: str,
        arguments: Dict[str, Any],
        agent_identity: Optional[str] = None
    ) -> str:
        """
        Generate intelligent cache key with agent awareness.
        
        Args:
            tool_name: Name of the tool
            operation: Operation being cached
            arguments: Tool arguments
            agent_identity: Agent identity for personalized caching
            
        Returns:
            Generated cache key
        """
        # Create base key components
        key_components = {
            "tool": tool_name,
            "operation": operation,
            "args": self._normalize_arguments(arguments)
        }
        
        # Add agent identity for personalized caching
        if agent_identity:
            key_components["agent"] = agent_identity
        
        # Create deterministic hash
        key_string = json.dumps(key_components, sort_keys=True)
        key_hash = hashlib.sha256(key_string.encode()).hexdigest()[:16]
        
        return f"{tool_name}:{operation}:{key_hash}"
    
    def _normalize_arguments(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize arguments for consistent cache keys."""
        normalized = {}
        
        for key, value in arguments.items():
            # Skip context and metadata that shouldn't affect caching
            if key in ["context", "user_context", "request_id", "timestamp"]:
                continue
            
            # Normalize different types
            if isinstance(value, (list, tuple)):
                normalized[key] = tuple(sorted(value) if all(isinstance(x, (str, int, float)) for x in value) else value)
            elif isinstance(value, dict):
                normalized[key] = self._normalize_arguments(value)
            else:
                normalized[key] = value
        
        return normalized
    
    async def get(
        self,
        tool_name: str,
        operation: str,
        arguments: Dict[str, Any],
        agent_identity: Optional[str] = None
    ) -> Optional[Any]:
        """
        Get value from cache with intelligent lookup.
        
        Args:
            tool_name: Name of the tool
            operation: Operation being cached
            arguments: Tool arguments
            agent_identity: Agent identity
            
        Returns:
            Cached value or None if not found
        """
        cache_key = self._generate_cache_key(tool_name, operation, arguments, agent_identity)
        
        # Try memory cache first
        if cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            
            if entry.is_expired():
                await self._evict_entry(cache_key, CacheLevel.MEMORY)
            else:
                entry.update_access()
                self.stats["hits"] += 1
                logger.debug(f"Cache hit (memory): {cache_key}")
                return entry.value
        
        # Try Redis cache
        if self.redis_client:
            try:
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    entry_data = pickle.loads(cached_data)
                    entry = CacheEntry(**entry_data)
                    
                    if entry.is_expired():
                        self.redis_client.delete(cache_key)
                    else:
                        # Promote to memory cache if frequently accessed
                        if entry.access_count > 5:
                            await self._set_memory_cache(cache_key, entry)
                        
                        entry.update_access()
                        self.stats["hits"] += 1
                        logger.debug(f"Cache hit (Redis): {cache_key}")
                        return entry.value
            except Exception as e:
                logger.warning(f"Redis cache get error: {e}")
        
        self.stats["misses"] += 1
        logger.debug(f"Cache miss: {cache_key}")
        return None
    
    async def set(
        self,
        tool_name: str,
        operation: str,
        arguments: Dict[str, Any],
        value: Any,
        ttl: Optional[int] = None,
        agent_identity: Optional[str] = None
    ) -> bool:
        """
        Set value in cache with intelligent storage.
        
        Args:
            tool_name: Name of the tool
            operation: Operation being cached
            arguments: Tool arguments
            value: Value to cache
            ttl: Time to live in seconds
            agent_identity: Agent identity
            
        Returns:
            True if successfully cached
        """
        cache_key = self._generate_cache_key(tool_name, operation, arguments, agent_identity)
        
        # Calculate value size
        try:
            value_size = len(pickle.dumps(value))
        except Exception:
            value_size = len(str(value).encode())
        
        # Create cache entry
        entry = CacheEntry(
            key=cache_key,
            value=value,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            ttl=ttl or self.default_ttl,
            size_bytes=value_size,
            agent_identity=agent_identity,
            tool_name=tool_name
        )
        
        # Determine cache level based on size and strategy
        cache_level = self._determine_cache_level(entry)
        
        success = False
        if cache_level == CacheLevel.MEMORY:
            success = await self._set_memory_cache(cache_key, entry)
        elif cache_level == CacheLevel.REDIS and self.redis_client:
            success = await self._set_redis_cache(cache_key, entry)
        
        if success:
            logger.debug(f"Cache set ({cache_level.value}): {cache_key}")
        
        return success
    
    def _determine_cache_level(self, entry: CacheEntry) -> CacheLevel:
        """Determine optimal cache level for entry."""
        # Large entries go to Redis
        if entry.size_bytes > 1024 * 1024:  # 1MB
            return CacheLevel.REDIS
        
        # Frequently accessed small entries go to memory
        if entry.size_bytes < 100 * 1024:  # 100KB
            return CacheLevel.MEMORY
        
        # Medium entries go to Redis by default
        return CacheLevel.REDIS
    
    async def _set_memory_cache(self, cache_key: str, entry: CacheEntry) -> bool:
        """Set entry in memory cache with eviction if needed."""
        # Check if we need to evict entries
        while (self.memory_usage + entry.size_bytes > self.memory_size_bytes and 
               len(self.memory_cache) > 0):
            await self._evict_memory_entry()
        
        # Add entry if there's space
        if self.memory_usage + entry.size_bytes <= self.memory_size_bytes:
            self.memory_cache[cache_key] = entry
            self.memory_usage += entry.size_bytes
            self.stats["memory_usage"] = self.memory_usage
            return True
        
        return False
    
    async def _set_redis_cache(self, cache_key: str, entry: CacheEntry) -> bool:
        """Set entry in Redis cache."""
        try:
            entry_data = asdict(entry)
            # Convert datetime objects to strings for serialization
            entry_data["created_at"] = entry.created_at.isoformat()
            entry_data["last_accessed"] = entry.last_accessed.isoformat()
            
            serialized_data = pickle.dumps(entry_data)
            
            if entry.ttl:
                self.redis_client.setex(cache_key, entry.ttl, serialized_data)
            else:
                self.redis_client.set(cache_key, serialized_data)
            
            return True
        except Exception as e:
            logger.warning(f"Redis cache set error: {e}")
            return False
    
    async def _evict_memory_entry(self):
        """Evict entry from memory cache based on strategy."""
        if not self.memory_cache:
            return
        
        if self.strategy == CacheStrategy.LRU:
            # Evict least recently used
            oldest_key = min(self.memory_cache.keys(), 
                           key=lambda k: self.memory_cache[k].last_accessed)
        elif self.strategy == CacheStrategy.LFU:
            # Evict least frequently used
            oldest_key = min(self.memory_cache.keys(),
                           key=lambda k: self.memory_cache[k].access_count)
        else:
            # Default to LRU
            oldest_key = min(self.memory_cache.keys(),
                           key=lambda k: self.memory_cache[k].last_accessed)
        
        await self._evict_entry(oldest_key, CacheLevel.MEMORY)
    
    async def _evict_entry(self, cache_key: str, level: CacheLevel):
        """Evict entry from specified cache level."""
        if level == CacheLevel.MEMORY and cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            self.memory_usage -= entry.size_bytes
            del self.memory_cache[cache_key]
            self.stats["evictions"] += 1
            self.stats["memory_usage"] = self.memory_usage
        elif level == CacheLevel.REDIS and self.redis_client:
            try:
                self.redis_client.delete(cache_key)
            except Exception as e:
                logger.warning(f"Redis eviction error: {e}")
    
    async def invalidate_pattern(self, pattern: str):
        """Invalidate cache entries matching pattern."""
        # Invalidate memory cache
        keys_to_remove = [key for key in self.memory_cache.keys() if pattern in key]
        for key in keys_to_remove:
            await self._evict_entry(key, CacheLevel.MEMORY)
        
        # Invalidate Redis cache
        if self.redis_client:
            try:
                keys = self.redis_client.keys(f"*{pattern}*")
                if keys:
                    self.redis_client.delete(*keys)
            except Exception as e:
                logger.warning(f"Redis pattern invalidation error: {e}")
    
    async def warm_cache(
        self,
        tool_name: str,
        warming_function: Callable,
        warming_data: List[Dict[str, Any]]
    ):
        """Warm cache with precomputed results."""
        task_key = f"{tool_name}_warming"
        
        if task_key in self.warming_tasks:
            return
        
        async def warming_task():
            try:
                for data in warming_data:
                    result = await warming_function(**data)
                    await self.set(
                        tool_name=tool_name,
                        operation="warm",
                        arguments=data,
                        value=result,
                        ttl=self.default_ttl * 2  # Longer TTL for warmed data
                    )
                    await asyncio.sleep(0.1)  # Prevent overwhelming
                
                logger.info(f"Cache warming completed for {tool_name}")
            except Exception as e:
                logger.error(f"Cache warming failed for {tool_name}: {e}")
            finally:
                if task_key in self.warming_tasks:
                    del self.warming_tasks[task_key]
        
        self.warming_tasks[task_key] = asyncio.create_task(warming_task())
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        hit_rate = (self.stats["hits"] / (self.stats["hits"] + self.stats["misses"]) 
                   if (self.stats["hits"] + self.stats["misses"]) > 0 else 0)
        
        return {
            **self.stats,
            "hit_rate": hit_rate,
            "memory_entries": len(self.memory_cache),
            "memory_usage_mb": self.memory_usage / (1024 * 1024),
            "memory_utilization": self.memory_usage / self.memory_size_bytes,
            "redis_connected": self.redis_client is not None
        }
    
    async def cleanup(self):
        """Cleanup cache resources."""
        # Cancel warming tasks
        for task in self.warming_tasks.values():
            task.cancel()
        
        # Clear memory cache
        self.memory_cache.clear()
        self.memory_usage = 0
        
        # Close Redis connection
        if self.redis_client:
            self.redis_client.close()
        
        logger.info("Cache cleanup completed")


# Global cache instance
_global_cache: Optional[IntelligentCache] = None


def get_cache() -> IntelligentCache:
    """Get global cache instance."""
    global _global_cache
    if _global_cache is None:
        _global_cache = IntelligentCache()
    return _global_cache


def cache_result(
    operation: str,
    ttl: Optional[int] = None,
    agent_aware: bool = True
):
    """
    Decorator for caching function results.
    
    Args:
        operation: Operation name for cache key
        ttl: Time to live in seconds
        agent_aware: Whether to include agent identity in cache key
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache = get_cache()
            
            # Extract tool name and arguments
            if args and hasattr(args[0], 'name'):
                tool_name = args[0].name
            else:
                tool_name = func.__name__
            
            # Extract agent identity if agent_aware
            agent_identity = None
            if agent_aware and len(args) > 1 and isinstance(args[1], dict):
                arguments = args[1]
                agent_identity = (arguments.get("agent_identity") or 
                                arguments.get("persona_id") or 
                                arguments.get("agent_id"))
            
            # Try to get from cache
            cached_result = await cache.get(
                tool_name=tool_name,
                operation=operation,
                arguments=kwargs if kwargs else (args[1] if len(args) > 1 else {}),
                agent_identity=agent_identity
            )
            
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            
            await cache.set(
                tool_name=tool_name,
                operation=operation,
                arguments=kwargs if kwargs else (args[1] if len(args) > 1 else {}),
                value=result,
                ttl=ttl,
                agent_identity=agent_identity
            )
            
            return result
        
        return wrapper
    return decorator
