import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DynamicWidget } from './DynamicWidget';
import { WidgetResponse, LayoutConfig, SectionResponse } from '@/types/dashboard-customization';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { useMediaQuery } from '@/hooks/use-media-query';
import { 
  Database, 
  BarChart3, 
  Settings, 
  Loader2, 
  Grid,
  Maximize2,
  Minimize2,
  MoreVertical
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { EnhancedEmptyState } from './EnhancedEmptyState';
import { DashboardGridSkeleton } from './SkeletonLoaders';
import { MobileWidgetWrapper, useMobileOptimization } from './mode/MobileDashboardOptimization';
import { useDashboardMode } from '@/stores/dashboard-mode-store';

interface ResponsiveDashboardGridProps {
  widgets?: WidgetResponse[];
  layoutConfig?: LayoutConfig;
  onWidgetUpdate?: (id: string, updates: Partial<WidgetResponse>) => void;
  onWidgetRemove?: (id: string) => void;
  onWidgetPositionUpdate?: (id: string, position: any) => void;
  onWidgetCustomize?: (widget: WidgetResponse) => void;
  onWidgetInsights?: (widget: WidgetResponse) => void;
  onWidgetMove?: (widgetId: string, targetSectionId: string) => void;
  onAddWidget?: () => void;
  availableSections?: SectionResponse[];
  dataSourceAssignments?: any[];
  isLoading?: boolean;
  className?: string;
}

export const ResponsiveDashboardGrid: React.FC<ResponsiveDashboardGridProps> = ({
  widgets = [],
  layoutConfig,
  onWidgetUpdate,
  onWidgetRemove,
  onWidgetPositionUpdate,
  onWidgetCustomize,
  onWidgetInsights,
  onWidgetMove,
  onAddWidget,
  availableSections,
  dataSourceAssignments = [],
  isLoading = false,
  className
}) => {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const isTablet = useMediaQuery('(max-width: 1024px)');
  const [expandedWidget, setExpandedWidget] = useState<string | null>(null);
  const [touchStartY, setTouchStartY] = useState<number | null>(null);

  // Mobile optimization and mode awareness
  const { is_mobile, is_touch_device } = useMobileOptimization();
  const { current_mode } = useDashboardMode();

  const hasDataSources = dataSourceAssignments && dataSourceAssignments.length > 0;
  const hasWidgets = widgets && widgets.length > 0;

  // Enhanced responsive grid configuration
  const getGridConfig = () => {
    if (isMobile) {
      return {
        columns: 1,
        gap: 'gap-3',
        className: 'grid-cols-1',
        containerClass: '',
        widgetClass: 'min-h-[200px]'
      };
    } else if (isTablet) {
      return {
        columns: 2,
        gap: 'gap-4',
        className: 'grid-cols-1 sm:grid-cols-2',
        containerClass: '',
        widgetClass: 'min-h-[250px]'
      };
    } else {
      return {
        columns: layoutConfig?.columns || 12,
        gap: `gap-${Math.floor((layoutConfig?.grid_gap || 16) / 4)}`,
        className: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6',
        containerClass: '',
        widgetClass: 'min-h-[300px]'
      };
    }
  };

  const gridConfig = getGridConfig();

  // Enhanced touch handlers for mobile interactions
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartY(e.touches[0].clientY);
    // Prevent default to avoid scrolling conflicts
    if (isMobile) {
      e.preventDefault();
    }
  };

  const handleTouchEnd = (e: React.TouchEvent, widgetId: string) => {
    if (touchStartY === null) return;
    
    const touchEndY = e.changedTouches[0].clientY;
    const deltaY = touchStartY - touchEndY;
    
    // Swipe up to expand, swipe down to minimize
    if (Math.abs(deltaY) > 50) {
      if (deltaY > 0 && expandedWidget !== widgetId) {
        setExpandedWidget(widgetId);
      } else if (deltaY < 0 && expandedWidget === widgetId) {
        setExpandedWidget(null);
      }
    }
    
    setTouchStartY(null);
  };

  const handleWidgetAction = (action: string, widget: WidgetResponse) => {
    switch (action) {
      case 'customize':
        onWidgetCustomize?.(widget);
        break;
      case 'insights':
        onWidgetInsights?.(widget);
        break;
      case 'remove':
        onWidgetRemove?.(widget.id);
        toast({
          title: "Widget Removed",
          description: `${widget.title} has been removed from the dashboard.`,
        });
        break;
      case 'expand':
        setExpandedWidget(expandedWidget === widget.id ? null : widget.id);
        break;
      default:
        break;
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={cn("dashboard-content", className)}>
        <DashboardGridSkeleton widgetCount={6} />
      </div>
    );
  }

  // Empty state - no data sources
  if (!hasDataSources) {
    return (
      <div className={cn("dashboard-content", className)}>
        <EnhancedEmptyState
          type="data"
          actions={[
            {
              label: "Upload Data",
              description: "Upload CSV, Excel files or connect to APIs",
              icon: Database,
              onClick: () => {
                toast({
                  title: "Data Upload",
                  description: "Navigate to Data Sources to upload your files.",
                });
              },
              primary: true,
              badge: "Start Here"
            }
          ]}
        />
      </div>
    );
  }

  // Empty state - no widgets
  if (!hasWidgets) {
    return (
      <div className={cn("dashboard-content", className)}>
        <EnhancedEmptyState
          type="widgets"
          actions={[
            {
              label: "Add Your First Widget",
              description: "Create charts, tables, and KPIs from your data",
              icon: BarChart3,
              onClick: onAddWidget || (() => {}),
              primary: true,
              badge: "Get Started"
            }
          ]}
        />
      </div>
    );
  }

  return (
    <div className={cn("dashboard-content", gridConfig.containerClass, className)}>
      {/* Enhanced Widget Grid */}
      <div
        className={cn(
          "grid",
          gridConfig.gap,
          gridConfig.className
        )}
        style={{
          gridAutoRows: `minmax(${isMobile ? '200px' : isTablet ? '250px' : '300px'}, auto)`,
        }}
      >
        <AnimatePresence mode="popLayout">
          {widgets.map((widget, index) => {
            const isExpanded = expandedWidget === widget.id;
            
            return (
              <motion.div
                key={widget.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ 
                  opacity: 1, 
                  scale: 1,
                  zIndex: isExpanded ? 50 : 1
                }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ 
                  duration: 0.3,
                  delay: index * 0.05
                }}
                className={cn(
                  "relative",
                  gridConfig.widgetClass,
                  isMobile && "touch-manipulation",
                  isTablet && "hover:scale-105 transition-transform duration-200",
                  isExpanded && "fixed inset-4 z-50 bg-white dark:bg-slate-800 rounded-xl shadow-2xl overflow-auto"
                )}
                style={!isExpanded ? {
                  gridColumn: isMobile ? "span 1" : isTablet ? "span 1" : `span ${Math.min(widget.position_config?.w || 4, gridConfig.columns)}`,
                  gridRow: `span ${widget.position_config?.h || (isMobile ? 1 : isTablet ? 1 : 3)}`,
                } : undefined}
                onTouchStart={isMobile ? handleTouchStart : undefined}
                onTouchEnd={isMobile ? (e) => handleTouchEnd(e, widget.id) : undefined}
              >
                <Card className={cn(
                  "dashboard-widget h-full hover-lift",
                  isExpanded && "h-full flex flex-col"
                )}>
                  {/* Widget Header */}
                  <div className="dashboard-widget-header">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <BarChart3 className="h-5 w-5 text-primary" />
                        <h3 className="font-semibold text-slate-900 dark:text-slate-100">
                          {widget.title}
                        </h3>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {/* Mobile-friendly expand button */}
                        {isMobile && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleWidgetAction('expand', widget)}
                            className="h-8 w-8 p-0"
                          >
                            {isExpanded ? (
                              <Minimize2 className="h-4 w-4" />
                            ) : (
                              <Maximize2 className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                        
                        {/* Widget actions menu */}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleWidgetAction('customize', widget)}>
                              <Settings className="h-4 w-4 mr-2" />
                              Customize
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleWidgetAction('insights', widget)}>
                              <BarChart3 className="h-4 w-4 mr-2" />
                              Insights
                            </DropdownMenuItem>
                            {!isMobile && (
                              <DropdownMenuItem onClick={() => handleWidgetAction('expand', widget)}>
                                {isExpanded ? (
                                  <>
                                    <Minimize2 className="h-4 w-4 mr-2" />
                                    Minimize
                                  </>
                                ) : (
                                  <>
                                    <Maximize2 className="h-4 w-4 mr-2" />
                                    Expand
                                  </>
                                )}
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem 
                              onClick={() => handleWidgetAction('remove', widget)}
                              className="text-red-600 dark:text-red-400"
                            >
                              Remove
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>

                  {/* Widget Content */}
                  <CardContent className={cn(
                    "dashboard-widget-content",
                    isExpanded && "flex-1 overflow-auto"
                  )}>
                    <DynamicWidget
                      widget={widget}
                      onRemove={() => onWidgetRemove?.(widget.id)}
                      onUpdate={(updates) => onWidgetUpdate?.(widget.id, updates)}
                      onCustomize={() => onWidgetCustomize?.(widget)}
                    />
                  </CardContent>
                </Card>

                {/* Expanded widget overlay close button */}
                {isExpanded && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setExpandedWidget(null)}
                    className="absolute top-2 right-2 z-60 h-8 w-8 p-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm"
                  >
                    <Minimize2 className="h-4 w-4" />
                  </Button>
                )}
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Mobile swipe hint */}
      {isMobile && hasWidgets && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="mt-6 text-center"
        >
          <p className="text-sm text-slate-500 dark:text-slate-400">
            💡 Swipe up on widgets to expand, swipe down to minimize
          </p>
        </motion.div>
      )}
    </div>
  );
};
