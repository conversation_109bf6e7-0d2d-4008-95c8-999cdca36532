"""
Shared Context Repository for Cross-Agent Intelligence.

This service provides persistent storage and management of cross-agent context,
insights, and interactions within business profile scopes.
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from ..database import get_db
from ..models.shared_context import (
    SharedContextCreate,
    SharedContextUpdate,
    SharedContextResponse,
    AgentInsightCreate,
    AgentInsightResponse,
    AgentInteractionCreate,
    AgentInteractionResponse
)

logger = logging.getLogger(__name__)


class SharedContextRepository:
    """
    Repository for managing shared context across agents within business profiles.
    
    This service handles:
    - Storing and retrieving agent insights
    - Managing agent interactions history
    - Cross-agent context sharing
    - Business profile scoped data isolation
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    # Agent Insights Management
    async def create_insight(self, business_profile_id: str, insight_data: AgentInsightCreate) -> AgentInsightResponse:
        """Create a new agent insight."""
        try:
            from ..database import AgentInsight
            
            insight = AgentInsight(
                id=str(uuid.uuid4()),
                business_profile_id=business_profile_id,
                source_agent_id=insight_data.source_agent_id,
                insight_type=insight_data.insight_type,
                content=insight_data.content,
                insight_metadata=insight_data.insight_metadata or {},
                relevance_tags=insight_data.relevance_tags or [],
                confidence_score=insight_data.confidence_score,
                is_active=True
            )
            
            self.db.add(insight)
            self.db.commit()
            self.db.refresh(insight)
            
            logger.info(f"Created insight {insight.id} from agent {insight_data.source_agent_id}")
            
            return AgentInsightResponse.model_validate(insight)
            
        except Exception as e:
            logger.error(f"Error creating insight: {e}")
            self.db.rollback()
            raise
    
    async def get_insights_for_agent(self, business_profile_id: str, requesting_agent_id: str, 
                                   limit: int = 50) -> List[AgentInsightResponse]:
        """Get relevant insights for a specific agent."""
        try:
            from ..database import AgentInsight
            
            # Get insights from other agents in the same business profile
            insights = self.db.query(AgentInsight).filter(
                and_(
                    AgentInsight.business_profile_id == business_profile_id,
                    AgentInsight.source_agent_id != requesting_agent_id,
                    AgentInsight.is_active == True
                )
            ).order_by(desc(AgentInsight.created_at)).limit(limit).all()
            
            return [AgentInsightResponse.model_validate(insight) for insight in insights]
            
        except Exception as e:
            logger.error(f"Error getting insights for agent: {e}")
            return []
    
    async def get_insights_by_type(self, business_profile_id: str, insight_types: List[str], 
                                 limit: int = 50) -> List[AgentInsightResponse]:
        """Get insights by specific types."""
        try:
            from ..database import AgentInsight
            
            insights = self.db.query(AgentInsight).filter(
                and_(
                    AgentInsight.business_profile_id == business_profile_id,
                    AgentInsight.insight_type.in_(insight_types),
                    AgentInsight.is_active == True
                )
            ).order_by(desc(AgentInsight.created_at)).limit(limit).all()
            
            return [AgentInsightResponse.model_validate(insight) for insight in insights]
            
        except Exception as e:
            logger.error(f"Error getting insights by type: {e}")
            return []
    
    async def update_insight_access(self, insight_id: str) -> bool:
        """Update insight access tracking."""
        try:
            from ..database import AgentInsight
            
            insight = self.db.query(AgentInsight).filter(AgentInsight.id == insight_id).first()
            if insight:
                insight.access_count += 1
                insight.last_accessed_at = datetime.utcnow()
                self.db.commit()
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error updating insight access: {e}")
            return False
    
    # Agent Interactions Management
    async def record_interaction(self, business_profile_id: str, 
                               interaction_data: AgentInteractionCreate) -> AgentInteractionResponse:
        """Record an agent interaction."""
        try:
            from ..database import AgentInteraction
            
            interaction = AgentInteraction(
                id=str(uuid.uuid4()),
                business_profile_id=business_profile_id,
                agent_id=interaction_data.agent_id,
                user_message=interaction_data.user_message,
                agent_response=interaction_data.agent_response,
                context_used=interaction_data.context_used or [],
                tools_used=interaction_data.tools_used or [],
                insights_generated=interaction_data.insights_generated or [],
                outcome=interaction_data.outcome,
                interaction_metadata=interaction_data.interaction_metadata or {}
            )
            
            self.db.add(interaction)
            self.db.commit()
            self.db.refresh(interaction)
            
            logger.info(f"Recorded interaction {interaction.id} for agent {interaction_data.agent_id}")
            
            return AgentInteractionResponse.model_validate(interaction)
            
        except Exception as e:
            logger.error(f"Error recording interaction: {e}")
            self.db.rollback()
            raise
    
    async def get_recent_interactions(self, business_profile_id: str, exclude_agent_id: str = None, 
                                    limit: int = 20) -> List[AgentInteractionResponse]:
        """Get recent interactions from other agents."""
        try:
            from ..database import AgentInteraction
            
            query = self.db.query(AgentInteraction).filter(
                AgentInteraction.business_profile_id == business_profile_id
            )
            
            if exclude_agent_id:
                query = query.filter(AgentInteraction.agent_id != exclude_agent_id)
            
            interactions = query.order_by(desc(AgentInteraction.created_at)).limit(limit).all()
            
            return [AgentInteractionResponse.model_validate(interaction) for interaction in interactions]
            
        except Exception as e:
            logger.error(f"Error getting recent interactions: {e}")
            return []
    
    async def get_agent_activity_summary(self, business_profile_id: str, 
                                       days: int = 7) -> Dict[str, Any]:
        """Get agent activity summary for a business profile."""
        try:
            from ..database import AgentInteraction, AgentInsight
            
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            # Get interaction counts by agent
            interaction_counts = self.db.query(
                AgentInteraction.agent_id,
                func.count(AgentInteraction.id).label('count')
            ).filter(
                and_(
                    AgentInteraction.business_profile_id == business_profile_id,
                    AgentInteraction.created_at >= cutoff_date
                )
            ).group_by(AgentInteraction.agent_id).all()
            
            # Get insight counts by agent
            insight_counts = self.db.query(
                AgentInsight.source_agent_id,
                func.count(AgentInsight.id).label('count')
            ).filter(
                and_(
                    AgentInsight.business_profile_id == business_profile_id,
                    AgentInsight.created_at >= cutoff_date
                )
            ).group_by(AgentInsight.source_agent_id).all()
            
            # Combine results
            activity_summary = {
                "interactions_by_agent": {row.agent_id: row.count for row in interaction_counts},
                "insights_by_agent": {row.source_agent_id: row.count for row in insight_counts},
                "total_interactions": sum(row.count for row in interaction_counts),
                "total_insights": sum(row.count for row in insight_counts),
                "active_agents": len(set([row.agent_id for row in interaction_counts] + 
                                       [row.source_agent_id for row in insight_counts])),
                "period_days": days
            }
            
            return activity_summary
            
        except Exception as e:
            logger.error(f"Error getting agent activity summary: {e}")
            return {}
    
    # Context Sharing Management
    async def create_shared_context(self, business_profile_id: str, 
                                  context_data: SharedContextCreate) -> SharedContextResponse:
        """Create shared context between agents."""
        try:
            from ..database import SharedContext
            
            shared_context = SharedContext(
                id=str(uuid.uuid4()),
                business_profile_id=business_profile_id,
                source_agent_id=context_data.source_agent_id,
                target_agent_id=context_data.target_agent_id,
                context_type=context_data.context_type,
                context_data=context_data.context_data,
                context_metadata=context_data.context_metadata or {},
                expires_at=context_data.expires_at
            )
            
            self.db.add(shared_context)
            self.db.commit()
            self.db.refresh(shared_context)
            
            logger.info(f"Created shared context from {context_data.source_agent_id} to {context_data.target_agent_id}")
            
            return SharedContextResponse.model_validate(shared_context)
            
        except Exception as e:
            logger.error(f"Error creating shared context: {e}")
            self.db.rollback()
            raise
    
    async def get_shared_context_for_agent(self, business_profile_id: str, 
                                         agent_id: str) -> List[SharedContextResponse]:
        """Get shared context targeted at a specific agent."""
        try:
            from ..database import SharedContext
            
            contexts = self.db.query(SharedContext).filter(
                and_(
                    SharedContext.business_profile_id == business_profile_id,
                    SharedContext.target_agent_id == agent_id,
                    or_(
                        SharedContext.expires_at.is_(None),
                        SharedContext.expires_at > datetime.utcnow()
                    )
                )
            ).order_by(desc(SharedContext.created_at)).all()
            
            return [SharedContextResponse.model_validate(context) for context in contexts]
            
        except Exception as e:
            logger.error(f"Error getting shared context for agent: {e}")
            return []
    
    # Cleanup and Maintenance
    async def cleanup_expired_data(self, business_profile_id: str = None) -> Dict[str, int]:
        """Clean up expired data."""
        try:
            from ..database import SharedContext, AgentInsight, AgentInteraction
            
            cleanup_stats = {
                "expired_contexts": 0,
                "old_interactions": 0,
                "inactive_insights": 0
            }
            
            # Clean up expired shared contexts
            query = self.db.query(SharedContext).filter(
                and_(
                    SharedContext.expires_at.isnot(None),
                    SharedContext.expires_at < datetime.utcnow()
                )
            )
            if business_profile_id:
                query = query.filter(SharedContext.business_profile_id == business_profile_id)
            
            expired_contexts = query.all()
            for context in expired_contexts:
                self.db.delete(context)
            cleanup_stats["expired_contexts"] = len(expired_contexts)
            
            # Clean up old interactions (older than 90 days)
            cutoff_date = datetime.utcnow() - timedelta(days=90)
            query = self.db.query(AgentInteraction).filter(
                AgentInteraction.created_at < cutoff_date
            )
            if business_profile_id:
                query = query.filter(AgentInteraction.business_profile_id == business_profile_id)
            
            old_interactions = query.all()
            for interaction in old_interactions:
                self.db.delete(interaction)
            cleanup_stats["old_interactions"] = len(old_interactions)
            
            self.db.commit()
            
            logger.info(f"Cleanup completed: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            self.db.rollback()
            return {"error": str(e)}
