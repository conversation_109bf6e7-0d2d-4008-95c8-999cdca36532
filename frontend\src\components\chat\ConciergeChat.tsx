import React, { useState, useEffect, useRef } from 'react';
import { useConcierge } from '@/contexts/ConciergeContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { PersonaSelector } from './PersonaSelector';
import { useToast } from '@/components/ui/use-toast';

export const ConciergeChat = () => {
  const { 
    messages = [], 
    sendMessage, 
    currentPersona, 
    switchPersona,
    isTyping = false,
    conciergeState
  } = useConcierge();
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const handleSend = () => {
    if (inputValue.trim()) {
      try {
        sendMessage(inputValue);
        setInputValue('');
      } catch (error) {
        toast({
          title: 'Message Failed',
          description: 'Could not send message. Please try again.',
          variant: 'destructive'
        });
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-lg font-semibold">Concierge Assistant</h2>
        <PersonaSelector 
          currentPersona={currentPersona} 
          onSelect={switchPersona}
          availablePersonas={conciergeState?.availablePersonas || []}
        />
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((msg, index) => (
          <div 
            key={index} 
            className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`max-w-xs md:max-w-md lg:max-w-lg p-3 rounded-lg ${
              msg.sender === 'user' 
                ? 'bg-brand-100 text-brand-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              <div className="flex items-start gap-2">
                {msg.sender === 'assistant' && (
                  <Avatar className="h-6 w-6 mt-1">
                    <AvatarImage src={currentPersona?.avatarUrl} />
                    <AvatarFallback>{currentPersona?.name[0]}</AvatarFallback>
                  </Avatar>
                )}
                <div>
                  <div className="font-medium">
                    {msg.sender === 'user' ? 'You' : currentPersona?.name}
                  </div>
                  <div className="mt-1">{msg.content}</div>
                </div>
                {msg.sender === 'user' && (
                  <Avatar className="h-6 w-6 mt-1">
                    <AvatarFallback>Y</AvatarFallback>
                  </Avatar>
                )}
              </div>
            </div>
          </div>
        ))}

        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-800 p-3 rounded-lg">
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={currentPersona?.avatarUrl} />
                  <AvatarFallback>{currentPersona?.name[0]}</AvatarFallback>
                </Avatar>
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="p-4 border-t flex gap-2">
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Type your message..."
        />
        <Button onClick={handleSend}>Send</Button>
      </div>
    </div>
  );
};
