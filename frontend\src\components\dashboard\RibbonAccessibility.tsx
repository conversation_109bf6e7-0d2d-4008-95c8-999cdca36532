/**
 * Ribbon Accessibility Enhancements
 * 
 * This file provides accessibility utilities and enhancements for the ribbon toolbar:
 * - ARIA labels and descriptions
 * - Keyboard navigation support
 * - Screen reader announcements
 * - Focus management
 * - High contrast mode support
 * - Reduced motion preferences
 */

import React, { useEffect, useRef, useCallback } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';

// Accessibility context for ribbon components
export interface AccessibilityContext {
  announceAction: (message: string) => void;
  focusNext: () => void;
  focusPrevious: () => void;
  focusTab: (tabId: string) => void;
  isHighContrast: boolean;
  isReducedMotion: boolean;
  screenReaderMode: boolean;
}

// Screen reader announcements
export const useScreenReaderAnnouncements = () => {
  const announcementRef = useRef<HTMLDivElement>(null);

  const announceAction = useCallback((message: string) => {
    if (announcementRef.current) {
      announcementRef.current.textContent = message;
      // Clear after announcement
      setTimeout(() => {
        if (announcementRef.current) {
          announcementRef.current.textContent = '';
        }
      }, 1000);
    }
  }, []);

  const AnnouncementRegion = () => (
    <div
      ref={announcementRef}
      aria-live="polite"
      aria-atomic="true"
      className="sr-only"
      role="status"
    />
  );

  return { announceAction, AnnouncementRegion };
};

// Keyboard navigation for ribbon
export const useRibbonKeyboardNavigation = (
  activeTab: string,
  setActiveTab: (tab: string) => void,
  tabs: string[]
) => {
  const ribbonRef = useRef<HTMLDivElement>(null);

  // Tab navigation
  useHotkeys('ctrl+tab', (e) => {
    e.preventDefault();
    const currentIndex = tabs.indexOf(activeTab);
    const nextIndex = (currentIndex + 1) % tabs.length;
    setActiveTab(tabs[nextIndex]);
  }, { enableOnFormTags: false });

  useHotkeys('ctrl+shift+tab', (e) => {
    e.preventDefault();
    const currentIndex = tabs.indexOf(activeTab);
    const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
    setActiveTab(tabs[prevIndex]);
  }, { enableOnFormTags: false });

  // Arrow key navigation within ribbon groups
  useHotkeys('arrowright', (e) => {
    if (document.activeElement?.closest('[role="toolbar"]')) {
      e.preventDefault();
      focusNext();
    }
  }, { enableOnFormTags: false });

  useHotkeys('arrowleft', (e) => {
    if (document.activeElement?.closest('[role="toolbar"]')) {
      e.preventDefault();
      focusPrevious();
    }
  }, { enableOnFormTags: false });

  const focusNext = useCallback(() => {
    const focusableElements = ribbonRef.current?.querySelectorAll(
      'button:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    if (focusableElements) {
      const currentIndex = Array.from(focusableElements).indexOf(document.activeElement as Element);
      const nextIndex = (currentIndex + 1) % focusableElements.length;
      (focusableElements[nextIndex] as HTMLElement)?.focus();
    }
  }, []);

  const focusPrevious = useCallback(() => {
    const focusableElements = ribbonRef.current?.querySelectorAll(
      'button:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    if (focusableElements) {
      const currentIndex = Array.from(focusableElements).indexOf(document.activeElement as Element);
      const prevIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1;
      (focusableElements[prevIndex] as HTMLElement)?.focus();
    }
  }, []);

  return { ribbonRef, focusNext, focusPrevious };
};

// Accessibility preferences detection
export const useAccessibilityPreferences = () => {
  const [preferences, setPreferences] = React.useState({
    isHighContrast: false,
    isReducedMotion: false,
    screenReaderMode: false,
  });

  useEffect(() => {
    // Detect high contrast mode
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');

    const updatePreferences = () => {
      setPreferences({
        isHighContrast: highContrastQuery.matches,
        isReducedMotion: reducedMotionQuery.matches,
        screenReaderMode: navigator.userAgent.includes('NVDA') || 
                         navigator.userAgent.includes('JAWS') ||
                         window.speechSynthesis?.speaking === true,
      });
    };

    updatePreferences();
    highContrastQuery.addEventListener('change', updatePreferences);
    reducedMotionQuery.addEventListener('change', updatePreferences);

    return () => {
      highContrastQuery.removeEventListener('change', updatePreferences);
      reducedMotionQuery.removeEventListener('change', updatePreferences);
    };
  }, []);

  return preferences;
};

// Enhanced button component with accessibility features
export const AccessibleRibbonButton: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  ariaLabel?: string;
  ariaDescription?: string;
  shortcut?: string;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
}> = ({
  children,
  onClick,
  disabled = false,
  ariaLabel,
  ariaDescription,
  shortcut,
  className = '',
  variant = 'outline',
  size = 'sm',
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { announceAction } = useScreenReaderAnnouncements();
  const { isHighContrast, isReducedMotion } = useAccessibilityPreferences();

  const handleClick = useCallback(() => {
    if (onClick) {
      onClick();
      if (ariaLabel) {
        announceAction(`${ariaLabel} activated`);
      }
    }
  }, [onClick, ariaLabel, announceAction]);

  const baseClasses = `
    inline-flex items-center justify-center rounded-md text-sm font-medium 
    transition-colors focus-visible:outline-none focus-visible:ring-2 
    focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 
    disabled:pointer-events-none ring-offset-background
    ${isHighContrast ? 'border-2 border-current' : ''}
    ${isReducedMotion ? 'transition-none' : 'transition-colors'}
  `;

  const variantClasses = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
  };

  const sizeClasses = {
    sm: 'h-8 px-3 text-xs',
    md: 'h-9 px-4 text-sm',
    lg: 'h-10 px-6 text-base',
  };

  return (
    <button
      ref={buttonRef}
      type="button"
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      onClick={handleClick}
      disabled={disabled}
      aria-label={ariaLabel}
      aria-describedby={ariaDescription ? `${ariaLabel}-desc` : undefined}
      title={shortcut ? `${ariaLabel} (${shortcut})` : ariaLabel}
    >
      {children}
      {ariaDescription && (
        <span id={`${ariaLabel}-desc`} className="sr-only">
          {ariaDescription}
        </span>
      )}
    </button>
  );
};

// Accessible dropdown menu component
export const AccessibleDropdownMenu: React.FC<{
  trigger: React.ReactNode;
  children: React.ReactNode;
  ariaLabel?: string;
}> = ({ trigger, children, ariaLabel }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);

  // Handle escape key
  useHotkeys('escape', () => {
    if (isOpen) {
      setIsOpen(false);
      triggerRef.current?.focus();
    }
  }, { enableOnFormTags: true });

  // Handle arrow keys in menu
  useHotkeys('arrowdown', (e) => {
    if (isOpen && menuRef.current?.contains(document.activeElement)) {
      e.preventDefault();
      const items = menuRef.current.querySelectorAll('[role="menuitem"]');
      const currentIndex = Array.from(items).indexOf(document.activeElement as Element);
      const nextIndex = (currentIndex + 1) % items.length;
      (items[nextIndex] as HTMLElement)?.focus();
    }
  }, { enableOnFormTags: true });

  useHotkeys('arrowup', (e) => {
    if (isOpen && menuRef.current?.contains(document.activeElement)) {
      e.preventDefault();
      const items = menuRef.current.querySelectorAll('[role="menuitem"]');
      const currentIndex = Array.from(items).indexOf(document.activeElement as Element);
      const prevIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
      (items[prevIndex] as HTMLElement)?.focus();
    }
  }, { enableOnFormTags: true });

  return (
    <div className="relative">
      <button
        ref={triggerRef}
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="menu"
        aria-label={ariaLabel}
        className="inline-flex items-center justify-center"
      >
        {trigger}
      </button>
      
      {isOpen && (
        <div
          ref={menuRef}
          role="menu"
          aria-orientation="vertical"
          className="absolute top-full left-0 z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md"
        >
          {children}
        </div>
      )}
    </div>
  );
};

// Responsive breakpoint utilities
export const useResponsiveBreakpoints = () => {
  const [breakpoint, setBreakpoint] = React.useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 640) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return {
    breakpoint,
    isMobile: breakpoint === 'mobile',
    isTablet: breakpoint === 'tablet',
    isDesktop: breakpoint === 'desktop',
  };
};
