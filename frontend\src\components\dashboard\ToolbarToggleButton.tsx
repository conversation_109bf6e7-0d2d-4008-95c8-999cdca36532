import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  ChevronUp, 
  ChevronDown, 
  Minimize2, 
  Maximize2,
  Menu,
  <PERSON>,
  <PERSON>,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToolbarVisibilityStore } from '@/stores/toolbar-visibility-store';
import { useDashboardMode } from '@/stores/dashboard-mode-store';

interface ToolbarToggleButtonProps {
  className?: string;
  variant?: 'chevron' | 'minimize' | 'menu' | 'eye';
  size?: 'sm' | 'default' | 'lg';
  position?: 'header' | 'floating' | 'inline';
  showLabel?: boolean;
  customLabel?: string;
}

export const ToolbarToggleButton: React.FC<ToolbarToggleButtonProps> = ({
  className,
  variant = 'chevron',
  size = 'sm',
  position = 'header',
  showLabel = false,
  customLabel,
}) => {
  const { current_mode } = useDashboardMode();
  const {
    isSimpleToolbarVisible,
    isAdvancedRibbonVisible,
    toggleSimpleToolbar,
    toggleAdvancedRibbon,
  } = useToolbarVisibilityStore();

  // Determine current visibility and toggle function based on mode
  const isVisible = current_mode === 'simple' ? isSimpleToolbarVisible : isAdvancedRibbonVisible;
  const toggleFunction = current_mode === 'simple' ? toggleSimpleToolbar : toggleAdvancedRibbon;

  // Get appropriate icons based on variant and state
  const getIcons = () => {
    switch (variant) {
      case 'chevron':
        return {
          visible: ChevronUp,
          hidden: ChevronDown,
        };
      case 'minimize':
        return {
          visible: Minimize2,
          hidden: Maximize2,
        };
      case 'menu':
        return {
          visible: X,
          hidden: Menu,
        };
      case 'eye':
        return {
          visible: EyeOff,
          hidden: Eye,
        };
      default:
        return {
          visible: ChevronUp,
          hidden: ChevronDown,
        };
    }
  };

  const icons = getIcons();
  const IconComponent = isVisible ? icons.visible : icons.hidden;

  // Generate tooltip text
  const getTooltipText = () => {
    if (customLabel) return customLabel;
    
    const action = isVisible ? 'Hide' : 'Show';
    const toolbarType = current_mode === 'simple' ? 'Toolbar' : 'Ribbon';
    return `${action} ${toolbarType}`;
  };

  // Generate button label
  const getButtonLabel = () => {
    if (customLabel) return customLabel;
    
    const action = isVisible ? 'Hide' : 'Show';
    const toolbarType = current_mode === 'simple' ? 'Tools' : 'Ribbon';
    return `${action} ${toolbarType}`;
  };

  // Position-specific styling
  const getPositionStyles = () => {
    switch (position) {
      case 'floating':
        return 'fixed top-20 right-4 z-50 shadow-lg';
      case 'inline':
        return '';
      case 'header':
      default:
        return '';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size={size}
            onClick={toggleFunction}
            className={cn(
              'transition-all duration-200 hover:bg-accent',
              getPositionStyles(),
              className
            )}
            aria-label={getTooltipText()}
          >
            <IconComponent className="h-4 w-4" />
            {showLabel && (
              <span className="ml-2 text-xs font-medium">
                {getButtonLabel()}
              </span>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <p>{getTooltipText()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Specialized variants for common use cases
export const HeaderToolbarToggle: React.FC<{ className?: string }> = ({ className }) => (
  <ToolbarToggleButton
    variant="chevron"
    position="header"
    className={className}
  />
);

export const FloatingToolbarToggle: React.FC<{ className?: string }> = ({ className }) => (
  <ToolbarToggleButton
    variant="minimize"
    position="floating"
    className={className}
  />
);

export const InlineToolbarToggle: React.FC<{ 
  className?: string; 
  showLabel?: boolean;
  variant?: 'chevron' | 'minimize' | 'menu' | 'eye';
}> = ({ className, showLabel = false, variant = 'chevron' }) => (
  <ToolbarToggleButton
    variant={variant}
    position="inline"
    showLabel={showLabel}
    className={className}
  />
);
