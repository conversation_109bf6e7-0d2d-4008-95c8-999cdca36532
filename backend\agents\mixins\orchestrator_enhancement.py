"""
Orchestrator Enhancement for Universal Tool Completion Support

This module provides enhanced orchestrator functionality to support
universal tool completion and conversational state management across all agents.
"""

import time
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class UniversalToolCompletionOrchestrator:
    """
    Enhanced orchestrator functionality for universal tool completion support.
    
    This class provides methods that can be integrated into the main orchestrator
    to support tool completion state restoration for all agent types.
    """
    
    # Agent type to tool indicator mappings
    AGENT_TOOL_INDICATORS = {
        "marketing": ["marketing_form_data", "marketing_task", "marketing_content_request"],
        "analysis": ["analysis_request", "data_analysis_task", "visualization_request", "query_request"],
        "concierge": ["persona_recommendation_request", "persona_selection", "concierge_task"],
        "composable-marketing-ai": ["marketing_form_data", "marketing_task", "marketing_content_request"],
        "composable-analysis-ai": ["analysis_request", "data_analysis_task", "visualization_request", "query_request"],
        "datagenius-concierge": ["persona_recommendation_request", "persona_selection", "concierge_task"]
    }
    
    # Agent type to conversational flag mappings
    AGENT_CONVERSATIONAL_FLAGS = {
        "marketing": [
            "skip_marketing_content_generation",
            "is_conversational",
            "content_generation_completed",
            "tool_completed",
            "auto_conversational_mode"
        ],
        "analysis": [
            "skip_analysis_execution",
            "is_conversational",
            "analysis_completed",
            "tool_completed",
            "auto_conversational_mode"
        ],
        "concierge": [
            "skip_persona_recommendation",
            "is_conversational", 
            "recommendation_completed",
            "tool_completed",
            "auto_conversational_mode"
        ]
    }
    
    @classmethod
    def get_agent_type_from_context(cls, context: Dict[str, Any]) -> Optional[str]:
        """
        Determine the agent type from context information.
        
        Args:
            context: Context dictionary
            
        Returns:
            Agent type string or None if not determinable
        """
        # Check current persona
        current_persona = context.get("current_persona", "")
        if current_persona:
            if "marketing" in current_persona.lower():
                return "marketing"
            elif "analysis" in current_persona.lower():
                return "analysis"
            elif "concierge" in current_persona.lower():
                return "concierge"
        
        # Check agent_id
        agent_id = context.get("agent_id", "")
        if agent_id:
            if "marketing" in agent_id.lower():
                return "marketing"
            elif "analysis" in agent_id.lower():
                return "analysis"
            elif "concierge" in agent_id.lower():
                return "concierge"
        
        # Check for agent-specific tool indicators
        for agent_type, indicators in cls.AGENT_TOOL_INDICATORS.items():
            if any(context.get(indicator) is not None for indicator in indicators):
                return agent_type.replace("composable-", "").replace("-ai", "")
        
        return None
    
    @classmethod
    def restore_conversational_state_for_agent(
        cls,
        enhanced_context: Dict[str, Any],
        conversation_history: List[Dict[str, Any]],
        agent_type: str
    ) -> bool:
        """
        Restore conversational state for a specific agent type.
        
        Args:
            enhanced_context: Context to enhance
            conversation_history: Conversation history
            agent_type: Type of agent (marketing, analysis, concierge)
            
        Returns:
            True if conversational state was restored
        """
        try:
            logger.info(f"🔄 ORCHESTRATOR: Checking conversational state for {agent_type} agent")
            
            # Get agent-specific indicators and flags
            tool_indicators = cls.AGENT_TOOL_INDICATORS.get(agent_type, [])
            conversational_flags = cls.AGENT_CONVERSATIONAL_FLAGS.get(agent_type, [])
            
            # Check if there are active tool triggers (should skip restoration)
            has_tool_triggers = any(
                enhanced_context.get(indicator) is not None or
                enhanced_context.get("metadata", {}).get(indicator) is not None
                for indicator in tool_indicators
            )
            
            # Also check generic tool triggers
            has_generic_triggers = (
                enhanced_context.get("is_regeneration", False) or
                enhanced_context.get("tool_call", False) or
                enhanced_context.get("button_triggered", False) or
                enhanced_context.get("form_submission", False)
            )
            
            if has_tool_triggers or has_generic_triggers:
                logger.info(f"🔧 ORCHESTRATOR: Tool triggers detected for {agent_type} - skipping state restoration")
                return False
            
            # Look through recent AI messages for conversational state
            recent_ai_messages = []
            for msg in reversed(conversation_history):
                if msg.get("sender") == "ai":
                    recent_ai_messages.append(msg)
                    if len(recent_ai_messages) >= 3:
                        break
            
            for msg in recent_ai_messages:
                metadata = msg.get("metadata", {}) or {}
                
                # Check for explicit conversational state metadata
                conversational_state = metadata.get("conversational_state", {})
                if conversational_state and conversational_state.get("agent_type") == agent_type:
                    if (conversational_state.get("next_message_should_be_conversational", False) or
                        conversational_state.get("auto_return_to_conversational", False) or
                        conversational_state.get("tool_completed", False)):
                        
                        # Restore agent-specific conversational flags
                        for flag in conversational_flags:
                            enhanced_context[flag] = True
                        
                        # Add restoration metadata
                        if "metadata" not in enhanced_context:
                            enhanced_context["metadata"] = {}
                        enhanced_context["metadata"]["conversational_state_restored"] = True
                        enhanced_context["metadata"]["restored_agent_type"] = agent_type
                        enhanced_context["metadata"]["restored_from_message_id"] = msg.get("id")
                        enhanced_context["metadata"]["restoration_timestamp"] = time.time()
                        
                        logger.info(f"✅ ORCHESTRATOR: Conversational state restored for {agent_type} agent")
                        logger.info(f"🔄 ORCHESTRATOR: Restored {len(conversational_flags)} conversational flags")
                        return True
                
                # Check for legacy metadata patterns
                if (metadata.get("generated_content", False) or
                    metadata.get("content_generation_completed", False) or
                    metadata.get("tool_execution", {}).get("status") == "completed"):
                    
                    # Check if there have been user messages since this content generation
                    user_messages_since = 0
                    found_this_message = False
                    
                    for check_msg in reversed(conversation_history):
                        if check_msg.get("id") == msg.get("id"):
                            found_this_message = True
                            continue
                        if found_this_message and check_msg.get("sender") == "user":
                            user_messages_since += 1
                    
                    if user_messages_since > 0:
                        # Restore conversational state based on legacy metadata
                        for flag in conversational_flags:
                            enhanced_context[flag] = True
                        
                        enhanced_context["is_follow_up_question"] = True
                        
                        if "metadata" not in enhanced_context:
                            enhanced_context["metadata"] = {}
                        enhanced_context["metadata"]["conversational_state_restored"] = True
                        enhanced_context["metadata"]["restored_from_legacy_metadata"] = True
                        enhanced_context["metadata"]["restored_agent_type"] = agent_type
                        enhanced_context["metadata"]["user_messages_since_content"] = user_messages_since
                        
                        logger.info(f"✅ ORCHESTRATOR: Conversational state restored for {agent_type} from legacy metadata")
                        logger.info(f"🔄 ORCHESTRATOR: {user_messages_since} user messages since content generation")
                        return True
                
                # Only check the most recent AI message for legacy patterns
                break
            
            return False
            
        except Exception as e:
            logger.error(f"❌ ORCHESTRATOR: Error restoring conversational state for {agent_type}: {str(e)}", exc_info=True)
            return False
    
    @classmethod
    def enhance_context_with_universal_tool_completion(
        cls,
        enhanced_context: Dict[str, Any],
        conversation_history: List[Dict[str, Any]]
    ) -> None:
        """
        Main method to enhance context with universal tool completion support.
        
        This method should be called from the orchestrator's _enhance_context_with_conversation_history
        method to provide universal tool completion support for all agents.
        
        Args:
            enhanced_context: Context to enhance
            conversation_history: Conversation history
        """
        try:
            logger.info("🔄 ORCHESTRATOR: Starting universal tool completion enhancement")
            
            # Determine agent type
            agent_type = cls.get_agent_type_from_context(enhanced_context)
            if not agent_type:
                logger.info("🔄 ORCHESTRATOR: Could not determine agent type - skipping tool completion enhancement")
                return
            
            logger.info(f"🔄 ORCHESTRATOR: Detected agent type: {agent_type}")
            
            # Restore conversational state for the detected agent type
            restored = cls.restore_conversational_state_for_agent(
                enhanced_context, 
                conversation_history, 
                agent_type
            )
            
            if not restored:
                logger.info(f"🔄 ORCHESTRATOR: No conversational state found for {agent_type} agent")
            
        except Exception as e:
            logger.error(f"❌ ORCHESTRATOR: Error in universal tool completion enhancement: {str(e)}", exc_info=True)
