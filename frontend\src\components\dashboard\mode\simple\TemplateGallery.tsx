/**
 * Template Gallery Component
 * 
 * Provides a curated collection of dashboard templates for Simple Mode users.
 * Features industry-specific templates, use-case templates, and live previews.
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Search,
  Star,
  Eye,
  Download,
  Sparkles,
  TrendingUp,
  BarChart3,
  PieChart,
  Users,
  ShoppingCart,
  DollarSign,
  Target,
  Calendar,
  Globe,
  Zap,
  Filter,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface DashboardTemplate {
  id: string;
  name: string;
  description: string;
  category: 'business' | 'sales' | 'marketing' | 'analytics' | 'finance' | 'operations';
  industry: string[];
  use_cases: string[];
  complexity: 'beginner' | 'intermediate' | 'advanced';
  widgets_count: number;
  preview_image?: string;
  rating: number;
  downloads: number;
  tags: string[];
  features: string[];
  layout_config: any;
  sections: any[];
  widgets: any[];
}

interface TemplateGalleryProps {
  className?: string;
  on_template_select?: (template: DashboardTemplate) => void;
  on_template_preview?: (template: DashboardTemplate) => void;
  show_preview_dialog?: boolean;
}

export const TemplateGallery: React.FC<TemplateGalleryProps> = ({
  className,
  on_template_select,
  on_template_preview,
  show_preview_dialog = true,
}) => {
  const [templates, set_templates] = useState<DashboardTemplate[]>([]);
  const [filtered_templates, set_filtered_templates] = useState<DashboardTemplate[]>([]);
  const [search_query, set_search_query] = useState('');
  const [selected_category, set_selected_category] = useState<string>('all');
  const [selected_complexity, set_selected_complexity] = useState<string>('all');
  const [preview_template, set_preview_template] = useState<DashboardTemplate | null>(null);
  const [is_loading, set_is_loading] = useState(true);

  // Load templates on component mount
  useEffect(() => {
    load_templates();
  }, []);

  // Filter templates based on search and filters
  useEffect(() => {
    let filtered = templates;

    // Search filter
    if (search_query) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(search_query.toLowerCase()) ||
        template.description.toLowerCase().includes(search_query.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(search_query.toLowerCase()))
      );
    }

    // Category filter
    if (selected_category !== 'all') {
      filtered = filtered.filter(template => template.category === selected_category);
    }

    // Complexity filter
    if (selected_complexity !== 'all') {
      filtered = filtered.filter(template => template.complexity === selected_complexity);
    }

    set_filtered_templates(filtered);
  }, [templates, search_query, selected_category, selected_complexity]);

  const load_templates = async () => {
    set_is_loading(true);
    try {
      // In a real implementation, this would fetch from an API
      const mock_templates = get_mock_templates();
      set_templates(mock_templates);
    } catch (error) {
      console.error('Error loading templates:', error);
    } finally {
      set_is_loading(false);
    }
  };

  const handle_template_select = (template: DashboardTemplate) => {
    on_template_select?.(template);
  };

  const handle_template_preview = (template: DashboardTemplate) => {
    if (show_preview_dialog) {
      set_preview_template(template);
    }
    on_template_preview?.(template);
  };

  const get_category_icon = (category: string) => {
    const icons = {
      business: BarChart3,
      sales: TrendingUp,
      marketing: Target,
      analytics: PieChart,
      finance: DollarSign,
      operations: Users,
    };
    return icons[category as keyof typeof icons] || BarChart3;
  };

  const get_complexity_color = (complexity: string) => {
    const colors = {
      beginner: 'text-green-600 bg-green-50',
      intermediate: 'text-yellow-600 bg-yellow-50',
      advanced: 'text-red-600 bg-red-50',
    };
    return colors[complexity as keyof typeof colors] || 'text-gray-600 bg-gray-50';
  };

  const categories = [
    { id: 'all', label: 'All Templates', icon: Globe },
    { id: 'business', label: 'Business', icon: BarChart3 },
    { id: 'sales', label: 'Sales', icon: TrendingUp },
    { id: 'marketing', label: 'Marketing', icon: Target },
    { id: 'analytics', label: 'Analytics', icon: PieChart },
    { id: 'finance', label: 'Finance', icon: DollarSign },
    { id: 'operations', label: 'Operations', icon: Users },
  ];

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Sparkles className="h-6 w-6 text-blue-500" />
          <h2 className="text-2xl font-bold">Dashboard Templates</h2>
          <Badge variant="secondary">Simple Mode</Badge>
        </div>
        <p className="text-muted-foreground">
          Choose from our curated collection of professional dashboard templates.
          Each template is designed for specific industries and use cases.
        </p>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={search_query}
            onChange={(e) => set_search_query(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <select
            value={selected_complexity}
            onChange={(e) => set_selected_complexity(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="all">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
      </div>

      {/* Category Tabs */}
      <Tabs value={selected_category} onValueChange={set_selected_category}>
        <TabsList className="grid w-full grid-cols-7">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <TabsTrigger key={category.id} value={category.id} className="text-xs">
                <IconComponent className="h-4 w-4 mr-1" />
                {category.label}
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value={selected_category} className="mt-6">
          {is_loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-32 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filtered_templates.map((template) => {
                const CategoryIcon = get_category_icon(template.category);
                return (
                  <Card key={template.id} className="group hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <CardTitle className="text-lg flex items-center space-x-2">
                            <CategoryIcon className="h-5 w-5 text-blue-500" />
                            <span>{template.name}</span>
                          </CardTitle>
                          <CardDescription className="text-sm">
                            {template.description}
                          </CardDescription>
                        </div>
                        <Badge 
                          variant="outline" 
                          className={cn("text-xs", get_complexity_color(template.complexity))}
                        >
                          {template.complexity}
                        </Badge>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      {/* Preview Area */}
                      <div className="h-32 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border-2 border-dashed border-blue-200 flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                          <p className="text-xs">{template.widgets_count} widgets</p>
                        </div>
                      </div>

                      {/* Template Info */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span>{template.rating}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Download className="h-4 w-4 text-muted-foreground" />
                            <span>{template.downloads}</span>
                          </div>
                        </div>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-1">
                          {template.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {template.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{template.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handle_template_preview(template)}
                          className="flex-1"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Preview
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handle_template_select(template)}
                          className="flex-1"
                        >
                          <Zap className="h-4 w-4 mr-1" />
                          Use Template
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          {!is_loading && filtered_templates.length === 0 && (
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No templates found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search criteria or browse different categories.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Preview Dialog */}
      {preview_template && (
        <Dialog open={!!preview_template} onOpenChange={() => set_preview_template(null)}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Sparkles className="h-5 w-5 text-blue-500" />
                <span>{preview_template.name}</span>
              </DialogTitle>
              <DialogDescription>
                {preview_template.description}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Template Details */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="font-medium">Category</p>
                  <p className="text-muted-foreground capitalize">{preview_template.category}</p>
                </div>
                <div>
                  <p className="font-medium">Complexity</p>
                  <p className="text-muted-foreground capitalize">{preview_template.complexity}</p>
                </div>
                <div>
                  <p className="font-medium">Widgets</p>
                  <p className="text-muted-foreground">{preview_template.widgets_count}</p>
                </div>
                <div>
                  <p className="font-medium">Rating</p>
                  <p className="text-muted-foreground">{preview_template.rating}/5</p>
                </div>
              </div>

              {/* Features */}
              <div>
                <p className="font-medium mb-2">Features</p>
                <div className="flex flex-wrap gap-2">
                  {preview_template.features.map((feature, index) => (
                    <Badge key={index} variant="outline">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Preview Area */}
              <div className="h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <BarChart3 className="h-16 w-16 mx-auto mb-4" />
                  <p>Template Preview</p>
                  <p className="text-sm">Interactive preview coming soon</p>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => set_preview_template(null)}>
                Close
              </Button>
              <Button onClick={() => {
                handle_template_select(preview_template);
                set_preview_template(null);
              }}>
                <Zap className="h-4 w-4 mr-2" />
                Use This Template
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

// Mock templates data (to be replaced with API calls)
function get_mock_templates(): DashboardTemplate[] {
  return [
    {
      id: 'sales-overview',
      name: 'Sales Overview',
      description: 'Comprehensive sales performance dashboard with KPIs and trends',
      category: 'sales',
      industry: ['retail', 'e-commerce', 'saas'],
      use_cases: ['sales tracking', 'performance monitoring', 'team management'],
      complexity: 'beginner',
      widgets_count: 6,
      rating: 4.8,
      downloads: 1250,
      tags: ['sales', 'kpi', 'revenue', 'trends'],
      features: ['Revenue tracking', 'Sales funnel', 'Team performance', 'Goal tracking'],
      layout_config: {},
      sections: [],
      widgets: [],
    },
    {
      id: 'marketing-analytics',
      name: 'Marketing Analytics',
      description: 'Track campaign performance and customer acquisition metrics',
      category: 'marketing',
      industry: ['marketing', 'advertising', 'digital'],
      use_cases: ['campaign tracking', 'roi analysis', 'customer insights'],
      complexity: 'intermediate',
      widgets_count: 8,
      rating: 4.6,
      downloads: 890,
      tags: ['marketing', 'campaigns', 'roi', 'analytics'],
      features: ['Campaign ROI', 'Customer acquisition', 'Channel performance', 'Conversion tracking'],
      layout_config: {},
      sections: [],
      widgets: [],
    },
    {
      id: 'financial-dashboard',
      name: 'Financial Dashboard',
      description: 'Monitor financial health with P&L, cash flow, and budget tracking',
      category: 'finance',
      industry: ['finance', 'accounting', 'business'],
      use_cases: ['financial reporting', 'budget tracking', 'cash flow'],
      complexity: 'advanced',
      widgets_count: 10,
      rating: 4.9,
      downloads: 650,
      tags: ['finance', 'budget', 'profit', 'cash flow'],
      features: ['P&L tracking', 'Budget vs actual', 'Cash flow', 'Financial ratios'],
      layout_config: {},
      sections: [],
      widgets: [],
    },
    // Add more mock templates as needed
  ];
}
