"""add user_search_activities table

Revision ID: ccc028f0f372
Revises: f4c56997e328
Create Date: 2025-05-27 19:01:04.216147

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ccc028f0f372'
down_revision = 'f4c56997e328' # Ensure this points to the previous migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_search_activities',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('session_id', sa.String(length=36), nullable=True),
    sa.Column('query', sa.String(length=500), nullable=False),
    sa.Column('filters_applied', sa.JSON(), nullable=True),
    sa.Column('result_count', sa.Integer(), nullable=True),
    sa.Column('clicked_result_ids', sa.JSON(), nullable=True),
    sa.Column('search_timestamp', sa.DateTime(), nullable=True),
    sa.Column('user_ip', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_search_activities_id'), 'user_search_activities', ['id'], unique=False)
    op.create_index(op.f('ix_user_search_activities_query'), 'user_search_activities', ['query'], unique=False)
    op.create_index(op.f('ix_user_search_activities_search_timestamp'), 'user_search_activities', ['search_timestamp'], unique=False)
    op.create_index(op.f('ix_user_search_activities_session_id'), 'user_search_activities', ['session_id'], unique=False)
    op.create_index(op.f('ix_user_search_activities_user_id'), 'user_search_activities', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_search_activities_user_id'), table_name='user_search_activities')
    op.drop_index(op.f('ix_user_search_activities_session_id'), table_name='user_search_activities')
    op.drop_index(op.f('ix_user_search_activities_search_timestamp'), table_name='user_search_activities')
    op.drop_index(op.f('ix_user_search_activities_query'), table_name='user_search_activities')
    op.drop_index(op.f('ix_user_search_activities_id'), table_name='user_search_activities')
    op.drop_table('user_search_activities')
    # ### end Alembic commands ###
