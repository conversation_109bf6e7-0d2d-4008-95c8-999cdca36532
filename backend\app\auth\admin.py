"""
Admin authentication utilities for the Datagenius application.

This module provides authentication utilities for admin users.
"""

import logging
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..database import get_db, User
from . import get_current_active_user

# Set up logging
logger = logging.getLogger(__name__)


async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> User:
    """
    Get the current admin user.

    Args:
        current_user: Current authenticated user
        db: Database session

    Returns:
        User object if the user is an admin

    Raises:
        HTTPException: If the user is not an admin
    """
    if not current_user.is_superuser:
        logger.warning(f"Non-admin user {current_user.id} attempted to access admin functionality")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access admin functionality"
        )
    
    return current_user


# Alias for backward compatibility and convenience
require_admin = get_current_admin_user
