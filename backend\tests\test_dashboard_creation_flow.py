#!/usr/bin/env python3
"""
Test script to verify the complete dashboard creation flow with data source selection.
"""

import requests
import json

# Test data for dashboard creation with data source assignments
dashboard_with_data_sources = {
    "name": "Sales Analytics Dashboard",
    "description": "Dashboard for analyzing sales data and customer metrics",
    "is_default": False,
    "is_public": False,
    "layout_config": {
        "columns": 12,
        "rows": 12,
        "grid_gap": 16,
        "margin": [10, 10],
        "container_padding": [10, 10],
        "auto_size": True
    },
    "theme_config": {
        "primary_color": "#3B82F6",
        "secondary_color": "#10B981",
        "background_color": "#F9FAFB",
        "text_color": "#1F2937",
        "border_color": "#E5E7EB",
        "accent_color": "#F59E0B",
        "success_color": "#10B981",
        "warning_color": "#F59E0B",
        "error_color": "#EF4444",
        "font_family": "Inter, system-ui, sans-serif",
        "font_size_base": 14,
        "border_radius": 8,
        "shadow_level": "md"
    },
    "refresh_interval": 300,
    "tags": ["sales", "analytics", "customer"],
    "data_source_assignments": [
        {
            "system_data_source_id": "ds-sales-csv-001",
            "alias": "Sales Data",
            "is_active": True
        },
        {
            "system_data_source_id": "ds-customer-db-002", 
            "alias": "Customer Database",
            "is_active": True
        }
    ]
}

# Test data for empty dashboard creation
empty_dashboard = {
    "name": "Empty Dashboard",
    "description": "A clean dashboard with no data sources",
    "is_default": False,
    "is_public": False,
    "layout_config": {
        "columns": 12,
        "rows": 12,
        "grid_gap": 16,
        "margin": [10, 10],
        "container_padding": [10, 10],
        "auto_size": True
    },
    "theme_config": {
        "primary_color": "#3B82F6",
        "secondary_color": "#10B981",
        "background_color": "#F9FAFB",
        "text_color": "#1F2937"
    },
    "refresh_interval": 300,
    "tags": [],
    "data_source_assignments": []
}

def test_dashboard_creation_flow():
    """Test the complete dashboard creation flow."""
    
    base_url = "http://localhost:8000"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_AUTH_TOKEN_HERE"  # Replace with actual token
    }
    
    print("🧪 Testing Dashboard Creation Flow")
    print("=" * 50)
    
    # Test 1: Get available data sources
    print("\n1. Testing data source retrieval...")
    try:
        response = requests.get(f"{base_url}/data-sources", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data_sources = response.json().get('data_sources', [])
            print(f"   ✅ Found {len(data_sources)} data sources")
            for ds in data_sources[:3]:  # Show first 3
                print(f"      - {ds['name']} ({ds['type']})")
        else:
            print(f"   ❌ Failed to get data sources: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Create dashboard with data sources
    print("\n2. Testing dashboard creation with data sources...")
    try:
        response = requests.post(
            f"{base_url}/api/dashboards/",
            headers=headers,
            json=dashboard_with_data_sources
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            dashboard = response.json()
            print(f"   ✅ Created dashboard: {dashboard['name']}")
            print(f"      ID: {dashboard['id']}")
            print(f"      Sections: {dashboard['section_count']}")
            print(f"      Widgets: {dashboard['widget_count']}")
            dashboard_id = dashboard['id']
        else:
            print(f"   ❌ Failed to create dashboard: {response.text}")
            dashboard_id = None
    except Exception as e:
        print(f"   ❌ Error: {e}")
        dashboard_id = None
    
    # Test 3: Create empty dashboard
    print("\n3. Testing empty dashboard creation...")
    try:
        response = requests.post(
            f"{base_url}/api/dashboards/",
            headers=headers,
            json=empty_dashboard
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            dashboard = response.json()
            print(f"   ✅ Created empty dashboard: {dashboard['name']}")
            print(f"      ID: {dashboard['id']}")
            print(f"      Sections: {dashboard['section_count']}")
            print(f"      Widgets: {dashboard['widget_count']}")
            empty_dashboard_id = dashboard['id']
        else:
            print(f"   ❌ Failed to create empty dashboard: {response.text}")
            empty_dashboard_id = None
    except Exception as e:
        print(f"   ❌ Error: {e}")
        empty_dashboard_id = None
    
    # Test 4: Verify dashboard data sources (if dashboard was created)
    if dashboard_id:
        print(f"\n4. Testing dashboard data source retrieval...")
        try:
            response = requests.get(
                f"{base_url}/api/dashboards/{dashboard_id}/data-sources",
                headers=headers
            )
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data_sources = response.json()
                print(f"   ✅ Dashboard has {len(data_sources)} data sources")
                for ds in data_sources:
                    print(f"      - {ds.get('name', 'Unknown')} ({ds.get('type', 'Unknown')})")
            else:
                print(f"   ❌ Failed to get dashboard data sources: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Test 5: Verify empty dashboard has no data sources
    if empty_dashboard_id:
        print(f"\n5. Testing empty dashboard data sources...")
        try:
            response = requests.get(
                f"{base_url}/api/dashboards/{empty_dashboard_id}/data-sources",
                headers=headers
            )
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data_sources = response.json()
                if len(data_sources) == 0:
                    print(f"   ✅ Empty dashboard correctly has no data sources")
                else:
                    print(f"   ⚠️  Empty dashboard unexpectedly has {len(data_sources)} data sources")
            else:
                print(f"   ❌ Failed to get empty dashboard data sources: {response.text}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Dashboard Creation Flow Test Complete")
    print("\nExpected Results:")
    print("✅ Data sources should be retrievable from /data-sources")
    print("✅ Dashboards should be created successfully")
    print("✅ New dashboards should start with 0 sections and 0 widgets")
    print("✅ Data source assignments should work correctly")
    print("✅ Empty dashboards should have no data sources")
    print("\nNote: Replace YOUR_AUTH_TOKEN_HERE with a valid authentication token")

if __name__ == "__main__":
    test_dashboard_creation_flow()
