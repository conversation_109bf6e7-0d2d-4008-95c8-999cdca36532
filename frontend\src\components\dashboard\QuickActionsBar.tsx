import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Plus,
  Copy,
  Trash2,
  Move,
  Settings,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface QuickActionsBarProps {
  isVisible: boolean;
  position: { x: number; y: number };
  onAddWidget?: () => void;
  onDuplicateWidget?: () => void;
  onDeleteWidget?: () => void;
  onMoveWidget?: () => void;
  onEditWidget?: () => void;
  onClose?: () => void;
  className?: string;
}

export const QuickActionsBar: React.FC<QuickActionsBarProps> = ({
  isVisible,
  position,
  onAddWidget,
  onDuplicateWidget,
  onDeleteWidget,
  onMoveWidget,
  onEditWidget,
  onClose,
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Auto-hide after 5 seconds of inactivity
  useEffect(() => {
    if (!isVisible) return;

    const timer = setTimeout(() => {
      onClose?.();
    }, 5000);

    return () => clearTimeout(timer);
  }, [isVisible, onClose]);

  // Close on escape key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose?.();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <TooltipProvider>
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 10 }}
          transition={{ duration: 0.2 }}
          className={cn(
            "fixed z-50 bg-white/95 backdrop-blur-sm border border-slate-200 rounded-lg shadow-lg",
            "flex items-center space-x-1 p-2",
            className
          )}
          style={{
            left: position.x,
            top: position.y,
            transform: 'translate(-50%, -100%)'
          }}
        >
          {/* Primary Actions - Always Visible */}
          <div className="flex items-center space-x-1">
            {onAddWidget && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={onAddWidget}
                    size="sm"
                    className="h-8 w-8 p-0 ribbon-button-primary"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Add Widget</p>
                  <p className="text-xs text-slate-400">Ctrl+N</p>
                </TooltipContent>
              </Tooltip>
            )}

            {onDuplicateWidget && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={onDuplicateWidget}
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Duplicate Widget</p>
                  <p className="text-xs text-slate-400">Ctrl+D</p>
                </TooltipContent>
              </Tooltip>
            )}

            {onDeleteWidget && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={onDeleteWidget}
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Delete Widget</p>
                  <p className="text-xs text-slate-400">Delete</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>

          {/* Secondary Actions - Expandable */}
          {(onMoveWidget || onEditWidget) && (
            <>
              <div className="w-px h-6 bg-slate-200" />
              
              <AnimatePresence>
                {isExpanded && (
                  <motion.div
                    initial={{ width: 0, opacity: 0 }}
                    animate={{ width: 'auto', opacity: 1 }}
                    exit={{ width: 0, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center space-x-1 overflow-hidden"
                  >
                    {onMoveWidget && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            onClick={onMoveWidget}
                            variant="outline"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <Move className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Move Widget</TooltipContent>
                      </Tooltip>
                    )}

                    {onEditWidget && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            onClick={onEditWidget}
                            variant="outline"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Edit Widget</TooltipContent>
                      </Tooltip>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={() => setIsExpanded(!isExpanded)}
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {isExpanded ? 'Show less' : 'More actions'}
                </TooltipContent>
              </Tooltip>
            </>
          )}

          {/* Close Button */}
          <div className="w-px h-6 bg-slate-200" />
          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-slate-400 hover:text-slate-600"
          >
            ×
          </Button>
        </motion.div>
      </AnimatePresence>
    </TooltipProvider>
  );
};

// Hook for managing quick actions bar state
export const useQuickActionsBar = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const showQuickActions = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setPosition({
      x: rect.left + rect.width / 2,
      y: rect.top
    });
    setIsVisible(true);
  };

  const hideQuickActions = () => {
    setIsVisible(false);
  };

  return {
    isVisible,
    position,
    showQuickActions,
    hideQuickActions
  };
};
