"""
Tool Call Manager Component

This component manages tool calls and conversation state to ensure proper handling of:
1. Tool-triggered requests (button clicks, form submissions)
2. Conversational follow-ups
3. Tool call results and context preservation
4. UI indicators for tool calls
"""

import logging
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from .base import AgentComponent

logger = logging.getLogger(__name__)


class RequestType(Enum):
    """Types of requests that can be processed."""
    TOOL_CALL = "tool_call"
    CONVERSATIONAL = "conversational"
    FOLLOW_UP = "follow_up"
    REGENERATION = "regeneration"


@dataclass
class ToolCallContext:
    """Context information for a tool call."""
    tool_name: str
    request_type: RequestType
    triggered_by: str  # "button", "form", "api", "user_message"
    form_data: Optional[Dict[str, Any]] = None
    tool_parameters: Optional[Dict[str, Any]] = None
    conversation_id: str = ""
    user_id: str = ""
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()


class ToolCallManager(AgentComponent):
    """
    Component for managing tool calls and conversation state.
    
    This component ensures that:
    - Tool-triggered requests are properly identified and processed
    - Conversational state is maintained between messages
    - Tool call results are preserved in conversation context
    - UI indicators are properly set for tool calls
    """
    
    def __init__(self):
        super().__init__()
        self.active_tool_calls: Dict[str, ToolCallContext] = {}
        self.conversation_states: Dict[str, Dict[str, Any]] = {}
    
    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the tool call manager."""
        logger.info("Initializing ToolCallManager")
        self.config = config
    
    async def process(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process context to manage tool calls and conversation state.
        
        Args:
            context: The current processing context
            
        Returns:
            Enhanced context with tool call management
        """
        conversation_id = context.get("conversation_id", "")
        user_id = str(context.get("user_id", ""))
        
        # Analyze the request type
        request_analysis = self._analyze_request_type(context)
        
        # Store the analysis in context
        context["request_analysis"] = request_analysis
        
        # Handle based on request type
        if request_analysis["type"] == RequestType.TOOL_CALL:
            await self._handle_tool_call_request(context, request_analysis)
        elif request_analysis["type"] == RequestType.CONVERSATIONAL:
            await self._handle_conversational_request(context, request_analysis)
        elif request_analysis["type"] == RequestType.FOLLOW_UP:
            await self._handle_follow_up_request(context, request_analysis)
        elif request_analysis["type"] == RequestType.REGENERATION:
            await self._handle_regeneration_request(context, request_analysis)
        
        # Update conversation state
        self._update_conversation_state(conversation_id, context, request_analysis)
        
        return context
    
    def _analyze_request_type(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze the request to determine its type and characteristics.
        
        Args:
            context: The processing context
            
        Returns:
            Dictionary with request analysis
        """
        message = context.get("message", "")
        
        # Check for tool-triggered indicators
        has_form_data = self._has_marketing_form_data(context)
        is_regeneration = self._is_regeneration_request(context)
        has_tool_indicators = self._has_tool_indicators(context)
        
        # Check conversation history for recent tool calls
        conversation_history = context.get("conversation_history", [])
        recent_tool_call = self._find_recent_tool_call(conversation_history)
        
        # Determine request type
        if has_form_data or is_regeneration or has_tool_indicators:
            request_type = RequestType.REGENERATION if is_regeneration else RequestType.TOOL_CALL
            triggered_by = "regeneration" if is_regeneration else ("form" if has_form_data else "button")
        elif recent_tool_call and self._is_follow_up_message(message):
            request_type = RequestType.FOLLOW_UP
            triggered_by = "user_message"
        else:
            request_type = RequestType.CONVERSATIONAL
            triggered_by = "user_message"
        
        analysis = {
            "type": request_type,
            "triggered_by": triggered_by,
            "has_form_data": has_form_data,
            "is_regeneration": is_regeneration,
            "has_tool_indicators": has_tool_indicators,
            "recent_tool_call": recent_tool_call,
            "should_use_tools": request_type in [RequestType.TOOL_CALL, RequestType.REGENERATION],
            "should_be_conversational": request_type in [RequestType.CONVERSATIONAL, RequestType.FOLLOW_UP],
            "confidence": self._calculate_confidence(request_type, has_form_data, is_regeneration, recent_tool_call)
        }
        
        logger.info(f"Request analysis: {analysis}")
        return analysis
    
    def _has_marketing_form_data(self, context: Dict[str, Any]) -> bool:
        """Check if the context contains marketing form data."""
        return (
            context.get("marketing_form_data") is not None or
            context.get("metadata", {}).get("marketing_form_data") is not None or
            context.get("context", {}).get("marketing_form_data") is not None
        )
    
    def _is_regeneration_request(self, context: Dict[str, Any]) -> bool:
        """Check if this is a regeneration request."""
        return (
            context.get("is_regeneration", False) or
            context.get("metadata", {}).get("is_regeneration", False)
        )
    
    def _has_tool_indicators(self, context: Dict[str, Any]) -> bool:
        """Check for explicit tool call indicators."""
        return (
            context.get("tool_call", False) or
            context.get("button_triggered", False) or
            context.get("form_submission", False)
        )
    
    def _find_recent_tool_call(self, conversation_history: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Find the most recent tool call in conversation history."""
        # Look through last 5 messages for tool calls
        recent_messages = conversation_history[-5:] if len(conversation_history) > 5 else conversation_history
        
        for msg in reversed(recent_messages):
            if msg.get("sender") == "ai":
                metadata = msg.get("metadata", {})
                if (metadata.get("generated_content", False) or 
                    metadata.get("tool_call_result", False) or
                    metadata.get("conversational_state", {}).get("content_generated", False)):
                    return {
                        "timestamp": metadata.get("timestamp", time.time()),
                        "tool_name": metadata.get("tool_name", "unknown"),
                        "content_type": metadata.get("content_type", "unknown")
                    }
        return None
    
    def _is_follow_up_message(self, message: str) -> bool:
        """Check if the message is a follow-up question."""
        follow_up_patterns = [
            "what else", "any other", "more ideas", "additional", "also", "furthermore",
            "what about", "how about", "can you", "do you have", "any more", "other ways",
            "what other", "anything else", "more suggestions", "other options"
        ]
        message_lower = message.lower()
        return any(pattern in message_lower for pattern in follow_up_patterns)
    
    def _calculate_confidence(self, request_type: RequestType, has_form_data: bool, 
                            is_regeneration: bool, recent_tool_call: Optional[Dict[str, Any]]) -> float:
        """Calculate confidence score for the request type determination."""
        if request_type == RequestType.TOOL_CALL and has_form_data:
            return 0.95
        elif request_type == RequestType.REGENERATION and is_regeneration:
            return 0.95
        elif request_type == RequestType.FOLLOW_UP and recent_tool_call:
            return 0.85
        elif request_type == RequestType.CONVERSATIONAL:
            return 0.75
        else:
            return 0.60
    
    async def _handle_tool_call_request(self, context: Dict[str, Any], analysis: Dict[str, Any]) -> None:
        """Handle tool call requests."""
        logger.info("Handling tool call request")
        
        # Set flags to ensure tool processing
        context["should_use_tools"] = True
        context["skip_conversational_mode"] = True
        context["tool_call_request"] = True
        
        # Add UI indicators
        context["ui_indicators"] = {
            "tool_call_active": True,
            "tool_name": "marketing_content_generator",
            "status": "processing"
        }
    
    async def _handle_conversational_request(self, context: Dict[str, Any], analysis: Dict[str, Any]) -> None:
        """Handle conversational requests."""
        logger.info("Handling conversational request")
        
        # Set flags for conversational processing
        context["should_be_conversational"] = True
        context["skip_tool_calls"] = True
        context["conversational_request"] = True
    
    async def _handle_follow_up_request(self, context: Dict[str, Any], analysis: Dict[str, Any]) -> None:
        """Handle follow-up requests."""
        logger.info("Handling follow-up request")
        
        # Set flags for follow-up processing
        context["should_be_conversational"] = True
        context["is_follow_up"] = True
        context["skip_tool_calls"] = True
        context["follow_up_request"] = True
        
        # Include context from recent tool call
        if analysis.get("recent_tool_call"):
            context["recent_tool_context"] = analysis["recent_tool_call"]
    
    async def _handle_regeneration_request(self, context: Dict[str, Any], analysis: Dict[str, Any]) -> None:
        """Handle regeneration requests."""
        logger.info("Handling regeneration request")
        
        # Set flags for regeneration processing
        context["should_use_tools"] = True
        context["is_regeneration"] = True
        context["regeneration_request"] = True
        
        # Add UI indicators
        context["ui_indicators"] = {
            "tool_call_active": True,
            "tool_name": "marketing_content_generator",
            "status": "regenerating"
        }
    
    def _update_conversation_state(self, conversation_id: str, context: Dict[str, Any], 
                                 analysis: Dict[str, Any]) -> None:
        """Update conversation state tracking."""
        if not conversation_id:
            return
        
        state = {
            "last_request_type": analysis["type"].value,
            "last_request_time": time.time(),
            "has_recent_tool_call": analysis.get("recent_tool_call") is not None,
            "conversation_mode": "tool_active" if analysis["should_use_tools"] else "conversational"
        }
        
        self.conversation_states[conversation_id] = state
        context["conversation_state_tracking"] = state
