"""
Vector database service for Datagenius using mem0ai.

This module provides a unified vector database service using mem0ai and Qdrant,
replacing the previous FAISS implementation. It handles document embedding,
storage, and retrieval for semantic search and other vector operations.
"""

import os
import logging
import uuid
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path

from mem0 import Memory
from langchain_core.documents import Document

from app.config import (
    MEM0_API_KEY,
    MEM0_ENDPOINT,
    MEM0_SELF_HOSTED,
    MEM0_DEFAULT_TTL,
    MEM0_MAX_MEMORIES,
    MEM0_MEMORY_THRESHOLD
)
from app.performance.memory_manager import memory_manager
from .memory_service import MemoryService
from .qdrant_manager import Qdrant<PERSON>anager
from .adaptive_chunking import AdaptiveChunker, ChunkingStrategy, ContentType

logger = logging.getLogger(__name__)

class VectorService:
    """
    Unified vector database service using mem0ai.

    This service provides a unified interface for vector database operations
    across all AI personas in the Datagenius application. It implements the
    Singleton pattern to ensure a single instance is shared across the application.
    """

    _instance = None

    def __new__(cls):
        """Implement singleton pattern for vector service."""
        if cls._instance is None:
            cls._instance = super(VectorService, cls).__new__(cls)
            cls._instance._initialize()
            logger.info("Initialized mem0ai Vector service")
        return cls._instance

    @classmethod
    def get_instance_with_user_id(cls, user_id: int):
        """
        Get vector service instance with user-specific memory service settings.

        Args:
            user_id: User ID to load settings for

        Returns:
            VectorService instance configured with user's saved settings
        """
        try:
            # Get memory service with user settings
            memory_service = MemoryService.get_instance_with_user_id(user_id)

            # Create or update vector service instance
            instance = cls()
            instance.memory_service = memory_service
            instance.memory = memory_service.memory
            instance.initialized = memory_service.initialized

            # Ensure vector_db_dir is set (in case _initialize wasn't called properly)
            if not hasattr(instance, 'vector_db_dir'):
                instance.vector_db_dir = os.path.join(os.getcwd(), "vector_db")
                os.makedirs(instance.vector_db_dir, exist_ok=True)

            logger.info(f"Vector service configured with user {user_id} settings")
            return instance
        except Exception as e:
            logger.error(f"Error configuring vector service for user {user_id}: {e}")
            # Fall back to default instance
            return cls()

    def _initialize(self):
        """Initialize the vector service with configuration."""
        try:
            # Use the existing memory service to leverage its configuration
            self.memory_service = MemoryService()
            self.memory = self.memory_service.memory

            # Set up vector database directory for file info storage
            self.vector_db_dir = os.path.join(os.getcwd(), "vector_db")
            os.makedirs(self.vector_db_dir, exist_ok=True)

            # Initialize adaptive chunker
            self.adaptive_chunker = AdaptiveChunker()

            # Track initialization status
            self.initialized = self.memory_service.initialized

            if self.initialized:
                logger.info(f"Vector service initialized successfully (self-hosted: {MEM0_SELF_HOSTED})")
                logger.info("Adaptive chunking system initialized")
            else:
                logger.error("Vector service initialization failed: Memory service not initialized")
        except Exception as e:
            self.initialized = False
            logger.error(f"Failed to initialize vector service: {e}", exc_info=True)

    async def embed_document(self, file_path: str, chunk_size: int = None,
                      chunk_overlap: int = None, use_adaptive_chunking: bool = True) -> Tuple[str, Dict[str, Any]]:
        """
        Embed a document using mem0ai with adaptive chunking strategies and memory monitoring.

        Args:
            file_path: Path to the document file
            chunk_size: Size of text chunks for splitting (optional, uses adaptive if None)
            chunk_overlap: Overlap between chunks (optional, uses adaptive if None)
            use_adaptive_chunking: Whether to use adaptive chunking strategies

        Returns:
            Tuple of (vector_store_id, file_info)
        """
        if not self.initialized:
            logger.warning("Vector service not initialized, cannot embed document")
            raise RuntimeError("Vector service not initialized")

        # Use memory monitoring for the entire embedding process
        async with memory_manager.monitor_memory(f"embed_document_{os.path.basename(file_path)}"):
            try:
                # Check if document is already embedded by searching for existing chunks
                logger.info(f"Checking for existing embeddings for file: {file_path}")
                existing_vector_store_id = self._find_existing_document(file_path)
                if existing_vector_store_id:
                    logger.info(f"✅ Document {file_path} already embedded with vector store ID: {existing_vector_store_id}")

                    # TEMPORARY: Force re-embedding to test the fix
                    logger.warning(f"🔧 TESTING: Force re-embedding document to test raw chunk storage fix")
                    self._cleanup_old_embeddings(file_path)

                # # Load existing file info
                # file_info = self._load_file_info(existing_vector_store_id)
                # if file_info:
                #     logger.info(f"✅ Loaded existing file info, returning existing vector store ID")
                #     return existing_vector_store_id, file_info
                # else:
                #     logger.warning(f"Could not load file info for existing vector store ID {existing_vector_store_id}, re-embedding")
                #     # Clean up old embeddings before re-embedding
                #     self._cleanup_old_embeddings(file_path)
                else:
                    logger.info(f"No existing embeddings found for {file_path}, creating new embedding")
                    # Clean up any orphaned embeddings for this file path
                    self._cleanup_old_embeddings(file_path)

                # Generate a unique ID for the vector store
                vector_store_id = str(uuid.uuid4())

                # Get file extension
                file_extension = os.path.splitext(file_path)[1].lower()

                # Load document content with memory monitoring
                logger.info(f"Loading document content from: {file_path}")
                document_content = self._load_document(file_path)
                logger.info(f"Loaded document content: {len(document_content)} characters")

                if not document_content or len(document_content.strip()) == 0:
                    logger.error(f"Document content is empty for file: {file_path}")
                    raise ValueError(f"Document content is empty for file: {file_path}")

                # Split document into chunks using adaptive or traditional chunking
                if use_adaptive_chunking:
                    # Use adaptive chunking
                    file_extension = os.path.splitext(file_path)[1].lower()
                    chunks_with_metadata = self.adaptive_chunker.chunk_document(
                        document_content,
                        file_extension
                    )
                    chunks = [chunk_text for chunk_text, _ in chunks_with_metadata]
                    chunk_metadata_list = [metadata for _, metadata in chunks_with_metadata]

                    logger.info(f"Adaptive chunking: Split document into {len(chunks)} chunks")
                    if chunks:
                        logger.info(f"Content type detected: {chunk_metadata_list[0].content_type.value}")
                        logger.info(f"First chunk preview: {chunks[0][:100]}...")
                else:
                    # Use traditional chunking with fallback defaults
                    if chunk_size is None:
                        chunk_size = 1000
                    if chunk_overlap is None:
                        chunk_overlap = 200

                    chunks = self._split_document(document_content, chunk_size, chunk_overlap)
                    chunk_metadata_list = None
                    logger.info(f"Traditional chunking: Split document into {len(chunks)} chunks (chunk_size={chunk_size}, overlap={chunk_overlap})")
                    if chunks:
                        logger.info(f"First chunk preview: {chunks[0][:100]}...")

                # Create metadata for the document
                if use_adaptive_chunking and chunk_metadata_list:
                    # Use adaptive chunking metadata
                    first_chunk_meta = chunk_metadata_list[0]
                    metadata = {
                        "source": file_path,
                        "file_type": file_extension.lstrip("."),
                        "content_type": first_chunk_meta.content_type.value,
                        "chunking_strategy": "adaptive",
                        "vector_store_id": vector_store_id,
                        "total_chunks": len(chunks)
                    }
                else:
                    # Use traditional metadata
                    metadata = {
                        "source": file_path,
                        "file_type": file_extension.lstrip("."),
                        "chunk_size": chunk_size or 1000,
                        "chunk_overlap": chunk_overlap or 200,
                        "chunking_strategy": "traditional",
                        "vector_store_id": vector_store_id,
                        "total_chunks": len(chunks)
                    }

                # Add each chunk to mem0ai as a separate memory
                chunk_results = []
                logger.info(f"Starting to embed {len(chunks)} chunks for document {file_path}")

                for i, chunk in enumerate(chunks):
                    if chunk.strip():  # Only add non-empty chunks
                        try:
                            logger.info(f"Embedding chunk {i+1}/{len(chunks)} (length: {len(chunk)} chars)")
                            logger.info(f"Chunk {i+1} preview: {chunk[:100]}...")

                            # Create enhanced chunk metadata
                            chunk_metadata = {
                                "document_id": vector_store_id,
                                "file_path": file_path,
                                "file_type": file_extension.lstrip("."),
                                "is_document": True,
                                "chunk_index": i,
                                "total_chunks": len(chunks),
                                **metadata
                            }

                            # Add adaptive chunking metadata if available
                            if use_adaptive_chunking and chunk_metadata_list and i < len(chunk_metadata_list):
                                adaptive_meta = chunk_metadata_list[i]
                                chunk_metadata.update({
                                    "chunk_id": adaptive_meta.chunk_id,
                                    "semantic_score": adaptive_meta.semantic_score,
                                    "keywords": adaptive_meta.keywords,
                                    "sentence_count": adaptive_meta.sentence_count,
                                    "word_count": adaptive_meta.word_count,
                                    "complexity_score": adaptive_meta.complexity_score
                                })

                            # Log what we're about to add to vector database
                            logger.info(f"Adding to vector database - chunk content: '{chunk[:100]}...'")
                            logger.info(f"Adding to vector database - metadata: {chunk_metadata}")

                            # Store raw chunk directly in vector database instead of using mem0ai memory processing
                            result = self._store_raw_chunk(chunk, chunk_metadata)

                            logger.info(f"Vector database add result for chunk {i+1}: {result}")

                            if result:
                                chunk_results.append(result)
                                logger.debug(f"✓ Chunk {i+1} embedded successfully")
                            else:
                                logger.warning(f"✗ Chunk {i+1} embedding returned no result")

                        except Exception as e:
                            logger.error(f"✗ Error embedding chunk {i+1}: {e}")
                            # Check for dimension mismatch specifically
                            if "Vector dimension error" in str(e):
                                logger.error(f"DIMENSION MISMATCH detected in chunk {i+1}")
                                logger.error(f"Expected dimensions don't match Hugging Face model output")
                                raise e  # Re-raise to trigger the dimension mismatch handler
                            # Continue with other chunks for other errors
                            continue

                logger.info(f"Successfully embedded {len(chunk_results)} chunks for document {file_path}")

                # Create file info
                file_info = {
                    "file_path": file_path,
                    "file_type": file_extension.lstrip("."),
                    "vector_store_id": vector_store_id,
                    "chunks_embedded": len(chunk_results),
                    **metadata
                }

                # Save file info to disk for future reference
                self._save_file_info(vector_store_id, file_info)

                logger.info(f"Embedded document {file_path} with vector store ID: {vector_store_id}")
                return vector_store_id, file_info
            except Exception as e:
                # Check if it's a dimension mismatch error
                if "Vector dimension error" in str(e):
                    logger.error(f"Vector dimension mismatch: {e}")
                    logger.info("This usually happens when switching between embedding models with different dimensions")
                    logger.info("Please run: python scripts/reset_qdrant_collections.py")
                    raise RuntimeError(
                        "Vector dimension mismatch detected. "
                        "Please reset Qdrant collections by running: python scripts/reset_qdrant_collections.py"
                    )
                else:
                    logger.error(f"Error embedding document: {e}")
                    raise

    def search_document(self, vector_store_id: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search a document using mem0ai.

        Args:
            vector_store_id: ID of the vector store
            query: Search query
            limit: Maximum number of results to return

        Returns:
            List of search results
        """
        if not self.initialized:
            logger.warning("Vector service not initialized, cannot search document")
            return []

        try:
            # Search directly in Qdrant for raw chunks instead of using mem0ai memory search
            logger.info(f"Searching for query: '{query[:100]}...' with vector_store_id: {vector_store_id}")

            # Generate embedding for the query
            from sentence_transformers import SentenceTransformer

            # Use the same embedding model as configured
            model_name = "sentence-transformers/all-MiniLM-L6-v2"  # Default HF model
            try:
                if hasattr(self, 'memory_service') and self.memory_service:
                    embedding_config = getattr(self.memory_service, 'embedding_config', {})
                    model_name = embedding_config.get('model', model_name)
            except:
                pass

            model = SentenceTransformer(model_name)
            query_embedding = model.encode(query).tolist()

            # Search directly in Qdrant
            import requests
            from app.config import QDRANT_HOST, QDRANT_PORT

            search_url = f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/mem0/points/search"
            search_payload = {
                "vector": query_embedding,
                "filter": {
                    "must": [
                        {
                            "key": "document_id",
                            "match": {"value": vector_store_id}
                        },
                        {
                            "key": "is_document",
                            "match": {"value": True}
                        }
                    ]
                },
                "limit": limit,
                "with_payload": True
            }

            response = requests.post(search_url, json=search_payload, timeout=30)

            if response.status_code != 200:
                logger.error(f"Qdrant search failed: {response.status_code} - {response.text}")
                return []

            data = response.json()
            results = data.get("result", [])

            logger.info(f"Raw Qdrant search returned {len(results)} results")

            # Format results
            formatted_results = []
            for i, result in enumerate(results):
                payload = result.get("payload", {})
                content = payload.get("content", "")
                score = result.get("score", 0)

                logger.info(f"Result {i}: document_id={payload.get('document_id')}, content_length={len(content)}, score={score}")
                logger.info(f"Result {i} content preview: '{content[:100]}...'")

                formatted_results.append({
                    "content": content,
                    "metadata": payload,
                    "score": score
                })
                logger.info(f"✓ Result {i} added - content: '{content[:100]}...'")

            logger.info(f"Found {len(formatted_results)} results for query: {query[:50]}...")
            return formatted_results
        except Exception as e:
            logger.error(f"Error searching document: {e}")
            return []

    def _split_document(self, content: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """
        Split document content into chunks.

        Args:
            content: Document content to split
            chunk_size: Maximum size of each chunk in characters
            chunk_overlap: Number of characters to overlap between chunks

        Returns:
            List of text chunks
        """
        try:
            from langchain.text_splitter import RecursiveCharacterTextSplitter

            # Create text splitter
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                length_function=len,
                separators=["\n\n", "\n", " ", ""]
            )

            # Split the content
            chunks = text_splitter.split_text(content)

            logger.debug(f"Split content into {len(chunks)} chunks")
            return chunks
        except ImportError:
            # Fallback to simple splitting if langchain is not available
            logger.warning("langchain not available, using simple text splitting")
            return self._simple_split_text(content, chunk_size, chunk_overlap)
        except Exception as e:
            logger.error(f"Error splitting document: {e}")
            # Fallback to simple splitting
            return self._simple_split_text(content, chunk_size, chunk_overlap)

    def _simple_split_text(self, text: str, chunk_size: int, chunk_overlap: int) -> List[str]:
        """
        Simple text splitting fallback method.

        Args:
            text: Text to split
            chunk_size: Maximum size of each chunk
            chunk_overlap: Overlap between chunks

        Returns:
            List of text chunks
        """
        chunks = []
        start = 0

        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]

            # Try to break at word boundaries
            if end < len(text) and not text[end].isspace():
                last_space = chunk.rfind(' ')
                if last_space > chunk_size // 2:  # Only break at space if it's not too early
                    end = start + last_space
                    chunk = text[start:end]

            chunks.append(chunk.strip())

            # Move start position with overlap
            start = end - chunk_overlap
            if start <= 0:
                start = end

        return [chunk for chunk in chunks if chunk.strip()]

    def _load_document(self, file_path: str) -> str:
        """
        Load document content from file.

        Args:
            file_path: Path to the document file

        Returns:
            Document content as string
        """
        file_extension = os.path.splitext(file_path)[1].lower()

        try:
            # Use appropriate loader based on file extension
            if file_extension == '.pdf':
                from langchain_community.document_loaders import PyPDFLoader
                loader = PyPDFLoader(file_path)
                documents = loader.load()
                return "\n\n".join([doc.page_content for doc in documents])
            elif file_extension in ['.docx', '.doc']:
                from langchain_community.document_loaders import Docx2txtLoader
                loader = Docx2txtLoader(file_path)
                documents = loader.load()
                return "\n\n".join([doc.page_content for doc in documents])
            elif file_extension == '.txt':
                from langchain_community.document_loaders import TextLoader
                loader = TextLoader(file_path)
                documents = loader.load()
                return "\n\n".join([doc.page_content for doc in documents])
            else:
                # Try to infer file type and use appropriate loader
                try:
                    from langchain_community.document_loaders import TextLoader
                    loader = TextLoader(file_path)
                    documents = loader.load()
                    return "\n\n".join([doc.page_content for doc in documents])
                except Exception as e:
                    logger.error(f"Error loading document with TextLoader: {e}")
                    raise ValueError(f"Unsupported file type: {file_extension}")
        except Exception as e:
            logger.error(f"Error loading document: {e}")
            raise

    def _save_file_info(self, vector_store_id: str, file_info: Dict[str, Any]) -> bool:
        """
        Save file info to disk.

        Args:
            vector_store_id: ID of the vector store
            file_info: File information dictionary

        Returns:
            True if successful, False otherwise
        """
        try:
            # Save as YAML
            import yaml
            info_path = os.path.join(self.vector_db_dir, f"{vector_store_id}_info.yaml")
            with open(info_path, 'w') as f:
                yaml.dump(file_info, f)

            logger.debug(f"Saved file info to {info_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving file info: {e}")
            return False

    def _load_file_info(self, vector_store_id: str) -> Optional[Dict[str, Any]]:
        """
        Load file info from disk.

        Args:
            vector_store_id: ID of the vector store

        Returns:
            File information dictionary or None if not found
        """
        try:
            import yaml
            info_path = os.path.join(self.vector_db_dir, f"{vector_store_id}_info.yaml")
            if os.path.exists(info_path):
                with open(info_path, 'r') as f:
                    file_info = yaml.safe_load(f)
                logger.debug(f"Loaded file info from {info_path}")
                return file_info
            else:
                logger.debug(f"File info not found at {info_path}")
                return None
        except Exception as e:
            logger.error(f"Error loading file info: {e}")
            return None

    def _find_existing_document(self, file_path: str) -> Optional[str]:
        """
        Find existing vector store ID for a document by searching for chunks with matching file_path.

        Args:
            file_path: Path to the document file

        Returns:
            Vector store ID if found, None otherwise
        """
        try:
            logger.info(f"Searching for existing document with file_path: {file_path}")
            # Search for any memories with this file_path in metadata
            # We'll search for a generic query and filter by file_path
            results = self.memory.search(
                "document content",  # Generic query
                user_id="system",
                limit=50  # Get more results to find our document
            )

            logger.info(f"Found {len(results.get('results', []))} total memories in search")

            # Look for chunks with matching file_path
            for i, result in enumerate(results.get("results", [])):
                metadata = result.get("metadata", {})
                result_file_path = metadata.get("file_path")
                is_document = metadata.get("is_document")
                document_id = metadata.get("document_id")

                logger.info(f"Memory {i}: file_path='{result_file_path}', is_document={is_document}, document_id={document_id}")

                if (result_file_path == file_path and is_document == True):
                    vector_store_id = document_id
                    if vector_store_id:
                        logger.info(f"✅ Found existing document with vector store ID: {vector_store_id}")
                        return vector_store_id

            logger.info(f"❌ No existing document found for file_path: {file_path}")
            return None
        except Exception as e:
            logger.error(f"Error finding existing document: {e}")
            return None

    def _cleanup_old_embeddings(self, file_path: str) -> bool:
        """
        Clean up old embeddings for a file path to prevent conflicts.

        Args:
            file_path: Path to the document file

        Returns:
            True if cleanup was successful, False otherwise
        """
        try:
            logger.info(f"Cleaning up old embeddings for file: {file_path}")

            # Method 1: Use mem0ai search to find and delete old embeddings
            try:
                # Search for any memories with this file_path in metadata
                results = self.memory.search(
                    f"file_path:{file_path}",  # More specific query
                    user_id="system",
                    limit=100  # Get more results to find all chunks
                )

                deleted_count = 0
                for result in results.get("results", []):
                    metadata = result.get("metadata", {})
                    result_file_path = metadata.get("file_path")
                    is_document = metadata.get("is_document")
                    memory_id = result.get("id")

                    if (result_file_path == file_path and is_document == True and memory_id):
                        try:
                            # Delete the memory
                            self.memory.delete(memory_id)
                            deleted_count += 1
                            logger.debug(f"Deleted old embedding with ID: {memory_id}")
                        except Exception as e:
                            logger.warning(f"Failed to delete memory {memory_id}: {e}")

                if deleted_count > 0:
                    logger.info(f"Cleaned up {deleted_count} old embeddings via mem0ai for file: {file_path}")
                else:
                    logger.info(f"No old embeddings found via mem0ai search for file: {file_path}")
            except Exception as e:
                logger.warning(f"mem0ai cleanup failed: {e}")

            # Method 2: Direct Qdrant cleanup as fallback
            try:
                import requests
                from app.config import QDRANT_HOST, QDRANT_PORT

                # Search directly in Qdrant for points with matching file_path
                search_url = f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/mem0/points/scroll"
                search_payload = {
                    "filter": {
                        "must": [
                            {
                                "key": "file_path",
                                "match": {"value": file_path}
                            },
                            {
                                "key": "is_document",
                                "match": {"value": True}
                            }
                        ]
                    },
                    "limit": 100
                }

                response = requests.post(search_url, json=search_payload, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    points = data.get("result", {}).get("points", [])

                    if points:
                        # Delete the points
                        point_ids = [point["id"] for point in points]
                        delete_url = f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/mem0/points/delete"
                        delete_payload = {"points": point_ids}

                        delete_response = requests.post(delete_url, json=delete_payload, timeout=10)
                        if delete_response.status_code == 200:
                            logger.info(f"Cleaned up {len(point_ids)} old embeddings via direct Qdrant cleanup for file: {file_path}")
                        else:
                            logger.warning(f"Failed to delete points via Qdrant: {delete_response.status_code}")
                    else:
                        logger.info(f"No old embeddings found via direct Qdrant search for file: {file_path}")
                else:
                    logger.warning(f"Failed to search Qdrant: {response.status_code}")

            except Exception as e:
                logger.warning(f"Direct Qdrant cleanup failed: {e}")

            return True
        except Exception as e:
            logger.error(f"Error cleaning up old embeddings: {e}")
            return False

    def _store_raw_chunk(self, chunk_content: str, metadata: Dict[str, Any]) -> Optional[str]:
        """
        Store raw chunk content directly in vector database bypassing mem0ai memory processing.

        Args:
            chunk_content: Raw text content to store
            metadata: Metadata for the chunk

        Returns:
            Chunk ID if successful, None otherwise
        """
        try:
            import requests
            from app.config import QDRANT_HOST, QDRANT_PORT

            # Generate embedding for the chunk using HuggingFace model
            from sentence_transformers import SentenceTransformer

            # Use the same embedding model as configured in memory service
            model_name = "sentence-transformers/all-MiniLM-L6-v2"  # Default HF model
            try:
                # Try to get the model from memory service config if available
                if hasattr(self, 'memory_service') and self.memory_service:
                    # Get embedding config from memory service
                    embedding_config = getattr(self.memory_service, 'embedding_config', {})
                    model_name = embedding_config.get('model', model_name)
            except:
                pass

            logger.info(f"Using embedding model: {model_name}")
            model = SentenceTransformer(model_name)

            # Generate embedding
            embedding = model.encode(chunk_content).tolist()
            logger.info(f"Generated embedding with {len(embedding)} dimensions")

            # Generate unique ID for this chunk
            chunk_id = str(uuid.uuid4())

            # Prepare point for Qdrant
            point = {
                "id": chunk_id,
                "vector": embedding,
                "payload": {
                    **metadata,
                    "content": chunk_content,  # Store the actual content
                    "content_length": len(chunk_content)
                }
            }

            # Insert directly into Qdrant
            insert_url = f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/mem0/points?wait=true"
            insert_payload = {
                "points": [point]
            }

            response = requests.put(insert_url, json=insert_payload, timeout=30)

            if response.status_code == 200:
                logger.info(f"Successfully stored raw chunk with ID: {chunk_id}")
                return chunk_id
            else:
                logger.error(f"Failed to store chunk in Qdrant: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error storing raw chunk: {e}")
            return None
