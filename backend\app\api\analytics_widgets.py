"""
Analytics Widgets API endpoints.
Provides analytics capabilities for dashboard widgets including data cleaning,
visualization generation, sentiment analysis, and statistical insights.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from app.database import get_db
from app.auth.admin import get_current_active_user
from app.services.analytics_widget_service import (
    AnalyticsWidgetService, AnalyticsRequest, AnalyticsResult,
    DataCleaningConfig, VisualizationConfig
)
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/analytics-widgets", tags=["analytics-widgets"])


def get_analytics_service(db: Session = Depends(get_db)) -> AnalyticsWidgetService:
    """Dependency to get analytics widget service."""
    return AnalyticsWidgetService(db)


@router.post("/analyze", response_model=AnalyticsResult)
async def analyze_widget_data(
    request: AnalyticsRequest,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    service: AnalyticsWidgetService = Depends(get_analytics_service)
):
    """Perform analytics on widget data."""
    try:
        result = await service.process_analytics_request(request, current_user["id"])
        return result
    except Exception as e:
        logger.error(f"Error analyzing widget data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze widget data"
        )


@router.post("/clean-data")
async def clean_widget_data(
    widget_id: str,
    data_source_id: str,
    config: DataCleaningConfig,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    service: AnalyticsWidgetService = Depends(get_analytics_service)
):
    """Clean data for a specific widget."""
    try:
        request = AnalyticsRequest(
            widget_id=widget_id,
            analysis_type="cleaning",
            data_source_id=data_source_id,
            parameters=config.model_dump(),
            cache_results=True
        )
        
        result = await service.process_analytics_request(request, current_user["id"])
        return result
    except Exception as e:
        logger.error(f"Error cleaning widget data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clean widget data"
        )


@router.post("/generate-visualization")
async def generate_widget_visualization(
    widget_id: str,
    data_source_id: str,
    config: VisualizationConfig,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    service: AnalyticsWidgetService = Depends(get_analytics_service)
):
    """Generate visualization configuration for a widget."""
    try:
        request = AnalyticsRequest(
            widget_id=widget_id,
            analysis_type="visualization",
            data_source_id=data_source_id,
            parameters=config.model_dump(),
            cache_results=True
        )
        
        result = await service.process_analytics_request(request, current_user["id"])
        return result
    except Exception as e:
        logger.error(f"Error generating visualization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate visualization"
        )


@router.post("/sentiment-analysis")
async def analyze_widget_sentiment(
    widget_id: str,
    data_source_id: str,
    text_column: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    service: AnalyticsWidgetService = Depends(get_analytics_service)
):
    """Perform sentiment analysis on widget text data."""
    try:
        request = AnalyticsRequest(
            widget_id=widget_id,
            analysis_type="sentiment",
            data_source_id=data_source_id,
            parameters={"text_column": text_column},
            cache_results=True
        )
        
        result = await service.process_analytics_request(request, current_user["id"])
        return result
    except Exception as e:
        logger.error(f"Error analyzing sentiment: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze sentiment"
        )


@router.post("/statistical-insights")
async def generate_statistical_insights(
    widget_id: str,
    data_source_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    service: AnalyticsWidgetService = Depends(get_analytics_service)
):
    """Generate statistical insights for widget data."""
    try:
        request = AnalyticsRequest(
            widget_id=widget_id,
            analysis_type="statistical",
            data_source_id=data_source_id,
            parameters={},
            cache_results=True
        )
        
        result = await service.process_analytics_request(request, current_user["id"])
        return result
    except Exception as e:
        logger.error(f"Error generating statistical insights: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate statistical insights"
        )


@router.get("/analysis-types")
async def get_available_analysis_types():
    """Get available analysis types and their descriptions."""
    return {
        "cleaning": {
            "name": "Data Cleaning",
            "description": "Clean and preprocess data by handling missing values, duplicates, and outliers",
            "parameters": {
                "remove_duplicates": "Remove duplicate rows",
                "handle_missing_values": "How to handle missing values (drop, fill_mean, fill_median, fill_mode, forward_fill)",
                "outlier_detection": "Detect and handle outliers",
                "outlier_method": "Outlier detection method (iqr, zscore, isolation_forest)",
                "normalize_text": "Normalize text columns",
                "standardize_dates": "Standardize date formats"
            }
        },
        "visualization": {
            "name": "Visualization Generation",
            "description": "Generate optimal visualization configurations based on data characteristics",
            "parameters": {
                "chart_type": "Type of visualization (chart, table, kpi, gauge, map, text, image)",
                "x_column": "X-axis column",
                "y_column": "Y-axis column",
                "color_column": "Color grouping column",
                "size_column": "Size column for scatter plots",
                "aggregation": "Data aggregation method (sum, mean, count, max, min)",
                "group_by": "Columns to group by",
                "filters": "Data filters"
            }
        },
        "sentiment": {
            "name": "Sentiment Analysis",
            "description": "Analyze sentiment in text data to understand emotional tone",
            "parameters": {
                "text_column": "Column containing text data to analyze"
            }
        },
        "statistical": {
            "name": "Statistical Insights",
            "description": "Generate statistical insights including correlations, distributions, and trends",
            "parameters": {}
        }
    }


@router.get("/widget-recommendations/{widget_id}")
async def get_widget_recommendations(
    widget_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    service: AnalyticsWidgetService = Depends(get_analytics_service)
):
    """Get analytics recommendations for a specific widget."""
    try:
        # This would analyze the widget's current configuration and data
        # to suggest improvements or additional analytics
        
        # Mock recommendations for now
        recommendations = [
            {
                "type": "cleaning",
                "priority": "high",
                "title": "Data Quality Issues Detected",
                "description": "Your data contains missing values and duplicates that could affect visualization accuracy.",
                "action": "Run data cleaning analysis",
                "estimated_improvement": "15-20% better data quality"
            },
            {
                "type": "visualization",
                "priority": "medium",
                "title": "Visualization Optimization",
                "description": "A different chart type might better represent your data patterns.",
                "action": "Generate visualization recommendations",
                "estimated_improvement": "Better data insights"
            },
            {
                "type": "statistical",
                "priority": "low",
                "title": "Statistical Insights Available",
                "description": "Discover correlations and trends in your data.",
                "action": "Run statistical analysis",
                "estimated_improvement": "Deeper data understanding"
            }
        ]
        
        return {
            "widget_id": widget_id,
            "recommendations": recommendations,
            "total_recommendations": len(recommendations)
        }
        
    except Exception as e:
        logger.error(f"Error getting widget recommendations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get widget recommendations"
        )


@router.post("/cache/clear")
async def clear_analytics_cache(
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    service: AnalyticsWidgetService = Depends(get_analytics_service)
):
    """Clear the analytics cache."""
    try:
        service.clear_cache()
        return {"message": "Analytics cache cleared successfully"}
    except Exception as e:
        logger.error(f"Error clearing analytics cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear analytics cache"
        )


@router.get("/health")
async def health_check():
    """Health check endpoint for the analytics widgets service."""
    return {
        "status": "healthy",
        "service": "analytics-widgets",
        "version": "1.0.0",
        "features": ["data_cleaning", "visualization_generation", "sentiment_analysis", "statistical_insights"]
    }
