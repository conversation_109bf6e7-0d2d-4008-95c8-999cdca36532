"""
LLM (Large Language Model) configuration settings.

This module provides LLM provider-specific configuration with validation
for AI model providers and their settings.
"""

import os
from typing import Dict, List, Optional, Any
from pydantic import Field, field_validator, computed_field
from .base import BaseConfig


class LLMProviderConfig(BaseConfig):
    """Configuration for a single LLM provider."""

    model_config = {"protected_namespaces": ()}  # Allow model_ fields

    name: str = Field(..., description="Provider name")
    api_key: str = Field(..., description="API key for the provider")
    endpoint: str = Field(..., description="API endpoint URL")
    enabled: bool = Field(default=True, description="Whether provider is enabled")
    priority: int = Field(default=1, ge=1, le=10, description="Provider priority (1=highest)")
    timeout: int = Field(default=30, ge=5, le=300, description="Request timeout in seconds")
    max_retries: int = Field(default=3, ge=0, le=10, description="Maximum retry attempts")
    retry_delay: float = Field(default=1.0, ge=0.1, le=10.0, description="Retry delay in seconds")

    # Rate limiting
    requests_per_minute: int = Field(default=60, ge=1, description="Requests per minute limit")
    requests_per_day: int = Field(default=1000, ge=1, description="Requests per day limit")

    # Model configuration
    default_model: Optional[str] = Field(default=None, description="Default model for this provider")
    available_models: List[str] = Field(default_factory=list, description="Available models")
    model_aliases: Dict[str, str] = Field(default_factory=dict, description="Model name aliases")

    # Provider-specific settings
    extra_headers: Dict[str, str] = Field(default_factory=dict, description="Extra HTTP headers")
    extra_params: Dict[str, Any] = Field(default_factory=dict, description="Extra request parameters")
    
    @field_validator('api_key')
    @classmethod
    def validate_api_key(cls, v: str) -> str:
        """Validate API key is not empty."""
        if not v or v.strip() == "":
            raise ValueError("API key cannot be empty")
        return v.strip()
    
    @field_validator('endpoint')
    @classmethod
    def validate_endpoint(cls, v: str) -> str:
        """Validate endpoint URL format."""
        if not v.startswith(('http://', 'https://')):
            raise ValueError("Endpoint must be a valid HTTP/HTTPS URL")
        return v.rstrip('/')


class LLMConfig(BaseConfig):
    """LLM configuration settings."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    # Default settings
    default_provider: str = Field(default="groq", description="Default LLM provider")
    fallback_providers: List[str] = Field(
        default=["groq", "openai"], description="Fallback provider chain"
    )
    
    # Model settings
    default_temperature: float = Field(
        default=0.7, ge=0.0, le=2.0, description="Default temperature for generation"
    )
    default_max_tokens: int = Field(
        default=2048, ge=1, le=32768, description="Default maximum tokens"
    )
    default_top_p: float = Field(
        default=1.0, ge=0.0, le=1.0, description="Default top-p sampling"
    )
    
    # Performance settings
    enable_caching: bool = Field(default=True, description="Enable response caching")
    cache_ttl_seconds: int = Field(default=3600, ge=60, description="Cache TTL in seconds")
    enable_streaming: bool = Field(default=True, description="Enable streaming responses")
    
    # Safety settings
    content_filter_enabled: bool = Field(default=True, description="Enable content filtering")
    max_context_length: int = Field(default=8192, ge=512, description="Maximum context length")
    
    # Monitoring
    log_requests: bool = Field(default=True, description="Log LLM requests")
    log_responses: bool = Field(default=False, description="Log LLM responses (may contain sensitive data)")
    track_usage: bool = Field(default=True, description="Track token usage")
    
    # Provider configurations
    providers: Dict[str, LLMProviderConfig] = Field(
        default_factory=dict, description="Provider configurations"
    )
    
    @field_validator('default_provider')
    @classmethod
    def validate_default_provider(cls, v: str) -> str:
        """Validate default provider name."""
        if not v:
            raise ValueError("Default provider cannot be empty")
        return v
    
    @field_validator('fallback_providers')
    @classmethod
    def validate_fallback_providers(cls, v: List[str]) -> List[str]:
        """Validate fallback providers list."""
        if not v:
            raise ValueError("Fallback providers list cannot be empty")
        return v
    
    @computed_field
    @property
    def enabled_providers(self) -> List[str]:
        """Get list of enabled provider names."""
        return [name for name, config in self.providers.items() if config.enabled]
    
    @computed_field
    @property
    def provider_priorities(self) -> Dict[str, int]:
        """Get provider priorities mapping."""
        return {name: config.priority for name, config in self.providers.items()}
    
    def get_provider_config(self, provider_name: str) -> Optional[LLMProviderConfig]:
        """Get configuration for a specific provider."""
        return self.providers.get(provider_name)
    
    def add_provider(self, provider_config: LLMProviderConfig) -> None:
        """Add a new provider configuration."""
        self.providers[provider_config.name] = provider_config
    
    @classmethod
    def from_env(cls) -> "LLMConfig":
        """Create LLM configuration from environment variables."""
        # Create provider configurations from environment
        providers = {}
        
        # Groq provider
        groq_api_key = os.getenv("GROQ_API_KEY", "")
        if groq_api_key:
            providers["groq"] = LLMProviderConfig(
                name="groq",
                api_key=groq_api_key,
                endpoint=os.getenv("GROQ_ENDPOINT", "https://api.groq.com/openai/v1"),
                priority=1,
                default_model="llama-3.1-8b-instant",
                available_models=["llama-3.1-8b-instant", "llama-3.1-70b-versatile", "mixtral-8x7b-32768"]
            )
        
        # OpenAI provider
        openai_api_key = os.getenv("OPENAI_API_KEY", "")
        if openai_api_key:
            providers["openai"] = LLMProviderConfig(
                name="openai",
                api_key=openai_api_key,
                endpoint=os.getenv("OPENAI_ENDPOINT", "https://api.openai.com/v1"),
                priority=2,
                default_model="gpt-3.5-turbo",
                available_models=["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
            )
        
        # Gemini provider
        gemini_api_key = os.getenv("GEMINI_API_KEY", "")
        if gemini_api_key:
            providers["gemini"] = LLMProviderConfig(
                name="gemini",
                api_key=gemini_api_key,
                endpoint=os.getenv("GEMINI_ENDPOINT", "https://generativelanguage.googleapis.com"),
                priority=3,
                default_model="gemini-pro",
                available_models=["gemini-pro", "gemini-pro-vision"]
            )
        
        # OpenRouter provider
        openrouter_api_key = os.getenv("OPENROUTER_API_KEY", "")
        if openrouter_api_key:
            providers["openrouter"] = LLMProviderConfig(
                name="openrouter",
                api_key=openrouter_api_key,
                endpoint=os.getenv("OPENROUTER_ENDPOINT", "https://openrouter.ai/api/v1"),
                priority=4,
                default_model="meta-llama/llama-3.1-8b-instruct:free"
            )
        
        # Ollama provider (local)
        ollama_endpoint = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
        providers["ollama"] = LLMProviderConfig(
            name="ollama",
            api_key="local",  # Ollama doesn't require API key
            endpoint=ollama_endpoint,
            priority=5,
            enabled=False,  # Disabled by default as it requires local setup
            default_model="llama3.1"
        )
        
        # Requesty provider
        requesty_api_key = os.getenv("REQUESTY_API_KEY", "")
        if requesty_api_key:
            providers["requesty"] = LLMProviderConfig(
                name="requesty",
                api_key=requesty_api_key,
                endpoint=os.getenv("REQUESTY_ENDPOINT", "https://router.requesty.ai/v1"),
                priority=6,
                default_model="mistral-saba-24b"
            )
        
        return cls(
            default_provider=os.getenv("DEFAULT_LLM_PROVIDER", "groq"),
            fallback_providers=os.getenv("FALLBACK_LLM_PROVIDERS", "groq,openai").split(","),
            default_temperature=float(os.getenv("DEFAULT_TEMPERATURE", "0.7")),
            default_max_tokens=int(os.getenv("DEFAULT_MAX_TOKENS", "2048")),
            default_top_p=float(os.getenv("DEFAULT_TOP_P", "1.0")),
            enable_caching=os.getenv("LLM_ENABLE_CACHING", "true").lower() == "true",
            cache_ttl_seconds=int(os.getenv("LLM_CACHE_TTL_SECONDS", "3600")),
            enable_streaming=os.getenv("LLM_ENABLE_STREAMING", "true").lower() == "true",
            content_filter_enabled=os.getenv("LLM_CONTENT_FILTER_ENABLED", "true").lower() == "true",
            max_context_length=int(os.getenv("LLM_MAX_CONTEXT_LENGTH", "8192")),
            log_requests=os.getenv("LLM_LOG_REQUESTS", "true").lower() == "true",
            log_responses=os.getenv("LLM_LOG_RESPONSES", "false").lower() == "true",
            track_usage=os.getenv("LLM_TRACK_USAGE", "true").lower() == "true",
            providers=providers
        )
