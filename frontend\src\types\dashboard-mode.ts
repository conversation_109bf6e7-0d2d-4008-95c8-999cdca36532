/**
 * Dashboard Mode Types and Interfaces
 * 
 * Defines the dual-mode interface system for dashboards with manual mode control,
 * persistent user preferences, and mode-aware component rendering.
 */

export type DashboardMode = 'simple' | 'advanced';

export interface ModeTransition {
  from_mode: DashboardMode;
  to_mode: DashboardMode;
  timestamp: string;
  trigger: 'user_action' | 'system_suggestion' | 'auto_switch';
  context?: Record<string, any>;
}

export interface UserModePreferences {
  default_mode: DashboardMode;
  remember_choice: boolean;
  show_mode_hints: boolean;
  auto_switch_enabled: boolean;
  simple_mode_features: {
    show_ai_assistant: boolean;
    enable_guided_workflows: boolean;
    show_template_gallery: boolean;
    enable_smart_suggestions: boolean;
    use_conversational_interface: boolean;
  };
  advanced_mode_features: {
    show_ribbon_toolbar: boolean;
    enable_technical_controls: boolean;
    show_code_editor: boolean;
    enable_custom_visualizations: boolean;
    show_performance_metrics: boolean;
  };
}

export interface ModeState {
  current_mode: DashboardMode;
  user_preferences: UserModePreferences;
  mode_history: ModeTransition[];
  is_switching: boolean;
  last_mode_change: string | null;
  mode_capabilities: {
    simple: string[];
    advanced: string[];
  };
}

export interface ModeContextData {
  user_expertise_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  dashboard_complexity: 'basic' | 'moderate' | 'complex' | 'enterprise';
  current_task: string | null;
  available_features: string[];
  ai_assistant_active: boolean;
}

export interface AIAssistantModeConfig {
  communication_style: 'conversational' | 'technical' | 'business';
  explanation_level: 'basic' | 'detailed' | 'expert';
  suggestion_frequency: 'minimal' | 'moderate' | 'frequent';
  proactive_assistance: boolean;
  context_awareness: boolean;
}

export interface SimpleModeConfig {
  ai_assistant: AIAssistantModeConfig;
  template_gallery: {
    show_industry_templates: boolean;
    show_use_case_templates: boolean;
    enable_template_preview: boolean;
  };
  guided_workflows: {
    enable_step_by_step: boolean;
    show_progress_indicators: boolean;
    provide_contextual_help: boolean;
  };
  widget_creation: {
    use_natural_language: boolean;
    show_smart_suggestions: boolean;
    enable_one_click_creation: boolean;
  };
}

export interface AdvancedModeConfig {
  ribbon_toolbar: {
    show_all_tabs: boolean;
    enable_customization: boolean;
    show_keyboard_shortcuts: boolean;
  };
  technical_controls: {
    show_raw_data_access: boolean;
    enable_custom_queries: boolean;
    show_performance_metrics: boolean;
  };
  ai_assistant: AIAssistantModeConfig;
  customization: {
    enable_custom_components: boolean;
    show_code_editor: boolean;
    enable_api_access: boolean;
  };
}

export interface ModeComponentProps {
  mode: DashboardMode;
  mode_config: SimpleModeConfig | AdvancedModeConfig;
  on_mode_change: (new_mode: DashboardMode) => void;
  context_data: ModeContextData;
}

export interface ModeIndicatorProps {
  current_mode: DashboardMode;
  is_switching: boolean;
  on_toggle: () => void;
  show_hints: boolean;
}

export interface ModeTransitionProps {
  from_mode: DashboardMode;
  to_mode: DashboardMode;
  on_confirm: () => void;
  on_cancel: () => void;
  preserve_data: boolean;
}

// Default configurations
export const DEFAULT_USER_MODE_PREFERENCES: UserModePreferences = {
  default_mode: 'simple',
  remember_choice: true,
  show_mode_hints: true,
  auto_switch_enabled: false,
  simple_mode_features: {
    show_ai_assistant: true,
    enable_guided_workflows: true,
    show_template_gallery: true,
    enable_smart_suggestions: true,
    use_conversational_interface: true,
  },
  advanced_mode_features: {
    show_ribbon_toolbar: true,
    enable_technical_controls: true,
    show_code_editor: false,
    enable_custom_visualizations: true,
    show_performance_metrics: false,
  },
};

export const DEFAULT_SIMPLE_MODE_CONFIG: SimpleModeConfig = {
  ai_assistant: {
    communication_style: 'conversational',
    explanation_level: 'basic',
    suggestion_frequency: 'moderate',
    proactive_assistance: true,
    context_awareness: true,
  },
  template_gallery: {
    show_industry_templates: true,
    show_use_case_templates: true,
    enable_template_preview: true,
  },
  guided_workflows: {
    enable_step_by_step: true,
    show_progress_indicators: true,
    provide_contextual_help: true,
  },
  widget_creation: {
    use_natural_language: true,
    show_smart_suggestions: true,
    enable_one_click_creation: true,
  },
};

export const DEFAULT_ADVANCED_MODE_CONFIG: AdvancedModeConfig = {
  ribbon_toolbar: {
    show_all_tabs: true,
    enable_customization: true,
    show_keyboard_shortcuts: true,
  },
  technical_controls: {
    show_raw_data_access: true,
    enable_custom_queries: true,
    show_performance_metrics: true,
  },
  ai_assistant: {
    communication_style: 'technical',
    explanation_level: 'detailed',
    suggestion_frequency: 'minimal',
    proactive_assistance: false,
    context_awareness: true,
  },
  customization: {
    enable_custom_components: true,
    show_code_editor: true,
    enable_api_access: true,
  },
};
