"""
SQLAlchemy models for the Knowledge Graph.
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, ForeignKey, JSON, Text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from ..database import Base


class KGEntity(Base):
    """
    Represents an entity (node) in the Knowledge Graph.
    """
    __tablename__ = "kg_entities"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    entity_type = Column(String, index=True, nullable=False) # e.g., 'Person', 'Organization', 'Topic'
    name = Column(String, index=True, nullable=False)
    properties = Column(JSON) # Flexible storage for entity attributes
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships where this entity is the source
    source_relationships = relationship("KGRelationship", foreign_keys="[KGRelationship.source_entity_id]", back_populates="source_entity", cascade="all, delete-orphan")
    # Relationships where this entity is the target
    target_relationships = relationship("KGRelationship", foreign_keys="[KGRelationship.target_entity_id]", back_populates="target_entity", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<KGEntity(id={self.id}, type='{self.entity_type}', name='{self.name}')>"


class KGRelationship(Base):
    """
    Represents a relationship (edge) between two entities in the Knowledge Graph.
    """
    __tablename__ = "kg_relationships"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    relationship_type = Column(String, index=True, nullable=False) # e.g., 'WORKS_AT', 'RELATED_TO', 'MENTIONS'
    source_entity_id = Column(UUID(as_uuid=True), ForeignKey("kg_entities.id"), nullable=False, index=True)
    target_entity_id = Column(UUID(as_uuid=True), ForeignKey("kg_entities.id"), nullable=False, index=True)
    properties = Column(JSON) # Flexible storage for relationship attributes (e.g., weight, date range)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define relationships to the KGEntity model
    source_entity = relationship("KGEntity", foreign_keys=[source_entity_id], back_populates="source_relationships")
    target_entity = relationship("KGEntity", foreign_keys=[target_entity_id], back_populates="target_relationships")

    def __repr__(self):
        return f"<KGRelationship(id={self.id}, type='{self.relationship_type}', source={self.source_entity_id}, target={self.target_entity_id})>"
