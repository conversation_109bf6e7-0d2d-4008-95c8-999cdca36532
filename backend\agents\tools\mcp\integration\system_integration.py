"""
System Integration Module for MCP Tools.

This module provides comprehensive integration of all MCP tool components including
caching, monitoring, error handling, security, validation, analytics, and testing.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Type, Callable
from dataclasses import dataclass
from datetime import datetime
import json

from ..base import BaseMCPTool
from ..caching.intelligent_cache import get_cache, cache_result
from ..monitoring.performance_monitor import get_performance_monitor
from ..error_handling.robust_error_handler import get_error_handler, ErrorContext
from ..security.access_control import get_access_controller, SecurityContext, PermissionLevel
from ..validation.input_validator import get_input_validator
from ..analytics.usage_analytics import get_usage_analytics, track_tool_usage
from ..testing.test_framework import get_test_framework
from ..composition.tool_chain import Too<PERSON><PERSON>hai<PERSON>, ChainBuilder

logger = logging.getLogger(__name__)


@dataclass
class IntegrationConfig:
    """Configuration for system integration."""
    enable_caching: bool = True
    enable_monitoring: bool = True
    enable_error_handling: bool = True
    enable_security: bool = True
    enable_validation: bool = True
    enable_analytics: bool = True
    enable_testing: bool = False  # Usually disabled in production
    
    # Performance settings
    cache_ttl_seconds: int = 3600
    max_retry_attempts: int = 3
    request_timeout_seconds: int = 30
    
    # Security settings
    require_authentication: bool = True
    enable_rate_limiting: bool = True
    log_security_events: bool = True
    
    # Analytics settings
    track_performance: bool = True
    track_usage_patterns: bool = True
    retention_days: int = 90


class EnhancedMCPTool(BaseMCPTool):
    """
    Enhanced MCP Tool with full system integration.
    
    This class wraps any MCP tool with comprehensive features:
    - Intelligent caching
    - Performance monitoring
    - Error handling and recovery
    - Security and access control
    - Input validation
    - Usage analytics
    - Agent awareness
    """
    
    def __init__(
        self,
        base_tool: BaseMCPTool,
        config: Optional[IntegrationConfig] = None
    ):
        """
        Initialize enhanced MCP tool.
        
        Args:
            base_tool: The base MCP tool to enhance
            config: Integration configuration
        """
        # Initialize base class
        super().__init__(
            name=base_tool.name,
            description=base_tool.description,
            input_schema=base_tool.input_schema
        )
        
        self.base_tool = base_tool
        self.config = config or IntegrationConfig()
        
        # Initialize system components
        self.cache = get_cache() if self.config.enable_caching else None
        self.monitor = get_performance_monitor() if self.config.enable_monitoring else None
        self.error_handler = get_error_handler() if self.config.enable_error_handling else None
        self.access_controller = get_access_controller() if self.config.enable_security else None
        self.validator = get_input_validator() if self.config.enable_validation else None
        self.analytics = get_usage_analytics() if self.config.enable_analytics else None
        
        logger.info(f"Enhanced MCP tool initialized: {self.name}")
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute tool with full system integration.

        Args:
            arguments: Tool arguments

        Returns:
            Tool execution result
        """
        start_time = datetime.now()
        execution_context = {
            "tool_name": self.name,
            "start_time": start_time,
            "arguments": arguments
        }
        
        try:
            # 1. Extract security context
            security_context = self._extract_security_context(arguments)
            execution_context["security_context"] = security_context
            
            # 2. Security and access control check
            if self.config.enable_security and self.access_controller:
                access_granted, access_info = await self.access_controller.check_access(
                    tool_name=self.name,
                    security_context=security_context,
                    input_data=arguments
                )
                
                if not access_granted:
                    return self._create_error_response(
                        "Access denied",
                        access_info.get("reason", "Insufficient permissions"),
                        {"access_info": access_info}
                    )
                
                execution_context["access_info"] = access_info
            
            # 3. Input validation
            if self.config.enable_validation and self.validator:
                validation_result = self.validator.validate_input(self.name, arguments)

                if not validation_result.is_valid:
                    return self._create_error_response(
                        "Validation failed",
                        "; ".join(validation_result.errors),
                        {"validation_errors": validation_result.errors}
                    )

                # Use validated data
                arguments = validation_result.validated_data or arguments
                execution_context["validation_result"] = validation_result
            
            # 4. Check cache
            cached_result = None
            if self.config.enable_caching and self.cache:
                cached_result = await self.cache.get(
                    tool_name=self.name,
                    operation="execute",
                    arguments=arguments,
                    agent_identity=security_context.agent_identity if security_context else None
                )
                
                if cached_result:
                    execution_context["cache_hit"] = True
                    await self._track_execution(execution_context, cached_result, True)
                    return cached_result
            
            execution_context["cache_hit"] = False
            
            # 5. Execute with monitoring and error handling
            result = await self._execute_with_monitoring(arguments, execution_context)
            
            # 6. Cache successful results
            if (self.config.enable_caching and self.cache and 
                not result.get("isError") and cached_result is None):
                await self.cache.set(
                    tool_name=self.name,
                    operation="execute",
                    arguments=arguments,
                    value=result,
                    ttl=self.config.cache_ttl_seconds,
                    agent_identity=security_context.agent_identity if security_context else None
                )
            
            # 7. Track usage analytics
            await self._track_execution(execution_context, result, False)
            
            return result
            
        except Exception as e:
            # Handle unexpected errors
            error_context = ErrorContext(
                tool_name=self.name,
                operation="execute",
                agent_identity=security_context.agent_identity if 'security_context' in execution_context else None,
                input_data=arguments
            )
            
            if self.config.enable_error_handling and self.error_handler:
                recovery_result = await self.error_handler.handle_error(e, error_context)
                
                if recovery_result.success:
                    await self._track_execution(execution_context, recovery_result.result, False)
                    return recovery_result.result
                else:
                    error_response = self._create_error_response(
                        "Tool execution failed",
                        recovery_result.error.user_message,
                        {
                            "error_id": recovery_result.error.error_id,
                            "recovery_actions": recovery_result.recovery_actions_taken
                        }
                    )
                    await self._track_execution(execution_context, error_response, False)
                    return error_response
            else:
                # Basic error response
                error_response = self._create_error_response(
                    "Tool execution failed",
                    str(e),
                    {"error_type": type(e).__name__}
                )
                await self._track_execution(execution_context, error_response, False)
                return error_response
    
    async def _execute_with_monitoring(
        self,
        arguments: Dict[str, Any],
        execution_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute tool with performance monitoring."""
        if self.config.enable_monitoring and self.monitor:
            # Extract agent identity from security context properly
            security_context = execution_context.get("security_context")
            agent_identity = security_context.agent_identity if security_context else None

            async with self.monitor.monitor_execution(
                tool_name=self.name,
                agent_identity=agent_identity,
                input_data=arguments
            ):
                result = await self.base_tool.execute(arguments)
                return result
        else:
            return await self.base_tool.execute(arguments)
    
    def _extract_security_context(self, arguments: Dict[str, Any]) -> SecurityContext:
        """Extract security context from arguments."""
        return SecurityContext(
            user_id=arguments.get("user_id"),
            agent_identity=arguments.get("agent_identity") or arguments.get("persona_id"),
            ip_address=arguments.get("ip_address"),
            user_agent=arguments.get("user_agent"),
            session_id=arguments.get("session_id"),
            request_id=arguments.get("request_id"),
            authentication_method=arguments.get("auth_method")
        )
    
    async def _track_execution(
        self,
        execution_context: Dict[str, Any],
        result: Dict[str, Any],
        cache_hit: bool
    ):
        """Track tool execution for analytics."""
        if not self.config.enable_analytics or not self.analytics:
            return
        
        execution_time = (datetime.now() - execution_context["start_time"]).total_seconds()
        success = not result.get("isError", False)
        
        security_context = execution_context.get("security_context")
        
        await track_tool_usage(
            tool_name=self.name,
            agent_identity=security_context.agent_identity if security_context else None,
            execution_time=execution_time,
            success=success,
            user_id=security_context.user_id if security_context else None,
            input_size=len(json.dumps(execution_context["arguments"], default=str)),
            output_size=len(json.dumps(result, default=str)),
            error_type=result.get("metadata", {}).get("error_type") if not success else None,
            context={
                "cache_hit": cache_hit,
                "validation_performed": "validation_result" in execution_context,
                "security_checked": "access_info" in execution_context
            }
        )
    
    def _create_error_response(
        self,
        error_type: str,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "isError": True,
            "content": [{
                "type": "text",
                "text": message
            }],
            "metadata": {
                "error_type": error_type,
                "tool_name": self.name,
                "timestamp": datetime.now().isoformat(),
                **(metadata or {})
            }
        }


class MCPToolRegistry:
    """Registry for managing enhanced MCP tools."""
    
    def __init__(self, config: Optional[IntegrationConfig] = None):
        """Initialize tool registry."""
        self.config = config or IntegrationConfig()
        self.tools: Dict[str, EnhancedMCPTool] = {}
        self.tool_chains: Dict[str, ToolChain] = {}
        
        logger.info("MCP Tool Registry initialized")
    
    def register_tool(self, base_tool: BaseMCPTool) -> EnhancedMCPTool:
        """Register and enhance a tool."""
        enhanced_tool = EnhancedMCPTool(base_tool, self.config)
        self.tools[base_tool.name] = enhanced_tool
        
        logger.info(f"Registered enhanced tool: {base_tool.name}")
        return enhanced_tool
    
    def register_tool_chain(self, chain_id: str, tool_chain: ToolChain):
        """Register a tool chain."""
        self.tool_chains[chain_id] = tool_chain
        logger.info(f"Registered tool chain: {chain_id}")
    
    def get_tool(self, tool_name: str) -> Optional[EnhancedMCPTool]:
        """Get enhanced tool by name."""
        return self.tools.get(tool_name)
    
    def get_tool_chain(self, chain_id: str) -> Optional[ToolChain]:
        """Get tool chain by ID."""
        return self.tool_chains.get(chain_id)
    
    def list_tools(self) -> List[str]:
        """List all registered tool names."""
        return list(self.tools.keys())
    
    def list_tool_chains(self) -> List[str]:
        """List all registered tool chain IDs."""
        return list(self.tool_chains.keys())
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool by name."""
        tool = self.get_tool(tool_name)
        if not tool:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Tool '{tool_name}' not found"}],
                "metadata": {"error_type": "tool_not_found"}
            }
        
        return await tool.execute(arguments)
    
    async def execute_tool_chain(
        self,
        chain_id: str,
        initial_input: Optional[Dict[str, Any]] = None,
        agent_identity: Optional[str] = None
    ) -> Dict[str, Any]:
        """Execute a tool chain by ID."""
        chain = self.get_tool_chain(chain_id)
        if not chain:
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Tool chain '{chain_id}' not found"}],
                "metadata": {"error_type": "chain_not_found"}
            }
        
        result = await chain.execute(initial_input, agent_identity)
        
        return {
            "content": [{"type": "text", "text": "Tool chain executed successfully"}],
            "metadata": {
                "chain_id": result.chain_id,
                "success": result.success,
                "steps_executed": result.steps_executed,
                "total_execution_time": result.total_execution_time,
                "results": result.results
            }
        }
    
    async def run_system_tests(self) -> Dict[str, Any]:
        """Run comprehensive system tests."""
        if not self.config.enable_testing:
            return {"error": "Testing is disabled in configuration"}
        
        test_framework = get_test_framework()
        
        # Convert enhanced tools to base tools for testing
        base_tools = [tool.base_tool for tool in self.tools.values()]
        
        # Run all tests
        test_results = await test_framework.run_all_tests(base_tools)
        
        # Generate report
        report = test_framework.generate_test_report()
        
        return {
            "test_results": test_results,
            "report": report,
            "timestamp": datetime.now().isoformat()
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        status = {
            "timestamp": datetime.now().isoformat(),
            "tools": {
                "registered": len(self.tools),
                "tool_names": list(self.tools.keys())
            },
            "tool_chains": {
                "registered": len(self.tool_chains),
                "chain_ids": list(self.tool_chains.keys())
            },
            "configuration": {
                "caching_enabled": self.config.enable_caching,
                "monitoring_enabled": self.config.enable_monitoring,
                "security_enabled": self.config.enable_security,
                "analytics_enabled": self.config.enable_analytics
            }
        }
        
        # Add component status
        if self.config.enable_monitoring:
            monitor = get_performance_monitor()
            status["performance"] = monitor.get_performance_summary()
        
        if self.config.enable_analytics:
            analytics = get_usage_analytics()
            status["analytics"] = analytics.get_usage_insights()
        
        if self.config.enable_security:
            access_controller = get_access_controller()
            status["security"] = access_controller.get_security_statistics()
        
        if self.config.enable_caching:
            cache = get_cache()
            status["cache"] = cache.get_stats()
        
        return status
    
    async def cleanup(self):
        """Cleanup system resources."""
        logger.info("Starting system cleanup...")
        
        # Cleanup components
        if self.config.enable_caching:
            cache = get_cache()
            await cache.cleanup()
        
        if self.config.enable_monitoring:
            monitor = get_performance_monitor()
            await monitor.cleanup()
        
        if self.config.enable_analytics:
            analytics = get_usage_analytics()
            await analytics.cleanup_old_data()
        
        if self.config.enable_testing:
            test_framework = get_test_framework()
            test_framework.cleanup()
        
        logger.info("System cleanup completed")


# Global registry instance
_global_registry: Optional[MCPToolRegistry] = None


def get_tool_registry(config: Optional[IntegrationConfig] = None) -> MCPToolRegistry:
    """Get global tool registry instance."""
    global _global_registry
    if _global_registry is None:
        _global_registry = MCPToolRegistry(config)
    return _global_registry


def create_production_config() -> IntegrationConfig:
    """Create production-ready configuration."""
    return IntegrationConfig(
        enable_caching=True,
        enable_monitoring=True,
        enable_error_handling=True,
        enable_security=True,
        enable_validation=True,
        enable_analytics=True,
        enable_testing=False,  # Disabled in production
        
        cache_ttl_seconds=1800,  # 30 minutes
        max_retry_attempts=3,
        request_timeout_seconds=60,
        
        require_authentication=True,
        enable_rate_limiting=True,
        log_security_events=True,
        
        track_performance=True,
        track_usage_patterns=True,
        retention_days=30  # Shorter retention for production
    )


def create_development_config() -> IntegrationConfig:
    """Create development-friendly configuration."""
    return IntegrationConfig(
        enable_caching=True,
        enable_monitoring=True,
        enable_error_handling=False,  # Disabled for development to see real errors
        enable_security=False,  # Relaxed for development
        enable_validation=True,
        enable_analytics=True,
        enable_testing=True,  # Enabled for development

        cache_ttl_seconds=300,  # 5 minutes
        max_retry_attempts=1,
        request_timeout_seconds=30,

        require_authentication=False,
        enable_rate_limiting=False,
        log_security_events=False,

        track_performance=True,
        track_usage_patterns=True,
        retention_days=7  # Shorter retention for development
    )
