import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Plus,
  Edit,
  Copy,
  Trash2,
  MoreVertical,
  RefreshCw,
  Settings,
  BarChart3,
  Table,
  Gauge,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Activity,
  Eye,
  EyeOff
} from 'lucide-react';
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';
import { useToast } from '@/hooks/use-toast';
import { WidgetResponse, VisualizationType } from '@/types/dashboard-customization';
import { WidgetEditor } from './WidgetEditor';
import { AddWidgetDialog } from './AddWidgetDialog';

interface WidgetManagementPanelProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const getWidgetIcon = (type: VisualizationType) => {
  switch (type) {
    case VisualizationType.CHART:
      return BarChart3;
    case VisualizationType.TABLE:
      return Table;
    case VisualizationType.KPI:
      return Gauge;
    case VisualizationType.PIE_CHART:
      return PieChart;
    case VisualizationType.LINE_CHART:
      return LineChart;
    default:
      return Activity;
  }
};

const getWidgetTypeLabel = (type: VisualizationType) => {
  switch (type) {
    case VisualizationType.CHART:
      return 'Chart';
    case VisualizationType.TABLE:
      return 'Table';
    case VisualizationType.KPI:
      return 'KPI';
    case VisualizationType.PIE_CHART:
      return 'Pie Chart';
    case VisualizationType.LINE_CHART:
      return 'Line Chart';
    default:
      return 'Widget';
  }
};

export const WidgetManagementPanel: React.FC<WidgetManagementPanelProps> = ({
  open,
  onOpenChange,
}) => {
  const { toast } = useToast();
  const [selectedWidget, setSelectedWidget] = useState<WidgetResponse | null>(null);
  const [showWidgetEditor, setShowWidgetEditor] = useState(false);
  const [showAddWidget, setShowAddWidget] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [widgetToDelete, setWidgetToDelete] = useState<WidgetResponse | null>(null);
  const [selectedSectionForWidget, setSelectedSectionForWidget] = useState<string | null>(null);
  const [showSectionSelector, setShowSectionSelector] = useState(false);

  const {
    currentLayout,
    updateWidget,
    deleteWidget,
    createWidget,
    refreshWidget,
  } = useUnifiedDashboardStore();

  const widgets = currentLayout?.widgets || [];

  const handleEditWidget = (widget: WidgetResponse) => {
    setSelectedWidget(widget);
    setShowWidgetEditor(true);
  };

  const handleDuplicateWidget = async (widget: WidgetResponse) => {
    try {
      const duplicatedWidget = {
        ...widget,
        title: `${widget.title} (Copy)`,
        position_config: {
          ...widget.position_config,
          x: widget.position_config.x + 1,
          y: widget.position_config.y + 1,
        },
      };
      
      await createWidget(duplicatedWidget);
      toast({
        title: "Widget Duplicated",
        description: "Widget has been duplicated successfully.",
      });
    } catch (error) {
      toast({
        title: "Duplication Failed",
        description: "Failed to duplicate widget.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteWidget = (widget: WidgetResponse) => {
    setWidgetToDelete(widget);
    setShowDeleteDialog(true);
  };

  const confirmDeleteWidget = async () => {
    if (!widgetToDelete) return;

    try {
      await deleteWidget(widgetToDelete.id);
      setShowDeleteDialog(false);
      setWidgetToDelete(null);
      toast({
        title: "Widget Deleted",
        description: "Widget has been deleted successfully.",
      });
    } catch (error) {
      toast({
        title: "Deletion Failed",
        description: "Failed to delete widget.",
        variant: "destructive",
      });
    }
  };

  const handleToggleWidget = async (widget: WidgetResponse) => {
    try {
      await updateWidget(widget.id, { is_active: !widget.is_active });
      toast({
        title: widget.is_active ? "Widget Hidden" : "Widget Shown",
        description: `Widget has been ${widget.is_active ? 'hidden' : 'shown'}.`,
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update widget visibility.",
        variant: "destructive",
      });
    }
  };

  const handleRefreshWidget = async (widget: WidgetResponse) => {
    try {
      await refreshWidget(widget.id);
      toast({
        title: "Widget Refreshed",
        description: "Widget data has been refreshed.",
      });
    } catch (error) {
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh widget data.",
        variant: "destructive",
      });
    }
  };

  const handleWidgetAdded = async (widgetData: any) => {
    if (!selectedSectionForWidget) return;

    try {
      await createWidget({
        section_id: selectedSectionForWidget,
        title: widgetData.title,
        widget_type: widgetData.type,
        data_config: widgetData.dataConfig,
        visualization_config: widgetData.visualizationConfig,
        position_config: {
          x: 0,
          y: 0,
          w: widgetData.width || 4,
          h: widgetData.height || 4,
        },
        customization: widgetData.customization,
        refresh_interval: 300,
      });

      toast({
        title: "Widget Added",
        description: "New widget has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create widget.",
        variant: "destructive",
      });
    }
  };

  const handleSaveWidget = async (widgetId: string, updates: Partial<WidgetResponse>) => {
    await updateWidget(widgetId, updates);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Plus className="h-5 w-5" />
                <span>Add & Manage Widgets</span>
                <Badge variant="secondary">{widgets.length} widgets</Badge>
              </div>
              <Button onClick={() => {
                if (currentLayout?.sections.length === 1) {
                  setSelectedSectionForWidget(currentLayout.sections[0].id);
                  setShowAddWidget(true);
                } else {
                  setShowSectionSelector(true);
                }
              }} className="bg-primary text-primary-foreground hover:bg-primary/90">
                <Plus className="h-4 w-4 mr-2" />
                Add Widget
              </Button>
            </DialogTitle>
            <DialogDescription>
              Add new widgets to your dashboard or manage existing ones. Edit, duplicate, or delete widgets as needed.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {widgets.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No widgets yet</h3>
                  <p className="text-muted-foreground text-center mb-4">
                    Start building your dashboard by adding your first widget.
                  </p>
                  <Button onClick={() => {
                    if (currentLayout?.sections.length === 1) {
                      setSelectedSectionForWidget(currentLayout.sections[0].id);
                      setShowAddWidget(true);
                    } else {
                      setShowSectionSelector(true);
                    }
                  }}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Widget
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4">
                {widgets.map((widget) => {
                  const IconComponent = getWidgetIcon(widget.widget_type);
                  return (
                    <Card key={widget.id} className={!widget.is_active ? 'opacity-60' : ''}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <IconComponent className="h-5 w-5 text-primary" />
                            <div>
                              <CardTitle className="text-base">{widget.title}</CardTitle>
                              <CardDescription>
                                {getWidgetTypeLabel(widget.widget_type)} • 
                                Section: {currentLayout?.sections.find(s => s.id === widget.section_id)?.name || 'Unknown'}
                              </CardDescription>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {!widget.is_active && (
                              <Badge variant="secondary">Hidden</Badge>
                            )}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleEditWidget(widget)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit Widget
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDuplicateWidget(widget)}>
                                  <Copy className="h-4 w-4 mr-2" />
                                  Duplicate
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleRefreshWidget(widget)}>
                                  <RefreshCw className="h-4 w-4 mr-2" />
                                  Refresh Data
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleToggleWidget(widget)}>
                                  {widget.is_active ? (
                                    <>
                                      <EyeOff className="h-4 w-4 mr-2" />
                                      Hide Widget
                                    </>
                                  ) : (
                                    <>
                                      <Eye className="h-4 w-4 mr-2" />
                                      Show Widget
                                    </>
                                  )}
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => handleDeleteWidget(widget)}
                                  className="text-destructive"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete Widget
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <span>
                            Position: {widget.position_config.x}, {widget.position_config.y}
                          </span>
                          <span>
                            Size: {widget.position_config.w} × {widget.position_config.h}
                          </span>
                          <span>
                            Refresh: {widget.refresh_interval}s
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Widget Editor Dialog */}
      <WidgetEditor
        open={showWidgetEditor}
        onOpenChange={setShowWidgetEditor}
        widget={selectedWidget}
        onSave={handleSaveWidget}
      />

      {/* Add Widget Dialog */}
      {selectedSectionForWidget && (
        <AddWidgetDialog
          open={showAddWidget}
          onOpenChange={setShowAddWidget}
          onWidgetAdded={handleWidgetAdded}
          availableDataSources={currentLayout?.dashboard?.data_source_assignments || []}
          sectionId={selectedSectionForWidget}
        />
      )}

      {/* Section Selector Dialog */}
      <Dialog open={showSectionSelector} onOpenChange={setShowSectionSelector}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Select Section</DialogTitle>
            <DialogDescription>
              Choose which section to add the widget to.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-3 py-4">
            {currentLayout?.sections.map((section) => (
              <Button
                key={section.id}
                variant="outline"
                className="justify-start h-auto p-4"
                onClick={() => {
                  setSelectedSectionForWidget(section.id);
                  setShowAddWidget(true);
                  setShowSectionSelector(false);
                }}
              >
                <div className="flex items-center space-x-3">
                  <div className="h-5 w-5" style={{ color: section.color }}>
                    {section.icon && React.createElement(
                      {
                        'chart': BarChart3,
                        'table': Table,
                        'gauge': Gauge,
                        'pie': PieChart,
                        'line': LineChart,
                        'activity': Activity,
                      }[section.icon] || BarChart3
                    )}
                  </div>
                  <div className="text-left">
                    <div className="font-medium">{section.name}</div>
                    {section.description && (
                      <div className="text-sm text-muted-foreground">{section.description}</div>
                    )}
                  </div>
                  <Badge variant="outline" className="ml-auto">
                    {currentLayout.widgets.filter(w => w.section_id === section.id).length} widgets
                  </Badge>
                </div>
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Widget</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{widgetToDelete?.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteWidget} className="bg-destructive text-destructive-foreground">
              Delete Widget
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
