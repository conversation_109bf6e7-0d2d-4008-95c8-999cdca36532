/**
 * Floating AI Assistant Component Tests
 * 
 * Tests for the floating modal AI assistant functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { FloatingAIAssistant } from '../FloatingAIAssistant';
import { useDashboardMode } from '@/stores/dashboard-mode-store';

// Mock the dashboard mode store
jest.mock('@/stores/dashboard-mode-store');
const mockUseDashboardMode = useDashboardMode as jest.MockedFunction<typeof useDashboardMode>;

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

describe('FloatingAIAssistant', () => {
  const defaultProps = {
    isOpen: true,
    onOpenChange: jest.fn(),
    onWidgetCreate: jest.fn(),
    onSectionCreate: jest.fn(),
    onTemplateApply: jest.fn(),
    onDataConfigure: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDashboardMode.mockReturnValue({
      current_mode: 'simple',
      toggle_mode: jest.fn(),
      can_switch: true,
      is_switching: false,
    });
  });

  it('renders when open', () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    expect(screen.getByText('Datagenius AI')).toBeInTheDocument();
    expect(screen.getByText('Simple Mode')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<FloatingAIAssistant {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Datagenius AI')).not.toBeInTheDocument();
  });

  it('displays welcome message on mount', () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    expect(screen.getByText(/Hi! I'm your Datagenius AI assistant/)).toBeInTheDocument();
  });

  it('shows different welcome message for advanced mode', () => {
    mockUseDashboardMode.mockReturnValue({
      current_mode: 'advanced',
      toggle_mode: jest.fn(),
      can_switch: true,
      is_switching: false,
    });

    render(<FloatingAIAssistant {...defaultProps} />);
    
    expect(screen.getByText(/Welcome to Datagenius Pro/)).toBeInTheDocument();
    expect(screen.getByText('Advanced Mode')).toBeInTheDocument();
  });

  it('handles minimize functionality', () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    const minimizeButton = screen.getByRole('button', { name: /minimize/i });
    fireEvent.click(minimizeButton);
    
    // Should show minimized state
    expect(screen.getByRole('button')).toHaveClass('rounded-full');
  });

  it('handles close functionality', () => {
    const onOpenChange = jest.fn();
    render(<FloatingAIAssistant {...defaultProps} onOpenChange={onOpenChange} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    expect(onOpenChange).toHaveBeenCalledWith(false);
  });

  it('sends messages when input is provided', async () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    const input = screen.getByPlaceholderText(/Ask me anything about your dashboard/);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Create a chart' } });
    fireEvent.click(sendButton);
    
    // Should show user message
    expect(screen.getByText('Create a chart')).toBeInTheDocument();
    
    // Should show loading state
    expect(screen.getByText('Thinking...')).toBeInTheDocument();
    
    // Wait for AI response
    await waitFor(() => {
      expect(screen.getByText(/I can help you create various types of widgets/)).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('handles suggestion clicks', () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    // Click on a suggestion from the welcome message
    const suggestion = screen.getByText('Create a chart widget');
    fireEvent.click(suggestion);
    
    // Should populate the input field
    const input = screen.getByPlaceholderText(/Ask me anything about your dashboard/);
    expect(input).toHaveValue('Create a chart widget');
  });

  it('handles action button clicks', async () => {
    const onWidgetCreate = jest.fn();
    render(<FloatingAIAssistant {...defaultProps} onWidgetCreate={onWidgetCreate} />);
    
    // Send a message that triggers widget creation actions
    const input = screen.getByPlaceholderText(/Ask me anything about your dashboard/);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Create a bar chart' } });
    fireEvent.click(sendButton);
    
    // Wait for AI response with action buttons
    await waitFor(() => {
      const createBarChartButton = screen.getByText('Create Bar Chart');
      fireEvent.click(createBarChartButton);
      
      expect(onWidgetCreate).toHaveBeenCalledWith({ type: 'bar_chart' });
    }, { timeout: 2000 });
  });

  it('handles voice input toggle', () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    const voiceButton = screen.getByRole('button', { name: /mic/i });
    fireEvent.click(voiceButton);
    
    // Should toggle voice input state (visual feedback)
    expect(voiceButton).toHaveClass('bg-red-100');
  });

  it('clears messages when refresh is clicked', () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    // Should have welcome message initially
    expect(screen.getByText(/Hi! I'm your Datagenius AI assistant/)).toBeInTheDocument();
    
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);
    
    // Messages should be cleared (welcome message will be re-added by useEffect)
    // This is a bit tricky to test due to the useEffect, but we can check that refresh button works
    expect(refreshButton).toBeInTheDocument();
  });

  it('handles keyboard input for sending messages', async () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    const input = screen.getByPlaceholderText(/Ask me anything about your dashboard/);
    
    fireEvent.change(input, { target: { value: 'Help me' } });
    fireEvent.keyPress(input, { key: 'Enter', code: 'Enter' });
    
    // Should show user message
    expect(screen.getByText('Help me')).toBeInTheDocument();
    
    // Should show loading state
    expect(screen.getByText('Thinking...')).toBeInTheDocument();
  });

  it('disables input and send button when loading', async () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    const input = screen.getByPlaceholderText(/Ask me anything about your dashboard/);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    // During loading, input and button should be disabled
    expect(input).toBeDisabled();
    expect(sendButton).toBeDisabled();
  });

  it('handles maximize/restore functionality', () => {
    render(<FloatingAIAssistant {...defaultProps} />);
    
    const maximizeButton = screen.getByRole('button', { name: /maximize/i });
    fireEvent.click(maximizeButton);
    
    // Should toggle maximize state (visual changes are hard to test without DOM inspection)
    expect(maximizeButton).toBeInTheDocument();
  });
});
