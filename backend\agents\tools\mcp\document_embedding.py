"""
Document embedding and vector database tool for the Datagenius backend using mem0ai.

This module provides an MCP tool for processing documents, creating embeddings,
and storing them using mem0ai and Qdrant, replacing the previous FAISS implementation.
"""

import logging
import os
import sys # Added for path manipulation
from typing import Dict, Any, Optional
import uuid
from pathlib import Path

from ..mcp.base import BaseMCPTool
from ...utils.vector_service import VectorService
from ...utils.chunking_performance_manager import ChunkingPerformanceManager

# Set up logging first
logger = logging.getLogger(__name__)

# Add yaml_utils import relative to backend directory
# Assuming this script is run from a context where 'backend' is the root or in PYTHONPATH
try:
    from app.utils.yaml_utils import load_yaml, save_yaml
except ImportError:
    # Attempt relative import if direct fails (might happen in certain execution contexts)
    try:
        # Calculate path to backend/app/utils relative to this file's location
        current_dir = os.path.dirname(os.path.abspath(__file__))
        utils_dir = os.path.abspath(os.path.join(current_dir, '..', '..', 'app', 'utils'))
        if utils_dir not in sys.path:
            sys.path.insert(0, os.path.dirname(utils_dir)) # Add parent of utils ('app')
        from utils.yaml_utils import load_yaml, save_yaml
    except ImportError as e:
        logger.warning(f"Error importing yaml_utils: {e}. Using fallback implementation.")
        # Implement fallback YAML functions with proper error handling
        def load_yaml(file_path: str) -> Dict[str, Any]:
            """Fallback YAML loader using standard library."""
            import yaml
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
            except Exception as e:
                logger.error(f"Error loading YAML file {file_path}: {e}")
                return {}

        def save_yaml(data: Dict[str, Any], file_path: str) -> bool:
            """Fallback YAML saver using standard library."""
            import yaml
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.safe_dump(data, f, default_flow_style=False, allow_unicode=True)
                return True
            except Exception as e:
                logger.error(f"Error saving YAML file {file_path}: {e}")
                return False


class DocumentEmbeddingTool(BaseMCPTool):
    """Tool for embedding documents and storing them in a vector database."""

    def __init__(self):
        """Initialize the document embedding tool with mem0ai."""
        super().__init__(
            name="document_embedding", # This name must match the 'name' field in the YAML schema
            description="Process documents, create embeddings, and store using mem0ai", # This can be overridden by YAML
            schema_path="document_embedding.yaml" # Relative to backend/schemas/tools/
            # input_schema and annotations will be loaded from the YAML file.
        )
        self.data_dir = "data"
        self.default_chunk_size = 1000
        self.default_chunk_overlap = 200

        # Initialize vector service with mem0ai
        self.vector_service = VectorService()

        # Initialize performance manager
        self.performance_manager = ChunkingPerformanceManager()

        # Marketing field queries
        self.marketing_field_queries = {
            "brand_description": "Based on the provided context, write a concise brand description. Extract information about the company's mission, values, and unique selling points.",
            "target_audience": "Based on the provided context, identify and describe the target audience or customer segments for this business. Include demographics, psychographics, and key characteristics.",
            "products_services": "Based on the provided context, list and briefly describe the main products and/or services offered by the business.",
            "marketing_goals": "Based on the provided context, identify the key marketing goals or objectives for this business. If not explicitly stated, suggest reasonable goals based on the business type and information provided.",
            "existing_content": "Based on the provided context, summarize any existing marketing content, campaigns, or channels mentioned in the document.",
            "keywords": "Based on the provided context, generate a list of 10-15 relevant keywords for this business that could be used for marketing purposes. Format as a comma-separated list.",
            "suggested_topics": "Based on the provided context, suggest 5-7 content topics that would be relevant for this business's marketing strategy. Present as a numbered list.",
            "competitive_landscape": "Based on the provided context, describe the competitive landscape for this business. Include information about competitors, market position, industry trends, and competitive advantages or challenges.",
            "budget": "Based on the provided context, identify any budget constraints, financial considerations, or resource allocations mentioned for marketing activities. If not explicitly stated, suggest reasonable budget considerations based on the business type.",
            "timeline": "Based on the provided context, identify any timeline constraints, deadlines, or scheduling requirements mentioned for marketing activities. Include project timelines, campaign schedules, or launch dates.",
            "platforms": "Based on the provided context, identify the specific platforms, channels, or mediums mentioned for marketing and content distribution. Include social media platforms, advertising channels, or communication mediums."
        }

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # Set data directory
        self.data_dir = config.get("data_dir", self.data_dir)

        # Set vector database directory
        self.vector_db_dir = config.get("vector_db_dir", self.vector_db_dir)

        # Create vector database directory if it doesn't exist
        os.makedirs(self.vector_db_dir, exist_ok=True)

        # Set default chunk size and overlap
        self.default_chunk_size = config.get("chunk_size", self.default_chunk_size)
        self.default_chunk_overlap = config.get("chunk_overlap", self.default_chunk_overlap)

        # Set default embeddings model
        self.default_embeddings_model = config.get("embeddings_model", self.default_embeddings_model)

        logger.info(f"Initialized document embedding tool with vector_db_dir: {self.vector_db_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            file_path = arguments["file_path"]
            operation = arguments["operation"]

            # Make sure we have an absolute path
            file_path = os.path.abspath(file_path)

            # Log the file path we're trying to access
            logger.info(f"Attempting to access file: {file_path}")

            # Check if the file exists
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }

            # Get file extension
            file_extension = Path(file_path).suffix.lower()

            # Check if the file is supported
            if file_extension not in [".pdf", ".docx", ".txt"]:
                logger.warning(f"Potentially unsupported file extension: {file_extension}, but will attempt to process as PDF")
                # Force PDF for unknown extensions as a fallback
                file_extension = ".pdf"

            # Get chunking parameters with adaptive defaults
            use_adaptive_chunking = arguments.get("use_adaptive_chunking", True)
            performance_profile = arguments.get("performance_profile", "balanced")
            chunk_size = arguments.get("chunk_size", None)  # None enables adaptive
            chunk_overlap = arguments.get("chunk_overlap", None)  # None enables adaptive

            # Set performance profile for this operation
            if hasattr(self.performance_manager, 'performance_profile'):
                self.performance_manager.performance_profile = performance_profile

            # Execute the operation
            if operation == "embed":
                # Get optimal configuration for this document
                if use_adaptive_chunking:
                    # Analyze document to determine optimal settings
                    try:
                        with open(file_path, 'rb') as f:
                            file_size = len(f.read())

                        # Get content type hint from file extension
                        content_type_hint = self._get_content_type_hint(file_extension)

                        # Get optimal configuration
                        optimal_config = self.performance_manager.get_optimal_config(
                            content_type_hint, file_size
                        )

                        logger.info(f"Using adaptive chunking with profile: {performance_profile}")
                        logger.info(f"Optimal config: {optimal_config.get('chunking_strategy', {})}")

                    except Exception as e:
                        logger.warning(f"Could not determine optimal config: {e}, using defaults")
                        optimal_config = {}

                # Process the document and create embeddings using adaptive chunking
                vector_store_id, file_info = await self.vector_service.embed_document(
                    file_path=file_path,
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap,
                    use_adaptive_chunking=use_adaptive_chunking
                )

                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Document processed and embedded successfully using mem0ai. Vector store ID: {vector_store_id}"
                        }
                    ],
                    "metadata": {
                        "vector_store_id": vector_store_id,
                        "file_info": file_info
                    }
                }

            elif operation == "query":
                # Get query
                query = arguments.get("query")
                if not query:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Query is required for 'query' operation"
                            }
                        ]
                    }

                # First, ensure the document is embedded with adaptive chunking
                try:
                    vector_store_id, _ = await self.vector_service.embed_document(
                        file_path=file_path,
                        chunk_size=chunk_size,
                        chunk_overlap=chunk_overlap,
                        use_adaptive_chunking=use_adaptive_chunking
                    )
                except Exception as e:
                    logger.warning(f"Could not embed document: {e}")
                    vector_store_id = None

                # Search using VectorService
                if vector_store_id:
                    results = self.vector_service.search_document(vector_store_id, query, limit=5)
                else:
                    results = []

                # Format the results
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "content": result.get("content", ""),
                        "metadata": result.get("metadata", {})
                    })

                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Query results:\n\n{formatted_results[0]['content'] if formatted_results else 'No results found.'}"
                        }
                    ],
                    "metadata": {
                        "query": query,
                        "results": formatted_results
                    }
                }

            elif operation == "query_marketing_fields":
                # First, ensure the document is embedded with adaptive chunking
                try:
                    vector_store_id, _ = await self.vector_service.embed_document(
                        file_path=file_path,
                        chunk_size=chunk_size,
                        chunk_overlap=chunk_overlap,
                        use_adaptive_chunking=use_adaptive_chunking
                    )
                except Exception as e:
                    logger.warning(f"Could not embed document: {e}")
                    vector_store_id = None

                # Query the vector store for each marketing field
                marketing_fields = {}
                for field, query in self.marketing_field_queries.items():
                    if vector_store_id:
                        results = self.vector_service.search_document(vector_store_id, query, limit=3)
                        if results:
                            # Combine the results
                            context = "\n\n".join([result.get("content", "") for result in results])

                            # Use the context to generate a response for the field
                            from langchain_core.prompts import ChatPromptTemplate
                            from langchain_groq import ChatGroq

                            # Initialize the LLM
                            llm = ChatGroq(
                                temperature=0.3,
                                model_name="llama3-70b-8192",
                                groq_api_key=os.getenv("GROQ_API_KEY", "")
                            )

                            # Create the prompt
                            prompt = ChatPromptTemplate.from_template(
                                """You are an AI assistant that helps extract information from documents.

                                Context from document:
                                {context}

                                Task: {query}

                                Provide a concise, well-formatted response based only on the information in the context.
                                If the context doesn't contain relevant information, provide a reasonable response based on the type of business or organization mentioned.
                                """
                            )

                            # Create the chain
                            chain = prompt | llm

                            # Execute the chain
                            response = await chain.ainvoke({"context": context, "query": query})

                            # Store the response
                            marketing_fields[field] = response.content
                        else:
                            marketing_fields[field] = ""
                    else:
                        marketing_fields[field] = ""

                return {
                    "content": [
                        {
                            "type": "text",
                            "text": "Marketing fields extracted successfully using mem0ai."
                        }
                    ],
                    "metadata": {
                        "marketing_fields": marketing_fields
                    }
                }

            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported operation: {operation}"
                        }
                    ]
                }

        except Exception as e:
            logger.error(f"Error executing document embedding tool: {e}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error executing document embedding tool: {str(e)}"
                    }
                ]
            }

    def _get_content_type_hint(self, file_extension: str) -> str:
        """
        Get content type hint based on file extension.

        Args:
            file_extension: File extension (e.g., '.pdf', '.docx')

        Returns:
            Content type hint string
        """
        extension_mapping = {
            '.pdf': 'general_text',
            '.docx': 'general_text',
            '.doc': 'general_text',
            '.txt': 'general_text',
            '.md': 'technical_documents',
            '.py': 'code_documentation',
            '.js': 'code_documentation',
            '.json': 'structured_data',
            '.csv': 'structured_data',
            '.xml': 'structured_data'
        }

        return extension_mapping.get(file_extension.lower(), 'general_text')

    # Legacy methods removed - now using VectorService with mem0ai
