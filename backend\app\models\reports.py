from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Literal
from datetime import datetime, timezone

from sqlalchemy import Column, String, DateTime, JSON, Boolean, ForeignKey, Text, Integer # Added Integer
from sqlalchemy.orm import relationship
from ..database import Base # Changed to relative import
from ..utils.db_utils import get_utc_now # Changed to relative import
import uuid

class ReportConfig(BaseModel):
    report_name: str = Field(..., description="Name of the report")
    data_source_id: str = Field(..., description="Identifier for the data source to be used")
    filters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Filters to apply to the data")
    group_by: Optional[List[str]] = Field(default_factory=list, description="Fields to group the data by")
    aggregations: Optional[Dict[str, str]] = Field(default_factory=dict, description="Aggregations to perform, e.g., {'sales': 'sum'}")
    chart_type: Optional[Literal["bar", "line", "pie", "table"]] = Field("table", description="Type of chart for visualization")
    user_id: int = Field(..., description="User ID requesting the report") # Changed to int

class Report(BaseModel):
    report_id: str = Field(..., description="Unique identifier for the generated report")
    report_name: str = Field(..., description="Name of the report")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of report generation")
    config_used: ReportConfig = Field(..., description="The configuration used to generate this report")
    data: List[Dict[str, Any]] = Field(..., description="The actual report data (e.g., list of rows)")
    summary: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Summary statistics or insights")
    visualization_url: Optional[str] = Field(None, description="URL to an image or interactive visualization of the report")

class ReportSchedule(BaseModel):
    schedule_id: Optional[str] = Field(None, description="Unique identifier for the schedule")
    report_config: ReportConfig = Field(..., description="Configuration of the report to be scheduled")
    cron_expression: str = Field(..., description="Cron expression for the schedule (e.g., '0 0 * * MON')")
    next_run_time: Optional[datetime] = Field(None, description="Calculated next run time")
    is_active: bool = Field(True, description="Whether the schedule is active")
    last_run_status: Optional[Literal["success", "failed", "pending"]] = Field("pending", description="Status of the last run")
    user_id: int = Field(..., description="User ID who owns this schedule") # Changed to int

class ReportExport(BaseModel):
    report_id: str
    format: Literal["csv", "excel", "pdf"]
    content: bytes # This will hold the raw bytes of the exported file
    filename: str

# SQLAlchemy Models

class ReportModel(Base):
    __tablename__ = "reports"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    report_name = Column(String, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True) # Changed to Integer
    generated_at = Column(DateTime(timezone=True), default=get_utc_now, nullable=False)
    
    config_used_raw = Column("config_used", JSON, nullable=False)  # Stores ReportConfig as JSON
    data_raw = Column("data", JSON, nullable=False) # Stores List[Dict[str, Any]] as JSON
    summary_raw = Column("summary", JSON, nullable=True) # Stores Dict[str, Any] as JSON
    
    # Instead of visualization_url, store config for frontend rendering
    visualization_config = Column(JSON, nullable=True) 
    
    data_source_id = Column(String, nullable=True, index=True) # Extracted from config_used for easier querying

    # Relationship (optional, if you need to access user object from report)
    # user = relationship("User") # Assuming User model exists

    @property
    def config_used(self) -> ReportConfig:
        return ReportConfig(**self.config_used_raw)

    @config_used.setter
    def config_used(self, value: ReportConfig):
        self.config_used_raw = value.model_dump()
        self.data_source_id = value.data_source_id

    @property
    def data(self) -> List[Dict[str, Any]]:
        return self.data_raw

    @data.setter
    def data(self, value: List[Dict[str, Any]]):
        self.data_raw = value
    
    @property
    def summary(self) -> Optional[Dict[str, Any]]:
        return self.summary_raw

    @summary.setter
    def summary(self, value: Optional[Dict[str, Any]]):
        self.summary_raw = value


class ReportScheduleModel(Base):
    __tablename__ = "report_schedules"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True) # Changed to Integer
    
    report_config_raw = Column("report_config", JSON, nullable=False) # Stores ReportConfig as JSON
    
    cron_expression = Column(String, nullable=False)
    next_run_time = Column(DateTime(timezone=True), nullable=True, index=True)
    is_active = Column(Boolean, default=True, nullable=False)
    last_run_status = Column(String, nullable=True) # "success", "failed", "pending"
    
    created_at = Column(DateTime(timezone=True), default=get_utc_now, nullable=False)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now, nullable=False)

    # Relationship (optional)
    # user = relationship("User")

    @property
    def report_config(self) -> ReportConfig:
        return ReportConfig(**self.report_config_raw)

    @report_config.setter
    def report_config(self, value: ReportConfig):
        self.report_config_raw = value.model_dump()
