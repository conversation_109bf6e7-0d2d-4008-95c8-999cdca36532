import { useEffect, useRef } from 'react';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>att<PERSON>,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { BarChart4, Image as ImageIcon, TrendingUp, PieChart as PieIcon } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { VisualizationData } from '@/utils/visualization';
import { SaveToDashboardButton } from './SaveToDashboardButton';

interface ChartVisualizationProps {
  visualization: VisualizationData;
  className?: string;
}

// Enhanced color palette for better accessibility
const CHART_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // <PERSON>
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280'  // Gray
];

// Chart type configurations
const CHART_CONFIGS = {
  bar: { icon: BarChart4, name: 'Bar Chart' },
  line: { icon: TrendingUp, name: 'Line Chart' },
  area: { icon: TrendingUp, name: 'Area Chart' },
  pie: { icon: PieIcon, name: 'Pie Chart' },
  scatter: { icon: BarChart4, name: 'Scatter Plot' },
  radar: { icon: BarChart4, name: 'Radar Chart' }
};

export const ChartVisualization = ({ visualization, className = '' }: ChartVisualizationProps) => {
  const chartRef = useRef<HTMLDivElement>(null);

  // Debug logging
  console.log('=== ENHANCED CHART VISUALIZATION DEBUG ===');
  console.log('Visualization object:', visualization);
  console.log('Visualization data:', visualization.data);
  console.log('Chart type:', visualization.config?.type);
  console.log('Has image data:', !!visualization.data?.image);

  // Enhanced chart rendering function
  const renderChart = () => {
    const { data, config } = visualization;
    const chartType = config?.type || 'bar';

    // Handle different data formats
    let chartData = data;
    if (data.labels && data.datasets) {
      // Convert Chart.js format to Recharts format
      chartData = data.labels.map((label: string, index: number) => {
        const item: any = { name: label };
        data.datasets.forEach((dataset: any, datasetIndex: number) => {
          item[dataset.label || `Series ${datasetIndex + 1}`] = dataset.data[index];
        });
        return item;
      });
    }

    const chartProps = {
      data: chartData,
      margin: { top: 20, right: 30, left: 20, bottom: 20 }
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis dataKey="name" stroke="#6B7280" fontSize={12} />
            <YAxis stroke="#6B7280" fontSize={12} />
            <Tooltip
              contentStyle={{
                backgroundColor: '#FFFFFF',
                border: '1px solid #E5E7EB',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
              }}
            />
            <Legend />
            {data.datasets?.map((dataset: any, index: number) => (
              <Line
                key={index}
                type="monotone"
                dataKey={dataset.label || `Series ${index + 1}`}
                stroke={CHART_COLORS[index % CHART_COLORS.length]}
                strokeWidth={2}
                dot={{ fill: CHART_COLORS[index % CHART_COLORS.length], strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: CHART_COLORS[index % CHART_COLORS.length], strokeWidth: 2 }}
              />
            ))}
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis dataKey="name" stroke="#6B7280" fontSize={12} />
            <YAxis stroke="#6B7280" fontSize={12} />
            <Tooltip
              contentStyle={{
                backgroundColor: '#FFFFFF',
                border: '1px solid #E5E7EB',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
              }}
            />
            <Legend />
            {data.datasets?.map((dataset: any, index: number) => (
              <Area
                key={index}
                type="monotone"
                dataKey={dataset.label || `Series ${index + 1}`}
                stroke={CHART_COLORS[index % CHART_COLORS.length]}
                fill={CHART_COLORS[index % CHART_COLORS.length]}
                fillOpacity={0.6}
              />
            ))}
          </AreaChart>
        );

      case 'pie':
        const pieData = Array.isArray(chartData) ? chartData :
          data.labels?.map((label: string, index: number) => ({
            name: label,
            value: data.datasets?.[0]?.data[index] || 0
          })) || [];

        return (
          <PieChart>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {pieData.map((entry: any, index: number) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        );

      case 'scatter':
        return (
          <ScatterChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis dataKey="x" type="number" stroke="#6B7280" fontSize={12} />
            <YAxis dataKey="y" type="number" stroke="#6B7280" fontSize={12} />
            <Tooltip cursor={{ strokeDasharray: '3 3' }} />
            <Legend />
            {data.datasets?.map((dataset: any, index: number) => (
              <Scatter
                key={index}
                name={dataset.label || `Series ${index + 1}`}
                data={dataset.data}
                fill={CHART_COLORS[index % CHART_COLORS.length]}
              />
            ))}
          </ScatterChart>
        );

      case 'radar':
        return (
          <RadarChart {...chartProps}>
            <PolarGrid stroke="#E5E7EB" />
            <PolarAngleAxis dataKey="name" tick={{ fontSize: 12, fill: '#6B7280' }} />
            <PolarRadiusAxis tick={{ fontSize: 10, fill: '#6B7280' }} />
            <Tooltip />
            <Legend />
            {data.datasets?.map((dataset: any, index: number) => (
              <Radar
                key={index}
                name={dataset.label || `Series ${index + 1}`}
                dataKey={dataset.label || `Series ${index + 1}`}
                stroke={CHART_COLORS[index % CHART_COLORS.length]}
                fill={CHART_COLORS[index % CHART_COLORS.length]}
                fillOpacity={0.3}
              />
            ))}
          </RadarChart>
        );

      default: // bar chart
        return (
          <BarChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis dataKey="name" stroke="#6B7280" fontSize={12} />
            <YAxis stroke="#6B7280" fontSize={12} />
            <Tooltip
              contentStyle={{
                backgroundColor: '#FFFFFF',
                border: '1px solid #E5E7EB',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
              }}
            />
            <Legend />
            {data.datasets?.map((dataset: any, index: number) => (
              <Bar
                key={index}
                dataKey={dataset.label || `Series ${index + 1}`}
                fill={CHART_COLORS[index % CHART_COLORS.length]}
                radius={[2, 2, 0, 0]}
              />
            ))}
          </BarChart>
        );
    }
  };

  const { data, title, description, config } = visualization;
  const chartType = config?.type || 'bar';
  const ChartIcon = CHART_CONFIGS[chartType as keyof typeof CHART_CONFIGS]?.icon || BarChart4;
  const chartName = CHART_CONFIGS[chartType as keyof typeof CHART_CONFIGS]?.name || 'Chart';

  // Check if this is an image-based visualization
  if (data.image) {
    console.log('Rendering image-based chart visualization');
    return (
      <Card className={`overflow-hidden shadow-lg border-gray-200 ${className}`} role="img" aria-label={title || 'Data Visualization'}>
        <CardHeader className="pb-2 bg-gradient-to-r from-white to-gray-50 border-b border-gray-100">
          <div className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5 text-blue-500" aria-hidden="true" />
            <CardTitle className="text-lg text-blue-700">{title || 'Data Visualization'}</CardTitle>
          </div>
          {description && <CardDescription className="text-gray-600">{description}</CardDescription>}
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex justify-center bg-white rounded-lg border border-gray-200 p-4">
            <img
              src={data.image}
              alt={title || 'Visualization'}
              className="max-w-full max-h-[600px] object-contain rounded-md shadow-sm"
              style={{ minHeight: '200px' }}
              onError={(e) => {
                console.error('Failed to load visualization image');
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling?.classList.remove('hidden');
              }}
            />
            <div className="hidden text-center text-gray-500 p-8" role="alert">
              <ImageIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" aria-hidden="true" />
              <p>Failed to load visualization</p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between items-center border-t border-gray-100 bg-gray-50 py-3 px-4">
          <div className="text-sm text-gray-500">
            Generated by PandasAI
          </div>
          <SaveToDashboardButton visualization={visualization} />
        </CardFooter>
      </Card>
    );
  }

  // For standard chart visualization with enhanced Recharts
  const { labels, datasets } = data;

  // Validate data format
  if (!data || (!data.image && (!labels || !datasets || !Array.isArray(labels) || !Array.isArray(datasets)))) {
    return (
      <Card className={`overflow-hidden ${className}`} role="alert">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <ChartIcon className="h-5 w-5 text-blue-500" aria-hidden="true" />
            <CardTitle className="text-lg">{title || chartName}</CardTitle>
          </div>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div className="p-8 text-center text-gray-500">
            <ChartIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" aria-hidden="true" />
            <p>No data available for visualization</p>
            <p className="text-sm mt-2">Please check your data source configuration.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Enhanced chart rendering with Recharts
  return (
    <Card className={`overflow-hidden shadow-lg border-gray-200 ${className}`} role="img" aria-label={`${chartName}: ${title || 'Data Visualization'}`}>
      <CardHeader className="pb-2 bg-gradient-to-r from-white to-gray-50 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <ChartIcon className="h-5 w-5 text-blue-500" aria-hidden="true" />
          <CardTitle className="text-lg text-blue-700">{title || chartName}</CardTitle>
        </div>
        {description && <CardDescription className="text-gray-600">{description}</CardDescription>}
      </CardHeader>
      <CardContent className="p-6">
        <div className="h-80 w-full" ref={chartRef}>
          <ResponsiveContainer width="100%" height="100%">
            {renderChart()}
          </ResponsiveContainer>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between items-center border-t border-gray-100 bg-gray-50 py-3 px-4">
        <div className="text-sm text-gray-500 flex items-center gap-2">
          <ChartIcon className="h-4 w-4" aria-hidden="true" />
          <span>{chartName}</span>
        </div>
        <SaveToDashboardButton visualization={visualization} />
      </CardFooter>
    </Card>
  );
};
