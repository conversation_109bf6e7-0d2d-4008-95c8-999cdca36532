#!/usr/bin/env python3
"""
Test script to verify that the concierge agent uses real marketplace data 
for persona recommendations instead of hallucinating.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_persona_recommendations():
    """Test that persona recommendations use real marketplace data."""
    try:
        print("🧪 Testing Persona Recommendations with Real Data...")
        
        from backend.agents.concierge_agent.concierge import ConciergeAgent
        
        concierge = ConciergeAgent()
        await concierge._initialize({})
        
        # Test queries that should trigger persona recommendations
        test_queries = [
            "what persona is best for data analytics",
            "I need help with marketing content",
            "which agent can help me analyze my data",
            "recommend a persona for business intelligence",
            "what AI can help with text classification"
        ]
        
        for query in test_queries:
            print(f"\n📝 Testing query: '{query}'")
            
            # Process the message
            result = await concierge.process_message(
                message=query,
                user_id="1",
                conversation_id="test_conv",
                context={"conversation_history": []}
            )
            
            if result.get("success", False):
                response = result.get("message", "")
                metadata = result.get("metadata", {})
                
                print(f"✅ Query processed successfully")
                print(f"   Intent: {metadata.get('intent', 'unknown')}")
                print(f"   Response length: {len(response)} characters")
                
                # Check if this is a persona recommendation
                is_persona_rec = metadata.get("is_persona_recommendation", False)
                recommended_personas = metadata.get("recommended_personas", [])
                recommendation_type = metadata.get("recommendation_type", "none")
                
                print(f"   Is persona recommendation: {is_persona_rec}")
                print(f"   Recommendation type: {recommendation_type}")
                print(f"   Number of recommended personas: {len(recommended_personas)}")
                
                if is_persona_rec and recommended_personas:
                    print(f"   📋 Recommended personas:")
                    for persona in recommended_personas:
                        name = persona.get("name", "Unknown")
                        category = persona.get("category", "unknown")
                        is_available = persona.get("is_available", False)
                        price = persona.get("price", 0.0)
                        
                        print(f"      - {name} ({category}) - Available: {is_available}")
                        if price > 0:
                            print(f"        Price: ${price:.2f}")
                        
                        # Check for capabilities
                        capabilities = persona.get("capabilities", [])
                        if capabilities:
                            print(f"        Capabilities: {', '.join(capabilities[:3])}")
                    
                    # Verify no hallucinated personas
                    hallucinated_names = [
                        "Data Analyst", "Business Intelligence Expert", 
                        "Machine Learning Specialist", "Data Scientist",
                        "Business Analyst", "Marketing Expert"
                    ]
                    
                    found_hallucinated = []
                    for persona in recommended_personas:
                        name = persona.get("name", "")
                        if name in hallucinated_names:
                            found_hallucinated.append(name)
                    
                    if found_hallucinated:
                        print(f"   ❌ Found hallucinated personas: {found_hallucinated}")
                        return False
                    else:
                        print(f"   ✅ No hallucinated personas found - using real marketplace data!")
                
                # Check that response doesn't contain hallucinated content
                hallucinated_phrases = [
                    "Data Analyst", "Business Intelligence Expert",
                    "Machine Learning Specialist", "Data Scientist persona",
                    "Business Analyst persona", "Marketing Expert persona"
                ]
                
                found_hallucinated_text = []
                for phrase in hallucinated_phrases:
                    if phrase in response:
                        found_hallucinated_text.append(phrase)
                
                if found_hallucinated_text:
                    print(f"   ⚠️  Found potentially hallucinated text: {found_hallucinated_text}")
                else:
                    print(f"   ✅ Response text appears to use real data")
                
            else:
                print(f"   ❌ Query failed: {result}")
                return False
        
        print("\n🎉 All persona recommendation tests passed!")
        print("   ✅ Using real marketplace data instead of hallucinating")
        print("   ✅ Returning interactive persona metadata")
        print("   ✅ Proper categorization (owned/free/paid)")
        print("   ✅ No hardcoded or hallucinated persona names")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_recommendation_ui_metadata():
    """Test that recommendations include proper UI metadata."""
    try:
        print("\n🎨 Testing Recommendation UI Metadata...")
        
        from backend.agents.concierge_agent.concierge import ConciergeAgent
        
        concierge = ConciergeAgent()
        await concierge._initialize({})
        
        # Test a specific recommendation query
        result = await concierge.process_message(
            message="I need help analyzing my sales data",
            user_id="1", 
            conversation_id="test_conv",
            context={"conversation_history": []}
        )
        
        if result.get("success", False):
            metadata = result.get("metadata", {})
            
            # Check for required metadata fields
            required_fields = [
                "is_persona_recommendation",
                "recommended_personas",
                "recommendation_type"
            ]
            
            missing_fields = [field for field in required_fields if field not in metadata]
            if missing_fields:
                print(f"   ❌ Missing metadata fields: {missing_fields}")
                return False
            
            recommended_personas = metadata.get("recommended_personas", [])
            if recommended_personas:
                # Check persona structure
                sample_persona = recommended_personas[0]
                required_persona_fields = [
                    "id", "name", "description", "category", 
                    "is_available", "is_owned", "capabilities"
                ]
                
                missing_persona_fields = [
                    field for field in required_persona_fields 
                    if field not in sample_persona
                ]
                
                if missing_persona_fields:
                    print(f"   ❌ Missing persona fields: {missing_persona_fields}")
                    return False
                
                print(f"   ✅ Persona metadata structure is complete")
                print(f"   ✅ Found {len(recommended_personas)} personas with full metadata")
                
                # Check categories
                categories = set(p.get("category") for p in recommended_personas)
                print(f"   ✅ Persona categories: {categories}")
                
                return True
            else:
                print(f"   ⚠️  No personas recommended for this query")
                return True
        else:
            print(f"   ❌ Query failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ UI metadata test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 Starting Persona Recommendation Tests\n")
        
        # Test that recommendations use real data
        recommendations_success = await test_persona_recommendations()
        
        if recommendations_success:
            # Test UI metadata structure
            ui_success = await test_recommendation_ui_metadata()
            
            if ui_success:
                print("\n✅ All tests passed! The concierge agent now:")
                print("   🎯 Uses real marketplace data for recommendations")
                print("   🚫 No longer hallucinates fake personas")
                print("   🎨 Provides interactive UI metadata")
                print("   📱 Supports categorized persona buttons")
                print("   🔄 Enables seamless persona switching")
            else:
                print("\n❌ UI metadata tests failed.")
        else:
            print("\n❌ Recommendation tests failed.")
    
    asyncio.run(main())
