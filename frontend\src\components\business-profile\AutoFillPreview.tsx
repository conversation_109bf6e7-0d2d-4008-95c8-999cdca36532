/**
 * Auto-fill Preview Component
 * 
 * Displays extracted data and field mappings for user review and acceptance
 */

import React, { useState, useCallback } from 'react';
import { Check, X, Eye, EyeOff, Info, AlertTriangle, CheckCircle } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AutoFillResponse, 
  FieldMapping, 
  getConfidenceColor, 
  getConfidenceIcon, 
  formatConfidenceScore,
  getFieldDefinition,
  fieldHasOptions,
  getFieldOptions
} from '@/lib/businessProfileAutoFillApi';

interface AutoFillPreviewProps {
  autoFillData: AutoFillResponse;
  onAccept: (acceptedFields: Record<string, string>) => void;
  onReject: () => void;
  onFieldChange?: (fieldName: string, value: string) => void;
}

interface FieldState {
  accepted: boolean;
  value: string;
  modified: boolean;
}

export const AutoFillPreview: React.FC<AutoFillPreviewProps> = ({
  autoFillData,
  onAccept,
  onReject,
  onFieldChange,
}) => {
  const [fieldStates, setFieldStates] = useState<Record<string, FieldState>>(() => {
    const initialStates: Record<string, FieldState> = {};
    
    if (autoFillData.field_mappings) {
      Object.entries(autoFillData.field_mappings).forEach(([fieldName, mapping]) => {
        if (mapping.value && mapping.value !== 'null') {
          initialStates[fieldName] = {
            accepted: mapping.confidence === 'high', // Auto-accept high confidence fields
            value: mapping.value,
            modified: false,
          };
        }
      });
    }
    
    return initialStates;
  });

  const [showSourceData, setShowSourceData] = useState(false);
  const [showProcessingNotes, setShowProcessingNotes] = useState(false);

  // Handle field acceptance toggle
  const handleFieldToggle = useCallback((fieldName: string, accepted: boolean) => {
    setFieldStates(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        accepted,
      },
    }));
  }, []);

  // Handle field value change
  const handleFieldValueChange = useCallback((fieldName: string, value: string) => {
    setFieldStates(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        value,
        modified: true,
      },
    }));
    
    onFieldChange?.(fieldName, value);
  }, [onFieldChange]);

  // Handle accept all
  const handleAcceptAll = useCallback(() => {
    const acceptedFields: Record<string, string> = {};
    
    Object.entries(fieldStates).forEach(([fieldName, state]) => {
      if (state.accepted && state.value) {
        acceptedFields[fieldName] = state.value;
      }
    });
    
    onAccept(acceptedFields);
  }, [fieldStates, onAccept]);

  // Handle accept selected
  const handleAcceptSelected = useCallback(() => {
    const acceptedFields: Record<string, string> = {};
    
    Object.entries(fieldStates).forEach(([fieldName, state]) => {
      if (state.accepted && state.value) {
        acceptedFields[fieldName] = state.value;
      }
    });
    
    onAccept(acceptedFields);
  }, [fieldStates, onAccept]);

  // Get accepted fields count
  const acceptedCount = Object.values(fieldStates).filter(state => state.accepted).length;
  const totalFields = Object.keys(fieldStates).length;

  if (!autoFillData.field_mappings) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No field mappings available in the auto-fill data.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Auto-fill Preview
            </CardTitle>
            <Badge variant="outline">
              {formatConfidenceScore(autoFillData.overall_confidence || 0)} confidence
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">
            {autoFillData.source_summary}
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm">
              <span className="font-medium">{acceptedCount}</span> of{' '}
              <span className="font-medium">{totalFields}</span> fields selected
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowProcessingNotes(!showProcessingNotes)}
              >
                {showProcessingNotes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                Notes
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSourceData(!showSourceData)}
              >
                {showSourceData ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                Source Data
              </Button>
            </div>
          </div>

          {/* Processing Notes */}
          {showProcessingNotes && autoFillData.processing_notes && (
            <Collapsible open={showProcessingNotes}>
              <CollapsibleContent>
                <div className="mb-4 p-3 bg-muted rounded-md">
                  <h4 className="text-sm font-medium mb-2">Processing Notes</h4>
                  <ul className="text-xs space-y-1">
                    {autoFillData.processing_notes.map((note, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Info className="h-3 w-3 mt-0.5 text-muted-foreground flex-shrink-0" />
                        {note}
                      </li>
                    ))}
                  </ul>
                </div>
              </CollapsibleContent>
            </Collapsible>
          )}

          {/* Source Data */}
          {showSourceData && autoFillData.source_data && (
            <Collapsible open={showSourceData}>
              <CollapsibleContent>
                <div className="mb-4 p-3 bg-muted rounded-md">
                  <h4 className="text-sm font-medium mb-2">Source Data</h4>
                  <pre className="text-xs overflow-auto max-h-40 whitespace-pre-wrap">
                    {JSON.stringify(autoFillData.source_data, null, 2)}
                  </pre>
                </div>
              </CollapsibleContent>
            </Collapsible>
          )}
        </CardContent>
      </Card>

      {/* Field Mappings */}
      <div className="space-y-4">
        {Object.entries(autoFillData.field_mappings).map(([fieldName, mapping]) => {
          if (!mapping.value || mapping.value === 'null') return null;

          const fieldDef = getFieldDefinition(fieldName);
          const fieldState = fieldStates[fieldName];
          const hasOptions = fieldHasOptions(fieldName);
          const options = hasOptions ? getFieldOptions(fieldName) : [];

          return (
            <Card key={fieldName} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <Checkbox
                      checked={fieldState?.accepted || false}
                      onCheckedChange={(checked) => 
                        handleFieldToggle(fieldName, checked as boolean)
                      }
                      className="mt-1"
                    />
                    <div>
                      <h4 className="font-medium">
                        {fieldDef?.label || fieldName}
                        {fieldDef?.required && <span className="text-red-500 ml-1">*</span>}
                      </h4>
                      {fieldDef?.description && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {fieldDef.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getConfidenceColor(mapping.confidence)}>
                      {getConfidenceIcon(mapping.confidence)} {mapping.confidence}
                    </Badge>
                    {fieldState?.modified && (
                      <Badge variant="outline" className="text-xs">
                        Modified
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {/* Field Input */}
                  <div>
                    {hasOptions ? (
                      <Select
                        value={fieldState?.value || ''}
                        onValueChange={(value) => handleFieldValueChange(fieldName, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                        <SelectContent>
                          {options.map((option) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : fieldDef?.maxLength && fieldDef.maxLength > 255 ? (
                      <Textarea
                        value={fieldState?.value || ''}
                        onChange={(e) => handleFieldValueChange(fieldName, e.target.value)}
                        placeholder={`Enter ${fieldDef.label.toLowerCase()}`}
                        className="min-h-[80px]"
                      />
                    ) : (
                      <Input
                        value={fieldState?.value || ''}
                        onChange={(e) => handleFieldValueChange(fieldName, e.target.value)}
                        placeholder={`Enter ${fieldDef?.label.toLowerCase() || fieldName}`}
                      />
                    )}
                  </div>

                  {/* Mapping Info */}
                  <div className="text-xs space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">Source:</span>
                      <code className="bg-muted px-1 rounded">{mapping.source}</code>
                    </div>
                    {mapping.reasoning && (
                      <div className="flex items-start gap-2">
                        <span className="text-muted-foreground">Reasoning:</span>
                        <span className="flex-1">{mapping.reasoning}</span>
                      </div>
                    )}
                    {mapping.alternatives && mapping.alternatives.length > 0 && (
                      <div className="flex items-start gap-2">
                        <span className="text-muted-foreground">Alternatives:</span>
                        <span className="flex-1">{mapping.alternatives.join(', ')}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-4 border-t">
        <Button variant="outline" onClick={onReject}>
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button 
          onClick={handleAcceptSelected}
          disabled={acceptedCount === 0}
        >
          <Check className="mr-2 h-4 w-4" />
          Apply Selected ({acceptedCount})
        </Button>
      </div>
    </div>
  );
};
