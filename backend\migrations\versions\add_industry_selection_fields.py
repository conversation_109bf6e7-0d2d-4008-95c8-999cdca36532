"""Add industry selection fields to users table

Revision ID: add_industry_selection_fields
Revises: user_profile_migration
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_industry_selection_fields'
down_revision = 'fb59f25d232a'
branch_labels = None
depends_on = None


def upgrade():
    """Add industry selection fields to users table."""
    # Check if columns already exist
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('users')]

    with op.batch_alter_table('users', schema=None) as batch_op:
        # Add industry selection fields if they don't exist
        if 'selected_industry' not in columns:
            batch_op.add_column(sa.Column('selected_industry', sa.String(100), nullable=True))

        if 'industry_selection_completed' not in columns:
            batch_op.add_column(sa.Column('industry_selection_completed', sa.<PERSON>(), nullable=True, default=False))

    # Update existing users to have industry_selection_completed = True (they've already been using the system)
    conn.execute(sa.text("UPDATE users SET industry_selection_completed = TRUE WHERE industry_selection_completed IS NULL"))


def downgrade():
    """Remove industry selection fields from users table."""
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_column('industry_selection_completed')
        batch_op.drop_column('selected_industry')
