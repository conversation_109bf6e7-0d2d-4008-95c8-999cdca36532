
import { Link, useLocation } from "react-router-dom";
import { Home, LayoutDashboard, Users, Settings, MessageSquare, BarChart4, Database, ShieldAlert, ChevronLeft, Lock, Unlock, Menu } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useNavbarStore } from "@/stores/navbar-store";
import { useAuth } from "@/contexts/AuthContext";

const sidebarItems = [
  {
    href: "/home",
    label: "Home",
    icon: Home,
  },
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
  },
  {
    href: "/ai-marketplace",
    label: "Marketplace",
    icon: Users,
  },
  {
    href: "/data-integration",
    label: "Data Integration",
    icon: Database,
  },
  {
    href: "/data-chat",
    label: "Data Chat",
    icon: MessageSquare,
  },
  {
    href: "/reports",
    label: "Reports",
    icon: BarChart4,
  },
  {
    href: "/settings",
    label: "Settings",
    icon: Settings,
  },
];

export const LeftNavbar = () => {
  const location = useLocation();
  const pathname = location.pathname;
  const { user } = useAuth();

  // Use navbar store for state management
  const {
    isLocked,
    setHovered,
    toggleExpanded,
    toggleLocked,
    shouldShowExpanded
  } = useNavbarStore();

  // Check if user has admin privileges
  const isAdmin = user?.is_superuser || user?.email === '<EMAIL>';

  // Determine if navbar should be shown expanded
  const shouldShow = shouldShowExpanded();

  return (
    <>
      {/* Enhanced Navbar Container */}
      <motion.div
        className={cn(
          "fixed left-0 top-0 h-screen bg-white/95 backdrop-blur-sm border-r border-slate-200/50 z-40 flex flex-col overflow-hidden",
          "transition-all duration-300 ease-in-out",
          isLocked && "border-r-2 border-r-blue-300 bg-blue-50/30", // Enhanced visual indicator for locked state
          !shouldShow && !isLocked && "hover:shadow-2xl hover:bg-white", // Enhanced shadow and background on hover
          shouldShow ? "shadow-xl" : "shadow-md"
        )}
        initial={false}
        animate={{
          width: shouldShow ? 240 : 64,
          x: 0
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        onMouseEnter={() => !isLocked && setHovered(true)}
        onMouseLeave={() => !isLocked && setHovered(false)}
      >
        {/* Header Section */}
        <div className={cn("border-b border-gray-200", shouldShow ? "p-4" : "p-2")}>
          {shouldShow ? (
            /* Expanded Header Layout */
            <div className="flex items-center justify-between">
              <AnimatePresence mode="wait">
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  <Link to="/home" className="flex items-center space-x-2">
                    <span className="font-bold text-lg">AI Toolkit</span>
                  </Link>
                </motion.div>
              </AnimatePresence>

              {/* Control Buttons for Expanded State */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center space-x-1"
              >
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleLocked}
                  className={cn(
                    "h-8 w-8 hover:bg-gray-100 transition-colors",
                    isLocked
                      ? "text-blue-600 hover:text-blue-700 bg-blue-50"
                      : "text-gray-500 hover:text-gray-700"
                  )}
                  title={isLocked ? "Unlock navbar" : "Lock navbar"}
                >
                  {isLocked ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleExpanded}
                  className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
                  title="Collapse navbar"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </motion.div>
            </div>
          ) : (
            /* Collapsed Header Layout */
            <div className="flex items-center justify-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleExpanded}
                className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
                title="Expand navbar"
              >
                <Menu className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Enhanced Navigation Section */}
        <nav className={cn(
          "flex-1 px-2 py-6 space-y-2 overflow-x-hidden",
          shouldShow ? "overflow-y-auto" : "overflow-y-hidden"
        )}>
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.href}
                to={item.href}
                className={cn(
                  "flex items-center py-3 rounded-lg transition-all duration-200 relative",
                  "group hover:scale-105 transform",
                  shouldShow ? "px-4" : "px-3 justify-center",
                  isActive
                    ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg font-medium"
                    : "text-slate-600 hover:text-slate-900 hover:bg-slate-100/80 hover:shadow-md"
                )}
                title={!shouldShow ? item.label : undefined}
                role="menuitem"
                aria-current={isActive ? "page" : undefined}
              >
                {/* Active indicator */}
                {isActive && (
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-white rounded-r-full" />
                )}

                <item.icon className={cn(
                  "h-5 w-5 flex-shrink-0 transition-colors duration-200",
                  isActive ? "text-white" : "text-slate-500 group-hover:text-slate-700"
                )} />

                <AnimatePresence mode="wait">
                  {shouldShow && (
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.2 }}
                      className={cn(
                        "ml-3 text-sm font-medium whitespace-nowrap",
                        isActive ? "text-white" : "text-slate-700"
                      )}
                    >
                      {item.label}
                    </motion.span>
                  )}
                </AnimatePresence>

                {/* Enhanced Tooltip for collapsed state */}
                {!shouldShow && (
                  <div className="absolute left-full ml-3 px-3 py-2 bg-slate-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg">
                    {item.label}
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-slate-900 rotate-45" />
                  </div>
                )}
              </Link>
            );
          })}

          {/* Admin Link - Show only for admin users */}
          {isAdmin && (
            <Link
              to="/admin"
              className={cn(
                "flex items-center py-3 rounded-lg transition-all duration-200 group relative",
                "mt-4 border-t border-slate-200 pt-4",
                shouldShow ? "px-3 mx-2" : "px-2 mx-2 justify-center",
                pathname.startsWith("/admin")
                  ? "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg"
                  : "text-slate-600 hover:text-slate-900 hover:bg-red-50 border border-red-200"
              )}
              title={!shouldShow ? "Admin Panel" : undefined}
              role="menuitem"
              aria-current={pathname.startsWith("/admin") ? "page" : undefined}
            >
              {/* Active indicator */}
              {pathname.startsWith("/admin") && (
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-white rounded-r-full" />
              )}

              <ShieldAlert className={cn(
                "h-5 w-5 flex-shrink-0 transition-colors duration-200",
                pathname.startsWith("/admin") ? "text-white" : "text-red-500 group-hover:text-red-600"
              )} />

              <AnimatePresence mode="wait">
                {shouldShow && (
                  <motion.span
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.2 }}
                    className={cn(
                      "ml-3 text-sm font-medium whitespace-nowrap",
                      pathname.startsWith("/admin") ? "text-white" : "text-slate-700"
                    )}
                  >
                    Admin Panel
                  </motion.span>
                )}
              </AnimatePresence>

              {/* Enhanced tooltip for collapsed state */}
              {!shouldShow && (
                <div className="absolute left-full ml-3 px-3 py-2 bg-slate-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg">
                  Admin Panel
                  <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-slate-900 rotate-45" />
                </div>
              )}
            </Link>
          )}
        </nav>
      </motion.div>

      {/* Spacer to prevent content overlap */}
      <div
        className={cn(
          "transition-all duration-300 ease-in-out flex-shrink-0",
          shouldShow ? "w-60" : "w-16"
        )}
      />
    </>
  );
};
