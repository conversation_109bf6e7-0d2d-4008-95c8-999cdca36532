import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';

interface GeneralSettingsProps {
  settings: any;
  onUpdate: (settings: any) => void;
}

const GeneralSettings = ({ settings, onUpdate }: GeneralSettingsProps) => {
  const [formData, setFormData] = useState({
    siteName: '',
    siteDescription: '',
    contactEmail: '',
    supportEmail: '',
    defaultLanguage: 'en',
    defaultCurrency: 'USD',
    enableMaintenanceMode: false,
    maintenanceMessage: '',
    enableBetaFeatures: false,
    maxUploadSize: 10,
    defaultPageSize: 20,
  });

  // Initialize form data when settings are loaded
  useEffect(() => {
    if (settings) {
      setFormData({
        siteName: settings.siteName || '',
        siteDescription: settings.siteDescription || '',
        contactEmail: settings.contactEmail || '',
        supportEmail: settings.supportEmail || '',
        defaultLanguage: settings.defaultLanguage || 'en',
        defaultCurrency: settings.defaultCurrency || 'USD',
        enableMaintenanceMode: settings.enableMaintenanceMode || false,
        maintenanceMessage: settings.maintenanceMessage || '',
        enableBetaFeatures: settings.enableBetaFeatures || false,
        maxUploadSize: settings.maxUploadSize || 10,
        defaultPageSize: settings.defaultPageSize || 20,
      });
    }
  }, [settings]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const updated = { ...prev, [name]: value };
      onUpdate(updated);
      return updated;
    });
  };

  // Handle number input change
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue)) {
      setFormData((prev) => {
        const updated = { ...prev, [name]: numValue };
        onUpdate(updated);
        return updated;
      });
    }
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => {
      const updated = { ...prev, [name]: value };
      onUpdate(updated);
      return updated;
    });
  };

  // Handle checkbox change
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData((prev) => {
      const updated = { ...prev, [name]: checked };
      onUpdate(updated);
      return updated;
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Site Information</h3>
        <div className="grid gap-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="siteName" className="text-right">
              Site Name
            </Label>
            <Input
              id="siteName"
              name="siteName"
              value={formData.siteName}
              onChange={handleInputChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="siteDescription" className="text-right pt-2">
              Site Description
            </Label>
            <Textarea
              id="siteDescription"
              name="siteDescription"
              value={formData.siteDescription}
              onChange={handleInputChange}
              className="col-span-3"
              rows={3}
            />
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Contact Information</h3>
        <div className="grid gap-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contactEmail" className="text-right">
              Contact Email
            </Label>
            <Input
              id="contactEmail"
              name="contactEmail"
              type="email"
              value={formData.contactEmail}
              onChange={handleInputChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="supportEmail" className="text-right">
              Support Email
            </Label>
            <Input
              id="supportEmail"
              name="supportEmail"
              type="email"
              value={formData.supportEmail}
              onChange={handleInputChange}
              className="col-span-3"
            />
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Localization</h3>
        <div className="grid gap-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="defaultLanguage" className="text-right">
              Default Language
            </Label>
            <Select
              value={formData.defaultLanguage}
              onValueChange={(value) => handleSelectChange('defaultLanguage', value)}
            >
              <SelectTrigger id="defaultLanguage" className="col-span-3">
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
                <SelectItem value="zh">Chinese</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="defaultCurrency" className="text-right">
              Default Currency
            </Label>
            <Select
              value={formData.defaultCurrency}
              onValueChange={(value) => handleSelectChange('defaultCurrency', value)}
            >
              <SelectTrigger id="defaultCurrency" className="col-span-3">
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="USD">US Dollar (USD)</SelectItem>
                <SelectItem value="EUR">Euro (EUR)</SelectItem>
                <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                <SelectItem value="JPY">Japanese Yen (JPY)</SelectItem>
                <SelectItem value="CAD">Canadian Dollar (CAD)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">System Settings</h3>
        <div className="grid gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="enableMaintenanceMode"
              checked={formData.enableMaintenanceMode}
              onCheckedChange={(checked) => handleCheckboxChange('enableMaintenanceMode', checked as boolean)}
            />
            <Label htmlFor="enableMaintenanceMode">Enable Maintenance Mode</Label>
          </div>
          {formData.enableMaintenanceMode && (
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="maintenanceMessage" className="text-right pt-2">
                Maintenance Message
              </Label>
              <Textarea
                id="maintenanceMessage"
                name="maintenanceMessage"
                value={formData.maintenanceMessage}
                onChange={handleInputChange}
                className="col-span-3"
                rows={3}
              />
            </div>
          )}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="enableBetaFeatures"
              checked={formData.enableBetaFeatures}
              onCheckedChange={(checked) => handleCheckboxChange('enableBetaFeatures', checked as boolean)}
            />
            <Label htmlFor="enableBetaFeatures">Enable Beta Features</Label>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="maxUploadSize" className="text-right">
              Max Upload Size (MB)
            </Label>
            <Input
              id="maxUploadSize"
              name="maxUploadSize"
              type="number"
              min="1"
              max="100"
              value={formData.maxUploadSize}
              onChange={handleNumberChange}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="defaultPageSize" className="text-right">
              Default Page Size
            </Label>
            <Input
              id="defaultPageSize"
              name="defaultPageSize"
              type="number"
              min="10"
              max="100"
              step="10"
              value={formData.defaultPageSize}
              onChange={handleNumberChange}
              className="col-span-3"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralSettings;
