#!/usr/bin/env python3
"""
Test script to verify dashboard creation fix.
"""

import requests
import json

# Test data that matches the frontend structure
dashboard_data = {
    "name": "Test Dashboard",
    "description": "A test dashboard to verify the fix",
    "is_default": False,
    "is_public": False,
    "layout_config": {
        "columns": 12,
        "rows": 12,
        "grid_gap": 16,
        "margin": [10, 10],
        "container_padding": [10, 10],
        "auto_size": True
    },
    "theme_config": {
        "primary_color": "#3B82F6",
        "secondary_color": "#10B981",
        "background_color": "#F9FAFB",
        "text_color": "#1F2937",
        "border_color": "#E5E7EB",
        "accent_color": "#F59E0B",
        "success_color": "#10B981",
        "warning_color": "#F59E0B",
        "error_color": "#EF4444",
        "font_family": "Inter, system-ui, sans-serif",
        "font_size_base": 14,
        "border_radius": 8,
        "shadow_level": "md"
    },
    "refresh_interval": 300,
    "tags": ["test"],
    "data_source_assignments": []
}

def test_dashboard_creation():
    """Test dashboard creation with the fixed backend."""
    
    # You would need to replace this with a valid auth token
    # For now, this is just to show the expected request structure
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_AUTH_TOKEN_HERE"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/dashboards/",
            headers=headers,
            json=dashboard_data
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Dashboard creation successful!")
            return True
        else:
            print("❌ Dashboard creation failed!")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing dashboard creation fix...")
    print("Note: You need to replace YOUR_AUTH_TOKEN_HERE with a valid token")
    print("Expected request structure:")
    print(json.dumps(dashboard_data, indent=2))
