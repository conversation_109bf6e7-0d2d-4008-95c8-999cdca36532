/* Enhanced styling for analysis results and data visualizations */

/* Table styling enhancements */
.analysis-table {
  @apply shadow-lg border-2 border-gray-300 rounded-lg overflow-hidden;
}

.analysis-table table {
  @apply w-full border-collapse bg-white;
}

.analysis-table thead {
  @apply bg-gradient-to-r from-brand-600 to-brand-700 text-white;
}

.analysis-table th {
  @apply border border-gray-400 px-4 py-3 text-left font-bold text-white text-sm uppercase tracking-wide;
}

.analysis-table tbody {
  @apply divide-y divide-gray-200;
}

.analysis-table tr {
  @apply hover:bg-gray-50 transition-colors duration-150;
}

.analysis-table td {
  @apply border border-gray-300 px-4 py-3 text-gray-900 font-medium;
}

/* Chart container styling */
.analysis-chart {
  @apply bg-white rounded-lg border border-gray-200 p-4 shadow-sm;
}

.analysis-chart img {
  @apply max-w-full max-h-[600px] object-contain rounded-md shadow-sm;
  min-height: 200px;
}

/* Markdown content styling for analysis results */
.analysis-markdown {
  @apply prose prose-sm max-w-none prose-gray;
}

.analysis-markdown h1 {
  @apply text-xl font-bold mb-3 text-gray-900 border-b border-gray-200 pb-2;
}

.analysis-markdown h2 {
  @apply text-lg font-semibold mb-2 text-gray-800 mt-4;
}

.analysis-markdown h3 {
  @apply text-base font-medium mb-2 text-gray-700 mt-3;
}

.analysis-markdown p {
  @apply mb-3;
}

.analysis-markdown ul {
  @apply list-disc list-inside mb-3 space-y-1;
}

.analysis-markdown ol {
  @apply list-decimal list-inside mb-3 space-y-1;
}

.analysis-markdown li {
  @apply mb-1;
}

.analysis-markdown code {
  @apply bg-gray-200 px-1 py-0.5 rounded text-sm font-mono;
}

.analysis-markdown pre {
  @apply bg-gray-100 p-3 rounded-lg overflow-x-auto mb-3;
}

.analysis-markdown blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic mb-3;
}

.analysis-markdown a {
  @apply text-blue-600 hover:text-blue-800 underline;
}

/* Responsive table wrapper */
.table-responsive {
  @apply overflow-x-auto;
}

/* Loading states for visualizations */
.visualization-loading {
  @apply flex items-center justify-center p-8 text-gray-500;
}

.visualization-error {
  @apply flex items-center justify-center p-8 text-red-500 bg-red-50 border border-red-200 rounded-lg;
}

/* Animation for visualization appearance */
.visualization-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced card styling for analysis results */
.analysis-card {
  @apply shadow-lg border-gray-200 w-full;
}

.analysis-card-header {
  @apply bg-gradient-to-r from-white to-gray-50 border-b border-gray-100;
}

.analysis-card-title {
  @apply text-brand-700;
}

.analysis-card-description {
  @apply text-gray-600;
}

/* Tab styling for analysis results */
.analysis-tabs {
  @apply w-full border-b rounded-none px-4 bg-gray-50;
}

/* Correlation matrix specific styling */
.correlation-matrix {
  @apply text-xs;
}

.correlation-matrix td {
  @apply text-center;
}

/* Statistical summary styling */
.stats-summary {
  @apply bg-gray-50 p-3 rounded text-sm;
}

.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2;
}

.stats-item {
  @apply flex items-center justify-between bg-white p-2 rounded border;
}

/* Data type badges */
.dtype-badge {
  @apply text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded;
}

/* Missing values indicators */
.missing-values-good {
  @apply text-green-600 font-medium;
}

.missing-values-bad {
  @apply text-red-600 font-medium;
}

/* Hover effects for interactive elements */
.interactive-hover {
  @apply transition-all duration-200 hover:shadow-md hover:scale-105;
}

/* Print styles for analysis results */
@media print {
  .analysis-table {
    @apply shadow-none border border-gray-400;
  }
  
  .analysis-chart {
    @apply shadow-none border border-gray-400;
  }
  
  .analysis-card {
    @apply shadow-none border border-gray-400;
  }
}

/* Dark mode support (if needed in the future) */
@media (prefers-color-scheme: dark) {
  .analysis-table {
    @apply bg-gray-800 border-gray-600;
  }
  
  .analysis-table th {
    @apply bg-gray-700 text-gray-100 border-gray-600;
  }
  
  .analysis-table td {
    @apply bg-gray-800 text-gray-100 border-gray-600;
  }
  
  .analysis-chart {
    @apply bg-gray-800 border-gray-600;
  }
}
