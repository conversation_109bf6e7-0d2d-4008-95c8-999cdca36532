"""
File API endpoints for the Datagenius backend.

This module provides API endpoints for file management.
"""

import logging
import uuid
import os
import pandas as pd
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from sqlalchemy.orm import Session

from ..models.file import FileResponse, FileListResponse
from ..models.auth import User
from ..database import get_db, create_file, get_file, get_user_files, delete_file, get_data_sources_by_file_id, delete_data_source
from ..auth import get_current_active_user
from .. import config
from ..middleware.security_middleware import secure_file_uploader, memory_manager

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/files", tags=["Files"])


@router.post("", response_model=FileResponse)
async def upload_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Upload a file using secure streaming approach.

    Args:
        file: The file to upload
        db: Database session
        current_user: Current authenticated user

    Returns:
        File information
    """
    logger.info(f"User {current_user.id} uploading file {file.filename}")

    # Use memory monitoring for the upload process
    async with memory_manager.monitor_memory(f"file_upload_{file.filename}"):
        try:
            # Use secure file uploader with streaming
            file_path, file_info = await secure_file_uploader.upload_file_streaming(
                file, str(current_user.id)
            )

            # Extract metadata for CSV and Excel files
            num_rows = None
            columns = None
            file_extension = os.path.splitext(file.filename.lower())[1]

            if file_extension in [".csv", ".xlsx", ".xls"]:
                try:
                    if file_extension == ".csv":
                        df = pd.read_csv(file_path)
                    else:
                        df = pd.read_excel(file_path)

                    num_rows = len(df)
                    columns = df.columns.tolist()

                    logger.info(f"Extracted metadata: {num_rows} rows, {len(columns)} columns")
                except Exception as e:
                    logger.error(f"Error extracting metadata from file: {str(e)}", exc_info=True)
                    # Continue without metadata rather than failing the upload

            # Create file record in database
            db_file = create_file(
                db,
                user_id=current_user.id,
                filename=file.filename,
                file_path=file_path,
                file_size=file_info["file_size"],
                num_rows=num_rows,
                columns=columns
            )

            logger.info(f"File {file.filename} uploaded successfully with ID {db_file.id}")

            # Store file context in mem0 for persistent access
            try:
                import time
                from agents.utils.memory_service import MemoryService
                memory_service = MemoryService()

                # Create file information for memory storage using the secure file info
                memory_file_info = {
                    "id": db_file.id,
                    "name": db_file.filename,
                    "type": "file",
                    "file_path": db_file.file_path,
                    "file_size": db_file.file_size,
                    "num_rows": db_file.num_rows,
                    "columns": db_file.columns,
                    "file_hash": file_info.get("file_hash"),
                    "upload_timestamp": file_info.get("upload_timestamp")
                }

                # Store in memory with metadata
                memory_metadata = {
                    "timestamp": time.time(),
                    "upload_session": "secure_file_upload",
                    "security_validated": True
                }

                # Note: We don't have conversation_id here, so we'll store it as a general file upload
                # It will be associated with conversations when used in chat
                memory_service.add_memory(
                    content=f"User securely uploaded file '{db_file.filename}' with {num_rows or 'unknown'} rows and {len(columns) if columns else 'unknown'} columns. File ID: {db_file.id}",
                    user_id=str(current_user.id),
                    metadata={
                        **memory_metadata,
                        "type": "file_upload",
                        "file_id": db_file.id,
                        "file_name": db_file.filename,
                        "file_hash": file_info.get("file_hash")
                    }
                )

                logger.info(f"Stored secure file context in memory for file: {db_file.filename}")

            except Exception as e:
                logger.error(f"Error storing file context in memory: {e}")
                # Continue without failing the upload

            return db_file

        except HTTPException:
            # Re-raise HTTP exceptions (validation errors, etc.)
            raise
        except Exception as e:
            logger.error(f"Unexpected error during file upload: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="File upload failed")


@router.get("", response_model=FileListResponse)
async def list_files(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    List files.

    Args:
        skip: Number of files to skip
        limit: Maximum number of files to return
        db: Database session
        current_user: Current authenticated user

    Returns:
        List of files
    """
    logger.info(f"User {current_user.id} listing files")

    files = get_user_files(db, current_user.id, skip, limit)
    return {"files": files}


@router.get("/{file_id}", response_model=FileResponse)
async def get_file_info(
    file_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get file information.

    Args:
        file_id: ID of the file
        db: Database session
        current_user: Current authenticated user

    Returns:
        File information
    """
    logger.info(f"User {current_user.id} getting file {file_id}")

    file = get_file(db, file_id)
    if not file:
        logger.warning(f"File {file_id} not found")
        raise HTTPException(status_code=404, detail="File not found")

    if file.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to access file {file_id} owned by user {file.user_id}")
        raise HTTPException(status_code=403, detail="Not authorized to access this file")

    return file


@router.get("/{file_id}/related-datasources")
async def get_file_related_datasources(
    file_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get data sources that are related to a specific file.

    Args:
        file_id: ID of the file
        db: Database session
        current_user: Current authenticated user

    Returns:
        List of related data sources
    """
    logger.info(f"User {current_user.id} checking related datasources for file {file_id}")

    file = get_file(db, file_id)
    if not file:
        logger.warning(f"File {file_id} not found")
        raise HTTPException(status_code=404, detail="File not found")

    if file.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to access file {file_id} owned by user {file.user_id}")
        raise HTTPException(status_code=403, detail="Not authorized to access this file")

    # Get related data sources
    related_datasources = get_data_sources_by_file_id(db, file_id, current_user.id)

    return {
        "file_id": file_id,
        "related_datasources": [
            {
                "id": ds.id,
                "name": ds.name,
                "type": ds.type,
                "description": ds.description,
                "is_active": ds.is_active,
                "created_at": ds.created_at.isoformat(),
                "updated_at": ds.updated_at.isoformat()
            }
            for ds in related_datasources
        ]
    }


@router.delete("/{file_id}")
async def delete_file_endpoint(
    file_id: str,
    delete_related_datasources: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a file and optionally its related data sources.

    Args:
        file_id: ID of the file
        delete_related_datasources: Whether to also delete related data sources
        db: Database session
        current_user: Current authenticated user

    Returns:
        Success message
    """
    logger.info(f"User {current_user.id} deleting file {file_id}, delete_related_datasources={delete_related_datasources}")

    file = get_file(db, file_id)
    if not file:
        logger.warning(f"File {file_id} not found")
        raise HTTPException(status_code=404, detail="File not found")

    if file.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to delete file {file_id} owned by user {file.user_id}")
        raise HTTPException(status_code=403, detail="Not authorized to delete this file")

    # If requested, delete related data sources first
    if delete_related_datasources:
        related_datasources = get_data_sources_by_file_id(db, file_id, current_user.id)
        for ds in related_datasources:
            logger.info(f"Deleting related data source {ds.id} ({ds.name})")
            delete_data_source(db, ds.id)

    # Delete the file from disk
    try:
        os.remove(file.file_path)
    except Exception as e:
        logger.error(f"Error deleting file from disk: {str(e)}", exc_info=True)

    # Delete the file from the database
    delete_file(db, file_id)

    return {"message": "File deleted successfully"}
