/**
 * Utility for tracking user activity and implementing an idle timeout
 */

type IdleTimerCallback = () => void;

class IdleTimer {
  private timeout: number;
  private onIdle: IdleTimerCallback;
  private timer: number | null = null;
  private isIdle = false;
  private activityEvents = [
    'mousedown', 'mousemove', 'keypress', 
    'scroll', 'touchstart', 'click', 'keydown'
  ];

  /**
   * Create a new idle timer
   * @param onIdle Callback function to execute when user becomes idle
   * @param idleTime Time in milliseconds before user is considered idle (default: 30 minutes)
   */
  constructor(onIdle: IdleTimerCallback, idleTime: number = 30 * 60 * 1000) {
    this.timeout = idleTime;
    this.onIdle = onIdle;
  }

  /**
   * Start tracking user activity
   */
  start(): void {
    // Reset any existing timer
    this.reset();

    // Set up the initial timer
    this.timer = window.setTimeout(() => this.handleIdle(), this.timeout);

    // Add event listeners for user activity
    this.activityEvents.forEach(event => {
      window.addEventListener(event, this.handleUserActivity);
    });

    console.log(`Idle timer started with timeout of ${this.timeout / 1000} seconds`);
  }

  /**
   * Stop tracking user activity
   */
  stop(): void {
    if (this.timer) {
      window.clearTimeout(this.timer);
      this.timer = null;
    }

    // Remove event listeners
    this.activityEvents.forEach(event => {
      window.removeEventListener(event, this.handleUserActivity);
    });

    this.isIdle = false;
    console.log('Idle timer stopped');
  }

  /**
   * Reset the idle timer
   */
  reset(): void {
    if (this.timer) {
      window.clearTimeout(this.timer);
      this.timer = window.setTimeout(() => this.handleIdle(), this.timeout);
    }
    this.isIdle = false;
  }

  /**
   * Handle user activity events
   */
  private handleUserActivity = (): void => {
    if (this.isIdle) {
      console.log('User activity detected after idle state');
    }
    this.reset();
  };

  /**
   * Handle idle state
   */
  private handleIdle(): void {
    this.isIdle = true;
    console.log('User is idle, executing idle callback');
    this.onIdle();
  }

  /**
   * Update the idle timeout
   * @param idleTime New idle timeout in milliseconds
   */
  updateTimeout(idleTime: number): void {
    this.timeout = idleTime;
    this.reset();
    console.log(`Idle timeout updated to ${idleTime / 1000} seconds`);
  }
}

export default IdleTimer;
