"""
Hugging Face classifier for the Datagenius classification agent.

This module provides functions for classifying text using Hugging Face models.
"""

import logging
import pandas as pd
from typing import List, Dict, Any, Optional, Union
import asyncio
from tqdm import tqdm

# Configure logging
logger = logging.getLogger(__name__)

async def classify_texts_with_hf(
    texts: List[str],
    categories: Optional[List[str]] = None,
    model_name: str = "facebook/bart-large-mnli",
    threshold: float = 0.5,
    batch_size: int = 16,
    sample_size: Optional[int] = None
) -> pd.DataFrame:
    """
    Classify texts using a Hugging Face model.

    Args:
        texts: List of texts to classify
        categories: List of categories to classify into (optional)
        model_name: Name of the Hugging Face model to use
        threshold: Classification threshold
        batch_size: Batch size for processing
        sample_size: Optional sample size to limit processing

    Returns:
        DataFrame with classification results
    """
    try:
        # Import here to avoid hard dependencies
        from transformers import AutoModelForSequenceClassification, AutoTokenizer
        import torch
    except ImportError:
        logger.error("transformers package not installed. Install with 'pip install transformers torch'")
        raise ImportError("transformers package not installed")

    logger.info(f"Classifying {len(texts)} texts with Hugging Face model {model_name}")

    # Use default categories if none provided
    if not categories:
        categories = ["positive", "negative", "neutral"]
    
    # Sample texts if sample_size is provided
    if sample_size and sample_size < len(texts):
        import random
        sampled_indices = random.sample(range(len(texts)), sample_size)
        sampled_texts = [texts[i] for i in sampled_indices]
        texts_to_process = sampled_texts
        logger.info(f"Sampled {sample_size} texts for classification")
    else:
        texts_to_process = texts

    try:
        # Load model and tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForSequenceClassification.from_pretrained(model_name)
        
        # Check if GPU is available
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model = model.to(device)
        logger.info(f"Using device: {device}")

        # Process texts in batches
        results = []
        for i in tqdm(range(0, len(texts_to_process), batch_size), desc="Classifying texts"):
            batch_texts = texts_to_process[i:i+batch_size]
            
            # Tokenize inputs
            inputs = tokenizer(batch_texts, return_tensors="pt", padding=True, truncation=True, max_length=512)
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # Get model predictions
            with torch.no_grad():
                outputs = model(**inputs)
                
            # Process predictions
            probs = torch.nn.functional.softmax(outputs.logits, dim=-1)
            predictions = torch.argmax(probs, dim=-1)
            
            # Convert to results
            for j, (text, pred, prob) in enumerate(zip(batch_texts, predictions, probs)):
                pred_idx = pred.item()
                confidence = prob[pred_idx].item()
                
                if confidence >= threshold:
                    category = model.config.id2label.get(pred_idx, "Unknown")
                    results.append({
                        "text": text,
                        "category": category,
                        "confidence": confidence
                    })
                else:
                    results.append({
                        "text": text,
                        "category": "Unclassified",
                        "confidence": confidence
                    })
        
        # Convert results to DataFrame
        results_df = pd.DataFrame(results)
        logger.info(f"Classification complete. Classified {len(results_df)} texts.")
        return results_df
        
    except Exception as e:
        logger.error(f"Error classifying texts with Hugging Face: {str(e)}", exc_info=True)
        # Return empty DataFrame with expected columns
        return pd.DataFrame(columns=["text", "category", "confidence"])
