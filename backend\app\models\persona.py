"""
Persona models for the Datagenius backend.

This module provides Pydantic models for AI persona-related functionality.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class PersonaBase(BaseModel):
    """Base model for persona data."""
    id: str
    name: str
    description: str
    industry: str
    skills: List[str]
    rating: float
    review_count: int
    image_url: str
    is_available: bool = True
    capabilities: Optional[List[str]] = None
    # Additional fields for admin management
    price: float = 10.0
    provider: str = "groq"
    model: Optional[str] = None
    is_active: bool = True
    age_restriction: int = Field(0, ge=0)
    content_filters: Optional[Dict[str, Any]] = None
    # Purchase status
    is_purchased: bool = False


class PersonaResponse(BaseModel):
    """Model for persona response."""
    persona: PersonaBase


class PersonaListResponse(BaseModel):
    """Model for persona list response."""
    personas: List[PersonaBase]


class UserPersonaAccessResponse(BaseModel):
    """Model for user persona access response."""
    has_access: bool
    is_purchased: bool


class PersonaVersionBase(BaseModel):
    """Base model for persona version data."""
    id: str
    persona_id: str
    version: str
    config: Dict[str, Any]
    is_active: bool = False
    created_at: Optional[datetime] = None
    created_by: Optional[int] = None
    is_file_only: bool = False


class PersonaVersionCreate(BaseModel):
    """Model for creating a new persona version."""
    version: str
    config: Dict[str, Any]
    is_active: bool = False


class PersonaVersionResponse(BaseModel):
    """Model for persona version response."""
    version: PersonaVersionBase


class PersonaVersionListResponse(BaseModel):
    """Model for persona version list response."""
    versions: List[PersonaVersionBase]
