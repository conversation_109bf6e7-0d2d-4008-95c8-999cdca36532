"""
Exception hierarchy and error handling for marketing agent.

This module provides a comprehensive exception hierarchy and error handling
utilities for the marketing agent system.
"""

import logging
from typing import Dict, Any, Optional, List
from enum import Enum

logger = logging.getLogger(__name__)


class ErrorSeverity(str, Enum):
    """Error severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class ErrorCategory(str, Enum):
    """Error categories for classification."""
    VALIDATION = "validation"
    PROCESSING = "processing"
    EXTERNAL_SERVICE = "external_service"
    CONFIGURATION = "configuration"
    AUTHENTICATION = "authentication"
    RATE_LIMITING = "rate_limiting"
    NETWORK = "network"
    INTERNAL = "internal"


class MarketingAgentException(Exception):
    """Base exception for marketing agent errors."""
    
    def __init__(
        self,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        category: ErrorCategory = ErrorCategory.INTERNAL,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.user_message = user_message or self._generate_user_message()
    
    def _generate_user_message(self) -> str:
        """Generate a user-friendly error message."""
        return "I encountered an issue while processing your request. Please try again."
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging/serialization."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "user_message": self.user_message,
            "category": self.category.value,
            "severity": self.severity.value,
            "context": self.context
        }


class ValidationError(MarketingAgentException):
    """Exception for validation errors."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        validation_issues: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        self.field = field
        self.validation_issues = validation_issues or []
    
    def _generate_user_message(self) -> str:
        if self.field:
            return f"There's an issue with the {self.field} field. Please check your input and try again."
        return "Please check your input and try again."


class ContentGenerationError(MarketingAgentException):
    """Exception for content generation errors."""
    
    def __init__(self, message: str, provider: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CONTENT_GENERATION_ERROR",
            category=ErrorCategory.PROCESSING,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        self.provider = provider
    
    def _generate_user_message(self) -> str:
        return "I'm having trouble generating content right now. Please try again in a moment."


class ExternalServiceError(MarketingAgentException):
    """Exception for external service errors."""
    
    def __init__(
        self,
        message: str,
        service_name: str,
        status_code: Optional[int] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            category=ErrorCategory.EXTERNAL_SERVICE,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        self.service_name = service_name
        self.status_code = status_code
    
    def _generate_user_message(self) -> str:
        return f"I'm having trouble connecting to {self.service_name}. Please try again in a moment."


class AuthenticationError(MarketingAgentException):
    """Exception for authentication errors."""
    
    def __init__(self, message: str, provider: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.CRITICAL,
            **kwargs
        )
        self.provider = provider
    
    def _generate_user_message(self) -> str:
        return "There's an authentication issue. Please contact support if this persists."


class RateLimitError(MarketingAgentException):
    """Exception for rate limiting errors."""
    
    def __init__(
        self,
        message: str,
        retry_after: Optional[int] = None,
        provider: Optional[str] = None,
        **kwargs
    ):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            category=ErrorCategory.RATE_LIMITING,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        self.retry_after = retry_after
        self.provider = provider
    
    def _generate_user_message(self) -> str:
        if self.retry_after:
            return f"I'm currently rate limited. Please try again in {self.retry_after} seconds."
        return "I'm currently rate limited. Please try again in a few minutes."


class ConfigurationError(MarketingAgentException):
    """Exception for configuration errors."""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            category=ErrorCategory.CONFIGURATION,
            severity=ErrorSeverity.CRITICAL,
            **kwargs
        )
        self.config_key = config_key
    
    def _generate_user_message(self) -> str:
        return "There's a configuration issue. Please contact support."


class NetworkError(MarketingAgentException):
    """Exception for network-related errors."""
    
    def __init__(self, message: str, endpoint: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="NETWORK_ERROR",
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        self.endpoint = endpoint
    
    def _generate_user_message(self) -> str:
        return "I'm having network connectivity issues. Please try again in a moment."


class ErrorHandler:
    """Centralized error handler for marketing agent."""
    
    def __init__(self):
        self.error_counts = {}
        self.error_patterns = {}
    
    def handle_exception(
        self,
        exception: Exception,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Handle an exception and return appropriate response.
        
        Args:
            exception: The exception to handle
            context: Additional context information
            
        Returns:
            Dictionary with error response
        """
        # Convert to MarketingAgentException if needed
        if not isinstance(exception, MarketingAgentException):
            marketing_exception = self._convert_exception(exception, context)
        else:
            marketing_exception = exception
        
        # Log the error
        self._log_error(marketing_exception, context)
        
        # Track error for monitoring
        self._track_error(marketing_exception)
        
        # Generate response
        return self._generate_error_response(marketing_exception)
    
    def _convert_exception(
        self,
        exception: Exception,
        context: Optional[Dict[str, Any]] = None
    ) -> MarketingAgentException:
        """Convert generic exception to MarketingAgentException."""
        error_message = str(exception)
        
        # Pattern matching for common errors
        if "connection" in error_message.lower() or "timeout" in error_message.lower():
            return NetworkError(
                message=error_message,
                context=context
            )
        elif "rate limit" in error_message.lower():
            return RateLimitError(
                message=error_message,
                context=context
            )
        elif "authentication" in error_message.lower() or "api key" in error_message.lower():
            return AuthenticationError(
                message=error_message,
                context=context
            )
        else:
            return MarketingAgentException(
                message=error_message,
                context=context
            )
    
    def _log_error(
        self,
        exception: MarketingAgentException,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log error with appropriate level."""
        log_data = exception.to_dict()
        if context:
            log_data["context"].update(context)
        
        if exception.severity == ErrorSeverity.CRITICAL:
            logger.critical(f"Critical error: {exception.message}", extra=log_data)
        elif exception.severity == ErrorSeverity.HIGH:
            logger.error(f"High severity error: {exception.message}", extra=log_data)
        elif exception.severity == ErrorSeverity.MEDIUM:
            logger.warning(f"Medium severity error: {exception.message}", extra=log_data)
        else:
            logger.info(f"Low severity error: {exception.message}", extra=log_data)
    
    def _track_error(self, exception: MarketingAgentException) -> None:
        """Track error for monitoring and analytics."""
        error_key = f"{exception.category.value}:{exception.error_code}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
    
    def _generate_error_response(self, exception: MarketingAgentException) -> Dict[str, Any]:
        """Generate error response for the user."""
        return {
            "message": exception.user_message,
            "metadata": {
                "error": True,
                "error_code": exception.error_code,
                "error_category": exception.category.value,
                "error_severity": exception.severity.value,
                "timestamp": logger.handlers[0].formatter.formatTime(
                    logger.makeRecord(
                        logger.name, logging.ERROR, "", 0, "", (), None
                    )
                ) if logger.handlers else None
            }
        }
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics for monitoring."""
        return {
            "error_counts": self.error_counts.copy(),
            "total_errors": sum(self.error_counts.values())
        }


# Global error handler instance
error_handler = ErrorHandler()
