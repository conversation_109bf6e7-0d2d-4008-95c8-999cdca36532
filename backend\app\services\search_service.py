"""
Search Service for the Datagenius backend.

This module provides services for logging search activities,
performing searches (including semantic search placeholders),
and generating recommendations.
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import String # Added String import
from fastapi import Depends
import uuid
import logging # Added logging

from ..models.search import (
    UserSearchActivity,
    SearchQueryLogCreate, SearchQueryLogResponse,
    SearchRecommendationRequest, SearchRecommendation, SearchRecommendationResponse
)
from ..database import get_db, User as UserModelDB # SQLAlchemy User model
    # Placeholder for a more sophisticated search client (e.g., Elasticsearch, OpenSearch)
    # from ..search_client import SearchClient

logger = logging.getLogger(__name__) # Initialize logger

class SearchService:
    def __init__(self, db: Session): #, search_client: Optional[SearchClient] = None):
        self.db = db
        # self.search_client = search_client or SearchClient() # Initialize your search client

    async def log_search_activity(self, log_data: SearchQueryLogCreate) -> UserSearchActivity:
        """
        Logs a user's search query and activity.
        """
        db_log_item = UserSearchActivity(
            id=str(uuid.uuid4()),
            **log_data.model_dump(exclude_unset=True)
        )
        self.db.add(db_log_item)
        self.db.commit()
        self.db.refresh(db_log_item)
        return db_log_item

    async def perform_search(self, query: str, user_id: Optional[int] = None,
                             filters: Optional[Dict[str, Any]] = None,
                             limit: int = 10, offset: int = 0) -> Dict[str, Any]:
        """
        Performs a search.
        Placeholder for actual search logic (e.g., Elasticsearch, semantic search).
        Currently returns dummy data.
        """
        # Log the search query
        log_entry = SearchQueryLogCreate(query=query, user_id=user_id, filters_applied=filters)
        # In a real scenario, result_count would be set after getting search results
        # await self.log_search_activity(log_entry)

        # Placeholder: Semantic search logic would go here
        # results = self.search_client.search(query, filters, limit, offset)

        # Enhanced search with real data integration
        try:
            search_results = await self._perform_enhanced_search(query, filters, limit, offset, user_id)

            # Update log with result count
            log_entry.result_count = len(search_results)
            await self.log_search_activity(log_entry)

            return {
                "query": query,
                "filters": filters,
                "results": search_results,
                "total_count": len(search_results),
                "limit": limit,
                "offset": offset
            }

        except Exception as e:
            logger.error(f"Error in enhanced search: {e}")
            # Return empty results instead of dummy data
            log_entry.result_count = 0
            await self.log_search_activity(log_entry)

            return {
                "query": query,
                "filters": filters,
                "results": [],
                "total_count": 0,
                "limit": limit,
                "offset": offset,
                "error": "Search service temporarily unavailable"
            }

    async def _perform_enhanced_search(self, query: str, filters: Optional[Dict[str, Any]],
                                     limit: int, offset: int, user_id: Optional[int]) -> List[Dict[str, Any]]:
        """Perform enhanced search across multiple data sources."""
        import logging
        logger = logging.getLogger(__name__)

        results = []

        try:
            # Search personas
            persona_results = await self._search_personas(query, filters, user_id)
            results.extend(persona_results)

            # Search conversations/chat history
            chat_results = await self._search_conversations(query, filters, user_id)
            results.extend(chat_results)

            # Search data sources
            data_results = await self._search_data_sources(query, filters, user_id)
            results.extend(data_results)

            # Sort by relevance score and apply pagination
            results.sort(key=lambda x: x.get('score', 0), reverse=True)

            # Apply offset and limit
            paginated_results = results[offset:offset + limit]

            return paginated_results

        except Exception as e:
            logger.error(f"Error in enhanced search: {e}")
            return []

    async def _search_personas(self, query: str, filters: Optional[Dict[str, Any]],
                              user_id: Optional[int]) -> List[Dict[str, Any]]:
        """Search through available personas."""
        results = []

        try:
            # Import here to avoid circular imports
            from ..database import Persona # Changed
            from sqlalchemy import or_ # String is now imported at the top

            # Build query
            db_query = self.db.query(Persona).filter(Persona.is_active == True)

            # Apply type filter
            if not filters or filters.get('type') in [None, 'persona']:
                # Add text search on name, description, industry
                search_terms = query.lower().split()
                for term in search_terms:
                    db_query = db_query.filter(
                        or_(
                            Persona.name.ilike(f'%{term}%'),
                            Persona.description.ilike(f'%{term}%'),
                            Persona.industry.ilike(f'%{term}%')
                        )
                    )

                personas = db_query.limit(5).all()

                for persona in personas:
                    # Calculate relevance score based on query match
                    relevance_score = self._calculate_persona_relevance(query, persona)

                    results.append({
                        "id": f"persona_{persona.id}",
                        "type": "persona",
                        "title": persona.name,
                        "score": relevance_score,
                        "snippet": persona.description[:100] + "..." if len(persona.description) > 100 else persona.description,
                        "metadata": {
                            "industry": persona.industry,
                            "rating": persona.rating,
                            "price": persona.price
                        }
                    })

        except Exception as e:
            logger.error(f"Error searching personas: {e}")

        return results

    async def _search_conversations(self, query: str, filters: Optional[Dict[str, Any]],
                                   user_id: Optional[int]) -> List[Dict[str, Any]]:
        """Search through conversation history."""
        results = []

        try:
            if user_id and (not filters or filters.get('type') in [None, 'conversation']):
                # Import here to avoid circular imports
                from ..models.chat import Conversation, Message
                from sqlalchemy import or_

                # Search in messages
                search_terms = query.lower().split()
                message_query = self.db.query(Message).join(Conversation).filter(
                    Conversation.user_id == user_id
                )

                for term in search_terms:
                    message_query = message_query.filter(
                        Message.content.ilike(f'%{term}%')
                    )

                messages = message_query.limit(3).all()

                for message in messages:
                    relevance_score = self._calculate_text_relevance(query, message.content)

                    results.append({
                        "id": f"conversation_{message.conversation_id}",
                        "type": "conversation",
                        "title": f"Chat: {message.content[:50]}...",
                        "score": relevance_score,
                        "snippet": message.content[:150] + "..." if len(message.content) > 150 else message.content,
                        "metadata": {
                            "created_at": message.created_at.isoformat(),
                            "persona_id": message.persona_id
                        }
                    })

        except Exception as e:
            logger.error(f"Error searching conversations: {e}")

        return results

    async def _search_data_sources(self, query: str, filters: Optional[Dict[str, Any]],
                                  user_id: Optional[int]) -> List[Dict[str, Any]]:
        """Search through data sources."""
        results = []

        try:
            if user_id and (not filters or filters.get('type') in [None, 'data_source']):
                # Import here to avoid circular imports
                from ..models.data_source import DataSource # Changed
                from sqlalchemy import or_

                # Build query for user's data sources
                db_query = self.db.query(DataSource).filter(DataSource.user_id == user_id)

                # Add text search on name, description
                search_terms = query.lower().split()
                for term in search_terms:
                    db_query = db_query.filter(
                        or_(
                            DataSource.name.ilike(f'%{term}%'),
                            DataSource.description.ilike(f'%{term}%')
                        )
                    )

                data_sources = db_query.limit(3).all()

                for ds in data_sources:
                    relevance_score = self._calculate_text_relevance(query, f"{ds.name} {ds.description or ''}")

                    results.append({
                        "id": f"data_{ds.id}",
                        "type": "data_source",
                        "title": ds.name,
                        "score": relevance_score,
                        "snippet": ds.description or "Data source",
                        "metadata": {
                            "source_type": ds.source_type,
                            "created_at": ds.created_at.isoformat()
                        }
                    })

        except Exception as e:
            logger.error(f"Error searching data sources: {e}")

        return results

    def _calculate_persona_relevance(self, query: str, persona) -> float:
        """Calculate relevance score for a persona based on query."""
        score = 0.0
        query_lower = query.lower()

        # Check name match
        if query_lower in persona.name.lower():
            score += 0.8

        # Check description match
        if persona.description and query_lower in persona.description.lower():
            score += 0.6

        # Check industry match
        if persona.industry and query_lower in persona.industry.lower():
            score += 0.4

        # Boost score based on rating
        if persona.rating:
            score += (persona.rating / 5.0) * 0.2

        return min(score, 1.0)

    def _calculate_text_relevance(self, query: str, text: str) -> float:
        """Calculate relevance score for text content."""
        if not text:
            return 0.0

        query_lower = query.lower()
        text_lower = text.lower()

        # Simple relevance calculation
        if query_lower == text_lower:
            return 1.0
        elif query_lower in text_lower:
            return 0.8
        else:
            # Check for partial word matches
            query_words = query_lower.split()
            text_words = text_lower.split()

            matches = sum(1 for word in query_words if word in text_words)
            return (matches / len(query_words)) * 0.6 if query_words else 0.0

    async def get_search_recommendations(self, request_data: SearchRecommendationRequest) -> SearchRecommendationResponse:
        """
        Generates search recommendations.
        Placeholder for recommendation logic (e.g., collaborative filtering, content-based).
        Currently returns dummy data.
        """
        # In a real implementation, use request_data.user_id, session_id, current_query, context_items
        # to fetch user history, item features, etc., and feed into a recommendation model.
        
        recommendations: List[SearchRecommendation] = []
        
        # Strategy 1: If current_query is provided, find similar items
        if request_data.current_query:
            try:
                # Perform a light search based on the current query
                # We can reuse parts of _perform_enhanced_search or a simplified version
                # For simplicity, let's assume we search personas based on the query
                from ..database import Persona # Changed
                from sqlalchemy import or_

                db_query = self.db.query(Persona).filter(Persona.is_active == True)
                search_terms = request_data.current_query.lower().split()
                for term in search_terms:
                    db_query = db_query.filter(
                        or_(
                            Persona.name.ilike(f'%{term}%'),
                            Persona.description.ilike(f'%{term}%')
                        )
                    )
                similar_personas = db_query.limit(request_data.limit // 2 or 2).all() # Get a few similar personas
                for p in similar_personas:
                    recommendations.append(SearchRecommendation(
                        item_id=p.id, 
                        item_type="persona", 
                        score=self._calculate_persona_relevance(request_data.current_query, p), # Reuse scoring
                        reasoning=f"Similar to your search for '{request_data.current_query}'"
                    ))
            except Exception as e:
                logger.error(f"Error generating recommendations based on current_query: {e}")

        # Strategy 2: Content-based filtering using context_items (e.g., recently viewed, cart items)
        # This is a simplified example. A real system might use item embeddings or collaborative filtering.
        if request_data.context_items:
            from ..database import Persona # Changed
            
            # Example: If context items are persona IDs, find personas with similar skills or industry
            context_persona_ids = [item.item_id for item in request_data.context_items if item.item_type == "persona"]
            if context_persona_ids:
                # Fetch details of context personas
                context_personas = self.db.query(Persona).filter(Persona.id.in_(context_persona_ids)).all()
                
                # Collect unique skills and industries from context personas
                relevant_skills = set()
                relevant_industries = set()
                for cp in context_personas:
                    if cp.skills: # skills is JSON
                        relevant_skills.update(cp.skills)
                    if cp.industry:
                        relevant_industries.add(cp.industry)
                
                # Find other personas matching these skills/industries
                if relevant_skills or relevant_industries:
                    q = self.db.query(Persona).filter(Persona.is_active == True, Persona.id.notin_(context_persona_ids))
                    
                    skill_filters = []
                    if relevant_skills:
                        # This is tricky with JSON array. A better way is to have a separate skills table or use JSON functions.
                        # For simplicity, let's assume skills is a simple list in JSON and we do a like search.
                        # This is NOT efficient for large datasets.
                        for skill in relevant_skills:
                             skill_filters.append(Persona.skills.cast(String).ilike(f'%"{skill}"%')) # Crude check
                    
                    industry_filters = []
                    if relevant_industries:
                        industry_filters.append(Persona.industry.in_(list(relevant_industries)))

                    if skill_filters and industry_filters:
                        q = q.filter(or_(*skill_filters, *industry_filters))
                    elif skill_filters:
                        q = q.filter(or_(*skill_filters))
                    elif industry_filters:
                        q = q.filter(or_(*industry_filters))
                    
                    related_personas = q.limit(request_data.limit // 2 or 2).all()
                    for p in related_personas:
                        if not any(r.item_id == p.id for r in recommendations): # Avoid duplicates
                             recommendations.append(SearchRecommendation(
                                item_id=p.id,
                                item_type="persona",
                                score=0.7, # Generic score for related items
                                reasoning="Related to items you've interacted with"
                            ))

        # Strategy 3: Popular items (fallback if few recommendations)
        if len(recommendations) < request_data.limit:
            from ..database import Persona # Changed
            # Example: Recommend popular personas (e.g., high rating, many reviews)
            # This is a simplified popularity metric.
            popular_personas = self.db.query(Persona)\
                .filter(Persona.is_active == True)\
                .order_by(Persona.rating.desc(), Persona.review_count.desc())\
                .limit(request_data.limit - len(recommendations))\
                .all()
            
            for p in popular_personas:
                if not any(r.item_id == p.id for r in recommendations): # Avoid duplicates
                    recommendations.append(SearchRecommendation(
                        item_id=p.id,
                        item_type="persona",
                        score=0.6, # Generic score for popular items
                        reasoning="Popular item"
                    ))
        
        # Ensure unique recommendations by item_id and sort by score
        final_recommendations_dict: Dict[str, SearchRecommendation] = {}
        for rec in recommendations:
            if rec.item_id not in final_recommendations_dict or \
               (rec.score or 0) > (final_recommendations_dict[rec.item_id].score or 0):
                final_recommendations_dict[rec.item_id] = rec
        
        sorted_recommendations = sorted(final_recommendations_dict.values(), key=lambda r: r.score or 0, reverse=True)

        return SearchRecommendationResponse(
            recommendations=sorted_recommendations[:request_data.limit],
            request_id=str(uuid.uuid4())
        )

    async def get_user_search_history(self, user_id: int, limit: int = 20, offset: int = 0) -> List[UserSearchActivity]:
        """
        Retrieves the search history for a given user.
        """
        return self.db.query(UserSearchActivity)\
            .filter(UserSearchActivity.user_id == user_id)\
            .order_by(UserSearchActivity.search_timestamp.desc())\
            .offset(offset)\
            .limit(limit)\
            .all()

def get_search_service(db: Session = Depends(get_db)):
    # search_client = SearchClient() # Initialize your actual search client here
    # return SearchService(db, search_client)
    return SearchService(db)
