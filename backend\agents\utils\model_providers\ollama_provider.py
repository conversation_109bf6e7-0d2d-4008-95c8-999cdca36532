"""
Ollama model provider for the Datagenius backend.

This module provides a model provider implementation for Ollama (local models).
"""

import logging
import requests
from typing import Dict, Any, List, Union, Optional

# Import LangChain models with compatibility for different versions
try:
    # Try newer LangChain structure
    from langchain_core.language_models.base import BaseLanguageModel
    from langchain_core.language_models.chat_models import BaseChatModel
except ImportError:
    try:
        # Try older LangChain structure
        from langchain.schema.language_model import BaseLanguageModel
        from langchain.chat_models.base import BaseChatModel
    except ImportError:
        # Fallback to even older structure
        from langchain.base_language import BaseLanguageModel
        from langchain.chat_models.base import BaseChatModel

from .base import BaseModelProvider
from .exceptions import ModelInitializationError, ModelNotFoundError
from .config import get_provider_config

# Configure logging
logger = logging.getLogger(__name__)


class OllamaProvider(BaseModelProvider):
    """Model provider implementation for Ollama."""

    @property
    def provider_id(self) -> str:
        """Get the provider ID."""
        return "ollama"

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        return "Ollama"

    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the provider with configuration.

        Args:
            config: Configuration dictionary for the provider
        """
        await super().initialize(config)

        # Get provider configuration
        provider_config = get_provider_config("ollama")

        # Set default model if not specified
        if not self._default_model_id:
            self._default_model_id = provider_config.get("default_model", "llama3")

        # Set default endpoint if not specified
        if not self._endpoint:
            self._endpoint = provider_config.get("endpoint", "http://localhost:11434")

    async def _initialize_model(self, model_id: str, config: Dict[str, Any]) -> Union[BaseLanguageModel, BaseChatModel]:
        """
        Initialize a model instance.

        Args:
            model_id: ID of the model to initialize
            config: Configuration for the model

        Returns:
            Initialized model instance

        Raises:
            ModelInitializationError: If there's an error initializing the model
            ModelNotFoundError: If the model is not found
        """
        try:
            # Check if the model exists
            models = await self.list_models()
            model_exists = any(model["id"] == model_id for model in models)

            if not model_exists:
                # If the model doesn't exist but is a known model, we'll try anyway
                known_models = ["llama3", "llama2", "mistral", "gemma", "phi"]
                if model_id not in known_models and not any(model_id.startswith(prefix) for prefix in known_models):
                    raise ModelNotFoundError(f"Model '{model_id}' not found in Ollama")

            # Import here to avoid hard dependencies
            try:
                from langchain_community.chat_models import ChatOllama
            except ImportError:
                logger.warning("langchain-ollama not installed, attempting to install...")
                import subprocess
                subprocess.check_call(["pip", "install", "langchain-ollama"])
                from langchain_community.chat_models import ChatOllama

            # Initialize the model
            model = ChatOllama(
                temperature=config.get("temperature", 0.7),
                model=model_id,
                base_url=self._endpoint
            )

            logger.info(f"Initialized Ollama model '{model_id}'")
            return model

        except ImportError as e:
            raise ModelInitializationError(f"Error importing Ollama: {str(e)}")
        except Exception as e:
            raise ModelInitializationError(f"Error initializing Ollama model '{model_id}': {str(e)}")

    async def _fetch_models(self) -> List[Dict[str, Any]]:
        """
        Fetch available models from Ollama.

        Returns:
            List of model metadata dictionaries
        """
        try:
            # Make request to Ollama API
            response = requests.get(f"{self._endpoint}/api/tags", timeout=5)
            response.raise_for_status()

            # Parse response
            data = response.json()
            models = data.get("models", [])

            # Format models
            formatted_models = []
            for model in models:
                model_id = model.get("name", "")

                # Get display name
                display_name = model_id
                # Try to make a nicer display name
                if ":" in display_name:
                    display_name = display_name.split(":")[0]

                # Capitalize first letter of each word
                display_name = " ".join(word.capitalize() for word in display_name.split("-"))

                # Get model size from model details
                model_size = ""
                if "details" in model:
                    parameter_count = model.get("details", {}).get("parameter_count", 0)
                    if parameter_count > 0:
                        # Convert to billions
                        billions = parameter_count / 1_000_000_000
                        if billions >= 1:
                            model_size = f" ({billions:.1f}B)"

                formatted_models.append({
                    "id": model_id,
                    "name": f"{display_name}{model_size}",
                    "description": f"Local Ollama model: {model_id}",
                    "context_length": model.get("details", {}).get("context_length", 4096),
                    "provider": "ollama"
                })

            return formatted_models

        except Exception as e:
            logger.error(f"Error fetching Ollama models: {str(e)}", exc_info=True)
            # Return a static list of known models as a fallback
            return [
                {
                    "id": "llama3",
                    "name": "Llama 3",
                    "description": "Meta's Llama 3 model (local)",
                    "context_length": 8192,
                    "provider": "ollama"
                },
                {
                    "id": "llama2",
                    "name": "Llama 2",
                    "description": "Meta's Llama 2 model (local)",
                    "context_length": 4096,
                    "provider": "ollama"
                },
                {
                    "id": "mistral",
                    "name": "Mistral",
                    "description": "Mistral AI's model (local)",
                    "context_length": 8192,
                    "provider": "ollama"
                }
            ]

    async def is_available(self) -> bool:
        """
        Check if Ollama is available.

        Returns:
            True if Ollama is available, False otherwise
        """
        try:
            # Try to connect to the Ollama API
            response = requests.get(f"{self._endpoint}/api/tags", timeout=2)
            return response.status_code == 200
        except Exception:
            return False
