"""
Validation framework for marketing agent inputs and outputs.

This module provides comprehensive validation for marketing tasks,
user inputs, and generated content.
"""

import logging
import re
from typing import Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum

logger = logging.getLogger(__name__)


class MarketingTaskType(str, Enum):
    """Enumeration of valid marketing task types."""
    MARKETING_STRATEGY = "marketing_strategy"
    CAMPAIGN_STRATEGY = "campaign_strategy"
    SOCIAL_MEDIA_CONTENT = "social_media_content"
    SEO_OPTIMIZATION = "seo_optimization"
    POST_COMPOSER = "post_composer"


class ValidationSeverity(str, Enum):
    """Validation issue severity levels."""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ValidationIssue(BaseModel):
    """Represents a validation issue."""
    field: str
    message: str
    severity: ValidationSeverity
    code: str


class ValidationResult(BaseModel):
    """Result of validation process."""
    is_valid: bool
    issues: List[ValidationIssue] = Field(default_factory=list)
    warnings: List[ValidationIssue] = Field(default_factory=list)
    
    @property
    def has_errors(self) -> bool:
        """Check if there are any error-level issues."""
        return any(issue.severity == ValidationSeverity.ERROR for issue in self.issues)
    
    @property
    def has_warnings(self) -> bool:
        """Check if there are any warning-level issues."""
        return any(issue.severity == ValidationSeverity.WARNING for issue in self.issues)


class MarketingTaskValidator:
    """Validator for marketing task inputs."""
    
    def __init__(self):
        self.min_description_length = 10
        self.max_description_length = 5000
        self.required_fields = ["task_type"]
        self.recommended_fields = ["brand_description", "target_audience", "marketing_goals"]
    
    def validate_task(self, task_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate a marketing task.
        
        Args:
            task_data: Dictionary containing task data
            
        Returns:
            ValidationResult with validation status and issues
        """
        issues = []
        
        # Validate required fields
        issues.extend(self._validate_required_fields(task_data))
        
        # Validate task type
        issues.extend(self._validate_task_type(task_data))
        
        # Validate text fields
        issues.extend(self._validate_text_fields(task_data))
        
        # Validate business logic
        issues.extend(self._validate_business_logic(task_data))
        
        # Check for recommended fields
        issues.extend(self._check_recommended_fields(task_data))
        
        is_valid = not any(issue.severity == ValidationSeverity.ERROR for issue in issues)
        
        return ValidationResult(
            is_valid=is_valid,
            issues=issues,
            warnings=[issue for issue in issues if issue.severity == ValidationSeverity.WARNING]
        )
    
    def _validate_required_fields(self, task_data: Dict[str, Any]) -> List[ValidationIssue]:
        """Validate required fields are present."""
        issues = []
        
        for field in self.required_fields:
            if field not in task_data or not task_data[field]:
                issues.append(ValidationIssue(
                    field=field,
                    message=f"Required field '{field}' is missing or empty",
                    severity=ValidationSeverity.ERROR,
                    code="REQUIRED_FIELD_MISSING"
                ))
        
        return issues
    
    def _validate_task_type(self, task_data: Dict[str, Any]) -> List[ValidationIssue]:
        """Validate task type is valid."""
        issues = []
        
        task_type = task_data.get("task_type")
        if task_type and task_type not in [t.value for t in MarketingTaskType]:
            issues.append(ValidationIssue(
                field="task_type",
                message=f"Invalid task type '{task_type}'. Valid types: {[t.value for t in MarketingTaskType]}",
                severity=ValidationSeverity.ERROR,
                code="INVALID_TASK_TYPE"
            ))
        
        return issues
    
    def _validate_text_fields(self, task_data: Dict[str, Any]) -> List[ValidationIssue]:
        """Validate text field lengths and content."""
        issues = []
        
        text_fields = [
            "brand_description", "target_audience", "products_services",
            "marketing_goals", "existing_content", "keywords", "suggested_topics"
        ]
        
        for field in text_fields:
            value = task_data.get(field, "")
            if value:
                # Check length
                if len(value) < self.min_description_length:
                    issues.append(ValidationIssue(
                        field=field,
                        message=f"Field '{field}' is too short (minimum {self.min_description_length} characters)",
                        severity=ValidationSeverity.WARNING,
                        code="FIELD_TOO_SHORT"
                    ))
                elif len(value) > self.max_description_length:
                    issues.append(ValidationIssue(
                        field=field,
                        message=f"Field '{field}' is too long (maximum {self.max_description_length} characters)",
                        severity=ValidationSeverity.ERROR,
                        code="FIELD_TOO_LONG"
                    ))
                
                # Check for suspicious content
                if self._contains_suspicious_content(value):
                    issues.append(ValidationIssue(
                        field=field,
                        message=f"Field '{field}' contains potentially inappropriate content",
                        severity=ValidationSeverity.WARNING,
                        code="SUSPICIOUS_CONTENT"
                    ))
        
        return issues
    
    def _validate_business_logic(self, task_data: Dict[str, Any]) -> List[ValidationIssue]:
        """Validate business logic constraints."""
        issues = []
        
        # Check if social media task has appropriate content
        if task_data.get("task_type") == MarketingTaskType.SOCIAL_MEDIA_CONTENT:
            if not task_data.get("target_audience"):
                issues.append(ValidationIssue(
                    field="target_audience",
                    message="Social media content requires target audience specification",
                    severity=ValidationSeverity.WARNING,
                    code="SOCIAL_MEDIA_NEEDS_AUDIENCE"
                ))
        
        # Check if SEO task has keywords
        if task_data.get("task_type") == MarketingTaskType.SEO_OPTIMIZATION:
            if not task_data.get("keywords"):
                issues.append(ValidationIssue(
                    field="keywords",
                    message="SEO optimization requires keywords specification",
                    severity=ValidationSeverity.WARNING,
                    code="SEO_NEEDS_KEYWORDS"
                ))
        
        return issues
    
    def _check_recommended_fields(self, task_data: Dict[str, Any]) -> List[ValidationIssue]:
        """Check for recommended fields to improve output quality."""
        issues = []
        
        for field in self.recommended_fields:
            if not task_data.get(field):
                issues.append(ValidationIssue(
                    field=field,
                    message=f"Recommended field '{field}' is missing. Providing this will improve output quality.",
                    severity=ValidationSeverity.INFO,
                    code="RECOMMENDED_FIELD_MISSING"
                ))
        
        return issues
    
    def _contains_suspicious_content(self, text: str) -> bool:
        """Check if text contains suspicious or inappropriate content."""
        # Simple pattern matching for demonstration
        suspicious_patterns = [
            r'\b(hack|exploit|malware|virus)\b',
            r'\b(illegal|fraud|scam)\b',
            r'<script[^>]*>',  # Basic XSS detection
        ]
        
        text_lower = text.lower()
        for pattern in suspicious_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        
        return False


class IntentValidator:
    """Validator for user intent classification."""
    
    def __init__(self):
        self.confidence_threshold = 0.7
    
    def validate_intent_classification(
        self, 
        message: str, 
        classified_intent: str, 
        confidence: float
    ) -> ValidationResult:
        """
        Validate intent classification results.
        
        Args:
            message: Original user message
            classified_intent: Classified intent
            confidence: Classification confidence score
            
        Returns:
            ValidationResult with validation status
        """
        issues = []
        
        # Check confidence level
        if confidence < self.confidence_threshold:
            issues.append(ValidationIssue(
                field="confidence",
                message=f"Low confidence in intent classification: {confidence:.2f}",
                severity=ValidationSeverity.WARNING,
                code="LOW_CONFIDENCE"
            ))
        
        # Check message length
        if len(message.strip()) < 3:
            issues.append(ValidationIssue(
                field="message",
                message="Message too short for reliable intent classification",
                severity=ValidationSeverity.WARNING,
                code="MESSAGE_TOO_SHORT"
            ))
        
        # Validate intent type
        valid_intents = [
            "content_generation", "conversational", "follow_up_question",
            "capability_inquiry", "general_question"
        ]
        
        if classified_intent not in valid_intents:
            issues.append(ValidationIssue(
                field="classified_intent",
                message=f"Unknown intent type: {classified_intent}",
                severity=ValidationSeverity.ERROR,
                code="UNKNOWN_INTENT"
            ))
        
        is_valid = not any(issue.severity == ValidationSeverity.ERROR for issue in issues)
        
        return ValidationResult(
            is_valid=is_valid,
            issues=issues,
            warnings=[issue for issue in issues if issue.severity == ValidationSeverity.WARNING]
        )


# Global validator instances
task_validator = MarketingTaskValidator()
intent_validator = IntentValidator()
