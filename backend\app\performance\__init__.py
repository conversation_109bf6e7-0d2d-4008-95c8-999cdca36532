"""
Performance optimization package for Datagenius Phase 1 implementation.

This package provides comprehensive performance optimizations including:
- Multi-level caching (L1: Memory, L2: Redis, L3: Database query cache)
- Database query optimization and indexing
- WebSocket connection pooling and optimization
- Memory management and leak prevention
- Performance monitoring and metrics

Target Metrics (Phase 1):
- Dashboard load time: < 1.5 seconds (from ~2.5s)
- Cache hit ratio: > 85%
- Memory usage: < 80MB per session
- WebSocket reconnection: < 2 seconds
- Database queries per load: < 8 (from ~15)
"""

import logging
import asyncio
from typing import Dict, Any

from .optimization import cache_manager, pool_manager
from .database_optimization import db_optimizer
from .websocket_optimization import optimized_ws_manager

logger = logging.getLogger(__name__)


class PerformanceManager:
    """
    Central performance management system for Phase 1 optimizations.
    
    Coordinates all performance optimization components and provides
    unified monitoring and control.
    """

    def __init__(self):
        """Initialize the performance manager."""
        self.cache_manager = cache_manager
        self.pool_manager = pool_manager
        self.db_optimizer = db_optimizer
        self.ws_manager = optimized_ws_manager
        
        self.optimization_status = {
            "cache_system": False,
            "database_optimization": False,
            "websocket_optimization": False,
            "monitoring_enabled": False
        }

    async def initialize_optimizations(self) -> Dict[str, Any]:
        """
        Initialize all Phase 1 performance optimizations.
        
        Returns:
            Dictionary with initialization results and metrics
        """
        logger.info("Initializing Phase 1 performance optimizations...")
        
        results = {
            "cache_system": {"status": "pending", "details": {}},
            "database_optimization": {"status": "pending", "details": {}},
            "websocket_optimization": {"status": "pending", "details": {}},
            "monitoring": {"status": "pending", "details": {}},
            "overall_status": "pending"
        }
        
        try:
            # Initialize cache system
            logger.info("Initializing advanced caching system...")
            cache_stats = self.cache_manager.get_stats()
            results["cache_system"] = {
                "status": "success",
                "details": {
                    "redis_available": cache_stats.get("redis_available", False),
                    "memory_cache_size": cache_stats.get("memory_cache_size", 0),
                    "cache_dependencies": cache_stats.get("cache_dependencies_count", 0)
                }
            }
            self.optimization_status["cache_system"] = True
            logger.info("✓ Advanced caching system initialized")
            
            # Initialize database optimizations
            logger.info("Applying database optimizations...")
            db_results = await self.db_optimizer.apply_dashboard_optimizations()
            results["database_optimization"] = {
                "status": "success" if not db_results.get("errors") else "partial",
                "details": db_results
            }
            self.optimization_status["database_optimization"] = True
            logger.info(f"✓ Database optimizations applied: {db_results['indexes_created']} indexes created")
            
            # Initialize WebSocket optimizations
            logger.info("Initializing WebSocket optimizations...")
            ws_stats = self.ws_manager.get_performance_stats()
            results["websocket_optimization"] = {
                "status": "success",
                "details": {
                    "max_connections": self.ws_manager.max_connections,
                    "heartbeat_interval": self.ws_manager.heartbeat_interval,
                    "current_connections": ws_stats.get("current_connections", 0)
                }
            }
            self.optimization_status["websocket_optimization"] = True
            logger.info("✓ WebSocket optimizations initialized")
            
            # Initialize monitoring
            logger.info("Enabling performance monitoring...")
            monitoring_stats = await self._initialize_monitoring()
            results["monitoring"] = {
                "status": "success",
                "details": monitoring_stats
            }
            self.optimization_status["monitoring_enabled"] = True
            logger.info("✓ Performance monitoring enabled")
            
            # Overall status
            all_successful = all(self.optimization_status.values())
            results["overall_status"] = "success" if all_successful else "partial"
            
            logger.info(f"Phase 1 optimizations initialized: {results['overall_status']}")
            return results
            
        except Exception as e:
            logger.error(f"Performance optimization initialization failed: {e}")
            results["overall_status"] = "failed"
            results["error"] = str(e)
            return results

    async def _initialize_monitoring(self) -> Dict[str, Any]:
        """Initialize performance monitoring."""
        from ..monitoring.metrics import metrics
        
        # Start background monitoring tasks
        asyncio.create_task(self._monitor_cache_performance())
        asyncio.create_task(self._monitor_database_performance())
        asyncio.create_task(self._monitor_websocket_performance())
        asyncio.create_task(self._monitor_memory_usage())
        
        return {
            "cache_monitoring": True,
            "database_monitoring": True,
            "websocket_monitoring": True,
            "memory_monitoring": True
        }

    async def _monitor_cache_performance(self):
        """Monitor cache performance metrics."""
        while True:
            try:
                await asyncio.sleep(60)  # Monitor every minute
                
                stats = self.cache_manager.get_stats()
                
                # Update Prometheus metrics
                from ..monitoring.metrics import metrics
                
                if stats.get("total_requests", 0) > 0:
                    hit_ratio = stats.get("hit_rate_percent", 0)
                    metrics.update_cache_hit_ratio("overall", hit_ratio)
                    
                    l1_hit_ratio = stats.get("l1_hit_rate_percent", 0)
                    metrics.update_cache_hit_ratio("l1_memory", l1_hit_ratio)
                    
                    l2_hit_ratio = stats.get("l2_hit_rate_percent", 0)
                    metrics.update_cache_hit_ratio("l2_redis", l2_hit_ratio)
                
                # Update cache operation metrics
                metrics.update_cache_performance("l1_memory", "hit")
                metrics.update_cache_performance("l2_redis", "hit")
                
            except Exception as e:
                logger.error(f"Cache monitoring error: {e}")
                await asyncio.sleep(60)

    async def _monitor_database_performance(self):
        """Monitor database performance metrics."""
        while True:
            try:
                await asyncio.sleep(300)  # Monitor every 5 minutes
                
                # Get database performance metrics
                db_metrics = self.db_optimizer.get_performance_metrics()
                pool_stats = self.pool_manager.get_stats()
                
                # Update connection metrics
                from ..monitoring.metrics import metrics
                metrics.update_database_connections(pool_stats.get("active_connections", 0))
                
                # Log performance summary
                logger.info(f"Database performance: {pool_stats.get('avg_query_time', 0):.3f}s avg query time")
                
            except Exception as e:
                logger.error(f"Database monitoring error: {e}")
                await asyncio.sleep(300)

    async def _monitor_websocket_performance(self):
        """Monitor WebSocket performance metrics."""
        while True:
            try:
                await asyncio.sleep(30)  # Monitor every 30 seconds
                
                ws_stats = self.ws_manager.get_performance_stats()
                
                # Update WebSocket metrics
                from ..monitoring.metrics import metrics
                metrics.update_websocket_connections(ws_stats.get("active_connections", 0))
                
                # Check for performance issues
                error_rate = ws_stats.get("error_rate", 0)
                if error_rate > 0.1:  # 10% error rate threshold
                    logger.warning(f"High WebSocket error rate: {error_rate:.2%}")
                
            except Exception as e:
                logger.error(f"WebSocket monitoring error: {e}")
                await asyncio.sleep(30)

    async def _monitor_memory_usage(self):
        """Monitor memory usage across components."""
        while True:
            try:
                await asyncio.sleep(120)  # Monitor every 2 minutes
                
                # Get cache memory usage
                cache_memory = await self.cache_manager.get_memory_usage()
                
                # Update memory metrics
                from ..monitoring.metrics import metrics
                metrics.update_memory_usage("cache", cache_memory.get("estimated_memory_bytes", 0))
                
                # Check memory thresholds
                total_memory = cache_memory.get("estimated_memory_bytes", 0)
                if total_memory > 80 * 1024 * 1024:  # 80MB threshold
                    logger.warning(f"High memory usage: {total_memory / 1024 / 1024:.1f}MB")
                
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                await asyncio.sleep(120)

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        summary = {
            "optimization_status": self.optimization_status.copy(),
            "cache_stats": self.cache_manager.get_stats(),
            "database_stats": self.pool_manager.get_stats(),
            "websocket_stats": self.ws_manager.get_performance_stats(),
            "timestamp": asyncio.get_event_loop().time()
        }
        
        # Calculate overall health score
        health_score = 100
        
        # Cache health (25% weight)
        cache_stats = summary["cache_stats"]
        if cache_stats.get("total_requests", 0) > 0:
            hit_rate = cache_stats.get("hit_rate_percent", 0)
            if hit_rate < 85:  # Target: >85%
                health_score -= (85 - hit_rate) * 0.25
        
        # Database health (25% weight)
        db_stats = summary["database_stats"]
        avg_query_time = db_stats.get("avg_query_time", 0)
        if avg_query_time > 0.1:  # Target: <100ms average
            health_score -= min(25, (avg_query_time - 0.1) * 250)
        
        # WebSocket health (25% weight)
        ws_stats = summary["websocket_stats"]
        error_rate = ws_stats.get("error_rate", 0)
        if error_rate > 0.05:  # Target: <5% error rate
            health_score -= min(25, (error_rate - 0.05) * 500)
        
        # Memory health (25% weight)
        # This would need actual memory measurements
        
        summary["health_score"] = max(0, health_score)
        return summary


# Global performance manager instance
performance_manager = PerformanceManager()


# Convenience functions for easy access
async def initialize_performance_optimizations():
    """Initialize all Phase 1 performance optimizations."""
    return await performance_manager.initialize_optimizations()


def get_performance_summary():
    """Get current performance summary."""
    return performance_manager.get_performance_summary()


# Export main components
__all__ = [
    "performance_manager",
    "cache_manager",
    "pool_manager", 
    "db_optimizer",
    "optimized_ws_manager",
    "initialize_performance_optimizations",
    "get_performance_summary"
]
