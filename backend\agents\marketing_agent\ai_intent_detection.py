"""
AI-powered intent detection for marketing agent.

This module provides intelligent intent classification using LLM-based analysis
instead of hardcoded patterns, enabling more accurate and flexible intent detection.
"""

import logging
import json
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class IntentType(str, Enum):
    """Types of user intents."""
    CONTENT_GENERATION = "content_generation"
    CONVERSATIONAL = "conversational"
    FOLLOW_UP_QUESTION = "follow_up_question"
    CAPABILITY_INQUIRY = "capability_inquiry"
    GREETING = "greeting"
    CLARIFICATION = "clarification"
    FEEDBACK = "feedback"
    UNKNOWN = "unknown"


@dataclass
class IntentResult:
    """Result of intent detection."""
    intent_type: IntentType
    confidence: float
    reasoning: str
    suggested_action: str
    metadata: Dict[str, Any]


class AIIntentDetector:
    """AI-powered intent detection using LLM analysis."""
    
    def __init__(self, mcp_server=None):
        self.mcp_server = mcp_server
        self._cache = {}
        self._cache_ttl = 300  # 5 minutes
        
        # Intent detection prompt template
        self.intent_prompt = """
You are an expert AI assistant specializing in understanding user intent in marketing conversations.

Analyze the following user message and conversation context to determine the user's intent.

USER MESSAGE: "{message}"

CONVERSATION CONTEXT:
- Previous messages: {conversation_history}
- Has marketing form data: {has_form_data}
- Is first conversation: {is_first_conversation}
- Previous generated content: {has_previous_content}

INTENT CATEGORIES:
1. CONTENT_GENERATION: User wants to create marketing content (strategy, campaign, social media, etc.)
2. CONVERSATIONAL: User is asking follow-up questions, seeking advice, or having a conversation
3. FOLLOW_UP_QUESTION: User is asking "what else", "any other ideas", "what about", etc.
4. CAPABILITY_INQUIRY: User is asking what you can do or how you can help
5. GREETING: User is saying hello, hi, or starting the conversation
6. CLARIFICATION: User is asking for clarification about previous responses
7. FEEDBACK: User is providing feedback on generated content
8. UNKNOWN: Intent is unclear or doesn't fit other categories

ANALYSIS GUIDELINES:
- If user submitted a form or explicitly asks to "generate", "create", "develop" content → CONTENT_GENERATION
- If user asks "what else", "any other ideas", "what do you recommend" → FOLLOW_UP_QUESTION
- If user asks "what can you do", "how can you help" → CAPABILITY_INQUIRY
- If user says "thanks", "good", "why", "how" after content generation → CONVERSATIONAL
- If user greets or starts conversation → GREETING
- Consider conversation flow and context

Respond with a JSON object containing:
{{
    "intent_type": "one of the categories above",
    "confidence": 0.0-1.0,
    "reasoning": "brief explanation of why you chose this intent",
    "suggested_action": "what the system should do next",
    "metadata": {{
        "requires_content_generation": true/false,
        "is_follow_up": true/false,
        "conversation_context_needed": true/false
    }}
}}
"""
    
    async def detect_intent(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> IntentResult:
        """
        Detect user intent using AI analysis.
        
        Args:
            message: User's message text
            context: Conversation context
            
        Returns:
            IntentResult with detected intent and metadata
        """
        try:
            # Check cache first
            cache_key = self._generate_cache_key(message, context)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                logger.debug(f"Using cached intent result for: {message[:50]}...")
                return cached_result
            
            # Prepare context for analysis
            analysis_context = self._prepare_context(context or {})
            
            # Generate prompt
            prompt = self.intent_prompt.format(
                message=message,
                conversation_history=self._format_conversation_history(analysis_context.get('conversation_history', [])),
                has_form_data=analysis_context.get('has_marketing_form_data', False),
                is_first_conversation=analysis_context.get('is_first_conversation', True),
                has_previous_content=analysis_context.get('has_previous_content', False)
            )
            
            # Get AI analysis
            if self.mcp_server:
                result = await self._get_ai_analysis(prompt)
            else:
                # Fallback to rule-based detection
                result = self._fallback_detection(message, analysis_context)
            
            # Cache result
            self._cache_result(cache_key, result)
            
            logger.info(f"Intent detected: {result.intent_type} (confidence: {result.confidence:.2f}) - {result.reasoning}")
            return result
            
        except Exception as e:
            logger.error(f"Error in AI intent detection: {e}")
            # Fallback to rule-based detection
            return self._fallback_detection(message, context or {})
    
    async def _get_ai_analysis(self, prompt: str) -> IntentResult:
        """Get AI analysis of user intent."""
        try:
            # Use conversation tool for intent analysis
            from agents.utils import create_agent_context

            user_context = create_agent_context(
                agent_id="composable-marketing-ai",
                additional_context={
                    "task": "intent_analysis",
                    "response_format": "json",
                    "analysis_type": "marketing_intent"
                }
            )

            tool_result = await self.mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": user_context,
                "intent_type": "analysis",
                "confidence": 1.0,
                "temperature": 0.3,  # Lower temperature for more consistent analysis
                "max_tokens": 500
            })
            
            if tool_result.get("isError", False):
                raise Exception(f"AI analysis failed: {tool_result}")
            
            # Parse AI response
            response_text = tool_result.get("content", [{}])[0].get("text", "")
            
            # Extract JSON from response
            try:
                # Try to find JSON in the response
                start_idx = response_text.find('{')
                end_idx = response_text.rfind('}') + 1
                
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = response_text[start_idx:end_idx]
                    analysis = json.loads(json_str)
                    
                    return IntentResult(
                        intent_type=IntentType(analysis.get('intent_type', 'unknown')),
                        confidence=float(analysis.get('confidence', 0.5)),
                        reasoning=analysis.get('reasoning', 'AI analysis'),
                        suggested_action=analysis.get('suggested_action', 'process_normally'),
                        metadata=analysis.get('metadata', {})
                    )
                else:
                    raise ValueError("No valid JSON found in AI response")
                    
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Failed to parse AI response as JSON: {e}")
                # Try to extract intent from text response
                return self._extract_intent_from_text(response_text)
                
        except Exception as e:
            logger.error(f"Error getting AI analysis: {e}")
            raise
    
    def _extract_intent_from_text(self, text: str) -> IntentResult:
        """Extract intent from text response when JSON parsing fails."""
        text_lower = text.lower()
        
        # Look for intent keywords in the response
        if 'content_generation' in text_lower or 'generate' in text_lower:
            intent_type = IntentType.CONTENT_GENERATION
            confidence = 0.7
        elif 'follow_up' in text_lower or 'follow up' in text_lower:
            intent_type = IntentType.FOLLOW_UP_QUESTION
            confidence = 0.7
        elif 'conversational' in text_lower or 'conversation' in text_lower:
            intent_type = IntentType.CONVERSATIONAL
            confidence = 0.7
        elif 'capability' in text_lower or 'help' in text_lower:
            intent_type = IntentType.CAPABILITY_INQUIRY
            confidence = 0.6
        elif 'greeting' in text_lower or 'hello' in text_lower:
            intent_type = IntentType.GREETING
            confidence = 0.8
        else:
            intent_type = IntentType.UNKNOWN
            confidence = 0.3
        
        return IntentResult(
            intent_type=intent_type,
            confidence=confidence,
            reasoning="Extracted from AI text response",
            suggested_action="process_based_on_intent",
            metadata={"source": "text_extraction"}
        )
    
    def _fallback_detection(self, message: str, context: Dict[str, Any]) -> IntentResult:
        """Fallback rule-based intent detection."""
        message_lower = message.lower().strip()
        
        # Check for explicit content generation requests
        content_patterns = [
            "generate", "create", "develop", "build", "write", "make",
            "marketing strategy", "campaign", "social media", "seo"
        ]
        
        if any(pattern in message_lower for pattern in content_patterns):
            return IntentResult(
                intent_type=IntentType.CONTENT_GENERATION,
                confidence=0.8,
                reasoning="Contains explicit content generation keywords",
                suggested_action="generate_content",
                metadata={"requires_content_generation": True}
            )
        
        # Check for follow-up questions
        follow_up_patterns = [
            "what else", "any other", "what about", "how about",
            "can you suggest", "do you recommend", "what do you think",
            "any more", "other ideas", "anything else"
        ]
        
        if any(pattern in message_lower for pattern in follow_up_patterns):
            return IntentResult(
                intent_type=IntentType.FOLLOW_UP_QUESTION,
                confidence=0.9,
                reasoning="Contains follow-up question patterns",
                suggested_action="provide_conversational_response",
                metadata={"is_follow_up": True, "conversation_context_needed": True}
            )
        
        # Check for capability inquiries
        capability_patterns = [
            "what can you do", "how can you help", "what services",
            "what are your capabilities", "how can you assist"
        ]
        
        if any(pattern in message_lower for pattern in capability_patterns):
            return IntentResult(
                intent_type=IntentType.CAPABILITY_INQUIRY,
                confidence=0.9,
                reasoning="Asking about capabilities",
                suggested_action="explain_capabilities",
                metadata={"conversation_context_needed": False}
            )
        
        # Check for greetings
        if message_lower in ["hello", "hi", "hey", "start"] or len(message_lower) < 10:
            return IntentResult(
                intent_type=IntentType.GREETING,
                confidence=0.8,
                reasoning="Short greeting or simple message",
                suggested_action="provide_greeting_response",
                metadata={"is_greeting": True}
            )
        
        # Check for conversational responses
        conversational_patterns = [
            "thanks", "thank you", "ok", "okay", "good", "great",
            "why", "how", "yes", "no", "sure"
        ]
        
        if any(pattern in message_lower for pattern in conversational_patterns):
            return IntentResult(
                intent_type=IntentType.CONVERSATIONAL,
                confidence=0.7,
                reasoning="Contains conversational response patterns",
                suggested_action="provide_conversational_response",
                metadata={"conversation_context_needed": True}
            )
        
        # Default to conversational if unsure
        return IntentResult(
            intent_type=IntentType.CONVERSATIONAL,
            confidence=0.5,
            reasoning="Default classification for unclear intent",
            suggested_action="provide_conversational_response",
            metadata={"conversation_context_needed": True}
        )
    
    def _prepare_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare context for intent analysis."""
        return {
            'conversation_history': context.get('conversation_history', []),
            'has_marketing_form_data': bool(context.get('marketing_form_data')),
            'is_first_conversation': context.get('is_first_conversation', True),
            'has_previous_content': self._has_previous_content(context.get('conversation_history', []))
        }
    
    def _has_previous_content(self, conversation_history: List[Dict]) -> bool:
        """Check if conversation has previously generated content."""
        for message in conversation_history:
            if message.get('sender') == 'ai' and message.get('metadata', {}).get('generated_content'):
                return True
        return False
    
    def _format_conversation_history(self, history: List[Dict]) -> str:
        """Format conversation history for analysis."""
        if not history:
            return "No previous messages"
        
        formatted = []
        for msg in history[-3:]:  # Last 3 messages for context
            sender = msg.get('sender', 'unknown')
            content = msg.get('content', '')[:100]  # Truncate for brevity
            formatted.append(f"{sender}: {content}")
        
        return " | ".join(formatted)
    
    def _generate_cache_key(self, message: str, context: Optional[Dict[str, Any]]) -> str:
        """Generate cache key for intent detection."""
        import hashlib
        
        # Create a hash of message and relevant context
        cache_data = {
            'message': message.lower().strip(),
            'has_form_data': bool(context and context.get('marketing_form_data')),
            'conversation_length': len(context.get('conversation_history', [])) if context else 0
        }
        
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()[:16]
    
    def _get_cached_result(self, cache_key: str) -> Optional[IntentResult]:
        """Get cached intent result if still valid."""
        if cache_key in self._cache:
            cached_data = self._cache[cache_key]
            if time.time() - cached_data['timestamp'] < self._cache_ttl:
                return cached_data['result']
            else:
                # Remove expired cache entry
                del self._cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: IntentResult) -> None:
        """Cache intent detection result."""
        self._cache[cache_key] = {
            'result': result,
            'timestamp': time.time()
        }
        
        # Clean up old cache entries (simple cleanup)
        if len(self._cache) > 100:
            # Remove oldest entries
            sorted_items = sorted(
                self._cache.items(),
                key=lambda x: x[1]['timestamp']
            )
            for key, _ in sorted_items[:20]:  # Remove oldest 20 entries
                del self._cache[key]


# Global intent detector instance
_intent_detector: Optional[AIIntentDetector] = None


def initialize_intent_detector(mcp_server=None) -> AIIntentDetector:
    """Initialize global intent detector."""
    global _intent_detector
    _intent_detector = AIIntentDetector(mcp_server)
    logger.info("AI intent detector initialized")
    return _intent_detector


def get_intent_detector() -> Optional[AIIntentDetector]:
    """Get global intent detector instance."""
    return _intent_detector


async def detect_intent(message: str, context: Optional[Dict[str, Any]] = None) -> IntentResult:
    """
    Convenience function to detect intent using global detector.
    
    Args:
        message: User's message text
        context: Conversation context
        
    Returns:
        IntentResult with detected intent and metadata
    """
    detector = get_intent_detector()
    if detector:
        return await detector.detect_intent(message, context)
    else:
        # Fallback if no detector initialized
        fallback_detector = AIIntentDetector()
        return fallback_detector._fallback_detection(message, context or {})
