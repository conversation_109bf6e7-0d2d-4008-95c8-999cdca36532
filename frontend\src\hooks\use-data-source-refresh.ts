import { useState, useCallback, useEffect } from 'react';

// Global event emitter for data source refresh events
class DataSourceRefreshEmitter {
  private listeners: Set<() => void> = new Set();
  private refreshCounter = 0;

  subscribe(callback: () => void) {
    this.listeners.add(callback);
    return () => {
      this.listeners.delete(callback);
    };
  }

  emit() {
    this.refreshCounter++;
    this.listeners.forEach(callback => callback());
  }

  getCounter() {
    return this.refreshCounter;
  }
}

// Global instance
const dataSourceRefreshEmitter = new DataSourceRefreshEmitter();

/**
 * Hook for triggering data source refresh events
 */
export const useDataSourceRefreshTrigger = () => {
  const triggerRefresh = useCallback(() => {
    console.log('Triggering data source refresh...');
    dataSourceRefreshEmitter.emit();
  }, []);

  return { triggerRefresh };
};

/**
 * Hook for listening to data source refresh events
 */
export const useDataSourceRefresh = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    const unsubscribe = dataSourceRefreshEmitter.subscribe(() => {
      setRefreshTrigger(prev => prev + 1);
    });

    return unsubscribe;
  }, []);

  return { refreshTrigger };
};

/**
 * Hook for auto-refreshing data sources when files are uploaded
 */
export const useAutoRefreshOnUpload = () => {
  const { triggerRefresh } = useDataSourceRefreshTrigger();

  const handleFileUploaded = useCallback((uploadedFile: any) => {
    console.log('File uploaded, triggering data source refresh:', uploadedFile);
    // Small delay to ensure the data source is created in the backend
    setTimeout(() => {
      triggerRefresh();
    }, 500);
  }, [triggerRefresh]);

  const handleDataSourceCreated = useCallback((dataSource: any) => {
    console.log('Data source created, triggering refresh:', dataSource);
    // Small delay to ensure the data source is fully processed
    setTimeout(() => {
      triggerRefresh();
    }, 300);
  }, [triggerRefresh]);

  return {
    handleFileUploaded,
    handleDataSourceCreated,
    triggerRefresh
  };
};
