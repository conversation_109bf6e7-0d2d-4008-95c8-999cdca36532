"""Add workflow execution tables

Revision ID: fdcf7758248c
Revises: 82479d2b6028
Create Date: 2025-05-26 13:05:38.151617

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fdcf7758248c'
down_revision = '82479d2b6028'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('kg_entities',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('entity_type', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('properties', sa.JSON(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_kg_entities_entity_type'), 'kg_entities', ['entity_type'], unique=False)
    op.create_index(op.f('ix_kg_entities_name'), 'kg_entities', ['name'], unique=False)
    op.create_table('workflow_executions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('status', sa.Enum('CREATED', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'PAUSED', name='workflow_status_enum'), nullable=False),
    sa.Column('start_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('end_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('user_id', sa.String(), nullable=True),
    sa.Column('session_id', sa.String(), nullable=True),
    sa.Column('context', sa.JSON(), nullable=True),
    sa.Column('workflow_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('kg_relationships',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('relationship_type', sa.String(), nullable=False),
    sa.Column('source_entity_id', sa.UUID(), nullable=False),
    sa.Column('target_entity_id', sa.UUID(), nullable=False),
    sa.Column('properties', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['source_entity_id'], ['kg_entities.id'], ),
    sa.ForeignKeyConstraint(['target_entity_id'], ['kg_entities.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_kg_relationships_relationship_type'), 'kg_relationships', ['relationship_type'], unique=False)
    op.create_index(op.f('ix_kg_relationships_source_entity_id'), 'kg_relationships', ['source_entity_id'], unique=False)
    op.create_index(op.f('ix_kg_relationships_target_entity_id'), 'kg_relationships', ['target_entity_id'], unique=False)
    op.create_table('workflow_task_executions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('workflow_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('agent_type', sa.String(length=255), nullable=False),
    sa.Column('input_data', sa.JSON(), nullable=True),
    sa.Column('dependencies', sa.JSON(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'WAITING', name='task_status_enum'), nullable=False),
    sa.Column('result', sa.JSON(), nullable=True),
    sa.Column('error', sa.Text(), nullable=True),
    sa.Column('start_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('end_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=False),
    sa.Column('max_retries', sa.Integer(), nullable=False),
    sa.Column('timeout_seconds', sa.Integer(), nullable=False),
    sa.Column('task_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['workflow_id'], ['workflow_executions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_workflow_task_executions_workflow_id'), 'workflow_task_executions', ['workflow_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_workflow_task_executions_workflow_id'), table_name='workflow_task_executions')
    op.drop_table('workflow_task_executions')
    op.drop_index(op.f('ix_kg_relationships_target_entity_id'), table_name='kg_relationships')
    op.drop_index(op.f('ix_kg_relationships_source_entity_id'), table_name='kg_relationships')
    op.drop_index(op.f('ix_kg_relationships_relationship_type'), table_name='kg_relationships')
    op.drop_table('kg_relationships')
    op.drop_table('workflow_executions')
    op.drop_index(op.f('ix_kg_entities_name'), table_name='kg_entities')
    op.drop_index(op.f('ix_kg_entities_entity_type'), table_name='kg_entities')
    op.drop_table('kg_entities')
    # ### end Alembic commands ###
