import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Zap, 
  Target, 
  Share2, 
  Search, 
  BarChart3, 
  Lightbulb,
  ChevronLeft,
  ChevronRight,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface QuickAction {
  id: string;
  label: string;
  description: string;
  icon: string;
  category: string;
  enabled: boolean;
  metadata?: Record<string, any>;
}

interface QuickActionsCarouselProps {
  personaId?: string;
  conversationId: string;
  onActionExecute: (actionId: string, context?: Record<string, any>) => Promise<void>;
  className?: string;
}

const iconMap: Record<string, React.ComponentType<any>> = {
  strategy: Target,
  campaign: Zap,
  social: Share2,
  seo: Search,
  analysis: BarChart3,
  default: Lightbulb
};

export function QuickActionsCarousel({ 
  personaId, 
  conversationId, 
  onActionExecute, 
  className 
}: QuickActionsCarouselProps) {
  const [actions, setActions] = useState<QuickAction[]>([]);
  const [loading, setLoading] = useState(true);
  const [executing, setExecuting] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Fetch quick actions from API
  useEffect(() => {
    const fetchQuickActions = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const url = personaId 
          ? `/api/quick-actions/persona/${personaId}`
          : '/api/quick-actions/';
          
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch quick actions: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (data.actions) {
          setActions(data.actions.filter((action: QuickAction) => action.enabled));
        } else if (data.specialized && Array.isArray(data.actions)) {
          // Handle persona-specific response format
          setActions(data.actions.filter((action: QuickAction) => action.enabled !== false));
        } else {
          setActions([]);
        }
        
      } catch (err) {
        console.error('Error fetching quick actions:', err);
        setError(err instanceof Error ? err.message : 'Failed to load quick actions');
        setActions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchQuickActions();
  }, [personaId]);

  const handleActionClick = async (action: QuickAction) => {
    if (executing) return;

    try {
      setExecuting(action.id);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Execute via API
      const response = await fetch('/api/quick-actions/execute', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action_id: action.id,
          conversation_id: conversationId,
          context: {
            persona_id: personaId,
            triggered_from: 'quick_actions_carousel'
          },
          metadata: action.metadata
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to execute action: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Action execution failed');
      }

      // Call the parent handler for any additional processing
      await onActionExecute(action.id, {
        persona_id: personaId,
        triggered_from: 'quick_actions_carousel',
        result: result
      });

    } catch (err) {
      console.error('Error executing quick action:', err);
      setError(err instanceof Error ? err.message : 'Failed to execute action');
    } finally {
      setExecuting(null);
    }
  };

  const nextActions = () => {
    setCurrentIndex((prev) => 
      prev + 3 >= actions.length ? 0 : prev + 3
    );
  };

  const prevActions = () => {
    setCurrentIndex((prev) => 
      prev - 3 < 0 ? Math.max(0, actions.length - 3) : prev - 3
    );
  };

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName] || iconMap.default;
    return <IconComponent className="h-5 w-5" />;
  };

  const visibleActions = actions.slice(currentIndex, currentIndex + 3);
  const canNavigate = actions.length > 3;

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading quick actions...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("w-full border-destructive/50", className)}>
        <CardContent className="p-4">
          <div className="text-sm text-destructive text-center">
            {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (actions.length === 0) {
    return null; // Don't render if no actions available
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Zap className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Quick Actions</span>
            {personaId === 'marketing_agent' && (
              <Badge variant="secondary" className="text-xs">
                Marketing
              </Badge>
            )}
          </div>
          
          {canNavigate && (
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={prevActions}
                disabled={currentIndex === 0}
                className="h-6 w-6 p-0"
              >
                <ChevronLeft className="h-3 w-3" />
              </Button>
              <span className="text-xs text-muted-foreground px-2">
                {Math.floor(currentIndex / 3) + 1} / {Math.ceil(actions.length / 3)}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={nextActions}
                disabled={currentIndex + 3 >= actions.length}
                className="h-6 w-6 p-0"
              >
                <ChevronRight className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {visibleActions.map((action) => (
            <TooltipProvider key={action.id}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleActionClick(action)}
                    disabled={executing !== null}
                    className={cn(
                      "h-auto p-3 flex flex-col items-center space-y-2 text-center",
                      "hover:bg-primary/5 hover:border-primary/20 transition-colors",
                      executing === action.id && "opacity-50"
                    )}
                  >
                    {executing === action.id ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      getIcon(action.icon)
                    )}
                    <div className="space-y-1">
                      <div className="text-xs font-medium leading-tight">
                        {action.label}
                      </div>
                      <Badge 
                        variant="secondary" 
                        className="text-[10px] px-1 py-0"
                      >
                        {action.category}
                      </Badge>
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <div className="space-y-1">
                    <div className="font-medium">{action.label}</div>
                    <div className="text-xs text-muted-foreground">
                      {action.description}
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>

        {executing && (
          <div className="mt-3 text-xs text-muted-foreground text-center">
            Executing action...
          </div>
        )}
      </CardContent>
    </Card>
  );
}
