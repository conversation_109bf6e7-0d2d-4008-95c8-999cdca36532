import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useNavigate } from "react-router-dom";
import {
  Building2,
  Factory,
  Leaf,
  Car,
  Server,
  ChartBar,
  HeartPulse,
  GraduationCap,
  ShoppingBag,
  Plane,
  Plus
} from "lucide-react";

const industries = [
  { id: 1, name: "Real Estate", icon: Building2 },
  { id: 2, name: "Manufacturing", icon: Factory },
  { id: 3, name: "Agriculture", icon: Leaf },
  { id: 4, name: "Automotive", icon: Car },
  { id: 5, name: "Technology", icon: Server },
  { id: 6, name: "Finance", icon: ChartBar },
  { id: 7, name: "Healthcare", icon: HeartPulse },
  { id: 8, name: "Education", icon: GraduationCap },
  { id: 9, name: "Retail", icon: ShoppingBag },
  { id: 10, name: "Travel", icon: Plane }
];

const IndustrySelection = () => {
  const navigate = useNavigate();
  const [selectedIndustry, setSelectedIndustry] = useState<number | null>(null);
  const [customIndustry, setCustomIndustry] = useState("");
  const [isOtherSelected, setIsOtherSelected] = useState(false);

  const handleNext = () => {
    if (selectedIndustry || (isOtherSelected && customIndustry)) {
      navigate("/data-integration");
    }
  };

  const handleSkip = () => {
    navigate("/data-integration");
  };

  const handleIndustrySelect = (id: number) => {
    setSelectedIndustry(id);
    setIsOtherSelected(false);
    setCustomIndustry("");
  };

  const handleOtherSelect = () => {
    setSelectedIndustry(null);
    setIsOtherSelected(true);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-gradient-to-b from-brand-100 to-brand-200 p-6"
    >
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <motion.h1
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className="text-3xl font-bold text-brand-600 mb-2"
          >
            Select Your Industry
          </motion.h1>
          <motion.p
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="text-gray-600"
          >
            Choose the industry that best matches your business
          </motion.p>
        </div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4"
        >
          {industries.map((industry, index) => (
            <motion.div
              key={industry.id}
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card
                className={`p-4 cursor-pointer transition-all hover:shadow-lg ${
                  selectedIndustry === industry.id
                    ? "ring-2 ring-brand-500 bg-brand-50"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => handleIndustrySelect(industry.id)}
              >
                <div className="flex flex-col items-center space-y-2">
                  <industry.icon
                    className={`w-8 h-8 ${
                      selectedIndustry === industry.id
                        ? "text-brand-500"
                        : "text-gray-600"
                    }`}
                  />
                  <span className="text-sm font-medium text-center">
                    {industry.name}
                  </span>
                </div>
              </Card>
            </motion.div>
          ))}

          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: industries.length * 0.1 }}
          >
            <Card
              className={`p-4 cursor-pointer transition-all hover:shadow-lg ${
                isOtherSelected ? "ring-2 ring-brand-500 bg-brand-50" : "hover:bg-gray-50"
              }`}
              onClick={handleOtherSelect}
            >
              <div className="flex flex-col items-center space-y-2">
                <Plus
                  className={`w-8 h-8 ${
                    isOtherSelected ? "text-brand-500" : "text-gray-600"
                  }`}
                />
                <span className="text-sm font-medium text-center">Other</span>
              </div>
            </Card>
          </motion.div>
        </motion.div>

        {isOtherSelected && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 max-w-md mx-auto"
          >
            <Input
              type="text"
              placeholder="Enter your industry"
              value={customIndustry}
              onChange={(e) => setCustomIndustry(e.target.value)}
              className="w-full"
            />
          </motion.div>
        )}

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-8 text-center space-x-4"
        >
          <Button
            className="bg-brand-500 hover:bg-brand-600 px-8"
            disabled={!selectedIndustry && !(isOtherSelected && customIndustry)}
            onClick={handleNext}
          >
            Next
          </Button>
          <Button
            variant="outline"
            className="px-8"
            onClick={handleSkip}
          >
            Skip
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default IndustrySelection;
