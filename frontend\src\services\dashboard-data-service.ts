/**
 * Dashboard Data Service
 * 
 * Service for connecting dashboard widgets to various data sources including
 * user-uploaded files, databases, and real-time data streams.
 */

import { fileApi, dashboardApi } from '@/lib/api';
import { DataSourceConfig } from '@/types/dashboard-customization';

export interface DataSourceInfo {
  id: string;
  name: string;
  type: 'file' | 'database' | 'api' | 'platform';
  description?: string;
  schema?: Record<string, any>;
  lastUpdated: string;
  recordCount?: number;
}

export interface QueryResult {
  data: Record<string, any>[];
  columns: string[];
  totalRecords: number;
  executionTime: number;
  error?: string;
}

export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string[];
    borderWidth?: number;
  }>;
}

export interface KPIData {
  value: number | string;
  label: string;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  format?: 'number' | 'currency' | 'percentage';
}

export interface TableData {
  columns: Array<{
    key: string;
    label: string;
    type: 'string' | 'number' | 'date' | 'boolean';
    sortable?: boolean;
  }>;
  rows: Record<string, any>[];
  pagination?: {
    page: number;
    pageSize: number;
    totalPages: number;
    totalRecords: number;
  };
}

class DashboardDataService {
  private dataSourceCache = new Map<string, DataSourceInfo>();
  private queryCache = new Map<string, { result: QueryResult; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get available data sources for the current user
   */
  async getAvailableDataSources(): Promise<DataSourceInfo[]> {
    try {
      // Get user's uploaded files
      const filesResponse = await fileApi.getFiles();
      const fileDataSources: DataSourceInfo[] = filesResponse.files.map(file => ({
        id: `file-${file.id}`,
        name: file.filename || file.name,
        type: 'file' as const,
        description: `Uploaded file: ${file.content_type}`,
        lastUpdated: file.created_at,
        recordCount: file.record_count,
      }));

      // Add platform data sources
      const platformDataSources: DataSourceInfo[] = [
        {
          id: 'platform-metrics',
          name: 'Platform Metrics',
          type: 'platform',
          description: 'Real-time platform usage and analytics',
          lastUpdated: new Date().toISOString(),
        },
        {
          id: 'platform-engagement',
          name: 'User Engagement',
          type: 'platform',
          description: 'Message engagement and data integration metrics',
          lastUpdated: new Date().toISOString(),
        },
        {
          id: 'platform-users',
          name: 'User Analytics',
          type: 'platform',
          description: 'User segmentation and platform usage patterns',
          lastUpdated: new Date().toISOString(),
        },
      ];

      const allDataSources = [...fileDataSources, ...platformDataSources];
      
      // Cache the data sources
      allDataSources.forEach(ds => this.dataSourceCache.set(ds.id, ds));
      
      return allDataSources;
    } catch (error) {
      console.error('Failed to get data sources:', error);
      return [];
    }
  }

  /**
   * Execute a query against a data source
   */
  async executeQuery(dataSourceId: string, config: DataSourceConfig): Promise<QueryResult> {
    const cacheKey = `${dataSourceId}-${JSON.stringify(config)}`;
    const cached = this.queryCache.get(cacheKey);
    
    // Return cached result if still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.result;
    }

    try {
      let result: QueryResult;

      if (dataSourceId.startsWith('platform-')) {
        result = await this.executePlatformQuery(dataSourceId, config);
      } else if (dataSourceId.startsWith('file-')) {
        result = await this.executeFileQuery(dataSourceId, config);
      } else {
        throw new Error(`Unsupported data source type: ${dataSourceId}`);
      }

      // Cache the result
      this.queryCache.set(cacheKey, { result, timestamp: Date.now() });
      
      return result;
    } catch (error) {
      console.error('Query execution failed:', error);
      return {
        data: [],
        columns: [],
        totalRecords: 0,
        executionTime: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Transform query result to chart data format
   */
  transformToChartData(
    result: QueryResult,
    chartType: 'line' | 'bar' | 'pie' | 'doughnut',
    labelColumn: string,
    valueColumns: string[]
  ): ChartData {
    if (result.error || result.data.length === 0) {
      return { labels: [], datasets: [] };
    }

    const labels = result.data.map(row => String(row[labelColumn] || ''));
    
    const datasets = valueColumns.map((column, index) => {
      const data = result.data.map(row => Number(row[column]) || 0);
      
      // Generate colors for the dataset
      const colors = this.generateColors(data.length, index);
      
      return {
        label: column,
        data,
        backgroundColor: chartType === 'pie' || chartType === 'doughnut' ? colors : colors[0],
        borderColor: colors[0],
        borderWidth: 1,
      };
    });

    return { labels, datasets };
  }

  /**
   * Transform query result to KPI data format
   */
  transformToKPIData(
    result: QueryResult,
    valueColumn: string,
    labelColumn?: string,
    format: 'number' | 'currency' | 'percentage' = 'number'
  ): KPIData {
    if (result.error || result.data.length === 0) {
      return { value: 0, label: 'No Data', format };
    }

    const latestRow = result.data[result.data.length - 1];
    const value = latestRow[valueColumn];
    const label = labelColumn ? String(latestRow[labelColumn]) : valueColumn;

    // Calculate change if we have historical data
    let change: number | undefined;
    let changeType: 'increase' | 'decrease' | 'neutral' | undefined;
    
    if (result.data.length > 1) {
      const previousRow = result.data[result.data.length - 2];
      const previousValue = Number(previousRow[valueColumn]) || 0;
      const currentValue = Number(value) || 0;
      
      change = ((currentValue - previousValue) / previousValue) * 100;
      changeType = change > 0 ? 'increase' : change < 0 ? 'decrease' : 'neutral';
    }

    return {
      value: format === 'percentage' ? `${Number(value) || 0}%` : Number(value) || 0,
      label,
      change,
      changeType,
      format,
    };
  }

  /**
   * Transform query result to table data format
   */
  transformToTableData(
    result: QueryResult,
    pageSize: number = 10,
    page: number = 1
  ): TableData {
    if (result.error) {
      return { columns: [], rows: [] };
    }

    // Generate column definitions from the first row
    const columns = result.columns.map(col => ({
      key: col,
      label: col.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      type: this.inferColumnType(result.data, col),
      sortable: true,
    }));

    // Paginate the data
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const rows = result.data.slice(startIndex, endIndex);

    return {
      columns,
      rows,
      pagination: {
        page,
        pageSize,
        totalPages: Math.ceil(result.totalRecords / pageSize),
        totalRecords: result.totalRecords,
      },
    };
  }

  /**
   * Execute query against platform data sources
   */
  private async executePlatformQuery(dataSourceId: string, config: DataSourceConfig): Promise<QueryResult> {
    const startTime = Date.now();
    
    try {
      let data: any;
      
      switch (dataSourceId) {
        // Platform metrics endpoints removed
          
        default:
          throw new Error(`Unknown platform data source: ${dataSourceId}`);
      }
    } catch (error) {
      throw new Error(`Platform query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute query against file data sources
   */
  private async executeFileQuery(dataSourceId: string, config: DataSourceConfig): Promise<QueryResult> {
    // This would be implemented to query uploaded files
    // For now, return mock data
    const startTime = Date.now();
    
    return {
      data: [
        { id: 1, name: 'Sample Data', value: 100, date: '2024-01-01' },
        { id: 2, name: 'Sample Data 2', value: 150, date: '2024-01-02' },
      ],
      columns: ['id', 'name', 'value', 'date'],
      totalRecords: 2,
      executionTime: Date.now() - startTime,
    };
  }

  // Platform metrics transform methods removed

  /**
   * Infer column type from data
   */
  private inferColumnType(data: Record<string, any>[], column: string): 'string' | 'number' | 'date' | 'boolean' {
    if (data.length === 0) return 'string';
    
    const sample = data[0][column];
    
    if (typeof sample === 'boolean') return 'boolean';
    if (typeof sample === 'number') return 'number';
    if (sample instanceof Date || /^\d{4}-\d{2}-\d{2}/.test(String(sample))) return 'date';
    
    return 'string';
  }

  /**
   * Generate colors for charts
   */
  private generateColors(count: number, datasetIndex: number = 0): string[] {
    const baseColors = [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
      '#06B6D4', '#F97316', '#84CC16', '#EC4899', '#6366F1'
    ];
    
    const colors: string[] = [];
    for (let i = 0; i < count; i++) {
      colors.push(baseColors[(i + datasetIndex) % baseColors.length]);
    }
    
    return colors;
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.dataSourceCache.clear();
    this.queryCache.clear();
  }
}

export const dashboardDataService = new DashboardDataService();
