import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { X, Trash2, AlertTriangle } from 'lucide-react';
import { Dashboard, DashboardUpdate, DashboardDataSourceAssignment, DashboardDataSourceAssignmentCreate, DashboardDataSourceAssignmentUpdate } from '@/types/dashboard-customization';
import { DashboardDataSourceManager } from './DashboardDataSourceManager';
import { useToast } from '@/hooks/use-toast';

interface DashboardEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dashboard: Dashboard;
  onSubmit: (updates: DashboardUpdate) => Promise<void>;
  onDelete?: (dashboardId: string) => Promise<void>;
  canDelete?: boolean;
}

export const DashboardEditDialog: React.FC<DashboardEditDialogProps> = ({
  open,
  onOpenChange,
  dashboard,
  onSubmit,
  onDelete,
  canDelete = true
}) => {
  const [formData, setFormData] = useState<DashboardUpdate>({});
  const [tagInput, setTagInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [dataSourceAssignments, setDataSourceAssignments] = useState<DashboardDataSourceAssignment[]>([]);
  const { toast } = useToast();

  // Initialize form data when dashboard changes
  useEffect(() => {
    if (dashboard) {
      setFormData({
        name: dashboard.name,
        description: dashboard.description || '',
        is_default: dashboard.is_default,
        is_public: dashboard.is_public,
        refresh_interval: dashboard.refresh_interval,
        tags: dashboard.tags || []
      });
      setDataSourceAssignments(dashboard.data_source_assignments || []);
    }
  }, [dashboard]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name?.trim()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Include data source assignments in the update
      const updateData = {
        ...formData,
        data_source_assignments: dataSourceAssignments.map(assignment => ({
          system_data_source_id: assignment.system_data_source_id,
          alias: assignment.alias,
          is_active: assignment.is_active,
        }))
      };

      await onSubmit(updateData);
    } catch (error) {
      console.error('Failed to update dashboard:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }));
  };

  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Data source assignment management handlers
  const handleAddDataSourceAssignment = (assignment: DashboardDataSourceAssignmentCreate) => {
    const newAssignment: DashboardDataSourceAssignment = {
      id: `temp-${Date.now()}`, // Temporary ID for UI
      dashboard_id: dashboard.id,
      ...assignment,
      is_active: assignment.is_active ?? true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    setDataSourceAssignments(prev => [...prev, newAssignment]);
  };

  const handleUpdateDataSourceAssignment = (id: string, updates: DashboardDataSourceAssignmentUpdate) => {
    setDataSourceAssignments(prev => prev.map(assignment =>
      assignment.id === id ? { ...assignment, ...updates, updated_at: new Date().toISOString() } : assignment
    ));
  };

  const handleDeleteDataSourceAssignment = (id: string) => {
    setDataSourceAssignments(prev => prev.filter(assignment => assignment.id !== id));
  };

  // Handle dashboard deletion
  const handleDelete = async () => {
    if (!onDelete) return;

    setIsDeleting(true);
    try {
      await onDelete(dashboard.id);
      setShowDeleteDialog(false);
      onOpenChange(false);
      toast({
        title: "Dashboard Deleted",
        description: "Dashboard has been permanently deleted.",
      });
    } catch (error) {
      console.error('Error deleting dashboard:', error);
      toast({
        title: "Delete Failed",
        description: "Failed to delete dashboard. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Edit Dashboard</DialogTitle>
          <DialogDescription>
            Update your dashboard settings and configuration.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">Basic Settings</TabsTrigger>
            <TabsTrigger value="data-sources">Data Sources</TabsTrigger>
          </TabsList>

          <form onSubmit={handleSubmit} className="space-y-4">
            <TabsContent value="basic" className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Dashboard Name *</Label>
            <Input
              id="name"
              value={formData.name || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter dashboard name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what this dashboard shows"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags?.map((tag) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleRemoveTag(tag)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                id="tags"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleTagInputKeyPress}
                placeholder="Add tags (press Enter)"
                className="flex-1"
              />
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleAddTag}
                disabled={!tagInput.trim()}
              >
                Add
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="refresh_interval">Refresh Interval (seconds)</Label>
            <Input
              id="refresh_interval"
              type="number"
              min="30"
              max="3600"
              value={formData.refresh_interval || 300}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                refresh_interval: parseInt(e.target.value) || 300 
              }))}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Label htmlFor="is_default">Set as Default Dashboard</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_default"
                  checked={formData.is_default || false}
                  onCheckedChange={(checked) => setFormData(prev => ({ 
                    ...prev, 
                    is_default: checked 
                  }))}
                />
                <span className="text-sm text-muted-foreground">
                  This will be your default dashboard
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Label htmlFor="is_public">Make Public</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_public"
                  checked={formData.is_public || false}
                  onCheckedChange={(checked) => setFormData(prev => ({ 
                    ...prev, 
                    is_public: checked 
                  }))}
                />
                <span className="text-sm text-muted-foreground">
                  Other users can view this dashboard
                </span>
              </div>
            </div>
          </div>

          <div className="bg-muted/50 p-3 rounded-lg">
            <div className="text-sm text-muted-foreground">
              <strong>Dashboard Stats:</strong>
              <div className="mt-1">
                • {dashboard.section_count} sections
                • {dashboard.widget_count} widgets
                • Created: {new Date(dashboard.created_at).toLocaleDateString()}
                • Last updated: {new Date(dashboard.updated_at).toLocaleDateString()}
              </div>
            </div>
          </div>
            </TabsContent>

            <TabsContent value="data-sources" className="space-y-4">
              <DashboardDataSourceManager
                dataSourceAssignments={dataSourceAssignments}
                onAddDataSourceAssignment={handleAddDataSourceAssignment}
                onUpdateDataSourceAssignment={handleUpdateDataSourceAssignment}
                onDeleteDataSourceAssignment={handleDeleteDataSourceAssignment}
              />
            </TabsContent>

          <DialogFooter className="flex justify-between">
            <div className="flex-1">
              {onDelete && canDelete && !dashboard.is_default && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => setShowDeleteDialog(true)}
                  disabled={isSubmitting || isDeleting}
                  className="mr-auto"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Dashboard
                </Button>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting || isDeleting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!formData.name?.trim() || isSubmitting || isDeleting}
              >
                {isSubmitting ? 'Updating...' : 'Update Dashboard'}
              </Button>
            </div>
          </DialogFooter>
        </form>
        </Tabs>
      </DialogContent>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <span>Delete Dashboard</span>
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{dashboard?.name}"? This action cannot be undone.
              All sections, widgets, and configurations will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isDeleting ? 'Deleting...' : 'Delete Dashboard'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Dialog>
  );
};
