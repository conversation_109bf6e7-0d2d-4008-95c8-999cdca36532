import React from 'react';
import { InteractiveChartVisualization } from './InteractiveChartVisualization';

// Test component to verify interactive chart rendering
export const InteractiveChartTest: React.FC = () => {
  // Simulate the exact data structure from the backend logs
  const testData = {
    chart_data: [
      { name: "Category A", value: 4000, original_name: "Category A" },
      { name: "Category B", value: 3000, original_name: "Category B" },
      { name: "Category C", value: 2000, original_name: "Category C" },
      { name: "Category D", value: 2780, original_name: "Category D" },
      { name: "Category E", value: 1890, original_name: "Category E" }
    ],
    columns: ["name", "value"],
    metadata: {
      x_axis: "name",
      y_axes: ["value"],
      data_type: "backend_generated",
      total_records: 5
    },
    fallback_image: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
  };

  return (
    <div className="p-8 space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Interactive Chart Test</h2>
        <p className="text-gray-600 mb-6">
          This test component verifies that interactive charts render correctly with backend data.
        </p>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-3">Bar Chart Test</h3>
          <div className="border rounded-lg p-4">
            <InteractiveChartVisualization
              title="Test Bar Chart"
              description="Testing interactive bar chart with simulated backend data"
              data={testData}
              chartType="bar"
              config={{ responsive: true, interactive: true }}
            />
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3">Pie Chart Test</h3>
          <div className="border rounded-lg p-4">
            <InteractiveChartVisualization
              title="Test Pie Chart"
              description="Testing interactive pie chart with simulated backend data"
              data={testData}
              chartType="pie"
              config={{ responsive: true, interactive: true }}
            />
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3">Line Chart Test</h3>
          <div className="border rounded-lg p-4">
            <InteractiveChartVisualization
              title="Test Line Chart"
              description="Testing interactive line chart with simulated backend data"
              data={testData}
              chartType="line"
              config={{ responsive: true, interactive: true }}
            />
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold mb-2">Expected Behavior:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Charts should render as interactive React components (not static images)</li>
          <li>• Hover tooltips should work</li>
          <li>• Charts should be responsive</li>
          <li>• No "Static Fallback" badge should appear</li>
          <li>• Console should show "Interactive chart component created successfully"</li>
        </ul>
      </div>
    </div>
  );
};

export default InteractiveChartTest;
