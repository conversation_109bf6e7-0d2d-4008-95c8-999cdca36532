"""
Utility functions for initializing models in agents.

This module provides utility functions for initializing LLM models in agents
based on persona configuration.
"""

import logging
from typing import Dict, Any, Optional, Union

# Import LangChain models with compatibility for different versions
try:
    # Try newer LangChain structure
    from langchain_core.language_models.base import BaseLanguageModel
    from langchain_core.language_models.chat_models import BaseChatModel
except ImportError:
    try:
        # Try older LangChain structure
        from langchain.schema.language_model import BaseLanguageModel
        from langchain.schema.chat_model import BaseChatModel
    except ImportError:
        # Fallback to even older structure
        from langchain.base_language import BaseLanguageModel
        from langchain.chat_models.base import BaseChatModel

# Import the model provider system
from .model_providers.utils import get_model

# Configure logging
logger = logging.getLogger(__name__)


async def load_agent_database_config(persona_id: str) -> Dict[str, Any]:
    """
    Load agent configuration from database (admin panel settings).

    Args:
        persona_id: The ID of the persona/agent

    Returns:
        Dictionary containing database configuration (provider, model, etc.)
    """
    db_config = {}

    try:
        # Import database functions
        from ...app.database import get_db, get_persona

        # Get database session
        db_gen = get_db()
        db = next(db_gen)

        try:
            # Get persona configuration from database
            persona = get_persona(db, persona_id)
            if persona:
                db_config["provider"] = persona.provider
                db_config["model"] = persona.model
                logger.info(f"Loaded database config for {persona_id}: provider={persona.provider}, model={persona.model}")
            else:
                logger.warning(f"Persona '{persona_id}' not found in database")
        finally:
            db.close()

    except Exception as e:
        logger.warning(f"Could not load database configuration for {persona_id}: {e}")

    return db_config


def merge_agent_config(yaml_config: Dict[str, Any], db_config: Dict[str, Any], persona_id: str) -> Dict[str, Any]:
    """
    Merge YAML configuration with database configuration, prioritizing database settings.

    Args:
        yaml_config: Configuration from YAML files
        db_config: Configuration from database (admin panel)
        persona_id: The ID of the persona/agent

    Returns:
        Merged configuration dictionary
    """
    # Start with YAML config as base
    merged_config = yaml_config.copy()

    # Override with database config (admin panel settings take priority)
    if db_config.get("provider"):
        merged_config["provider"] = db_config["provider"]

    if db_config.get("model"):
        merged_config["model"] = db_config["model"]

    # Map model display names to actual model IDs
    model_mapping = {
        "Llama 3 3.70B Versatile": "llama3-70b-8192",
        "Llama 3.1 8B Instant": "llama-3.1-8b-instant",
        "Llama 3.1 70B Versatile": "llama-3.1-70b-versatile",
        "Mixtral 8x7B Instant": "mixtral-8x7b-32768",
        "Gemma 7B IT": "gemma-7b-it",
        "GPT-4": "gpt-4",
        "GPT-3.5 Turbo": "gpt-3.5-turbo",
        "Claude 3 Sonnet": "claude-3-sonnet-20240229",
        "Claude 3 Opus": "claude-3-opus-20240229"
    }

    # Map the model name if it's a display name
    if merged_config.get("model") in model_mapping:
        original_model = merged_config["model"]
        merged_config["model"] = model_mapping[original_model]
        logger.info(f"Mapped model for {persona_id}: '{original_model}' -> '{merged_config['model']}'")

    # Ensure provider is lowercase
    if merged_config.get("provider"):
        merged_config["provider"] = merged_config["provider"].lower()

    logger.info(f"Final merged config for {persona_id}: provider={merged_config.get('provider')}, model={merged_config.get('model')}")
    return merged_config


async def initialize_agent_model(
    config: Dict[str, Any],
    default_provider: str = "groq",
    default_model: Optional[str] = None
) -> Union[BaseLanguageModel, BaseChatModel]:
    """
    Initialize an LLM model for an agent based on configuration.

    This function uses the centralized model provider system to initialize
    a model based on the agent's configuration.

    Args:
        config: Agent configuration dictionary
        default_provider: Default provider to use if not specified in config
        default_model: Default model to use if not specified in config

    Returns:
        Initialized LLM model
    """
    # Get provider from config or use default
    provider_id = config.get("provider", default_provider).lower()

    # Get model from config or use default
    model_id = config.get("model", default_model)

    # Get model configuration
    model_config = {
        "temperature": config.get("temperature", 0.7),
    }

    # Add any additional configuration
    if "max_tokens" in config:
        model_config["max_tokens"] = config.get("max_tokens")

    if "top_p" in config:
        model_config["top_p"] = config.get("top_p")

    # Initialize the model using the model provider system
    try:
        logger.info(f"Initializing model from provider '{provider_id}', model '{model_id}'")
        model = await get_model(provider_id, model_id, model_config)
        logger.info(f"Successfully initialized model from provider '{provider_id}'")
        return model
    except Exception as e:
        logger.error(f"Error initializing model from provider '{provider_id}': {str(e)}", exc_info=True)
        raise ValueError(f"Failed to initialize model: {str(e)}")


async def initialize_component_model(
    component_config: Dict[str, Any],
    agent_config: Dict[str, Any],
    default_provider: str = "groq",
    default_model: Optional[str] = None
) -> Union[BaseLanguageModel, BaseChatModel]:
    """
    Initialize an LLM model for a component based on configuration.

    This function uses the centralized model provider system to initialize
    a model based on the component's configuration, falling back to the
    agent's configuration if needed.

    Args:
        component_config: Component configuration dictionary
        agent_config: Agent configuration dictionary
        default_provider: Default provider to use if not specified in config
        default_model: Default model to use if not specified in config

    Returns:
        Initialized LLM model
    """
    # First check component config
    provider_id = component_config.get("provider")
    model_id = component_config.get("model")

    # If not in component config, check agent config
    if not provider_id:
        provider_id = agent_config.get("provider", default_provider)

    if not model_id:
        model_id = agent_config.get("model", default_model)

    # Ensure provider_id is lowercase
    provider_id = provider_id.lower()

    # Get model configuration
    model_config = {
        "temperature": component_config.get("temperature", agent_config.get("temperature", 0.7)),
    }

    # Add any additional configuration
    if "max_tokens" in component_config:
        model_config["max_tokens"] = component_config.get("max_tokens")
    elif "max_tokens" in agent_config:
        model_config["max_tokens"] = agent_config.get("max_tokens")

    if "top_p" in component_config:
        model_config["top_p"] = component_config.get("top_p")
    elif "top_p" in agent_config:
        model_config["top_p"] = agent_config.get("top_p")

    # Initialize the model using the model provider system
    try:
        logger.info(f"Initializing component model from provider '{provider_id}', model '{model_id}'")
        model = await get_model(provider_id, model_id, model_config)
        logger.info(f"Successfully initialized component model from provider '{provider_id}'")
        return model
    except Exception as e:
        logger.error(f"Error initializing component model from provider '{provider_id}': {str(e)}", exc_info=True)
        raise ValueError(f"Failed to initialize component model: {str(e)}")
