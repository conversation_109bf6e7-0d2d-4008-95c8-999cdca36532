import React from 'react';
import { EnhancedMessage } from '@/components/chat/EnhancedMessage';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const sampleAnalysisWithTables = `# Data Profile for: Mobile duka

## Overview

| Property | Value |
|----------|-------|
| **Rows** | 1000 |
| **Columns** | 11 |
| **Data Types** | 7 categorical, 4 numeric |

## Data Preview (first few rows)

| TransactionID | Date | MobileModel | Brand | Price | UnitsSold | totalRevenue | CustomerAge | CustomerGender | Location | PaymentMethod |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 4f8d4e0a-b5a5 | 2024-01-06 | direction | Green Inc | 136.95 | 95 | 28000.80 | 32 | female | Port Erik | Online |
| 4f6f3f14-f522 | 2024-04-05 | right | <PERSON> | 1010.24 | 64 | 23785.52 | 55 | female | <PERSON> Linda | Credit Card |
| 9f50b6d6-dcc5 | 2024-02-13 | summer | <PERSON> | 400.80 | 95 | 31262.36 | 57 | Male | East Angelastad | Online |

## Statistical Summary

| Statistic | Price | UnitsSold | totalRevenue | CustomerAge |
|-----------|-------|-----------|--------------|-------------|
| **count** | 1000 | 1000 | 1000 | 1000 |
| **mean** | 550.45 | 75.23 | 41375.67 | 42.5 |
| **std** | 289.12 | 28.45 | 15234.89 | 12.8 |
| **min** | 50.00 | 10 | 500.00 | 18 |
| **25%** | 325.50 | 55 | 30125.25 | 32 |
| **50%** | 550.00 | 75 | 41250.00 | 42 |
| **75%** | 775.25 | 95 | 52500.75 | 53 |
| **max** | 1200.00 | 150 | 180000.00 | 70 |

This analysis shows that the mobile sales data contains comprehensive transaction information with good data quality and distribution.`;

const sampleMixedContent = `# Sales Analysis Report

Here's a summary of the key findings:

## Top Products by Revenue

| Product | Revenue | Units Sold | Avg Price |
|---------|---------|------------|-----------|
| iPhone 15 Pro | $125,000 | 250 | $500.00 |
| Samsung Galaxy S24 | $98,000 | 280 | $350.00 |
| Google Pixel 8 | $67,500 | 225 | $300.00 |

The analysis reveals strong performance across all product categories.

## Regional Performance

| Region | Total Sales | Growth Rate |
|--------|-------------|-------------|
| North America | $290,500 | +15.2% |
| Europe | $245,800 | +12.8% |
| Asia Pacific | $198,200 | +18.5% |

These results indicate healthy growth across all regions, with Asia Pacific showing the highest growth rate.`;

export const TableDetectionTest: React.FC = () => {
  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      <Card>
        <CardHeader>
          <CardTitle>Table Detection and Rendering Test</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            This test shows how the enhanced message component automatically detects tables 
            in Composable Analyst responses and renders them using dedicated table components.
          </p>
        </CardContent>
      </Card>

      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-bold text-gray-800 mb-4">Test 1: Data Profile with Multiple Tables</h2>
          <EnhancedMessage
            id="test-table-1"
            content={sampleAnalysisWithTables}
            sender="ai"
            timestamp={new Date().toISOString()}
            personaName="Composable Analyst"
            personaAvatar="/placeholder.svg"
            attachments={[]}
            metadata={{ agent_type: 'analysis' }}
            onCopy={() => console.log('Copied')}
            onFeedback={(rating) => console.log('Feedback:', rating)}
          />
        </div>

        <div>
          <h2 className="text-xl font-bold text-gray-800 mb-4">Test 2: Mixed Content with Tables</h2>
          <EnhancedMessage
            id="test-table-2"
            content={sampleMixedContent}
            sender="ai"
            timestamp={new Date().toISOString()}
            personaName="Composable Analyst"
            personaAvatar="/placeholder.svg"
            attachments={[]}
            metadata={{ agent_type: 'analysis' }}
            onCopy={() => console.log('Copied')}
            onFeedback={(rating) => console.log('Feedback:', rating)}
          />
        </div>

        <div>
          <h2 className="text-xl font-bold text-gray-800 mb-4">Test 3: Regular Message (No Special Rendering)</h2>
          <EnhancedMessage
            id="test-regular"
            content="This is a regular message without tables. It should render normally with standard markdown formatting."
            sender="ai"
            timestamp={new Date().toISOString()}
            personaName="Regular Assistant"
            personaAvatar="/placeholder.svg"
            attachments={[]}
            metadata={{}}
            onCopy={() => console.log('Copied')}
            onFeedback={(rating) => console.log('Feedback:', rating)}
          />
        </div>

        <div>
          <h2 className="text-xl font-bold text-gray-800 mb-4">Test 4: Composable Analyst with Metadata Table</h2>
          <EnhancedMessage
            id="test-metadata"
            content="Here's the data preview for your uploaded file:"
            sender="ai"
            timestamp={new Date().toISOString()}
            personaName="Composable Analyst"
            personaAvatar="/placeholder.svg"
            attachments={[]}
            metadata={{
              agent_type: 'analysis',
              preview_data: [
                { Name: 'John Doe', Age: 30, City: 'New York', Salary: 75000 },
                { Name: 'Jane Smith', Age: 28, City: 'Los Angeles', Salary: 82000 },
                { Name: 'Bob Johnson', Age: 35, City: 'Chicago', Salary: 68000 },
                { Name: 'Alice Brown', Age: 32, City: 'Houston', Salary: 71000 }
              ]
            }}
            onCopy={() => console.log('Copied')}
            onFeedback={(rating) => console.log('Feedback:', rating)}
          />
        </div>
      </div>
    </div>
  );
};

export default TableDetectionTest;
