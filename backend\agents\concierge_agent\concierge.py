"""
Concierge Agent for Datagenius.

This agent guides users, recommends personas, and assists with data interactions.
Implements intelligent user guidance, persona recommendation, and workflow coordination.
"""

import logging
import random
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pydantic import ValidationError

from ..enhanced_composable import EnhancedComposableAgent
from ..mixins import ToolCompletionMixin
from ..registry import AgentRegistry
# Hypothetical UserKnowledgeGraphService, to be implemented later
# from backend.app.services.knowledge_graph_service import UserKnowledgeGraphService
from .models import (
    UserIntent, ConversationContext, LLMAnalysisResponse, LLMAnalysisConfig,
    ConversationState, PersonaRecommendation, ConciergeResponse,
    PersonaRoutingRequest, PersonaRoutingResponse, ConversationStage
)
from .config_loader import get_config_loader
from .services import ConciergeService, ConversationStateManager, PersonaRecommendationService
from .exceptions import ConciergeEx<PERSON><PERSON><PERSON><PERSON>, ConciergeAgentException

logger = logging.getLogger(__name__)


class ConciergeAgent(ToolCompletionMixin, EnhancedComposableAgent):
    """
    The Concierge Agent acts as the primary point of contact, guiding users
    and coordinating interactions with other specialized agents.

    Features:
    - Intelligent intent recognition
    - Persona recommendation based on user needs
    - Data attachment assistance
    - Workflow coordination
    - Context-aware conversations
    """

    def get_agent_type(self) -> str:
        """Return the agent type identifier."""
        return "concierge"

    def get_tool_indicators(self) -> List[str]:
        """Return list of context keys that indicate tool-triggered requests."""
        return ["persona_recommendation_request", "persona_selection", "concierge_task"]

    def get_conversational_flags(self) -> List[str]:
        """Return list of context keys that indicate conversational mode."""
        return [
            "skip_persona_recommendation",
            "is_conversational",
            "recommendation_completed",
            "tool_completed",
            "auto_conversational_mode"
        ]

    def _get_agent_specific_new_request_patterns(self) -> List[str]:
        """Return agent-specific patterns that indicate new tool requests."""
        return [
            "recommend personas", "suggest agents", "help me choose",
            "show me options", "what personas", "which agent should",
            "persona recommendations", "agent suggestions", "recommend", "suggest"
        ]

    def __init__(self):
        """Initialize the Concierge Agent."""
        super().__init__()
        self.agent_registry = AgentRegistry
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        # self.kg_service = UserKnowledgeGraphService() # Initialize KG service

        # Load YAML configuration with Pydantic validation
        self.config_loader = get_config_loader()
        self.prompt_config = None  # Loaded lazily
        self.analysis_config = None  # Loaded lazily

        logger.info("Concierge Agent initialized with YAML-configured LLM-based guidance capabilities.")

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the Concierge Agent, loading its specific components.

        Args:
            config: Configuration dictionary for the agent.
        """
        await super()._initialize(config)

        # Initialize persona recommendation thresholds
        self.recommendation_threshold = config.get("recommendation_threshold", 0.7)
        self.max_recommendations = config.get("max_recommendations", 3)
        self.consider_user_history = config.get("consider_user_history", True)

        # Load model configuration from admin panel settings
        # Note: User settings will be loaded per-request since they're user-specific
        await self._load_model_configuration(config)

        logger.info(f"Concierge Agent '{self.name}' initialized with {len(self.components)} components.")

    async def _load_model_configuration(self, config: Dict[str, Any], user_settings: Optional[Dict[str, Any]] = None) -> None:
        """
        Load model configuration from admin panel database settings and user preferences.

        Args:
            config: Configuration dictionary for the agent
            user_settings: Optional user settings containing LLM provider and model preferences
        """
        try:
            # Import the centralized configuration utilities
            from ..utils.model_init import load_agent_database_config, merge_agent_config

            # Load database configuration for this agent
            db_config = await load_agent_database_config("concierge-agent")

            # Merge YAML config with database config (database takes priority)
            merged_config = merge_agent_config(config, db_config, "concierge-agent")

            # Check if user has specific concierge agent settings
            provider = "groq"
            model = "llama-3.1-8b-instant"

            if user_settings:
                user_provider = user_settings.get("concierge_agent_provider", "").lower()
                user_model = user_settings.get("concierge_agent_model", "")

                # Use user settings if both provider and model are specified
                if user_provider and user_model:
                    provider = user_provider
                    model = user_model
                    logger.info(f"Using user-configured concierge agent settings: {provider}/{model}")
                else:
                    # Fall back to merged config
                    provider = merged_config.get("provider", provider)
                    model = merged_config.get("model", model)
            else:
                # Use merged config
                provider = merged_config.get("provider", provider)
                model = merged_config.get("model", model)

            # Create analysis config with final settings
            analysis_config_data = {
                "provider": provider,
                "model": model,
                "temperature": 0.1,  # Keep low for analysis tasks
                "max_tokens": None,
                "timeout": 30,
                "retry_attempts": 3
            }

            # Load the analysis configuration with merged values
            self.analysis_config = self.config_loader.load_analysis_config(analysis_config_data)

            logger.info(f"Loaded model configuration for concierge agent: provider={analysis_config_data['provider']}, model={analysis_config_data['model']}")

        except Exception as e:
            logger.error(f"Error loading model configuration: {e}")
            # Fall back to default configuration
            self.analysis_config = self.config_loader.load_analysis_config()
            logger.info("Using default model configuration as fallback")

    async def _get_user_settings(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get user settings for LLM provider and model preferences.

        Args:
            user_id: The ID of the user

        Returns:
            Dictionary containing user settings or None if not found
        """
        try:
            from app.database import get_db, get_provider_settings

            # Get database session
            db = next(get_db())

            # Get user provider settings
            settings = get_provider_settings(db, int(user_id))

            return settings
        except Exception as e:
            logger.error(f"Error getting user settings for user {user_id}: {e}")
            return None

    async def _get_available_personas(self) -> List[Dict[str, str]]:
        """Get list of available personas with their descriptions."""
        try:
            available_personas = self.agent_registry.list_registered_personas()
            personas_info = []

            for persona_id in available_personas:
                config = self.agent_registry.get_configuration(persona_id)
                if config:
                    personas_info.append({
                        "id": persona_id,
                        "name": config.get("name", persona_id),
                        "description": config.get("description", "No description available")
                    })

            return personas_info
        except Exception as e:
            logger.error(f"Error getting available personas: {e}")
            return []

    async def parse_user_intent(self, message: str, user_context: Dict[str, Any]) -> UserIntent:
        """
        Parse user intent using LLM-based understanding instead of hardcoded patterns.

        Args:
            message: User's message
            user_context: Context about the user and conversation

        Returns:
            UserIntent object with parsed information
        """
        # Check if this is a continuing conversation
        is_continuing_conversation = user_context.get("is_continuing_conversation", False)
        conversation_state = user_context.get("conversation_state")

        # Handle simple greetings in continuing conversations
        if (message.lower().strip() in ["hello", "hi", "hey", "start"] and
            is_continuing_conversation and conversation_state and
            conversation_state.conversation_history):
            return UserIntent(
                intent_type="general_question",
                confidence=0.9,
                entities={},
                suggested_personas=[],
                requires_data=False,
                complexity_score=0.1
            )

        # Use LLM to understand user intent and recommend personas
        try:
            llm_analysis = await self._analyze_user_request_with_llm(message, user_context)

            return UserIntent(
                intent_type=llm_analysis.get("intent_type", "general_question"),
                confidence=llm_analysis.get("confidence", 0.7),
                entities=llm_analysis.get("entities", {}),
                suggested_personas=llm_analysis.get("suggested_personas", []),
                requires_data=llm_analysis.get("requires_data", False),
                complexity_score=llm_analysis.get("complexity_score", 0.5)
            )

        except Exception as e:
            logger.error(f"Error in LLM-based intent parsing: {e}")
            # Fallback to basic analysis
            return UserIntent(
                intent_type="general_question",
                confidence=0.5,
                entities={},
                suggested_personas=[],
                requires_data="data" in message.lower() or "file" in message.lower(),
                complexity_score=0.5
            )

    async def _analyze_user_request_with_llm(self, message: str, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced LLM analysis with multi-dimensional understanding and advanced context processing.

        Args:
            message: User's message
            user_context: Context about the user and conversation

        Returns:
            Dictionary with validated analysis results including intent, personas, etc.
        """
        try:
            # Load configuration if not already loaded
            if self.prompt_config is None:
                self.prompt_config = self.config_loader.load_prompt_config()
            if self.analysis_config is None:
                self.analysis_config = self.config_loader.load_analysis_config()

            # Check if we need to reload configuration with user-specific settings
            user_id = user_context.get("user_id")
            if user_id:
                user_settings = await self._get_user_settings(str(user_id))
                if user_settings:
                    # Check if user has specific concierge agent settings
                    user_provider = user_settings.get("concierge_agent_provider", "").lower()
                    user_model = user_settings.get("concierge_agent_model", "")

                    # If user has specific settings, reload configuration
                    if user_provider and user_model:
                        current_provider = self.analysis_config.provider if self.analysis_config else ""
                        current_model = self.analysis_config.model if self.analysis_config else ""

                        # Only reload if settings are different
                        if current_provider != user_provider or current_model != user_model:
                            logger.info(f"Reloading concierge agent configuration for user {user_id} with {user_provider}/{user_model}")
                            await self._load_model_configuration({}, user_settings)

            # Get enhanced personas description with capabilities and ratings
            personas_description = await self._build_enhanced_personas_description(str(user_id) if user_id else None)

            # Fetch user-specific knowledge graph insights with enhanced processing
            user_kg_insights = await self._get_enhanced_user_insights(user_context)

            # Build enhanced conversation context with sentiment and patterns
            conversation_history_context = await self._build_enhanced_conversation_context(user_context)

            # Get user interaction patterns and preferences
            user_patterns = await self._analyze_user_patterns(user_context)

            # Create comprehensive context for LLM
            full_context_for_llm = self._create_comprehensive_context(
                user_kg_insights, conversation_history_context, user_patterns, message
            )

            # Determine analysis complexity and adjust approach
            analysis_complexity = self._assess_analysis_complexity(message, user_context)
            analysis_mode = self.config_loader.get_analysis_mode(
                "detailed" if analysis_complexity > 0.7 else "quick"
            )

            # Create enhanced analysis prompt with multiple analysis dimensions
            analysis_prompt = self._create_enhanced_analysis_prompt(
                personas_description, message, full_context_for_llm, analysis_complexity
            )

            # Get LLM with enhanced configuration
            from ..utils.model_providers.utils import get_model
            llm = await get_model(
                self.analysis_config.provider,
                self.analysis_config.model,
                {
                    "temperature": analysis_mode.temperature,
                    "max_tokens": 2000 if analysis_complexity > 0.7 else 1500,
                    "top_p": 0.9,
                    "frequency_penalty": 0.1
                }
            )

            # Create enhanced messages with system prompt and examples
            messages = [
                {"role": "system", "content": self._get_enhanced_system_prompt()},
                {"role": "user", "content": analysis_prompt}
            ]

            # Add few-shot examples for better performance
            if analysis_complexity > 0.5:
                messages = self._add_few_shot_examples(messages)

            # Get the analysis with enhanced error handling
            response = await self._call_llm_with_retry(llm, messages)
            response_text = response.content if hasattr(response, 'content') else str(response)

            # Enhanced parsing with multiple fallback strategies
            analysis_data = await self._parse_llm_response_enhanced(response_text, message)

            # Enhanced validation with confidence scoring
            validation_result = await self._validate_response_enhanced(analysis_data, message, user_context)

            if not validation_result["is_valid"]:
                logger.warning(f"LLM response failed enhanced validation: {validation_result['reason']}")
                return self._get_enhanced_fallback_response("validation_error", message, user_context)

            # Create validated Pydantic model with enhancements
            validated_response = LLMAnalysisResponse(**analysis_data)

            # Post-process and enhance the response
            enhanced_response = await self._post_process_analysis(validated_response, message, user_context)

            # Log detailed analytics
            await self._log_analysis_metrics(enhanced_response, message, user_context, analysis_complexity)

            logger.info(f"Enhanced LLM analysis successful: {enhanced_response.reasoning[:100]}...")
            return enhanced_response.model_dump()

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM JSON response: {e}")
            return self._get_enhanced_fallback_response("parsing_error", message, user_context)

        except ValidationError as e:
            logger.error(f"LLM response validation failed: {e}")
            return self._get_enhanced_fallback_response("validation_error", message, user_context)

        except Exception as e:
            logger.error(f"Error in enhanced LLM analysis: {e}")
            return self._get_enhanced_fallback_response("llm_error", message, user_context)

    async def _build_enhanced_personas_description(self, user_id: str = None) -> str:
        """Build enhanced personas description using real marketplace data instead of hallucinating."""
        try:
            # Use the PersonaMarketplaceTool to get real data
            from ..tools.mcp.persona_marketplace_tool import PersonaMarketplaceTool

            marketplace_tool = PersonaMarketplaceTool()
            await marketplace_tool._initialize({})

            # Get all available personas from marketplace
            tool_result = await marketplace_tool.execute({
                "user_id": user_id or "1",  # Default user_id if not provided
                "query_type": "available_personas"
            })

            if not tool_result.get("isError", False):
                metadata = tool_result.get("metadata", {})
                data = metadata.get("data", {})
                available_personas = data.get("available_personas", [])

                if available_personas:
                    descriptions = []
                    for persona in available_personas:
                        name = persona.get("name", persona.get("id", "Unknown"))
                        description = persona.get("description", "AI assistant for specialized tasks")
                        capabilities = persona.get("capabilities", [])
                        rating = persona.get("rating", 0.0)
                        review_count = persona.get("review_count", 0)
                        price = persona.get("price", 0.0)

                        # Build enhanced description
                        capability_str = ", ".join(capabilities) if capabilities else "General assistance"
                        rating_str = f" (⭐ {rating:.1f}/5.0, {review_count} reviews)" if rating > 0 else ""
                        price_str = f" - ${price:.2f}" if price > 0 else " - Free"

                        descriptions.append(
                            f"- **{name}**: {description}\n"
                            f"  Capabilities: {capability_str}{rating_str}{price_str}"
                        )

                    return "\n".join(descriptions)
                else:
                    logger.warning("No personas found in marketplace")
                    return "No personas currently available in the marketplace."
            else:
                logger.error(f"Error from marketplace tool: {tool_result.get('content', [{}])[0].get('text', 'Unknown error')}")
                return await self._fallback_personas_description()

        except Exception as e:
            logger.error(f"Error building enhanced personas description: {e}")
            return await self._fallback_personas_description()

    async def _fallback_personas_description(self) -> str:
        """Fallback method to get personas description when marketplace tool fails."""
        try:
            # Get personas from registry as fallback
            available_personas = self.agent_registry.list_registered_personas()

            descriptions = []
            for persona_id in available_personas:
                try:
                    config = self.agent_registry.get_configuration(persona_id)
                    if config:
                        name = config.get("name", persona_id.replace("-", " ").title())
                        description = config.get("description", "AI assistant for specialized tasks")
                        capabilities = config.get("capabilities", [])

                        capability_str = ", ".join(capabilities) if capabilities else "General assistance"
                        descriptions.append(f"- **{name}**: {description} (Capabilities: {capability_str})")
                except Exception as e:
                    logger.warning(f"Error getting config for persona {persona_id}: {e}")
                    descriptions.append(f"- **{persona_id.replace('-', ' ').title()}**: Available AI assistant")

            return "\n".join(descriptions) if descriptions else "AI personas available for various tasks."

        except Exception as e:
            logger.error(f"Error in fallback personas description: {e}")
            return "AI personas available for various tasks."

    async def _get_enhanced_user_insights(self, user_context: Dict[str, Any]) -> str:
        """Get enhanced user insights from knowledge graph and interaction history."""
        insights = []

        try:
            user_id = user_context.get("user_id")
            if user_id:
                # Get user preferences and patterns
                user_preferences = user_context.get("user_preferences", {})
                if user_preferences:
                    insights.append(f"User Preferences: {', '.join(f'{k}: {v}' for k, v in user_preferences.items())}")

                # Get interaction history summary
                conversation_history = user_context.get("conversation_history", [])
                if conversation_history:
                    recent_topics = self._extract_recent_topics(conversation_history)
                    if recent_topics:
                        insights.append(f"Recent Topics: {', '.join(recent_topics)}")

                # Get user expertise level indicators
                expertise_indicators = self._assess_user_expertise(conversation_history)
                if expertise_indicators:
                    insights.append(f"User Expertise: {expertise_indicators}")

        except Exception as e:
            logger.error(f"Error getting enhanced user insights: {e}")

        return "\n".join(insights) if insights else ""

    async def _build_enhanced_conversation_context(self, user_context: Dict[str, Any]) -> str:
        """Build enhanced conversation context with sentiment and pattern analysis."""
        try:
            conversation_history = user_context.get("conversation_history", [])
            if not conversation_history:
                return ""

            # Analyze recent conversation patterns
            recent_messages = conversation_history[-5:]  # Last 5 messages

            # Extract sentiment and topics
            context_parts = []

            # Add conversation flow analysis
            if len(recent_messages) > 1:
                flow_analysis = self._analyze_conversation_flow(recent_messages)
                if flow_analysis:
                    context_parts.append(f"Conversation Flow: {flow_analysis}")

            # Add recent interaction summary
            recent_summary = self._summarize_recent_interactions(recent_messages)
            if recent_summary:
                context_parts.append(f"Recent Context: {recent_summary}")

            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"Error building enhanced conversation context: {e}")
            return self._build_conversation_context(user_context)  # Fallback to original method

    async def _analyze_user_patterns(self, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user interaction patterns and preferences."""
        patterns = {
            "communication_style": "standard",
            "complexity_preference": "medium",
            "response_length_preference": "medium",
            "technical_level": "intermediate"
        }

        try:
            conversation_history = user_context.get("conversation_history", [])
            if conversation_history:
                # Analyze communication style
                patterns["communication_style"] = self._detect_communication_style(conversation_history)

                # Analyze complexity preference
                patterns["complexity_preference"] = self._detect_complexity_preference(conversation_history)

                # Analyze technical level
                patterns["technical_level"] = self._assess_technical_level(conversation_history)

        except Exception as e:
            logger.error(f"Error analyzing user patterns: {e}")

        return patterns

    def _build_personas_description(self) -> str:
        """Build personas description from YAML configuration."""
        descriptions = []
        for persona_id, persona_desc in self.prompt_config.persona_descriptions.items():
            capabilities = ", ".join(persona_desc.capabilities)
            descriptions.append(f"- {persona_id}: {persona_desc.description} (Capabilities: {capabilities})")
        return "\n".join(descriptions)

    def _build_conversation_context(self, user_context: Dict[str, Any]) -> str:
        """Build conversation context string for the LLM prompt."""
        conversation_history = user_context.get("conversation_history", [])
        if not conversation_history:
            return ""

        recent_messages = conversation_history[-3:]  # Last 3 messages for context
        history_lines = [
            f"{msg.get('sender', 'unknown')}: {msg.get('content', '')}"
            for msg in recent_messages
        ]
        return f"Recent Conversation History:\n" + "\n".join(history_lines) + "\n"

    def _get_fallback_response(self, error_type: str, message: str) -> Dict[str, Any]:
        """Get fallback response using YAML configuration."""
        fallback = self.config_loader.get_fallback_response(error_type)

        # Add basic analysis for data requirement
        requires_data = any(keyword in message.lower() for keyword in ["data", "file", "csv", "excel", "analyze"])

        return {
            "intent_type": fallback.intent_type,
            "confidence": fallback.confidence,
            "entities": {},
            "suggested_personas": [],
            "requires_data": requires_data,
            "complexity_score": 0.5,
            "reasoning": fallback.reasoning
        }

    def _get_enhanced_fallback_response(self, error_type: str, message: str, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced fallback response with better intent detection for persona queries."""
        # Check if this looks like a persona query or request based on keywords
        message_lower = message.lower()

        # Detect persona queries
        persona_query_keywords = [
            "what personas", "what agents", "what can you do", "what are available",
            "show me", "list", "available", "personas", "agents", "what options"
        ]

        # Detect persona requests
        persona_request_keywords = [
            "recommend", "suggest", "best persona", "which persona", "help me find",
            "need a persona", "want a persona", "persona for", "agent for"
        ]

        if any(keyword in message_lower for keyword in persona_query_keywords):
            intent_type = "persona_query"
            confidence = 0.8
        elif any(keyword in message_lower for keyword in persona_request_keywords):
            intent_type = "persona_request"
            confidence = 0.8
        else:
            # Use original fallback logic
            fallback = self.config_loader.get_fallback_response(error_type)
            intent_type = fallback.intent_type
            confidence = fallback.confidence

        # Add basic analysis for data requirement
        requires_data = any(keyword in message.lower() for keyword in ["data", "file", "csv", "excel", "analyze"])

        return {
            "intent_type": intent_type,
            "confidence": confidence,
            "entities": {},
            "suggested_personas": [],
            "requires_data": requires_data,
            "complexity_score": 0.5,
            "reasoning": f"Enhanced fallback for {error_type} - detected {intent_type} intent"
        }

    async def _parse_llm_response_enhanced(self, response_text: str, message: str) -> Dict[str, Any]:
        """Enhanced parsing with multiple fallback strategies."""
        try:
            # Try to parse as JSON
            if response_text.strip().startswith('{'):
                return json.loads(response_text)

            # Try to extract JSON from markdown code blocks
            import re
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))

            # Try to find JSON anywhere in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))

        except json.JSONDecodeError:
            pass

        # Fallback: create response based on message analysis
        return self._create_fallback_analysis(message)

    def _create_fallback_analysis(self, message: str) -> Dict[str, Any]:
        """Create fallback analysis when LLM parsing fails."""
        message_lower = message.lower()

        # Detect intent based on keywords
        if any(keyword in message_lower for keyword in ["what personas", "what agents", "available", "list", "show me"]):
            intent_type = "persona_query"
            confidence = 0.7
        elif any(keyword in message_lower for keyword in ["recommend", "suggest", "best", "which", "help me find"]):
            intent_type = "persona_request"
            confidence = 0.7

        else:
            intent_type = "general_question"
            confidence = 0.5

        return {
            "intent_type": intent_type,
            "confidence": confidence,
            "entities": {},
            "suggested_personas": [],
            "requires_data": "data" in message_lower or "file" in message_lower,
            "complexity_score": 0.5,
            "reasoning": f"Fallback analysis detected {intent_type} intent"
        }

    async def _validate_response_enhanced(self, analysis_data: Dict[str, Any], message: str, user_context: Dict[str, Any]) -> Dict[str, bool]:
        """Enhanced validation with confidence scoring."""
        try:
            # Check required fields
            required_fields = ["intent_type", "confidence"]
            for field in required_fields:
                if field not in analysis_data:
                    return {"is_valid": False, "reason": f"Missing required field: {field}"}

            # Validate intent type
            valid_intents = [
                "persona_request", "persona_query", "general_question"
            ]
            if analysis_data["intent_type"] not in valid_intents:
                return {"is_valid": False, "reason": f"Invalid intent type: {analysis_data['intent_type']}"}

            # Validate confidence range
            confidence = analysis_data.get("confidence", 0)
            if not (0 <= confidence <= 1):
                return {"is_valid": False, "reason": f"Invalid confidence: {confidence}"}

            return {"is_valid": True, "reason": "Validation passed"}

        except Exception as e:
            return {"is_valid": False, "reason": f"Validation error: {e}"}

    def _create_enhanced_analysis_prompt(self, personas_description: str, message: str, context: str, complexity: float) -> str:
        """Create enhanced analysis prompt with multiple analysis dimensions."""
        try:
            # Load prompt template from YAML config
            prompt_config = self.config_loader.load_prompt_config()
            template = prompt_config.analysis_prompt.user_prompt_template

            # Format the template with actual values
            return template.format(
                personas_description=personas_description,
                message=message,
                conversation_context=context if context else "No previous conversation context."
            )
        except Exception as e:
            logger.error(f"Error creating enhanced analysis prompt: {e}")
            # Fallback to basic prompt
            return f"""Available AI Personas:
{personas_description}

User's Request: "{message}"

{context}

Analyze the user's request and respond with a JSON object containing:
{{
    "intent_type": "one of: persona_request, persona_query, data_help, analysis_request, marketing_request, classification_request, general_question",
    "confidence": 0.0-1.0,
    "entities": {{}},
    "suggested_personas": [],
    "requires_data": false,
    "complexity_score": 0.5,
    "reasoning": "explanation of the analysis"
}}"""

    def _get_enhanced_system_prompt(self) -> str:
        """Get enhanced system prompt for LLM analysis."""
        try:
            prompt_config = self.config_loader.load_prompt_config()
            return prompt_config.analysis_prompt.system_message
        except Exception as e:
            logger.error(f"Error getting enhanced system prompt: {e}")
            # Fallback system prompt
            return """You are the Datagenius Concierge AI, an intelligent assistant that analyzes user requests
and recommends the most suitable AI personas for their needs.

Your role is to:
1. Understand what the user wants to accomplish
2. Extract relevant entities and context
3. Recommend the most appropriate AI personas
4. Assess task complexity and data requirements
5. Provide clear reasoning for your recommendations

Always respond with valid JSON that matches the expected schema.

Intent Classification Guidelines:
- "persona_request": User wants recommendations for specific tasks
- "persona_query": User wants to browse/list available personas
- Use "persona_request" when user has a specific need and wants targeted recommendations
- Use "persona_query" when user wants to explore options or see what's available"""

    def _add_few_shot_examples(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Add few-shot examples for better performance."""
        examples = [
            {
                "role": "user",
                "content": 'User\'s Request: "What personas are available?"\n\nAnalyze this request.'
            },
            {
                "role": "assistant",
                "content": '{"intent_type": "persona_query", "confidence": 0.9, "entities": {}, "suggested_personas": [], "requires_data": false, "complexity_score": 0.2, "reasoning": "User is asking to see available personas - this is a persona query"}'
            },
            {
                "role": "user",
                "content": 'User\'s Request: "Recommend the best persona for marketing"\n\nAnalyze this request.'
            },
            {
                "role": "assistant",
                "content": '{"intent_type": "persona_request", "confidence": 0.9, "entities": {"task_type": "marketing"}, "suggested_personas": ["marketing-agent"], "requires_data": false, "complexity_score": 0.6, "reasoning": "User wants a specific recommendation for marketing tasks"}'
            }
        ]

        # Insert examples before the actual user message
        return messages[:-1] + examples + [messages[-1]]

    async def _call_llm_with_retry(self, llm, messages: List[Dict[str, str]], max_retries: int = 3) -> Any:
        """Call LLM with retry logic."""
        for attempt in range(max_retries):
            try:
                if hasattr(llm, 'ainvoke'):
                    # LangChain async style
                    return await llm.ainvoke(messages)
                elif hasattr(llm, 'invoke'):
                    # LangChain sync style (wrap in async)
                    import asyncio
                    return await asyncio.get_event_loop().run_in_executor(None, llm.invoke, messages)
                else:
                    raise ValueError(f"Unknown LLM interface: {type(llm)}")

            except Exception as e:
                logger.warning(f"LLM call attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(1)  # Wait before retry

    async def _post_process_analysis(self, response: LLMAnalysisResponse, message: str, user_context: Dict[str, Any]) -> LLMAnalysisResponse:
        """Post-process and enhance the LLM analysis response."""
        # Add any post-processing logic here
        # For now, just return the response as-is
        return response

    async def _log_analysis_metrics(self, response: LLMAnalysisResponse, message: str, user_context: Dict[str, Any], complexity: float) -> None:
        """Log detailed analytics for the analysis."""
        logger.info(f"Analysis metrics - Intent: {response.intent_type}, Confidence: {response.confidence}, Complexity: {complexity}")

    # Helper methods for enhanced analysis
    def _create_comprehensive_context(self, user_insights: str, conversation_context: str, user_patterns: Dict[str, Any], message: str) -> str:
        """Create comprehensive context for LLM analysis."""
        context_parts = []

        if user_insights:
            context_parts.append(f"User Insights:\n{user_insights}")

        if conversation_context:
            context_parts.append(f"Conversation Context:\n{conversation_context}")

        if user_patterns:
            patterns_str = ", ".join(f"{k}: {v}" for k, v in user_patterns.items())
            context_parts.append(f"User Patterns: {patterns_str}")

        return "\n\n".join(context_parts) if context_parts else ""

    def _assess_analysis_complexity(self, message: str, user_context: Dict[str, Any]) -> float:
        """Assess the complexity of the analysis required."""
        complexity_score = 0.3  # Base complexity

        # Increase complexity based on message length
        if len(message) > 100:
            complexity_score += 0.2

        # Increase complexity if conversation history exists
        if user_context.get("conversation_history"):
            complexity_score += 0.2

        # Increase complexity for specific keywords
        complex_keywords = ["analyze", "compare", "complex", "detailed", "comprehensive"]
        if any(keyword in message.lower() for keyword in complex_keywords):
            complexity_score += 0.3

        return min(complexity_score, 1.0)

    # Helper methods for conversation analysis
    def _extract_recent_topics(self, conversation_history: List[Dict[str, Any]]) -> List[str]:
        """Extract recent topics from conversation history."""
        topics = []
        for msg in conversation_history[-5:]:  # Last 5 messages
            content = msg.get("content", "").lower()
            # Simple keyword extraction
            if "data" in content or "analysis" in content:
                topics.append("data analysis")
            elif "marketing" in content or "campaign" in content:
                topics.append("marketing")
            elif "persona" in content or "agent" in content:
                topics.append("personas")
        return list(set(topics))  # Remove duplicates

    def _assess_user_expertise(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Assess user expertise level from conversation history."""
        if not conversation_history:
            return "beginner"

        # Simple heuristic based on technical terms used
        technical_terms = ["api", "algorithm", "database", "sql", "python", "machine learning"]
        total_messages = len(conversation_history)
        technical_count = 0

        for msg in conversation_history:
            content = msg.get("content", "").lower()
            if any(term in content for term in technical_terms):
                technical_count += 1

        if technical_count / total_messages > 0.3:
            return "advanced"
        elif technical_count / total_messages > 0.1:
            return "intermediate"
        else:
            return "beginner"

    def _analyze_conversation_flow(self, recent_messages: List[Dict[str, Any]]) -> str:
        """Analyze conversation flow patterns."""
        if len(recent_messages) < 2:
            return ""

        # Simple flow analysis
        last_msg = recent_messages[-1].get("content", "").lower()
        prev_msg = recent_messages[-2].get("content", "").lower()

        if "?" in last_msg and "?" in prev_msg:
            return "question sequence"
        elif any(word in last_msg for word in ["thanks", "thank you", "great"]):
            return "positive feedback"
        elif any(word in last_msg for word in ["help", "confused", "unclear"]):
            return "needs clarification"
        else:
            return "normal flow"

    def _summarize_recent_interactions(self, recent_messages: List[Dict[str, Any]]) -> str:
        """Summarize recent interactions."""
        if not recent_messages:
            return ""

        # Extract key themes from recent messages
        themes = []
        for msg in recent_messages:
            content = msg.get("content", "").lower()
            if "persona" in content or "agent" in content:
                themes.append("discussing personas")
            elif "data" in content:
                themes.append("working with data")
            elif "help" in content:
                themes.append("seeking assistance")

        if themes:
            return f"Recent discussion about: {', '.join(set(themes))}"
        else:
            return "General conversation"

    def _detect_communication_style(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Detect user's communication style."""
        if not conversation_history:
            return "standard"

        # Analyze message characteristics
        total_length = sum(len(msg.get("content", "")) for msg in conversation_history)
        avg_length = total_length / len(conversation_history)

        if avg_length > 100:
            return "detailed"
        elif avg_length < 20:
            return "concise"
        else:
            return "standard"

    def _detect_complexity_preference(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Detect user's complexity preference."""
        if not conversation_history:
            return "medium"

        # Look for indicators of complexity preference
        complex_indicators = ["detailed", "comprehensive", "thorough", "in-depth"]
        simple_indicators = ["simple", "quick", "basic", "easy"]

        complex_count = 0
        simple_count = 0

        for msg in conversation_history:
            content = msg.get("content", "").lower()
            complex_count += sum(1 for indicator in complex_indicators if indicator in content)
            simple_count += sum(1 for indicator in simple_indicators if indicator in content)

        if complex_count > simple_count:
            return "high"
        elif simple_count > complex_count:
            return "low"
        else:
            return "medium"

    def _assess_technical_level(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Assess user's technical level."""
        return self._assess_user_expertise(conversation_history)  # Reuse existing method

    # Removed _suggest_personas_for_intent - now handled by LLM analysis in _analyze_user_request_with_llm

    # Removed _calculate_complexity - now handled by LLM analysis

    async def get_conversation_context(self, user_id: str, session_id: str, database_context: Dict[str, Any] = None) -> ConversationContext:
        """Get or create conversation context for a user session, merging database history."""
        context_key = f"{user_id}_{session_id}"

        if context_key not in self.conversation_contexts:
            self.conversation_contexts[context_key] = ConversationContext(
                user_id=user_id,
                session_id=session_id,
                conversation_history=[],
                user_preferences={},
                current_task=None,
                attached_data=[],
                last_interaction=datetime.now()
            )

        context = self.conversation_contexts[context_key]

        # Merge database conversation history if provided
        if database_context and "conversation_history" in database_context:
            database_history = database_context["conversation_history"]
            if database_history:
                # Convert database history to our internal format
                merged_history = []

                # Add database history first (older messages)
                for db_msg in database_history:
                    if db_msg.get("sender") == "user":
                        merged_history.append({
                            "timestamp": db_msg.get("timestamp", ""),
                            "user_message": db_msg.get("content", ""),
                            "agent_response": "",  # Will be filled by next message
                            "intent": "unknown",
                            "confidence": 0.5
                        })
                    elif db_msg.get("sender") == "ai" and merged_history:
                        # Update the last entry with the AI response
                        merged_history[-1]["agent_response"] = db_msg.get("content", "")

                # Merge with in-memory history (newer messages)
                # Remove duplicates by checking timestamps and content
                existing_timestamps = {entry.get("timestamp") for entry in context.conversation_history}

                for db_entry in merged_history:
                    if db_entry.get("timestamp") not in existing_timestamps:
                        context.conversation_history.insert(0, db_entry)  # Insert at beginning (older)

                # Sort by timestamp to maintain chronological order
                context.conversation_history.sort(key=lambda x: x.get("timestamp", ""))

                # Keep only last 50 interactions for memory efficiency
                if len(context.conversation_history) > 50:
                    context.conversation_history = context.conversation_history[-50:]

                logger.info(f"Merged conversation context: {len(database_history)} DB messages, {len(context.conversation_history)} total history entries")

        return context

    async def update_conversation_context(
        self,
        context: ConversationContext,
        message: str,
        response: str,
        intent: UserIntent
    ) -> None:
        """Update conversation context with new interaction."""
        context.conversation_history.append({
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "agent_response": response,
            "intent": intent.intent_type,
            "confidence": intent.confidence
        })

        # Keep only last 50 interactions for memory efficiency
        if len(context.conversation_history) > 50:
            context.conversation_history = context.conversation_history[-50:]

        context.last_interaction = datetime.now()

        # Update current task if applicable
        if intent.intent_type in ["analysis_request", "marketing_request", "classification_request"]:
            context.current_task = intent.intent_type

    async def generate_welcome_message(self, user_context: Dict[str, Any]) -> str:
        """Generate a personalized welcome message for the user."""
        user_name = user_context.get("user_name", "there")

        welcome_messages = [
            f"Hello {user_name}! I'm your Datagenius Concierge. I'm here to help you find the perfect AI persona for your needs and guide you through your data journey.",
            f"Welcome back, {user_name}! Ready to unlock insights from your data? I can recommend the best AI persona for your specific task.",
            f"Hi {user_name}! I'm here to make your Datagenius experience seamless. Whether you need data analysis, marketing content, or classification - I'll guide you to the right persona."
        ]

        return random.choice(welcome_messages)

    async def generate_contextual_greeting(
        self,
        message: str,
        conversation_context: ConversationContext,
        user_context: Dict[str, Any]
    ) -> str:
        """
        Generate a contextual greeting for continuing conversations.

        Args:
            message: The user's greeting message
            conversation_context: The conversation context with history
            user_context: Additional user context from orchestrator

        Returns:
            A contextual greeting that acknowledges the ongoing conversation
        """
        user_name = user_context.get("user_name", "")
        greeting_prefix = f"Hello again{', ' + user_name if user_name else ''}! "

        # Analyze recent conversation history to provide context
        recent_history = conversation_context.conversation_history[-3:] if conversation_context.conversation_history else []

        if not recent_history:
            # Fallback if no in-memory history but database indicates conversation exists
            return greeting_prefix + "How can I continue to help you today?"

        # Look for patterns in recent conversation
        last_intent = recent_history[-1].get("intent", "") if recent_history else ""

        contextual_responses = {
            "persona_request": "I see we were discussing persona recommendations. Would you like to continue exploring options or do you have a new request?",
            "persona_query": "We were looking at available personas. Would you like to see more options or do you have a specific need?",
            "general_question": "How can I continue to assist you today?"
        }

        contextual_message = contextual_responses.get(last_intent, "How can I continue to help you today?")

        return greeting_prefix + contextual_message

    async def handle_persona_query(self, message: str, user_id: str) -> Dict[str, Any]:
        """
        Handle queries about available personas using real marketplace data.

        Args:
            message: User's message asking about personas
            user_id: ID of the user making the query

        Returns:
            Dictionary with response message and metadata for interactive UI
        """
        try:
            # Use the PersonaMarketplaceTool to get real data
            from ..tools.mcp.persona_marketplace_tool import PersonaMarketplaceTool

            marketplace_tool = PersonaMarketplaceTool()
            await marketplace_tool._initialize({})

            # Get marketplace overview with user ownership status
            tool_result = await marketplace_tool.execute({
                "user_id": user_id,
                "query_type": "marketplace_overview"
            })

            if not tool_result.get("isError", False):
                metadata = tool_result.get("metadata", {})
                data = metadata.get("data", {})

                owned_personas = data.get("owned_personas", [])
                free_personas = data.get("free_personas", [])
                paid_personas = data.get("paid_personas", [])

                # Build comprehensive response
                response_parts = []

                # Add owned personas section
                if owned_personas:
                    response_parts.append("## 🎯 **Your Owned Personas**")
                    response_parts.append("Click any persona below to start chatting with them:")
                    response_parts.append("")
                else:
                    response_parts.append("## 🎯 **Your Owned Personas**")
                    response_parts.append("You don't own any personas yet. Check out the free and paid options below!")
                    response_parts.append("")

                # Add free personas section
                if free_personas:
                    response_parts.append("## 🆓 **Free Personas Available**")
                    response_parts.append("These personas are free to use - click to start chatting:")
                    response_parts.append("")

                # Add paid personas section
                if paid_personas:
                    response_parts.append("## 💎 **Premium Personas Available**")
                    response_parts.append("Purchase these personas from the marketplace to unlock advanced capabilities:")
                    response_parts.append("")

                # Add summary
                total_owned = len(owned_personas)
                total_free = len(free_personas)
                total_paid = len(paid_personas)

                response_parts.append("---")
                response_parts.append(f"**Summary**: You own {total_owned} persona(s). {total_free} free and {total_paid} premium personas are available.")
                response_parts.append("")
                response_parts.append("💡 **Tip**: Tell me what you want to accomplish, and I'll recommend the best persona for your needs!")

                # Prepare personas for interactive UI
                interactive_personas = []

                # Add owned personas (can be selected immediately)
                for persona in owned_personas:
                    interactive_personas.append({
                        "id": persona.get("id", ""),
                        "name": persona.get("name", "Unknown"),
                        "description": persona.get("description", "AI assistant"),
                        "capabilities": persona.get("capabilities", []),
                        "rating": persona.get("rating", 0.0),
                        "review_count": persona.get("review_count", 0),
                        "price": 0.0,  # Owned personas are free to use
                        "is_owned": True,
                        "is_available": True,
                        "category": "owned"
                    })

                # Add free personas (can be selected immediately)
                for persona in free_personas:
                    interactive_personas.append({
                        "id": persona.get("id", ""),
                        "name": persona.get("name", "Unknown"),
                        "description": persona.get("description", "AI assistant"),
                        "capabilities": persona.get("capabilities", []),
                        "rating": persona.get("rating", 0.0),
                        "review_count": persona.get("review_count", 0),
                        "price": 0.0,
                        "is_owned": False,
                        "is_available": True,
                        "category": "free"
                    })

                # Add paid personas (need to be purchased first)
                for persona in paid_personas:
                    interactive_personas.append({
                        "id": persona.get("id", ""),
                        "name": persona.get("name", "Unknown"),
                        "description": persona.get("description", "AI assistant"),
                        "capabilities": persona.get("capabilities", []),
                        "rating": persona.get("rating", 0.0),
                        "review_count": persona.get("review_count", 0),
                        "price": persona.get("price", 0.0),
                        "is_owned": False,
                        "is_available": False,  # Need to purchase first
                        "category": "paid"
                    })

                return {
                    "message": "\n".join(response_parts),
                    "metadata": {
                        "is_persona_recommendation": True,
                        "recommended_personas": interactive_personas,
                        "persona_query_response": True,
                        "owned_count": total_owned,
                        "free_count": total_free,
                        "paid_count": total_paid
                    }
                }
            else:
                error_msg = tool_result.get('content', [{}])[0].get('text', 'Unknown error')
                logger.error(f"Error from marketplace tool: {error_msg}")
                return {
                    "message": self._get_fallback_persona_response(),
                    "metadata": {"is_persona_recommendation": False}
                }

        except Exception as e:
            logger.error(f"Error handling persona query: {e}")
            return {
                "message": self._get_fallback_persona_response(),
                "metadata": {"is_persona_recommendation": False}
            }

    def _get_fallback_persona_response(self) -> str:
        """Fallback response when marketplace tool fails."""
        return """I'm having trouble accessing the persona marketplace right now.

Here's what I can tell you:
- Datagenius offers various AI personas for different tasks
- Each persona specializes in specific areas like data analysis, content creation, or technical assistance
- Some personas are free, while others are premium offerings

Please try asking again in a moment, or let me know what specific task you need help with and I'll do my best to guide you!"""

    async def generate_persona_recommendation(
        self,
        intent: UserIntent,
        context: ConversationContext,
        user_id: str = None,
        user_requirements: str = ""
    ) -> Dict[str, Any]:
        """
        Generate a detailed persona recommendation using real marketplace data.
        Returns both message and metadata for interactive UI rendering.
        """
        try:
            # Use the PersonaMarketplaceTool to get real recommendations
            from ..tools.mcp.persona_marketplace_tool import PersonaMarketplaceTool

            marketplace_tool = PersonaMarketplaceTool()
            await marketplace_tool._initialize({})

            # Get recommendations based on user requirements and intent
            tool_result = await marketplace_tool.execute({
                "user_id": user_id or "1",
                "query_type": "recommendations",
                "intent_type": intent.intent_type,
                "user_requirements": user_requirements
            })

            if not tool_result.get("isError", False):
                metadata = tool_result.get("metadata", {})
                data = metadata.get("data", {})

                owned_recommendations = data.get("owned_recommendations", [])
                purchase_recommendations = data.get("purchase_recommendations", [])

                # Build response message
                response_parts = []

                # Add confidence-based intro
                if intent.confidence > 0.8:
                    response_parts.append("Based on your request, I found the perfect personas for you:")
                elif intent.confidence > 0.6:
                    response_parts.append("I have some great recommendations based on your needs:")
                else:
                    response_parts.append("Here are some personas that might help with your request:")

                response_parts.append("")

                # Add owned personas section
                if owned_recommendations:
                    response_parts.append("## 🎯 **Recommended Personas You Own**")
                    response_parts.append("These personas are perfect for your request - click to start using them:")
                    response_parts.append("")

                # Add available personas section
                if purchase_recommendations:
                    if owned_recommendations:
                        response_parts.append("## 🛒 **Additional Recommended Personas**")
                        response_parts.append("These personas would also be great for your needs:")
                    else:
                        response_parts.append("## 🛒 **Recommended Personas**")
                        response_parts.append("These personas are perfect for your request:")
                    response_parts.append("")

                # Add guidance
                if owned_recommendations or purchase_recommendations:
                    response_parts.append("---")
                    response_parts.append("💡 **Tip**: Click any persona above to start working with them, or tell me more about your specific needs for a more targeted recommendation!")
                else:
                    response_parts.append("I don't have specific persona recommendations for your request right now.")
                    response_parts.append("Could you provide more details about what you're trying to accomplish?")
                    response_parts.append("For example, are you looking to analyze data, create content, or organize information?")

                # Prepare personas for interactive UI
                interactive_personas = []

                # Add owned personas (can be selected immediately)
                for persona in owned_recommendations:
                    interactive_personas.append({
                        "id": persona.get("id", ""),
                        "name": persona.get("name", "Unknown"),
                        "description": persona.get("description", "AI assistant"),
                        "capabilities": persona.get("capabilities", []),
                        "rating": persona.get("rating", 0.0),
                        "review_count": persona.get("review_count", 0),
                        "price": 0.0,  # Owned personas are free to use
                        "is_owned": True,
                        "is_available": True,
                        "category": "owned"
                    })

                # Add available personas (free and paid)
                for persona in purchase_recommendations:
                    price = persona.get("price", 0.0)
                    interactive_personas.append({
                        "id": persona.get("id", ""),
                        "name": persona.get("name", "Unknown"),
                        "description": persona.get("description", "AI assistant"),
                        "capabilities": persona.get("capabilities", []),
                        "rating": persona.get("rating", 0.0),
                        "review_count": persona.get("review_count", 0),
                        "price": price,
                        "is_owned": False,
                        "is_available": price == 0.0,  # Free personas are available, paid need purchase
                        "category": "free" if price == 0.0 else "paid"
                    })

                return {
                    "message": "\n".join(response_parts),
                    "metadata": {
                        "is_persona_recommendation": True,
                        "recommended_personas": interactive_personas,
                        "persona_query_response": True,  # This enables the proper frontend rendering
                        "recommendation_type": "targeted",
                        "owned_count": len(owned_recommendations),
                        "available_count": len(purchase_recommendations),
                        "intent_type": intent.intent_type,
                        "confidence": intent.confidence
                    }
                }
            else:
                error_msg = tool_result.get('content', [{}])[0].get('text', 'Unknown error')
                logger.error(f"Error from marketplace tool: {error_msg}")
                return self._get_fallback_recommendation_response(intent)

        except Exception as e:
            logger.error(f"Error generating persona recommendation: {e}")
            return self._get_fallback_recommendation_response(intent)

    def _get_fallback_recommendation_response(self, intent: UserIntent) -> Dict[str, Any]:
        """Fallback response when marketplace tool fails for recommendations."""
        fallback_message = f"""I'm having trouble accessing the persona marketplace right now to give you specific recommendations.

However, based on your request, here are some general suggestions:

{self._generate_general_guidance(intent)}

Please try asking again in a moment, or visit the marketplace directly to browse available personas."""

        return {
            "message": fallback_message,
            "metadata": {
                "is_persona_recommendation": False,
                "recommended_personas": [],
                "persona_query_response": False,  # Fallback doesn't have interactive personas
                "recommendation_type": "fallback"
            }
        }

    async def _get_persona_info(self, persona_id: str) -> Optional[Dict[str, Any]]:
        """Get persona information dynamically from the registry."""
        try:
            # First try to get configuration from the registry
            config = self.agent_registry.get_configuration(persona_id)
            if config:
                return {
                    "name": config.get("name", persona_id.replace("-", " ").title()),
                    "description": config.get("description", "AI assistant for specialized tasks.")
                }

            # Fallback: try to get agent class and extract info
            agent_class = self.agent_registry.get_agent_class(persona_id)
            if agent_class:
                # Extract name from class name
                class_name = agent_class.__name__
                name = class_name.replace("Agent", "").replace("AI", " AI")

                # Use docstring as description if available
                description = agent_class.__doc__ or "AI assistant for specialized tasks."

                return {
                    "name": name,
                    "description": description.strip()
                }

            # Final fallback: generate basic info from persona_id
            return {
                "name": persona_id.replace("-", " ").title(),
                "description": f"AI assistant specialized in {persona_id.replace('-', ' ')} tasks."
            }

        except Exception as e:
            logger.error(f"Error fetching persona info for {persona_id}: {e}")
            return None

    async def _generate_conversational_response(
        self,
        message: str,
        intent: UserIntent,
        conversation_context: ConversationContext,
        user_context: Dict[str, Any]
    ) -> str:
        """Generate a conversational response using the conversation tool for proper AI assistant responses."""
        try:
            # Convert conversation context to the format expected by the conversation tool
            # Use both in-memory context and database context for complete history
            conversation_history = []

            # First, add database conversation history if available
            database_history = user_context.get("conversation_history", [])
            for db_msg in database_history:
                conversation_history.append({
                    "sender": db_msg.get("sender", "unknown"),
                    "content": db_msg.get("content", ""),
                    "timestamp": db_msg.get("timestamp", "")
                })

            # Then add in-memory conversation context (more recent interactions)
            if conversation_context.conversation_history:
                for entry in conversation_context.conversation_history:
                    # Add user message
                    if entry.get('user_message'):
                        conversation_history.append({
                            "sender": "user",
                            "content": entry.get('user_message', ''),
                            "timestamp": entry.get('timestamp', '')
                        })
                    # Add assistant response
                    if entry.get('agent_response'):
                        conversation_history.append({
                            "sender": "assistant",
                            "content": entry.get('agent_response', ''),
                            "timestamp": entry.get('timestamp', '')
                        })

            # Remove duplicates and sort by timestamp
            seen_messages = set()
            unique_history = []
            for msg in conversation_history:
                msg_key = f"{msg['sender']}:{msg['content'][:50]}:{msg['timestamp']}"
                if msg_key not in seen_messages:
                    seen_messages.add(msg_key)
                    unique_history.append(msg)

            # Sort by timestamp to maintain chronological order
            unique_history.sort(key=lambda x: x.get('timestamp', ''))

            # Keep only recent messages for context (last 30 messages)
            conversation_history = unique_history[-30:] if len(unique_history) > 30 else unique_history

            # Check if this is a continuing conversation
            is_continuing_conversation = len(conversation_history) > 0

            logger.info(f"Conversation tool will use {len(conversation_history)} messages for context, continuing: {is_continuing_conversation}")

            # Use the conversation tool for proper AI assistant responses
            from ..tools.mcp import AVAILABLE_TOOLS
            conversation_tool_class = AVAILABLE_TOOLS.get("ConversationTool")

            if conversation_tool_class:
                conversation_tool = conversation_tool_class()
                await conversation_tool._initialize({})

                # Create agent context using the dynamic system
                from agents.utils import create_agent_context

                enhanced_user_context = create_agent_context(
                    agent_id="concierge-agent",
                    additional_context=user_context
                )

                tool_result = await conversation_tool.execute({
                    "message": message,
                    "conversation_history": conversation_history,
                    "user_context": enhanced_user_context,
                    "intent_type": intent.intent_type,
                    "confidence": intent.confidence,
                    "is_continuing_conversation": is_continuing_conversation,
                    "provider": self.analysis_config.provider,
                    "model": self.analysis_config.model,
                    "temperature": 0.7
                })

                if not tool_result.get("isError", False):
                    content_items = tool_result.get("content", [])
                    response_text = "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"])
                    if response_text.strip():
                        logger.info(f"Generated conversational response using conversation tool: {response_text[:100]}...")
                        return response_text.strip()
                else:
                    logger.error(f"Conversation tool returned error: {tool_result}")

            # Fallback to natural static responses if tool fails
            logger.warning("Conversation tool failed, using fallback response")
            return self._get_natural_fallback_response(intent, message)

        except Exception as e:
            logger.error(f"Error generating conversational response: {e}")
            # Fallback to more natural static responses
            return self._get_natural_fallback_response(intent, message)

    def _get_natural_fallback_response(self, intent: UserIntent, message: str) -> str:
        """Generate more natural fallback responses when LLM fails."""
        # Analyze message for basic patterns to provide more contextual responses
        message_lower = message.lower()

        # Try to provide helpful responses to common question patterns
        if any(word in message_lower for word in ["cat", "cats", "animal", "animals", "pet", "pets"]):
            return "Cats are fascinating creatures! They're independent, intelligent, and have been companions to humans for thousands of years. Did you know cats have excellent night vision and can rotate their ears 180 degrees? If you're interested in learning more about animals or need help with research on any topic, I can connect you with specialized personas that could dive deeper into the subject!"

        elif any(word in message_lower for word in ["help", "assist", "support"]):
            return "I'd be happy to help! What are you looking to accomplish today? Whether it's analyzing data, creating content, answering questions, or something else entirely, I can either help directly or guide you to the right specialized persona."

        elif any(word in message_lower for word in ["what", "how", "can you", "do you", "tell me about"]):
            return "Great question! I'm here to help in multiple ways - I can answer questions, provide information, and guide you to specialized AI personas for deeper expertise. What would you like to know about or work on?"

        elif any(word in message_lower for word in ["data", "file", "csv", "excel"]):
            return "It sounds like you might be working with data! I can help you find the right AI persona to analyze, visualize, or process your data. What would you like to do with it?"

        elif any(word in message_lower for word in ["marketing", "content", "campaign"]):
            return "Interesting! Are you looking to create marketing content or develop campaigns? I can connect you with our marketing specialist who excels at that kind of work."

        else:
            return "I'm here to help you in whatever way I can! I can answer questions, provide information, or guide you to specialized AI personas for deeper expertise. What would you like to know about or work on today?"

    def _generate_general_guidance(self, intent: UserIntent) -> str:
        """Generate general guidance when no specific personas are recommended."""
        # This method is now deprecated in favor of conversational responses
        # Keeping for backward compatibility but should not be used for general_question intents
        guidance_map = {
            "persona_request": "I'd be happy to recommend a persona! Could you tell me more about what you're trying to accomplish? For example:\n• Analyze data (CSV, Excel files)\n• Create marketing content\n• Classify or organize information",
            "data_help": "Great! I can help you with data. Here's what you can do:\n• Upload CSV or Excel files for analysis\n• Get insights and visualizations\n• Process documents (PDF, Word)\n\nWhat type of data are you working with?"
        }

        return guidance_map.get(intent.intent_type, "I'm here to help! What can I assist you with today?")

    async def generate_data_attachment_guidance(self, file_type: Optional[str] = None) -> str:
        """Generate guidance for data attachment."""
        base_message = "📎 **Data Attachment Help**\n\n"

        if file_type:
            file_guidance = {
                "csv": "CSV files are perfect for the Composable Analyst! You'll get detailed analysis, visualizations, and statistical insights.",
                "excel": "Excel files work great with our data analysis personas. They can handle multiple sheets and complex data structures.",
                "pdf": "PDF files can be processed for text extraction and analysis. Great for document classification and content analysis."
            }
            base_message += file_guidance.get(file_type, "This file type is supported by our data processing personas.")
        else:
            base_message += "You can attach various file types:\n• **CSV/Excel**: For data analysis and visualization\n• **PDF/Word**: For document processing and text analysis\n• **JSON/XML**: For structured data analysis"

        base_message += "\n\n🎯 **Pro Tip**: Upload your file first, then tell me what you want to discover about your data!"

        return base_message

    async def handle_persona_handoff(
        self,
        target_persona: str,
        user_message: str,
        context: ConversationContext
    ) -> Dict[str, Any]:
        """Handle handoff to a specific persona with context preservation."""
        try:
            # Prepare handoff context
            handoff_context = {
                "original_message": user_message,
                "user_intent": context.current_task,
                "conversation_history": context.conversation_history[-3:],  # Last 3 interactions
                "attached_data": context.attached_data,
                "user_preferences": context.user_preferences,
                "handoff_reason": f"User request routed from Concierge Agent",
                "timestamp": datetime.now().isoformat()
            }

            # Create agent instance
            agent_instance = await self.agent_registry.create_agent_instance(target_persona)

            if not agent_instance:
                return {
                    "success": False,
                    "message": f"Sorry, I couldn't connect you to the {target_persona} persona. Please try again.",
                    "error": "agent_instantiation_failed"
                }

            # Prepare handoff message
            handoff_message = f"Hello! The Concierge has connected you with me to help with: {user_message}"

            return {
                "success": True,
                "agent_instance": agent_instance,
                "handoff_context": handoff_context,
                "handoff_message": handoff_message,
                "target_persona": target_persona
            }

        except Exception as e:
            logger.error(f"Error in persona handoff to {target_persona}: {e}")
            return {
                "success": False,
                "message": "I encountered an issue connecting you to that persona. Please try again.",
                "error": str(e)
            }

    async def cleanup_old_contexts(self, max_age_hours: int = 24) -> None:
        """Clean up old conversation contexts to manage memory."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        contexts_to_remove = [
            key for key, context in self.conversation_contexts.items()
            if context.last_interaction < cutoff_time
        ]

        for key in contexts_to_remove:
            del self.conversation_contexts[key]

        if contexts_to_remove:
            logger.info(f"Cleaned up {len(contexts_to_remove)} old conversation contexts")

    async def _process_message(
        self,
        user_id: int,
        message: str,
        conversation_id: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Override the component-based processing to use concierge-specific logic.

        This ensures the concierge agent always uses its own response generation
        instead of relying on potentially problematic LLM components.

        Note: This method signature matches what the streaming mixin expects:
        _process_message(user_id: int, message: str, conversation_id: str, context: Dict)
        """
        logger.info(f"ConciergeAgent._process_message called - bypassing component processing")

        # Call the main process_message method with correct parameter order
        return await self.process_message(
            message=message,
            user_id=str(user_id),
            conversation_id=conversation_id,
            context=context
        )

    async def _perform_cross_modal_search(self, query: str, user_id: str,
                                        conversation_id: str) -> Dict[str, Any]:
        """
        Perform cross-modal search across user's data using enhanced data access tool.

        Args:
            query: Search query
            user_id: User ID
            conversation_id: Conversation ID

        Returns:
            Search results from multiple data sources
        """
        try:
            # Get user's files from database
            from app.database import get_db, get_user_files
            from sqlalchemy.orm import Session

            db_gen = get_db()
            db: Session = next(db_gen)
            try:
                user_files = get_user_files(db, int(user_id))
                if not user_files:
                    return {
                        "success": False,
                        "message": "No files found for cross-modal search",
                        "results": []
                    }

                # Prepare data sources for cross-modal query
                data_sources = []
                for file_record in user_files[:10]:  # Limit to 10 most recent files
                    if file_record.file_path and os.path.exists(file_record.file_path):
                        data_sources.append({
                            "id": str(file_record.id),
                            "name": file_record.filename,
                            "file_path": file_record.file_path
                        })

                if not data_sources:
                    return {
                        "success": False,
                        "message": "No accessible files found for search",
                        "results": []
                    }

                # Use enhanced data access tool for cross-modal query
                from agents.tools.mcp.data_access import DataAccessTool
                data_tool = DataAccessTool()

                result = await data_tool.execute({
                    "operation": "cross_modal_query",
                    "data_source": data_sources,
                    "params": {
                        "query": query,
                        "limit": 5,
                        "user_id": user_id,
                        "conversation_id": conversation_id
                    }
                })

                if not result.get("isError", False):
                    metadata = result.get("metadata", {})
                    return {
                        "success": True,
                        "message": f"Found results across {len(data_sources)} data sources",
                        "results": metadata,
                        "sources_searched": len(data_sources)
                    }
                else:
                    return {
                        "success": False,
                        "message": "Error performing cross-modal search",
                        "error": result.get("content", [{}])[0].get("text", "Unknown error")
                    }

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error in cross-modal search: {e}")
            return {
                "success": False,
                "message": "Error performing search across your data",
                "error": str(e)
            }

    async def _build_user_knowledge_graph(self, user_id: str,
                                        conversation_id: str) -> Dict[str, Any]:
        """
        Build a knowledge graph from user's data using enhanced data access tool.

        Args:
            user_id: User ID
            conversation_id: Conversation ID

        Returns:
            Knowledge graph information
        """
        try:
            # Get user's files from database
            from app.database import get_db, get_user_files
            from sqlalchemy.orm import Session

            db_gen = get_db()
            db: Session = next(db_gen)
            try:
                user_files = get_user_files(db, int(user_id))
                if not user_files:
                    return {
                        "success": False,
                        "message": "No files found to build knowledge graph"
                    }

                # Prepare data sources
                data_sources = []
                for file_record in user_files[:5]:  # Limit to 5 files for knowledge graph
                    if file_record.file_path and os.path.exists(file_record.file_path):
                        data_sources.append(file_record.file_path)

                if not data_sources:
                    return {
                        "success": False,
                        "message": "No accessible files found for knowledge graph"
                    }

                # Use enhanced data access tool to build knowledge graph
                from agents.tools.mcp.data_access import DataAccessTool
                data_tool = DataAccessTool()

                result = await data_tool.execute({
                    "operation": "build_knowledge_graph",
                    "data_source": data_sources,
                    "params": {
                        "graph_name": f"user_{user_id}_knowledge_graph",
                        "user_id": user_id,
                        "conversation_id": conversation_id
                    }
                })

                if not result.get("isError", False):
                    metadata = result.get("metadata", {})
                    return {
                        "success": True,
                        "message": f"Built knowledge graph from {len(data_sources)} sources",
                        "graph_info": metadata,
                        "sources_processed": len(data_sources)
                    }
                else:
                    return {
                        "success": False,
                        "message": "Error building knowledge graph",
                        "error": result.get("content", [{}])[0].get("text", "Unknown error")
                    }

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error building knowledge graph: {e}")
            return {
                "success": False,
                "message": "Error building knowledge graph from your data",
                "error": str(e)
            }

    async def process_message(
        self,
        message: str,
        user_id: str, # Changed from user_context
        conversation_id: str, # Changed from session_id
        context: Dict[str, Any] # This is the new user_context
    ) -> Dict[str, Any]:
        """
        Main message processing method for the Concierge Agent.

        Args:
            message: User's message
            user_id: The ID of the user.
            conversation_id: The ID of the conversation (used as session_id).
            context: Additional context from the orchestrator (replaces old user_context).

        Returns:
            Response dictionary with message and metadata
        """
        logger.info(f"ConciergeAgent.process_message received message: '{message[:50]}...', user_id: {user_id}, conversation_id: {conversation_id}, context keys: {list(context.keys()) if context else None}")
        try:
            # Get conversation context using user_id and conversation_id (as session_id)
            # Ensure user_id is converted to string for Pydantic validation
            user_id_str = str(user_id) if user_id is not None else "anonymous"
            conversation_state_context = await self.get_conversation_context(
                user_id_str,
                conversation_id,
                database_context=context  # Pass the database context for history merging
            )

            # Check if this is a continuing conversation by examining conversation history
            database_conversation_history = context.get("conversation_history", [])
            has_conversation_history = len(database_conversation_history) > 0

            # Also check in-memory conversation context
            has_in_memory_history = len(conversation_state_context.conversation_history) > 0

            # Determine if this is a new conversation or continuation
            is_continuing_conversation = has_conversation_history or has_in_memory_history

            logger.info(f"Conversation analysis - DB history: {len(database_conversation_history)}, Memory history: {len(conversation_state_context.conversation_history)}, Is continuing: {is_continuing_conversation}")

            # Check for conversation initiation first (before intent parsing)
            if (context.get("task_type") == "initiate" or
                (message.strip() == "" and not is_continuing_conversation) or
                (message.lower().strip() in ["hello", "hi", "hey", "start"] and not is_continuing_conversation) or
                context.get("is_initial_greeting", False)) and not is_continuing_conversation:
                # This is a new conversation initiation
                logger.info("Generating welcome message for new conversation initiation")
                response_message = await self.generate_welcome_message(context)
                # Create a basic intent for metadata
                intent = UserIntent(
                    intent_type="greeting",
                    confidence=1.0,
                    suggested_personas=[],
                    requires_data=False,
                    complexity_score=0.1,
                    entities={}
                )
            else:
                # Check for enhanced data-related queries first
                data_keywords = ["search my data", "find in my files", "analyze my documents", "what's in my data",
                               "search across", "find information", "look through my files"]
                knowledge_keywords = ["build knowledge graph", "create knowledge map", "connect my data",
                                    "relationships in my data", "entities in my files"]

                if any(keyword in message.lower() for keyword in data_keywords):
                    logger.info("Detected data search query, attempting enhanced cross-modal search")
                    try:
                        search_results = await self._perform_cross_modal_search(message, user_id, conversation_id)
                        if search_results.get("success"):
                            # Include enhanced search results in the response
                            response_parts = [
                                f"🔍 I searched across your data and found relevant information:",
                                ""
                            ]

                            results = search_results.get("results", {})
                            structured_results = results.get("structured_results", [])
                            unstructured_results = results.get("unstructured_results", [])

                            if structured_results:
                                total_matches = sum(r.get("match_count", 0) for r in structured_results)
                                response_parts.append(f"📊 **Structured Data**: {total_matches} matches across {len(structured_results)} datasets")

                                # Show sample matches
                                for result in structured_results[:2]:
                                    source_name = os.path.basename(result.get("source", "Unknown"))
                                    match_count = result.get("match_count", 0)
                                    response_parts.append(f"   • {source_name}: {match_count} relevant records")

                            if unstructured_results:
                                total_matches = sum(r.get("match_count", 0) for r in unstructured_results)
                                response_parts.append(f"📄 **Documents**: {total_matches} matches across {len(unstructured_results)} documents")

                                # Show sample matches
                                for result in unstructured_results[:2]:
                                    source_name = os.path.basename(result.get("source", "Unknown"))
                                    match_count = result.get("match_count", 0)
                                    response_parts.append(f"   • {source_name}: {match_count} relevant sections")

                            response_parts.extend([
                                "",
                                "🤖 **Next Steps:**",
                                "• Ask me to analyze specific findings in detail",
                                "• Request a specialized agent for deeper analysis",
                                "• Build a knowledge graph to see connections",
                                "",
                                "What would you like to explore further?"
                            ])

                            response_message = "\n".join(response_parts)

                            # Create intent for metadata
                            intent = UserIntent(
                                intent_type="data_search",
                                confidence=0.9,
                                suggested_personas=["analysis_agent"],
                                requires_data=True,
                                complexity_score=0.7,
                                entities={}
                            )
                        else:
                            # Search failed, continue with normal processing
                            enhanced_user_context = context.copy()
                            enhanced_user_context["is_continuing_conversation"] = is_continuing_conversation
                            enhanced_user_context["conversation_state"] = conversation_state_context
                            intent = await self.parse_user_intent(message, enhanced_user_context)

                    except Exception as e:
                        logger.error(f"Error in enhanced cross-modal search: {e}")
                        # Continue with normal processing
                        enhanced_user_context = context.copy()
                        enhanced_user_context["is_continuing_conversation"] = is_continuing_conversation
                        enhanced_user_context["conversation_state"] = conversation_state_context
                        intent = await self.parse_user_intent(message, enhanced_user_context)

                elif any(keyword in message.lower() for keyword in knowledge_keywords):
                    logger.info("Detected knowledge graph request, attempting to build user knowledge graph")
                    try:
                        kg_results = await self._build_user_knowledge_graph(user_id, conversation_id)
                        if kg_results.get("success"):
                            graph_info = kg_results.get("graph_info", {})
                            entities_count = graph_info.get("entities_count", 0)
                            sources_count = kg_results.get("sources_processed", 0)

                            response_parts = [
                                f"🧠 I've built a knowledge graph from your data!",
                                "",
                                f"📊 **Graph Statistics:**",
                                f"   • {entities_count} entities extracted",
                                f"   • {sources_count} data sources processed",
                                f"   • Graph ID: {graph_info.get('graph_name', 'Unknown')}",
                                "",
                                "🔍 **What you can do now:**",
                                "• Query the knowledge graph for specific entities",
                                "• Explore relationships between different data points",
                                "• Use the graph for enhanced data analysis",
                                "",
                                "Would you like me to help you explore the knowledge graph or connect you with a specialized agent?"
                            ]

                            response_message = "\n".join(response_parts)

                            # Create intent for metadata
                            intent = UserIntent(
                                intent_type="knowledge_graph",
                                confidence=0.9,
                                suggested_personas=["analysis_agent"],
                                requires_data=True,
                                complexity_score=0.8,
                                entities={}
                            )
                        else:
                            # Knowledge graph building failed, continue with normal processing
                            enhanced_user_context = context.copy()
                            enhanced_user_context["is_continuing_conversation"] = is_continuing_conversation
                            enhanced_user_context["conversation_state"] = conversation_state_context
                            intent = await self.parse_user_intent(message, enhanced_user_context)

                    except Exception as e:
                        logger.error(f"Error building knowledge graph: {e}")
                        # Continue with normal processing
                        enhanced_user_context = context.copy()
                        enhanced_user_context["is_continuing_conversation"] = is_continuing_conversation
                        enhanced_user_context["conversation_state"] = conversation_state_context
                        intent = await self.parse_user_intent(message, enhanced_user_context)
                else:
                    # Parse user intent using LLM-based analysis (no hardcoded keywords)
                    # Enhanced to include conversation history awareness
                    enhanced_user_context = context.copy()
                    enhanced_user_context["is_continuing_conversation"] = is_continuing_conversation
                    enhanced_user_context["conversation_state"] = conversation_state_context
                    intent = await self.parse_user_intent(message, enhanced_user_context)

                # Generate appropriate response based on intent and conversation state
                # Handle persona queries and requests using marketplace tool
                if intent.intent_type == "persona_query":
                    logger.info("LLM detected persona query - using marketplace tool")
                    persona_query_result = await self.handle_persona_query(message, user_id_str)
                    response_message = persona_query_result["message"]
                    persona_metadata = persona_query_result["metadata"]
                elif message.lower().strip() in ["hello", "hi", "hey", "start"] and is_continuing_conversation:
                    # For continuing conversations, provide a contextual greeting
                    logger.info("Generating contextual greeting for continuing conversation")
                    response_message = await self.generate_contextual_greeting(message, conversation_state_context, context)
                elif is_continuing_conversation and intent.intent_type == "general_question":
                    # For continuing conversations, use conversational responses for general questions only
                    logger.info(f"Generating conversational response for continuing conversation (intent: {intent.intent_type})")
                    response_message = await self._generate_conversational_response(message, intent, conversation_state_context, context)
                elif intent.intent_type == "persona_request":
                    # Only use marketplace recommendations for explicit persona requests
                    logger.info("LLM detected explicit persona request - generating recommendation using marketplace tool")
                    recommendation_result = await self.generate_persona_recommendation(
                        intent,
                        conversation_state_context,
                        user_id=user_id_str,
                        user_requirements=message
                    )
                    response_message = recommendation_result["message"]
                    recommendation_metadata = recommendation_result["metadata"]

                elif intent.intent_type == "general_question":
                    # Use conversational response for general questions
                    logger.info("Generating conversational response for general question")
                    response_message = await self._generate_conversational_response(message, intent, conversation_state_context, context)
                else:
                    # For other specific intent types, use conversational responses if continuing, otherwise guidance
                    if is_continuing_conversation:
                        logger.info(f"Generating conversational response for continuing conversation (fallback, intent: {intent.intent_type})")
                        response_message = await self._generate_conversational_response(message, intent, conversation_state_context, context)
                    else:
                        response_message = self._generate_general_guidance(intent)

            # Update conversation context
            await self.update_conversation_context(conversation_state_context, message, response_message, intent)

            # Prepare response metadata
            response_metadata = {
                "intent": intent.intent_type,
                "confidence": intent.confidence,
                "suggested_personas": intent.suggested_personas,
                "requires_data": intent.requires_data,
                "complexity_score": intent.complexity_score,
                "entities": intent.entities,
                "is_continuing_conversation": is_continuing_conversation
            }

            # Add persona metadata if this was a persona query
            if intent.intent_type == "persona_query" and 'persona_metadata' in locals():
                response_metadata.update(persona_metadata)

            # Add recommendation metadata if this was a persona recommendation
            if 'recommendation_metadata' in locals():
                response_metadata.update(recommendation_metadata)

            # Clean up old contexts periodically
            if len(self.conversation_contexts) > 100:
                await self.cleanup_old_contexts()

            return {
                "message": response_message,
                "metadata": response_metadata,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error processing message in Concierge Agent: {e}")
            return {
                "message": "I apologize, but I encountered an issue processing your request. Please try again.",
                "metadata": {"error": str(e)},
                "success": False
            }


