"""
Enhanced configuration management for marketing agent.

This module provides comprehensive configuration management with validation,
environment-specific settings, and dynamic configuration updates.
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum

logger = logging.getLogger(__name__)


class AIProvider(str, Enum):
    """Supported AI providers."""
    GROQ = "groq"
    OPENAI = "openai"
    OPENROUTER = "openrouter"
    REQUESTY = "requesty"
    OLLAMA = "ollama"
    GOOGLE = "google"


class Environment(str, Enum):
    """Environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class CacheConfig(BaseModel):
    """Cache configuration."""
    enabled: bool = Field(default=True, description="Enable caching")
    ttl: int = Field(default=3600, description="Cache TTL in seconds")
    max_size: int = Field(default=1000, description="Maximum cache size")
    backend: str = Field(default="memory", description="Cache backend (memory, redis)")
    redis_url: Optional[str] = Field(default=None, description="Redis URL for cache backend")


class ValidationConfig(BaseModel):
    """Validation configuration."""
    enabled: bool = Field(default=True, description="Enable input validation")
    strict_mode: bool = Field(default=False, description="Enable strict validation mode")
    min_description_length: int = Field(default=10, description="Minimum description length")
    max_description_length: int = Field(default=5000, description="Maximum description length")
    allowed_file_types: List[str] = Field(
        default=["pdf", "doc", "docx", "txt", "csv", "xlsx"],
        description="Allowed file types for uploads"
    )


class PerformanceConfig(BaseModel):
    """Performance configuration."""
    request_timeout: int = Field(default=30, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    retry_delay: float = Field(default=1.0, description="Delay between retries in seconds")
    max_concurrent_requests: int = Field(default=10, description="Maximum concurrent requests")
    enable_monitoring: bool = Field(default=True, description="Enable performance monitoring")


class SecurityConfig(BaseModel):
    """Security configuration."""
    enable_input_sanitization: bool = Field(default=True, description="Enable input sanitization")
    max_upload_size: int = Field(default=10485760, description="Maximum upload size in bytes (10MB)")
    allowed_origins: List[str] = Field(default=["*"], description="Allowed CORS origins")
    rate_limit_requests: int = Field(default=100, description="Rate limit requests per minute")
    enable_content_filtering: bool = Field(default=True, description="Enable content filtering")


class AIProviderConfig(BaseModel):
    """AI provider configuration."""
    provider: AIProvider = Field(default=AIProvider.GROQ, description="AI provider to use")
    model: str = Field(default="llama3-70b-8192", description="AI model to use")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Temperature for AI responses")
    max_tokens: int = Field(default=4000, ge=1, le=32000, description="Maximum tokens for AI responses")
    api_key: Optional[str] = Field(default=None, description="API key for the provider")
    base_url: Optional[str] = Field(default=None, description="Base URL for the provider")
    
    @field_validator('temperature')
    @classmethod
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('Temperature must be between 0.0 and 2.0')
        return v


class MarketingAgentConfig(BaseModel):
    """Comprehensive configuration model for marketing agent."""
    
    # Environment settings
    environment: Environment = Field(default=Environment.DEVELOPMENT, description="Current environment")
    debug: bool = Field(default=False, description="Enable debug mode")
    log_level: str = Field(default="INFO", description="Logging level")
    
    # AI Provider settings
    ai_provider: AIProviderConfig = Field(default_factory=AIProviderConfig)
    
    # Agent behavior settings
    max_context_length: int = Field(default=10, ge=1, le=50, description="Maximum conversation context length")
    default_tone: str = Field(default="professional", description="Default tone for content")
    enable_follow_up_detection: bool = Field(default=True, description="Enable follow-up question detection")
    conversation_timeout: int = Field(default=1800, description="Conversation timeout in seconds")
    
    # Component configurations
    cache: CacheConfig = Field(default_factory=CacheConfig)
    validation: ValidationConfig = Field(default_factory=ValidationConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    # Feature flags
    enable_analytics: bool = Field(default=True, description="Enable usage analytics")
    enable_feedback_collection: bool = Field(default=True, description="Enable feedback collection")
    enable_experimental_features: bool = Field(default=False, description="Enable experimental features")
    
    # Integration settings
    database_url: Optional[str] = Field(default=None, description="Database URL")
    redis_url: Optional[str] = Field(default=None, description="Redis URL")
    vector_db_url: Optional[str] = Field(default=None, description="Vector database URL")
    
    @model_validator(mode='after')
    def validate_config(self):
        """Validate configuration consistency."""
        # Validate cache backend
        if self.cache.backend == 'redis':
            if not self.cache.redis_url and not self.redis_url:
                raise ValueError('Redis URL required when using Redis cache backend')

        # Validate AI provider
        if self.ai_provider.provider and not self.ai_provider.api_key:
            # Check environment variables for API key
            env_key = f"{self.ai_provider.provider.upper()}_API_KEY"
            if not os.getenv(env_key):
                logger.warning(f"No API key found for provider {self.ai_provider.provider}")

        return self
    
    class Config:
        env_prefix = "MARKETING_AGENT_"
        env_nested_delimiter = "__"


class ConfigManager:
    """Configuration manager with dynamic loading and validation."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path
        self._config: Optional[MarketingAgentConfig] = None
        self._config_file_mtime: Optional[float] = None
    
    def load_config(self, force_reload: bool = False) -> MarketingAgentConfig:
        """
        Load configuration from file and environment variables.
        
        Args:
            force_reload: Force reload even if config hasn't changed
            
        Returns:
            MarketingAgentConfig instance
        """
        if self._config and not force_reload and not self._config_changed():
            return self._config
        
        # Load from file if specified
        file_config = {}
        if self.config_path and Path(self.config_path).exists():
            try:
                with open(self.config_path, 'r') as f:
                    file_config = json.load(f)
                self._config_file_mtime = Path(self.config_path).stat().st_mtime
                logger.info(f"Loaded configuration from {self.config_path}")
            except Exception as e:
                logger.error(f"Failed to load config file {self.config_path}: {e}")
        
        # Merge with environment variables
        env_config = self._load_from_env()
        merged_config = {**file_config, **env_config}
        
        # Create and validate config
        try:
            self._config = MarketingAgentConfig(**merged_config)
            logger.info("Configuration loaded and validated successfully")
            return self._config
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            # Return default config as fallback
            self._config = MarketingAgentConfig()
            return self._config
    
    def _config_changed(self) -> bool:
        """Check if config file has changed."""
        if not self.config_path or not Path(self.config_path).exists():
            return False
        
        current_mtime = Path(self.config_path).stat().st_mtime
        return current_mtime != self._config_file_mtime
    
    def _load_from_env(self) -> Dict[str, Any]:
        """Load configuration from environment variables."""
        env_config = {}
        
        # AI Provider settings
        if os.getenv('MARKETING_AGENT_AI_PROVIDER__PROVIDER'):
            env_config['ai_provider'] = {
                'provider': os.getenv('MARKETING_AGENT_AI_PROVIDER__PROVIDER'),
                'model': os.getenv('MARKETING_AGENT_AI_PROVIDER__MODEL', 'llama3-70b-8192'),
                'temperature': float(os.getenv('MARKETING_AGENT_AI_PROVIDER__TEMPERATURE', '0.7')),
                'max_tokens': int(os.getenv('MARKETING_AGENT_AI_PROVIDER__MAX_TOKENS', '4000')),
                'api_key': os.getenv('MARKETING_AGENT_AI_PROVIDER__API_KEY'),
                'base_url': os.getenv('MARKETING_AGENT_AI_PROVIDER__BASE_URL')
            }
        
        # Environment settings
        if os.getenv('MARKETING_AGENT_ENVIRONMENT'):
            env_config['environment'] = os.getenv('MARKETING_AGENT_ENVIRONMENT')
        
        if os.getenv('MARKETING_AGENT_DEBUG'):
            env_config['debug'] = os.getenv('MARKETING_AGENT_DEBUG').lower() == 'true'
        
        # Database URLs
        if os.getenv('MARKETING_AGENT_DATABASE_URL'):
            env_config['database_url'] = os.getenv('MARKETING_AGENT_DATABASE_URL')
        
        if os.getenv('MARKETING_AGENT_REDIS_URL'):
            env_config['redis_url'] = os.getenv('MARKETING_AGENT_REDIS_URL')
        
        return env_config


# Global configuration manager
config_manager = ConfigManager(
    config_path=os.getenv('MARKETING_AGENT_CONFIG_PATH', 'config/marketing_agent.json')
)


def get_config() -> MarketingAgentConfig:
    """Get current configuration."""
    return config_manager.get_config()


def reload_config() -> MarketingAgentConfig:
    """Reload configuration from sources."""
    return config_manager.load_config(force_reload=True)
