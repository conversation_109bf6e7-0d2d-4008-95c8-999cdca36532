"""
Integration tests for business profile system.

This test suite covers end-to-end functionality, security integration,
and production scenarios for the business profile implementation.
"""

import pytest
import uuid
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.database import Base, get_db
from app.models.auth import User
from app.models.business_profile import (
    BusinessProfileCreate,
    BusinessProfileUpdate,
    BusinessProfileSwitchRequest
)
from app.services.business_profile_service import BusinessProfileService


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_business_profiles.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_user():
    """Create mock user for testing."""
    user = Mock(spec=User)
    user.id = 1
    user.email = "<EMAIL>"
    user.is_active = True
    return user


@pytest.fixture
def auth_headers(mock_user):
    """Create authentication headers for testing."""
    # In real implementation, this would be a valid JWT token
    return {"Authorization": "Bearer test-token"}


class TestBusinessProfileSecurity:
    """Test business profile security features."""
    
    def test_uuid_validation_in_endpoints(self, client, auth_headers):
        """Test UUID validation in API endpoints."""
        # Test invalid UUID format
        response = client.get(
            "/business-profiles/invalid-uuid",
            headers=auth_headers
        )
        assert response.status_code == 400
        assert "Invalid Profile ID format" in response.json()["detail"]
    
    def test_unauthorized_access_prevention(self, client):
        """Test that unauthorized requests are rejected."""
        response = client.get("/business-profiles/")
        assert response.status_code == 401
    
    def test_input_validation_on_create(self, client, auth_headers):
        """Test input validation on profile creation."""
        # Test empty name
        invalid_data = {
            "name": "",
            "description": "Test description"
        }
        response = client.post(
            "/business-profiles/",
            json=invalid_data,
            headers=auth_headers
        )
        assert response.status_code == 400
        
        # Test name with forbidden characters
        invalid_data = {
            "name": "Test <script>alert('xss')</script>",
            "description": "Test description"
        }
        response = client.post(
            "/business-profiles/",
            json=invalid_data,
            headers=auth_headers
        )
        assert response.status_code == 400
    
    def test_metadata_size_validation(self, client, auth_headers):
        """Test metadata size validation."""
        large_metadata = {"key": "x" * 20000}  # Exceeds 10KB limit
        profile_data = {
            "name": "Test Profile",
            "context_metadata": large_metadata
        }
        response = client.post(
            "/business-profiles/",
            json=profile_data,
            headers=auth_headers
        )
        assert response.status_code == 400
        assert "too large" in response.json()["detail"]


class TestBusinessProfileCRUD:
    """Test CRUD operations for business profiles."""
    
    @patch('app.auth.get_current_active_user')
    def test_create_profile_success(self, mock_auth, client, mock_user):
        """Test successful profile creation."""
        mock_auth.return_value = mock_user
        
        profile_data = {
            "name": "Test Business",
            "description": "A test business profile",
            "industry": "Technology",
            "business_type": "B2B",
            "business_size": "startup"
        }
        
        with patch('app.services.business_profile_service.BusinessProfileService.create_profile') as mock_create:
            mock_profile = Mock()
            mock_profile.id = str(uuid.uuid4())
            mock_profile.name = profile_data["name"]
            mock_profile.user_id = mock_user.id
            mock_create.return_value = mock_profile
            
            response = client.post(
                "/business-profiles/",
                json=profile_data
            )
            
            assert response.status_code == 200
            mock_create.assert_called_once()
    
    @patch('app.auth.get_current_active_user')
    def test_list_profiles_with_counts(self, mock_auth, client, mock_user):
        """Test profile listing with data source and dashboard counts."""
        mock_auth.return_value = mock_user
        
        with patch('app.services.business_profile_service.BusinessProfileService.list_profiles') as mock_list:
            mock_response = Mock()
            mock_response.profiles = []
            mock_response.total = 0
            mock_response.active_profile_id = None
            mock_list.return_value = mock_response
            
            response = client.get("/business-profiles/")
            
            assert response.status_code == 200
            mock_list.assert_called_once_with(mock_user.id)
    
    @patch('app.auth.get_current_active_user')
    def test_switch_active_profile(self, mock_auth, client, mock_user):
        """Test switching active profile."""
        mock_auth.return_value = mock_user
        
        profile_id = str(uuid.uuid4())
        switch_data = {"profile_id": profile_id}
        
        with patch('app.services.business_profile_service.BusinessProfileService.switch_active_profile') as mock_switch:
            mock_switch.return_value = True
            
            response = client.post(
                "/business-profiles/switch",
                json=switch_data
            )
            
            assert response.status_code == 200
            assert response.json()["success"] is True
            mock_switch.assert_called_once_with(mock_user.id, profile_id)


class TestDataSourceAssignment:
    """Test data source assignment functionality."""
    
    @patch('app.auth.get_current_active_user')
    def test_assign_data_source_with_validation(self, mock_auth, client, mock_user):
        """Test data source assignment with validation."""
        mock_auth.return_value = mock_user
        
        profile_id = str(uuid.uuid4())
        data_source_id = str(uuid.uuid4())
        
        assignment_data = {
            "data_source_id": data_source_id,
            "role": "sales_data",
            "priority": 1
        }
        
        with patch('app.services.business_profile_service.BusinessProfileService.assign_data_source') as mock_assign:
            mock_assignment = Mock()
            mock_assignment.id = str(uuid.uuid4())
            mock_assignment.business_profile_id = profile_id
            mock_assignment.data_source_id = data_source_id
            mock_assign.return_value = mock_assignment
            
            response = client.post(
                f"/business-profiles/{profile_id}/data-sources",
                json=assignment_data
            )
            
            assert response.status_code == 200
            mock_assign.assert_called_once()
    
    def test_invalid_data_source_assignment(self, client, auth_headers):
        """Test invalid data source assignment."""
        profile_id = str(uuid.uuid4())
        
        # Invalid priority (exceeds max)
        invalid_data = {
            "data_source_id": str(uuid.uuid4()),
            "priority": 101  # Exceeds max of 100
        }
        
        response = client.post(
            f"/business-profiles/{profile_id}/data-sources",
            json=invalid_data,
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error


class TestConcurrencyAndRaceConditions:
    """Test concurrent operations and race condition prevention."""
    
    @patch('app.auth.get_current_active_user')
    @pytest.mark.asyncio
    async def test_concurrent_profile_switching(self, mock_auth, mock_user):
        """Test concurrent profile switching doesn't cause race conditions."""
        mock_auth.return_value = mock_user
        
        # Simulate concurrent profile switches
        profile_ids = [str(uuid.uuid4()) for _ in range(3)]
        
        async def switch_profile(profile_id):
            service = Mock(spec=BusinessProfileService)
            service.switch_active_profile = AsyncMock(return_value=True)
            return await service.switch_active_profile(mock_user.id, profile_id)
        
        # Run concurrent switches
        tasks = [switch_profile(pid) for pid in profile_ids]
        results = await asyncio.gather(*tasks)
        
        # All should succeed (mocked), but in real implementation,
        # only one should be active due to database constraints
        assert all(results)
    
    def test_database_transaction_integrity(self):
        """Test database transaction integrity during failures."""
        # This would test that failed operations don't leave partial data
        # Implementation depends on actual database setup
        pass


class TestPerformanceAndScaling:
    """Test performance characteristics and scaling behavior."""
    
    @patch('app.auth.get_current_active_user')
    def test_bulk_profile_listing_performance(self, mock_auth, mock_user):
        """Test performance of profile listing with many profiles."""
        mock_auth.return_value = mock_user
        
        # Mock service with many profiles
        with patch('app.services.business_profile_service.BusinessProfileService.list_profiles') as mock_list:
            # Simulate 50 profiles (max allowed)
            mock_profiles = [Mock() for _ in range(50)]
            mock_response = Mock()
            mock_response.profiles = mock_profiles
            mock_response.total = 50
            mock_list.return_value = mock_response
            
            client = TestClient(app)
            response = client.get("/business-profiles/")
            
            assert response.status_code == 200
            # Should handle large number of profiles efficiently
            assert len(response.json()["profiles"]) == 50
    
    def test_cache_performance(self):
        """Test caching performance and memory usage."""
        # This would test the business profile context caching
        # Implementation depends on actual cache setup
        pass


class TestErrorHandlingAndRecovery:
    """Test error handling and recovery scenarios."""
    
    @patch('app.auth.get_current_active_user')
    def test_database_connection_failure_handling(self, mock_auth, client, mock_user):
        """Test handling of database connection failures."""
        mock_auth.return_value = mock_user
        
        with patch('app.services.business_profile_service.BusinessProfileService.list_profiles') as mock_list:
            mock_list.side_effect = Exception("Database connection failed")
            
            response = client.get("/business-profiles/")
            
            assert response.status_code == 500
            assert "Failed to list business profiles" in response.json()["detail"]
    
    @patch('app.auth.get_current_active_user')
    def test_knowledge_graph_service_failure(self, mock_auth, client, mock_user):
        """Test handling of knowledge graph service failures."""
        mock_auth.return_value = mock_user
        
        profile_data = {
            "name": "Test Profile",
            "description": "Test description"
        }
        
        with patch('app.services.business_profile_service.BusinessProfileService.create_profile') as mock_create:
            # Simulate KG service failure during profile creation
            mock_create.side_effect = Exception("Knowledge graph service unavailable")
            
            response = client.post(
                "/business-profiles/",
                json=profile_data
            )
            
            assert response.status_code == 500
            assert "Failed to create business profile" in response.json()["detail"]


class TestSecurityAuditLogging:
    """Test security audit logging functionality."""
    
    @patch('app.auth.get_current_active_user')
    @patch('app.security.business_profile_security.security_audit_log')
    def test_audit_logging_on_profile_operations(self, mock_audit_log, mock_auth, client, mock_user):
        """Test that security operations are properly logged."""
        mock_auth.return_value = mock_user
        
        profile_id = str(uuid.uuid4())
        switch_data = {"profile_id": profile_id}
        
        with patch('app.services.business_profile_service.BusinessProfileService.switch_active_profile') as mock_switch:
            mock_switch.return_value = True
            
            response = client.post(
                "/business-profiles/switch",
                json=switch_data
            )
            
            assert response.status_code == 200
            # Verify audit log was called
            mock_audit_log.assert_called_once_with(
                "profile_switched",
                mock_user.id,
                profile_id
            )
