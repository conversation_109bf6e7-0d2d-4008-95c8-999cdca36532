/**
 * Comprehensive integration tests for the dashboard system.
 * Tests the complete workflow from dashboard creation to widget management.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';

// Import components to test
import { Dashboard } from '@/components/Dashboard';
import { DashboardSecurityProvider } from '@/contexts/DashboardSecurityContext';
import { DashboardErrorBoundary } from '@/components/dashboard/DashboardErrorBoundary';
import { DragDropWidgetGrid } from '@/components/dashboard/DragDropWidgetGrid';
import { WidgetEditor } from '@/components/dashboard/WidgetEditor';
import { DashboardSettingsDialog } from '@/components/dashboard/DashboardSettingsDialog';

// Import utilities
import { validateDashboardCreate, validateWidgetCreate } from '@/utils/dashboard-validation';
import { errorHandler, ErrorType, ErrorSeverity } from '@/utils/error-handling';
import { performanceMonitor } from '@/utils/performance-optimization';

// Mock data
const mockDashboard = {
  id: 'dashboard-1',
  name: 'Test Dashboard',
  description: 'A test dashboard',
  is_default: false,
  is_public: false,
  refresh_interval: 300,
  tags: ['test'],
  theme_config: {},
  layout_config: { columns: 12, rows: 12, grid_gap: 16, responsive: true },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  created_by: 'user-1',
  section_count: 2,
  widget_count: 3,
};

const mockSection = {
  id: 'section-1',
  dashboard_id: 'dashboard-1',
  name: 'Test Section',
  description: 'A test section',
  color: '#3b82f6',
  icon: 'chart',
  layout_config: { columns: 12, rows: 6, grid_gap: 16 },
  customization: {},
  position: 0,
  is_active: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

const mockWidget = {
  id: 'widget-1',
  section_id: 'section-1',
  title: 'Test Widget',
  description: 'A test widget',
  widget_type: 'chart' as const,
  data_config: {
    data_source_id: 'datasource-1',
    query: 'SELECT * FROM test_table',
    x_field: 'date',
    y_field: 'value',
  },
  visualization_config: {
    chart_type: 'line',
    color_scheme: 'default',
    show_legend: true,
    show_grid: true,
  },
  position_config: { x: 0, y: 0, w: 4, h: 3 },
  customization: {},
  refresh_interval: 300,
  is_active: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

const mockDataSource = {
  id: 'datasource-1',
  name: 'Test Data Source',
  type: 'csv' as const,
  connection_config: { file_path: '/test.csv' },
  schema_config: {},
  is_active: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  created_by: 'user-1',
};

// Mock API responses
const mockApiResponses = {
  '/api/dashboard-customization/dashboards': {
    GET: [mockDashboard],
    POST: mockDashboard,
  },
  '/api/dashboard-customization/sections': {
    GET: [mockSection],
    POST: mockSection,
  },
  '/api/dashboard-customization/widgets': {
    GET: [mockWidget],
    POST: mockWidget,
  },
  '/api/data-sources': {
    GET: [mockDataSource],
  },
};

// Mock fetch
global.fetch = vi.fn((url: string, options?: RequestInit) => {
  const method = options?.method || 'GET';
  const response = mockApiResponses[url as keyof typeof mockApiResponses]?.[method as keyof typeof mockApiResponses[keyof typeof mockApiResponses]];
  
  return Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve(response),
  } as Response);
});

// Mock WebSocket
global.WebSocket = vi.fn().mockImplementation(() => ({
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  send: vi.fn(),
  close: vi.fn(),
  readyState: 1,
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <DashboardSecurityProvider>
          <DashboardErrorBoundary>
            {children}
          </DashboardErrorBoundary>
        </DashboardSecurityProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Dashboard Integration Tests', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
    errorHandler.clearErrors();
    performanceMonitor.clearMetrics();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Dashboard Creation and Management', () => {
    it('should create a new dashboard with validation', async () => {
      // Test validation utility
      const validData = {
        name: 'New Dashboard',
        description: 'A new dashboard for testing',
        is_default: false,
        is_public: false,
        refresh_interval: 300,
        tags: ['new', 'test'],
        theme_config: { primary_color: '#3b82f6' },
        layout_config: { columns: 12, rows: 12, grid_gap: 16, responsive: true },
      };

      const validationResult = validateDashboardCreate(validData);
      expect(validationResult.success).toBe(true);
      expect(validationResult.data).toEqual(expect.objectContaining(validData));
    });

    it('should reject invalid dashboard data', () => {
      const invalidData = {
        name: '', // Empty name should fail
        refresh_interval: 10, // Too low
        tags: new Array(15).fill('tag'), // Too many tags
      };

      const validationResult = validateDashboardCreate(invalidData);
      expect(validationResult.success).toBe(false);
      expect(validationResult.errors).toContain('Dashboard name is required');
      expect(validationResult.errors).toContain('Refresh interval must be at least 30 seconds');
      expect(validationResult.errors).toContain('Maximum 10 tags allowed');
    });
  });

  describe('Widget Management', () => {
    it('should validate widget creation data', () => {
      const validWidgetData = {
        section_id: 'section-1',
        title: 'Test Widget',
        widget_type: 'chart' as const,
        data_config: {
          data_source_id: 'datasource-1',
          query: 'SELECT * FROM test',
        },
        position_config: { x: 0, y: 0, w: 4, h: 3 },
        refresh_interval: 300,
      };

      const validationResult = validateWidgetCreate(validWidgetData);
      expect(validationResult.success).toBe(true);
    });

    it('should reject invalid widget data', () => {
      const invalidWidgetData = {
        section_id: 'invalid-uuid',
        title: '',
        widget_type: 'invalid-type',
        position_config: { x: -1, y: 0, w: 0, h: 0 },
      };

      const validationResult = validateWidgetCreate(invalidWidgetData);
      expect(validationResult.success).toBe(false);
      expect(validationResult.errors).toContain('Invalid section ID');
      expect(validationResult.errors).toContain('Widget title is required');
    });
  });

  describe('Error Handling', () => {
    it('should handle and categorize errors correctly', () => {
      // Test network error
      const networkError = new Error('Failed to fetch');
      const handledError = errorHandler.handleError(networkError, {
        showToast: false,
        logError: false,
        reportError: false,
      });

      expect(handledError.type).toBe(ErrorType.NETWORK);
      expect(handledError.severity).toBe(ErrorSeverity.MEDIUM);
      expect(handledError.message).toBe('Failed to fetch');

      // Test validation error
      const validationError = new Error('Validation failed: Invalid input');
      const handledValidationError = errorHandler.handleError(validationError, {
        showToast: false,
        logError: false,
        reportError: false,
      });

      expect(handledValidationError.type).toBe(ErrorType.VALIDATION);
      expect(handledValidationError.severity).toBe(ErrorSeverity.LOW);
    });

    it('should track error statistics', () => {
      // Generate some test errors
      errorHandler.handleError(new Error('Network error'), { showToast: false, logError: false, reportError: false });
      errorHandler.handleError(new Error('Validation error'), { showToast: false, logError: false, reportError: false });
      errorHandler.handleError(new Error('Security threat detected'), { showToast: false, logError: false, reportError: false });

      const stats = errorHandler.getErrorStats();
      expect(stats.total).toBe(3);
      expect(stats.byType[ErrorType.NETWORK]).toBe(1);
      expect(stats.byType[ErrorType.VALIDATION]).toBe(1);
      expect(stats.byType[ErrorType.SECURITY]).toBe(1);
    });
  });

  describe('Performance Monitoring', () => {
    it('should track performance metrics', () => {
      const endTiming = performanceMonitor.startTiming('test-operation');
      
      // Simulate some work
      const start = Date.now();
      while (Date.now() - start < 10) {
        // Wait 10ms
      }
      
      const duration = endTiming();
      expect(duration).toBeGreaterThan(0);

      const metrics = performanceMonitor.getMetrics('test-operation');
      expect(metrics).toBeTruthy();
      expect(metrics!.count).toBe(1);
      expect(metrics!.latest).toBe(duration);
    });

    it('should calculate performance statistics', () => {
      // Record multiple measurements
      performanceMonitor.recordMetric('render-time', 100);
      performanceMonitor.recordMetric('render-time', 150);
      performanceMonitor.recordMetric('render-time', 200);

      const metrics = performanceMonitor.getMetrics('render-time');
      expect(metrics).toBeTruthy();
      expect(metrics!.count).toBe(3);
      expect(metrics!.average).toBe(150);
      expect(metrics!.min).toBe(100);
      expect(metrics!.max).toBe(200);
    });
  });

  describe('Security Validation', () => {
    it('should detect XSS attempts', () => {
      const maliciousInput = '<script>alert("xss")</script>';
      
      // This would be tested with the security validation hook
      // For now, we'll test the validation logic directly
      expect(maliciousInput).toContain('<script>');
    });

    it('should detect SQL injection attempts', () => {
      const maliciousQuery = "'; DROP TABLE users; --";
      
      // This would be tested with the security validation
      expect(maliciousQuery).toContain('DROP TABLE');
    });
  });

  describe('Component Integration', () => {
    it('should render dashboard with error boundary', async () => {
      render(
        <TestWrapper>
          <Dashboard viewMode="customizable" />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
      });
    });

    it('should handle component errors gracefully', async () => {
      // Create a component that throws an error
      const ErrorComponent = () => {
        throw new Error('Test error');
      };

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <TestWrapper>
          <DashboardErrorBoundary>
            <ErrorComponent />
          </DashboardErrorBoundary>
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/dashboard error/i)).toBeInTheDocument();
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Real-time Updates', () => {
    it('should handle WebSocket connection', () => {
      // Mock WebSocket connection
      const mockWs = new WebSocket('ws://localhost:8000/ws/dashboard');
      
      expect(mockWs).toBeDefined();
      expect(mockWs.addEventListener).toHaveBeenCalled();
    });
  });

  describe('Data Flow Integration', () => {
    it('should complete full dashboard workflow', async () => {
      // This test would simulate:
      // 1. Creating a dashboard
      // 2. Adding sections
      // 3. Adding widgets
      // 4. Configuring data sources
      // 5. Real-time updates
      // 6. Error handling
      // 7. Performance monitoring

      const workflow = async () => {
        // 1. Validate dashboard creation
        const dashboardData = {
          name: 'Integration Test Dashboard',
          description: 'Testing complete workflow',
          refresh_interval: 300,
        };
        
        const dashboardValidation = validateDashboardCreate(dashboardData);
        expect(dashboardValidation.success).toBe(true);

        // 2. Validate widget creation
        const widgetData = {
          section_id: 'section-1',
          title: 'Integration Test Widget',
          widget_type: 'chart' as const,
          position_config: { x: 0, y: 0, w: 4, h: 3 },
        };
        
        const widgetValidation = validateWidgetCreate(widgetData);
        expect(widgetValidation.success).toBe(true);

        // 3. Monitor performance
        const endTiming = performanceMonitor.startTiming('integration-test');
        
        // Simulate some processing time
        await new Promise(resolve => setTimeout(resolve, 50));
        
        const duration = endTiming();
        expect(duration).toBeGreaterThan(0);

        return { dashboardValidation, widgetValidation, duration };
      };

      const result = await workflow();
      expect(result.dashboardValidation.success).toBe(true);
      expect(result.widgetValidation.success).toBe(true);
      expect(result.duration).toBeGreaterThan(0);
    });
  });
});

// Export test utilities for use in other test files
export {
  TestWrapper,
  mockDashboard,
  mockSection,
  mockWidget,
  mockDataSource,
  mockApiResponses,
};
