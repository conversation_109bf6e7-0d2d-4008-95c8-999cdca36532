"""
PandasAI v3 wrapper module.

This module provides a wrapper for the PandasAI v3 Agent class, making it easier
to use PandasAI v3 in the Datagenius backend.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional, Union, List

import pandasai as pai
from pandasai import Agent
from .llm_providers import LLMProviderFactory

logger = logging.getLogger(__name__)

class PandasAIWrapper:
    """Wrapper for PandasAI v3 Agent class."""

    def __init__(self):
        """Initialize the PandasAI wrapper."""
        self.api_key = None
        self.agent = None
        self.df = None
        self.config = {}

    def initialize(self, api_key: str, provider: str = "openai"):
        """Initialize PandasAI with API key."""
        logger.info(f"PandasAI wrapper initialize - api_key length: {len(api_key) if api_key else 0}, provider: {provider}")

        self.api_key = api_key
        self.provider = provider

        # Set the PandasAI API key globally if it's a PANDASAI_API_KEY
        if api_key and api_key == os.getenv("PANDASAI_API_KEY"):
            try:
                # Set the global PandasAI API key
                pai.api_key.set(api_key)
                logger.info("Set global PandasAI API key")
            except Exception as e:
                logger.warning(f"Could not set global PandasAI API key: {e}")

        logger.info(f"Initialized PandasAI with {provider} API key")

    def load_dataframe(self, file_path: str) -> bool:
        """Load a dataframe from a file path."""
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                logger.error(f"File does not exist: {file_path}")

                # Try to find the file in common directories
                search_dirs = ["data", "uploads", "backend/data", "backend/uploads", "temp_uploads", "backend/temp_uploads", "."]
                for directory in search_dirs:
                    base_name = os.path.basename(file_path)
                    potential_path = os.path.join(directory, base_name)
                    if os.path.exists(potential_path):
                        logger.info(f"Found file at alternative path: {potential_path}")
                        file_path = potential_path
                        break

                # If still not found, return False
                if not os.path.exists(file_path):
                    logger.error(f"Could not find file at any location")
                    return False

            # Load the dataframe based on file extension
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.csv':
                logger.info(f"Loading CSV file: {file_path}")
                try:
                    # Try with different encodings if needed
                    self.df = pai.read_csv(file_path)
                except UnicodeDecodeError:
                    logger.info("Trying with different encoding (latin1)")
                    self.df = pai.read_csv(file_path, encoding='latin1')
            elif file_ext in ['.xlsx', '.xls']:
                logger.info(f"Loading Excel file: {file_path}")
                self.df = pai.read_excel(file_path)
            elif file_ext == '.json':
                logger.info(f"Loading JSON file: {file_path}")
                self.df = pai.read_json(file_path)
            else:
                # Try to infer the file type from content
                logger.warning(f"Unsupported file extension: {file_ext}, attempting to infer file type")

                # Try as CSV first
                try:
                    self.df = pai.read_csv(file_path)
                    logger.info(f"Successfully loaded as CSV: {file_path}")
                except Exception as csv_error:
                    logger.warning(f"Failed to load as CSV: {csv_error}")

                    # Try as Excel
                    try:
                        self.df = pai.read_excel(file_path)
                        logger.info(f"Successfully loaded as Excel: {file_path}")
                    except Exception as excel_error:
                        logger.warning(f"Failed to load as Excel: {excel_error}")

                        # Try as JSON
                        try:
                            self.df = pai.read_json(file_path)
                            logger.info(f"Successfully loaded as JSON: {file_path}")
                        except Exception as json_error:
                            logger.error(f"Could not load file as any supported format: {file_path}")
                            return False

            # Verify the dataframe was loaded successfully
            if self.df is None or self.df.empty:
                logger.error(f"Loaded dataframe is empty or None: {file_path}")
                return False

            logger.info(f"Successfully loaded dataframe from {file_path} with shape {self.df.shape}")
            return True
        except Exception as e:
            logger.error(f"Error loading dataframe from {file_path}: {e}", exc_info=True)
            return False

    def create_agent(self, df: Optional[pd.DataFrame] = None, model: Optional[str] = None) -> bool:
        """Create a PandasAI Agent instance."""
        try:
            if df is not None:
                self.df = df

            if self.df is None:
                logger.error("No dataframe loaded")
                return False

            if not hasattr(self, 'provider') or not self.api_key:
                logger.error(f"Provider or API key not initialized - has provider: {hasattr(self, 'provider')}, provider: {getattr(self, 'provider', 'None')}, api_key length: {len(self.api_key) if self.api_key else 0}")
                return False

            # Create LLM provider using our factory
            logger.info(f"Creating LLM provider - provider: {self.provider}, model: {model}")
            llm = LLMProviderFactory.create_provider(
                provider=self.provider,
                api_key=self.api_key,
                model=model
            )
            logger.info(f"Successfully created LLM provider: {type(llm)}")

            # Enhanced configuration for better SQL generation and visualization
            config = {
                "llm": llm,
                "verbose": True,  # Enable verbose logging for debugging
                "conversational": False,
                "save_charts": True,
                "save_charts_path": "exports/charts",
                "enable_cache": False,  # Disable cache to avoid stale results
                "custom_whitelisted_dependencies": ["matplotlib", "seaborn", "plotly"],
                "max_retries": 2,  # Reduce retries to fail faster
                "enforce_privacy": False
            }

            # Create agent with enhanced configuration
            self.agent = Agent(self.df, config=config)
            logger.info(f"Created PandasAI Agent instance with {self.provider} provider and enhanced config")
            return True
        except Exception as e:
            logger.error(f"Error creating PandasAI Agent: {e}", exc_info=True)
            return False

    def chat(self, query: str) -> Dict[str, Any]:
        """Chat with the PandasAI Agent."""
        try:
            if self.agent is None:
                logger.error("PandasAI Agent not initialized")
                return {"error": "Agent not initialized"}

            response = self.agent.chat(query)
            logger.info(f"PandasAI chat response type: {type(response)}")
            logger.info(f"PandasAI chat response content: {str(response)[:200]}...")

            # Process and format the response
            formatted_response = self._format_response(response)
            logger.info(f"Formatted response type: {formatted_response.get('type', 'unknown')}")
            return formatted_response
        except Exception as e:
            logger.error(f"Error in PandasAI chat: {e}", exc_info=True)
            return {"error": str(e)}

    def _format_response(self, response: Any) -> Dict[str, Any]:
        """Format the response from PandasAI."""
        logger.info(f"Formatting response: {response} (type: {type(response)})")

        # Handle different response types
        if isinstance(response, pd.DataFrame):
            return {
                "type": "dataframe",
                "data": response.to_dict(orient="records"),
                "columns": response.columns.tolist()
            }
        elif isinstance(response, (int, float)):
            return {
                "type": "number",
                "value": response
            }
        elif isinstance(response, dict):
            # Handle dictionary responses from PandasAI (like {"type": "plot", "value": "filename.png"})
            if response.get("type") == "plot" and response.get("value"):
                # This is a chart response
                image_path = response.get("value")
                logger.info(f"Found plot response with image path: {image_path}")
                return {
                    "type": "chart",
                    "image_path": image_path
                }
            else:
                # Other dictionary responses
                return response
        elif hasattr(response, "image_path") and response.image_path:
            # Handle chart response (object with image_path attribute)
            return {
                "type": "chart",
                "image_path": response.image_path
            }
        elif str(type(response)).find('ChartResponse') != -1:
            # Handle PandasAI ChartResponse objects
            image_path = str(response)  # ChartResponse.__str__ returns the path
            logger.info(f"Found ChartResponse object with path: {image_path}")
            return {
                "type": "chart",
                "image_path": image_path
            }
        elif isinstance(response, str) and (response.endswith('.png') or response.endswith('.jpg') or response.endswith('.jpeg')):
            # Handle string responses that look like image paths
            logger.info(f"Found string response that looks like image path: {response}")
            return {
                "type": "chart",
                "image_path": response
            }
        else:
            # Default to text response
            return {
                "type": "text",
                "text": str(response)
            }

    def train(self, instructions: Optional[str] = None,
              queries: Optional[List[str]] = None,
              codes: Optional[List[str]] = None) -> bool:
        """Train the PandasAI Agent."""
        try:
            if self.agent is None:
                logger.error("PandasAI Agent not initialized")
                return False

            if instructions:
                self.agent.train(docs=instructions)
                logger.info("Trained PandasAI Agent with instructions")

            if queries and codes and len(queries) == len(codes):
                self.agent.train(queries=queries, codes=codes)
                logger.info(f"Trained PandasAI Agent with {len(queries)} Q/A pairs")

            return True
        except Exception as e:
            logger.error(f"Error training PandasAI Agent: {e}", exc_info=True)
            return False
