"""
Test script to verify the fix for circular references in JSON serialization.

This script creates objects with circular references and tests that our
ensure_serializable function can handle them without causing infinite recursion.
"""

import logging
import json
import sys

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("test_circular_refs")

# Add the parent directory to sys.path
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the functions to test
from app.utils.json_utils import ensure_serializable, sanitize_metadata

def test_circular_dict():
    """Test with a dictionary that contains a circular reference to itself."""
    logger.info("Testing circular dictionary")

    # Create a dictionary with a circular reference
    d = {"name": "test"}
    d["self"] = d

    try:
        # This would cause infinite recursion without our fix
        result = ensure_serializable(d)
        logger.info(f"Successfully serialized circular dictionary: {json.dumps(result)}")
        return True
    except RecursionError:
        logger.error("RecursionError occurred when serializing circular dictionary")
        return False

def test_circular_list():
    """Test with a list that contains a circular reference to itself."""
    logger.info("Testing circular list")

    # Create a list with a circular reference
    l = [1, 2, 3]
    l.append(l)

    try:
        # This would cause infinite recursion without our fix
        result = ensure_serializable(l)
        logger.info(f"Successfully serialized circular list: {json.dumps(result)}")
        return True
    except RecursionError:
        logger.error("RecursionError occurred when serializing circular list")
        return False

class CircularObject:
    """A class with a circular reference to itself."""
    def __init__(self, name):
        self.name = name
        self.parent = None
        self.children = []

    def add_child(self, child):
        """Add a child and set its parent to self."""
        self.children.append(child)
        child.parent = self

def test_circular_object():
    """Test with objects that contain circular references to each other."""
    logger.info("Testing circular object")

    # Create objects with circular references
    parent = CircularObject("parent")
    child1 = CircularObject("child1")
    child2 = CircularObject("child2")

    parent.add_child(child1)
    parent.add_child(child2)

    try:
        # This would cause infinite recursion without our fix
        result = ensure_serializable(parent)
        logger.info(f"Successfully serialized circular object: {json.dumps(result)}")
        return True
    except RecursionError:
        logger.error("RecursionError occurred when serializing circular object")
        return False

def test_sanitize_metadata():
    """Test sanitize_metadata with circular references."""
    logger.info("Testing sanitize_metadata with circular references")

    # Create metadata with circular references
    metadata = {
        "name": "test",
        "object": CircularObject("test_object")
    }
    metadata["self"] = metadata
    metadata["object"].metadata = metadata

    try:
        # This would cause infinite recursion without our fix
        result = sanitize_metadata(metadata)
        logger.info(f"Successfully sanitized metadata with circular references: {json.dumps(result)}")
        return True
    except RecursionError:
        logger.error("RecursionError occurred when sanitizing metadata with circular references")
        return False

def test_tuple_keys():
    """Test with a dictionary that has tuple keys."""
    logger.info("Testing dictionary with tuple keys")

    # Create a dictionary with tuple keys
    d = {
        "normal_key": "normal_value",
        (1, 2, 3): "tuple_value",
        ("a", "b"): "another_tuple_value",
        frozenset([1, 2, 3]): "frozenset_value"
    }

    try:
        # This would fail without our fix
        result = ensure_serializable(d)
        logger.info(f"Successfully serialized dictionary with tuple keys: {json.dumps(result)}")
        return True
    except TypeError:
        logger.error("TypeError occurred when serializing dictionary with tuple keys")
        return False

if __name__ == "__main__":
    logger.info("Starting circular reference tests")

    # Run the tests
    tests = [
        test_circular_dict,
        test_circular_list,
        test_circular_object,
        test_sanitize_metadata,
        test_tuple_keys
    ]

    success = True
    for test in tests:
        if not test():
            success = False

    if success:
        logger.info("All tests passed!")
    else:
        logger.error("Some tests failed!")
