
import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>hart,
  Line,
  <PERSON><PERSON>hart,
  Bar,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ReportPreviewProps {
  dataSource: string;
  timeRange: string;
  visualizations: string[];
}

export function ReportPreview({
  dataSource,
  timeRange,
  visualizations,
}: ReportPreviewProps) {
  // Simulated data for different visualization types
  const lineData = [
    { name: "Jan", value: 400 },
    { name: "Feb", value: 300 },
    { name: "Mar", value: 600 },
    { name: "Apr", value: 800 },
    { name: "May", value: 500 },
    { name: "<PERSON>", value: 900 },
  ];

  const barData = [
    { name: "Product A", value: 400 },
    { name: "Product B", value: 300 },
    { name: "Product C", value: 600 },
    { name: "Product D", value: 800 },
    { name: "Product E", value: 500 },
  ];

  const pieData = [
    { name: "Category A", value: 400 },
    { name: "Category B", value: 300 },
    { name: "Category C", value: 300 },
    { name: "Category D", value: 200 },
  ];

  const scatterData = [
    { x: 100, y: 200, z: 200 },
    { x: 120, y: 100, z: 260 },
    { x: 170, y: 300, z: 400 },
    { x: 140, y: 250, z: 280 },
    { x: 150, y: 400, z: 500 },
    { x: 110, y: 280, z: 200 },
  ];

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

  const kpiData = [
    { label: "Total Revenue", value: "$42,389", change: "+12.5%" },
    { label: "Average Order Value", value: "$128", change: "+5.2%" },
    { label: "Conversion Rate", value: "3.2%", change: "+0.8%" },
    { label: "Active Users", value: "12,456", change: "+18.2%" },
  ];

  const tableData = [
    {
      id: 1,
      product: "Product A",
      sales: 354,
      revenue: "$4,890",
      growth: "+12.5%",
    },
    {
      id: 2,
      product: "Product B",
      sales: 258,
      revenue: "$3,204",
      growth: "-3.2%",
    },
    {
      id: 3,
      product: "Product C",
      sales: 185,
      revenue: "$2,990",
      growth: "+8.1%",
    },
    {
      id: 4,
      product: "Product D",
      sales: 320,
      revenue: "$4,320",
      growth: "+15.3%",
    },
  ];

  return (
    <div className="space-y-6">
      <Tabs defaultValue="preview" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="preview">Preview</TabsTrigger>
          <TabsTrigger value="data">Data</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="space-y-6">
          {/* KPI Cards */}
          {visualizations.includes("kpi") && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {kpiData.map((kpi, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="text-2xl font-bold">{kpi.value}</div>
                    <p className="text-sm text-muted-foreground">{kpi.label}</p>
                    <div
                      className={`mt-2 text-sm ${
                        kpi.change.startsWith("+")
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {kpi.change} from previous period
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Line Chart */}
            {visualizations.includes("line") && (
              <Card>
                <CardHeader>
                  <CardTitle>Trend Over Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={lineData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="value"
                          stroke="#8884d8"
                          activeDot={{ r: 8 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Bar Chart */}
            {visualizations.includes("bar") && (
              <Card>
                <CardHeader>
                  <CardTitle>Comparison by Category</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={barData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="value" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Pie Chart */}
            {visualizations.includes("pie") && (
              <Card>
                <CardHeader>
                  <CardTitle>Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={pieData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) =>
                            `${name}: ${(percent * 100).toFixed(0)}%`
                          }
                        >
                          {pieData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={COLORS[index % COLORS.length]}
                            />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Scatter Chart */}
            {visualizations.includes("scatter") && (
              <Card>
                <CardHeader>
                  <CardTitle>Correlation Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <ScatterChart>
                        <CartesianGrid />
                        <XAxis
                          type="number"
                          dataKey="x"
                          name="Variable X"
                          unit=""
                        />
                        <YAxis
                          type="number"
                          dataKey="y"
                          name="Variable Y"
                          unit=""
                        />
                        <Tooltip cursor={{ strokeDasharray: "3 3" }} />
                        <Scatter
                          name="Data Points"
                          data={scatterData}
                          fill="#8884d8"
                        />
                      </ScatterChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Data Table */}
          {visualizations.includes("table") && (
            <Card>
              <CardHeader>
                <CardTitle>Detailed Data</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Product</th>
                        <th className="text-left p-2">Sales</th>
                        <th className="text-left p-2">Revenue</th>
                        <th className="text-left p-2">Growth</th>
                      </tr>
                    </thead>
                    <tbody>
                      {tableData.map((row) => (
                        <tr key={row.id} className="border-b">
                          <td className="p-2">{row.product}</td>
                          <td className="p-2">{row.sales}</td>
                          <td className="p-2">{row.revenue}</td>
                          <td
                            className={`p-2 ${
                              row.growth.startsWith("+")
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {row.growth}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="data">
          <Card>
            <CardContent className="pt-6">
              <p className="text-muted-foreground mb-4">
                Raw data used to generate this report
              </p>
              <pre className="bg-secondary p-4 rounded-md overflow-auto text-xs">
                {JSON.stringify(
                  {
                    lineData,
                    barData,
                    pieData,
                    scatterData,
                    kpiData,
                    tableData,
                  },
                  null,
                  2
                )}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">Report Settings</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure how this report is generated and displayed
                  </p>
                </div>

                <div>
                  <h4 className="text-sm font-medium">Data Source:</h4>
                  <p className="text-sm text-muted-foreground">
                    {dataSource === "sales"
                      ? "Sales Data"
                      : dataSource === "customers"
                      ? "Customer Data"
                      : dataSource === "marketing"
                      ? "Marketing Campaigns"
                      : dataSource === "inventory"
                      ? "Inventory Data"
                      : "Unknown"}
                  </p>
                </div>

                <div>
                  <h4 className="text-sm font-medium">Time Range:</h4>
                  <p className="text-sm text-muted-foreground">
                    {timeRange === "last-7-days"
                      ? "Last 7 Days"
                      : timeRange === "last-30-days"
                      ? "Last 30 Days"
                      : timeRange === "last-90-days"
                      ? "Last Quarter"
                      : timeRange === "last-year"
                      ? "Last Year"
                      : timeRange === "custom"
                      ? "Custom Range"
                      : "Unknown"}
                  </p>
                </div>

                <div>
                  <h4 className="text-sm font-medium">Visualizations:</h4>
                  <p className="text-sm text-muted-foreground">
                    {visualizations.join(", ")}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
