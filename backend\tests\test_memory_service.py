"""
Test script for the Memory Service with local mem0ai.

This script tests the basic functionality of the Memory Service
with a local mem0ai instance using Qdrant for vector storage
and Ollama for the LLM.
"""

import asyncio
import logging
import sys
import os
import time

# Add the parent directory to the path so we can import the backend modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from agents.utils.memory_service import MemoryService
from agents.utils.qdrant_manager import QdrantManager
from app.config import MEM0_SELF_HOSTED

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_memory_service():
    """Test the basic functionality of the Memory Service."""
    logger.info("Testing Memory Service with local mem0ai")
    logger.info(f"Self-hosted mode: {MEM0_SELF_HOSTED}")

    # Check if Qdrant is running
    if not QdrantManager.is_qdrant_running():
        logger.info("Qdrant is not running. Starting it...")
        success = QdrantManager.ensure_qdrant_running()
        if not success:
            logger.error("Failed to start Qdrant. Test cannot continue.")
            return
        # Give Qdrant a moment to fully initialize
        time.sleep(2)

    # Initialize the Memory Service
    memory_service = MemoryService()

    if not memory_service.initialized:
        logger.error("Memory Service failed to initialize")
        return

    # Test user ID
    test_user_id = "test-user-123"

    # Test adding a memory
    test_memory = "This is a test memory for the local mem0ai instance."
    test_metadata = {"type": "test", "source": "test_script"}

    logger.info("Adding a test memory...")
    result = memory_service.add_memory(
        content=test_memory,
        user_id=test_user_id,
        metadata=test_metadata
    )

    if result:
        logger.info(f"Successfully added memory: {result}")
    else:
        logger.error("Failed to add memory")
        return

    # Test searching for memories
    logger.info("Searching for memories...")
    search_result = memory_service.search_memories(
        query="test memory",
        user_id=test_user_id,
        limit=5
    )

    if search_result and search_result.get("results"):
        logger.info(f"Found {len(search_result['results'])} memories")
        for i, memory in enumerate(search_result["results"]):
            logger.info(f"Memory {i+1}: {memory.get('memory')[:50]}... (relevance: {memory.get('relevance', 0):.2f})")
    else:
        logger.warning("No memories found or search failed")

    # Test adding a conversation
    test_conversation = [
        {"role": "user", "content": "Hello, this is a test message."},
        {"role": "assistant", "content": "Hi there! This is a test response."},
        {"role": "user", "content": "Can you remember this conversation?"},
        {"role": "assistant", "content": "Yes, I'll remember this conversation."}
    ]

    logger.info("Adding a test conversation...")
    conv_result = memory_service.add_conversation(
        messages=test_conversation,
        user_id=test_user_id,
        metadata={"type": "conversation", "source": "test_script"}
    )

    if conv_result:
        logger.info(f"Successfully added conversation: {conv_result}")
    else:
        logger.error("Failed to add conversation")

    # Test searching for the conversation
    logger.info("Searching for the conversation...")
    conv_search_result = memory_service.search_memories(
        query="remember this conversation",
        user_id=test_user_id,
        limit=5
    )

    if conv_search_result and conv_search_result.get("results"):
        logger.info(f"Found {len(conv_search_result['results'])} memories related to the conversation")
        for i, memory in enumerate(conv_search_result["results"]):
            logger.info(f"Memory {i+1}: {memory.get('memory')[:50]}... (relevance: {memory.get('relevance', 0):.2f})")
    else:
        logger.warning("No conversation memories found or search failed")

    logger.info("Memory Service test completed")

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_memory_service())
