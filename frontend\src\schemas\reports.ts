// Corresponds to Pydantic schemas that would be used for reports API
// These are based on the interfaces defined in ReportsPage.tsx

export interface ReportConfig {
  name: string;
  data_source_id: string;
  report_type: string; // e.g., "sales_summary", "user_activity"
  filters?: Record<string, any>;
  dimensions?: string[];
  metrics?: string[];
}

export interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly';
  time_of_day: string; // e.g., "09:00"
  day_of_week?: number; // 0 (Sun) - 6 (Sat) for weekly
  day_of_month?: number; // 1-31 for monthly
}

export interface Report {
  id: string;
  name: string;
  config: ReportConfig;
  schedule?: ReportSchedule | null;
  last_generated_at?: string | null; // ISO date string
  status?: string; // e.g., "generating", "available", "failed"
  created_at: string; // ISO date string
  // Add other relevant fields from backend response if they exist
  // e.g., generated_file_url?: string | null;
}

// For creating a report
export interface ReportCreateData {
  config: ReportConfig;
  schedule?: ReportSchedule | null;
}

// For updating a report (could be partial)
export interface ReportUpdateData {
  config?: Partial<ReportConfig>;
  schedule?: Partial<ReportSchedule> | null;
  name?: string;
}
