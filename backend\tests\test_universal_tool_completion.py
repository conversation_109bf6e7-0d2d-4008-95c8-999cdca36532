"""
Comprehensive tests for Universal Tool Completion implementation.

This test suite validates that all agents properly integrate with the ToolCompletionMixin
and that the universal tool completion system works correctly across all agent types.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
import asyncio
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch

# Import the mixins and orchestrator first
from agents.mixins import ToolCompletionMixin
from agents.mixins.orchestrator_enhancement import UniversalToolCompletionOrchestrator


class MockAgent(ToolCompletionMixin):
    """Mock agent for testing ToolCompletionMixin functionality."""

    def __init__(self, agent_type: str):
        self._agent_type = agent_type

    def get_agent_type(self) -> str:
        return self._agent_type

    def get_tool_indicators(self) -> List[str]:
        if self._agent_type == "marketing":
            return ["marketing_form_data", "marketing_task", "marketing_content_request"]
        elif self._agent_type == "analysis":
            return ["analysis_request", "data_analysis_task", "visualization_request", "query_request"]
        elif self._agent_type == "concierge":
            return ["persona_recommendation_request", "persona_selection", "concierge_task"]
        return []

    def get_conversational_flags(self) -> List[str]:
        if self._agent_type == "marketing":
            return ["skip_marketing_content_generation", "is_conversational", "tool_completed"]
        elif self._agent_type == "analysis":
            return ["skip_analysis_execution", "is_conversational", "tool_completed"]
        elif self._agent_type == "concierge":
            return ["skip_persona_recommendation", "is_conversational", "tool_completed"]
        return ["is_conversational", "tool_completed"]


class TestToolCompletionMixinIntegration:
    """Test that all agents properly integrate with ToolCompletionMixin."""

    def test_marketing_agent_mixin_integration(self):
        """Test that MarketingAgent properly implements ToolCompletionMixin."""
        agent = MockAgent("marketing")

        # Test that agent inherits from ToolCompletionMixin
        assert isinstance(agent, ToolCompletionMixin)

        # Test required abstract methods are implemented
        assert agent.get_agent_type() == "marketing"
        assert isinstance(agent.get_tool_indicators(), list)
        assert isinstance(agent.get_conversational_flags(), list)

        # Test specific tool indicators
        tool_indicators = agent.get_tool_indicators()
        expected_indicators = ["marketing_form_data", "marketing_task", "marketing_content_request"]
        assert all(indicator in tool_indicators for indicator in expected_indicators)

        # Test conversational flags
        conversational_flags = agent.get_conversational_flags()
        expected_flags = ["skip_marketing_content_generation", "is_conversational", "tool_completed"]
        assert all(flag in conversational_flags for flag in expected_flags)
    
    def test_analysis_agent_mixin_integration(self):
        """Test that AnalysisAgent properly implements ToolCompletionMixin."""
        agent = MockAgent("analysis")

        # Test that agent inherits from ToolCompletionMixin
        assert isinstance(agent, ToolCompletionMixin)

        # Test required abstract methods are implemented
        assert agent.get_agent_type() == "analysis"
        assert isinstance(agent.get_tool_indicators(), list)
        assert isinstance(agent.get_conversational_flags(), list)

        # Test specific tool indicators
        tool_indicators = agent.get_tool_indicators()
        expected_indicators = ["analysis_request", "data_analysis_task", "visualization_request", "query_request"]
        assert all(indicator in tool_indicators for indicator in expected_indicators)

        # Test conversational flags
        conversational_flags = agent.get_conversational_flags()
        expected_flags = ["skip_analysis_execution", "is_conversational", "tool_completed"]
        assert all(flag in conversational_flags for flag in expected_flags)

    def test_concierge_agent_mixin_integration(self):
        """Test that ConciergeAgent properly implements ToolCompletionMixin."""
        agent = MockAgent("concierge")

        # Test that agent inherits from ToolCompletionMixin
        assert isinstance(agent, ToolCompletionMixin)

        # Test required abstract methods are implemented
        assert agent.get_agent_type() == "concierge"
        assert isinstance(agent.get_tool_indicators(), list)
        assert isinstance(agent.get_conversational_flags(), list)

        # Test specific tool indicators
        tool_indicators = agent.get_tool_indicators()
        expected_indicators = ["persona_recommendation_request", "persona_selection", "concierge_task"]
        assert all(indicator in tool_indicators for indicator in expected_indicators)

        # Test conversational flags
        conversational_flags = agent.get_conversational_flags()
        expected_flags = ["skip_persona_recommendation", "is_conversational", "tool_completed"]
        assert all(flag in conversational_flags for flag in expected_flags)


class TestUniversalToolCompletionFunctionality:
    """Test the universal tool completion functionality across all agents."""
    
    @pytest.fixture
    def marketing_agent(self):
        """Create a marketing agent for testing."""
        return MockAgent("marketing")

    @pytest.fixture
    def analysis_agent(self):
        """Create an analysis agent for testing."""
        return MockAgent("analysis")

    @pytest.fixture
    def concierge_agent(self):
        """Create a concierge agent for testing."""
        return MockAgent("concierge")
    
    def test_tool_triggered_request_detection(self, marketing_agent):
        """Test that tool-triggered requests are properly detected."""
        # Test with tool indicator present
        context_with_tool = {"marketing_form_data": {"task": "create content"}}
        analysis = marketing_agent.analyze_request_intelligently(context_with_tool)
        
        assert analysis["should_use_tools"] is True
        assert analysis["should_be_conversational"] is False
        assert analysis["is_tool_call"] is True
        assert analysis["agent_type"] == "marketing"
    
    def test_conversational_mode_detection(self, marketing_agent):
        """Test that conversational mode is properly detected."""
        # Test with conversational flags present
        context_conversational = {
            "message": "Can you explain more about this?",
            "metadata": {"tool_completion_reset": True}
        }
        analysis = marketing_agent.analyze_request_intelligently(context_conversational)
        
        assert analysis["should_use_tools"] is False
        assert analysis["should_be_conversational"] is True
        assert analysis["is_follow_up"] is True
        assert analysis["agent_type"] == "marketing"
    
    def test_context_reset_functionality(self, analysis_agent):
        """Test that context reset works properly."""
        context = {
            "analysis_request": {"data": "test"},
            "message": "Analyze this data",
            "metadata": {}
        }
        
        # Reset context for conversational mode
        analysis_agent.reset_context_for_conversational_mode(context)
        
        # Check that tool indicators are marked as used
        assert context.get("analysis_request_used") is True
        
        # Check that metadata is updated
        metadata = context.get("metadata", {})
        assert metadata.get("tool_completion_reset") is True
        assert metadata.get("auto_conversational_mode") is True
    
    def test_conversational_context_preparation(self, concierge_agent):
        """Test that conversational context is properly prepared."""
        context = {
            "message": "Tell me more about the recommendations",
            "conversation_history": [
                {"role": "assistant", "content": "Here are some persona recommendations..."}
            ],
            "metadata": {"tool_completed": True}
        }
        
        conv_context = concierge_agent.prepare_conversational_context(context)
        
        # Check that conversational flags are set
        assert conv_context.get("is_conversational") is True
        assert conv_context.get("skip_persona_recommendation") is True
        
        # Check that conversation history is preserved
        assert "conversation_history" in conv_context
        assert len(conv_context["conversation_history"]) > 0


class TestUniversalToolCompletionOrchestrator:
    """Test the UniversalToolCompletionOrchestrator functionality."""
    
    def test_agent_type_detection(self):
        """Test that agent type is properly detected from context."""
        # Test marketing agent detection
        marketing_context = {"marketing_form_data": {"task": "test"}}
        agent_type = UniversalToolCompletionOrchestrator.get_agent_type_from_context(marketing_context)
        assert agent_type == "marketing"
        
        # Test analysis agent detection
        analysis_context = {"analysis_request": {"data": "test"}}
        agent_type = UniversalToolCompletionOrchestrator.get_agent_type_from_context(analysis_context)
        assert agent_type == "analysis"
        
        # Test concierge agent detection
        concierge_context = {"persona_recommendation_request": {"user": "test"}}
        agent_type = UniversalToolCompletionOrchestrator.get_agent_type_from_context(concierge_context)
        assert agent_type == "concierge"
    
    def test_conversational_state_restoration(self):
        """Test that conversational state is properly restored."""
        context = {"message": "Follow-up question"}
        conversation_history = [
            {
                "role": "assistant",
                "content": "Generated marketing content",
                "metadata": {
                    "tool_execution": {"tool_name": "generate_marketing_content", "status": "completed"},
                    "tool_completion_reset": True
                }
            }
        ]
        
        restored = UniversalToolCompletionOrchestrator.restore_conversational_state_for_agent(
            context, conversation_history, "marketing"
        )
        
        assert restored is True
        assert context.get("skip_marketing_content_generation") is True
        assert context.get("is_conversational") is True
        assert context.get("metadata", {}).get("conversational_state_restored") is True


class TestToolCompletionWorkflow:
    """Test complete tool completion workflows."""
    
    @pytest.fixture
    def mock_context(self):
        """Create a mock context for testing."""
        return {
            "user_id": 1,
            "message": "Create marketing content",
            "conversation_id": "test-123",
            "marketing_form_data": {"task": "content_creation"},
            "metadata": {}
        }
    
    def test_tool_execution_to_conversational_transition(self, mock_context):
        """Test the complete workflow from tool execution to conversational mode."""
        agent = MockAgent("marketing")

        # Step 1: Initial tool execution
        analysis = agent.analyze_request_intelligently(mock_context)
        assert analysis["should_use_tools"] is True

        # Step 2: Simulate tool completion and context reset
        agent.reset_context_for_conversational_mode(mock_context)
        agent.add_conversational_state_metadata(mock_context, {"content": "Generated content"})

        # Step 3: Follow-up message should be conversational
        follow_up_context = {
            "message": "Can you explain this content?",
            "metadata": mock_context["metadata"]
        }

        follow_up_analysis = agent.analyze_request_intelligently(follow_up_context)
        assert follow_up_analysis["should_use_tools"] is False
        assert follow_up_analysis["should_be_conversational"] is True

    def test_new_tool_request_override(self, mock_context):
        """Test that new tool requests override conversational mode."""
        agent = MockAgent("marketing")

        # Set up conversational state
        mock_context["metadata"] = {"tool_completion_reset": True, "is_conversational": True}

        # New tool request should override conversational mode
        new_tool_context = {
            **mock_context,
            "marketing_task": {"new_task": "strategy"},
            "message": "Create a new marketing strategy"
        }

        analysis = agent.analyze_request_intelligently(new_tool_context)
        assert analysis["should_use_tools"] is True
        assert analysis["is_tool_call"] is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
