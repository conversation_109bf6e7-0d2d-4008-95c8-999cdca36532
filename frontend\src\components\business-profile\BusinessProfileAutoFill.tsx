/**
 * Business Profile Auto-fill Component
 * 
 * Main component that orchestrates the auto-fill workflow including
 * input collection, processing, preview, and form population
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Loader2, <PERSON>rkles, AlertCircle, CheckCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AutoFillInput } from './AutoFillInput';
import { AutoFillPreview } from './AutoFillPreview';
import { 
  AutoFillResponse, 
  AutoFillProcessor, 
  ProcessingStatus 
} from '@/lib/businessProfileAutoFillApi';

interface BusinessProfileAutoFillProps {
  onAutoFill: (fields: Record<string, string>) => void;
  onClose?: () => void;
  disabled?: boolean;
}

type AutoFillStep = 'input' | 'processing' | 'preview' | 'complete';

export const BusinessProfileAutoFill: React.FC<BusinessProfileAutoFillProps> = ({
  onAutoFill,
  onClose,
  disabled = false,
}) => {
  const [currentStep, setCurrentStep] = useState<AutoFillStep>('input');
  const [files, setFiles] = useState<File[]>([]);
  const [urls, setUrls] = useState<string[]>([]);
  const [autoFillData, setAutoFillData] = useState<AutoFillResponse | null>(null);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({ status: 'idle' });
  const [error, setError] = useState<string | null>(null);

  const processor = AutoFillProcessor.getInstance();

  // Subscribe to processing status updates
  useEffect(() => {
    const unsubscribe = processor.onStatusChange(setProcessingStatus);
    return unsubscribe;
  }, [processor]);

  // Handle files change
  const handleFilesChange = useCallback((newFiles: File[]) => {
    setFiles(newFiles);
    setError(null);
  }, []);

  // Handle URLs change
  const handleUrlsChange = useCallback((newUrls: string[]) => {
    setUrls(newUrls);
    setError(null);
  }, []);

  // Handle processing start
  const handleStartProcessing = useCallback(async () => {
    if (files.length === 0 && urls.length === 0) {
      setError('Please add at least one document or URL to process');
      return;
    }

    try {
      setCurrentStep('processing');
      setError(null);

      const result = await processor.processSources(
        files.length > 0 ? files : undefined,
        urls.length > 0 ? urls : undefined
      );

      setAutoFillData(result);
      setCurrentStep('preview');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Processing failed');
      setCurrentStep('input');
    }
  }, [files, urls, processor]);

  // Handle auto-fill acceptance
  const handleAcceptAutoFill = useCallback((acceptedFields: Record<string, string>) => {
    onAutoFill(acceptedFields);
    setCurrentStep('complete');
    
    // Auto-close after a short delay
    setTimeout(() => {
      onClose?.();
    }, 2000);
  }, [onAutoFill, onClose]);

  // Handle auto-fill rejection
  const handleRejectAutoFill = useCallback(() => {
    setCurrentStep('input');
    setAutoFillData(null);
    processor.reset();
  }, [processor]);

  // Handle restart
  const handleRestart = useCallback(() => {
    setCurrentStep('input');
    setFiles([]);
    setUrls([]);
    setAutoFillData(null);
    setError(null);
    processor.reset();
  }, [processor]);

  // Handle close
  const handleClose = useCallback(() => {
    processor.reset();
    onClose?.();
  }, [processor, onClose]);

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'input':
        return (
          <div className="space-y-6">
            <AutoFillInput
              onFilesChange={handleFilesChange}
              onUrlsChange={handleUrlsChange}
              disabled={disabled}
            />
            
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end gap-3">
              {onClose && (
                <Button variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
              )}
              <Button
                onClick={handleStartProcessing}
                disabled={disabled || (files.length === 0 && urls.length === 0)}
              >
                <Sparkles className="mr-2 h-4 w-4" />
                Start Auto-fill
              </Button>
            </div>
          </div>
        );

      case 'processing':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Loader2 className="h-5 w-5 animate-spin" />
                  Processing Sources
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{processingStatus.message || 'Processing...'}</span>
                    {processingStatus.progress && (
                      <span>{processingStatus.progress}%</span>
                    )}
                  </div>
                  <Progress 
                    value={processingStatus.progress || 0} 
                    className="w-full" 
                  />
                </div>

                <div className="text-sm text-muted-foreground">
                  <p>Processing your sources to extract business information...</p>
                  <ul className="mt-2 space-y-1">
                    {files.length > 0 && (
                      <li>• {files.length} document{files.length !== 1 ? 's' : ''}</li>
                    )}
                    {urls.length > 0 && (
                      <li>• {urls.length} website{urls.length !== 1 ? 's' : ''}</li>
                    )}
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'preview':
        return autoFillData ? (
          <AutoFillPreview
            autoFillData={autoFillData}
            onAccept={handleAcceptAutoFill}
            onReject={handleRejectAutoFill}
          />
        ) : (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No auto-fill data available for preview.
            </AlertDescription>
          </Alert>
        );

      case 'complete':
        return (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
                <div>
                  <h3 className="text-lg font-medium">Auto-fill Complete!</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Your business profile form has been populated with the selected information.
                  </p>
                </div>
                <div className="flex justify-center gap-3">
                  <Button variant="outline" onClick={handleRestart}>
                    Process More Sources
                  </Button>
                  {onClose && (
                    <Button onClick={handleClose}>
                      Close
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Step Indicator */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Intelligent Auto-fill
          </h2>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span className={currentStep === 'input' ? 'font-medium text-foreground' : ''}>
              1. Input
            </span>
            <span>→</span>
            <span className={currentStep === 'processing' ? 'font-medium text-foreground' : ''}>
              2. Processing
            </span>
            <span>→</span>
            <span className={currentStep === 'preview' ? 'font-medium text-foreground' : ''}>
              3. Preview
            </span>
            <span>→</span>
            <span className={currentStep === 'complete' ? 'font-medium text-foreground' : ''}>
              4. Complete
            </span>
          </div>
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          Upload documents or provide website URLs to automatically populate your business profile
        </p>
      </div>

      {/* Step Content */}
      {renderStepContent()}
    </div>
  );
};
