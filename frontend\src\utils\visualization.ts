/**
 * Visualization utilities for rendering data visualizations.
 */

export type VisualizationType = 'chart' | 'interactive_chart' | 'table' | 'tree' | 'network' | 'heatmap' | 'custom' | 'classification' | 'marketing' | 'data_preview' | 'analysis_result';

export interface VisualizationData {
  type: VisualizationType;
  title?: string;
  description?: string;
  data: any;
  config?: any;
  chart_type?: string; // For interactive charts
}

/**
 * Validates visualization data to ensure it has the required properties.
 */
export function validateVisualization(visualization: any): VisualizationData | null {
  console.log('🔍 validateVisualization called with:', visualization);
  console.log('🔍 visualization type:', visualization?.type);
  console.log('🔍 visualization data:', visualization?.data);

  if (!visualization || typeof visualization !== 'object') {
    console.error('❌ Invalid visualization data: not an object');
    return null;
  }

  if (!visualization.type || !visualization.data) {
    console.error('❌ Invalid visualization data: missing required properties');
    return null;
  }

  // Validate based on type
  switch (visualization.type) {
    case 'chart':
      console.log('🔍 Validating chart data...');
      if (!validateChartData(visualization.data)) {
        console.error('❌ Chart data validation failed');
        return null;
      }
      break;
    case 'interactive_chart':
      console.log('🔍 Validating interactive chart data...');
      if (!validateInteractiveChartData(visualization.data)) {
        console.error('❌ Interactive chart data validation failed');
        return null;
      }
      break;
    case 'table':
      if (!validateTableData(visualization.data)) {
        return null;
      }
      break;
    case 'classification':
      // Basic validation for classification data
      if (!visualization.data || !visualization.data.results) {
        console.error('Invalid classification data: missing results');
        return null;
      }
      break;
    case 'marketing':
      // Basic validation for marketing content data
      if (!visualization.data) {
        console.error('Invalid marketing content data: missing data');
        return null;
      }
      break;
    case 'data_preview':
      // Basic validation for data preview
      if (!visualization.data || (!visualization.data.preview_data && !visualization.data.description)) {
        console.error('Invalid data preview: missing preview data or description');
        return null;
      }
      break;
    case 'tree':
    case 'network':
    case 'heatmap':
    case 'custom':
      // Basic validation for other types
      if (!visualization.data) {
        console.error(`Invalid ${visualization.type} data: missing data`);
        return null;
      }
      break;
    default:
      console.error(`Unknown visualization type: ${visualization.type}`);
      return null;
  }

  return visualization as VisualizationData;
}

/**
 * Validates chart data.
 */
function validateChartData(data: any): boolean {
  if (!data) {
    console.error('Invalid chart data: missing data');
    return false;
  }

  // If it's an image-based visualization, it should have an image property
  if (data.image) {
    return true;
  }

  // For standard chart data
  if (!data.labels || !Array.isArray(data.labels)) {
    console.error('Invalid chart data: missing or invalid labels');
    return false;
  }

  if (!data.datasets || !Array.isArray(data.datasets)) {
    console.error('Invalid chart data: missing or invalid datasets');
    return false;
  }

  return true;
}

/**
 * Validates interactive chart data.
 */
function validateInteractiveChartData(data: any): boolean {
  console.log('🔍 validateInteractiveChartData called with data:', data);
  console.log('🔍 data keys:', Object.keys(data || {}));

  if (!data) {
    console.error('Invalid interactive chart data: missing data');
    return false;
  }

  // Interactive chart data should have chart_data array
  if (data.chart_data && Array.isArray(data.chart_data)) {
    console.log('✅ Valid interactive chart data: found chart_data array with', data.chart_data.length, 'items');
    return true;
  }

  // Fallback to image-based validation
  if (data.image || data.fallback_image) {
    console.log('✅ Valid interactive chart data: found fallback image');
    return true;
  }

  console.error('❌ Invalid interactive chart data: missing chart_data or image');
  console.error('❌ Available data keys:', Object.keys(data || {}));
  return false;
}

/**
 * Validates table data.
 */
function validateTableData(data: any): boolean {
  if (!data) {
    console.error('Invalid table data: missing data');
    return false;
  }

  if (!data.headers || !Array.isArray(data.headers)) {
    console.error('Invalid table data: missing or invalid headers');
    return false;
  }

  if (!data.rows || !Array.isArray(data.rows)) {
    console.error('Invalid table data: missing or invalid rows');
    return false;
  }

  return true;
}

/**
 * Processes raw visualization data from the API.
 */
export function processVisualizationData(metadata: any): VisualizationData | null {
  if (!metadata) {
    return null;
  }

  console.log('🔍 processVisualizationData called with metadata:', metadata);
  console.log('🔍 Full metadata structure:', JSON.stringify(metadata, null, 2));

  // 🚨 CRITICAL: Always try backend interactive chart data first
  console.log('🚨 PRIORITY CHECK: Looking for backend interactive chart data');
  console.log('🔍 Metadata keys:', Object.keys(metadata || {}));
  console.log('🔍 Interactive chart data:', metadata.interactive_chart);

  // 🚨 DEBUGGING: Check all possible locations for interactive chart data
  console.log('🔍 DEBUGGING - Checking all data locations:');
  console.log('🔍 metadata.interactive_chart:', metadata.interactive_chart);
  console.log('🔍 metadata.visualization:', metadata.visualization);
  console.log('🔍 metadata.visualization?.data:', metadata.visualization?.data);
  console.log('🔍 metadata.visualization?.data?.interactive_chart:', metadata.visualization?.data?.interactive_chart);
  console.log('🔍 metadata.content:', metadata.content);

  // Check for interactive chart data from backend
  if (metadata.interactive_chart && metadata.interactive_chart.chart_data) {
    console.log('✅ BACKEND INTERACTIVE CHART DATA FOUND - Using real data');
    console.log('🎨 Chart data length:', metadata.interactive_chart.chart_data.length);
    console.log('🎨 Chart type:', metadata.interactive_chart.chart_type);
    const backendChart = createInteractiveChartFromBackendData(metadata);
    if (backendChart) {
      console.log('✅ Successfully created backend interactive chart');
      return backendChart;
    }
  }

  // Fallback to metadata parsing (should be rare now)
  const interactiveChart = createInteractiveChartFromMetadata(metadata);
  if (interactiveChart) {
    console.log('✅ INTERACTIVE CHART CREATED from metadata - Using parsed data');
    return interactiveChart;
  }
  console.log('🔍 metadata keys:', Object.keys(metadata || {}));

  // Handle direct visualization object
  if (metadata.visualization && typeof metadata.visualization === 'object') {
    console.log('Found visualization object in metadata:', metadata.visualization);
    console.log('🔍 visualization type:', metadata.visualization.type);
    console.log('🔍 visualization data keys:', Object.keys(metadata.visualization.data || {}));

    // Check if this is already an interactive chart
    if (metadata.visualization.type === 'interactive_chart') {
      console.log('Direct interactive chart found in visualization metadata');
      return validateVisualization(metadata.visualization);
    }

    // Check if the visualization has interactive chart data nested within it
    if (metadata.visualization.data?.interactive_chart) {
      console.log('Found nested interactive chart data in visualization');
      const interactiveData = metadata.visualization.data.interactive_chart;

      if (interactiveData.data?.chart_data) {
        const interactiveVisualization = {
          type: 'interactive_chart',
          title: interactiveData.title || metadata.visualization.title || 'Interactive Chart',
          description: interactiveData.description || metadata.visualization.description || 'Interactive chart generated from data analysis',
          data: {
            ...interactiveData.data,
            fallback_image: metadata.visualization.data?.image // Keep static image as fallback if available
          },
          config: interactiveData.config || { responsive: true, interactive: true },
          chart_type: interactiveData.chart_type || 'bar'
        };

        console.log('Creating interactive chart from nested data:', interactiveVisualization);
        return validateVisualization(interactiveVisualization);
      }
    }

    // Default handling for other visualization types
    return validateVisualization(metadata.visualization);
  }

  // Handle image-based visualization from content
  if (metadata.content && Array.isArray(metadata.content)) {
    console.log('Processing metadata content:', metadata.content);
    const imageContent = metadata.content.find(item => item.type === 'image' && item.src);
    if (imageContent) {
      console.log('Found image content in metadata:', imageContent);
      console.log('Image src preview:', imageContent.src.substring(0, 50) + '...');

      // Extract title from text content if available
      const textContent = metadata.content.find(item => item.type === 'text' && item.text);
      const title = textContent?.text || metadata.prompt || 'Data Visualization';

      // Check if we have interactive chart data in the metadata
      // The backend puts interactive chart data in metadata.visualization.data.interactive_chart
      const interactiveData = metadata.visualization?.data?.interactive_chart;

      if (interactiveData && interactiveData.data?.chart_data) {
        console.log('Found interactive chart data, creating interactive visualization');
        const interactiveVisualization = {
          type: 'interactive_chart',
          title: interactiveData.title || title,
          description: interactiveData.description || 'Interactive chart generated from data analysis',
          data: {
            ...interactiveData.data,
            fallback_image: imageContent.src // Keep static image as fallback
          },
          config: interactiveData.config || {},
          chart_type: interactiveData.chart_type || 'bar'
        };

        console.log('Creating interactive chart visualization:', interactiveVisualization);
        return validateVisualization(interactiveVisualization);
      } else {
        console.log('No interactive chart data found, checking for alternative locations...');

        // Also check if interactive chart data is at the root level of visualization data
        const rootInteractiveData = metadata.visualization?.data;
        if (rootInteractiveData?.chart_data && rootInteractiveData?.metadata) {
          console.log('Found interactive chart data at root level, creating interactive visualization');
          const interactiveVisualization = {
            type: 'interactive_chart',
            title: metadata.visualization?.title || title,
            description: metadata.visualization?.description || 'Interactive chart generated from data analysis',
            data: {
              chart_data: rootInteractiveData.chart_data,
              columns: rootInteractiveData.columns,
              metadata: rootInteractiveData.metadata,
              fallback_image: imageContent.src // Keep static image as fallback
            },
            config: metadata.visualization?.config || { responsive: true, interactive: true },
            chart_type: metadata.visualization?.chart_type || rootInteractiveData.chart_type || 'bar'
          };

          console.log('Creating interactive chart visualization from root data:', interactiveVisualization);
          return validateVisualization(interactiveVisualization);
        }

        // CRITICAL FIX: Always try to create interactive chart first, even from static image data
        console.log('🚨 ATTEMPTING TO CREATE INTERACTIVE CHART FROM STATIC IMAGE DATA');

        // Try to extract data from metadata for interactive chart
        const interactiveFromStatic = createInteractiveChartFromMetadata(metadata, imageContent.src);
        if (interactiveFromStatic) {
          console.log('✅ Successfully created interactive chart from static data:', interactiveFromStatic);
          return validateVisualization(interactiveFromStatic);
        }

        // Only fallback to static if absolutely no data available
        console.log('⚠️ Falling back to static chart - no interactive data available');
        const chartVisualization = {
          type: 'chart',
          title: title,
          description: 'Generated using PandasAI analysis',
          data: {
            image: imageContent.src
          }
        };

        console.log('Creating static chart visualization:', chartVisualization);
        return validateVisualization(chartVisualization);
      }
    }
  }

  // Handle interactive chart data at the top level of metadata
  if (metadata.interactive_chart && metadata.interactive_chart.data?.chart_data) {
    console.log('Found interactive chart data at metadata root level');
    const interactiveData = metadata.interactive_chart;

    const interactiveVisualization = {
      type: 'interactive_chart',
      title: interactiveData.title || 'Interactive Chart',
      description: interactiveData.description || 'Interactive chart generated from data analysis',
      data: interactiveData.data,
      config: interactiveData.config || { responsive: true, interactive: true },
      chart_type: interactiveData.chart_type || 'bar'
    };

    console.log('Creating interactive chart from root metadata:', interactiveVisualization);
    return validateVisualization(interactiveVisualization);
  }

  // Handle classification results
  if (metadata.sample_results && metadata.classification_type) {
    return validateVisualization({
      type: 'classification',
      title: 'Classification Results',
      description: `${metadata.sample_results.length} texts classified using ${metadata.classification_type === 'llm' ? 'LLM' : 'Hugging Face'} classification`,
      data: {
        results: metadata.sample_results,
        classification_type: metadata.classification_type
      }
    });
  }

  // Handle marketing content
  if (metadata.task_type && metadata.generated_content) {
    // Use the response field which contains the actual content
    return validateVisualization({
      type: 'marketing',
      title: `${metadata.task_type.replace(/_/g, ' ')} Content`,
      description: 'Generated marketing content',
      data: {
        content: metadata.response || metadata.content || '',
        task_type: metadata.task_type,
        canExport: true
      }
    });
  }

  // Handle data preview
  if (metadata.data_preview || metadata.data_profile) {
    const previewType = metadata.data_preview ? 'preview' : 'profile';
    const previewData = metadata.data_preview || metadata.data_profile;

    // Extract preview data from the metadata
    let dataToDisplay = null;
    let columns = [];

    if (previewData.metadata && previewData.metadata.preview_data) {
      dataToDisplay = previewData.metadata.preview_data;
      columns = Object.keys(dataToDisplay[0] || {});
    } else if (previewData.metadata && previewData.metadata.filtered_data) {
      dataToDisplay = previewData.metadata.filtered_data;
      columns = Object.keys(dataToDisplay[0] || {});
    } else if (previewData.metadata && previewData.metadata.columns) {
      columns = previewData.metadata.columns;
    }

    // Get the description text
    let descriptionText = '';
    if (previewData.content && previewData.content.length > 0) {
      descriptionText = previewData.content.map((item: any) =>
        item.type === 'text' ? item.text : ''
      ).join('\n');
    }

    return validateVisualization({
      type: 'data_preview',
      title: `Data ${previewType === 'preview' ? 'Preview' : 'Profile'}`,
      description: `${previewType === 'preview' ? 'First few rows' : 'Statistical summary'} of the data`,
      data: {
        preview_data: dataToDisplay,
        columns: columns,
        description: descriptionText,
        metadata: previewData.metadata || {}
      }
    });
  }

  // Handle data info (for analysis agent responses)
  if (metadata.data_info) {
    const dataInfo = metadata.data_info;

    // Extract info data from the metadata
    let infoData = null;
    let columns = [];

    if (dataInfo.metadata) {
      columns = dataInfo.metadata.columns || [];
      infoData = {
        shape: dataInfo.metadata.shape,
        dtypes: dataInfo.metadata.dtypes,
        missing_values: dataInfo.metadata.missing_values
      };
    }

    // Get the description text
    let descriptionText = '';
    if (dataInfo.content && dataInfo.content.length > 0) {
      descriptionText = dataInfo.content.map((item: any) =>
        item.type === 'text' ? item.text : ''
      ).join('\n');
    }

    return validateVisualization({
      type: 'data_preview',
      title: 'Data Information',
      description: 'Dataset structure and information',
      data: {
        preview_data: null,
        columns: columns,
        description: descriptionText,
        metadata: infoData || {}
      }
    });
  }

  // Handle table content embedded in response content
  if (metadata.content && Array.isArray(metadata.content)) {
    const tableContent = metadata.content.find(item => item.type === 'table');
    if (tableContent) {
      // Handle different table formats
      let headers = [];
      let rows = [];

      if (tableContent.table) {
        headers = tableContent.table.headers || [];
        rows = tableContent.table.rows || [];
      } else if (tableContent.data && tableContent.columns) {
        // Handle PandasAI format
        headers = tableContent.columns;
        rows = tableContent.data.map(row => headers.map(col => row[col] || ''));
      }

      if (headers.length > 0 && rows.length > 0) {
        return validateVisualization({
          type: 'table',
          title: metadata.title || 'Analysis Results',
          description: metadata.description || 'Data analysis table',
          data: {
            headers: headers,
            rows: rows
          }
        });
      }
    }
  }

  // Handle string-based visualization types (legacy format)
  if (metadata.visualization && typeof metadata.visualization === 'string') {
    // Convert legacy format to new format
    const type = metadata.visualization as VisualizationType;
    let data: any;

    switch (type) {
      case 'chart':
        data = {
          labels: metadata.chart_labels || [],
          datasets: metadata.chart_datasets || [],
        };
        break;
      case 'table':
        data = {
          headers: metadata.table_headers || [],
          rows: metadata.table_rows || [],
        };
        break;
      default:
        data = metadata.visualization_data || {};
    }

    return validateVisualization({
      type,
      title: metadata.visualization_title,
      description: metadata.visualization_description,
      data,
      config: metadata.visualization_config,
    });
  }

  // Handle analysis results with mixed content (text, tables, images)
  if (metadata.content && Array.isArray(metadata.content) && metadata.content.length > 0) {
    const hasMultipleContentTypes = metadata.content.some(item =>
      ['table', 'image', 'text'].includes(item.type)
    );

    if (hasMultipleContentTypes) {
      return validateVisualization({
        type: 'analysis_result',
        title: metadata.title || 'Analysis Results',
        description: metadata.description || 'Comprehensive analysis output',
        data: {
          content: metadata.content,
          metadata: metadata
        }
      });
    }
  }

  return null;
}

/**
 * REMOVED: Sample chart visualization function has been removed.
 * The system now uses real data from the backend exclusively.
 */

/**
 * REMOVED: Sample table visualization function has been removed.
 * The system now uses real data from the backend exclusively.
 */

/**
 * CRITICAL FUNCTION: Create interactive chart from any available metadata
 * This function aggressively tries to extract data for interactive charts
 */
function createInteractiveChartFromMetadata(metadata: any, fallbackImage?: string): VisualizationData | null {
  console.log('🔍 Attempting to create interactive chart from metadata:', metadata);

  // Priority 1: Check for explicit interactive chart data
  if (metadata.chart_data || metadata.data?.chart_data) {
    const chartData = metadata.chart_data || metadata.data.chart_data;
    console.log('✅ Found explicit chart_data:', chartData);

    return {
      type: 'interactive_chart',
      title: metadata.title || metadata.prompt || 'Interactive Chart',
      description: metadata.description || 'Interactive data visualization',
      data: chartData,
      chart_type: chartData.chart_type || metadata.chart_type || 'bar',
      config: { responsive: true, interactive: true }
    };
  }

  // Priority 2: Check for PandasAI results with data
  if (metadata.content && Array.isArray(metadata.content)) {
    // Look for data content in the response
    const dataContent = metadata.content.find((item: any) =>
      item.type === 'data' ||
      (item.type === 'text' && item.data) ||
      (item.text && typeof item.text === 'object')
    );

    if (dataContent) {
      console.log('✅ Found data content in PandasAI response:', dataContent);

      const data = dataContent.data || dataContent.text || dataContent;
      return {
        type: 'interactive_chart',
        title: metadata.prompt || 'Data Analysis Chart',
        description: 'Generated from PandasAI analysis',
        data: {
          chart_data: Array.isArray(data) ? data : [data],
          chart_type: 'bar',
          metadata: {
            x_axis: 'name',
            y_axes: ['value']
          }
        },
        chart_type: 'bar',
        config: { responsive: true, interactive: true }
      };
    }
  }

  // Priority 3: No more fake data generation - return null if no real data
  console.log('❌ No real data available for interactive chart - fake data generation removed');

  console.log('❌ Could not create interactive chart from metadata');
  return null;
}

/**
 * Create interactive chart from backend data (NEW: Uses real backend data)
 */
function createInteractiveChartFromBackendData(metadata: any): VisualizationData | null {
  console.log('🎨 Creating interactive chart from backend data:', metadata.interactive_chart);

  const interactiveData = metadata.interactive_chart;
  if (!interactiveData || !interactiveData.chart_data || !Array.isArray(interactiveData.chart_data)) {
    console.log('❌ Invalid backend interactive chart data');
    console.log('🔍 Interactive data structure:', interactiveData);
    return null;
  }

  const chartData = interactiveData.chart_data;
  const chartType = interactiveData.chart_type || 'bar';
  const chartMetadata = interactiveData.metadata || {};

  console.log('✅ Backend chart data:', {
    type: chartType,
    records: chartData.length,
    metadata: chartMetadata,
    sampleData: chartData.slice(0, 2) // Log first 2 records for debugging
  });

  // Get fallback image if available
  const fallbackImage = metadata.visualization?.data?.fallback_image ||
                       metadata.visualization?.data?.image;

  return {
    type: 'interactive_chart',
    title: metadata.prompt || chartMetadata.title || interactiveData.title || 'Data Visualization',
    description: interactiveData.description || `Interactive ${chartType} chart with ${chartData.length} data points`,
    data: {
      chart_data: chartData,
      chart_type: chartType,
      columns: chartMetadata.columns || interactiveData.columns || [],
      metadata: {
        ...chartMetadata,
        data_source: 'backend_generated',
        total_records: chartData.length
      },
      fallback_image: fallbackImage
    },
    chart_type: chartType,
    config: {
      responsive: true,
      interactive: true,
      ...interactiveData.config
    }
  };
}

/**
 * Detect chart type from prompt/context
 */
function detectChartTypeFromContext(metadata: any): string {
  // Safely convert to strings and get all relevant text
  const prompt = String(metadata.prompt || metadata.title || '').toLowerCase();
  const content = String(metadata.content || '').toLowerCase();
  const description = String(metadata.description || '').toLowerCase();
  const fullText = `${prompt} ${content} ${description}`;

  console.log('🔍 Detecting chart type from context:', fullText);
  console.log('🔍 Raw metadata:', metadata);

  // PIE CHART - Highest priority for distribution/proportion data
  if (fullText.includes('pie chart') || fullText.includes('pie') ||
      (fullText.includes('distribution') && (fullText.includes('gender') || fullText.includes('category'))) ||
      fullText.includes('proportion') || fullText.includes('percentage') || fullText.includes('share')) {
    console.log('✅ Detected chart type: PIE');
    return 'pie';
  }

  // SCATTER CHART - Only for correlation/relationship data
  if (fullText.includes('scatter') || fullText.includes('correlation') ||
      fullText.includes('relationship') || fullText.includes('vs') || fullText.includes('against')) {
    console.log('✅ Detected chart type: SCATTER');
    return 'scatter';
  }

  // HISTOGRAM/BAR - For frequency distributions
  if (fullText.includes('histogram') || fullText.includes('frequency') || fullText.includes('count')) {
    console.log('✅ Detected chart type: BAR (histogram)');
    return 'bar';
  }

  // LINE CHART - For trends over time
  if (fullText.includes('line') || fullText.includes('trend') || fullText.includes('time') || fullText.includes('over time')) {
    console.log('✅ Detected chart type: LINE');
    return 'line';
  }

  // AREA CHART
  if (fullText.includes('area') || fullText.includes('filled')) {
    console.log('✅ Detected chart type: AREA');
    return 'area';
  }

  // Default to bar chart for general categorical data
  console.log('✅ Detected chart type: BAR (default)');
  return 'bar';
}
