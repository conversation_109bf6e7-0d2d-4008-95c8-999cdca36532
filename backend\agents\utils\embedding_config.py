"""
Embedding configuration utilities for Datagenius.

This module provides utilities for managing embedding model configurations,
including loading available models, getting recommendations, and validating configurations.
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
import yaml

logger = logging.getLogger(__name__)


class EmbeddingConfigManager:
    """Manager for embedding model configurations."""

    def __init__(self):
        """Initialize the embedding config manager."""
        self.config_path = Path(__file__).parent.parent / "configs" / "embedding_models.yaml"
        self._config = None
        self._load_config()

    def _load_config(self):
        """Load the embedding models configuration."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
            logger.info("Loaded embedding models configuration")
        except Exception as e:
            logger.error(f"Failed to load embedding models configuration: {e}")
            self._config = self._get_fallback_config()

    def _get_fallback_config(self) -> Dict[str, Any]:
        """Get fallback configuration if the YAML file cannot be loaded - Hugging Face only."""
        return {
            "embedding_providers": {
                "huggingface": {
                    "name": "Hugging Face",
                    "requires_api_key": False,
                    "models": [
                        {
                            "id": "BAAI/bge-small-en-v1.5",
                            "name": "BGE Small English v1.5",
                            "dimensions": 384,
                            "description": "High-quality small English embedding model"
                        },
                        {
                            "id": "BAAI/bge-base-en-v1.5",
                            "name": "BGE Base English v1.5",
                            "dimensions": 768,
                            "description": "High-quality base English embedding model"
                        },
                        {
                            "id": "sentence-transformers/all-MiniLM-L6-v2",
                            "name": "All MiniLM L6 v2",
                            "dimensions": 384,
                            "description": "Fast and efficient embedding model"
                        }
                    ]
                }
            },
            "default_configs": {
                "fast_and_free": {
                    "provider": "huggingface",
                    "model": "BAAI/bge-small-en-v1.5"
                }
            }
        }

    def get_available_providers(self) -> List[Dict[str, Any]]:
        """Get list of available embedding providers."""
        providers = []
        for provider_id, provider_config in self._config.get("embedding_providers", {}).items():
            providers.append({
                "id": provider_id,
                "name": provider_config.get("name", provider_id),
                "description": provider_config.get("description", ""),
                "requires_api_key": provider_config.get("requires_api_key", False)
            })
        return providers

    def get_available_models(self, provider: str) -> List[Dict[str, Any]]:
        """Get list of available models for a specific provider."""
        provider_config = self._config.get("embedding_providers", {}).get(provider, {})
        return provider_config.get("models", [])

    def get_model_info(self, provider: str, model_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific model."""
        models = self.get_available_models(provider)
        for model in models:
            if model.get("id") == model_id:
                return model
        return None

    def get_default_config(self, config_name: str = "fast_and_free") -> Dict[str, Any]:
        """Get a default configuration by name."""
        return self._config.get("default_configs", {}).get(config_name, {
            "provider": "huggingface",
            "model": "BAAI/bge-small-en-v1.5"
        })

    def get_recommendations(self, use_case: str = "general_purpose") -> List[str]:
        """Get model recommendations for a specific use case."""
        return self._config.get("recommendations", {}).get(use_case, [
            "BAAI/bge-base-en-v1.5",
            "sentence-transformers/all-mpnet-base-v2"
        ])

    def validate_config(self, provider: str, model: str) -> bool:
        """Validate if a provider and model combination is supported."""
        models = self.get_available_models(provider)
        return any(m.get("id") == model for m in models)

    def get_embedding_config_for_mem0(self, provider: str, model: str,
                                     api_key: Optional[str] = None) -> Dict[str, Any]:
        """
        Get embedding configuration formatted for mem0ai.

        Args:
            provider: Embedding provider (openai, huggingface, etc.)
            model: Model identifier
            api_key: API key if required

        Returns:
            Configuration dictionary for mem0ai
        """
        if not self.validate_config(provider, model):
            logger.warning(f"Invalid provider/model combination: {provider}/{model}")
            # Fall back to default
            default = self.get_default_config()
            provider = default["provider"]
            model = default["model"]

        config = {
            "provider": provider,
            "config": {
                "model": model
            }
        }

        # Add API key if required and provided
        provider_info = self._config.get("embedding_providers", {}).get(provider, {})
        if provider_info.get("requires_api_key", False) and api_key:
            config["config"]["api_key"] = api_key

        return config

    def get_requirements(self, provider: str) -> Dict[str, List[str]]:
        """Get installation requirements for a provider."""
        return self._config.get("requirements", {}).get(provider, {})

    def suggest_best_model(self, use_case: str = "general_purpose",
                          prefer_free: bool = True) -> Dict[str, Any]:
        """
        Suggest the best Hugging Face model for a given use case.

        Args:
            use_case: The use case (general_purpose, cost_conscious, high_performance, multilingual)
            prefer_free: Whether to prefer free models (always True for Hugging Face only)

        Returns:
            Dictionary with provider, model, and reasoning
        """
        recommendations = self.get_recommendations(use_case)

        # Only consider Hugging Face models
        for model_id in recommendations:
            if "/" in model_id:  # Hugging Face models typically have org/model format
                return {
                    "provider": "huggingface",
                    "model": model_id,
                    "reasoning": f"High-quality Hugging Face model recommended for {use_case}"
                }

        # Fallback to default Hugging Face model
        return {
            "provider": "huggingface",
            "model": "BAAI/bge-small-en-v1.5",
            "reasoning": "Default high-quality Hugging Face embedding model"
        }


# Global instance
embedding_config_manager = EmbeddingConfigManager()


def get_embedding_config_manager() -> EmbeddingConfigManager:
    """Get the global embedding configuration manager instance."""
    return embedding_config_manager


def get_recommended_embedding_config(use_case: str = "general_purpose",
                                   prefer_free: bool = True) -> Dict[str, Any]:
    """
    Get a recommended Hugging Face embedding configuration for mem0ai.

    Args:
        use_case: The use case for the embeddings
        prefer_free: Whether to prefer free models (always True for Hugging Face only)

    Returns:
        Configuration dictionary for mem0ai with Hugging Face model
    """
    manager = get_embedding_config_manager()
    suggestion = manager.suggest_best_model(use_case, prefer_free)

    # Always return Hugging Face configuration (no API key needed)
    return {
        "provider": "huggingface",
        "config": {
            "model": suggestion["model"]
        }
    }
