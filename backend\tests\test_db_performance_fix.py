#!/usr/bin/env python3
"""
Test script to verify the database performance monitoring fix.

This script tests the fixed PostgreSQL query compatibility and
provides options to disable monitoring if needed.
"""

import os
import sys
import logging
from pathlib import Path

# Add backend root to path
backend_root = Path(__file__).parent
sys.path.insert(0, str(backend_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_database_performance_monitoring():
    """Test the database performance monitoring functionality."""
    logger.info("Testing database performance monitoring...")

    try:
        from app.performance.database_optimization import DatabaseOptimizer

        # Create optimizer instance
        optimizer = DatabaseOptimizer()

        # Test getting performance metrics
        logger.info("Testing performance metrics retrieval...")
        metrics = optimizer.get_performance_metrics()

        if metrics.get("monitoring_disabled"):
            logger.info("✅ Performance monitoring is disabled (as configured)")
        else:
            logger.info("✅ Performance metrics retrieved successfully")
            logger.info(f"Table stats: {len(metrics.get('table_stats', []))} tables")
            logger.info(f"Index stats: {len(metrics.get('index_stats', []))} indexes")
            logger.info(f"Dashboard count: {metrics.get('dashboard_count', 0)}")
            logger.info(f"Widget count: {metrics.get('widget_count', 0)}")

        return True

    except Exception as e:
        logger.error(f"❌ Database performance monitoring test failed: {e}")
        return False


def test_with_monitoring_disabled():
    """Test with performance monitoring disabled."""
    logger.info("Testing with performance monitoring disabled...")

    # Set environment variable to disable monitoring
    os.environ["DISABLE_DB_PERFORMANCE_MONITORING"] = "true"

    try:
        from app.performance.database_optimization import DatabaseOptimizer

        # Create optimizer instance
        optimizer = DatabaseOptimizer()

        # Test getting performance metrics
        metrics = optimizer.get_performance_metrics()

        if metrics.get("monitoring_disabled"):
            logger.info("✅ Performance monitoring successfully disabled")
            return True
        else:
            logger.error("❌ Performance monitoring was not disabled")
            return False

    except Exception as e:
        logger.error(f"❌ Test with disabled monitoring failed: {e}")
        return False
    finally:
        # Clean up environment variable
        os.environ.pop("DISABLE_DB_PERFORMANCE_MONITORING", None)


def test_postgresql_compatibility():
    """Test PostgreSQL version compatibility."""
    logger.info("Testing PostgreSQL compatibility...")

    try:
        from app.database import engine
        from sqlalchemy import text

        # Test basic PostgreSQL connection
        with engine.connect() as conn:
            # Test if we can access pg_stat_user_tables
            try:
                result = conn.execute(text("SELECT version()"))
                version = result.fetchone()[0]
                logger.info(f"PostgreSQL version: {version}")

                # Test the fixed query
                result = conn.execute(text("""
                    SELECT
                        schemaname,
                        relname as table_name,
                        n_tup_ins as inserts,
                        n_tup_upd as updates,
                        n_tup_del as deletes,
                        n_live_tup as live_tuples,
                        n_dead_tup as dead_tuples
                    FROM pg_stat_user_tables
                    WHERE relname IN ('dashboards', 'dashboard_sections', 'dashboard_widgets')
                    LIMIT 5
                """))

                rows = result.fetchall()
                logger.info(f"✅ Fixed query works! Retrieved {len(rows)} table stats")
                return True

            except Exception as e:
                logger.warning(f"Direct query test failed: {e}")

                # Test fallback query
                try:
                    result = conn.execute(text("""
                        SELECT
                            schemaname,
                            'unknown' as table_name,
                            COALESCE(SUM(n_tup_ins), 0) as inserts,
                            COALESCE(SUM(n_tup_upd), 0) as updates,
                            COALESCE(SUM(n_tup_del), 0) as deletes,
                            COALESCE(SUM(n_live_tup), 0) as live_tuples,
                            COALESCE(SUM(n_dead_tup), 0) as dead_tuples
                        FROM pg_stat_user_tables
                        WHERE schemaname = 'public'
                        GROUP BY schemaname
                        LIMIT 1
                    """))

                    rows = result.fetchall()
                    logger.info(f"✅ Fallback query works! Retrieved {len(rows)} aggregated stats")
                    return True

                except Exception as e2:
                    logger.error(f"❌ Both queries failed: {e2}")
                    return False

    except Exception as e:
        logger.error(f"❌ PostgreSQL compatibility test failed: {e}")
        return False


def show_fix_summary():
    """Show summary of the fix applied."""
    logger.info("\n" + "="*60)
    logger.info("DATABASE PERFORMANCE MONITORING FIX SUMMARY")
    logger.info("="*60)
    logger.info("Issue: PostgreSQL column 'tablename' does not exist error")
    logger.info("Fix Applied:")
    logger.info("  1. Changed 'tablename' to 'relname' in pg_stat_user_tables query")
    logger.info("  2. Added fallback query for better compatibility")
    logger.info("  3. Added error handling for different PostgreSQL versions")
    logger.info("  4. Added option to disable monitoring: DISABLE_DB_PERFORMANCE_MONITORING=true")
    logger.info("\nTo disable monitoring permanently, add to your .env file:")
    logger.info("DISABLE_DB_PERFORMANCE_MONITORING=true")
    logger.info("="*60)


def main():
    """Run all database performance monitoring tests."""
    logger.info("🔧 Database Performance Monitoring Fix Test")
    logger.info("=" * 50)

    show_fix_summary()

    tests = [
        ("PostgreSQL Compatibility", test_postgresql_compatibility),
        ("Performance Monitoring (Enabled)", test_database_performance_monitoring),
        ("Performance Monitoring (Disabled)", test_with_monitoring_disabled)
    ]

    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))

    # Summary
    logger.info("\n📊 Test Results Summary:")
    logger.info("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {status}: {test_name}")
        if result:
            passed += 1

    total = len(results)
    logger.info(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All database performance monitoring tests passed!")
        logger.info("The PostgreSQL compatibility issue has been resolved.")
        return 0
    else:
        logger.error(f"💥 {total - passed} tests failed")
        logger.info("Consider setting DISABLE_DB_PERFORMANCE_MONITORING=true if issues persist")
        return 1


if __name__ == "__main__":
    sys.exit(main())
