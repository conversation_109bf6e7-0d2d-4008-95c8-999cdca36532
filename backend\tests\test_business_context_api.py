"""
Integration tests for the Business Context API.

This module tests the API endpoints to ensure they properly integrate
with the enhanced business context analyzer.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
import json

from app.main import app
from app.models.auth import User


class TestBusinessContextAPI:
    """Test suite for Business Context API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_user(self):
        """Mock authenticated user."""
        user = User()
        user.id = 1
        user.email = "<EMAIL>"
        user.is_active = True
        return user
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer test_token"}
    
    @pytest.fixture
    def sample_request_data(self):
        """Sample request data for testing."""
        return {
            "data_sources": ["doc1", "doc2"],
            "conversation_history": [
                {"role": "user", "content": "I run a tech startup"},
                {"role": "user", "content": "We need help with lead generation"}
            ],
            "analysis_depth": "standard",
            "include_recommendations": True,
            "use_ai_analysis": True
        }
    
    @pytest.fixture
    def mock_business_context_result(self):
        """Mock business context detection result."""
        return {
            "success": True,
            "business_context": {
                "industry": "Technology",
                "business_type": "B2B",
                "business_size": "startup",
                "target_market": "Small businesses",
                "key_products": ["SaaS platform"],
                "marketing_challenges": ["lead generation", "content marketing"],
                "competitive_advantages": ["AI-powered features"],
                "current_marketing_channels": ["social media", "content marketing"],
                "budget_indicators": "limited",
                "geographic_focus": "North America",
                "business_stage": "growth",
                "confidence_score": 0.8
            },
            "recommendations": [
                {
                    "type": "strategy",
                    "title": "Content Marketing Strategy",
                    "description": "Develop a content marketing strategy to establish thought leadership",
                    "action": "content_strategy",
                    "priority": "high"
                }
            ],
            "metadata": {
                "analysis_depth": "standard",
                "analysis_method": "ai_powered",
                "data_sources_analyzed": 2,
                "conversation_messages_analyzed": 2,
                "confidence_score": 0.8
            }
        }
    
    def test_detect_business_context_success(self, client, auth_headers, sample_request_data, mock_business_context_result, mock_user):
        """Test successful business context detection."""
        with patch("app.auth.get_current_active_user", return_value=mock_user):
            with patch("agents.tools.mcp.business_context_detection.BusinessContextTool.execute") as mock_execute:
                mock_execute.return_value = mock_business_context_result
                
                response = client.post(
                    "/api/agents/detect-business-context",
                    json=sample_request_data,
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                data = response.json()
                
                assert data["success"] is True
                assert "business_context" in data
                assert "recommendations" in data
                assert data["business_context"]["industry"] == "Technology"
                assert data["business_context"]["confidence_score"] == 0.8
                assert len(data["recommendations"]) > 0
    
    def test_detect_business_context_ai_analysis_enabled(self, client, auth_headers, sample_request_data, mock_user):
        """Test that AI analysis is properly enabled."""
        with patch("app.auth.get_current_active_user", return_value=mock_user):
            with patch("agents.tools.mcp.business_context_detection.BusinessContextTool.execute") as mock_execute:
                mock_execute.return_value = {"success": True, "business_context": {}, "recommendations": []}
                
                response = client.post(
                    "/api/agents/detect-business-context",
                    json=sample_request_data,
                    headers=auth_headers
                )
                
                # Verify that use_ai_analysis parameter was passed
                mock_execute.assert_called_once()
                call_args = mock_execute.call_args[1]  # kwargs
                assert call_args["use_ai_analysis"] is True
    
    def test_detect_business_context_ai_analysis_disabled(self, client, auth_headers, mock_user):
        """Test business context detection with AI analysis disabled."""
        request_data = {
            "data_sources": ["doc1"],
            "conversation_history": [],
            "analysis_depth": "quick",
            "use_ai_analysis": False
        }
        
        with patch("app.auth.get_current_active_user", return_value=mock_user):
            with patch("agents.tools.mcp.business_context_detection.BusinessContextTool.execute") as mock_execute:
                mock_execute.return_value = {
                    "success": True,
                    "business_context": {"industry": "Unknown", "confidence_score": 0.3},
                    "recommendations": [],
                    "metadata": {"analysis_method": "pattern_based"}
                }
                
                response = client.post(
                    "/api/agents/detect-business-context",
                    json=request_data,
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                data = response.json()
                
                # Verify that AI analysis was disabled
                call_args = mock_execute.call_args[1]
                assert call_args["use_ai_analysis"] is False
                
                # Should still get a result, but with pattern-based analysis
                assert data["success"] is True
                assert data["metadata"]["analysis_method"] == "pattern_based"
    
    def test_detect_business_context_error_handling(self, client, auth_headers, sample_request_data, mock_user):
        """Test error handling in business context detection."""
        with patch("app.auth.get_current_active_user", return_value=mock_user):
            with patch("agents.tools.mcp.business_context_detection.BusinessContextTool.execute") as mock_execute:
                mock_execute.return_value = {
                    "success": False,
                    "error": "Analysis failed due to insufficient data"
                }
                
                response = client.post(
                    "/api/agents/detect-business-context",
                    json=sample_request_data,
                    headers=auth_headers
                )
                
                assert response.status_code == 200  # API should handle errors gracefully
                data = response.json()
                
                assert data["success"] is False
                assert "error" in data
                assert "insufficient data" in data["error"]
    
    def test_detect_business_context_unauthorized(self, client, sample_request_data):
        """Test unauthorized access to business context detection."""
        response = client.post(
            "/api/agents/detect-business-context",
            json=sample_request_data
        )
        
        assert response.status_code == 401  # Unauthorized
    
    def test_detect_business_context_invalid_data(self, client, auth_headers, mock_user):
        """Test business context detection with invalid request data."""
        invalid_data = {
            "analysis_depth": "invalid_depth",  # Invalid value
            "use_ai_analysis": "not_a_boolean"  # Invalid type
        }
        
        with patch("app.auth.get_current_active_user", return_value=mock_user):
            response = client.post(
                "/api/agents/detect-business-context",
                json=invalid_data,
                headers=auth_headers
            )
            
            assert response.status_code == 422  # Validation error
    
    def test_get_business_context_capabilities(self, client, auth_headers, mock_user):
        """Test getting business context capabilities."""
        with patch("app.auth.get_current_active_user", return_value=mock_user):
            response = client.get(
                "/api/agents/business-context-capabilities",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert "capabilities" in data
            assert "supported_industries" in data["capabilities"]
            assert "detectable_attributes" in data["capabilities"]
            assert "analysis_depths" in data["capabilities"]
    
    def test_business_context_with_empty_data(self, client, auth_headers, mock_user):
        """Test business context detection with minimal data."""
        minimal_data = {
            "data_sources": [],
            "conversation_history": [],
            "analysis_depth": "quick"
        }
        
        with patch("app.auth.get_current_active_user", return_value=mock_user):
            with patch("agents.tools.mcp.business_context_detection.BusinessContextTool.execute") as mock_execute:
                mock_execute.return_value = {
                    "success": True,
                    "business_context": {
                        "industry": None,
                        "business_type": None,
                        "confidence_score": 0.0
                    },
                    "recommendations": [],
                    "metadata": {"analysis_method": "pattern_based"}
                }
                
                response = client.post(
                    "/api/agents/detect-business-context",
                    json=minimal_data,
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                data = response.json()
                
                assert data["success"] is True
                assert data["business_context"]["confidence_score"] == 0.0
    
    def test_business_context_comprehensive_analysis(self, client, auth_headers, mock_user):
        """Test comprehensive business context analysis."""
        comprehensive_data = {
            "data_sources": ["doc1", "doc2", "doc3"],
            "conversation_history": [
                {"role": "user", "content": "I run a healthcare startup focused on telemedicine"},
                {"role": "user", "content": "We're targeting individual patients and small clinics"},
                {"role": "user", "content": "Our main challenge is building trust with patients"}
            ],
            "analysis_depth": "comprehensive",
            "use_ai_analysis": True
        }
        
        with patch("app.auth.get_current_active_user", return_value=mock_user):
            with patch("agents.tools.mcp.business_context_detection.BusinessContextTool.execute") as mock_execute:
                mock_execute.return_value = {
                    "success": True,
                    "business_context": {
                        "industry": "Healthcare",
                        "business_type": "B2C",
                        "business_size": "startup",
                        "target_market": "Individual patients and small clinics",
                        "key_products": ["Telemedicine platform"],
                        "marketing_challenges": ["trust building", "patient acquisition"],
                        "confidence_score": 0.9
                    },
                    "recommendations": [
                        {
                            "type": "trust_building",
                            "title": "Patient Trust Strategy",
                            "description": "Develop trust-building initiatives for healthcare marketing",
                            "priority": "high"
                        }
                    ],
                    "metadata": {
                        "analysis_method": "ai_powered",
                        "confidence_score": 0.9
                    }
                }
                
                response = client.post(
                    "/api/agents/detect-business-context",
                    json=comprehensive_data,
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                data = response.json()
                
                assert data["success"] is True
                assert data["business_context"]["industry"] == "Healthcare"
                assert data["business_context"]["confidence_score"] == 0.9
                assert "trust building" in data["business_context"]["marketing_challenges"]
                assert len(data["recommendations"]) > 0
                
                # Verify comprehensive analysis was requested
                call_args = mock_execute.call_args[1]
                assert call_args["analysis_depth"] == "comprehensive"
