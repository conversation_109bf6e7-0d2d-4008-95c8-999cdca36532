"""Add state column to conversations table

Revision ID: 82479d2b6028
Revises: 61740c3db93c
Create Date: 2025-04-30 20:22:14.739972

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '82479d2b6028'
down_revision = '61740c3db93c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('conversations', sa.Column('state', sa.String(length=50), nullable=True))
    op.create_index(op.f('ix_conversations_state'), 'conversations', ['state'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_conversations_state'), table_name='conversations')
    op.drop_column('conversations', 'state')
    # ### end Alembic commands ###
