import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Loader2, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw,
  MessageSquare,
  User,
  <PERSON><PERSON>,
  <PERSON>rk<PERSON>
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  metadata?: {
    recommendations?: PersonaRecommendation[];
    suggested_actions?: string[];
    error?: boolean;
    requires_user_input?: boolean;
  };
}

interface PersonaRecommendation {
  persona_id: string;
  confidence: number;
  reasoning: string;
  availability: string;
  capabilities: string[];
  priority: number;
}

interface ConciergeResponse {
  message: string;
  metadata: {
    recommendations?: PersonaRecommendation[];
    suggested_actions?: string[];
    error?: boolean;
    requires_user_input?: boolean;
  };
  success: boolean;
}

interface ImprovedConciergeChatProps {
  conversationId: string;
  onPersonaSelect?: (personaId: string) => void;
  className?: string;
}

export const ImprovedConciergeChat: React.FC<ImprovedConciergeChatProps> = ({
  conversationId,
  onPersonaSelect,
  className = ''
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'error'>('connected');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage: Message = {
      id: 'welcome',
      content: "Hello! I'm your Datagenius Concierge. I can help you find the right AI specialist for your task, assist with data questions, and guide you through our platform. What would you like to work on today?",
      sender: 'assistant',
      timestamp: new Date(),
      metadata: {
        suggested_actions: [
          "Analyze my data",
          "Create marketing content",
          "Classify documents",
          "Browse available personas"
        ]
      }
    };
    setMessages([welcomeMessage]);
  }, []);

  // Send message to concierge
  const sendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/concierge/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          message: content,
          conversation_id: conversationId,
          context: {
            timestamp: new Date().toISOString(),
            user_agent: navigator.userAgent
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: ConciergeResponse = await response.json();

      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        content: data.message,
        sender: 'assistant',
        timestamp: new Date(),
        metadata: data.metadata
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Show success toast for successful interactions
      if (data.success && data.metadata.recommendations?.length) {
        toast({
          title: "Recommendations Ready",
          description: `Found ${data.metadata.recommendations.length} AI specialist(s) for your task.`,
          duration: 3000
        });
      }

    } catch (err) {
      console.error('Error sending message:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      
      setError(errorMessage);
      
      const errorResponse: Message = {
        id: `error-${Date.now()}`,
        content: "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.",
        sender: 'assistant',
        timestamp: new Date(),
        metadata: { error: true }
      };

      setMessages(prev => [...prev, errorResponse]);

      toast({
        title: "Connection Error",
        description: errorMessage,
        variant: "destructive",
        duration: 5000
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage(inputValue);
  };

  // Handle suggested action clicks
  const handleSuggestedAction = (action: string) => {
    sendMessage(action);
  };

  // Handle persona selection
  const handlePersonaSelect = (personaId: string) => {
    if (onPersonaSelect) {
      onPersonaSelect(personaId);
    }
    toast({
      title: "Persona Selected",
      description: `Connecting you to ${personaId.replace('-', ' ')}...`,
      duration: 3000
    });
  };

  // Retry connection
  const retryConnection = () => {
    setError(null);
    setConnectionStatus('connecting');
    setTimeout(() => setConnectionStatus('connected'), 1000);
  };

  return (
    <Card className={`flex flex-col h-full ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/concierge-avatar.png" />
            <AvatarFallback>
              <Sparkles className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <span>Datagenius Concierge</span>
          <Badge 
            variant={connectionStatus === 'connected' ? 'default' : connectionStatus === 'connecting' ? 'secondary' : 'destructive'}
            className="ml-auto"
          >
            {connectionStatus === 'connected' && <CheckCircle className="h-3 w-3 mr-1" />}
            {connectionStatus === 'connecting' && <Loader2 className="h-3 w-3 mr-1 animate-spin" />}
            {connectionStatus === 'error' && <AlertCircle className="h-3 w-3 mr-1" />}
            {connectionStatus}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="mx-4 mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              {error}
              <Button variant="outline" size="sm" onClick={retryConnection}>
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-y-auto px-4 space-y-4">
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-[80%] ${message.sender === 'user' ? 'order-2' : 'order-1'}`}>
                  <div className={`flex items-start gap-2 ${message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      {message.sender === 'user' ? (
                        <AvatarFallback><User className="h-4 w-4" /></AvatarFallback>
                      ) : (
                        <>
                          <AvatarImage src="/concierge-avatar.png" />
                          <AvatarFallback><Bot className="h-4 w-4" /></AvatarFallback>
                        </>
                      )}
                    </Avatar>
                    
                    <div className={`rounded-lg p-3 ${
                      message.sender === 'user' 
                        ? 'bg-brand-500 text-white' 
                        : message.metadata?.error
                          ? 'bg-red-50 border border-red-200 text-red-800'
                          : 'bg-gray-50 border border-gray-200'
                    }`}>
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      
                      {/* Persona Recommendations */}
                      {message.metadata?.recommendations && message.metadata.recommendations.length > 0 && (
                        <div className="mt-3 space-y-2">
                          <p className="text-xs font-medium text-gray-600">Recommended AI Specialists:</p>
                          {message.metadata.recommendations.map((rec, index) => (
                            <div
                              key={rec.persona_id}
                              className="bg-white rounded border p-2 cursor-pointer hover:bg-gray-50 transition-colors"
                              onClick={() => handlePersonaSelect(rec.persona_id)}
                            >
                              <div className="flex items-center justify-between">
                                <span className="font-medium text-sm">
                                  {rec.persona_id.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                </span>
                                <Badge variant="secondary" className="text-xs">
                                  {Math.round(rec.confidence * 100)}% match
                                </Badge>
                              </div>
                              <p className="text-xs text-gray-600 mt-1">{rec.reasoning}</p>
                              {rec.capabilities.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {rec.capabilities.slice(0, 3).map((cap, i) => (
                                    <Badge key={i} variant="outline" className="text-xs">
                                      {cap}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Suggested Actions */}
                      {message.metadata?.suggested_actions && message.metadata.suggested_actions.length > 0 && (
                        <div className="mt-3 space-y-1">
                          <p className="text-xs font-medium text-gray-600">Quick Actions:</p>
                          <div className="flex flex-wrap gap-1">
                            {message.metadata.suggested_actions.map((action, index) => (
                              <Button
                                key={index}
                                variant="outline"
                                size="sm"
                                className="text-xs h-6"
                                onClick={() => handleSuggestedAction(action)}
                              >
                                {action}
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <p className={`text-xs text-gray-500 mt-1 ${message.sender === 'user' ? 'text-right' : 'text-left'}`}>
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Typing Indicator */}
          {isLoading && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex justify-start"
            >
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/concierge-avatar.png" />
                  <AvatarFallback><Bot className="h-4 w-4" /></AvatarFallback>
                </Avatar>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center gap-1">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-gray-600">Thinking...</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Input Form */}
        <div className="border-t p-4">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Ask me anything about Datagenius or describe your task..."
              disabled={isLoading}
              className="flex-1"
            />
            <Button 
              type="submit" 
              disabled={isLoading || !inputValue.trim()}
              size="icon"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  );
};
