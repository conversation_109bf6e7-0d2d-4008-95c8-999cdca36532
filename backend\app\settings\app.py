"""
Main application configuration.

This module provides the main application configuration that combines
all other configuration modules into a single, hierarchical structure.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from pydantic import Field, field_validator, computed_field
from .base import BaseConfig, EnvironmentConfig, LoggingConfig, MonitoringConfig
from .database import DatabaseConfig, RedisConfig
from .security import SecurityConfig
from .llm import LLMConfig


class EmailConfig(BaseConfig):
    """Email configuration settings."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    enabled: bool = Field(default=False, description="Enable email functionality")
    sender: str = Field(default="<EMAIL>", description="Default sender email")
    smtp_server: str = Field(default="smtp.example.com", description="SMTP server hostname")
    smtp_port: int = Field(default=587, ge=1, le=65535, description="SMTP server port")
    smtp_user: str = Field(default="", description="SMTP username")
    smtp_password: str = Field(default="", description="SMTP password")
    use_tls: bool = Field(default=True, description="Use TLS for SMTP connection")
    use_ssl: bool = Field(default=False, description="Use SSL for SMTP connection")
    
    # Email templates
    template_dir: str = Field(default="templates/email", description="Email template directory")
    
    @field_validator('smtp_port')
    @classmethod
    def validate_smtp_port(cls, v: int) -> int:
        """Validate SMTP port number."""
        common_ports = [25, 465, 587, 2525]
        if v not in common_ports:
            # Allow custom ports but warn
            pass
        return v
    
    @classmethod
    def from_env(cls) -> "EmailConfig":
        """Create email configuration from environment variables."""
        return cls(
            enabled=os.getenv("EMAIL_ENABLED", "false").lower() == "true",
            sender=os.getenv("EMAIL_SENDER", "<EMAIL>"),
            smtp_server=os.getenv("EMAIL_SMTP_SERVER", "smtp.example.com"),
            smtp_port=int(os.getenv("EMAIL_SMTP_PORT", "587")),
            smtp_user=os.getenv("EMAIL_SMTP_USER", ""),
            smtp_password=os.getenv("EMAIL_SMTP_PASSWORD", ""),
            use_tls=os.getenv("EMAIL_USE_TLS", "true").lower() == "true",
            use_ssl=os.getenv("EMAIL_USE_SSL", "false").lower() == "true",
            template_dir=os.getenv("EMAIL_TEMPLATE_DIR", "templates/email")
        )


class FileConfig(BaseConfig):
    """File handling configuration."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    upload_dir: str = Field(default="temp_uploads", description="Upload directory path")
    max_upload_size: int = Field(default=10485760, description="Maximum upload size in bytes")  # 10MB
    allowed_extensions: list[str] = Field(
        default=[".csv", ".xlsx", ".pdf", ".doc", ".docx", ".txt"],
        description="Allowed file extensions"
    )
    
    # Chunking configuration
    chunking_performance_profile: str = Field(
        default="balanced", description="Chunking performance profile"
    )
    chunking_use_adaptive: bool = Field(default=True, description="Use adaptive chunking")
    chunking_enable_caching: bool = Field(default=True, description="Enable chunking cache")
    chunking_batch_size: int = Field(default=16, ge=1, description="Chunking batch size")
    chunking_parallel_workers: int = Field(default=3, ge=1, description="Parallel workers for chunking")
    
    @field_validator('chunking_performance_profile')
    @classmethod
    def validate_chunking_profile(cls, v: str) -> str:
        """Validate chunking performance profile."""
        allowed_profiles = ['fast', 'balanced', 'quality']
        if v not in allowed_profiles:
            raise ValueError(f"Chunking profile must be one of: {allowed_profiles}")
        return v
    
    @classmethod
    def from_env(cls) -> "FileConfig":
        """Create file configuration from environment variables."""
        allowed_extensions = os.getenv("ALLOWED_FILE_EXTENSIONS", ".csv,.xlsx,.pdf,.doc,.docx,.txt")
        
        return cls(
            upload_dir=os.getenv("UPLOAD_DIR", "temp_uploads"),
            max_upload_size=int(os.getenv("MAX_UPLOAD_SIZE", "10485760")),
            allowed_extensions=allowed_extensions.split(","),
            chunking_performance_profile=os.getenv("CHUNKING_PERFORMANCE_PROFILE", "balanced"),
            chunking_use_adaptive=os.getenv("CHUNKING_USE_ADAPTIVE", "true").lower() == "true",
            chunking_enable_caching=os.getenv("CHUNKING_ENABLE_CACHING", "true").lower() == "true",
            chunking_batch_size=int(os.getenv("CHUNKING_BATCH_SIZE", "16")),
            chunking_parallel_workers=int(os.getenv("CHUNKING_PARALLEL_WORKERS", "3"))
        )


class VectorConfig(BaseConfig):
    """Vector database configuration."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    # Qdrant configuration
    qdrant_host: str = Field(default="localhost", description="Qdrant host")
    qdrant_port: int = Field(default=6333, ge=1, le=65535, description="Qdrant port")
    qdrant_api_key: Optional[str] = Field(default=None, description="Qdrant API key")
    qdrant_https: bool = Field(default=False, description="Use HTTPS for Qdrant")
    
    # mem0ai configuration
    mem0_api_key: str = Field(default="", description="mem0ai API key")
    mem0_endpoint: str = Field(default="", description="mem0ai endpoint")
    mem0_self_hosted: bool = Field(default=False, description="Use self-hosted mem0ai")
    mem0_default_ttl: int = Field(default=2592000, description="Default TTL for memories (seconds)")
    mem0_max_memories: int = Field(default=1000, description="Maximum number of memories")
    mem0_memory_threshold: float = Field(default=0.7, description="Memory similarity threshold")
    
    @classmethod
    def from_env(cls) -> "VectorConfig":
        """Create vector configuration from environment variables."""
        return cls(
            qdrant_host=os.getenv("QDRANT_HOST", "localhost"),
            qdrant_port=int(os.getenv("QDRANT_PORT", "6333")),
            qdrant_api_key=os.getenv("QDRANT_API_KEY"),
            qdrant_https=os.getenv("QDRANT_HTTPS", "false").lower() == "true",
            mem0_api_key=os.getenv("MEM0_API_KEY", ""),
            mem0_endpoint=os.getenv("MEM0_ENDPOINT", ""),
            mem0_self_hosted=os.getenv("MEM0_SELF_HOSTED", "false").lower() == "true",
            mem0_default_ttl=int(os.getenv("MEM0_DEFAULT_TTL", "2592000")),
            mem0_max_memories=int(os.getenv("MEM0_MAX_MEMORIES", "1000")),
            mem0_memory_threshold=float(os.getenv("MEM0_MEMORY_THRESHOLD", "0.7"))
        )


class AppConfig(BaseConfig):
    """Main application configuration."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    # Application metadata
    name: str = Field(default="Datagenius", description="Application name")
    version: str = Field(default="1.0.0", description="Application version")
    description: str = Field(
        default="AI-powered data analysis platform", description="Application description"
    )
    
    # Environment and debugging
    environment: EnvironmentConfig = Field(default_factory=EnvironmentConfig)
    debug: bool = Field(default=False, description="Enable debug mode")
    
    # Frontend configuration
    frontend_url: str = Field(default="http://localhost:5173", description="Frontend URL")
    
    # Component configurations
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    llm: LLMConfig = Field(default_factory=LLMConfig)
    email: EmailConfig = Field(default_factory=EmailConfig)
    files: FileConfig = Field(default_factory=FileConfig)
    vector: VectorConfig = Field(default_factory=VectorConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    
    # OAuth configuration
    google_client_id: str = Field(default="", description="Google OAuth client ID")
    google_client_secret: str = Field(default="", description="Google OAuth client secret")
    google_redirect_uri: str = Field(
        default="http://localhost:5173/auth/google/callback",
        description="Google OAuth redirect URI"
    )
    
    @computed_field
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.is_production
    
    @computed_field
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment.is_development
    
    @computed_field
    @property
    def config_summary(self) -> Dict[str, Any]:
        """Get a summary of the configuration (without sensitive data)."""
        return {
            "app": {
                "name": self.name,
                "version": self.version,
                "environment": self.environment.environment,
                "debug": self.debug
            },
            "database": {
                "url_scheme": self.database.url.split("://")[0] if self.database.url else "unknown",
                "pool_size": self.database.pool_size,
                "echo": self.database.echo
            },
            "security": {
                "jwt_algorithm": self.security.jwt_algorithm,
                "max_upload_size_mb": self.security.max_upload_size_mb,
                "rate_limiting_enabled": self.security.rate_limiting_enabled
            },
            "llm": {
                "default_provider": self.llm.default_provider,
                "enabled_providers": self.llm.enabled_providers,
                "enable_caching": self.llm.enable_caching
            }
        }
    
    @classmethod
    def from_env(cls) -> "AppConfig":
        """Create application configuration from environment variables."""
        return cls(
            name=os.getenv("APP_NAME", "Datagenius"),
            version=os.getenv("APP_VERSION", "1.0.0"),
            description=os.getenv("APP_DESCRIPTION", "AI-powered data analysis platform"),
            debug=os.getenv("DEBUG", "false").lower() == "true",
            frontend_url=os.getenv("FRONTEND_URL", "http://localhost:5173"),
            environment=EnvironmentConfig(
                environment=os.getenv("ENVIRONMENT", "development"),
                debug=os.getenv("DEBUG", "false").lower() == "true",
                testing=os.getenv("TESTING", "false").lower() == "true"
            ),
            database=DatabaseConfig.from_env(),
            redis=RedisConfig.from_env(),
            security=SecurityConfig.from_env(),
            llm=LLMConfig.from_env(),
            email=EmailConfig.from_env(),
            files=FileConfig.from_env(),
            vector=VectorConfig.from_env(),
            logging=LoggingConfig(
                level=os.getenv("LOG_LEVEL", "INFO"),
                file_path=os.getenv("LOG_FILE_PATH"),
                max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", "10485760")),
                backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
            ),
            monitoring=MonitoringConfig(
                enabled=os.getenv("MONITORING_ENABLED", "true").lower() == "true",
                performance_tracking=os.getenv("PERFORMANCE_TRACKING", "true").lower() == "true",
                error_tracking=os.getenv("ERROR_TRACKING", "true").lower() == "true"
            ),
            google_client_id=os.getenv("GOOGLE_CLIENT_ID", ""),
            google_client_secret=os.getenv("GOOGLE_CLIENT_SECRET", ""),
            google_redirect_uri=os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:5173/auth/google/callback")
        )
    
    @classmethod
    def from_yaml_file(cls, file_path: Union[str, Path]) -> "AppConfig":
        """Load configuration from YAML file."""
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        with open(path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        return cls(**data)
    
    def save_to_yaml(self, file_path: Union[str, Path]) -> None:
        """Save configuration to YAML file."""
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(path, 'w', encoding='utf-8') as f:
            yaml.dump(self.model_dump(), f, default_flow_style=False, indent=2)
