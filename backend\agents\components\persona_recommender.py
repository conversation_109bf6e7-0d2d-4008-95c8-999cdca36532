"""
Component for recommending suitable AI personas based on user queries.

This component uses a more sophisticated approach to match user needs with
appropriate personas, considering user history, preferences, and the
semantic meaning of their requests.
"""

import logging
import re
import sys
from typing import Dict, Any, List, Tuple, Optional
from collections import defaultdict
import time # Added for timestamp
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent
# Assuming PersonaManager provides access to available personas
from ..persona_manager import PersonaManager

logger = logging.getLogger(__name__)


class PersonaRecommenderComponent(AgentComponent):
    """
    Analyzes user requests and suggests relevant AI personas.
    """

    def __init__(self):
        """Initialize the PersonaRecommenderComponent."""
        super().__init__()
        self.persona_manager = PersonaManager() # Instantiate manager to access personas

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component. Load available persona details if needed.

        Args:
            config: Configuration dictionary for the component.
        """
        self.recommendation_threshold = config.get("recommendation_threshold", 0.7)
        self.max_recommendations = config.get("max_recommendations", 3)
        self.consider_user_history = config.get("consider_user_history", True)

        # Define persona capability mappings for better matching
        self.capability_keywords = {
            "data_analysis": [
                "analyze", "analysis", "data", "chart", "graph", "visualization",
                "statistics", "insights", "trends", "dataset", "csv", "excel",
                "numbers", "metrics", "dashboard", "report", "analytics"
            ],
            "marketing": [
                "marketing", "campaign", "advertisement", "promotion", "brand",
                "content", "social media", "strategy", "audience", "messaging",
                "copywriting", "seo", "email", "market research"
            ],
            "classification": [
                "classify", "categorize", "sort", "organize", "group", "label",
                "tag", "taxonomy", "classification", "categories", "clustering"
            ],
            "document_processing": [
                "document", "pdf", "text", "extract", "ocr", "scan", "parse",
                "summarize", "summary", "doc", "docx", "word"
            ]
        }

        # Store persona details for faster access
        self.persona_details = {}
        self.persona_capabilities = defaultdict(list)

        logger.info(f"PersonaRecommenderComponent '{self.name}' initialized with threshold {self.recommendation_threshold}")

        # Ensure persona manager is loaded
        if not self.persona_manager.is_loaded():
            await self.persona_manager.load_personas()
            logger.info("Persona manager loaded personas during component initialization.")

        # Preprocess persona information for faster matching
        await self._preprocess_personas()


    async def _preprocess_personas(self) -> None:
        """
        Preprocess persona information for faster matching.
        Extracts capabilities, keywords, and other relevant information.
        """
        available_personas = self.persona_manager.list_personas()

        for persona in available_personas:
            persona_id = persona.get('id')
            if not persona_id:
                continue

            # Store full persona details
            self.persona_details[persona_id] = persona

            # Extract and store capabilities
            capabilities = persona.get('capabilities', [])
            for capability in capabilities:
                self.persona_capabilities[capability].append(persona_id)

            logger.debug(f"Preprocessed persona {persona_id} with capabilities: {capabilities}")

        logger.info(f"Preprocessed {len(available_personas)} personas with {len(self.persona_capabilities)} capability types")

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process the user message to identify potential persona recommendations.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object, potentially with recommendations.
        """
        user_message = context.message or ""
        # user_id is UUID, _get_user_history expects int. This needs to be reconciled.
        # For now, I'll assume _get_user_history can handle string UUID or needs adjustment.
        # Or, if user_id from AgentProcessingContext is meant to be an int, that's a different issue.
        # Let's assume user_id from context is the correct one to use.
        user_id_str = str(context.user_id) # Use string representation for now
        conversation_id = str(context.conversation_id)

        logger.debug(f"PersonaRecommenderComponent processing message: {user_message}")

        # Get user history if available and enabled
        user_history: Dict[str, Any] = {}
        if self.consider_user_history and context.user_id: # Check if user_id is not None
            # Assuming _get_user_history needs the user_id as it was (int or str)
            # If AgentProcessingContext.user_id is UUID, _get_user_history might need adjustment
            # For now, passing it as is. If it's an int, it's fine. If UUID, _get_user_history needs to handle it.
            # The original code had user_id: int type hint for _get_user_history.
            # This is a potential type mismatch if context.user_id is UUID.
            # For the purpose of this refactor, I will assume _get_user_history can adapt or user_id is int.
            # If user_id is UUID, a mapping or different lookup might be needed.
            # Let's assume for now that the user_id from context is what _get_user_history expects.
            # The original code used context.get("user_id") which could be int.
            # AgentProcessingContext.user_id is UUID. This is a significant change.
            # For now, I will proceed assuming _get_user_history can handle a string version of the UUID.
            # Or, if an integer ID is truly needed, that implies a lookup service.
            # Given the current structure, I'll pass user_id_str.
            # The _get_user_history is a placeholder, so this detail is less critical for this refactor.
            user_history = await self._get_user_history(user_id_str) # Pass string UUID

        # Score personas based on the message content and user history
        persona_scores = await self._score_personas(user_message, user_history) # user_history is Dict[str, Any]

        # Filter personas based on threshold and sort by score
        recommended_personas = []
        for persona_id, score in sorted(persona_scores.items(), key=lambda x: x[1], reverse=True):
            if score >= self.recommendation_threshold and len(recommended_personas) < self.max_recommendations:
                persona_details = self.persona_details.get(persona_id)
                if persona_details:
                    recommended_personas.append((persona_details, score))

        if recommended_personas:
            # Format recommendations
            recommendations_text = "\nBased on your request, you might find these personas helpful:\n"
            for persona, score in recommended_personas:
                # Include confidence score (rounded to 2 decimal places) if in debug mode
                score_text = f" (confidence: {score:.2f})" if logger.level <= logging.DEBUG else ""
                recommendations_text += f"- **{persona['name']}**: {persona['description']}{score_text}\n"

            # Add usage instructions
            recommendations_text += "\nYou can select a persona by clicking on it or asking me to connect you with it."

            # Add to metadata
            context.metadata["persona_recommendations_text"] = recommendations_text # Use a more specific key
            context.metadata["recommended_persona_ids"] = [p[0]['id'] for p in recommended_personas]
            context.response = recommendations_text # Set the response for the user

            # Store recommendations in metadata for future reference by other components if needed
            if conversation_id: # conversation_id is now a string
                context.metadata["recent_persona_recommendations_details"] = { # More specific key
                    "text": recommendations_text,
                    "personas": [p[0]['id'] for p in recommended_personas],
                    "timestamp": time.time() # Use current time
                }

            logger.info(f"Recommended personas: {[p[0]['name'] for p in recommended_personas]}")
        else:
            logger.debug("No specific personas recommended based on the message.")
            generic_message = (
                "I don't have a specific persona recommendation based on your request. "
                "Could you provide more details about what you're trying to accomplish? "
                "For example, are you looking to analyze data, create marketing content, "
                "or organize information?"
            )
            context.metadata["persona_recommendations_text"] = generic_message
            context.response = generic_message # Set the response for the user

        return context

    async def _get_user_history(self, user_id: str) -> Dict[str, Any]: # Changed user_id type to str
        """
        Retrieve user history for better persona recommendations.

        Args:
            user_id: The ID of the user (as string, potentially UUID).

        Returns:
            Dictionary containing user history information.
        """
        # In a real implementation, this would query a database or service
        # to get the user's history, preferences, and past interactions
        # For now, we'll return an empty dictionary

        # Placeholder for future implementation
        return {
            "recent_personas": [],
            "preferred_personas": [],
            "interaction_counts": {}
        }

    async def _score_personas(self, message: str, user_history: Dict[str, Any]) -> Dict[str, float]:
        """
        Score personas based on the message content and user history.

        Args:
            message: The user's message.
            user_history: The user's history information.

        Returns:
            Dictionary mapping persona IDs to scores.
        """
        message_lower = message.lower()
        scores = defaultdict(float)

        # Score based on capability keywords
        for capability, keywords in self.capability_keywords.items():
            capability_score = 0
            for keyword in keywords:
                if keyword in message_lower:
                    # Add score based on keyword match
                    capability_score += 0.2
                    # Add extra score for exact matches or phrases
                    if re.search(r'\b' + re.escape(keyword) + r'\b', message_lower):
                        capability_score += 0.3

            # Normalize capability score
            if capability_score > 0:
                capability_score = min(capability_score, 1.0)

                # Add score to personas with this capability
                for persona_id in self.persona_capabilities.get(capability, []):
                    scores[persona_id] += capability_score

        # Score based on direct persona mentions
        for persona_id, persona in self.persona_details.items():
            persona_name = persona.get('name', '')
            if persona_name.lower() in message_lower:
                # Direct mention is a strong signal
                scores[persona_id] += 0.8

        # Consider user history if available
        if user_history:
            # Boost scores for recently used personas (but not too much)
            for persona_id in user_history.get('recent_personas', []):
                if persona_id in scores:
                    scores[persona_id] += 0.1

            # Boost scores for preferred personas
            for persona_id in user_history.get('preferred_personas', []):
                if persona_id in scores:
                    scores[persona_id] += 0.2

        # Normalize final scores to be between 0 and 1
        max_score = max(scores.values()) if scores else 1.0
        if max_score > 0:
            for persona_id in scores:
                scores[persona_id] /= max_score

        return dict(scores)

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        # Capabilities defined in the component's config or default here
        return self.config.get("capabilities", ["persona_recommendation"])
