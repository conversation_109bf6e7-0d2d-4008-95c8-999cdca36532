import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useMediaQuery } from '@/hooks/use-media-query';

interface ResponsiveChatLayoutProps {
  sidebar: React.ReactNode;
  chat: React.ReactNode;
  rightPanel?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  sidebarWidth?: number;
  rightPanelWidth?: number;
  className?: string;
}

export const ResponsiveChatLayout: React.FC<ResponsiveChatLayoutProps> = ({
  sidebar,
  chat,
  rightPanel,
  header,
  footer,
  sidebarWidth = 320,
  rightPanelWidth = 300,
  className = ''
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [rightPanelOpen, setRightPanelOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  
  const isMobile = useMediaQuery('(max-width: 768px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');

  // Auto-collapse sidebar on tablet
  useEffect(() => {
    if (isTablet && !isMobile) {
      setSidebarCollapsed(true);
    } else if (!isTablet) {
      setSidebarCollapsed(false);
    }
  }, [isTablet, isMobile]);

  // Close mobile panels when switching to desktop
  useEffect(() => {
    if (!isMobile) {
      setSidebarOpen(false);
      setRightPanelOpen(false);
    }
  }, [isMobile]);

  const effectiveSidebarWidth = sidebarCollapsed ? 64 : sidebarWidth;

  return (
    <div className={`flex flex-col h-screen bg-gray-50 ${className}`}>
      {/* Header */}
      {header && (
        <div className="flex-shrink-0 z-30 relative">
          {header}
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden relative">
        {/* Desktop Sidebar */}
        {!isMobile && (
          <motion.div
            className="flex-shrink-0 bg-white border-r border-gray-200 relative z-20"
            animate={{ width: effectiveSidebarWidth }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            {/* Sidebar Toggle Button */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute -right-3 top-4 z-30 h-6 w-6 p-0 bg-white border border-gray-200 rounded-full shadow-sm"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            >
              {sidebarCollapsed ? (
                <ChevronRight className="h-3 w-3" />
              ) : (
                <ChevronLeft className="h-3 w-3" />
              )}
            </Button>

            {/* Sidebar Content */}
            <div className="h-full overflow-hidden">
              {sidebar}
            </div>
          </motion.div>
        )}

        {/* Mobile Sidebar Overlay */}
        <AnimatePresence>
          {isMobile && sidebarOpen && (
            <>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black bg-opacity-50 z-40"
                onClick={() => setSidebarOpen(false)}
              />
              <motion.div
                initial={{ x: -sidebarWidth }}
                animate={{ x: 0 }}
                exit={{ x: -sidebarWidth }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                className="fixed left-0 top-0 h-full bg-white z-50 shadow-xl"
                style={{ width: sidebarWidth }}
              >
                <div className="flex items-center justify-between p-4 border-b">
                  <h2 className="font-semibold">Menu</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSidebarOpen(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="h-full overflow-auto pb-16">
                  {sidebar}
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Mobile Menu Button */}
          {isMobile && (
            <div className="flex items-center justify-between p-2 bg-white border-b">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-4 w-4 mr-2" />
                Menu
              </Button>
              {rightPanel && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setRightPanelOpen(true)}
                >
                  <Menu className="h-4 w-4 mr-2" />
                  Panel
                </Button>
              )}
            </div>
          )}

          {/* Chat Content */}
          <div className="flex-1 overflow-hidden">
            {chat}
          </div>
        </div>

        {/* Desktop Right Panel */}
        {!isMobile && rightPanel && (
          <motion.div
            className="flex-shrink-0 bg-white border-l border-gray-200"
            style={{ width: rightPanelWidth }}
          >
            {rightPanel}
          </motion.div>
        )}

        {/* Mobile Right Panel Overlay */}
        <AnimatePresence>
          {isMobile && rightPanelOpen && rightPanel && (
            <>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black bg-opacity-50 z-40"
                onClick={() => setRightPanelOpen(false)}
              />
              <motion.div
                initial={{ x: rightPanelWidth }}
                animate={{ x: 0 }}
                exit={{ x: rightPanelWidth }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                className="fixed right-0 top-0 h-full bg-white z-50 shadow-xl"
                style={{ width: rightPanelWidth }}
              >
                <div className="flex items-center justify-between p-4 border-b">
                  <h2 className="font-semibold">Panel</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setRightPanelOpen(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="h-full overflow-auto pb-16">
                  {rightPanel}
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>

      {/* Footer */}
      {footer && (
        <div className="flex-shrink-0 z-30">
          {footer}
        </div>
      )}
    </div>
  );
};

interface StickyFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const StickyFooter: React.FC<StickyFooterProps> = ({ children, className = '' }) => {
  return (
    <div className={`sticky bottom-0 bg-white border-t border-gray-200 z-20 ${className}`}>
      {children}
    </div>
  );
};

interface ChatContainerProps {
  children: React.ReactNode;
  className?: string;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({ children, className = '' }) => {
  return (
    <div className={`flex flex-col h-full ${className}`}>
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {children}
      </div>
    </div>
  );
};

interface CollapsibleSidebarProps {
  children: React.ReactNode;
  isCollapsed: boolean;
  onToggle: () => void;
  title?: string;
  className?: string;
}

export const CollapsibleSidebar: React.FC<CollapsibleSidebarProps> = ({
  children,
  isCollapsed,
  onToggle,
  title,
  className = ''
}) => {
  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {title && (
        <div className="flex items-center justify-between p-4 border-b">
          <AnimatePresence mode="wait">
            {!isCollapsed && (
              <motion.h2
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                className="font-semibold"
              >
                {title}
              </motion.h2>
            )}
          </AnimatePresence>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="ml-auto"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>
      )}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  );
};
