"""
Memory management system for Phase 1 optimization.

This module provides comprehensive memory monitoring and management including:
- Process memory monitoring
- Memory leak detection
- Resource cleanup automation
- Memory usage optimization
"""

import logging
import psutil
import gc
import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import deque

logger = logging.getLogger(__name__)


@dataclass
class MemorySnapshot:
    """Represents a memory usage snapshot."""
    timestamp: datetime
    rss_mb: float  # Resident Set Size
    vms_mb: float  # Virtual Memory Size
    percent: float  # Memory percentage
    available_system_mb: float
    operation: str


class MemoryManager:
    """
    Advanced memory management system for monitoring and optimization.
    """
    
    def __init__(self, max_memory_mb: int = 512, monitoring_interval: int = 60):
        """
        Initialize memory manager.
        
        Args:
            max_memory_mb: Maximum memory usage threshold in MB
            monitoring_interval: Monitoring interval in seconds
        """
        self.max_memory_mb = max_memory_mb
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.monitoring_interval = monitoring_interval
        
        # Memory tracking
        self.memory_snapshots: deque = deque(maxlen=1000)  # Keep last 1000 snapshots
        self.memory_alerts: List[Dict[str, Any]] = []
        self.cleanup_callbacks: List[Callable] = []
        
        # Monitoring state
        self.monitoring_active = False
        self.last_gc_time = time.time()
        self.gc_threshold = 300  # Run GC every 5 minutes
        
        # Performance metrics
        self.stats = {
            "peak_memory_mb": 0.0,
            "avg_memory_mb": 0.0,
            "memory_alerts_count": 0,
            "gc_runs": 0,
            "cleanup_runs": 0
        }
        
        logger.info(f"Initialized MemoryManager with {max_memory_mb}MB threshold")

    def get_current_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage information."""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            system_memory = psutil.virtual_memory()
            
            usage = {
                "rss_mb": memory_info.rss / 1024 / 1024,
                "vms_mb": memory_info.vms / 1024 / 1024,
                "percent": process.memory_percent(),
                "available_system_mb": system_memory.available / 1024 / 1024,
                "total_system_mb": system_memory.total / 1024 / 1024,
                "system_percent": system_memory.percent
            }
            
            # Update peak memory
            if usage["rss_mb"] > self.stats["peak_memory_mb"]:
                self.stats["peak_memory_mb"] = usage["rss_mb"]
            
            return usage
            
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return {"error": str(e)}

    @asynccontextmanager
    async def monitor_memory(self, operation_name: str):
        """
        Context manager for monitoring memory usage during operations.
        
        Args:
            operation_name: Name of the operation being monitored
        """
        initial_memory = self.get_current_memory_usage()
        start_time = datetime.now()
        
        # Create initial snapshot
        initial_snapshot = MemorySnapshot(
            timestamp=start_time,
            rss_mb=initial_memory.get('rss_mb', 0),
            vms_mb=initial_memory.get('vms_mb', 0),
            percent=initial_memory.get('percent', 0),
            available_system_mb=initial_memory.get('available_system_mb', 0),
            operation=f"{operation_name}_start"
        )
        self.memory_snapshots.append(initial_snapshot)
        
        try:
            logger.debug(f"Starting {operation_name} - Initial memory: {initial_memory.get('rss_mb', 0):.2f}MB")
            yield
            
        finally:
            final_memory = self.get_current_memory_usage()
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # Create final snapshot
            final_snapshot = MemorySnapshot(
                timestamp=end_time,
                rss_mb=final_memory.get('rss_mb', 0),
                vms_mb=final_memory.get('vms_mb', 0),
                percent=final_memory.get('percent', 0),
                available_system_mb=final_memory.get('available_system_mb', 0),
                operation=f"{operation_name}_end"
            )
            self.memory_snapshots.append(final_snapshot)
            
            # Calculate memory change
            memory_increase = final_memory.get('rss_mb', 0) - initial_memory.get('rss_mb', 0)
            
            logger.info(
                f"Completed {operation_name} - "
                f"Duration: {duration:.2f}s, "
                f"Memory change: {memory_increase:+.2f}MB, "
                f"Final memory: {final_memory.get('rss_mb', 0):.2f}MB"
            )
            
            # Check for memory threshold violations
            if final_memory.get('rss_mb', 0) > self.max_memory_mb:
                await self._handle_memory_threshold_violation(operation_name, final_memory)
            
            # Check for potential memory leaks
            if memory_increase > 50:  # More than 50MB increase
                await self._handle_potential_memory_leak(operation_name, memory_increase)

    async def _handle_memory_threshold_violation(self, operation: str, memory_info: Dict[str, Any]):
        """Handle memory threshold violations."""
        alert = {
            "timestamp": datetime.now(),
            "type": "memory_threshold_violation",
            "operation": operation,
            "memory_mb": memory_info.get('rss_mb', 0),
            "threshold_mb": self.max_memory_mb,
            "system_available_mb": memory_info.get('available_system_mb', 0)
        }
        
        self.memory_alerts.append(alert)
        self.stats["memory_alerts_count"] += 1
        
        logger.warning(
            f"Memory threshold violation in {operation}: "
            f"{memory_info.get('rss_mb', 0):.2f}MB > {self.max_memory_mb}MB"
        )
        
        # Trigger cleanup
        await self._trigger_memory_cleanup()

    async def _handle_potential_memory_leak(self, operation: str, memory_increase: float):
        """Handle potential memory leaks."""
        alert = {
            "timestamp": datetime.now(),
            "type": "potential_memory_leak",
            "operation": operation,
            "memory_increase_mb": memory_increase
        }
        
        self.memory_alerts.append(alert)
        
        logger.warning(
            f"Potential memory leak detected in {operation}: "
            f"{memory_increase:.2f}MB increase"
        )
        
        # Trigger aggressive cleanup
        await self._trigger_memory_cleanup(aggressive=True)

    async def _trigger_memory_cleanup(self, aggressive: bool = False):
        """Trigger memory cleanup procedures."""
        logger.info(f"Triggering memory cleanup (aggressive={aggressive})")
        
        try:
            # Run garbage collection
            if aggressive:
                # Force full garbage collection
                for generation in range(3):
                    collected = gc.collect()
                    logger.debug(f"GC generation {generation}: collected {collected} objects")
            else:
                collected = gc.collect()
                logger.debug(f"GC collected {collected} objects")
            
            self.stats["gc_runs"] += 1
            self.last_gc_time = time.time()
            
            # Run registered cleanup callbacks
            for callback in self.cleanup_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback()
                    else:
                        callback()
                except Exception as e:
                    logger.error(f"Error in cleanup callback: {e}")
            
            self.stats["cleanup_runs"] += 1
            
            # Log memory after cleanup
            post_cleanup_memory = self.get_current_memory_usage()
            logger.info(f"Memory after cleanup: {post_cleanup_memory.get('rss_mb', 0):.2f}MB")
            
        except Exception as e:
            logger.error(f"Error during memory cleanup: {e}")

    def register_cleanup_callback(self, callback: Callable):
        """Register a cleanup callback function."""
        self.cleanup_callbacks.append(callback)
        logger.debug(f"Registered cleanup callback: {callback.__name__}")

    def check_memory_threshold(self) -> bool:
        """
        Check if current memory usage is within acceptable limits.
        
        Returns:
            True if memory usage is acceptable, False otherwise
        """
        current_memory = self.get_current_memory_usage()
        current_mb = current_memory.get('rss_mb', 0)
        
        if current_mb > self.max_memory_mb:
            logger.warning(f"Memory threshold exceeded: {current_mb:.2f}MB > {self.max_memory_mb}MB")
            return False
        
        return True

    def get_memory_stats(self) -> Dict[str, Any]:
        """Get comprehensive memory statistics."""
        current_memory = self.get_current_memory_usage()
        
        # Calculate average memory from snapshots
        if self.memory_snapshots:
            avg_memory = sum(snapshot.rss_mb for snapshot in self.memory_snapshots) / len(self.memory_snapshots)
            self.stats["avg_memory_mb"] = avg_memory
        
        return {
            "current_memory": current_memory,
            "stats": self.stats,
            "recent_alerts": self.memory_alerts[-10:],  # Last 10 alerts
            "snapshots_count": len(self.memory_snapshots),
            "monitoring_active": self.monitoring_active
        }

    async def start_monitoring(self):
        """Start continuous memory monitoring."""
        if self.monitoring_active:
            logger.warning("Memory monitoring already active")
            return
        
        self.monitoring_active = True
        logger.info("Starting continuous memory monitoring")
        
        asyncio.create_task(self._monitoring_loop())

    async def stop_monitoring(self):
        """Stop continuous memory monitoring."""
        self.monitoring_active = False
        logger.info("Stopped continuous memory monitoring")

    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                # Take memory snapshot
                current_memory = self.get_current_memory_usage()
                snapshot = MemorySnapshot(
                    timestamp=datetime.now(),
                    rss_mb=current_memory.get('rss_mb', 0),
                    vms_mb=current_memory.get('vms_mb', 0),
                    percent=current_memory.get('percent', 0),
                    available_system_mb=current_memory.get('available_system_mb', 0),
                    operation="monitoring"
                )
                self.memory_snapshots.append(snapshot)
                
                # Check thresholds
                if not self.check_memory_threshold():
                    await self._handle_memory_threshold_violation("monitoring", current_memory)
                
                # Periodic garbage collection
                if time.time() - self.last_gc_time > self.gc_threshold:
                    await self._trigger_memory_cleanup()
                
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in memory monitoring loop: {e}")
                await asyncio.sleep(self.monitoring_interval)


# Global memory manager instance
memory_manager = MemoryManager()
