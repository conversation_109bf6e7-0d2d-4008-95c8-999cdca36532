import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UnifiedHeader } from '@/components/UnifiedHeader';
import { EnhancedRibbonToolbar } from '@/components/dashboard/mode/advanced/EnhancedRibbonToolbar';
import { QuickActionsBar, useQuickActionsBar } from '@/components/dashboard/QuickActionsBar';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    header: 'header',
    div: 'div',
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

// Mock auth context
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { email: '<EMAIL>' },
    logout: jest.fn(),
  }),
}));

// Mock cart icon
jest.mock('@/components/cart/CartIcon', () => ({
  CartIcon: () => <div data-testid="cart-icon">Cart</div>,
}));

describe('Optimized Dashboard Header', () => {
  const defaultProps = {
    title: 'Test Dashboard',
    description: 'Test dashboard description',
    isLoading: false,
    showDashboardControls: true,
    dashboardSelector: <div data-testid="dashboard-selector">Dashboard Selector</div>,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Header Layout Optimization', () => {
    it('should display primary actions in center section on desktop', () => {
      render(<UnifiedHeader {...defaultProps} />);
      
      // Primary actions should be visible on desktop
      expect(screen.getByRole('button', { name: /add widget/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /refresh/i })).toBeInTheDocument();
    });

    it('should show dashboard selector in left section', () => {
      render(<UnifiedHeader {...defaultProps} />);
      
      expect(screen.getByTestId('dashboard-selector')).toBeInTheDocument();
    });

    it('should display user menu in right section', () => {
      render(<UnifiedHeader {...defaultProps} />);

      // User menu should be present
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });

  describe('Simplified Interface', () => {
    it('should not contain dashboard control buttons', () => {
      render(<UnifiedHeader {...defaultProps} />);

      // Dashboard controls should not be present (moved to ribbon)
      expect(screen.queryByRole('button', { name: /add widget/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /settings/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /refresh/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /export/i })).not.toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('should show loading indicator when isLoading is true', () => {
      render(<UnifiedHeader {...defaultProps} isLoading={true} />);

      // Should show loading spinner in title area
      const loadingIcon = screen.getByRole('img', { hidden: true });
      expect(loadingIcon).toBeInTheDocument();
    });
  });



  describe('Quick Actions Bar', () => {
    const QuickActionsTestComponent = () => {
      const { isVisible, position, showQuickActions, hideQuickActions } = useQuickActionsBar();
      
      return (
        <>
          <button onClick={showQuickActions} data-testid="trigger">
            Show Quick Actions
          </button>
          <QuickActionsBar
            isVisible={isVisible}
            position={position}
            onAddWidget={jest.fn()}
            onDuplicateWidget={jest.fn()}
            onDeleteWidget={jest.fn()}
            onClose={hideQuickActions}
          />
        </>
      );
    };

    it('should show quick actions bar when triggered', async () => {
      const user = userEvent.setup();
      render(<QuickActionsTestComponent />);
      
      await user.click(screen.getByTestId('trigger'));
      
      expect(screen.getByRole('button', { name: /add widget/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /duplicate widget/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /delete widget/i })).toBeInTheDocument();
    });

    it('should hide quick actions bar on escape key', async () => {
      const user = userEvent.setup();
      render(<QuickActionsTestComponent />);
      
      await user.click(screen.getByTestId('trigger'));
      expect(screen.getByRole('button', { name: /add widget/i })).toBeInTheDocument();
      
      await user.keyboard('{Escape}');
      
      await waitFor(() => {
        expect(screen.queryByRole('button', { name: /add widget/i })).not.toBeInTheDocument();
      });
    });
  });

  describe('Enhanced Settings Menu', () => {
    it('should combine ribbon toggle with settings menu', async () => {
      const user = userEvent.setup();
      render(<UnifiedHeader {...defaultProps} />);

      // Settings button should be present
      const settingsButton = screen.getByRole('button', { name: /dashboard settings/i });
      expect(settingsButton).toBeInTheDocument();

      // Click settings to open menu
      await user.click(settingsButton);

      // Should show ribbon toggle option
      expect(screen.getByText(/hide ribbon toolbar/i)).toBeInTheDocument();
      expect(screen.getByText('Ctrl+R')).toBeInTheDocument();
    });

    it('should show dashboard templates option in settings', async () => {
      const user = userEvent.setup();
      render(<UnifiedHeader {...defaultProps} />);

      const settingsButton = screen.getByRole('button', { name: /dashboard settings/i });
      await user.click(settingsButton);

      // Should show templates option
      expect(screen.getByText('Dashboard Templates')).toBeInTheDocument();
    });

    it('should open templates dialog when clicked', async () => {
      const user = userEvent.setup();
      render(<UnifiedHeader {...defaultProps} />);

      const settingsButton = screen.getByRole('button', { name: /dashboard settings/i });
      await user.click(settingsButton);

      const templatesOption = screen.getByText('Dashboard Templates');
      await user.click(templatesOption);

      // Templates dialog should open
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(screen.getByText('Choose from pre-built templates')).toBeInTheDocument();
      });
    });

    it('should toggle ribbon when option is clicked', async () => {
      const user = userEvent.setup();
      render(<UnifiedHeader {...defaultProps} />);

      const settingsButton = screen.getByRole('button', { name: /dashboard settings/i });
      await user.click(settingsButton);

      const ribbonToggle = screen.getByText(/hide ribbon toolbar/i);
      await user.click(ribbonToggle);

      // Ribbon should be hidden (tested by checking if it's not rendered)
      // This would need to be verified by checking the ribbon toolbar visibility
    });

    it('should maintain keyboard shortcut functionality', async () => {
      const user = userEvent.setup();
      render(<UnifiedHeader {...defaultProps} />);

      // Ctrl+R should still work for ribbon toggle
      await user.keyboard('{Control>}r{/Control}');

      // Ribbon state should change (implementation would verify this)
    });
  });

  describe('Dashboard Templates Integration', () => {
    it('should call onTemplateSelect when template is chosen', async () => {
      const mockTemplateSelect = jest.fn();
      const user = userEvent.setup();

      render(<UnifiedHeader {...defaultProps} onTemplateSelect={mockTemplateSelect} />);

      // Open settings menu
      const settingsButton = screen.getByRole('button', { name: /dashboard settings/i });
      await user.click(settingsButton);

      // Click templates option
      const templatesOption = screen.getByText('Dashboard Templates');
      await user.click(templatesOption);

      // Wait for dialog to open and simulate template selection
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      // This would require the templates component to be rendered and interacted with
      // The actual template selection would be tested in the DashboardTemplates component tests
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and keyboard navigation', () => {
      render(<UnifiedHeader {...defaultProps} />);

      // All buttons should be accessible
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeInTheDocument();
      });

      // Tooltips should provide additional context
      expect(screen.getByRole('button', { name: /add widget/i })).toBeInTheDocument();
    });

    it('should show keyboard shortcuts in tooltips', async () => {
      const user = userEvent.setup();
      render(<UnifiedHeader {...defaultProps} />);

      // Hover over add widget button to show tooltip
      await user.hover(screen.getByRole('button', { name: /add widget/i }));

      await waitFor(() => {
        expect(screen.getByText('Ctrl+N')).toBeInTheDocument();
      });
    });

    it('should show enhanced tooltip for settings button', async () => {
      const user = userEvent.setup();
      render(<UnifiedHeader {...defaultProps} />);

      // Hover over settings button
      const settingsButton = screen.getByRole('button', { name: /dashboard settings/i });
      await user.hover(settingsButton);

      await waitFor(() => {
        expect(screen.getByText('Dashboard settings and options')).toBeInTheDocument();
        expect(screen.getByText('Includes ribbon toggle (Ctrl+R)')).toBeInTheDocument();
      });
    });
  });
});
