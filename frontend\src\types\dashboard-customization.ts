/**
 * TypeScript types for Dashboard Customization API
 * Matches backend Pydantic models for type safety
 */

// Enums
export enum VisualizationType {
  CHART = 'chart',
  TABLE = 'table',
  KPI = 'kpi',
  GAUGE = 'gauge',
  HEATMAP = 'heatmap',
  NETWORK = 'network',
  TREE = 'tree',
  MAP = 'map',
  TEXT = 'text',
  IMAGE = 'image',
}

// Base configuration types
export interface LayoutConfig {
  columns: number;
  rows: number;
  grid_gap: number;
  responsive: boolean;
  breakpoints?: Record<string, { columns: number; rows: number }>;
}

export interface SectionCustomization {
  background_color?: string;
  border_color?: string;
  border_width?: number;
  border_radius?: number;
  padding?: number[];
  margin?: number[];
  header_style?: Record<string, any>;
  border_style?: 'solid' | 'dashed' | 'dotted' | 'none';
  shadow?: boolean;
  animation?: string;
}

export interface PositionConfig {
  x: number;
  y: number;
  w: number;
  h: number;
}

export interface DataSourceConfig {
  dataSourceId: string;
  query?: string;
  filters?: Record<string, any>;
  aggregation?: string;
  groupBy?: string[];
  sortBy?: string;
  limit?: number;
}

// System-level data source (from Data Integration page)
export interface SystemDataSource {
  id: string;
  name: string;
  type: 'file' | 'database' | 'api' | 'mcp';
  description?: string;
  is_active: boolean;
  metadata: any;
  source_metadata?: any;
  user_id: number;
  created_at: string;
  updated_at: string;
}

// Dashboard-level data source assignment (references system data sources)
export interface DashboardDataSourceAssignment {
  id: string;
  dashboard_id: string;
  system_data_source_id: string;
  alias?: string; // Optional alias for this data source within the dashboard
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // Populated from system data source
  system_data_source?: SystemDataSource;
}

// Flattened data source for display purposes (combines assignment and system data source info)
export interface DashboardDataSource {
  id: string;
  name: string;
  type: string;
  description?: string;
  isActive: boolean;
  lastUpdated: string;
  recordCount?: number;
  refreshInterval: number;
  alias?: string;
  connectionConfig?: Record<string, any>;
}

export interface DashboardDataSourceAssignmentCreate {
  system_data_source_id: string;
  alias?: string;
  is_active?: boolean;
}

export interface DashboardDataSourceAssignmentUpdate {
  alias?: string;
  is_active?: boolean;
}

// Widget data source reference (references dashboard data source assignment)
export interface WidgetDataSourceConfig {
  dashboard_data_source_assignment_id: string;
  query?: string;
  filters?: Record<string, any>;
  aggregation?: string;
  groupBy?: string[];
  sortBy?: string;
  limit?: number;
}

export interface WidgetCustomization {
  colors?: string[];
  theme?: string;
  showLegend?: boolean;
  showTooltip?: boolean;
  animation?: boolean;
  fontSize?: number;
  fontFamily?: string;
}

// Section types
export interface SectionCreate {
  dashboard_id?: string; // Optional in frontend, will be added by store
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  layout_config?: LayoutConfig;
  customization?: SectionCustomization;
  data_source_id?: string;
  template?: string;
}

export interface SectionUpdate {
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
  layout_config?: LayoutConfig;
  customization?: SectionCustomization;
  data_source_id?: string;
  position?: number;
  is_active?: boolean;
}

export interface SectionResponse {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  layout_config?: LayoutConfig;
  customization?: SectionCustomization;
  data_source_id?: string;
  position: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  widget_count: number;
}

// Widget types
export interface WidgetCreate {
  section_id: string;
  title: string;
  widget_type: VisualizationType;
  data_config?: WidgetDataSourceConfig;
  visualization_config?: Record<string, any>;
  position_config: PositionConfig;
  customization?: WidgetCustomization;
  refresh_interval?: number;
}

export interface WidgetUpdate {
  title?: string;
  widget_type?: VisualizationType;
  data_config?: WidgetDataSourceConfig;
  visualization_config?: Record<string, any>;
  position_config?: PositionConfig;
  customization?: WidgetCustomization;
  refresh_interval?: number;
  is_active?: boolean;
}

export interface WidgetResponse {
  id: string;
  section_id: string;
  title: string;
  widget_type: string;
  data_config?: WidgetDataSourceConfig;
  visualization_config?: Record<string, any>;
  position_config: PositionConfig;
  customization?: WidgetCustomization;
  refresh_interval: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface WidgetMoveRequest {
  widget_id: string;
  target_section_id: string;
  position_config: PositionConfig;
}

// Insights types
export interface WidgetInsightResponse {
  summary: Record<string, any>;
  ai_insights?: string;
  trends?: Record<string, any>;
  confidence: number;
}

// Dashboard management types
export interface Dashboard {
  id: string;
  name: string;
  description?: string;
  is_default: boolean;
  is_public: boolean;
  layout_config: Record<string, any>;
  theme_config: Record<string, any>;
  refresh_interval: number;
  tags?: string[];
  data_source_assignments?: DashboardDataSourceAssignment[];
  created_at: string;
  updated_at: string;
  section_count: number;
  widget_count: number;
}

export interface DashboardCreate {
  name: string;
  description?: string;
  is_default?: boolean;
  is_public?: boolean;
  layout_config?: Record<string, any>;
  theme_config?: Record<string, any>;
  refresh_interval?: number;
  tags?: string[];
  data_source_assignments?: DashboardDataSourceAssignmentCreate[];
}

export interface DashboardUpdate {
  name?: string;
  description?: string;
  is_default?: boolean;
  is_public?: boolean;
  layout_config?: Record<string, any>;
  theme_config?: Record<string, any>;
  refresh_interval?: number;
  tags?: string[];
  data_source_assignments?: DashboardDataSourceAssignmentUpdate[];
}

export interface DashboardResponse extends Dashboard {}

// Layout response type
export interface DashboardLayoutResponse {
  dashboard: DashboardResponse;
  sections: SectionResponse[];
  widgets: WidgetResponse[];
  total_sections: number;
  total_widgets: number;
  last_updated: string;
}

// Error types
export interface DashboardError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// Hook return types
export interface UseDashboardLayoutResult {
  layout: DashboardLayoutResponse | null;
  isLoading: boolean;
  error: DashboardError | null;
  refetch: () => Promise<void>;
}

export interface UseDashboardManagementResult {
  dashboards: Dashboard[];
  activeDashboard: Dashboard | null;
  isLoading: boolean;
  error: DashboardError | null;
  createDashboard: (dashboard: DashboardCreate) => Promise<Dashboard>;
  updateDashboard: (id: string, dashboard: DashboardUpdate) => Promise<Dashboard>;
  deleteDashboard: (id: string) => Promise<void>;
  setActiveDashboard: (id: string) => Promise<void>;
  refetch: () => Promise<void>;
}
