import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

// Types for the unified data access tool
export interface DataQuery {
  source_id: string;
  query_type: 'sql' | 'filter' | 'aggregate' | 'search';
  query: string;
  parameters?: Record<string, any>;
  limit?: number;
  offset?: number;
  cache_ttl?: number;
}

export interface DataResult {
  success: boolean;
  data?: Record<string, any>[];
  columns?: string[];
  total_rows: number;
  execution_time: number;
  cached: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

export interface DataSource {
  id: string;
  name: string;
  type: string;
  description?: string;
  is_active: boolean;
  sync_status?: string;
  last_sync?: string;
}

export interface SchemaColumn {
  name: string;
  type: string;
  nullable: boolean;
  unique_values?: number;
}

export interface SchemaInfo {
  success: boolean;
  columns?: SchemaColumn[];
  row_count?: number;
  memory_usage?: number;
  error?: string;
}

export interface DataSummary {
  success: boolean;
  total_rows: number;
  columns: number;
  numeric_columns: number;
  statistics: Record<string, {
    mean: number;
    median: number;
    std: number;
    min: number;
    max: number;
    null_count: number;
  }>;
  error?: string;
}

export interface QueryTemplate {
  description: string;
  query_type: string;
  query: string;
  parameters?: Record<string, any>;
  example: string;
}

export const useUnifiedDataAccess = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const handleError = useCallback((error: any, operation: string) => {
    const message = error.response?.data?.detail || error.message || `Failed to ${operation}`;
    setError(message);
    toast({
      title: "Data Access Error",
      description: message,
      variant: "destructive",
    });
  }, [toast]);

  const getDataSources = useCallback(async (): Promise<DataSource[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/data-access/sources', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const sources = await response.json();
      return sources;
    } catch (error) {
      handleError(error, 'fetch data sources');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  const executeQuery = useCallback(async (query: DataQuery): Promise<DataResult | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/data-access/query', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(query),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success && result.error) {
        throw new Error(result.error);
      }

      return result;
    } catch (error) {
      handleError(error, 'execute query');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  const testConnection = useCallback(async (sourceId: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/data-access/sources/${sourceId}/test`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast({
          title: "Connection Test",
          description: `Connection successful (${result.response_time?.toFixed(2)}s)`,
        });
      } else {
        throw new Error(result.error || 'Connection test failed');
      }

      return result.success;
    } catch (error) {
      handleError(error, 'test connection');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [handleError, toast]);

  const getSchemaInfo = useCallback(async (sourceId: string): Promise<SchemaInfo | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/data-access/sources/${sourceId}/schema`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const schema = await response.json();
      return schema;
    } catch (error) {
      handleError(error, 'get schema information');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  const getSampleData = useCallback(async (sourceId: string, limit: number = 10): Promise<DataResult | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/data-access/sources/${sourceId}/sample?limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      handleError(error, 'get sample data');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  const getDataSummary = useCallback(async (sourceId: string): Promise<DataSummary | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/data-access/sources/${sourceId}/summary`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const summary = await response.json();
      return summary;
    } catch (error) {
      handleError(error, 'get data summary');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  const getQueryTemplates = useCallback(async (): Promise<Record<string, Record<string, QueryTemplate>>> => {
    try {
      const response = await fetch('/api/data-access/query-builder/templates', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      handleError(error, 'get query templates');
      return {};
    }
  }, [handleError]);

  const clearCache = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/data-access/cache/clear', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      toast({
        title: "Cache Cleared",
        description: "Data access cache has been cleared successfully",
      });

      return true;
    } catch (error) {
      handleError(error, 'clear cache');
      return false;
    }
  }, [handleError, toast]);

  return {
    isLoading,
    error,
    getDataSources,
    executeQuery,
    testConnection,
    getSchemaInfo,
    getSampleData,
    getDataSummary,
    getQueryTemplates,
    clearCache,
  };
};
