"""
Enhanced Base Component Architecture for Phase 2 Refactoring.

This module implements the standardized base component architecture as specified
in the refactor.md Phase 2 requirements, providing consistent interfaces,
error handling, and metrics collection across all agent components.
"""

import logging
import time
import psutil
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ComponentMetrics:
    """Metrics collection for component performance monitoring."""
    
    name: str
    success_count: int = 0
    error_count: int = 0
    total_execution_time: float = 0.0
    last_execution_time: Optional[float] = None
    provider_successes: Dict[str, int] = field(default_factory=dict)
    provider_failures: Dict[str, int] = field(default_factory=dict)
    error_types: Dict[str, int] = field(default_factory=dict)
    
    def record_success(self, provider: Optional[str] = None, execution_time: float = 0.0):
        """Record a successful operation."""
        self.success_count += 1
        self.total_execution_time += execution_time
        self.last_execution_time = execution_time
        
        if provider:
            self.provider_successes[provider] = self.provider_successes.get(provider, 0) + 1
    
    def record_error(self, error: Exception, provider: Optional[str] = None):
        """Record an error occurrence."""
        self.error_count += 1
        error_type = type(error).__name__
        self.error_types[error_type] = self.error_types.get(error_type, 0) + 1
        
        if provider:
            self.provider_failures[provider] = self.provider_failures.get(provider, 0) + 1
    
    def record_provider_failure(self, provider: str):
        """Record a provider-specific failure."""
        self.provider_failures[provider] = self.provider_failures.get(provider, 0) + 1
    
    def get_success_rate(self) -> float:
        """Calculate success rate."""
        total = self.success_count + self.error_count
        return self.success_count / total if total > 0 else 0.0
    
    def get_average_execution_time(self) -> float:
        """Calculate average execution time."""
        return self.total_execution_time / self.success_count if self.success_count > 0 else 0.0


@dataclass
class AgentContext:
    """Enhanced context object for agent processing."""
    
    # Core fields
    user_id: str
    message: str = ""
    status: str = "pending"
    
    # Data fields
    fields: Dict[str, Any] = field(default_factory=dict)
    errors: List[Dict[str, str]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Processing state
    processing_history: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def get_field(self, key: str, default: Any = None) -> Any:
        """Get a field value with optional default."""
        return self.fields.get(key, default)
    
    def set_field(self, key: str, value: Any) -> None:
        """Set a field value."""
        self.fields[key] = value
        self.updated_at = datetime.now()
    
    def has_field(self, key: str) -> bool:
        """Check if a field exists."""
        return key in self.fields
    
    def add_error(self, component: str, error_message: str) -> None:
        """Add an error to the context."""
        self.errors.append({
            "component": component,
            "message": error_message,
            "timestamp": datetime.now().isoformat()
        })
        self.updated_at = datetime.now()
    
    def set_status(self, status: str) -> None:
        """Set the processing status."""
        self.status = status
        self.updated_at = datetime.now()
    
    def add_processing_step(self, step: str) -> None:
        """Add a processing step to history."""
        self.processing_history.append(f"{datetime.now().isoformat()}: {step}")
        self.updated_at = datetime.now()


class BaseAgentComponent(ABC):
    """
    Enhanced base class for all agent components implementing Phase 2 architecture.
    
    Provides standardized error handling, validation, metrics collection,
    and consistent interfaces across all components.
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        Initialize the base component.
        
        Args:
            name: Component name identifier
            config: Configuration dictionary
        """
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"agent.component.{name}")
        self.metrics = ComponentMetrics(name)
        self._initialized = False
        
        # Memory monitoring
        self.memory_threshold_mb = config.get("memory_threshold_mb", 512)
        
        self.logger.info(f"Initialized component: {name}")
    
    async def initialize(self) -> None:
        """Initialize the component with any required setup."""
        if self._initialized:
            return
        
        try:
            await self._initialize_component()
            self._initialized = True
            self.logger.info(f"Component {self.name} initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize component {self.name}: {e}")
            raise
    
    @abstractmethod
    async def _initialize_component(self) -> None:
        """Component-specific initialization logic."""
        pass
    
    @abstractmethod
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process the agent context and return updated context.
        
        Args:
            context: AgentContext object containing request data and state
            
        Returns:
            Updated AgentContext object
        """
        pass
    
    @abstractmethod
    def get_required_fields(self) -> List[str]:
        """
        Return list of required context fields.
        
        Returns:
            List of required field names
        """
        pass
    
    async def execute_with_monitoring(self, context: AgentContext) -> AgentContext:
        """
        Execute the component with full monitoring and error handling.
        
        Args:
            context: AgentContext to process
            
        Returns:
            Updated AgentContext
        """
        start_time = time.time()
        
        try:
            # Ensure component is initialized
            await self.initialize()
            
            # Validate input
            await self.validate_input(context)
            
            # Add processing step
            context.add_processing_step(f"Starting {self.name}")
            
            # Monitor memory usage
            async with self._monitor_memory(f"{self.name}_execution"):
                # Execute the component
                result_context = await self.process(context)
            
            # Record success metrics
            execution_time = time.time() - start_time
            self.metrics.record_success(execution_time=execution_time)
            
            # Add completion step
            result_context.add_processing_step(f"Completed {self.name}")
            
            return result_context
            
        except Exception as e:
            # Handle error gracefully
            execution_time = time.time() - start_time
            return await self.handle_error(e, context, execution_time)
    
    async def handle_error(self, error: Exception, context: AgentContext, execution_time: float = 0.0) -> AgentContext:
        """
        Handle errors gracefully and update context.
        
        Args:
            error: The exception that occurred
            context: Current AgentContext
            execution_time: Time spent before error occurred
            
        Returns:
            Updated AgentContext with error information
        """
        self.logger.error(f"Error in {self.name}: {str(error)}", exc_info=True)
        self.metrics.record_error(error)
        
        context.add_error(self.name, str(error))
        context.set_status("error")
        context.add_processing_step(f"Error in {self.name}: {str(error)}")
        
        return context
    
    async def validate_input(self, context: AgentContext) -> bool:
        """
        Validate input context.
        
        Args:
            context: AgentContext to validate
            
        Returns:
            True if valid
            
        Raises:
            ValueError: If required fields are missing
        """
        required_fields = self.get_required_fields()
        missing_fields = []
        
        for field in required_fields:
            if not context.has_field(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")
        
        return True

    @asynccontextmanager
    async def _monitor_memory(self, operation_name: str):
        """
        Monitor memory usage during operation.

        Args:
            operation_name: Name of the operation being monitored
        """
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024

        try:
            yield
        finally:
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_increase = current_memory - initial_memory

            if current_memory > self.memory_threshold_mb:
                self.logger.warning(
                    f"Memory usage exceeded threshold in {operation_name}: "
                    f"{current_memory:.2f}MB (increase: {memory_increase:.2f}MB)"
                )
            else:
                self.logger.debug(
                    f"Memory usage for {operation_name}: "
                    f"{current_memory:.2f}MB (increase: {memory_increase:.2f}MB)"
                )

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value by key.

        Args:
            key: Configuration key
            default: Default value if key is not found

        Returns:
            Configuration value
        """
        return self.config.get(key, default)

    def get_metrics(self) -> ComponentMetrics:
        """
        Get component metrics.

        Returns:
            ComponentMetrics object
        """
        return self.metrics

    def get_health_status(self) -> Dict[str, Any]:
        """
        Get component health status.

        Returns:
            Health status dictionary
        """
        return {
            "name": self.name,
            "initialized": self._initialized,
            "success_rate": self.metrics.get_success_rate(),
            "total_operations": self.metrics.success_count + self.metrics.error_count,
            "average_execution_time": self.metrics.get_average_execution_time(),
            "memory_threshold_mb": self.memory_threshold_mb
        }
