import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

interface IntegrationSettingsProps {
  settings?: Record<string, any>; // Adjust type based on actual settings structure
  onUpdate: (data: Record<string, any>) => void;
}

const IntegrationSettings: React.FC<IntegrationSettingsProps> = ({ settings = {}, onUpdate }) => {
  const [localSettings, setLocalSettings] = useState(settings);

  useEffect(() => {
    // Update local state if props change from parent
    setLocalSettings(settings);
  }, [settings]);

  const handleChange = (section: string, key: string, value: string) => {
    const updatedSection = { ...localSettings[section], [key]: value };
    const updatedSettings = { ...localSettings, [section]: updatedSection };
    setLocalSettings(updatedSettings);
    onUpdate(updatedSettings); // Notify parent immediately on change
  };

  // Helper to get nested value safely
  const getValue = (section: string, key: string) => {
    return localSettings?.[section]?.[key] || '';
  };


  return (
    <Card>
      <CardHeader>
        <CardTitle>Integration Settings</CardTitle>
        <CardDescription>
          Configure third-party integrations for your application.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Data Sources Section */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Data Sources</h3>
          <p className="text-sm text-muted-foreground">
            Connect databases, APIs, or other data sources.
          </p>
          <Separator />
          <div className="space-y-4 pt-2">
            {/* Example: Database Connection */}
            <div>
              <Label htmlFor="db-connection-string">Database Connection String</Label>
              <Input
                id="db-connection-string"
                placeholder="e.g., postgresql://user:password@host:port/database"
                value={getValue('dataSources', 'dbConnectionString')}
                onChange={(e) => handleChange('dataSources', 'dbConnectionString', e.target.value)}
              />
            </div>
            {/* Example: API Endpoint */}
            <div>
              <Label htmlFor="api-endpoint">External API Endpoint</Label>
              <Input
                id="api-endpoint"
                placeholder="e.g., https://api.example.com/v1"
                value={getValue('dataSources', 'apiEndpoint')}
                onChange={(e) => handleChange('dataSources', 'apiEndpoint', e.target.value)}
              />
            </div>
            {/* Save button is handled by the parent AdminSettings component */}
            {/* <Button>Save Data Source Settings</Button> */}
          </div>
        </div>

        {/* Authentication Providers Section */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Authentication Providers</h3>
          <p className="text-sm text-muted-foreground">
            Enable OAuth providers like Google, GitHub, etc.
          </p>
          <Separator />
          <div className="space-y-4 pt-2">
            {/* Example: Google OAuth */}
            <div>
              <Label htmlFor="google-client-id">Google Client ID</Label>
              <Input
                id="google-client-id"
                placeholder="Enter Google Client ID"
                value={getValue('authProviders', 'googleClientId')}
                onChange={(e) => handleChange('authProviders', 'googleClientId', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="google-client-secret">Google Client Secret</Label>
              <Input
                id="google-client-secret"
                type="password"
                placeholder="Enter Google Client Secret"
                value={getValue('authProviders', 'googleClientSecret')}
                onChange={(e) => handleChange('authProviders', 'googleClientSecret', e.target.value)}
              />
            </div>
            {/* <Button>Save Authentication Settings</Button> */}
          </div>
        </div>

        {/* Analytics Services Section */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Analytics Services</h3>
          <p className="text-sm text-muted-foreground">
            Integrate with services like Google Analytics, Mixpanel, etc.
          </p>
          <Separator />
          <div className="space-y-4 pt-2">
            {/* Example: Google Analytics */}
            <div>
              <Label htmlFor="ga-tracking-id">Google Analytics Tracking ID</Label>
              <Input
                id="ga-tracking-id"
                placeholder="e.g., UA-XXXXX-Y"
                value={getValue('analytics', 'gaTrackingId')}
                onChange={(e) => handleChange('analytics', 'gaTrackingId', e.target.value)}
              />
            </div>
            {/* <Button>Save Analytics Settings</Button> */}
          </div>
        </div>

        {/* Storage Services Section */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Storage Services</h3>
          <p className="text-sm text-muted-foreground">
            Connect to cloud storage like AWS S3, Google Cloud Storage.
          </p>
          <Separator />
          <div className="space-y-4 pt-2">
            {/* Example: AWS S3 */}
            <div>
              <Label htmlFor="s3-bucket-name">S3 Bucket Name</Label>
              <Input
                id="s3-bucket-name"
                placeholder="Enter S3 Bucket Name"
                value={getValue('storage', 's3BucketName')}
                onChange={(e) => handleChange('storage', 's3BucketName', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="s3-access-key">S3 Access Key ID</Label>
              <Input
                id="s3-access-key"
                placeholder="Enter S3 Access Key ID"
                value={getValue('storage', 's3AccessKeyId')}
                onChange={(e) => handleChange('storage', 's3AccessKeyId', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="s3-secret-key">S3 Secret Access Key</Label>
              <Input
                id="s3-secret-key"
                type="password"
                placeholder="Enter S3 Secret Access Key"
                value={getValue('storage', 's3SecretAccessKey')}
                onChange={(e) => handleChange('storage', 's3SecretAccessKey', e.target.value)}
              />
            </div>
            {/* <Button>Save Storage Settings</Button> */}
          </div>
        </div>

        {/* Payment Gateways Section */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Payment Gateways</h3>
          <p className="text-sm text-muted-foreground">
            Integrate with Stripe, PayPal, etc.
          </p>
          <Separator />
          <div className="space-y-4 pt-2">
            {/* Example: Stripe */}
            <div>
              <Label htmlFor="stripe-publishable-key">Stripe Publishable Key</Label>
              <Input
                id="stripe-publishable-key"
                placeholder="pk_test_..."
                value={getValue('payments', 'stripePublishableKey')}
                onChange={(e) => handleChange('payments', 'stripePublishableKey', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="stripe-secret-key">Stripe Secret Key</Label>
              <Input
                id="stripe-secret-key"
                type="password"
                placeholder="sk_test_..."
                value={getValue('payments', 'stripeSecretKey')}
                onChange={(e) => handleChange('payments', 'stripeSecretKey', e.target.value)}
              />
            </div>
            {/* <Button>Save Payment Settings</Button> */}
          </div>
        </div>

        {/* Notification Services Section */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium">Notification Services</h3>
          <p className="text-sm text-muted-foreground">
            Connect email services (SendGrid, Mailgun) or SMS (Twilio).
          </p>
          <Separator />
          <div className="space-y-4 pt-2">
            {/* Example: SendGrid */}
            <div>
              <Label htmlFor="sendgrid-api-key">SendGrid API Key</Label>
              <Input
                id="sendgrid-api-key"
                type="password"
                placeholder="Enter SendGrid API Key"
                value={getValue('notifications', 'sendgridApiKey')}
                onChange={(e) => handleChange('notifications', 'sendgridApiKey', e.target.value)}
              />
            </div>
            {/* Example: Twilio */}
            <div>
              <Label htmlFor="twilio-account-sid">Twilio Account SID</Label>
              <Input
                id="twilio-account-sid"
                placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                value={getValue('notifications', 'twilioAccountSid')}
                onChange={(e) => handleChange('notifications', 'twilioAccountSid', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="twilio-auth-token">Twilio Auth Token</Label>
              <Input
                id="twilio-auth-token"
                type="password"
                placeholder="Enter Twilio Auth Token"
                value={getValue('notifications', 'twilioAuthToken')}
                onChange={(e) => handleChange('notifications', 'twilioAuthToken', e.target.value)}
              />
            </div>
            {/* <Button>Save Notification Settings</Button> */}
          </div>
        </div>
      </CardContent>
      {/* CardFooter is handled by the parent AdminSettings component */}
    </Card>
  );
};

export default IntegrationSettings;
