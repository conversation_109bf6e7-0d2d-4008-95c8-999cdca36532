"""Add dashboard data source assignments table

Revision ID: add_dashboard_data_source_assignments
Revises: 
Create Date: 2025-06-27 17:50:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_dashboard_data_source_assignments'
down_revision = None  # Set this to the latest revision
branch_labels = None
depends_on = None


def upgrade():
    """Create dashboard_data_source_assignments table."""
    op.create_table('dashboard_data_source_assignments',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('dashboard_id', sa.String(length=36), nullable=False),
        sa.Column('system_data_source_id', sa.String(length=36), nullable=False),
        sa.Column('alias', sa.String(length=255), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['dashboard_id'], ['dashboards.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['system_data_source_id'], ['data_sources.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_dashboard_data_source_assignments_id'), 'dashboard_data_source_assignments', ['id'], unique=False)


def downgrade():
    """Drop dashboard_data_source_assignments table."""
    op.drop_index(op.f('ix_dashboard_data_source_assignments_id'), table_name='dashboard_data_source_assignments')
    op.drop_table('dashboard_data_source_assignments')
