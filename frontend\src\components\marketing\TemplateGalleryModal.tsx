import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import {
  Search,
  Filter,
  TrendingUp,
  Users,
  Mail,
  PenTool,
  Zap,
  FileText,
  Target,
  BarChart3,
  Star,
  Clock,
  ArrowRight,
  Loader2
} from 'lucide-react';

interface TemplateGalleryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTemplate: (template: MarketingTemplate) => void;
}

interface MarketingTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  industry: string;
  businessSize: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  timeEstimate: string;
  rating: number;
  usageCount: number;
  actionType: string;
  preview: string;
  tags: string[];
  icon: React.ComponentType<{ className?: string }>;
}

// Template data will be loaded from API
const getIconForCategory = (category: string) => {
  const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
    strategy: TrendingUp,
    social: Users,
    email: Mail,
    content: PenTool,
    campaigns: Target,
    research: BarChart3
  };
  return iconMap[category] || FileText;
};
  {
    id: 'social-media-calendar',
    name: 'Social Media Content Calendar',
    description: '30-day social media content plan with post ideas',
    category: 'social',
    industry: 'all',
    businessSize: 'all',
    difficulty: 'beginner',
    timeEstimate: '10-15 min',
    rating: 4.6,
    usageCount: 2156,
    actionType: 'social_media_content',
    preview: 'Daily post ideas, hashtags, and engagement strategies',
    tags: ['social media', 'content calendar', 'engagement'],
    icon: Users
  },
  {
    id: 'email-welcome-series',
    name: 'Welcome Email Series',
    description: '5-part welcome email sequence for new subscribers',
    category: 'email',
    industry: 'all',
    businessSize: 'all',
    difficulty: 'beginner',
    timeEstimate: '15-20 min',
    rating: 4.7,
    usageCount: 1834,
    actionType: 'email_marketing',
    preview: 'Onboarding sequence with value delivery and engagement',
    tags: ['email marketing', 'onboarding', 'automation'],
    icon: Mail
  },
  {
    id: 'blog-content-strategy',
    name: 'Blog Content Strategy',
    description: 'Content marketing strategy with blog post ideas',
    category: 'content',
    industry: 'all',
    businessSize: 'all',
    difficulty: 'intermediate',
    timeEstimate: '15-20 min',
    rating: 4.5,
    usageCount: 1456,
    actionType: 'blog_content',
    preview: 'Content pillars, topic clusters, and editorial calendar',
    tags: ['content marketing', 'SEO', 'blogging'],
    icon: PenTool
  },
  {
    id: 'holiday-campaign',
    name: 'Holiday Marketing Campaign',
    description: 'Seasonal marketing campaign template',
    category: 'campaigns',
    industry: 'retail',
    businessSize: 'all',
    difficulty: 'intermediate',
    timeEstimate: '20-25 min',
    rating: 4.4,
    usageCount: 987,
    actionType: 'campaign_strategy',
    preview: 'Multi-channel holiday campaign with promotions and content',
    tags: ['seasonal', 'campaigns', 'promotions'],
    icon: Target


export const TemplateGalleryModal: React.FC<TemplateGalleryModalProps> = ({
  isOpen,
  onClose,
  onSelectTemplate
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedIndustry, setSelectedIndustry] = useState('all');
  const [sortBy, setSortBy] = useState('popular');
  const [templates, setTemplates] = useState<MarketingTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [industries, setIndustries] = useState<any[]>([]);
  const { toast } = useToast();

  // Load templates from API
  const loadTemplates = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (selectedIndustry !== 'all') params.append('industry', selectedIndustry);
      if (searchTerm) params.append('search_term', searchTerm);

      const response = await fetch(`/api/templates?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        // Convert API data to component format
        const templatesWithIcons = data.templates.map((template: any) => ({
          ...template,
          businessSize: template.business_size,
          timeEstimate: template.time_estimate,
          usageCount: template.usage_count,
          actionType: template.action_type,
          icon: getIconForCategory(template.category)
        }));

        // Sort templates
        const sortedTemplates = sortTemplates(templatesWithIcons, sortBy);
        setTemplates(sortedTemplates);
      } else {
        throw new Error(data.error || 'Failed to load templates');
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      toast({
        title: "Error loading templates",
        description: "Failed to load template gallery. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Load categories and metadata
  const loadCategories = async () => {
    try {
      const response = await fetch('/api/templates/categories/list', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const categoryOptions = [
            { value: 'all', label: 'All Categories' },
            ...Object.keys(data.categories).map(cat => ({
              value: cat,
              label: data.categories[cat].name
            }))
          ];
          setCategories(categoryOptions);

          const industryOptions = [
            { value: 'all', label: 'All Industries' },
            ...data.metadata.available_industries.map((industry: string) => ({
              value: industry,
              label: industry.charAt(0).toUpperCase() + industry.slice(1)
            }))
          ];
          setIndustries(industryOptions);
        }
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  // Sort templates function
  const sortTemplates = (templateList: MarketingTemplate[], sortMethod: string) => {
    return [...templateList].sort((a, b) => {
      switch (sortMethod) {
        case 'popular':
          return b.usageCount - a.usageCount;
        case 'rating':
          return b.rating - a.rating;
        case 'newest':
          return b.name.localeCompare(a.name);
        default:
          return 0;
      }
    });
  };

  // Handle template selection with usage tracking
  const handleTemplateSelect = async (template: MarketingTemplate) => {
    try {
      // Track template usage
      await fetch(`/api/templates/${template.id}/use`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
    } catch (error) {
      console.error('Error tracking template usage:', error);
    }

    onSelectTemplate(template);
    onClose();
  };

  // Load data when modal opens or filters change
  useEffect(() => {
    if (isOpen) {
      loadCategories();
    }
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      loadTemplates();
    }
  }, [isOpen, selectedCategory, selectedIndustry, searchTerm, sortBy]);

  const filteredTemplates = templates;
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesIndustry = selectedIndustry === 'all' || template.industry === selectedIndustry || template.industry === 'all';
    
    return matchesSearch && matchesCategory && matchesIndustry;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.usageCount - a.usageCount;
      case 'rating':
        return b.rating - a.rating;
      case 'newest':
        return b.name.localeCompare(a.name);
      default:
        return 0;
    }
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleTemplateSelect = (template: MarketingTemplate) => {
    onSelectTemplate(template);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-brand-600" />
            Marketing Template Gallery
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex gap-2">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Industry" />
                </SelectTrigger>
                <SelectContent>
                  {industries.map(industry => (
                    <SelectItem key={industry.value} value={industry.value}>
                      {industry.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popular">Popular</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                  <SelectItem value="newest">Newest</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Results Count */}
          <div className="text-sm text-gray-600">
            Showing {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''}
          </div>

          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-brand-600" />
              <span className="ml-2 text-gray-600">Loading templates...</span>
            </div>
          )}

          {/* Templates Grid */}
          {!loading && (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredTemplates.map((template) => {
              const TemplateIcon = template.icon;
              
              return (
                <Card 
                  key={template.id}
                  className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 hover:border-brand-300"
                  onClick={() => handleTemplateSelect(template)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <TemplateIcon className="h-5 w-5 text-brand-600" />
                        <Badge className={getDifficultyColor(template.difficulty)}>
                          {template.difficulty}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        {template.rating}
                      </div>
                    </div>
                    
                    <CardTitle className="text-lg leading-tight">
                      {template.name}
                    </CardTitle>
                    <CardDescription className="text-sm">
                      {template.description}
                    </CardDescription>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <p className="text-xs text-gray-600 italic">
                        "{template.preview}"
                      </p>
                      
                      <div className="flex flex-wrap gap-1">
                        {template.tags.slice(0, 3).map(tag => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {template.timeEstimate}
                        </div>
                        <div>
                          {template.usageCount.toLocaleString()} uses
                        </div>
                      </div>
                      
                      <Button size="sm" className="w-full">
                        Use Template
                        <ArrowRight className="h-3 w-3 ml-1" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
          )}

          {/* No Results */}
          {!loading && filteredTemplates.length === 0 && (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No templates found
              </h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your search criteria or browse all templates.
              </p>
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('all');
                  setSelectedIndustry('all');
                }}
              >
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
