"""
Interactive Chart Data MCP Tool

This module provides an MCP-compatible tool for extracting structured chart data
from PandasAI operations to enable interactive React-based visualizations.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Union, Tuple
import re
import json
from datetime import datetime

from .base import BaseMCPTool

logger = logging.getLogger(__name__)

class InteractiveChartDataTool(BaseMCPTool):
    """MCP tool for extracting interactive chart data from DataFrames and queries."""
    
    def __init__(self):
        super().__init__(
            name="interactive_chart_data",
            description="Extract structured chart data from DataFrames for interactive visualizations",
            input_schema={
                "type": "object",
                "properties": {
                    "dataframe_data": {
                        "type": "object",
                        "description": "DataFrame data in dict format (from df.to_dict())"
                    },
                    "query": {
                        "type": "string", 
                        "description": "Original query that generated the visualization"
                    },
                    "chart_type_hint": {
                        "type": "string",
                        "enum": ["bar", "line", "pie", "scatter", "area", "heatmap", "auto"],
                        "description": "Hint for chart type detection"
                    },
                    "file_path": {
                        "type": "string",
                        "description": "Path to the original data file"
                    }
                },
                "required": ["query"]
            },
            output_schema={
                "type": "object",
                "properties": {
                    "tool_name": {"type": "string"},
                    "status": {"type": "string"},
                    "isError": {"type": "boolean"},
                    "interactive_chart": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "chart_type": {"type": "string"},
                            "title": {"type": "string"},
                            "description": {"type": "string"},
                            "data": {
                                "type": "object",
                                "properties": {
                                    "chart_data": {"type": "array"},
                                    "columns": {"type": "array"},
                                    "metadata": {"type": "object"}
                                }
                            },
                            "config": {"type": "object"}
                        }
                    }
                }
            }
        )

    def _transform_label_for_readability(self, label: str) -> str:
        """Transform labels for better readability, especially UUIDs and long strings."""
        if not isinstance(label, str):
            return str(label)

        # Check if it's a UUID pattern
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        if re.match(uuid_pattern, label.lower()):
            # For UUIDs, show first 8 characters + "..."
            return f"{label[:8]}..."

        # Check if it's a long string (more than 20 characters)
        if len(label) > 20:
            # Truncate long strings
            return f"{label[:17]}..."

        # Check for common patterns that can be simplified
        # Email addresses - show just the username part
        if '@' in label and '.' in label:
            return label.split('@')[0]

        # File paths - show just the filename
        if '/' in label or '\\' in label:
            return label.split('/')[-1].split('\\')[-1]

        # URLs - show just the domain
        if label.startswith(('http://', 'https://')):
            try:
                from urllib.parse import urlparse
                parsed = urlparse(label)
                return parsed.netloc or label[:20] + "..."
            except:
                return label[:20] + "..."

        return label

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the interactive chart data extraction."""
        try:
            query = kwargs.get("query", "")
            dataframe_data = kwargs.get("dataframe_data")
            chart_type_hint = kwargs.get("chart_type_hint", "auto")
            file_path = kwargs.get("file_path")
            
            logger.info(f"Extracting interactive chart data for query: {query}")
            
            # Load DataFrame if file_path provided
            df = None
            if file_path:
                logger.info(f"Loading DataFrame from file: {file_path}")
                df = self._load_dataframe(file_path)
            elif dataframe_data:
                logger.info(f"Creating DataFrame from provided data with {len(dataframe_data)} records")
                df = pd.DataFrame(dataframe_data)

            if df is None or df.empty:
                logger.error("No valid DataFrame data could be created")
                return {
                    "tool_name": self.name,
                    "status": "error",
                    "isError": True,
                    "error": "No valid DataFrame data provided"
                }

            logger.info(f"Successfully created DataFrame with shape: {df.shape}")
            logger.info(f"DataFrame columns: {list(df.columns)}")
            
            # Analyze query to determine chart type and data requirements
            logger.info("Analyzing query for chart type and configuration")
            chart_analysis = self._analyze_query_for_chart(query, df, chart_type_hint)
            logger.info(f"Chart analysis result: {chart_analysis}")

            # Extract and format chart data
            logger.info("Extracting chart data from DataFrame")
            chart_data = self._extract_chart_data(df, chart_analysis)
            logger.info(f"Extracted chart data with {len(chart_data.get('chart_data', []))} data points")

            # Create interactive chart response
            interactive_chart = {
                "type": "interactive_chart",
                "chart_type": chart_analysis["chart_type"],
                "title": chart_analysis["title"],
                "description": chart_analysis["description"],
                "data": chart_data,
                "config": chart_analysis["config"]
            }

            logger.info(f"Successfully created interactive chart: {interactive_chart['chart_type']}")
            return {
                "tool_name": self.name,
                "status": "success",
                "isError": False,
                "interactive_chart": interactive_chart
            }
            
        except Exception as e:
            logger.error(f"Error extracting interactive chart data: {e}", exc_info=True)
            return {
                "tool_name": self.name,
                "status": "error", 
                "isError": True,
                "error": str(e)
            }
    
    def _load_dataframe(self, file_path: str) -> Optional[pd.DataFrame]:
        """Load DataFrame from file path."""
        try:
            import os
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return None
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.csv':
                return pd.read_csv(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                return pd.read_excel(file_path)
            elif file_ext == '.json':
                return pd.read_json(file_path)
            else:
                logger.warning(f"Unsupported file extension: {file_ext}")
                return None
                
        except Exception as e:
            logger.error(f"Error loading DataFrame: {e}")
            return None
    
    def _analyze_query_for_chart(self, query: str, df: pd.DataFrame, hint: str) -> Dict[str, Any]:
        """Analyze query to determine chart type and configuration."""
        query_lower = query.lower()
        
        # Chart type detection
        chart_type = self._detect_chart_type(query_lower, df, hint)
        
        # Extract title and description
        title = self._extract_title_from_query(query)
        description = f"Interactive {chart_type} chart generated from data analysis"
        
        # Determine axes and grouping
        axes_config = self._determine_axes_configuration(query_lower, df, chart_type)
        
        return {
            "chart_type": chart_type,
            "title": title,
            "description": description,
            "config": {
                "responsive": True,
                "interactive": True,
                "axes": axes_config,
                "animation": True
            }
        }
    
    def _detect_chart_type(self, query: str, df: pd.DataFrame, hint: str) -> str:
        """Detect appropriate chart type from query and data."""
        if hint != "auto":
            return hint
        
        # Chart type keywords
        if any(word in query for word in ["pie", "proportion", "percentage", "share"]):
            return "pie"
        elif any(word in query for word in ["line", "trend", "over time", "timeline", "time series"]):
            return "line"
        elif any(word in query for word in ["scatter", "correlation", "relationship", "vs"]):
            return "scatter"
        elif any(word in query for word in ["area", "filled"]):
            return "area"
        elif any(word in query for word in ["heatmap", "heat map", "correlation matrix"]):
            return "heatmap"
        else:
            # Default to bar chart
            return "bar"
    
    def _extract_title_from_query(self, query: str) -> str:
        """Extract a meaningful title from the query."""
        # Clean up the query to create a title
        title = query.strip()
        
        # Remove common prefixes
        prefixes_to_remove = [
            "create a", "make a", "show me", "display", "plot", "chart", "graph",
            "visualize", "generate", "create", "make", "show"
        ]
        
        for prefix in prefixes_to_remove:
            if title.lower().startswith(prefix):
                title = title[len(prefix):].strip()
        
        # Capitalize first letter
        if title:
            title = title[0].upper() + title[1:]
        else:
            title = "Data Visualization"
        
        return title

    def _determine_axes_configuration(self, query: str, df: pd.DataFrame, chart_type: str) -> Dict[str, Any]:
        """Determine axes configuration based on query and data."""
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_columns = df.select_dtypes(include=['object', 'category']).columns.tolist()
        datetime_columns = df.select_dtypes(include=['datetime64']).columns.tolist()

        # Default configuration
        config = {
            "x_axis": None,
            "y_axes": [],
            "color_column": None,
            "size_column": None
        }

        # Determine X-axis
        if datetime_columns:
            config["x_axis"] = datetime_columns[0]
        elif categorical_columns:
            config["x_axis"] = categorical_columns[0]
        elif numeric_columns:
            config["x_axis"] = numeric_columns[0]

        # Determine Y-axis based on chart type
        if chart_type == "pie":
            # For pie charts, we need categorical and numeric data
            if categorical_columns and numeric_columns:
                config["x_axis"] = categorical_columns[0]  # Categories
                config["y_axes"] = [numeric_columns[0]]    # Values
        elif chart_type == "scatter":
            # For scatter plots, we need at least 2 numeric columns
            if len(numeric_columns) >= 2:
                config["x_axis"] = numeric_columns[0]
                config["y_axes"] = [numeric_columns[1]]
                if len(numeric_columns) >= 3:
                    config["size_column"] = numeric_columns[2]
        else:
            # For bar, line, area charts
            if numeric_columns:
                config["y_axes"] = numeric_columns[:3]  # Limit to 3 series

        # Determine color grouping
        if len(categorical_columns) > 1:
            config["color_column"] = categorical_columns[1] if config["x_axis"] != categorical_columns[1] else categorical_columns[0]

        return config

    def _extract_chart_data(self, df: pd.DataFrame, chart_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and format chart data for Recharts compatibility."""
        chart_type = chart_analysis["chart_type"]
        axes_config = chart_analysis["config"]["axes"]

        try:
            if chart_type == "pie":
                logger.info(f"🥧 EXTRACTING PIE CHART DATA")
                logger.info(f"🥧 Axes config: {axes_config}")
                logger.info(f"🥧 DataFrame shape: {df.shape}")
                logger.info(f"🥧 DataFrame columns: {df.columns.tolist()}")
                result = self._extract_pie_chart_data(df, axes_config)
                logger.info(f"🥧 PIE CHART EXTRACTION SUCCESS: {len(result.get('chart_data', []))} items")
                return result
            elif chart_type == "heatmap":
                return self._extract_heatmap_data(df, axes_config)
            else:
                return self._extract_standard_chart_data(df, axes_config, chart_type)
        except Exception as e:
            logger.error(f"❌ Error extracting chart data for {chart_type}: {e}")
            logger.error(f"❌ Axes config was: {axes_config}")
            logger.error(f"❌ DataFrame info: shape={df.shape}, columns={df.columns.tolist()}")
            import traceback
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            # Fallback to simple data extraction
            logger.warning(f"❌ FALLING BACK TO RAW DATA for {chart_type}")
            return self._extract_fallback_data(df)

    def _extract_pie_chart_data(self, df: pd.DataFrame, axes_config: Dict[str, Any]) -> Dict[str, Any]:
        """Extract data for pie charts."""
        logger.info(f"🥧 PIE CHART DATA EXTRACTION START")
        x_col = axes_config.get("x_axis")
        y_col = axes_config.get("y_axes", [None])[0]
        logger.info(f"🥧 Initial x_col: {x_col}, y_col: {y_col}")

        if not x_col or not y_col:
            logger.info(f"🥧 Missing axes, using fallback column detection")
            # Fallback: use first categorical and numeric columns
            cat_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            num_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            logger.info(f"🥧 Available categorical columns: {cat_cols}")
            logger.info(f"🥧 Available numeric columns: {num_cols}")

            if cat_cols and num_cols:
                x_col = cat_cols[0]
                y_col = num_cols[0]
                logger.info(f"🥧 Selected x_col: {x_col}, y_col: {y_col}")
            else:
                logger.error(f"🥧 No suitable columns found for pie chart")
                raise ValueError("No suitable columns for pie chart")

        # Group by category and sum values
        logger.info(f"🥧 Grouping by {x_col} and summing {y_col}")
        logger.info(f"🥧 Sample data before grouping: {df[[x_col, y_col]].head()}")
        grouped = df.groupby(x_col)[y_col].sum().reset_index()
        logger.info(f"🥧 Grouped data shape: {grouped.shape}")
        logger.info(f"🥧 Grouped data: {grouped}")

        chart_data = []
        for _, row in grouped.iterrows():
            original_name = str(row[x_col])
            readable_name = self._transform_label_for_readability(original_name)
            chart_data.append({
                "name": readable_name,
                "value": float(row[y_col]),
                "original_name": original_name  # Keep original for tooltips/details
            })

        logger.info(f"🥧 Final chart_data: {chart_data}")

        return {
            "chart_data": chart_data,
            "columns": [x_col, y_col],
            "metadata": {
                "x_axis": x_col,
                "y_axes": [y_col],
                "data_type": "categorical_numeric",
                "total_records": len(chart_data)
            }
        }

    def _extract_heatmap_data(self, df: pd.DataFrame, axes_config: Dict[str, Any]) -> Dict[str, Any]:
        """Extract data for heatmap visualization."""
        # For heatmaps, we typically want correlation data or pivot table data
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()

        if len(numeric_columns) >= 2:
            # Create correlation matrix
            corr_matrix = df[numeric_columns].corr()

            chart_data = []
            for i, row_name in enumerate(corr_matrix.index):
                for j, col_name in enumerate(corr_matrix.columns):
                    chart_data.append({
                        "x": col_name,
                        "y": row_name,
                        "value": float(corr_matrix.iloc[i, j])
                    })

            return {
                "chart_data": chart_data,
                "columns": numeric_columns,
                "metadata": {
                    "data_type": "correlation_matrix",
                    "x_categories": corr_matrix.columns.tolist(),
                    "y_categories": corr_matrix.index.tolist(),
                    "total_records": len(chart_data)
                }
            }
        else:
            raise ValueError("Insufficient numeric columns for heatmap")

    def _extract_standard_chart_data(self, df: pd.DataFrame, axes_config: Dict[str, Any], chart_type: str) -> Dict[str, Any]:
        """Extract data for standard charts (bar, line, area, scatter)."""
        x_col = axes_config.get("x_axis")
        y_cols = axes_config.get("y_axes", [])
        color_col = axes_config.get("color_column")

        if not x_col:
            # Use first available column as x-axis
            x_col = df.columns[0]

        if not y_cols:
            # Use numeric columns as y-axes
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            y_cols = numeric_cols[:3] if numeric_cols else [df.columns[1]] if len(df.columns) > 1 else []

        # Prepare data
        chart_data = []

        # Group by x-axis if needed
        if color_col and color_col in df.columns:
            # Group by both x and color columns
            grouped = df.groupby([x_col, color_col])[y_cols].mean().reset_index()

            # Pivot to get series for each color group
            for y_col in y_cols:
                pivot_data = grouped.pivot(index=x_col, columns=color_col, values=y_col).fillna(0)

                for x_val in pivot_data.index:
                    original_name = str(x_val)
                    readable_name = self._transform_label_for_readability(original_name)
                    row_data = {"name": readable_name, "original_name": original_name}
                    for color_val in pivot_data.columns:
                        row_data[f"{y_col}_{color_val}"] = float(pivot_data.loc[x_val, color_val])
                    chart_data.append(row_data)
        else:
            # Simple grouping by x-axis
            if df[x_col].dtype in ['object', 'category']:
                # Categorical x-axis: group and aggregate
                grouped = df.groupby(x_col)[y_cols].mean().reset_index()
            else:
                # Numeric x-axis: use data as-is or sample if too large
                grouped = df[[x_col] + y_cols].copy()
                if len(grouped) > 1000:  # Limit data points for performance
                    grouped = grouped.sample(n=1000).sort_values(x_col)

            for _, row in grouped.iterrows():
                original_name = str(row[x_col])
                readable_name = self._transform_label_for_readability(original_name)
                row_data = {"name": readable_name, "original_name": original_name}
                for y_col in y_cols:
                    row_data[y_col] = float(row[y_col]) if pd.notna(row[y_col]) else 0
                chart_data.append(row_data)

        return {
            "chart_data": chart_data,
            "columns": [x_col] + y_cols + ([color_col] if color_col else []),
            "metadata": {
                "x_axis": x_col,
                "y_axes": y_cols,
                "color_column": color_col,
                "data_type": "standard_chart",
                "total_records": len(chart_data)
            }
        }

    def _extract_fallback_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Fallback data extraction when other methods fail."""
        # Simple approach: use first few columns
        columns = df.columns.tolist()[:5]  # Limit columns
        data = df[columns].head(100).to_dict('records')  # Limit rows

        # Convert to chart-friendly format
        chart_data = []
        for i, row in enumerate(data):
            row_data = {"name": f"Row {i+1}"}
            for col in columns:
                if pd.api.types.is_numeric_dtype(df[col]):
                    row_data[col] = float(row[col]) if pd.notna(row[col]) else 0
                else:
                    row_data[col] = str(row[col])
            chart_data.append(row_data)

        return {
            "chart_data": chart_data,
            "columns": columns,
            "metadata": {
                "data_type": "fallback",
                "total_records": len(chart_data),
                "note": "Fallback data extraction used"
            }
        }
