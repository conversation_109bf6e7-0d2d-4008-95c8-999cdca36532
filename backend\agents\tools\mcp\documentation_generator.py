"""
Documentation Generation System for MCP Tools.

This module provides automatic documentation generation for all MCP tools
including OpenAPI schema generation, usage examples, and interactive
tool testing interfaces.
"""

import logging
import json
import inspect
from typing import Dict, Any, List, Optional, Type, get_type_hints
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import importlib
import pkgutil

logger = logging.getLogger(__name__)


class DocumentationType(Enum):
    """Types of documentation to generate."""
    OPENAPI_SCHEMA = "openapi_schema"
    USAGE_EXAMPLES = "usage_examples"
    API_REFERENCE = "api_reference"
    DEVELOPER_GUIDE = "developer_guide"
    INTERACTIVE_DOCS = "interactive_docs"
    TOOL_CATALOG = "tool_catalog"


@dataclass
class ToolDocumentation:
    """Container for tool documentation."""
    tool_name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    examples: List[Dict[str, Any]]
    parameters: Dict[str, Any]
    agent_compatibility: List[str]
    performance_metrics: Dict[str, Any]
    dependencies: List[str]
    version: str
    last_updated: str


@dataclass
class DocumentationConfig:
    """Configuration for documentation generation."""
    output_directory: str = "docs/mcp_tools"
    include_examples: bool = True
    include_schemas: bool = True
    include_performance: bool = True
    generate_interactive: bool = True
    format_types: List[str] = None
    
    def __post_init__(self):
        if self.format_types is None:
            self.format_types = ["html", "markdown", "json"]


class DocumentationGenerator:
    """
    Comprehensive documentation generator for MCP tools.
    
    Features:
    - Automatic OpenAPI schema generation
    - Usage examples with code snippets
    - Interactive documentation interfaces
    - Tool discovery and cataloging
    - Performance benchmarking documentation
    - Agent compatibility matrices
    - Developer guides and tutorials
    """
    
    def __init__(self, config: DocumentationConfig = None):
        """Initialize the documentation generator."""
        self.config = config or DocumentationConfig()
        self.tools_registry = {}
        self.schemas = {}
        self.examples = {}
        
        # Ensure output directory exists
        Path(self.config.output_directory).mkdir(parents=True, exist_ok=True)

    def discover_tools(self, tools_package: str = "backend.agents.tools.mcp") -> Dict[str, Any]:
        """
        Automatically discover all MCP tools in the package.
        
        Args:
            tools_package: Package path containing MCP tools
            
        Returns:
            Dictionary of discovered tools and their metadata
        """
        discovered_tools = {}
        
        try:
            # Import the tools package
            package = importlib.import_module(tools_package)
            package_path = package.__path__
            
            # Iterate through all modules in the package
            for importer, modname, ispkg in pkgutil.iter_modules(package_path):
                if ispkg or modname.startswith('_'):
                    continue
                
                try:
                    # Import the module
                    module_name = f"{tools_package}.{modname}"
                    module = importlib.import_module(module_name)
                    
                    # Look for tool classes
                    for name, obj in inspect.getmembers(module, inspect.isclass):
                        if self._is_mcp_tool(obj):
                            tool_info = self._extract_tool_info(obj, module)
                            discovered_tools[name] = tool_info
                            
                except Exception as e:
                    logger.warning(f"Failed to import module {modname}: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Failed to discover tools in package {tools_package}: {e}")
        
        self.tools_registry = discovered_tools
        return discovered_tools

    def _is_mcp_tool(self, cls: Type) -> bool:
        """Check if a class is an MCP tool."""
        # Check for common MCP tool patterns
        if hasattr(cls, 'execute') and hasattr(cls, 'input_schema'):
            return True
        
        # Check for inheritance from base tool classes
        base_names = [base.__name__ for base in cls.__mro__]
        mcp_indicators = ['MCPTool', 'EnhancedMCPTool', 'BaseTool']
        
        return any(indicator in base_names for indicator in mcp_indicators)

    def _extract_tool_info(self, tool_class: Type, module) -> Dict[str, Any]:
        """Extract comprehensive information about a tool."""
        tool_info = {
            'class_name': tool_class.__name__,
            'module': module.__name__,
            'description': self._get_tool_description(tool_class),
            'input_schema': self._get_input_schema(tool_class),
            'output_schema': self._get_output_schema(tool_class),
            'methods': self._get_tool_methods(tool_class),
            'dependencies': self._get_tool_dependencies(module),
            'agent_compatibility': self._get_agent_compatibility(tool_class),
            'examples': self._generate_tool_examples(tool_class),
            'performance_notes': self._get_performance_notes(tool_class)
        }
        
        return tool_info

    def _get_tool_description(self, tool_class: Type) -> str:
        """Extract tool description from docstring or attributes."""
        # Try docstring first
        if tool_class.__doc__:
            return tool_class.__doc__.strip().split('\n')[0]
        
        # Try description attribute
        if hasattr(tool_class, 'description'):
            return getattr(tool_class, 'description')
        
        # Try name attribute
        if hasattr(tool_class, 'name'):
            return f"Tool: {getattr(tool_class, 'name')}"
        
        return f"MCP Tool: {tool_class.__name__}"

    def _get_input_schema(self, tool_class: Type) -> Dict[str, Any]:
        """Extract input schema from tool class."""
        if hasattr(tool_class, 'input_schema'):
            schema = getattr(tool_class, 'input_schema')
            if callable(schema):
                try:
                    return schema()
                except:
                    return {}
            return schema if isinstance(schema, dict) else {}
        
        # Try to infer from execute method signature
        if hasattr(tool_class, 'execute'):
            try:
                sig = inspect.signature(tool_class.execute)
                type_hints = get_type_hints(tool_class.execute)
                
                schema = {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
                
                for param_name, param in sig.parameters.items():
                    if param_name in ['self', 'cls']:
                        continue
                    
                    param_schema = {"type": "string"}  # Default
                    
                    if param_name in type_hints:
                        param_type = type_hints[param_name]
                        param_schema = self._type_to_schema(param_type)
                    
                    schema["properties"][param_name] = param_schema
                    
                    if param.default == inspect.Parameter.empty:
                        schema["required"].append(param_name)
                
                return schema
                
            except Exception as e:
                logger.warning(f"Failed to infer schema for {tool_class.__name__}: {e}")
        
        return {}

    def _get_output_schema(self, tool_class: Type) -> Dict[str, Any]:
        """Extract output schema from tool class."""
        if hasattr(tool_class, 'output_schema'):
            schema = getattr(tool_class, 'output_schema')
            if callable(schema):
                try:
                    return schema()
                except:
                    return {}
            return schema if isinstance(schema, dict) else {}
        
        # Try to infer from execute method return type
        if hasattr(tool_class, 'execute'):
            try:
                type_hints = get_type_hints(tool_class.execute)
                if 'return' in type_hints:
                    return_type = type_hints['return']
                    return self._type_to_schema(return_type)
            except Exception as e:
                logger.warning(f"Failed to infer output schema for {tool_class.__name__}: {e}")
        
        return {"type": "object", "description": "Tool execution result"}

    def _type_to_schema(self, type_hint) -> Dict[str, Any]:
        """Convert Python type hint to JSON schema."""
        if type_hint == str:
            return {"type": "string"}
        elif type_hint == int:
            return {"type": "integer"}
        elif type_hint == float:
            return {"type": "number"}
        elif type_hint == bool:
            return {"type": "boolean"}
        elif type_hint == list:
            return {"type": "array"}
        elif type_hint == dict:
            return {"type": "object"}
        elif hasattr(type_hint, '__origin__'):
            # Handle generic types like List[str], Dict[str, Any]
            origin = type_hint.__origin__
            if origin == list:
                return {"type": "array"}
            elif origin == dict:
                return {"type": "object"}
        
        return {"type": "string", "description": f"Type: {type_hint}"}

    def _get_tool_methods(self, tool_class: Type) -> List[Dict[str, Any]]:
        """Extract public methods from tool class."""
        methods = []
        
        for name, method in inspect.getmembers(tool_class, inspect.ismethod):
            if not name.startswith('_'):
                method_info = {
                    'name': name,
                    'description': method.__doc__.strip().split('\n')[0] if method.__doc__ else '',
                    'signature': str(inspect.signature(method))
                }
                methods.append(method_info)
        
        return methods

    def _get_tool_dependencies(self, module) -> List[str]:
        """Extract dependencies from module imports."""
        dependencies = []
        
        # Get module source if available
        try:
            source = inspect.getsource(module)
            lines = source.split('\n')
            
            for line in lines:
                line = line.strip()
                if line.startswith('import ') or line.startswith('from '):
                    # Extract package name
                    if line.startswith('import '):
                        package = line.replace('import ', '').split(' as ')[0].split('.')[0]
                    else:
                        package = line.split('from ')[1].split(' import')[0].split('.')[0]
                    
                    # Skip standard library and local imports
                    if package not in ['os', 'sys', 'json', 'typing', 'dataclasses', 'enum', 'pathlib', 'logging']:
                        if not package.startswith('backend'):
                            dependencies.append(package)
        
        except Exception as e:
            logger.warning(f"Failed to extract dependencies: {e}")
        
        return list(set(dependencies))

    def _get_agent_compatibility(self, tool_class: Type) -> List[str]:
        """Determine which agents are compatible with this tool."""
        # This could be enhanced with actual compatibility testing
        # For now, assume all tools are compatible with all agents
        return ["analyst", "marketer", "concierge", "composable_analyst"]

    def _generate_tool_examples(self, tool_class: Type) -> List[Dict[str, Any]]:
        """Generate usage examples for the tool."""
        examples = []
        
        # Try to find examples in docstring
        if tool_class.__doc__:
            doc = tool_class.__doc__
            if 'Example:' in doc or 'Examples:' in doc:
                # Extract example section
                example_section = doc.split('Example')[1] if 'Example:' in doc else doc.split('Examples')[1]
                examples.append({
                    'title': 'Basic Usage',
                    'description': 'Example from tool documentation',
                    'code': example_section.strip()
                })
        
        # Generate basic example based on input schema
        input_schema = self._get_input_schema(tool_class)
        if input_schema and 'properties' in input_schema:
            example_input = {}
            for prop, schema in input_schema['properties'].items():
                example_input[prop] = self._generate_example_value(schema)
            
            examples.append({
                'title': 'Generated Example',
                'description': 'Auto-generated example based on input schema',
                'input': example_input,
                'code': f"tool.execute({json.dumps(example_input, indent=2)})"
            })
        
        return examples

    def _generate_example_value(self, schema: Dict[str, Any]) -> Any:
        """Generate example value based on schema."""
        schema_type = schema.get('type', 'string')
        
        if schema_type == 'string':
            if 'enum' in schema:
                return schema['enum'][0]
            return "example_string"
        elif schema_type == 'integer':
            return 42
        elif schema_type == 'number':
            return 3.14
        elif schema_type == 'boolean':
            return True
        elif schema_type == 'array':
            return ["example_item"]
        elif schema_type == 'object':
            return {"key": "value"}
        
        return "example_value"

    def _get_performance_notes(self, tool_class: Type) -> Dict[str, Any]:
        """Extract performance-related information."""
        return {
            'complexity': 'O(n)',  # This would need actual analysis
            'memory_usage': 'Low',  # This would need actual profiling
            'typical_runtime': '< 1s',  # This would need benchmarking
            'scalability': 'Good'  # This would need testing
        }

    def generate_openapi_schema(self) -> Dict[str, Any]:
        """Generate OpenAPI 3.0 schema for all discovered tools."""
        if not self.tools_registry:
            self.discover_tools()

        openapi_schema = {
            "openapi": "3.0.0",
            "info": {
                "title": "MCP Tools API",
                "description": "Comprehensive API documentation for Model Context Protocol (MCP) Tools",
                "version": "1.0.0",
                "contact": {
                    "name": "MCP Tools Support",
                    "email": "<EMAIL>"
                }
            },
            "servers": [
                {
                    "url": "http://localhost:8000/api/mcp",
                    "description": "Local development server"
                }
            ],
            "paths": {},
            "components": {
                "schemas": {},
                "securitySchemes": {
                    "bearerAuth": {
                        "type": "http",
                        "scheme": "bearer",
                        "bearerFormat": "JWT"
                    }
                }
            },
            "security": [{"bearerAuth": []}]
        }

        # Generate paths and schemas for each tool
        for tool_name, tool_info in self.tools_registry.items():
            # Create path for tool execution
            path_name = f"/tools/{tool_name.lower()}/execute"

            openapi_schema["paths"][path_name] = {
                "post": {
                    "summary": f"Execute {tool_name}",
                    "description": tool_info.get('description', ''),
                    "tags": [self._get_tool_category(tool_name)],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": tool_info.get('input_schema', {}),
                                "examples": self._create_openapi_examples(tool_info.get('examples', []))
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful execution",
                            "content": {
                                "application/json": {
                                    "schema": tool_info.get('output_schema', {})
                                }
                            }
                        },
                        "400": {
                            "description": "Invalid input parameters",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "error": {"type": "string"},
                                            "details": {"type": "object"}
                                        }
                                    }
                                }
                            }
                        },
                        "500": {
                            "description": "Internal server error",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "error": {"type": "string"},
                                            "trace": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            # Add schema definitions
            schema_name = f"{tool_name}Input"
            openapi_schema["components"]["schemas"][schema_name] = tool_info.get('input_schema', {})

            output_schema_name = f"{tool_name}Output"
            openapi_schema["components"]["schemas"][output_schema_name] = tool_info.get('output_schema', {})

        return openapi_schema

    def _get_tool_category(self, tool_name: str) -> str:
        """Categorize tool based on name and functionality."""
        tool_name_lower = tool_name.lower()

        if any(keyword in tool_name_lower for keyword in ['data', 'access', 'query', 'database']):
            return "Data Access"
        elif any(keyword in tool_name_lower for keyword in ['text', 'processing', 'nlp', 'language']):
            return "Text Processing"
        elif any(keyword in tool_name_lower for keyword in ['statistical', 'analysis', 'stats', 'ml', 'machine']):
            return "Analytics"
        elif any(keyword in tool_name_lower for keyword in ['time', 'series', 'forecast', 'trend']):
            return "Time Series"
        elif any(keyword in tool_name_lower for keyword in ['marketing', 'strategy', 'business']):
            return "Business Intelligence"
        else:
            return "General"

    def _create_openapi_examples(self, examples: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Convert tool examples to OpenAPI format."""
        openapi_examples = {}

        for i, example in enumerate(examples):
            example_name = example.get('title', f'example_{i+1}').replace(' ', '_').lower()

            openapi_examples[example_name] = {
                "summary": example.get('title', f'Example {i+1}'),
                "description": example.get('description', ''),
                "value": example.get('input', {})
            }

        return openapi_examples

    def generate_markdown_documentation(self) -> str:
        """Generate comprehensive markdown documentation."""
        if not self.tools_registry:
            self.discover_tools()

        markdown_content = []

        # Header
        markdown_content.extend([
            "# MCP Tools Documentation",
            "",
            "Comprehensive documentation for Model Context Protocol (MCP) Tools.",
            "",
            "## Table of Contents",
            ""
        ])

        # Generate table of contents
        categories = {}
        for tool_name, tool_info in self.tools_registry.items():
            category = self._get_tool_category(tool_name)
            if category not in categories:
                categories[category] = []
            categories[category].append(tool_name)

        for category, tools in categories.items():
            markdown_content.append(f"- [{category}](#{category.lower().replace(' ', '-')})")
            for tool in tools:
                markdown_content.append(f"  - [{tool}](#{tool.lower()})")

        markdown_content.append("")

        # Generate documentation for each category
        for category, tools in categories.items():
            markdown_content.extend([
                f"## {category}",
                ""
            ])

            for tool_name in tools:
                tool_info = self.tools_registry[tool_name]
                markdown_content.extend(self._generate_tool_markdown(tool_name, tool_info))

        return "\n".join(markdown_content)

    def _generate_tool_markdown(self, tool_name: str, tool_info: Dict[str, Any]) -> List[str]:
        """Generate markdown documentation for a single tool."""
        content = []

        # Tool header
        content.extend([
            f"### {tool_name}",
            "",
            tool_info.get('description', 'No description available.'),
            ""
        ])

        # Input schema
        input_schema = tool_info.get('input_schema', {})
        if input_schema and 'properties' in input_schema:
            content.extend([
                "#### Input Parameters",
                "",
                "| Parameter | Type | Required | Description |",
                "|-----------|------|----------|-------------|"
            ])

            required_params = input_schema.get('required', [])
            for param, schema in input_schema['properties'].items():
                param_type = schema.get('type', 'string')
                is_required = "Yes" if param in required_params else "No"
                description = schema.get('description', 'No description')
                content.append(f"| {param} | {param_type} | {is_required} | {description} |")

            content.append("")

        # Examples
        examples = tool_info.get('examples', [])
        if examples:
            content.extend([
                "#### Examples",
                ""
            ])

            for example in examples:
                content.extend([
                    f"**{example.get('title', 'Example')}**",
                    "",
                    example.get('description', ''),
                    "",
                    "```json",
                    json.dumps(example.get('input', {}), indent=2),
                    "```",
                    ""
                ])

        # Agent compatibility
        compatibility = tool_info.get('agent_compatibility', [])
        if compatibility:
            content.extend([
                "#### Agent Compatibility",
                "",
                f"Compatible with: {', '.join(compatibility)}",
                ""
            ])

        # Dependencies
        dependencies = tool_info.get('dependencies', [])
        if dependencies:
            content.extend([
                "#### Dependencies",
                "",
                f"Required packages: {', '.join(dependencies)}",
                ""
            ])

        content.append("---")
        content.append("")

        return content

    def generate_interactive_documentation(self) -> Dict[str, Any]:
        """Generate interactive documentation data for web interfaces."""
        if not self.tools_registry:
            self.discover_tools()

        interactive_docs = {
            "metadata": {
                "title": "MCP Tools Interactive Documentation",
                "version": "1.0.0",
                "generated_at": "2024-01-01T00:00:00Z",  # Would use actual timestamp
                "total_tools": len(self.tools_registry)
            },
            "categories": {},
            "tools": {},
            "search_index": []
        }

        # Organize by categories
        categories = {}
        for tool_name, tool_info in self.tools_registry.items():
            category = self._get_tool_category(tool_name)
            if category not in categories:
                categories[category] = {
                    "name": category,
                    "description": f"Tools for {category.lower()} operations",
                    "tools": []
                }
            categories[category]["tools"].append(tool_name)

        interactive_docs["categories"] = categories

        # Add detailed tool information
        for tool_name, tool_info in self.tools_registry.items():
            interactive_docs["tools"][tool_name] = {
                "name": tool_name,
                "description": tool_info.get('description', ''),
                "category": self._get_tool_category(tool_name),
                "input_schema": tool_info.get('input_schema', {}),
                "output_schema": tool_info.get('output_schema', {}),
                "examples": tool_info.get('examples', []),
                "agent_compatibility": tool_info.get('agent_compatibility', []),
                "dependencies": tool_info.get('dependencies', []),
                "performance": tool_info.get('performance_notes', {}),
                "test_interface": {
                    "enabled": True,
                    "default_values": self._generate_default_test_values(tool_info.get('input_schema', {}))
                }
            }

            # Add to search index
            search_entry = {
                "name": tool_name,
                "description": tool_info.get('description', ''),
                "category": self._get_tool_category(tool_name),
                "keywords": self._extract_keywords(tool_name, tool_info)
            }
            interactive_docs["search_index"].append(search_entry)

        return interactive_docs

    def _generate_default_test_values(self, input_schema: Dict[str, Any]) -> Dict[str, Any]:
        """Generate default values for testing interface."""
        defaults = {}

        if 'properties' in input_schema:
            for prop, schema in input_schema['properties'].items():
                defaults[prop] = self._generate_example_value(schema)

        return defaults

    def _extract_keywords(self, tool_name: str, tool_info: Dict[str, Any]) -> List[str]:
        """Extract keywords for search indexing."""
        keywords = [tool_name.lower()]

        # Add words from description
        description = tool_info.get('description', '')
        keywords.extend(description.lower().split())

        # Add category
        keywords.append(self._get_tool_category(tool_name).lower())

        # Add dependencies
        keywords.extend(tool_info.get('dependencies', []))

        # Remove duplicates and common words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [k for k in set(keywords) if k not in stop_words and len(k) > 2]

        return keywords

    def save_documentation(self, doc_type: DocumentationType, output_path: str = None) -> str:
        """Save generated documentation to file."""
        if output_path is None:
            output_path = Path(self.config.output_directory)
        else:
            output_path = Path(output_path)

        output_path.mkdir(parents=True, exist_ok=True)

        if doc_type == DocumentationType.OPENAPI_SCHEMA:
            schema = self.generate_openapi_schema()
            file_path = output_path / "openapi.json"
            with open(file_path, 'w') as f:
                json.dump(schema, f, indent=2)

        elif doc_type == DocumentationType.API_REFERENCE:
            markdown = self.generate_markdown_documentation()
            file_path = output_path / "api_reference.md"
            with open(file_path, 'w') as f:
                f.write(markdown)

        elif doc_type == DocumentationType.INTERACTIVE_DOCS:
            interactive = self.generate_interactive_documentation()
            file_path = output_path / "interactive_docs.json"
            with open(file_path, 'w') as f:
                json.dump(interactive, f, indent=2)

        elif doc_type == DocumentationType.TOOL_CATALOG:
            catalog = self._generate_tool_catalog()
            file_path = output_path / "tool_catalog.json"
            with open(file_path, 'w') as f:
                json.dump(catalog, f, indent=2)

        else:
            raise ValueError(f"Unsupported documentation type: {doc_type}")

        return str(file_path)

    def _generate_tool_catalog(self) -> Dict[str, Any]:
        """Generate a comprehensive tool catalog."""
        if not self.tools_registry:
            self.discover_tools()

        catalog = {
            "catalog_info": {
                "name": "MCP Tools Catalog",
                "version": "1.0.0",
                "description": "Comprehensive catalog of available MCP tools",
                "total_tools": len(self.tools_registry),
                "categories": list(set(self._get_tool_category(name) for name in self.tools_registry.keys()))
            },
            "tools": []
        }

        for tool_name, tool_info in self.tools_registry.items():
            catalog_entry = {
                "name": tool_name,
                "id": tool_name.lower().replace(' ', '_'),
                "description": tool_info.get('description', ''),
                "category": self._get_tool_category(tool_name),
                "version": "1.0.0",  # Would extract from tool if available
                "status": "active",
                "agent_compatibility": tool_info.get('agent_compatibility', []),
                "input_parameters": len(tool_info.get('input_schema', {}).get('properties', {})),
                "has_examples": len(tool_info.get('examples', [])) > 0,
                "dependencies": tool_info.get('dependencies', []),
                "performance_tier": self._classify_performance_tier(tool_info),
                "last_updated": "2024-01-01",  # Would use actual date
                "documentation_url": f"/docs/tools/{tool_name.lower()}",
                "test_url": f"/test/tools/{tool_name.lower()}"
            }
            catalog["tools"].append(catalog_entry)

        return catalog

    def _classify_performance_tier(self, tool_info: Dict[str, Any]) -> str:
        """Classify tool performance tier based on complexity and dependencies."""
        dependencies = tool_info.get('dependencies', [])

        # Simple heuristic based on dependencies
        if any(dep in ['tensorflow', 'torch', 'sklearn'] for dep in dependencies):
            return "high_compute"
        elif any(dep in ['pandas', 'numpy', 'scipy'] for dep in dependencies):
            return "medium_compute"
        else:
            return "low_compute"
