/**
 * Mode Indicator Component
 * 
 * Displays current dashboard mode with toggle functionality and visual indicators.
 * Provides clear mode status and easy switching between Simple and Advanced modes.
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Switch } from '@/components/ui/switch';
import { 
  Spark<PERSON>, 
  Settings, 
  RotateCcw, 
  Info,
  Zap,
  Wrench
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDashboardMode } from '@/stores/dashboard-mode-store';
import { DashboardMode } from '@/types/dashboard-mode';

interface ModeIndicatorProps {
  className?: string;
  variant?: 'compact' | 'full' | 'badge-only';
  show_hints?: boolean;
  show_capabilities?: boolean;
}

export const ModeIndicator: React.FC<ModeIndicatorProps> = ({
  className,
  variant = 'full',
  show_hints = true,
  show_capabilities = false,
}) => {
  const { current_mode, is_switching, toggle_mode, can_switch } = useDashboardMode();

  const mode_config = {
    simple: {
      label: 'Simple Mode',
      description: 'AI-guided dashboard creation with conversational interface',
      icon: Sparkles,
      color: 'bg-blue-500',
      badge_variant: 'default' as const,
      capabilities: ['AI Assistant', 'Templates', 'Guided Workflows', 'Natural Language'],
    },
    advanced: {
      label: 'Advanced Mode',
      description: 'Full technical control with ribbon toolbar and customization',
      icon: Settings,
      color: 'bg-purple-500',
      badge_variant: 'secondary' as const,
      capabilities: ['Ribbon Toolbar', 'Custom Queries', 'Code Editor', 'API Access'],
    },
  };

  const current_config = mode_config[current_mode];
  const IconComponent = current_config.icon;

  const handle_toggle = () => {
    if (can_switch && !is_switching) {
      toggle_mode();
    }
  };

  if (variant === 'badge-only') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge 
              variant={current_config.badge_variant}
              className={cn(
                "cursor-pointer transition-all duration-200 hover:scale-105",
                is_switching && "animate-pulse",
                className
              )}
              onClick={handle_toggle}
            >
              <IconComponent className="h-3 w-3 mr-1" />
              {current_config.label}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-sm">
              <p className="font-medium">{current_config.label}</p>
              <p className="text-muted-foreground">{current_config.description}</p>
              {show_hints && (
                <p className="text-xs mt-1 text-blue-400">Click to switch modes</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <div className="flex items-center space-x-1">
          <div className={cn("w-2 h-2 rounded-full", current_config.color)} />
          <span className="text-sm font-medium">{current_config.label}</span>
        </div>
        <Switch
          checked={current_mode === 'advanced'}
          onCheckedChange={() => handle_toggle()}
          disabled={!can_switch || is_switching}
          className="scale-75"
        />
      </div>
    );
  }

  return (
    <div className={cn("flex items-center space-x-3 p-3 bg-card rounded-lg border", className)}>
      {/* Mode Status */}
      <div className="flex items-center space-x-2">
        <div className={cn("w-3 h-3 rounded-full", current_config.color)} />
        <div>
          <div className="flex items-center space-x-1">
            <IconComponent className="h-4 w-4" />
            <span className="font-medium">{current_config.label}</span>
            {is_switching && (
              <RotateCcw className="h-3 w-3 animate-spin text-muted-foreground" />
            )}
          </div>
          <p className="text-xs text-muted-foreground">{current_config.description}</p>
        </div>
      </div>

      {/* Mode Toggle */}
      <div className="flex items-center space-x-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                onClick={handle_toggle}
                disabled={!can_switch || is_switching}
                className="h-8"
              >
                <Zap className="h-3 w-3 mr-1" />
                Switch to {current_mode === 'simple' ? 'Advanced' : 'Simple'}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Switch to {current_mode === 'simple' ? 'Advanced' : 'Simple'} Mode</p>
              {show_hints && (
                <p className="text-xs text-muted-foreground mt-1">
                  {current_mode === 'simple' 
                    ? 'Get full technical control and customization options'
                    : 'Get AI-guided assistance and simplified interface'
                  }
                </p>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Capabilities (Optional) */}
      {show_capabilities && (
        <div className="flex items-center space-x-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Info className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm">
                  <p className="font-medium mb-1">Available in {current_config.label}:</p>
                  <ul className="text-xs space-y-1">
                    {current_config.capabilities.map((capability, index) => (
                      <li key={index} className="flex items-center space-x-1">
                        <Wrench className="h-2 w-2" />
                        <span>{capability}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      )}
    </div>
  );
};

// Utility component for mode-aware rendering
interface ModeAwareProps {
  mode?: DashboardMode;
  simple_content?: React.ReactNode;
  advanced_content?: React.ReactNode;
  children?: (mode: DashboardMode) => React.ReactNode;
}

export const ModeAware: React.FC<ModeAwareProps> = ({
  mode,
  simple_content,
  advanced_content,
  children,
}) => {
  const { current_mode } = useDashboardMode();
  const active_mode = mode || current_mode;

  if (children) {
    return <>{children(active_mode)}</>;
  }

  return (
    <>
      {active_mode === 'simple' ? simple_content : advanced_content}
    </>
  );
};
