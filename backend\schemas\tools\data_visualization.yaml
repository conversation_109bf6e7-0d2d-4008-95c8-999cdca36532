name: data_visualization
description: Visualize data using various chart types
input_schema:
  type: object
  properties:
    file_path:
      type: string
      description: Path to the data file (CSV, Excel, JSON supported)
    chart_type:
      type: string
      enum:
        - bar
        - line
        - scatter
        - pie
        - heatmap
      description: Type of chart to create
    x_column:
      type: string
      description: Name of the column to use for the x-axis (for bar, line, scatter)
    y_column:
      type: string
      description: Name of the column to use for the y-axis (for bar, line, scatter)
    value_column:
      type: string
      description: Name of the column to use for values (for pie, heatmap)
    label_column:
      type: string
      description: Name of the column to use for labels (for pie)
    group_by_column:
      type: string
      description: Name of the column to group data by (optional, for grouped charts)
    title:
      type: string
      description: Title for the chart (optional)
    x_label:
      type: string
      description: Label for the x-axis (optional)
    y_label:
      type: string
      description: Label for the y-axis (optional)
  required:
    - file_path
    - chart_type
output_schema:
  type: object
  properties:
    tool_name:
      type: string
    status:
      type: string
      enum: [success, error]
    isError:
      type: boolean
    message:
      type: string
      description: A message describing the outcome, or an error message.
    content:
      type: array
      items:
        type: object
        properties:
          type:
            type: string
            enum:
              - text
              - image
          text:
            type: string
            description: Text content, if any.
          src:
            type: string
            description: Base64 encoded image data URI (for image type).
        required:
          - type
  required:
    - tool_name
    - status
    - isError
annotations:
  title: Visualize Data
  readOnlyHint: true
  openWorldHint: false
  categories:
    - data_analysis
    - visualization
