"""
Enhanced web scraping service that integrates Crawl4AI with existing WebScrapingService.

This module provides a unified interface that uses Crawl4AI for improved extraction
while maintaining backward compatibility with the existing web scraping implementation.
"""

import logging
import os
from typing import Dict, Any

from .crawl4ai_web_service import Crawl4AIWebService
from .web_scraping_service import WebScrapingService

logger = logging.getLogger(__name__)

class EnhancedWebScrapingService:
    """
    Unified web scraping service that combines Crawl4AI and existing WebScrapingService.
    
    This service provides a single interface for web scraping that automatically
    chooses the best extraction method while maintaining full backward compatibility
    with existing API endpoints and response formats.
    """
    
    def __init__(self):
        """Initialize the enhanced web scraping service."""
        self.crawl4ai_service = Crawl4AIWebService()
        self.legacy_service = WebScrapingService()
        self.session = None
        
        logger.info("Enhanced web scraping service initialized")
    
    async def extract_business_info(self, url: str) -> Dict[str, Any]:
        """
        Extract business information from a website URL.
        
        This method provides the same interface as the original WebScrapingService
        but uses the enhanced Crawl4AI service with intelligent fallback.
        
        Args:
            url: Website URL to scrape
            
        Returns:
            Dictionary with extracted business information in the same format
            as the original WebScrapingService
        """
        try:
            logger.info(f"Enhanced extraction starting for URL: {url}")
            
            # Use the Crawl4AI service which has built-in fallback logic
            result = await self.crawl4ai_service.extract_business_info(url)
            
            logger.info(f"Enhanced extraction completed for {url} using method: {result.get('extraction_method', 'unknown')}")
            
            return result
            
        except Exception as e:
            logger.error(f"Enhanced extraction failed for {url}: {e}")
            
            # Final fallback - return error structure compatible with existing code
            return {
                'source_url': url,
                'business_name': '',
                'description': '',
                'industry': '',
                'contact_info': {'emails': [], 'phones': [], 'addresses': []},
                'social_links': [],
                'structured_data': {},
                'meta_data': {},
                'content_summary': '',
                'extraction_timestamp': 0,
                'extraction_method': 'error',
                'error_message': str(e)
            }
    
    async def scrape_website(self, url: str):
        """
        Scrape website content - delegates to legacy service for compatibility.
        
        This method maintains compatibility with any code that directly calls
        the scrape_website method from the original WebScrapingService.
        
        Args:
            url: Website URL to scrape
            
        Returns:
            ScrapedContent object from the original WebScrapingService
        """
        logger.info(f"Legacy scrape_website method called for {url}")
        return await self.legacy_service.scrape_website(url)
    
    async def __aenter__(self):
        """Async context manager entry."""
        # Initialize both services
        await self.crawl4ai_service.__aenter__()
        await self.legacy_service.__aenter__()
        self.session = self.legacy_service.session
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        # Clean up both services
        await self.crawl4ai_service.__aexit__(exc_type, exc_val, exc_tb)
        await self.legacy_service.__aexit__(exc_type, exc_val, exc_tb)
        self.session = None
    
    # Delegate other methods to legacy service for full compatibility
    def _infer_business_name(self, content):
        """Delegate to legacy service."""
        return self.legacy_service._infer_business_name(content)
    
    def _infer_industry(self, content):
        """Delegate to legacy service."""
        return self.legacy_service._infer_industry(content)
    
    def _extract_title(self, soup):
        """Delegate to legacy service."""
        return self.legacy_service._extract_title(soup)
    
    def _extract_description(self, soup):
        """Delegate to legacy service."""
        return self.legacy_service._extract_description(soup)
    
    def _extract_main_content(self, soup):
        """Delegate to legacy service."""
        return self.legacy_service._extract_main_content(soup)
    
    def _extract_structured_data(self, soup):
        """Delegate to legacy service."""
        return self.legacy_service._extract_structured_data(soup)
    
    def _extract_contact_info(self, soup, content):
        """Delegate to legacy service."""
        return self.legacy_service._extract_contact_info(soup, content)
    
    def _extract_social_links(self, soup):
        """Delegate to legacy service."""
        return self.legacy_service._extract_social_links(soup)
    
    def _extract_images(self, soup, base_url):
        """Delegate to legacy service."""
        return self.legacy_service._extract_images(soup, base_url)
    
    def _extract_meta_data(self, soup):
        """Delegate to legacy service."""
        return self.legacy_service._extract_meta_data(soup)


# Create a factory function for easy service instantiation
def create_web_scraping_service():
    """
    Factory function to create the appropriate web scraping service.
    
    Returns:
        EnhancedWebScrapingService instance
    """
    return EnhancedWebScrapingService()


# Maintain backward compatibility by providing the same interface
WebScrapingServiceEnhanced = EnhancedWebScrapingService
