import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  MessageSquare,
  Database,
  BarChart4,
  Users,
  FileText,
  Clock,
  TrendingUp,
  Bot,
  Upload,
  Settings,
  ArrowRight,
  Activity,
  Calendar,
  User
} from "lucide-react";
import { DashboardLayout } from "@/components/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { chatApi, fileApi, personaApi } from "@/lib/api";

interface RecentActivity {
  id: string;
  type: 'conversation' | 'file_upload' | 'persona_purchase';
  title: string;
  description: string;
  timestamp: string;
  icon: any;
}

interface QuickStat {
  label: string;
  value: string;
  icon: any;
  trend?: string;
  color: string;
}

const Home = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [quickStats, setQuickStats] = useState<QuickStat[]>([]);
  const [recentFiles, setRecentFiles] = useState<any[]>([]);
  const [availablePersonas, setAvailablePersonas] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      setIsLoading(true);

      // Load recent conversations
      const conversationsResponse = await chatApi.getConversations(0, 5);

      // Load recent files
      const filesResponse = await fileApi.getFiles(0, 5);
      setRecentFiles(filesResponse.files || []);

      // Load available personas
      const personasResponse = await personaApi.getPersonas();
      setAvailablePersonas(personasResponse.personas || []);

      // Create recent activity from conversations and files
      const activities: RecentActivity[] = [];

      // Add recent conversations
      if (conversationsResponse.conversations) {
        conversationsResponse.conversations.slice(0, 3).forEach(conv => {
          activities.push({
            id: conv.id,
            type: 'conversation',
            title: conv.title || 'New Conversation',
            description: `Chat with ${conv.persona_id}`,
            timestamp: conv.updated_at,
            icon: MessageSquare
          });
        });
      }

      // Add recent file uploads
      if (filesResponse.files) {
        filesResponse.files.slice(0, 2).forEach(file => {
          activities.push({
            id: file.id,
            type: 'file_upload',
            title: file.filename,
            description: `Uploaded ${file.file_size ? Math.round(file.file_size / 1024) + ' KB' : 'file'}`,
            timestamp: file.created_at,
            icon: Upload
          });
        });
      }

      // Sort activities by timestamp
      activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      setRecentActivity(activities.slice(0, 5));

      // Set quick stats
      setQuickStats([
        {
          label: "Conversations",
          value: conversationsResponse.conversations?.length.toString() || "0",
          icon: MessageSquare,
          color: "text-blue-600"
        },
        {
          label: "Files Uploaded",
          value: filesResponse.files?.length.toString() || "0",
          icon: FileText,
          color: "text-green-600"
        },
        {
          label: "AI Personas",
          value: personasResponse.personas?.length.toString() || "0",
          icon: Bot,
          color: "text-purple-600"
        },
        {
          label: "Days Active",
          value: user?.created_at ? Math.floor((new Date().getTime() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24)).toString() : "0",
          icon: Calendar,
          color: "text-orange-600"
        }
      ]);

    } catch (error) {
      console.error('Error loading home data:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };

  const formatRelativeTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-r from-brand-50 to-brand-100 rounded-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {getGreeting()}, {user?.first_name || user?.username || 'there'}! 👋
              </h1>
              <p className="text-gray-600 mt-2">
                Welcome back to your AI-powered data intelligence workspace
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-2">
              <User className="h-8 w-8 text-brand-600" />
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{user?.email}</p>
                <p className="text-xs text-gray-500">
                  Member since {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
        >
          {quickStats.map((stat, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-brand-600" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Jump into your most-used features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button asChild variant="outline" className="h-20 flex-col space-y-2">
                  <Link to="/data-chat">
                    <MessageSquare className="h-6 w-6" />
                    <span>Start Chat</span>
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-20 flex-col space-y-2">
                  <Link to="/data-integration">
                    <Upload className="h-6 w-6" />
                    <span>Upload Data</span>
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-20 flex-col space-y-2">
                  <Link to="/ai-marketplace">
                    <Bot className="h-6 w-6" />
                    <span>AI Marketplace</span>
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-20 flex-col space-y-2">
                  <Link to="/reports">
                    <BarChart4 className="h-6 w-6" />
                    <span>View Reports</span>
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-brand-600" />
                  Recent Activity
                </CardTitle>
                <CardDescription>
                  Your latest interactions and uploads
                </CardDescription>
              </CardHeader>
              <CardContent>
                {recentActivity.length > 0 ? (
                  <div className="space-y-4">
                    {recentActivity.map((activity) => (
                      <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                        <activity.icon className="h-5 w-5 text-gray-500 mt-0.5" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {activity.title}
                          </p>
                          <p className="text-sm text-gray-500">
                            {activity.description}
                          </p>
                        </div>
                        <span className="text-xs text-gray-400">
                          {formatRelativeTime(activity.timestamp)}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No recent activity</p>
                    <p className="text-sm">Start a conversation or upload data to see activity here</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Files */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-brand-600" />
                  Recent Files
                </CardTitle>
                <CardDescription>
                  Your recently uploaded data files
                </CardDescription>
              </CardHeader>
              <CardContent>
                {recentFiles.length > 0 ? (
                  <div className="space-y-3">
                    {recentFiles.map((file) => (
                      <div key={file.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-gray-500" />
                          <div>
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {file.filename}
                            </p>
                            <p className="text-xs text-gray-500">
                              {file.num_rows ? `${file.num_rows} rows` : 'Unknown size'} • {formatRelativeTime(file.created_at)}
                            </p>
                          </div>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {file.file_size ? Math.round(file.file_size / 1024) + ' KB' : 'N/A'}
                        </Badge>
                      </div>
                    ))}
                    <div className="pt-2">
                      <Button asChild variant="ghost" size="sm" className="w-full">
                        <Link to="/data-integration">
                          View All Files <ArrowRight className="h-4 w-4 ml-2" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Database className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No files uploaded yet</p>
                    <p className="text-sm mb-4">Upload your first data file to get started</p>
                    <Button asChild size="sm">
                      <Link to="/data-integration">
                        Upload File <Upload className="h-4 w-4 ml-2" />
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* AI Personas & Getting Started */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Available AI Personas */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5 text-brand-600" />
                  AI Personas
                </CardTitle>
                <CardDescription>
                  Specialized AI assistants for your data needs
                </CardDescription>
              </CardHeader>
              <CardContent>
                {availablePersonas.length > 0 ? (
                  <div className="space-y-3">
                    {availablePersonas.slice(0, 3).map((persona) => (
                      <div key={persona.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 rounded-full bg-brand-100 flex items-center justify-center">
                            <Bot className="h-4 w-4 text-brand-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {persona.name}
                            </p>
                            <p className="text-xs text-gray-500 truncate">
                              {persona.description}
                            </p>
                          </div>
                        </div>
                        <Badge variant={persona.is_available ? "default" : "secondary"} className="text-xs">
                          {persona.is_available ? "Available" : "Unavailable"}
                        </Badge>
                      </div>
                    ))}
                    <div className="pt-2">
                      <Button asChild variant="ghost" size="sm" className="w-full">
                        <Link to="/ai-marketplace">
                          Explore All Personas <ArrowRight className="h-4 w-4 ml-2" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Bot className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No personas available</p>
                    <p className="text-sm mb-4">Check the marketplace for AI assistants</p>
                    <Button asChild size="sm">
                      <Link to="/ai-marketplace">
                        Browse Marketplace <Users className="h-4 w-4 ml-2" />
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Getting Started */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-brand-600" />
                  Getting Started
                </CardTitle>
                <CardDescription>
                  Quick steps to maximize your DataGenius experience
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 rounded-full bg-brand-600 text-white text-xs flex items-center justify-center font-medium">
                      1
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Upload Your Data</p>
                      <p className="text-xs text-gray-500">Start by uploading CSV, Excel, or other data files</p>
                      <Button asChild variant="link" size="sm" className="p-0 h-auto text-xs">
                        <Link to="/data-integration">Upload now →</Link>
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 rounded-full bg-brand-600 text-white text-xs flex items-center justify-center font-medium">
                      2
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Choose an AI Persona</p>
                      <p className="text-xs text-gray-500">Select specialized AI assistants for your analysis needs</p>
                      <Button asChild variant="link" size="sm" className="p-0 h-auto text-xs">
                        <Link to="/ai-marketplace">Browse personas →</Link>
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 rounded-full bg-brand-600 text-white text-xs flex items-center justify-center font-medium">
                      3
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Start Analyzing</p>
                      <p className="text-xs text-gray-500">Ask questions about your data in natural language</p>
                      <Button asChild variant="link" size="sm" className="p-0 h-auto text-xs">
                        <Link to="/data-chat">Start chat →</Link>
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 rounded-full bg-brand-600 text-white text-xs flex items-center justify-center font-medium">
                      4
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Generate Reports</p>
                      <p className="text-xs text-gray-500">Create comprehensive reports and visualizations</p>
                      <Button asChild variant="link" size="sm" className="p-0 h-auto text-xs">
                        <Link to="/reports">View reports →</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Home;
