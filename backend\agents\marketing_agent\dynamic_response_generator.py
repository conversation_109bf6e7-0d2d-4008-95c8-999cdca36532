"""
Dynamic Response Generator for Marketing Agent.

This module replaces hardcoded fallback responses with intelligent,
context-aware responses generated by LLM.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)


class DynamicResponseGenerator:
    """Generates dynamic, context-aware responses instead of hardcoded fallbacks."""
    
    def __init__(self):
        self.response_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def generate_contextual_response(
        self, 
        mcp_server: Any,
        scenario: str,
        context: Dict[str, Any],
        user_message: str = "",
        error_details: Optional[str] = None
    ) -> str:
        """
        Generate a contextual response based on scenario and user context.
        
        Args:
            mcp_server: MCP server instance for LLM calls
            scenario: The scenario type (error, fallback, greeting, etc.)
            context: User and conversation context
            user_message: Original user message
            error_details: Technical error details if applicable
            
        Returns:
            Generated response string
        """
        try:
            # Check cache first
            cache_key = self._get_cache_key(scenario, user_message, context)
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                return cached_response
            
            # Generate response based on scenario
            if scenario == "technical_error":
                response = await self._generate_error_response(
                    mcp_server, context, user_message, error_details
                )
            elif scenario == "service_unavailable":
                response = await self._generate_service_unavailable_response(
                    mcp_server, context, user_message
                )
            elif scenario == "clarification_needed":
                response = await self._generate_clarification_response(
                    mcp_server, context, user_message
                )
            elif scenario == "general_assistance":
                response = await self._generate_assistance_response(
                    mcp_server, context, user_message
                )
            elif scenario == "data_processing_error":
                response = await self._generate_data_error_response(
                    mcp_server, context, user_message
                )
            else:
                response = await self._generate_generic_response(
                    mcp_server, context, user_message, scenario
                )
            
            # Cache the response
            self._cache_response(cache_key, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating dynamic response: {e}")
            return self._get_emergency_fallback(scenario)
    
    async def _generate_error_response(
        self, 
        mcp_server: Any, 
        context: Dict[str, Any], 
        user_message: str,
        error_details: Optional[str]
    ) -> str:
        """Generate contextual error response."""
        
        # Determine user's current task context
        task_context = self._extract_task_context(context, user_message)
        
        prompt = f"""You are a professional marketing AI assistant. A technical error occurred while helping a user.

User's request: "{user_message}"
Current task context: {task_context}
Error type: Technical processing issue

Generate a helpful, empathetic response that:
1. Acknowledges the issue professionally
2. Reassures the user you're still available to help
3. Suggests alternative approaches for their marketing needs
4. Maintains confidence in your marketing expertise
5. Offers specific next steps they can take

Keep the response concise (2-3 sentences) and focused on moving forward constructively."""

        try:
            result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "error_response_generation",
                    "scenario": "technical_error",
                    "maintain_marketing_focus": True
                },
                "intent_type": "error_handling",
                "temperature": 0.7,
                "max_tokens": 150
            })
            
            if not result.get("isError", False):
                response_text = result.get("content", [{}])[0].get("text", "")
                if response_text.strip():
                    return response_text.strip()
                    
        except Exception as e:
            logger.error(f"Error generating LLM error response: {e}")
        
        return self._get_emergency_fallback("technical_error")
    
    async def _generate_service_unavailable_response(
        self, 
        mcp_server: Any, 
        context: Dict[str, Any], 
        user_message: str
    ) -> str:
        """Generate response for service unavailability."""
        
        task_context = self._extract_task_context(context, user_message)
        
        prompt = f"""You are a marketing AI assistant. A service is temporarily unavailable.

User's request: "{user_message}"
Task context: {task_context}

Generate a professional response that:
1. Explains the temporary service issue
2. Provides alternative ways to help with their marketing needs
3. Suggests specific marketing actions they can take while waiting
4. Maintains enthusiasm for helping with their marketing goals
5. Gives a realistic timeframe if possible

Keep it helpful and solution-oriented (2-3 sentences)."""

        try:
            result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "service_unavailable_response",
                    "scenario": "service_unavailable"
                },
                "intent_type": "service_handling",
                "temperature": 0.6,
                "max_tokens": 120
            })
            
            if not result.get("isError", False):
                response_text = result.get("content", [{}])[0].get("text", "")
                if response_text.strip():
                    return response_text.strip()
                    
        except Exception as e:
            logger.error(f"Error generating service unavailable response: {e}")
        
        return self._get_emergency_fallback("service_unavailable")
    
    async def _generate_clarification_response(
        self, 
        mcp_server: Any, 
        context: Dict[str, Any], 
        user_message: str
    ) -> str:
        """Generate response when clarification is needed."""
        
        prompt = f"""You are a marketing AI assistant. The user's request needs clarification.

User's message: "{user_message}"

Generate a helpful response that:
1. Acknowledges their request positively
2. Asks specific clarifying questions about their marketing needs
3. Provides examples of what you can help with
4. Shows enthusiasm for helping once you understand better

Keep it conversational and encouraging (2-3 sentences)."""

        try:
            result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "clarification_request",
                    "scenario": "clarification_needed"
                },
                "intent_type": "clarification",
                "temperature": 0.7,
                "max_tokens": 120
            })
            
            if not result.get("isError", False):
                response_text = result.get("content", [{}])[0].get("text", "")
                if response_text.strip():
                    return response_text.strip()
                    
        except Exception as e:
            logger.error(f"Error generating clarification response: {e}")
        
        return self._get_emergency_fallback("clarification_needed")
    
    async def _generate_assistance_response(
        self, 
        mcp_server: Any, 
        context: Dict[str, Any], 
        user_message: str
    ) -> str:
        """Generate general assistance response."""
        
        has_data = context.get("has_data_source", False)
        user_industry = context.get("user_info", {}).get("industry", "")
        
        prompt = f"""You are a marketing AI assistant ready to help.

User's message: "{user_message}"
Has uploaded data: {has_data}
User industry: {user_industry or "Not specified"}

Generate an enthusiastic, helpful response that:
1. Welcomes them warmly
2. Highlights your marketing expertise
3. Mentions specific marketing areas you can help with
4. Includes relevant action suggestions based on their context
5. Asks what they'd like to work on first

Keep it energetic and marketing-focused (2-3 sentences)."""

        try:
            result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "general_assistance",
                    "scenario": "assistance_offer",
                    "has_data": has_data,
                    "industry": user_industry
                },
                "intent_type": "assistance",
                "temperature": 0.8,
                "max_tokens": 150
            })
            
            if not result.get("isError", False):
                response_text = result.get("content", [{}])[0].get("text", "")
                if response_text.strip():
                    return response_text.strip()
                    
        except Exception as e:
            logger.error(f"Error generating assistance response: {e}")
        
        return self._get_emergency_fallback("general_assistance")
    
    async def _generate_data_error_response(
        self, 
        mcp_server: Any, 
        context: Dict[str, Any], 
        user_message: str
    ) -> str:
        """Generate response for data processing errors."""
        
        prompt = f"""You are a marketing AI assistant. There was an issue processing the user's data.

User's request: "{user_message}"

Generate a helpful response that:
1. Acknowledges the data processing issue
2. Suggests alternative ways to help with their marketing
3. Offers to work with different data or manual input
4. Maintains confidence in creating great marketing content
5. Provides specific next steps

Keep it solution-focused and encouraging (2-3 sentences)."""

        try:
            result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "data_error_response",
                    "scenario": "data_processing_error"
                },
                "intent_type": "data_error_handling",
                "temperature": 0.6,
                "max_tokens": 130
            })
            
            if not result.get("isError", False):
                response_text = result.get("content", [{}])[0].get("text", "")
                if response_text.strip():
                    return response_text.strip()
                    
        except Exception as e:
            logger.error(f"Error generating data error response: {e}")
        
        return self._get_emergency_fallback("data_processing_error")
    
    async def _generate_generic_response(
        self, 
        mcp_server: Any, 
        context: Dict[str, Any], 
        user_message: str,
        scenario: str
    ) -> str:
        """Generate generic contextual response."""
        
        prompt = f"""You are a marketing AI assistant. Generate an appropriate response for this scenario.

User's message: "{user_message}"
Scenario: {scenario}

Generate a professional, helpful response that maintains your marketing expertise and offers assistance."""

        try:
            result = await mcp_server.call_tool("handle_conversation", {
                "message": prompt,
                "conversation_history": [],
                "user_context": {
                    "task": "generic_response",
                    "scenario": scenario
                },
                "intent_type": "generic_assistance",
                "temperature": 0.7,
                "max_tokens": 100
            })
            
            if not result.get("isError", False):
                response_text = result.get("content", [{}])[0].get("text", "")
                if response_text.strip():
                    return response_text.strip()
                    
        except Exception as e:
            logger.error(f"Error generating generic response: {e}")
        
        return self._get_emergency_fallback("generic")
    
    def _extract_task_context(self, context: Dict[str, Any], user_message: str) -> str:
        """Extract relevant task context from user context and message."""
        task_indicators = []
        
        # Check for marketing task indicators
        marketing_tasks = {
            "strategy": ["strategy", "plan", "planning"],
            "content": ["content", "blog", "article", "post"],
            "social": ["social", "instagram", "facebook", "twitter"],
            "email": ["email", "newsletter", "campaign"],
            "analysis": ["analysis", "research", "competitor", "market"]
        }
        
        message_lower = user_message.lower()
        for task_type, keywords in marketing_tasks.items():
            if any(keyword in message_lower for keyword in keywords):
                task_indicators.append(task_type)
        
        # Check context for form data
        if context.get("marketing_form_data"):
            content_type = context["marketing_form_data"].get("content_type", "")
            if content_type:
                task_indicators.append(content_type)
        
        return ", ".join(task_indicators) if task_indicators else "general marketing assistance"
    
    def _get_cache_key(self, scenario: str, user_message: str, context: Dict[str, Any]) -> str:
        """Generate cache key for response caching."""
        # Create a simple hash of key context elements
        context_elements = [
            scenario,
            user_message[:50],  # First 50 chars of message
            str(context.get("has_data_source", False)),
            context.get("user_info", {}).get("industry", "")
        ]
        return "|".join(context_elements)
    
    def _get_cached_response(self, cache_key: str) -> Optional[str]:
        """Get cached response if still valid."""
        if cache_key in self.response_cache:
            cached_data = self.response_cache[cache_key]
            if datetime.now().timestamp() - cached_data["timestamp"] < self.cache_ttl:
                return cached_data["response"]
            else:
                # Remove expired cache entry
                del self.response_cache[cache_key]
        return None
    
    def _cache_response(self, cache_key: str, response: str):
        """Cache the generated response."""
        self.response_cache[cache_key] = {
            "response": response,
            "timestamp": datetime.now().timestamp()
        }
        
        # Simple cache cleanup - remove old entries if cache gets too large
        if len(self.response_cache) > 100:
            # Remove oldest 20 entries
            sorted_items = sorted(
                self.response_cache.items(), 
                key=lambda x: x[1]["timestamp"]
            )
            for key, _ in sorted_items[:20]:
                del self.response_cache[key]
    
    def _get_emergency_fallback(self, scenario: str) -> str:
        """Get emergency fallback when all else fails."""
        emergency_fallbacks = {
            "technical_error": "I encountered a technical issue, but I'm still here to help with your marketing needs. Let's try a different approach - what marketing goal would you like to work on?",
            "service_unavailable": "Some services are temporarily unavailable, but I can still help you with marketing strategy and content creation. What would you like to focus on?",
            "clarification_needed": "I'd love to help you with your marketing needs! Could you tell me more about what specific marketing challenge you're working on?",
            "general_assistance": "I'm your marketing AI assistant, ready to help you create amazing marketing content and strategies. What marketing goal can I help you achieve today?",
            "data_processing_error": "I had trouble processing your data, but I can still create excellent marketing content for you. Let's work together on your marketing strategy - what's your main goal?",
            "generic": "I'm here to help with all your marketing needs. What marketing challenge can I assist you with today?"
        }
        
        return emergency_fallbacks.get(
            scenario, 
            "I'm your marketing AI assistant, ready to help you succeed. What marketing goal can I help you achieve?"
        )
