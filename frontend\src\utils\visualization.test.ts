/**
 * Test file for visualization utilities
 * This file contains test cases to verify the enhanced visualization processing
 */

import { processVisualizationData, validateVisualization } from './visualization';

// Mock data for testing
const mockTableData = {
  headers: ['Name', 'Age', 'City'],
  rows: [
    ['<PERSON>', '32', 'New York'],
    ['<PERSON>', '28', 'San Francisco'],
    ['<PERSON>', '45', 'Chicago']
  ]
};

const mockImageData = {
  type: 'image',
  src: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
};

const mockPandasAIResponse = {
  content: [
    {
      type: 'text',
      text: 'Analysis results for correlation analysis'
    },
    {
      type: 'image',
      src: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    }
  ]
};

const mockMixedContentResponse = {
  content: [
    {
      type: 'text',
      text: '# Analysis Results\n\nHere are the findings from your data analysis.'
    },
    {
      type: 'table',
      data: [
        { Name: 'John', Age: 32, City: 'New York' },
        { Name: 'Jane', Age: 28, City: 'San Francisco' }
      ],
      columns: ['Name', 'Age', 'City']
    },
    {
      type: 'image',
      src: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    }
  ]
};

// Test functions
export const testVisualizationProcessing = () => {
  console.log('🧪 Testing Visualization Processing...');

  // Test 1: Basic table validation
  console.log('Test 1: Basic table validation');
  const tableVisualization = validateVisualization({
    type: 'table',
    title: 'Test Table',
    data: mockTableData
  });
  console.log('✅ Table validation:', tableVisualization ? 'PASSED' : 'FAILED');

  // Test 2: Chart validation
  console.log('Test 2: Chart validation');
  const chartVisualization = validateVisualization({
    type: 'chart',
    title: 'Test Chart',
    data: { image: mockImageData.src }
  });
  console.log('✅ Chart validation:', chartVisualization ? 'PASSED' : 'FAILED');

  // Test 3: PandasAI response processing
  console.log('Test 3: PandasAI response processing');
  const pandasAIResult = processVisualizationData(mockPandasAIResponse);
  console.log('✅ PandasAI processing:', pandasAIResult ? 'PASSED' : 'FAILED');
  if (pandasAIResult) {
    console.log('   Type:', pandasAIResult.type);
    console.log('   Title:', pandasAIResult.title);
  }

  // Test 4: Mixed content processing
  console.log('Test 4: Mixed content processing');
  const mixedContentResult = processVisualizationData(mockMixedContentResponse);
  console.log('✅ Mixed content processing:', mixedContentResult ? 'PASSED' : 'FAILED');
  if (mixedContentResult) {
    console.log('   Type:', mixedContentResult.type);
    console.log('   Content items:', mixedContentResult.data.content?.length);
  }

  // Test 5: Invalid data handling
  console.log('Test 5: Invalid data handling');
  const invalidResult = processVisualizationData(null);
  console.log('✅ Invalid data handling:', invalidResult === null ? 'PASSED' : 'FAILED');

  // Test 6: Empty metadata handling
  console.log('Test 6: Empty metadata handling');
  const emptyResult = processVisualizationData({});
  console.log('✅ Empty metadata handling:', emptyResult === null ? 'PASSED' : 'FAILED');

  console.log('🎉 Visualization processing tests completed!');
};

// Test data for different scenarios
export const mockAnalysisResults = {
  correlationAnalysis: {
    content: [
      {
        type: 'text',
        text: `# Correlation Analysis

I understand you're interested in finding correlations within your 'Mobile data' dataset. Correlation analysis is a powerful tool for identifying relationships between variables. Let's dive into the results.

## Correlation Matrix

| | CustomerAge | UnitsSold | totalRevenue | Price | |
|---|---|---|---|---|---|
| CustomerAge | 1.000000 | 0.144111 | 0.142857 | 0.984521 | 0.984521 |
| UnitsSold | 0.144111 | 1.000000 | 0.984521 | 0.984521 | 0.984521 |
| totalRevenue | 0.142857 | 0.984521 | 1.000000 | 0.984521 | 0.984521 |
| Price | 0.984521 | 0.984521 | 0.984521 | 1.000000 | 1.000000 |

The correlation matrix above displays the pairwise correlation coefficients between variables. A correlation coefficient ranges from -1 (perfect negative correlation) to 1 (perfect positive correlation).

## Some interesting findings:

- **UnitsSold** and **TotalRevenue** have an extremely strong positive correlation (0.984521), which makes sense as the more units sold, the higher the total revenue.
- **Price** is also strongly correlated with **UnitsSold** (0.984521) and **TotalRevenue** (0.984521), indicating that higher prices are associated with higher unit sales.
- **CustomerAge** has a very strong correlation with **Price** (0.984521), suggesting that older customers tend to purchase higher-priced items.

## Next Steps

You might want to visualize:
- [Distribution of Values](action:visualize_distribution) - See how the values are distributed
- [Trends Over Time](action:visualize_trends) - If time-based data is available
- [Comparisons Between Groups](action:visualize_comparisons) - Compare different categories

Is there anything else you'd like to know about this data?`
      }
    ],
    task_type: 'pandasai_analysis'
  },

  visualizationResult: {
    content: [
      {
        type: 'text',
        text: 'Generated visualization based on prompt: Create an appropriate plot for Mobile data.csv.'
      },
      {
        type: 'image',
        src: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      }
    ],
    task_type: 'pandasai_visualization'
  },

  dataProfile: {
    data_info: {
      content: [
        {
          type: 'text',
          text: 'Dataset contains 1000 rows and 5 columns with customer purchase data.'
        }
      ],
      metadata: {
        shape: [1000, 5],
        columns: ['CustomerAge', 'UnitsSold', 'totalRevenue', 'Price', 'Category'],
        dtypes: {
          'CustomerAge': 'int64',
          'UnitsSold': 'int64',
          'totalRevenue': 'float64',
          'Price': 'float64',
          'Category': 'object'
        },
        missing_values: {
          'CustomerAge': 0,
          'UnitsSold': 0,
          'totalRevenue': 0,
          'Price': 0,
          'Category': 5
        }
      }
    }
  }
};

// Function to test with real-world data
export const testWithMockData = () => {
  console.log('🧪 Testing with Mock Analysis Results...');

  Object.entries(mockAnalysisResults).forEach(([testName, testData]) => {
    console.log(`\nTesting ${testName}:`);
    const result = processVisualizationData(testData);
    if (result) {
      console.log(`✅ ${testName} processed successfully`);
      console.log(`   Type: ${result.type}`);
      console.log(`   Title: ${result.title}`);
      if (result.data.content) {
        console.log(`   Content items: ${result.data.content.length}`);
      }
    } else {
      console.log(`❌ ${testName} failed to process`);
    }
  });

  console.log('\n🎉 Mock data testing completed!');
};

// Export test runner
export const runAllTests = () => {
  testVisualizationProcessing();
  console.log('\n' + '='.repeat(50) + '\n');
  testWithMockData();
};

// Auto-run tests if this file is executed directly
if (typeof window !== 'undefined' && window.location?.search?.includes('test=visualization')) {
  runAllTests();
}
