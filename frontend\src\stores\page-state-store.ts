import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Define the structure for different page states
export interface DataChatState {
  selectedPersona?: {
    id: string;
    name: string;
  };
  conversationId?: string;
  inputMessage?: string;
  attachedFiles?: string[]; // File names/IDs
  selectedDataSources?: string[];
  showSpecializedComponent?: boolean;
  activeTab?: string;
}

export interface SettingsState {
  activeTab?: string;
  formData?: Record<string, any>;
  unsavedChanges?: boolean;
  theme?: string;
  notifications?: Record<string, boolean>;
  llmProvider?: string;
  selectedModel?: string;
}

export interface DataIntegrationState {
  uploadedFiles?: string[];
  selectedFiles?: string[];
  activeTab?: string;
  formData?: Record<string, any>;
  processingStatus?: Record<string, string>;
}

export interface AIMarketplaceState {
  searchQuery?: string;
  selectedCategory?: string;
  filters?: Record<string, any>;
  sortBy?: string;
  viewMode?: 'grid' | 'list';
  selectedPersonas?: string[];
}

export interface ReportsState {
  dateRange?: {
    from: string; // Store as ISO string instead of Date object
    to: string;   // Store as ISO string instead of Date object
  };
  selectedReportType?: string;
  filters?: Record<string, any>;
  activeTab?: string;
  chartType?: string;
}

export interface MainDashboardState {
  activeSection?: string;
  selectedWidgets?: string[];
  layoutMode?: string;
  customizations?: Record<string, any>;
}

// Combined page states interface
export interface PageStates {
  dataChat?: DataChatState;
  settings?: SettingsState;
  dataIntegration?: DataIntegrationState;
  aiMarketplace?: AIMarketplaceState;
  reports?: ReportsState;
  mainDashboard?: MainDashboardState;
}

// Store interface
interface PageStateStore {
  pageStates: PageStates;
  lastVisited: Record<string, number>; // Track when each page was last visited
  
  // Generic methods
  setPageState: <K extends keyof PageStates>(
    page: K, 
    state: Partial<PageStates[K]>
  ) => void;
  
  getPageState: <K extends keyof PageStates>(
    page: K
  ) => PageStates[K] | undefined;
  
  clearPageState: <K extends keyof PageStates>(page: K) => void;
  clearAllStates: () => void;
  
  // Specific page methods for better type safety
  setDataChatState: (state: Partial<DataChatState>) => void;
  getDataChatState: () => DataChatState | undefined;
  
  setSettingsState: (state: Partial<SettingsState>) => void;
  getSettingsState: () => SettingsState | undefined;
  
  setDataIntegrationState: (state: Partial<DataIntegrationState>) => void;
  getDataIntegrationState: () => DataIntegrationState | undefined;
  
  setAIMarketplaceState: (state: Partial<AIMarketplaceState>) => void;
  getAIMarketplaceState: () => AIMarketplaceState | undefined;
  
  setReportsState: (state: Partial<ReportsState>) => void;
  getReportsState: () => ReportsState | undefined;
  
  setMainDashboardState: (state: Partial<MainDashboardState>) => void;
  getMainDashboardState: () => MainDashboardState | undefined;
  
  // Utility methods
  updateLastVisited: (page: keyof PageStates) => void;
  cleanupExpiredStates: (maxAge?: number) => void;
}

// Create the store with persistence
export const usePageStateStore = create<PageStateStore>()(
  persist(
    (set, get) => ({
      pageStates: {},
      lastVisited: {},
      
      // Generic methods
      setPageState: (page, state) => {
        set((current) => ({
          pageStates: {
            ...current.pageStates,
            [page]: {
              ...current.pageStates[page],
              ...state,
            },
          },
          lastVisited: {
            ...current.lastVisited,
            [page]: Date.now(),
          },
        }));
      },
      
      getPageState: (page) => {
        return get().pageStates[page];
      },
      
      clearPageState: (page) => {
        set((current) => {
          const newPageStates = { ...current.pageStates };
          delete newPageStates[page];
          
          const newLastVisited = { ...current.lastVisited };
          delete newLastVisited[page];
          
          return {
            pageStates: newPageStates,
            lastVisited: newLastVisited,
          };
        });
      },
      
      clearAllStates: () => {
        set({ pageStates: {}, lastVisited: {} });
      },
      
      // Specific page methods
      setDataChatState: (state) => {
        get().setPageState('dataChat', state);
      },
      
      getDataChatState: () => {
        return get().getPageState('dataChat');
      },
      
      setSettingsState: (state) => {
        get().setPageState('settings', state);
      },
      
      getSettingsState: () => {
        return get().getPageState('settings');
      },
      
      setDataIntegrationState: (state) => {
        get().setPageState('dataIntegration', state);
      },
      
      getDataIntegrationState: () => {
        return get().getPageState('dataIntegration');
      },
      
      setAIMarketplaceState: (state) => {
        get().setPageState('aiMarketplace', state);
      },
      
      getAIMarketplaceState: () => {
        return get().getPageState('aiMarketplace');
      },
      
      setReportsState: (state) => {
        get().setPageState('reports', state);
      },
      
      getReportsState: () => {
        return get().getPageState('reports');
      },
      
      setMainDashboardState: (state) => {
        get().setPageState('mainDashboard', state);
      },
      
      getMainDashboardState: () => {
        return get().getPageState('mainDashboard');
      },
      
      // Utility methods
      updateLastVisited: (page) => {
        set((current) => ({
          lastVisited: {
            ...current.lastVisited,
            [page]: Date.now(),
          },
        }));
      },
      
      cleanupExpiredStates: (maxAge = 7 * 24 * 60 * 60 * 1000) => { // Default 7 days
        const now = Date.now();
        const current = get();
        
        const newPageStates = { ...current.pageStates };
        const newLastVisited = { ...current.lastVisited };
        
        Object.keys(current.lastVisited).forEach((page) => {
          const lastVisited = current.lastVisited[page];
          if (now - lastVisited > maxAge) {
            delete newPageStates[page as keyof PageStates];
            delete newLastVisited[page];
          }
        });
        
        set({
          pageStates: newPageStates,
          lastVisited: newLastVisited,
        });
      },
    }),
    {
      name: 'datagenius-page-states',
      version: 1,
      // Only persist essential data, not temporary UI states
      partialize: (state) => ({
        pageStates: state.pageStates,
        lastVisited: state.lastVisited,
      }),
    }
  )
);
