"""
Pricing, Subscription, Discount, and Promotion models for the Datagenius backend.

This module provides SQLAlchemy models for managing pricing tiers,
subscriptions, discounts, and promotions.
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
# from sqlalchemy.ext.declarative import declarative_base # Base is imported from database
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field # Import Pydantic BaseModel and Field

from ..database import Base # Assuming your Base is in app.database

# SQLAlchemy Models

class PricingTier(Base):
    __tablename__ = "pricing_tiers"

    id = Column(String(36), primary_key=True, index=True)
    persona_id = Column(String(50), index=True, nullable=True) # Can be null if it's a general platform tier
    name = Column(String(100), nullable=False, unique=True)
    description = Column(String(255), nullable=True)
    base_price = Column(Float, nullable=False) # Could be monthly, one-time, etc.
    currency = Column(String(3), default="USD", nullable=False)
    billing_period = Column(String(50), nullable=True) # e.g., "monthly", "yearly", "one-time"
    features = Column(JSON, nullable=True) # List of features included in this tier
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship to subscriptions (one tier can have many subscriptions)
    # subscriptions = relationship("Subscription", back_populates="pricing_tier")

class Subscription(Base):
    __tablename__ = "subscriptions"

    id = Column(String(36), primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True) # Assuming you have a User model
    pricing_tier_id = Column(String(36), ForeignKey("pricing_tiers.id"), nullable=False, index=True)
    status = Column(String(50), default="active", nullable=False) # e.g., "active", "cancelled", "past_due"
    start_date = Column(DateTime, default=datetime.utcnow)
    end_date = Column(DateTime, nullable=True) # For fixed-term or cancelled subscriptions
    next_billing_date = Column(DateTime, nullable=True)
    auto_renew = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="subscriptions") # Define "subscriptions" in User model
    pricing_tier = relationship("PricingTier") # No back_populates needed if PricingTier doesn't need direct access

class Discount(Base):
    __tablename__ = "discounts"

    id = Column(String(36), primary_key=True, index=True)
    code = Column(String(50), unique=True, nullable=False, index=True)
    description = Column(String(255), nullable=True)
    discount_type = Column(String(50), nullable=False) # "percentage", "fixed_amount"
    value = Column(Float, nullable=False) # Percentage (e.g., 10 for 10%) or fixed amount
    max_uses = Column(Integer, nullable=True) # Max number of times this discount can be used overall
    uses_count = Column(Integer, default=0)
    valid_from = Column(DateTime, default=datetime.utcnow)
    valid_until = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    applicable_tier_ids = Column(JSON, nullable=True) # List of PricingTier IDs this discount applies to, or null for all
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Promotion(Base):
    __tablename__ = "promotions"

    id = Column(String(36), primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(String(255), nullable=True)
    promotion_type = Column(String(50), nullable=False) # e.g., "free_trial", "bundle_discount", "upgrade_offer"
    details = Column(JSON, nullable=True) # Specifics of the promotion
    start_date = Column(DateTime, default=datetime.utcnow)
    end_date = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    target_audience_criteria = Column(JSON, nullable=True) # Criteria for who is eligible
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Pydantic Schemas for API validation and serialization

class PricingTierBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    base_price: float = Field(..., gt=0)
    currency: str = Field("USD", min_length=3, max_length=3)
    billing_period: Optional[str] = Field("monthly", max_length=50) # e.g., "monthly", "yearly", "one-time"
    features: Optional[List[str]] = []
    persona_id: Optional[str] = Field(None, max_length=50)
    is_active: bool = True

class PricingTierCreate(PricingTierBase):
    pass

class PricingTierResponse(PricingTierBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class SubscriptionBase(BaseModel):
    user_id: int
    pricing_tier_id: str
    status: str = Field("active", max_length=50) # e.g., "active", "cancelled", "past_due"
    auto_renew: bool = True

class SubscriptionCreate(SubscriptionBase):
    pass

class SubscriptionResponse(SubscriptionBase):
    id: str
    start_date: datetime
    end_date: Optional[datetime] = None
    next_billing_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    pricing_tier: Optional[PricingTierResponse] = None # Include tier details

    class Config:
        from_attributes = True

class DiscountBase(BaseModel):
    code: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=255)
    discount_type: str = Field(..., max_length=50) # "percentage", "fixed_amount"
    value: float = Field(..., gt=0) # Percentage (e.g., 10 for 10%) or fixed amount
    max_uses: Optional[int] = Field(None, gt=0)
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None
    is_active: bool = True
    applicable_tier_ids: Optional[List[str]] = []

class DiscountCreate(DiscountBase):
    pass

class DiscountResponse(DiscountBase):
    id: str
    uses_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PromotionBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    promotion_type: str = Field(..., max_length=50) # e.g., "free_trial", "bundle_discount", "upgrade_offer"
    details: Optional[Dict[str, Any]] = {}
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_active: bool = True
    target_audience_criteria: Optional[Dict[str, Any]] = {}

class PromotionCreate(PromotionBase):
    pass

class PromotionResponse(PromotionBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Update User model to include subscriptions relationship
# This should be done in your existing User model file (e.g., backend/app/models/auth.py or user.py)
# Example:
# from sqlalchemy.orm import relationship
# class User(Base):
#     # ... other columns
#     subscriptions = relationship("Subscription", back_populates="user", cascade="all, delete-orphan")
