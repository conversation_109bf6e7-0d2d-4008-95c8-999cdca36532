"""User profile migration

Revision ID: 003
Revises: 002
Create Date: 2023-07-03

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite


# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if users table exists
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)

    # Only proceed if users table exists
    if 'users' in inspector.get_table_names():
        # Get existing columns
        columns = [col['name'] for col in inspector.get_columns('users')]

        with op.batch_alter_table('users', schema=None) as batch_op:
            # Add new profile columns if they don't exist
            if 'bio' not in columns:
                batch_op.add_column(sa.Column('bio', sa.Text(), nullable=True))

            if 'job_title' not in columns:
                batch_op.add_column(sa.Column('job_title', sa.String(100), nullable=True))

            if 'company' not in columns:
                batch_op.add_column(sa.Column('company', sa.String(100), nullable=True))

            if 'website' not in columns:
                batch_op.add_column(sa.Column('website', sa.String(255), nullable=True))

            if 'location' not in columns:
                batch_op.add_column(sa.Column('location', sa.String(100), nullable=True))

            if 'theme_preference' not in columns:
                batch_op.add_column(sa.Column('theme_preference', sa.String(20), nullable=True, server_default='light'))


def downgrade() -> None:
    # Check if users table exists
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)

    # Only proceed if users table exists
    if 'users' in inspector.get_table_names():
        # Get existing columns
        columns = [col['name'] for col in inspector.get_columns('users')]

        with op.batch_alter_table('users', schema=None) as batch_op:
            # Drop columns if they exist
            if 'theme_preference' in columns:
                batch_op.drop_column('theme_preference')

            if 'location' in columns:
                batch_op.drop_column('location')

            if 'website' in columns:
                batch_op.drop_column('website')

            if 'company' in columns:
                batch_op.drop_column('company')

            if 'job_title' in columns:
                batch_op.drop_column('job_title')

            if 'bio' in columns:
                batch_op.drop_column('bio')
