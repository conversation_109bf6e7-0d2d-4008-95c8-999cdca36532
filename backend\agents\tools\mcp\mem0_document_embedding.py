"""
Document embedding MCP tool using mem0ai for the Datagenius backend.

This module provides an MCP-compatible tool for processing documents, creating embeddings,
and storing them using mem0ai and Qdrant, replacing the previous FAISS implementation.
"""

import logging
import os
import sys
from typing import Dict, Any, Optional
import uuid
from pathlib import Path
import yaml

from ..mcp.base import BaseMCPTool
from ...utils.vector_service import VectorService

logger = logging.getLogger(__name__)

def save_yaml(data: Dict[str, Any], file_path: str) -> bool:
    """
    Save data as YAML file.

    Args:
        data: Data to save
        file_path: Path to save the file

    Returns:
        True if successful, False otherwise
    """
    try:
        with open(file_path, 'w') as f:
            yaml.dump(data, f)
        return True
    except Exception as e:
        logger.error(f"Error saving YAML file: {e}")
        return False

def load_yaml(file_path: str) -> Optional[Dict[str, Any]]:
    """
    Load data from YAML file.

    Args:
        file_path: Path to the file

    Returns:
        Loaded data or None if error
    """
    try:
        with open(file_path, 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"Error loading YAML file: {e}")
        return None

class Mem0DocumentEmbeddingTool(BaseMCPTool):
    """Tool for document embedding using mem0ai."""

    def __init__(self):
        """Initialize the document embedding tool."""
        super().__init__(
            name="mem0_document_embedding",
            description="Process documents, create embeddings, and store in mem0ai vector database",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the document file to be processed."
                    },
                    "operation": {
                        "type": "string",
                        "enum": ["embed", "query", "query_marketing_fields"],
                        "description": "The operation to perform (embed, query, or query_marketing_fields)."
                    },
                    "query": {
                        "type": "string",
                        "description": "The query string to use for 'query' or 'query_marketing_fields' operations."
                    },
                    "chunk_size": {
                        "type": "integer",
                        "default": 1000,
                        "description": "The size of chunks for splitting the document."
                    },
                    "chunk_overlap": {
                        "type": "integer",
                        "default": 200,
                        "description": "The overlap between chunks."
                    }
                },
                "required": ["file_path", "operation"]
            }
        )
        self.data_dir = "data"
        self.vector_db_dir = "vector_db"
        self.default_chunk_size = 1000
        self.default_chunk_overlap = 200
        
        # Marketing field queries for the query_marketing_fields operation
        # These queries match the form fields expected by the frontend
        self.marketing_field_queries = {
            "brand_description": "Based on the provided context, write a concise brand description. Extract information about the company's mission, values, and unique selling points.",
            "target_audience": "Based on the provided context, identify and describe the target audience or customer segments for this business. Include demographics, psychographics, and key characteristics.",
            "products_services": "Based on the provided context, list and briefly describe the main products and/or services offered by the business.",
            "marketing_goals": "Based on the provided context, identify the key marketing goals or objectives for this business. If not explicitly stated, suggest reasonable goals based on the business type and information provided.",
            "existing_content": "Based on the provided context, summarize any existing marketing content, campaigns, or channels mentioned in the document.",
            "keywords": "Based on the provided context, generate a list of 10-15 relevant keywords for this business that could be used for marketing purposes. Format as a comma-separated list.",
            "suggested_topics": "Based on the provided context, suggest 5-7 content topics that would be relevant for this business's marketing strategy. Present as a numbered list.",
            "competitive_landscape": "Based on the provided context, describe the competitive landscape for this business. Include information about competitors, market position, industry trends, and competitive advantages or challenges.",
            "budget": "Based on the provided context, identify any budget constraints, financial considerations, or resource allocations mentioned for marketing activities. If not explicitly stated, suggest reasonable budget considerations based on the business type.",
            "timeline": "Based on the provided context, identify any timeline constraints, deadlines, or scheduling requirements mentioned for marketing activities. Include project timelines, campaign schedules, or launch dates.",
            "platforms": "Based on the provided context, identify the specific platforms, channels, or mediums mentioned for marketing and content distribution. Include social media platforms, advertising channels, or communication mediums."
        }

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # Set data directory
        if "data_dir" in config:
            self.data_dir = config["data_dir"]
        
        # Set vector database directory
        if "vector_db_dir" in config:
            self.vector_db_dir = config["vector_db_dir"]
        
        # Create directories if they don't exist
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.vector_db_dir, exist_ok=True)
        
        # Initialize vector service
        self.vector_service = VectorService()
        
        logger.info(f"Initialized mem0 document embedding tool with vector database directory: {self.vector_db_dir}")

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the document embedding tool.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results
        """
        try:
            # Extract arguments
            file_path = arguments.get("file_path")
            operation = arguments.get("operation")
            query = arguments.get("query")
            chunk_size = arguments.get("chunk_size", self.default_chunk_size)
            chunk_overlap = arguments.get("chunk_overlap", self.default_chunk_overlap)
            
            # Validate file path
            if not file_path or not os.path.exists(file_path):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }
            
            # Get file extension
            file_extension = os.path.splitext(file_path)[1].lower()
            
            # Execute the operation
            if operation == "embed":
                # Process the document and create embeddings
                vector_store_id, file_info = await self.vector_service.embed_document(
                    file_path=file_path,
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap
                )
                
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Document processed and embedded successfully. Vector store ID: {vector_store_id}"
                        }
                    ],
                    "metadata": {
                        "vector_store_id": vector_store_id,
                        "file_info": file_info
                    }
                }
            
            elif operation == "query":
                # Get query
                if not query:
                    return {
                        "isError": True,
                        "content": [
                            {
                                "type": "text",
                                "text": "Query is required for 'query' operation"
                            }
                        ]
                    }
                
                # Find vector store ID for the file
                vector_store_id = await self._find_vector_store_id(file_path)
                if not vector_store_id:
                    # If vector store doesn't exist, create it
                    vector_store_id, _ = self.vector_service.embed_document(
                        file_path=file_path,
                        chunk_size=chunk_size,
                        chunk_overlap=chunk_overlap
                    )
                
                # Query the vector store
                results = self.vector_service.search_document(vector_store_id, query, limit=5)
                
                # Format the results
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "content": result.get("content", ""),
                        "metadata": result.get("metadata", {})
                    })
                
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Query results:\n\n{results[0].get('content', '') if results else 'No results found.'}"
                        }
                    ],
                    "metadata": {
                        "query": query,
                        "results": formatted_results
                    }
                }
            
            elif operation == "query_marketing_fields":
                # Find vector store ID for the file
                vector_store_id = await self._find_vector_store_id(file_path)
                if not vector_store_id:
                    # If vector store doesn't exist, create it
                    vector_store_id, _ = self.vector_service.embed_document(
                        file_path=file_path,
                        chunk_size=chunk_size,
                        chunk_overlap=chunk_overlap
                    )
                
                # Query the vector store for each marketing field
                marketing_fields = {}
                for field, query in self.marketing_field_queries.items():
                    results = self.vector_service.search_document(vector_store_id, query, limit=3)
                    if results:
                        # Combine the results
                        context = "\n\n".join([result.get("content", "") for result in results])
                        
                        # Use the context to generate a response for the field
                        from langchain_core.prompts import ChatPromptTemplate
                        from langchain_groq import ChatGroq
                        
                        # Initialize the LLM
                        llm = ChatGroq(
                            temperature=0.3,
                            model_name="llama3-70b-8192",
                            groq_api_key=os.getenv("GROQ_API_KEY", "")
                        )
                        
                        # Create the prompt
                        prompt = ChatPromptTemplate.from_template(
                            "Based on the following context, answer the question: {query}\n\nContext: {context}"
                        )
                        
                        # Create the chain
                        chain = prompt | llm
                        
                        # Execute the chain
                        response = await chain.ainvoke({"context": context, "query": query})
                        
                        # Store the response
                        marketing_fields[field] = response.content
                    else:
                        marketing_fields[field] = ""
                
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Marketing fields extracted from document: {file_path}"
                        }
                    ],
                    "metadata": {
                        "marketing_fields": marketing_fields
                    }
                }
            
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unknown operation: {operation}"
                        }
                    ]
                }
        
        except Exception as e:
            logger.error(f"Error in document embedding tool: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error processing document: {str(e)}"
                    }
                ]
            }

    async def _find_vector_store_id(self, file_path: str) -> Optional[str]:
        """
        Find the vector store ID for a file.

        Args:
            file_path: Path to the file

        Returns:
            Vector store ID if found, None otherwise
        """
        try:
            # Check if there's a YAML file with metadata for this file
            for info_file in os.listdir(self.vector_db_dir):
                if info_file.endswith("_info.yaml"):
                    info_path = os.path.join(self.vector_db_dir, info_file)
                    file_info = load_yaml(info_path)
                    
                    if file_info and file_info.get("file_path") == file_path:
                        vector_store_id = file_info.get("vector_store_id")
                        if vector_store_id:
                            logger.info(f"Found vector store ID for {file_path}: {vector_store_id}")
                            return vector_store_id
            
            logger.info(f"No vector store ID found for {file_path}")
            return None
        except Exception as e:
            logger.error(f"Error finding vector store ID: {e}")
            return None
