import { useEffect } from 'react';
import { usePageStateCleanup } from '@/hooks/use-page-state';

/**
 * Component to handle page state cleanup and maintenance.
 * Should be mounted at the app level to ensure cleanup runs on app startup.
 */
export const PageStateManager = () => {
  const { cleanupExpiredStates } = usePageStateCleanup();

  useEffect(() => {
    // Clean up expired states on app startup
    cleanupExpiredStates();
  }, [cleanupExpiredStates]);

  // This component doesn't render anything
  return null;
};

export default PageStateManager;
