#!/usr/bin/env python3
"""
Test script for LLM-based multilingual marketing intent detection.

This script demonstrates how the marketing agent now uses LLM for:
1. Language detection
2. Intent detection  
3. Multilingual response generation

No hardcoded patterns - the LLM makes all decisions.
"""

import asyncio
from typing import Dict, Any

# Mock MCP server for testing
class MockMCPServer:
    """Mock MCP server that implements the expected interface"""

    def __init__(self):
        self.name = "mock_mcp_server"

    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Mock LLM responses for testing"""
        print(f"🔧 Mock MCP Server called with tool: {tool_name}")
        print(f"📋 Parameters: {params}")

        # Handle conversation tool calls (used for LLM analysis)
        if tool_name == "handle_conversation":
            message = params.get("message", "")
            user_context = params.get("user_context", {})

            # Check if this is a language/intent analysis request
            if user_context.get("analysis_type") == "multilingual_marketing_intent":
                print("🧠 Processing YAML-based language and intent analysis request")

                # Mock YAML response for language and intent detection
                if "desarrollar" in message.lower() or "campaña" in message.lower():
                    yaml_response = """language: es
language_name: Spanish
intent_type: marketing_request
confidence: 0.95
is_marketing_request: true
marketing_action: develop
marketing_subject: campaign
reasoning: User wants to develop campaign ideas in Spanish
suggested_response_tone: professional
requires_content_generation: true"""
                elif "développer" in message.lower() or "campagne" in message.lower():
                    yaml_response = """language: fr
language_name: French
intent_type: marketing_request
confidence: 0.93
is_marketing_request: true
marketing_action: develop
marketing_subject: campaign
reasoning: User wants to develop campaign ideas in French
suggested_response_tone: consultative
requires_content_generation: true"""
                elif "developing" in message.lower() and "campaign" in message.lower():
                    yaml_response = """language: en
language_name: English
intent_type: marketing_request
confidence: 0.92
is_marketing_request: true
marketing_action: develop
marketing_subject: campaign
reasoning: User wants to develop campaign ideas in English
suggested_response_tone: enthusiastic
requires_content_generation: true"""
                elif any(greeting in message.lower() for greeting in ["hello", "hi", "hola", "bonjour", "comment", "cómo"]):
                    # Detect language for greetings
                    if "hola" in message.lower() or "cómo" in message.lower():
                        lang = "es"
                        lang_name = "Spanish"
                    elif "bonjour" in message.lower() or "comment" in message.lower():
                        lang = "fr"
                        lang_name = "French"
                    else:
                        lang = "en"
                        lang_name = "English"

                    yaml_response = f"""language: {lang}
language_name: {lang_name}
intent_type: conversational
confidence: 0.85
is_marketing_request: false
marketing_action: null
marketing_subject: null
reasoning: General conversational greeting in {lang}
suggested_response_tone: friendly
requires_content_generation: false"""
                else:
                    yaml_response = """language: en
language_name: English
intent_type: conversational
confidence: 0.85
is_marketing_request: false
marketing_action: null
marketing_subject: null
reasoning: General conversational message
suggested_response_tone: friendly
requires_content_generation: false"""

                return {
                    "isError": False,
                    "content": [{
                        "type": "text",
                        "text": yaml_response
                    }]
                }

            # Handle multilingual response generation
            elif user_context.get("task") == "multilingual_response_generation":
                print("🌍 Processing multilingual response generation request")
                language = user_context.get("target_language", "en")
                response_type = user_context.get("response_type", "general")

                responses = {
                    "en": {
                        "acknowledgment": "Great! I'm here to help with your marketing needs. I can assist with:\n\n• Strategy development\n• Campaign creation\n• Content generation\n• Social media optimization\n\nWhat would you like to focus on?",
                        "general": "Hello! I'm your marketing AI assistant. I can help you with:\n\n• Marketing strategy development\n• Campaign planning\n• Content creation\n• Social media management\n• SEO optimization\n\nWhat marketing challenge can I help you solve today?"
                    },
                    "es": {
                        "acknowledgment": "¡Perfecto! Estoy aquí para ayudarte con tus necesidades de marketing. Puedo asistirte con:\n\n• Desarrollo de estrategias\n• Creación de campañas\n• Generación de contenido\n• Optimización de redes sociales\n\n¿En qué te gustaría enfocarte?",
                        "general": "¡Hola! Soy tu asistente de marketing con IA. Puedo ayudarte con:\n\n• Desarrollo de estrategia de marketing\n• Planificación de campañas\n• Creación de contenido\n• Gestión de redes sociales\n• Optimización SEO\n\n¿Qué desafío de marketing puedo ayudarte a resolver hoy?"
                    },
                    "fr": {
                        "acknowledgment": "Parfait ! Je suis là pour vous aider avec vos besoins marketing. Je peux vous assister avec :\n\n• Développement de stratégies\n• Création de campagnes\n• Génération de contenu\n• Optimisation des médias sociaux\n\nSur quoi aimeriez-vous vous concentrer ?",
                        "general": "Bonjour ! Je suis votre assistant marketing IA. Je peux vous aider avec :\n\n• Développement de stratégie marketing\n• Planification de campagnes\n• Création de contenu\n• Gestion des médias sociaux\n• Optimisation SEO\n\nQuel défi marketing puis-je vous aider à résoudre aujourd'hui ?"
                    }
                }

                response_text = responses.get(language, responses["en"]).get(response_type, responses["en"]["general"])

                return {
                    "isError": False,
                    "content": [{"type": "text", "text": response_text}]
                }

        # Default response for unknown tools
        print(f"❌ Unknown tool: {tool_name}")
        return {"isError": True, "error": f"Unknown tool: {tool_name}"}

# Test cases
async def test_multilingual_detection():
    """Test the LLM-based multilingual detection"""
    
    # Import the component (this would normally be done differently)
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from agents.marketing_agent.components import MarketingParserComponent
    
    # Create component with mock MCP server
    component = MarketingParserComponent()
    mock_server = MockMCPServer()
    
    test_messages = [
        ("Developing new campaign ideas", "en", True),
        ("Desarrollar nuevas ideas de campaña", "es", True),
        ("Développer de nouvelles idées de campagne", "fr", True),
        ("Hello, how are you?", "en", False),
        ("Hola, ¿cómo estás?", "es", False),
        ("Bonjour, comment allez-vous?", "fr", False)
    ]
    
    print("🧪 Testing LLM-based Multilingual Marketing Intent Detection\n")
    
    for message, expected_lang, expected_marketing in test_messages:
        print(f"📝 Testing: '{message}'")
        
        # Create context with mock MCP server
        context = {
            "agent_components": [mock_server],
            "conversation_history": []
        }
        
        try:
            # Test language and intent detection
            analysis = await component._llm_detect_language_and_intent(message, context)
            
            detected_lang = analysis.get("language", "unknown")
            is_marketing = analysis.get("is_marketing_request", False)
            confidence = analysis.get("confidence", 0.0)
            reasoning = analysis.get("reasoning", "")
            
            print(f"   🌍 Language: {detected_lang} (expected: {expected_lang})")
            print(f"   🎯 Marketing Request: {is_marketing} (expected: {expected_marketing})")
            print(f"   📊 Confidence: {confidence:.2f}")
            print(f"   💭 Reasoning: {reasoning}")
            
            # Check if detection is correct
            lang_correct = detected_lang == expected_lang
            marketing_correct = is_marketing == expected_marketing
            
            if lang_correct and marketing_correct:
                print("   ✅ PASS\n")
            else:
                print("   ❌ FAIL\n")
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}\n")

if __name__ == "__main__":
    asyncio.run(test_multilingual_detection())
