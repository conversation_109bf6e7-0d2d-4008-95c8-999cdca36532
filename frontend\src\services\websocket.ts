import { toast } from '@/hooks/use-toast';

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  source?: string;
}

export interface DashboardUpdateMessage extends WebSocketMessage {
  type: 'dashboard_update';
  payload: {
    dashboard_id: string;
    update_type: 'layout' | 'data' | 'widget' | 'section' | 'data_source';
    data: any;
  };
}

export interface DataSourceUpdateMessage extends WebSocketMessage {
  type: 'data_source_update';
  payload: {
    data_source_id: string;
    dashboard_ids: string[];
    update_type: 'refresh' | 'schema_change' | 'connection_status';
    data: any;
  };
}

export interface WidgetDataUpdateMessage extends WebSocketMessage {
  type: 'widget_data_update';
  payload: {
    widget_id: string;
    dashboard_id: string;
    data: any;
    error?: string;
  };
}

export type DashboardWebSocketMessage = 
  | DashboardUpdateMessage 
  | DataSourceUpdateMessage 
  | WidgetDataUpdateMessage;

export class DashboardWebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private messageHandlers: Map<string, Set<(message: DashboardWebSocketMessage) => void>> = new Map();
  private connectionStateHandlers: Set<(connected: boolean) => void> = new Set();
  private isConnected = false;
  private shouldReconnect = true;

  constructor(private baseUrl: string = '') {
    // Use backend server for WebSocket connections
    if (!this.baseUrl) {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      // In development, backend runs on port 8000
      const backendHost = window.location.hostname + ':8000';
      this.baseUrl = `${protocol}//${backendHost}`;
    }
  }

  connect(token?: string): Promise<void> {
    // If already connected, resolve immediately
    if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
      return Promise.resolve();
    }

    // If connection is in progress, wait for it
    if (this.ws?.readyState === WebSocket.CONNECTING) {
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (this.ws?.readyState === WebSocket.OPEN) {
            resolve();
          } else if (this.ws?.readyState === WebSocket.CLOSED) {
            reject(new Error('Connection failed'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    return new Promise((resolve, reject) => {
      try {
        // Get token from parameter or localStorage
        const authToken = token || localStorage.getItem('token');
        if (!authToken) {
          console.error('❌ No token provided for WebSocket connection!');
          reject(new Error('No authentication token provided'));
          return;
        }

        // Construct WebSocket URL with proper protocol and error handling
        let wsUrl: string;
        try {
          const baseUrl = this.baseUrl.replace(/^http/, 'ws');
          wsUrl = `${baseUrl}/ws/dashboard?token=${encodeURIComponent(authToken)}`;
          console.log('🔗 Connecting to Dashboard WebSocket:', wsUrl.replace(/token=[^&]+/, 'token=***'));
        } catch (urlError) {
          console.error('Error constructing WebSocket URL:', urlError);
          reject(new Error('Failed to construct WebSocket URL'));
          return;
        }

        this.ws = new WebSocket(wsUrl);

        // Track if we've already resolved/rejected to prevent double calls
        let settled = false;

        this.ws.onopen = () => {
          if (settled) return;
          console.log('✅ Dashboard WebSocket connected successfully');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.notifyConnectionStateHandlers(true);
          settled = true;
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: DashboardWebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error, 'Raw data:', event.data);
          }
        };

        this.ws.onclose = (event) => {
          console.log(`🔌 Dashboard WebSocket disconnected: Code ${event.code}, Reason: ${event.reason || 'No reason provided'}`);
          this.isConnected = false;
          this.stopHeartbeat();
          this.notifyConnectionStateHandlers(false);

          // Handle different close codes appropriately
          if (this.shouldReconnect && event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            // Don't reconnect immediately on authentication errors
            if (event.code === 1008 || event.code === 4001) {
              console.warn('Authentication error, not attempting reconnect');
              if (!settled) {
                settled = true;
                reject(new Error('WebSocket authentication failed'));
              }
              return;
            }
            this.scheduleReconnect(authToken);
          } else if (event.code === 1000) {
            console.log('WebSocket closed normally');
          }

          // If connection failed during initial connection
          if (!settled && event.code !== 1000) {
            settled = true;
            reject(new Error(`WebSocket connection failed: ${event.code} ${event.reason || 'Unknown error'}`));
          }
        };

        this.ws.onerror = (error) => {
          console.error('❌ Dashboard WebSocket error:', error);
          // Don't reject immediately on error, wait for close event
          // This prevents double rejection
        };

        // Connection timeout with cleanup
        const timeoutId = setTimeout(() => {
          if (!settled && this.ws?.readyState !== WebSocket.OPEN) {
            console.error('⏰ WebSocket connection timeout');
            settled = true;
            if (this.ws) {
              this.ws.close();
            }
            reject(new Error('WebSocket connection timeout'));
          }
        }, 10000);

        // Clear timeout on successful connection
        if (this.ws) {
          this.ws.addEventListener('open', () => {
            clearTimeout(timeoutId);
          });
        }

      } catch (error) {
        console.error('Error in WebSocket connect method:', error);
        reject(error);
      }
    });
  }

  disconnect(): void {
    this.shouldReconnect = false;
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.notifyConnectionStateHandlers(false);
  }

  subscribe(messageType: string, handler: (message: DashboardWebSocketMessage) => void): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set());
    }
    
    this.messageHandlers.get(messageType)!.add(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.messageHandlers.get(messageType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.messageHandlers.delete(messageType);
        }
      }
    };
  }

  subscribeToConnectionState(handler: (connected: boolean) => void): () => void {
    this.connectionStateHandlers.add(handler);
    
    // Immediately notify of current state
    handler(this.isConnected);

    // Return unsubscribe function
    return () => {
      this.connectionStateHandlers.delete(handler);
    };
  }

  subscribeToDashboard(dashboardId: string): void {
    this.sendMessage({
      type: 'subscribe_dashboard',
      payload: { dashboard_id: dashboardId },
      timestamp: new Date().toISOString(),
    });
  }

  unsubscribeFromDashboard(dashboardId: string): void {
    this.sendMessage({
      type: 'unsubscribe_dashboard',
      payload: { dashboard_id: dashboardId },
      timestamp: new Date().toISOString(),
    });
  }

  subscribeToDataSource(dataSourceId: string): void {
    this.sendMessage({
      type: 'subscribe_data_source',
      payload: { data_source_id: dataSourceId },
      timestamp: new Date().toISOString(),
    });
  }

  requestWidgetRefresh(widgetId: string): void {
    this.sendMessage({
      type: 'refresh_widget',
      payload: { widget_id: widgetId },
      timestamp: new Date().toISOString(),
    });
  }

  private sendMessage(message: WebSocketMessage): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
    }
  }

  private handleMessage(message: DashboardWebSocketMessage): void {
    console.log('Received WebSocket message:', message);

    // Handle system messages
    if (message.type === 'heartbeat') {
      this.sendMessage({
        type: 'heartbeat_response',
        payload: {},
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Notify specific message type handlers
    const handlers = this.messageHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in WebSocket message handler:', error);
        }
      });
    }

    // Notify global handlers
    const globalHandlers = this.messageHandlers.get('*');
    if (globalHandlers) {
      globalHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in global WebSocket message handler:', error);
        }
      });
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.sendMessage({
        type: 'heartbeat',
        payload: {},
        timestamp: new Date().toISOString(),
      });
    }, 30000); // Send heartbeat every 30 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private scheduleReconnect(token?: string): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect(token).catch(error => {
          console.error('WebSocket reconnect failed:', error);
        });
      }
    }, delay);
  }

  private notifyConnectionStateHandlers(connected: boolean): void {
    this.connectionStateHandlers.forEach(handler => {
      try {
        handler(connected);
      } catch (error) {
        console.error('Error in connection state handler:', error);
      }
    });
  }

  getConnectionState(): boolean {
    return this.isConnected;
  }

  getReconnectAttempts(): number {
    return this.reconnectAttempts;
  }
}

// Singleton instance
export const dashboardWebSocket = new DashboardWebSocketService();

// Note: Auto-connect removed to prevent connection attempts without authentication token
// WebSocket connections are now initiated explicitly by components when needed
