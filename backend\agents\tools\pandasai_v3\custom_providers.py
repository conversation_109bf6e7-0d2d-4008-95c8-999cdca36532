"""
Custom LLM providers for PandasAI v3.

This module provides custom LLM provider implementations for PandasAI v3,
supporting various AI providers including OpenAI, Groq, Google Gemini, OpenRouter, and Requesty.
"""

import logging
import os
from typing import Dict, Any, Optional, List, Union

from pandasai.llm.base import LLM

logger = logging.getLogger(__name__)

class GroqLLM(LLM):
    """Groq LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "llama3-70b-8192"):
        """Initialize the Groq LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None

    @property
    def type(self) -> str:
        """Return the type of the LLM provider."""
        return "groq"

    @property
    def client(self):
        """Get the Groq client."""
        if self._client is None:
            try:
                import groq
                self._client = groq.Client(api_key=self.api_key)
            except ImportError:
                raise ImportError("The 'groq' package is required to use the Groq LLM provider. "
                                 "Please install it with 'pip install groq'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1024
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error generating completion with Groq: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with Groq: {e}")

    def generate_code(self, prompt: str, context=None) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt that ensures only code is returned
        code_prompt = f"""Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly.

CRITICAL REQUIREMENTS:
1. Return ONLY valid Python code without any explanations, comments, or markdown formatting
2. Do not include phrases like "Here is the code:" or wrap the code in markdown blocks
3. Ensure all parentheses, brackets, and quotes are properly matched
4. Use execute_sql_query() function to get data from the database
5. For plot results: result = {{"type": "plot", "value": "filename.png"}}
6. Always call plt.savefig() before setting the result
7. Double-check syntax - no unmatched parentheses or brackets

EXAMPLE STRUCTURE:
```
import pandas as pd
import matplotlib.pyplot as plt

sql_query = "SELECT column, COUNT(*) as count FROM table GROUP BY column"
df = execute_sql_query(sql_query)

plt.figure(figsize=(10, 6))
plt.pie(df['count'], labels=df['column'], autopct='%1.1f%%')
plt.title('Title')
plt.savefig('chart.png')

result = {{"type": "plot", "value": "chart.png"}}
```

Question: {prompt}"""

        response = self.completion(code_prompt)

        # Clean up the response to extract only the code
        # Remove common prefixes and markdown formatting
        lines = response.strip().split('\n')
        code_lines = []
        in_code_block = False

        for line in lines:
            # Skip explanatory text at the beginning
            if any(phrase in line.lower() for phrase in ['here is', 'here\'s', 'the code', 'python code', 'corrected', 'updated']):
                continue
            # Handle markdown code blocks
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                continue
            # Only include lines that look like code
            if in_code_block or line.strip().startswith(('import ', 'from ', 'def ', 'class ', 'if ', 'for ', 'while ', 'try:', 'except:', 'with ', 'result =', 'df', 'plt.', 'fig', 'ax')):
                code_lines.append(line)
            elif code_lines:  # If we've started collecting code, include other lines too
                code_lines.append(line)

        return '\n'.join(code_lines).strip()

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)


class GeminiLLM(LLM):
    """Google Gemini LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "gemini-pro"):
        """Initialize the Google Gemini LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None

    @property
    def type(self) -> str:
        """Return the type of the LLM provider."""
        return "gemini"

    @property
    def client(self):
        """Get the Google Gemini client."""
        if self._client is None:
            try:
                import google.generativeai as genai
                genai.configure(api_key=self.api_key)
                self._client = genai
            except ImportError:
                raise ImportError("The 'google-generativeai' package is required to use the Google Gemini LLM provider. "
                                 "Please install it with 'pip install google-generativeai'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            model = self.client.GenerativeModel(self.model)
            response = model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Error generating completion with Google Gemini: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with Google Gemini: {e}")

    def generate_code(self, prompt: str, context=None) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt
        code_prompt = f"Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly. Only return the code without any explanation.\n\nQuestion: {prompt}"
        return self.completion(code_prompt)

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)


class OpenAILLM(LLM):
    """OpenAI LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        """Initialize the OpenAI LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None

    @property
    def type(self) -> str:
        """Return the type of the LLM provider."""
        return "openai"

    @property
    def client(self):
        """Get the OpenAI client."""
        if self._client is None:
            try:
                import openai
                self._client = openai.OpenAI(api_key=self.api_key)
            except ImportError:
                raise ImportError("The 'openai' package is required to use the OpenAI LLM provider. "
                                 "Please install it with 'pip install openai'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1024
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error generating completion with OpenAI: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with OpenAI: {e}")

    def generate_code(self, prompt: str, context=None) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt
        code_prompt = f"Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly. Only return the code without any explanation.\n\nQuestion: {prompt}"
        return self.completion(code_prompt)

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)


class OpenRouterLLM(LLM):
    """OpenRouter LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "openai/gpt-3.5-turbo"):
        """Initialize the OpenRouter LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None
        self.base_url = "https://openrouter.ai/api/v1"

    @property
    def type(self) -> str:
        """Return the type of the LLM provider."""
        return "openrouter"

    @property
    def client(self):
        """Get the OpenRouter client."""
        if self._client is None:
            try:
                import openai
                self._client = openai.OpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url,
                    default_headers={
                        "HTTP-Referer": "https://datagenius.app",
                        "X-Title": "Datagenius App"
                    }
                )
            except ImportError:
                raise ImportError("The 'openai' package is required to use the OpenRouter LLM provider. "
                                 "Please install it with 'pip install openai'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1024
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error generating completion with OpenRouter: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with OpenRouter: {e}")

    def generate_code(self, prompt: str, context=None) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt
        code_prompt = f"Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly. Only return the code without any explanation.\n\nQuestion: {prompt}"
        return self.completion(code_prompt)

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)


class RequestyLLM(LLM):
    """Requesty LLM provider for PandasAI."""

    def __init__(self, api_key: str, model: str = "openai/gpt-3.5-turbo"):
        """Initialize the Requesty LLM provider."""
        super().__init__()
        self.api_key = api_key
        self.model = model
        self._client = None
        self.base_url = "https://router.requesty.ai/v1"

    @property
    def type(self) -> str:
        """Return the type of the LLM provider."""
        return "requesty"

    @property
    def client(self):
        """Get the Requesty client."""
        if self._client is None:
            try:
                import openai
                self._client = openai.OpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url,
                    default_headers={
                        "HTTP-Referer": "https://datagenius.app",
                        "X-Title": "Datagenius App"
                    }
                )
            except ImportError:
                raise ImportError("The 'openai' package is required to use the Requesty LLM provider. "
                                 "Please install it with 'pip install openai'.")
        return self._client

    def completion(self, prompt: str) -> str:
        """Generate a completion for the given prompt."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1024
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error generating completion with Requesty: {e}", exc_info=True)
            raise ValueError(f"Error generating completion with Requesty: {e}")

    def generate_code(self, prompt: str, context=None) -> str:
        """Generate code for the given prompt."""
        # For code generation, we use a more specific prompt
        code_prompt = f"Generate Python code to answer the following question using pandas and matplotlib/seaborn/plotly. Only return the code without any explanation.\n\nQuestion: {prompt}"
        return self.completion(code_prompt)

    def is_configured(self) -> bool:
        """Check if the LLM provider is configured."""
        return bool(self.api_key)
