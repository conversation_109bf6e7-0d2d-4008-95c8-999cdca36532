"""
Security validation and protection for dashboard operations.
Implements comprehensive security measures for dashboard access and data protection.
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Set, Union
from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.models.dashboard_customization import Dashboard, DashboardWidget, DashboardSection
from app.database import DataSource, User

logger = logging.getLogger(__name__)

class DashboardSecurityValidator:
    """Validates dashboard operations for security compliance."""
    
    # SQL injection patterns
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)",
        r"(--|#|/\*|\*/)",
        r"(\b(UNION|OR|AND)\b.*\b(SELECT|INSERT|UPDATE|DELETE)\b)",
        r"(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)",
        r"(<script|</script>|javascript:|vbscript:)",
        r"(\b(EXEC|EXECUTE|SP_|XP_)\b)",
    ]
    
    # XSS patterns
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"vbscript:",
        r"onload\s*=",
        r"onerror\s*=",
        r"onclick\s*=",
        r"onmouseover\s*=",
        r"<iframe[^>]*>",
        r"<object[^>]*>",
        r"<embed[^>]*>",
    ]
    
    # Allowed data source types
    ALLOWED_DATA_SOURCE_TYPES = {
        'csv', 'json', 'api', 'database', 'file'
    }
    
    # Maximum limits
    MAX_DASHBOARD_NAME_LENGTH = 100
    MAX_DESCRIPTION_LENGTH = 500
    MAX_WIDGETS_PER_DASHBOARD = 50
    MAX_SECTIONS_PER_DASHBOARD = 20
    MAX_QUERY_LENGTH = 5000
    MAX_CONFIG_SIZE = 10000  # bytes
    
    def __init__(self):
        self.compiled_sql_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.SQL_INJECTION_PATTERNS]
        self.compiled_xss_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.XSS_PATTERNS]

    def validate_dashboard_access(self, user: User, dashboard: Dashboard, operation: str) -> bool:
        """Validate user access to dashboard operations."""
        try:
            # Check if user owns the dashboard
            if dashboard.user_id == user.id:
                return True

            # Check if dashboard is public and operation is read-only
            if dashboard.is_public and operation in ['read', 'view']:
                return True

            # Check if user has explicit permissions (would be implemented with a permissions system)
            # For now, we'll allow admin users full access
            if user.is_superuser:
                return True

            logger.warning(f"Access denied: User {user.id} attempted {operation} on dashboard {dashboard.id}")
            return False

        except Exception as e:
            logger.error(f"Error validating dashboard access: {e}")
            return False

    def validate_input_security(self, input_text: str, field_name: str) -> Dict[str, Any]:
        """Validate input for security threats."""
        result = {
            'is_safe': True,
            'threats': [],
            'sanitized_input': input_text,
        }
        
        if not input_text:
            return result
        
        # Check for SQL injection
        for pattern in self.compiled_sql_patterns:
            if pattern.search(input_text):
                result['is_safe'] = False
                result['threats'].append(f'Potential SQL injection detected in {field_name}')
                break
        
        # Check for XSS
        for pattern in self.compiled_xss_patterns:
            if pattern.search(input_text):
                result['is_safe'] = False
                result['threats'].append(f'Potential XSS attack detected in {field_name}')
                break
        
        # Sanitize input
        result['sanitized_input'] = self.sanitize_input(input_text)
        
        return result

    def sanitize_input(self, input_text: str) -> str:
        """Sanitize input by removing potentially dangerous content."""
        if not input_text:
            return input_text
        
        # Remove script tags
        sanitized = re.sub(r'<script[^>]*>.*?</script>', '', input_text, flags=re.IGNORECASE | re.DOTALL)
        
        # Remove javascript: and vbscript: protocols
        sanitized = re.sub(r'javascript:', '', sanitized, flags=re.IGNORECASE)
        sanitized = re.sub(r'vbscript:', '', sanitized, flags=re.IGNORECASE)
        
        # Remove event handlers
        sanitized = re.sub(r'on\w+\s*=\s*["\'][^"\']*["\']', '', sanitized, flags=re.IGNORECASE)
        
        # Remove potentially dangerous HTML tags
        dangerous_tags = ['iframe', 'object', 'embed', 'form', 'input']
        for tag in dangerous_tags:
            sanitized = re.sub(f'<{tag}[^>]*>.*?</{tag}>', '', sanitized, flags=re.IGNORECASE | re.DOTALL)
            sanitized = re.sub(f'<{tag}[^>]*/?>', '', sanitized, flags=re.IGNORECASE)
        
        return sanitized.strip()

    def validate_dashboard_data(self, dashboard_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate dashboard creation/update data."""
        result = {
            'is_valid': True,
            'errors': [],
            'sanitized_data': dashboard_data.copy(),
        }
        
        # Validate name
        if 'name' in dashboard_data:
            if len(dashboard_data['name']) > self.MAX_DASHBOARD_NAME_LENGTH:
                result['is_valid'] = False
                result['errors'].append(f'Dashboard name exceeds maximum length of {self.MAX_DASHBOARD_NAME_LENGTH}')
            
            name_validation = self.validate_input_security(dashboard_data['name'], 'name')
            if not name_validation['is_safe']:
                result['is_valid'] = False
                result['errors'].extend(name_validation['threats'])
            result['sanitized_data']['name'] = name_validation['sanitized_input']
        
        # Validate description
        if 'description' in dashboard_data and dashboard_data['description']:
            if len(dashboard_data['description']) > self.MAX_DESCRIPTION_LENGTH:
                result['is_valid'] = False
                result['errors'].append(f'Description exceeds maximum length of {self.MAX_DESCRIPTION_LENGTH}')
            
            desc_validation = self.validate_input_security(dashboard_data['description'], 'description')
            if not desc_validation['is_safe']:
                result['is_valid'] = False
                result['errors'].extend(desc_validation['threats'])
            result['sanitized_data']['description'] = desc_validation['sanitized_input']
        
        # Validate configuration size
        if 'theme_config' in dashboard_data:
            config_size = len(json.dumps(dashboard_data['theme_config']))
            if config_size > self.MAX_CONFIG_SIZE:
                result['is_valid'] = False
                result['errors'].append(f'Theme configuration exceeds maximum size of {self.MAX_CONFIG_SIZE} bytes')
        
        if 'layout_config' in dashboard_data:
            config_size = len(json.dumps(dashboard_data['layout_config']))
            if config_size > self.MAX_CONFIG_SIZE:
                result['is_valid'] = False
                result['errors'].append(f'Layout configuration exceeds maximum size of {self.MAX_CONFIG_SIZE} bytes')
        
        return result

    def validate_widget_data(self, widget_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate widget creation/update data."""
        result = {
            'is_valid': True,
            'errors': [],
            'sanitized_data': widget_data.copy(),
        }
        
        # Validate title
        if 'title' in widget_data:
            title_validation = self.validate_input_security(widget_data['title'], 'title')
            if not title_validation['is_safe']:
                result['is_valid'] = False
                result['errors'].extend(title_validation['threats'])
            result['sanitized_data']['title'] = title_validation['sanitized_input']
        
        # Validate data configuration
        if 'data_config' in widget_data and widget_data['data_config']:
            data_config = widget_data['data_config']
            
            # Validate query
            if 'query' in data_config and data_config['query']:
                if len(data_config['query']) > self.MAX_QUERY_LENGTH:
                    result['is_valid'] = False
                    result['errors'].append(f'Query exceeds maximum length of {self.MAX_QUERY_LENGTH}')
                
                query_validation = self.validate_input_security(data_config['query'], 'query')
                if not query_validation['is_safe']:
                    result['is_valid'] = False
                    result['errors'].extend(query_validation['threats'])
                
                # Additional SQL validation for queries
                if self.contains_dangerous_sql(data_config['query']):
                    result['is_valid'] = False
                    result['errors'].append('Query contains potentially dangerous SQL operations')
        
        # Validate configuration sizes
        for config_field in ['data_config', 'visualization_config', 'customization']:
            if config_field in widget_data and widget_data[config_field]:
                config_size = len(json.dumps(widget_data[config_field]))
                if config_size > self.MAX_CONFIG_SIZE:
                    result['is_valid'] = False
                    result['errors'].append(f'{config_field} exceeds maximum size of {self.MAX_CONFIG_SIZE} bytes')
        
        return result

    def contains_dangerous_sql(self, query: str) -> bool:
        """Check if query contains dangerous SQL operations."""
        dangerous_operations = [
            'DROP', 'DELETE', 'INSERT', 'UPDATE', 'CREATE', 'ALTER',
            'EXEC', 'EXECUTE', 'SP_', 'XP_', 'TRUNCATE'
        ]
        
        query_upper = query.upper()
        for operation in dangerous_operations:
            if operation in query_upper:
                return True
        
        return False

    def validate_data_source_access(self, user: User, data_source_id: str, db: Session) -> bool:
        """Validate user access to data source."""
        try:
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
            if not data_source:
                return False

            # Check if user owns the data source or has access
            if data_source.user_id == user.id:
                return True

            # Check if data source is public
            if getattr(data_source, 'is_public', False):
                return True

            # Admin access
            if user.is_superuser:
                return True

            return False

        except Exception as e:
            logger.error(f"Error validating data source access: {e}")
            return False

    def validate_rate_limit(self, user_id: int, operation: str, limit: int = 100, window_minutes: int = 60) -> bool:
        """Validate rate limiting for operations (simplified implementation)."""
        # In a production environment, this would use Redis or a similar cache
        # For now, we'll implement a basic in-memory rate limiter
        
        # This is a placeholder - in production, implement proper rate limiting
        return True

    def audit_log_operation(self, user: User, operation: str, resource_type: str, resource_id: str, details: Dict[str, Any] = None):
        """Log security-relevant operations for auditing."""
        audit_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'user_id': user.id,
            'user_email': user.email,
            'operation': operation,
            'resource_type': resource_type,
            'resource_id': resource_id,
            'ip_address': getattr(user, 'current_ip', 'unknown'),
            'user_agent': getattr(user, 'current_user_agent', 'unknown'),
            'details': details or {},
        }

        # In production, this would be sent to a security logging service
        logger.info(f"Security audit: {json.dumps(audit_entry)}")

    def check_dashboard_limits(self, user: User, db: Session) -> Dict[str, Any]:
        """Check if user has reached dashboard limits."""
        try:
            user_dashboards = db.query(Dashboard).filter(Dashboard.user_id == user.id).count()
            user_widgets = db.query(DashboardWidget).filter(DashboardWidget.user_id == user.id).count()

            # Define limits based on user type (could be based on subscription, etc.)
            max_dashboards = 50  # Default limit
            max_widgets = 500   # Default limit

            # Check if user has premium features (this would be based on subscription in production)
            # For now, we'll use is_superuser as a proxy for premium access
            if user.is_superuser:
                max_dashboards = 200
                max_widgets = 2000

            return {
                'can_create_dashboard': user_dashboards < max_dashboards,
                'can_create_widget': user_widgets < max_widgets,
                'current_dashboards': user_dashboards,
                'current_widgets': user_widgets,
                'max_dashboards': max_dashboards,
                'max_widgets': max_widgets,
            }

        except Exception as e:
            logger.error(f"Error checking dashboard limits: {e}")
            return {
                'can_create_dashboard': False,
                'can_create_widget': False,
                'error': str(e),
            }

# Global security validator instance
dashboard_security = DashboardSecurityValidator()


def require_dashboard_access(operation: str):
    """Decorator to require dashboard access validation."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # This would be implemented as a proper FastAPI dependency
            # For now, it's a placeholder for the security check
            return func(*args, **kwargs)
        return wrapper
    return decorator


def validate_dashboard_operation(user: User, dashboard: Dashboard, operation: str) -> None:
    """Validate dashboard operation and raise HTTPException if invalid."""
    if not dashboard_security.validate_dashboard_access(user, dashboard, operation):
        dashboard_security.audit_log_operation(
            user, operation, 'dashboard', dashboard.id,
            {'result': 'access_denied'}
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Access denied for {operation} operation on dashboard"
        )

    dashboard_security.audit_log_operation(
        user, operation, 'dashboard', dashboard.id,
        {'result': 'access_granted'}
    )
