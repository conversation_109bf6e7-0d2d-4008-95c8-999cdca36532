/**
 * Dashboard Management Hooks
 * 
 * Custom hooks that provide clean interfaces for dashboard operations.
 * These hooks abstract the store complexity and provide optimized re-rendering.
 */

import { useCallback, useEffect, useMemo } from 'react';
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { dashboardManagementApi } from '@/lib/api';
import {
  Dashboard,
  DashboardCreate,
  DashboardUpdate,
  SectionCreate,
  WidgetCreate,
  DashboardError
} from '@/types/dashboard-customization';

/**
 * Hook for dashboard management operations
 */
export function useDashboardManagement() {
  const { toast } = useToast();
  const { isAuthenticated } = useAuth();

  const {
    dashboards,
    activeDashboardId,
    isLoading,
    error,
    pendingOperations,
    loadDashboards,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    setActiveDashboard,
    clearError
  } = useUnifiedDashboardStore();

  // Get active dashboard
  const activeDashboard = useMemo(() => 
    dashboards.find(d => d.id === activeDashboardId) || null,
    [dashboards, activeDashboardId]
  );

  // Initialize dashboards on mount (only if authenticated)
  useEffect(() => {
    if (isAuthenticated && dashboards.length === 0 && !isLoading) {
      loadDashboards();
    }
  }, [isAuthenticated, dashboards.length, isLoading, loadDashboards]);

  // Handle errors with toast notifications
  useEffect(() => {
    if (error) {
      toast({
        title: "Dashboard Error",
        description: error.message,
        variant: "destructive",
      });
      clearError();
    }
  }, [error, toast, clearError]);

  // Wrapped actions with error handling
  const handleCreateDashboard = useCallback(async (dashboard: DashboardCreate) => {
    try {
      const newDashboard = await createDashboard(dashboard);
      toast({
        title: "Dashboard Created",
        description: `"${newDashboard.name}" has been created successfully.`,
      });
      return newDashboard;
    } catch (error) {
      toast({
        title: "Failed to Create Dashboard",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [createDashboard, toast]);

  const handleUpdateDashboard = useCallback(async (id: string, updates: DashboardUpdate) => {
    try {
      const updatedDashboard = await updateDashboard(id, updates);
      toast({
        title: "Dashboard Updated",
        description: `"${updatedDashboard.name}" has been updated successfully.`,
      });
      return updatedDashboard;
    } catch (error) {
      toast({
        title: "Failed to Update Dashboard",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [updateDashboard, toast]);

  const handleDeleteDashboard = useCallback(async (id: string) => {
    const dashboard = dashboards.find(d => d.id === id);
    try {
      await deleteDashboard(id);
      toast({
        title: "Dashboard Deleted",
        description: `"${dashboard?.name || 'Dashboard'}" has been deleted successfully.`,
      });
    } catch (error) {
      toast({
        title: "Failed to Delete Dashboard",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [deleteDashboard, dashboards, toast]);

  const handleSetActiveDashboard = useCallback(async (id: string) => {
    try {
      await setActiveDashboard(id);
      const dashboard = dashboards.find(d => d.id === id);
      toast({
        title: "Dashboard Switched",
        description: `Switched to "${dashboard?.name || 'Dashboard'}".`,
      });
    } catch (error) {
      toast({
        title: "Failed to Switch Dashboard",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [setActiveDashboard, dashboards, toast]);

  // Handle bulk data source assignment
  const handleBulkAssignDataSources = useCallback(async (dashboardId: string, dataSourceIds: string[]) => {
    try {
      const result = await dashboardManagementApi.bulkAssignDataSources(dashboardId, dataSourceIds);

      // Refresh dashboard data sources
      const store = useUnifiedDashboardStore.getState();
      await store.refreshDashboardDataSources(dashboardId);

      toast({
        title: "Data Sources Assigned",
        description: `Successfully assigned ${result.assigned} data sources to the dashboard.`,
      });

      if (result.failed > 0) {
        toast({
          title: "Some Assignments Failed",
          description: `${result.failed} data sources could not be assigned. Check the details for more information.`,
          variant: "destructive",
        });
      }

      return result;
    } catch (error) {
      console.error('Error bulk assigning data sources:', error);
      toast({
        title: "Assignment Failed",
        description: error instanceof Error ? error.message : 'Failed to assign data sources',
        variant: "destructive",
      });
      throw error;
    }
  }, [toast]);

  return {
    // State
    dashboards,
    activeDashboard,
    isLoading,
    hasPendingOperations: pendingOperations.size > 0,

    // Actions
    createDashboard: handleCreateDashboard,
    updateDashboard: handleUpdateDashboard,
    deleteDashboard: handleDeleteDashboard,
    setActiveDashboard: handleSetActiveDashboard,
    refreshDashboards: loadDashboards,
    bulkAssignDataSources: handleBulkAssignDataSources,
  };
}

/**
 * Hook for dashboard layout operations
 */
export function useDashboardLayout() {
  const { toast } = useToast();
  
  const {
    currentLayout,
    isLoading,
    error,
    pendingOperations,
    loadCurrentLayout,
    createSection,
    updateSection,
    deleteSection,
    duplicateSection,
    createWidget,
    updateWidget,
    deleteWidget,
    moveWidget,
    clearError
  } = useUnifiedDashboardStore();

  // Handle errors with toast notifications
  useEffect(() => {
    if (error) {
      toast({
        title: "Layout Error",
        description: error.message,
        variant: "destructive",
      });
      clearError();
    }
  }, [error, toast, clearError]);

  // Section management with error handling
  const handleCreateSection = useCallback(async (section: SectionCreate) => {
    try {
      const newSection = await createSection(section);
      toast({
        title: "Section Created",
        description: `"${newSection.name}" section has been created.`,
      });
      return newSection;
    } catch (error) {
      toast({
        title: "Failed to Create Section",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [createSection, toast]);

  const handleUpdateSection = useCallback(async (id: string, updates: any) => {
    try {
      const updatedSection = await updateSection(id, updates);
      toast({
        title: "Section Updated",
        description: `"${updatedSection.name}" section has been updated.`,
      });
      return updatedSection;
    } catch (error) {
      toast({
        title: "Failed to Update Section",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [updateSection, toast]);

  const handleDeleteSection = useCallback(async (id: string) => {
    const section = currentLayout?.sections.find(s => s.id === id);
    try {
      await deleteSection(id);
      toast({
        title: "Section Deleted",
        description: `"${section?.name || 'Section'}" has been deleted.`,
      });
    } catch (error) {
      toast({
        title: "Failed to Delete Section",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [deleteSection, currentLayout, toast]);

  const handleDuplicateSection = useCallback(async (id: string) => {
    try {
      const duplicatedSection = await duplicateSection(id);
      toast({
        title: "Section Duplicated",
        description: `"${duplicatedSection.name}" has been created as a copy.`,
      });
      return duplicatedSection;
    } catch (error) {
      toast({
        title: "Failed to Duplicate Section",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [duplicateSection, toast]);

  // Widget management with error handling
  const handleCreateWidget = useCallback(async (widget: WidgetCreate) => {
    try {
      const newWidget = await createWidget(widget);
      toast({
        title: "Widget Created",
        description: `"${newWidget.title}" widget has been created.`,
      });
      return newWidget;
    } catch (error) {
      toast({
        title: "Failed to Create Widget",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [createWidget, toast]);

  const handleUpdateWidget = useCallback(async (id: string, updates: any) => {
    try {
      const updatedWidget = await updateWidget(id, updates);
      toast({
        title: "Widget Updated",
        description: `"${updatedWidget.title}" widget has been updated.`,
      });
      return updatedWidget;
    } catch (error) {
      toast({
        title: "Failed to Update Widget",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [updateWidget, toast]);

  const handleDeleteWidget = useCallback(async (id: string) => {
    const widget = currentLayout?.widgets.find(w => w.id === id);
    try {
      await deleteWidget(id);
      toast({
        title: "Widget Deleted",
        description: `"${widget?.title || 'Widget'}" has been deleted.`,
      });
    } catch (error) {
      toast({
        title: "Failed to Delete Widget",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [deleteWidget, currentLayout, toast]);

  const handleMoveWidget = useCallback(async (widgetId: string, targetSectionId: string, position: any) => {
    try {
      const movedWidget = await moveWidget(widgetId, targetSectionId, position);
      toast({
        title: "Widget Moved",
        description: `"${movedWidget.title}" has been moved successfully.`,
      });
      return movedWidget;
    } catch (error) {
      toast({
        title: "Failed to Move Widget",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      throw error;
    }
  }, [moveWidget, toast]);

  return {
    // State
    layout: currentLayout,
    sections: currentLayout?.sections || [],
    widgets: currentLayout?.widgets || [],
    isLoading,
    hasPendingOperations: pendingOperations.size > 0,
    
    // Actions
    refreshLayout: loadCurrentLayout,
    
    // Section actions
    createSection: handleCreateSection,
    updateSection: handleUpdateSection,
    deleteSection: handleDeleteSection,
    duplicateSection: handleDuplicateSection,
    
    // Widget actions
    createWidget: handleCreateWidget,
    updateWidget: handleUpdateWidget,
    deleteWidget: handleDeleteWidget,
    moveWidget: handleMoveWidget,
  };
}

/**
 * Hook for dashboard templates and presets
 */
export function useDashboardTemplates() {
  // Mock templates for now - will be replaced with actual API
  const templates = useMemo(() => [
    {
      id: 'analytics-template',
      name: 'Analytics Dashboard',
      description: 'Pre-configured dashboard for data analytics with charts and KPIs',
      preview: '/templates/analytics-preview.png',
      sections: [
        { name: 'Key Metrics', widgets: ['revenue-kpi', 'users-kpi', 'conversion-chart'] },
        { name: 'Trends', widgets: ['revenue-trend', 'user-growth'] }
      ]
    },
    {
      id: 'executive-template',
      name: 'Executive Summary',
      description: 'High-level overview dashboard for executives',
      preview: '/templates/executive-preview.png',
      sections: [
        { name: 'Performance', widgets: ['performance-gauge', 'targets-progress'] },
        { name: 'Insights', widgets: ['key-insights', 'recommendations'] }
      ]
    },
    {
      id: 'operational-template',
      name: 'Operations Dashboard',
      description: 'Real-time operational metrics and monitoring',
      preview: '/templates/operational-preview.png',
      sections: [
        { name: 'System Health', widgets: ['uptime-status', 'error-rates'] },
        { name: 'Activity', widgets: ['request-volume', 'response-times'] }
      ]
    }
  ], []);

  const applyTemplate = useCallback(async (templateId: string, dashboardName: string) => {
    // This will be implemented when we have template application logic
    console.log(`Applying template ${templateId} to dashboard ${dashboardName}`);
    // For now, return a mock success
    return Promise.resolve();
  }, []);

  return {
    templates,
    applyTemplate,
  };
}
