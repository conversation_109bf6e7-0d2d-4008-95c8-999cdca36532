import React from 'react';
import { motion } from 'framer-motion';
import { Loader2, Message<PERSON><PERSON>re, Wifi, WifiOff, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

interface ConnectionStatusProps {
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ status, className = '' }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connecting':
        return {
          icon: Loader2,
          text: 'Connecting...',
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          iconClass: 'animate-spin'
        };
      case 'connected':
        return {
          icon: Wifi,
          text: 'Connected',
          color: 'bg-green-100 text-green-800 border-green-200',
          iconClass: ''
        };
      case 'disconnected':
        return {
          icon: WifiOff,
          text: 'Disconnected',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          iconClass: ''
        };
      case 'error':
        return {
          icon: AlertCircle,
          text: 'Connection Error',
          color: 'bg-red-100 text-red-800 border-red-200',
          iconClass: ''
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <Badge variant="outline" className={`${config.color} ${className}`}>
      <Icon className={`h-3 w-3 mr-1 ${config.iconClass}`} />
      {config.text}
    </Badge>
  );
};

interface MessageDeliveryStatusProps {
  status: 'pending' | 'delivered' | 'failed';
  className?: string;
}

export const MessageDeliveryStatus: React.FC<MessageDeliveryStatusProps> = ({ status, className = '' }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'pending':
        return {
          icon: Clock,
          color: 'text-yellow-500',
          iconClass: ''
        };
      case 'delivered':
        return {
          icon: CheckCircle,
          color: 'text-green-500',
          iconClass: ''
        };
      case 'failed':
        return {
          icon: AlertCircle,
          color: 'text-red-500',
          iconClass: ''
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <Icon className={`h-3 w-3 ${config.color} ${config.iconClass} ${className}`} />
  );
};

interface ConversationRestorationLoaderProps {
  isRestoring: boolean;
  personaName?: string;
  conversationId?: string;
}

export const ConversationRestorationLoader: React.FC<ConversationRestorationLoaderProps> = ({
  isRestoring,
  personaName,
  conversationId
}) => {
  if (!isRestoring) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="mb-4"
    >
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex items-center gap-3">
          <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
          <div className="flex-1">
            <h4 className="font-medium text-blue-900">Restoring Conversation</h4>
            <p className="text-sm text-blue-700">
              {personaName && `Loading conversation with ${personaName}`}
              {conversationId && (
                <span className="block text-xs text-blue-600 mt-1">
                  ID: {conversationId.substring(0, 8)}...
                </span>
              )}
            </p>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

interface ChatSkeletonLoaderProps {
  messageCount?: number;
}

export const ChatSkeletonLoader: React.FC<ChatSkeletonLoaderProps> = ({ messageCount = 3 }) => {
  return (
    <div className="space-y-4">
      {Array.from({ length: messageCount }).map((_, index) => (
        <div key={index} className={`flex ${index % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
          <div className={`max-w-xs md:max-w-md lg:max-w-lg p-3 rounded-lg ${
            index % 2 === 0 ? 'bg-brand-100' : 'bg-gray-100'
          }`}>
            <div className="flex items-start gap-2">
              {index % 2 !== 0 && <Skeleton className="h-6 w-6 rounded-full mt-1" />}
              <div className="flex-1">
                <Skeleton className="h-4 w-16 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              {index % 2 === 0 && <Skeleton className="h-6 w-6 rounded-full mt-1" />}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

interface TypingIndicatorProps {
  isTyping: boolean;
  personaName?: string;
  avatarUrl?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  isTyping,
  personaName,
  avatarUrl
}) => {
  if (!isTyping) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      className="flex justify-start mb-4"
    >
      <div className="bg-gray-100 text-gray-800 p-3 rounded-lg max-w-xs">
        <div className="flex items-center gap-2">
          <div className="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium">
            {personaName ? personaName[0] : 'AI'}
          </div>
          <div className="flex items-center gap-1">
            <span className="text-sm text-gray-600">{personaName || 'AI'} is typing</span>
            <div className="flex gap-1 ml-2">
              <motion.div
                className="w-1 h-1 bg-gray-400 rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
              />
              <motion.div
                className="w-1 h-1 bg-gray-400 rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
              />
              <motion.div
                className="w-1 h-1 bg-gray-400 rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
              />
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

interface InitializationLoaderProps {
  stage: 'connecting' | 'loading_personas' | 'restoring_conversation' | 'ready';
  message?: string;
}

export const InitializationLoader: React.FC<InitializationLoaderProps> = ({ stage, message }) => {
  const getStageConfig = () => {
    switch (stage) {
      case 'connecting':
        return {
          icon: Wifi,
          text: 'Connecting to chat server...',
          progress: 25
        };
      case 'loading_personas':
        return {
          icon: MessageSquare,
          text: 'Loading AI personas...',
          progress: 50
        };
      case 'restoring_conversation':
        return {
          icon: Loader2,
          text: 'Restoring conversation...',
          progress: 75
        };
      case 'ready':
        return {
          icon: CheckCircle,
          text: 'Ready to chat!',
          progress: 100
        };
    }
  };

  const config = getStageConfig();
  const Icon = config.icon;

  return (
    <div className="flex flex-col items-center justify-center h-full py-12">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="text-center"
      >
        <Icon className={`h-12 w-12 text-brand-500 mx-auto mb-4 ${
          stage === 'restoring_conversation' ? 'animate-spin' : ''
        }`} />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {config.text}
        </h3>
        {message && (
          <p className="text-sm text-gray-600 mb-4">{message}</p>
        )}
        <div className="w-64 bg-gray-200 rounded-full h-2 mx-auto">
          <motion.div
            className="bg-brand-500 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${config.progress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
        <p className="text-xs text-gray-500 mt-2">{config.progress}% complete</p>
      </motion.div>
    </div>
  );
};
