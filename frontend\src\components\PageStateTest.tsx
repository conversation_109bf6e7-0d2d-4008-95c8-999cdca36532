import React, { useState } from 'react';
import { usePageStateDebug } from '@/hooks/use-page-state';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

/**
 * Test component to verify page state functionality.
 * This component can be temporarily added to any page to debug page state.
 */
export const PageStateTest: React.FC = () => {
  const { allStates, lastVisited, logStates } = usePageStateDebug();
  const [isVisible, setIsVisible] = useState(false);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
        >
          Debug Page State
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-auto">
      <Card className="bg-white shadow-lg border-blue-200">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Page State Debug</CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <Button
              onClick={logStates}
              variant="outline"
              size="sm"
              className="w-full"
            >
              Log States to Console
            </Button>
          </div>

          <div>
            <h4 className="text-xs font-medium text-gray-700 mb-2">Active Page States:</h4>
            <div className="space-y-1">
              {Object.keys(allStates).length === 0 ? (
                <p className="text-xs text-gray-500">No page states found</p>
              ) : (
                Object.entries(allStates).map(([page, state]) => (
                  <div key={page} className="flex items-center justify-between">
                    <Badge variant="secondary" className="text-xs">
                      {page}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {Object.keys(state || {}).length} keys
                    </span>
                  </div>
                ))
              )}
            </div>
          </div>

          <div>
            <h4 className="text-xs font-medium text-gray-700 mb-2">Last Visited:</h4>
            <div className="space-y-1">
              {Object.keys(lastVisited).length === 0 ? (
                <p className="text-xs text-gray-500">No visit history</p>
              ) : (
                Object.entries(lastVisited).map(([page, timestamp]) => (
                  <div key={page} className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {page}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {new Date(timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                ))
              )}
            </div>
          </div>

          <div className="pt-2 border-t">
            <p className="text-xs text-gray-500">
              Total states: {Object.keys(allStates).length}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PageStateTest;
