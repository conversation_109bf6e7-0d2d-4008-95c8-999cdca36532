#!/usr/bin/env python3
"""
Test script to verify the Concierge agent configuration loads properly.
"""

import sys
import logging
from pathlib import Path

# Add backend root to path
backend_root = Path(__file__).parent
sys.path.insert(0, str(backend_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_concierge_config():
    """Test that the Concierge agent configuration loads properly."""
    try:
        # Import required modules
        from agents.persona_manager import PersonaManager
        from agents.registry import AgentRegistry
        from schemas.agent_config_schemas import PersonaConfig
        
        # Initialize persona manager
        personas_dir = backend_root / "personas"
        persona_manager = PersonaManager(str(personas_dir))
        
        # Load configurations
        configs = persona_manager.load_persona_configs()
        logger.info(f"Loaded {len(configs)} persona configurations")
        
        # Check if concierge-agent is loaded
        if "concierge-agent" in configs:
            logger.info("✅ Concierge agent configuration found")
            config = configs["concierge-agent"]
            
            # Check required fields
            required_fields = ["id", "name", "agent_class", "components", "system_prompts"]
            for field in required_fields:
                if field in config:
                    logger.info(f"✅ {field}: {type(config[field])}")
                else:
                    logger.error(f"❌ Missing required field: {field}")
            
            # Check components
            components = config.get("components", [])
            logger.info(f"✅ Found {len(components)} components")
            
            # Check for MCP server component
            mcp_component = next((c for c in components if c.get("type") == "mcp_server"), None)
            if mcp_component:
                logger.info("✅ MCP server component found")
            else:
                logger.error("❌ MCP server component not found")
            
            # Check system prompts
            system_prompts = config.get("system_prompts", {})
            if "greeting" in system_prompts:
                logger.info("✅ Greeting prompt template found")
            else:
                logger.error("❌ Greeting prompt template not found")
            
            return True
        else:
            logger.error("❌ Concierge agent configuration not found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing concierge config: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = test_concierge_config()
    if success:
        logger.info("🎉 Concierge agent configuration test passed!")
        sys.exit(0)
    else:
        logger.error("💥 Concierge agent configuration test failed!")
        sys.exit(1)
