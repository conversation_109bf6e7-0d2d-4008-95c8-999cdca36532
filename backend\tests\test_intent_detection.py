#!/usr/bin/env python3
"""
Test script to verify intent detection is working correctly.
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.analysis_agent.composable_agent import ComposableAnalysisAgent

def test_intent_detection():
    """Test the fallback intent detection with various messages."""
    
    agent = ComposableAnalysisAgent()
    
    test_messages = [
        "create a pie chart of the customer gender",
        "make a bar chart showing sales by region", 
        "visualize the data with a scatter plot",
        "show me a histogram of ages",
        "analyze the trends in the data",
        "what insights can you find",
        "search for customers in New York",
        "extract entities from the document"
    ]
    
    print("Testing intent detection...")
    print("=" * 50)
    
    for message in test_messages:
        intent = agent._fallback_intent_detection(message)
        print(f"Message: '{message}'")
        print(f"Intent: {intent['primary_intent']} (confidence: {intent['confidence']})")
        print(f"Suggested tools: {intent['suggested_tools']}")
        print("-" * 30)

if __name__ == "__main__":
    test_intent_detection()
