#!/usr/bin/env python3
"""
Test script to verify that the dashboard security fix works correctly.
This script tests that User objects can be passed to dashboard security functions
without the AttributeError that was occurring before.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import User
from app.security.dashboard_security import dashboard_security
from sqlalchemy.orm import Session
from unittest.mock import Mock, MagicMock

def test_dashboard_security_with_user_object():
    """Test that dashboard security functions work with User objects."""
    
    print("Testing dashboard security functions with User objects...")
    
    # Create a mock User object with the attributes we need
    mock_user = Mock(spec=User)
    mock_user.id = 1
    mock_user.email = "<EMAIL>"
    mock_user.is_superuser = False
    mock_user.is_active = True
    
    # Create a mock database session
    mock_db = Mock(spec=Session)
    
    # Mock the query results for dashboard limits check
    mock_dashboard_query = Mock()
    mock_dashboard_query.filter.return_value.count.return_value = 5  # 5 existing dashboards

    mock_widget_query = Mock()
    mock_widget_query.join.return_value.filter.return_value.count.return_value = 10  # 10 existing widgets

    # Set up the mock to return different queries for different models
    def mock_query_side_effect(model):
        if 'Dashboard' in str(model):
            return mock_dashboard_query
        else:  # DashboardWidget
            return mock_widget_query

    mock_db.query.side_effect = mock_query_side_effect
    
    try:
        # Test check_dashboard_limits function
        print("Testing check_dashboard_limits...")
        limits = dashboard_security.check_dashboard_limits(mock_user, mock_db)
        
        # Verify the function returns expected structure
        assert 'can_create_dashboard' in limits
        assert 'can_create_widget' in limits
        assert 'current_dashboards' in limits
        assert 'current_widgets' in limits
        assert 'max_dashboards' in limits
        assert 'max_widgets' in limits
        
        print(f"✓ check_dashboard_limits returned: {limits}")
        
        # Test audit_log_operation function
        print("Testing audit_log_operation...")
        dashboard_security.audit_log_operation(
            mock_user, 
            'test_operation', 
            'dashboard', 
            'test_dashboard_id',
            {'test': 'details'}
        )
        print("✓ audit_log_operation completed without error")
        
        # Test validate_dashboard_access function
        print("Testing validate_dashboard_access...")
        mock_dashboard = Mock()
        mock_dashboard.id = 'test_dashboard_id'
        mock_dashboard.user_id = 1  # Same as mock_user.id
        mock_dashboard.is_public = False
        
        access_result = dashboard_security.validate_dashboard_access(
            mock_user, 
            mock_dashboard, 
            'read'
        )
        
        # Should return True since user owns the dashboard
        assert access_result == True
        print(f"✓ validate_dashboard_access returned: {access_result}")
        
        # Test validate_data_source_access function
        print("Testing validate_data_source_access...")

        # Mock data source query
        mock_data_source = Mock()
        mock_data_source.user_id = 1  # Same as mock_user.id

        # Reset the mock for data source query
        mock_db.query.side_effect = None
        mock_db.query.return_value.filter.return_value.first.return_value = mock_data_source
        
        data_source_access = dashboard_security.validate_data_source_access(
            mock_user,
            'test_data_source_id',
            mock_db
        )
        
        # Should return True since user owns the data source
        assert data_source_access == True
        print(f"✓ validate_data_source_access returned: {data_source_access}")
        
        print("\n🎉 All tests passed! The dashboard security fix is working correctly.")
        print("✓ User objects can now be passed to dashboard security functions")
        print("✓ No more AttributeError: 'User' object has no attribute 'get'")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_object_attributes():
    """Test that User objects have the expected attributes."""
    
    print("\nTesting User object attributes...")
    
    # Create a mock User object
    mock_user = Mock(spec=User)
    mock_user.id = 1
    mock_user.email = "<EMAIL>"
    mock_user.is_superuser = False
    mock_user.is_active = True
    
    # Test that we can access attributes directly (not with .get())
    try:
        user_id = mock_user.id
        user_email = mock_user.email
        is_superuser = mock_user.is_superuser
        is_active = mock_user.is_active
        
        print(f"✓ User.id: {user_id}")
        print(f"✓ User.email: {user_email}")
        print(f"✓ User.is_superuser: {is_superuser}")
        print(f"✓ User.is_active: {is_active}")
        
        # Test that .get() method doesn't exist (this would cause the original error)
        try:
            mock_user.get('id')
            print("❌ User object unexpectedly has .get() method")
            return False
        except AttributeError:
            print("✓ User object correctly doesn't have .get() method")
            
        return True
        
    except Exception as e:
        print(f"❌ Failed to access User attributes: {e}")
        return False

if __name__ == "__main__":
    print("Dashboard Security Fix Verification Test")
    print("=" * 50)
    
    # Run the tests
    test1_passed = test_user_object_attributes()
    test2_passed = test_dashboard_security_with_user_object()
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("The dashboard security fix is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("The dashboard security fix may not be working correctly.")
        sys.exit(1)
