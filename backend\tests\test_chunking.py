#!/usr/bin/env python3
"""
Test script to verify the streaming chunking logic works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.mixins.streaming_mixin import StreamingMixin

class TestAgent(StreamingMixin):
    """Test agent to test chunking functionality."""
    
    def __init__(self):
        self.name = "test_agent"
    
    async def process_message(self, message, user_id, conversation_id, context):
        """Dummy implementation for testing."""
        return {
            "message": "Test response for chunking",
            "metadata": {},
            "success": True
        }

def test_chunking():
    """Test the chunking functionality."""
    agent = TestAgent()
    
    # Test text that should demonstrate the spacing issue
    test_text = "I think there might be some misconception about the effects of weed. While it can certainly alter your perception and mood, it's not capable of physically transporting you to space. The active compounds in cannabis, such as THC, interact with your brain and body to produce a range of effects, including relaxation, euphoria, and altered sensory experiences."
    
    print("Original text:")
    print(f"'{test_text}'")
    print(f"Length: {len(test_text)}")
    print()
    
    # Test chunking with different sizes
    for chunk_size in [30, 50, 80]:
        print(f"Testing chunk size: {chunk_size}")
        chunks = agent._split_into_chunks(test_text, chunk_size, word_boundary=True)
        
        print(f"Number of chunks: {len(chunks)}")
        
        # Show each chunk
        for i, chunk in enumerate(chunks):
            print(f"Chunk {i+1}: '{chunk}' (length: {len(chunk)})")
        
        # Reconstruct text by concatenating chunks
        reconstructed = "".join(chunks)
        print(f"Reconstructed: '{reconstructed}'")
        print(f"Matches original: {reconstructed == test_text}")
        print(f"Reconstructed length: {len(reconstructed)}")
        print()
        
        # Check for missing spaces by looking for concatenated words
        words_original = test_text.split()
        words_reconstructed = reconstructed.split()
        print(f"Original word count: {len(words_original)}")
        print(f"Reconstructed word count: {len(words_reconstructed)}")
        
        if len(words_original) != len(words_reconstructed):
            print("❌ Word count mismatch - likely missing spaces!")
            # Find differences
            for i, (orig, recon) in enumerate(zip(words_original, words_reconstructed)):
                if orig != recon:
                    print(f"  Difference at word {i}: '{orig}' vs '{recon}'")
        else:
            print("✅ Word count matches")
        
        print("-" * 60)

if __name__ == "__main__":
    test_chunking()
