/**
 * Business Profile Auto-fill API client
 * 
 * This module provides API functions for intelligent auto-filling of business
 * profile forms using document processing and web scraping.
 */

import { apiRequest } from './api';

// Types
export interface FieldMapping {
  value: string;
  confidence: 'high' | 'medium' | 'low' | 'none';
  source: string;
  reasoning: string;
  alternatives: string[];
}

export interface AutoFillResponse {
  success: boolean;
  message: string;
  suggestions?: Record<string, any>;
  field_mappings?: Record<string, FieldMapping>;
  overall_confidence?: number;
  source_summary?: string;
  processing_notes?: string[];
  source_data?: Record<string, any>;
}

export interface ValidationResponse {
  is_valid: boolean;
  message: string;
}

export interface ProcessingStatus {
  status: 'idle' | 'processing' | 'success' | 'error';
  message?: string;
  progress?: number;
}

/**
 * Process a document file for auto-fill suggestions
 */
export const processDocumentForAutoFill = async (file: File): Promise<AutoFillResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  return apiRequest('/business-profile-autofill/process-document', {
    method: 'POST',
    body: formData,
    // Don't set Content-Type header - let browser set it for FormData
    headers: {},
  });
};



/**
 * Process a website URL for auto-fill suggestions
 */
export const processUrlForAutoFill = async (url: string): Promise<AutoFillResponse> => {
  return apiRequest('/business-profile-autofill/process-url', {
    method: 'POST',
    body: JSON.stringify({ url }),
  });
};

/**
 * Process multiple sources (documents and URLs) for comprehensive auto-fill
 */
export const processMultipleSourcesForAutoFill = async (
  files?: File[],
  urls?: string[]
): Promise<AutoFillResponse> => {
  const formData = new FormData();

  // Add files
  if (files && files.length > 0) {
    files.forEach((file) => {
      formData.append('files', file);
    });
  }

  // Add URLs
  if (urls && urls.length > 0) {
    urls.forEach((url) => {
      formData.append('urls', url);
    });
  }

  return apiRequest('/business-profile-autofill/process-multiple', {
    method: 'POST',
    body: formData,
    headers: {},
  });
};

/**
 * Validate a field value against business profile field definitions
 */
export const validateFieldValue = async (
  fieldName: string,
  value: string
): Promise<ValidationResponse> => {
  return apiRequest('/business-profile-autofill/validate-field', {
    method: 'POST',
    body: JSON.stringify({
      field_name: fieldName,
      value,
    }),
  });
};

/**
 * Get confidence level color for UI display
 */
export const getConfidenceColor = (confidence: string): string => {
  switch (confidence) {
    case 'high':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'medium':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'low':
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case 'none':
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

/**
 * Get confidence level icon
 */
export const getConfidenceIcon = (confidence: string): string => {
  switch (confidence) {
    case 'high':
      return '✓';
    case 'medium':
      return '~';
    case 'low':
      return '?';
    case 'none':
    default:
      return '○';
  }
};

/**
 * Format confidence score as percentage
 */
export const formatConfidenceScore = (score: number): string => {
  return `${Math.round(score * 100)}%`;
};

/**
 * Check if a file type is supported for auto-fill
 */
export const isSupportedFileType = (file: File): boolean => {
  const supportedExtensions = ['.pdf', '.docx', '.doc', '.txt', '.csv', '.xlsx', '.xls'];
  const fileName = file.name.toLowerCase();
  return supportedExtensions.some(ext => fileName.endsWith(ext));
};

/**
 * Get file type display name
 */
export const getFileTypeDisplayName = (fileName: string): string => {
  const extension = fileName.toLowerCase().split('.').pop();
  
  const typeMap: Record<string, string> = {
    pdf: 'PDF Document',
    docx: 'Word Document',
    doc: 'Word Document (Legacy)',
    txt: 'Text File',
    csv: 'CSV Spreadsheet',
    xlsx: 'Excel Spreadsheet',
    xls: 'Excel Spreadsheet (Legacy)',
  };

  return typeMap[extension || ''] || 'Unknown File Type';
};

/**
 * Validate URL format
 */
export const isValidUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
};

/**
 * Normalize URL (add https if missing)
 */
export const normalizeUrl = (url: string): string => {
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`;
  }
  return url;
};

/**
 * Extract domain from URL for display
 */
export const extractDomain = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return url;
  }
};

/**
 * Business profile field definitions for validation and display
 */
export const businessProfileFields = {
  name: {
    label: 'Business Name',
    description: 'The official business or company name',
    required: true,
    maxLength: 255,
  },
  description: {
    label: 'Description',
    description: 'A brief overview of what the business does',
    required: false,
    maxLength: 2000,
  },
  industry: {
    label: 'Industry',
    description: 'The industry or sector the business operates in',
    required: false,
    maxLength: 100,
  },
  business_type: {
    label: 'Business Type',
    description: 'Type of business model',
    required: false,
    options: ['B2B', 'B2C', 'B2B2C', 'marketplace', 'saas', 'ecommerce'],
  },
  business_size: {
    label: 'Business Size',
    description: 'Size of the business',
    required: false,
    options: ['startup', 'small', 'medium', 'large', 'enterprise'],
  },
  target_audience: {
    label: 'Target Audience',
    description: 'Primary target audience or customer base',
    required: false,
    maxLength: 1000,
  },
  products_services: {
    label: 'Products & Services',
    description: 'Main products or services offered',
    required: false,
    maxLength: 2000,
  },
  marketing_goals: {
    label: 'Marketing Goals',
    description: 'Marketing objectives and goals',
    required: false,
    maxLength: 1000,
  },
  competitive_landscape: {
    label: 'Competitive Landscape',
    description: 'Information about competitors and market position',
    required: false,
    maxLength: 1000,
  },
  budget_indicators: {
    label: 'Budget Indicators',
    description: 'Budget range or financial constraints',
    required: false,
    maxLength: 500,
  },
  geographic_focus: {
    label: 'Geographic Focus',
    description: 'Geographic markets or regions served',
    required: false,
    maxLength: 500,
  },
  business_stage: {
    label: 'Business Stage',
    description: 'Current stage of business development',
    required: false,
    options: ['idea', 'startup', 'growth', 'mature', 'enterprise'],
  },
};

/**
 * Get field definition by name
 */
export const getFieldDefinition = (fieldName: string) => {
  return businessProfileFields[fieldName as keyof typeof businessProfileFields];
};

/**
 * Check if field has predefined options
 */
export const fieldHasOptions = (fieldName: string): boolean => {
  const field = getFieldDefinition(fieldName);
  return field && 'options' in field;
};

/**
 * Get field options
 */
export const getFieldOptions = (fieldName: string): string[] => {
  const field = getFieldDefinition(fieldName);
  return (field && 'options' in field) ? field.options : [];
};

/**
 * Auto-fill processing utilities
 */
export class AutoFillProcessor {
  private static instance: AutoFillProcessor;
  private processingStatus: ProcessingStatus = { status: 'idle' };
  private statusCallbacks: ((status: ProcessingStatus) => void)[] = [];

  static getInstance(): AutoFillProcessor {
    if (!AutoFillProcessor.instance) {
      AutoFillProcessor.instance = new AutoFillProcessor();
    }
    return AutoFillProcessor.instance;
  }

  /**
   * Subscribe to processing status updates
   */
  onStatusChange(callback: (status: ProcessingStatus) => void): () => void {
    this.statusCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.statusCallbacks.indexOf(callback);
      if (index > -1) {
        this.statusCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Update processing status
   */
  private updateStatus(status: ProcessingStatus): void {
    this.processingStatus = status;
    this.statusCallbacks.forEach(callback => callback(status));
  }

  /**
   * Get current processing status
   */
  getStatus(): ProcessingStatus {
    return this.processingStatus;
  }

  /**
   * Process sources with status updates
   */
  async processSources(files?: File[], urls?: string[]): Promise<AutoFillResponse> {
    try {
      this.updateStatus({ status: 'processing', message: 'Processing sources...', progress: 0 });

      let result: AutoFillResponse;

      if (files && files.length === 1 && (!urls || urls.length === 0)) {
        // Single file processing - now uses AI embedding approach by default
        this.updateStatus({ status: 'processing', message: 'Processing document with AI embedding...', progress: 50 });
        result = await processDocumentForAutoFill(files[0]);
      } else if (urls && urls.length === 1 && (!files || files.length === 0)) {
        // Single URL processing
        this.updateStatus({ status: 'processing', message: 'Processing website...', progress: 50 });
        result = await processUrlForAutoFill(urls[0]);
      } else {
        // Multiple sources processing
        this.updateStatus({ status: 'processing', message: 'Processing multiple sources...', progress: 50 });
        result = await processMultipleSourcesForAutoFill(files, urls);
      }

      this.updateStatus({ 
        status: 'success', 
        message: 'Processing completed successfully', 
        progress: 100 
      });

      return result;
    } catch (error) {
      this.updateStatus({ 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Processing failed' 
      });
      throw error;
    }
  }

  /**
   * Reset processing status
   */
  reset(): void {
    this.updateStatus({ status: 'idle' });
  }
}
