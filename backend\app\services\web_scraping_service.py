"""
Web scraping service for extracting business information from websites.

This module provides functionality to scrape and parse website content
to identify business details for auto-filling business profile forms.
"""

import logging
import re
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import urljoin, urlparse
import aiohttp
from bs4 import BeautifulSoup
import html2text
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ScrapedContent:
    """Container for scraped website content."""
    url: str
    title: str
    description: str
    content: str
    meta_data: Dict[str, Any]
    structured_data: Dict[str, Any]
    contact_info: Dict[str, Any]
    social_links: List[str]
    images: List[str]

class WebScrapingService:
    """Service for scraping and extracting business information from websites."""
    
    def __init__(self):
        self.session = None
        self.html_converter = html2text.HTML2Text()
        self.html_converter.ignore_links = False
        self.html_converter.ignore_images = True
        
        # Common business-related selectors
        self.business_selectors = {
            'name': [
                'h1', '.company-name', '.business-name', '.brand-name',
                '[itemProp="name"]', '.site-title', '.logo-text'
            ],
            'description': [
                '.description', '.about', '.company-description',
                '[itemProp="description"]', '.intro', '.summary'
            ],
            'contact': [
                '.contact', '.contact-info', '.contact-details',
                '[itemProp="telephone"]', '[itemProp="email"]'
            ],
            'address': [
                '.address', '.location', '[itemProp="address"]',
                '.contact-address', '.office-location'
            ]
        }
        
        # Patterns for extracting structured information
        self.patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
            'social_media': r'(facebook|twitter|linkedin|instagram|youtube)\.com/[\w\-\.]+',
            'business_hours': r'(monday|tuesday|wednesday|thursday|friday|saturday|sunday).*?(\d{1,2}:\d{2}|\d{1,2}\s?(am|pm))',
        }

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def scrape_website(self, url: str) -> ScrapedContent:
        """
        Scrape a website and extract business information.
        
        Args:
            url: The website URL to scrape
            
        Returns:
            ScrapedContent object with extracted information
        """
        try:
            # Validate and normalize URL
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            logger.info(f"Scraping website: {url}")
            
            # Fetch the webpage
            async with self.session.get(url) as response:
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: Failed to fetch {url}")
                
                html_content = await response.text()
                content_type = response.headers.get('content-type', '')
                
                if 'text/html' not in content_type:
                    raise Exception(f"Invalid content type: {content_type}")
            
            # Parse HTML content
            soup = BeautifulSoup(html_content, 'lxml')
            
            # Extract basic information
            title = self._extract_title(soup)
            description = self._extract_description(soup)
            content = self._extract_main_content(soup)
            
            # Extract structured data
            structured_data = self._extract_structured_data(soup)
            
            # Extract contact information
            contact_info = self._extract_contact_info(soup, content)
            
            # Extract social media links
            social_links = self._extract_social_links(soup)
            
            # Extract images
            images = self._extract_images(soup, url)
            
            # Extract meta data
            meta_data = self._extract_meta_data(soup)
            
            return ScrapedContent(
                url=url,
                title=title,
                description=description,
                content=content,
                meta_data=meta_data,
                structured_data=structured_data,
                contact_info=contact_info,
                social_links=social_links,
                images=images
            )
            
        except Exception as e:
            logger.error(f"Error scraping website {url}: {e}")
            raise

    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract the page title."""
        # Try multiple sources for title
        title_sources = [
            soup.find('title'),
            soup.find('h1'),
            soup.find('meta', {'property': 'og:title'}),
            soup.find('meta', {'name': 'twitter:title'})
        ]
        
        for source in title_sources:
            if source:
                title = source.get('content') if source.name == 'meta' else source.get_text()
                if title and title.strip():
                    return title.strip()
        
        return "Unknown"

    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extract page description."""
        # Try multiple sources for description
        desc_sources = [
            soup.find('meta', {'name': 'description'}),
            soup.find('meta', {'property': 'og:description'}),
            soup.find('meta', {'name': 'twitter:description'})
        ]
        
        for source in desc_sources:
            if source and source.get('content'):
                return source.get('content').strip()
        
        # Fallback to first paragraph
        first_p = soup.find('p')
        if first_p:
            return first_p.get_text().strip()[:200] + "..."
        
        return ""

    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """Extract main content from the page."""
        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header"]):
            script.decompose()
        
        # Try to find main content area
        main_content = (
            soup.find('main') or 
            soup.find('article') or 
            soup.find('div', class_=re.compile(r'content|main|body', re.I)) or
            soup.find('body')
        )
        
        if main_content:
            # Convert to text
            text_content = self.html_converter.handle(str(main_content))
            return text_content.strip()
        
        return soup.get_text().strip()

    def _extract_structured_data(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract structured data (JSON-LD, microdata, etc.)."""
        structured_data = {}
        
        # Extract JSON-LD
        json_ld_scripts = soup.find_all('script', type='application/ld+json')
        for script in json_ld_scripts:
            try:
                import json
                data = json.loads(script.string)
                if isinstance(data, dict):
                    structured_data.update(data)
                elif isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            structured_data.update(item)
            except (json.JSONDecodeError, AttributeError):
                continue
        
        # Extract microdata
        microdata_items = soup.find_all(attrs={"itemtype": True})
        for item in microdata_items:
            item_type = item.get('itemtype', '')
            if 'Organization' in item_type or 'LocalBusiness' in item_type:
                props = {}
                for prop in item.find_all(attrs={"itemprop": True}):
                    prop_name = prop.get('itemprop')
                    prop_value = prop.get('content') or prop.get_text().strip()
                    if prop_name and prop_value:
                        props[prop_name] = prop_value
                structured_data.update(props)
        
        return structured_data

    def _extract_contact_info(self, soup: BeautifulSoup, content: str) -> Dict[str, Any]:
        """Extract contact information."""
        contact_info = {
            'emails': [],
            'phones': [],
            'addresses': []
        }
        
        # Extract emails
        emails = re.findall(self.patterns['email'], content, re.IGNORECASE)
        contact_info['emails'] = list(set(emails))
        
        # Extract phone numbers
        phones = re.findall(self.patterns['phone'], content)
        contact_info['phones'] = ['-'.join(phone[1:]) for phone in phones if phone[1]]
        
        # Extract addresses from structured data or text
        address_elements = soup.find_all(attrs={"itemprop": "address"})
        for addr in address_elements:
            contact_info['addresses'].append(addr.get_text().strip())
        
        return contact_info

    def _extract_social_links(self, soup: BeautifulSoup) -> List[str]:
        """Extract social media links."""
        social_links = []
        
        # Find all links
        links = soup.find_all('a', href=True)
        for link in links:
            href = link['href']
            if re.search(self.patterns['social_media'], href, re.IGNORECASE):
                social_links.append(href)
        
        return list(set(social_links))

    def _extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract relevant images (logos, etc.)."""
        images = []
        
        # Look for logo images
        logo_selectors = [
            'img[alt*="logo" i]',
            'img[src*="logo" i]',
            '.logo img',
            '.brand img',
            'img[class*="logo" i]'
        ]
        
        for selector in logo_selectors:
            logo_imgs = soup.select(selector)
            for img in logo_imgs:
                src = img.get('src')
                if src:
                    # Convert relative URLs to absolute
                    absolute_url = urljoin(base_url, src)
                    images.append(absolute_url)
        
        return list(set(images))

    def _extract_meta_data(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract meta data from the page."""
        meta_data = {}
        
        # Extract all meta tags
        meta_tags = soup.find_all('meta')
        for meta in meta_tags:
            name = meta.get('name') or meta.get('property')
            content = meta.get('content')
            if name and content:
                meta_data[name] = content
        
        return meta_data

    async def extract_business_info(self, url: str) -> Dict[str, Any]:
        """
        Extract business information from a website URL.
        
        Args:
            url: Website URL to scrape
            
        Returns:
            Dictionary with extracted business information
        """
        try:
            scraped_content = await self.scrape_website(url)
            
            # Process and structure the extracted information
            business_info = {
                'source_url': url,
                'business_name': self._infer_business_name(scraped_content),
                'description': scraped_content.description or scraped_content.content[:500],
                'industry': self._infer_industry(scraped_content),
                'contact_info': scraped_content.contact_info,
                'social_links': scraped_content.social_links,
                'structured_data': scraped_content.structured_data,
                'meta_data': scraped_content.meta_data,
                'content_summary': scraped_content.content[:1000] if scraped_content.content else "",
                'extraction_timestamp': asyncio.get_event_loop().time()
            }
            
            return business_info
            
        except Exception as e:
            logger.error(f"Error extracting business info from {url}: {e}")
            raise

    def _infer_business_name(self, content: ScrapedContent) -> str:
        """Infer business name from scraped content."""
        # Try structured data first
        if content.structured_data:
            name = (
                content.structured_data.get('name') or
                content.structured_data.get('legalName') or
                content.structured_data.get('alternateName')
            )
            if name:
                return name
        
        # Try meta data
        if content.meta_data:
            name = (
                content.meta_data.get('og:site_name') or
                content.meta_data.get('application-name')
            )
            if name:
                return name
        
        # Fallback to title
        return content.title

    def _infer_industry(self, content: ScrapedContent) -> str:
        """Infer industry from scraped content."""
        # Industry keywords mapping
        industry_keywords = {
            'technology': ['software', 'tech', 'digital', 'app', 'platform', 'saas', 'ai', 'machine learning'],
            'healthcare': ['health', 'medical', 'hospital', 'clinic', 'doctor', 'pharmacy'],
            'finance': ['bank', 'financial', 'investment', 'insurance', 'loan', 'credit'],
            'retail': ['shop', 'store', 'retail', 'ecommerce', 'marketplace', 'fashion'],
            'education': ['school', 'university', 'education', 'learning', 'training', 'course'],
            'real_estate': ['real estate', 'property', 'housing', 'rental', 'mortgage'],
            'food': ['restaurant', 'food', 'catering', 'cafe', 'dining', 'delivery'],
            'automotive': ['car', 'auto', 'vehicle', 'automotive', 'dealership'],
            'construction': ['construction', 'building', 'contractor', 'renovation'],
            'consulting': ['consulting', 'advisory', 'professional services', 'strategy']
        }
        
        content_text = (content.content + ' ' + content.description + ' ' + content.title).lower()
        
        for industry, keywords in industry_keywords.items():
            if any(keyword in content_text for keyword in keywords):
                return industry.replace('_', ' ').title()
        
        return "Other"
