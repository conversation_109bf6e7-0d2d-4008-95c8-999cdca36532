import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Paperclip, 
  Mic, 
  MicOff, 
  Database, 
  X, 
  FileText, 
  Image as ImageIcon,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ConnectionStatus } from './ChatLoadingStates';

interface AttachedFile {
  id: string;
  name: string;
  type: string;
  size: number;
}

interface AttachedDataSource {
  id: string;
  name: string;
  type: string;
}

interface EnhancedChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onAttachFile?: () => void;
  onAttachDataSource?: () => void;
  onStartRecording?: () => void;
  onStopRecording?: () => void;
  attachedFiles?: AttachedFile[];
  attachedDataSources?: AttachedDataSource[];
  onRemoveFile?: (fileId: string) => void;
  onRemoveDataSource?: (sourceId: string) => void;
  isLoading?: boolean;
  isRecording?: boolean;
  connectionStatus?: 'connecting' | 'connected' | 'disconnected' | 'error';
  placeholder?: string;
  disabled?: boolean;
  maxLength?: number;
  showAttachments?: boolean;
  className?: string;
}

export const EnhancedChatInput: React.FC<EnhancedChatInputProps> = ({
  value,
  onChange,
  onSend,
  onAttachFile,
  onAttachDataSource,
  onStartRecording,
  onStopRecording,
  attachedFiles = [],
  attachedDataSources = [],
  onRemoveFile,
  onRemoveDataSource,
  isLoading = false,
  isRecording = false,
  connectionStatus = 'connected',
  placeholder = "Type your message...",
  disabled = false,
  maxLength = 4000,
  showAttachments = true,
  className = ''
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [charCount, setCharCount] = useState(0);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [value]);

  // Update character count
  useEffect(() => {
    setCharCount(value.length);
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!disabled && !isLoading && value.trim() && connectionStatus === 'connected') {
        onSend();
      }
    }
  };

  const handleSend = () => {
    if (!disabled && !isLoading && value.trim() && connectionStatus === 'connected') {
      onSend();
    }
  };

  const toggleRecording = () => {
    if (isRecording) {
      onStopRecording?.();
    } else {
      onStartRecording?.();
    }
  };

  const canSend = !disabled && !isLoading && value.trim() && connectionStatus === 'connected';
  const hasAttachments = attachedFiles.length > 0 || attachedDataSources.length > 0;

  return (
    <div className={`bg-white border-t border-gray-200 ${className}`}>
      {/* Attachments Display */}
      <AnimatePresence>
        {showAttachments && hasAttachments && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="px-4 py-2 border-b border-gray-100 bg-gray-50"
          >
            <div className="flex flex-wrap gap-2">
              {/* Attached Files */}
              {attachedFiles.map((file) => (
                <motion.div
                  key={file.id}
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.8, opacity: 0 }}
                >
                  <Badge variant="secondary" className="flex items-center gap-1 pr-1">
                    <FileText className="h-3 w-3" />
                    <span className="text-xs truncate max-w-24">{file.name}</span>
                    {onRemoveFile && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-gray-300"
                        onClick={() => onRemoveFile(file.id)}
                      >
                        <X className="h-2 w-2" />
                      </Button>
                    )}
                  </Badge>
                </motion.div>
              ))}

              {/* Attached Data Sources */}
              {attachedDataSources.map((source) => (
                <motion.div
                  key={source.id}
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.8, opacity: 0 }}
                >
                  <Badge variant="outline" className="flex items-center gap-1 pr-1 border-blue-200 text-blue-700">
                    <Database className="h-3 w-3" />
                    <span className="text-xs truncate max-w-24">{source.name}</span>
                    {onRemoveDataSource && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-blue-100"
                        onClick={() => onRemoveDataSource(source.id)}
                      >
                        <X className="h-2 w-2" />
                      </Button>
                    )}
                  </Badge>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Input Area */}
      <div className="p-4">
        {/* Connection Status */}
        <div className="flex items-center justify-between mb-3">
          <ConnectionStatus status={connectionStatus} />
          {maxLength && (
            <span className={`text-xs ${
              charCount > maxLength * 0.9 ? 'text-red-500' : 'text-gray-400'
            }`}>
              {charCount}/{maxLength}
            </span>
          )}
        </div>

        {/* Input Container */}
        <div className={`relative border rounded-lg transition-all duration-200 ${
          isFocused ? 'border-brand-500 ring-1 ring-brand-500' : 'border-gray-300'
        } ${disabled ? 'bg-gray-50' : 'bg-white'}`}>
          <Textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            disabled={disabled}
            maxLength={maxLength}
            className="min-h-[44px] max-h-[120px] resize-none border-0 focus:ring-0 focus:border-0 bg-transparent"
            style={{ paddingRight: '120px' }}
          />

          {/* Action Buttons */}
          <div className="absolute right-2 bottom-2 flex items-center gap-1">
            {/* Attachment Button */}
            {onAttachFile && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={disabled}
                  >
                    <Paperclip className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2">
                  <div className="space-y-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={onAttachFile}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Attach File
                    </Button>
                    {onAttachDataSource && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start"
                        onClick={onAttachDataSource}
                      >
                        <Database className="h-4 w-4 mr-2" />
                        Attach Data Source
                      </Button>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
            )}

            {/* Recording Button */}
            {(onStartRecording || onStopRecording) && (
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 w-8 p-0 ${isRecording ? 'text-red-500 bg-red-50' : ''}`}
                onClick={toggleRecording}
                disabled={disabled}
              >
                {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              </Button>
            )}

            {/* Send Button */}
            <Button
              size="sm"
              className="h-8 w-8 p-0"
              onClick={handleSend}
              disabled={!canSend}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Error Message */}
        {connectionStatus === 'error' && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2 flex items-center gap-2 text-red-600 text-sm"
          >
            <AlertCircle className="h-4 w-4" />
            <span>Connection error. Please check your internet connection.</span>
          </motion.div>
        )}

        {/* Disconnected Message */}
        {connectionStatus === 'disconnected' && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2 flex items-center gap-2 text-gray-600 text-sm"
          >
            <AlertCircle className="h-4 w-4" />
            <span>Disconnected. Attempting to reconnect...</span>
          </motion.div>
        )}
      </div>
    </div>
  );
};
