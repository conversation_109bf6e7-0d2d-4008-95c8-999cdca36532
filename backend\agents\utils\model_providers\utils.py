"""
Utility functions for the model provider system.

This module provides utility functions for working with the model provider system.
"""

import logging
import os
import time
import asyncio
from typing import Dict, Any, Optional, Union, List

# Import LangChain models with compatibility for different versions
try:
    # Try newer LangChain structure
    from langchain_core.language_models.base import BaseLanguageModel
    from langchain_core.language_models.chat_models import BaseChatModel
    from langchain_core.runnables.base import Runnable
except ImportError:
    try:
        # Try older LangChain structure
        from langchain.schema.language_model import BaseLanguageModel
        from langchain_core.language_models.chat_models import BaseChatModel
        from langchain_core.runnables.base import Runnable
    except ImportError:
        # Fallback to even older structure
        from langchain.base_language import BaseLanguageModel
        from langchain.chat_models.base import BaseChatModel
        # For very old versions, Runnable might not exist
        try:
            from langchain_core.runnables.base import Runnable
        except ImportError:
            Runnable = object  # Fallback to object if <PERSON>na<PERSON> doesn't exist

from .registry import ModelProviderRegistry
from .exceptions import ModelProviderError, ProviderNotFoundError, ProviderNotAvailableError
from .cache import get_cached_response, cache_response, clear_cache, get_cache_stats
from .config import PROVIDER_CONFIG
from .monitoring import record_model_usage, get_usage_stats, get_request_history, clear_usage_history, ModelUsageTracker

# Configure logging
logger = logging.getLogger(__name__)


async def get_model(
    provider_id: Optional[str] = None,
    model_id: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None,
    use_cache: bool = True
) -> Union[BaseLanguageModel, BaseChatModel]:
    """
    Get a model instance from a provider.

    This is a convenience function that wraps ModelProviderRegistry.get_model.

    Args:
        provider_id: ID of the provider (uses default if None)
        model_id: ID of the model (uses provider's default if None)
        config: Optional configuration for the model
        use_cache: Whether to use the response cache

    Returns:
        Initialized model instance

    Raises:
        ModelProviderError: If there's an error getting the model
    """
    try:
        # If no provider_id is specified, use the default provider
        if provider_id is None:
            # Find the first provider with an API key
            for pid, pconfig in PROVIDER_CONFIG.items():
                if pconfig["api_key"] or pid == "ollama":
                    provider_id = pid
                    logger.info(f"Using default provider: {provider_id}")
                    break

            # If no provider is available, use Ollama as a fallback
            if provider_id is None:
                provider_id = "ollama"
                logger.info("No provider with API key found, using Ollama as fallback")

        # If no model_id is specified, use the default model for the provider
        if model_id is None and provider_id is not None:
            provider_config = PROVIDER_CONFIG.get(provider_id, {})
            model_id = provider_config.get("default_model")
            logger.info(f"Using default model for {provider_id}: {model_id}")

        # Add retry logic for model initialization
        max_retries = 3
        retry_delay = 1  # seconds
        last_exception = None

        for attempt in range(max_retries):
            try:
                # Get the model from the registry
                model = await ModelProviderRegistry.get_model(provider_id, model_id, config)
                # If successful, break out of the retry loop
                break
            except Exception as e:
                last_exception = e
                logger.warning(f"Attempt {attempt+1}/{max_retries} failed to initialize model {model_id} from provider {provider_id}: {str(e)}")

                # Check if this is a connection error
                if "connection" in str(e).lower() or "timeout" in str(e).lower() or "socket" in str(e).lower() or "websocket" in str(e).lower():
                    if attempt < max_retries - 1:  # Don't sleep on the last attempt
                        logger.info(f"Connection error detected, retrying in {retry_delay} seconds...")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    # If it's not a connection error, don't retry
                    raise

        # If we've exhausted all retries and still have an exception, raise it
        if 'model' not in locals():
            if last_exception:
                raise last_exception
            else:
                raise ModelProviderError(f"Failed to initialize model {model_id} from provider {provider_id} after {max_retries} attempts")

        # If caching is enabled, wrap the model with caching functionality
        if use_cache:
            # Check if the model has an ainvoke method
            if hasattr(model, 'ainvoke'):
                # Create a wrapper for the model that caches responses and tracks usage
                original_ainvoke = model.ainvoke

                async def cached_monitored_ainvoke(prompt, **kwargs):
                    # Check if the response is in the cache
                    cached_response = await get_cached_response(provider_id, model_id, prompt, kwargs)
                    if cached_response is not None:
                        logger.info(f"Using cached response for {provider_id}/{model_id}")

                        # Record cache hit in monitoring
                        await record_model_usage(
                            provider_id=provider_id,
                            model_id=model_id,
                            prompt_tokens=len(str(prompt).split()),  # Rough estimate
                            completion_tokens=len(str(cached_response).split()),  # Rough estimate
                            latency=0.001,  # Negligible latency for cache hit
                            cost=0.0,  # No cost for cache hit
                            error=None
                        )

                        return cached_response

                    # Create a usage tracker
                    start_time = time.time()
                    error = None

                    try:
                        # Get the response from the model
                        # If this is called from our wrapper, we need to use the wrapped model's methods
                        if hasattr(model, 'model') and hasattr(model, '__getattr__'):
                            # This is our wrapper class
                            wrapped_model = model.model

                            # Determine which method to call based on the model type
                            if hasattr(wrapped_model, 'agenerate'):
                                # Legacy LangChain style
                                response = await wrapped_model.agenerate([prompt])
                                result = response.generations[0][0].text
                            elif hasattr(wrapped_model, 'acompletion'):
                                # OpenAI style
                                response = await wrapped_model.acompletion(
                                    prompt=prompt,
                                    **kwargs
                                )
                                result = response.choices[0].text
                            elif hasattr(wrapped_model, 'acall'):
                                # Some LangChain models use acall
                                response = await wrapped_model.acall(prompt, **kwargs)
                                result = response
                            else:
                                # Generic fallback
                                logger.warning(f"Unknown model interface for {wrapped_model.__class__.__name__}, attempting direct call")
                                result = await wrapped_model(prompt, **kwargs)

                            response = result
                        else:
                            # Normal case - use the original ainvoke method
                            response = await original_ainvoke(prompt, **kwargs)

                        # Cache the response
                        await cache_response(provider_id, model_id, prompt, kwargs, response)

                        return response
                    except Exception as e:
                        error = str(e)
                        logger.error(f"Error in cached_monitored_ainvoke: {str(e)}", exc_info=True)
                        raise
                    finally:
                        # Calculate latency
                        latency = time.time() - start_time

                        # Record usage in monitoring
                        await record_model_usage(
                            provider_id=provider_id,
                            model_id=model_id,
                            prompt_tokens=len(str(prompt).split()),  # Rough estimate
                            completion_tokens=len(str(response).split()) if 'response' in locals() else 0,  # Rough estimate
                            latency=latency,
                            cost=0.0,  # We don't have accurate cost information yet
                            error=error
                        )

                # Try to replace the model's ainvoke method with our cached and monitored version
                try:
                    model.ainvoke = cached_monitored_ainvoke
                except Exception as e:
                    logger.warning(f"Could not set ainvoke method on model: {str(e)}")
                    # Model doesn't allow setting attributes (e.g., Pydantic models)
                    # Create a wrapper class that delegates to the original model
                    logger.info(f"Creating wrapper for {model.__class__.__name__}")

                    class ModelWrapper(Runnable):
                        def __init__(self, wrapped_model):
                            super().__init__()
                            self.model = wrapped_model

                        async def ainvoke(self, prompt, **kwargs):
                            # Handle different prompt formats for LangChain models
                            try:
                                # Check if this is a ChatGroq or similar LangChain chat model
                                if hasattr(self.model, '__class__') and 'Chat' in self.model.__class__.__name__:
                                    # This is a chat model, handle message format properly
                                    if isinstance(prompt, list):
                                        # Convert dict messages to LangChain message objects
                                        from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

                                        langchain_messages = []
                                        for msg in prompt:
                                            if isinstance(msg, dict):
                                                role = msg.get("role", "user")
                                                content = msg.get("content", "")

                                                if role == "system":
                                                    langchain_messages.append(SystemMessage(content=content))
                                                elif role == "assistant" or role == "ai":
                                                    langchain_messages.append(AIMessage(content=content))
                                                else:  # user or default
                                                    langchain_messages.append(HumanMessage(content=content))
                                            else:
                                                # Assume it's already a LangChain message object
                                                langchain_messages.append(msg)

                                        # Use agenerate instead of ainvoke to avoid the dict response issue
                                        if hasattr(self.model, 'agenerate'):
                                            response = await self.model.agenerate([langchain_messages])
                                            result = response.generations[0][0].text

                                            # Create a response-like object with content attribute
                                            class ResponseWrapper:
                                                def __init__(self, content):
                                                    self.content = content
                                            return ResponseWrapper(result)
                                        else:
                                            # Fallback: try the original ainvoke but handle dict response
                                            response = await self.model.ainvoke(langchain_messages, **kwargs)

                                            # Handle dict response from ChatGroq
                                            if isinstance(response, dict):
                                                content = response.get('content', str(response))
                                                class ResponseWrapper:
                                                    def __init__(self, content):
                                                        self.content = content
                                                return ResponseWrapper(content)
                                            elif hasattr(response, 'content'):
                                                return response
                                            else:
                                                # Create a response-like object with content attribute
                                                class ResponseWrapper:
                                                    def __init__(self, content):
                                                        self.content = content
                                                return ResponseWrapper(str(response))

                                    else:
                                        # Single prompt string, convert to HumanMessage
                                        from langchain_core.messages import HumanMessage

                                        # Use agenerate instead of ainvoke to avoid the dict response issue
                                        if hasattr(self.model, 'agenerate'):
                                            response = await self.model.agenerate([[HumanMessage(content=str(prompt))]])
                                            result = response.generations[0][0].text

                                            class ResponseWrapper:
                                                def __init__(self, content):
                                                    self.content = content
                                            return ResponseWrapper(result)
                                        else:
                                            # Fallback: try the original ainvoke but handle dict response
                                            response = await self.model.ainvoke([HumanMessage(content=str(prompt))], **kwargs)

                                            # Handle dict response from ChatGroq
                                            if isinstance(response, dict):
                                                content = response.get('content', str(response))
                                                class ResponseWrapper:
                                                    def __init__(self, content):
                                                        self.content = content
                                                return ResponseWrapper(content)
                                            elif hasattr(response, 'content'):
                                                return response
                                            else:
                                                class ResponseWrapper:
                                                    def __init__(self, content):
                                                        self.content = content
                                                return ResponseWrapper(str(response))

                                # For non-chat models, use the original logic
                                elif hasattr(self.model, 'agenerate'):
                                    # Legacy LangChain style
                                    response = await self.model.agenerate([prompt])
                                    return response.generations[0][0].text
                                elif hasattr(self.model, 'acompletion'):
                                    # OpenAI style
                                    response = await self.model.acompletion(
                                        prompt=prompt,
                                        **kwargs
                                    )
                                    return response.choices[0].text
                                elif hasattr(self.model, 'acall'):
                                    # Some LangChain models use acall
                                    return await self.model.acall(prompt, **kwargs)
                                else:
                                    # Generic fallback
                                    logger.warning(f"Unknown model interface for {self.model.__class__.__name__}, attempting direct call")
                                    return await self.model(prompt, **kwargs)

                            except Exception as e:
                                logger.error(f"Error in ModelWrapper.ainvoke: {str(e)}", exc_info=True)
                                raise

                        def invoke(self, input, config=None, **kwargs):
                            """Invoke the model synchronously."""
                            # Delegate to the wrapped model if it has invoke
                            if hasattr(self.model, 'invoke'):
                                return self.model.invoke(input, config=config, **kwargs)
                            else:
                                # Implement synchronous fallback using asyncio
                                import asyncio
                                try:
                                    loop = asyncio.get_event_loop()
                                    if loop.is_running():
                                        # If we're in an async context, we can't use run()
                                        raise RuntimeError("Cannot run sync invoke in async context. Use ainvoke instead.")
                                    else:
                                        return loop.run_until_complete(self.model.ainvoke(input, config=config, **kwargs))
                                except RuntimeError:
                                    # Create new event loop if none exists
                                    loop = asyncio.new_event_loop()
                                    asyncio.set_event_loop(loop)
                                    try:
                                        return loop.run_until_complete(self.model.ainvoke(input, config=config, **kwargs))
                                    finally:
                                        loop.close()

                        @property
                        def InputType(self):
                            """Return the input type for this runnable."""
                            return str

                        @property
                        def OutputType(self):
                            """Return the output type for this runnable."""
                            return str

                        def __getattr__(self, name):
                            return getattr(self.model, name)

                    # Replace the model with our wrapper
                    model = ModelWrapper(model)
            else:
                # Model doesn't have ainvoke method, log a warning
                logger.warning(f"Model {model_id} from provider {provider_id} doesn't have an ainvoke method, creating wrapper")

                # Create a wrapper class that provides an ainvoke method
                class ModelWrapper(Runnable):
                    def __init__(self, wrapped_model):
                        super().__init__()
                        self.model = wrapped_model

                    async def ainvoke(self, prompt, **kwargs):
                        # Create a usage tracker
                        start_time = time.time()
                        error = None
                        result = None

                        try:
                            # Handle different prompt formats for LangChain models
                            # Check if this is a ChatGroq or similar LangChain chat model
                            if hasattr(self.model, '__class__') and 'Chat' in self.model.__class__.__name__:
                                # This is a chat model, handle message format properly
                                if isinstance(prompt, list):
                                    # Convert dict messages to LangChain message objects
                                    from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

                                    langchain_messages = []
                                    for msg in prompt:
                                        if isinstance(msg, dict):
                                            role = msg.get("role", "user")
                                            content = msg.get("content", "")

                                            if role == "system":
                                                langchain_messages.append(SystemMessage(content=content))
                                            elif role == "assistant" or role == "ai":
                                                langchain_messages.append(AIMessage(content=content))
                                            else:  # user or default
                                                langchain_messages.append(HumanMessage(content=content))
                                        else:
                                            # Assume it's already a LangChain message object
                                            langchain_messages.append(msg)

                                    # Use agenerate with proper message format
                                    if hasattr(self.model, 'agenerate'):
                                        response = await self.model.agenerate([langchain_messages])
                                        result = response.generations[0][0].text

                                        # Create a response-like object with content attribute
                                        class ResponseWrapper:
                                            def __init__(self, content):
                                                self.content = content
                                        return ResponseWrapper(result)
                                    else:
                                        # Fallback for models without agenerate
                                        result = str(langchain_messages[-1].content) if langchain_messages else ""
                                        class ResponseWrapper:
                                            def __init__(self, content):
                                                self.content = content
                                        return ResponseWrapper(result)

                                else:
                                    # Single prompt string, convert to HumanMessage
                                    from langchain_core.messages import HumanMessage
                                    if hasattr(self.model, 'agenerate'):
                                        response = await self.model.agenerate([[HumanMessage(content=str(prompt))]])
                                        result = response.generations[0][0].text

                                        class ResponseWrapper:
                                            def __init__(self, content):
                                                self.content = content
                                        return ResponseWrapper(result)
                                    else:
                                        result = str(prompt)
                                        class ResponseWrapper:
                                            def __init__(self, content):
                                                self.content = content
                                        return ResponseWrapper(result)

                            # For non-chat models, use the original logic
                            elif hasattr(self.model, 'agenerate'):
                                # Legacy LangChain style
                                response = await self.model.agenerate([prompt])
                                result = response.generations[0][0].text
                            elif hasattr(self.model, 'agenerate_prompt'):
                                # Some LangChain models use agenerate_prompt
                                response = await self.model.agenerate_prompt(prompt, **kwargs)
                                result = response.generations[0][0].text
                            elif hasattr(self.model, 'acompletion'):
                                # OpenAI style
                                response = await self.model.acompletion(
                                    prompt=prompt,
                                    **kwargs
                                )
                                result = response.choices[0].text
                            elif hasattr(self.model, 'acall'):
                                # Some LangChain models use acall
                                result = await self.model.acall(prompt, **kwargs)
                            else:
                                # Generic fallback
                                logger.warning(f"Unknown model interface for {self.model.__class__.__name__}, attempting direct call")
                                result = await self.model(prompt, **kwargs)

                            return result
                        except Exception as e:
                            error = str(e)
                            logger.error(f"Error in ainvoke wrapper: {str(e)}", exc_info=True)
                            raise
                        finally:
                            # Calculate latency
                            latency = time.time() - start_time

                            # Record usage in monitoring
                            await record_model_usage(
                                provider_id=provider_id,
                                model_id=model_id,
                                prompt_tokens=len(str(prompt).split()),  # Rough estimate
                                completion_tokens=len(str(result).split()) if result is not None else 0,  # Rough estimate
                                latency=latency,
                                cost=0.0,  # We don't have accurate cost information yet
                                error=error
                            )

                    def invoke(self, input, config=None, **kwargs):
                        """Invoke the model synchronously."""
                        # Delegate to the wrapped model if it has invoke
                        if hasattr(self.model, 'invoke'):
                            return self.model.invoke(input, config=config, **kwargs)
                        else:
                            # Implement synchronous fallback using asyncio
                            import asyncio
                            try:
                                loop = asyncio.get_event_loop()
                                if loop.is_running():
                                    # If we're in an async context, we can't use run()
                                    raise RuntimeError("Cannot run sync invoke in async context. Use ainvoke instead.")
                                else:
                                    return loop.run_until_complete(self.model.ainvoke(input, config=config, **kwargs))
                            except RuntimeError:
                                # Create new event loop if none exists
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                                try:
                                    return loop.run_until_complete(self.model.ainvoke(input, config=config, **kwargs))
                                finally:
                                    loop.close()

                    @property
                    def InputType(self):
                        """Return the input type for this runnable."""
                        return str

                    @property
                    def OutputType(self):
                        """Return the output type for this runnable."""
                        return str

                    def __getattr__(self, name):
                        # Delegate all other attributes to the wrapped model
                        return getattr(self.model, name)

                # Replace the model with our wrapper
                model = ModelWrapper(model)

        return model
    except Exception as e:
        logger.error(f"Error getting model: {str(e)}", exc_info=True)
        raise ModelProviderError(f"Error getting model: {str(e)}")


async def get_provider(
    provider_id: str,
    config: Optional[Dict[str, Any]] = None
):
    """
    Get a provider instance by ID.

    This is a convenience function that wraps ModelProviderRegistry.get_provider.

    Args:
        provider_id: ID of the provider
        config: Optional configuration for the provider

    Returns:
        Initialized provider instance

    Raises:
        ProviderNotFoundError: If the provider is not found
    """
    try:
        return await ModelProviderRegistry.get_provider(provider_id, config)
    except Exception as e:
        logger.error(f"Error getting provider: {str(e)}", exc_info=True)
        raise ProviderNotFoundError(f"Error getting provider: {str(e)}")


async def list_available_providers() -> List[Dict[str, Any]]:
    """
    List all available providers with their status.

    Returns:
        List of provider metadata dictionaries
    """
    providers = []
    for provider_id in ModelProviderRegistry.list_registered_providers():
        try:
            provider = await ModelProviderRegistry.get_provider(provider_id, {})
            available = await provider.is_available()
            providers.append({
                "id": provider_id,
                "name": provider.provider_name,
                "available": available
            })
        except Exception as e:
            logger.warning(f"Error checking provider {provider_id}: {str(e)}")
            providers.append({
                "id": provider_id,
                "name": provider_id.capitalize(),
                "available": False,
                "error": str(e)
            })

    return providers


async def list_available_models() -> Dict[str, List[Dict[str, Any]]]:
    """
    List all available models grouped by provider.

    Returns:
        Dictionary mapping provider IDs to lists of model metadata dictionaries
    """
    models_by_provider = {}
    for provider_id in ModelProviderRegistry.list_registered_providers():
        try:
            provider = await ModelProviderRegistry.get_provider(provider_id, {})
            if await provider.is_available():
                models = await provider.list_models()
                models_by_provider[provider_id] = models
        except Exception as e:
            logger.warning(f"Error listing models for provider {provider_id}: {str(e)}")

    return models_by_provider


def get_api_key(provider_id: str) -> Optional[str]:
    """
    Get the API key for a provider from environment variables.

    Args:
        provider_id: The provider ID

    Returns:
        The API key or None if not found
    """
    provider_id = provider_id.lower()
    if provider_id == "groq":
        return os.getenv("GROQ_API_KEY")
    elif provider_id == "openai":
        return os.getenv("OPENAI_API_KEY")
    elif provider_id == "anthropic":
        return os.getenv("ANTHROPIC_API_KEY")
    elif provider_id == "gemini":
        return os.getenv("GEMINI_API_KEY")
    elif provider_id == "openrouter":
        return os.getenv("OPENROUTER_API_KEY")
    elif provider_id == "cohere":
        return os.getenv("COHERE_API_KEY")
    elif provider_id == "mistral":
        return os.getenv("MISTRAL_API_KEY")
    elif provider_id == "azure":
        return os.getenv("AZURE_OPENAI_API_KEY")
    else:
        return None
