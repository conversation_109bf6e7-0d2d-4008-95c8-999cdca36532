import { useEffect, useRef } from 'react';
import { Grid } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { VisualizationData } from '@/utils/visualization';

interface HeatmapVisualizationProps {
  visualization: VisualizationData;
  className?: string;
}

export const HeatmapVisualization = ({ visualization, className = '' }: HeatmapVisualizationProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const title = visualization.title || 'Heatmap';
  const description = visualization.description || 'Heatmap visualization';

  useEffect(() => {
    if (!canvasRef.current || !visualization.data.matrix) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const matrix = visualization.data.matrix;
    const rows = matrix.length;
    const cols = rows > 0 ? matrix[0].length : 0;

    // Set canvas dimensions
    canvas.width = canvas.clientWidth;
    canvas.height = canvas.clientHeight;

    // Calculate cell dimensions
    const cellWidth = canvas.width / cols;
    const cellHeight = canvas.height / rows;

    // Find min and max values for color scaling
    let min = Infinity;
    let max = -Infinity;
    for (let i = 0; i < rows; i++) {
      for (let j = 0; j < cols; j++) {
        const value = matrix[i][j];
        if (value < min) min = value;
        if (value > max) max = value;
      }
    }

    // Draw the heatmap
    for (let i = 0; i < rows; i++) {
      for (let j = 0; j < cols; j++) {
        const value = matrix[i][j];
        const normalizedValue = (value - min) / (max - min);
        
        // Generate color (blue to red gradient)
        const r = Math.floor(normalizedValue * 255);
        const b = Math.floor((1 - normalizedValue) * 255);
        const g = Math.floor(Math.min(r, b) / 2);
        
        ctx.fillStyle = `rgb(${r}, ${g}, ${b})`;
        ctx.fillRect(j * cellWidth, i * cellHeight, cellWidth, cellHeight);
        
        // Add cell border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 0.5;
        ctx.strokeRect(j * cellWidth, i * cellHeight, cellWidth, cellHeight);
        
        // Add text if cell is large enough
        if (cellWidth > 30 && cellHeight > 20) {
          ctx.fillStyle = 'white';
          ctx.font = '10px Arial';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(
            value.toFixed(2),
            j * cellWidth + cellWidth / 2,
            i * cellHeight + cellHeight / 2
          );
        }
      }
    }

    // Draw row labels if provided
    if (visualization.data.rowLabels && cellHeight > 15) {
      ctx.fillStyle = 'black';
      ctx.font = '10px Arial';
      ctx.textAlign = 'right';
      ctx.textBaseline = 'middle';
      
      for (let i = 0; i < rows; i++) {
        if (i < visualization.data.rowLabels.length) {
          ctx.fillText(
            visualization.data.rowLabels[i],
            -5, // Offset from the heatmap
            i * cellHeight + cellHeight / 2
          );
        }
      }
    }

    // Draw column labels if provided
    if (visualization.data.colLabels && cellWidth > 15) {
      ctx.fillStyle = 'black';
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      
      for (let j = 0; j < cols; j++) {
        if (j < visualization.data.colLabels.length) {
          ctx.fillText(
            visualization.data.colLabels[j],
            j * cellWidth + cellWidth / 2,
            -5 // Offset from the heatmap
          );
        }
      }
    }
  }, [visualization.data]);

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Grid className="h-5 w-5 text-brand-500" />
          <CardTitle className="text-lg">{title}</CardTitle>
        </div>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="h-[400px] w-full relative">
          <canvas ref={canvasRef} className="w-full h-full" />
        </div>
      </CardContent>
    </Card>
  );
};
