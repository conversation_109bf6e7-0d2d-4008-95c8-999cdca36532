-- Migration: Add Marketing Fields to Business Profiles
-- Description: Add marketing-specific fields (budget, timeline, platforms) to business_profiles table
-- Date: 2025-01-15

-- Add marketing-specific fields to business_profiles table
ALTER TABLE business_profiles 
ADD COLUMN IF NOT EXISTS budget TEXT CHECK (LENGTH(budget) <= 1000),
ADD COLUMN IF NOT EXISTS timeline TEXT CHECK (LENGTH(timeline) <= 1000),
ADD COLUMN IF NOT EXISTS platforms TEXT CHECK (LENGTH(platforms) <= 1000);

-- Add comments to the new columns
COMMENT ON COLUMN business_profiles.budget IS 'Budget constraints, allocations, or financial considerations';
COMMENT ON COLUMN business_profiles.timeline IS 'Timeline constraints, deadlines, or scheduling requirements';
COMMENT ON COLUMN business_profiles.platforms IS 'Specific platforms, channels, or mediums for content distribution';

-- Create indexes for the new fields (optional, for performance if needed)
-- These are commented out by default as they may not be needed for text search
-- CREATE INDEX IF NOT EXISTS idx_business_profiles_budget ON business_profiles USING gin(to_tsvector('english', budget)) WHERE budget IS NOT NULL;
-- CREATE INDEX IF NOT EXISTS idx_business_profiles_timeline ON business_profiles USING gin(to_tsvector('english', timeline)) WHERE timeline IS NOT NULL;
-- CREATE INDEX IF NOT EXISTS idx_business_profiles_platforms ON business_profiles USING gin(to_tsvector('english', platforms)) WHERE platforms IS NOT NULL;

-- Migration completed successfully
