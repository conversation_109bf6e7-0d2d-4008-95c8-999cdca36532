"""
Shared Component Library for Phase 2 Architecture Consolidation.

This package contains shared components that consolidate duplicate logic
across different agents, providing standardized implementations for
common functionality like LLM processing, data retrieval, and context management.
"""

from .shared_llm_processor import SharedLLMProcessor
from .shared_data_retriever import SharedDataRetriever
from .shared_context_manager import SharedContextManager

__all__ = [
    "SharedLLMProcessor",
    "SharedDataRetriever", 
    "SharedContextManager"
]
