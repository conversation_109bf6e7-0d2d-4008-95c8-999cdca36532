"""
Chart Optimization Utilities for Enhanced Performance and UX
"""
import os
import base64
import logging
from typing import Dict, Any, Optional, Tuple
from PIL import Image, ImageOps
import io
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ChartOptimizer:
    """Optimizes chart images for better performance and UX"""
    
    def __init__(self, max_file_size_kb: int = 500, quality: int = 85):
        self.max_file_size_kb = max_file_size_kb
        self.quality = quality
        self.cache = {}
        
    def optimize_chart_image(self, image_path: str, target_width: Optional[int] = None) -> Dict[str, Any]:
        """
        Optimize chart image for web display
        
        Args:
            image_path: Path to the original chart image
            target_width: Target width for resizing (maintains aspect ratio)
            
        Returns:
            Dict containing optimized image data and metadata
        """
        try:
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Chart image not found: {image_path}")
                
            # Check cache first
            cache_key = f"{image_path}_{target_width}_{self.quality}"
            if cache_key in self.cache:
                logger.info(f"Using cached optimized image for {image_path}")
                return self.cache[cache_key]
            
            # Open and process image
            with Image.open(image_path) as img:
                original_size = img.size
                original_file_size = os.path.getsize(image_path)
                
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Create white background for transparency
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize if target width specified
                if target_width and img.width > target_width:
                    ratio = target_width / img.width
                    new_height = int(img.height * ratio)
                    img = img.resize((target_width, new_height), Image.Resampling.LANCZOS)
                    logger.info(f"Resized image from {original_size} to {img.size}")
                
                # Optimize file size
                optimized_data = self._optimize_file_size(img)
                
                # Create result
                result = {
                    "base64_data": optimized_data["base64"],
                    "data_url": f"data:image/jpeg;base64,{optimized_data['base64']}",
                    "metadata": {
                        "original_size": original_size,
                        "optimized_size": img.size,
                        "original_file_size": original_file_size,
                        "optimized_file_size": optimized_data["file_size"],
                        "compression_ratio": round(optimized_data["file_size"] / original_file_size, 2),
                        "quality": optimized_data["quality"],
                        "format": "JPEG",
                        "optimization_timestamp": datetime.now().isoformat()
                    }
                }
                
                # Cache the result
                self.cache[cache_key] = result
                logger.info(f"Optimized chart: {original_file_size} -> {optimized_data['file_size']} bytes "
                           f"({result['metadata']['compression_ratio']}x compression)")
                
                return result
                
        except Exception as e:
            logger.error(f"Error optimizing chart image {image_path}: {e}", exc_info=True)
            # Fallback: return original image as base64
            try:
                with open(image_path, "rb") as f:
                    original_data = base64.b64encode(f.read()).decode("utf-8")
                return {
                    "base64_data": original_data,
                    "data_url": f"data:image/png;base64,{original_data}",
                    "metadata": {
                        "original_file_size": os.path.getsize(image_path),
                        "optimization_failed": True,
                        "error": str(e)
                    }
                }
            except Exception as fallback_error:
                logger.error(f"Fallback optimization also failed: {fallback_error}")
                raise
    
    def _optimize_file_size(self, img: Image.Image) -> Dict[str, Any]:
        """Optimize image file size while maintaining quality"""
        target_size = self.max_file_size_kb * 1024
        quality = self.quality
        
        while quality > 20:  # Don't go below 20% quality
            buffer = io.BytesIO()
            img.save(buffer, format='JPEG', quality=quality, optimize=True)
            file_size = buffer.tell()
            
            if file_size <= target_size or quality <= 30:
                buffer.seek(0)
                base64_data = base64.b64encode(buffer.getvalue()).decode("utf-8")
                return {
                    "base64": base64_data,
                    "file_size": file_size,
                    "quality": quality
                }
            
            quality -= 10
        
        # If we can't get under target size, use minimum quality
        buffer = io.BytesIO()
        img.save(buffer, format='JPEG', quality=20, optimize=True)
        buffer.seek(0)
        base64_data = base64.b64encode(buffer.getvalue()).decode("utf-8")
        
        return {
            "base64": base64_data,
            "file_size": buffer.tell(),
            "quality": 20
        }
    
    def create_thumbnail(self, image_path: str, size: Tuple[int, int] = (200, 150)) -> str:
        """Create a thumbnail version of the chart"""
        try:
            with Image.open(image_path) as img:
                # Create thumbnail maintaining aspect ratio
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Save as base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=70, optimize=True)
                buffer.seek(0)
                
                return base64.b64encode(buffer.getvalue()).decode("utf-8")
                
        except Exception as e:
            logger.error(f"Error creating thumbnail for {image_path}: {e}")
            return ""
    
    def get_image_info(self, image_path: str) -> Dict[str, Any]:
        """Get detailed information about the chart image"""
        try:
            with Image.open(image_path) as img:
                return {
                    "size": img.size,
                    "mode": img.mode,
                    "format": img.format,
                    "file_size": os.path.getsize(image_path),
                    "has_transparency": img.mode in ('RGBA', 'LA', 'P'),
                    "aspect_ratio": round(img.width / img.height, 2)
                }
        except Exception as e:
            logger.error(f"Error getting image info for {image_path}: {e}")
            return {}
    
    def clear_cache(self, older_than_hours: int = 24):
        """Clear optimization cache for entries older than specified hours"""
        cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
        
        keys_to_remove = []
        for key, value in self.cache.items():
            if "metadata" in value and "optimization_timestamp" in value["metadata"]:
                timestamp = datetime.fromisoformat(value["metadata"]["optimization_timestamp"])
                if timestamp < cutoff_time:
                    keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.cache[key]
        
        logger.info(f"Cleared {len(keys_to_remove)} cached optimization entries")


class ChartResponseEnhancer:
    """Enhances chart responses with additional metadata and optimizations"""
    
    def __init__(self):
        self.optimizer = ChartOptimizer()
    
    def enhance_chart_response(self, 
                             image_path: str, 
                             query: str, 
                             provider: str,
                             optimize: bool = True) -> Dict[str, Any]:
        """
        Enhance chart response with optimizations and metadata
        
        Args:
            image_path: Path to the chart image
            query: Original query that generated the chart
            provider: AI provider used
            optimize: Whether to optimize the image
            
        Returns:
            Enhanced response with optimized image and rich metadata
        """
        try:
            # Get basic image info
            image_info = self.optimizer.get_image_info(image_path)
            
            # Optimize image if requested
            if optimize:
                optimized = self.optimizer.optimize_chart_image(image_path, target_width=800)
                image_data = optimized["data_url"]
                optimization_metadata = optimized["metadata"]
            else:
                # Use original image
                with open(image_path, "rb") as f:
                    base64_data = base64.b64encode(f.read()).decode("utf-8")
                image_data = f"data:image/png;base64,{base64_data}"
                optimization_metadata = {"optimized": False}
            
            # Create thumbnail
            thumbnail = self.optimizer.create_thumbnail(image_path)
            
            # Enhanced metadata
            enhanced_metadata = {
                "chart_info": {
                    "query": query,
                    "provider": provider,
                    "generated_at": datetime.now().isoformat(),
                    "image_path": image_path
                },
                "image_info": image_info,
                "optimization": optimization_metadata,
                "thumbnail": f"data:image/jpeg;base64,{thumbnail}" if thumbnail else None,
                "performance": {
                    "load_priority": "high" if image_info.get("file_size", 0) < 100000 else "normal",
                    "lazy_load": image_info.get("file_size", 0) > 500000,
                    "preload_thumbnail": bool(thumbnail)
                }
            }
            
            return {
                "isError": False,
                "content": [
                    {"type": "text", "text": f"📊 Generated visualization for: {query}"},
                    {
                        "type": "image", 
                        "src": image_data,
                        "metadata": enhanced_metadata
                    }
                ],
                "metadata": enhanced_metadata
            }
            
        except Exception as e:
            logger.error(f"Error enhancing chart response: {e}", exc_info=True)
            # Fallback to basic response
            try:
                with open(image_path, "rb") as f:
                    base64_data = base64.b64encode(f.read()).decode("utf-8")
                
                return {
                    "isError": False,
                    "content": [
                        {"type": "text", "text": f"Generated visualization for: {query}"},
                        {"type": "image", "src": f"data:image/png;base64,{base64_data}"}
                    ],
                    "metadata": {
                        "enhancement_failed": True,
                        "error": str(e)
                    }
                }
            except Exception as fallback_error:
                logger.error(f"Fallback enhancement failed: {fallback_error}")
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Error processing chart: {str(e)}"}]
                }


# Global instances
chart_optimizer = ChartOptimizer()
chart_enhancer = ChartResponseEnhancer()
