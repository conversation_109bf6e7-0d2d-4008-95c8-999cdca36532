"""
Template Gallery and Quick Setup Tools for Enhanced Marketing Experience.

This module provides tools for template browsing, business setup, and example showcasing
to improve user onboarding and reduce complexity.
"""

import logging
import yaml
import os
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from .base import BaseMCPTool

logger = logging.getLogger(__name__)


class MarketingTemplate:
    """Marketing template data structure."""

    def __init__(self, template_data: Dict[str, Any]):
        self.id = template_data.get("id")
        self.name = template_data.get("name")
        self.description = template_data.get("description")
        self.category = template_data.get("category")
        self.industry = template_data.get("industry", "all")
        self.business_size = template_data.get("business_size", "all")
        self.difficulty = template_data.get("difficulty", "beginner")
        self.time_estimate = template_data.get("time_estimate", "10-15 min")
        self.action_type = template_data.get("action_type")
        self.preview = template_data.get("preview", "")
        self.tags = template_data.get("tags", [])
        self.template_content = template_data.get("template_content", {})
        self.usage_count = template_data.get("usage_count", 0)
        self.rating = template_data.get("rating", 4.0)
        self.created_date = template_data.get("created_date")
        self.updated_date = template_data.get("updated_date")

    def to_dict(self) -> Dict[str, Any]:
        """Convert template to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "industry": self.industry,
            "business_size": self.business_size,
            "difficulty": self.difficulty,
            "time_estimate": self.time_estimate,
            "action_type": self.action_type,
            "preview": self.preview,
            "tags": self.tags,
            "template_content": self.template_content,
            "usage_count": self.usage_count,
            "rating": self.rating,
            "created_date": self.created_date,
            "updated_date": self.updated_date
        }


class TemplateRepository:
    """Repository for managing marketing templates."""

    def __init__(self):
        self.templates_cache = None
        self.templates_file_path = self._get_templates_file_path()

    def _get_templates_file_path(self) -> str:
        """Get the path to the templates configuration file."""
        # Get the directory of this file
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # Go up to the backend directory and find templates
        backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        templates_path = os.path.join(backend_dir, "data", "marketing_templates.yaml")

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(templates_path), exist_ok=True)

        return templates_path

    def _load_default_templates(self) -> List[Dict[str, Any]]:
        """Load default marketing templates."""
        return [
            {
                "id": "product-launch-strategy",
                "name": "Product Launch Strategy",
                "description": "Complete go-to-market strategy for new product launches",
                "category": "strategy",
                "industry": "technology",
                "business_size": "all",
                "difficulty": "intermediate",
                "time_estimate": "20-25 min",
                "action_type": "marketing_strategy",
                "preview": "Comprehensive launch plan with timeline, channels, and metrics",
                "tags": ["product launch", "go-to-market", "strategy"],
                "template_content": {
                    "brand_description": "Technology company launching innovative product",
                    "marketing_goals": "Generate awareness, drive pre-orders, establish market presence",
                    "suggested_topics": "Product benefits, competitive advantages, launch timeline",
                    "tone": "Professional",
                    "timeline": "3-6 months pre-launch to 6 months post-launch"
                },
                "usage_count": 1247,
                "rating": 4.8,
                "created_date": "2024-01-15",
                "updated_date": "2024-06-15"
            },
            {
                "id": "social-media-calendar",
                "name": "Social Media Content Calendar",
                "description": "30-day social media content plan with post ideas",
                "category": "social",
                "industry": "all",
                "business_size": "all",
                "difficulty": "beginner",
                "time_estimate": "10-15 min",
                "action_type": "social_media_content",
                "preview": "Daily post ideas, hashtags, and engagement strategies",
                "tags": ["social media", "content calendar", "engagement"],
                "template_content": {
                    "marketing_goals": "Increase engagement, build community, drive brand awareness",
                    "suggested_topics": "Behind-the-scenes, tips, user-generated content, industry news",
                    "tone": "Conversational",
                    "platforms": "Instagram, LinkedIn, Twitter, Facebook"
                },
                "usage_count": 2156,
                "rating": 4.6,
                "created_date": "2024-01-10",
                "updated_date": "2024-06-10"
            },
            {
                "id": "email-welcome-series",
                "name": "Welcome Email Series",
                "description": "5-part welcome email sequence for new subscribers",
                "category": "email",
                "industry": "all",
                "business_size": "all",
                "difficulty": "beginner",
                "time_estimate": "15-20 min",
                "action_type": "email_marketing",
                "preview": "Onboarding sequence with value delivery and engagement",
                "tags": ["email marketing", "onboarding", "automation"],
                "template_content": {
                    "marketing_goals": "Nurture leads, increase engagement, drive conversions",
                    "suggested_topics": "Welcome message, value proposition, resources, testimonials",
                    "tone": "Friendly",
                    "timeline": "5 emails over 2 weeks"
                },
                "usage_count": 1834,
                "rating": 4.7,
                "created_date": "2024-01-20",
                "updated_date": "2024-06-20"
            },
            {
                "id": "blog-content-strategy",
                "name": "Blog Content Strategy",
                "description": "Content marketing strategy with blog post ideas",
                "category": "content",
                "industry": "all",
                "business_size": "all",
                "difficulty": "intermediate",
                "time_estimate": "15-20 min",
                "action_type": "blog_content",
                "preview": "Content pillars, topic clusters, and editorial calendar",
                "tags": ["content marketing", "SEO", "blogging"],
                "template_content": {
                    "marketing_goals": "Drive organic traffic, establish thought leadership, educate audience",
                    "suggested_topics": "Industry trends, how-to guides, case studies, best practices",
                    "tone": "Informative",
                    "keywords": "Industry-specific keywords, long-tail phrases"
                },
                "usage_count": 1456,
                "rating": 4.5,
                "created_date": "2024-02-01",
                "updated_date": "2024-06-01"
            },
            {
                "id": "competitor-analysis-framework",
                "name": "Competitive Analysis Framework",
                "description": "Systematic approach to analyzing competitors",
                "category": "research",
                "industry": "all",
                "business_size": "all",
                "difficulty": "beginner",
                "time_estimate": "15-20 min",
                "action_type": "competitor_analysis",
                "preview": "SWOT analysis, positioning map, and opportunity identification",
                "tags": ["competitive analysis", "market research", "positioning"],
                "template_content": {
                    "marketing_goals": "Identify opportunities, understand market position, inform strategy",
                    "suggested_topics": "Competitive landscape, market gaps, positioning opportunities",
                    "tone": "Analytical"
                },
                "usage_count": 1123,
                "rating": 4.6,
                "created_date": "2024-02-10",
                "updated_date": "2024-06-10"
            },
            {
                "id": "holiday-campaign",
                "name": "Holiday Marketing Campaign",
                "description": "Seasonal marketing campaign template",
                "category": "campaigns",
                "industry": "retail",
                "business_size": "all",
                "difficulty": "intermediate",
                "time_estimate": "20-25 min",
                "action_type": "campaign_strategy",
                "preview": "Multi-channel holiday campaign with promotions and content",
                "tags": ["seasonal", "campaigns", "promotions"],
                "template_content": {
                    "marketing_goals": "Increase holiday sales, boost brand awareness, drive customer acquisition",
                    "suggested_topics": "Holiday promotions, gift guides, seasonal content",
                    "tone": "Festive",
                    "timeline": "6 weeks pre-holiday to 2 weeks post-holiday"
                },
                "usage_count": 987,
                "rating": 4.4,
                "created_date": "2024-02-15",
                "updated_date": "2024-06-15"
            }
        ]

    def _ensure_templates_file_exists(self):
        """Ensure the templates file exists with default data."""
        if not os.path.exists(self.templates_file_path):
            default_templates = self._load_default_templates()
            try:
                with open(self.templates_file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(default_templates, f, default_flow_style=False, allow_unicode=True, indent=2)
                logger.info(f"Created default templates file at {self.templates_file_path}")
            except Exception as e:
                logger.error(f"Error creating templates file: {e}")

    def load_templates(self) -> List[MarketingTemplate]:
        """Load all marketing templates."""
        if self.templates_cache is not None:
            return self.templates_cache

        try:
            self._ensure_templates_file_exists()

            with open(self.templates_file_path, 'r', encoding='utf-8') as f:
                templates_data = yaml.safe_load(f)

            templates = [MarketingTemplate(template_data) for template_data in templates_data]
            self.templates_cache = templates

            logger.info(f"Loaded {len(templates)} marketing templates")
            return templates

        except Exception as e:
            logger.error(f"Error loading templates: {e}")
            # Return default templates as fallback
            default_data = self._load_default_templates()
            return [MarketingTemplate(template_data) for template_data in default_data]

    def get_template_by_id(self, template_id: str) -> Optional[MarketingTemplate]:
        """Get a specific template by ID."""
        templates = self.load_templates()
        for template in templates:
            if template.id == template_id:
                return template
        return None

    def filter_templates(self,
                        category: Optional[str] = None,
                        industry: Optional[str] = None,
                        business_size: Optional[str] = None,
                        difficulty: Optional[str] = None,
                        search_term: Optional[str] = None) -> List[MarketingTemplate]:
        """Filter templates based on criteria."""
        templates = self.load_templates()
        filtered = []

        for template in templates:
            # Category filter
            if category and category != "all" and template.category != category:
                continue

            # Industry filter
            if industry and industry != "all" and template.industry != industry and template.industry != "all":
                continue

            # Business size filter
            if business_size and business_size != "all" and template.business_size != business_size and template.business_size != "all":
                continue

            # Difficulty filter
            if difficulty and template.difficulty != difficulty:
                continue

            # Search term filter
            if search_term:
                search_lower = search_term.lower()
                if not (search_lower in template.name.lower() or
                       search_lower in template.description.lower() or
                       any(search_lower in tag.lower() for tag in template.tags)):
                    continue

            filtered.append(template)

        return filtered

    def increment_usage_count(self, template_id: str):
        """Increment the usage count for a template."""
        try:
            templates = self.load_templates()
            for template in templates:
                if template.id == template_id:
                    template.usage_count += 1
                    break

            # Save updated templates back to file
            templates_data = [template.to_dict() for template in templates]
            with open(self.templates_file_path, 'w', encoding='utf-8') as f:
                yaml.dump(templates_data, f, default_flow_style=False, allow_unicode=True, indent=2)

            # Clear cache to force reload
            self.templates_cache = None

        except Exception as e:
            logger.error(f"Error incrementing usage count for template {template_id}: {e}")

    def validate_template_structure(self, template_data: Dict[str, Any]) -> bool:
        """Validate that a template has the required structure."""
        required_fields = [
            'id', 'name', 'description', 'category', 'action_type',
            'difficulty', 'time_estimate', 'tags'
        ]

        try:
            # Check required fields
            for field in required_fields:
                if field not in template_data:
                    logger.error(f"Template missing required field: {field}")
                    return False

            # Validate field types
            if not isinstance(template_data.get('tags'), list):
                logger.error("Template tags must be a list")
                return False

            if not isinstance(template_data.get('template_content'), dict):
                logger.warning("Template content should be a dictionary")

            # Validate enum values
            valid_difficulties = ['beginner', 'intermediate', 'advanced']
            if template_data.get('difficulty') not in valid_difficulties:
                logger.error(f"Invalid difficulty level: {template_data.get('difficulty')}")
                return False

            valid_categories = ['strategy', 'content', 'social', 'email', 'campaigns', 'research']
            if template_data.get('category') not in valid_categories:
                logger.error(f"Invalid category: {template_data.get('category')}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating template structure: {e}")
            return False

    def add_template(self, template_data: Dict[str, Any]) -> bool:
        """Add a new template to the repository."""
        try:
            # Validate template structure
            if not self.validate_template_structure(template_data):
                return False

            # Load existing templates
            templates = self.load_templates()

            # Check for duplicate IDs
            if any(t.id == template_data['id'] for t in templates):
                logger.error(f"Template with ID {template_data['id']} already exists")
                return False

            # Add new template
            new_template = MarketingTemplate(template_data)
            templates.append(new_template)

            # Save to file
            templates_data = [template.to_dict() for template in templates]
            with open(self.templates_file_path, 'w', encoding='utf-8') as f:
                yaml.dump(templates_data, f, default_flow_style=False, allow_unicode=True, indent=2)

            # Clear cache
            self.templates_cache = None

            logger.info(f"Successfully added template: {template_data['id']}")
            return True

        except Exception as e:
            logger.error(f"Error adding template: {e}")
            return False


# Global template repository instance
template_repository = TemplateRepository()


class TemplateGalleryTool(BaseMCPTool):
    """Tool for browsing and selecting marketing templates."""
    
    name = "browse_template_gallery"
    description = "Browse available marketing templates and quick-start options"
    
    class InputSchema(BaseModel):
        category: Optional[str] = Field(default="all", description="Template category (strategy, social, email, etc.)")
        industry: Optional[str] = Field(default="", description="Industry filter")
        business_size: Optional[str] = Field(default="", description="Business size (startup, small, enterprise)")
        
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute template gallery browsing."""
        try:
            category = kwargs.get("category", "all")
            industry = kwargs.get("industry", "")
            business_size = kwargs.get("business_size", "")
            search_term = kwargs.get("search_term", "")
            sort_by = kwargs.get("sort_by", "popular")

            # Load templates from repository
            filtered_templates = template_repository.filter_templates(
                category=category,
                industry=industry,
                business_size=business_size,
                search_term=search_term
            )

            # Sort templates
            if sort_by == "popular":
                filtered_templates.sort(key=lambda t: t.usage_count, reverse=True)
            elif sort_by == "rating":
                filtered_templates.sort(key=lambda t: t.rating, reverse=True)
            elif sort_by == "newest":
                filtered_templates.sort(key=lambda t: t.updated_date or t.created_date, reverse=True)

            # Generate response content
            if not filtered_templates:
                content = "No templates found matching your criteria. Try adjusting your filters or browse all templates."
            else:
                content = "## 📋 Marketing Template Gallery\n\n"
                content += f"Found {len(filtered_templates)} templates for you:\n\n"

                # Group by category
                categories = {}
                for template in filtered_templates:
                    if template.category not in categories:
                        categories[template.category] = []
                    categories[template.category].append(template)

                for cat, templates_in_cat in categories.items():
                    content += f"### {cat.title()} Templates\n\n"

                    for template in templates_in_cat[:5]:  # Limit to 5 per category
                        content += f"**{template.name}**\n"
                        content += f"{template.description}\n"
                        content += f"*{template.preview}*\n"
                        content += f"⭐ {template.rating} | 🕒 {template.time_estimate} | 👥 {template.usage_count:,} uses\n"
                        content += f"[Use This Template](action:{template.action_type})\n\n"

                content += "💡 **Tip:** Click any template to get started with pre-filled content that you can customize for your business."

            # Convert templates to dict format for response
            template_dicts = [template.to_dict() for template in filtered_templates]

            return {
                "success": True,
                "content": content,
                "templates": template_dicts,
                "metadata": {
                    "tool_type": "template_gallery",
                    "category": category,
                    "total_templates": len(filtered_templates),
                    "sort_by": sort_by,
                    "filters_applied": {
                        "category": category,
                        "industry": industry,
                        "business_size": business_size,
                        "search_term": search_term
                    }
                }
            }

        except Exception as e:
            logger.error(f"Error browsing template gallery: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "Failed to load template gallery. Please try again."
            }


class BusinessSetupTool(BaseMCPTool):
    """Tool for guided business setup and onboarding."""
    
    name = "business_setup_guide"
    description = "Guide users through business setup for personalized marketing"
    
    class InputSchema(BaseModel):
        step: Optional[str] = Field(default="start", description="Setup step (start, basic, goals, audience)")
        business_info: Optional[Dict[str, Any]] = Field(default={}, description="Collected business information")
        
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute business setup guidance."""
        try:
            step = kwargs.get("step", "start")
            business_info = kwargs.get("business_info", {})
            
            if step == "start":
                content = """## 🚀 Quick Business Setup
                
Let's get your marketing personalized in just 2 minutes!

**Step 1: Tell me about your business**
- What industry are you in?
- What products or services do you offer?
- How would you describe your brand in one sentence?

**Step 2: Your marketing goals**
- What's your main marketing challenge right now?
- What would success look like for you?

**Step 3: Your audience**
- Who are your ideal customers?
- Where do they typically find businesses like yours?

[Start Setup](action:business_setup_basic) [Skip Setup](action:template_gallery)

💡 **Why this helps:** With just a few details, I can recommend the most relevant templates and create content that fits your specific business needs."""

            elif step == "basic":
                content = """## 📝 Basic Business Information

Please share some basic details about your business:

**Industry & Business Type:**
- What industry are you in? (e.g., technology, retail, healthcare, consulting)
- What's your business model? (B2B, B2C, marketplace, SaaS, etc.)

**Products & Services:**
- What do you sell or offer?
- What makes you different from competitors?

**Brand Personality:**
- How would you describe your brand in 3 words?
- What tone should your marketing have? (professional, friendly, innovative, etc.)

[Continue to Goals](action:business_setup_goals) [Back to Templates](action:template_gallery)"""

            elif step == "goals":
                content = """## 🎯 Marketing Goals & Challenges

Help me understand what you want to achieve:

**Current Challenges:**
- What's your biggest marketing challenge right now?
- What have you tried that hasn't worked well?

**Success Metrics:**
- What would marketing success look like for you?
- How do you currently measure marketing performance?

**Priorities:**
- Are you focused on brand awareness, lead generation, sales, or customer retention?
- What's your timeline for seeing results?

[Continue to Audience](action:business_setup_audience) [Get Recommendations](action:business_setup_complete)"""

            elif step == "audience":
                content = """## 👥 Target Audience

Tell me about your ideal customers:

**Demographics:**
- Who are your ideal customers? (age, location, job title, etc.)
- What size companies do you work with? (if B2B)

**Behavior & Preferences:**
- Where do they spend time online?
- How do they prefer to receive information?
- What motivates their purchasing decisions?

**Current Reach:**
- How do you currently reach your audience?
- What channels work best for you?

[Get My Recommendations](action:business_setup_complete) [Back to Goals](action:business_setup_goals)"""

            elif step == "complete":
                content = """## ✅ Setup Complete!

Based on your business profile, here are my personalized recommendations:

### 🎯 Recommended Starting Points:
[Create Marketing Strategy](action:marketing_strategy) - Build a comprehensive plan
[Social Media Content](action:social_media_content) - Start engaging your audience
[Email Marketing](action:email_marketing) - Nurture your leads

### 📋 Suggested Templates:
[Browse Templates](action:template_gallery) - Quick-start options for your industry

### 📊 Analysis Tools:
[Competitor Analysis](action:competitor_analysis) - Understand your market
[Audience Research](action:audience_research) - Deepen customer insights

💡 **Next Steps:** Choose any option above to start creating marketing content tailored to your business. I'll remember your setup information to make everything more relevant!"""

            else:
                content = "Invalid setup step. Please start over with the business setup."
            
            return {
                "success": True,
                "content": content,
                "metadata": {
                    "tool_type": "business_setup",
                    "step": step,
                    "business_info": business_info
                }
            }
            
        except Exception as e:
            logger.error(f"Error in business setup: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "Failed to load business setup. Please try again."
            }


class ShowExamplesTool(BaseMCPTool):
    """Tool for showing marketing examples and inspiration."""
    
    name = "show_marketing_examples"
    description = "Show examples of marketing content and strategies for inspiration"
    
    class InputSchema(BaseModel):
        content_type: Optional[str] = Field(default="all", description="Type of examples to show")
        industry: Optional[str] = Field(default="", description="Industry filter for examples")
        
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute examples showcase."""
        try:
            content_type = kwargs.get("content_type", "all").lower()
            industry = kwargs.get("industry", "").lower()
            
            content = """## 💡 Marketing Examples & Inspiration

Here are some examples of what I can create for you:

### 📊 Marketing Strategy Example
**SaaS Product Launch Strategy**
- Market analysis and competitive positioning
- Go-to-market timeline with key milestones
- Channel strategy (content, paid ads, partnerships)
- Success metrics and KPIs
[Create Similar Strategy](action:marketing_strategy)

### 📱 Social Media Content Example
**B2B LinkedIn Content Series**
- "5 Industry Trends That Will Shape 2024"
- "Behind the Scenes: Our Product Development Process"
- "Customer Success Story: How [Client] Achieved 300% Growth"
[Generate Social Content](action:social_media_content)

### 📧 Email Marketing Example
**Welcome Email Series**
- Email 1: Welcome & brand story
- Email 2: How to get started (with resources)
- Email 3: Customer success stories
- Email 4: Exclusive offer for new subscribers
[Create Email Campaign](action:email_marketing)

### 🎯 Campaign Example
**Holiday Season Campaign**
- Pre-holiday awareness phase
- Black Friday/Cyber Monday promotions
- Post-holiday retention strategy
- Cross-channel coordination (email, social, ads)
[Develop Campaign](action:campaign_strategy)

### 📝 Blog Content Example
**"The Complete Guide to [Your Topic]"**
- SEO-optimized structure with target keywords
- Actionable tips and best practices
- Visual content suggestions
- Call-to-action for lead generation
[Write Blog Post](action:blog_content)

**Ready to create your own?** Choose any action above, and I'll help you create professional marketing content tailored to your business!"""
            
            return {
                "success": True,
                "content": content,
                "metadata": {
                    "tool_type": "examples_showcase",
                    "content_type": content_type
                }
            }
            
        except Exception as e:
            logger.error(f"Error showing examples: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "Failed to load examples. Please try again."
            }
