"""
Memory service for Datagenius using mem0ai.

This module provides a centralized memory service for AI personas using mem0ai,
enabling personalized user experiences, improved context retention, and more
efficient token usage.
"""

import os
import logging
import subprocess
from typing import Dict, Any, List, Optional, Union
from mem0 import Memory

from app.config import (
    MEM0_API_KEY,
    MEM0_ENDPOINT,
    MEM0_SELF_HOSTED,
    MEM0_DEFAULT_TTL,
    MEM0_MAX_MEMORIES,
    MEM0_MEMORY_THRESHOLD
)
from .qdrant_manager import QdrantManager

logger = logging.getLogger(__name__)

class MemoryService:
    """
    Centralized memory service using mem0ai.

    This service provides a unified interface for memory operations across
    all AI personas in the Datagenius application. It implements the Singleton
    pattern to ensure a single instance is shared across the application.
    """

    _instance = None

    def __new__(cls):
        """Implement singleton pattern for memory service."""
        if cls._instance is None:
            cls._instance = super(MemoryService, cls).__new__(cls)
            cls._instance._initialize()
            logger.info("Initialized mem0ai Memory service")
        return cls._instance

    @classmethod
    def get_instance_with_user_id(cls, user_id: int):
        """
        Get memory service instance with user-specific settings loaded from database.

        Args:
            user_id: User ID to load settings for

        Returns:
            MemoryService instance configured with user's saved settings
        """
        try:
            # Load user settings from database
            from app.database import get_db, get_provider_settings

            # Get database session
            db = next(get_db())

            # Get user provider settings
            user_settings = get_provider_settings(db, user_id)
            logger.info(f"Loaded user settings for user {user_id}: {user_settings}")

            return cls.get_instance_with_user_settings(user_settings)
        except Exception as e:
            logger.error(f"Error loading user settings for user {user_id}: {e}")
            # Fall back to default settings
            return cls.get_instance_with_user_settings(None)

    @classmethod
    def get_instance_with_user_settings(cls, user_settings: Optional[Dict[str, Any]] = None):
        """
        Get memory service instance with user-specific settings.

        Args:
            user_settings: Optional user settings containing LLM provider and model preferences

        Returns:
            MemoryService instance configured with user settings
        """
        instance = cls()

        # If user settings are provided and different from current, reinitialize
        if user_settings and hasattr(instance, '_current_user_settings'):
            current_provider = instance._current_user_settings.get("memory_service_provider", "")
            current_model = instance._current_user_settings.get("memory_service_model", "")
            new_provider = user_settings.get("memory_service_provider", "")
            new_model = user_settings.get("memory_service_model", "")

            # Only reinitialize if settings have changed
            if current_provider != new_provider or current_model != new_model:
                logger.info(f"Memory service settings changed, reinitializing with new configuration")
                instance._initialize(user_settings)
                instance._current_user_settings = user_settings.copy()
        elif user_settings and not hasattr(instance, '_current_user_settings'):
            # First time with user settings
            logger.info(f"Initializing memory service with user settings for the first time")
            instance._initialize(user_settings)
            instance._current_user_settings = user_settings.copy()
        elif not hasattr(instance, '_current_user_settings'):
            # No user settings, store empty dict
            instance._current_user_settings = {}

        return instance

    def _get_llm_config(self, user_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get LLM configuration based on user settings.
        Supports all Datagenius providers: OpenAI, Groq, Gemini, Anthropic, OpenRouter, Requesty, Ollama, Cohere, Mistral, Azure.

        Args:
            user_settings: Optional user settings containing LLM provider and model preferences

        Returns:
            Dictionary containing LLM configuration
        """
        # Import provider configuration
        try:
            from agents.utils.model_providers.config import PROVIDER_CONFIG
        except ImportError:
            logger.warning("Could not import provider config, using fallback configuration")
            PROVIDER_CONFIG = {}

        # Default configuration (prefer Groq over Ollama to avoid startup delays)
        # Note: mem0ai doesn't support base_url parameter, so we use standard provider configs
        if os.getenv("GROQ_API_KEY"):
            default_config = {
                "provider": "groq",
                "config": {
                    "model": "llama-3.1-8b-instant",  # Use a model that works well with mem0ai
                    "api_key": os.getenv("GROQ_API_KEY", "")
                }
            }
        elif os.getenv("OPENAI_API_KEY"):
            default_config = {
                "provider": "openai",
                "config": {
                    "model": "gpt-3.5-turbo",
                    "api_key": os.getenv("OPENAI_API_KEY", "")
                }
            }
        else:
            # Fallback to Ollama only if no cloud providers are available
            default_config = {
                "provider": "ollama",
                "config": {
                    "model": "llama3.2:3b",
                    "ollama_base_url": os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
                }
            }

        # If no user settings provided, return default
        if not user_settings:
            provider_name = default_config.get("provider", "unknown")
            logger.info(f"No user settings provided, using default {provider_name} configuration")
            return default_config

        # Get user's memory service provider and model preferences
        provider = user_settings.get("memory_service_provider", "").lower().strip()
        model = user_settings.get("memory_service_model", "").strip()

        # If user hasn't configured memory service settings, use default
        if not provider or not model:
            logger.info(f"Incomplete user settings (provider='{provider}', model='{model}'), using default")
            return default_config

        logger.info(f"Configuring memory service for provider: {provider}, model: {model}")

        # Get provider configuration from centralized config
        provider_config = PROVIDER_CONFIG.get(provider, {})

        # Validate that the requested provider is available
        if not self._validate_provider_availability(provider):
            logger.warning(f"Provider '{provider}' is not available (missing API key or configuration), using fallback")
            return self._get_fallback_config()

        # Configure based on user's provider choice
        # Note: mem0ai doesn't support base_url parameter, so we use standard provider configs
        if provider == "openai":
            api_key = os.getenv("OPENAI_API_KEY", "")
            if not api_key:
                logger.warning("OpenAI API key not found, falling back")
                return self._get_fallback_config()
            return {
                "provider": "openai",
                "config": {
                    "model": model,
                    "api_key": api_key
                }
            }

        elif provider == "groq":
            return {
                "provider": "groq",
                "config": {
                    "model": model,
                    "api_key": os.getenv("GROQ_API_KEY", "")
                }
            }

        elif provider == "anthropic":
            return {
                "provider": "anthropic",
                "config": {
                    "model": model,
                    "api_key": os.getenv("ANTHROPIC_API_KEY", "")
                }
            }

        elif provider == "gemini":
            return {
                "provider": "gemini",
                "config": {
                    "model": model,
                    "api_key": os.getenv("GEMINI_API_KEY", "")
                }
            }

        elif provider == "openrouter":
            # OpenRouter uses OpenAI-compatible API, but mem0ai doesn't support custom base_url
            # Fall back to a supported provider
            logger.warning("OpenRouter not directly supported by mem0ai, falling back to supported provider")
            return self._get_fallback_config()

        elif provider == "requesty":
            # Requesty uses OpenAI-compatible API, but mem0ai doesn't support custom base_url
            # Fall back to a supported provider
            logger.warning("Requesty not directly supported by mem0ai, falling back to supported provider")
            return self._get_fallback_config()

        elif provider == "cohere":
            # Check if mem0ai supports Cohere natively
            return {
                "provider": "cohere",
                "config": {
                    "model": model,
                    "api_key": os.getenv("COHERE_API_KEY", "")
                }
            }

        elif provider == "mistral":
            # Check if mem0ai supports Mistral natively
            return {
                "provider": "mistral",
                "config": {
                    "model": model,
                    "api_key": os.getenv("MISTRAL_API_KEY", "")
                }
            }

        elif provider == "azure":
            return {
                "provider": "azure_openai",
                "config": {
                    "model": model,
                    "api_key": os.getenv("AZURE_OPENAI_API_KEY", ""),
                    "azure_endpoint": os.getenv("AZURE_OPENAI_ENDPOINT", ""),
                    "api_version": os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-15-preview")
                }
            }

        elif provider == "ollama":
            # Use a smaller model if the requested model is too large for memory
            optimized_model = self._optimize_ollama_model(model)
            return {
                "provider": "ollama",
                "config": {
                    "model": optimized_model,
                    "ollama_base_url": provider_config.get("endpoint", "http://localhost:11434")
                }
            }

        else:
            # Fallback to robust fallback configuration for unknown providers
            logger.warning(f"Unknown memory service provider: '{provider}', using robust fallback")
            return self._get_fallback_config()

    def _optimize_ollama_model(self, model: str) -> str:
        """
        Optimize Ollama model selection for memory constraints.

        Args:
            model: Requested model name

        Returns:
            Optimized model name that fits in available memory
        """
        # Map of large models to smaller alternatives
        model_optimizations = {
            "devstral:latest": "llama3.2:3b",
            "devstral": "llama3.2:3b",
            "llama3:70b": "llama3.2:3b",
            "llama3:8b": "llama3.2:3b",
            "codellama:34b": "llama3.2:3b",
            "codellama:13b": "llama3.2:3b",
            "codellama:7b": "llama3.2:3b",
            "mistral:7b": "llama3.2:3b",
            "mixtral:8x7b": "llama3.2:3b",
            "qwen:14b": "llama3.2:3b",
            "qwen:7b": "llama3.2:3b"
        }

        # Check if model needs optimization
        if model in model_optimizations:
            optimized = model_optimizations[model]
            logger.info(f"Optimizing Ollama model from '{model}' to '{optimized}' for memory constraints")
            return optimized

        # If model is already small or unknown, use as-is
        return model

    def _get_embedder_config(self, user_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get embedder configuration with support for multiple embedding providers.
        Defaults to Hugging Face for local, cost-effective embeddings.

        Args:
            user_settings: Optional user settings containing embedding preferences

        Returns:
            Dictionary containing embedder configuration
        """
        # Get user's embedding preferences
        embedding_provider = None
        embedding_model = None

        if user_settings:
            embedding_provider = user_settings.get("embedding_provider", "").lower().strip()
            embedding_model = user_settings.get("embedding_model", "").strip()

        # Default to Hugging Face if no preferences specified
        if not embedding_provider or not embedding_model:
            embedding_provider = "huggingface"
            embedding_model = "BAAI/bge-small-en-v1.5"

        logger.info(f"Using embedding provider: {embedding_provider}, model: {embedding_model}")

        # Configure based on embedding provider
        if embedding_provider == "huggingface":
            return {
                "provider": "huggingface",
                "config": {
                    "model": embedding_model
                }
            }

        elif embedding_provider == "openai":
            api_key = os.getenv("OPENAI_API_KEY", "")
            if not api_key:
                logger.warning("OpenAI API key not found for embeddings, falling back to Hugging Face")
                return {
                    "provider": "huggingface",
                    "config": {
                        "model": "BAAI/bge-small-en-v1.5"
                    }
                }
            return {
                "provider": "openai",
                "config": {
                    "model": embedding_model,
                    "api_key": api_key
                }
            }

        elif embedding_provider == "azure":
            api_key = os.getenv("AZURE_OPENAI_API_KEY", "")
            endpoint = os.getenv("AZURE_OPENAI_ENDPOINT", "")
            if not api_key or not endpoint:
                logger.warning("Azure OpenAI credentials not found for embeddings, falling back to Hugging Face")
                return {
                    "provider": "huggingface",
                    "config": {
                        "model": "BAAI/bge-small-en-v1.5"
                    }
                }
            return {
                "provider": "azure_openai",
                "config": {
                    "model": embedding_model,
                    "api_key": api_key,
                    "azure_endpoint": endpoint,
                    "api_version": os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-15-preview")
                }
            }

        elif embedding_provider == "cohere":
            api_key = os.getenv("COHERE_API_KEY", "")
            if not api_key:
                logger.warning("Cohere API key not found for embeddings, falling back to Hugging Face")
                return {
                    "provider": "huggingface",
                    "config": {
                        "model": "BAAI/bge-small-en-v1.5"
                    }
                }
            return {
                "provider": "cohere",
                "config": {
                    "model": embedding_model,
                    "api_key": api_key
                }
            }

        else:
            # Fallback to Hugging Face for unknown providers
            logger.warning(f"Unknown embedding provider: '{embedding_provider}', using Hugging Face")
            return {
                "provider": "huggingface",
                "config": {
                    "model": "BAAI/bge-small-en-v1.5"
                }
            }

    def _validate_provider_availability(self, provider: str) -> bool:
        """
        Validate if a provider is available (has required API keys/endpoints).

        Args:
            provider: Provider name to validate

        Returns:
            True if provider is available, False otherwise
        """
        provider = provider.lower().strip()

        if provider == "openai":
            return bool(os.getenv("OPENAI_API_KEY", ""))
        elif provider == "groq":
            return bool(os.getenv("GROQ_API_KEY", ""))
        elif provider == "anthropic":
            return bool(os.getenv("ANTHROPIC_API_KEY", ""))
        elif provider == "gemini":
            return bool(os.getenv("GEMINI_API_KEY", ""))
        elif provider == "openrouter":
            return bool(os.getenv("OPENROUTER_API_KEY", ""))
        elif provider == "requesty":
            return bool(os.getenv("REQUESTY_API_KEY", ""))
        elif provider == "cohere":
            return bool(os.getenv("COHERE_API_KEY", ""))
        elif provider == "mistral":
            return bool(os.getenv("MISTRAL_API_KEY", ""))
        elif provider == "azure":
            return bool(os.getenv("AZURE_OPENAI_API_KEY", "") and os.getenv("AZURE_OPENAI_ENDPOINT", ""))
        elif provider == "ollama":
            # Ollama doesn't require API keys, but check if endpoint is accessible
            return self._check_ollama_availability()
        else:
            return False

    def _check_ollama_availability(self) -> bool:
        """
        Check if Ollama is available with a fast timeout to prevent server startup delays.

        Returns:
            True if Ollama is accessible, False otherwise
        """
        try:
            import requests

            # Get Ollama endpoint
            ollama_endpoint = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")

            # Try to connect with a very short timeout (2 seconds)
            logger.info(f"Checking Ollama availability at {ollama_endpoint}")
            response = requests.get(
                f"{ollama_endpoint}/api/tags",
                timeout=2  # Very short timeout to prevent hanging
            )

            if response.status_code == 200:
                logger.info("✅ Ollama is available")
                return True
            else:
                logger.info(f"❌ Ollama returned status code: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            logger.info("❌ Ollama connection timed out (not available)")
            return False
        except requests.exceptions.ConnectionError:
            logger.info("❌ Ollama connection failed (not running)")
            return False
        except Exception as e:
            logger.info(f"❌ Ollama availability check failed: {e}")
            return False

    def get_available_providers(self) -> List[str]:
        """
        Get list of available providers based on configured API keys.

        Returns:
            List of available provider names
        """
        all_providers = [
            "openai", "groq", "anthropic", "gemini", "openrouter",
            "requesty", "cohere", "mistral", "azure", "ollama"
        ]

        available = []
        for provider in all_providers:
            if self._validate_provider_availability(provider):
                available.append(provider)

        logger.info(f"Available providers: {available}")
        return available

    def _get_fallback_config(self) -> Dict[str, Any]:
        """
        Get a robust fallback configuration that should always work.

        Returns:
            Dictionary containing fallback LLM configuration
        """
        # Try providers in order of preference for fallback
        # Prioritize cloud providers that are more likely to be available
        fallback_providers = ["groq", "openai", "ollama"]

        for provider in fallback_providers:
            if self._validate_provider_availability(provider):
                if provider == "ollama":
                    return {
                        "provider": "ollama",
                        "config": {
                            "model": "llama3.2:3b",
                            "ollama_base_url": os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
                        }
                    }
                elif provider == "groq":
                    return {
                        "provider": "groq",
                        "config": {
                            "model": "llama-3.1-8b-instant",
                            "api_key": os.getenv("GROQ_API_KEY", "")
                        }
                    }
                elif provider == "openai":
                    return {
                        "provider": "openai",
                        "config": {
                            "model": "gpt-3.5-turbo",
                            "api_key": os.getenv("OPENAI_API_KEY", "")
                        }
                    }

        # Last resort: return Ollama config even if not validated
        logger.warning("No validated providers available, using Ollama as last resort")
        return {
            "provider": "ollama",
            "config": {
                "model": "llama3.2:3b",
                "ollama_base_url": "http://localhost:11434"
            }
        }

    def _initialize(self, user_settings: Optional[Dict[str, Any]] = None):
        """Initialize the memory service with configuration.

        Args:
            user_settings: Optional user settings containing LLM provider and model preferences
        """
        try:
            # Initialize mem0ai Memory with appropriate configuration
            if MEM0_SELF_HOSTED:
                # Initialize for local/self-hosted usage
                logger.info("Initializing mem0ai in self-hosted mode")

                # Get Qdrant connection parameters based on environment
                qdrant_params = QdrantManager.get_qdrant_connection_params()

                # Ensure Qdrant is running
                if not QdrantManager.is_qdrant_running(host=qdrant_params['host'], port=qdrant_params['port']):
                    logger.info(f"Qdrant is not running at {qdrant_params['host']}:{qdrant_params['port']}. Attempting to start it...")

                    # Try to start Qdrant
                    success = QdrantManager.ensure_qdrant_running()

                    # Check again after attempting to start
                    if success and QdrantManager.is_qdrant_running(host=qdrant_params['host'], port=qdrant_params['port']):
                        logger.info(f"Successfully started Qdrant at {qdrant_params['host']}:{qdrant_params['port']}")
                    else:
                        logger.warning(f"Failed to start Qdrant at {qdrant_params['host']}:{qdrant_params['port']}. Memory service will use in-memory storage.")
                        # Fall back to local file-based Qdrant storage
                        logger.info("Falling back to local file-based Qdrant storage")
                        config = {
                            "vector_store": {
                                "provider": "qdrant",  # Use Qdrant with file-based storage
                                "config": {
                                    "collection_name": "mem0",
                                    "path": os.path.join(os.getcwd(), "qdrant_storage"),
                                    "on_disk": True  # Ensure persistence
                                }
                            },
                            "llm": self._get_llm_config(user_settings),
                            "embedder": self._get_embedder_config(user_settings)
                        }

                        # Initialize Memory with fallback configuration
                        self.memory = Memory.from_config(config)
                        logger.info("Initialized mem0ai with local file-based Qdrant storage and Ollama")
                        return

                logger.info(f"Using Qdrant at {qdrant_params['host']}:{qdrant_params['port']}")

                # Configure for local usage with Qdrant as vector store
                embedder_config = self._get_embedder_config(user_settings)
                config = {
                    "vector_store": {
                        "provider": "qdrant",
                        "config": qdrant_params
                    },
                    "llm": self._get_llm_config(user_settings),
                    "embedder": embedder_config
                }

                # Log detailed configuration for debugging
                logger.info(f"Initializing mem0ai with config: {config}")
                logger.info(f"Embedder provider: {embedder_config.get('provider')}")
                logger.info(f"Embedder model: {embedder_config.get('config', {}).get('model')}")

                # Check existing Qdrant collections before initialization
                try:
                    import requests
                    qdrant_url = f"http://{qdrant_params['host']}:{qdrant_params['port']}/collections"
                    response = requests.get(qdrant_url)
                    if response.status_code == 200:
                        collections_data = response.json()
                        existing_collections = []
                        if "result" in collections_data and "collections" in collections_data["result"]:
                            existing_collections = [col["name"] for col in collections_data["result"]["collections"]]
                        logger.info(f"Existing Qdrant collections before mem0ai init: {existing_collections}")

                        # Check dimensions of existing mem0 collection if it exists
                        if "mem0" in existing_collections:
                            collection_info_url = f"http://{qdrant_params['host']}:{qdrant_params['port']}/collections/mem0"
                            collection_response = requests.get(collection_info_url)
                            if collection_response.status_code == 200:
                                collection_info = collection_response.json()
                                if "result" in collection_info and "config" in collection_info["result"]:
                                    vector_size = collection_info["result"]["config"]["params"]["vectors"]["size"]
                                    logger.warning(f"Existing 'mem0' collection has vector size: {vector_size}")
                                    if vector_size != 384:  # Expected size for Hugging Face BAAI/bge-small-en-v1.5
                                        logger.error(f"DIMENSION MISMATCH: Existing collection expects {vector_size} dimensions, but Hugging Face model produces 384 dimensions")
                                        logger.error("This will cause embedding failures. Please reset Qdrant collections.")
                except Exception as e:
                    logger.warning(f"Could not check existing Qdrant collections: {e}")

                # Ensure no OpenAI API key is available to force Hugging Face usage
                original_openai_key = os.environ.get("OPENAI_API_KEY")
                if original_openai_key:
                    logger.warning("Temporarily removing OPENAI_API_KEY to prevent mem0ai fallback")
                    del os.environ["OPENAI_API_KEY"]

                try:
                    # WORKAROUND: Pre-create Qdrant collection with correct dimensions
                    # This prevents mem0ai from creating it with wrong dimensions
                    logger.info("Pre-creating Qdrant collection with correct Hugging Face dimensions...")
                    self._ensure_correct_qdrant_collection(qdrant_params)

                    # Initialize Memory with configuration
                    self.memory = Memory.from_config(config)
                    logger.info("Initialized mem0ai with local Qdrant and Hugging Face configuration")

                    # Verify that Hugging Face embedder is actually being used
                    logger.info("Verifying embedder configuration...")
                    test_result = self._verify_embedder_usage()
                    if not test_result:
                        logger.error("Failed to verify Hugging Face embedder usage!")

                finally:
                    # Restore OpenAI API key if it was set
                    if original_openai_key:
                        os.environ["OPENAI_API_KEY"] = original_openai_key
                        logger.info("Restored OPENAI_API_KEY environment variable")
            else:
                # Initialize for hosted service with API key
                logger.info("Initializing mem0ai with hosted service")
                api_key = MEM0_API_KEY
                endpoint = MEM0_ENDPOINT if MEM0_ENDPOINT else None

                if not api_key:
                    logger.warning("No mem0ai API key provided for hosted service")

                # Create configuration for hosted service
                if api_key:
                    # Initialize with API key for hosted service
                    self.memory = Memory(api_key=api_key)
                    if endpoint:
                        # Set custom endpoint if provided
                        self.memory.set_endpoint(endpoint)
                else:
                    # Fall back to local mode with minimal configuration
                    logger.warning("No API key provided, falling back to basic local mode")
                    self.memory = Memory()

            # Set configuration from environment or use defaults
            self.default_ttl = MEM0_DEFAULT_TTL
            self.default_limit = 5
            self.max_memories = MEM0_MAX_MEMORIES
            self.memory_threshold = MEM0_MEMORY_THRESHOLD

            # Track initialization status
            self.initialized = True
            logger.info(f"Memory service initialized successfully (self-hosted: {MEM0_SELF_HOSTED})")
        except Exception as e:
            self.initialized = False
            logger.error(f"Failed to initialize memory service: {e}", exc_info=True)

    def add_memory(self, content: str, user_id: str, metadata: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Add a memory for a specific user.

        Args:
            content: The content to store in memory
            user_id: The ID of the user this memory belongs to
            metadata: Optional metadata to store with the memory

        Returns:
            Memory object or None if operation failed
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot add memory")
            return None

        try:
            # Validate and sanitize inputs
            if not isinstance(content, str):
                logger.warning(f"Content must be a string, got {type(content)}. Converting...")
                content = str(content)

            if not content.strip():
                logger.warning("Empty content provided, skipping memory addition")
                return None

            if not isinstance(user_id, str):
                logger.warning(f"User ID must be a string, got {type(user_id)}. Converting...")
                user_id = str(user_id)

            # Ensure metadata is a dictionary
            if metadata is None:
                metadata = {}
            elif not isinstance(metadata, dict):
                logger.warning(f"Metadata must be a dictionary, got {type(metadata)}. Converting...")
                metadata = {"original_metadata": str(metadata)}

            # Add memory using mem0ai
            result = self.memory.add(content, user_id=user_id, metadata=metadata)

            logger.debug(f"Added memory for user {user_id}: {content[:50]}...")
            return result
        except Exception as e:
            logger.error(f"Error adding memory: {e}", exc_info=True)
            return None

    def search_memories(self, query: str, user_id: str, limit: int = None,
                       metadata_filter: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Search for relevant memories for a user.

        Args:
            query: The search query
            user_id: The ID of the user whose memories to search
            limit: Maximum number of results to return (default: self.default_limit)
            metadata_filter: Optional filter for metadata fields

        Returns:
            Dictionary containing search results
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot search memories")
            return {"results": []}

        try:
            # Use default limit if not specified
            limit = limit or self.default_limit

            # Search memories using mem0ai
            results = self.memory.search(
                query,
                user_id=user_id,
                limit=limit,
                metadata_filter=metadata_filter
            )

            logger.debug(f"Found {len(results.get('results', []))} memories for query: {query[:50]}...")
            return results
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            return {"results": []}

    def add_conversation(self, messages: List[Dict[str, Any]], user_id: str,
                        metadata: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Add a conversation to memory.

        Args:
            messages: List of message dictionaries
            user_id: The ID of the user this conversation belongs to
            metadata: Optional metadata to store with the conversation

        Returns:
            Memory object or None if operation failed
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot add conversation")
            return None

        try:
            # Ensure metadata is a dictionary
            metadata = metadata or {}

            # Format conversation as a string
            if isinstance(messages, list) and messages:
                # Convert messages to a string representation
                conversation_text = self._format_conversation(messages)
            else:
                # Handle empty list or non-list input
                conversation_text = str(messages) if messages else "Empty conversation"

            # Add conversation to memory
            result = self.memory.add(conversation_text, user_id=user_id, metadata=metadata)

            logger.debug(f"Added conversation with {len(messages) if isinstance(messages, list) else 'unknown'} messages for user {user_id}")
            return result
        except Exception as e:
            logger.error(f"Error adding conversation: {e}")
            return None

    def add_file_context(self, file_info: Dict[str, Any], user_id: str,
                        conversation_id: str, metadata: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Add file context to memory for persistent access throughout conversation.

        Args:
            file_info: Dictionary containing file information (id, name, path, etc.)
            user_id: The ID of the user who uploaded the file
            conversation_id: The ID of the conversation
            metadata: Optional metadata to store with the file context

        Returns:
            Memory object or None if operation failed
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot add file context")
            return None

        try:
            # Ensure metadata is a dictionary
            metadata = metadata or {}
            metadata.update({
                "type": "file_context",
                "conversation_id": conversation_id,
                "file_id": file_info.get("id"),
                "file_name": file_info.get("name"),
                "timestamp": metadata.get("timestamp", 0)
            })

            # Create a descriptive text about the file
            file_description = f"User uploaded file '{file_info.get('name', 'unknown')}' "
            if file_info.get("type"):
                file_description += f"of type {file_info.get('type')} "
            if file_info.get("columns"):
                file_description += f"with columns: {', '.join(file_info.get('columns', []))} "
            if file_info.get("shape"):
                shape = file_info.get("shape")
                file_description += f"containing {shape[0]} rows and {shape[1]} columns "

            file_description += f"in conversation {conversation_id}. This file should remain accessible for analysis throughout the conversation."

            # Add file context to memory
            result = self.memory.add(file_description, user_id=user_id, metadata=metadata)

            logger.info(f"Added file context to memory for user {user_id}: {file_info.get('name')}")
            return result
        except Exception as e:
            logger.error(f"Error adding file context: {e}")
            return None

    def search_file_context(self, user_id: str, conversation_id: str,
                           limit: int = 5) -> Dict[str, Any]:
        """
        Search for file context in the current conversation.

        Args:
            user_id: The ID of the user
            conversation_id: The ID of the conversation
            limit: Maximum number of results to return

        Returns:
            Dictionary containing search results with file context
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot search file context")
            return {"results": []}

        try:
            # Search for file context in this conversation
            metadata_filter = {
                "type": "file_context",
                "conversation_id": conversation_id
            }

            results = self.memory.search(
                query="uploaded file data analysis",
                user_id=user_id,
                limit=limit,
                filters=metadata_filter
            )

            logger.debug(f"Found {len(results) if results else 0} file context memories for conversation {conversation_id}")
            return {"results": results if results else []}
        except Exception as e:
            logger.error(f"Error searching file context: {e}")
            return {"results": []}

    def add_conversation_context(self, context_summary: str, user_id: str,
                               conversation_id: str, metadata: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Add conversation context summary to memory for better continuity.

        Args:
            context_summary: Summary of the conversation context
            user_id: The ID of the user
            conversation_id: The ID of the conversation
            metadata: Optional metadata to store with the context

        Returns:
            Memory object or None if operation failed
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot add conversation context")
            return None

        try:
            # Ensure metadata is a dictionary
            metadata = metadata or {}
            metadata.update({
                "type": "conversation_context",
                "conversation_id": conversation_id,
                "timestamp": metadata.get("timestamp", 0)
            })

            # Add conversation context to memory
            result = self.memory.add(context_summary, user_id=user_id, metadata=metadata)

            logger.debug(f"Added conversation context to memory for user {user_id} in conversation {conversation_id}")
            return result
        except Exception as e:
            logger.error(f"Error adding conversation context: {e}")
            return None

    def _format_conversation(self, messages: List[Dict[str, Any]]) -> str:
        """
        Format a list of messages into a string for storage.

        Args:
            messages: List of message dictionaries

        Returns:
            Formatted conversation string
        """
        conversation_parts = []

        for msg in messages:
            role = msg.get("role", "unknown")
            content = msg.get("content", "")
            conversation_parts.append(f"{role}: {content}")

        return "\n".join(conversation_parts)

    def delete_memory(self, memory_id: str) -> bool:
        """
        Delete a specific memory.

        Args:
            memory_id: The ID of the memory to delete

        Returns:
            True if successful, False otherwise
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot delete memory")
            return False

        try:
            self.memory.delete(memory_id)
            logger.debug(f"Deleted memory with ID: {memory_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting memory: {e}")
            return False

    def _verify_embedder_usage(self) -> bool:
        """
        Verify that mem0ai is actually using Hugging Face embedder.

        Returns:
            True if Hugging Face embedder is being used, False otherwise
        """
        try:
            # Create a test embedding to check what provider is being used
            test_text = "Test embedding verification"

            # Monitor network requests to detect which API is being called
            import requests
            from unittest.mock import patch

            openai_called = False
            huggingface_called = False

            def mock_post(*args, **kwargs):
                nonlocal openai_called, huggingface_called
                url = args[0] if args else kwargs.get('url', '')

                if 'openai.com' in str(url):
                    openai_called = True
                    logger.error(f"DETECTED OpenAI API call: {url}")
                elif 'huggingface.co' in str(url):
                    huggingface_called = True
                    logger.info(f"Detected Hugging Face API call: {url}")

                # Call the original function
                return requests.post(*args, **kwargs)

            # Patch requests.post to monitor API calls
            with patch('requests.post', side_effect=mock_post):
                try:
                    # Try to add a test memory
                    result = self.memory.add(
                        test_text,
                        user_id="embedder_test",
                        metadata={"test": "embedder_verification"}
                    )

                    if openai_called:
                        logger.error("❌ mem0ai is using OpenAI embedder despite Hugging Face configuration!")
                        return False
                    elif huggingface_called:
                        logger.info("✅ mem0ai is correctly using Hugging Face embedder")
                        return True
                    else:
                        logger.info("✅ No external API calls detected - likely using local Hugging Face model")
                        return True

                except Exception as e:
                    if "openai" in str(e).lower():
                        logger.error(f"❌ OpenAI-related error detected: {e}")
                        return False
                    else:
                        logger.warning(f"Embedder verification test failed: {e}")
                        return True  # Assume success if not OpenAI-related

        except Exception as e:
            logger.warning(f"Could not verify embedder usage: {e}")
            return True  # Assume success if verification fails

    def _ensure_correct_qdrant_collection(self, qdrant_params: Dict[str, Any]) -> bool:
        """
        Ensure Qdrant collection exists with correct dimensions for Hugging Face embeddings.

        Args:
            qdrant_params: Qdrant connection parameters

        Returns:
            True if collection is correctly configured, False otherwise
        """
        try:
            import requests

            host = qdrant_params['host']
            port = qdrant_params['port']
            collection_name = "mem0"

            # Check if collection already exists
            collection_url = f"http://{host}:{port}/collections/{collection_name}"
            response = requests.get(collection_url)

            if response.status_code == 200:
                # Collection exists, check its dimensions
                collection_info = response.json()
                if "result" in collection_info and "config" in collection_info["result"]:
                    vector_size = collection_info["result"]["config"]["params"]["vectors"]["size"]
                    logger.info(f"Existing collection '{collection_name}' has vector size: {vector_size}")

                    if vector_size == 384:  # Correct for Hugging Face BAAI/bge-small-en-v1.5
                        logger.info("✅ Collection already has correct dimensions")
                        return True
                    else:
                        logger.warning(f"❌ Collection has wrong dimensions ({vector_size}), deleting and recreating...")
                        # Delete the collection
                        delete_response = requests.delete(collection_url)
                        if delete_response.status_code == 200:
                            logger.info("✓ Deleted collection with wrong dimensions")
                        else:
                            logger.error(f"Failed to delete collection: {delete_response.status_code}")
                            return False

            # Create collection with correct dimensions for Hugging Face
            logger.info("Creating new collection with 384 dimensions for Hugging Face embeddings...")

            collection_config = {
                "vectors": {
                    "size": 384,  # Dimension for BAAI/bge-small-en-v1.5
                    "distance": "Cosine"
                }
            }

            create_response = requests.put(
                collection_url,
                json=collection_config,
                headers={"Content-Type": "application/json"}
            )

            if create_response.status_code == 200:
                logger.info("✅ Successfully created collection with correct dimensions")
                return True
            else:
                logger.error(f"Failed to create collection: {create_response.status_code} - {create_response.text}")
                return False

        except Exception as e:
            logger.error(f"Error ensuring correct Qdrant collection: {e}")
            return False

    def clear_user_memories(self, user_id: str) -> bool:
        """
        Clear all memories for a specific user.

        Args:
            user_id: The ID of the user whose memories to clear

        Returns:
            True if successful, False otherwise
        """
        if not self.initialized:
            logger.warning("Memory service not initialized, cannot clear user memories")
            return False

        try:
            self.memory.clear(user_id=user_id)
            logger.info(f"Cleared all memories for user: {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing memories for user {user_id}: {e}")
            return False
