#!/usr/bin/env python3
"""
Quick test for section deletion functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
from app.database import get_db, User
from app.models.dashboard_customization import Dashboard, DashboardSection, DashboardWidget
from app.services.dashboard_service import DatageniusDashboardService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_section_deletion():
    """Test section deletion functionality."""
    logger.info("Testing Section Deletion...")
    
    try:
        db = next(get_db())
        service = DatageniusDashboardService(db)
        
        # Get a test user
        user = db.query(User).first()
        if not user:
            logger.error("No users found in database")
            return False
        
        # Get user's sections
        sections = db.query(DashboardSection).filter(
            DashboardSection.user_id == user.id
        ).all()
        
        logger.info(f"Found {len(sections)} sections for user {user.id}")
        
        if sections:
            # Test deleting the first section
            section_to_delete = sections[0]
            logger.info(f"Testing deletion of section: {section_to_delete.id} - {section_to_delete.name}")
            
            # Check if section has widgets
            widgets = db.query(DashboardWidget).filter(
                DashboardWidget.section_id == section_to_delete.id
            ).all()
            logger.info(f"Section has {len(widgets)} widgets")
            
            # Test the delete method
            result = await service.delete_section(section_to_delete.id, user.id)
            
            if result:
                logger.info("✅ Section deletion test PASSED")
                
                # Verify section is actually deleted
                deleted_section = db.query(DashboardSection).filter(
                    DashboardSection.id == section_to_delete.id
                ).first()
                
                if deleted_section is None:
                    logger.info("✅ Section successfully removed from database")
                else:
                    logger.error("❌ Section still exists in database")
                    return False
                
                # Verify widgets are also deleted
                remaining_widgets = db.query(DashboardWidget).filter(
                    DashboardWidget.section_id == section_to_delete.id
                ).all()
                
                if len(remaining_widgets) == 0:
                    logger.info("✅ All widgets successfully removed")
                else:
                    logger.error(f"❌ {len(remaining_widgets)} widgets still exist")
                    return False
                
                return True
            else:
                logger.error("❌ Section deletion returned False")
                return False
        else:
            logger.info("No sections to test deletion with")
            return True
        
    except Exception as e:
        logger.error(f"❌ Section deletion test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

async def main():
    """Run the section deletion test."""
    logger.info("🧪 Starting Section Deletion Test")
    logger.info("=" * 40)
    
    result = await test_section_deletion()
    
    logger.info("\n" + "=" * 40)
    if result:
        logger.info("🎉 Section deletion functionality is working!")
    else:
        logger.error("❌ Section deletion test failed")
    
    return 0 if result else 1

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(result)
