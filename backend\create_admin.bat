@echo off
REM Script to create an admin user for Datagenius

echo Datagenius Admin User Creation Tool
echo ===================================

set /p EMAIL="Enter admin email: "
set /p PASSWORD="Enter admin password: "
set /p USERNAME="Enter username (optional, press Enter to skip): "
set /p FIRSTNAME="Enter first name (optional, press Enter to skip): "
set /p LASTNAME="Enter last name (optional, press Enter to skip): "

echo.
echo Creating admin user...

set CMD=python create_admin.py --email "%EMAIL%" --password "%PASSWORD%"

if not "%USERNAME%"=="" (
    set CMD=%CMD% --username "%USERNAME%"
)

if not "%FIRSTNAME%"=="" (
    set CMD=%CMD% --first-name "%FIRSTNAME%"
)

if not "%LASTNAME%"=="" (
    set CMD=%CMD% --last-name "%LASTNAME%"
)

%CMD%

echo.
pause
