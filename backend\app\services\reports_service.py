import asyncio
import uuid
from datetime import datetime, timezone
from typing import List, Dict, Any, Literal, Optional
import pandas as pd
from croniter import croniter
import io # For Excel export

# For PDF export
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors

from sqlalchemy.orm import Session # Added for DB session typing
from ..models.reports import ReportConfig, Report, ReportSchedule, ReportExport, ReportModel, ReportScheduleModel # Changed
from ..tasks import generate_scheduled_report_task # Changed
from ..utils.db_utils import get_utc_now # Changed

# Real DataAccessService implementation using actual Datagenius data
class DataAccessService:
    def __init__(self, db: Session): # Type hint for db session
        self.db = db

    async def fetch_data(self, data_source_id: str, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Fetches real data from Datagenius data sources.
        Supports conversations, messages, users, and file data.
        """
        try:
            if data_source_id == "conversations":
                return await self._fetch_conversations_data(filters)
            elif data_source_id == "messages":
                return await self._fetch_messages_data(filters)
            elif data_source_id == "users":
                return await self._fetch_users_data(filters)
            elif data_source_id == "data_sources":
                return await self._fetch_data_sources_data(filters)
            elif data_source_id == "files":
                return await self._fetch_files_data(filters)
            elif data_source_id.startswith("file_"):
                # Handle specific file data
                file_id = data_source_id.replace("file_", "")
                return await self._fetch_file_content_data(file_id, filters)
            else:
                # Fallback to sample data for unknown sources
                return await self._fetch_sample_data(data_source_id, filters)

        except Exception as e:
            print(f"Error fetching data for source {data_source_id}: {e}")
            # Return empty DataFrame on error
            return pd.DataFrame()

    async def _fetch_conversations_data(self, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Fetch conversations data from database."""
        from ..models.chat import Conversation # Changed
        from ..models.auth import User # Changed

        query = self.db.query(
            Conversation.id,
            Conversation.title,
            Conversation.created_at,
            Conversation.updated_at,
            User.username.label('user_name'),
            Conversation.persona_id
        ).join(User, Conversation.user_id == User.id)

        # Apply filters
        if filters:
            if 'start_date' in filters:
                query = query.filter(Conversation.created_at >= filters['start_date'])
            if 'end_date' in filters:
                query = query.filter(Conversation.created_at <= filters['end_date'])
            if 'persona_id' in filters:
                query = query.filter(Conversation.persona_id == filters['persona_id'])

        conversations = query.all()

        data = []
        for conv in conversations:
            data.append({
                'id': conv.id,
                'title': conv.title,
                'created_at': conv.created_at,
                'updated_at': conv.updated_at,
                'user_name': conv.user_name,
                'persona_id': conv.persona_id
            })

        return pd.DataFrame(data)

    async def _fetch_messages_data(self, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Fetch messages data from database."""
        from ..models.chat import Message, Conversation # Changed
        from ..models.auth import User # Changed

        query = self.db.query(
            Message.id,
            Message.content,
            Message.role,
            Message.created_at,
            Conversation.title.label('conversation_title'),
            User.username.label('user_name'),
            Message.persona_id
        ).join(Conversation, Message.conversation_id == Conversation.id)\
         .join(User, Conversation.user_id == User.id)

        # Apply filters
        if filters:
            if 'start_date' in filters:
                query = query.filter(Message.created_at >= filters['start_date'])
            if 'end_date' in filters:
                query = query.filter(Message.created_at <= filters['end_date'])
            if 'role' in filters:
                query = query.filter(Message.role == filters['role'])
            if 'persona_id' in filters:
                query = query.filter(Message.persona_id == filters['persona_id'])

        messages = query.all()

        data = []
        for msg in messages:
            data.append({
                'id': msg.id,
                'content': msg.content[:200] + '...' if len(msg.content) > 200 else msg.content,
                'role': msg.role,
                'created_at': msg.created_at,
                'conversation_title': msg.conversation_title,
                'user_name': msg.user_name,
                'persona_id': msg.persona_id,
                'content_length': len(msg.content)
            })

        return pd.DataFrame(data)

    async def _fetch_users_data(self, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Fetch users data from database."""
        from ..models.auth import User # Changed
        from ..models.chat import Conversation # Changed
        from sqlalchemy import func

        # Get user data with conversation counts
        query = self.db.query(
            User.id,
            User.username,
            User.email,
            User.created_at,
            User.is_active,
            func.count(Conversation.id).label('conversation_count')
        ).outerjoin(Conversation, User.id == Conversation.user_id)\
         .group_by(User.id, User.username, User.email, User.created_at, User.is_active)

        # Apply filters
        if filters:
            if 'is_active' in filters:
                query = query.filter(User.is_active == filters['is_active'])
            if 'start_date' in filters:
                query = query.filter(User.created_at >= filters['start_date'])

        users = query.all()

        data = []
        for user in users:
            data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'created_at': user.created_at,
                'is_active': user.is_active,
                'conversation_count': user.conversation_count
            })

        return pd.DataFrame(data)

    async def _fetch_data_sources_data(self, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Fetch data sources information."""
        try:
            from ..models.data_source import DataSource # Changed

            query = self.db.query(DataSource)

            # Apply filters
            if filters:
                if 'source_type' in filters:
                    query = query.filter(DataSource.source_type == filters['source_type'])
                if 'is_active' in filters:
                    query = query.filter(DataSource.is_active == filters['is_active'])

            data_sources = query.all()

            data = []
            for ds in data_sources:
                data.append({
                    'id': ds.id,
                    'name': ds.name,
                    'source_type': ds.source_type,
                    'created_at': ds.created_at,
                    'is_active': ds.is_active,
                    'description': ds.description
                })

            return pd.DataFrame(data)
        except Exception as e:
            print(f"Error fetching data sources: {e}")
            return pd.DataFrame()

    async def _fetch_files_data(self, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Fetch files data from database."""
        try:
            from ..models.file import File # Changed
            from ..models.auth import User # Changed

            query = self.db.query(
                File.id,
                File.filename,
                File.file_type,
                File.file_size,
                File.created_at,
                User.username.label('uploaded_by')
            ).join(User, File.user_id == User.id)

            # Apply filters
            if filters:
                if 'file_type' in filters:
                    query = query.filter(File.file_type == filters['file_type'])
                if 'start_date' in filters:
                    query = query.filter(File.created_at >= filters['start_date'])

            files = query.all()

            data = []
            for file in files:
                data.append({
                    'id': file.id,
                    'filename': file.filename,
                    'file_type': file.file_type,
                    'file_size': file.file_size,
                    'created_at': file.created_at,
                    'uploaded_by': file.uploaded_by
                })

            return pd.DataFrame(data)
        except Exception as e:
            print(f"Error fetching files: {e}")
            return pd.DataFrame()

    async def _fetch_file_content_data(self, file_id: str, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Fetch content from a specific file."""
        try:
            from ..models.file import File # Changed
            import os

            file_record = self.db.query(File).filter(File.id == file_id).first()
            if not file_record:
                return pd.DataFrame()

            file_path = file_record.file_path
            if not os.path.exists(file_path):
                return pd.DataFrame()

            # Read file based on type
            if file_record.file_type.lower() == 'csv':
                df = pd.read_csv(file_path)
            elif file_record.file_type.lower() in ['xlsx', 'xls']:
                df = pd.read_excel(file_path)
            else:
                # For other file types, return metadata
                return pd.DataFrame([{
                    'file_id': file_id,
                    'filename': file_record.filename,
                    'file_type': file_record.file_type,
                    'message': 'File content not readable as tabular data'
                }])

            # Apply filters to the DataFrame
            if filters:
                for column, filter_value in filters.items():
                    if column in df.columns:
                        if isinstance(filter_value, dict):
                            if "min" in filter_value:
                                df = df[df[column] >= filter_value["min"]]
                            if "max" in filter_value:
                                df = df[df[column] <= filter_value["max"]]
                        else:
                            df = df[df[column] == filter_value]

            return df

        except Exception as e:
            print(f"Error reading file {file_id}: {e}")
            return pd.DataFrame()

    async def _fetch_sample_data(self, data_source_id: str, filters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Fallback sample data for unknown sources."""
        sample_data = {
            'id': [1, 2, 3, 4, 5],
            'name': [f'Item {i}' for i in range(1, 6)],
            'value': [10, 20, 30, 40, 50],
            'category': ['A', 'B', 'A', 'C', 'B']
        }

        df = pd.DataFrame(sample_data)

        # Apply filters if provided
        if filters:
            for column, filter_value in filters.items():
                if column in df.columns:
                    if isinstance(filter_value, list):
                        df = df[df[column].isin(filter_value)]
                    else:
                        df = df[df[column] == filter_value]

        return df

class ReportGenerator:
    """
    Service class for generating, scheduling, and exporting reports.
    """
    def __init__(self, db: Session): # Type hint for db session
        self.data_access_service = DataAccessService(db)
        self.db = db

    async def generate_report(self, config: ReportConfig) -> Report:
        """
        Generates a report based on the provided configuration.
        Fetches data, applies grouping and aggregations, and saves the report to the DB.
        Returns the Pydantic Report model.
        """
        report_model_id = str(uuid.uuid4())
        generated_time = get_utc_now()

        # 1. Fetch data
        try:
            df = await self.data_access_service.fetch_data(config.data_source_id, config.filters)
        except Exception as e:
            # Log this error properly
            print(f"Error fetching data for source {config.data_source_id}: {e}")
            # Create and save an error report
            error_report_model = ReportModel(
                id=report_model_id,
                report_name=config.report_name + " (Error)",
                user_id=config.user_id,
                generated_at=generated_time,
                config_used_raw=config.model_dump(),
                data_raw=[],
                summary_raw={"error": f"Failed to fetch data: {str(e)}"},
                data_source_id=config.data_source_id
            )
            self.db.add(error_report_model)
            self.db.commit()
            return Report(
                report_id=report_model_id,
                report_name=error_report_model.report_name,
                generated_at=error_report_model.generated_at,
                config_used=config, # Original config
                data=[],
                summary=error_report_model.summary_raw
            )

        report_data_list: List[Dict[str, Any]] = []
        summary_stats: Dict[str, Any] = {}
        visualization_cfg: Optional[Dict[str, Any]] = None

        if df.empty:
            summary_stats = {"message": "No data found for the given configuration."}
        else:
            processed_df = df.copy()
            if config.group_by:
                if not all(col in processed_df.columns for col in config.group_by):
                    missing_cols = [col for col in config.group_by if col not in processed_df.columns]
                    summary_stats = {"error": f"Grouping columns not found: {', '.join(missing_cols)}"}
                elif config.aggregations:
                    try:
                        processed_df = processed_df.groupby(config.group_by, as_index=False).agg(config.aggregations)
                    except Exception as e:
                        summary_stats = {"error": f"Failed to apply aggregations: {str(e)}"}
                else: # group_by present, but no aggregations
                    processed_df = processed_df.groupby(config.group_by, as_index=False).size().reset_index(name='count')
            
            report_data_list = processed_df.to_dict(orient='records')
            summary_stats = {
                "total_rows_fetched": len(df),
                "total_rows_processed": len(processed_df),
                "filters_applied": len(config.filters or []),
                "groups_applied": len(config.group_by or []),
                "aggregations_applied": len(config.aggregations or []),
                "columns": df.columns.tolist() if not df.empty else []
            }

            if config.chart_type != "table" and report_data_list:
                # Store chart configuration instead of a dummy URL
                visualization_cfg = {
                    "chart_type": config.chart_type,
                    # Frontend will need to know which columns to use based on chart type and data
                    "data_columns": processed_df.columns.tolist(), 
                    "title": config.report_name
                }
        
        # Persist the ReportModel to the database
        db_report = ReportModel(
            id=report_model_id,
            report_name=config.report_name,
            user_id=config.user_id,
            generated_at=generated_time,
            config_used=config, # Setter handles dict conversion
            data=report_data_list, # Setter handles list of dicts
            summary=summary_stats, # Setter handles dict
            visualization_config=visualization_cfg,
            data_source_id=config.data_source_id
        )
        self.db.add(db_report)
        self.db.commit()
        self.db.refresh(db_report)

        # Return Pydantic model
        return Report(
            report_id=db_report.id,
            report_name=db_report.report_name,
            generated_at=db_report.generated_at,
            config_used=config, # Original Pydantic config
            data=db_report.data, # Property getter returns list of dicts
            summary=db_report.summary, # Property getter returns dict
            # visualization_url is part of Pydantic model, map our config to it if needed by frontend
            # For now, we'll keep it separate. Frontend can use visualization_config from DB.
            # Or, if Pydantic model must have URL, construct a conceptual one:
            visualization_url=f"/api/reports/{db_report.id}/visualization" if visualization_cfg else None
        )

    async def schedule_report(self, schedule_config_pydantic: ReportSchedule) -> ReportScheduleModel:
        """
        Schedules a report. Persists the schedule and enqueues a Celery task.
        Returns the persisted SQLAlchemy ReportScheduleModel.
        """
        now_utc = get_utc_now()
        next_run_time_val = None
        try:
            iter_cron = croniter(schedule_config_pydantic.cron_expression, now_utc)
            next_run_time_val = iter_cron.get_next(datetime)
        except Exception as e:
            # Consider raising an HTTP Exception for invalid cron
            print(f"Error parsing cron expression '{schedule_config_pydantic.cron_expression}': {e}")
            # For now, we'll save it as inactive with no next run time
            schedule_config_pydantic.is_active = False
            schedule_config_pydantic.last_run_status = "failed" # Or "invalid_schedule"

        db_schedule = ReportScheduleModel(
            id=str(uuid.uuid4()) if not schedule_config_pydantic.schedule_id else schedule_config_pydantic.schedule_id,
            user_id=schedule_config_pydantic.user_id,
            report_config=schedule_config_pydantic.report_config, # Setter handles dict conversion
            cron_expression=schedule_config_pydantic.cron_expression,
            next_run_time=next_run_time_val,
            is_active=schedule_config_pydantic.is_active,
            last_run_status=schedule_config_pydantic.last_run_status or "pending",
            created_at=now_utc,
            updated_at=now_utc
        )
        
        self.db.add(db_schedule)
        self.db.commit()
        self.db.refresh(db_schedule)

        if db_schedule.is_active and db_schedule.next_run_time:
            # Enqueue Celery task
            generate_scheduled_report_task.apply_async(
                args=[db_schedule.id],
                eta=db_schedule.next_run_time # Schedule task for next_run_time
            )
            print(f"Report '{db_schedule.report_config.report_name}' (Schedule ID: {db_schedule.id}) for user {db_schedule.user_id} scheduled. Task enqueued for: {db_schedule.next_run_time}")
        
        return db_schedule # Return the SQLAlchemy model instance

    async def export_report(self, report_id: str, format_type: Literal["csv", "excel", "pdf"], user_id: str) -> ReportExport:
        """
        Exports a previously generated report in the specified format.
        Fetches report data from the database.
        """
        # Fetch the generated report data from ReportModel
        report_model_instance = self.db.query(ReportModel).filter(ReportModel.id == report_id, ReportModel.user_id == user_id).first()

        if not report_model_instance:
            return ReportExport(
                report_id=report_id,
                format=format_type,
                content=b"Error: Report not found or access denied.",
                filename=f"error_report_{report_id}.txt"
            )

        report_data_list = report_model_instance.data # Access data via property
        if not report_data_list:
            return ReportExport(
                report_id=report_id,
                format=format_type,
                content=b"Error: Report data is empty.",
                filename=f"error_report_{report_id}.txt"
            )

        df = pd.DataFrame(report_data_list)
        filename = f"{report_model_instance.report_name.replace(' ', '_')}_{report_id}.{format_type}"
        content_bytes = b""

        if df.empty:
             return ReportExport(
                report_id=report_id,
                format=format_type,
                content=b"Report data is empty.",
                filename=filename
            )

        if format_type == "csv":
            csv_buffer = io.StringIO()
            df.to_csv(csv_buffer, index=False)
            content_bytes = csv_buffer.getvalue().encode('utf-8')
        elif format_type == "excel":
            excel_buffer = io.BytesIO()
            with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='ReportData')
            content_bytes = excel_buffer.getvalue()
        elif format_type == "pdf":
            pdf_buffer = io.BytesIO()
            doc = SimpleDocTemplate(pdf_buffer, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            story.append(Paragraph(report_model_instance.report_name, styles['h1']))
            story.append(Spacer(1, 12))
            if report_model_instance.summary:
                for key, value in report_model_instance.summary.items(): # Access summary via property
                    story.append(Paragraph(f"<b>{key.replace('_', ' ').title()}:</b> {value}", styles['Normal']))
                story.append(Spacer(1, 12))
            if not df.empty:
                data_for_table = [df.columns.tolist()] + df.values.tolist()
                table = Table(data_for_table)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(table)
            else:
                story.append(Paragraph("No data available for the table.", styles['Normal']))
            try:
                doc.build(story)
                content_bytes = pdf_buffer.getvalue()
            except Exception as e:
                print(f"Error generating PDF: {e}")
                content_bytes = f"Error generating PDF: {e}".encode('utf-8')
                filename = f"error_report_{report_id}.txt"

        return ReportExport(
            report_id=report_id,
            format=format_type,
            content=content_bytes,
            filename=filename
        )

    async def get_scheduled_reports(self, user_id: str) -> List[ReportScheduleModel]:
        """
        Retrieves all scheduled reports for a given user from the database.
        """
        schedules = self.db.query(ReportScheduleModel).filter(ReportScheduleModel.user_id == user_id).all()
        return schedules

    async def get_generated_report(self, report_id: str, user_id: str) -> Optional[ReportModel]:
        """
        Retrieves a specific generated report by its ID from the database.
        """
        report = self.db.query(ReportModel).filter(ReportModel.id == report_id, ReportModel.user_id == user_id).first()
        return report
    
    # This method is called by the Celery task, which is synchronous.
    # It needs a synchronous version of generate_report.
    # For simplicity, we'll make generate_report itself callable from sync context if db session is managed by caller.
    # Or, create a dedicated sync version. Let's assume the Celery task handles the async event loop if needed.
    # The Celery task is now async, so it can call the async generate_report.
