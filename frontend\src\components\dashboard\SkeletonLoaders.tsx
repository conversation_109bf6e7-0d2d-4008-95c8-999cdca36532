import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({ className, animate = true }) => (
  <div
    className={cn(
      "skeleton",
      animate && "animate-pulse",
      className
    )}
  />
);

// Dashboard Header Skeleton
export const DashboardHeaderSkeleton: React.FC = () => (
  <div className="dashboard-header px-6 py-4">
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-6">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="hidden md:flex items-center space-x-4">
          <Skeleton className="h-8 w-24 rounded-full" />
          <Skeleton className="h-8 w-20 rounded-full" />
        </div>
      </div>
      <div className="flex items-center space-x-3">
        <Skeleton className="h-10 w-32 rounded-lg" />
        <Skeleton className="h-10 w-24 rounded-lg" />
        <Skeleton className="h-10 w-10 rounded-lg" />
      </div>
    </div>
  </div>
);

// Widget Skeleton
export const WidgetSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn("dashboard-widget", className)}>
    <CardHeader className="dashboard-widget-header">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Skeleton className="h-5 w-5 rounded" />
          <Skeleton className="h-5 w-32" />
        </div>
        <Skeleton className="h-8 w-8 rounded" />
      </div>
    </CardHeader>
    <CardContent className="dashboard-widget-content">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-32 w-full rounded-lg" />
        </div>
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-16" />
        </div>
      </div>
    </CardContent>
  </Card>
);

// Chart Widget Skeleton
export const ChartWidgetSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn("dashboard-widget", className)}>
    <CardHeader className="dashboard-widget-header">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Skeleton className="h-5 w-5 rounded" />
          <Skeleton className="h-5 w-28" />
        </div>
        <div className="flex items-center space-x-2">
          <Skeleton className="h-6 w-16 rounded-full" />
          <Skeleton className="h-8 w-8 rounded" />
        </div>
      </div>
    </CardHeader>
    <CardContent className="dashboard-widget-content">
      <div className="space-y-4">
        {/* Chart area */}
        <div className="relative h-48 bg-slate-100 dark:bg-slate-700 rounded-lg overflow-hidden">
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            animate={{ x: [-100, 400] }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          />
          {/* Simulated chart bars */}
          <div className="absolute bottom-4 left-4 right-4 flex items-end justify-between space-x-2">
            {[40, 70, 30, 90, 60, 80, 45].map((height, index) => (
              <Skeleton
                key={index}
                className="w-8 rounded-t"
                style={{ height: `${height}%` }}
              />
            ))}
          </div>
        </div>
        
        {/* Legend */}
        <div className="flex items-center justify-center space-x-6">
          {[1, 2, 3].map((item) => (
            <div key={item} className="flex items-center space-x-2">
              <Skeleton className="h-3 w-3 rounded-full" />
              <Skeleton className="h-4 w-16" />
            </div>
          ))}
        </div>
      </div>
    </CardContent>
  </Card>
);

// Table Widget Skeleton
export const TableWidgetSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn("dashboard-widget", className)}>
    <CardHeader className="dashboard-widget-header">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Skeleton className="h-5 w-5 rounded" />
          <Skeleton className="h-5 w-24" />
        </div>
        <Skeleton className="h-8 w-20 rounded" />
      </div>
    </CardHeader>
    <CardContent className="dashboard-widget-content p-0">
      <div className="space-y-0">
        {/* Table header */}
        <div className="flex items-center space-x-4 p-4 border-b border-slate-100 dark:border-slate-700">
          {[1, 2, 3, 4].map((col) => (
            <Skeleton key={col} className="h-4 flex-1" />
          ))}
        </div>
        
        {/* Table rows */}
        {[1, 2, 3, 4, 5].map((row) => (
          <div key={row} className="flex items-center space-x-4 p-4 border-b border-slate-50 dark:border-slate-800">
            {[1, 2, 3, 4].map((col) => (
              <Skeleton key={col} className="h-4 flex-1" />
            ))}
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);

// KPI Widget Skeleton
export const KPIWidgetSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <Card className={cn("dashboard-widget", className)}>
    <CardHeader className="dashboard-widget-header">
      <div className="flex items-center justify-between">
        <Skeleton className="h-5 w-28" />
        <Skeleton className="h-8 w-8 rounded" />
      </div>
    </CardHeader>
    <CardContent className="dashboard-widget-content">
      <div className="space-y-4">
        <div className="text-center space-y-2">
          <Skeleton className="h-12 w-32 mx-auto" />
          <Skeleton className="h-4 w-20 mx-auto" />
        </div>
        
        <div className="flex items-center justify-center space-x-2">
          <Skeleton className="h-4 w-4 rounded-full" />
          <Skeleton className="h-4 w-16" />
        </div>
        
        <div className="space-y-2">
          <Skeleton className="h-2 w-full rounded-full" />
          <div className="flex justify-between">
            <Skeleton className="h-3 w-8" />
            <Skeleton className="h-3 w-12" />
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);

// Dashboard Grid Skeleton
export const DashboardGridSkeleton: React.FC<{ 
  widgetCount?: number;
  className?: string;
}> = ({ widgetCount = 6, className }) => (
  <div className={cn("grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3", className)}>
    {Array.from({ length: widgetCount }).map((_, index) => {
      // Randomly choose widget type for variety
      const widgetTypes = [WidgetSkeleton, ChartWidgetSkeleton, TableWidgetSkeleton, KPIWidgetSkeleton];
      const WidgetComponent = widgetTypes[index % widgetTypes.length];
      
      return (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <WidgetComponent />
        </motion.div>
      );
    })}
  </div>
);

// Section Tabs Skeleton
export const SectionTabsSkeleton: React.FC = () => (
  <div className="border-b">
    <div className="flex items-center space-x-6 px-6 py-2">
      {[1, 2, 3].map((tab) => (
        <div key={tab} className="flex items-center space-x-2 py-2">
          <Skeleton className="h-4 w-4 rounded" />
          <Skeleton className="h-4 w-20" />
        </div>
      ))}
    </div>
  </div>
);

// Progressive Loading Container
export const ProgressiveLoader: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  skeleton: React.ReactNode;
  className?: string;
}> = ({ isLoading, children, skeleton, className }) => (
  <div className={className}>
    {isLoading ? skeleton : children}
  </div>
);
