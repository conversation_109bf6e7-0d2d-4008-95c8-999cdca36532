"""
Persona Manager for the Datagenius backend.

This module provides functionality for managing AI personas, including loading
persona configurations from YAML files and synchronizing them with the database.
"""

import os
import yaml
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional

from sqlalchemy.orm import Session
from pydantic import ValidationError

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import PersonaConfig using centralized import
from app.utils.import_utils import import_agent_schemas
_, PersonaConfig = import_agent_schemas()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PersonaManager:
    """
    Manager for AI personas.

    This class provides functionality for loading persona configurations from YAML files
    and synchronizing them with the database.
    """

    def __init__(self, personas_dir: str = None):
        """
        Initialize the persona manager.

        Args:
            personas_dir: Directory containing persona configuration files
        """
        if personas_dir is None:
            # Use default personas directory
            backend_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            self.personas_dir = os.path.join(backend_root, "personas")
        else:
            self.personas_dir = personas_dir

        self.config_path = Path(self.personas_dir)
        logger.info(f"Initialized PersonaManager with personas directory: {self.personas_dir}")

    def load_persona_configs(self) -> Dict[str, Dict[str, Any]]:
        """
        Load persona configurations from YAML files.

        Returns:
            Dictionary mapping persona IDs to their configurations
        """
        logger.info(f"Loading persona configurations from {self.personas_dir}")

        if not self.config_path.exists():
            logger.warning(f"Personas directory {self.personas_dir} does not exist")
            return {}

        persona_configs = {}
        yaml_files = list(self.config_path.glob("*.yaml"))
        logger.info(f"Found {len(yaml_files)} YAML files")

        for file_path in yaml_files:
            try:
                # Skip version files (they have multiple dashes in the filename)
                # Files like "marketing-ai-1.1.0.yaml" are version files
                # Files like "marketing-ai.yaml" are main persona files
                if file_path.stem.count("-") > 1:
                    logger.debug(f"Skipping version file: {file_path}")
                    continue

                logger.debug(f"Processing file: {file_path}")
                try:
                    # Try to open with UTF-8 encoding first
                    with open(file_path, "r", encoding="utf-8") as f:
                        config = yaml.safe_load(f)
                except UnicodeDecodeError:
                    # If UTF-8 fails, try with utf-8-sig (handles BOM)
                    try:
                        with open(file_path, "r", encoding="utf-8-sig") as f:
                            config = yaml.safe_load(f)
                    except UnicodeDecodeError:
                        # If that fails too, use latin-1 which can read any file
                        logger.warning(f"Falling back to latin-1 encoding for file: {file_path}")
                        with open(file_path, "r", encoding="latin-1") as f:
                            config = yaml.safe_load(f)

                if not config: # Handle empty YAML files
                    logger.warning(f"Empty configuration file: {file_path}")
                    continue

                try:
                    # Validate the loaded config against the Pydantic model
                    # We'll use the file stem as a fallback for 'id' if not in config,
                    # though PersonaConfig requires 'id'. This logic might need adjustment
                    # if 'id' is truly optional in the file and derived solely from filename.
                    # For now, we assume 'id' should be in the YAML.
                    if 'id' not in config:
                        config['id'] = file_path.stem # Or handle as an error if 'id' is mandatory in YAML
                        logger.warning(f"Persona 'id' not found in {file_path}, using filename stem '{config['id']}'. Ensure 'id' field is present in YAML.")

                    validated_config = PersonaConfig(**config)
                    persona_configs[validated_config.id] = validated_config.model_dump(exclude_none=True) # Use .model_dump() for Pydantic v2+
                    logger.debug(f"Successfully validated and loaded configuration for persona {validated_config.id}")
                except ValidationError as e:
                    logger.error(f"Validation error for configuration file {file_path}:\n{e}")
                    continue # Skip this invalid configuration
                except Exception as e: # Catch other potential errors during Pydantic model instantiation
                    logger.error(f"Error processing/validating configuration file {file_path} after YAML load: {e}")
                    continue

            except Exception as e: # Errors during file reading or YAML parsing
                logger.error(f"Error reading or parsing YAML file {file_path}: {e}")

        logger.info(f"Successfully loaded and validated {len(persona_configs)} persona configurations")
        return persona_configs

    def sync_personas_with_database(self, db: Session) -> List[str]:
        """
        Synchronize personas with the database.

        This method loads persona configurations from YAML files and ensures that
        corresponding records exist in the database.

        Args:
            db: Database session

        Returns:
            List of persona IDs that were synchronized
        """
        from app.database import get_persona, create_persona, update_persona

        logger.info("Synchronizing personas with database")

        # Load persona configurations
        persona_configs = self.load_persona_configs()

        # Track synchronized personas
        synchronized_personas = []

        # Synchronize each persona with the database
        for persona_id, config in persona_configs.items():
            try:
                # Check if persona exists in database
                existing_persona = get_persona(db, persona_id)

                if existing_persona:
                    logger.info(f"Updating existing persona '{persona_id}' in database")
                    # Update persona with latest configuration
                    update_data = {
                        "name": config.get("name", existing_persona.name),
                        "description": config.get("description", existing_persona.description),
                        "industry": config.get("industry", existing_persona.industry),
                        "skills": config.get("skills", existing_persona.skills),
                        "rating": config.get("rating", existing_persona.rating),
                        "review_count": config.get("review_count", existing_persona.review_count),
                        "image_url": config.get("image_url", existing_persona.image_url),
                        "price": config.get("price", existing_persona.price),
                        "provider": config.get("provider", existing_persona.provider),
                        "model": config.get("model", existing_persona.model),
                        "is_active": config.get("is_active", existing_persona.is_active),
                        "age_restriction": config.get("age_restriction", existing_persona.age_restriction),
                        "content_filters": config.get("content_filters", existing_persona.content_filters)
                    }

                    updated_persona = update_persona(db, persona_id, update_data)
                    if updated_persona:
                        synchronized_personas.append(persona_id)
                        logger.info(f"Successfully updated persona '{persona_id}' in database")
                    else:
                        logger.error(f"Failed to update persona '{persona_id}' in database")
                else:
                    logger.info(f"Creating new persona '{persona_id}' in database")
                    # Create new persona
                    create_data = {
                        "id": persona_id,
                        "name": config.get("name", ""),
                        "description": config.get("description", ""),
                        "industry": config.get("industry", ""),
                        "skills": config.get("skills", []),
                        "rating": config.get("rating", 4.5),
                        "review_count": config.get("review_count", 0),
                        "image_url": config.get("image_url", "/placeholder.svg"),
                        "price": config.get("price", 10.0),
                        "provider": config.get("provider", "groq"),
                        "model": config.get("model", ""),
                        "is_active": config.get("is_active", True),
                        "age_restriction": config.get("age_restriction", 0),
                        "content_filters": config.get("content_filters", {})
                    }

                    new_persona = create_persona(db, create_data)
                    if new_persona:
                        synchronized_personas.append(persona_id)
                        logger.info(f"Successfully created persona '{persona_id}' in database")
                    else:
                        logger.error(f"Failed to create persona '{persona_id}' in database")
            except Exception as e:
                logger.error(f"Error synchronizing persona '{persona_id}': {e}", exc_info=True)

        logger.info(f"Synchronized {len(synchronized_personas)} personas with database")
        return synchronized_personas

    def get_persona_config(self, persona_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the configuration for a specific persona.

        Args:
            persona_id: ID of the persona

        Returns:
            Persona configuration or None if not found
        """
        persona_configs = self.load_persona_configs()
        return persona_configs.get(persona_id)

    def get_all_persona_ids(self) -> List[str]:
        """
        Get a list of all persona IDs.

        Returns:
            List of persona IDs
        """
        persona_configs = self.load_persona_configs()
        return list(persona_configs.keys())


# Create a singleton instance
persona_manager = PersonaManager()
