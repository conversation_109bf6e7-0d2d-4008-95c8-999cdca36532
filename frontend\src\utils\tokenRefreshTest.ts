/**
 * Token refresh testing utility
 * This file provides utilities to test the token refresh mechanism
 */

import { authApi } from '@/lib/authApi';
import { isTokenExpired, shouldRefreshToken, getTokenTimeRemaining } from './jwt';

export const testTokenRefresh = async (): Promise<void> => {
  console.log('=== Token Refresh Test ===');
  
  // Check current token status
  const currentToken = localStorage.getItem('token');
  const refreshToken = localStorage.getItem('refresh_token');
  
  if (!currentToken) {
    console.log('❌ No access token found');
    return;
  }
  
  if (!refreshToken) {
    console.log('❌ No refresh token found');
    return;
  }
  
  console.log('✅ Both tokens found');
  
  // Check token expiration
  const isExpired = isTokenExpired(currentToken);
  const shouldRefresh = shouldRefreshToken(currentToken);
  const timeRemaining = getTokenTimeRemaining(currentToken);
  
  console.log(`Token expired: ${isExpired}`);
  console.log(`Should refresh: ${shouldRefresh}`);
  console.log(`Time remaining: ${Math.round(timeRemaining / 1000)} seconds`);
  
  // If token should be refreshed, test the refresh mechanism
  if (shouldRefresh || isExpired) {
    console.log('🔄 Testing token refresh...');
    
    try {
      const response = await authApi.refreshToken(refreshToken);
      console.log('✅ Token refresh successful');
      console.log('New access token received:', response.access_token ? 'Yes' : 'No');
      console.log('New refresh token received:', response.refresh_token ? 'Yes' : 'No');
      
      // Store the new tokens
      localStorage.setItem('token', response.access_token);
      if (response.refresh_token) {
        localStorage.setItem('refresh_token', response.refresh_token);
      }
      
      console.log('✅ Tokens updated in localStorage');
      
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      if (errorMessage.includes('device fingerprint')) {
        console.log('🔍 Device fingerprint issue detected');
      } else if (errorMessage.includes('Maximum token refreshes')) {
        console.log('🔍 Maximum refresh count reached');
      } else if (errorMessage.includes('Invalid refresh token')) {
        console.log('🔍 Refresh token is invalid or expired');
      }
    }
  } else {
    console.log('ℹ️ Token refresh not needed at this time');
  }
  
  console.log('=== Test Complete ===');
};

// Function to force a token refresh for testing
export const forceTokenRefresh = async (): Promise<boolean> => {
  console.log('🔄 Forcing token refresh...');
  
  const refreshToken = localStorage.getItem('refresh_token');
  if (!refreshToken) {
    console.error('❌ No refresh token available');
    return false;
  }
  
  try {
    const response = await authApi.refreshToken(refreshToken);
    
    // Store the new tokens
    localStorage.setItem('token', response.access_token);
    if (response.refresh_token) {
      localStorage.setItem('refresh_token', response.refresh_token);
    }
    
    console.log('✅ Forced token refresh successful');
    return true;
  } catch (error) {
    console.error('❌ Forced token refresh failed:', error);
    return false;
  }
};

// Add to window for easy testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testTokenRefresh = testTokenRefresh;
  (window as any).forceTokenRefresh = forceTokenRefresh;
}
