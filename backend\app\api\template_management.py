"""
Template Management API endpoints.

This module provides REST API endpoints for managing marketing templates
including CRUD operations and validation.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from ..auth import get_current_active_user
from ..database import User
from agents.tools.mcp.template_gallery_tools import template_repository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/templates", tags=["template-management"])


class TemplateCreateRequest(BaseModel):
    """Request model for creating a new template."""
    id: str = Field(..., description="Unique template identifier")
    name: str = Field(..., description="Template name")
    description: str = Field(..., description="Template description")
    category: str = Field(..., description="Template category")
    industry: str = Field(default="all", description="Target industry")
    business_size: str = Field(default="all", description="Target business size")
    difficulty: str = Field(..., description="Difficulty level: beginner, intermediate, advanced")
    time_estimate: str = Field(..., description="Estimated completion time")
    action_type: str = Field(..., description="Associated action type")
    preview: str = Field(..., description="Template preview text")
    tags: List[str] = Field(..., description="Template tags")
    template_content: Dict[str, Any] = Field(default={}, description="Template content structure")


class TemplateUpdateRequest(BaseModel):
    """Request model for updating a template."""
    name: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    industry: Optional[str] = None
    business_size: Optional[str] = None
    difficulty: Optional[str] = None
    time_estimate: Optional[str] = None
    action_type: Optional[str] = None
    preview: Optional[str] = None
    tags: Optional[List[str]] = None
    template_content: Optional[Dict[str, Any]] = None


class TemplateResponse(BaseModel):
    """Response model for template operations."""
    success: bool
    message: str
    template: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class TemplateListResponse(BaseModel):
    """Response model for template listing."""
    success: bool
    templates: List[Dict[str, Any]]
    total_count: int
    metadata: Dict[str, Any]


@router.get("/", response_model=TemplateListResponse)
async def list_templates(
    category: Optional[str] = None,
    industry: Optional[str] = None,
    business_size: Optional[str] = None,
    difficulty: Optional[str] = None,
    search_term: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
) -> TemplateListResponse:
    """
    List all available marketing templates with optional filtering.
    """
    try:
        logger.info(f"Template list requested by user {current_user.id}")
        
        # Filter templates based on parameters
        filtered_templates = template_repository.filter_templates(
            category=category,
            industry=industry,
            business_size=business_size,
            difficulty=difficulty,
            search_term=search_term
        )
        
        # Convert to dict format
        template_dicts = [template.to_dict() for template in filtered_templates]
        
        return TemplateListResponse(
            success=True,
            templates=template_dicts,
            total_count=len(template_dicts),
            metadata={
                "filters_applied": {
                    "category": category,
                    "industry": industry,
                    "business_size": business_size,
                    "difficulty": difficulty,
                    "search_term": search_term
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error listing templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving templates"
        )


@router.get("/{template_id}", response_model=TemplateResponse)
async def get_template(
    template_id: str,
    current_user: User = Depends(get_current_active_user)
) -> TemplateResponse:
    """
    Get a specific template by ID.
    """
    try:
        template = template_repository.get_template_by_id(template_id)
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template with ID {template_id} not found"
            )
        
        return TemplateResponse(
            success=True,
            message="Template retrieved successfully",
            template=template.to_dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving template {template_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving template"
        )


@router.post("/", response_model=TemplateResponse)
async def create_template(
    template_request: TemplateCreateRequest,
    current_user: User = Depends(get_current_active_user)
) -> TemplateResponse:
    """
    Create a new marketing template.
    
    Note: This endpoint is typically restricted to admin users in production.
    """
    try:
        logger.info(f"Template creation requested by user {current_user.id}")
        
        # Convert request to template data
        template_data = template_request.model_dump()
        
        # Add metadata
        template_data.update({
            "usage_count": 0,
            "rating": 4.0,
            "created_date": "2024-06-21",
            "updated_date": "2024-06-21"
        })
        
        # Add template to repository
        success = template_repository.add_template(template_data)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create template. Check validation errors."
            )
        
        return TemplateResponse(
            success=True,
            message="Template created successfully",
            template=template_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating template"
        )


@router.put("/{template_id}", response_model=TemplateResponse)
async def update_template(
    template_id: str,
    template_request: TemplateUpdateRequest,
    current_user: User = Depends(get_current_active_user)
) -> TemplateResponse:
    """
    Update an existing template.
    
    Note: This endpoint is typically restricted to admin users in production.
    """
    try:
        logger.info(f"Template update requested by user {current_user.id} for template {template_id}")
        
        # Get existing template
        existing_template = template_repository.get_template_by_id(template_id)
        if not existing_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template with ID {template_id} not found"
            )
        
        # Update template data
        template_data = existing_template.to_dict()
        update_data = template_request.model_dump(exclude_unset=True)
        template_data.update(update_data)
        template_data["updated_date"] = "2024-06-21"
        
        # Validate updated template
        if not template_repository.validate_template_structure(template_data):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Updated template data is invalid"
            )
        
        # For now, we'll need to implement update functionality in the repository
        # This is a simplified approach - in production you'd want proper update methods
        return TemplateResponse(
            success=True,
            message="Template update functionality not yet implemented",
            template=template_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating template {template_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating template"
        )


@router.post("/{template_id}/use", response_model=TemplateResponse)
async def use_template(
    template_id: str,
    current_user: User = Depends(get_current_active_user)
) -> TemplateResponse:
    """
    Mark a template as used (increment usage count).
    """
    try:
        # Check if template exists
        template = template_repository.get_template_by_id(template_id)
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template with ID {template_id} not found"
            )
        
        # Increment usage count
        template_repository.increment_usage_count(template_id)
        
        # Get updated template
        updated_template = template_repository.get_template_by_id(template_id)
        
        return TemplateResponse(
            success=True,
            message="Template usage recorded successfully",
            template=updated_template.to_dict() if updated_template else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error recording template usage for {template_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error recording template usage"
        )


@router.get("/categories/list")
async def list_template_categories(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Get list of available template categories and metadata.
    """
    try:
        templates = template_repository.load_templates()
        
        # Aggregate category information
        categories = {}
        industries = set()
        business_sizes = set()
        difficulties = set()
        
        for template in templates:
            category = template.category
            if category not in categories:
                categories[category] = {
                    "name": category.title(),
                    "count": 0,
                    "templates": []
                }
            
            categories[category]["count"] += 1
            categories[category]["templates"].append({
                "id": template.id,
                "name": template.name,
                "difficulty": template.difficulty
            })
            
            industries.add(template.industry)
            business_sizes.add(template.business_size)
            difficulties.add(template.difficulty)
        
        return {
            "success": True,
            "categories": categories,
            "metadata": {
                "total_templates": len(templates),
                "available_industries": sorted(list(industries)),
                "available_business_sizes": sorted(list(business_sizes)),
                "available_difficulties": sorted(list(difficulties))
            }
        }
        
    except Exception as e:
        logger.error(f"Error listing template categories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving template categories"
        )
