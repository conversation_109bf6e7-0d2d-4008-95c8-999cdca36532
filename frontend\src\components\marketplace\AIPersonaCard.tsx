
import { <PERSON>, <PERSON>, Loader2, Shopping<PERSON><PERSON> } from "lucide-react";
import { AIPersona } from "@/data/aiPersonas";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { chatApi } from "@/lib/api";
import { useCart } from "@/contexts/CartContext";

interface AIPersonaCardProps {
  persona: AIPersona;
}

export function AIPersonaCard({ persona }: AIPersonaCardProps) {
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { addToCart } = useCart();

  const handleAddToCart = async () => {
    if (!persona.isAvailable) {
      toast({
        title: "Not Available",
        description: "This AI persona is not currently available.",
        variant: "destructive",
      });
      return;
    }

    setIsAddingToCart(true);
    try {
      // Add the persona to the cart
      await addToCart(persona.id);

      // Show success message
      toast({
        title: "Added to Cart",
        description: `${persona.name} has been added to your cart!`,
        variant: "default",
      });
    } catch (error) {
      console.error("Failed to add persona to cart:", error);
      toast({
        title: "Error",
        description: "Failed to add the AI persona to your cart. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleDeploy = async () => {
    if (!persona.isAvailable) {
      toast({
        title: "Not Available",
        description: "This AI persona is not currently available.",
        variant: "destructive",
      });
      return;
    }

    setIsAddingToCart(true);
    try {
      // Create a new conversation with this persona
      const conversation = await chatApi.createConversation(persona.id, `Chat with ${persona.name}`);

      // Show success message
      toast({
        title: "Success",
        description: `${persona.name} has been deployed successfully!`,
        variant: "default",
      });

      // Redirect to the chat page
      navigate(`/data-chat?conversation=${conversation.id}`);
    } catch (error) {
      console.error("Failed to deploy persona:", error);
      toast({
        title: "Error",
        description: "Failed to deploy the AI persona. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsAddingToCart(false);
    }
  };

  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="h-full flex flex-col overflow-hidden">
        <CardHeader className="pb-4">
          <div className="flex justify-between items-start">
            <div className="space-y-1">
              <CardTitle>{persona.name}</CardTitle>
              <CardDescription className="text-sm">{persona.industry}</CardDescription>
            </div>
            <div className="h-12 w-12 rounded-full overflow-hidden bg-muted flex items-center justify-center">
              <img
                src={persona.imageUrl}
                alt={persona.name}
                className="h-full w-full object-cover"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-grow pb-2">
          <p className="text-sm text-muted-foreground mb-4">{persona.description}</p>
          <div className="flex flex-wrap gap-2 mt-4">
            {persona.skills.slice(0, 3).map((skill) => (
              <Badge key={skill} variant="outline" className="bg-primary/5">
                {skill}
              </Badge>
            ))}
          </div>
        </CardContent>
        <CardFooter className="flex-col space-y-3 pt-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
              <span className="text-sm font-medium">{persona.rating}</span>
              <span className="text-xs text-muted-foreground ml-1">({persona.reviewCount} reviews)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">$10.00</span>
              {persona.isPurchased ? (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  <Check className="h-3 w-3 mr-1" /> Purchased
                </Badge>
              ) : persona.isAvailable ? (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  <Check className="h-3 w-3 mr-1" /> Available
                </Badge>
              ) : null}
            </div>
          </div>
          <Button
            className="w-full"
            onClick={handleAddToCart}
            disabled={isAddingToCart || !persona.isAvailable}
          >
            {isAddingToCart ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Adding to Cart...
              </>
            ) : (
              <>
                <ShoppingCart className="h-4 w-4 mr-2" />
                {persona.isAvailable ? "Add to Cart" : "Not Available"}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
