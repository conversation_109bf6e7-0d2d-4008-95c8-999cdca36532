"""
Enhanced Knowledge Graph Service for managing knowledge graphs and entity relationships.
Implements Phase 2.2 requirements for complete knowledge graph functionality.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from app.models.knowledge_graph import KGEntity, KGRelationship
from app.database import get_db
import uuid
from datetime import datetime
import networkx as nx
import json
from collections import defaultdict
import re
import asyncio

logger = logging.getLogger(__name__)


class EnhancedKnowledgeGraphService:
    """Enhanced service for managing knowledge graphs with AI-powered entity extraction and relationship building."""

    def __init__(self, db: Session = None):
        """Initialize the enhanced knowledge graph service."""
        self.db = db or next(get_db())
        self.graph = nx.DiGraph()
        self._initialize_nlp_models()
        logger.info("EnhancedKnowledgeGraphService initialized")

    def _initialize_nlp_models(self):
        """Initialize NLP models for entity extraction and relationship detection."""
        try:
            # Try to load spaCy model for named entity recognition
            import spacy
            self.nlp = spacy.load("en_core_web_sm")
            logger.info("spaCy model loaded successfully")
        except Exception as e:
            logger.warning(f"Could not initialize spaCy model: {e}. Using fallback methods.")
            self.nlp = None

    async def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract entities from text using NLP models.

        Args:
            text: Input text to extract entities from

        Returns:
            List of extracted entities with type, name, and confidence
        """
        entities = []

        if self.nlp:
            try:
                doc = self.nlp(text)

                for ent in doc.ents:
                    entity_data = {
                        "name": ent.text.strip(),
                        "type": ent.label_,
                        "confidence": 0.8,  # spaCy doesn't provide confidence scores
                        "start_char": ent.start_char,
                        "end_char": ent.end_char,
                        "description": f"{ent.label_} entity extracted from text"
                    }
                    entities.append(entity_data)

                logger.info(f"Extracted {len(entities)} entities from text")

            except Exception as e:
                logger.error(f"Error in entity extraction: {e}")

        else:
            # Fallback: simple keyword extraction
            entities = self._fallback_entity_extraction(text)

        return entities

    def _fallback_entity_extraction(self, text: str) -> List[Dict[str, Any]]:
        """Enhanced fallback entity extraction using advanced patterns and NLP techniques."""
        entities = []

        # Enhanced patterns for common entity types
        patterns = {
            'EMAIL': {
                'pattern': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
                'confidence': 0.95,
                'description': 'Email address'
            },
            'PHONE': {
                'pattern': r'\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b',
                'confidence': 0.9,
                'description': 'Phone number'
            },
            'DATE': {
                'pattern': r'\b(?:\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{4}[/-]\d{2}[/-]\d{2}|(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4})\b',
                'confidence': 0.85,
                'description': 'Date'
            },
            'TIME': {
                'pattern': r'\b(?:[01]?[0-9]|2[0-3]):[0-5][0-9](?:\s*(?:AM|PM))?\b',
                'confidence': 0.8,
                'description': 'Time'
            },
            'MONEY': {
                'pattern': r'\$\d+(?:,\d{3})*(?:\.\d{2})?|\b\d+(?:,\d{3})*(?:\.\d{2})?\s*(?:dollars?|USD|cents?)\b',
                'confidence': 0.9,
                'description': 'Monetary amount'
            },
            'PERCENTAGE': {
                'pattern': r'\b\d+(?:\.\d+)?%\b',
                'confidence': 0.9,
                'description': 'Percentage'
            },
            'URL': {
                'pattern': r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
                'confidence': 0.95,
                'description': 'URL'
            }
        }

        # Extract entities using patterns
        for entity_type, config in patterns.items():
            matches = re.finditer(config['pattern'], text, re.IGNORECASE)
            for match in matches:
                entities.append({
                    "name": match.group(),
                    "type": entity_type,
                    "confidence": config['confidence'],
                    "description": config['description'],
                    "start": match.start(),
                    "end": match.end()
                })

        # Enhanced organization detection
        org_patterns = [
            r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:Inc|Corp|LLC|Ltd|Company|Organization|Foundation|Institute|University|College)\b',
            r'\b(?:Google|Microsoft|Apple|Amazon|Facebook|Meta|Twitter|LinkedIn|GitHub|Netflix|Tesla|SpaceX|OpenAI|Anthropic)\b'
        ]

        for pattern in org_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                entities.append({
                    "name": match.group(),
                    "type": "ORG",
                    "confidence": 0.8,
                    "description": "Organization",
                    "start": match.start(),
                    "end": match.end()
                })

        # Enhanced person name detection
        person_patterns = [
            r'\b(?:Mr|Mrs|Ms|Dr|Prof)\.?\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b',
            r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b(?=\s+(?:said|told|mentioned|explained|asked|replied|stated))',
        ]

        for pattern in person_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                entities.append({
                    "name": match.group(),
                    "type": "PERSON",
                    "confidence": 0.7,
                    "description": "Person",
                    "start": match.start(),
                    "end": match.end()
                })

        # Technology and product detection
        tech_keywords = [
            'Python', 'JavaScript', 'Java', 'C++', 'C#', 'Ruby', 'PHP', 'Go', 'Rust', 'Swift',
            'React', 'Vue', 'Angular', 'Node.js', 'Django', 'Flask', 'Spring', 'Laravel',
            'API', 'REST', 'GraphQL', 'JSON', 'XML', 'HTTP', 'HTTPS', 'OAuth', 'JWT',
            'MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'Docker', 'Kubernetes', 'AWS', 'Azure', 'GCP'
        ]

        for keyword in tech_keywords:
            if re.search(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE):
                entities.append({
                    "name": keyword,
                    "type": "TECHNOLOGY",
                    "confidence": 0.9,
                    "description": "Technology or tool",
                    "context": self._extract_context_around_keyword(text, keyword)
                })

        # Location detection (basic)
        location_patterns = [
            r'\b(?:New York|Los Angeles|Chicago|Houston|Phoenix|Philadelphia|San Francisco|Seattle|Boston|Denver|Miami|Atlanta|Dallas|Austin|Portland|Las Vegas)\b',
            r'\b[A-Z][a-z]+,\s*[A-Z]{2}\b',  # City, State format
        ]

        for pattern in location_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                entities.append({
                    "name": match.group(),
                    "type": "GPE",
                    "confidence": 0.7,
                    "description": "Geographic location",
                    "start": match.start(),
                    "end": match.end()
                })

        # Remove duplicates and sort by confidence
        unique_entities = []
        seen_entities = set()

        for entity in sorted(entities, key=lambda x: x['confidence'], reverse=True):
            entity_key = f"{entity['name'].lower()}_{entity['type']}"
            if entity_key not in seen_entities:
                seen_entities.add(entity_key)
                unique_entities.append(entity)

        return unique_entities

    def _extract_context_around_keyword(self, text: str, keyword: str, window: int = 30) -> str:
        """Extract context around a keyword."""
        match = re.search(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE)
        if match:
            start = max(0, match.start() - window)
            end = min(len(text), match.end() + window)
            return text[start:end].strip()
        return ""

    async def build_relationships(self, entities: List[Dict[str, Any]], context: str = "") -> List[Dict[str, Any]]:
        """
        Build relationships between entities based on context and proximity.

        Args:
            entities: List of entities to build relationships between
            context: Additional context for relationship inference

        Returns:
            List of relationships with source, target, type, and confidence
        """
        relationships = []

        # Build relationships based on entity proximity and types
        for i, entity1 in enumerate(entities):
            for j, entity2 in enumerate(entities[i+1:], i+1):
                relationship = await self._infer_relationship(entity1, entity2, context)
                if relationship:
                    relationships.append(relationship)

        logger.info(f"Built {len(relationships)} relationships from {len(entities)} entities")
        return relationships

    async def _infer_relationship(self, entity1: Dict[str, Any], entity2: Dict[str, Any], context: str) -> Optional[Dict[str, Any]]:
        """Infer relationship between two entities."""

        # Define relationship rules based on entity types
        relationship_rules = {
            ("PERSON", "ORG"): "WORKS_AT",
            ("PERSON", "GPE"): "LIVES_IN",
            ("ORG", "GPE"): "LOCATED_IN",
            ("PERSON", "PERSON"): "KNOWS",
            ("EMAIL", "PERSON"): "BELONGS_TO",
            ("PHONE", "PERSON"): "BELONGS_TO",
            ("DATE", "EVENT"): "OCCURS_ON"
        }

        entity1_type = entity1.get("type", "UNKNOWN")
        entity2_type = entity2.get("type", "UNKNOWN")

        # Check for direct rule match
        relationship_type = relationship_rules.get((entity1_type, entity2_type))
        if not relationship_type:
            relationship_type = relationship_rules.get((entity2_type, entity1_type))
            if relationship_type:
                # Swap entities if reverse rule found
                entity1, entity2 = entity2, entity1

        if relationship_type:
            return {
                "source": entity1["name"],
                "target": entity2["name"],
                "type": relationship_type,
                "confidence": 0.7,
                "properties": {
                    "context": context,
                    "inferred": True
                }
            }

        # Check for co-occurrence in context
        if context and entity1["name"] in context and entity2["name"] in context:
            return {
                "source": entity1["name"],
                "target": entity2["name"],
                "type": "RELATED_TO",
                "confidence": 0.5,
                "properties": {
                    "context": context,
                    "co_occurrence": True
                }
            }

        return None

    async def create_entity(self, entity_type: str, name: str, properties: Dict[str, Any] = None, description: str = None) -> KGEntity:
        """Create a new entity in the knowledge graph."""
        try:
            entity = KGEntity(
                entity_type=entity_type,
                name=name,
                properties=properties or {},
                description=description
            )

            self.db.add(entity)
            self.db.commit()
            self.db.refresh(entity)

            logger.info(f"Created entity: {entity.name} (type: {entity.entity_type})")
            return entity

        except Exception as e:
            logger.error(f"Error creating entity: {e}")
            self.db.rollback()
            raise

    async def create_relationship(self, source_entity_id: uuid.UUID, target_entity_id: uuid.UUID,
                                relationship_type: str, properties: Dict[str, Any] = None) -> KGRelationship:
        """Create a relationship between two entities."""
        try:
            relationship = KGRelationship(
                relationship_type=relationship_type,
                source_entity_id=source_entity_id,
                target_entity_id=target_entity_id,
                properties=properties or {}
            )

            self.db.add(relationship)
            self.db.commit()
            self.db.refresh(relationship)

            logger.info(f"Created relationship: {relationship_type} between {source_entity_id} and {target_entity_id}")
            return relationship

        except Exception as e:
            logger.error(f"Error creating relationship: {e}")
            self.db.rollback()
            raise

    async def query_graph(self, query: str) -> Dict[str, Any]:
        """
        Query the knowledge graph using natural language.

        Args:
            query: Natural language query

        Returns:
            Query results with entities and relationships
        """
        try:
            # Parse query to identify entity types and relationships
            query_lower = query.lower()

            # Simple query parsing (can be enhanced with NLP)
            if "who" in query_lower or "person" in query_lower:
                entities = self.db.query(KGEntity).filter(KGEntity.entity_type == "PERSON").all()
            elif "organization" in query_lower or "company" in query_lower:
                entities = self.db.query(KGEntity).filter(KGEntity.entity_type == "ORG").all()
            elif "email" in query_lower:
                entities = self.db.query(KGEntity).filter(KGEntity.entity_type == "EMAIL").all()
            else:
                # General search across all entities
                entities = self.db.query(KGEntity).filter(
                    or_(
                        KGEntity.name.ilike(f"%{query}%"),
                        KGEntity.description.ilike(f"%{query}%")
                    )
                ).all()

            # Get relationships for found entities
            entity_ids = [entity.id for entity in entities]
            relationships = self.db.query(KGRelationship).filter(
                or_(
                    KGRelationship.source_entity_id.in_(entity_ids),
                    KGRelationship.target_entity_id.in_(entity_ids)
                )
            ).all()

            result = {
                "query": query,
                "entities": [
                    {
                        "id": str(entity.id),
                        "name": entity.name,
                        "type": entity.entity_type,
                        "description": entity.description,
                        "properties": entity.properties
                    }
                    for entity in entities
                ],
                "relationships": [
                    {
                        "id": str(rel.id),
                        "source": str(rel.source_entity_id),
                        "target": str(rel.target_entity_id),
                        "type": rel.relationship_type,
                        "properties": rel.properties
                    }
                    for rel in relationships
                ],
                "count": len(entities)
            }

            logger.info(f"Query '{query}' returned {len(entities)} entities and {len(relationships)} relationships")
            return result

        except Exception as e:
            logger.error(f"Error querying graph: {e}")
            return {"query": query, "entities": [], "relationships": [], "count": 0, "error": str(e)}

    async def visualize_graph(self, user_id: str = None, max_nodes: int = 50) -> Dict[str, Any]:
        """
        Generate visualization data for the knowledge graph.

        Args:
            user_id: Optional user ID to filter user-specific data
            max_nodes: Maximum number of nodes to include

        Returns:
            Graph visualization data
        """
        try:
            # Get entities (limit for performance)
            entities_query = self.db.query(KGEntity).limit(max_nodes)
            entities = entities_query.all()

            # Get relationships for these entities
            entity_ids = [entity.id for entity in entities]
            relationships = self.db.query(KGRelationship).filter(
                and_(
                    KGRelationship.source_entity_id.in_(entity_ids),
                    KGRelationship.target_entity_id.in_(entity_ids)
                )
            ).all()

            # Build NetworkX graph for layout calculation
            G = nx.DiGraph()

            # Add nodes
            for entity in entities:
                G.add_node(str(entity.id),
                          name=entity.name,
                          type=entity.entity_type,
                          description=entity.description)

            # Add edges
            for rel in relationships:
                G.add_edge(str(rel.source_entity_id),
                          str(rel.target_entity_id),
                          type=rel.relationship_type)

            # Calculate layout
            try:
                pos = nx.spring_layout(G, k=1, iterations=50)
            except:
                # Fallback to random layout if spring layout fails
                pos = nx.random_layout(G)

            # Prepare visualization data
            nodes = []
            for entity in entities:
                node_id = str(entity.id)
                node_pos = pos.get(node_id, (0, 0))

                nodes.append({
                    "id": node_id,
                    "name": entity.name,
                    "type": entity.entity_type,
                    "description": entity.description,
                    "x": float(node_pos[0]) * 500,  # Scale for visualization
                    "y": float(node_pos[1]) * 500,
                    "size": min(len(entity.name) * 2 + 10, 50),  # Size based on name length
                    "color": self._get_node_color(entity.entity_type)
                })

            links = []
            for rel in relationships:
                links.append({
                    "source": str(rel.source_entity_id),
                    "target": str(rel.target_entity_id),
                    "type": rel.relationship_type,
                    "label": rel.relationship_type
                })

            visualization_data = {
                "nodes": nodes,
                "links": links,
                "stats": {
                    "total_nodes": len(nodes),
                    "total_links": len(links),
                    "node_types": len(set(entity.entity_type for entity in entities)),
                    "relationship_types": len(set(rel.relationship_type for rel in relationships))
                }
            }

            logger.info(f"Generated visualization with {len(nodes)} nodes and {len(links)} links")
            return visualization_data

        except Exception as e:
            logger.error(f"Error generating visualization: {e}")
            return {"nodes": [], "links": [], "stats": {}, "error": str(e)}

    def _get_node_color(self, entity_type: str) -> str:
        """Get color for node based on entity type."""
        color_map = {
            "PERSON": "#3B82F6",      # Blue
            "ORG": "#10B981",         # Green
            "GPE": "#F59E0B",         # Orange
            "EMAIL": "#8B5CF6",       # Purple
            "PHONE": "#EF4444",       # Red
            "DATE": "#6B7280",        # Gray
            "EVENT": "#EC4899",       # Pink
            "UNKNOWN": "#9CA3AF"      # Light gray
        }
        return color_map.get(entity_type, "#9CA3AF")