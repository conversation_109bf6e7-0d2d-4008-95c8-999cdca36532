"""
Advanced Statistical Methods Module for MCP Tools.

This module provides comprehensive statistical analysis methods including
ANOVA, chi-square tests, t-tests, and hypothesis testing framework with
proper statistical validation and reporting.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
import warnings

logger = logging.getLogger(__name__)


class TestType(Enum):
    """Statistical test types."""
    T_TEST_ONE_SAMPLE = "t_test_one_sample"
    T_TEST_TWO_SAMPLE = "t_test_two_sample"
    T_TEST_PAIRED = "t_test_paired"
    ANOVA_ONE_WAY = "anova_one_way"
    ANOVA_TWO_WAY = "anova_two_way"
    CHI_SQUARE_GOODNESS = "chi_square_goodness"
    CHI_SQUARE_INDEPENDENCE = "chi_square_independence"
    CORRELATION_PEARSON = "correlation_pearson"
    CORRELATION_SPEARMAN = "correlation_spearman"
    MANN_WHITNEY_U = "mann_whitney_u"
    WILCOXON_SIGNED_RANK = "wilcoxon_signed_rank"
    KRUSKAL_WALLIS = "kruskal_wallis"


@dataclass
class StatisticalResult:
    """Container for statistical test results."""
    test_type: TestType
    statistic: float
    p_value: float
    degrees_of_freedom: Optional[int] = None
    effect_size: Optional[float] = None
    confidence_interval: Optional[Tuple[float, float]] = None
    interpretation: str = ""
    assumptions_met: bool = True
    assumptions_notes: List[str] = None
    raw_output: Dict[str, Any] = None

    def __post_init__(self):
        if self.assumptions_notes is None:
            self.assumptions_notes = []
        if self.raw_output is None:
            self.raw_output = {}


class AdvancedStatisticalMethods:
    """
    Advanced statistical methods with comprehensive validation and reporting.
    
    Features:
    - Multiple statistical tests (parametric and non-parametric)
    - Assumption checking and validation
    - Effect size calculations
    - Confidence intervals
    - Comprehensive reporting
    - Agent-aware result formatting
    """
    
    def __init__(self):
        """Initialize the statistical methods module."""
        self.alpha_level = 0.05
        self.confidence_level = 0.95
        
        # Import statistical libraries with fallbacks
        try:
            import scipy.stats as stats
            self.stats = stats
            self.scipy_available = True
        except ImportError:
            logger.warning("SciPy not available - some statistical tests will be limited")
            self.scipy_available = False
            self.stats = None
        
        try:
            import statsmodels.api as sm
            import statsmodels.stats.api as sms
            self.statsmodels = sm
            self.statsmodels_stats = sms
            self.statsmodels_available = True
        except ImportError:
            logger.warning("Statsmodels not available - some advanced features will be limited")
            self.statsmodels_available = False
            self.statsmodels = None
            self.statsmodels_stats = None

    def perform_t_test(self, data: Union[pd.Series, List], 
                      test_type: TestType = TestType.T_TEST_ONE_SAMPLE,
                      **kwargs) -> StatisticalResult:
        """
        Perform various t-tests with comprehensive validation.
        
        Args:
            data: Data for analysis (Series or list)
            test_type: Type of t-test to perform
            **kwargs: Additional parameters (mu, data2, etc.)
            
        Returns:
            StatisticalResult with comprehensive test results
        """
        if not self.scipy_available:
            raise ImportError("SciPy required for t-tests")
        
        # Validate inputs
        if isinstance(data, list):
            data = pd.Series(data)
        
        # Remove missing values
        data_clean = data.dropna()
        
        if len(data_clean) < 2:
            raise ValueError("Insufficient data for t-test (need at least 2 observations)")
        
        # Check assumptions
        assumptions_met, assumptions_notes = self._check_t_test_assumptions(data_clean, test_type, **kwargs)
        
        # Perform the appropriate t-test
        if test_type == TestType.T_TEST_ONE_SAMPLE:
            mu = kwargs.get('mu', 0)
            statistic, p_value = self.stats.ttest_1samp(data_clean, mu)
            df = len(data_clean) - 1
            effect_size = self._calculate_cohens_d_one_sample(data_clean, mu)
            
        elif test_type == TestType.T_TEST_TWO_SAMPLE:
            data2 = kwargs.get('data2')
            if data2 is None:
                raise ValueError("data2 required for two-sample t-test")
            
            if isinstance(data2, list):
                data2 = pd.Series(data2)
            data2_clean = data2.dropna()
            
            equal_var = kwargs.get('equal_var', True)
            statistic, p_value = self.stats.ttest_ind(data_clean, data2_clean, equal_var=equal_var)
            df = len(data_clean) + len(data2_clean) - 2 if equal_var else None
            effect_size = self._calculate_cohens_d_two_sample(data_clean, data2_clean)
            
        elif test_type == TestType.T_TEST_PAIRED:
            data2 = kwargs.get('data2')
            if data2 is None:
                raise ValueError("data2 required for paired t-test")
            
            if isinstance(data2, list):
                data2 = pd.Series(data2)
            
            # Align data for pairing
            if len(data) != len(data2):
                raise ValueError("Paired data must have same length")
            
            # Remove pairs with missing values
            valid_pairs = ~(data.isna() | data2.isna())
            data_paired = data[valid_pairs]
            data2_paired = data2[valid_pairs]
            
            statistic, p_value = self.stats.ttest_rel(data_paired, data2_paired)
            df = len(data_paired) - 1
            effect_size = self._calculate_cohens_d_paired(data_paired, data2_paired)
        
        else:
            raise ValueError(f"Unsupported t-test type: {test_type}")
        
        # Calculate confidence interval
        confidence_interval = self._calculate_t_test_ci(data_clean, test_type, **kwargs)
        
        # Generate interpretation
        interpretation = self._interpret_t_test_result(statistic, p_value, effect_size, test_type)
        
        return StatisticalResult(
            test_type=test_type,
            statistic=float(statistic),
            p_value=float(p_value),
            degrees_of_freedom=df,
            effect_size=effect_size,
            confidence_interval=confidence_interval,
            interpretation=interpretation,
            assumptions_met=assumptions_met,
            assumptions_notes=assumptions_notes,
            raw_output={
                'statistic': float(statistic),
                'p_value': float(p_value),
                'degrees_of_freedom': df,
                'sample_size': len(data_clean),
                'alpha_level': self.alpha_level
            }
        )

    def perform_anova(self, data: pd.DataFrame, 
                     dependent_var: str,
                     independent_vars: List[str],
                     test_type: TestType = TestType.ANOVA_ONE_WAY) -> StatisticalResult:
        """
        Perform ANOVA analysis with comprehensive validation.
        
        Args:
            data: DataFrame containing the data
            dependent_var: Name of dependent variable
            independent_vars: List of independent variable names
            test_type: Type of ANOVA to perform
            
        Returns:
            StatisticalResult with comprehensive ANOVA results
        """
        if not self.scipy_available:
            raise ImportError("SciPy required for ANOVA")
        
        # Validate inputs
        if dependent_var not in data.columns:
            raise ValueError(f"Dependent variable '{dependent_var}' not found in data")
        
        for var in independent_vars:
            if var not in data.columns:
                raise ValueError(f"Independent variable '{var}' not found in data")
        
        # Remove missing values
        analysis_data = data[[dependent_var] + independent_vars].dropna()
        
        if len(analysis_data) < 3:
            raise ValueError("Insufficient data for ANOVA (need at least 3 observations)")
        
        # Check assumptions
        assumptions_met, assumptions_notes = self._check_anova_assumptions(
            analysis_data, dependent_var, independent_vars, test_type
        )
        
        if test_type == TestType.ANOVA_ONE_WAY:
            if len(independent_vars) != 1:
                raise ValueError("One-way ANOVA requires exactly one independent variable")
            
            groups = [group[dependent_var].values for name, group in 
                     analysis_data.groupby(independent_vars[0])]
            
            statistic, p_value = self.stats.f_oneway(*groups)
            
            # Calculate effect size (eta-squared)
            effect_size = self._calculate_eta_squared_one_way(analysis_data, dependent_var, independent_vars[0])
            
            # Degrees of freedom
            k = len(groups)  # number of groups
            n = len(analysis_data)  # total sample size
            df_between = k - 1
            df_within = n - k
            df = (df_between, df_within)
            
        elif test_type == TestType.ANOVA_TWO_WAY:
            if len(independent_vars) != 2:
                raise ValueError("Two-way ANOVA requires exactly two independent variables")
            
            # Use statsmodels for two-way ANOVA if available
            if self.statsmodels_available:
                import statsmodels.formula.api as smf
                
                formula = f"{dependent_var} ~ C({independent_vars[0]}) + C({independent_vars[1]}) + C({independent_vars[0]}):C({independent_vars[1]})"
                model = smf.ols(formula, data=analysis_data).fit()
                anova_table = sm.stats.anova_lm(model, typ=2)
                
                # Extract main effects and interaction
                statistic = anova_table.loc[f"C({independent_vars[0]})", "F"]
                p_value = anova_table.loc[f"C({independent_vars[0]})", "PR(>F)"]
                
                effect_size = self._calculate_eta_squared_two_way(anova_table)
                df = (anova_table.loc[f"C({independent_vars[0]})", "df"], 
                     anova_table.loc["Residual", "df"])
            else:
                raise ImportError("Statsmodels required for two-way ANOVA")
        
        else:
            raise ValueError(f"Unsupported ANOVA type: {test_type}")
        
        # Generate interpretation
        interpretation = self._interpret_anova_result(statistic, p_value, effect_size, test_type)
        
        return StatisticalResult(
            test_type=test_type,
            statistic=float(statistic),
            p_value=float(p_value),
            degrees_of_freedom=df,
            effect_size=effect_size,
            interpretation=interpretation,
            assumptions_met=assumptions_met,
            assumptions_notes=assumptions_notes,
            raw_output={
                'statistic': float(statistic),
                'p_value': float(p_value),
                'degrees_of_freedom': df,
                'sample_size': len(analysis_data),
                'groups': len(set(analysis_data[independent_vars[0]])) if test_type == TestType.ANOVA_ONE_WAY else None
            }
        )

    def perform_chi_square_test(self, data: Union[pd.DataFrame, pd.Series],
                               test_type: TestType = TestType.CHI_SQUARE_INDEPENDENCE,
                               **kwargs) -> StatisticalResult:
        """
        Perform chi-square tests with comprehensive validation.

        Args:
            data: Data for analysis (DataFrame for independence, Series for goodness of fit)
            test_type: Type of chi-square test
            **kwargs: Additional parameters (expected frequencies, etc.)

        Returns:
            StatisticalResult with comprehensive chi-square results
        """
        if not self.scipy_available:
            raise ImportError("SciPy required for chi-square tests")

        assumptions_met = True
        assumptions_notes = []

        if test_type == TestType.CHI_SQUARE_INDEPENDENCE:
            if not isinstance(data, pd.DataFrame):
                raise ValueError("DataFrame required for chi-square independence test")

            # Create contingency table
            if data.shape[1] != 2:
                raise ValueError("Chi-square independence test requires exactly 2 categorical variables")

            contingency_table = pd.crosstab(data.iloc[:, 0], data.iloc[:, 1])

            # Check assumptions
            expected_freq = self.stats.contingency.expected_freq(contingency_table)
            if (expected_freq < 5).any().any():
                assumptions_met = False
                assumptions_notes.append("Some expected frequencies < 5 (chi-square assumption violated)")

            statistic, p_value, dof, expected = self.stats.chi2_contingency(contingency_table)
            effect_size = self._calculate_cramers_v(contingency_table, statistic)

        elif test_type == TestType.CHI_SQUARE_GOODNESS:
            if isinstance(data, pd.DataFrame):
                if data.shape[1] != 1:
                    raise ValueError("Single column required for goodness of fit test")
                data = data.iloc[:, 0]

            observed = data.value_counts().sort_index()
            expected = kwargs.get('expected')

            if expected is None:
                # Equal expected frequencies
                expected = [len(data) / len(observed)] * len(observed)

            # Check assumptions
            if any(e < 5 for e in expected):
                assumptions_met = False
                assumptions_notes.append("Some expected frequencies < 5 (chi-square assumption violated)")

            statistic, p_value = self.stats.chisquare(observed, expected)
            dof = len(observed) - 1
            effect_size = self._calculate_goodness_of_fit_effect_size(observed, expected)

        else:
            raise ValueError(f"Unsupported chi-square test type: {test_type}")

        # Generate interpretation
        interpretation = self._interpret_chi_square_result(statistic, p_value, effect_size, test_type)

        return StatisticalResult(
            test_type=test_type,
            statistic=float(statistic),
            p_value=float(p_value),
            degrees_of_freedom=int(dof),
            effect_size=effect_size,
            interpretation=interpretation,
            assumptions_met=assumptions_met,
            assumptions_notes=assumptions_notes,
            raw_output={
                'statistic': float(statistic),
                'p_value': float(p_value),
                'degrees_of_freedom': int(dof),
                'sample_size': len(data)
            }
        )

    def perform_correlation_analysis(self, x: pd.Series, y: pd.Series,
                                   method: str = "pearson") -> StatisticalResult:
        """
        Perform correlation analysis with comprehensive validation.

        Args:
            x: First variable
            y: Second variable
            method: Correlation method ('pearson', 'spearman', 'kendall')

        Returns:
            StatisticalResult with correlation analysis results
        """
        if not self.scipy_available:
            raise ImportError("SciPy required for correlation analysis")

        # Remove missing values pairwise
        valid_pairs = ~(x.isna() | y.isna())
        x_clean = x[valid_pairs]
        y_clean = y[valid_pairs]

        if len(x_clean) < 3:
            raise ValueError("Insufficient data for correlation (need at least 3 pairs)")

        assumptions_met = True
        assumptions_notes = []

        if method.lower() == "pearson":
            test_type = TestType.CORRELATION_PEARSON
            correlation, p_value = self.stats.pearsonr(x_clean, y_clean)

            # Check assumptions for Pearson correlation
            if not self._check_normality(x_clean) or not self._check_normality(y_clean):
                assumptions_met = False
                assumptions_notes.append("Data may not be normally distributed (consider Spearman correlation)")

        elif method.lower() == "spearman":
            test_type = TestType.CORRELATION_SPEARMAN
            correlation, p_value = self.stats.spearmanr(x_clean, y_clean)

        elif method.lower() == "kendall":
            correlation, p_value = self.stats.kendalltau(x_clean, y_clean)
            test_type = TestType.CORRELATION_SPEARMAN  # Use same enum for now

        else:
            raise ValueError(f"Unsupported correlation method: {method}")

        # Effect size is the correlation coefficient itself
        effect_size = abs(correlation)

        # Calculate confidence interval for correlation
        confidence_interval = self._calculate_correlation_ci(correlation, len(x_clean))

        # Generate interpretation
        interpretation = self._interpret_correlation_result(correlation, p_value, method)

        return StatisticalResult(
            test_type=test_type,
            statistic=float(correlation),
            p_value=float(p_value),
            effect_size=effect_size,
            confidence_interval=confidence_interval,
            interpretation=interpretation,
            assumptions_met=assumptions_met,
            assumptions_notes=assumptions_notes,
            raw_output={
                'correlation': float(correlation),
                'p_value': float(p_value),
                'method': method,
                'sample_size': len(x_clean)
            }
        )

    def perform_nonparametric_test(self, data1: pd.Series, data2: pd.Series = None,
                                 test_type: TestType = TestType.MANN_WHITNEY_U) -> StatisticalResult:
        """
        Perform non-parametric tests.

        Args:
            data1: First dataset
            data2: Second dataset (for two-sample tests)
            test_type: Type of non-parametric test

        Returns:
            StatisticalResult with test results
        """
        if not self.scipy_available:
            raise ImportError("SciPy required for non-parametric tests")

        data1_clean = data1.dropna()

        if test_type == TestType.MANN_WHITNEY_U:
            if data2 is None:
                raise ValueError("data2 required for Mann-Whitney U test")

            data2_clean = data2.dropna()
            statistic, p_value = self.stats.mannwhitneyu(data1_clean, data2_clean, alternative='two-sided')
            effect_size = self._calculate_mann_whitney_effect_size(data1_clean, data2_clean)

        elif test_type == TestType.WILCOXON_SIGNED_RANK:
            if data2 is None:
                # One-sample test against zero
                statistic, p_value = self.stats.wilcoxon(data1_clean)
            else:
                # Paired test
                if len(data1) != len(data2):
                    raise ValueError("Paired data must have same length")

                valid_pairs = ~(data1.isna() | data2.isna())
                data1_paired = data1[valid_pairs]
                data2_paired = data2[valid_pairs]

                statistic, p_value = self.stats.wilcoxon(data1_paired, data2_paired)

            effect_size = self._calculate_wilcoxon_effect_size(data1_clean, data2)

        elif test_type == TestType.KRUSKAL_WALLIS:
            # This would need multiple groups - simplified for now
            if data2 is None:
                raise ValueError("Multiple groups required for Kruskal-Wallis test")

            data2_clean = data2.dropna()
            statistic, p_value = self.stats.kruskal(data1_clean, data2_clean)
            effect_size = None  # Complex to calculate for K-W

        else:
            raise ValueError(f"Unsupported non-parametric test: {test_type}")

        interpretation = self._interpret_nonparametric_result(statistic, p_value, test_type)

        return StatisticalResult(
            test_type=test_type,
            statistic=float(statistic),
            p_value=float(p_value),
            effect_size=effect_size,
            interpretation=interpretation,
            assumptions_met=True,  # Non-parametric tests have fewer assumptions
            assumptions_notes=["Non-parametric test - fewer distributional assumptions"],
            raw_output={
                'statistic': float(statistic),
                'p_value': float(p_value),
                'sample_size_1': len(data1_clean),
                'sample_size_2': len(data2.dropna()) if data2 is not None else None
            }
        )

    # Helper methods for assumption checking
    def _check_t_test_assumptions(self, data: pd.Series, test_type: TestType, **kwargs) -> Tuple[bool, List[str]]:
        """Check assumptions for t-tests."""
        assumptions_met = True
        notes = []

        # Check normality
        if not self._check_normality(data):
            assumptions_met = False
            notes.append("Data may not be normally distributed")

        # Check for outliers
        if self._has_extreme_outliers(data):
            notes.append("Extreme outliers detected - may affect results")

        # For two-sample tests, check equal variances if specified
        if test_type == TestType.T_TEST_TWO_SAMPLE:
            data2 = kwargs.get('data2')
            equal_var = kwargs.get('equal_var', True)
            if equal_var and data2 is not None:
                if isinstance(data2, list):
                    data2 = pd.Series(data2)
                data2_clean = data2.dropna()

                if not self._check_equal_variances(data, data2_clean):
                    assumptions_met = False
                    notes.append("Equal variances assumption may be violated")

        return assumptions_met, notes

    def _check_anova_assumptions(self, data: pd.DataFrame, dependent_var: str,
                                independent_vars: List[str], test_type: TestType) -> Tuple[bool, List[str]]:
        """Check assumptions for ANOVA."""
        assumptions_met = True
        notes = []

        # Check normality within groups
        for var in independent_vars:
            groups = data.groupby(var)[dependent_var]
            for name, group in groups:
                if len(group) >= 3 and not self._check_normality(group):
                    assumptions_met = False
                    notes.append(f"Normality assumption violated in group {name}")
                    break

        # Check homogeneity of variances (Levene's test)
        if test_type == TestType.ANOVA_ONE_WAY:
            groups = [group[dependent_var].values for name, group in data.groupby(independent_vars[0])]
            if len(groups) >= 2:
                try:
                    levene_stat, levene_p = self.stats.levene(*groups)
                    if levene_p < 0.05:
                        assumptions_met = False
                        notes.append("Homogeneity of variances assumption violated (Levene's test p < 0.05)")
                except:
                    notes.append("Could not perform Levene's test for equal variances")

        return assumptions_met, notes

    def _check_normality(self, data: pd.Series, alpha: float = 0.05) -> bool:
        """Check normality using Shapiro-Wilk test."""
        if len(data) < 3:
            return True  # Cannot test with too few observations

        try:
            if len(data) <= 5000:  # Shapiro-Wilk is reliable for smaller samples
                stat, p_value = self.stats.shapiro(data)
                return p_value > alpha
            else:
                # Use Anderson-Darling for larger samples
                result = self.stats.anderson(data, dist='norm')
                return result.statistic < result.critical_values[2]  # 5% significance level
        except:
            return True  # Assume normal if test fails

    def _check_equal_variances(self, data1: pd.Series, data2: pd.Series, alpha: float = 0.05) -> bool:
        """Check equal variances using Levene's test."""
        try:
            stat, p_value = self.stats.levene(data1, data2)
            return p_value > alpha
        except:
            return True  # Assume equal if test fails

    def _has_extreme_outliers(self, data: pd.Series, threshold: float = 3.0) -> bool:
        """Check for extreme outliers using z-score."""
        try:
            z_scores = np.abs((data - data.mean()) / data.std())
            return (z_scores > threshold).any()
        except:
            return False

    # Effect size calculations
    def _calculate_cohens_d_one_sample(self, data: pd.Series, mu: float) -> float:
        """Calculate Cohen's d for one-sample t-test."""
        return abs(data.mean() - mu) / data.std()

    def _calculate_cohens_d_two_sample(self, data1: pd.Series, data2: pd.Series) -> float:
        """Calculate Cohen's d for two-sample t-test."""
        n1, n2 = len(data1), len(data2)
        pooled_std = np.sqrt(((n1 - 1) * data1.var() + (n2 - 1) * data2.var()) / (n1 + n2 - 2))
        return abs(data1.mean() - data2.mean()) / pooled_std

    def _calculate_cohens_d_paired(self, data1: pd.Series, data2: pd.Series) -> float:
        """Calculate Cohen's d for paired t-test."""
        differences = data1 - data2
        return abs(differences.mean()) / differences.std()

    def _calculate_eta_squared_one_way(self, data: pd.DataFrame, dependent_var: str, independent_var: str) -> float:
        """Calculate eta-squared for one-way ANOVA."""
        groups = data.groupby(independent_var)[dependent_var]

        # Between-group sum of squares
        grand_mean = data[dependent_var].mean()
        ss_between = sum(len(group) * (group.mean() - grand_mean) ** 2 for name, group in groups)

        # Total sum of squares
        ss_total = sum((data[dependent_var] - grand_mean) ** 2)

        return ss_between / ss_total if ss_total > 0 else 0

    def _calculate_eta_squared_two_way(self, anova_table) -> float:
        """Calculate eta-squared for two-way ANOVA."""
        # This is simplified - would need more complex calculation for partial eta-squared
        ss_total = anova_table['sum_sq'].sum()
        ss_effect = anova_table.iloc[0]['sum_sq']  # First effect
        return ss_effect / ss_total if ss_total > 0 else 0

    def _calculate_cramers_v(self, contingency_table: pd.DataFrame, chi2_stat: float) -> float:
        """Calculate Cramer's V for chi-square test."""
        n = contingency_table.sum().sum()
        min_dim = min(contingency_table.shape) - 1
        return np.sqrt(chi2_stat / (n * min_dim)) if min_dim > 0 else 0

    def _calculate_goodness_of_fit_effect_size(self, observed, expected) -> float:
        """Calculate effect size for goodness of fit test."""
        # Use Cohen's w
        return np.sqrt(sum((o - e) ** 2 / e for o, e in zip(observed, expected)) / len(observed))

    def _calculate_t_test_ci(self, data: pd.Series, test_type: TestType, **kwargs) -> Tuple[float, float]:
        """Calculate confidence interval for t-test."""
        try:
            if test_type == TestType.T_TEST_ONE_SAMPLE:
                mean = data.mean()
                sem = data.std() / np.sqrt(len(data))
                t_critical = self.stats.t.ppf((1 + self.confidence_level) / 2, len(data) - 1)
                margin = t_critical * sem
                return (mean - margin, mean + margin)
            else:
                # For two-sample tests, return difference CI (simplified)
                return (None, None)
        except:
            return (None, None)

    def _calculate_correlation_ci(self, correlation: float, n: int) -> Tuple[float, float]:
        """Calculate confidence interval for correlation coefficient."""
        try:
            # Fisher's z-transformation
            z = 0.5 * np.log((1 + correlation) / (1 - correlation))
            se = 1 / np.sqrt(n - 3)
            z_critical = self.stats.norm.ppf((1 + self.confidence_level) / 2)

            z_lower = z - z_critical * se
            z_upper = z + z_critical * se

            # Transform back to correlation scale
            r_lower = (np.exp(2 * z_lower) - 1) / (np.exp(2 * z_lower) + 1)
            r_upper = (np.exp(2 * z_upper) - 1) / (np.exp(2 * z_upper) + 1)

            return (r_lower, r_upper)
        except:
            return (None, None)

    # Effect size calculations for non-parametric tests
    def _calculate_mann_whitney_effect_size(self, data1: pd.Series, data2: pd.Series) -> float:
        """Calculate effect size for Mann-Whitney U test (rank-biserial correlation)."""
        try:
            n1, n2 = len(data1), len(data2)
            u_stat, _ = self.stats.mannwhitneyu(data1, data2, alternative='two-sided')
            return 1 - (2 * u_stat) / (n1 * n2)
        except:
            return None

    def _calculate_wilcoxon_effect_size(self, data1: pd.Series, data2: pd.Series = None) -> float:
        """Calculate effect size for Wilcoxon test."""
        try:
            if data2 is None:
                # One-sample test - use median effect size
                return abs(data1.median()) / data1.std()
            else:
                # Paired test - use effect size of differences
                differences = data1 - data2
                return abs(differences.median()) / differences.std()
        except:
            return None

    # Interpretation methods
    def _interpret_t_test_result(self, statistic: float, p_value: float,
                                effect_size: float, test_type: TestType) -> str:
        """Generate interpretation for t-test results."""
        significance = "significant" if p_value < self.alpha_level else "not significant"

        # Effect size interpretation (Cohen's conventions)
        if effect_size is not None:
            if effect_size < 0.2:
                effect_desc = "negligible"
            elif effect_size < 0.5:
                effect_desc = "small"
            elif effect_size < 0.8:
                effect_desc = "medium"
            else:
                effect_desc = "large"
        else:
            effect_desc = "unknown"

        test_name = {
            TestType.T_TEST_ONE_SAMPLE: "one-sample t-test",
            TestType.T_TEST_TWO_SAMPLE: "two-sample t-test",
            TestType.T_TEST_PAIRED: "paired t-test"
        }.get(test_type, "t-test")

        return f"The {test_name} result is {significance} (p = {p_value:.4f}). " \
               f"The effect size is {effect_desc} (Cohen's d = {effect_size:.3f})." if effect_size else \
               f"The {test_name} result is {significance} (p = {p_value:.4f})."

    def _interpret_anova_result(self, statistic: float, p_value: float,
                               effect_size: float, test_type: TestType) -> str:
        """Generate interpretation for ANOVA results."""
        significance = "significant" if p_value < self.alpha_level else "not significant"

        # Effect size interpretation (eta-squared conventions)
        if effect_size is not None:
            if effect_size < 0.01:
                effect_desc = "negligible"
            elif effect_size < 0.06:
                effect_desc = "small"
            elif effect_size < 0.14:
                effect_desc = "medium"
            else:
                effect_desc = "large"
        else:
            effect_desc = "unknown"

        test_name = {
            TestType.ANOVA_ONE_WAY: "one-way ANOVA",
            TestType.ANOVA_TWO_WAY: "two-way ANOVA"
        }.get(test_type, "ANOVA")

        return f"The {test_name} result is {significance} (F = {statistic:.3f}, p = {p_value:.4f}). " \
               f"The effect size is {effect_desc} (η² = {effect_size:.3f})." if effect_size else \
               f"The {test_name} result is {significance} (F = {statistic:.3f}, p = {p_value:.4f})."

    def _interpret_chi_square_result(self, statistic: float, p_value: float,
                                    effect_size: float, test_type: TestType) -> str:
        """Generate interpretation for chi-square results."""
        significance = "significant" if p_value < self.alpha_level else "not significant"

        if test_type == TestType.CHI_SQUARE_INDEPENDENCE:
            test_desc = "chi-square test of independence"
            if effect_size is not None:
                if effect_size < 0.1:
                    effect_desc = "negligible"
                elif effect_size < 0.3:
                    effect_desc = "small"
                elif effect_size < 0.5:
                    effect_desc = "medium"
                else:
                    effect_desc = "large"
                effect_text = f"The association strength is {effect_desc} (Cramer's V = {effect_size:.3f})."
            else:
                effect_text = ""
        else:
            test_desc = "chi-square goodness of fit test"
            effect_text = f"Effect size (Cohen's w) = {effect_size:.3f}." if effect_size else ""

        return f"The {test_desc} result is {significance} (χ² = {statistic:.3f}, p = {p_value:.4f}). {effect_text}"

    def _interpret_correlation_result(self, correlation: float, p_value: float, method: str) -> str:
        """Generate interpretation for correlation results."""
        significance = "significant" if p_value < self.alpha_level else "not significant"

        # Correlation strength interpretation
        abs_corr = abs(correlation)
        if abs_corr < 0.1:
            strength = "negligible"
        elif abs_corr < 0.3:
            strength = "weak"
        elif abs_corr < 0.5:
            strength = "moderate"
        elif abs_corr < 0.7:
            strength = "strong"
        else:
            strength = "very strong"

        direction = "positive" if correlation > 0 else "negative"

        return f"The {method} correlation is {significance} (r = {correlation:.3f}, p = {p_value:.4f}). " \
               f"There is a {strength} {direction} relationship between the variables."

    def _interpret_nonparametric_result(self, statistic: float, p_value: float, test_type: TestType) -> str:
        """Generate interpretation for non-parametric test results."""
        significance = "significant" if p_value < self.alpha_level else "not significant"

        test_names = {
            TestType.MANN_WHITNEY_U: "Mann-Whitney U test",
            TestType.WILCOXON_SIGNED_RANK: "Wilcoxon signed-rank test",
            TestType.KRUSKAL_WALLIS: "Kruskal-Wallis test"
        }

        test_name = test_names.get(test_type, "non-parametric test")

        return f"The {test_name} result is {significance} (statistic = {statistic:.3f}, p = {p_value:.4f}). " \
               f"This test makes fewer distributional assumptions than parametric alternatives."

    def format_result_for_agent(self, result: StatisticalResult, agent_identity: str = "analyst") -> Dict[str, Any]:
        """
        Format statistical results for agent-specific presentation.

        Args:
            result: StatisticalResult to format
            agent_identity: Agent identity for customized formatting

        Returns:
            Formatted result dictionary
        """
        # Base formatting
        formatted_result = {
            "test_type": result.test_type.value,
            "statistic": result.statistic,
            "p_value": result.p_value,
            "interpretation": result.interpretation,
            "assumptions_met": result.assumptions_met
        }

        # Add optional fields
        if result.degrees_of_freedom is not None:
            formatted_result["degrees_of_freedom"] = result.degrees_of_freedom

        if result.effect_size is not None:
            formatted_result["effect_size"] = result.effect_size

        if result.confidence_interval and result.confidence_interval[0] is not None:
            formatted_result["confidence_interval"] = result.confidence_interval

        if result.assumptions_notes:
            formatted_result["assumptions_notes"] = result.assumptions_notes

        # Agent-specific enhancements
        if agent_identity == "marketer":
            formatted_result["business_implications"] = self._add_business_implications(result)
        elif agent_identity == "concierge":
            formatted_result["simplified_explanation"] = self._add_simplified_explanation(result)
        elif agent_identity == "analyst":
            formatted_result["technical_details"] = result.raw_output

        return formatted_result

    def _add_business_implications(self, result: StatisticalResult) -> str:
        """Add business implications for marketing agents."""
        if result.p_value < self.alpha_level:
            return "This significant result suggests actionable insights for business decision-making. " \
                   "Consider implementing changes based on these findings."
        else:
            return "No significant difference detected. Current strategies may be maintained, " \
                   "or additional data collection may be needed."

    def _add_simplified_explanation(self, result: StatisticalResult) -> str:
        """Add simplified explanation for concierge agents."""
        if result.p_value < self.alpha_level:
            return "The analysis shows a meaningful difference or relationship in your data. " \
                   "This suggests the pattern you're seeing is likely real, not due to chance."
        else:
            return "The analysis doesn't show a clear pattern or difference. " \
                   "Any differences you see might just be due to random variation."
