import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Settings,
  Database,
  Plus,
  Edit,
  Trash2,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Star,
  Copy,
  Download,
  Upload,
  Shield,
  Clock,
  Code,
  AlertTriangle,
  Info,
  CheckCircle
} from 'lucide-react';
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';
import { useDashboardManagement } from '@/hooks/use-dashboard-management';
import { useToast } from '@/hooks/use-toast';
import { DashboardCreateDialog } from './DashboardCreateDialog';
import { DashboardDataSourceManager } from './DashboardDataSourceManager';

interface DashboardSettingsPanelProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dashboardId?: string;
}

interface DashboardSettings {
  general: {
    name: string;
    description: string;
    tags: string[];
    isPublic: boolean;
    isDefault: boolean;
  };
  performance: {
    refreshInterval: number;
    autoRefresh: boolean;
    cacheEnabled: boolean;
    cacheDuration: number;
    lazyLoading: boolean;
  };
  security: {
    requireAuth: boolean;
    allowComments: boolean;
    allowDownloads: boolean;
    allowEmbedding: boolean;
    ipWhitelist: string[];
  };
  advanced: {
    enableDebugMode: boolean;
    customCSS: string;
    customJS: string;
    apiEndpoints: Record<string, string>;
  };
}

const DEFAULT_SETTINGS: DashboardSettings = {
  general: {
    name: '',
    description: '',
    tags: [],
    isPublic: false,
    isDefault: false,
  },
  performance: {
    refreshInterval: 300,
    autoRefresh: true,
    cacheEnabled: true,
    cacheDuration: 3600,
    lazyLoading: true,
  },
  security: {
    requireAuth: true,
    allowComments: true,
    allowDownloads: true,
    allowEmbedding: false,
    ipWhitelist: [],
  },
  advanced: {
    enableDebugMode: false,
    customCSS: '',
    customJS: '',
    apiEndpoints: {},
  },
};

export const DashboardSettingsPanel: React.FC<DashboardSettingsPanelProps> = ({
  open,
  onOpenChange,
  dashboardId,
}) => {
  const { toast } = useToast();
  const { currentLayout, updateDashboard } = useUnifiedDashboardStore();

  const [settings, setSettings] = useState<DashboardSettings>(DEFAULT_SETTINGS);
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  // Load current settings
  useEffect(() => {
    if (open && dashboardId && currentLayout?.dashboard) {
      loadDashboardSettings();
    }
  }, [open, dashboardId, currentLayout]);

  const loadDashboardSettings = async () => {
    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      } else {
        // Use current dashboard data as fallback
        const dashboard = currentLayout?.dashboard;
        if (dashboard) {
          setSettings({
            general: {
              name: dashboard.name || '',
              description: dashboard.description || '',
              tags: dashboard.tags || [],
              isPublic: dashboard.is_public || false,
              isDefault: dashboard.is_default || false,
            },
            performance: {
              refreshInterval: dashboard.refresh_interval || 300,
              autoRefresh: true,
              cacheEnabled: true,
              cacheDuration: 3600,
              lazyLoading: true,
            },
            security: {
              requireAuth: !dashboard.is_public,
              allowComments: true,
              allowDownloads: true,
              allowEmbedding: false,
              ipWhitelist: [],
            },
            advanced: {
              enableDebugMode: false,
              customCSS: '',
              customJS: '',
              apiEndpoints: {},
            },
          });
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load dashboard settings.",
        variant: "destructive",
      });
    }
  };

  const updateSetting = (section: keyof DashboardSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleAddTag = () => {
    if (newTag.trim() && !settings.general.tags.includes(newTag.trim())) {
      updateSetting('general', 'tags', [...settings.general.tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    updateSetting('general', 'tags', settings.general.tags.filter(tag => tag !== tagToRemove));
  };

  const handleSaveSettings = async () => {
    if (!dashboardId) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/settings`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        // Also update the dashboard basic info
        await updateDashboard(dashboardId, {
          name: settings.general.name,
          description: settings.general.description,
          is_public: settings.general.isPublic,
          is_default: settings.general.isDefault,
          refresh_interval: settings.performance.refreshInterval,
          tags: settings.general.tags,
        });

        setHasChanges(false);
        toast({
          title: "Settings Saved",
          description: "Dashboard settings have been updated successfully.",
        });
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save dashboard settings.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleExportSettings = () => {
    const settingsData = JSON.stringify(settings, null, 2);
    const blob = new Blob([settingsData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `dashboard-${dashboardId}-settings.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast({
      title: "Settings Exported",
      description: "Dashboard settings have been exported.",
    });
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target?.result as string);
        setSettings(importedSettings);
        setHasChanges(true);

        toast({
          title: "Settings Imported",
          description: "Dashboard settings have been imported successfully.",
        });
      } catch (error) {
        toast({
          title: "Import Error",
          description: "Invalid settings file format.",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-[600px] sm:max-w-[600px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Dashboard Settings</span>
          </SheetTitle>
          <SheetDescription>
            Configure dashboard behavior, security, and advanced options.
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 mt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Dashboard Name</Label>
                    <Input
                      id="name"
                      value={settings.general.name}
                      onChange={(e) => updateSetting('general', 'name', e.target.value)}
                      placeholder="Enter dashboard name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={settings.general.description}
                      onChange={(e) => updateSetting('general', 'description', e.target.value)}
                      placeholder="Describe what this dashboard shows"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Tags</Label>
                    <div className="flex space-x-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add a tag"
                        onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                      />
                      <Button onClick={handleAddTag} variant="outline">
                        Add
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {settings.general.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="cursor-pointer"
                          onClick={() => handleRemoveTag(tag)}
                        >
                          {tag} ×
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Public Dashboard</p>
                        <p className="text-sm text-muted-foreground">
                          Make this dashboard publicly accessible
                        </p>
                      </div>
                      <Switch
                        checked={settings.general.isPublic}
                        onCheckedChange={(checked) => updateSetting('general', 'isPublic', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Default Dashboard</p>
                        <p className="text-sm text-muted-foreground">
                          Set as the default dashboard for new users
                        </p>
                      </div>
                      <Switch
                        checked={settings.general.isDefault}
                        onCheckedChange={(checked) => updateSetting('general', 'isDefault', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Clock className="h-5 w-5" />
                    <span>Performance Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Auto Refresh Interval (seconds)</Label>
                    <Select
                      value={settings.performance.refreshInterval.toString()}
                      onValueChange={(value) => updateSetting('performance', 'refreshInterval', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="60">1 minute</SelectItem>
                        <SelectItem value="300">5 minutes</SelectItem>
                        <SelectItem value="600">10 minutes</SelectItem>
                        <SelectItem value="1800">30 minutes</SelectItem>
                        <SelectItem value="3600">1 hour</SelectItem>
                        <SelectItem value="0">Disabled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Auto Refresh</p>
                      <p className="text-sm text-muted-foreground">
                        Automatically refresh dashboard data
                      </p>
                    </div>
                    <Switch
                      checked={settings.performance.autoRefresh}
                      onCheckedChange={(checked) => updateSetting('performance', 'autoRefresh', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Enable Caching</p>
                      <p className="text-sm text-muted-foreground">
                        Cache data to improve performance
                      </p>
                    </div>
                    <Switch
                      checked={settings.performance.cacheEnabled}
                      onCheckedChange={(checked) => updateSetting('performance', 'cacheEnabled', checked)}
                    />
                  </div>

                  {settings.performance.cacheEnabled && (
                    <div className="space-y-2">
                      <Label>Cache Duration (seconds)</Label>
                      <Input
                        type="number"
                        value={settings.performance.cacheDuration}
                        onChange={(e) => updateSetting('performance', 'cacheDuration', parseInt(e.target.value))}
                        min="60"
                        max="86400"
                      />
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Lazy Loading</p>
                      <p className="text-sm text-muted-foreground">
                        Load widgets only when visible
                      </p>
                    </div>
                    <Switch
                      checked={settings.performance.lazyLoading}
                      onCheckedChange={(checked) => updateSetting('performance', 'lazyLoading', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Security & Access</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Require Authentication</p>
                      <p className="text-sm text-muted-foreground">
                        Users must sign in to view dashboard
                      </p>
                    </div>
                    <Switch
                      checked={settings.security.requireAuth}
                      onCheckedChange={(checked) => updateSetting('security', 'requireAuth', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Allow Comments</p>
                      <p className="text-sm text-muted-foreground">
                        Users can add comments to widgets
                      </p>
                    </div>
                    <Switch
                      checked={settings.security.allowComments}
                      onCheckedChange={(checked) => updateSetting('security', 'allowComments', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Allow Downloads</p>
                      <p className="text-sm text-muted-foreground">
                        Users can download data and images
                      </p>
                    </div>
                    <Switch
                      checked={settings.security.allowDownloads}
                      onCheckedChange={(checked) => updateSetting('security', 'allowDownloads', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Allow Embedding</p>
                      <p className="text-sm text-muted-foreground">
                        Dashboard can be embedded in other sites
                      </p>
                    </div>
                    <Switch
                      checked={settings.security.allowEmbedding}
                      onCheckedChange={(checked) => updateSetting('security', 'allowEmbedding', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Code className="h-5 w-5" />
                    <span>Advanced Options</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Debug Mode</p>
                      <p className="text-sm text-muted-foreground">
                        Enable debugging information
                      </p>
                    </div>
                    <Switch
                      checked={settings.advanced.enableDebugMode}
                      onCheckedChange={(checked) => updateSetting('advanced', 'enableDebugMode', checked)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Custom CSS</Label>
                    <Textarea
                      value={settings.advanced.customCSS}
                      onChange={(e) => updateSetting('advanced', 'customCSS', e.target.value)}
                      placeholder="/* Custom CSS styles */"
                      rows={4}
                      className="font-mono text-sm"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Custom JavaScript</Label>
                    <Textarea
                      value={settings.advanced.customJS}
                      onChange={(e) => updateSetting('advanced', 'customJS', e.target.value)}
                      placeholder="// Custom JavaScript code"
                      rows={4}
                      className="font-mono text-sm"
                    />
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm font-medium text-yellow-800">Warning</span>
                    </div>
                    <p className="text-sm text-yellow-700 mt-1">
                      Custom code can affect dashboard performance and security. Use with caution.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleExportSettings}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <div>
                <Input
                  type="file"
                  accept=".json"
                  onChange={handleImportSettings}
                  className="hidden"
                  id="settings-import"
                />
                <Label htmlFor="settings-import">
                  <Button variant="outline" asChild>
                    <span>
                      <Upload className="h-4 w-4 mr-2" />
                      Import
                    </span>
                  </Button>
                </Label>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleSaveSettings}
                disabled={!hasChanges || isSaving}
                className="flex items-center space-x-2"
              >
                {isSaving ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    <span>Save Settings</span>
                  </>
                )}
              </Button>
            </div>
          </div>

          {hasChanges && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Info className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Unsaved Changes</span>
              </div>
              <p className="text-sm text-blue-700 mt-1">
                You have unsaved changes. Click "Save Settings" to apply them.
              </p>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};
