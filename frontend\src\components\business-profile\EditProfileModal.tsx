import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { BusinessProfileForm } from './BusinessProfileForm';
import { businessProfileApi, BusinessProfile, BusinessProfileUpdate } from '@/lib/businessProfileApi';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

interface EditProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  profileId: string;
  onProfileUpdated?: (profile: BusinessProfile) => void;
}

export const EditProfileModal: React.FC<EditProfileModalProps> = ({
  isOpen,
  onClose,
  profileId,
  onProfileUpdated
}) => {
  const [profile, setProfile] = useState<BusinessProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();

  // Load profile data when modal opens
  useEffect(() => {
    if (isOpen && profileId) {
      loadProfile();
    }
  }, [isOpen, profileId]);

  const loadProfile = async () => {
    setIsLoading(true);
    try {
      const profileData = await businessProfileApi.getProfile(profileId);
      setProfile(profileData);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load profile data',
        variant: 'destructive',
      });
      onClose();
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (data: BusinessProfileUpdate) => {
    setIsUpdating(true);
    try {
      const updatedProfile = await businessProfileApi.updateProfile(profileId, data);
      
      toast({
        title: 'Profile Updated',
        description: 'Your business profile has been updated successfully.',
      });

      if (onProfileUpdated) {
        onProfileUpdated(updatedProfile);
      }

      onClose();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update profile',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Business Profile</DialogTitle>
          <DialogDescription>
            Update your business profile information to improve AI agent context and recommendations.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading profile...</span>
          </div>
        ) : profile ? (
          <BusinessProfileForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isUpdating}
            initialData={profile}
            submitLabel="Update Profile"
            title="Update Business Profile"
            description="Modify your business profile information"
          />
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">Failed to load profile data</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
