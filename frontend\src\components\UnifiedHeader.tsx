import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import {
  Zap,
  User,
  LogOut,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CartIcon } from '@/components/cart/CartIcon';
import { useAuth } from '@/contexts/AuthContext';
import { useNavbarStore } from '@/stores/navbar-store';
import { ModeToggleSwitch } from './dashboard/DualModeIntegration';
import { HeaderToolbarToggle } from './dashboard/ToolbarToggleButton';
import { BusinessProfileHeaderDropdown } from '@/components/business-profile/BusinessProfileHeaderDropdown';
import { cn } from '@/lib/utils';

interface UnifiedHeaderProps {
  // Core application props
  className?: string;

  // Context-aware props
  showDashboardContext?: boolean;
  dashboardTitle?: string;
  isSystemLoading?: boolean;

  // Optional contextual elements (simplified)
  quickActions?: React.ReactNode;
}

export const UnifiedHeader: React.FC<UnifiedHeaderProps> = ({
  className,
  showDashboardContext = false,
  dashboardTitle,
  isSystemLoading = false,
  quickActions,
}) => {
  const { user, logout } = useAuth();
  const { shouldShowExpanded, getNavbarWidth } = useNavbarStore();

  // Get current navbar state for responsive positioning
  const isNavbarExpanded = shouldShowExpanded();
  const navbarWidth = getNavbarWidth();

  // Handle logout
  const handleLogout = () => {
    logout();
  };

  // Global keyboard shortcuts only (dashboard shortcuts handled by mode-specific toolbars)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Global shortcuts only - Ctrl/Cmd + K for command palette, etc.
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        // Open global command palette if implemented
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <motion.div
      className="sticky top-0 z-20 w-full"
      initial={false}
      animate={{
        marginLeft: navbarWidth,
        width: `calc(100% - ${navbarWidth}px)`
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "bg-white/95 backdrop-blur-sm border-b border-slate-200/50",
          "py-2 flex justify-center min-h-[48px] w-full",
          className
        )}
        style={{
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)'
        }}
      >
        {/* Optimized header content with clear responsibility separation */}
        <div className="w-full max-w-[1600px] px-4 sm:px-6 lg:px-8 flex items-center justify-between">

          {/* Left Section - Context-Aware Dashboard Information */}
          <div className="flex items-center space-x-4 flex-1 min-w-0">
            {showDashboardContext ? (
              <>
                {/* Simplified Dashboard Context */}
                <div className="flex items-center space-x-2 min-w-0">
                  <h1 className="text-lg font-semibold text-slate-900 truncate">
                    {dashboardTitle || "Dashboard"}
                  </h1>
                  {isSystemLoading && (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="flex items-center flex-shrink-0"
                    >
                      <Zap className="h-4 w-4 text-blue-500" />
                    </motion.div>
                  )}
                </div>

                {/* Toolbar Toggle - Controls mode-specific toolbars */}
                <HeaderToolbarToggle className="flex-shrink-0" />
              </>
            ) : (
              <div className="flex-1" />
            )}
          </div>

          {/* Center Section - Quick Actions (Optional) */}
          <div className="flex-1 flex justify-center">
            {quickActions && (
              <div className="flex items-center space-x-2">
                {quickActions}
              </div>
            )}
          </div>

          {/* Right Section - Global Application Controls */}
          <div className="flex items-center space-x-4 flex-shrink-0">
            {/* Business Profile Selector */}
            <BusinessProfileHeaderDropdown />

            {/* Mode Toggle - Only show on dashboard pages */}
            {showDashboardContext && <ModeToggleSwitch compact />}

            {/* Core Application Services */}
            <div className="flex items-center space-x-3 border-l border-slate-200 pl-4">
              <CartIcon />

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-9 w-9 p-0 rounded-full">
                    <User className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>
                    {user?.email || 'User Account'}
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="cursor-pointer" onClick={handleLogout}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </motion.header>
    </motion.div>
  );
};
