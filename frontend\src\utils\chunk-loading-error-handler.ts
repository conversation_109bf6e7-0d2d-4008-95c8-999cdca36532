/**
 * Chunk Loading Error Handler
 * Handles dynamic import failures and provides fallback mechanisms
 */

interface ChunkLoadError extends Error {
  code?: string;
  type?: string;
}

interface RetryOptions {
  maxRetries?: number;
  retryDelay?: number;
  fallbackComponent?: React.ComponentType<any>;
}

class ChunkLoadingErrorHandler {
  private retryCount = new Map<string, number>();
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000;

  /**
   * Handle chunk loading errors with retry logic
   */
  async handleChunkLoadError<T>(
    importFn: () => Promise<T>,
    chunkName: string,
    options: RetryOptions = {}
  ): Promise<T> {
    const { maxRetries = this.maxRetries, retryDelay = this.retryDelay } = options;
    const currentRetries = this.retryCount.get(chunkName) || 0;

    try {
      const result = await importFn();
      // Reset retry count on success
      this.retryCount.delete(chunkName);
      return result;
    } catch (error) {
      const chunkError = error as ChunkLoadError;
      
      console.error(`Chunk loading failed for ${chunkName}:`, chunkError);

      // Check if this is a chunk loading error
      if (this.isChunkLoadError(chunkError)) {
        if (currentRetries < maxRetries) {
          console.log(`Retrying chunk load for ${chunkName} (attempt ${currentRetries + 1}/${maxRetries})`);
          
          this.retryCount.set(chunkName, currentRetries + 1);
          
          // Wait before retrying
          await this.delay(retryDelay * (currentRetries + 1));
          
          // Try to clear module cache if possible
          this.clearModuleCache(chunkName);
          
          return this.handleChunkLoadError(importFn, chunkName, options);
        } else {
          console.error(`Max retries exceeded for chunk ${chunkName}`);
          this.retryCount.delete(chunkName);
          
          // Suggest page reload
          this.suggestPageReload(chunkName);
          throw new Error(`Failed to load chunk ${chunkName} after ${maxRetries} retries`);
        }
      }
      
      throw error;
    }
  }

  /**
   * Check if error is related to chunk loading
   */
  private isChunkLoadError(error: ChunkLoadError): boolean {
    const chunkErrorPatterns = [
      /loading chunk/i,
      /loading css chunk/i,
      /failed to fetch/i,
      /networkerror/i,
      /chunk load failed/i,
      /loading module/i
    ];

    const errorMessage = error.message || '';
    const errorType = error.type || '';
    
    return chunkErrorPatterns.some(pattern => 
      pattern.test(errorMessage) || pattern.test(errorType)
    );
  }

  /**
   * Clear module cache for retry
   */
  private clearModuleCache(chunkName: string): void {
    try {
      // Clear any cached modules if possible
      if ('webpackChunkName' in window) {
        // Webpack specific cache clearing
        delete (window as any).__webpack_require__.cache[chunkName];
      }
    } catch (error) {
      console.warn('Could not clear module cache:', error);
    }
  }

  /**
   * Suggest page reload to user
   */
  private suggestPageReload(chunkName: string): void {
    const shouldReload = window.confirm(
      `Failed to load application module (${chunkName}). Would you like to reload the page to try again?`
    );
    
    if (shouldReload) {
      window.location.reload();
    }
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a higher-order component for handling chunk loading errors
   */
  withChunkErrorHandling<P extends object>(
    importFn: () => Promise<{ default: React.ComponentType<P> }>,
    chunkName: string,
    fallbackComponent?: React.ComponentType<P>
  ) {
    return React.lazy(async () => {
      try {
        return await this.handleChunkLoadError(importFn, chunkName);
      } catch (error) {
        console.error(`Failed to load component ${chunkName}:`, error);
        
        if (fallbackComponent) {
          return { default: fallbackComponent };
        }
        
        // Return a default error component
        return {
          default: (props: P) => React.createElement('div', {
            className: 'p-4 text-center text-red-600 bg-red-50 rounded-lg border border-red-200',
            children: [
              React.createElement('h3', { 
                className: 'font-semibold mb-2',
                children: 'Component Loading Error'
              }),
              React.createElement('p', {
                className: 'text-sm',
                children: `Failed to load ${chunkName}. Please refresh the page.`
              }),
              React.createElement('button', {
                className: 'mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700',
                onClick: () => window.location.reload(),
                children: 'Reload Page'
              })
            ]
          })
        };
      }
    });
  }

  /**
   * Global error handler for unhandled chunk loading errors
   */
  setupGlobalErrorHandler(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason;
      if (this.isChunkLoadError(error)) {
        console.error('Unhandled chunk loading error:', error);
        event.preventDefault();
        
        // Show user-friendly error message
        this.showChunkErrorNotification();
      }
    });

    // Handle general errors
    window.addEventListener('error', (event) => {
      if (this.isChunkLoadError(event.error)) {
        console.error('Chunk loading error:', event.error);
        this.showChunkErrorNotification();
      }
    });
  }

  /**
   * Show user-friendly error notification
   */
  private showChunkErrorNotification(): void {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg z-50';
    notification.innerHTML = `
      <div class="flex items-center space-x-2">
        <div class="flex-1">
          <h4 class="font-semibold">Loading Error</h4>
          <p class="text-sm">Some components failed to load. Please refresh the page.</p>
        </div>
        <button onclick="window.location.reload()" class="bg-red-700 hover:bg-red-800 px-3 py-1 rounded text-sm">
          Reload
        </button>
        <button onclick="this.parentElement.parentElement.remove()" class="text-red-200 hover:text-white">
          ×
        </button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 10000);
  }
}

// Export singleton instance
export const chunkLoadingErrorHandler = new ChunkLoadingErrorHandler();

// React import
import React from 'react';

// Setup global error handling on module load
if (typeof window !== 'undefined') {
  chunkLoadingErrorHandler.setupGlobalErrorHandler();
}

// Utility function for lazy loading with error handling
export const lazyWithErrorHandling = <P extends object>(
  importFn: () => Promise<{ default: React.ComponentType<P> }>,
  chunkName: string,
  fallbackComponent?: React.ComponentType<P>
) => {
  return chunkLoadingErrorHandler.withChunkErrorHandling(importFn, chunkName, fallbackComponent);
};
