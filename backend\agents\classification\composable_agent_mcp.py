"""
Composable classification agent for the Datagenius backend.

This module provides a composable implementation of the classification agent
that uses the component-based architecture with MCP tools.
"""

import logging
from typing import Dict, Any, Optional, List

from agents.composable import ComposableAgent
from .components_mcp import (
    ClassificationParserComponent,
    MCPClassifierComponent,
    ClassificationErrorHandlerComponent
)
from agents.components.mcp_server import MCPServerComponent

# Configure logging
logger = logging.getLogger(__name__)


class ComposableClassificationAgent(ComposableAgent):
    """Composable implementation of the classification agent using MCP tools."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the composable classification agent.

        Args:
            config: Configuration dictionary for the agent
        """
        logger.info("Initializing Composable Classification Agent with MCP tools")

        # Load database configuration and merge with YAML config
        merged_config = await self._load_merged_configuration(config)

        # Call the parent initialization to set up the base components
        await super()._initialize(merged_config)

        # If no components were configured, set up the default components
        if not self.components:
            logger.info("No components configured, setting up default classification components")

            # Create and initialize the parser component
            parser_component = ClassificationParserComponent()
            await parser_component.initialize({
                "name": "classification_parser"
            })
            self.components.append(parser_component)

            # Create and initialize the MCP server component with essential tools
            from agents.components import create_mcp_server_with_essential_tools

            # Create MCP server with essential tools first
            mcp_server_component = await create_mcp_server_with_essential_tools({
                "name": "classification_tools",
                "server_name": "datagenius-classification-tools",
                "server_version": "1.0.0",
                "tools": [
                    {
                        "type": "text_classification"
                    }
                ]
            })
            self.components.append(mcp_server_component)

            # Create and initialize the MCP classifier component
            mcp_component = MCPClassifierComponent()
            await mcp_component.initialize({
                "name": "mcp_classifier"
            })
            self.components.append(mcp_component)

            # Create and initialize the error handler component
            error_component = ClassificationErrorHandlerComponent()
            await error_component.initialize({
                "name": "error_handler"
            })
            self.components.append(error_component)

            logger.info(f"Initialized {len(self.components)} default classification components")

    async def _load_merged_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Load and merge YAML configuration with database configuration.

        Args:
            config: YAML configuration dictionary

        Returns:
            Merged configuration with database settings taking priority
        """
        try:
            # Import the centralized configuration utilities
            from ..utils.model_init import load_agent_database_config, merge_agent_config

            # Load database configuration for this agent
            db_config = await load_agent_database_config("composable-classifier-ai")

            # Merge YAML config with database config (database takes priority)
            merged_config = merge_agent_config(config, db_config, "composable-classifier-ai")

            return merged_config

        except Exception as e:
            logger.error(f"Error loading database configuration for classification agent: {e}")
            # Fall back to original config
            return config

    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message using the agent's components.

        Args:
            user_id: The ID of the user sending the message
            message: The user's message text
            conversation_id: The ID of the conversation
            context: Additional context information

        Returns:
            Dict containing response text and any additional data
        """
        # Create initial context with agent components for inter-component communication
        ctx = {
            "user_id": user_id,
            "message": message,
            "conversation_id": conversation_id,
            "context": context or {},
            "agent_config": self.config,
            "agent_components": self.components,
            "response": "",
            "metadata": {}
        }

        # Process the message using the parent method
        return await super().process_message(user_id, message, conversation_id, context)
