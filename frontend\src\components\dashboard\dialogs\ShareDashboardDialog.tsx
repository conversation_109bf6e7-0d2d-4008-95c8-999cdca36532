/**
 * Share Dashboard Dialog
 * 
 * Dialog for sharing dashboards with other users.
 * Features:
 * - Public/private sharing options
 * - User permission management
 * - Share link generation
 * - Access control settings
 * - Collaboration features
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Share,
  Copy,
  Globe,
  Lock,
  Users,
  Mail,
  Link,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  Check,
  ExternalLink,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ShareDashboardDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dashboardId: string;
  dashboardName: string;
  isPublic?: boolean;
}

interface SharedUser {
  id: string;
  email: string;
  name: string;
  role: 'viewer' | 'editor' | 'admin';
  addedAt: string;
  lastAccessed?: string;
}

const PERMISSION_LEVELS = [
  { value: 'viewer', label: 'Viewer', description: 'Can view the dashboard' },
  { value: 'editor', label: 'Editor', description: 'Can view and edit the dashboard' },
  { value: 'admin', label: 'Admin', description: 'Full access including sharing' },
];

export const ShareDashboardDialog: React.FC<ShareDashboardDialogProps> = ({
  open,
  onOpenChange,
  dashboardId,
  dashboardName,
  isPublic = false,
}) => {
  const { toast } = useToast();
  
  const [activeTab, setActiveTab] = useState('general');
  const [isPublicSharing, setIsPublicSharing] = useState(isPublic);
  const [shareLink, setShareLink] = useState('');
  const [sharedUsers, setSharedUsers] = useState<SharedUser[]>([]);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserRole, setNewUserRole] = useState<'viewer' | 'editor' | 'admin'>('viewer');
  const [isLoading, setIsLoading] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);

  // Load sharing data
  useEffect(() => {
    if (open && dashboardId) {
      loadSharingData();
    }
  }, [open, dashboardId]);

  const loadSharingData = async () => {
    try {
      setIsLoading(true);
      
      // Load current sharing settings
      const response = await fetch(`/api/dashboards/${dashboardId}/sharing`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setIsPublicSharing(data.isPublic);
        setSharedUsers(data.sharedUsers || []);
        setShareLink(data.shareLink || generateShareLink());
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load sharing settings.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generateShareLink = () => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/dashboard/shared/${dashboardId}`;
  };

  const handlePublicSharingToggle = async (enabled: boolean) => {
    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/sharing/public`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ isPublic: enabled }),
      });

      if (response.ok) {
        setIsPublicSharing(enabled);
        toast({
          title: enabled ? "Public Sharing Enabled" : "Public Sharing Disabled",
          description: enabled 
            ? "Anyone with the link can view this dashboard."
            : "Dashboard is now private.",
        });
      } else {
        throw new Error('Failed to update sharing settings');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update public sharing settings.",
        variant: "destructive",
      });
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareLink);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
      
      toast({
        title: "Link Copied",
        description: "Share link has been copied to clipboard.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy link to clipboard.",
        variant: "destructive",
      });
    }
  };

  const handleAddUser = async () => {
    if (!newUserEmail.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/sharing/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          email: newUserEmail,
          role: newUserRole,
        }),
      });

      if (response.ok) {
        const newUser = await response.json();
        setSharedUsers(prev => [...prev, newUser]);
        setNewUserEmail('');
        
        toast({
          title: "User Added",
          description: `${newUserEmail} has been given ${newUserRole} access.`,
        });
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to add user');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add user.",
        variant: "destructive",
      });
    }
  };

  const handleUpdateUserRole = async (userId: string, newRole: string) => {
    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/sharing/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ role: newRole }),
      });

      if (response.ok) {
        setSharedUsers(prev => prev.map(user => 
          user.id === userId ? { ...user, role: newRole as any } : user
        ));
        
        toast({
          title: "Permission Updated",
          description: "User permissions have been updated.",
        });
      } else {
        throw new Error('Failed to update user role');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user permissions.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveUser = async (userId: string) => {
    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/sharing/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        setSharedUsers(prev => prev.filter(user => user.id !== userId));
        
        toast({
          title: "User Removed",
          description: "User access has been revoked.",
        });
      } else {
        throw new Error('Failed to remove user');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove user access.",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return Settings;
      case 'editor': return Edit;
      case 'viewer': return Eye;
      default: return Eye;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'editor': return 'bg-blue-100 text-blue-800';
      case 'viewer': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Share className="h-5 w-5" />
            <span>Share Dashboard</span>
          </DialogTitle>
          <DialogDescription>
            Share "{dashboardName}" with others or make it publicly accessible.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            {/* Public Sharing */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <Globe className="h-5 w-5" />
                  <span>Public Access</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Make dashboard public</p>
                    <p className="text-sm text-muted-foreground">
                      Anyone with the link can view this dashboard
                    </p>
                  </div>
                  <Switch
                    checked={isPublicSharing}
                    onCheckedChange={handlePublicSharingToggle}
                  />
                </div>

                {isPublicSharing && (
                  <div className="space-y-3">
                    <Label>Share Link</Label>
                    <div className="flex space-x-2">
                      <Input
                        value={shareLink}
                        readOnly
                        className="flex-1"
                      />
                      <Button
                        variant="outline"
                        onClick={handleCopyLink}
                        className="flex items-center space-x-2"
                      >
                        {linkCopied ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                        <span>{linkCopied ? 'Copied' : 'Copy'}</span>
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => window.open(shareLink, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Users className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-2xl font-bold">{sharedUsers.length}</p>
                      <p className="text-sm text-muted-foreground">Shared Users</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    {isPublicSharing ? (
                      <Globe className="h-5 w-5 text-green-600" />
                    ) : (
                      <Lock className="h-5 w-5 text-muted-foreground" />
                    )}
                    <div>
                      <p className="text-sm font-medium">
                        {isPublicSharing ? 'Public' : 'Private'}
                      </p>
                      <p className="text-sm text-muted-foreground">Visibility</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="users" className="space-y-4">
            {/* Add User */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Add User</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex space-x-2">
                  <div className="flex-1">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={newUserEmail}
                      onChange={(e) => setNewUserEmail(e.target.value)}
                    />
                  </div>
                  <div className="w-32">
                    <Label>Role</Label>
                    <Select value={newUserRole} onValueChange={(value: any) => setNewUserRole(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {PERMISSION_LEVELS.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button onClick={handleAddUser}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* User List */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Shared Users ({sharedUsers.length})</CardTitle>
              </CardHeader>
              <CardContent>
                {sharedUsers.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No users have access to this dashboard</p>
                    <p className="text-sm">Add users above to start collaborating</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {sharedUsers.map((user) => {
                      const RoleIcon = getRoleIcon(user.role);
                      
                      return (
                        <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium">
                                {user.name?.charAt(0) || user.email.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium">{user.name || user.email}</p>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                              <p className="text-xs text-muted-foreground">
                                Added {formatDate(user.addedAt)}
                                {user.lastAccessed && ` • Last accessed ${formatDate(user.lastAccessed)}`}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className={getRoleColor(user.role)}>
                              <RoleIcon className="h-3 w-3 mr-1" />
                              {user.role}
                            </Badge>
                            <Select
                              value={user.role}
                              onValueChange={(value) => handleUpdateUserRole(user.id, value)}
                            >
                              <SelectTrigger className="w-24 h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {PERMISSION_LEVELS.map((level) => (
                                  <SelectItem key={level.value} value={level.value}>
                                    {level.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveUser(user.id)}
                              className="h-8 w-8 p-0 text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Advanced Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Allow comments</p>
                      <p className="text-sm text-muted-foreground">
                        Users can add comments to dashboard widgets
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Allow downloads</p>
                      <p className="text-sm text-muted-foreground">
                        Users can download dashboard data and images
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Require authentication</p>
                      <p className="text-sm text-muted-foreground">
                        Users must sign in to view the dashboard
                      </p>
                    </div>
                    <Switch defaultChecked={!isPublicSharing} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
