#!/usr/bin/env python3
"""
Widget Management Test Script

This script tests the widget management functionality in dashboard settings,
including widget creation, editing, deletion, and data source integration.
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any, List

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy.orm import Session
from app.database import get_db, User, DataSource
from app.services.dashboard_service import DatageniusDashboardService
from app.models.dashboard_customization import (
    DashboardCreate, SectionCreate, WidgetCreate, WidgetUpdate,
    Dashboard, DashboardSection, DashboardWidget, WidgetDataSourceConfig
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WidgetManagementTestSuite:
    """Test suite for widget management functionality."""
    
    def __init__(self):
        self.db: Session = next(get_db())
        self.service = DatageniusDashboardService(self.db)
        self.test_user_id = 1
        self.test_dashboard_id = None
        self.test_section_id = None
        self.test_widget_ids = []
        self.test_data_source_ids = []
        
    async def run_all_tests(self):
        """Run all widget management tests."""
        logger.info("🚀 Starting Widget Management Test Suite")
        
        try:
            # Setup test environment
            await self.setup_test_environment()
            
            # Test widget CRUD operations
            await self.test_widget_crud()
            
            # Test widget data source integration
            await self.test_widget_data_source_integration()
            
            # Test widget positioning and layout
            await self.test_widget_positioning()
            
            # Test widget validation
            await self.test_widget_validation()
            
            logger.info("✅ All widget management tests completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Widget management test suite failed: {e}")
            raise
        finally:
            await self.cleanup()
    
    async def setup_test_environment(self):
        """Set up test dashboard, section, and data sources."""
        logger.info("Setting up test environment...")
        
        # Create test dashboard
        dashboard_data = DashboardCreate(
            name="Widget Management Test Dashboard",
            description="Dashboard for testing widget management",
            is_default=False,
            is_public=False,
            layout_config={"grid_size": 12, "row_height": 100},
            theme_config={"primary_color": "#3B82F6"},
            refresh_interval=300
        )
        
        dashboard = await self.service.create_dashboard(self.test_user_id, dashboard_data)
        self.test_dashboard_id = dashboard.id
        logger.info(f"✓ Created test dashboard: {dashboard.id}")
        
        # Create test section
        section_data = SectionCreate(
            dashboard_id=dashboard.id,
            name="Test Section",
            description="Section for widget testing",
            color="#3B82F6",
            icon="BarChart3",
            layout_config={"columns": 12, "rows": 6}
        )
        
        section = await self.service.create_section(self.test_user_id, section_data)
        self.test_section_id = section.id
        logger.info(f"✓ Created test section: {section.id}")
        
        # Create test data sources
        test_data_sources = [
            {
                "name": "Widget Test File Data Source",
                "type": "file",
                "description": "Test file data source for widgets",
                "is_active": True,
                "metadata": {"file_type": "csv", "columns": ["name", "value", "date"]},
                "source_metadata": {"file_id": "widget-test-file-1"}
            },
            {
                "name": "Widget Test API Data Source",
                "type": "api",
                "description": "Test API data source for widgets",
                "is_active": True,
                "metadata": {"endpoint": "https://api.example.com/data", "format": "json"},
                "source_metadata": {"api_key": "test-key"}
            }
        ]
        
        for ds_data in test_data_sources:
            data_source = DataSource(
                id=f"widget-test-ds-{len(self.test_data_source_ids) + 1}",
                user_id=self.test_user_id,
                **ds_data
            )
            self.db.add(data_source)
            self.test_data_source_ids.append(data_source.id)
        
        self.db.commit()
        logger.info(f"✓ Created {len(self.test_data_source_ids)} test data sources")
        
        # Assign data sources to dashboard
        result = await self.service.bulk_assign_data_sources(
            self.test_dashboard_id,
            self.test_data_source_ids,
            self.test_user_id
        )
        logger.info(f"✓ Assigned {result['assigned']} data sources to dashboard")
    
    async def test_widget_crud(self):
        """Test widget CRUD operations."""
        logger.info("Testing widget CRUD operations...")
        
        # Create widget
        widget_data = WidgetCreate(
            section_id=self.test_section_id,
            title="Test KPI Widget",
            widget_type="kpi",
            data_config={
                "dashboard_data_source_id": self.test_data_source_ids[0] if self.test_data_source_ids else None,
                "query": "SELECT COUNT(*) as total FROM data",
                "aggregation": "sum"
            },
            visualization_config={
                "format": "number",
                "prefix": "$",
                "suffix": "",
                "decimal_places": 0
            },
            position_config={"x": 0, "y": 0, "w": 3, "h": 2},
            customization={
                "background_color": "#ffffff",
                "text_color": "#000000",
                "border_radius": 8
            },
            refresh_interval=300
        )
        
        widget = await self.service.create_widget(self.test_user_id, widget_data)
        self.test_widget_ids.append(widget.id)
        logger.info(f"✓ Created widget: {widget.title} (ID: {widget.id})")
        
        # Read widget
        retrieved_widget = await self.service.get_widget(widget.id, self.test_user_id)
        assert retrieved_widget is not None, "Widget should be retrievable"
        assert retrieved_widget.title == "Test KPI Widget", "Widget title should match"
        logger.info(f"✓ Retrieved widget: {retrieved_widget.title}")
        
        # Update widget
        update_data = WidgetUpdate(
            title="Updated Test KPI Widget",
            visualization_config={
                "format": "currency",
                "prefix": "$",
                "suffix": " USD",
                "decimal_places": 2
            }
        )
        
        updated_widget = await self.service.update_widget(widget.id, self.test_user_id, update_data)
        assert updated_widget.title == "Updated Test KPI Widget", "Widget title should be updated"
        logger.info(f"✓ Updated widget: {updated_widget.title}")
        
        logger.info("✓ Widget CRUD operations successful")
    
    async def test_widget_data_source_integration(self):
        """Test widget integration with data sources."""
        logger.info("Testing widget data source integration...")
        
        if not self.test_data_source_ids:
            logger.warning("No test data sources available, skipping data source integration test")
            return
        
        # Create widget with data source configuration
        widget_data = WidgetCreate(
            section_id=self.test_section_id,
            title="Data Source Chart Widget",
            widget_type="chart",
            data_config={
                "dashboard_data_source_id": self.test_data_source_ids[0],
                "query": "SELECT date, value FROM data ORDER BY date",
                "filters": {"status": "active"},
                "group_by": ["date"],
                "sort_by": "date",
                "limit": 100
            },
            visualization_config={
                "chart_type": "line",
                "x_axis": "date",
                "y_axis": "value",
                "color_scheme": "blue"
            },
            position_config={"x": 3, "y": 0, "w": 6, "h": 4},
            refresh_interval=300
        )
        
        widget = await self.service.create_widget(self.test_user_id, widget_data)
        self.test_widget_ids.append(widget.id)
        logger.info(f"✓ Created widget with data source: {widget.title}")
        
        # Verify data source configuration
        assert widget.data_config is not None, "Widget should have data configuration"
        assert "dashboard_data_source_id" in widget.data_config, "Widget should reference data source"
        logger.info("✓ Widget data source configuration verified")
        
        logger.info("✓ Widget data source integration successful")
    
    async def test_widget_positioning(self):
        """Test widget positioning and layout management."""
        logger.info("Testing widget positioning...")
        
        if not self.test_widget_ids:
            logger.warning("No test widgets available, skipping positioning test")
            return
        
        # Test widget move operation
        widget_id = self.test_widget_ids[0]
        new_position = {"x": 6, "y": 2, "w": 4, "h": 3}
        
        moved_widget = await self.service.move_widget(
            widget_id,
            self.test_section_id,
            new_position,
            self.test_user_id
        )
        
        assert moved_widget.position_config["x"] == 6, "Widget X position should be updated"
        assert moved_widget.position_config["y"] == 2, "Widget Y position should be updated"
        logger.info(f"✓ Moved widget to position: {new_position}")
        
        logger.info("✓ Widget positioning tests successful")
    
    async def test_widget_validation(self):
        """Test widget validation and error handling."""
        logger.info("Testing widget validation...")
        
        # Test invalid section ID
        try:
            invalid_widget = WidgetCreate(
                section_id="invalid-section-id",
                title="Invalid Widget",
                widget_type="kpi",
                position_config={"x": 0, "y": 0, "w": 3, "h": 2}
            )
            await self.service.create_widget(self.test_user_id, invalid_widget)
            assert False, "Should raise error for invalid section ID"
        except ValueError:
            logger.info("✓ Correctly handled invalid section ID")
        
        logger.info("✓ Widget validation tests successful")
    
    async def cleanup(self):
        """Clean up test data."""
        logger.info("Cleaning up test data...")
        
        try:
            # Delete test widgets
            for widget_id in self.test_widget_ids:
                await self.service.delete_widget(widget_id, self.test_user_id)
            logger.info(f"✓ Deleted {len(self.test_widget_ids)} test widgets")
            
            # Delete test section
            if self.test_section_id:
                await self.service.delete_section(self.test_section_id, self.test_user_id)
                logger.info(f"✓ Deleted test section: {self.test_section_id}")
            
            # Delete test dashboard
            if self.test_dashboard_id:
                await self.service.delete_dashboard(self.test_dashboard_id, self.test_user_id)
                logger.info(f"✓ Deleted test dashboard: {self.test_dashboard_id}")
            
            # Delete test data sources
            for ds_id in self.test_data_source_ids:
                data_source = self.db.query(DataSource).filter(DataSource.id == ds_id).first()
                if data_source:
                    self.db.delete(data_source)
            
            self.db.commit()
            logger.info(f"✓ Deleted {len(self.test_data_source_ids)} test data sources")
            
        except Exception as e:
            logger.warning(f"Cleanup warning: {e}")
        finally:
            self.db.close()

async def main():
    """Main test runner."""
    test_suite = WidgetManagementTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
