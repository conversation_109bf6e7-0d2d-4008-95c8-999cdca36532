"""
Data retriever component for the Datagenius agent system.

This module provides a component for retrieving data from various sources.
"""

import logging
import os
import json
import pandas as pd
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent

logger = logging.getLogger(__name__)


class DataRetrieverComponent(AgentComponent):
    """Component for retrieving data from various sources."""

    def __init__(self):
        """Initialize the data retriever component."""
        super().__init__()
        self.data_sources = {}
        self.default_data_dir = "data"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        # Set data directory
        self.default_data_dir = config.get("data_dir", self.default_data_dir)

        # Register data sources from configuration
        if "data_sources" in config:
            for source_config in config["data_sources"]:
                source_id = source_config.get("id")
                source_path = source_config.get("path")
                source_type = source_config.get("type", "csv")

                if not source_id or not source_path:
                    logger.warning("Data source configuration missing 'id' or 'path'")
                    continue

                # Register the data source
                self.data_sources[source_id] = {
                    "path": source_path,
                    "type": source_type,
                    "description": source_config.get("description", ""),
                    "metadata": source_config.get("metadata", {})
                }
                logger.info(f"Registered data source '{source_id}' of type '{source_type}' at '{source_path}'")

        logger.info(f"Initialized data retriever with {len(self.data_sources)} data sources")

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        user_message_lower_strip = (context.message or "").lower().strip()

        # Check if we need to list available data sources
        if user_message_lower_strip in ["list data", "what data do you have", "show data sources"]:
            if not self.data_sources:
                context.response = "No data sources are currently available."
                return context

            source_descriptions = []
            for source_id, source_info in self.data_sources.items():
                desc = source_info.get("description", "No description")
                source_descriptions.append(f"- {source_id}: {desc}")

            context.response = "Available data sources:\n" + "\n".join(source_descriptions)
            context.metadata["listed_data_sources"] = True
            return context

        # Check if we need to retrieve data
        retrieve_data_params = context.metadata.get("retrieve_data")
        if retrieve_data_params:
            source_id = retrieve_data_params.get("source_id")
            if not source_id or source_id not in self.data_sources:
                context.response = f"Data source '{source_id}' not found."
                context.add_error(self.name, "data_source_not_found", {"source_id": source_id})
                return context

            # Get the data source information
            source_info = self.data_sources[source_id]
            source_path = source_info["path"]
            source_type = source_info["type"]

            # Check if the path is relative and prepend the data directory
            if not os.path.isabs(source_path):
                source_path = os.path.join(self.default_data_dir, source_path)

            # Check if the file exists
            if not os.path.exists(source_path):
                context.response = f"Data file for source '{source_id}' not found at '{source_path}'."
                context.add_error(self.name, "data_file_not_found", {"source_id": source_id, "path": source_path})
                return context

            try:
                # Load the data based on the source type
                data_content: Any = None # To store loaded data (DataFrame or JSON)
                if source_type.lower() == "csv":
                    data_content = pd.read_csv(source_path)
                elif source_type.lower() in ["excel", "xls", "xlsx"]:
                    data_content = pd.read_excel(source_path)
                elif source_type.lower() == "json":
                    with open(source_path, "r") as f:
                        data_content = json.load(f)
                else:
                    context.response = f"Unsupported data source type: {source_type}"
                    context.add_error(self.name, "unsupported_data_source_type", {"source_type": source_type})
                    return context

                # Process the data based on the request
                operation = retrieve_data_params.get("operation", "head")
                result_payload: Any = None # To store the processed result for the response

                component_data_store = context.component_data.setdefault(self.name, {})
                component_data_store["raw_data"] = data_content # Store raw loaded data

                if isinstance(data_content, pd.DataFrame):
                    if operation == "head":
                        n = retrieve_data_params.get("n", 5)
                        result_payload = data_content.head(n).to_dict(orient="records")
                    elif operation == "tail":
                        n = retrieve_data_params.get("n", 5)
                        result_payload = data_content.tail(n).to_dict(orient="records")
                    elif operation == "sample":
                        n = retrieve_data_params.get("n", 5)
                        result_payload = data_content.sample(n=min(n, len(data_content))).to_dict(orient="records")
                    elif operation == "describe":
                        result_payload = data_content.describe().to_dict()
                    elif operation == "info":
                        result_payload = {
                            "columns": list(data_content.columns),
                            "shape": data_content.shape,
                            "dtypes": {col: str(dtype) for col, dtype in data_content.dtypes.items()}
                        }
                    else:
                        context.response = f"Unsupported operation: {operation}"
                        context.add_error(self.name, "unsupported_operation", {"operation": operation})
                        return context
                else: # JSON data
                    result_payload = data_content

                # Store the result in component_data
                component_data_store["data_result"] = result_payload
                context.metadata["data_source_retrieved"] = source_id # Changed key for clarity
                context.metadata["data_operation_performed"] = operation # Changed key for clarity

                # Generate a response message
                if isinstance(data_content, pd.DataFrame):
                    if operation == "head":
                        context.response = f"Here are the first {len(result_payload)} rows from '{source_id}'."
                    elif operation == "tail":
                        context.response = f"Here are the last {len(result_payload)} rows from '{source_id}'."
                    elif operation == "sample":
                        context.response = f"Here is a sample of {len(result_payload)} rows from '{source_id}'."
                    elif operation == "describe":
                        context.response = f"Here is a statistical summary of '{source_id}'."
                    elif operation == "info":
                        context.response = f"Here is information about the structure of '{source_id}'."
                else: # JSON data
                    context.response = f"Successfully retrieved data from '{source_id}'."

                return context

            except Exception as e:
                logger.error(f"Error retrieving data from '{source_id}': {e}", exc_info=True)
                context.response = f"Error retrieving data from '{source_id}': {str(e)}"
                context.add_error(self.name, "data_retrieval_error", {"source_id": source_id, "error_message": str(e)})
                return context

        # If no specific data retrieval was requested, just pass through
        return context

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        return ["data_retrieval"] + [f"data_source:{source_id}" for source_id in self.data_sources.keys()]
