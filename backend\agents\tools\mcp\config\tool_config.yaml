# MCP Tools Configuration
# This file contains global configuration settings for all MCP tools

# Global Settings
global:
  # Default AI provider settings
  default_provider: "groq"
  default_model: "llama-3.1-8b-instant"
  
  # Timeout settings
  default_timeout: 30
  max_timeout: 120
  
  # Retry settings
  default_retries: 3
  max_retries: 5
  
  # Caching settings
  enable_caching: true
  default_cache_ttl: 300
  max_cache_ttl: 3600
  
  # Validation settings
  enable_input_validation: true
  enable_input_sanitization: true
  
  # Performance settings
  enable_performance_monitoring: true
  enable_usage_analytics: true
  
  # Security settings
  enable_rate_limiting: true
  enable_access_control: true
  enable_audit_logging: true

# Agent-specific configurations
agents:
  analyst:
    preferred_providers: ["groq", "openai"]
    preferred_models: ["llama-3.1-70b-versatile", "gpt-4"]
    timeout_multiplier: 1.5
    cache_ttl_multiplier: 1.2
    analysis_depth: "advanced"
    explanation_level: "detailed"
    
  marketer:
    preferred_providers: ["openai", "groq"]
    preferred_models: ["gpt-4", "llama-3.1-8b-instant"]
    timeout_multiplier: 1.0
    cache_ttl_multiplier: 1.0
    analysis_depth: "moderate"
    explanation_level: "business_focused"
    
  classifier:
    preferred_providers: ["groq", "openai"]
    preferred_models: ["llama-3.1-8b-instant", "gpt-3.5-turbo"]
    timeout_multiplier: 0.8
    cache_ttl_multiplier: 1.5
    analysis_depth: "moderate"
    explanation_level: "structured"
    
  concierge:
    preferred_providers: ["groq", "openai"]
    preferred_models: ["llama-3.1-8b-instant", "gpt-3.5-turbo"]
    timeout_multiplier: 0.7
    cache_ttl_multiplier: 0.8
    analysis_depth: "basic"
    explanation_level: "accessible"

# Tool-specific configurations
tools:
  intent_detection:
    enable_agent_awareness: true
    enable_llm_fallback: true
    simple_case_threshold: 50
    high_confidence_threshold: 0.8
    medium_confidence_threshold: 0.6
    supported_intents:
      - "greeting"
      - "question"
      - "request"
      - "content_generation"
      - "marketing_advice"
      - "data_analysis"
      - "classification"
      - "follow_up"
      - "conversational"
    
  intent_analysis:
    enable_agent_awareness: true
    enable_detailed_analysis: true
    confidence_threshold: 0.7
    max_analysis_depth: 5
    include_recommendations: true
    
  language_detection:
    enable_agent_awareness: true
    enable_cultural_context: true
    confidence_threshold: 0.8
    supported_languages:
      - "english"
      - "spanish"
      - "french"
      - "german"
      - "italian"
      - "portuguese"
      - "chinese"
      - "japanese"
    
  pandasai_analysis:
    enable_agent_awareness: true
    enable_style_enhancement: true
    enable_caching: true
    cache_ttl: 600
    max_retries: 3
    timeout: 60
    
  pandasai_query:
    enable_agent_awareness: true
    enable_style_enhancement: true
    enable_caching: true
    cache_ttl: 300
    max_retries: 2
    timeout: 45
    
  data_visualization:
    enable_agent_awareness: true
    enable_style_customization: true
    default_chart_style: "clean"
    default_color_scheme: "professional"
    enable_optimization: true
    max_chart_size: "1920x1080"
    
  data_storytelling:
    enable_agent_awareness: true
    enable_narrative_customization: true
    default_narrative_style: "informative"
    default_tone: "professional"
    max_story_length: 2000
    
  text_processing:
    enable_agent_awareness: true
    enable_style_adaptation: true
    max_text_length: 50000
    enable_summarization: true
    enable_keyword_extraction: true
    
  data_access:
    enable_agent_awareness: true
    enable_multi_modal: true
    supported_formats:
      - "csv"
      - "xlsx"
      - "json"
      - "parquet"
      - "pdf"
      - "docx"
    max_file_size: "100MB"
    enable_streaming: true
    
  statistical_analysis:
    enable_agent_awareness: true
    enable_advanced_stats: true
    confidence_level: 0.95
    enable_ml_integration: true
    max_features: 1000

# Validation rules
validation:
  required_fields:
    - "file_path"
    - "query"
  
  field_types:
    file_path: "string"
    query: "string"
    api_key: "string"
    provider: "string"
    model: "string"
    
  field_constraints:
    query:
      min_length: 1
      max_length: 10000
    file_path:
      pattern: "^[a-zA-Z0-9_/.-]+$"
    provider:
      allowed_values: ["openai", "groq", "google", "ollama"]

# Error handling configuration
error_handling:
  enable_graceful_degradation: true
  enable_retry_logic: true
  enable_fallback_providers: true
  
  retry_strategies:
    exponential_backoff:
      base_delay: 1
      max_delay: 60
      multiplier: 2
    
  fallback_chains:
    primary: ["groq", "openai"]
    secondary: ["google", "ollama"]
    
  error_categories:
    transient:
      - "timeout"
      - "rate_limit"
      - "network_error"
    permanent:
      - "invalid_api_key"
      - "unsupported_model"
      - "malformed_request"

# Performance monitoring
monitoring:
  enable_metrics_collection: true
  enable_performance_tracking: true
  enable_usage_analytics: true
  
  metrics:
    - "execution_time"
    - "success_rate"
    - "error_rate"
    - "cache_hit_rate"
    - "resource_usage"
    
  thresholds:
    max_execution_time: 300
    min_success_rate: 0.95
    max_error_rate: 0.05

# Security configuration
security:
  enable_input_sanitization: true
  enable_output_filtering: true
  enable_rate_limiting: true
  
  rate_limits:
    per_user_per_minute: 60
    per_user_per_hour: 1000
    per_tool_per_minute: 100
    
  sanitization_rules:
    remove_sql_injection: true
    remove_script_tags: true
    validate_file_paths: true
    
  access_control:
    require_authentication: true
    require_authorization: true
    enable_role_based_access: true

# Logging configuration
logging:
  level: "INFO"
  enable_structured_logging: true
  enable_audit_trail: true
  
  log_categories:
    - "tool_execution"
    - "agent_detection"
    - "performance_metrics"
    - "security_events"
    - "error_handling"
    
  retention_policy:
    debug_logs: "7d"
    info_logs: "30d"
    error_logs: "90d"
    audit_logs: "365d"
