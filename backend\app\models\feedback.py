from sqlalchemy import Column, String, Integer, Text, JSON, Boolean, DateTime, ForeignKey
from datetime import datetime, timezone
from typing import Optional, Dict, List, Any
from pydantic import BaseModel

# Import Base from the main database module
from ..database import Base  # Corrected import
from ..utils.db_utils import get_utc_now # Corrected import

class UserFeedback(Base):
    __tablename__ = "user_feedback"
    id = Column(String(36), primary_key=True, index=True) # Assuming UUIDs as strings
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    conversation_id = Column(String(36), ForeignKey("conversations.id"), nullable=True, index=True)
    message_id = Column(String(36), ForeignKey("messages.id"), nullable=True, index=True)
    persona_id = Column(String(50), ForeignKey("personas.id"), nullable=True, index=True)

    feedback_type = Column(String(50), nullable=False)  # "message_rating", "conversation_rating", "persona_review", "feature_feedback"
    rating = Column(Integer, nullable=True)  # 1-5 stars for ratings
    sentiment = Column(String(20), nullable=True)  # "positive", "negative", "neutral"
    feedback_text = Column(Text, nullable=True)
    feedback_tags = Column(JSON, nullable=True)  # List of strings e.g. ["helpful", "accurate"]
    is_anonymous = Column(Boolean, default=False)

    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships (optional, but good for ORM use)
    # user = relationship("User")
    # conversation = relationship("Conversation")
    # message = relationship("Message")
    # persona = relationship("Persona")

class FeedbackSurvey(Base):
    __tablename__ = "feedback_surveys"
    id = Column(String(36), primary_key=True, index=True) # Assuming UUIDs as strings
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    survey_type = Column(String(50), nullable=False)  # "satisfaction", "feature_request", "bug_report", "nps"
    questions = Column(JSON, nullable=False)  # Survey questions configuration
    target_audience = Column(JSON, nullable=True)  # User criteria for survey targeting
    is_active = Column(Boolean, default=True)

    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    expires_at = Column(DateTime(timezone=True), nullable=True)

class SurveyResponse(Base):
    __tablename__ = "survey_responses"
    id = Column(String(36), primary_key=True, index=True) # Assuming UUIDs as strings
    survey_id = Column(String(36), ForeignKey("feedback_surveys.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)

    responses = Column(JSON, nullable=False)  # User's answers to survey questions
    completion_time_seconds = Column(Integer, nullable=True)
    is_complete = Column(Boolean, default=False)

    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    # survey = relationship("FeedbackSurvey")
    # user = relationship("User")

# Pydantic models for request/response, distinct from SQLAlchemy ORM models

class UserFeedbackResponse(BaseModel):
    """Pydantic model for UserFeedback responses."""
    id: str
    user_id: int
    conversation_id: Optional[str] = None
    message_id: Optional[str] = None
    persona_id: Optional[str] = None
    feedback_type: str
    rating: Optional[int] = None
    sentiment: Optional[str] = None
    feedback_text: Optional[str] = None
    feedback_tags: Optional[List[str]] = None
    is_anonymous: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class FeedbackSurveyResponse(BaseModel):
    """Pydantic model for FeedbackSurvey responses."""
    id: str
    title: str
    description: Optional[str] = None
    survey_type: str
    questions: List[Dict[str, Any]]
    target_audience: Optional[Dict[str, Any]] = None
    is_active: bool
    created_at: datetime
    expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class SurveyResponseResponse(BaseModel):
    """Pydantic model for SurveyResponse responses."""
    id: str
    survey_id: str
    user_id: int
    responses: Dict[str, Any]
    completion_time_seconds: Optional[int] = None
    is_complete: bool
    created_at: datetime
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class FeedbackAnalytics(BaseModel): # Pydantic model for service return type
    average_rating: Optional[float] = None
    sentiment_distribution: Optional[Dict[str, int]] = None
    common_issues: Optional[List[str]] = None
    improvement_suggestions: Optional[List[str]] = None
    nps_score: Optional[float] = None

class NPSAnalytics(BaseModel): # Pydantic model
    nps_score: float
    promoters: int
    passives: int
    detractors: int
    total_responses: int

class FeedbackAlert(BaseModel): # Pydantic model
    feedback_id: str
    user_id: str
    alert_type: str # e.g., "very_low_rating", "negative_sentiment_urgent"
    message_preview: str
    created_at: datetime
