"""
Performance Monitoring System for MCP Tools.

This module provides comprehensive performance monitoring, metrics collection,
and optimization tracking for all MCP tools including execution time,
success rates, and resource usage analytics.
"""

import logging
import time
import asyncio
import psutil
import threading
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
from contextlib import asynccontextmanager
import json
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics for a tool execution."""
    
    tool_name: str
    agent_identity: Optional[str]
    execution_time: float
    success: bool
    error_message: Optional[str] = None
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    input_size_bytes: int = 0
    output_size_bytes: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AggregatedMetrics:
    """Aggregated performance metrics for analysis."""
    
    tool_name: str
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    success_rate: float = 0.0
    avg_execution_time: float = 0.0
    min_execution_time: float = float('inf')
    max_execution_time: float = 0.0
    avg_memory_usage: float = 0.0
    avg_cpu_usage: float = 0.0
    total_input_bytes: int = 0
    total_output_bytes: int = 0
    last_updated: datetime = field(default_factory=datetime.now)


class MetricsCollector:
    """Collects and stores performance metrics."""
    
    def __init__(self, max_metrics_history: int = 10000):
        """Initialize the metrics collector."""
        self.max_metrics_history = max_metrics_history
        self.metrics_history: deque = deque(maxlen=max_metrics_history)
        self.aggregated_metrics: Dict[str, AggregatedMetrics] = {}
        self.lock = threading.RLock()
        
    def add_metric(self, metric: PerformanceMetrics):
        """Add a performance metric."""
        with self.lock:
            self.metrics_history.append(metric)
            self._update_aggregated_metrics(metric)
    
    def _update_aggregated_metrics(self, metric: PerformanceMetrics):
        """Update aggregated metrics with new metric."""
        tool_name = metric.tool_name
        
        if tool_name not in self.aggregated_metrics:
            self.aggregated_metrics[tool_name] = AggregatedMetrics(tool_name=tool_name)
        
        agg = self.aggregated_metrics[tool_name]
        
        # Update counts
        agg.total_executions += 1
        if metric.success:
            agg.successful_executions += 1
        else:
            agg.failed_executions += 1
        
        # Update success rate
        agg.success_rate = agg.successful_executions / agg.total_executions
        
        # Update execution time metrics
        total_time = agg.avg_execution_time * (agg.total_executions - 1) + metric.execution_time
        agg.avg_execution_time = total_time / agg.total_executions
        agg.min_execution_time = min(agg.min_execution_time, metric.execution_time)
        agg.max_execution_time = max(agg.max_execution_time, metric.execution_time)
        
        # Update resource usage metrics
        total_memory = agg.avg_memory_usage * (agg.total_executions - 1) + metric.memory_usage_mb
        agg.avg_memory_usage = total_memory / agg.total_executions
        
        total_cpu = agg.avg_cpu_usage * (agg.total_executions - 1) + metric.cpu_usage_percent
        agg.avg_cpu_usage = total_cpu / agg.total_executions
        
        # Update data size metrics
        agg.total_input_bytes += metric.input_size_bytes
        agg.total_output_bytes += metric.output_size_bytes
        
        agg.last_updated = datetime.now()
    
    def get_metrics_for_tool(self, tool_name: str, limit: Optional[int] = None) -> List[PerformanceMetrics]:
        """Get metrics for a specific tool."""
        with self.lock:
            tool_metrics = [m for m in self.metrics_history if m.tool_name == tool_name]
            if limit:
                tool_metrics = tool_metrics[-limit:]
            return tool_metrics
    
    def get_aggregated_metrics(self, tool_name: Optional[str] = None) -> Dict[str, AggregatedMetrics]:
        """Get aggregated metrics."""
        with self.lock:
            if tool_name:
                return {tool_name: self.aggregated_metrics.get(tool_name)}
            return self.aggregated_metrics.copy()
    
    def get_recent_metrics(self, minutes: int = 60) -> List[PerformanceMetrics]:
        """Get metrics from the last N minutes."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        with self.lock:
            return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def clear_metrics(self):
        """Clear all metrics."""
        with self.lock:
            self.metrics_history.clear()
            self.aggregated_metrics.clear()


class ResourceMonitor:
    """Monitors system resource usage during tool execution."""
    
    def __init__(self):
        """Initialize the resource monitor."""
        self.process = psutil.Process()
    
    def get_current_usage(self) -> Dict[str, float]:
        """Get current resource usage."""
        try:
            memory_info = self.process.memory_info()
            cpu_percent = self.process.cpu_percent()
            
            return {
                "memory_mb": memory_info.rss / 1024 / 1024,  # Convert to MB
                "cpu_percent": cpu_percent
            }
        except Exception as e:
            logger.warning(f"Failed to get resource usage: {e}")
            return {"memory_mb": 0.0, "cpu_percent": 0.0}


class PerformanceMonitor:
    """
    Comprehensive performance monitoring system for MCP tools.
    
    Provides execution time tracking, resource usage monitoring,
    success rate analysis, and performance optimization insights.
    """
    
    def __init__(self, enable_resource_monitoring: bool = True):
        """Initialize the performance monitor."""
        self.enable_resource_monitoring = enable_resource_monitoring
        self.metrics_collector = MetricsCollector()
        self.resource_monitor = ResourceMonitor() if enable_resource_monitoring else None
        self.active_executions: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.RLock()
        
    @asynccontextmanager
    async def monitor_execution(
        self,
        tool_name: str,
        agent_identity: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        input_data: Optional[Any] = None
    ):
        """
        Context manager for monitoring tool execution.
        
        Args:
            tool_name: Name of the tool being executed
            agent_identity: Agent identity for context
            context: Additional context information
            input_data: Input data for size calculation
        """
        execution_id = f"{tool_name}_{int(time.time() * 1000000)}"
        start_time = time.time()
        start_resources = self.resource_monitor.get_current_usage() if self.resource_monitor else {}
        
        # Calculate input size
        input_size = self._calculate_data_size(input_data) if input_data else 0
        
        # Track active execution
        with self.lock:
            self.active_executions[execution_id] = {
                "tool_name": tool_name,
                "agent_identity": agent_identity,
                "start_time": start_time,
                "start_resources": start_resources,
                "context": context or {}
            }
        
        success = False
        error_message = None
        output_data = None
        
        try:
            yield
            success = True
        except Exception as e:
            success = False
            error_message = str(e)
            raise
        finally:
            # Calculate execution metrics
            end_time = time.time()
            execution_time = end_time - start_time
            end_resources = self.resource_monitor.get_current_usage() if self.resource_monitor else {}
            
            # Calculate resource usage
            memory_usage = end_resources.get("memory_mb", 0) - start_resources.get("memory_mb", 0)
            cpu_usage = end_resources.get("cpu_percent", 0)
            
            # Calculate output size (if available)
            output_size = self._calculate_data_size(output_data) if output_data else 0
            
            # Create performance metric
            metric = PerformanceMetrics(
                tool_name=tool_name,
                agent_identity=agent_identity,
                execution_time=execution_time,
                success=success,
                error_message=error_message,
                memory_usage_mb=max(0, memory_usage),  # Ensure non-negative
                cpu_usage_percent=cpu_usage,
                input_size_bytes=input_size,
                output_size_bytes=output_size,
                context=context or {}
            )
            
            # Add metric to collector
            self.metrics_collector.add_metric(metric)
            
            # Remove from active executions
            with self.lock:
                self.active_executions.pop(execution_id, None)
            
            # Log performance info
            self._log_performance_metric(metric)
    
    def _calculate_data_size(self, data: Any) -> int:
        """Calculate the size of data in bytes."""
        try:
            if isinstance(data, str):
                return len(data.encode('utf-8'))
            elif isinstance(data, (dict, list)):
                return len(json.dumps(data, default=str).encode('utf-8'))
            elif hasattr(data, '__len__'):
                return len(str(data).encode('utf-8'))
            else:
                return len(str(data).encode('utf-8'))
        except Exception:
            return 0
    
    def _log_performance_metric(self, metric: PerformanceMetrics):
        """Log performance metric information."""
        status = "SUCCESS" if metric.success else "FAILED"
        log_message = (
            f"PERF [{metric.tool_name}] {status} - "
            f"Time: {metric.execution_time:.3f}s, "
            f"Memory: {metric.memory_usage_mb:.1f}MB, "
            f"CPU: {metric.cpu_usage_percent:.1f}%"
        )
        
        if metric.agent_identity:
            log_message += f", Agent: {metric.agent_identity}"
        
        if not metric.success and metric.error_message:
            log_message += f", Error: {metric.error_message}"
        
        logger.info(log_message)
    
    def get_performance_summary(self, tool_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a comprehensive performance summary.
        
        Args:
            tool_name: Optional tool name to filter by
            
        Returns:
            Dict: Performance summary
        """
        aggregated = self.metrics_collector.get_aggregated_metrics(tool_name)
        recent_metrics = self.metrics_collector.get_recent_metrics(60)  # Last hour
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "aggregated_metrics": {},
            "recent_performance": {},
            "active_executions": len(self.active_executions),
            "system_health": self._get_system_health()
        }
        
        # Process aggregated metrics
        for tool, metrics in aggregated.items():
            if metrics:
                summary["aggregated_metrics"][tool] = {
                    "total_executions": metrics.total_executions,
                    "success_rate": round(metrics.success_rate, 3),
                    "avg_execution_time": round(metrics.avg_execution_time, 3),
                    "min_execution_time": round(metrics.min_execution_time, 3),
                    "max_execution_time": round(metrics.max_execution_time, 3),
                    "avg_memory_usage": round(metrics.avg_memory_usage, 2),
                    "avg_cpu_usage": round(metrics.avg_cpu_usage, 2),
                    "total_data_processed": {
                        "input_mb": round(metrics.total_input_bytes / 1024 / 1024, 2),
                        "output_mb": round(metrics.total_output_bytes / 1024 / 1024, 2)
                    }
                }
        
        # Process recent performance
        if recent_metrics:
            tool_recent = defaultdict(list)
            for metric in recent_metrics:
                tool_recent[metric.tool_name].append(metric)
            
            for tool, metrics_list in tool_recent.items():
                successful = [m for m in metrics_list if m.success]
                failed = [m for m in metrics_list if not m.success]
                
                summary["recent_performance"][tool] = {
                    "executions_last_hour": len(metrics_list),
                    "success_rate": len(successful) / len(metrics_list) if metrics_list else 0,
                    "avg_execution_time": sum(m.execution_time for m in successful) / len(successful) if successful else 0,
                    "error_rate": len(failed) / len(metrics_list) if metrics_list else 0
                }
        
        return summary
    
    def _get_system_health(self) -> Dict[str, Any]:
        """Get current system health metrics."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "cpu_usage_percent": cpu_percent,
                "memory_usage_percent": memory.percent,
                "memory_available_gb": round(memory.available / 1024 / 1024 / 1024, 2),
                "disk_usage_percent": disk.percent,
                "disk_free_gb": round(disk.free / 1024 / 1024 / 1024, 2)
            }
        except Exception as e:
            logger.warning(f"Failed to get system health: {e}")
            return {"error": str(e)}
    
    def get_performance_insights(self, tool_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get performance insights and optimization recommendations.
        
        Args:
            tool_name: Optional tool name to analyze
            
        Returns:
            Dict: Performance insights and recommendations
        """
        aggregated = self.metrics_collector.get_aggregated_metrics(tool_name)
        insights = {
            "timestamp": datetime.now().isoformat(),
            "insights": [],
            "recommendations": [],
            "alerts": []
        }
        
        for tool, metrics in aggregated.items():
            if not metrics or metrics.total_executions < 10:
                continue  # Need sufficient data for insights
            
            tool_insights = []
            tool_recommendations = []
            tool_alerts = []
            
            # Performance insights
            if metrics.success_rate < 0.95:
                tool_alerts.append(f"Low success rate ({metrics.success_rate:.1%}) for {tool}")
                tool_recommendations.append(f"Investigate error patterns for {tool}")
            
            if metrics.avg_execution_time > 30:
                tool_insights.append(f"{tool} has high average execution time ({metrics.avg_execution_time:.1f}s)")
                tool_recommendations.append(f"Consider optimizing {tool} for better performance")
            
            if metrics.avg_memory_usage > 500:  # 500MB threshold
                tool_insights.append(f"{tool} uses significant memory ({metrics.avg_memory_usage:.1f}MB)")
                tool_recommendations.append(f"Review memory usage patterns for {tool}")
            
            if metrics.avg_cpu_usage > 80:  # 80% threshold
                tool_alerts.append(f"High CPU usage ({metrics.avg_cpu_usage:.1f}%) for {tool}")
                tool_recommendations.append(f"Optimize CPU-intensive operations in {tool}")
            
            # Add tool-specific insights
            insights["insights"].extend(tool_insights)
            insights["recommendations"].extend(tool_recommendations)
            insights["alerts"].extend(tool_alerts)
        
        return insights
    
    def export_metrics(self, file_path: str, tool_name: Optional[str] = None):
        """
        Export metrics to a file.
        
        Args:
            file_path: Path to export file
            tool_name: Optional tool name to filter by
        """
        try:
            metrics = self.metrics_collector.get_metrics_for_tool(tool_name) if tool_name else list(self.metrics_collector.metrics_history)
            
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "tool_filter": tool_name,
                "total_metrics": len(metrics),
                "metrics": [
                    {
                        "tool_name": m.tool_name,
                        "agent_identity": m.agent_identity,
                        "execution_time": m.execution_time,
                        "success": m.success,
                        "error_message": m.error_message,
                        "memory_usage_mb": m.memory_usage_mb,
                        "cpu_usage_percent": m.cpu_usage_percent,
                        "input_size_bytes": m.input_size_bytes,
                        "output_size_bytes": m.output_size_bytes,
                        "timestamp": m.timestamp.isoformat(),
                        "context": m.context
                    }
                    for m in metrics
                ]
            }
            
            with open(file_path, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            logger.info(f"Exported {len(metrics)} metrics to {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to export metrics: {e}")
            raise
    
    def clear_metrics(self):
        """Clear all performance metrics."""
        self.metrics_collector.clear_metrics()
        logger.info("Performance metrics cleared")


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    return performance_monitor


def monitor_performance(tool_name: str, agent_identity: Optional[str] = None):
    """
    Decorator for monitoring tool performance.
    
    Args:
        tool_name: Name of the tool
        agent_identity: Agent identity for context
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            async with performance_monitor.monitor_execution(tool_name, agent_identity):
                return await func(*args, **kwargs)
        return wrapper
    return decorator
