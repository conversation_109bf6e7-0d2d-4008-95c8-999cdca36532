/**
 * React Query hooks for notifications functionality
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useEffect, useCallback } from 'react';
import { 
  notificationsApi, 
  Notification, 
  NotificationPrefs 
} from '@/lib/api';
import { useToast } from './use-toast';
import { useAuth } from '@/contexts/AuthContext';

// Query keys
export const notificationKeys = {
  all: ['notifications'] as const,
  list: (limit?: number, offset?: number) => [...notificationKeys.all, 'list', limit, offset] as const,
  preferences: () => [...notificationKeys.all, 'preferences'] as const,
};

/**
 * Hook to fetch user notifications
 */
export function useNotifications(limit: number = 20, offset: number = 0) {
  return useQuery({
    queryKey: notificationKeys.list(limit, offset),
    queryFn: () => notificationsApi.getNotifications(limit, offset),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
    retry: 3,
  });
}

/**
 * Hook to mark notification as read
 */
export function useMarkAsRead() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (notificationId: string) => notificationsApi.markAsRead(notificationId),
    onSuccess: () => {
      // Invalidate notifications list to update read status
      queryClient.invalidateQueries({ queryKey: notificationKeys.all });
    },
    onError: (error) => {
      toast({
        title: "Failed to mark as read",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to mark all notifications as read
 */
export function useMarkAllAsRead() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: () => notificationsApi.markAllAsRead(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: notificationKeys.all });
      toast({
        title: "All notifications marked as read",
        description: "Your notification list has been cleared.",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to mark all as read",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to fetch and update notification preferences
 */
export function useNotificationPreferences() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const query = useQuery({
    queryKey: notificationKeys.preferences(),
    queryFn: () => notificationsApi.getPreferences(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });

  const updateMutation = useMutation({
    mutationFn: (prefs: Partial<NotificationPrefs>) => notificationsApi.updatePreferences(prefs),
    onSuccess: (data) => {
      queryClient.setQueryData(notificationKeys.preferences(), data);
      toast({
        title: "Preferences Updated",
        description: "Your notification preferences have been saved.",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update preferences",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    },
  });

  return {
    preferences: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    updatePreferences: updateMutation.mutate,
    isUpdating: updateMutation.isPending,
  };
}

/**
 * Hook for real-time notifications with WebSocket
 */
export function useRealTimeNotifications() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [isConnected, setIsConnected] = useState(false);
  const [newNotifications, setNewNotifications] = useState<Notification[]>([]);

  const addNotification = useCallback((notification: Notification) => {
    // Add to local state for immediate UI update
    setNewNotifications(prev => [notification, ...prev]);

    // Show toast for new notification
    toast({
      title: notification.title,
      description: notification.message,
      variant: notification.urgency === 'high' ? 'destructive' : 'default',
    });

    // Update React Query cache
    queryClient.invalidateQueries({ queryKey: notificationKeys.all });
  }, [queryClient, toast]);

  useEffect(() => {
    if (!user?.id) {
      console.log("useRealTimeNotifications: No user_id, WebSocket not connecting.");
      return;
    }

    const userIdParam = encodeURIComponent(String(user.id));
    let wsBaseUrl = import.meta.env.VITE_WS_URL;
    if (!wsBaseUrl) {
        wsBaseUrl = window.location.protocol === 'https:' ? 'wss://' + window.location.host : 'ws://' + window.location.host;
    }
    const wsUrl = `${wsBaseUrl}/api/notifications/stream?user_id=${userIdParam}`;
    
    let ws: WebSocket | null = null;
    let reconnectTimer: NodeJS.Timeout | null = null;

    const connectWebSocket = () => {
      if (reconnectTimer) clearTimeout(reconnectTimer);
      try {
        ws = new WebSocket(wsUrl);
        console.log(`Attempting to connect to Notifications WebSocket: ${wsUrl}`);

        ws.onopen = () => {
          console.log('Notifications WebSocket connected');
          setIsConnected(true);
        };

        ws.onmessage = (event) => {
          try {
            const notification: Notification = JSON.parse(event.data as string); // Added "as string"
            addNotification(notification);
            console.log('Notification received via WebSocket:', notification);
          } catch (error) {
            console.error('Error parsing notification WebSocket message:', error);
          }
        };

        ws.onclose = (event) => {
          console.log(`Notifications WebSocket disconnected (code: ${event.code}, reason: ${event.reason}), attempting to reconnect...`);
          setIsConnected(false);
          if (ws) {
            ws.onopen = null;
            ws.onmessage = null;
            ws.onclose = null;
            ws.onerror = null;
          }
          // Reconnect after 5 seconds if not a normal closure (1000)
          if (event.code !== 1000) {
            reconnectTimer = setTimeout(connectWebSocket, 5000);
          }
        };

        ws.onerror = (error) => {
          console.error('Notifications WebSocket error:', error);
          setIsConnected(false);
          // ws.onclose will typically be called after an error, triggering reconnect logic.
        };
      } catch (error) {
        console.error('Failed to connect to notifications WebSocket:', error);
        setIsConnected(false);
        if (reconnectTimer) clearTimeout(reconnectTimer);
        reconnectTimer = setTimeout(connectWebSocket, 10000);
      }
    };

    if (user?.id) {
        connectWebSocket();
    }

    return () => {
      if (reconnectTimer) clearTimeout(reconnectTimer);
      if (ws) {
        console.log("Closing notifications WebSocket connection.");
        ws.onclose = null; // Prevent reconnect on manual close
        ws.close();
      }
    };
  }, [user?.id, addNotification]);

  const clearNewNotifications = useCallback(() => {
    setNewNotifications([]);
  }, []);

  return {
    isConnected,
    newNotifications,
    clearNewNotifications,
    unreadCount: newNotifications.length,
  };
}

/**
 * Comprehensive notifications hook
 */
export function useNotificationsManager() {
  const notifications = useNotifications();
  const markAsRead = useMarkAsRead();
  const markAllAsRead = useMarkAllAsRead();
  const realTime = useRealTimeNotifications();
  const preferences = useNotificationPreferences();

  const unreadCount = (notifications.data?.notifications || [])
    .filter(n => !n.is_read).length + realTime.unreadCount;

  return {
    // Data
    notifications: notifications.data?.notifications || [],
    preferences: preferences.preferences,
    unreadCount,
    
    // Loading states
    isLoading: notifications.isLoading || preferences.isLoading,
    isError: notifications.isError || preferences.isError,
    error: notifications.error || preferences.error, // Expose combined error
    
    // Actions
    markAsRead: markAsRead.mutate,
    markAllAsRead: markAllAsRead.mutate,
    updatePreferences: preferences.updatePreferences,
    
    // Real-time
    isConnected: realTime.isConnected,
    newNotifications: realTime.newNotifications,
    clearNewNotifications: realTime.clearNewNotifications,
    
    // Refetch
    refetch: notifications.refetch,
  };
}
