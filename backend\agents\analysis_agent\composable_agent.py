"""
Composable analysis agent for the Datagenius backend.

This module provides a composable implementation of the analysis agent
that uses the component-based architecture.
"""

import logging
import os
import json # Added json import
from typing import Dict, Any, Optional, List

# Configure logging first
logger = logging.getLogger(__name__)

from agents.composable import ComposableAgent
from agents.mixins import ToolCompletionMixin
from agents.components.mcp_server import MCPServerComponent
from agents.components.persistent_context_manager import PersistentContextManager
# Attempt to import formatting utilities, or define simple ones if not available
try:
    from agents.utils.template_processor import format_number, format_data_types, format_memory_usage
except ImportError:
    logger.warning("Could not import formatting utilities from template_processor. Using basic formatting.")
    def format_number(n, *args, **kwargs): return str(n) if n is not None else "N/A"
    def format_data_types(d, *args, **kwargs): return str(d) if d else "N/A"
    def format_memory_usage(m, *args, **kwargs): return str(m) if m is not None else "N/A"

from .components import (
    AnalysisLLMComponent,
    AnalysisParserComponent,
    DataLoaderComponent,
    AnalysisExecutorComponent,
    SemanticLayerComponent,
    PandasAITrainingComponent
)


class ComposableAnalysisAgent(ToolCompletionMixin, ComposableAgent):
    """Composable implementation of the analysis agent."""

    def get_agent_type(self) -> str:
        """Return the agent type identifier."""
        return "analysis"

    def get_tool_indicators(self) -> List[str]:
        """Return list of context keys that indicate tool-triggered requests."""
        return ["analysis_request", "data_analysis_task", "visualization_request", "query_request"]

    def get_conversational_flags(self) -> List[str]:
        """Return list of context keys that indicate conversational mode."""
        return [
            "skip_analysis_execution",
            "is_conversational",
            "analysis_completed",
            "tool_completed",
            "auto_conversational_mode"
        ]

    def _get_agent_specific_new_request_patterns(self) -> List[str]:
        """Return agent-specific patterns that indicate new tool requests."""
        return [
            "analyze this data", "run analysis on", "create visualization",
            "query the data", "generate chart", "show me insights",
            "statistical analysis", "data exploration", "trend analysis",
            "analyze", "visualize", "query", "chart", "graph", "plot"
        ]

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the composable analysis agent.

        Args:
            config: Configuration dictionary for the agent
        """
        logger.info("Initializing Composable Analysis Agent")

        # Call the parent initialization to set up the base components
        await super()._initialize(config)

        # Load database configuration and merge with YAML config
        merged_config = await self._load_merged_configuration(config)

        # If no components were configured, set up the default components
        if not self.components:
            logger.info("No components configured, setting up default analysis components")

            # Create and initialize the LLM component
            llm_component = AnalysisLLMComponent()

            # Get provider and model from merged config (database takes priority)
            provider = merged_config.get("provider", "")
            model = merged_config.get("model", "")
            api_key = merged_config.get("api_key", "")

            # Initialize the LLM component with the centralized model provider system
            await llm_component.initialize({
                "name": "analysis_llm",
                "provider": provider,
                "model": model,
                "api_key": api_key
            })
            self.components.append(llm_component)

            # Create and initialize the parser component
            parser_component = AnalysisParserComponent()
            await parser_component.initialize({
                "name": "analysis_parser"
            })
            self.components.append(parser_component)

            # Create and initialize the data loader component
            loader_component = DataLoaderComponent()
            await loader_component.initialize({
                "name": "data_loader",
                "data_dir": config.get("data_dir", "data")
            })
            self.components.append(loader_component)

            # Create and initialize the analysis executor component
            executor_component = AnalysisExecutorComponent()
            await executor_component.initialize({
                "name": "analysis_executor"
            })
            self.components.append(executor_component)

            # Create and initialize the semantic layer component
            semantic_layer_component = SemanticLayerComponent()
            await semantic_layer_component.initialize({
                "name": "semantic_layer_manager"
            })
            self.components.append(semantic_layer_component)

            # Create and initialize the PandasAI training component
            pandasai_training_component = PandasAITrainingComponent()
            await pandasai_training_component.initialize({
                "name": "pandasai_trainer"
            })
            self.components.append(pandasai_training_component)

            # Create and initialize the MCP server component with all data analysis tools
            from agents.components import create_mcp_server_with_essential_tools

            # Create MCP server with enhanced data access and analysis tools
            mcp_server_component = await create_mcp_server_with_essential_tools({
                "name": "analysis_tools",
                "server_name": "datagenius-analysis-tools",
                "server_version": "1.0.0",
                "tools": [
                    # Enhanced data access tool (supports all file types)
                    {"type": "data_access"},
                    # Traditional analysis tools
                    {"type": "data_analysis"},
                    {"type": "data_cleaning"},
                    {"type": "data_visualization"},
                    {"type": "data_querying"},
                    {"type": "advanced_query"},
                    {"type": "data_filtering"},
                    {"type": "sentiment_analysis"},
                    {"type": "text_processing"},
                    {"type": "document_embedding"},
                    # Phase 2 Enhancements: Add new tool types
                    {"type": "advanced_visualization"}, # For heatmaps, 3D plots, network graphs, geospatial maps
                    {"type": "machine_learning"},      # For predictive analytics, pattern recognition, feature importance
                    {"type": "statistical_analysis"},  # For advanced tests, anomaly detection, time series
                    {"type": "natural_language_query"},# For enhanced context-aware query processing
                    {"type": "data_storytelling"},     # For generating narrative explanations
                    # PandasAI v3 tools
                    {"type": "pandasai_analysis"},     # For data analysis using PandasAI v3
                    {"type": "pandasai_visualization"},# For data visualization using PandasAI v3
                    {"type": "pandasai_query"}         # For natural language queries using PandasAI v3
                ]
            })
            self.components.append(mcp_server_component)
            logger.info("Added MCP server component with comprehensive and enhanced data analysis tools")

            logger.info(f"Initialized {len(self.components)} default analysis components")

    async def _load_merged_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Load and merge YAML configuration with database configuration.

        Args:
            config: YAML configuration dictionary

        Returns:
            Merged configuration with database settings taking priority
        """
        try:
            # Import the centralized configuration utilities
            from ..utils.model_init import load_agent_database_config, merge_agent_config

            # Load database configuration for this agent
            db_config = await load_agent_database_config("composable-analysis-ai")

            # Merge YAML config with database config (database takes priority)
            merged_config = merge_agent_config(config, db_config, "composable-analysis-ai")

            return merged_config

        except Exception as e:
            logger.error(f"Error loading database configuration for analysis agent: {e}")
            # Fall back to original config
            return config

    def _extract_text_from_content_items(self, content_items: List[Dict[str, Any]]) -> str:
        """Helper to extract text from a list of content items."""
        if not content_items:
            return "Not available"
        return "\n".join([item.get("text", "") for item in content_items if item.get("type") == "text"]).strip()

    def _construct_data_profile_summary(
        self,
        file_name: str,
        info_metadata: Dict[str, Any],
        preview_content_items: List[Dict[str, Any]],
        profile_content_items: List[Dict[str, Any]]
    ) -> str:
        """
        Constructs a data profile summary string directly from metadata and content.
        """
        summary_parts = [f"# Data Profile for: {file_name}\n"]

        # Basic Info
        rows = info_metadata.get("shape", [None, None])[0]
        cols = info_metadata.get("shape", [None, None])[1]
        summary_parts.append(f"## Overview")

        # Create a nice overview table
        overview_table = [
            "| Property | Value |",
            "|----------|-------|",
            f"| **Rows** | {format_number(rows)} |",
            f"| **Columns** | {format_number(cols)} |"
        ]

        dtypes = info_metadata.get("dtypes", {})
        if dtypes:
            overview_table.append(f"| **Data Types** | {format_data_types(dtypes)} |")

        memory = info_metadata.get("memory_usage", {}).get("total") # Assuming memory_usage is a dict with 'total'
        if memory is not None:
             overview_table.append(f"| **Memory Usage** | {format_memory_usage(memory)} |")
        else: # Fallback if structure is different
            memory_raw = info_metadata.get("memory_usage")
            if memory_raw is not None:
                overview_table.append(f"| **Memory Usage** | {format_memory_usage(memory_raw)} |")

        summary_parts.extend(overview_table)

        # Data Preview - Convert to markdown table if possible
        preview_text = self._extract_text_from_content_items(preview_content_items)
        summary_parts.append(f"\n## Data Preview (first few rows)")

        # Try to convert preview text to markdown table
        formatted_preview = self._format_preview_as_table(preview_text)
        summary_parts.append(formatted_preview)

        # Statistical Profile - Convert to markdown table if possible
        profile_text = self._extract_text_from_content_items(profile_content_items)
        summary_parts.append(f"\n## Statistical Summary")

        # Try to convert profile text to markdown table
        formatted_profile = self._format_profile_as_table(profile_text)
        summary_parts.append(formatted_profile)

        # Data Quality (Missing Values)
        missing_values_text = "Information not available" # Default if summary object or keys are missing
        if "missing_values_summary" in info_metadata:
            mv_summary = info_metadata["missing_values_summary"]
            # Ensure mv_summary is a dictionary and contains the expected keys
            if isinstance(mv_summary, dict) and \
               "total_missing_values" in mv_summary and \
               "columns_with_missing_values" in mv_summary:
                total_missing = mv_summary["total_missing_values"]
                cols_with_missing = mv_summary["columns_with_missing_values"]

                # Ensure these are numbers before comparison
                if isinstance(total_missing, (int, float)) and total_missing > 0:
                    missing_values_text = f"{format_number(total_missing)} missing values across {format_number(cols_with_missing)} columns."
                elif isinstance(total_missing, (int, float)) and total_missing == 0:
                    missing_values_text = "No missing values detected."
            # If mv_summary is not a dict or keys are missing, missing_values_text remains "Information not available"

        summary_parts.append(f"\n**Data Quality:**\n- Missing Values: {missing_values_text}")

        summary_parts.append("\n\nWhat would you like to explore or analyze in this dataset?")
        return "\n".join(summary_parts)

    def _format_preview_as_table(self, preview_text: str) -> str:
        """
        Convert preview text to a markdown table format.
        """
        if not preview_text or preview_text.strip() == "Not available":
            return "\n*No preview data available*\n"

        try:
            # Try to parse the preview text as a table
            lines = preview_text.strip().split('\n')
            if len(lines) < 2:
                return f"\n```\n{preview_text}\n```\n"

            # Look for tabular data patterns
            # Check if it looks like a pandas DataFrame output
            if any('|' in line for line in lines[:3]):
                # Already in table format, just clean it up
                table_lines = []
                for line in lines:
                    if line.strip() and '|' in line:
                        # Clean up the line and ensure proper markdown table format
                        cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                        if cells:
                            table_lines.append('| ' + ' | '.join(cells) + ' |')

                if len(table_lines) >= 2:
                    # Add header separator if not present
                    if not any('-' in line for line in table_lines[1:2]):
                        header_sep = '|' + '|'.join([' --- ' for _ in table_lines[0].split('|')[1:-1]]) + '|'
                        table_lines.insert(1, header_sep)
                    return '\n' + '\n'.join(table_lines) + '\n'

            # If not in table format, wrap in code block
            return f"\n```\n{preview_text}\n```\n"

        except Exception as e:
            logger.warning(f"Error formatting preview as table: {e}")
            return f"\n```\n{preview_text}\n```\n"

    def _format_profile_as_table(self, profile_text: str) -> str:
        """
        Convert profile text to a markdown table format.
        """
        if not profile_text or profile_text.strip() == "Not available":
            return "\n*No statistical summary available*\n"

        try:
            # Try to parse the profile text as a table
            lines = profile_text.strip().split('\n')
            if len(lines) < 2:
                return f"\n```\n{profile_text}\n```\n"

            # Look for statistical summary patterns
            if any('|' in line for line in lines[:3]):
                # Already in table format, just clean it up
                table_lines = []
                for line in lines:
                    if line.strip() and '|' in line:
                        # Clean up the line and ensure proper markdown table format
                        cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                        if cells:
                            table_lines.append('| ' + ' | '.join(cells) + ' |')

                if len(table_lines) >= 2:
                    # Add header separator if not present
                    if not any('-' in line for line in table_lines[1:2]):
                        header_sep = '|' + '|'.join([' --- ' for _ in table_lines[0].split('|')[1:-1]]) + '|'
                        table_lines.insert(1, header_sep)
                    return '\n' + '\n'.join(table_lines) + '\n'

            # Try to convert describe() output to table format
            if 'count' in profile_text and 'mean' in profile_text:
                # This looks like pandas describe() output
                return self._convert_describe_to_table(profile_text)

            # If not in table format, wrap in code block
            return f"\n```\n{profile_text}\n```\n"

        except Exception as e:
            logger.warning(f"Error formatting profile as table: {e}")
            return f"\n```\n{profile_text}\n```\n"

    def _convert_describe_to_table(self, describe_text: str) -> str:
        """
        Convert pandas describe() output to markdown table.
        """
        try:
            lines = describe_text.strip().split('\n')
            if len(lines) < 2:
                return f"\n```\n{describe_text}\n```\n"

            # Find the header line and data lines
            header_line = None
            data_lines = []

            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue

                # Look for the statistics names (count, mean, std, etc.)
                if any(stat in line.lower() for stat in ['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max']):
                    if header_line is None and i > 0:
                        # Previous line might be the header
                        header_line = lines[i-1].strip()
                    data_lines.append(line)

            if not data_lines:
                return f"\n```\n{describe_text}\n```\n"

            # Try to parse as table
            table_lines = []

            # Add header if we found one
            if header_line and any(char.isalpha() for char in header_line):
                # Split header by whitespace
                headers = header_line.split()
                if len(headers) > 1:
                    table_lines.append('| Statistic | ' + ' | '.join(headers) + ' |')
                    table_lines.append('|-----------|' + '|'.join([' --- ' for _ in headers]) + '|')

            # Add data rows
            for line in data_lines:
                parts = line.split()
                if len(parts) > 1:
                    stat_name = parts[0]
                    values = parts[1:]
                    table_lines.append(f'| **{stat_name}** | ' + ' | '.join(values) + ' |')

            if len(table_lines) >= 3:  # Header + separator + at least one data row
                return '\n' + '\n'.join(table_lines) + '\n'
            else:
                return f"\n```\n{describe_text}\n```\n"

        except Exception as e:
            logger.warning(f"Error converting describe output to table: {e}")
            return f"\n```\n{describe_text}\n```\n"

    async def process_message(self,
                             user_id: int,
                             message: str,
                             conversation_id: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message using the agent's components.
        """
        logger.info(f"=== ANALYSIS AGENT PROCESS_MESSAGE START ===")
        logger.info(f"User ID: {user_id}, Message: '{message}', Conversation ID: {conversation_id}")
        logger.info(f"Context received: {context}")
        logger.info(f"Agent config: {self.config}")
        logger.info(f"DEBUG: ComposableAnalysisAgent.process_message called with message: '{message}'")

        # Enhanced context preparation with conversation history
        enhanced_context = context or {}

        # Check for persistent attachments setting from frontend
        persistent_attachments = enhanced_context.get("persistent_attachments", False)
        logger.info(f"Persistent attachments setting: {persistent_attachments}")

        # Log conversation history if available
        conversation_history = enhanced_context.get("conversation_history", [])
        if conversation_history:
            logger.info(f"Analysis Agent: Processing with {len(conversation_history)} conversation messages")
            # Log recent conversation for debugging
            for i, msg in enumerate(conversation_history[-3:]):  # Last 3 messages
                logger.info(f"  Message {i+1}: {msg.get('sender', 'unknown')} - {msg.get('content', '')[:100]}...")
        else:
            logger.info("Analysis Agent: No conversation history available")

        ctx = {
            "user_id": user_id,
            "message": message,
            "conversation_id": conversation_id,
            "context": enhanced_context,
            "agent_config": self.config,
            "agent_components": self.components,
            "response": "",
            "metadata": {},
            "conversation_history": conversation_history,  # Make conversation history easily accessible
            "conversation_context": enhanced_context.get("conversation_context", {}),
            "persistent_attachments": persistent_attachments  # Pass persistent attachments setting
        }

        # Attempt to find a structured data_source object in the context
        data_source_obj_from_context = None
        if context:
            if "data_source" in context:
                data_source_obj_from_context = context["data_source"]
                logger.info(f"Found 'data_source' directly in context: {data_source_obj_from_context}")
            elif "message" in context and isinstance(context["message"], dict) and "metadata" in context["message"] and "data_source" in context["message"]["metadata"]:
                data_source_obj_from_context = context["message"]["metadata"]["data_source"]
                logger.info(f"Found 'data_source' in message metadata: {data_source_obj_from_context}")
            elif "conversation" in context and isinstance(context["conversation"], dict) and "metadata" in context["conversation"] and "data_source" in context["conversation"]["metadata"]:
                data_source_obj_from_context = context["conversation"]["metadata"]["data_source"]
                logger.info(f"Found 'data_source' in conversation metadata: {data_source_obj_from_context}")
            elif "file_attachment" in context:
                data_source_obj_from_context = context["file_attachment"]
                logger.info(f"Found 'file_attachment' in context: {data_source_obj_from_context}")
            elif "attachments" in context and context["attachments"]:
                if isinstance(context["attachments"], list) and len(context["attachments"]) > 0:
                    data_source_obj_from_context = context["attachments"][0]
                    logger.info(f"Found 'data_source' in attachments list: {data_source_obj_from_context}")
                elif isinstance(context["attachments"], dict):
                    data_source_obj_from_context = context["attachments"]
                    logger.info(f"Found 'data_source' in attachments dict: {data_source_obj_from_context}")

        if data_source_obj_from_context:
            ctx["data_source"] = data_source_obj_from_context # Store for later use in prompt if needed
            try:
                logger.info(f"Full data_source object from context: {json.dumps(data_source_obj_from_context, indent=2, default=str)}")
            except Exception as e:
                logger.error(f"Error serializing data_source_obj_from_context for logging: {e}. Raw: {data_source_obj_from_context}")

        # Initialize file_path. It will be set if a file is successfully located.
        file_path: Optional[str] = None
        file_id: Optional[str] = None
        file_name: str = "your data" # Default file name

        if data_source_obj_from_context:
            logger.info(f"=== DATA_SOURCE OBJECT FOUND IN CONTEXT. ATTEMPTING TO DETERMINE FILE PATH ===")
            logger.info(f"Type of data_source_obj_from_context: {type(data_source_obj_from_context)}")
            if isinstance(data_source_obj_from_context, dict):
                logger.info(f"Keys in data_source_obj_from_context (dict): {list(data_source_obj_from_context.keys())}")

            try:
                # Extract file_id and file_name from the data_source_obj_from_context
                if isinstance(data_source_obj_from_context, dict):
                    file_id = data_source_obj_from_context.get("id")
                    logger.info(f"Attempt 1 (direct get 'id'): file_id = {file_id}")
                    file_name = data_source_obj_from_context.get("name", file_name)
                    file_path_direct = data_source_obj_from_context.get("file_path") # Keep direct path if provided

                    if not file_id and "source_metadata" in data_source_obj_from_context:
                        source_metadata = data_source_obj_from_context.get("source_metadata", {})
                        if isinstance(source_metadata, dict):
                            logger.info(f"Checking in source_metadata: {source_metadata}")
                            file_id = source_metadata.get("file_id") or source_metadata.get("id")
                            logger.info(f"Attempt 2 (source_metadata): file_id = {file_id}")
                            if not file_name or file_name == "your data": # Update file_name if found here
                                file_name = source_metadata.get("file_name") or source_metadata.get("name", file_name)

                    if not file_id and "metadata" in data_source_obj_from_context: # Check 'metadata' key within data_source_obj
                        metadata_ds = data_source_obj_from_context.get("metadata", {})
                        if isinstance(metadata_ds, dict):
                            logger.info(f"Checking in data_source_obj.metadata: {metadata_ds}")
                            file_id = metadata_ds.get("file_id") or metadata_ds.get("id")
                            logger.info(f"Attempt 3 (data_source_obj.metadata): file_id = {file_id}")
                            if not file_name or file_name == "your data": # Update file_name if found here
                                file_name = metadata_ds.get("file_name") or metadata_ds.get("name", file_name)

                elif isinstance(data_source_obj_from_context, (str, int)):
                    file_id = str(data_source_obj_from_context)
                    logger.info(f"Attempt 4 (data_source_obj is str/int): file_id = {file_id}")

                # Log final extracted values before DB lookup
                logger.info(f"Final extracted values before DB/FS lookup: file_id='{file_id}', file_name='{file_name}', direct_path_from_context='{file_path_direct if 'file_path_direct' in locals() and file_path_direct else 'Not Provided'}'")

                # 1. Attempt to get file_path from database if file_id is available
                if file_id:
                    try:
                        from app.database import get_db, get_file
                        from sqlalchemy.orm import Session
                        db_gen = get_db()
                        db: Session = next(db_gen)
                        try:
                            db_file = get_file(db, file_id)
                            if db_file and db_file.file_path:
                                file_path = db_file.file_path
                                logger.info(f"Found file path in database: '{file_path}'. Will attempt to use this path.")
                            elif db_file:
                                logger.warning(f"File ID '{file_id}' found in database, but its file_path is empty.")
                            else:
                                logger.warning(f"File ID '{file_id}' not found in database.")
                        finally:
                            db.close()
                    except Exception as e:
                        logger.error(f"Error looking up file ID '{file_id}' in database: {str(e)}")

                # 2. If database lookup failed or no file_id, attempt filesystem search
                if not file_path:
                    logger.info(f"Database did not yield a file path (or no file_id). Attempting filesystem search for file_name='{file_name}', file_id='{file_id}'.")
                    possible_file_paths = []
                    # Use direct path from data_source if available
                    if 'file_path_direct' in locals() and file_path_direct:
                        possible_file_paths.append(file_path_direct)
                        if not os.path.isabs(file_path_direct):
                            possible_file_paths.extend([
                                os.path.join("data", file_path_direct), os.path.join("uploads", file_path_direct), os.path.join("backend/data", file_path_direct)
                            ])

                    # Construct paths based on file_id
                    if file_id:
                        for ext in ['.csv', '.xlsx', '.xls', '']: # Empty ext for names that might include it
                            base_names = [file_id, f"file_{file_id}"]
                            if file_name and file_name != "your data" and file_id not in file_name: # Add file_name if distinct and useful
                                base_names.append(file_name)
                            for bn in base_names:
                                possible_file_paths.append(f"{bn}{ext}") # Relative to CWD
                                for d in ["data", "uploads", "backend/data", "temp_uploads"]:
                                    possible_file_paths.append(os.path.join(d, f"{bn}{ext}"))

                    # Construct paths based on file_name (if not already covered by file_id logic)
                    if file_name and file_name != "your data":
                        for ext in ['.csv', '.xlsx', '.xls', '']:
                             fn_to_check = file_name if ext == '' and '.' in file_name else f"{file_name}{ext}"
                             if fn_to_check not in possible_file_paths: # Avoid duplicates
                                possible_file_paths.append(fn_to_check)
                                for d in ["data", "uploads", "backend/data", "temp_uploads"]:
                                    possible_file_paths.append(os.path.join(d, fn_to_check))

                    possible_file_paths = list(dict.fromkeys(p for p in possible_file_paths if p)) # Unique, non-empty
                    logger.info(f"Checking possible file paths: {possible_file_paths}")
                    for p in possible_file_paths:
                        if os.path.exists(p):
                            file_path = p
                            logger.info(f"Found data file via filesystem search at path: '{file_path}'")
                            break
                        # else: logger.debug(f"Path does not exist: {p}") # Too verbose for INFO

                    if not file_path: # Last resort: search common upload dirs for most recent
                        logger.warning(f"Filesystem search failed. Searching common upload directories for most recent data file potentially matching '{file_name}'.")
                        from app.config import UPLOAD_DIR # Assuming UPLOAD_DIR is configured
                        upload_dirs_to_check = list(dict.fromkeys([UPLOAD_DIR, "data", "uploads", "backend/data", "temp_uploads", "."]))
                        recent_files_found = []
                        for directory in upload_dirs_to_check:
                            if os.path.exists(directory) and os.path.isdir(directory):
                                try:
                                    all_files = [os.path.join(directory, f) for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]
                                    data_files_in_dir = [f for f in all_files if f.endswith(('.csv', '.xlsx', '.xls'))]
                                    if file_name and file_name != "your data":
                                        named_matches = [f for f in data_files_in_dir if file_name.lower() in os.path.basename(f).lower()]
                                        if named_matches: recent_files_found.extend(sorted(named_matches, key=os.path.getmtime, reverse=True))
                                    recent_files_found.extend(sorted(data_files_in_dir, key=os.path.getmtime, reverse=True))
                                except Exception as e_fs: logger.error(f"Error listing files in {directory}: {e_fs}")
                        if recent_files_found:
                            file_path = recent_files_found[0] # Take the most recent overall or named match
                            logger.info(f"Using most recently modified/matched file from searched directories: '{file_path}'")

            except Exception as e_outer:
                logger.error(f"Outer error during file path determination from data_source_obj: {e_outer}", exc_info=True)
                file_path = None # Ensure file_path is None on any error in this block

        else: # No data_source_obj_from_context
            logger.warning("No structured data_source object found in context. File analysis will be skipped.")
            # file_path remains None

        logger.info(f"Final determined file_path: '{file_path}' (Will be None if not found)")

        # Store determined file_id and file_name in ctx for prompt consistency
        ctx["file_id"] = file_id
        ctx["file_name"] = file_name if file_name and file_name != "your data" else (os.path.basename(file_path) if file_path else "your data")


        mcp_server = next((comp for comp in self.components if isinstance(comp, MCPServerComponent)), None)
        if not mcp_server:
            ctx["response"] = "I'm sorry, I'm not properly configured to respond. MCP server component missing."
            logger.error("No MCP server component found.")
            return {"message": ctx["response"], "metadata": ctx["metadata"]}

        try:
            # Handle "send_file_to_persona" flow separately if active (usually for initial display)
            # Only process files for initial uploads, not follow-up questions
            should_process_file_for_persona = (
                context and
                context.get("send_file_to_persona") and
                data_source_obj_from_context and
                (not message or message.strip() == "" or len(message.strip()) < 10)  # Only for initial uploads or very short messages
            )

            if should_process_file_for_persona:
                logger.info("Processing file for initial persona introduction (send_file_to_persona is true)")
                # This flow might use data_source_obj_from_context directly
                # It's often for displaying a preview and might not need full file_path resolution if tool handles it
                # For simplicity, we assume it uses the data_source_obj_from_context
                persona_result = await mcp_server.call_tool("data_access", {
                    "operation": "send_to_persona", "data_source": data_source_obj_from_context,
                    "params": {"sample_size": 10, "include_table": True}
                })
                if not persona_result.get("isError", False):
                    ctx["response"] = self._extract_text_from_content_items(persona_result.get("content", []))
                    ctx["metadata"]["file_processed_for_persona"] = True
                    # If this is the only action, return early
                    if not message or message.strip() == "": return {"message": ctx["response"], "metadata": ctx["metadata"]}
                else:
                    err_msg = self._extract_text_from_content_items(persona_result.get("content", [])) or "Error processing file for persona."
                    logger.error(f"Error in send_file_to_persona: {err_msg}")
                    ctx["response"] = err_msg
                    ctx["metadata"]["file_error"] = True
                    # If this is the only action and it failed, return early
                    if not message or message.strip() == "": return {"message": ctx["response"], "metadata": ctx["metadata"]}
            elif context and context.get("send_file_to_persona"):
                # For follow-up questions, check if we have file context from conversation history
                logger.info("Checking for file context in follow-up message for analysis agent")

                # Look for data source in current context or conversation history
                if not data_source_obj_from_context:
                    conversation_history = context.get("conversation_history", [])
                    for msg in reversed(conversation_history):
                        msg_metadata = msg.get("metadata", {})
                        if msg_metadata.get("data_source"):
                            data_source_obj_from_context = msg_metadata["data_source"]
                            logger.info(f"Analysis agent: Found file context from conversation history: {data_source_obj_from_context}")
                            # Update the context with the found data source
                            ctx["data_source"] = data_source_obj_from_context
                            break

                # Skip initial file processing and clear the flag
                logger.info("Skipping initial file processing for follow-up question - using conversation context instead")
                context["send_file_to_persona"] = False


            # For follow-up messages without send_file_to_persona flag, check for file context
            if not data_source_obj_from_context and message and len(message.strip()) > 10:
                logger.info("Checking conversation history for file context in follow-up message")
                conversation_history = context.get("conversation_history", [])
                for msg in reversed(conversation_history):
                    msg_metadata = msg.get("metadata", {})
                    if msg_metadata.get("data_source"):
                        data_source_obj_from_context = msg_metadata["data_source"]
                        logger.info(f"Analysis agent: Found file context from conversation history for follow-up: {data_source_obj_from_context}")
                        # Update the context with the found data source
                        ctx["data_source"] = data_source_obj_from_context
                        # Re-extract file information
                        file_id = self._extract_file_id(ctx)
                        if file_id:
                            file_path = self._resolve_file_path(file_id, ctx)
                            logger.info(f"Analysis agent: Resolved file path from conversation context: {file_path}")
                        break

            data_profile_summary_response = None
            data_analysis_results = None # For results from specific analysis tools

            if file_path: # All data operations depend on a valid file_path
                logger.info(f"Proceeding with analysis for file: '{file_path}'")

                # Determine if a data profile should be generated
                should_profile_data = False
                should_generate_preview = False
                profile_keywords = ["analyze", "profile", "describe", "summarize", "statistics", "statistical_summary", "overview", "show me", "tell me about", "preview", "summary"]

                # Check for empty message, greetings, or profile keywords
                message_lower = message.strip().lower()
                if (not message.strip() or
                    message_lower in ["", "hi", "hello"] or
                    any(keyword in message_lower for keyword in profile_keywords)):
                    logger.info(f"Data profiling requested (empty message, greeting, or keyword found).")
                    should_profile_data = True
                    should_generate_preview = True

                # Check if this is the first interaction with this file in the conversation
                conversation_history = context.get("conversation_history", [])
                is_first_file_interaction = True
                if conversation_history:
                    for msg in conversation_history:
                        msg_metadata = msg.get("metadata", {})
                        if (msg_metadata.get("data_preview") or
                            msg_metadata.get("data_profile") or
                            msg_metadata.get("file_processed_for_persona")):
                            is_first_file_interaction = False
                            logger.info("Found previous file interaction in conversation history - skipping automatic preview")
                            break

                # Generate preview only for first interaction or explicit requests
                if is_first_file_interaction:
                    logger.info("First interaction with file - generating preview")
                    should_generate_preview = True

                # Prepare data_source object for tools, now using the resolved file_path
                tool_data_source = {"id": file_id, "name": ctx["file_name"], "file_path": file_path}

                logger.info(f"🔍 COMPOSABLE AGENT: Creating tool_data_source object")
                logger.info(f"📋 file_id: {file_id}")
                logger.info(f"📋 file_name: {ctx['file_name']}")
                logger.info(f"📋 file_path: {file_path}")
                logger.info(f"📋 Full tool_data_source: {tool_data_source}")
                logger.info(f"📋 Original context data_source: {ctx.get('data_source', 'None')}")
                logger.info(f"📋 Should generate preview: {should_generate_preview}")

                # First, detect content type to determine appropriate operations
                logger.info(f"🔍 COMPOSABLE AGENT: Detecting content type for intelligent processing")
                content_type_res = await mcp_server.call_tool("data_access", {
                    "data_source": tool_data_source,
                    "operation": "detect_content_type"
                })

                content_info = content_type_res.get("metadata", {}) if not content_type_res.get("isError") else {}

                # Fallback content type detection for CSV files
                if not content_info and file_path and file_path.lower().endswith('.csv'):
                    logger.info(f"🔍 Fallback: Detected CSV file, assuming structured data")
                    content_info = {
                        "is_structured": True,
                        "file_type": "csv",
                        "has_text": False
                    }

                logger.info(f"📋 Content type detected: {content_info}")

                # Only generate data previews when appropriate
                preview_res = None
                info_res = None
                describe_res = None

                if should_generate_preview:
                    logger.info(f"🔍 COMPOSABLE AGENT: Generating data preview and profile")
                    # Use enhanced data access operations based on content type
                    if content_info.get("is_structured"):
                        # For structured data, use traditional operations
                        logger.info(f"🔍 COMPOSABLE AGENT: Processing structured data")
                        preview_res = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "head",
                            "params": {"n": 10}
                        })
                        info_res = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "info"
                        })
                        describe_res = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "describe"
                        })
                    elif content_info.get("has_text"):
                        # For unstructured data, use content analysis
                        logger.info(f"🔍 COMPOSABLE AGENT: Processing unstructured data")
                        preview_res = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "extract_text"
                        })
                        info_res = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "analyze_structure"
                        })
                        describe_res = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "extract_entities"
                        })
                    else:
                        # Fallback to basic operations
                        logger.info(f"🔍 COMPOSABLE AGENT: Using fallback operations")
                        preview_res = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "head",
                            "params": {"n": 10}
                        })
                        info_res = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "info"
                        })
                        describe_res = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "describe"
                        })

                    # Only add to metadata if preview generation was requested and successful
                    if preview_res and not preview_res.get("isError"):
                        ctx["metadata"]["data_preview"] = preview_res
                    if info_res and not info_res.get("isError"):
                        ctx["metadata"]["data_info"] = info_res
                    if describe_res and not describe_res.get("isError"):
                        ctx["metadata"]["data_profile"] = describe_res
                else:
                    logger.info(f"🔍 COMPOSABLE AGENT: Skipping data preview generation for follow-up question")

                # If errors occurred in essential data access (when preview was requested), log them and potentially stop
                if should_generate_preview and (
                    (preview_res and preview_res.get("isError")) or
                    (info_res and info_res.get("isError")) or
                    (describe_res and describe_res.get("isError"))
                ):
                    logger.error(f"Error accessing data for profiling. Preview: {preview_res.get('isError') if preview_res else 'Not attempted'}, Info: {info_res.get('isError') if info_res else 'Not attempted'}, Describe: {describe_res.get('isError') if describe_res else 'Not attempted'}")
                    # Construct an error message if essential data access failed
                    error_parts = []
                    if preview_res and preview_res.get("isError"):
                        error_parts.append(f"Preview failed: {self._extract_text_from_content_items(preview_res.get('content', []))}")
                    if info_res and info_res.get("isError"):
                        error_parts.append(f"Info failed: {self._extract_text_from_content_items(info_res.get('content', []))}")
                    if describe_res and describe_res.get("isError"):
                        error_parts.append(f"Describe failed: {self._extract_text_from_content_items(describe_res.get('content', []))}")

                    ctx["response"] = f"I encountered an issue while trying to load basic information about your data file ('{ctx['file_name']}'):\n" + "\n".join(error_parts) + "\nPlease ensure the file is accessible and in a supported format."
                    ctx["metadata"]["analysis_error"] = "data_access_failure"
                    # Return early if basic data access fails
                    return {"message": ctx["response"], "metadata": ctx["metadata"]}


                if should_profile_data and should_generate_preview and info_res and preview_res and describe_res:
                    data_profile_summary_response = self._construct_data_profile_summary(
                        ctx["file_name"],
                        info_res.get("metadata", {}),
                        preview_res.get("content", []),
                        describe_res.get("content", [])
                    )
                    ctx["metadata"]["task_type"] = "data_profile_summary"
                    # If only profiling was requested (e.g. empty message with attachment), this is the main response
                    if not message.strip() or any(keyword in message_lower for keyword in profile_keywords):
                        ctx["response"] = data_profile_summary_response
                        return {"message": ctx["response"], "metadata": ctx["metadata"]}

                # Handle specific user queries (including analysis requests)
                if message.strip():
                    logger.info(f"Handling user query: '{message}'")

                    # Use LLM-based intent analysis instead of hardcoded keywords
                    logger.info("Performing LLM-based intent analysis...")

                    # Prepare context for intent analysis
                    intent_context = {
                        "data_sources": context.get("data_sources", []),
                        "content_types": []
                    }

                    # Add content type information
                    if content_info.get("is_structured"):
                        intent_context["content_types"].append("structured")
                    if content_info.get("has_text"):
                        intent_context["content_types"].append("text")
                    if content_info.get("has_tables"):
                        intent_context["content_types"].append("tables")

                    # Perform intent analysis
                    intent_result = await mcp_server.call_tool("intent_analysis", {
                        "message": message,
                        "context": intent_context,
                        "provider": self.config.get("provider", "groq"),
                        "model": self.config.get("model", "llama-3.1-8b-instant")
                    })

                    # Extract intent information
                    intent_data = {}
                    if not intent_result.get("isError") and intent_result.get("metadata", {}).get("intent_analysis"):
                        llm_intent_data = intent_result["metadata"]["intent_analysis"]
                        logger.info(f"LLM Intent analysis result: {llm_intent_data.get('primary_intent')} (confidence: {llm_intent_data.get('confidence', 0)})")

                        # Always also run fallback detection to check for visualization keywords
                        fallback_intent_data = self._fallback_intent_detection(message)
                        logger.info(f"Fallback intent analysis result: {fallback_intent_data.get('primary_intent')}")

                        # If fallback detects visualization but LLM doesn't, use fallback
                        if (fallback_intent_data.get('primary_intent') == 'visualization' and
                            llm_intent_data.get('primary_intent') != 'visualization'):
                            logger.info("🎨 Overriding LLM intent with fallback visualization detection")
                            intent_data = fallback_intent_data
                        else:
                            intent_data = llm_intent_data
                    else:
                        logger.warning("Intent analysis failed, falling back to keyword detection")
                        # Fallback to simple keyword detection
                        intent_data = self._fallback_intent_detection(message)

                    # Extract intent flags for routing
                    primary_intent = intent_data.get("primary_intent", "general_query")
                    is_visualization_request = primary_intent == "visualization"
                    is_analysis_request = primary_intent in ["analysis", "statistical_analysis", "correlation_analysis", "data_exploration"]
                    is_entity_request = primary_intent == "entity_extraction"
                    is_search_request = primary_intent == "search"

                    # Debug logging for intent detection
                    logger.info(f"Intent detection results:")
                    logger.info(f"  - Primary intent: {primary_intent}")
                    logger.info(f"  - Is visualization request: {is_visualization_request}")
                    logger.info(f"  - Is analysis request: {is_analysis_request}")
                    logger.info(f"  - Message: {message[:100]}...")

                    # Route to appropriate tool based on content type and request type
                    # PRIORITIZE VISUALIZATION REQUESTS FIRST
                    if is_visualization_request:
                        if content_info.get("is_structured"):
                            # Call pandasai_visualization for structured data visualization
                            logger.info(f"🎨 ROUTING TO VISUALIZATION: Detected visualization request for structured data")
                            logger.info(f"🎨 Content info: {content_info}")
                            logger.info(f"🎨 Is structured: {content_info.get('is_structured')}")
                            logger.info(f"🎨 Is visualization request: {is_visualization_request}")

                            # Enhanced visualization prompt with specific instructions
                            viz_prompt = f"""Create a data visualization chart for: {message}

Data file: {ctx['file_name']}

Instructions:
- Generate a clear, informative chart/plot
- Use appropriate chart type for the data (bar, line, scatter, pie, etc.)
- Include proper labels and title
- Return only the visualization image
- If there are column name issues, use the exact column names from the data"""

                            logger.info(f"🎨 Calling pandasai_visualization with enhanced prompt")

                            # Extract file_path from data_source if it's an object
                            file_path_param = None
                            if isinstance(tool_data_source, dict) and "file_path" in tool_data_source:
                                file_path_param = tool_data_source["file_path"]
                            elif isinstance(tool_data_source, str):
                                file_path_param = tool_data_source

                            data_analysis_results = await mcp_server.call_tool("pandasai_visualization", {
                                "data_source": tool_data_source,
                                "file_path": file_path_param,
                                "query": viz_prompt,
                                "prompt": viz_prompt,  # Keep both for compatibility
                                "api_key": self.config.get("api_key"),
                                "provider": self.config.get("provider", "groq"),
                                "model": self.config.get("model", "llama3-70b-8192")
                            })
                            ctx["metadata"]["task_type"] = "pandasai_visualization"

                            logger.info(f"🎨 Visualization tool result: {data_analysis_results}")

                            # Ensure visualization metadata is properly set
                            if data_analysis_results and not data_analysis_results.get("isError"):
                                logger.info("🎨 Visualization tool succeeded")
                                ctx["data_analysis_results"] = data_analysis_results

                                # Set content for frontend processing
                                if "content" in data_analysis_results:
                                    ctx["metadata"]["content"] = data_analysis_results["content"]

                                    # Check for image content to confirm chart generation
                                    has_image = any(item.get("type") == "image" for item in data_analysis_results["content"])
                                    if has_image:
                                        logger.info("🎨 Confirmed chart image in response content")
                                        ctx["metadata"]["has_chart_image"] = True

                                # Extract and merge visualization metadata
                                viz_metadata = data_analysis_results.get("metadata", {})
                                if viz_metadata:
                                    # Merge all visualization metadata into context
                                    ctx["metadata"].update(viz_metadata)
                                    logger.info(f"🎨 Merged visualization metadata keys: {list(viz_metadata.keys())}")

                                if viz_metadata.get("visualization"):
                                    ctx["metadata"]["visualization"] = viz_metadata["visualization"]
                                    logger.info(f"🎨 Visualization metadata set: {ctx['metadata']['visualization'].get('type', 'unknown')}")
                                else:
                                    logger.warning(f"🎨 No visualization metadata found in result")

                                # Set response text
                                if "content" in data_analysis_results:
                                    text_content = [item for item in data_analysis_results["content"] if item.get("type") == "text"]
                                    if text_content:
                                        ctx["response"] = text_content[0].get("text", "Visualization created successfully.")
                                    else:
                                        ctx["response"] = "I have created a visualization for your data."
                                else:
                                    ctx["response"] = "I have generated a visualization for your data."
                            else:
                                logger.error(f"🎨 Visualization tool failed or returned error")
                                if data_analysis_results and data_analysis_results.get("isError"):
                                    error_content = data_analysis_results.get("content", [])
                                    if error_content and len(error_content) > 0:
                                        error_text = error_content[0].get("text", "Unknown visualization error")
                                        logger.error(f"Error from visualization tool: {error_text}")
                        else:
                            logger.warning(f"🎨 Visualization requested but data is not structured")

                    elif content_info.get("has_text") and is_entity_request:
                        # Extract entities from text documents
                        logger.info(f"Detected entity extraction request for text content")
                        data_analysis_results = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "extract_entities"
                        })
                        ctx["metadata"]["task_type"] = "entity_extraction"

                    elif content_info.get("has_text") and is_search_request:
                        # Semantic search in documents
                        logger.info(f"Detected search request for text content")
                        data_analysis_results = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "search_document",
                            "params": {"query": message, "limit": 5}
                        })
                        ctx["metadata"]["task_type"] = "document_search"

                    elif content_info.get("is_structured") and (is_analysis_request or should_profile_data):
                        # Call pandasai_query for structured data analysis
                        logger.info(f"Detected analysis request for structured data")
                        data_analysis_results = await mcp_server.call_tool("pandasai_query", {
                            "data_source": tool_data_source, "query": message,
                            "api_key": self.config.get("api_key"), "provider": self.config.get("provider", "openai")
                        })
                        ctx["metadata"]["task_type"] = "pandasai_query"

                    elif content_info.get("has_text") and is_analysis_request:
                        # Content analysis for text documents
                        logger.info(f"Detected content analysis request for text data")
                        data_analysis_results = await mcp_server.call_tool("data_access", {
                            "data_source": tool_data_source,
                            "operation": "content_analysis"
                        })
                        ctx["metadata"]["task_type"] = "content_analysis"

                    else:
                        # Default routing based on content type
                        if content_info.get("is_structured"):
                            logger.info(f"Default structured data processing with pandasai_query")
                            data_analysis_results = await mcp_server.call_tool("pandasai_query", {
                                "data_source": tool_data_source, "query": message,
                                "api_key": self.config.get("api_key"), "provider": self.config.get("provider", "openai")
                            })
                            ctx["metadata"]["task_type"] = "pandasai_query"
                        else:
                            logger.info(f"Default unstructured data processing with content analysis")
                            data_analysis_results = await mcp_server.call_tool("data_access", {
                                "data_source": tool_data_source,
                                "operation": "content_analysis"
                            })
                            ctx["metadata"]["task_type"] = "content_analysis"

                    if data_analysis_results and data_analysis_results.get("isError"):
                        logger.error(f"Error from analysis tool ({ctx['metadata']['task_type']}): {self._extract_text_from_content_items(data_analysis_results.get('content',[]))}")
                        # Continue to generate a response explaining the error

            # Construct final response prompt for LLM
            prompt_parts = [
                "You are Composable Analyst, an advanced AI assistant specializing in data analysis, visualization, machine learning, and data storytelling.",
                f"The user asked: '{message}'.",
                "",
                "IMPORTANT FORMATTING GUIDELINES:",
                "- Use proper markdown formatting with headers (# ## ###) to organize your response",
                "- Format ALL tabular data as markdown tables with | separators and proper alignment",
                "- Use **bold** text for important metrics and findings",
                "- Format numerical data clearly with appropriate precision",
                "- Include clear explanations before and after any tables or data",
                "- Use bullet points (-) for lists and key insights",
                "- Make your response visually appealing and easy to read",
                "- When showing correlations, format them as proper markdown tables",
                "- When showing statistical summaries, use markdown tables instead of code blocks",
                ""
            ]
            if file_path:
                prompt_parts.append(f"They are working with the data source: '{ctx['file_name']}'.")
                if data_profile_summary_response and not data_analysis_results : # If profile was generated and no other analysis
                     prompt_parts.append(f"Here is an initial profile of their data:\n{data_profile_summary_response}")
                if data_analysis_results and not data_analysis_results.get("isError"):
                    # Add analysis results to context metadata
                    if data_analysis_results.get("metadata"):
                        ctx["metadata"].update(data_analysis_results["metadata"])

                    analysis_text = self._extract_text_from_content_items(data_analysis_results.get("content", []))
                    prompt_parts.append(f"I performed the requested '{ctx['metadata'].get('task_type', 'analysis')}' and found:\n{analysis_text.strip()}")

                    # Check if visualization was generated
                    if "visualization" in ctx["metadata"].get("task_type", ""):
                        if ctx["metadata"].get("visualization"):
                            prompt_parts.append("I have generated a visualization for your data. You can see the chart displayed below.")
                        else:
                            prompt_parts.append("I have processed your visualization request. The chart should be displayed below.")
                elif data_analysis_results and data_analysis_results.get("isError"):
                    error_text = self._extract_text_from_content_items(data_analysis_results.get('content',[]))
                    prompt_parts.append(f"I tried to perform '{ctx['metadata'].get('task_type', 'analysis')}' but encountered an error: {error_text}")
                prompt_parts.extend([
                    "",
                    "Provide a helpful, insightful response with proper markdown formatting.",
                    "If analysis was done, explain it clearly with well-formatted tables and sections.",
                    "Use markdown tables (| Column 1 | Column 2 |) for any tabular data.",
                    "Suggest next steps if appropriate."
                ])
            else: # No file_path
                prompt_parts.append("No data source is currently available for analysis. This could be because no data file was attached, or an attached file could not be located or accessed.")
                prompt_parts.append("To proceed with analysis, please ensure a valid data file (e.g., CSV, Excel) is attached using the 'Attach Data' button.")

            final_prompt = "\n\n".join(prompt_parts)

            # Generate content using the conversation tool for proper AI assistant responses
            # Try primary provider first, then fallback if it fails
            primary_provider = self.config.get("provider", "groq")
            fallback_providers = ["openai", "anthropic", "gemini"] if primary_provider == "groq" else ["groq", "openai"]

            llm_response_result = None
            for provider in [primary_provider] + fallback_providers:
                try:
                    # Create agent context using the dynamic system
                    from agents.utils import create_agent_context

                    user_context = create_agent_context(
                        agent_id="composable-analysis-ai",
                        additional_context={
                            "is_analysis_response": True  # Backward compatibility
                        }
                    )

                    llm_response_result = await mcp_server.call_tool("handle_conversation", {
                        "message": final_prompt,
                        "conversation_history": context.get("conversation_history", []),
                        "user_context": user_context,
                        "intent_type": "analysis_request",
                        "confidence": 0.9,
                        "is_continuing_conversation": len(context.get("conversation_history", [])) > 0,
                        "provider": provider,
                        "model": self.config.get("model", "llama3-70b-8192" if provider == "groq" else None),
                        "temperature": 0.7
                    })

                    if not llm_response_result.get("isError", False):
                        logger.info(f"Successfully used provider: {provider}")
                        break
                    else:
                        logger.warning(f"Provider {provider} failed, trying next...")

                except Exception as e:
                    logger.warning(f"Provider {provider} error: {e}, trying next...")
                    continue

            if llm_response_result and not llm_response_result.get("isError", False):
                ctx["response"] = self._extract_text_from_content_items(llm_response_result.get("content", []))
                if not ctx["response"].strip() and file_path : # If LLM gave empty response but analysis was done
                    ctx["response"] = "I have processed your request. Please check the analysis panel for detailed results or visualizations."
            elif llm_response_result:
                error_content = self._extract_text_from_content_items(llm_response_result.get("content", [])) or "Unknown error generating response."
                ctx["response"] = f"I'm sorry, I encountered an error while generating the final response: {error_content}"
                logger.error(f"Error generating LLM response: {error_content}")
            else:
                # All providers failed, provide a basic response
                if ctx["metadata"].get("task_type") == "pandasai_visualization" and ctx["metadata"].get("visualization"):
                    ctx["response"] = "I have generated a visualization for your data. The chart should be displayed below."
                elif data_analysis_results and not data_analysis_results.get("isError"):
                    analysis_text = self._extract_text_from_content_items(data_analysis_results.get("content", []))
                    ctx["response"] = f"I have completed the analysis. Here are the results:\n\n{analysis_text}"
                else:
                    ctx["response"] = "I have processed your request, but I'm unable to generate a detailed response at the moment due to service issues. Please try again later."
                logger.warning("All LLM providers failed, using fallback response")

        except Exception as e_global:
            ctx["response"] = f"I'm sorry, I encountered an unexpected error: {str(e_global)}"
            logger.error(f"Global error in process_message: {str(e_global)}", exc_info=True)
            ctx["metadata"]["process_error"] = str(e_global)

        if not ctx.get("response"): # Fallback if response is still empty
            ctx["response"] = "I'm sorry, I wasn't able to process your request properly. Please try again."
            if "process_error" not in ctx["metadata"] and "analysis_error" not in ctx["metadata"]:
                 ctx["metadata"]["final_error"] = "no_response_generated"

        logger.info(f"=== ANALYSIS AGENT PROCESS_MESSAGE END ===")
        logger.info(f"Final response preview: {ctx.get('response', '')[:200]}...")
        logger.info(f"Final metadata keys: {list(ctx.get('metadata', {}).keys())}")
        return {"message": ctx.get("response", ""), "metadata": ctx.get("metadata", {})}

    def _fallback_intent_detection(self, message: str) -> Dict[str, Any]:
        """
        Fallback intent detection using simple keyword matching.
        Used when LLM-based intent analysis fails.
        """
        message_lower = message.lower()
        logger.info(f"🔍 Fallback intent detection for message: '{message_lower}'")

        # Default intent structure
        intent = {
            "primary_intent": "general_query",
            "confidence": 0.6,
            "intent_details": {
                "specific_request": message[:200]
            },
            "suggested_tools": ["handle_conversation"],
            "language": "en",
            "urgency": "medium",
            "complexity": "moderate"
        }

        # Visualization keywords
        viz_keywords = [
            "visualize", "visualization", "visualizations", "chart", "charts", "plot", "plots",
            "graph", "graphs", "bar", "pie", "correlation", "distribution", "visual", "visuals",
            "display", "histogram", "scatter", "line chart", "bar chart", "pie chart",
            "create chart", "create plot", "create graph", "create visualization", "create visualizations",
            "show chart", "show plot", "show graph", "show visualization", "show visualizations",
            "generate chart", "generate plot", "generate graph", "generate visualization", "generate visualizations"
        ]

        matched_keywords = [word for word in viz_keywords if word in message_lower]
        if matched_keywords:
            logger.info(f"🎨 VISUALIZATION KEYWORDS MATCHED: {matched_keywords}")
            intent["primary_intent"] = "visualization"
            intent["suggested_tools"] = ["pandasai_visualization"]
            intent["intent_details"]["visualization_type"] = "chart"

        # Analysis keywords
        elif any(word in message_lower for word in [
            "analyze", "analysis", "insights", "trends", "patterns",
            "statistics", "summary", "what", "how", "why"
        ]):
            intent["primary_intent"] = "analysis"
            intent["suggested_tools"] = ["pandasai_query", "data_access"]
            intent["intent_details"]["analysis_type"] = "exploratory"

        # Entity extraction keywords
        elif any(word in message_lower for word in [
            "entities", "people", "organizations", "extract", "find names",
            "who", "what companies"
        ]):
            intent["primary_intent"] = "entity_extraction"
            intent["suggested_tools"] = ["data_access"]
            intent["intent_details"]["data_operation"] = "extract_entities"

        # Search keywords
        elif any(word in message_lower for word in [
            "search", "find", "look for", "contains", "mentions"
        ]):
            intent["primary_intent"] = "search"
            intent["suggested_tools"] = ["data_access"]
            intent["intent_details"]["data_operation"] = "search"

        return intent
