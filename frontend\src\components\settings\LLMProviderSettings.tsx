import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Check, AlertCircle, ShieldAlert, Save, Eye, EyeOff, Info, Key, Bot } from 'lucide-react';
import { providerApi, Provider, ProviderSettings } from '@/lib/providerApi';

import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';
import { Toolt<PERSON>, Toolt<PERSON>Content, Toolt<PERSON>Provider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import EmbeddingModelSettings from './EmbeddingModelSettings';

interface ProviderState {
  apiKey: string;
  isTesting: boolean;
  status: 'unknown' | 'available' | 'unavailable';
  showApiKey: boolean;
}

// Provider icons/logos
const ProviderIcon = ({ providerId }: { providerId: string }) => {
  switch (providerId) {
    case 'openai':
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z" />
        </svg>
      );
    case 'anthropic':
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2L2 19.5h20L12 2zm0 6.5 5.5 9.5h-11l5.5-9.5z" />
        </svg>
      );
    case 'groq':
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 18c-4.418 0-8-3.582-8-8s3.582-8 8-8 8 3.582 8 8-3.582 8-8 8zm-4-9a1 1 0 1 1 0-2 1 1 0 0 1 0 2zm4 0a1 1 0 1 1 0-2 1 1 0 0 1 0 2zm4 0a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
        </svg>
      );
    case 'gemini':
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2L4 5v6c0 5.55 3.84 10.74 8 12 4.16-1.26 8-6.45 8-12V5l-8-3zm0 2.08L18 7v4c0 4.7-3.08 8.92-6 10.08-2.92-1.16-6-5.4-6-10.08V7l6-2.92z" />
        </svg>
      );
    case 'openrouter':
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-5-9h10v2H7v-2z" />
        </svg>
      );
    case 'requesty':
      return (
        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 14H5v-2h7v2zm7-4H5v-2h14v2zm0-4H5V7h14v2z" />
        </svg>
      );
    default:
      return <Key className="h-4 w-4" />;
  }
};

// Default providers to show when API fails
const DEFAULT_PROVIDERS: Provider[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'OpenAI\'s GPT models for text generation and more.',
    is_available: false
  },
  {
    id: 'anthropic',
    name: 'Anthropic',
    description: 'Anthropic\'s Claude models for text generation.',
    is_available: false
  },
  {
    id: 'groq',
    name: 'Groq',
    description: 'Groq\'s high-performance inference API for LLMs.',
    is_available: false
  },
  {
    id: 'gemini',
    name: 'Google Gemini',
    description: 'Google\'s Gemini models for text and multimodal tasks.',
    is_available: false
  },
  {
    id: 'openrouter',
    name: 'OpenRouter',
    description: 'Access to multiple LLM providers through a single API.',
    is_available: false
  },
  {
    id: 'requesty',
    name: 'Requesty',
    description: 'Requesty AI for text generation and more.',
    is_available: false
  }
];

export const LLMProviderSettings = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { refreshToken, logout } = useAuth();
  const [providers, setProviders] = useState<Provider[]>([]);
  const [providerStates, setProviderStates] = useState<Record<string, ProviderState>>({});
  const [settings, setSettings] = useState<ProviderSettings>({
    default_provider: 'openai',
    use_local_llm: false,
    memory_service_provider: '',
    memory_service_model: '',
    concierge_agent_provider: '',
    concierge_agent_model: '',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeProvider, setActiveProvider] = useState<string>('openai');
  const [availableModels, setAvailableModels] = useState<Record<string, ProviderModel[]>>({});

  // Function to handle authentication error
  const handleAuthError = () => {
    // Log the user out and redirect to login page
    toast({
      title: 'Session Expired',
      description: 'Your session has expired. Please sign in again.',
      variant: 'destructive',
    });

    // Short delay before logout to allow toast to be seen
    setTimeout(() => {
      logout();
      navigate('/login');
    }, 1500);
  };

  // Function to load models for a provider
  const loadModelsForProvider = async (providerId: string) => {
    if (!providerId || availableModels[providerId]) {
      return; // Already loaded or invalid provider
    }

    try {
      const response = await providerApi.getProviderModels(providerId);
      setAvailableModels(prev => ({
        ...prev,
        [providerId]: response.models
      }));
    } catch (error) {
      console.error(`Error loading models for provider ${providerId}:`, error);
    }
  };

  // Function to load data
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('Loading providers...');

      // Load providers
      const providersResponse = await providerApi.getProviders();
      console.log('Providers response:', providersResponse);

      if (!providersResponse || !providersResponse.providers) {
        console.error('Invalid providers response:', providersResponse);
        throw new Error('Invalid providers response');
      }

      const { providers } = providersResponse;
      console.log('Providers loaded:', providers);
      setProviders(providers);

      // Initialize provider states
      const states: Record<string, ProviderState> = {};
      providers.forEach(provider => {
        states[provider.id] = {
          apiKey: '',
          isTesting: false,
          status: provider.is_available ? 'available' : 'unknown',
          showApiKey: false,
        };
      });
      setProviderStates(states);
      console.log('Provider states initialized:', states);

      // Load settings
      console.log('Loading settings...');
      try {
        const settings = await providerApi.getSettings();
        console.log('Settings loaded:', settings);
        setSettings(settings);

        // Set active provider to default provider if available and not empty
        if (settings.default_provider && settings.default_provider !== "") {
          setActiveProvider(settings.default_provider);
        } else if (providers.length > 0) {
          // If no default provider, set the first available provider as active
          const availableProvider = providers.find(p => p.is_available);
          if (availableProvider) {
            setActiveProvider(availableProvider.id);
          } else {
            // If no available providers, just set the first one
            setActiveProvider(providers[0].id);
          }
        }
      } catch (settingsError: any) {
        console.error('Error loading provider settings:', settingsError);

        // Use default settings if there's an error
        const defaultSettings = {
          default_provider: '',
          use_local_llm: false
        };
        console.log('Using default settings:', defaultSettings);
        setSettings(defaultSettings);

        // Don't try to save default settings automatically, as this could overwrite user's existing settings
        console.log('Using default settings in UI only, not saving to database');
        // We'll let the user explicitly save settings if they want to

        // Show a toast notification but don't block the UI
        toast({
          title: 'Using Default Settings',
          description: 'Could not load your provider settings. Using default settings instead.',
          variant: 'default',
        });
      }
    } catch (error: any) {
      console.error('Error loading providers:', error);
      // Check if it's an authentication error
      const isAuthError = error?.message?.includes('Authentication failed') ||
                         error?.message?.includes('Could not validate credentials') ||
                         error?.message?.includes('session') ||
                         error?.message?.includes('token');

      if (isAuthError) {
        // Handle authentication error by logging out
        handleAuthError();
        return;
      } else {
        setError(error?.message || 'An error occurred while loading providers');
        toast({
          title: 'Error Loading Providers',
          description: error?.message || 'An error occurred while loading providers.',
          variant: 'destructive',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };



  // Load providers and settings
  useEffect(() => {
    loadData();
  }, []);

  // Update API key for a provider
  const updateApiKey = (providerId: string, apiKey: string) => {
    setProviderStates(prev => ({
      ...prev,
      [providerId]: {
        ...prev[providerId],
        apiKey,
      },
    }));
  };

  // Save API key for a provider
  const saveApiKey = async (providerId: string) => {
    const apiKey = providerStates[providerId]?.apiKey;
    if (!apiKey) return;

    setProviderStates(prev => ({
      ...prev,
      [providerId]: {
        ...prev[providerId],
        isTesting: true,
        status: 'unknown',
      },
    }));

    try {
      const response = await providerApi.setApiKey(providerId, apiKey);

      setProviderStates(prev => ({
        ...prev,
        [providerId]: {
          ...prev[providerId],
          status: response.is_valid ? 'available' : 'unavailable',
        },
      }));

      // Update providers list to reflect availability
      setProviders(prev => prev.map(p =>
        p.id === providerId ? { ...p, is_available: response.is_valid } : p
      ));

      toast({
        title: response.is_valid ? `${getProviderName(providerId)} API Key Valid` : `${getProviderName(providerId)} API Key Invalid`,
        description: response.message || (response.is_valid
          ? `Your ${getProviderName(providerId)} API key is valid and working.`
          : `Your ${getProviderName(providerId)} API key is invalid or not working.`),
        variant: response.is_valid ? 'default' : 'destructive',
      });

      // If no default provider is set, set this as default
      if (response.is_valid && !settings.default_provider) {
        saveDefaultProvider(providerId);
      }
    } catch (error) {
      console.error(`Error saving ${providerId} API key:`, error);

      setProviderStates(prev => ({
        ...prev,
        [providerId]: {
          ...prev[providerId],
          status: 'unavailable',
        },
      }));

      toast({
        title: `Error Saving ${getProviderName(providerId)} API Key`,
        description: `An error occurred while saving your ${getProviderName(providerId)} API key.`,
        variant: 'destructive',
      });
    } finally {
      setProviderStates(prev => ({
        ...prev,
        [providerId]: {
          ...prev[providerId],
          isTesting: false,
        },
      }));
    }
  };

  // Save default provider
  const saveDefaultProvider = async (providerId: string) => {
    try {
      console.log(`Saving default provider: '${providerId}'`);
      console.log(`Current settings before save:`, settings);

      // Ensure providerId is not null or undefined
      const safeProviderId = providerId || "";

      // Only update if the value is different from current setting
      if (settings.default_provider === safeProviderId) {
        console.log(`Default provider already set to '${safeProviderId}', skipping save`);
        return;
      }

      const updatedSettings = await providerApi.setSettings({
        ...settings,
        default_provider: safeProviderId,
      });

      console.log('Updated settings from server:', updatedSettings);
      setSettings(updatedSettings);

      // Update active provider to match default provider if not empty
      if (safeProviderId !== "") {
        setActiveProvider(safeProviderId);

        toast({
          title: 'Default Provider Saved',
          description: `${getProviderName(safeProviderId)} is now your default LLM provider.`,
        });
      } else {
        toast({
          title: 'Default Provider Cleared',
          description: 'No default provider is set. You will be prompted to choose a provider when needed.',
        });
      }
    } catch (error) {
      console.error('Error saving default provider:', error);

      toast({
        title: 'Error Saving Default Provider',
        description: 'An error occurred while saving your default provider.',
        variant: 'destructive',
      });
    }
  };

  // Toggle local LLM usage
  const toggleLocalLLM = async (checked: boolean) => {
    try {
      const updatedSettings = await providerApi.setSettings({
        ...settings,
        use_local_llm: checked,
      });

      setSettings(updatedSettings);

      toast({
        title: checked ? 'Local LLM Enabled' : 'Local LLM Disabled',
        description: checked
          ? 'The application will use your local LLM when available.'
          : 'The application will use cloud LLM providers.',
      });
    } catch (error) {
      console.error('Error toggling local LLM:', error);

      toast({
        title: 'Error Toggling Local LLM',
        description: 'An error occurred while toggling local LLM usage.',
        variant: 'destructive',
      });
    }
  };

  // Save memory service provider
  const saveMemoryServiceProvider = async (providerId: string) => {
    try {
      const updatedSettings = await providerApi.setSettings({
        ...settings,
        memory_service_provider: providerId,
        memory_service_model: '', // Reset model when provider changes
      });

      setSettings(updatedSettings);

      // Load models for the new provider
      if (providerId) {
        await loadModelsForProvider(providerId);
      }

      toast({
        title: 'Memory Service Provider Updated',
        description: `${getProviderName(providerId)} is now used for memory service.`,
      });
    } catch (error) {
      console.error('Error saving memory service provider:', error);
      toast({
        title: 'Error Saving Memory Service Provider',
        description: 'An error occurred while saving the memory service provider.',
        variant: 'destructive',
      });
    }
  };

  // Save memory service model
  const saveMemoryServiceModel = async (modelId: string) => {
    try {
      const updatedSettings = await providerApi.setSettings({
        ...settings,
        memory_service_model: modelId,
      });

      setSettings(updatedSettings);

      toast({
        title: 'Memory Service Model Updated',
        description: `${modelId} is now used for memory service.`,
      });
    } catch (error) {
      console.error('Error saving memory service model:', error);
      toast({
        title: 'Error Saving Memory Service Model',
        description: 'An error occurred while saving the memory service model.',
        variant: 'destructive',
      });
    }
  };

  // Save concierge agent provider
  const saveConciergeAgentProvider = async (providerId: string) => {
    try {
      const updatedSettings = await providerApi.setSettings({
        ...settings,
        concierge_agent_provider: providerId,
        concierge_agent_model: '', // Reset model when provider changes
      });

      setSettings(updatedSettings);

      // Load models for the new provider
      if (providerId) {
        await loadModelsForProvider(providerId);
      }

      toast({
        title: 'Concierge Agent Provider Updated',
        description: `${getProviderName(providerId)} is now used for concierge agent.`,
      });
    } catch (error) {
      console.error('Error saving concierge agent provider:', error);
      toast({
        title: 'Error Saving Concierge Agent Provider',
        description: 'An error occurred while saving the concierge agent provider.',
        variant: 'destructive',
      });
    }
  };

  // Save concierge agent model
  const saveConciergeAgentModel = async (modelId: string) => {
    try {
      const updatedSettings = await providerApi.setSettings({
        ...settings,
        concierge_agent_model: modelId,
      });

      setSettings(updatedSettings);

      toast({
        title: 'Concierge Agent Model Updated',
        description: `${modelId} is now used for concierge agent.`,
      });
    } catch (error) {
      console.error('Error saving concierge agent model:', error);
      toast({
        title: 'Error Saving Concierge Agent Model',
        description: 'An error occurred while saving the concierge agent model.',
        variant: 'destructive',
      });
    }
  };

  // Get provider name
  const getProviderName = (providerId: string): string => {
    const provider = providers.find(p => p.id === providerId);
    return provider?.name || providerId.charAt(0).toUpperCase() + providerId.slice(1);
  };

  // Toggle API key visibility
  const toggleApiKeyVisibility = (providerId: string) => {
    setProviderStates(prev => ({
      ...prev,
      [providerId]: {
        ...prev[providerId],
        showApiKey: !prev[providerId].showApiKey,
      },
    }));
  };

  // Get available providers for default provider selection
  const getAvailableProviders = (): Provider[] => {
    return providers.filter(p => p.is_available);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Error state with message
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-red-500">
            <ShieldAlert className="mr-2 h-5 w-5" />
            Error Loading Providers
          </CardTitle>
          <CardDescription>
            There was a problem loading your provider settings.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>{error}</p>
            <Button
              variant="outline"
              onClick={() => {
                logout();
                navigate('/login');
              }}
            >
              Sign In Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="shadow-sm border-gray-200 overflow-hidden">
        <CardHeader className="pb-4 border-b bg-muted/20">
          <CardTitle className="text-xl flex items-center gap-2">
            <Key className="h-5 w-5 text-primary" />
            LLM Provider Settings
          </CardTitle>
          <CardDescription>
            Configure your LLM providers for AI-powered features.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-8">
          {/* Provider API Keys - Dropdown Menu */}
          <div className="w-full mb-8">
            <div className="flex items-center gap-3 mb-6">
              <div className="bg-primary/10 text-primary rounded-full p-1.5">
                <Key className="h-5 w-5" />
              </div>
              <h3 className="text-base font-medium">Provider API Keys</h3>
            </div>

            <Select
              value={activeProvider}
              onValueChange={(value) => setActiveProvider(value)}
            >
              <SelectTrigger className="w-full mb-6 bg-background border-input">
                <SelectValue placeholder="Select a provider">
                  {activeProvider && (
                    <span className="flex items-center gap-2">
                      <span className="text-primary">
                        <ProviderIcon providerId={activeProvider} />
                      </span>
                      <span>{getProviderName(activeProvider)}</span>
                      {providerStates[activeProvider]?.status === 'available' && (
                        <Badge
                          variant="outline"
                          className="ml-1 bg-green-50 text-green-700 border-green-200 h-5 px-1.5 flex items-center justify-center rounded-full"
                        >
                          <Check className="h-3 w-3 mr-1" />
                          Active
                        </Badge>
                      )}
                    </span>
                  )}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {providers.map(provider => {
                  const isAvailable = providerStates[provider.id]?.status === 'available';
                  return (
                    <SelectItem
                      key={provider.id}
                      value={provider.id}
                      className="py-2"
                    >
                      <span className="flex items-center gap-2">
                        <span className={cn(
                          "mr-1",
                          isAvailable ? "text-primary" : "text-muted-foreground/70"
                        )}>
                          <ProviderIcon providerId={provider.id} />
                        </span>
                        <span>{provider.name}</span>
                        {isAvailable && (
                          <Badge
                            variant="outline"
                            className="ml-1 bg-green-50 text-green-700 border-green-200 h-5 px-1.5 flex items-center justify-center rounded-full"
                          >
                            <Check className="h-3 w-3 mr-1" />
                            Active
                          </Badge>
                        )}
                      </span>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>

            {/* Provider API Key Content */}
            {activeProvider && providers.find(p => p.id === activeProvider) && (
              <div className="space-y-5 animate-in fade-in-50 border rounded-lg p-5 bg-card/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Label htmlFor={`${activeProvider}-api-key`} className="text-base font-medium">
                      {getProviderName(activeProvider)} API Key
                    </Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="w-80">{providers.find(p => p.id === activeProvider)?.description}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  {providerStates[activeProvider]?.status !== 'unknown' && (
                    <Badge
                      variant={providerStates[activeProvider]?.status === 'available' ? 'outline' : 'destructive'}
                      className={cn(
                        "px-3 py-1",
                        providerStates[activeProvider]?.status === 'available'
                          ? "bg-green-50 text-green-700 border-green-200"
                          : "bg-red-50 text-red-700 border-red-200"
                      )}
                    >
                      {providerStates[activeProvider]?.status === 'available' ? (
                        <span className="flex items-center">
                          <Check className="h-3.5 w-3.5 mr-1.5" />
                          Available
                        </span>
                      ) : (
                        <span className="flex items-center">
                          <AlertCircle className="h-3.5 w-3.5 mr-1.5" />
                          Unavailable
                        </span>
                      )}
                    </Badge>
                  )}
                </div>
                <div className="relative">
                  <div className="flex space-x-2">
                    <div className="relative flex-1 group">
                      <div className="absolute left-0 top-0 h-full flex items-center pl-3 pointer-events-none">
                        <Key className="h-4 w-4 text-muted-foreground/70" />
                      </div>
                      <Input
                        id={`${activeProvider}-api-key`}
                        type={providerStates[activeProvider]?.showApiKey ? "text" : "password"}
                        placeholder={getApiKeyPlaceholder(activeProvider)}
                        value={providerStates[activeProvider]?.apiKey || ''}
                        onChange={(e) => updateApiKey(activeProvider, e.target.value)}
                        className="pl-10 pr-10 font-mono text-sm bg-background border-input focus-visible:ring-1 transition-all"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground hover:text-foreground transition-colors"
                        onClick={() => toggleApiKeyVisibility(activeProvider)}
                      >
                        {providerStates[activeProvider]?.showApiKey ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <Button
                      onClick={() => saveApiKey(activeProvider)}
                      disabled={!providerStates[activeProvider]?.apiKey || providerStates[activeProvider]?.isTesting}
                      className="min-w-[80px] bg-primary hover:bg-primary/90 transition-colors"
                    >
                      {providerStates[activeProvider]?.isTesting ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      Save
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  {providers.find(p => p.id === activeProvider)?.description} Your key is stored securely and encrypted in the database.
                </p>
              </div>
            )}
          </div>

          <div className="grid gap-8 md:grid-cols-2">
            {/* Default Provider */}
            <div className="space-y-3 p-5 rounded-lg border bg-card/50 hover:bg-card/80 transition-colors">
              <div className="flex items-center gap-2 mb-1">
                <div className="bg-primary/10 text-primary rounded-full p-1.5">
                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <Label htmlFor="default-provider" className="text-base font-medium">Default Provider</Label>
              </div>
              <Select
                value={settings.default_provider || ""}
                onValueChange={saveDefaultProvider}
              >
                <SelectTrigger
                  id="default-provider"
                  className="bg-background border-input focus:ring-1 focus:ring-ring transition-all"
                >
                  <SelectValue placeholder="Select a default provider">
                    {settings.default_provider && settings.default_provider !== "" ? (
                      <span className="flex items-center gap-2">
                        <span className="text-primary">
                          <ProviderIcon providerId={settings.default_provider} />
                        </span>
                        {getProviderName(settings.default_provider)}
                      </span>
                    ) : null}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {getAvailableProviders().map((provider) => (
                    <SelectItem key={provider.id} value={provider.id}>
                      <span className="flex items-center gap-2">
                        <span className="text-primary">
                          <ProviderIcon providerId={provider.id} />
                        </span>
                        {provider.name}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Select which provider to use by default for AI-powered features.
              </p>
            </div>

            {/* Local LLM Toggle */}
            <div className="space-y-3 p-5 rounded-lg border bg-card/50 hover:bg-card/80 transition-colors">
              <div className="flex items-center gap-2 mb-1">
                <div className="bg-primary/10 text-primary rounded-full p-1.5">
                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                  </svg>
                </div>
                <Label htmlFor="use-local-llm" className="text-base font-medium">Use Local LLM</Label>
              </div>
              <div className="flex items-center justify-between bg-background rounded-md border border-input p-3">
                <span className="text-sm">Enable local LLM processing</span>
                <Switch
                  id="use-local-llm"
                  checked={settings.use_local_llm}
                  onCheckedChange={toggleLocalLLM}
                  className="data-[state=checked]:bg-green-500"
                />
              </div>
              <p className="text-sm text-muted-foreground">
                When enabled, the application will attempt to use a locally running LLM server before falling back to cloud providers.
              </p>
            </div>
          </div>

          {/* Agent-Specific Settings */}
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 text-primary rounded-full p-1.5">
                <Bot className="h-5 w-5" />
              </div>
              <h3 className="text-lg font-medium">Agent-Specific Settings</h3>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              {/* Memory Service Settings */}
              <div className="space-y-4 p-5 rounded-lg border bg-card/50 hover:bg-card/80 transition-colors">
                <div className="flex items-center gap-2 mb-2">
                  <div className="bg-blue-100 text-blue-600 rounded-full p-1.5">
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <Label className="text-base font-medium">Memory Service</Label>
                </div>

                <div className="space-y-3">
                  <div>
                    <Label htmlFor="memory-service-provider" className="text-sm font-medium">Provider</Label>
                    <Select
                      value={settings.memory_service_provider || ""}
                      onValueChange={saveMemoryServiceProvider}
                    >
                      <SelectTrigger
                        id="memory-service-provider"
                        className="bg-background border-input focus:ring-1 focus:ring-ring transition-all"
                      >
                        <SelectValue placeholder="Select provider for memory service">
                          {settings.memory_service_provider && settings.memory_service_provider !== "" ? (
                            <span className="flex items-center gap-2">
                              <span className="text-primary">
                                <ProviderIcon providerId={settings.memory_service_provider} />
                              </span>
                              {getProviderName(settings.memory_service_provider)}
                            </span>
                          ) : null}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {getAvailableProviders().map((provider) => (
                          <SelectItem key={provider.id} value={provider.id}>
                            <span className="flex items-center gap-2">
                              <span className="text-primary">
                                <ProviderIcon providerId={provider.id} />
                              </span>
                              {provider.name}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {settings.memory_service_provider && (
                    <div>
                      <Label htmlFor="memory-service-model" className="text-sm font-medium">Model</Label>
                      <Select
                        value={settings.memory_service_model || ""}
                        onValueChange={saveMemoryServiceModel}
                        onOpenChange={() => loadModelsForProvider(settings.memory_service_provider)}
                      >
                        <SelectTrigger
                          id="memory-service-model"
                          className="bg-background border-input focus:ring-1 focus:ring-ring transition-all"
                        >
                          <SelectValue placeholder="Select model for memory service" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableModels[settings.memory_service_provider]?.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              {model.name || model.id}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <p className="text-sm text-muted-foreground">
                  Configure the LLM provider and model used for memory processing and context management.
                </p>
              </div>

              {/* Concierge Agent Settings */}
              <div className="space-y-4 p-5 rounded-lg border bg-card/50 hover:bg-card/80 transition-colors">
                <div className="flex items-center gap-2 mb-2">
                  <div className="bg-green-100 text-green-600 rounded-full p-1.5">
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <Label className="text-base font-medium">Concierge Agent</Label>
                </div>

                <div className="space-y-3">
                  <div>
                    <Label htmlFor="concierge-agent-provider" className="text-sm font-medium">Provider</Label>
                    <Select
                      value={settings.concierge_agent_provider || ""}
                      onValueChange={saveConciergeAgentProvider}
                    >
                      <SelectTrigger
                        id="concierge-agent-provider"
                        className="bg-background border-input focus:ring-1 focus:ring-ring transition-all"
                      >
                        <SelectValue placeholder="Select provider for concierge agent">
                          {settings.concierge_agent_provider && settings.concierge_agent_provider !== "" ? (
                            <span className="flex items-center gap-2">
                              <span className="text-primary">
                                <ProviderIcon providerId={settings.concierge_agent_provider} />
                              </span>
                              {getProviderName(settings.concierge_agent_provider)}
                            </span>
                          ) : null}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {getAvailableProviders().map((provider) => (
                          <SelectItem key={provider.id} value={provider.id}>
                            <span className="flex items-center gap-2">
                              <span className="text-primary">
                                <ProviderIcon providerId={provider.id} />
                              </span>
                              {provider.name}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {settings.concierge_agent_provider && (
                    <div>
                      <Label htmlFor="concierge-agent-model" className="text-sm font-medium">Model</Label>
                      <Select
                        value={settings.concierge_agent_model || ""}
                        onValueChange={saveConciergeAgentModel}
                        onOpenChange={() => loadModelsForProvider(settings.concierge_agent_provider)}
                      >
                        <SelectTrigger
                          id="concierge-agent-model"
                          className="bg-background border-input focus:ring-1 focus:ring-ring transition-all"
                        >
                          <SelectValue placeholder="Select model for concierge agent" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableModels[settings.concierge_agent_provider]?.map((model) => (
                            <SelectItem key={model.id} value={model.id}>
                              {model.name || model.id}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <p className="text-sm text-muted-foreground">
                  Configure the LLM provider and model used for the concierge agent interactions.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row sm:justify-between gap-4 border-t bg-muted/10 py-5">
          <div className="flex items-center text-muted-foreground">
            <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <p className="text-xs">
              Your API keys are stored securely in the database and are encrypted.
            </p>
          </div>
          <Button
            className="w-full sm:w-auto gap-2 bg-primary hover:bg-primary/90 text-white shadow-sm transition-all"
            onClick={async () => {
              try {
                // Save current settings
                await providerApi.setSettings(settings);

                // Check if any provider has an unsaved API key and save it
                for (const provider of providers) {
                  const state = providerStates[provider.id];
                  if (state?.apiKey && !state.isTesting && state.status !== 'available') {
                    await saveApiKey(provider.id);
                  }
                }

                toast({
                  title: "Settings saved",
                  description: "Your provider settings have been saved successfully."
                });
              } catch (error) {
                console.error('Error saving all settings:', error);
                toast({
                  title: "Error saving settings",
                  description: "There was a problem saving your settings. Please try again.",
                  variant: "destructive"
                });
              }
            }}
          >
            <Save className="h-4 w-4" />
            Save All Changes
          </Button>
        </CardFooter>
      </Card>

      {/* Embedding Model Settings */}
      <EmbeddingModelSettings className="mt-6" />
    </div>
  );
};

// Helper function to get API key placeholder based on provider ID
function getApiKeyPlaceholder(providerId: string): string {
  const placeholders: Record<string, string> = {
    'openai': 'sk-...',
    'anthropic': 'sk-ant-...',
    'groq': 'gsk-...',
    'gemini': 'AIza...',
    'openrouter': 'sk-or-...',
    'requesty': 'rq-...',
  };

  return placeholders[providerId] || '...';
}
