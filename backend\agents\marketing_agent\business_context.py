"""
Production Business Context Analyzer for Marketing Agent.

This module provides AI-powered business context analysis using LLM providers
to replace mock implementations with intelligent context detection.
"""

import logging
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from ..components.shared.shared_llm_processor import SharedLLMProcessor, LLMProviderFactory
from ..components.base_component import AgentContext

logger = logging.getLogger(__name__)


class BusinessContext(BaseModel):
    """Business context model for structured analysis results."""
    industry: Optional[str] = None
    business_type: Optional[str] = None
    business_size: Optional[str] = None
    target_market: Optional[str] = None
    key_products: List[str] = Field(default_factory=list)
    marketing_challenges: List[str] = Field(default_factory=list)
    competitive_advantages: List[str] = Field(default_factory=list)
    current_marketing_channels: List[str] = Field(default_factory=list)
    budget_indicators: Optional[str] = None
    geographic_focus: Optional[str] = None
    business_stage: Optional[str] = None
    confidence_score: float = 0.0


class BusinessContextAnalyzer:
    """
    Production-ready business context analyzer using AI-powered analysis.
    
    This class replaces pattern-matching approaches with intelligent LLM-based
    analysis to provide more accurate and nuanced business context detection.
    """
    
    def __init__(self, llm_provider: str = "groq"):
        """
        Initialize the business context analyzer.
        
        Args:
            llm_provider: LLM provider to use for analysis
        """
        self.llm_provider = llm_provider
        self.llm_processor = SharedLLMProcessor({
            "fallback_providers": [llm_provider, "openai", "google"],
            "default_temperature": 0.3,  # Lower temperature for more consistent analysis
            "max_tokens": 2000
        })
        
        # Analysis prompts for different types of content
        self.analysis_prompts = {
            "data_analysis": self._get_data_analysis_prompt(),
            "conversation_analysis": self._get_conversation_analysis_prompt(),
            "profile_analysis": self._get_profile_analysis_prompt(),
            "synthesis": self._get_synthesis_prompt()
        }
    
    async def analyze_context(self, user_data: Dict[str, Any]) -> BusinessContext:
        """
        Analyze business context from available user data using AI.
        
        Args:
            user_data: Dictionary containing user data sources, conversations, and profile
            
        Returns:
            BusinessContext object with analyzed information
        """
        try:
            logger.info("Starting AI-powered business context analysis")
            
            # Initialize analysis results
            analysis_results = {}
            
            # Analyze data sources if available
            if user_data.get("data_sources"):
                data_analysis = await self._analyze_data_sources(user_data["data_sources"])
                analysis_results["data_analysis"] = data_analysis
            
            # Analyze conversation history if available
            if user_data.get("conversation_history"):
                conversation_analysis = await self._analyze_conversation_history(user_data["conversation_history"])
                analysis_results["conversation_analysis"] = conversation_analysis
            
            # Analyze user profile if available
            if user_data.get("user_profile"):
                profile_analysis = await self._analyze_user_profile(user_data["user_profile"])
                analysis_results["profile_analysis"] = profile_analysis
            
            # Synthesize all analyses into final business context
            business_context = await self._synthesize_context(analysis_results)
            
            logger.info(f"Business context analysis completed with confidence: {business_context.confidence_score:.2f}")
            return business_context
            
        except Exception as e:
            logger.error(f"Error in business context analysis: {e}")
            # Return basic context with low confidence
            return BusinessContext(
                industry="Unknown",
                business_type="Unknown",
                confidence_score=0.1
            )
    
    async def _analyze_data_sources(self, data_sources: List[str]) -> Dict[str, Any]:
        """Analyze uploaded data sources using AI."""
        try:
            # For now, we'll use the existing data extraction logic
            # In a full implementation, this would extract and analyze file contents
            from ..tools.mcp.data_access import DataAccessTool
            
            data_tool = DataAccessTool()
            await data_tool.initialize({})
            
            combined_content = []
            for data_source_id in data_sources[:3]:  # Limit to first 3 sources
                try:
                    result = await data_tool.execute({
                        "data_source": data_source_id,
                        "operation": "extract_text",
                        "params": {}
                    })
                    
                    if not result.get("isError") and result.get("metadata"):
                        content = result["metadata"].get("full_text", "")
                        if content:
                            combined_content.append(content[:2000])  # Limit content length
                            
                except Exception as e:
                    logger.warning(f"Error extracting content from {data_source_id}: {e}")
                    continue
            
            if not combined_content:
                return {}
            
            # Analyze combined content with AI
            context = AgentContext()
            context.set_field("prompt", self.analysis_prompts["data_analysis"].format(
                content="\n\n".join(combined_content)
            ))
            
            result_context = await self.llm_processor.process(context)
            
            if result_context.get_status() == "success":
                response = result_context.get_field("llm_response", "")
                return self._parse_analysis_response(response)
            
            return {}
            
        except Exception as e:
            logger.error(f"Error analyzing data sources: {e}")
            return {}
    
    async def _analyze_conversation_history(self, conversation_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze conversation history using AI."""
        try:
            if not conversation_history:
                return {}
            
            # Extract recent user messages
            user_messages = []
            for msg in conversation_history[-10:]:  # Last 10 messages
                if msg.get("role") == "user" or msg.get("sender") == "user":
                    content = msg.get("content", "")
                    if content:
                        user_messages.append(content)
            
            if not user_messages:
                return {}
            
            # Analyze conversation with AI
            context = AgentContext()
            context.set_field("prompt", self.analysis_prompts["conversation_analysis"].format(
                messages="\n".join(user_messages)
            ))
            
            result_context = await self.llm_processor.process(context)
            
            if result_context.get_status() == "success":
                response = result_context.get_field("llm_response", "")
                return self._parse_analysis_response(response)
            
            return {}
            
        except Exception as e:
            logger.error(f"Error analyzing conversation history: {e}")
            return {}
    
    async def _analyze_user_profile(self, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user profile using AI."""
        try:
            if not user_profile:
                return {}
            
            # Create profile summary
            profile_text = []
            for key, value in user_profile.items():
                if value:
                    profile_text.append(f"{key}: {value}")
            
            if not profile_text:
                return {}
            
            # Analyze profile with AI
            context = AgentContext()
            context.set_field("prompt", self.analysis_prompts["profile_analysis"].format(
                profile="\n".join(profile_text)
            ))
            
            result_context = await self.llm_processor.process(context)
            
            if result_context.get_status() == "success":
                response = result_context.get_field("llm_response", "")
                return self._parse_analysis_response(response)
            
            return {}
            
        except Exception as e:
            logger.error(f"Error analyzing user profile: {e}")
            return {}
    
    async def _synthesize_context(self, analysis_results: Dict[str, Any]) -> BusinessContext:
        """Synthesize all analysis results into final business context."""
        try:
            if not analysis_results:
                return BusinessContext(confidence_score=0.0)
            
            # Combine all analysis results
            combined_analysis = {}
            for analysis_type, results in analysis_results.items():
                if results:
                    combined_analysis[analysis_type] = results
            
            if not combined_analysis:
                return BusinessContext(confidence_score=0.0)
            
            # Use AI to synthesize final context
            context = AgentContext()
            context.set_field("prompt", self.analysis_prompts["synthesis"].format(
                analyses=str(combined_analysis)
            ))
            
            result_context = await self.llm_processor.process(context)
            
            if result_context.get_status() == "success":
                response = result_context.get_field("llm_response", "")
                return self._parse_business_context(response)
            
            return BusinessContext(confidence_score=0.1)
            
        except Exception as e:
            logger.error(f"Error synthesizing business context: {e}")
            return BusinessContext(confidence_score=0.0)

    def _get_data_analysis_prompt(self) -> str:
        """Get prompt template for data analysis."""
        return """
Analyze the following business data and extract key business context information.

Data Content:
{content}

Please analyze this content and provide insights about:
1. Industry or business sector
2. Business type (B2B, B2C, etc.)
3. Business size (startup, small, medium, enterprise)
4. Target market or customer base
5. Key products or services
6. Marketing challenges mentioned
7. Competitive advantages
8. Current marketing channels or strategies
9. Budget indicators
10. Geographic focus

Respond in a structured format with clear categories and specific insights based on the content.
Focus on factual information found in the data rather than assumptions.
"""

    def _get_conversation_analysis_prompt(self) -> str:
        """Get prompt template for conversation analysis."""
        return """
Analyze the following user messages to understand their business context and marketing needs.

User Messages:
{messages}

Based on these messages, identify:
1. Industry or business type mentioned
2. Business challenges or pain points
3. Marketing goals or objectives
4. Target audience references
5. Products or services discussed
6. Budget constraints or indicators
7. Current marketing activities
8. Competitive concerns
9. Business stage (startup, growth, established)
10. Geographic market focus

Provide specific insights based on what the user has explicitly mentioned or strongly implied.
Avoid making assumptions beyond what's clearly indicated in the messages.
"""

    def _get_profile_analysis_prompt(self) -> str:
        """Get prompt template for profile analysis."""
        return """
Analyze the following user profile information to determine business context.

User Profile:
{profile}

Based on this profile information, determine:
1. Industry classification
2. Business type and model
3. Company size or business stage
4. Role-based marketing challenges
5. Likely target market
6. Potential marketing needs
7. Budget considerations based on role/company
8. Geographic market indicators

Provide insights based on the profile data, focusing on what can be reasonably inferred
from the provided information.
"""

    def _get_synthesis_prompt(self) -> str:
        """Get prompt template for synthesis."""
        return """
Synthesize the following business analysis results into a comprehensive business context.

Analysis Results:
{analyses}

Create a unified business context that includes:
1. Most likely industry (with confidence level)
2. Business type (B2B/B2C/B2B2C)
3. Business size/stage
4. Target market description
5. Key products/services (list)
6. Primary marketing challenges (list)
7. Competitive advantages (list)
8. Current marketing channels (list)
9. Budget indicators
10. Geographic focus
11. Overall confidence score (0.0-1.0)

Resolve any conflicts between different analyses by weighing the reliability of each source.
Provide a confidence score based on the consistency and quality of available information.

Format the response as a structured summary with clear categories.
"""

    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse AI analysis response into structured data."""
        try:
            # Simple parsing - in production, this could use more sophisticated NLP
            result = {}
            lines = response.strip().split('\n')

            current_category = None
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Check for category headers
                if any(keyword in line.lower() for keyword in ['industry', 'business type', 'size', 'target', 'products', 'challenges', 'advantages', 'channels', 'budget', 'geographic']):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        result[key.strip().lower().replace(' ', '_')] = value.strip()
                    else:
                        current_category = line.lower().replace(' ', '_')
                elif current_category and line.startswith('-'):
                    # List item
                    if current_category not in result:
                        result[current_category] = []
                    result[current_category].append(line[1:].strip())

            return result

        except Exception as e:
            logger.error(f"Error parsing analysis response: {e}")
            return {}

    def _parse_business_context(self, response: str) -> BusinessContext:
        """Parse AI synthesis response into BusinessContext object."""
        try:
            # Extract structured information from the response
            context_data = self._parse_analysis_response(response)

            # Map to BusinessContext fields
            return BusinessContext(
                industry=context_data.get('industry') or context_data.get('most_likely_industry'),
                business_type=context_data.get('business_type'),
                business_size=context_data.get('business_size') or context_data.get('business_stage'),
                target_market=context_data.get('target_market') or context_data.get('target_market_description'),
                key_products=context_data.get('key_products') or context_data.get('products_services', []),
                marketing_challenges=context_data.get('marketing_challenges') or context_data.get('primary_marketing_challenges', []),
                competitive_advantages=context_data.get('competitive_advantages', []),
                current_marketing_channels=context_data.get('current_marketing_channels', []),
                budget_indicators=context_data.get('budget_indicators'),
                geographic_focus=context_data.get('geographic_focus'),
                business_stage=context_data.get('business_stage'),
                confidence_score=self._extract_confidence_score(response)
            )

        except Exception as e:
            logger.error(f"Error parsing business context: {e}")
            return BusinessContext(confidence_score=0.1)

    def _extract_confidence_score(self, response: str) -> float:
        """Extract confidence score from AI response."""
        try:
            import re

            # Look for confidence score patterns
            patterns = [
                r'confidence[:\s]+([0-9.]+)',
                r'confidence score[:\s]+([0-9.]+)',
                r'overall confidence[:\s]+([0-9.]+)'
            ]

            for pattern in patterns:
                match = re.search(pattern, response.lower())
                if match:
                    score = float(match.group(1))
                    return min(1.0, max(0.0, score))  # Clamp between 0 and 1

            # Default confidence based on response length and content
            if len(response) > 500:
                return 0.7
            elif len(response) > 200:
                return 0.5
            else:
                return 0.3

        except Exception as e:
            logger.error(f"Error extracting confidence score: {e}")
            return 0.3
