"""
API endpoints for AI models.

This module provides API endpoints for managing AI models.
"""

import logging
import sys
import os
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

# Add the parent directory to sys.path to allow importing from backend
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import model provider utilities
from agents.utils.model_providers.utils import list_available_models, list_available_providers

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/models",
    tags=["models"],
    responses={404: {"description": "Not found"}}
)


class ModelInfo(BaseModel):
    """Model information model."""
    id: str
    name: str
    description: Optional[str] = None
    context_length: Optional[int] = None
    provider: str


class ProviderInfo(BaseModel):
    """Provider information model."""
    id: str
    name: str
    available: bool
    error: Optional[str] = None


class ModelsResponse(BaseModel):
    """Response model for models endpoint."""
    models: List[ModelInfo]


class ProvidersResponse(BaseModel):
    """Response model for providers endpoint."""
    providers: List[ProviderInfo]


@router.get("/providers", response_model=ProvidersResponse)
async def get_providers():
    """
    Get all available providers.
    """
    try:
        providers = await list_available_providers()
        return {"providers": providers}
    except Exception as e:
        logger.error(f"Error getting providers: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting providers: {str(e)}"
        )


@router.get("/providers/{provider_id}", response_model=ModelsResponse)
async def get_models_for_provider(provider_id: str):
    """
    Get all available models for a specific provider.
    """
    try:
        # Get all models
        models_by_provider = await list_available_models()
        
        # Check if the provider exists
        if provider_id.lower() not in models_by_provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Provider '{provider_id}' not found"
            )
        
        # Return models for the provider
        return {"models": models_by_provider[provider_id.lower()]}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting models for provider '{provider_id}': {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting models for provider '{provider_id}': {str(e)}"
        )


@router.get("/", response_model=Dict[str, List[ModelInfo]])
async def get_all_models():
    """
    Get all available models grouped by provider.
    """
    try:
        models_by_provider = await list_available_models()
        return models_by_provider
    except Exception as e:
        logger.error(f"Error getting models: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting models: {str(e)}"
        )
