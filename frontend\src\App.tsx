
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import "./styles/analysis-results.css";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useState } from "react";
import { ReactErrorBoundary } from "@/utils/react-error-suppression.tsx";
import { DashboardErrorBoundary } from "@/components/dashboard/DashboardErrorBoundary";
import Index from "./pages/Index";
import Home from "./pages/Home";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import GoogleCallback from "./pages/GoogleCallback";
import NotFound from "./pages/NotFound";
import IndustrySelection from "./pages/IndustrySelection";
import DataIntegration from "./pages/DataIntegration";
import DataChat from "./pages/DataChat";
import MainDashboard from "./pages/MainDashboard";
import AIMarketplace from "./pages/AIMarketplace";
import ReportsPage from "./pages/ReportsPage"; // Changed from Reports to ReportsPage
import Settings from "./pages/Settings";
import SubscriptionManagementPage from "./pages/SubscriptionManagementPage";
import SearchPage from "./pages/SearchPage"; // Added import
import Cart from "./pages/Cart";
import AdminDashboard from "./pages/admin/AdminDashboard";
import AdminPersonas from "./pages/admin/AdminPersonas";
import AdminUsers from "./pages/admin/AdminUsers";
import AdminAnalytics from "./pages/admin/AdminAnalytics";
import AdminActivityLogs from "./pages/admin/AdminActivityLogs";
import AdminSettings from "./pages/admin/AdminSettings";
import { AuthProvider } from "./contexts/AuthContext";
import { CartProvider } from "./contexts/CartContext";
import { ConciergeProvider } from "./contexts/ConciergeContext";
import { BusinessProfileProvider } from "./contexts/BusinessProfileContext";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import AdminProtectedRoute from "./components/auth/AdminProtectedRoute";
import PageStateManager from "./components/PageStateManager";
import TableDetectionTest from "./components/test/TableDetectionTest";
import InteractiveChartTest from "./components/visualizations/InteractiveChartTest";

const App = () => {
  // Create a client
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000, // 1 minute
        refetchOnWindowFocus: false,
      },
    },
  }));

  return (
    <ReactErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <ReactErrorBoundary>
              <AuthProvider>
                <BusinessProfileProvider>
                  <CartProvider>
                    <ConciergeProvider>
                    <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/home" element={
                  <ProtectedRoute>
                    <Home />
                  </ProtectedRoute>
                } />
                <Route path="/login" element={<Login />} />
                <Route path="/signup" element={<Signup />} />
                <Route path="/auth/google/callback" element={<GoogleCallback />} />
                <Route path="/industry-selection" element={
                  <ProtectedRoute>
                    <IndustrySelection />
                  </ProtectedRoute>
                } />
                <Route path="/data-integration" element={
                  <ProtectedRoute>
                    <DataIntegration />
                  </ProtectedRoute>
                } />
                <Route path="/data-chat" element={
                  <ProtectedRoute>
                    <DataChat />
                  </ProtectedRoute>
                } />
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <DashboardErrorBoundary>
                      <MainDashboard />
                    </DashboardErrorBoundary>
                  </ProtectedRoute>
                } />
                <Route path="/ai-marketplace" element={
                  <ProtectedRoute>
                    <AIMarketplace />
                  </ProtectedRoute>
                } />
                <Route path="/reports" element={
                  <ProtectedRoute>
                    <ReportsPage /> {/* Changed from Reports to ReportsPage */}
                  </ProtectedRoute>
                } />
                <Route path="/settings" element={
                  <ProtectedRoute>
                    <Settings />
                  </ProtectedRoute>
                } />
                <Route path="/settings/subscriptions" element={
                  <ProtectedRoute>
                    <SubscriptionManagementPage />
                  </ProtectedRoute>
                } />
                <Route path="/search" element={
                  <ProtectedRoute>
                    <SearchPage />
                  </ProtectedRoute>
                } />
                <Route path="/cart" element={
                  <ProtectedRoute>
                    <Cart />
                  </ProtectedRoute>
                } />
                {/* Test Routes */}
                <Route path="/test/table-detection" element={<TableDetectionTest />} />
                {/* Admin Routes */}
                <Route path="/admin" element={
                  <AdminProtectedRoute>
                    <AdminDashboard />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/personas" element={
                  <AdminProtectedRoute>
                    <AdminPersonas />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/users" element={
                  <AdminProtectedRoute>
                    <AdminUsers />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/analytics" element={
                  <AdminProtectedRoute>
                    <AdminAnalytics />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/activity" element={
                  <AdminProtectedRoute>
                    <AdminActivityLogs />
                  </AdminProtectedRoute>
                } />
                <Route path="/admin/settings" element={
                  <AdminProtectedRoute>
                    <AdminSettings />
                  </AdminProtectedRoute>
                } />

                {/* Test Routes */}
                <Route path="/test/interactive-charts" element={<InteractiveChartTest />} />

                <Route path="*" element={<NotFound />} />
                </Routes>
                    </ConciergeProvider>
                  </CartProvider>
                </BusinessProfileProvider>
              </AuthProvider>
            </ReactErrorBoundary>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </ReactErrorBoundary>
  );
};

export default App;
