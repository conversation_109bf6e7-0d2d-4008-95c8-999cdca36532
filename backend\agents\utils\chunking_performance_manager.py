"""
Performance manager for chunking operations.

This module provides performance optimization, monitoring, and caching
for chunking and embedding operations.
"""

import time
import logging
import async<PERSON>
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import yaml
import os
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics for chunking operations."""
    operation_type: str
    duration_ms: float
    memory_usage_mb: float
    chunk_count: int
    document_size: int
    embedding_time_ms: float
    processing_time_ms: float
    cache_hit_rate: float
    timestamp: float

class ChunkingPerformanceManager:
    """
    Performance manager for chunking and embedding operations.
    
    Features:
    - Performance monitoring and metrics collection
    - Adaptive configuration based on performance
    - Caching for embeddings and chunks
    - Batch processing optimization
    - Resource usage monitoring
    """
    
    def __init__(self, config_path: str = None):
        """Initialize the performance manager."""
        self.config_path = config_path or "backend/agents/configs/chunking_performance.yaml"
        self.config = self._load_config()
        self.metrics_history = []
        self.cache = {}
        self.embedding_cache = {}
        self.performance_profile = self.config.get("default_profile", "balanced")
        
        # Initialize thread pool for parallel processing
        max_workers = self.config.get("performance_profiles", {}).get(
            self.performance_profile, {}
        ).get("batch_processing", {}).get("parallel_workers", 3)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        logger.info(f"Initialized chunking performance manager with profile: {self.performance_profile}")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load performance configuration from YAML file."""
        try:
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                logger.info(f"Loaded performance configuration from {self.config_path}")
                return config
            else:
                logger.warning(f"Configuration file not found: {self.config_path}")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if file is not available."""
        return {
            "default_profile": "balanced",
            "performance_profiles": {
                "balanced": {
                    "embedding_model": "sentence-transformers/all-MiniLM-L6-v2",
                    "chunking_strategy": {
                        "chunk_size": 1000,
                        "chunk_overlap": 200,
                        "semantic_splitting": True
                    },
                    "batch_processing": {
                        "enabled": True,
                        "batch_size": 16,
                        "parallel_workers": 3
                    },
                    "caching": {
                        "enabled": True,
                        "cache_embeddings": True,
                        "cache_chunks": False
                    }
                }
            }
        }
    
    def get_optimal_config(self, content_type: str, document_size: int) -> Dict[str, Any]:
        """
        Get optimal configuration based on content type and document size.
        
        Args:
            content_type: Type of content being processed
            document_size: Size of the document in characters
            
        Returns:
            Optimal configuration dictionary
        """
        # Start with base profile configuration
        profile_config = self.config.get("performance_profiles", {}).get(
            self.performance_profile, {}
        )
        
        # Apply content-specific optimizations
        content_optimizations = self.config.get("content_optimizations", {})
        if content_type in content_optimizations:
            content_config = content_optimizations[content_type]
            
            # Use recommended profile for this content type
            if "profile" in content_config:
                profile_config = self.config.get("performance_profiles", {}).get(
                    content_config["profile"], profile_config
                )
            
            # Apply custom settings
            if "custom_settings" in content_config:
                profile_config = self._merge_configs(profile_config, content_config["custom_settings"])
        
        # Apply adaptive batching based on document size
        batch_rules = self.config.get("batch_processing", {}).get("adaptive_rules", [])
        for rule in batch_rules:
            condition = rule.get("condition", "")
            if self._evaluate_condition(condition, document_size):
                if "batch_processing" not in profile_config:
                    profile_config["batch_processing"] = {}
                profile_config["batch_processing"]["batch_size"] = rule.get("batch_size", 16)
                break
        
        return profile_config
    
    def _merge_configs(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """Merge configuration dictionaries."""
        merged = base_config.copy()
        for key, value in override_config.items():
            if isinstance(value, dict) and key in merged and isinstance(merged[key], dict):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value
        return merged
    
    def _evaluate_condition(self, condition: str, document_size: int) -> bool:
        """Evaluate a condition string for adaptive configuration."""
        try:
            # Simple condition evaluation (can be enhanced)
            condition = condition.replace("document_size", str(document_size))
            return eval(condition)
        except:
            return False
    
    async def process_chunks_batch(
        self, 
        chunks: List[str], 
        embedding_function,
        batch_size: int = None
    ) -> List[Any]:
        """
        Process chunks in batches for optimal performance.
        
        Args:
            chunks: List of text chunks to process
            embedding_function: Function to generate embeddings
            batch_size: Size of batches (uses config if None)
            
        Returns:
            List of processed results
        """
        if batch_size is None:
            batch_size = self.config.get("performance_profiles", {}).get(
                self.performance_profile, {}
            ).get("batch_processing", {}).get("batch_size", 16)
        
        start_time = time.time()
        results = []
        
        # Process chunks in batches
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]
            
            # Check cache first
            cached_results = []
            uncached_chunks = []
            
            if self._is_caching_enabled():
                for chunk in batch:
                    cache_key = self._get_cache_key(chunk)
                    if cache_key in self.embedding_cache:
                        cached_results.append(self.embedding_cache[cache_key])
                    else:
                        uncached_chunks.append(chunk)
            else:
                uncached_chunks = batch
            
            # Process uncached chunks
            if uncached_chunks:
                batch_results = await self._process_batch_parallel(uncached_chunks, embedding_function)
                
                # Cache results if caching is enabled
                if self._is_caching_enabled():
                    for chunk, result in zip(uncached_chunks, batch_results):
                        cache_key = self._get_cache_key(chunk)
                        self.embedding_cache[cache_key] = result
                
                results.extend(batch_results)
            
            # Add cached results
            results.extend(cached_results)
        
        processing_time = (time.time() - start_time) * 1000
        
        # Record metrics
        self._record_metrics(
            operation_type="batch_processing",
            duration_ms=processing_time,
            chunk_count=len(chunks),
            cache_hit_rate=len(results) - len([r for batch in chunks for r in batch if r not in self.embedding_cache]) / len(chunks) if chunks else 0
        )
        
        return results
    
    async def _process_batch_parallel(self, chunks: List[str], embedding_function) -> List[Any]:
        """Process a batch of chunks in parallel."""
        loop = asyncio.get_event_loop()
        
        # Submit tasks to thread pool
        futures = [
            loop.run_in_executor(self.executor, embedding_function, chunk)
            for chunk in chunks
        ]
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*futures, return_exceptions=True)
        
        # Filter out exceptions and log errors
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Error processing chunk {i}: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    def _is_caching_enabled(self) -> bool:
        """Check if caching is enabled for current profile."""
        return self.config.get("performance_profiles", {}).get(
            self.performance_profile, {}
        ).get("caching", {}).get("enabled", False)
    
    def _get_cache_key(self, chunk: str) -> str:
        """Generate cache key for a chunk."""
        import hashlib
        return hashlib.md5(chunk.encode()).hexdigest()
    
    def _record_metrics(
        self, 
        operation_type: str, 
        duration_ms: float, 
        chunk_count: int = 0,
        document_size: int = 0,
        cache_hit_rate: float = 0.0
    ):
        """Record performance metrics."""
        import psutil
        
        metrics = PerformanceMetrics(
            operation_type=operation_type,
            duration_ms=duration_ms,
            memory_usage_mb=psutil.Process().memory_info().rss / 1024 / 1024,
            chunk_count=chunk_count,
            document_size=document_size,
            embedding_time_ms=0,  # To be filled by specific operations
            processing_time_ms=duration_ms,
            cache_hit_rate=cache_hit_rate,
            timestamp=time.time()
        )
        
        self.metrics_history.append(metrics)
        
        # Keep only recent metrics (last 1000 entries)
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
        
        # Check performance thresholds
        self._check_performance_thresholds(metrics)
    
    def _check_performance_thresholds(self, metrics: PerformanceMetrics):
        """Check if performance metrics exceed thresholds."""
        thresholds = self.config.get("performance_thresholds", {})
        
        # Check processing time
        processing_threshold = thresholds.get("chunk_processing_time_ms", {})
        if metrics.processing_time_ms > processing_threshold.get("critical", 5000):
            logger.critical(f"Critical processing time: {metrics.processing_time_ms}ms")
        elif metrics.processing_time_ms > processing_threshold.get("warning", 2000):
            logger.warning(f"High processing time: {metrics.processing_time_ms}ms")
        
        # Check memory usage
        memory_threshold = thresholds.get("memory_usage_mb", {})
        if metrics.memory_usage_mb > memory_threshold.get("critical", 1024):
            logger.critical(f"Critical memory usage: {metrics.memory_usage_mb}MB")
        elif metrics.memory_usage_mb > memory_threshold.get("warning", 512):
            logger.warning(f"High memory usage: {metrics.memory_usage_mb}MB")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.metrics_history[-100:]  # Last 100 operations
        
        avg_duration = sum(m.duration_ms for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics)
        avg_cache_hit_rate = sum(m.cache_hit_rate for m in recent_metrics) / len(recent_metrics)
        
        return {
            "average_duration_ms": avg_duration,
            "average_memory_usage_mb": avg_memory,
            "average_cache_hit_rate": avg_cache_hit_rate,
            "total_operations": len(self.metrics_history),
            "current_profile": self.performance_profile,
            "cache_size": len(self.embedding_cache)
        }
    
    def optimize_profile(self):
        """Automatically optimize performance profile based on metrics."""
        if len(self.metrics_history) < 10:
            return  # Need more data
        
        recent_metrics = self.metrics_history[-50:]
        avg_duration = sum(m.duration_ms for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_usage_mb for m in recent_metrics) / len(recent_metrics)
        
        # Simple optimization logic
        if avg_duration > 3000:  # Slow performance
            if self.performance_profile != "speed_optimized":
                logger.info("Switching to speed_optimized profile due to slow performance")
                self.performance_profile = "speed_optimized"
        elif avg_memory > 800:  # High memory usage
            if self.performance_profile != "memory_optimized":
                logger.info("Switching to memory_optimized profile due to high memory usage")
                self.performance_profile = "memory_optimized"
        elif avg_duration < 1000 and avg_memory < 400:  # Good performance
            if self.performance_profile != "quality_optimized":
                logger.info("Switching to quality_optimized profile due to good performance")
                self.performance_profile = "quality_optimized"
    
    def clear_cache(self):
        """Clear all caches."""
        self.cache.clear()
        self.embedding_cache.clear()
        logger.info("Cleared all caches")
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
