"""
Agent Awareness Validation Script.

This script validates that previously enhanced tools match the agent identity patterns
from the marketing strategy generation tool and ensures consistency across all tools.
"""

import logging
import os
import ast
import re
from typing import Dict, Any, List, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class AgentAwarenessValidator:
    """
    Validator for checking agent awareness implementation consistency.
    
    This validator ensures that all tools follow the same patterns as the
    marketing strategy generation tool for agent identity integration.
    """
    
    def __init__(self, tools_directory: str = None):
        """Initialize the validator."""
        self.tools_directory = tools_directory or os.path.dirname(os.path.dirname(__file__))
        self.reference_patterns = self._extract_reference_patterns()
        self.validation_results = {}
    
    def _extract_reference_patterns(self) -> Dict[str, Any]:
        """Extract the reference patterns from marketing strategy generation tool."""
        return {
            "required_imports": [
                "from agents.utils.agent_identity import detect_agent_identity",
                "from agents.utils.system_prompts import get_agent_system_prompt"
            ],
            "agent_detection_pattern": r"agent_identity = await detect_agent_identity\(",
            "agent_logging_pattern": r"logger\.info\(f[\"']Detected agent identity: \{agent_identity\}",
            "agent_metadata_pattern": r'"agent_identity": agent_identity',
            "agent_aware_flag_pattern": r'"agent_aware": True',
            "context_extraction_patterns": [
                r"user_context = arguments\.get\([\"']context[\"'], \{\}\)",
                r"agent_id = arguments\.get\([\"']persona_id[\"']\) or arguments\.get\([\"']agent_id[\"']\)"
            ]
        }
    
    def validate_tool_file(self, file_path: str) -> Dict[str, Any]:
        """
        Validate a specific tool file against the reference patterns.
        
        Args:
            file_path: Path to the tool file
            
        Returns:
            Validation results
        """
        validation_result = {
            "file_path": file_path,
            "is_valid": True,
            "score": 0,
            "max_score": 10,
            "issues": [],
            "recommendations": [],
            "patterns_found": {},
            "patterns_missing": []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check required imports
            imports_score = self._check_imports(content, validation_result)
            
            # Check agent detection pattern
            detection_score = self._check_agent_detection(content, validation_result)
            
            # Check agent logging
            logging_score = self._check_agent_logging(content, validation_result)
            
            # Check agent metadata
            metadata_score = self._check_agent_metadata(content, validation_result)
            
            # Check context extraction
            context_score = self._check_context_extraction(content, validation_result)
            
            # Calculate total score
            validation_result["score"] = (
                imports_score + detection_score + logging_score + 
                metadata_score + context_score
            )
            
            # Determine if valid (score >= 8 out of 10)
            validation_result["is_valid"] = validation_result["score"] >= 8
            
            # Add overall recommendations
            if validation_result["score"] < 8:
                validation_result["recommendations"].append(
                    "Tool needs updates to match the marketing strategy generation pattern"
                )
            
        except Exception as e:
            validation_result["is_valid"] = False
            validation_result["issues"].append(f"Failed to validate file: {str(e)}")
        
        return validation_result
    
    def _check_imports(self, content: str, result: Dict[str, Any]) -> int:
        """Check for required imports."""
        score = 0
        max_score = 2
        
        for required_import in self.reference_patterns["required_imports"]:
            if required_import in content:
                score += 1
                result["patterns_found"][f"import_{required_import.split()[-1]}"] = True
            else:
                result["patterns_missing"].append(required_import)
                result["issues"].append(f"Missing import: {required_import}")
        
        if score < max_score:
            result["recommendations"].append("Add missing agent-related imports")
        
        return score
    
    def _check_agent_detection(self, content: str, result: Dict[str, Any]) -> int:
        """Check for agent detection pattern."""
        pattern = self.reference_patterns["agent_detection_pattern"]
        
        if re.search(pattern, content):
            result["patterns_found"]["agent_detection"] = True
            return 2
        else:
            result["patterns_missing"].append("agent_detection")
            result["issues"].append("Missing agent identity detection")
            result["recommendations"].append("Add agent identity detection using detect_agent_identity()")
            return 0
    
    def _check_agent_logging(self, content: str, result: Dict[str, Any]) -> int:
        """Check for agent logging pattern."""
        pattern = self.reference_patterns["agent_logging_pattern"]
        
        if re.search(pattern, content):
            result["patterns_found"]["agent_logging"] = True
            return 1
        else:
            result["patterns_missing"].append("agent_logging")
            result["issues"].append("Missing agent identity logging")
            result["recommendations"].append("Add logging for detected agent identity")
            return 0
    
    def _check_agent_metadata(self, content: str, result: Dict[str, Any]) -> int:
        """Check for agent metadata patterns."""
        score = 0
        max_score = 3
        
        # Check for agent_identity in metadata
        if re.search(self.reference_patterns["agent_metadata_pattern"], content):
            score += 2
            result["patterns_found"]["agent_metadata"] = True
        else:
            result["patterns_missing"].append("agent_metadata")
            result["issues"].append("Missing agent_identity in metadata")
        
        # Check for agent_aware flag
        if re.search(self.reference_patterns["agent_aware_flag_pattern"], content):
            score += 1
            result["patterns_found"]["agent_aware_flag"] = True
        else:
            result["patterns_missing"].append("agent_aware_flag")
            result["issues"].append("Missing agent_aware flag in metadata")
        
        if score < max_score:
            result["recommendations"].append("Add agent metadata to all responses")
        
        return score
    
    def _check_context_extraction(self, content: str, result: Dict[str, Any]) -> int:
        """Check for context extraction patterns."""
        score = 0
        max_score = 2
        
        for pattern in self.reference_patterns["context_extraction_patterns"]:
            if re.search(pattern, content):
                score += 1
                result["patterns_found"][f"context_extraction_{score}"] = True
            else:
                result["patterns_missing"].append(f"context_extraction_{len(result['patterns_missing'])}")
        
        if score < max_score:
            result["issues"].append("Missing proper context extraction")
            result["recommendations"].append("Add proper user_context and agent_id extraction")
        
        return score
    
    def validate_previously_enhanced_tools(self) -> Dict[str, Any]:
        """
        Validate the previously enhanced tools mentioned in the request.
        
        Returns:
            Validation results for all previously enhanced tools
        """
        tools_to_validate = [
            "text_processing.py",
            "statistical_analysis.py", 
            "intent_detection.py",
            "language_detection.py",
            "pandasai_analysis.py"
        ]
        
        validation_summary = {
            "total_tools": len(tools_to_validate),
            "valid_tools": 0,
            "invalid_tools": 0,
            "average_score": 0,
            "tool_results": {},
            "overall_issues": [],
            "overall_recommendations": []
        }
        
        total_score = 0
        
        for tool_file in tools_to_validate:
            file_path = os.path.join(self.tools_directory, tool_file)
            
            if os.path.exists(file_path):
                result = self.validate_tool_file(file_path)
                validation_summary["tool_results"][tool_file] = result
                
                if result["is_valid"]:
                    validation_summary["valid_tools"] += 1
                else:
                    validation_summary["invalid_tools"] += 1
                
                total_score += result["score"]
            else:
                validation_summary["tool_results"][tool_file] = {
                    "file_path": file_path,
                    "is_valid": False,
                    "score": 0,
                    "issues": ["File not found"],
                    "recommendations": ["Ensure file exists"]
                }
                validation_summary["invalid_tools"] += 1
        
        # Calculate average score
        if validation_summary["total_tools"] > 0:
            validation_summary["average_score"] = total_score / validation_summary["total_tools"]
        
        # Generate overall recommendations
        if validation_summary["invalid_tools"] > 0:
            validation_summary["overall_recommendations"].extend([
                "Update invalid tools to match marketing strategy generation pattern",
                "Ensure all tools have consistent agent identity integration",
                "Add missing imports and patterns as identified in individual tool results"
            ])
        
        # Generate overall issues summary
        all_issues = []
        for tool_result in validation_summary["tool_results"].values():
            all_issues.extend(tool_result.get("issues", []))
        
        # Count common issues
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        # Add most common issues to overall issues
        for issue, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True):
            if count > 1:
                validation_summary["overall_issues"].append(f"{issue} (affects {count} tools)")
        
        return validation_summary
    
    def generate_validation_report(self, validation_results: Dict[str, Any]) -> str:
        """
        Generate a formatted validation report.
        
        Args:
            validation_results: Results from validate_previously_enhanced_tools
            
        Returns:
            Formatted validation report
        """
        report = f"""
# Agent Awareness Validation Report

## Summary
- **Total Tools Validated**: {validation_results['total_tools']}
- **Valid Tools**: {validation_results['valid_tools']}
- **Invalid Tools**: {validation_results['invalid_tools']}
- **Average Score**: {validation_results['average_score']:.1f}/10

## Tool-by-Tool Results

"""
        
        for tool_name, result in validation_results["tool_results"].items():
            status = "✅ VALID" if result["is_valid"] else "❌ INVALID"
            score = result["score"]
            max_score = result.get("max_score", 10)
            
            report += f"### {tool_name} - {status} ({score}/{max_score})\n\n"
            
            if result.get("issues"):
                report += "**Issues:**\n"
                for issue in result["issues"]:
                    report += f"- {issue}\n"
                report += "\n"
            
            if result.get("recommendations"):
                report += "**Recommendations:**\n"
                for rec in result["recommendations"]:
                    report += f"- {rec}\n"
                report += "\n"
            
            if result.get("patterns_found"):
                report += "**Patterns Found:**\n"
                for pattern, found in result["patterns_found"].items():
                    report += f"- {pattern}: {'✅' if found else '❌'}\n"
                report += "\n"
        
        if validation_results["overall_issues"]:
            report += "## Common Issues\n\n"
            for issue in validation_results["overall_issues"]:
                report += f"- {issue}\n"
            report += "\n"
        
        if validation_results["overall_recommendations"]:
            report += "## Overall Recommendations\n\n"
            for rec in validation_results["overall_recommendations"]:
                report += f"- {rec}\n"
            report += "\n"
        
        report += """
## Next Steps

1. **Fix Invalid Tools**: Address the issues identified in invalid tools
2. **Standardize Patterns**: Ensure all tools follow the marketing strategy generation pattern
3. **Add Missing Components**: Implement missing imports, detection, and metadata patterns
4. **Test Integration**: Verify that agent identity detection works correctly
5. **Update Documentation**: Ensure all tools are documented with agent awareness capabilities

## Reference Pattern (Marketing Strategy Generation Tool)

The reference implementation includes:
- Import statements for agent utilities
- Agent identity detection in execute method
- Proper context extraction (user_context, agent_id)
- Agent identity logging
- Agent metadata in responses (agent_identity, agent_aware)
- Agent-specific processing logic

All tools should follow this consistent pattern for agent awareness integration.
"""
        
        return report
    
    def fix_tool_issues(self, tool_file: str, backup: bool = True) -> Dict[str, Any]:
        """
        Automatically fix common issues in a tool file.
        
        Args:
            tool_file: Tool file to fix
            backup: Whether to create a backup
            
        Returns:
            Fix results
        """
        file_path = os.path.join(self.tools_directory, tool_file)
        
        # First validate to identify issues
        validation_result = self.validate_tool_file(file_path)
        
        if validation_result["is_valid"]:
            return {
                "file_path": file_path,
                "fixes_applied": [],
                "success": True,
                "message": "Tool is already valid, no fixes needed"
            }
        
        fix_result = {
            "file_path": file_path,
            "fixes_applied": [],
            "success": False,
            "errors": []
        }
        
        try:
            # Read current content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Create backup if requested
            if backup:
                backup_path = f"{file_path}.backup"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fix_result["backup_created"] = backup_path
            
            modified_content = content
            
            # Fix missing imports
            for required_import in self.reference_patterns["required_imports"]:
                if required_import not in content:
                    # Add import after existing imports
                    import_line = f"{required_import}\n"
                    if "from agents.utils" in content:
                        # Add after existing agent imports
                        modified_content = re.sub(
                            r"(from agents\.utils\..*\n)",
                            f"\\1{import_line}",
                            modified_content,
                            count=1
                        )
                    else:
                        # Add after other imports
                        modified_content = re.sub(
                            r"(from \.base import BaseMCPTool\n)",
                            f"\\1{import_line}",
                            modified_content,
                            count=1
                        )
                    fix_result["fixes_applied"].append(f"Added import: {required_import}")
            
            # Add agent detection if missing
            if not re.search(self.reference_patterns["agent_detection_pattern"], content):
                agent_detection_code = '''
        # Agent context for dynamic identity detection
        user_context = arguments.get("context", {})
        agent_id = arguments.get("persona_id") or arguments.get("agent_id")
        
        # Detect agent identity for personalized processing
        agent_identity = await detect_agent_identity(
            agent_id=agent_id,
            context=user_context,
            intent_type=self.name
        )
        
        logger.info(f"Detected agent identity: {agent_identity} for {self.name}")
'''
                
                # Insert after try: in execute method
                modified_content = re.sub(
                    r"(async def execute\(self, arguments: Dict\[str, Any\]\) -> Dict\[str, Any\]:\s*.*?try:\s*)",
                    f"\\1{agent_detection_code}",
                    modified_content,
                    flags=re.DOTALL
                )
                fix_result["fixes_applied"].append("Added agent identity detection")
            
            # Add agent metadata if missing
            if not re.search(self.reference_patterns["agent_metadata_pattern"], content):
                # This is more complex and would require AST manipulation
                # For now, just add a note
                fix_result["fixes_applied"].append("Note: Agent metadata needs manual addition")
            
            # Write modified content
            if modified_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                fix_result["success"] = True
            
        except Exception as e:
            fix_result["errors"].append(str(e))
        
        return fix_result


# Global validator instance
agent_awareness_validator = AgentAwarenessValidator()


def validate_previously_enhanced_tools():
    """Validate the previously enhanced tools and generate a report."""
    validator = AgentAwarenessValidator()
    results = validator.validate_previously_enhanced_tools()
    report = validator.generate_validation_report(results)
    
    print(report)
    return results


if __name__ == "__main__":
    validate_previously_enhanced_tools()
