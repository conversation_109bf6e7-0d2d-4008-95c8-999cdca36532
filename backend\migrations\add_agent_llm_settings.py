"""
Add LLM provider and model settings for memory service and concierge agent.

This migration adds new columns to the users table to store LLM provider
and model preferences for the memory service and concierge agent.
"""

import logging
import sys
import os
from sqlalchemy import text

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import engine

logger = logging.getLogger(__name__)

def upgrade():
    """Add new columns for agent-specific LLM settings."""
    try:
        with engine.connect() as connection:
            # Add memory service LLM settings columns
            connection.execute(text("""
                ALTER TABLE users
                ADD COLUMN memory_service_provider VARCHAR(50) NULL
            """))

            connection.execute(text("""
                ALTER TABLE users
                ADD COLUMN memory_service_model VARCHAR(100) NULL
            """))

            # Add concierge agent LLM settings columns
            connection.execute(text("""
                ALTER TABLE users
                ADD COLUMN concierge_agent_provider VARCHAR(50) NULL
            """))

            connection.execute(text("""
                ALTER TABLE users
                ADD COLUMN concierge_agent_model VARCHAR(100) NULL
            """))

            connection.commit()
            logger.info("Successfully added agent LLM settings columns to users table")

    except Exception as e:
        logger.error(f"Error adding agent LLM settings columns: {e}")
        raise

def downgrade():
    """Remove the agent-specific LLM settings columns."""
    try:
        with engine.connect() as connection:
            # Remove the columns in reverse order
            connection.execute(text("""
                ALTER TABLE users
                DROP COLUMN concierge_agent_model
            """))

            connection.execute(text("""
                ALTER TABLE users
                DROP COLUMN concierge_agent_provider
            """))

            connection.execute(text("""
                ALTER TABLE users
                DROP COLUMN memory_service_model
            """))

            connection.execute(text("""
                ALTER TABLE users
                DROP COLUMN memory_service_provider
            """))

            connection.commit()
            logger.info("Successfully removed agent LLM settings columns from users table")

    except Exception as e:
        logger.error(f"Error removing agent LLM settings columns: {e}")
        raise

if __name__ == "__main__":
    # Run the migration
    upgrade()
