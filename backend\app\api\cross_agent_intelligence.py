"""
Cross-Agent Intelligence API endpoints.

This module provides REST API endpoints for managing cross-agent intelligence,
including insights, interactions, and collaboration features.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..models.auth import User
from ..models.shared_context import (
    AgentInsightCreate,
    AgentInsightResponse,
    AgentInteractionCreate,
    AgentInteractionResponse,
    SharedContextCreate,
    SharedContextResponse,
    CrossAgentContextRequest,
    CrossAgentContextResponse,
    CrossAgentIntelligenceStats,
    InsightSearchRequest,
    InteractionSearchRequest,
    BusinessProfileIntelligenceReport
)
from ..services.shared_context_repository import SharedContextRepository
from ..auth import get_current_active_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/cross-agent-intelligence", tags=["cross-agent-intelligence"])


def get_shared_context_repository(db: Session = Depends(get_db)) -> SharedContextRepository:
    """Dependency to get shared context repository."""
    return SharedContextRepository(db)


@router.post("/insights", response_model=AgentInsightResponse)
async def create_agent_insight(
    business_profile_id: str,
    insight_data: AgentInsightCreate,
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Create a new agent insight."""
    try:
        # Verify business profile ownership
        # TODO: Add business profile ownership verification
        
        insight = await repository.create_insight(business_profile_id, insight_data)
        return insight
    except Exception as e:
        logger.error(f"Error creating agent insight: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create agent insight"
        )


@router.get("/insights", response_model=List[AgentInsightResponse])
async def get_agent_insights(
    business_profile_id: str,
    agent_id: str,
    limit: int = Query(default=50, ge=1, le=200),
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Get insights relevant to a specific agent."""
    try:
        insights = await repository.get_insights_for_agent(business_profile_id, agent_id, limit)
        return insights
    except Exception as e:
        logger.error(f"Error getting agent insights: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent insights"
        )


@router.post("/insights/search", response_model=List[AgentInsightResponse])
async def search_insights(
    search_request: InsightSearchRequest,
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Search insights with advanced filters."""
    try:
        # TODO: Implement advanced insight search
        insights = await repository.get_insights_for_agent(
            search_request.business_profile_id, 
            "", 
            search_request.limit
        )
        return insights
    except Exception as e:
        logger.error(f"Error searching insights: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search insights"
        )


@router.post("/interactions", response_model=AgentInteractionResponse)
async def record_agent_interaction(
    business_profile_id: str,
    interaction_data: AgentInteractionCreate,
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Record a new agent interaction."""
    try:
        interaction = await repository.record_interaction(business_profile_id, interaction_data)
        return interaction
    except Exception as e:
        logger.error(f"Error recording agent interaction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record agent interaction"
        )


@router.get("/interactions", response_model=List[AgentInteractionResponse])
async def get_agent_interactions(
    business_profile_id: str,
    agent_id: Optional[str] = None,
    limit: int = Query(default=20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Get recent agent interactions."""
    try:
        interactions = await repository.get_recent_interactions(
            business_profile_id, exclude_agent_id=agent_id, limit=limit
        )
        return interactions
    except Exception as e:
        logger.error(f"Error getting agent interactions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent interactions"
        )


@router.post("/shared-contexts", response_model=SharedContextResponse)
async def create_shared_context(
    business_profile_id: str,
    context_data: SharedContextCreate,
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Create shared context between agents."""
    try:
        shared_context = await repository.create_shared_context(business_profile_id, context_data)
        return shared_context
    except Exception as e:
        logger.error(f"Error creating shared context: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create shared context"
        )


@router.get("/shared-contexts", response_model=List[SharedContextResponse])
async def get_shared_contexts(
    business_profile_id: str,
    agent_id: str,
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Get shared contexts for a specific agent."""
    try:
        contexts = await repository.get_shared_context_for_agent(business_profile_id, agent_id)
        return contexts
    except Exception as e:
        logger.error(f"Error getting shared contexts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get shared contexts"
        )


@router.post("/context", response_model=CrossAgentContextResponse)
async def get_cross_agent_context(
    context_request: CrossAgentContextRequest,
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Get comprehensive cross-agent context for an agent."""
    try:
        # Get insights
        insights = await repository.get_insights_for_agent(
            context_request.business_profile_id,
            context_request.agent_id,
            context_request.max_insights
        )
        
        # Get interactions
        interactions = await repository.get_recent_interactions(
            context_request.business_profile_id,
            exclude_agent_id=context_request.agent_id,
            limit=context_request.max_interactions
        )
        
        # Get shared contexts
        shared_contexts = await repository.get_shared_context_for_agent(
            context_request.business_profile_id,
            context_request.agent_id
        )
        
        # Build response
        response = CrossAgentContextResponse(
            insights=insights,
            interactions=interactions,
            shared_contexts=shared_contexts,
            collaboration_opportunities=[],  # TODO: Implement collaboration opportunities
            agent_capabilities={},  # TODO: Implement agent capabilities
            context_summary=f"Cross-agent context for {context_request.agent_id}: {len(insights)} insights, {len(interactions)} interactions"
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting cross-agent context: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get cross-agent context"
        )


@router.get("/stats/{business_profile_id}", response_model=CrossAgentIntelligenceStats)
async def get_intelligence_stats(
    business_profile_id: str,
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Get cross-agent intelligence statistics for a business profile."""
    try:
        activity_summary = await repository.get_agent_activity_summary(business_profile_id, days=30)
        
        stats = CrossAgentIntelligenceStats(
            business_profile_id=business_profile_id,
            total_insights=activity_summary.get("total_insights", 0),
            total_interactions=activity_summary.get("total_interactions", 0),
            active_agents=activity_summary.get("active_agents", 0),
            insight_types={},  # TODO: Implement insight type breakdown
            agent_activity=activity_summary.get("interactions_by_agent", {}),
            collaboration_count=0,  # TODO: Implement collaboration count
            average_insight_confidence=0.0,  # TODO: Implement average confidence
            most_active_agent=None,  # TODO: Implement most active agent
            most_valuable_insight_type=None  # TODO: Implement most valuable insight type
        )
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting intelligence stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get intelligence statistics"
        )


@router.get("/report/{business_profile_id}", response_model=BusinessProfileIntelligenceReport)
async def get_intelligence_report(
    business_profile_id: str,
    days: int = Query(default=30, ge=1, le=365),
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Get comprehensive intelligence report for a business profile."""
    try:
        # Get activity summary
        activity_summary = await repository.get_agent_activity_summary(business_profile_id, days)
        
        # Get top insights
        top_insights = await repository.get_insights_for_agent(business_profile_id, "", limit=10)
        
        # Build report
        report = BusinessProfileIntelligenceReport(
            business_profile_id=business_profile_id,
            report_period_days=days,
            agent_performance=[],  # TODO: Implement agent performance metrics
            top_insights=top_insights,
            collaboration_patterns={},  # TODO: Implement collaboration patterns
            knowledge_growth={},  # TODO: Implement knowledge growth metrics
            recommendations=[]  # TODO: Implement recommendations
        )
        
        return report
        
    except Exception as e:
        logger.error(f"Error generating intelligence report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate intelligence report"
        )


@router.post("/cleanup/{business_profile_id}")
async def cleanup_expired_data(
    business_profile_id: str,
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Clean up expired data for a business profile."""
    try:
        cleanup_stats = await repository.cleanup_expired_data(business_profile_id)
        return {
            "success": True,
            "message": "Cleanup completed successfully",
            "stats": cleanup_stats
        }
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup expired data"
        )


@router.put("/insights/{insight_id}/access")
async def update_insight_access(
    insight_id: str,
    current_user: User = Depends(get_current_active_user),
    repository: SharedContextRepository = Depends(get_shared_context_repository)
):
    """Update insight access tracking."""
    try:
        success = await repository.update_insight_access(insight_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Insight not found"
            )
        return {"success": True, "message": "Insight access updated"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating insight access: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update insight access"
        )
