"""
Component registration for the Datagenius backend.

This module registers components with the component registry.
"""

import logging
from .registry import ComponentRegistry
from .llm_processor import LLMProcessorComponent
from .data_retriever import DataRetrieverComponent
from .mcp_server import MCPServerComponent
from .task_selector import TaskSelectorComponent
from .enhanced_data_retriever import EnhancedDataRetrieverComponent
from .enhanced_llm_processor import EnhancedLLMProcessorComponent
from .marketing_request_parser import MarketingRequestParserComponent
from .data_analyzer import DataAnalyzerComponent
from .persona_recommender import PersonaRecommenderComponent
from .data_attachment_assistant import DataAttachmentAssistantComponent
from .context_manager import ContextManagerComponent
from .enhanced_context_manager import EnhancedContextManagerComponent
from .persona_routing import PersonaRoutingComponent
from .persona_coordinator import PersonaCoordinatorComponent
from .bidirectional_communication import BidirectionalCommunicationComponent
from .team_manager import Team<PERSON>anagerComponent
from .advanced_router import AdvancedRouterComponent
from .role_assignment import RoleAssignmentComponent
from .concierge_welcome import ConciergeWelcomeComponent
from .memory_manager import MemoryManagerComponent
from .conversation_history_manager import ConversationHistoryManagerComponent

# Import Phase 2 shared components
from .shared.shared_llm_processor import SharedLLMProcessor
from .shared.shared_data_retriever import SharedDataRetriever
from .shared.shared_context_manager import SharedContextManager
from .shared.cross_agent_intelligence import CrossAgentIntelligence
from .shared.agent_interaction_tracker import AgentInteractionTracker
from .shared.universal_context_injector import UniversalContextInjector

# Import concierge-specific components
from ..concierge_agent.components import ConciergeStateTrackerComponent

# Import components from their respective modules
from ..marketing_agent.components import (
    MarketingParserComponent,
    MCPContentGeneratorComponent
)

logger = logging.getLogger(__name__)


def register_components():
    """Register all components with the registry."""
    # Register LLM processor component
    ComponentRegistry.register("llm_processor", LLMProcessorComponent)
    logger.info("Registered LLMProcessorComponent with component registry")

    # Register enhanced LLM processor component
    ComponentRegistry.register("enhanced_llm_processor", EnhancedLLMProcessorComponent)
    logger.info("Registered EnhancedLLMProcessorComponent with component registry")

    # Register data retriever component
    ComponentRegistry.register("data_retriever", DataRetrieverComponent)
    logger.info("Registered DataRetrieverComponent with component registry")

    # Register enhanced data retriever component
    ComponentRegistry.register("enhanced_data_retriever", EnhancedDataRetrieverComponent)
    logger.info("Registered EnhancedDataRetrieverComponent with component registry")

    # Register task selector component
    ComponentRegistry.register("task_selector", TaskSelectorComponent)
    logger.info("Registered TaskSelectorComponent with component registry")

    # Register MCP server component
    ComponentRegistry.register("mcp_server", MCPServerComponent)
    logger.info("Registered MCPServerComponent with component registry")

    # Register marketing request parser component
    ComponentRegistry.register("marketing_request_parser", MarketingRequestParserComponent)
    logger.info("Registered MarketingRequestParserComponent with component registry")

    # Register marketing parser component
    ComponentRegistry.register("marketing_parser", MarketingParserComponent)
    logger.info("Registered MarketingParserComponent with component registry")

    # Register Phase 2 shared components
    ComponentRegistry.register("shared_llm_processor", SharedLLMProcessor)
    logger.info("Registered SharedLLMProcessor with component registry")

    ComponentRegistry.register("shared_data_retriever", SharedDataRetriever)
    logger.info("Registered SharedDataRetriever with component registry")

    ComponentRegistry.register("shared_context_manager", SharedContextManager)
    logger.info("Registered SharedContextManager with component registry")

    ComponentRegistry.register("cross_agent_intelligence", CrossAgentIntelligence)
    logger.info("Registered CrossAgentIntelligence with component registry")

    ComponentRegistry.register("agent_interaction_tracker", AgentInteractionTracker)
    logger.info("Registered AgentInteractionTracker with component registry")

    ComponentRegistry.register("universal_context_injector", UniversalContextInjector)
    logger.info("Registered UniversalContextInjector with component registry")

    # Log all registered components before adding the content generator
    logger.info(f"Components before adding marketing_content_generator: {ComponentRegistry.list_registered_components()}")

    # Register marketing content generator component
    ComponentRegistry.register("marketing_content_generator", MCPContentGeneratorComponent)
    logger.info("Registered MCPContentGeneratorComponent with component registry")

    # Register data analyzer component
    ComponentRegistry.register("data_analyzer", DataAnalyzerComponent)
    logger.info("Registered DataAnalyzerComponent with component registry")

    # Register persona recommender component
    ComponentRegistry.register("persona_recommender", PersonaRecommenderComponent)
    logger.info("Registered PersonaRecommenderComponent with component registry")

    # Register data attachment assistant component
    ComponentRegistry.register("data_attachment_assistant", DataAttachmentAssistantComponent)
    logger.info("Registered DataAttachmentAssistantComponent with component registry")

    # Register context manager component
    ComponentRegistry.register("context_manager", ContextManagerComponent)
    logger.info("Registered ContextManagerComponent with component registry")

    # Register persona routing component
    ComponentRegistry.register("persona_routing", PersonaRoutingComponent)
    logger.info("Registered PersonaRoutingComponent with component registry")

    # Register concierge welcome component
    ComponentRegistry.register("concierge_welcome", ConciergeWelcomeComponent)
    logger.info("Registered ConciergeWelcomeComponent with component registry")

    # Register concierge state tracker component
    ComponentRegistry.register("concierge_state_tracker", ConciergeStateTrackerComponent)
    logger.info("Registered ConciergeStateTrackerComponent with component registry")

    # Register enhanced context manager component
    ComponentRegistry.register("enhanced_context_manager", EnhancedContextManagerComponent)
    logger.info("Registered EnhancedContextManagerComponent with component registry")

    # Register persona coordinator component
    ComponentRegistry.register("persona_coordinator", PersonaCoordinatorComponent)
    logger.info("Registered PersonaCoordinatorComponent with component registry")

    # Register bidirectional communication component
    ComponentRegistry.register("bidirectional_communication", BidirectionalCommunicationComponent)
    logger.info("Registered BidirectionalCommunicationComponent with component registry")

    # Register team manager component
    ComponentRegistry.register("team_manager", TeamManagerComponent)
    logger.info("Registered TeamManagerComponent with component registry")

    # Register advanced router component
    ComponentRegistry.register("advanced_router", AdvancedRouterComponent)
    logger.info("Registered AdvancedRouterComponent with component registry")

    # Register role assignment component
    ComponentRegistry.register("role_assignment", RoleAssignmentComponent)
    logger.info("Registered RoleAssignmentComponent with component registry")

    # Register memory manager component
    ComponentRegistry.register("memory_manager", MemoryManagerComponent)
    logger.info("Registered MemoryManagerComponent with component registry")

    # Register conversation history manager component
    ComponentRegistry.register("conversation_history_manager", ConversationHistoryManagerComponent)
    logger.info("Registered ConversationHistoryManagerComponent with component registry")

    # Log all registered components after adding the content generator
    logger.info(f"Components after adding all components: {ComponentRegistry.list_registered_components()}")

    # Log the registered components
    component_names = ComponentRegistry.list_registered_components()
    logger.info(f"Registered components: {component_names}")
