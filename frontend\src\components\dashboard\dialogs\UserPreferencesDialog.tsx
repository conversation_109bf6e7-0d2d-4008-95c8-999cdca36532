/**
 * User Preferences Dialog
 * 
 * Dialog for managing user-specific dashboard preferences.
 * Features:
 * - Dashboard display preferences
 * - Notification settings
 * - Theme and appearance options
 * - Data refresh preferences
 * - Accessibility settings
 * - Export/import preferences
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  User,
  Bell,
  Palette,
  RefreshCw,
  Accessibility,
  Save,
  Download,
  Upload,
  Monitor,
  Sun,
  Moon,
  Volume2,
  VolumeX,
  Eye,
  Zap,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { useDashboardMode } from '@/stores/dashboard-mode-store';

interface UserPreferencesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface UserPreferences {
  display: {
    theme: 'light' | 'dark' | 'system';
    compactMode: boolean;
    showTooltips: boolean;
    animationsEnabled: boolean;
    highContrast: boolean;
    fontSize: 'small' | 'medium' | 'large';
  };
  notifications: {
    enabled: boolean;
    soundEnabled: boolean;
    dataUpdates: boolean;
    systemAlerts: boolean;
    emailNotifications: boolean;
    pushNotifications: boolean;
  };
  dashboard: {
    defaultMode: 'simple' | 'advanced';
    autoSave: boolean;
    confirmDelete: boolean;
    showGridLines: boolean;
    snapToGrid: boolean;
    defaultRefreshInterval: number;
  };
  accessibility: {
    screenReader: boolean;
    keyboardNavigation: boolean;
    reducedMotion: boolean;
    focusIndicators: boolean;
    altTextRequired: boolean;
  };
  performance: {
    enableCaching: boolean;
    lazyLoading: boolean;
    preloadData: boolean;
    maxCacheSize: number;
    compressionEnabled: boolean;
  };
}

const DEFAULT_PREFERENCES: UserPreferences = {
  display: {
    theme: 'system',
    compactMode: false,
    showTooltips: true,
    animationsEnabled: true,
    highContrast: false,
    fontSize: 'medium',
  },
  notifications: {
    enabled: true,
    soundEnabled: true,
    dataUpdates: true,
    systemAlerts: true,
    emailNotifications: false,
    pushNotifications: false,
  },
  dashboard: {
    defaultMode: 'simple',
    autoSave: true,
    confirmDelete: true,
    showGridLines: false,
    snapToGrid: true,
    defaultRefreshInterval: 300,
  },
  accessibility: {
    screenReader: false,
    keyboardNavigation: true,
    reducedMotion: false,
    focusIndicators: true,
    altTextRequired: false,
  },
  performance: {
    enableCaching: true,
    lazyLoading: true,
    preloadData: false,
    maxCacheSize: 100,
    compressionEnabled: true,
  },
};

export const UserPreferencesDialog: React.FC<UserPreferencesDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const { toast } = useToast();
  const { current_mode, setMode } = useDashboardMode();
  
  const [preferences, setPreferences] = useState<UserPreferences>(DEFAULT_PREFERENCES);
  const [activeTab, setActiveTab] = useState('display');
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Load user preferences
  useEffect(() => {
    if (open) {
      loadUserPreferences();
    }
  }, [open]);

  const loadUserPreferences = async () => {
    try {
      const response = await fetch('/api/user/preferences', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPreferences({ ...DEFAULT_PREFERENCES, ...data });
      } else {
        // Load from localStorage as fallback
        const savedPrefs = localStorage.getItem('userPreferences');
        if (savedPrefs) {
          setPreferences({ ...DEFAULT_PREFERENCES, ...JSON.parse(savedPrefs) });
        }
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
      // Load from localStorage as fallback
      const savedPrefs = localStorage.getItem('userPreferences');
      if (savedPrefs) {
        setPreferences({ ...DEFAULT_PREFERENCES, ...JSON.parse(savedPrefs) });
      }
    }
  };

  const updatePreference = (section: keyof UserPreferences, key: string, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleSavePreferences = async () => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/user/preferences', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(preferences),
      });

      if (response.ok) {
        // Also save to localStorage as backup
        localStorage.setItem('userPreferences', JSON.stringify(preferences));
        
        // Apply immediate changes
        if (preferences.dashboard.defaultMode !== current_mode) {
          setMode(preferences.dashboard.defaultMode);
        }

        setHasChanges(false);
        toast({
          title: "Preferences Saved",
          description: "Your preferences have been updated successfully.",
        });
      } else {
        throw new Error('Failed to save preferences');
      }
    } catch (error) {
      // Save to localStorage as fallback
      localStorage.setItem('userPreferences', JSON.stringify(preferences));
      
      toast({
        title: "Preferences Saved Locally",
        description: "Preferences saved locally. Server sync will retry later.",
        variant: "default",
      });
      
      setHasChanges(false);
    } finally {
      setIsSaving(false);
    }
  };

  const handleExportPreferences = () => {
    const prefsData = JSON.stringify(preferences, null, 2);
    const blob = new Blob([prefsData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'dashboard-preferences.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast({
      title: "Preferences Exported",
      description: "Your preferences have been exported.",
    });
  };

  const handleImportPreferences = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedPrefs = JSON.parse(e.target?.result as string);
        setPreferences({ ...DEFAULT_PREFERENCES, ...importedPrefs });
        setHasChanges(true);
        
        toast({
          title: "Preferences Imported",
          description: "Your preferences have been imported successfully.",
        });
      } catch (error) {
        toast({
          title: "Import Error",
          description: "Invalid preferences file format.",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);
  };

  const handleResetToDefaults = () => {
    setPreferences(DEFAULT_PREFERENCES);
    setHasChanges(true);
    
    toast({
      title: "Reset to Defaults",
      description: "All preferences have been reset to default values.",
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>User Preferences</span>
          </DialogTitle>
          <DialogDescription>
            Customize your dashboard experience and settings.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="display">Display</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <div className="max-h-96 overflow-y-auto">
            <TabsContent value="display" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Palette className="h-5 w-5" />
                    <span>Display Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Theme</Label>
                    <Select
                      value={preferences.display.theme}
                      onValueChange={(value: any) => updatePreference('display', 'theme', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">
                          <div className="flex items-center space-x-2">
                            <Sun className="h-4 w-4" />
                            <span>Light</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="dark">
                          <div className="flex items-center space-x-2">
                            <Moon className="h-4 w-4" />
                            <span>Dark</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="system">
                          <div className="flex items-center space-x-2">
                            <Monitor className="h-4 w-4" />
                            <span>System</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Font Size</Label>
                    <Select
                      value={preferences.display.fontSize}
                      onValueChange={(value: any) => updatePreference('display', 'fontSize', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="large">Large</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Compact Mode</p>
                      <p className="text-sm text-muted-foreground">
                        Reduce spacing and padding for more content
                      </p>
                    </div>
                    <Switch
                      checked={preferences.display.compactMode}
                      onCheckedChange={(checked) => updatePreference('display', 'compactMode', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Show Tooltips</p>
                      <p className="text-sm text-muted-foreground">
                        Display helpful tooltips on hover
                      </p>
                    </div>
                    <Switch
                      checked={preferences.display.showTooltips}
                      onCheckedChange={(checked) => updatePreference('display', 'showTooltips', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Animations</p>
                      <p className="text-sm text-muted-foreground">
                        Enable smooth animations and transitions
                      </p>
                    </div>
                    <Switch
                      checked={preferences.display.animationsEnabled}
                      onCheckedChange={(checked) => updatePreference('display', 'animationsEnabled', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">High Contrast</p>
                      <p className="text-sm text-muted-foreground">
                        Increase contrast for better visibility
                      </p>
                    </div>
                    <Switch
                      checked={preferences.display.highContrast}
                      onCheckedChange={(checked) => updatePreference('display', 'highContrast', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Bell className="h-5 w-5" />
                    <span>Notification Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Enable Notifications</p>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications for important events
                      </p>
                    </div>
                    <Switch
                      checked={preferences.notifications.enabled}
                      onCheckedChange={(checked) => updatePreference('notifications', 'enabled', checked)}
                    />
                  </div>

                  {preferences.notifications.enabled && (
                    <>
                      <Separator />
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Sound Notifications</p>
                          <p className="text-sm text-muted-foreground">
                            Play sounds for notifications
                          </p>
                        </div>
                        <Switch
                          checked={preferences.notifications.soundEnabled}
                          onCheckedChange={(checked) => updatePreference('notifications', 'soundEnabled', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Data Updates</p>
                          <p className="text-sm text-muted-foreground">
                            Notify when dashboard data is updated
                          </p>
                        </div>
                        <Switch
                          checked={preferences.notifications.dataUpdates}
                          onCheckedChange={(checked) => updatePreference('notifications', 'dataUpdates', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">System Alerts</p>
                          <p className="text-sm text-muted-foreground">
                            Notify about system status and errors
                          </p>
                        </div>
                        <Switch
                          checked={preferences.notifications.systemAlerts}
                          onCheckedChange={(checked) => updatePreference('notifications', 'systemAlerts', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Email Notifications</p>
                          <p className="text-sm text-muted-foreground">
                            Send notifications to your email
                          </p>
                        </div>
                        <Switch
                          checked={preferences.notifications.emailNotifications}
                          onCheckedChange={(checked) => updatePreference('notifications', 'emailNotifications', checked)}
                        />
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="dashboard" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <RefreshCw className="h-5 w-5" />
                    <span>Dashboard Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Default Mode</Label>
                    <Select
                      value={preferences.dashboard.defaultMode}
                      onValueChange={(value: any) => updatePreference('dashboard', 'defaultMode', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="simple">Simple Mode</SelectItem>
                        <SelectItem value="advanced">Advanced Mode</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Default Refresh Interval</Label>
                    <Select
                      value={preferences.dashboard.defaultRefreshInterval.toString()}
                      onValueChange={(value) => updatePreference('dashboard', 'defaultRefreshInterval', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="60">1 minute</SelectItem>
                        <SelectItem value="300">5 minutes</SelectItem>
                        <SelectItem value="600">10 minutes</SelectItem>
                        <SelectItem value="1800">30 minutes</SelectItem>
                        <SelectItem value="3600">1 hour</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Auto Save</p>
                      <p className="text-sm text-muted-foreground">
                        Automatically save changes as you work
                      </p>
                    </div>
                    <Switch
                      checked={preferences.dashboard.autoSave}
                      onCheckedChange={(checked) => updatePreference('dashboard', 'autoSave', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Confirm Delete</p>
                      <p className="text-sm text-muted-foreground">
                        Ask for confirmation before deleting items
                      </p>
                    </div>
                    <Switch
                      checked={preferences.dashboard.confirmDelete}
                      onCheckedChange={(checked) => updatePreference('dashboard', 'confirmDelete', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Show Grid Lines</p>
                      <p className="text-sm text-muted-foreground">
                        Display grid lines in edit mode
                      </p>
                    </div>
                    <Switch
                      checked={preferences.dashboard.showGridLines}
                      onCheckedChange={(checked) => updatePreference('dashboard', 'showGridLines', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Snap to Grid</p>
                      <p className="text-sm text-muted-foreground">
                        Snap widgets to grid when moving
                      </p>
                    </div>
                    <Switch
                      checked={preferences.dashboard.snapToGrid}
                      onCheckedChange={(checked) => updatePreference('dashboard', 'snapToGrid', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="accessibility" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Accessibility className="h-5 w-5" />
                    <span>Accessibility Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Screen Reader Support</p>
                      <p className="text-sm text-muted-foreground">
                        Optimize for screen reader compatibility
                      </p>
                    </div>
                    <Switch
                      checked={preferences.accessibility.screenReader}
                      onCheckedChange={(checked) => updatePreference('accessibility', 'screenReader', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Keyboard Navigation</p>
                      <p className="text-sm text-muted-foreground">
                        Enable full keyboard navigation support
                      </p>
                    </div>
                    <Switch
                      checked={preferences.accessibility.keyboardNavigation}
                      onCheckedChange={(checked) => updatePreference('accessibility', 'keyboardNavigation', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Reduced Motion</p>
                      <p className="text-sm text-muted-foreground">
                        Minimize animations and motion effects
                      </p>
                    </div>
                    <Switch
                      checked={preferences.accessibility.reducedMotion}
                      onCheckedChange={(checked) => updatePreference('accessibility', 'reducedMotion', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Focus Indicators</p>
                      <p className="text-sm text-muted-foreground">
                        Show clear focus indicators for navigation
                      </p>
                    </div>
                    <Switch
                      checked={preferences.accessibility.focusIndicators}
                      onCheckedChange={(checked) => updatePreference('accessibility', 'focusIndicators', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Zap className="h-5 w-5" />
                    <span>Performance Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Enable Caching</p>
                      <p className="text-sm text-muted-foreground">
                        Cache data to improve loading times
                      </p>
                    </div>
                    <Switch
                      checked={preferences.performance.enableCaching}
                      onCheckedChange={(checked) => updatePreference('performance', 'enableCaching', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Lazy Loading</p>
                      <p className="text-sm text-muted-foreground">
                        Load content only when needed
                      </p>
                    </div>
                    <Switch
                      checked={preferences.performance.lazyLoading}
                      onCheckedChange={(checked) => updatePreference('performance', 'lazyLoading', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Preload Data</p>
                      <p className="text-sm text-muted-foreground">
                        Preload data for faster navigation
                      </p>
                    </div>
                    <Switch
                      checked={preferences.performance.preloadData}
                      onCheckedChange={(checked) => updatePreference('performance', 'preloadData', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Compression</p>
                      <p className="text-sm text-muted-foreground">
                        Compress data transfers to save bandwidth
                      </p>
                    </div>
                    <Switch
                      checked={preferences.performance.compressionEnabled}
                      onCheckedChange={(checked) => updatePreference('performance', 'compressionEnabled', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleExportPreferences}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <div>
              <Input
                type="file"
                accept=".json"
                onChange={handleImportPreferences}
                className="hidden"
                id="prefs-import"
              />
              <Label htmlFor="prefs-import">
                <Button variant="outline" asChild>
                  <span>
                    <Upload className="h-4 w-4 mr-2" />
                    Import
                  </span>
                </Button>
              </Label>
            </div>
            <Button variant="outline" onClick={handleResetToDefaults}>
              Reset
            </Button>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSavePreferences}
              disabled={!hasChanges || isSaving}
              className="flex items-center space-x-2"
            >
              {isSaving ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  <span>Save Preferences</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
