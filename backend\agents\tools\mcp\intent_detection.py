"""
Enhanced Intent Detection MCP Tool for Datagenius.

This module provides a comprehensive, agent-aware MCP-compatible tool for detecting
user intent in messages with dynamic agent identity integration, robust error handling,
and extensible configuration.
"""

import logging
import json
import yaml
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
from pydantic import BaseModel, Field, ValidationError

from .base import BaseMCPTool
from agents.utils.model_providers.utils import get_model
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class IntentDetectionConfig(BaseModel):
    """Configuration model for intent detection tool."""

    # Core settings
    enable_agent_awareness: bool = Field(default=True, description="Enable agent-specific intent detection")
    enable_llm_fallback: bool = Field(default=True, description="Enable LLM fallback for complex cases")
    simple_case_threshold: int = Field(default=50, ge=10, le=200, description="Character threshold for simple cases")

    # LLM settings
    default_provider: str = Field(default="groq", description="Default AI provider")
    default_model: str = Field(default="llama-3.1-8b-instant", description="Default AI model")
    llm_timeout: int = Field(default=30, ge=5, le=120, description="LLM timeout in seconds")
    max_retries: int = Field(default=3, ge=1, le=5, description="Maximum retry attempts")

    # Intent categories
    supported_intents: List[str] = Field(
        default=[
            "greeting", "question", "request", "content_generation",
            "marketing_advice", "data_analysis", "classification",
            "follow_up", "conversational"
        ],
        description="Supported intent categories"
    )

    # Confidence thresholds
    high_confidence_threshold: float = Field(default=0.8, ge=0.5, le=1.0)
    medium_confidence_threshold: float = Field(default=0.6, ge=0.3, le=0.9)

    # Agent-specific settings
    agent_contexts: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Agent-specific context configurations"
    )

    # Performance settings
    enable_caching: bool = Field(default=True, description="Enable result caching")
    cache_ttl: int = Field(default=300, ge=60, le=3600, description="Cache TTL in seconds")

    # Validation settings
    validate_inputs: bool = Field(default=True, description="Enable input validation")
    sanitize_inputs: bool = Field(default=True, description="Enable input sanitization")

    class Config:
        env_prefix = "INTENT_DETECTION_"


class IntentDetectionTool(BaseMCPTool):
    """Tool for detecting user intent in messages."""

    def __init__(self):
        """Initialize the intent detection tool."""
        super().__init__(
            name="detect_intent",
            description="Detect the intent of a user message",
            input_schema={
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "The user's message to analyze"
                    },
                    "context": {
                        "type": "object",
                        "description": "Additional context for intent detection"
                    },
                    "provider": {
                        "type": "string",
                        "description": "AI provider to use for detection"
                    },
                    "model": {
                        "type": "string",
                        "description": "AI model to use for detection"
                    }
                },
                "required": ["message"]
            },
            annotations={
                "title": "Detect Intent",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the intent detection tool.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            message = arguments["message"]
            context = arguments.get("context", {})
            provider_id = arguments.get("provider", "groq")
            model_name = arguments.get("model", "llama-3.1-8b-instant")

            # Simple rule-based detection for common cases
            if self._is_simple_case(message):
                intent_result = self._detect_simple_intent(message, context)
                return {
                    "isError": False,
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(intent_result)
                        }
                    ]
                }

            # Detect agent identity for context-aware intent detection
            agent_id = arguments.get("persona_id") or arguments.get("agent_id")
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=context,
                intent_type="intent_detection"
            )

            logger.info(f"Detected agent identity: {agent_identity} for intent detection")

            # Use LLM for complex detection with agent awareness
            try:
                model = get_model(provider_id, model_name)
                if not model:
                    raise Exception(f"Model {model_name} not available for provider {provider_id}")

                # Create agent-aware intent detection prompt
                prompt = await self._create_agent_aware_intent_prompt(message, context, agent_identity)

                response = await model.ainvoke(prompt)
                response_text = response.content if hasattr(response, 'content') else str(response)

                # Try to extract JSON from response
                try:
                    # Look for JSON in the response
                    import re
                    json_match = re.search(r'\{[^}]+\}', response_text, re.DOTALL)
                    if json_match:
                        result = json.loads(json_match.group())
                        return {
                            "isError": False,
                            "content": [
                                {
                                    "type": "text",
                                    "text": json.dumps(result)
                                }
                            ]
                        }
                except json.JSONDecodeError:
                    pass

                # Fallback if JSON parsing fails
                fallback_result = self._detect_simple_intent(message, context)
                fallback_result["method"] = "fallback"
                return {
                    "isError": False,
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(fallback_result)
                        }
                    ]
                }

            except Exception as e:
                logger.error(f"LLM intent detection failed: {e}")
                # Fallback to simple detection
                fallback_result = self._detect_simple_intent(message, context)
                fallback_result["method"] = "fallback"
                return {
                    "isError": False,
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(fallback_result)
                        }
                    ]
                }

        except Exception as e:
            logger.error(f"Intent detection failed: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Intent detection failed: {str(e)}"
                    }
                ]
            }

    def _is_simple_case(self, message: str) -> bool:
        """Check if this is a simple case that can be handled with rules."""
        return len(message.strip()) < 50

    def _detect_simple_intent(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Simple rule-based intent detection."""
        message_lower = message.lower().strip()
        
        # Greeting patterns
        if message_lower in ["hello", "hi", "hey", "start"] or len(message_lower) < 10:
            return {
                "intent_type": "greeting",
                "confidence": 0.9,
                "reasoning": "Short greeting or simple message",
                "requires_content_generation": False,
                "is_conversational": True,
                "method": "rule_based"
            }
        
        # Content generation patterns
        content_patterns = [
            "generate", "create", "develop", "build", "write", "make",
            "marketing strategy", "campaign", "social media", "seo"
        ]
        
        if any(pattern in message_lower for pattern in content_patterns):
            return {
                "intent_type": "content_generation",
                "confidence": 0.8,
                "reasoning": "Contains explicit content generation keywords",
                "requires_content_generation": True,
                "is_conversational": False,
                "method": "rule_based"
            }
        
        # Question patterns
        question_patterns = ["what", "how", "why", "when", "where", "can you", "?"]
        
        if any(pattern in message_lower for pattern in question_patterns):
            return {
                "intent_type": "question",
                "confidence": 0.7,
                "reasoning": "Contains question indicators",
                "requires_content_generation": False,
                "is_conversational": True,
                "method": "rule_based"
            }
        
        # Conversational patterns
        conversational_patterns = [
            "thanks", "thank you", "ok", "okay", "good", "great",
            "yes", "no", "sure"
        ]
        
        if any(pattern in message_lower for pattern in conversational_patterns):
            return {
                "intent_type": "conversational",
                "confidence": 0.7,
                "reasoning": "Contains conversational response patterns",
                "requires_content_generation": False,
                "is_conversational": True,
                "method": "rule_based"
            }
        
        # Default to conversational
        return {
            "intent_type": "conversational",
            "confidence": 0.5,
            "reasoning": "Default classification for unclear intent",
            "requires_content_generation": False,
            "is_conversational": True,
            "method": "rule_based"
        }

    async def _create_agent_aware_intent_prompt(
        self,
        message: str,
        context: Dict[str, Any],
        agent_identity: str
    ) -> str:
        """
        Create an agent-aware intent detection prompt.

        Args:
            message: User message to analyze
            context: User context
            agent_identity: Detected agent identity

        Returns:
            Agent-aware intent detection prompt
        """
        # Get agent-specific context for intent detection
        agent_context = self._get_agent_intent_context(agent_identity)

        prompt = f"""You are an intelligent intent detection system for a {agent_identity} agent.

AGENT CONTEXT:
{agent_context}

Analyze this message and detect the user's intent. Consider the agent context when determining intent.

Message: "{message}"
Context: {json.dumps(context, indent=2)}

Respond with this exact JSON format:
{{
    "intent_type": "one of: greeting, question, request, content_generation, marketing_advice, data_analysis, classification, follow_up, conversational",
    "confidence": confidence_score_between_0_and_1,
    "reasoning": "brief_explanation_of_your_analysis",
    "requires_content_generation": boolean_true_if_user_wants_content_created,
    "is_conversational": boolean_true_if_this_is_casual_conversation,
    "agent_specific_intent": "any_agent_specific_intent_detected",
    "method": "llm_based"
}}"""

        return prompt

    async def _get_agent_intent_context(self, agent_identity: str) -> str:
        """Get agent-specific context for intent detection using system prompt capabilities."""
        try:
            # Get the agent's system prompt to extract capabilities
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract capabilities from system prompt
            capabilities = await self._extract_capabilities_from_prompt(system_prompt)

            # Create context based on extracted capabilities
            if capabilities:
                capability_context = self._create_capability_based_context(agent_identity, capabilities)
                return capability_context

        except Exception as e:
            logger.warning(f"Failed to get agent system prompt for {agent_identity}: {e}")

        # Fallback to static contexts if dynamic extraction fails
        return self._get_fallback_agent_context(agent_identity)

    async def _extract_capabilities_from_prompt(self, system_prompt: str) -> List[str]:
        """Extract capabilities from agent system prompt."""
        capabilities = []

        if not system_prompt:
            return capabilities

        # Look for capability sections in the prompt
        capability_patterns = [
            r"Your capabilities include:\s*\n((?:- .+\n?)+)",
            r"capabilities:\s*\n((?:- .+\n?)+)",
            r"You can:\s*\n((?:- .+\n?)+)",
            r"I can help with:\s*\n((?:- .+\n?)+)"
        ]

        import re
        for pattern in capability_patterns:
            match = re.search(pattern, system_prompt, re.IGNORECASE | re.MULTILINE)
            if match:
                capability_text = match.group(1)
                # Extract individual capabilities
                for line in capability_text.split('\n'):
                    line = line.strip()
                    if line.startswith('- '):
                        capability = line[2:].strip()
                        if capability:
                            capabilities.append(capability)
                break

        return capabilities

    def _create_capability_based_context(self, agent_identity: str, capabilities: List[str]) -> str:
        """Create intent detection context based on agent capabilities."""
        capability_list = '\n'.join([f"- {cap}" for cap in capabilities])

        return f"""
You are analyzing intents for a {agent_identity} agent with the following capabilities:
{capability_list}

When detecting intent, prioritize requests that align with these capabilities:
- Look for keywords and phrases that match the agent's specialized skills
- Consider the agent's domain expertise when classifying ambiguous requests
- Assign higher confidence scores to intents that match the agent's core capabilities
- For requests outside the agent's expertise, suggest routing to appropriate specialists

Pay special attention to intent patterns that leverage this agent's unique strengths.
        """

    def _get_fallback_agent_context(self, agent_identity: str) -> str:
        """Fallback agent context when dynamic extraction fails."""
        contexts = {
            "marketer": """
You are analyzing intents for a marketing agent. Pay special attention to:
- Content creation requests (marketing strategies, campaigns, social media)
- Marketing advice and consultation requests
- Brand and audience-related questions
- Campaign planning and optimization requests
            """,
            "analyst": """
You are analyzing intents for a data analyst agent. Pay special attention to:
- Data analysis and visualization requests
- Statistical analysis and insights requests
- Data exploration and querying needs
- Reporting and dashboard creation requests
            """,
            "classifier": """
You are analyzing intents for a classification agent. Pay special attention to:
- Text classification and categorization requests
- Data organization and structuring needs
- Content tagging and labeling requests
- Pattern recognition and grouping tasks
            """,
            "concierge": """
You are analyzing intents for a concierge agent. Pay special attention to:
- General assistance and guidance requests
- Navigation and help-seeking behavior
- Persona recommendation needs
- General questions and conversational interactions
            """
        }

        return contexts.get(agent_identity, "You are analyzing intents for a general AI assistant.")
