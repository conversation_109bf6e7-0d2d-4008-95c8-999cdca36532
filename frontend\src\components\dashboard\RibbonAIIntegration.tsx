/**
 * Ribbon AI Integration
 * 
 * Enhanced AI Assistant integration specifically for ribbon menu actions.
 * Features:
 * - Context-aware AI responses based on ribbon action
 * - Mode-specific AI behavior (Simple vs Advanced)
 * - Conversational widget creation
 * - Dashboard optimization suggestions
 * - Natural language data analysis
 * - Code generation for advanced users
 */

import React, { useState, useCallback, useEffect } from 'react';
import { FloatingAIAssistant } from './FloatingAIAssistant';
import { useDashboardMode } from '@/stores/dashboard-mode-store';
import { useUnifiedDashboardStore } from '@/stores/unified-dashboard-store';
import { useToast } from '@/hooks/use-toast';

interface RibbonAIIntegrationProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  initialContext?: AIContext;
  onWidgetCreate?: (widget_config: any) => void;
  onSectionCreate?: (section_config: any) => void;
  onTemplateApply?: (template_id: string) => void;
  onDataConfigure?: (data_config: any) => void;
  onDashboardOptimize?: (optimization_params: any) => void;
}

interface AIContext {
  action: 'create_widget' | 'add_section' | 'apply_template' | 'configure_data' | 'optimize_dashboard' | 'analyze_data' | 'general';
  mode: 'simple' | 'advanced';
  data?: any;
  prompt?: string;
}

interface AIResponse {
  content: string;
  suggestions?: string[];
  actions?: any[];
  code?: string;
  config?: any;
}

export const RibbonAIIntegration: React.FC<RibbonAIIntegrationProps> = ({
  isOpen,
  onOpenChange,
  initialContext,
  onWidgetCreate,
  onSectionCreate,
  onTemplateApply,
  onDataConfigure,
  onDashboardOptimize,
}) => {
  const { toast } = useToast();
  const { current_mode } = useDashboardMode();
  const { currentLayout } = useUnifiedDashboardStore();
  
  const [aiContext, setAiContext] = useState<AIContext | null>(initialContext || null);

  // Update context when initialContext changes
  useEffect(() => {
    if (initialContext) {
      setAiContext(initialContext);
    }
  }, [initialContext]);

  // Enhanced AI response handler that considers ribbon context
  const handleAIResponse = useCallback(async (message: string): Promise<AIResponse> => {
    try {
      const contextualPrompt = buildContextualPrompt(message, aiContext, current_mode, currentLayout);
      
      // Call the actual AI agent (replace with real implementation)
      const response = await callComposableAnalysisAgent(contextualPrompt);
      
      return response;
    } catch (error) {
      console.error('AI Response Error:', error);
      return {
        content: "I apologize, but I encountered an error processing your request. Please try again.",
        suggestions: ["Try rephrasing your request", "Check your connection", "Contact support"],
      };
    }
  }, [aiContext, current_mode, currentLayout]);

  // Build contextual prompt based on ribbon action and mode
  const buildContextualPrompt = (
    userMessage: string,
    context: AIContext | null,
    mode: string,
    layout: any
  ): string => {
    let systemPrompt = '';
    
    // Base system prompt based on mode
    if (mode === 'simple') {
      systemPrompt = `You are a friendly AI assistant helping users create dashboards easily. 
      Use simple language, provide step-by-step guidance, and focus on visual results.
      Avoid technical jargon and complex concepts.`;
    } else {
      systemPrompt = `You are an advanced AI development assistant for dashboard creation.
      You can provide technical details, code examples, SQL queries, and advanced configurations.
      Users expect detailed technical responses and code snippets.`;
    }

    // Add context-specific instructions
    if (context) {
      switch (context.action) {
        case 'create_widget':
          systemPrompt += `\n\nThe user wants to create a widget. Help them choose the right visualization type,
          configure data sources, and customize appearance. ${mode === 'advanced' ? 'Provide code examples if needed.' : 'Keep it simple and visual.'}`;
          break;
        case 'add_section':
          systemPrompt += `\n\nThe user wants to add a new section to their dashboard. Help them organize content,
          choose layouts, and structure their dashboard effectively.`;
          break;
        case 'optimize_dashboard':
          systemPrompt += `\n\nThe user wants to optimize their dashboard. Analyze performance, suggest improvements,
          and provide actionable recommendations for better user experience.`;
          break;
        case 'analyze_data':
          systemPrompt += `\n\nThe user wants to analyze their data. Help them understand patterns, create insights,
          and suggest appropriate visualizations for their data.`;
          break;
      }
    }

    // Add current dashboard context
    if (layout?.dashboard) {
      systemPrompt += `\n\nCurrent dashboard context:
      - Name: ${layout.dashboard.name}
      - Sections: ${layout.sections?.length || 0}
      - Widgets: ${layout.widgets?.length || 0}
      - Theme: ${layout.dashboard.theme_config?.theme_name || 'default'}`;
    }

    return `${systemPrompt}\n\nUser message: ${userMessage}`;
  };

  // Call the actual AI agent (integrate with backend)
  const callComposableAnalysisAgent = async (prompt: string): Promise<AIResponse> => {
    try {
      const response = await fetch('/api/agents/composable-analysis/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          message: prompt,
          context: {
            mode: current_mode,
            dashboard_id: currentLayout?.dashboard?.id,
            action: aiContext?.action,
          },
        }),
      });

      if (!response.ok) {
        throw new Error('AI agent request failed');
      }

      const data = await response.json();
      
      return {
        content: data.response || data.message,
        suggestions: data.suggestions,
        actions: data.actions,
        code: data.code,
        config: data.config,
      };
    } catch (error) {
      // Fallback to simulated response
      return simulateContextualAIResponse(prompt, aiContext, current_mode);
    }
  };

  // Enhanced widget creation handler
  const handleWidgetCreate = useCallback((widget_config: any) => {
    // Add AI-generated metadata
    const enhancedConfig = {
      ...widget_config,
      created_by: 'ai_assistant',
      creation_context: aiContext?.action,
      ai_suggestions: true,
    };

    onWidgetCreate?.(enhancedConfig);
    
    toast({
      title: "Widget Created",
      description: "AI has created your widget. You can customize it further if needed.",
    });
  }, [aiContext, onWidgetCreate, toast]);

  // Enhanced section creation handler
  const handleSectionCreate = useCallback((section_config: any) => {
    const enhancedConfig = {
      ...section_config,
      created_by: 'ai_assistant',
      creation_context: aiContext?.action,
    };

    onSectionCreate?.(enhancedConfig);
    
    toast({
      title: "Section Added",
      description: "AI has added a new section to your dashboard.",
    });
  }, [aiContext, onSectionCreate, toast]);

  // Dashboard optimization handler
  const handleDashboardOptimize = useCallback((optimization_params: any) => {
    onDashboardOptimize?.(optimization_params);
    
    toast({
      title: "Dashboard Optimized",
      description: "AI has applied optimization suggestions to improve performance.",
    });
  }, [onDashboardOptimize, toast]);

  // Data analysis handler
  const handleDataAnalyze = useCallback(async (analysis_request: any) => {
    try {
      const response = await fetch('/api/agents/composable-analysis/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          ...analysis_request,
          dashboard_id: currentLayout?.dashboard?.id,
        }),
      });

      if (response.ok) {
        const analysisResult = await response.json();
        
        toast({
          title: "Analysis Complete",
          description: "AI has analyzed your data and generated insights.",
        });

        // You could trigger additional actions here like creating visualizations
        // based on the analysis results
      }
    } catch (error) {
      toast({
        title: "Analysis Failed",
        description: "Failed to analyze data. Please try again.",
        variant: "destructive",
      });
    }
  }, [currentLayout, toast]);

  // Code generation handler for advanced mode
  const handleCodeGenerate = useCallback((code_request: any) => {
    if (current_mode === 'advanced') {
      // Handle code generation for advanced users
      toast({
        title: "Code Generated",
        description: "AI has generated code for your request. Check the code panel.",
      });
    }
  }, [current_mode, toast]);

  return (
    <FloatingAIAssistant
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      onWidgetCreate={handleWidgetCreate}
      onSectionCreate={handleSectionCreate}
      onTemplateApply={onTemplateApply}
      onDataConfigure={onDataConfigure}
      onDataAnalyze={handleDataAnalyze}
      onDashboardOptimize={handleDashboardOptimize}
      onCodeGenerate={handleCodeGenerate}
    />
  );
};

// Simulated contextual AI response (fallback)
function simulateContextualAIResponse(
  prompt: string,
  context: AIContext | null,
  mode: string
): AIResponse {
  const isSimpleMode = mode === 'simple';
  
  if (context?.action === 'create_widget') {
    return {
      content: isSimpleMode
        ? "I'd love to help you create a widget! What kind of data do you want to visualize? I can create charts, tables, metrics, and more."
        : "I can help you create a custom widget with advanced configurations. What type of visualization do you need? I can provide code examples and technical specifications.",
      suggestions: isSimpleMode
        ? ["Create a bar chart", "Add a pie chart", "Make a metric card", "Show me options"]
        : ["Custom SQL chart", "Advanced time series", "Interactive visualization", "Code example"],
      actions: [
        {
          id: 'create_chart',
          label: 'Create Chart Widget',
          type: 'create_widget',
          data: { type: 'chart', subtype: 'bar' },
        },
      ],
    };
  }

  if (context?.action === 'optimize_dashboard') {
    return {
      content: isSimpleMode
        ? "I can help make your dashboard faster and more user-friendly! Let me analyze your current setup and suggest improvements."
        : "I'll analyze your dashboard performance, query efficiency, and user experience to provide optimization recommendations.",
      suggestions: isSimpleMode
        ? ["Speed up loading", "Improve layout", "Better colors", "Mobile friendly"]
        : ["Query optimization", "Caching strategy", "Performance metrics", "Code review"],
      actions: [
        {
          id: 'analyze_performance',
          label: 'Analyze Performance',
          type: 'optimize_dashboard',
          data: { analysis_type: 'performance' },
        },
      ],
    };
  }

  return {
    content: isSimpleMode
      ? "Hi! I'm here to help you build amazing dashboards. What would you like to create today?"
      : "Ready to assist with advanced dashboard development. What technical challenge can I help you solve?",
    suggestions: isSimpleMode
      ? ["Create a widget", "Add a section", "Optimize dashboard", "Analyze data"]
      : ["Generate SQL", "Custom component", "Performance tuning", "API integration"],
  };
}

// Hook for easy ribbon AI integration
export const useRibbonAI = () => {
  const [isAIOpen, setIsAIOpen] = useState(false);
  const [aiContext, setAiContext] = useState<AIContext | null>(null);

  const openAIWithContext = useCallback((context: AIContext) => {
    setAiContext(context);
    setIsAIOpen(true);
  }, []);

  const closeAI = useCallback(() => {
    setIsAIOpen(false);
    setAiContext(null);
  }, []);

  return {
    isAIOpen,
    aiContext,
    openAIWithContext,
    closeAI,
    setIsAIOpen,
  };
};
