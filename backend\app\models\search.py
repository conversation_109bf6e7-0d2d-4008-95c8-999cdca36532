"""
Search related SQLAlchemy models and Pydantic schemas.
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field

from ..database import Base

# SQLAlchemy Model
class UserSearchActivity(Base):
    __tablename__ = "user_search_activities"

    id = Column(String(36), primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True) # Nullable for anonymous users
    session_id = Column(String(36), nullable=True, index=True) # For anonymous or session-based tracking
    query = Column(String(500), nullable=False, index=True)
    filters_applied = Column(JSON, nullable=True)
    result_count = Column(Integer, nullable=True)
    clicked_result_ids = Column(JSON, nullable=True) # List of IDs of results clicked
    search_timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    user_ip = Column(String(45), nullable=True)
    user_agent = Column(String(255), nullable=True)

    user = relationship("User") # Define relationship if User model is accessible

# Pydantic Schemas for Search API (can be expanded later)

class SearchQueryLogCreate(BaseModel):
    query: str = Field(..., max_length=500)
    user_id: Optional[int] = None
    session_id: Optional[str] = None
    filters_applied: Optional[Dict[str, Any]] = None
    result_count: Optional[int] = None
    clicked_result_ids: Optional[List[str]] = None
    user_ip: Optional[str] = None
    user_agent: Optional[str] = None

class SearchQueryLogResponse(SearchQueryLogCreate):
    id: str
    search_timestamp: datetime

    class Config:
        from_attributes = True

class SearchRecommendationRequest(BaseModel):
    user_id: Optional[int] = None
    session_id: Optional[str] = None
    current_query: Optional[str] = None
    context_items: Optional[List[str]] = None # e.g., items in cart, recently viewed
    limit: int = 5

class SearchRecommendation(BaseModel):
    item_id: str
    item_type: str # e.g., "persona", "report", "dashboard_widget"
    score: float
    reasoning: Optional[str] = None

class SearchRecommendationResponse(BaseModel):
    recommendations: List[SearchRecommendation]
    request_id: Optional[str] = None
