"""
Dashboard Configuration Service for YAML-based dashboard management.
Handles loading, saving, and validating dashboard configurations from YAML files.
"""

import logging
import os
import yaml
from typing import Dict, Any, List, Optional
from pathlib import Path
from app.models.dashboard_customization import (
    DashboardTemplate, DashboardConfigValidator, DashboardCreate,
    SectionCreate, WidgetCreate, ThemeConfig, LayoutConfig
)

logger = logging.getLogger(__name__)


class DashboardConfigService:
    """Service for managing dashboard configurations with YAML support."""
    
    def __init__(self, templates_dir: str = "dashboard_templates"):
        """Initialize the dashboard config service."""
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(exist_ok=True)
        logger.info(f"Dashboard config service initialized with templates dir: {self.templates_dir}")
    
    def load_template(self, template_name: str) -> DashboardTemplate:
        """Load a dashboard template from YAML file."""
        try:
            template_path = self.templates_dir / f"{template_name}.yaml"
            if not template_path.exists():
                raise FileNotFoundError(f"Template '{template_name}' not found")
            
            return DashboardTemplate.from_yaml_file(str(template_path))
        except Exception as e:
            logger.error(f"Error loading template '{template_name}': {e}")
            raise
    
    def save_template(self, template: DashboardTemplate, template_name: str) -> None:
        """Save a dashboard template to YAML file."""
        try:
            template_path = self.templates_dir / f"{template_name}.yaml"
            template.save_to_file(str(template_path))
            logger.info(f"Template '{template_name}' saved successfully")
        except Exception as e:
            logger.error(f"Error saving template '{template_name}': {e}")
            raise
    
    def list_templates(self) -> List[Dict[str, Any]]:
        """List all available dashboard templates."""
        templates = []
        try:
            for template_file in self.templates_dir.glob("*.yaml"):
                try:
                    template = DashboardTemplate.from_yaml_file(str(template_file))
                    templates.append({
                        "name": template_file.stem,
                        "display_name": template.name,
                        "description": template.description,
                        "category": template.category,
                        "version": template.version,
                        "author": template.author,
                        "tags": template.tags,
                        "section_count": len(template.sections),
                        "widget_count": len(template.widgets)
                    })
                except Exception as e:
                    logger.warning(f"Error loading template {template_file}: {e}")
                    continue
        except Exception as e:
            logger.error(f"Error listing templates: {e}")
        
        return templates
    
    def validate_config(self, config_yaml: str) -> Dict[str, Any]:
        """Validate a dashboard configuration YAML."""
        try:
            return DashboardConfigValidator.validate_dashboard_yaml(config_yaml)
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            raise
    
    def create_default_templates(self) -> None:
        """Create default dashboard templates."""
        templates = [
            self._create_analytics_template(),
            self._create_business_template(),
            self._create_monitoring_template()
        ]
        
        for template in templates:
            try:
                template_name = template.name.lower().replace(" ", "_")
                self.save_template(template, template_name)
                logger.info(f"Created default template: {template_name}")
            except Exception as e:
                logger.error(f"Error creating default template {template.name}: {e}")
    
    def _create_analytics_template(self) -> DashboardTemplate:
        """Create analytics dashboard template."""
        return DashboardTemplate(
            name="Analytics Overview",
            description="Comprehensive analytics dashboard with key metrics and visualizations",
            category="analytics",
            dashboard_config=DashboardCreate(
                name="Analytics Dashboard",
                description="Data analytics and insights dashboard",
                layout_config={
                    "columns": 12,
                    "rows": 16,
                    "grid_gap": 16,
                    "margin": [10, 10],
                    "container_padding": [10, 10]
                },
                theme_config={
                    "primary_color": "#3B82F6",
                    "secondary_color": "#10B981",
                    "background_color": "#F9FAFB"
                }
            ),
            sections=[
                {
                    "name": "Key Metrics",
                    "description": "Primary KPIs and metrics",
                    "color": "#3B82F6",
                    "icon": "BarChart3"
                },
                {
                    "name": "Trends Analysis",
                    "description": "Trend charts and time series data",
                    "color": "#10B981",
                    "icon": "TrendingUp"
                }
            ],
            widgets=[
                {
                    "title": "Total Users",
                    "widget_type": "kpi",
                    "position_config": {"x": 0, "y": 0, "w": 3, "h": 2}
                },
                {
                    "title": "Revenue Trend",
                    "widget_type": "chart",
                    "position_config": {"x": 3, "y": 0, "w": 6, "h": 4}
                }
            ],
            version="1.0.0",
            author="Datagenius",
            tags=["analytics", "metrics", "kpi"]
        )
    
    def _create_business_template(self) -> DashboardTemplate:
        """Create business dashboard template."""
        return DashboardTemplate(
            name="Business Metrics",
            description="Business performance and operational metrics dashboard",
            category="business",
            dashboard_config=DashboardCreate(
                name="Business Dashboard",
                description="Business performance tracking",
                theme_config={
                    "primary_color": "#059669",
                    "secondary_color": "#DC2626",
                    "background_color": "#FFFFFF"
                }
            ),
            sections=[
                {
                    "name": "Sales Performance",
                    "description": "Sales metrics and targets",
                    "color": "#059669",
                    "icon": "TrendingUp"
                },
                {
                    "name": "Customer Insights",
                    "description": "Customer analytics and behavior",
                    "color": "#DC2626",
                    "icon": "Users"
                }
            ],
            widgets=[
                {
                    "title": "Monthly Sales",
                    "widget_type": "chart",
                    "position_config": {"x": 0, "y": 0, "w": 6, "h": 4}
                },
                {
                    "title": "Customer Satisfaction",
                    "widget_type": "gauge",
                    "position_config": {"x": 6, "y": 0, "w": 3, "h": 4}
                }
            ],
            version="1.0.0",
            author="Datagenius",
            tags=["business", "sales", "customers"]
        )
    
    def _create_monitoring_template(self) -> DashboardTemplate:
        """Create monitoring dashboard template."""
        return DashboardTemplate(
            name="System Monitoring",
            description="System performance and health monitoring dashboard",
            category="monitoring",
            dashboard_config=DashboardCreate(
                name="Monitoring Dashboard",
                description="System health and performance monitoring",
                refresh_interval=60,  # More frequent updates for monitoring
                theme_config={
                    "primary_color": "#7C3AED",
                    "secondary_color": "#F59E0B",
                    "background_color": "#1F2937"
                }
            ),
            sections=[
                {
                    "name": "System Health",
                    "description": "Core system metrics",
                    "color": "#7C3AED",
                    "icon": "Activity"
                },
                {
                    "name": "Performance",
                    "description": "Performance indicators",
                    "color": "#F59E0B",
                    "icon": "Gauge"
                }
            ],
            widgets=[
                {
                    "title": "CPU Usage",
                    "widget_type": "gauge",
                    "position_config": {"x": 0, "y": 0, "w": 3, "h": 3}
                },
                {
                    "title": "Memory Usage",
                    "widget_type": "gauge",
                    "position_config": {"x": 3, "y": 0, "w": 3, "h": 3}
                },
                {
                    "title": "Response Time",
                    "widget_type": "chart",
                    "position_config": {"x": 6, "y": 0, "w": 6, "h": 4}
                }
            ],
            version="1.0.0",
            author="Datagenius",
            tags=["monitoring", "performance", "system"]
        )
    
    def export_dashboard_config(self, dashboard_data: Dict[str, Any]) -> str:
        """Export dashboard configuration to YAML format."""
        try:
            # Validate the configuration first
            validated_config = DashboardConfigValidator.validate_dashboard_yaml(
                yaml.dump(dashboard_data)
            )
            return yaml.dump(validated_config, default_flow_style=False, sort_keys=True)
        except Exception as e:
            logger.error(f"Error exporting dashboard config: {e}")
            raise
    
    def import_dashboard_config(self, yaml_config: str) -> Dict[str, Any]:
        """Import and validate dashboard configuration from YAML."""
        try:
            return self.validate_config(yaml_config)
        except Exception as e:
            logger.error(f"Error importing dashboard config: {e}")
            raise
