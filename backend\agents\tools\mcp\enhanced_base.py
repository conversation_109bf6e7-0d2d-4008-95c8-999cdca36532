"""
Enhanced Base MCP Tool with Agent Awareness.

This module provides an enhanced base class that automatically includes agent identity
integration, making it the standard for all new MCP tools. This ensures consistent
agent-aware capabilities across the entire application.
"""

import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

from .base import BaseMCPTool
from .mixins.agent_aware_mixin import AgentAwareMixin
from .validation.input_validator import Input<PERSON>ali<PERSON>tor
from .error_handling.error_handler import get_error_handler
from .monitoring.performance_monitor import get_performance_monitor

logger = logging.getLogger(__name__)


class EnhancedMCPTool(BaseMCPTool, AgentAwareMixin, ABC):
    """
    Enhanced base class for MCP tools with built-in agent awareness.
    
    This class automatically provides:
    - Agent identity detection and integration
    - Input validation and sanitization
    - Error handling and recovery
    - Performance monitoring
    - Standardized response formatting
    
    All new MCP tools should inherit from this class to ensure consistent
    agent-aware capabilities and robust operation.
    
    Example:
        class MyNewTool(EnhancedMCPTool):
            def __init__(self):
                super().__init__(
                    name="my_new_tool",
                    description="Description of my new tool",
                    input_schema={...}
                )
            
            async def _execute_with_agent_awareness(
                self, 
                arguments: Dict[str, Any], 
                agent_identity: str, 
                agent_capabilities: Dict[str, Any]
            ) -> Dict[str, Any]:
                # Implement tool-specific logic here
                # Agent identity and capabilities are automatically available
                return {"content": [...]}
    """
    
    def __init__(
        self, 
        name: str, 
        description: str, 
        input_schema: Dict[str, Any],
        annotations: Optional[Dict[str, Any]] = None,
        enable_validation: bool = True,
        enable_monitoring: bool = True,
        enable_error_handling: bool = True
    ):
        """
        Initialize the enhanced MCP tool.
        
        Args:
            name: Tool name
            description: Tool description
            input_schema: Input schema for validation
            annotations: Optional annotations
            enable_validation: Enable input validation
            enable_monitoring: Enable performance monitoring
            enable_error_handling: Enable error handling
        """
        # Initialize base classes
        BaseMCPTool.__init__(self, name, description, input_schema, annotations)
        AgentAwareMixin.__init__(self)
        
        # Configuration
        self.enable_validation = enable_validation
        self.enable_monitoring = enable_monitoring
        self.enable_error_handling = enable_error_handling
        
        # Initialize components
        if self.enable_validation:
            self.validator = InputValidator()
        
        if self.enable_error_handling:
            self.error_handler = get_error_handler()
        
        if self.enable_monitoring:
            self.performance_monitor = get_performance_monitor()
        
        logger.info(f"Initialized enhanced MCP tool: {name}")
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with full agent awareness and enhanced capabilities.

        This method provides the standard execution flow:
        1. Input validation and sanitization
        2. Agent identity detection
        3. Performance monitoring
        4. Error handling and recovery
        5. Tool-specific execution with agent awareness
        6. Response enhancement with agent metadata

        Args:
            arguments: Tool execution arguments

        Returns:
            Enhanced tool execution results
        """
        # DEBUG: Log arguments received by EnhancedMCPTool
        logger.info(f"EnhancedMCPTool.execute called for {self.name}")
        logger.info(f"EnhancedMCPTool arguments keys: {list(arguments.keys())}")
        logger.info(f"EnhancedMCPTool content_type: {arguments.get('content_type', 'NOT_FOUND')}")
        # Start performance monitoring
        if self.enable_monitoring:
            monitor_context = self.performance_monitor.monitor_execution(
                tool_name=self.name,
                agent_identity=None,  # Will be updated after detection
                input_data=arguments
            )
            await monitor_context.__aenter__()
        else:
            monitor_context = None
        
        try:
            # Step 1: Input validation and sanitization
            if self.enable_validation:
                validation_result = await self._validate_and_sanitize_input(arguments)
                if validation_result.get("isError"):
                    return validation_result
                arguments = validation_result.get("sanitized_arguments", arguments)
            
            # Step 2: Agent identity detection
            agent_identity = await self.detect_agent_identity(arguments, self.name)
            
            # Update performance monitor with agent identity
            if monitor_context and hasattr(monitor_context, '_execution_context'):
                monitor_context._execution_context['agent_identity'] = agent_identity
            
            # Step 3: Execute with agent awareness and error handling
            if self.enable_error_handling:
                result = await self.error_handler.handle_with_retry(
                    operation=self._execute_with_agent_awareness_wrapper,
                    tool_name=self.name,
                    agent_identity=agent_identity,
                    context=arguments,
                    arguments=arguments,
                    agent_identity=agent_identity
                )
            else:
                result = await self._execute_with_agent_awareness_wrapper(
                    arguments, agent_identity
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in enhanced tool execution for {self.name}: {e}")
            
            # Create error response with agent context if available
            error_response = {
                "isError": True,
                "content": [{
                    "type": "text",
                    "text": f"Error executing {self.name}: {str(e)}"
                }],
                "metadata": {
                    "tool_name": self.name,
                    "error_type": type(e).__name__,
                    "enhanced_tool": True
                }
            }
            
            # Add agent context if available
            try:
                agent_identity = await self.detect_agent_identity(arguments, self.name)
                error_response["metadata"]["agent_identity"] = agent_identity
                error_response["metadata"]["agent_aware"] = True
            except:
                pass
            
            return error_response
            
        finally:
            # End performance monitoring
            if monitor_context:
                try:
                    await monitor_context.__aexit__(None, None, None)
                except:
                    pass
    
    async def _execute_with_agent_awareness_wrapper(
        self, 
        arguments: Dict[str, Any], 
        agent_identity: str
    ) -> Dict[str, Any]:
        """
        Wrapper for agent-aware execution.
        
        Args:
            arguments: Tool execution arguments
            agent_identity: Detected agent identity
            
        Returns:
            Tool execution results with agent metadata
        """
        # Get agent capabilities
        agent_capabilities = await self.get_agent_capabilities(agent_identity)
        
        # Execute tool-specific logic
        result = await self._execute_with_agent_awareness(
            arguments, agent_identity, agent_capabilities
        )
        
        # Enhance result with agent metadata
        enhanced_result = self._enhance_result_with_agent_metadata(
            result, agent_identity, agent_capabilities
        )
        
        return enhanced_result
    
    async def _validate_and_sanitize_input(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and sanitize input arguments.
        
        Args:
            arguments: Input arguments
            
        Returns:
            Validation result with sanitized arguments
        """
        try:
            # Validate against schema
            validation_result = await self.validator.validate_tool_input(
                tool_name=self.name,
                input_data=arguments,
                schema=self.input_schema
            )
            
            if not validation_result["is_valid"]:
                return {
                    "isError": True,
                    "content": [{
                        "type": "text",
                        "text": f"Input validation failed: {validation_result['error_message']}"
                    }],
                    "metadata": {
                        "tool_name": self.name,
                        "validation_errors": validation_result.get("validation_errors", []),
                        "enhanced_tool": True
                    }
                }
            
            return {
                "isError": False,
                "sanitized_arguments": validation_result.get("sanitized_data", arguments)
            }
            
        except Exception as e:
            logger.warning(f"Input validation failed for {self.name}: {e}")
            # Continue with original arguments if validation fails
            return {
                "isError": False,
                "sanitized_arguments": arguments
            }
    
    @abstractmethod
    async def _execute_with_agent_awareness(
        self, 
        arguments: Dict[str, Any], 
        agent_identity: str, 
        agent_capabilities: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute the tool with agent awareness.
        
        This method must be implemented by subclasses to provide tool-specific
        functionality with access to agent identity and capabilities.
        
        Args:
            arguments: Tool execution arguments
            agent_identity: Detected agent identity
            agent_capabilities: Agent capabilities and preferences
            
        Returns:
            Tool execution results
        """
        pass
    
    def get_agent_style_preferences(self, agent_capabilities: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract style preferences from agent capabilities.
        
        Args:
            agent_capabilities: Agent capabilities
            
        Returns:
            Style preferences for tool customization
        """
        return {
            "communication_style": agent_capabilities.get("communication_style", "professional"),
            "technical_level": agent_capabilities.get("technical_level", "moderate"),
            "focus_areas": agent_capabilities.get("focus_areas", []),
            "specialization": agent_capabilities.get("specialization", "general")
        }
    
    def format_response_for_agent(
        self, 
        content: str, 
        agent_capabilities: Dict[str, Any],
        response_type: str = "text"
    ) -> Dict[str, Any]:
        """
        Format response based on agent preferences.
        
        Args:
            content: Response content
            agent_capabilities: Agent capabilities
            response_type: Type of response (text, table, image, etc.)
            
        Returns:
            Formatted response
        """
        style_prefs = self.get_agent_style_preferences(agent_capabilities)
        
        # Customize content based on agent style
        if style_prefs["communication_style"] == "friendly":
            # Add friendly elements
            if not content.startswith(("😊", "👋", "🎉", "✨")):
                content = f"✨ {content}"
        elif style_prefs["communication_style"] == "technical":
            # Ensure technical precision
            if style_prefs["technical_level"] == "advanced":
                content = f"🔬 Technical Analysis: {content}"
        
        return {
            "content": [{
                "type": response_type,
                "text": content
            }],
            "metadata": {
                "formatted_for_agent": True,
                "agent_style": style_prefs["communication_style"],
                "technical_level": style_prefs["technical_level"]
            }
        }
    
    async def get_tool_usage_analytics(self) -> Dict[str, Any]:
        """
        Get analytics for this tool's usage.
        
        Returns:
            Tool usage analytics
        """
        if not self.enable_monitoring:
            return {"error": "Monitoring not enabled"}
        
        return self.performance_monitor.get_performance_summary(self.name)
    
    def __repr__(self) -> str:
        """String representation of the enhanced tool."""
        return f"EnhancedMCPTool(name='{self.name}', agent_aware=True)"
