"""
Code Execution MCP tool for the Datagenius backend.

This module provides a secure, sandboxed code execution environment for agents
to generate dynamic results, perform calculations, and create visualizations.
"""

import logging
import os
import sys
import io
import ast
import time
import tempfile
import subprocess
import base64
import traceback
from typing import Dict, Any, Optional, List, Union
from contextlib import redirect_stdout, redirect_stderr
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta

from .base import BaseMCPTool
from app.config import UPLOAD_DIR
from app.database import get_db, get_file
from sqlalchemy.orm import Session
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class SecureCodeExecutor:
    """Secure code execution environment with sandboxing and restrictions."""
    
    # Allowed imports for security
    ALLOWED_IMPORTS = {
        'pandas', 'pd', 'numpy', 'np', 'matplotlib', 'plt', 'seaborn', 'sns',
        'datetime', 'time', 'math', 'statistics', 'json', 're', 'collections',
        'itertools', 'functools', 'operator', 'random', 'uuid', 'hashlib',
        'base64', 'urllib', 'requests', 'scipy', 'sklearn', 'plotly'
    }
    
    # Restricted functions and modules
    RESTRICTED_NAMES = {
        'exec', 'eval', 'compile', '__import__', 'open', 'file', 'input', 'raw_input',
        'reload', 'vars', 'dir', 'globals', 'locals', 'delattr', 'setattr', 'getattr',
        'hasattr', 'callable', 'isinstance', 'issubclass', 'super', 'property',
        'staticmethod', 'classmethod', 'type', 'object', 'help', 'memoryview',
        'slice', 'buffer', 'bytearray', 'bytes', 'exit', 'quit', 'os', 'sys',
        'subprocess', 'importlib', 'pkgutil', 'imp', 'zipimport'
    }
    
    def __init__(self, timeout: int = 30, max_memory: int = 100):
        """
        Initialize the secure code executor.
        
        Args:
            timeout: Maximum execution time in seconds
            max_memory: Maximum memory usage in MB
        """
        self.timeout = timeout
        self.max_memory = max_memory
        self.execution_count = 0
        
    def validate_code(self, code: str) -> tuple[bool, str]:
        """
        Validate code for security issues.
        
        Args:
            code: Python code to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Parse the code into an AST
            tree = ast.parse(code)
            
            # Check for restricted operations
            for node in ast.walk(tree):
                # Check for imports
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            if alias.name not in self.ALLOWED_IMPORTS:
                                return False, f"Import '{alias.name}' is not allowed"
                    elif isinstance(node, ast.ImportFrom):
                        if node.module and node.module not in self.ALLOWED_IMPORTS:
                            return False, f"Import from '{node.module}' is not allowed"
                
                # Check for restricted function calls
                if isinstance(node, ast.Name) and node.id in self.RESTRICTED_NAMES:
                    return False, f"Use of '{node.id}' is not allowed"
                
                # Check for attribute access to restricted modules
                if isinstance(node, ast.Attribute):
                    if isinstance(node.value, ast.Name) and node.value.id in ['os', 'sys', 'subprocess']:
                        return False, f"Access to '{node.value.id}.{node.attr}' is not allowed"
            
            return True, ""
            
        except SyntaxError as e:
            return False, f"Syntax error: {str(e)}"
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def execute_code(self, code: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute Python code in a secure environment.
        
        Args:
            code: Python code to execute
            context: Additional context variables
            
        Returns:
            Execution results with output, errors, and generated files
        """
        self.execution_count += 1
        execution_id = f"exec_{int(time.time())}_{self.execution_count}"
        
        # Validate code first
        is_valid, error_msg = self.validate_code(code)
        if not is_valid:
            return {
                "success": False,
                "error": f"Security validation failed: {error_msg}",
                "execution_id": execution_id
            }
        
        # Prepare execution environment
        safe_globals = {
            '__builtins__': {
                'len', 'str', 'int', 'float', 'bool', 'list', 'dict', 'tuple', 'set',
                'range', 'enumerate', 'zip', 'map', 'filter', 'sorted', 'reversed',
                'sum', 'min', 'max', 'abs', 'round', 'pow', 'divmod', 'all', 'any',
                'print', 'format', 'repr', 'ord', 'chr', 'hex', 'oct', 'bin'
            },
            'pd': pd,
            'np': np,
            'plt': plt,
            'sns': sns,
            'datetime': datetime,
            'timedelta': timedelta,
            'time': time
        }
        
        # Add context variables if provided
        if context:
            safe_globals.update(context)
        
        # Capture output
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        try:
            # Execute with output capture
            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                # Create a temporary directory for file outputs
                with tempfile.TemporaryDirectory() as temp_dir:
                    # Set matplotlib to use Agg backend for non-interactive plotting
                    plt.switch_backend('Agg')
                    
                    # Add temp directory to context
                    safe_globals['temp_dir'] = temp_dir
                    
                    # Execute the code
                    exec(code, safe_globals)
                    
                    # Collect any generated files
                    generated_files = []
                    for filename in os.listdir(temp_dir):
                        file_path = os.path.join(temp_dir, filename)
                        if os.path.isfile(file_path):
                            with open(file_path, 'rb') as f:
                                file_content = base64.b64encode(f.read()).decode('utf-8')
                                generated_files.append({
                                    "filename": filename,
                                    "content": file_content,
                                    "size": os.path.getsize(file_path)
                                })
            
            # Get captured output
            stdout_output = stdout_capture.getvalue()
            stderr_output = stderr_capture.getvalue()
            
            return {
                "success": True,
                "execution_id": execution_id,
                "stdout": stdout_output,
                "stderr": stderr_output,
                "generated_files": generated_files,
                "execution_time": time.time()
            }
            
        except Exception as e:
            error_traceback = traceback.format_exc()
            return {
                "success": False,
                "execution_id": execution_id,
                "error": str(e),
                "traceback": error_traceback,
                "stdout": stdout_capture.getvalue(),
                "stderr": stderr_capture.getvalue()
            }


class CodeExecutionTool(BaseMCPTool):
    """
    MCP tool for secure code execution.
    
    This tool allows agents to execute Python code to generate dynamic results,
    perform calculations, create visualizations, and analyze data.
    """
    
    def __init__(self):
        """Initialize the code execution tool."""
        super().__init__()
        self.name = "execute_code"
        self.description = "Execute Python code to generate dynamic results and visualizations"
        self.executor = None
        
    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.
        
        Args:
            config: Configuration dictionary
        """
        timeout = config.get("timeout", 30)
        max_memory = config.get("max_memory", 100)
        
        self.executor = SecureCodeExecutor(timeout=timeout, max_memory=max_memory)
        logger.info(f"CodeExecutionTool initialized with timeout={timeout}s, max_memory={max_memory}MB")
    
    def get_schema(self) -> Dict[str, Any]:
        """
        Get the JSON schema for this tool.
        
        Returns:
            JSON schema for the tool parameters
        """
        return {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "description": "Python code to execute"
                },
                "language": {
                    "type": "string",
                    "enum": ["python"],
                    "default": "python",
                    "description": "Programming language (currently only Python is supported)"
                },
                "context": {
                    "type": "object",
                    "description": "Additional context variables to make available in the code",
                    "properties": {
                        "data_file_id": {"type": "string", "description": "ID of data file to load"},
                        "variables": {"type": "object", "description": "Custom variables to inject"}
                    }
                },
                "output_format": {
                    "type": "string",
                    "enum": ["text", "json", "html", "markdown"],
                    "default": "text",
                    "description": "Preferred output format"
                },
                "save_plots": {
                    "type": "boolean",
                    "default": True,
                    "description": "Whether to save generated plots as files"
                }
            },
            "required": ["code"]
        }
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the code execution tool with agent-aware capabilities.

        Args:
            params: Parameters for code execution

        Returns:
            Dictionary containing execution results and metadata
        """
        try:
            code = params.get("code", "")
            language = params.get("language", "python")
            context_params = params.get("context", {})
            output_format = params.get("output_format", "text")
            save_plots = params.get("save_plots", True)

            # Agent context for dynamic identity detection
            user_context = params.get("user_context", {})
            agent_id = params.get("persona_id") or params.get("agent_id")

            # Detect agent identity for personalized code execution
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=user_context,
                intent_type="code_execution"
            )

            logger.info(f"Detected agent identity: {agent_identity} for code execution")
            
            if not code.strip():
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": "No code provided for execution"}]
                }
            
            if language != "python":
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": f"Language '{language}' is not supported. Only Python is currently supported."}]
                }
            
            logger.info(f"Executing Python code: {code[:100]}...")
            
            # Prepare execution context with agent-specific enhancements
            execution_context = await self._prepare_agent_aware_context(agent_identity, context_params)

            # Load data file if specified
            if "data_file_id" in context_params:
                file_id = context_params["data_file_id"]
                try:
                    with get_db() as db:
                        file_record = get_file(db, file_id)
                        if file_record:
                            file_path = os.path.join(UPLOAD_DIR, file_record.filename)
                            if os.path.exists(file_path):
                                # Load data based on file extension
                                if file_path.endswith('.csv'):
                                    execution_context['df'] = pd.read_csv(file_path)
                                elif file_path.endswith(('.xlsx', '.xls')):
                                    execution_context['df'] = pd.read_excel(file_path)
                                elif file_path.endswith('.json'):
                                    execution_context['df'] = pd.read_json(file_path)
                                
                                execution_context['file_path'] = file_path
                                execution_context['file_name'] = file_record.filename
                except Exception as e:
                    logger.warning(f"Failed to load data file {file_id}: {e}")
            
            # Add custom variables
            if "variables" in context_params:
                execution_context.update(context_params["variables"])
            
            # Execute the code
            result = self.executor.execute_code(code, execution_context)
            
            if result["success"]:
                # Format successful result
                content_parts = []
                
                # Add stdout output
                if result.get("stdout"):
                    content_parts.append({
                        "type": "text",
                        "text": f"Output:\n{result['stdout']}"
                    })
                
                # Add stderr if present (warnings, etc.)
                if result.get("stderr"):
                    content_parts.append({
                        "type": "text", 
                        "text": f"Warnings/Errors:\n{result['stderr']}"
                    })
                
                # Add generated files (plots, etc.)
                if result.get("generated_files"):
                    for file_info in result["generated_files"]:
                        content_parts.append({
                            "type": "text",
                            "text": f"Generated file: {file_info['filename']} ({file_info['size']} bytes)"
                        })
                        
                        # If it's an image, include it as base64
                        if file_info['filename'].lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg')):
                            content_parts.append({
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": f"image/{file_info['filename'].split('.')[-1]}",
                                    "data": file_info['content']
                                }
                            })
                
                if not content_parts:
                    content_parts.append({
                        "type": "text",
                        "text": "Code executed successfully with no output."
                    })
                
                return {
                    "isError": False,
                    "content": content_parts,
                    "metadata": {
                        "execution_id": result["execution_id"],
                        "language": language,
                        "execution_time": result.get("execution_time"),
                        "files_generated": len(result.get("generated_files", [])),
                        "agent_identity": agent_identity,
                        "agent_aware": True
                    }
                }
            else:
                # Format error result
                error_text = f"Code execution failed:\n{result['error']}"
                if result.get("traceback"):
                    error_text += f"\n\nTraceback:\n{result['traceback']}"
                
                return {
                    "isError": True,
                    "content": [{"type": "text", "text": error_text}],
                    "metadata": {
                        "execution_id": result["execution_id"],
                        "language": language
                    }
                }
                
        except Exception as e:
            logger.error(f"Error in code execution tool: {e}")
            return {
                "isError": True,
                "content": [{"type": "text", "text": f"Tool execution error: {str(e)}"}]
            }

    async def _prepare_agent_aware_context(self, agent_identity: str, context_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare execution context with agent-specific enhancements.

        Args:
            agent_identity: Agent identity for customization
            context_params: Original context parameters

        Returns:
            Enhanced execution context
        """
        execution_context = {}

        try:
            # Get agent system prompt to extract preferences
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Add agent-specific imports and utilities
            if agent_identity == "analyst" or "analysis" in system_prompt.lower():
                execution_context.update({
                    "analysis_mode": True,
                    "preferred_viz_style": "analytical",
                    "include_statistics": True
                })
            elif agent_identity == "marketer" or "marketing" in system_prompt.lower():
                execution_context.update({
                    "business_mode": True,
                    "preferred_viz_style": "business",
                    "include_insights": True
                })
            elif agent_identity == "classifier" or "classification" in system_prompt.lower():
                execution_context.update({
                    "classification_mode": True,
                    "preferred_viz_style": "categorical",
                    "include_metrics": True
                })
            else:
                execution_context.update({
                    "general_mode": True,
                    "preferred_viz_style": "professional"
                })

            # Add agent-specific helper functions
            execution_context["agent_helpers"] = self._get_agent_helper_functions(agent_identity)

        except Exception as e:
            logger.warning(f"Failed to prepare agent-aware context: {e}")

        return execution_context

    def _get_agent_helper_functions(self, agent_identity: str) -> Dict[str, Any]:
        """Get agent-specific helper functions for code execution."""

        base_helpers = {
            "create_professional_plot": """
def create_professional_plot(data, title="", xlabel="", ylabel="", style="professional"):
    import matplotlib.pyplot as plt
    import seaborn as sns

    plt.style.use('seaborn-v0_8' if style == 'professional' else 'default')
    fig, ax = plt.subplots(figsize=(10, 6))

    if hasattr(data, 'plot'):
        data.plot(ax=ax)
    else:
        ax.plot(data)

    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_xlabel(xlabel, fontsize=12)
    ax.set_ylabel(ylabel, fontsize=12)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    return fig, ax
""",
            "format_results": f"""
def format_results(results, agent_style="{agent_identity}"):
    if isinstance(results, dict):
        formatted = "\\n".join([f"{{k}}: {{v}}" for k, v in results.items()])
    else:
        formatted = str(results)

    return f"Results ({{agent_style}} format):\\n{{formatted}}"
"""
        }

        # Add agent-specific helpers
        if agent_identity == "analyst":
            base_helpers["statistical_summary"] = """
def statistical_summary(data):
    import pandas as pd
    import numpy as np

    if isinstance(data, pd.DataFrame):
        summary = {
            'shape': data.shape,
            'dtypes': data.dtypes.to_dict(),
            'missing_values': data.isnull().sum().to_dict(),
            'numeric_summary': data.describe().to_dict() if len(data.select_dtypes(include=[np.number]).columns) > 0 else {}
        }
    else:
        summary = {'type': type(data).__name__, 'length': len(data) if hasattr(data, '__len__') else 'N/A'}

    return summary
"""
        elif agent_identity == "marketer":
            base_helpers["business_insights"] = """
def business_insights(data, metric_column=None):
    insights = []

    if hasattr(data, 'describe'):
        stats = data.describe()
        if metric_column and metric_column in data.columns:
            mean_val = data[metric_column].mean()
            insights.append(f"Average {metric_column}: {mean_val:.2f}")

            if mean_val > data[metric_column].median():
                insights.append("Performance is above median - positive indicator")
            else:
                insights.append("Performance is below median - opportunity for improvement")

    return insights
"""
        elif agent_identity == "classifier":
            base_helpers["classification_metrics"] = """
def classification_metrics(y_true, y_pred):
    try:
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted'),
            'recall': recall_score(y_true, y_pred, average='weighted'),
            'f1_score': f1_score(y_true, y_pred, average='weighted')
        }
    except ImportError:
        metrics = {'error': 'sklearn not available for classification metrics'}

    return metrics
"""

        return base_helpers
