import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Palette, 
  Grid, 
  Settings, 
  Eye,
  BarChart3,
  Table,
  Gauge,
  Network,
  TreePine,
  Map,
  Type,
  Image
} from 'lucide-react';
import { SectionResponse } from '@/types/dashboard-customization';

const ICON_OPTIONS = [
  { value: 'BarChart3', label: 'Chart', icon: Bar<PERSON><PERSON>3 },
  { value: 'Table', label: 'Table', icon: Table },
  { value: 'Gauge', label: 'Gauge', icon: Gauge },
  { value: 'Grid', label: 'Grid', icon: Grid },
  { value: 'Network', label: 'Network', icon: Network },
  { value: 'TreePine', label: 'Tree', icon: TreePine },
  { value: 'Map', label: 'Map', icon: Map },
  { value: 'Type', label: 'Text', icon: Type },
  { value: 'Image', label: 'Image', icon: Image },
];

const COLOR_PRESETS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Orange
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#EC4899', // Pink
  '#6B7280', // Gray
  '#14B8A6', // Teal
];

interface SectionCustomizerProps {
  section: SectionResponse;
  onUpdate: (updates: Partial<SectionResponse>) => void;
  onClose: () => void;
}

export const SectionCustomizer: React.FC<SectionCustomizerProps> = ({
  section,
  onUpdate,
  onClose,
}) => {
  const [formData, setFormData] = useState({
    name: section.name,
    description: section.description || '',
    color: section.color || '#3B82F6',
    icon: section.icon || 'Grid',
    layoutConfig: {
      columns: section.layout_config?.columns || 12,
      rows: section.layout_config?.rows || 6,
      grid_gap: section.layout_config?.grid_gap || 16,
    },
    customization: {
      background_color: section.customization?.background_color || '#FFFFFF',
      border_color: section.customization?.border_color || '#E5E7EB',
      border_width: section.customization?.border_width || 1,
      border_style: section.customization?.border_style || 'solid',
      padding: section.customization?.padding || [16, 16],
      margin: section.customization?.margin || [8, 8],
      header_style: section.customization?.header_style || {},
      shadow: section.customization?.shadow ?? true,
    },
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleLayoutChange = (field: string, value: number) => {
    setFormData(prev => ({
      ...prev,
      layoutConfig: {
        ...prev.layoutConfig,
        [field]: value,
      },
    }));
  };

  const handleCustomizationChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      customization: {
        ...prev.customization,
        [field]: value,
      },
    }));
  };

  const handleSave = () => {
    // Transform frontend data structure to match backend SectionUpdate schema (snake_case)
    const transformedData = {
      name: formData.name,
      description: formData.description,
      color: formData.color,
      icon: formData.icon,
      layout_config: formData.layoutConfig, // Convert camelCase to snake_case
      customization: formData.customization, // Already in snake_case format
    };

    console.log('Section update data being sent:', transformedData);
    onUpdate(transformedData);
  };

  const selectedIcon = ICON_OPTIONS.find(option => option.value === formData.icon);

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Customize Section: {section.name}</span>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="layout">Layout</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="section-name">Section Name</Label>
                  <Input
                    id="section-name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter section name"
                  />
                </div>

                <div>
                  <Label htmlFor="section-description">Description</Label>
                  <Textarea
                    id="section-description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter section description"
                    rows={3}
                  />
                </div>

                <div>
                  <Label>Section Icon</Label>
                  <div className="grid grid-cols-3 gap-2 mt-2">
                    {ICON_OPTIONS.map((option) => (
                      <Button
                        key={option.value}
                        variant={formData.icon === option.value ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleInputChange('icon', option.value)}
                        className="flex items-center space-x-2"
                      >
                        <option.icon className="h-4 w-4" />
                        <span>{option.label}</span>
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <Label>Section Color</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Input
                      type="color"
                      value={formData.color}
                      onChange={(e) => handleInputChange('color', e.target.value)}
                      className="w-16 h-10"
                    />
                    <div className="flex space-x-1">
                      {COLOR_PRESETS.map((color) => (
                        <Button
                          key={color}
                          variant="outline"
                          size="sm"
                          className="w-8 h-8 p-0"
                          style={{ backgroundColor: color }}
                          onClick={() => handleInputChange('color', color)}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="layout" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <Grid className="h-5 w-5" />
                  <span>Grid Layout</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label>Grid Columns: {formData.layoutConfig.columns}</Label>
                  <Slider
                    value={[formData.layoutConfig.columns]}
                    onValueChange={([value]) => handleLayoutChange('columns', value)}
                    min={6}
                    max={24}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Grid Rows: {formData.layoutConfig.rows}</Label>
                  <Slider
                    value={[formData.layoutConfig.rows]}
                    onValueChange={([value]) => handleLayoutChange('rows', value)}
                    min={6}
                    max={24}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Grid Gap: {formData.layoutConfig.grid_gap}px</Label>
                  <Slider
                    value={[formData.layoutConfig.grid_gap]}
                    onValueChange={([value]) => handleLayoutChange('grid_gap', value)}
                    min={8}
                    max={32}
                    step={4}
                    className="mt-2"
                  />
                </div>

                <div className="p-4 border rounded-lg bg-muted/50">
                  <div className="text-sm font-medium mb-2">Grid Preview</div>
                  <div 
                    className="border-2 border-dashed border-muted-foreground/30 rounded"
                    style={{
                      display: 'grid',
                      gridTemplateColumns: `repeat(${Math.min(formData.layoutConfig.columns, 12)}, 1fr)`,
                      gridTemplateRows: `repeat(${Math.min(formData.layoutConfig.rows, 6)}, 20px)`,
                      gap: `${formData.layoutConfig.grid_gap / 4}px`,
                      padding: `${formData.layoutConfig.grid_gap / 4}px`,
                      height: '120px',
                    }}
                  >
                    {Array.from({ length: Math.min(formData.layoutConfig.columns * Math.min(formData.layoutConfig.rows, 6), 72) }).map((_, i) => (
                      <div key={i} className="bg-muted rounded-sm" />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="appearance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <Palette className="h-5 w-5" />
                  <span>Visual Styling</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Background Color</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Input
                      type="color"
                      value={formData.customization.background_color}
                      onChange={(e) => handleCustomizationChange('background_color', e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={formData.customization.background_color}
                      onChange={(e) => handleCustomizationChange('background_color', e.target.value)}
                      placeholder="#FFFFFF"
                    />
                  </div>
                </div>

                <div>
                  <Label>Border Color</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Input
                      type="color"
                      value={formData.customization.border_color}
                      onChange={(e) => handleCustomizationChange('border_color', e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={formData.customization.border_color}
                      onChange={(e) => handleCustomizationChange('border_color', e.target.value)}
                      placeholder="#E5E7EB"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <Eye className="h-5 w-5" />
                  <span>Section Preview</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  className="border rounded-lg p-4"
                  style={{
                    backgroundColor: formData.customization.background_color,
                    borderColor: formData.customization.border_color,
                  }}
                >
                  <div className="flex items-center space-x-3 mb-4">
                    {selectedIcon && (
                      <div style={{ color: formData.color }}>
                        <selectedIcon.icon className="h-6 w-6" />
                      </div>
                    )}
                    <div>
                      <h3 className="text-lg font-semibold">{formData.name}</h3>
                      {formData.description && (
                        <p className="text-sm text-muted-foreground">{formData.description}</p>
                      )}
                    </div>
                    <Badge style={{ backgroundColor: formData.color, color: 'white' }}>
                      {section.widget_count} widgets
                    </Badge>
                  </div>
                  
                  <div 
                    className="border-2 border-dashed border-muted-foreground/30 rounded-lg"
                    style={{
                      display: 'grid',
                      gridTemplateColumns: `repeat(${Math.min(formData.layoutConfig.columns, 8)}, 1fr)`,
                      gridTemplateRows: `repeat(${Math.min(formData.layoutConfig.rows, 4)}, 40px)`,
                      gap: `${formData.layoutConfig.grid_gap / 2}px`,
                      padding: `${formData.layoutConfig.grid_gap / 2}px`,
                      height: '200px',
                    }}
                  >
                    {Array.from({ length: Math.min(formData.layoutConfig.columns * Math.min(formData.layoutConfig.rows, 4), 32) }).map((_, i) => (
                      <div key={i} className="bg-muted/50 rounded border" />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
