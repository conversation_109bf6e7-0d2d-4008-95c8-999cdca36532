#!/usr/bin/env python3
"""
Quick test to debug the "who are you" routing issue.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.orchestration.routing_component import RoutingComponent


async def test_who_are_you():
    """Test the specific 'who are you' message routing."""
    
    print("Testing 'who are you' routing")
    print("=" * 40)
    
    # Create routing component instance
    routing_component = RoutingComponent()
    
    # Test the specific case
    message = "who are you"
    context = {"current_persona": "composable-marketing-ai"}
    
    print(f"Message: '{message}'")
    print(f"Context: {context}")
    print(f"Expected: composable-marketing-ai")
    
    # Test persona switch detection
    is_switch = routing_component._is_persona_switch_request(message.lower())
    print(f"Is persona switch request: {is_switch}")
    
    # Test full routing
    result = await routing_component.determine_target_agent(message, context)
    print(f"Actual result: {result}")
    
    if result == "composable-marketing-ai":
        print("✅ PASSED - Correctly stayed with marketing agent")
    else:
        print(f"❌ FAILED - Expected composable-marketing-ai, got {result}")


if __name__ == "__main__":
    asyncio.run(test_who_are_you())
