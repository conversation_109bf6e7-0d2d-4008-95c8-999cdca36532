/**
 * React Query hooks for feedback functionality
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  feedbackApi, 
  MessageFeedback, 
  ConversationFeedback, 
  PersonaReview,
  FeedbackAnalytics,
  // Assuming SurveyResponse type will be defined in api.ts or here for the hook
  // For now, let's assume it returns 'any' or a specific SurveyResponse type if defined
} from '@/lib/api';
import { useToast } from './use-toast';

// Define SurveyResponse type if not already in api.ts, or import if it is
// For the purpose of this hook, we'll define the expected payload structure.
interface SubmitSurveyResponsePayload {
  survey_id: string;
  responses: Record<string, any>;
  completion_time_seconds?: number;
}

// Query keys
export const feedbackKeys = {
  all: ['feedback'] as const,
  analytics: (timePeriod?: string) => [...feedbackKeys.all, 'analytics', timePeriod] as const,
  nps: (timePeriod?: string) => [...feedbackKeys.all, 'nps', timePeriod] as const,
};

/**
 * Hook to submit message feedback (thumbs up/down)
 */
export function useMessageFeedback() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (feedback: MessageFeedback) => feedbackApi.submitMessageFeedback(feedback),
    onSuccess: () => {
      toast({
        title: "Feedback Submitted",
        description: "Thank you for your feedback!",
      });
      // Invalidate analytics queries to update feedback data
      queryClient.invalidateQueries({ queryKey: feedbackKeys.all });
    },
    onError: (error) => {
      toast({
        title: "Feedback Failed",
        description: error instanceof Error ? error.message : "Failed to submit feedback",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to submit conversation feedback
 */
export function useConversationFeedback() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (feedback: ConversationFeedback) => feedbackApi.submitConversationFeedback(feedback),
    onSuccess: () => {
      toast({
        title: "Conversation Rated",
        description: "Thank you for rating this conversation!",
      });
      queryClient.invalidateQueries({ queryKey: feedbackKeys.all });
    },
    onError: (error) => {
      toast({
        title: "Rating Failed",
        description: error instanceof Error ? error.message : "Failed to submit rating",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to submit persona review
 */
export function usePersonaReview() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (review: PersonaReview) => feedbackApi.submitPersonaReview(review),
    onSuccess: () => {
      toast({
        title: "Review Submitted",
        description: "Thank you for reviewing this AI persona!",
      });
      queryClient.invalidateQueries({ queryKey: feedbackKeys.all });
    },
    onError: (error) => {
      toast({
        title: "Review Failed",
        description: error instanceof Error ? error.message : "Failed to submit review",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to fetch feedback analytics (admin)
 */
export function useFeedbackAnalytics(timePeriod: string = 'week') {
  return useQuery({
    queryKey: feedbackKeys.analytics(timePeriod),
    queryFn: () => feedbackApi.getFeedbackAnalytics(timePeriod),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });
}

/**
 * Hook to fetch NPS analytics (admin)
 */
export function useNPSAnalytics(timePeriod: string = 'month') {
  return useQuery({
    queryKey: feedbackKeys.nps(timePeriod),
    queryFn: () => feedbackApi.getNPSAnalytics(timePeriod),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  });
}

/**
 * Utility hook for quick feedback actions
 */
export function useQuickFeedback() {
  const messageFeedback = useMessageFeedback();
  const conversationFeedback = useConversationFeedback();

  const submitThumbsUp = (messageId: string, conversationId?: string, personaId?: string) => {
    messageFeedback.mutate({
      message_id: messageId,
      rating: 5,
      conversation_id: conversationId,
      persona_id: personaId,
    });
  };

  const submitThumbsDown = (messageId: string, feedbackText?: string, conversationId?: string, personaId?: string) => {
    messageFeedback.mutate({
      message_id: messageId,
      rating: 1,
      feedback_text: feedbackText,
      conversation_id: conversationId,
      persona_id: personaId,
    });
  };

  const rateConversation = (conversationId: string, rating: number, tags?: string[], personaId?: string) => {
    conversationFeedback.mutate({
      conversation_id: conversationId,
      rating,
      feedback_tags: tags,
      persona_id: personaId,
    });
  };

  return {
    submitThumbsUp,
    submitThumbsDown,
    rateConversation,
    isSubmitting: messageFeedback.isPending || conversationFeedback.isPending,
  };
}

/**
 * Hook to submit survey response
 */
export function useSubmitSurveyResponse() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (response: SubmitSurveyResponsePayload) => 
      feedbackApi.submitSurveyResponse(response),
    onSuccess: () => {
      toast({
        title: "Survey Submitted",
        description: "Thank you for completing the survey!",
      });
      // Optionally invalidate queries related to surveys or user activity
      // queryClient.invalidateQueries({ queryKey: ['surveys'] }); 
    },
    onError: (error) => {
      toast({
        title: "Survey Submission Failed",
        description: error instanceof Error ? error.message : "Failed to submit survey",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for feedback state management
 */
export function useFeedbackState() {
  const analytics = useFeedbackAnalytics();
  const nps = useNPSAnalytics();

  return {
    analytics: analytics.data,
    nps: nps.data,
    isLoading: analytics.isLoading || nps.isLoading,
    isError: analytics.isError || nps.isError,
    error: analytics.error || nps.error,
    refetch: () => {
      analytics.refetch();
      nps.refetch();
    },
  };
}
