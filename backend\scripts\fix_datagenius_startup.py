#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix and restart the Datagenius server properly.
This script addresses the classyweb migration issues and ensures proper startup.
"""

import os
import sys
import subprocess
import time
import psutil
import logging
from pathlib import Path
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_datagenius_root():
    """Find the datagenius backend root directory."""
    current_dir = Path(__file__).parent.parent  # Go up from scripts/ to backend/

    # Verify this is the datagenius project
    datagenius_markers = [
        current_dir / "app" / "main.py",
        current_dir / "agents" / "analysis_agent" / "composable_agent.py",
        current_dir / "personas" / "composable-analyst.yaml",
        current_dir / "app" / "orchestration" / "orchestrator.py"
    ]

    missing_files = [f for f in datagenius_markers if not f.exists()]
    if missing_files:
        logger.error("This doesn't appear to be the datagenius project!")
        logger.error("Missing files:")
        for f in missing_files:
            logger.error(f"  - {f}")
        return None

    logger.info(f"✓ Verified datagenius project at: {current_dir}")
    return current_dir

def kill_existing_servers():
    """Kill any existing FastAPI/uvicorn servers."""
    logger.info("Checking for existing servers...")
    killed_count = 0

    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info.get('cmdline', [])
            if cmdline and any('uvicorn' in str(cmd) or 'fastapi' in str(cmd) for cmd in cmdline):
                logger.info(f"Killing existing server process: {proc.info['pid']}")
                proc.kill()
                proc.wait(timeout=5)
                killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
            pass

    if killed_count > 0:
        logger.info(f"Killed {killed_count} existing server process(es)")
    else:
        logger.info("No existing servers found")

def check_redis():
    """Check if Redis is running."""
    logger.info("Checking Redis status...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
        r.ping()
        logger.info("✓ Redis is running")
        return True
    except ImportError:
        logger.warning("Redis package not installed")
        return False
    except Exception as e:
        logger.warning(f"✗ Redis is not running: {e}")
        logger.info("To start Redis:")
        logger.info("  Option 1: docker run -d -p 6379:6379 redis:alpine")
        logger.info("  Option 2: Install and start Redis locally")
        return False

def check_database():
    """Check database configuration."""
    logger.info("Checking database configuration...")

    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        logger.warning("DATABASE_URL not set in environment")
        return False

    logger.info(f"Database URL: {database_url}")

    # Check if using PostgreSQL and if it's accessible
    if database_url.startswith("postgresql://"):
        try:
            # Try to import psycopg2 or psycopg2-binary
            try:
                import psycopg2
            except ImportError:
                import psycopg2_binary as psycopg2

            # Try to connect briefly
            conn = psycopg2.connect(database_url)
            conn.close()
            logger.info("✓ PostgreSQL database is accessible")
            return True
        except ImportError:
            logger.warning("psycopg2 or psycopg2-binary package not installed for PostgreSQL")
            logger.info("Install with: pip install psycopg2-binary")
            return False
        except Exception as e:
            logger.warning(f"✗ PostgreSQL database not accessible: {e}")
            logger.info("Make sure PostgreSQL is running and database exists")
            return False

    # For SQLite, just check if directory is writable
    elif database_url.startswith("sqlite://"):
        db_path = database_url.replace("sqlite:///", "")
        db_dir = Path(db_path).parent
        if db_dir.exists() and os.access(db_dir, os.W_OK):
            logger.info("✓ SQLite database directory is writable")
            return True
        else:
            logger.warning(f"✗ SQLite database directory not writable: {db_dir}")
            return False

    logger.info("✓ Database configuration looks good")
    return True

def verify_agent_registration():
    """Verify that agents are properly registered."""
    logger.info("Verifying agent registration...")

    try:
        # Import and check agent registry
        sys.path.insert(0, str(find_datagenius_root()))
        from agents.registry import AgentRegistry

        registered_agents = AgentRegistry.list_registered_personas()
        logger.info(f"Registered agents: {registered_agents}")

        required_agents = [
            "composable-analysis-ai",
            "composable-marketing-ai",
            "composable-classifier-ai",
            "concierge-agent"
        ]

        missing_agents = [agent for agent in required_agents if agent not in registered_agents]
        if missing_agents:
            logger.warning(f"Missing agents: {missing_agents}")
            return False

        logger.info("✓ All required agents are registered")
        return True

    except Exception as e:
        logger.error(f"Error checking agent registration: {e}")
        return False

def start_server(backend_root):
    """Start the Datagenius server."""
    logger.info("Starting Datagenius server...")

    # Change to backend directory
    os.chdir(backend_root)

    # Set environment variables
    env = os.environ.copy()
    env['PYTHONPATH'] = str(backend_root)

    # Start the server
    cmd = [sys.executable, "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    logger.info(f"Running command: {' '.join(cmd)}")
    logger.info(f"Working directory: {backend_root}")
    logger.info(f"PYTHONPATH: {env.get('PYTHONPATH')}")

    process = subprocess.Popen(cmd, env=env)
    return process

def main():
    """Main function to fix and restart the server."""
    logger.info("=== Datagenius Server Fix & Restart Script ===")

    # Find backend root
    backend_root = find_datagenius_root()
    if not backend_root:
        logger.error("Could not find datagenius backend directory")
        sys.exit(1)

    # Load environment variables from .env file
    env_file = backend_root / ".env"
    if env_file.exists():
        load_dotenv(env_file)
        logger.info(f"✓ Loaded environment variables from {env_file}")
    else:
        logger.warning(f"✗ No .env file found at {env_file}")
        logger.info("Using system environment variables only")

    # Check prerequisites
    redis_ok = check_redis()
    db_ok = check_database()

    if not db_ok:
        logger.error("Database configuration issues detected")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)

    # Kill existing servers
    kill_existing_servers()
    time.sleep(2)

    # Verify agent registration
    agents_ok = verify_agent_registration()
    if not agents_ok:
        logger.warning("Agent registration issues detected, but continuing...")

    # Start new server
    try:
        process = start_server(backend_root)

        logger.info(f"✓ Server started with PID: {process.pid}")
        logger.info("✓ Server should be available at: http://localhost:8000")
        logger.info("✓ API docs available at: http://localhost:8000/docs")
        logger.info("✓ Frontend should connect at: http://localhost:5173")

        if not redis_ok:
            logger.warning("⚠ Redis not running - authentication features may not work")

        logger.info("\nPress Ctrl+C to stop the server")

        # Wait for the process
        try:
            process.wait()
        except KeyboardInterrupt:
            logger.info("\nShutting down server...")
            process.terminate()
            process.wait(timeout=5)
            logger.info("✓ Server stopped")

    except Exception as e:
        logger.error(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
