/**
 * API service for interacting with the backend.
 */
import { cache } from '@/utils/cache';
import { requestQueue } from '@/utils/requestQueue';
import { authApi } from './authApi';
import { isTokenExpired, shouldRefreshToken } from '@/utils/jwt';
import { providerApi, type Provider, type ProviderModel } from './providerApi'; // Import types
import {
  DashboardLayoutResponse,
  SectionCreate,
  SectionUpdate,
  SectionResponse,
  WidgetCreate,
  WidgetUpdate,
  WidgetResponse,
  WidgetMoveRequest,
  WidgetInsightResponse,
  DashboardResponse,
  DashboardCreate,
  DashboardUpdate,
  DashboardDataSourceAssignment,
  DashboardDataSourceAssignmentCreate,
  DashboardDataSourceAssignmentUpdate
} from '@/types/dashboard-customization';

// Re-export providerApi and types for use in components
export { providerApi };
export type { Provider, ProviderModel }; // Use export type

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Types
export interface Conversation {
  id: string;
  user_id: number;
  persona_id: string;
  title: string;
  created_at: string;
  updated_at: string;
  messages: Message[];
}

export interface Message {
  id: string;
  conversation_id: string;
  sender: 'user' | 'ai';
  content: string;
  metadata?: any;
  created_at: string;
}

export interface SendMessageRequest {
  conversation_id: string;
  message: string;
  context?: any;
}

export interface SendMessageResponse {
  conversation_id: string;
  user_message: Message;
  ai_message: Message;
}

export interface ConversationListResponse {
  conversations: Conversation[];
  total: number;
}

export interface Persona {
  id: string;
  name: string;
  description: string;
  industry: string;
  skills: string[];
  rating: number;
  reviewCount: number;
  imageUrl: string;
  avatarUrl?: string;
  isAvailable: boolean;
  isPurchased?: boolean;
  capabilities?: string[];
}

// Helper function to get the auth token
const getToken = (): string | null => {
  return localStorage.getItem('token');
};

// Removed CustomRequestOptions and ApiRequestOptions to simplify and avoid type conflicts

// Track if a token refresh is in progress
let isRefreshing = false;
let refreshPromise: Promise<boolean> | null = null;

// Store pending requests that are waiting for token refresh
const pendingRequests: Array<() => void> = [];

// Helper function to handle authentication errors
const handleAuthError = async () => {
  const refreshToken = localStorage.getItem('refresh_token');

  // If no refresh token is available, log out immediately
  if (!refreshToken) {
    console.log('No refresh token available, clearing auth state');
    // Clear tokens
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('currentConversationId');
    localStorage.removeItem('currentPersonaId');

    // Throw an error that will be caught by the component
    throw new Error('Authentication failed. Your session has expired. Please sign in again.');
  }

  // If a refresh is already in progress, wait for it to complete
  if (isRefreshing) {
    console.log('Token refresh already in progress, waiting...');
    return refreshPromise;
  }

  // Start the refresh process
  console.log('Starting token refresh process...');
  isRefreshing = true;

  try {
    // Create a new promise for the refresh operation
    refreshPromise = new Promise(async (resolve) => {
      try {
        console.log('Attempting to refresh token...');
        // Attempt to refresh the token
        const response = await authApi.refreshToken(refreshToken);
        console.log('Token refresh successful');

        // Store the new tokens
        localStorage.setItem('token', response.access_token);
        if (response.refresh_token) {
          localStorage.setItem('refresh_token', response.refresh_token);
          console.log('New refresh token stored');
        } else {
          console.log('No new refresh token received, keeping existing one');
        }

        // Resolve all pending requests
        console.log(`Resolving ${pendingRequests.length} pending requests`);
        pendingRequests.forEach(callback => callback());
        pendingRequests.length = 0;

        resolve(true);
      } catch (error) {
        console.error('Token refresh failed:', error);

        // Check if it's a specific error that we can handle
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';

        // If it's a device fingerprint or security error, try a more lenient approach
        if (errorMessage.includes('device fingerprint') || errorMessage.includes('Security validation')) {
          console.log('Device fingerprint mismatch, attempting refresh without fingerprint validation');
          // We could implement a fallback here, but for now just clear tokens
        }

        // If refresh fails, clear tokens and reject
        localStorage.removeItem('token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('currentConversationId');
        localStorage.removeItem('currentPersonaId');

        // Reject all pending requests
        pendingRequests.length = 0;

        resolve(false);
        throw new Error('Authentication failed. Your session has expired. Please sign in again.');
      } finally {
        isRefreshing = false;
        refreshPromise = null;
      }
    });

    return await refreshPromise;
  } catch (error) {
    throw error;
  }
};

// Define custom options interface for our API requests
interface CustomRequestOptions extends Omit<RequestInit, 'cache'> {
  cache?: boolean; // Our custom cache flag, not the standard RequestCache
  cacheTtl?: number; // Cache time-to-live in milliseconds
}

// Helper function for API requests with custom options
export const apiRequest = async (endpoint: string, options: CustomRequestOptions = {}) => {
  // Extract custom options
  const { cache: useCache, cacheTtl, ...standardOptions } = options;

  // Create the request function
  const executeRequestWithAuth = async (): Promise<any> => {
    let token = getToken();

    // Check if token exists and needs refreshing before making the request
    if (token && shouldRefreshToken(token)) {
      try {
        // Try to refresh the token proactively
        await handleAuthError();
        // Get the new token after refresh
        token = getToken();
      } catch (error) {
        // If refresh fails, continue with the current token
        console.warn('Proactive token refresh failed, continuing with current token');
      }
    }

    // Don't set Content-Type for FormData - let browser set it automatically
    const headers: Record<string, string> = {
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      ...standardOptions.headers, // Include original headers
    };

    // Only set Content-Type to application/json if it's not FormData and not already set
    if (!(standardOptions.body instanceof FormData) && !headers['Content-Type'] && !headers['content-type']) {
      headers['Content-Type'] = 'application/json';
    }

    // Check if we should use cache
    if (useCache) {
      const cacheKey = `${endpoint}`;
      const cachedData = cache.get(cacheKey);
      if (cachedData) {
        console.log(`Using cached data for ${endpoint}`);
        return cachedData;
      }
    }

    try {
      // Log simplified options
      console.log('API Request options:', { endpoint, method: standardOptions.method || 'GET' });

      // Make the request with standard fetch options only
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...standardOptions, // Pass only standard fetch options
        headers,
      });

      // Handle authentication errors
      if (response.status === 401) {
        console.log('Authentication error, session expired');

        // Try to refresh the token
        const refreshSuccessful = await handleAuthError();

        if (refreshSuccessful) {
          // If refresh was successful, retry the original request
          return executeRequestWithAuth();
        } else {
          // If refresh failed, throw an error
          throw new Error('Authentication failed. Your session has expired. Please sign in again.');
        }
      }

      if (!response.ok) {
        const error = await response.json().catch(() => ({ detail: 'An error occurred' }));
        throw new Error(error.detail || 'An error occurred');
      }

      const data = await response.json();

      // Store in cache if caching is enabled
      if (useCache) {
        const cacheKey = `${endpoint}`;
        cache.set(cacheKey, data, { ttl: cacheTtl });
        console.log(`Cached data for ${endpoint} with TTL ${cacheTtl}ms`);
      }

      return data;
    } catch (error) {
      console.error(`API request error for ${endpoint}:`, error);
      throw error;
    }
  };

  // Execute the request directly
  try {
    return await executeRequestWithAuth(); // Ensure await here
  } catch (error) {
    console.error(`API request error for ${endpoint}:`, error);
    throw error;
  }
};

// Chat API functions
export const chatApi = {
  // Create a new conversation
  createConversation: async (persona_id: string, title?: string): Promise<Conversation> => {
    return apiRequest('/chat/conversations', {
      method: 'POST',
      body: JSON.stringify({ persona_id, title }),
    });
  },

  // Get a list of conversations
  getConversations: async (skip = 0, limit = 100): Promise<ConversationListResponse> => {
    // Removed cache, queue, priority options
    return apiRequest(`/chat/conversations?skip=${skip}&limit=${limit}`);
  },

  // Get a specific conversation with messages
  getConversation: async (conversation_id: string): Promise<Conversation> => {
    // Removed cache, queue, priority options
    return apiRequest(`/chat/conversations/${conversation_id}`);
  },

  // Update a conversation's title
  updateConversation: async (conversation_id: string, title: string): Promise<Conversation> => {
    return apiRequest(`/chat/conversations/${conversation_id}`, {
      method: 'PUT',
      body: JSON.stringify({ title }),
    });
  },

  // Delete a conversation
  deleteConversation: async (conversation_id: string): Promise<{ message: string }> => {
    return apiRequest(`/chat/conversations/${conversation_id}`, {
      method: 'DELETE',
    });
  },

  // Send a message to an agent
  sendMessage: async (request: SendMessageRequest): Promise<SendMessageResponse> => {
    return apiRequest('/chat/send', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },
};

// Agent API functions
export const agentApi = {
  // Invoke an agent directly
  invokeAgent: async (persona_id: string, request: {
    message: string;
    conversation_id: string;
    context?: any;
    config?: any;
  }): Promise<any> => {
    return apiRequest(`/agents/${persona_id}/invoke`, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  },

  // Get agent capabilities
  getAgentCapabilities: async (persona_id: string): Promise<{ capabilities: string[] }> => {
    return apiRequest(`/agents/${persona_id}/capabilities`);
  },
};

// File API functions
export const fileApi = {
  // Get a list of all files
  getFiles: async (skip = 0, limit = 100): Promise<{ files: any[] }> => {
    return apiRequest(`/files?skip=${skip}&limit=${limit}`);
  },

  // Get a specific file
  getFile: async (fileId: string): Promise<any> => {
    return apiRequest(`/files/${fileId}`);
  },

  // Get related datasources for a file
  getFileRelatedDatasources: async (fileId: string): Promise<{ file_id: string; related_datasources: any[] }> => {
    return apiRequest(`/files/${fileId}/related-datasources`);
  },

  // Delete a file
  deleteFile: async (fileId: string, deleteRelatedDatasources: boolean = false): Promise<{ message: string }> => {
    return apiRequest(`/files/${fileId}?delete_related_datasources=${deleteRelatedDatasources}`, {
      method: 'DELETE',
    });
  },

  // Upload a file
  uploadFile: async (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);

    // Use our apiRequest function with FormData
    return apiRequest('/files', {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header - let browser set it for FormData
      headers: {},
    });
  },
};

// Dashboard API types - Semantic naming for Datagenius platform
// Dashboard metrics types removed - metrics functionality removed

// Feedback API types
export interface MessageFeedback {
  message_id: string;
  rating: number;
  feedback_text?: string;
  conversation_id?: string;
  persona_id?: string;
}

export interface ConversationFeedback {
  conversation_id: string;
  persona_id?: string;
  rating: number;
  feedback_tags?: string[];
  feedback_text?: string;
}

export interface PersonaReview {
  persona_id: string;
  rating: number;
  review_text: string;
}

export interface FeedbackAnalytics {
  average_rating?: number;
  sentiment_distribution?: Record<string, number>;
  common_issues?: string[];
  improvement_suggestions?: string[];
  nps_score?: number;
}

// Notification API types
export interface Notification {
  id: string;
  user_id: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  created_at: string;
  is_read: boolean;
  urgency: 'low' | 'medium' | 'high';
}

export interface NotificationPrefs {
  user_id: string;
  receive_realtime: boolean;
  receive_email: boolean;
  disabled_types: string[];
}

// Report API types
export interface ReportConfig {
  report_name: string;
  data_source_id: string;
  filters?: Record<string, any>;
  group_by?: string[];
  aggregations?: Record<string, string>;
  chart_type?: 'bar' | 'line' | 'pie' | 'table';
  user_id: string;
}

export interface Report {
  report_id: string;
  report_name: string;
  generated_at: string;
  config_used: ReportConfig;
  data: Array<Record<string, any>>;
  summary?: Record<string, any>;
  visualization_url?: string;
}

// Dashboard metrics API removed - only customization features remain

// Dashboard Customization API functions
export const dashboardCustomizationApi = {
  // Get complete dashboard layout for current user
  getDashboardLayout: async (): Promise<DashboardLayoutResponse> => {
    return apiRequest('/api/dashboard/customization/layout');
  },

  // Section management
  createSection: async (section: SectionCreate): Promise<SectionResponse> => {
    return apiRequest('/api/dashboard/customization/sections', {
      method: 'POST',
      body: JSON.stringify(section),
    });
  },

  updateSection: async (sectionId: string, section: SectionUpdate): Promise<SectionResponse> => {
    return apiRequest(`/api/dashboard/customization/sections/${sectionId}`, {
      method: 'PUT',
      body: JSON.stringify(section),
    });
  },

  deleteSection: async (sectionId: string): Promise<{ message: string }> => {
    return apiRequest(`/api/dashboard/customization/sections/${sectionId}`, {
      method: 'DELETE',
    });
  },

  duplicateSection: async (sectionId: string): Promise<SectionResponse> => {
    return apiRequest(`/api/dashboard/customization/sections/${sectionId}/duplicate`, {
      method: 'POST',
    });
  },

  // Widget management
  createWidget: async (widget: WidgetCreate): Promise<WidgetResponse> => {
    return apiRequest('/api/dashboard/customization/widgets', {
      method: 'POST',
      body: JSON.stringify(widget),
    });
  },

  updateWidget: async (widgetId: string, widget: WidgetUpdate): Promise<WidgetResponse> => {
    return apiRequest(`/api/dashboard/customization/widgets/${widgetId}`, {
      method: 'PUT',
      body: JSON.stringify(widget),
    });
  },

  deleteWidget: async (widgetId: string): Promise<{ message: string }> => {
    return apiRequest(`/api/dashboard/customization/widgets/${widgetId}`, {
      method: 'DELETE',
    });
  },

  moveWidget: async (moveRequest: WidgetMoveRequest): Promise<WidgetResponse> => {
    return apiRequest('/api/dashboard/customization/widgets/move', {
      method: 'POST',
      body: JSON.stringify(moveRequest),
    });
  },

  // Widget insights
  getWidgetInsights: async (widgetId: string): Promise<WidgetInsightResponse> => {
    return apiRequest(`/api/dashboard/customization/widgets/${widgetId}/insights`);
  },
};

// Dashboard Management API functions
export const dashboardManagementApi = {
  // Dashboard CRUD operations
  getDashboards: async (): Promise<DashboardResponse[]> => {
    return apiRequest('/api/dashboards');
  },

  createDashboard: async (dashboard: DashboardCreate): Promise<DashboardResponse> => {
    return apiRequest('/api/dashboards', {
      method: 'POST',
      body: JSON.stringify(dashboard),
    });
  },

  getDashboard: async (dashboardId: string): Promise<DashboardResponse> => {
    return apiRequest(`/api/dashboards/${dashboardId}`);
  },

  updateDashboard: async (dashboardId: string, dashboard: DashboardUpdate): Promise<DashboardResponse> => {
    return apiRequest(`/api/dashboards/${dashboardId}`, {
      method: 'PUT',
      body: JSON.stringify(dashboard),
    });
  },

  deleteDashboard: async (dashboardId: string): Promise<{ message: string }> => {
    return apiRequest(`/api/dashboards/${dashboardId}`, {
      method: 'DELETE',
    });
  },

  getDashboardLayout: async (dashboardId: string): Promise<DashboardLayoutResponse> => {
    return apiRequest(`/api/dashboards/${dashboardId}/layout`);
  },

  duplicateDashboard: async (dashboardId: string, name?: string): Promise<DashboardResponse> => {
    const params = name ? `?name=${encodeURIComponent(name)}` : '';
    return apiRequest(`/api/dashboards/${dashboardId}/duplicate${params}`, {
      method: 'POST',
    });
  },

  setDefaultDashboard: async (dashboardId: string): Promise<{ message: string }> => {
    return apiRequest(`/api/dashboards/${dashboardId}/set-default`, {
      method: 'POST',
    });
  },

  getDefaultDashboard: async (): Promise<DashboardResponse> => {
    return apiRequest('/api/dashboards/default');
  },

  initializeDefaultDashboard: async (): Promise<DashboardResponse> => {
    return apiRequest('/api/dashboards/initialize-default', {
      method: 'POST',
    });
  },

  // Dashboard Data Source Management
  getDashboardDataSources: async (dashboardId: string): Promise<DashboardDataSourceAssignment[]> => {
    return apiRequest(`/api/dashboards/${dashboardId}/data-sources`);
  },

  addDashboardDataSource: async (
    dashboardId: string,
    dataSource: DashboardDataSourceAssignmentCreate
  ): Promise<DashboardDataSourceAssignment> => {
    return apiRequest(`/api/dashboards/${dashboardId}/data-sources`, {
      method: 'POST',
      body: JSON.stringify(dataSource),
    });
  },

  updateDashboardDataSource: async (
    dashboardId: string,
    dataSourceId: string,
    dataSource: DashboardDataSourceAssignmentUpdate
  ): Promise<DashboardDataSourceAssignment> => {
    return apiRequest(`/api/dashboards/${dashboardId}/data-sources/${dataSourceId}`, {
      method: 'PUT',
      body: JSON.stringify(dataSource),
    });
  },

  removeDashboardDataSource: async (dashboardId: string, dataSourceId: string): Promise<{ message: string }> => {
    return apiRequest(`/api/dashboards/${dashboardId}/data-sources/${dataSourceId}`, {
      method: 'DELETE',
    });
  },

  bulkAssignDataSources: async (dashboardId: string, dataSourceIds: string[]): Promise<{
    message: string;
    assigned: number;
    failed: number;
    errors: string[];
  }> => {
    return apiRequest(`/api/dashboards/${dashboardId}/data-sources/bulk-assign`, {
      method: 'POST',
      body: JSON.stringify(dataSourceIds),
    });
  },
};

// Feedback API functions
export const feedbackApi = {
  // Submit message feedback
  submitMessageFeedback: async (feedback: MessageFeedback): Promise<any> => {
    return apiRequest('/api/feedback/message', {
      method: 'POST',
      body: JSON.stringify(feedback),
    });
  },

  // Submit conversation feedback
  submitConversationFeedback: async (feedback: ConversationFeedback): Promise<any> => {
    return apiRequest('/api/feedback/conversation', {
      method: 'POST',
      body: JSON.stringify(feedback),
    });
  },

  // Submit persona review
  submitPersonaReview: async (review: PersonaReview): Promise<any> => {
    return apiRequest('/api/feedback/persona', {
      method: 'POST',
      body: JSON.stringify(review),
    });
  },

  // Get feedback analytics (admin)
  getFeedbackAnalytics: async (timePeriod: string = 'week'): Promise<FeedbackAnalytics> => {
    return apiRequest(`/api/feedback/analytics/trends?time_period=${timePeriod}`);
  },

  // Get NPS analytics (admin)
  getNPSAnalytics: async (timePeriod: string = 'month'): Promise<any> => {
    return apiRequest(`/api/feedback/analytics/nps?time_period=${timePeriod}`);
  },

  // Submit survey response
  submitSurveyResponse: async (response: {
    survey_id: string;
    responses: Record<string, any>;
    completion_time_seconds?: number;
  }): Promise<any> => { // Backend returns SurveyResponseModel
    return apiRequest('/api/feedback/surveys/responses', {
      method: 'POST',
      body: JSON.stringify(response),
    });
  },
};

// Notifications API functions
export const notificationsApi = {
  // Get user notifications
  getNotifications: async (limit: number = 20, offset: number = 0): Promise<{ notifications: Notification[] }> => {
    return apiRequest(`/api/notifications?limit=${limit}&offset=${offset}`);
  },

  // Mark notification as read
  markAsRead: async (notificationId: string): Promise<void> => {
    return apiRequest(`/api/notifications/${notificationId}/read`, {
      method: 'PUT',
    });
  },

  // Mark all notifications as read
  markAllAsRead: async (): Promise<void> => {
    return apiRequest('/api/notifications/mark-all-read', {
      method: 'PUT',
    });
  },

  // Get notification preferences
  getPreferences: async (): Promise<NotificationPrefs> => {
    return apiRequest('/api/notifications/preferences');
  },

  // Update notification preferences
  updatePreferences: async (prefs: Partial<NotificationPrefs>): Promise<NotificationPrefs> => {
    return apiRequest('/api/notifications/preferences', {
      method: 'PUT',
      body: JSON.stringify(prefs),
    });
  },
};

// Reports API functions
export const reportsApi = {
  // Generate a report
  generateReport: async (config: ReportConfig): Promise<Report> => {
    return apiRequest('/api/reports/generate', {
      method: 'POST',
      body: JSON.stringify(config),
    });
  },

  // Get user reports
  getReports: async (limit: number = 20, offset: number = 0): Promise<{ reports: Report[] }> => {
    return apiRequest(`/api/reports?limit=${limit}&offset=${offset}`);
  },

  // Get a specific report
  getReport: async (reportId: string): Promise<Report> => {
    return apiRequest(`/api/reports/${reportId}`);
  },

  // Export report
  exportReport: async (reportId: string, format: string): Promise<Blob> => {
    const response = await fetch(`${API_BASE_URL}/api/reports/${reportId}/export?format=${format}`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to export report');
    }

    return response.blob();
  },
};

// Persona API functions
export const personaApi = {
  // Get a list of all personas
  getPersonas: async (industry?: string): Promise<{ personas: Persona[] }> => {
    const endpoint = industry ? `/personas?industry=${industry}` : '/personas';
    // Removed cache, queue, priority options
    return apiRequest(endpoint);
  },

  // Get a specific persona
  getPersona: async (persona_id: string): Promise<Persona> => {
    // Removed cache, queue, priority options
    return apiRequest(`/personas/${persona_id}`);
  },

  // Check if a user has access to a persona
  checkPersonaAccess: async (persona_id: string): Promise<{ has_access: boolean, is_purchased: boolean }> => {
    // Removed cache, queue, priority options
    return apiRequest(`/personas/access/${persona_id}`);
  },

  // Get a list of purchased personas
  getPurchasedPersonas: async (): Promise<string[]> => {
    try {
      // Removed cache, queue, priority options
      const result = await apiRequest('/personas/purchased');

      console.log('Purchased personas response:', result);

      // Handle both array and non-array responses
      if (Array.isArray(result)) {
        return result;
      } else {
        console.warn('Unexpected response format from purchased personas endpoint:', result);
        return [];
      }
    } catch (error) {
      console.error('Error fetching purchased personas:', error);
      // Return empty array as fallback
      return [];
    }
  },

  // Get a list of all industries
  getIndustries: async (): Promise<{ industries: string[] }> => {
    // Removed cache, queue, priority options
    return apiRequest('/personas/industries');
  },
};

// WebSocket connection for real-time chat
export class ChatWebSocket {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: number = 1000; // Start with 1 second
  private pingInterval: number | null = null;
  private token: string | null = null;
  private conversationId: string | null = null;
  private messageHandlers: ((data: any) => void)[] = [];
  private statusHandlers: ((status: 'connecting' | 'connected' | 'disconnected' | 'error') => void)[] = [];
  private deliveryHandlers: ((messageId: string, status: 'delivered' | 'failed') => void)[] = [];
  private typingHandlers: ((isTyping: boolean) => void)[] = [];

  constructor() {
    this.token = getToken();
  }

  // Connect to the WebSocket server
  connect(conversationId: string): void {
    if (!this.token) {
      this.notifyStatus('error');
      console.error('No authentication token available');
      return;
    }

    this.conversationId = conversationId;
    this.notifyStatus('connecting');

    // Use the API_BASE_URL to determine the WebSocket URL
    const apiBaseUrl = API_BASE_URL || '';
    const wsBaseUrl = apiBaseUrl.replace(/^http/, 'ws');

    // Ensure the conversation ID is properly encoded
    const encodedConversationId = encodeURIComponent(conversationId);
    const encodedToken = encodeURIComponent(this.token);

    // If there's a custom API base URL, use it; otherwise, fall back to the current host
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

    // Construct the WebSocket URL with the token as a query parameter
    let wsUrl: string;
    if (wsBaseUrl) {
      wsUrl = `${wsBaseUrl}/chat/ws/${encodedConversationId}?token=${encodedToken}`;
    } else {
      wsUrl = `${wsProtocol}//${window.location.host}/chat/ws/${encodedConversationId}?token=${encodedToken}`;
    }

    // Log the WebSocket URL for debugging (hide token)
    console.log(`🔗 Connecting to WebSocket URL: ${wsUrl.replace(/token=[^&]+/, 'token=***')}`);
    console.log(`🆔 Frontend conversation ID: ${conversationId}`);

    try {
      console.log(`Creating new WebSocket connection to: ${wsUrl}`);
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log(`WebSocket connection established for conversation: ${conversationId}`);
        console.log(`WebSocket readyState: ${this.ws ? this.ws.readyState : 'null'}`);
        this.notifyStatus('connected');
        this.reconnectAttempts = 0;
        this.startPingInterval();

        // Send reconnect message if reconnecting
        if (this.reconnectAttempts > 0) {
          console.log(`Sending reconnect message for conversation: ${conversationId}`);
          this.send({ reconnect: true });
        }
      };

      this.ws.onmessage = (event) => {
        try {
          // Only log non-streaming messages to reduce console verbosity
          const data = JSON.parse(event.data);

          // Only log non-stream_chunk messages
          if (data.type !== 'stream_chunk') {
            console.log('🔍 WebSocket message:', data.type, data);
          }

          // Special logging for streaming events (reduced verbosity)
          if (data.type === 'stream_start' || data.type === 'stream_end') {
            console.log(`🎬 STREAMING EVENT: ${data.type} for message ${data.message_id}`);
          }

          if (data.type === 'user_message' || data.type === 'ai_message' ||
              data.type === 'ai_message_start' || data.type === 'ai_message_complete') {
            console.log(`Message ID: ${data.message?.id}, sender: ${data.message?.sender}, content length: ${data.message?.content?.length || 0}`);
          }

          this.notifyMessageHandlers(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          console.error('Raw message that caused error:', event.data.substring(0, 200));
        }
      };

      this.ws.onclose = (event) => {
        console.log(`WebSocket connection closed for conversation: ${conversationId}`);
        console.log(`Close code: ${event.code}, reason: ${event.reason || 'No reason provided'}`);
        this.notifyStatus('disconnected');
        this.stopPingInterval();

        // Only attempt to reconnect if the close wasn't clean (code 1000)
        if (event.code !== 1000) {
          console.log(`Attempting to reconnect due to abnormal closure (code: ${event.code})`);
          this.attemptReconnect();
        } else {
          console.log('Clean WebSocket closure, not attempting to reconnect');
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        // Log more detailed information about the WebSocket
        console.error('WebSocket readyState:', this.ws ? this.ws.readyState : 'null');
        console.error('WebSocket URL:', wsUrl);
        console.error('Conversation ID:', conversationId);
        this.notifyStatus('error');

        // Attempt to reconnect automatically on error
        this.attemptReconnect();
      };
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      this.notifyStatus('error');
    }
  }

  // Disconnect from the WebSocket server
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.stopPingInterval();
    this.notifyStatus('disconnected');
  }

  // Send a message through the WebSocket
  send(data: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('Sending data through WebSocket:', data);
      this.ws.send(JSON.stringify(data));
    } else {
      console.error('WebSocket is not connected, readyState:', this.ws ? this.ws.readyState : 'null');
      this.notifyStatus('disconnected');
    }
  }

  // Send a chat message
  sendChatMessage(message: string, metadata: any = {}, context: any = {}): void {
    console.log(`Sending chat message through WebSocket to conversation ${this.conversationId}`);
    console.log(`Message content: "${message.substring(0, 30)}${message.length > 30 ? '...' : ''}"`);
    console.log('Message metadata:', metadata);
    console.log('Message context:', context);

    this.send({
      message,
      metadata,
      context,
    });
  }

  // Add a message handler
  onMessage(handler: (data: any) => void): () => void {
    this.messageHandlers.push(handler);
    return () => {
      this.messageHandlers = this.messageHandlers.filter(h => h !== handler);
    };
  }

  // Add a status handler
  onStatus(handler: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void): () => void {
    this.statusHandlers.push(handler);
    return () => {
      this.statusHandlers = this.statusHandlers.filter(h => h !== handler);
    };
  }

  // Add a delivery confirmation handler
  onDelivery(handler: (messageId: string, status: 'delivered' | 'failed') => void): () => void {
    this.deliveryHandlers.push(handler);
    return () => {
      this.deliveryHandlers = this.deliveryHandlers.filter(h => h !== handler);
    };
  }

  // Add a typing indicator handler
  onTyping(handler: (isTyping: boolean) => void): () => void {
    this.typingHandlers.push(handler);
    return () => {
      this.typingHandlers = this.typingHandlers.filter(h => h !== handler);
    };
  }

  // Notify all message handlers
  private notifyMessageHandlers(data: any): void {
    // Process special message types
    if (data.type === 'typing_indicator' && this.typingHandlers.length > 0) {
      this.notifyTyping(data.is_typing);
    }

    if (data.type === 'message_delivered' && this.deliveryHandlers.length > 0) {
      this.notifyDelivery(data.message_id, 'delivered');
    }

    if (data.type === 'message_failed' && this.deliveryHandlers.length > 0) {
      this.notifyDelivery(data.message_id, 'failed');
    }

    // Handle streaming response chunks
    if (data.type === 'stream_start') {
      // Stream start event - create an empty message that will be updated
      console.log('🚀 ChatWebSocket: Stream started for message:', data.message_id);
    }

    if (data.type === 'stream_chunk') {
      // Stream chunk event - update the message with the new content
      console.log('📦 ChatWebSocket: Stream chunk received for message:', data.message_id, 'content:', data.content);
    }

    if (data.type === 'stream_end') {
      // Stream end event - finalize the message
      console.log('🏁 ChatWebSocket: Stream ended for message:', data.message_id);
    }

    // Notify all general message handlers
    this.messageHandlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error('Error in message handler:', error);
      }
    });
  }

  // Notify all status handlers
  private notifyStatus(status: 'connecting' | 'connected' | 'disconnected' | 'error'): void {
    this.statusHandlers.forEach(handler => {
      try {
        handler(status);
      } catch (error) {
        console.error('Error in status handler:', error);
      }
    });
  }

  // Notify all delivery handlers
  private notifyDelivery(messageId: string, status: 'delivered' | 'failed'): void {
    this.deliveryHandlers.forEach(handler => {
      try {
        handler(messageId, status);
      } catch (error) {
        console.error('Error in delivery handler:', error);
      }
    });
  }

  // Notify all typing handlers
  private notifyTyping(isTyping: boolean): void {
    this.typingHandlers.forEach(handler => {
      try {
        handler(isTyping);
      } catch (error) {
        console.error('Error in typing handler:', error);
      }
    });
  }

  // Start the ping interval to keep the connection alive
  private startPingInterval(): void {
    this.stopPingInterval();
    this.pingInterval = window.setInterval(() => {
      this.send({ ping: true });
    }, 30000); // Send ping every 30 seconds
  }

  // Stop the ping interval
  private stopPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  // Attempt to reconnect to the WebSocket server
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Maximum reconnect attempts reached');
      this.notifyStatus('error');
      return;
    }

    this.reconnectAttempts++;
    const timeout = this.reconnectTimeout * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${timeout}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    this.notifyStatus('connecting');

    setTimeout(() => {
      if (this.conversationId) {
        this.connect(this.conversationId);
      }
    }, timeout);
  }
}
