/**
 * Floating AI Assistant Modal Component
 * 
 * A sophisticated floating modal window for the Datagenius AI assistant with:
 * - Draggable positioning
 * - Resizable dimensions
 * - Minimize/maximize functionality
 * - Persistent state
 * - Enhanced UX with smooth animations
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import {
  Bot,
  Send,
  Minimize2,
  Maximize2,
  X,
  Move,
  GripVertical,
  Sparkles,
  MessageCircle,
  RefreshCw,
  Mic,
  MicOff,
  Settings,
  BarChart3,
  <PERSON><PERSON>hart,
  LineChart,
  Plus,
  Database,
  Lightbulb,
  TrendingUp,
  Calculator,
  Code,
  Zap,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDashboardMode } from '@/stores/dashboard-mode-store';

interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  suggestions?: string[];
  actions?: AssistantAction[];
}

interface AssistantAction {
  id: string;
  label: string;
  type: 'create_widget' | 'add_section' | 'apply_template' | 'configure_data' | 'analyze_data' | 'optimize_dashboard' | 'show_code' | 'explain_concept';
  icon: React.ComponentType<{ className?: string }>;
  data?: any;
}

interface FloatingAIAssistantProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onWidgetCreate?: (widget_config: any) => void;
  onSectionCreate?: (section_config: any) => void;
  onTemplateApply?: (template_id: string) => void;
  onDataConfigure?: (data_config: any) => void;
  onDataAnalyze?: (analysis_request: any) => void;
  onDashboardOptimize?: (optimization_params: any) => void;
  onCodeGenerate?: (code_request: any) => void;
  className?: string;
}

interface Position {
  x: number;
  y: number;
}

interface Size {
  width: number;
  height: number;
}

export const FloatingAIAssistant: React.FC<FloatingAIAssistantProps> = ({
  isOpen,
  onOpenChange,
  onWidgetCreate,
  onSectionCreate,
  onTemplateApply,
  onDataConfigure,
  onDataAnalyze,
  onDashboardOptimize,
  onCodeGenerate,
  className,
}) => {
  const { current_mode } = useDashboardMode();

  // Debug logging
  console.log('FloatingAIAssistant: Render called with isOpen:', isOpen);
  
  // State management
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  
  // Position and size state
  const [position, setPosition] = useState<Position>({ x: 100, y: 100 });
  const [size, setSize] = useState<Size>({ width: 400, height: 600 });
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragOffset, setDragOffset] = useState<Position>({ x: 0, y: 0 });
  
  // Refs
  const modalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const dragHandleRef = useRef<HTMLDivElement>(null);

  // Initialize with welcome message
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage: Message = {
        id: '1',
        type: 'assistant',
        content: current_mode === 'simple' 
          ? "Hi! I'm your Datagenius AI assistant. I'm here to help you create amazing dashboards with ease. What would you like to build today?"
          : "Welcome to Datagenius Pro. I'm ready to assist with advanced dashboard development, custom queries, and technical analysis. How can I help?",
        timestamp: new Date().toISOString(),
        suggestions: current_mode === 'simple' ? [
          "Create a chart widget",
          "Add a new section",
          "Show me templates",
          "Help with data setup",
        ] : [
          "Generate custom SQL query",
          "Optimize dashboard performance",
          "Create advanced visualization",
          "Configure data sources",
        ],
      };
      setMessages([welcomeMessage]);
    }
  }, [current_mode, messages.length]);

  // Handle dragging
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (isMaximized) return;
    
    setIsDragging(true);
    const rect = modalRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  }, [isMaximized]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || isMaximized) return;
    
    setPosition({
      x: e.clientX - dragOffset.x,
      y: e.clientY - dragOffset.y,
    });
  }, [isDragging, dragOffset, isMaximized]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
  }, []);

  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  // Handle minimize/maximize
  const handleMinimize = () => {
    setIsMinimized(true);
  };

  const handleMaximize = () => {
    setIsMaximized(!isMaximized);
    if (!isMaximized) {
      // Store current position and size before maximizing
      setPosition({ x: 0, y: 0 });
      setSize({ width: window.innerWidth - 100, height: window.innerHeight - 100 });
    } else {
      // Restore to default size
      setPosition({ x: 100, y: 100 });
      setSize({ width: 400, height: 600 });
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  // Message handling
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Simulate AI response (integrate with actual agent later)
      const response = await simulateAIResponse(inputMessage, { mode: current_mode });
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.content,
        timestamp: new Date().toISOString(),
        suggestions: response.suggestions,
        actions: response.actions,
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error getting AI response:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'system',
        content: "I'm sorry, I encountered an error. Please try again.",
        timestamp: new Date().toISOString(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
    inputRef.current?.focus();
  };

  const handleActionClick = (action: AssistantAction) => {
    switch (action.type) {
      case 'create_widget':
        onWidgetCreate?.(action.data);
        break;
      case 'add_section':
        onSectionCreate?.(action.data);
        break;
      case 'apply_template':
        onTemplateApply?.(action.data?.template_id);
        break;
      case 'configure_data':
        onDataConfigure?.(action.data);
        break;
      case 'analyze_data':
        onDataAnalyze?.(action.data);
        break;
      case 'optimize_dashboard':
        onDashboardOptimize?.(action.data);
        break;
      case 'show_code':
        onCodeGenerate?.(action.data);
        break;
      case 'explain_concept':
        // Handle concept explanation by adding it to the conversation
        const explanationMessage: Message = {
          id: Date.now().toString(),
          type: 'assistant',
          content: `Let me explain ${action.data?.concept || 'this concept'} for you...`,
          timestamp: new Date().toISOString(),
        };
        setMessages(prev => [...prev, explanationMessage]);
        break;
    }
  };

  const toggleVoiceInput = () => {
    setIsListening(!isListening);
    // Voice input implementation would go here
  };

  // Minimized state
  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsMinimized(false)}
          className="rounded-full h-14 w-14 shadow-lg bg-blue-600 hover:bg-blue-700 text-white"
          size="lg"
        >
          <Bot className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  if (!isOpen) {
    console.log('FloatingAIAssistant: Not rendering because isOpen is false');
    return null;
  }

  console.log('FloatingAIAssistant: Rendering modal with position:', position, 'size:', size);

  return (
    <div className="fixed inset-0 z-[9999] pointer-events-none">
      <div
        ref={modalRef}
        className={cn(
          "absolute pointer-events-auto bg-white rounded-lg shadow-2xl border border-slate-200",
          "transition-all duration-200 ease-in-out",
          isMaximized && "!fixed !inset-4",
          className
        )}
        style={
          isMaximized
            ? {}
            : {
                left: position.x,
                top: position.y,
                width: size.width,
                height: size.height,
              }
        }
      >
        {/* Header with drag handle */}
        <div
          ref={dragHandleRef}
          className={cn(
            "flex items-center justify-between p-4 floating-ai-header rounded-t-lg",
            !isMaximized && "cursor-move"
          )}
          onMouseDown={handleMouseDown}
        >
          <div className="flex items-center space-x-2">
            <Bot className="h-5 w-5 text-blue-500" />
            <span className="font-semibold text-slate-900">Datagenius AI</span>
            <Badge variant="secondary" className="text-xs">
              {current_mode === 'simple' ? 'Simple Mode' : 'Advanced Mode'}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMessages([])}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMinimize}
              className="h-8 w-8 p-0"
            >
              <Minimize2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMaximize}
              className="h-8 w-8 p-0"
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content area */}
        <div className="flex flex-col h-full">
          {/* Messages */}
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex",
                    message.type === 'user' ? 'justify-end' : 'justify-start'
                  )}
                >
                  <div
                    className={cn(
                      "max-w-[80%] rounded-lg p-3",
                      message.type === 'user'
                        ? 'floating-ai-message-user'
                        : message.type === 'system'
                        ? 'bg-red-50 text-red-800 border border-red-200'
                        : 'floating-ai-message-assistant'
                    )}
                  >
                    <p className="text-sm">{message.content}</p>
                    
                    {/* Suggestions */}
                    {message.suggestions && message.suggestions.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {message.suggestions.map((suggestion, index) => (
                          <Button
                            key={index}
                            variant="outline"
                            size="sm"
                            onClick={() => handleSuggestionClick(suggestion)}
                            className="text-xs h-7"
                          >
                            {suggestion}
                          </Button>
                        ))}
                      </div>
                    )}
                    
                    {/* Actions */}
                    {message.actions && message.actions.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {message.actions.map((action) => (
                          <Button
                            key={action.id}
                            variant="default"
                            size="sm"
                            onClick={() => handleActionClick(action)}
                            className="text-xs h-7"
                          >
                            <action.icon className="h-3 w-3 mr-1" />
                            {action.label}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-slate-100 rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span className="text-sm text-slate-600">Thinking...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Input area */}
          <div className="p-4 border-t border-slate-200">
            <div className="flex items-center space-x-2">
              <Input
                ref={inputRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Ask me anything about your dashboard..."
                className="flex-1 floating-ai-input"
                disabled={isLoading}
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleVoiceInput}
                className={cn(
                  "h-10 w-10 p-0",
                  isListening && "bg-red-100 text-red-600"
                )}
              >
                {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              </Button>
              <Button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isLoading}
                size="sm"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Simulate AI response (to be replaced with actual agent integration)
async function simulateAIResponse(message: string, context: { mode: string }): Promise<{
  content: string;
  suggestions?: string[];
  actions?: AssistantAction[];
}> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  const lowerMessage = message.toLowerCase();

  if (lowerMessage.includes('widget') || lowerMessage.includes('chart')) {
    return {
      content: "I can help you create various types of widgets! What kind of data visualization would you like to add?",
      suggestions: [
        "Create a bar chart",
        "Add a pie chart",
        "Make a line graph",
        "Show me all widget types",
      ],
      actions: [
        {
          id: 'create_bar_chart',
          label: 'Create Bar Chart',
          type: 'create_widget',
          icon: BarChart3,
          data: { type: 'bar_chart' },
        },
        {
          id: 'create_pie_chart',
          label: 'Create Pie Chart',
          type: 'create_widget',
          icon: PieChart,
          data: { type: 'pie_chart' },
        },
      ],
    };
  }

  if (lowerMessage.includes('template')) {
    return {
      content: "I have several dashboard templates available! You can choose from business analytics, sales tracking, marketing metrics, and more. Would you like to see the template gallery?",
      suggestions: [
        "Show business templates",
        "Sales dashboard template",
        "Marketing analytics template",
      ],
      actions: [
        {
          id: 'template_gallery',
          label: 'Browse Templates',
          type: 'apply_template',
          icon: Sparkles,
        },
      ],
    };
  }

  if (lowerMessage.includes('analyze') || lowerMessage.includes('data analysis')) {
    return {
      content: "I can help you analyze your data in various ways! What type of analysis would you like to perform?",
      suggestions: [
        "Analyze trends",
        "Statistical analysis",
        "Performance metrics",
        "Data insights",
      ],
      actions: [
        {
          id: 'analyze_trends',
          label: 'Analyze Trends',
          type: 'analyze_data',
          icon: TrendingUp,
          data: { analysis_type: 'trends' },
        },
        {
          id: 'statistical_analysis',
          label: 'Statistical Analysis',
          type: 'analyze_data',
          icon: Calculator,
          data: { analysis_type: 'statistical' },
        },
      ],
    };
  }

  if (lowerMessage.includes('optimize') || lowerMessage.includes('performance')) {
    return {
      content: "I can help optimize your dashboard for better performance! What aspect would you like to improve?",
      suggestions: [
        "Optimize loading speed",
        "Improve responsiveness",
        "Reduce memory usage",
      ],
      actions: [
        {
          id: 'optimize_performance',
          label: 'Optimize Performance',
          type: 'optimize_dashboard',
          icon: Zap,
          data: { optimization_type: 'performance' },
        },
      ],
    };
  }

  if (lowerMessage.includes('code') || lowerMessage.includes('sql') || lowerMessage.includes('query')) {
    return {
      content: "I can help you with code generation and custom queries! What would you like me to create?",
      suggestions: [
        "Generate SQL query",
        "Create custom component",
        "Write data transformation",
      ],
      actions: [
        {
          id: 'generate_sql',
          label: 'Generate SQL',
          type: 'show_code',
          icon: Code,
          data: { code_type: 'sql' },
        },
        {
          id: 'custom_component',
          label: 'Custom Component',
          type: 'show_code',
          icon: Code,
          data: { code_type: 'component' },
        },
      ],
    };
  }

  return {
    content: context.mode === 'simple'
      ? "I'm here to help you build your dashboard! You can ask me to create widgets, add sections, apply templates, configure data sources, or analyze your data. What would you like to do?"
      : "I'm ready to assist with advanced dashboard development. I can help with custom queries, performance optimization, code generation, data analysis, or complex visualizations. What technical challenge can I help you solve?",
    suggestions: context.mode === 'simple' ? [
      "Create a widget",
      "Add a new section",
      "Show me templates",
      "Help with data",
      "Analyze my data",
    ] : [
      "Generate SQL query",
      "Optimize performance",
      "Create custom visualization",
      "Configure API access",
      "Advanced data analysis",
    ],
  };
}
