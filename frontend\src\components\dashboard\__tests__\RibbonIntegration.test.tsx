/**
 * Integration tests for the Enhanced Dashboard Ribbon Toolbar
 * Tests the ribbon's integration with the dashboard system
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { EnhancedRibbonToolbar } from '../mode/advanced/EnhancedRibbonToolbar';
import { useDashboardHeader } from '../../../hooks/use-dashboard-header';

// Mock the dashboard hook
vi.mock('../../../hooks/use-dashboard-header');

// Mock WebSocket
const mockWebSocket = {
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: WebSocket.OPEN,
};

global.WebSocket = vi.fn(() => mockWebSocket) as any;

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.localStorage = localStorageMock as any;

// Mock fetch for API calls
global.fetch = vi.fn();

describe('Ribbon Integration Tests', () => {
  let queryClient: QueryClient;
  
  const mockDashboardData = {
    id: '1',
    name: 'Test Dashboard',
    layout: {
      widgets: [
        { id: 'widget1', type: 'chart', title: 'Chart 1' },
        { id: 'widget2', type: 'table', title: 'Table 1' },
      ],
      sections: [
        { id: 'section1', title: 'Section 1' },
      ],
    },
    updated_at: '2024-01-01T00:00:00Z',
  };

  const mockDashboardHook = {
    activeDashboard: mockDashboardData,
    currentLayout: mockDashboardData.layout,
    wsConnected: true,
    handleSave: vi.fn(),
    handleRefresh: vi.fn(),
    handleAddWidget: vi.fn(),
    handleAddSection: vi.fn(),
    handleExport: vi.fn(),
    handleShare: vi.fn(),
    setShowWidgetManagement: vi.fn(),
    setShowDashboardSettings: vi.fn(),
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    
    vi.mocked(useDashboardHeader).mockReturnValue(mockDashboardHook as any);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  describe('Dashboard State Integration', () => {
    it('displays correct widget count from dashboard state', () => {
      renderWithProviders(
        <DashboardRibbonToolbar
          widgetCount={mockDashboardData.layout.widgets.length}
          isConnected={true}
        />
      );

      // Widget count should be reflected in the interface
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });

    it('shows connection status correctly', () => {
      renderWithProviders(
        <DashboardRibbonToolbar
          isConnected={false}
          widgetCount={2}
        />
      );

      // Should handle disconnected state gracefully
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });

    it('updates last modified timestamp', () => {
      const lastUpdated = new Date('2024-01-01T12:00:00Z');
      
      renderWithProviders(
        <DashboardRibbonToolbar
          lastUpdated={lastUpdated}
          widgetCount={2}
        />
      );

      // Should display the timestamp (implementation would show formatted time)
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });
  });

  describe('Save and Export Integration', () => {
    it('integrates with dashboard save functionality', async () => {
      const user = userEvent.setup();
      const onSave = vi.fn().mockResolvedValue({ success: true });
      
      renderWithProviders(
        <DashboardRibbonToolbar
          onSave={onSave}
          widgetCount={2}
        />
      );

      const saveButton = screen.getByRole('button', { name: /save dashboard/i });
      await user.click(saveButton);

      expect(onSave).toHaveBeenCalledTimes(1);
    });

    it('handles save errors gracefully', async () => {
      const user = userEvent.setup();
      const onSave = vi.fn().mockRejectedValue(new Error('Save failed'));
      
      renderWithProviders(
        <DashboardRibbonToolbar
          onSave={onSave}
          widgetCount={2}
        />
      );

      const saveButton = screen.getByRole('button', { name: /save dashboard/i });
      await user.click(saveButton);

      expect(onSave).toHaveBeenCalledTimes(1);
      // Error handling would be tested with actual error display
    });

    it('exports dashboard with correct format', async () => {
      const user = userEvent.setup();
      const onExport = vi.fn().mockResolvedValue({ url: 'export-url' });
      
      renderWithProviders(
        <DashboardRibbonToolbar
          onExport={onExport}
          widgetCount={2}
        />
      );

      const exportButton = screen.getByRole('button', { name: /export/i });
      await user.click(exportButton);

      const pdfOption = screen.getByText('PDF Document');
      await user.click(pdfOption);

      expect(onExport).toHaveBeenCalledWith('pdf');
    });
  });

  describe('Widget Management Integration', () => {
    it('adds widgets to dashboard', async () => {
      const user = userEvent.setup();
      const onAddWidget = vi.fn().mockResolvedValue({ id: 'new-widget' });
      
      renderWithProviders(
        <DashboardRibbonToolbar
          onAddWidget={onAddWidget}
          widgetCount={2}
        />
      );

      // Navigate to Insert tab
      const insertTab = screen.getByRole('tab', { name: /insert/i });
      await user.click(insertTab);

      const addWidgetButton = screen.getByRole('button', { name: /add widget/i });
      await user.click(addWidgetButton);

      const chartOption = screen.getByText('Bar Chart');
      await user.click(chartOption);

      expect(onAddWidget).toHaveBeenCalledWith('chart');
    });

    it('manages widget sections', async () => {
      const user = userEvent.setup();
      const onAddSection = vi.fn().mockResolvedValue({ id: 'new-section' });
      
      renderWithProviders(
        <DashboardRibbonToolbar
          onAddSection={onAddSection}
          widgetCount={2}
        />
      );

      // Navigate to Insert tab
      const insertTab = screen.getByRole('tab', { name: /insert/i });
      await user.click(insertTab);

      const sectionButton = screen.getByRole('button', { name: /section/i });
      await user.click(sectionButton);

      expect(onAddSection).toHaveBeenCalledTimes(1);
    });
  });

  describe('Data Source Integration', () => {
    it('refreshes data sources', async () => {
      const user = userEvent.setup();
      const onDataRefresh = vi.fn().mockResolvedValue({ success: true });
      
      renderWithProviders(
        <DashboardRibbonToolbar
          onDataRefresh={onDataRefresh}
          availableDataSources={[
            { id: 'ds1', name: 'Data Source 1', type: 'csv', status: 'connected' },
            { id: 'ds2', name: 'Data Source 2', type: 'api', status: 'disconnected' },
          ]}
          widgetCount={2}
        />
      );

      // Navigate to Data tab
      const dataTab = screen.getByRole('tab', { name: /data/i });
      await user.click(dataTab);

      const refreshButton = screen.getByRole('button', { name: /refresh all data/i });
      await user.click(refreshButton);

      expect(onDataRefresh).toHaveBeenCalledTimes(1);
    });

    it('shows data source status', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(
        <DashboardRibbonToolbar
          availableDataSources={[
            { id: 'ds1', name: 'Connected Source', type: 'csv', status: 'connected' },
            { id: 'ds2', name: 'Disconnected Source', type: 'api', status: 'disconnected' },
          ]}
          widgetCount={2}
        />
      );

      // Navigate to File tab to see data source refresh options
      const refreshDropdown = screen.getByRole('button', { name: /data sources/i });
      await user.click(refreshDropdown);

      expect(screen.getByText('Connected Source')).toBeInTheDocument();
      expect(screen.getByText('Disconnected Source')).toBeInTheDocument();
    });
  });

  describe('Theme and Layout Integration', () => {
    it('changes dashboard theme', async () => {
      const user = userEvent.setup();
      const onThemeChange = vi.fn();
      
      renderWithProviders(
        <DashboardRibbonToolbar
          onThemeChange={onThemeChange}
          currentTheme="light"
          widgetCount={2}
        />
      );

      // Navigate to Style tab
      const styleTab = screen.getByRole('tab', { name: /style/i });
      await user.click(styleTab);

      const themeButton = screen.getByRole('button', { name: /theme/i });
      await user.click(themeButton);

      const darkTheme = screen.getByText('Dark');
      await user.click(darkTheme);

      expect(onThemeChange).toHaveBeenCalledWith('dark');
    });

    it('changes dashboard layout', async () => {
      const user = userEvent.setup();
      const onLayoutChange = vi.fn();
      
      renderWithProviders(
        <DashboardRibbonToolbar
          onLayoutChange={onLayoutChange}
          currentLayout="grid"
          widgetCount={2}
        />
      );

      // Navigate to Style tab
      const styleTab = screen.getByRole('tab', { name: /style/i });
      await user.click(styleTab);

      const layoutButton = screen.getByRole('button', { name: /layout/i });
      await user.click(layoutButton);

      const masonryLayout = screen.getByText('Masonry');
      await user.click(masonryLayout);

      expect(onLayoutChange).toHaveBeenCalledWith('masonry');
    });
  });

  describe('Permissions Integration', () => {
    it('respects user permissions for editing', () => {
      renderWithProviders(
        <DashboardRibbonToolbar
          userPermissions={{
            canEdit: false,
            canShare: true,
            canExport: true,
            canManageUsers: false,
            canAccessSettings: false,
          }}
          widgetCount={2}
        />
      );

      // Should still render but some actions might be disabled
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });

    it('shows admin features only for authorized users', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(
        <DashboardRibbonToolbar
          userPermissions={{
            canEdit: true,
            canShare: true,
            canExport: true,
            canManageUsers: true,
            canAccessSettings: true,
          }}
          widgetCount={2}
        />
      );

      // Navigate to Admin tab
      const adminTab = screen.getByRole('tab', { name: /admin/i });
      await user.click(adminTab);

      // Should show admin features
      expect(screen.getByText('Access Control')).toBeInTheDocument();
    });

    it('hides admin features for unauthorized users', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(
        <DashboardRibbonToolbar
          userPermissions={{
            canEdit: true,
            canShare: true,
            canExport: true,
            canManageUsers: false,
            canAccessSettings: false,
          }}
          widgetCount={2}
        />
      );

      // Navigate to Admin tab
      const adminTab = screen.getByRole('tab', { name: /admin/i });
      await user.click(adminTab);

      // Access Control button should be disabled
      const accessControlButton = screen.getByRole('button', { name: /access control/i });
      expect(accessControlButton).toBeDisabled();
    });
  });

  describe('Real-time Updates Integration', () => {
    it('handles WebSocket connection status', () => {
      renderWithProviders(
        <DashboardRibbonToolbar
          isConnected={true}
          widgetCount={2}
        />
      );

      // Should show connected state
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });

    it('handles WebSocket disconnection', () => {
      renderWithProviders(
        <DashboardRibbonToolbar
          isConnected={false}
          widgetCount={2}
        />
      );

      // Should handle disconnected state
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });

    it('updates widget count in real-time', () => {
      const { rerender } = renderWithProviders(
        <DashboardRibbonToolbar
          widgetCount={2}
          isConnected={true}
        />
      );

      // Simulate widget count update
      rerender(
        <DashboardRibbonToolbar
          widgetCount={3}
          isConnected={true}
        />
      );

      // Should reflect updated count
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });
  });

  describe('Error Handling Integration', () => {
    it('handles API errors gracefully', async () => {
      const user = userEvent.setup();
      const onSave = vi.fn().mockRejectedValue(new Error('Network error'));
      
      renderWithProviders(
        <DashboardRibbonToolbar
          onSave={onSave}
          widgetCount={2}
        />
      );

      const saveButton = screen.getByRole('button', { name: /save dashboard/i });
      await user.click(saveButton);

      // Should handle error without crashing
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });

    it('recovers from WebSocket errors', () => {
      renderWithProviders(
        <DashboardRibbonToolbar
          isConnected={false}
          widgetCount={2}
        />
      );

      // Should continue to function even when disconnected
      expect(screen.getByRole('toolbar')).toBeInTheDocument();
    });
  });
});
