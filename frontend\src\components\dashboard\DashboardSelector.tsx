/**
 * Dashboard Selector Component
 * 
 * Provides interface for switching between multiple dashboards,
 * creating new dashboards, and managing dashboard settings.
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Plus,
  Settings,
  Co<PERSON>,
  Trash2,
  MoreH<PERSON>zontal,
  LayoutDashboard,
  Star,
  StarOff,
  Layout
} from 'lucide-react';
import { useDashboardManagement, useDashboardTemplates } from '@/hooks/use-dashboard-management';
import { DashboardCreate, DashboardUpdate } from '@/types/dashboard-customization';
import { DashboardTemplates } from './DashboardTemplates';

interface DashboardSelectorProps {
  className?: string;
}

export const DashboardSelector: React.FC<DashboardSelectorProps> = ({ className }) => {
  const {
    dashboards,
    activeDashboard,
    isLoading,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    setActiveDashboard,
  } = useDashboardManagement();

  const { templates } = useDashboardTemplates();

  // Dialog states
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showTemplatesDialog, setShowTemplatesDialog] = useState(false);
  const [selectedDashboardId, setSelectedDashboardId] = useState<string | null>(null);

  // Form states
  const [createForm, setCreateForm] = useState<DashboardCreate>({
    name: '',
    description: '',
    is_default: false,
  });

  const [editForm, setEditForm] = useState<DashboardUpdate>({});

  // Handle dashboard selection
  const handleDashboardChange = (dashboardId: string) => {
    setActiveDashboard(dashboardId);
  };

  // Handle create dashboard
  const handleCreateDashboard = async () => {
    if (!createForm.name.trim()) return;

    try {
      await createDashboard(createForm);
      setShowCreateDialog(false);
      setCreateForm({ name: '', description: '', is_default: false });
    } catch (error) {
      // Error is handled by the hook
    }
  };

  // Handle edit dashboard
  const handleEditDashboard = async () => {
    if (!selectedDashboardId || !editForm.name?.trim()) return;

    try {
      await updateDashboard(selectedDashboardId, editForm);
      setShowEditDialog(false);
      setEditForm({});
      setSelectedDashboardId(null);
    } catch (error) {
      // Error is handled by the hook
    }
  };

  // Handle delete dashboard
  const handleDeleteDashboard = async () => {
    if (!selectedDashboardId) return;

    try {
      await deleteDashboard(selectedDashboardId);
      setShowDeleteDialog(false);
      setSelectedDashboardId(null);
    } catch (error) {
      // Error is handled by the hook
    }
  };

  // Open edit dialog
  const openEditDialog = (dashboardId: string) => {
    const dashboard = dashboards.find(d => d.id === dashboardId);
    if (dashboard) {
      setSelectedDashboardId(dashboardId);
      setEditForm({
        name: dashboard.name,
        description: dashboard.description,
        is_default: dashboard.is_default,
      });
      setShowEditDialog(true);
    }
  };

  // Open delete dialog
  const openDeleteDialog = (dashboardId: string) => {
    setSelectedDashboardId(dashboardId);
    setShowDeleteDialog(true);
  };

  const selectedDashboard = selectedDashboardId 
    ? dashboards.find(d => d.id === selectedDashboardId)
    : null;

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Dashboard Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm">
        <Select
          value={activeDashboard?.id || ''}
          onValueChange={handleDashboardChange}
          disabled={isLoading}
        >
          <SelectTrigger className="border-0 shadow-none p-0 h-auto font-medium text-gray-900 hover:text-gray-700">
            <SelectValue placeholder="Select dashboard" />
          </SelectTrigger>
          <SelectContent>
            {dashboards.map((dashboard) => (
              <SelectItem key={dashboard.id} value={dashboard.id}>
                <div className="flex items-center space-x-2">
                  <span>{dashboard.name}</span>
                  {dashboard.is_default && (
                    <Badge variant="secondary" className="text-xs">
                      Default
                    </Badge>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <span className="text-gray-400">›</span>
        <span className="text-gray-500">{dashboards.length} dashboards available</span>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowCreateDialog(true)}
          disabled={isLoading}
          className="text-gray-700 border-gray-300 hover:bg-gray-50"
        >
          <Plus className="h-4 w-4 mr-1" />
          New Dashboard
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => {}}
          disabled={isLoading}
          className="text-gray-700 border-gray-300 hover:bg-gray-50"
        >
          Save
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => openEditDialog(activeDashboard?.id || '')}
          disabled={isLoading || !activeDashboard}
          className="text-gray-700 border-gray-300 hover:bg-gray-50"
        >
          <Settings className="h-4 w-4 mr-1" />
          Settings
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowTemplatesDialog(true)}
          disabled={isLoading}
          className="text-gray-700 border-gray-300 hover:bg-gray-50"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Widget
        </Button>

        {/* More Options */}
        {activeDashboard && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowTemplatesDialog(true)}>
                <Layout className="h-4 w-4 mr-2" />
                Templates
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {}}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Dashboard
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => openDeleteDialog(activeDashboard.id)}
                className="text-destructive"
                disabled={activeDashboard.is_default || dashboards.length <= 1}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Dashboard
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Create Dashboard Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Dashboard</DialogTitle>
            <DialogDescription>
              Create a new dashboard to organize your data visualizations.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="dashboard-name">Dashboard Name</Label>
              <Input
                id="dashboard-name"
                value={createForm.name}
                onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter dashboard name"
              />
            </div>
            <div>
              <Label htmlFor="dashboard-description">Description (Optional)</Label>
              <Textarea
                id="dashboard-description"
                value={createForm.description}
                onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter dashboard description"
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is-default"
                checked={createForm.is_default}
                onCheckedChange={(checked) => setCreateForm(prev => ({ ...prev, is_default: checked }))}
              />
              <Label htmlFor="is-default">Set as default dashboard</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateDashboard} disabled={!createForm.name.trim()}>
              Create Dashboard
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dashboard Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Dashboard</DialogTitle>
            <DialogDescription>
              Update your dashboard settings and information.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-dashboard-name">Dashboard Name</Label>
              <Input
                id="edit-dashboard-name"
                value={editForm.name || ''}
                onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter dashboard name"
              />
            </div>
            <div>
              <Label htmlFor="edit-dashboard-description">Description</Label>
              <Textarea
                id="edit-dashboard-description"
                value={editForm.description || ''}
                onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter dashboard description"
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-is-default"
                checked={editForm.is_default || false}
                onCheckedChange={(checked) => setEditForm(prev => ({ ...prev, is_default: checked }))}
              />
              <Label htmlFor="edit-is-default">Set as default dashboard</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditDashboard} disabled={!editForm.name?.trim()}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dashboard Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Dashboard</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedDashboard?.name}"? This action cannot be undone.
              All sections and widgets in this dashboard will be permanently removed.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteDashboard}>
              Delete Dashboard
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dashboard Templates Dialog */}
      <DashboardTemplates
        open={showTemplatesDialog}
        onOpenChange={setShowTemplatesDialog}
        onTemplateApplied={() => {
          setShowTemplatesDialog(false);
          // Refresh dashboards list
        }}
      />
    </div>
  );
};
