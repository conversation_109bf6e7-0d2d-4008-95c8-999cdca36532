"""
Agent-Aware Mixin for MCP Tools.

This module provides a standardized mixin that ensures all MCP tools have consistent
agent identity integration capabilities. This makes agent awareness an app standard
that automatically applies to all tools, including new ones.
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Union
from abc import ABC, abstractmethod

from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class AgentAwareMixin(ABC):
    """
    Mixin class that provides standardized agent identity integration for MCP tools.
    
    This mixin ensures that all tools have consistent agent-aware capabilities:
    - Agent identity detection
    - Agent-specific customization
    - Enhanced metadata with agent information
    - Standardized error handling for agent operations
    
    Usage:
        class MyTool(BaseMCPTool, AgentAwareMixin):
            async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
                # Agent identity is automatically detected and available
                agent_identity = await self.detect_agent_identity(arguments)
                
                # Use agent-aware processing
                result = await self.process_with_agent_awareness(
                    arguments, agent_identity, self._my_processing_function
                )
                
                return result
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize the agent-aware mixin."""
        super().__init__(*args, **kwargs)
        self._agent_identity_cache = {}
        self._agent_capabilities_cache = {}
    
    async def detect_agent_identity(
        self, 
        arguments: Dict[str, Any], 
        intent_type: Optional[str] = None
    ) -> str:
        """
        Detect agent identity from arguments with caching.
        
        Args:
            arguments: Tool execution arguments
            intent_type: Optional intent type for context
            
        Returns:
            Detected agent identity
        """
        try:
            # Extract agent context
            user_context = arguments.get("context", {})
            agent_id = (
                arguments.get("persona_id") or 
                arguments.get("agent_id") or 
                user_context.get("persona_id") or 
                user_context.get("agent_id")
            )
            
            # Create cache key
            cache_key = f"{agent_id}_{intent_type or 'default'}"
            
            # Check cache first
            if cache_key in self._agent_identity_cache:
                return self._agent_identity_cache[cache_key]
            
            # Detect agent identity
            if not intent_type:
                intent_type = getattr(self, 'name', 'unknown_tool')
            
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=user_context,
                intent_type=intent_type
            )
            
            # Cache the result
            self._agent_identity_cache[cache_key] = agent_identity
            
            logger.info(f"Detected agent identity: {agent_identity} for tool: {getattr(self, 'name', 'unknown')}")
            return agent_identity
            
        except Exception as e:
            logger.warning(f"Failed to detect agent identity: {e}")
            return "unknown"
    
    async def get_agent_capabilities(self, agent_identity: str) -> Dict[str, Any]:
        """
        Get agent capabilities with caching.
        
        Args:
            agent_identity: Agent identity
            
        Returns:
            Agent capabilities dictionary
        """
        try:
            # Check cache first
            if agent_identity in self._agent_capabilities_cache:
                return self._agent_capabilities_cache[agent_identity]
            
            # Get agent system prompt
            system_prompt = await get_agent_system_prompt(agent_identity)
            
            # Extract capabilities
            capabilities = await self._extract_capabilities_from_prompt(system_prompt, agent_identity)
            
            # Cache the result
            self._agent_capabilities_cache[agent_identity] = capabilities
            
            return capabilities
            
        except Exception as e:
            logger.warning(f"Failed to get agent capabilities for {agent_identity}: {e}")
            return self._get_default_capabilities(agent_identity)
    
    async def process_with_agent_awareness(
        self,
        arguments: Dict[str, Any],
        agent_identity: str,
        processing_function: callable,
        *args,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process tool execution with agent awareness.
        
        Args:
            arguments: Tool execution arguments
            agent_identity: Detected agent identity
            processing_function: Function to execute with agent awareness
            *args, **kwargs: Additional arguments for processing function
            
        Returns:
            Enhanced result with agent metadata
        """
        try:
            # Get agent capabilities
            capabilities = await self.get_agent_capabilities(agent_identity)
            
            # Add agent context to kwargs
            kwargs['agent_identity'] = agent_identity
            kwargs['agent_capabilities'] = capabilities
            
            # Execute the processing function
            if asyncio.iscoroutinefunction(processing_function):
                result = await processing_function(arguments, *args, **kwargs)
            else:
                result = processing_function(arguments, *args, **kwargs)
            
            # Enhance result with agent metadata
            enhanced_result = self._enhance_result_with_agent_metadata(
                result, agent_identity, capabilities
            )
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error in agent-aware processing: {e}")
            # Return error with agent context
            return {
                "isError": True,
                "content": [{
                    "type": "text",
                    "text": f"Error in agent-aware processing: {str(e)}"
                }],
                "metadata": {
                    "agent_identity": agent_identity,
                    "agent_aware": True,
                    "error_type": type(e).__name__
                }
            }
    
    def _enhance_result_with_agent_metadata(
        self,
        result: Dict[str, Any],
        agent_identity: str,
        capabilities: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Enhance result with standardized agent metadata.
        
        Args:
            result: Original result
            agent_identity: Agent identity
            capabilities: Agent capabilities
            
        Returns:
            Enhanced result with agent metadata
        """
        if not isinstance(result, dict):
            result = {"content": [{"type": "text", "text": str(result)}]}
        
        # Ensure metadata exists
        if "metadata" not in result:
            result["metadata"] = {}
        
        # Add standard agent metadata
        result["metadata"].update({
            "agent_identity": agent_identity,
            "agent_aware": True,
            "agent_capabilities_count": len(capabilities.get("capabilities", [])),
            "tool_name": getattr(self, 'name', 'unknown')
        })
        
        # Add agent-specific metadata if available
        if capabilities.get("specialization"):
            result["metadata"]["agent_specialization"] = capabilities["specialization"]
        
        if capabilities.get("focus_areas"):
            result["metadata"]["agent_focus_areas"] = capabilities["focus_areas"]
        
        return result
    
    async def _extract_capabilities_from_prompt(
        self, 
        system_prompt: str, 
        agent_identity: str
    ) -> Dict[str, Any]:
        """
        Extract capabilities from agent system prompt.
        
        Args:
            system_prompt: Agent system prompt
            agent_identity: Agent identity
            
        Returns:
            Extracted capabilities
        """
        capabilities = {
            "capabilities": [],
            "specialization": None,
            "focus_areas": [],
            "communication_style": "professional",
            "technical_level": "moderate"
        }
        
        if not system_prompt:
            return self._get_default_capabilities(agent_identity)
        
        import re
        
        # Extract capabilities list
        capability_patterns = [
            r"Your capabilities include:\s*\n((?:- .+\n?)+)",
            r"capabilities:\s*\n((?:- .+\n?)+)",
            r"You can:\s*\n((?:- .+\n?)+)",
            r"I can help with:\s*\n((?:- .+\n?)+)",
            r"Your core expertise includes:\s*\n((?:- .+\n?)+)"
        ]
        
        for pattern in capability_patterns:
            match = re.search(pattern, system_prompt, re.IGNORECASE | re.MULTILINE)
            if match:
                capability_text = match.group(1)
                for line in capability_text.split('\n'):
                    line = line.strip()
                    if line.startswith('- '):
                        capability = line[2:].strip()
                        if capability:
                            capabilities["capabilities"].append(capability)
                break
        
        # Extract specialization
        specialization_patterns = [
            r"You are a (.+?) (?:agent|specialist|expert)",
            r"specialized in (.+?)(?:\.|,|\n)",
            r"expertise in (.+?)(?:\.|,|\n)"
        ]
        
        for pattern in specialization_patterns:
            match = re.search(pattern, system_prompt, re.IGNORECASE)
            if match:
                capabilities["specialization"] = match.group(1).strip()
                break
        
        # Extract focus areas
        focus_areas = []
        focus_keywords = {
            "marketing": ["marketing", "campaign", "brand", "advertising"],
            "analysis": ["analysis", "analytics", "data", "statistics"],
            "classification": ["classification", "categorization", "tagging"],
            "visualization": ["visualization", "charts", "graphs", "plots"],
            "content_creation": ["content", "writing", "generation", "creation"],
            "strategy": ["strategy", "planning", "optimization"]
        }
        
        for area, keywords in focus_keywords.items():
            if any(keyword in system_prompt.lower() for keyword in keywords):
                focus_areas.append(area)
        
        capabilities["focus_areas"] = focus_areas
        
        # Extract communication style
        if re.search(r"friendly|approachable|casual", system_prompt, re.IGNORECASE):
            capabilities["communication_style"] = "friendly"
        elif re.search(r"professional|formal|business", system_prompt, re.IGNORECASE):
            capabilities["communication_style"] = "professional"
        elif re.search(r"technical|analytical|precise", system_prompt, re.IGNORECASE):
            capabilities["communication_style"] = "technical"
        
        # Extract technical level
        if re.search(r"advanced|expert|sophisticated", system_prompt, re.IGNORECASE):
            capabilities["technical_level"] = "advanced"
        elif re.search(r"basic|simple|accessible", system_prompt, re.IGNORECASE):
            capabilities["technical_level"] = "basic"
        
        return capabilities
    
    def _get_default_capabilities(self, agent_identity: str) -> Dict[str, Any]:
        """
        Get default capabilities for specific agent types.
        
        Args:
            agent_identity: Agent identity
            
        Returns:
            Default capabilities
        """
        default_capabilities = {
            "analyst": {
                "capabilities": [
                    "Data analysis and visualization",
                    "Statistical analysis",
                    "Pattern recognition",
                    "Report generation"
                ],
                "specialization": "data analysis",
                "focus_areas": ["analysis", "visualization", "statistics"],
                "communication_style": "technical",
                "technical_level": "advanced"
            },
            "marketer": {
                "capabilities": [
                    "Marketing strategy development",
                    "Campaign planning",
                    "Content creation",
                    "Brand analysis"
                ],
                "specialization": "marketing",
                "focus_areas": ["marketing", "content_creation", "strategy"],
                "communication_style": "professional",
                "technical_level": "moderate"
            },
            "classifier": {
                "capabilities": [
                    "Text classification",
                    "Data categorization",
                    "Pattern recognition",
                    "Content organization"
                ],
                "specialization": "classification",
                "focus_areas": ["classification", "analysis"],
                "communication_style": "technical",
                "technical_level": "moderate"
            },
            "concierge": {
                "capabilities": [
                    "General assistance",
                    "User guidance",
                    "Information retrieval",
                    "Task coordination"
                ],
                "specialization": "general assistance",
                "focus_areas": ["assistance", "guidance"],
                "communication_style": "friendly",
                "technical_level": "basic"
            }
        }
        
        return default_capabilities.get(agent_identity, {
            "capabilities": ["General AI assistance"],
            "specialization": "general",
            "focus_areas": ["assistance"],
            "communication_style": "professional",
            "technical_level": "moderate"
        })
    
    def clear_agent_cache(self):
        """Clear agent identity and capabilities cache."""
        self._agent_identity_cache.clear()
        self._agent_capabilities_cache.clear()
        logger.info("Agent cache cleared")
    
    @abstractmethod
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Abstract method that must be implemented by tools using this mixin.
        
        This method should use the agent-aware capabilities provided by this mixin.
        """
        pass
