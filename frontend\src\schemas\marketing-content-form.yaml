title: Marketing Content Form
description: Form for generating any type of marketing content
properties:
  content_type:
    type: string
    description: Type of marketing content to generate (e.g., marketing_strategy, campaign_strategy, social_media_content, seo_optimization, blog_content, email_marketing, ad_copy, press_release, competitor_analysis, audience_research, market_analysis, brand_positioning, content_calendar, data_driven_analysis, data_driven_campaigns, etc.)
    default: marketing_strategy
    minLength: 3
    maxLength: 50
    pattern: "^[a-z0-9_-]+$"

  document_format:
    type: enum
    enum:
      - report
      - presentation
      - brief
      - checklist
      - template
      - guide
      - analysis
    description: Format of the output document
    default: report
  
  brand_description:
    type: string
    description: Description of the brand
    minLength: 1

  target_audience:
    type: string
    description: Description of the target audience
    minLength: 1

  products_services:
    type: string
    description: Description of products or services

  marketing_goals:
    type: string
    description: Marketing goals and objectives

  existing_content:
    type: string
    description: Existing marketing content (optional)

  keywords:
    type: string
    description: Target keywords (optional)

  suggested_topics:
    type: string
    description: Suggested topics (optional)
  
  tone:
    type: string
    description: Tone of the content
    default: Professional

  competitive_landscape:
    type: string
    description: Information about competitors and market landscape (optional)

  budget:
    type: string
    description: Budget constraints or considerations (optional)

  timeline:
    type: string
    description: Timeline or deadline information (optional)

  platforms:
    type: string
    description: Specific platforms or channels to focus on (optional)

  provider:
    type: string
    description: AI provider to use (optional)

  model:
    type: string
    description: AI model to use (optional)

  temperature:
    type: number
    description: Temperature for generation
    minimum: 0
    maximum: 1
    default: 0.7

  # Additional fields for specific content types
  campaign_objective:
    type: string
    description: Specific campaign objective (for campaign planning)

  ad_type:
    type: string
    description: Type of advertisement (for ad copy generation)

  platform:
    type: string
    description: Specific platform or channel (for platform-specific content)

  character_limit:
    type: number
    description: Character limit for content (for social media, ads, etc.)

  announcement_type:
    type: string
    description: Type of announcement (for press releases)

  headline:
    type: string
    description: Main headline or title (for press releases, blog posts, etc.)

  key_details:
    type: string
    description: Key details or information to include

  target_media:
    type: string
    description: Target media outlets or journalists (for press releases)

  product_service:
    type: string
    description: Specific product or service focus

  email_type:
    type: string
    description: Type of email campaign (newsletter, promotional, welcome series, etc.)

  blog_topic:
    type: string
    description: Specific blog post topic or theme

  analysis_focus:
    type: string
    description: Specific focus area for analysis (competitors, audience, market trends, etc.)

required:
  - content_type
  - brand_description
