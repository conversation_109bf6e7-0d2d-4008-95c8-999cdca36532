"""
Test script to validate the fixes for JSON serialization and database issues.

This script tests:
1. The update_conversation function with various inputs
2. The create_message and update_message functions with non-serializable metadata
3. The broadcast function with non-serializable message data
"""

import logging
import sys
import json
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import MetaData

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("test_fixes")

# Add the parent directory to sys.path
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the functions to test
from app.database import get_db, update_conversation, create_message, update_message, get_conversation
from app.utils.json_utils import ensure_serializable, sanitize_metadata

def test_update_conversation():
    """Test the update_conversation function with various inputs."""
    logger.info("Testing update_conversation function")
    
    # Get a database session
    db = next(get_db())
    
    try:
        # Test with empty dictionary
        logger.info("Testing with empty dictionary")
        conversation_id = "f0fcad92-b30d-4466-8c7d-afb5258a213a"  # Use a valid conversation ID
        result = update_conversation(db, conversation_id, {})
        logger.info(f"Result: {result}")
        
        # Test with title as a dictionary
        logger.info("Testing with title as a dictionary")
        result = update_conversation(db, conversation_id, {"title": {}})
        logger.info(f"Result: {result}")
        
        # Test with normal title
        logger.info("Testing with normal title")
        result = update_conversation(db, conversation_id, {"title": "Test Title"})
        logger.info(f"Result: {result}")
        
        # Test with metadata containing non-serializable objects
        logger.info("Testing with non-serializable metadata")
        metadata = {
            "test_metadata": True,
            "complex_object": MetaData(),  # Non-serializable SQLAlchemy object
            "nested": {
                "datetime": datetime.now(),  # Non-serializable datetime
                "function": lambda x: x  # Non-serializable function
            }
        }
        result = update_conversation(db, conversation_id, {"metadata": metadata})
        logger.info(f"Result: {result}")
        
    finally:
        db.close()

def test_message_functions():
    """Test the create_message and update_message functions with non-serializable metadata."""
    logger.info("Testing message functions")
    
    # Get a database session
    db = next(get_db())
    
    try:
        # Test creating a message with non-serializable metadata
        logger.info("Testing create_message with non-serializable metadata")
        conversation_id = "f0fcad92-b30d-4466-8c7d-afb5258a213a"  # Use a valid conversation ID
        metadata = {
            "test_metadata": True,
            "complex_object": MetaData(),  # Non-serializable SQLAlchemy object
            "nested": {
                "datetime": datetime.now(),  # Non-serializable datetime
                "function": lambda x: x  # Non-serializable function
            }
        }
        message = create_message(db, conversation_id, "test", "Test message", metadata)
        logger.info(f"Created message: {message.id}")
        
        # Test updating a message with non-serializable metadata
        logger.info("Testing update_message with non-serializable metadata")
        updated_metadata = {
            "updated": True,
            "complex_object": MetaData(),  # Non-serializable SQLAlchemy object
        }
        updated_message = update_message(db, message.id, metadata=updated_metadata)
        logger.info(f"Updated message: {updated_message.id}")
        
        # Verify the metadata is serializable
        logger.info("Verifying metadata is serializable")
        try:
            json_str = json.dumps(updated_message.message_metadata)
            logger.info(f"Metadata is serializable: {json_str[:100]}...")
        except Exception as e:
            logger.error(f"Metadata is not serializable: {str(e)}")
        
    finally:
        db.close()

def test_json_utils():
    """Test the JSON utility functions."""
    logger.info("Testing JSON utility functions")
    
    # Test with various non-serializable objects
    test_objects = [
        ("Simple string", "test"),
        ("Integer", 123),
        ("Float", 123.45),
        ("Boolean", True),
        ("None", None),
        ("List with simple values", [1, 2, 3]),
        ("Dictionary with simple values", {"a": 1, "b": 2}),
        ("Nested dictionary", {"a": {"b": {"c": 1}}}),
        ("Datetime", datetime.now()),
        ("SQLAlchemy MetaData", MetaData()),
        ("Function", lambda x: x),
        ("Object with __dict__", type("TestObject", (), {"attr": 123})()),
        ("Complex nested structure", {
            "simple": 123,
            "complex": MetaData(),
            "nested": {
                "datetime": datetime.now(),
                "function": lambda x: x,
                "list": [1, datetime.now(), MetaData()]
            }
        })
    ]
    
    for name, obj in test_objects:
        logger.info(f"Testing {name}")
        try:
            serializable = ensure_serializable(obj)
            json_str = json.dumps(serializable)
            logger.info(f"Successfully serialized: {json_str[:100]}...")
        except Exception as e:
            logger.error(f"Failed to serialize: {str(e)}")

if __name__ == "__main__":
    logger.info("Starting tests")
    
    # Test JSON utility functions
    test_json_utils()
    
    # Test database functions
    test_update_conversation()
    test_message_functions()
    
    logger.info("Tests completed")
