import uuid
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session
from ..models.notifications import NotificationModel, NotificationCreate, NotificationUpdate, NotificationPreferences, NotificationResponse # Added NotificationResponse
from ..utils.db_utils import get_utc_now
# Import the manager instance from the new utility file
from ..utils.connection_manager import manager as ws_manager # Using manager from new util file

logger = logging.getLogger(__name__)

class NotificationsService:
    def __init__(self, db: Session):
        self.db = db

    async def get_notifications_for_user(
        self, user_id: int, skip: int = 0, limit: int = 100, include_read: bool = False
    ) -> List[NotificationModel]:
        query = self.db.query(NotificationModel).filter(NotificationModel.user_id == user_id)
        if not include_read:
            query = query.filter(NotificationModel.is_read == False)
        return query.order_by(NotificationModel.created_at.desc()).offset(skip).limit(limit).all()

    async def create_notification(self, notification_data: NotificationCreate) -> NotificationModel:
        """Create a notification with enhanced real-time delivery and intelligent routing."""

        # Apply intelligent filtering and deduplication
        if await self._should_filter_notification(notification_data):
            logger.info(f"Notification filtered for user {notification_data.user_id}: {notification_data.title}")
            return None  # Return None for filtered notifications

        # Enhance notification data with additional context
        enhanced_metadata = await self._enhance_notification_metadata(notification_data)

        db_notification = NotificationModel(
            id=str(uuid.uuid4()),
            user_id=notification_data.user_id,
            type=notification_data.type,
            title=notification_data.title,
            message=notification_data.message,
            link=notification_data.link,
            notification_metadata=enhanced_metadata,
            created_at=get_utc_now(),
            is_read=False
        )
        self.db.add(db_notification)
        self.db.commit()
        self.db.refresh(db_notification)

        # Enhanced real-time notification with multiple delivery channels
        delivery_results = await self._deliver_notification_enhanced(db_notification)

        # Update delivery status
        if delivery_results.get("delivered", False):
            db_notification.notification_metadata = {
                **(db_notification.notification_metadata or {}),
                "delivery_results": delivery_results,
                "delivered_at": get_utc_now().isoformat()
            }
            self.db.commit()

        # Schedule follow-up actions if needed
        await self._schedule_follow_up_actions(db_notification, delivery_results)

        logger.info(f"Enhanced notification created for user {notification_data.user_id}: {notification_data.title}")
        return db_notification

    async def _should_filter_notification(self, notification_data: NotificationCreate) -> bool:
        """Intelligent notification filtering to prevent spam and duplicates."""
        try:
            # Check for recent duplicate notifications
            recent_threshold = get_utc_now() - timedelta(minutes=5)
            recent_similar = self.db.query(NotificationModel).filter(
                NotificationModel.user_id == notification_data.user_id,
                NotificationModel.type == notification_data.type,
                NotificationModel.title == notification_data.title,
                NotificationModel.created_at >= recent_threshold
            ).first()

            if recent_similar:
                logger.info(f"Filtering duplicate notification for user {notification_data.user_id}")
                return True

            # Check user notification frequency limits
            if await self._exceeds_frequency_limits(notification_data.user_id, notification_data.type):
                logger.info(f"Filtering notification due to frequency limits for user {notification_data.user_id}")
                return True

            # Check user preferences
            user_prefs = await self.get_user_notification_preferences(notification_data.user_id)
            if user_prefs and not user_prefs.in_app_enabled:
                return True

            # Type-specific filtering
            type_specific_enabled = getattr(user_prefs, f"{notification_data.type}_in_app", True)
            if not type_specific_enabled:
                return True

            return False

        except Exception as e:
            logger.error(f"Error in notification filtering: {e}")
            return False  # Don't filter on error

    async def _enhance_notification_metadata(self, notification_data: NotificationCreate) -> Dict[str, Any]:
        """Enhance notification metadata with additional context and analytics."""
        enhanced_metadata = notification_data.notification_metadata or {}

        # Add system metadata
        enhanced_metadata.update({
            "created_timestamp": get_utc_now().isoformat(),
            "notification_version": "2.0",
            "priority": self._calculate_notification_priority(notification_data),
            "category": self._categorize_notification(notification_data.type),
            "expires_at": self._calculate_expiration(notification_data.type).isoformat(),
            "delivery_channels": await self._determine_delivery_channels(notification_data.user_id),
            "actions": self._get_notification_actions(notification_data.type, enhanced_metadata)
        })

        return enhanced_metadata

    async def _deliver_notification_enhanced(self, notification: NotificationModel) -> Dict[str, Any]:
        """Enhanced notification delivery with multiple channels and fallback."""
        delivery_results = {
            "websocket": False,
            "email": False,
            "push": False,
            "delivered": False,
            "channels_attempted": [],
            "errors": []
        }

        try:
            # WebSocket delivery (primary channel)
            websocket_result = await self._deliver_via_websocket(notification)
            delivery_results["websocket"] = websocket_result["success"]
            delivery_results["channels_attempted"].append("websocket")

            if not websocket_result["success"]:
                delivery_results["errors"].append(f"WebSocket: {websocket_result.get('error', 'Unknown error')}")

            # Email delivery (if enabled and appropriate)
            user_prefs = await self.get_user_notification_preferences(notification.user_id)
            if user_prefs and user_prefs.email_enabled:
                email_result = await self._deliver_via_email(notification)
                delivery_results["email"] = email_result["success"]
                delivery_results["channels_attempted"].append("email")

                if not email_result["success"]:
                    delivery_results["errors"].append(f"Email: {email_result.get('error', 'Unknown error')}")

            # Push notification delivery (future implementation)
            # push_result = await self._deliver_via_push(notification)
            # delivery_results["push"] = push_result["success"]

            # Mark as delivered if any channel succeeded
            delivery_results["delivered"] = any([
                delivery_results["websocket"],
                delivery_results["email"],
                delivery_results["push"]
            ])

            # Track delivery analytics
            await self._track_delivery_analytics(notification, delivery_results)

        except Exception as e:
            logger.error(f"Error in enhanced notification delivery: {e}")
            delivery_results["errors"].append(f"System error: {str(e)}")

        return delivery_results

    async def _deliver_via_websocket(self, notification: NotificationModel) -> Dict[str, Any]:
        """Deliver notification via WebSocket with enhanced payload."""
        try:
            notification_resp = NotificationResponse.model_validate(notification)
            enhanced_payload = {
                "type": "new_notification",
                "data": notification_resp.model_dump(),
                "metadata": {
                    "delivery_method": "websocket",
                    "timestamp": get_utc_now().isoformat(),
                    "requires_acknowledgment": notification.type in ["alert", "critical"],
                    "auto_dismiss_after": self._get_auto_dismiss_time(notification.type)
                }
            }

            await ws_manager.send_personal_message(enhanced_payload, str(notification.user_id))

            return {"success": True, "method": "websocket"}

        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {e}")
            return {"success": False, "method": "websocket", "error": str(e)}

    async def mark_notification_as_read(self, notification_id: str, user_id: int) -> Optional[NotificationModel]:
        db_notification = self.db.query(NotificationModel).filter(
            NotificationModel.id == notification_id,
            NotificationModel.user_id == user_id
        ).first()

        if db_notification and not db_notification.is_read:
            db_notification.is_read = True
            db_notification.read_at = get_utc_now()
            self.db.commit()
            self.db.refresh(db_notification)
            return db_notification
        return db_notification # Return notification even if already read, or None if not found

    async def mark_all_notifications_as_read(self, user_id: int) -> int:
        updated_count = self.db.query(NotificationModel).filter(
            NotificationModel.user_id == user_id,
            NotificationModel.is_read == False
        ).update({"is_read": True, "read_at": get_utc_now()}, synchronize_session=False)
        self.db.commit()
        return updated_count

    async def get_unread_notification_count(self, user_id: int) -> int:
        return self.db.query(NotificationModel).filter(
            NotificationModel.user_id == user_id,
            NotificationModel.is_read == False
        ).count()

    # --- Notification Preferences (Placeholder - could be a separate service) ---
    async def get_user_notification_preferences(self, user_id: int) -> Optional[NotificationPreferences]:
        # This would typically fetch from a UserNotificationPreferencesModel table
        # For now, returning a default Pydantic model
        # In a real app, load from DB or return default if not found
        # db_prefs = self.db.query(UserNotificationPreferencesModel).filter_by(user_id=user_id).first()
        # if db_prefs:
        #     return NotificationPreferences(**db_prefs.preferences, user_id=user_id)
        return NotificationPreferences(user_id=user_id) # Default preferences

    async def update_user_notification_preferences(
        self, user_id: int, preferences_data: NotificationPreferences
    ) -> NotificationPreferences:
        # This would update UserNotificationPreferencesModel
        # For now, just validates and returns the input
        # db_prefs = self.db.query(UserNotificationPreferencesModel).filter_by(user_id=user_id).first()
        # if db_prefs:
        #     db_prefs.preferences = preferences_data.model_dump(exclude={"user_id"})
        # else:
        #     db_prefs = UserNotificationPreferencesModel(user_id=user_id, preferences=preferences_data.model_dump(exclude={"user_id"}))
        #     self.db.add(db_prefs)
        # self.db.commit()
        return preferences_data

    async def _send_email_notification(self, notification: NotificationModel):
        # Placeholder for actual email sending logic
        # This would use an email library (e.g., FastAPI-Mail, smtplib)
        # and email templates.
        print(f"Simulating email notification to user {notification.user_id}: {notification.title} - {notification.message}")
        pass
