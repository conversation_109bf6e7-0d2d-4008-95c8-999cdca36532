#!/usr/bin/env python3
"""
Dashboard Settings Comprehensive Test Script

This script tests all aspects of the dashboard settings functionality including:
1. API endpoints
2. Database operations
3. Data source management
4. JSON serialization
5. Error handling
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy.orm import Session
from app.database import get_db, User, DataSource
from app.services.dashboard_service import DatageniusDashboardService
from app.models.dashboard_customization import (
    DashboardCreate, DashboardUpdate, DashboardDataSourceAssignmentCreate,
    Dashboard, DashboardDataSourceAssignment
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DashboardSettingsTestSuite:
    """Comprehensive test suite for dashboard settings functionality."""
    
    def __init__(self):
        self.db: Session = next(get_db())
        self.service = DatageniusDashboardService(self.db)
        self.test_user_id = 1  # Assuming test user exists
        self.test_dashboard_id = None
        self.test_data_source_ids = []
        
    async def run_all_tests(self):
        """Run all dashboard settings tests."""
        logger.info("🚀 Starting Dashboard Settings Comprehensive Test Suite")
        
        try:
            # Test 1: Database Connection and Models
            await self.test_database_connection()
            
            # Test 2: Dashboard CRUD Operations
            await self.test_dashboard_crud()
            
            # Test 3: Data Source Management
            await self.test_data_source_management()
            
            # Test 4: JSON Serialization
            await self.test_json_serialization()
            
            # Test 5: Bulk Operations
            await self.test_bulk_operations()
            
            # Test 6: Error Handling
            await self.test_error_handling()
            
            logger.info("✅ All dashboard settings tests completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Test suite failed: {e}")
            raise
        finally:
            await self.cleanup()
    
    async def test_database_connection(self):
        """Test database connection and basic queries."""
        logger.info("Testing database connection...")
        
        # Test user exists
        user = self.db.query(User).filter(User.id == self.test_user_id).first()
        if not user:
            logger.warning(f"Test user {self.test_user_id} not found, creating one...")
            # Create test user if needed
            user = User(
                id=self.test_user_id,
                username="test_user",
                email="<EMAIL>",
                hashed_password="test_hash",
                is_active=True
            )
            self.db.add(user)
            self.db.commit()
        
        logger.info(f"✓ Database connection successful, test user: {user.username}")
    
    async def test_dashboard_crud(self):
        """Test dashboard CRUD operations."""
        logger.info("Testing dashboard CRUD operations...")
        
        # Create dashboard
        dashboard_data = DashboardCreate(
            name="Test Dashboard Settings",
            description="Dashboard for testing settings functionality",
            is_default=False,
            is_public=False,
            layout_config={
                "grid_size": 12,
                "row_height": 100,
                "margin": [10, 10]
            },
            theme_config={
                "primary_color": "#3B82F6",
                "background_color": "#F9FAFB"
            },
            refresh_interval=300
        )
        
        dashboard = await self.service.create_dashboard(self.test_user_id, dashboard_data)
        self.test_dashboard_id = dashboard.id
        logger.info(f"✓ Dashboard created: {dashboard.name} (ID: {dashboard.id})")
        
        # Read dashboard
        retrieved_dashboard = await self.service.get_dashboard(dashboard.id, self.test_user_id)
        assert retrieved_dashboard is not None, "Dashboard should be retrievable"
        logger.info(f"✓ Dashboard retrieved: {retrieved_dashboard.name}")
        
        # Update dashboard
        update_data = DashboardUpdate(
            name="Updated Test Dashboard",
            description="Updated description",
            refresh_interval=600
        )
        updated_dashboard = await self.service.update_dashboard(dashboard.id, self.test_user_id, update_data)
        assert updated_dashboard.name == "Updated Test Dashboard", "Dashboard name should be updated"
        logger.info(f"✓ Dashboard updated: {updated_dashboard.name}")
        
        logger.info("✓ Dashboard CRUD operations successful")
    
    async def test_data_source_management(self):
        """Test data source management functionality."""
        logger.info("Testing data source management...")
        
        # Create test data sources
        test_data_sources = [
            {
                "name": "Test File Data Source",
                "type": "file",
                "description": "Test file data source",
                "is_active": True,
                "metadata": {"file_type": "csv", "size": 1024},
                "source_metadata": {"file_id": "test-file-1"}
            },
            {
                "name": "Test Database Data Source", 
                "type": "database",
                "description": "Test database data source",
                "is_active": True,
                "metadata": {"db_type": "postgresql", "host": "localhost"},
                "source_metadata": {"connection_string": "postgresql://test"}
            }
        ]
        
        for ds_data in test_data_sources:
            data_source = DataSource(
                id=f"test-ds-{len(self.test_data_source_ids) + 1}",
                user_id=self.test_user_id,
                **ds_data
            )
            self.db.add(data_source)
            self.test_data_source_ids.append(data_source.id)
        
        self.db.commit()
        logger.info(f"✓ Created {len(self.test_data_source_ids)} test data sources")
        
        # Test bulk assignment
        if self.test_dashboard_id:
            result = await self.service.bulk_assign_data_sources(
                self.test_dashboard_id, 
                self.test_data_source_ids, 
                self.test_user_id
            )
            logger.info(f"✓ Bulk assigned {result['assigned']} data sources")
            
            # Test retrieval of dashboard data sources
            assignments = await self.service.get_dashboard_data_sources(
                self.test_dashboard_id, 
                self.test_user_id
            )
            logger.info(f"✓ Retrieved {len(assignments)} data source assignments")
        
        logger.info("✓ Data source management tests successful")

    async def test_json_serialization(self):
        """Test JSON serialization for complex data structures."""
        logger.info("Testing JSON serialization...")

        # Test complex layout config
        complex_layout = {
            "grid_size": 12,
            "row_height": 100,
            "margin": [10, 10],
            "breakpoints": {
                "lg": 1200,
                "md": 996,
                "sm": 768,
                "xs": 480
            },
            "responsive": True,
            "nested_config": {
                "widgets": [
                    {"id": "widget-1", "type": "chart", "config": {"chart_type": "line"}},
                    {"id": "widget-2", "type": "kpi", "config": {"format": "currency"}}
                ]
            }
        }

        # Test theme config
        complex_theme = {
            "primary_color": "#3B82F6",
            "secondary_color": "#10B981",
            "background_color": "#F9FAFB",
            "text_color": "#1F2937",
            "custom_css": {
                ".dashboard-widget": {
                    "border-radius": "8px",
                    "box-shadow": "0 1px 3px rgba(0,0,0,0.1)"
                }
            },
            "color_palette": ["#3B82F6", "#10B981", "#F59E0B", "#EF4444"]
        }

        # Create dashboard with complex configs
        dashboard_data = DashboardCreate(
            name="JSON Serialization Test Dashboard",
            description="Testing complex JSON serialization",
            layout_config=complex_layout,
            theme_config=complex_theme,
            refresh_interval=300
        )

        dashboard = await self.service.create_dashboard(self.test_user_id, dashboard_data)
        logger.info(f"✓ Dashboard with complex JSON created: {dashboard.id}")

        # Retrieve and verify JSON integrity
        retrieved = await self.service.get_dashboard(dashboard.id, self.test_user_id)

        # Verify layout config
        assert retrieved.layout_config["grid_size"] == 12, "Layout config should be preserved"
        assert "breakpoints" in retrieved.layout_config, "Nested objects should be preserved"
        assert retrieved.layout_config["nested_config"]["widgets"][0]["type"] == "chart", "Deep nesting should work"

        # Verify theme config
        assert retrieved.theme_config["primary_color"] == "#3B82F6", "Theme config should be preserved"
        assert "custom_css" in retrieved.theme_config, "Complex nested objects should be preserved"

        logger.info("✓ JSON serialization tests successful")

    async def test_bulk_operations(self):
        """Test bulk operations performance and correctness."""
        logger.info("Testing bulk operations...")

        if not self.test_dashboard_id:
            logger.warning("No test dashboard available for bulk operations test")
            return

        # Test bulk assignment with mixed valid/invalid data sources
        mixed_data_source_ids = self.test_data_source_ids + ["invalid-id-1", "invalid-id-2"]

        result = await self.service.bulk_assign_data_sources(
            self.test_dashboard_id,
            mixed_data_source_ids,
            self.test_user_id
        )

        assert result["assigned"] >= 0, "Should assign valid data sources"
        assert result["failed"] >= 0, "Should handle failed assignments"
        assert len(result["errors"]) >= 0, "Should report errors"

        logger.info(f"✓ Bulk operations: {result['assigned']} assigned, {result['failed']} failed")

        logger.info("✓ Bulk operations tests successful")

    async def test_error_handling(self):
        """Test error handling and edge cases."""
        logger.info("Testing error handling...")

        # Test invalid dashboard ID - service returns None, doesn't raise exception
        result = await self.service.get_dashboard("invalid-dashboard-id", self.test_user_id)
        assert result is None, "Should return None for invalid dashboard ID"
        logger.info("✓ Correctly handled invalid dashboard ID")

        # Test unauthorized access - service returns None for non-existent user
        if self.test_dashboard_id:
            result = await self.service.get_dashboard(self.test_dashboard_id, 99999)  # Non-existent user
            assert result is None, "Should return None for unauthorized access"
            logger.info("✓ Correctly handled unauthorized access")

        # Test invalid data in bulk assignment
        try:
            result = await self.service.bulk_assign_data_sources(
                "invalid-dashboard-id",
                ["invalid-data-source"],
                self.test_user_id
            )
            assert False, "Should raise error for invalid dashboard in bulk assignment"
        except ValueError:
            logger.info("✓ Correctly handled invalid dashboard in bulk assignment")

        logger.info("✓ Error handling tests successful")

    async def cleanup(self):
        """Clean up test data."""
        logger.info("Cleaning up test data...")

        try:
            # Delete test dashboard and related data
            if self.test_dashboard_id:
                await self.service.delete_dashboard(self.test_dashboard_id, self.test_user_id)
                logger.info(f"✓ Deleted test dashboard: {self.test_dashboard_id}")

            # Delete test data sources
            for ds_id in self.test_data_source_ids:
                data_source = self.db.query(DataSource).filter(DataSource.id == ds_id).first()
                if data_source:
                    self.db.delete(data_source)

            self.db.commit()
            logger.info(f"✓ Deleted {len(self.test_data_source_ids)} test data sources")

        except Exception as e:
            logger.warning(f"Cleanup warning: {e}")
        finally:
            self.db.close()

async def main():
    """Main test runner."""
    test_suite = DashboardSettingsTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
