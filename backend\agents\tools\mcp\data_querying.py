"""
Data querying MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for querying data using natural language.
"""

import logging
import os
import json
import pandas as pd
from typing import Dict, Any, List, Optional

from .base import BaseMCPTool
from agents.utils.model_providers import get_model

logger = logging.getLogger(__name__)


class DataQueryingTool(BaseMCPTool):
    """Tool for querying data using natural language."""

    def __init__(self):
        """Initialize the data querying tool."""
        super().__init__(
            name="query_data",
            description="Query data using natural language",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string"},
                    "query": {"type": "string"},
                    "provider": {"type": "string"},
                    "model": {"type": "string"},
                    "temperature": {"type": "number"}
                },
                "required": ["file_path", "query"]
            },
            annotations={
                "title": "Query Data",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        self.data_dir = "data"
        self.default_provider = "groq"
        self.default_model = "llama3-70b-8192"
        self.default_temperature = 0.0

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        if "data_dir" in config:
            self.data_dir = config["data_dir"]
            logger.info(f"Set data directory to: {self.data_dir}")

        if "default_provider" in config:
            self.default_provider = config["default_provider"]

        if "default_model" in config:
            self.default_model = config["default_model"]

        if "default_temperature" in config:
            self.default_temperature = config["default_temperature"]

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution

        Returns:
            Tool execution results in MCP format
        """
        try:
            file_path = arguments["file_path"]
            query = arguments["query"]
            provider = arguments.get("provider", self.default_provider)
            model_name = arguments.get("model", self.default_model)
            temperature = arguments.get("temperature", self.default_temperature)

            # Check if the path is relative and prepend the data directory
            if not os.path.isabs(file_path):
                file_path = os.path.join(self.data_dir, file_path)

            # Check if the file exists
            if not os.path.exists(file_path):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"File not found: {file_path}"
                        }
                    ]
                }

            # Load the data
            if file_path.endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            # Create a prompt template
            prompt_template = """
            You are a data analysis assistant. Answer the following question about the data.

            Data information:
            - Shape: {shape}
            - Columns: {columns}
            - Data types: {dtypes}
            - First few rows: {head}

            Question: {query}

            Provide a clear, concise answer. If you need to perform calculations, explain your approach.
            If the question cannot be answered with the available data, explain why.
            """

            # Format the prompt
            prompt = prompt_template.format(
                shape=str(df.shape),
                columns=str(df.columns.tolist()),
                dtypes=str(df.dtypes.to_dict()),
                head=str(df.head().to_dict()),
                query=query
            )

            # Get the model
            model = await get_model(provider, model_name, {"temperature": temperature})

            if not model:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Failed to initialize model {model_name} from provider {provider}"
                        }
                    ]
                }

            # Execute the query
            response = await model.agenerate_text(prompt)

            # Only create data preview if explicitly requested or if response mentions specific columns
            data_preview = None
            preview_keywords = ["show", "display", "preview", "table", "data", "rows", "sample"]
            should_include_preview = any(keyword in query.lower() for keyword in preview_keywords)

            if should_include_preview:
                try:
                    # If the response mentions specific columns, create a preview of those columns
                    mentioned_columns = []
                    for col in df.columns:
                        if col in response:
                            mentioned_columns.append(col)

                    if mentioned_columns:
                        data_preview = df[mentioned_columns].head(10).to_dict(orient="records")
                    elif should_include_preview:
                        # If preview was explicitly requested but no specific columns mentioned, show first few columns
                        preview_cols = df.columns[:5].tolist()  # Limit to first 5 columns to avoid overwhelming display
                        data_preview = df[preview_cols].head(10).to_dict(orient="records")
                except Exception as e:
                    logger.warning(f"Error creating data preview: {str(e)}")

            return {
                "content": [
                    {
                        "type": "text",
                        "text": response
                    }
                ],
                "metadata": {
                    "query": query,
                    "data_preview": data_preview if data_preview else None
                }
            }

        except Exception as e:
            logger.error(f"Error querying data: {str(e)}", exc_info=True)
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error querying data: {str(e)}"
                    }
                ]
            }
