/**
 * Ribbon Integration Wrapper
 * 
 * Wrapper component that integrates all ribbon action handlers with the ribbon toolbars
 * and provides the enhanced AI assistant functionality.
 * Features:
 * - Connects ribbon buttons to actual functionality
 * - Provides AI assistant integration
 * - Handles all modal dialogs and panels
 * - Manages state between ribbon and dashboard components
 */

import React from 'react';
import { useRibbonActionHandlers } from './RibbonActionHandlers';
import { RibbonAIIntegration } from './RibbonAIIntegration';

// Import all dialog components
import { DashboardBrowserDialog } from './dialogs/DashboardBrowserDialog';
import { DashboardCreationWizard } from './dialogs/DashboardCreationWizard';
import { RecentDashboardsDropdown } from './dialogs/RecentDashboardsDropdown';
import { DataSourceConnectionWizard } from './dialogs/DataSourceConnectionWizard';
import { FileUploadDialog } from './dialogs/FileUploadDialog';
import { ShareDashboardDialog } from './dialogs/ShareDashboardDialog';
import { ExportDashboardDialog } from './dialogs/ExportDashboardDialog';
import { EmbedCodeDialog } from './dialogs/EmbedCodeDialog';
import { DashboardSettingsPanel } from './DashboardSettingsPanel';
import { UserPreferencesDialog } from './dialogs/UserPreferencesDialog';
import { ThemeSelector } from './dialogs/ThemeSelector';
import { LayoutSelector } from './dialogs/LayoutSelector';

interface RibbonIntegrationWrapperProps {
  children: React.ReactNode;
  dashboardId?: string;
  onWidgetCreate?: (widget_config: any) => void;
  onSectionCreate?: (section_config: any) => void;
  onTemplateApply?: (template_id: string) => void;
  onDataConfigure?: (data_config: any) => void;
}

export const RibbonIntegrationWrapper: React.FC<RibbonIntegrationWrapperProps> = ({
  children,
  dashboardId,
  onWidgetCreate,
  onSectionCreate,
  onTemplateApply,
  onDataConfigure,
}) => {
  // Get all ribbon action handlers
  const {
    // Dashboard Operations
    handleDashboardOpen,
    handleDashboardSave,
    handleDashboardNew,
    handleDashboardRecent,
    
    // Data Operations
    handleDataConnect,
    handleDataRefresh,
    handleFileUpload,
    
    // Style Operations
    handleThemeChange,
    handleLayoutChange,
    
    // Share Operations
    handleShareDashboard,
    handleDashboardExport,
    handleShareEmbed,
    
    // Admin Operations
    handleAdminSettings,
    handleAdminPreferences,
    
    // AI Operations
    handleAICreate,
    handleAIOptimize,
    handleAIAnalyze,
    handleAISection,
    
    // Dialog states
    showDashboardBrowser,
    setShowDashboardBrowser,
    showCreationWizard,
    setShowCreationWizard,
    showRecentDropdown,
    setShowRecentDropdown,
    showDataConnectionWizard,
    setShowDataConnectionWizard,
    showFileUpload,
    setShowFileUpload,
    showShareDialog,
    setShowShareDialog,
    showExportDialog,
    setShowExportDialog,
    showEmbedDialog,
    setShowEmbedDialog,
    showSettingsPanel,
    setShowSettingsPanel,
    showPreferencesDialog,
    setShowPreferencesDialog,
    showThemeSelector,
    setShowThemeSelector,
    showLayoutSelector,
    setShowLayoutSelector,
    
    // Enhanced AI Integration
    isAIOpen,
    aiContext,
    closeAI,
    
    // Loading states
    operationLoading,
    isLoading,
    
    // Data
    dashboards,
    activeDashboard,
    currentLayout,
  } = useRibbonActionHandlers({
    onWidgetCreate,
    onSectionCreate,
    onTemplateApply,
    onDataConfigure,
  });

  // Provide ribbon handlers to child components through context or props
  const ribbonHandlers = {
    // Dashboard operations
    onDashboardOpen: handleDashboardOpen,
    onDashboardSave: handleDashboardSave,
    onDashboardNew: handleDashboardNew,
    onDashboardRecent: handleDashboardRecent,
    
    // Data operations
    onDataConnect: handleDataConnect,
    onDataRefresh: handleDataRefresh,
    onFileUpload: handleFileUpload,
    
    // Style operations
    onThemeChange: handleThemeChange,
    onLayoutChange: handleLayoutChange,
    
    // Share operations
    onShareDashboard: handleShareDashboard,
    onDashboardExport: handleDashboardExport,
    onShareEmbed: handleShareEmbed,
    
    // Admin operations
    onAdminSettings: handleAdminSettings,
    onAdminPreferences: handleAdminPreferences,
    
    // AI operations
    onAICreate: handleAICreate,
    onAIOptimize: handleAIOptimize,
    onAIAnalyze: handleAIAnalyze,
    onAISection: handleAISection,
    
    // State information
    operationLoading,
    isLoading,
  };

  return (
    <>
      {/* Render children with ribbon handlers */}
      {React.cloneElement(children as React.ReactElement, { ribbonHandlers })}

      {/* Dashboard Operations Dialogs */}
      <DashboardBrowserDialog
        open={showDashboardBrowser}
        onOpenChange={setShowDashboardBrowser}
        onDashboardSelect={(dashboardId) => {
          // Handle dashboard selection
          setShowDashboardBrowser(false);
        }}
      />

      <DashboardCreationWizard
        open={showCreationWizard}
        onOpenChange={setShowCreationWizard}
        onDashboardCreated={(dashboardId) => {
          // Handle new dashboard creation
          setShowCreationWizard(false);
        }}
      />

      <RecentDashboardsDropdown
        open={showRecentDropdown}
        onOpenChange={setShowRecentDropdown}
        onDashboardSelect={(dashboardId) => {
          // Handle recent dashboard selection
          setShowRecentDropdown(false);
        }}
      />

      {/* Data Operations Dialogs */}
      <DataSourceConnectionWizard
        open={showDataConnectionWizard}
        onOpenChange={setShowDataConnectionWizard}
        onDataSourceCreated={(dataSourceId) => {
          // Handle new data source
          setShowDataConnectionWizard(false);
        }}
      />

      <FileUploadDialog
        open={showFileUpload}
        onOpenChange={setShowFileUpload}
        onFilesUploaded={(fileIds) => {
          // Handle uploaded files
          setShowFileUpload(false);
        }}
      />

      {/* Style Operations Dialogs */}
      <ThemeSelector
        open={showThemeSelector}
        onOpenChange={setShowThemeSelector}
        dashboardId={dashboardId}
      />

      <LayoutSelector
        open={showLayoutSelector}
        onOpenChange={setShowLayoutSelector}
        dashboardId={dashboardId}
      />

      {/* Share Operations Dialogs */}
      {activeDashboard && (
        <>
          <ShareDashboardDialog
            open={showShareDialog}
            onOpenChange={setShowShareDialog}
            dashboardId={activeDashboard.id}
            dashboardName={activeDashboard.name}
            isPublic={activeDashboard.is_public}
          />

          <ExportDashboardDialog
            open={showExportDialog}
            onOpenChange={setShowExportDialog}
            dashboardId={activeDashboard.id}
            dashboardName={activeDashboard.name}
          />

          <EmbedCodeDialog
            open={showEmbedDialog}
            onOpenChange={setShowEmbedDialog}
            dashboardId={activeDashboard.id}
            dashboardName={activeDashboard.name}
          />
        </>
      )}

      {/* Admin Operations Panels */}
      <DashboardSettingsPanel
        open={showSettingsPanel}
        onOpenChange={setShowSettingsPanel}
        dashboardId={dashboardId}
      />

      <UserPreferencesDialog
        open={showPreferencesDialog}
        onOpenChange={setShowPreferencesDialog}
      />

      {/* Enhanced AI Assistant Integration */}
      <RibbonAIIntegration
        isOpen={isAIOpen}
        onOpenChange={closeAI}
        initialContext={aiContext}
        onWidgetCreate={onWidgetCreate}
        onSectionCreate={onSectionCreate}
        onTemplateApply={onTemplateApply}
        onDataConfigure={onDataConfigure}
        onDashboardOptimize={(params) => {
          // Handle dashboard optimization
          console.log('Dashboard optimization:', params);
        }}
      />
    </>
  );
};

// Context for providing ribbon handlers to child components
export const RibbonContext = React.createContext<any>(null);

// Hook for accessing ribbon handlers in child components
export const useRibbonHandlers = () => {
  const context = React.useContext(RibbonContext);
  if (!context) {
    throw new Error('useRibbonHandlers must be used within RibbonIntegrationWrapper');
  }
  return context;
};

// Higher-order component for easy integration
export const withRibbonIntegration = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return React.forwardRef<any, P & RibbonIntegrationWrapperProps>((props, ref) => {
    const { dashboardId, onWidgetCreate, onSectionCreate, onTemplateApply, onDataConfigure, ...componentProps } = props;
    
    return (
      <RibbonIntegrationWrapper
        dashboardId={dashboardId}
        onWidgetCreate={onWidgetCreate}
        onSectionCreate={onSectionCreate}
        onTemplateApply={onTemplateApply}
        onDataConfigure={onDataConfigure}
      >
        <Component {...(componentProps as P)} ref={ref} />
      </RibbonIntegrationWrapper>
    );
  });
};
