import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronRight, ChevronLeft, Info, X, Compass, HelpCircle } from 'lucide-react';
import { WorkflowVisualization, createWorkflowSteps } from './WorkflowVisualization';
import { CoordinationPanel } from './CoordinationPanel';
import { TeamPanel } from './TeamPanel';
import { useConcierge } from '@/contexts/ConciergeContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useMediaQuery } from '@/hooks/use-media-query';
import { createReactSyncApp } from '@/utils/createReactSyncApp';

interface ConciergePanelProps {
  className?: string;
}

export const ConciergePanel: React.FC<ConciergePanelProps> = ({
  className = ''
}) => {
  const { conciergeState } = useConcierge();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showMobileDialog, setShowMobileDialog] = useState(false);
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Reset expanded state when switching between mobile and desktop
  useEffect(() => {
    if (isMobile) {
      setIsExpanded(false);
    }
  }, [isMobile]);

  if (!conciergeState) return null;

  const workflowSteps = createWorkflowSteps(conciergeState);

  // Mobile dialog version
  if (isMobile) {
    return (
      <div className={`relative ${className}`}>
        <Dialog open={showMobileDialog} onOpenChange={setShowMobileDialog}>
          <DialogTrigger asChild>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1 bg-white/80 backdrop-blur-sm"
              >
                <Compass className="h-4 w-4 text-brand-500" />
                <span>Workflow</span>
              </Button>
            </motion.div>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Compass className="h-5 w-5 text-brand-500" />
                Concierge Workflow
              </DialogTitle>
            </DialogHeader>
            <div className="mt-2">
              <WorkflowVisualization steps={workflowSteps} />
            </div>

            {/* Coordination Panel - Mobile */}
            <div className="mt-4">
              <CoordinationPanel />
            </div>

            {/* Team Panel - Mobile */}
            <div className="mt-4">
              <TeamPanel />
            </div>

            <div className="mt-4 text-sm text-gray-500 flex items-start gap-2">
              <HelpCircle className="h-4 w-4 text-brand-400 flex-shrink-0 mt-0.5" />
              <p>
                The concierge guides you through a personalized workflow to help you get the most out of your data.
                Follow the steps above to complete your journey.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  // Desktop expandable panel version
  return (
    <div className={`relative ${className}`}>
      <AnimatePresence mode="wait">
        {isExpanded ? (
          <motion.div
            key="expanded"
            initial={{ opacity: 0, width: 0, x: 20 }}
            animate={{
              opacity: 1,
              width: 'auto',
              x: 0,
              transition: {
                type: "spring",
                stiffness: 300,
                damping: 30,
                mass: 1
              }
            }}
            exit={{
              opacity: 0,
              width: 0,
              x: 20,
              transition: {
                duration: 0.2
              }
            }}
            className="w-80"
          >
            <Card className="h-full shadow-lg border-brand-100">
              <CardContent className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium flex items-center gap-2 text-brand-700">
                    <Compass className="h-5 w-5 text-brand-500" />
                    Concierge Workflow
                  </h3>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setIsExpanded(false)}
                      className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </motion.div>
                </div>
                <WorkflowVisualization steps={workflowSteps} />

                {/* Coordination Panel */}
                <div className="mt-6">
                  <CoordinationPanel />
                </div>

                {/* Team Panel */}
                <div className="mt-6">
                  <TeamPanel />
                </div>

                <div className="mt-4 text-sm text-gray-500 flex items-start gap-2 bg-blue-50 p-3 rounded-md">
                  <HelpCircle className="h-4 w-4 text-blue-500 flex-shrink-0 mt-0.5" />
                  <p>
                    The concierge guides you through a personalized workflow to help you get the most out of your data.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ) : (
          <motion.div
            key="collapsed"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{
              opacity: 1,
              scale: 1,
              transition: {
                type: "spring",
                stiffness: 500,
                damping: 30
              }
            }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="absolute top-0 right-0"
          >
            <motion.div
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsExpanded(true)}
                className="flex items-center gap-1 bg-white shadow-sm border-brand-100 text-brand-700 hover:bg-brand-50"
              >
                <Compass className="h-4 w-4 text-brand-500" />
                <span>Workflow</span>
                <motion.div
                  animate={{
                    x: [0, 3, 0],
                  }}
                  transition={{
                    repeat: Infinity,
                    repeatType: "mirror",
                    duration: 1.5,
                    ease: "easeInOut",
                  }}
                >
                  <ChevronRight className="h-4 w-4" />
                </motion.div>
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
