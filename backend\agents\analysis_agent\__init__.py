"""
Analysis agent for the Datagenius backend.

This package contains the implementation of the analysis agent for the Datagenius backend,
including components for data analysis, visualization, and querying.
"""

import logging

# Configure logging
logger = logging.getLogger(__name__)

# Register analysis components
from .register import register_analysis_components
register_analysis_components()

logger.info("Initialized analysis agent package")
