"""
Component registration for the classification agent.

This module registers classification components with the component registry.
"""

import logging
from agents.components.registry import ComponentRegistry
from .components import (
    ClassificationParserComponent,
    HuggingFaceClassifierComponent,
    LLMClassifierComponent,
    ClassificationErrorHandlerComponent
)
from .components_mcp import (
    ClassificationParserComponent as MC<PERSON>lassificationParserComponent,
    MCPClassifierComponent,
    ClassificationErrorHandlerComponent as MCPClassificationErrorHandlerComponent
)

logger = logging.getLogger(__name__)


def register_classification_components():
    """Register all classification components with the registry."""
    # Register standard classification components
    ComponentRegistry.register("classification_parser", ClassificationParserComponent)
    logger.info("Registered ClassificationParserComponent with component registry")

    ComponentRegistry.register("hf_classifier", HuggingFaceClassifierComponent)
    logger.info("Registered HuggingFaceClassifierComponent with component registry")

    ComponentRegistry.register("llm_classifier", LLMClassifierComponent)
    logger.info("Registered LLMClassifierComponent with component registry")

    ComponentRegistry.register("error_handler", ClassificationErrorHandlerComponent)
    logger.info("Registered ClassificationErrorHandlerComponent with component registry")

    # Register MCP-based classification components
    ComponentRegistry.register("mcp_classifier", MCPClassifierComponent)
    logger.info("Registered MCPClassifierComponent with component registry")

    # Log the registered components
    component_names = ComponentRegistry.list_registered_components()
    logger.info(f"Registered classification components: {component_names}")
