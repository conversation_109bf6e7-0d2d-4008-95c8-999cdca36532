/**
 * Test component to preview how Composable Analyst responses will look
 * This can be used for testing the enhanced styling
 */

import React from 'react';
import { EnhancedMessage } from '../chat/EnhancedMessage';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

const sampleAnalysisResponse = `# Data Profile for: Mobile duka

## Overview

| Property | Value |
|----------|-------|
| **Rows** | 1,000 |
| **Columns** | 11 |
| **Data Types** | 7 categorical, 4 numeric |
| **Memory Usage** | 85.2 KB |

## Data Preview (first few rows)

| CustomerAge | UnitsSold | totalRevenue | Price | Category |
|-------------|-----------|--------------|-------|----------|
| 32 | 15 | 450.00 | 30.00 | Electronics |
| 28 | 8 | 240.00 | 30.00 | Clothing |
| 45 | 22 | 660.00 | 30.00 | Home |
| 24 | 12 | 360.00 | 30.00 | Electronics |
| 38 | 18 | 540.00 | 30.00 | Sports |

## Statistical Summary

| Statistic | CustomerAge | UnitsSold | totalRevenue | Price |
|-----------|-------------|-----------|--------------|-------|
| **count** | 1000.0 | 1000.0 | 1000.0 | 1000.0 |
| **mean** | 35.2 | 15.8 | 474.0 | 30.0 |
| **std** | 8.4 | 5.2 | 156.0 | 0.0 |
| **min** | 18.0 | 5.0 | 150.0 | 30.0 |
| **25%** | 28.0 | 12.0 | 360.0 | 30.0 |
| **50%** | 35.0 | 16.0 | 480.0 | 30.0 |
| **75%** | 42.0 | 20.0 | 600.0 | 30.0 |
| **max** | 65.0 | 30.0 | 900.0 | 30.0 |

## Key Insights

- **Customer Age Distribution**: The average customer age is **35.2 years** with a standard deviation of 8.4 years
- **Sales Performance**: Average units sold per transaction is **15.8 units**
- **Revenue Analysis**: Total revenue ranges from $150 to $900 per transaction
- **Price Consistency**: All items have a consistent price of **$30.00**

## Correlation Analysis

| Variable | CustomerAge | UnitsSold | totalRevenue | Price |
|----------|-------------|-----------|--------------|-------|
| **CustomerAge** | 1.000 | 0.144 | 0.143 | 0.985 |
| **UnitsSold** | 0.144 | 1.000 | 0.985 | 0.985 |
| **totalRevenue** | 0.143 | 0.985 | 1.000 | 0.985 |
| **Price** | 0.985 | 0.985 | 0.985 | 1.000 |

### Key Findings:

- **Strong Correlation**: UnitsSold and totalRevenue show a very strong positive correlation (0.985)
- **Price Impact**: Price is highly correlated with all other variables
- **Customer Behavior**: Older customers tend to purchase higher-priced items

## Next Steps

Would you like me to:
- [Visualize Distribution](action:visualize_distribution) - See how the values are distributed
- [Create Correlation Heatmap](action:correlation_heatmap) - Visual correlation analysis
- [Analyze Trends](action:analyze_trends) - Look for patterns over time
- [Generate Report](action:generate_report) - Create a comprehensive analysis report`;

const sampleCorrelationResponse = `# Correlation Analysis

I understand you're interested in finding correlations within your 'Mobile data' dataset. Correlation analysis is a powerful tool for identifying relationships between variables. Let's dive into the results.

## Correlation Matrix

| | CustomerAge | UnitsSold | totalRevenue | Price |
|---|-------------|-----------|--------------|-------|
| **CustomerAge** | 1.000000 | 0.144111 | 0.142857 | 0.984521 |
| **UnitsSold** | 0.144111 | 1.000000 | 0.984521 | 0.984521 |
| **totalRevenue** | 0.142857 | 0.984521 | 1.000000 | 0.984521 |
| **Price** | 0.984521 | 0.984521 | 0.984521 | 1.000000 |

The correlation matrix above displays the pairwise correlation coefficients between variables. A correlation coefficient ranges from -1 (perfect negative correlation) to 1 (perfect positive correlation).

## Key Findings

- **UnitsSold** and **TotalRevenue** have an extremely strong positive correlation (**0.984521**), which makes sense as the more units sold, the higher the total revenue.
- **Price** is also strongly correlated with **UnitsSold** (0.984521) and **TotalRevenue** (0.984521), indicating that higher prices are associated with higher unit sales.
- **CustomerAge** has a very strong correlation with **Price** (0.984521), suggesting that older customers tend to purchase higher-priced items.

## Statistical Significance

All correlations above **0.8** are considered very strong and statistically significant, indicating reliable relationships between these variables.

## Business Implications

1. **Revenue Optimization**: Focus on increasing units sold to maximize revenue
2. **Customer Segmentation**: Target older customers for premium products
3. **Pricing Strategy**: Consider age-based pricing models

## Next Steps

You might want to visualize:
- [Distribution of Values](action:visualize_distribution) - See how the values are distributed
- [Trends Over Time](action:visualize_trends) - If time-based data is available
- [Comparisons Between Groups](action:visualize_comparisons) - Compare different categories

Is there anything else you'd like to know about this data?`;

export const AnalysisResponsePreview: React.FC = () => {
  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      <Card>
        <CardHeader>
          <CardTitle>Composable Analyst Response Styling Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            This preview shows how Composable Analyst responses will be styled with enhanced markdown formatting.
          </p>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h2 className="text-xl font-bold text-gray-800">Sample Data Profile Response</h2>
        <EnhancedMessage
          id="test-1"
          content={sampleAnalysisResponse}
          sender="ai"
          timestamp={new Date().toISOString()}
          personaName="Composable Analyst"
          personaAvatar="/placeholder.svg"
          attachments={[]}
          metadata={{ agent_type: 'analysis' }}
          onCopy={() => console.log('Copied')}
          onFeedback={(rating) => console.log('Feedback:', rating)}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-bold text-gray-800">Sample Correlation Analysis Response</h2>
        <EnhancedMessage
          id="test-2"
          content={sampleCorrelationResponse}
          sender="ai"
          timestamp={new Date().toISOString()}
          personaName="Composable Analyst"
          personaAvatar="/placeholder.svg"
          attachments={[]}
          metadata={{ agent_type: 'analysis' }}
          onCopy={() => console.log('Copied')}
          onFeedback={(rating) => console.log('Feedback:', rating)}
        />
      </div>
    </div>
  );
};

export default AnalysisResponsePreview;
