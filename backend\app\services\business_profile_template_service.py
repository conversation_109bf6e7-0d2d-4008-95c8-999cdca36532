"""
Business Profile Template Service

This service manages industry-specific business profile templates
and provides functionality to auto-populate profile forms based on
selected industries.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class BusinessProfileTemplateService:
    """Service for managing business profile templates."""
    
    def __init__(self):
        """Initialize the template service."""
        self.templates: Dict[str, Dict[str, Any]] = {}
        self.templates_file = Path(__file__).parent.parent / "templates" / "business_profile_templates.yaml"
        self._load_templates()
    
    def _load_templates(self) -> None:
        """Load templates from YAML file."""
        try:
            if self.templates_file.exists():
                with open(self.templates_file, 'r', encoding='utf-8') as file:
                    data = yaml.safe_load(file)
                    self.templates = data.get('templates', {})
                    logger.info(f"Loaded {len(self.templates)} business profile templates")
            else:
                logger.warning(f"Templates file not found: {self.templates_file}")
                self._create_default_templates()
        except Exception as e:
            logger.error(f"Error loading business profile templates: {e}")
            self._create_default_templates()
    
    def _create_default_templates(self) -> None:
        """Create default templates if file loading fails."""
        self.templates = {
            'default': {
                'business_type': 'B2B',
                'business_size': 'small',
                'target_audience': 'Business professionals and decision-makers',
                'products_services': 'Professional services and solutions',
                'marketing_goals': 'Build brand awareness and generate leads',
                'competitive_landscape': 'Competitive market with established players',
                'budget_indicators': '$5K-25K monthly marketing budget',
                'geographic_focus': 'Regional with potential for expansion',
                'business_stage': 'growth',
                'platforms': 'LinkedIn, industry publications, networking events'
            }
        }
        logger.info("Created default business profile templates")
    
    def get_template(self, industry: str) -> Dict[str, Any]:
        """
        Get template for a specific industry.
        
        Args:
            industry: Industry name
            
        Returns:
            Template data for the industry
        """
        # Normalize industry name (remove extra spaces, convert to title case)
        normalized_industry = industry.strip().title()
        
        # Try exact match first
        if normalized_industry in self.templates:
            template = self.templates[normalized_industry].copy()
            logger.info(f"Found exact template match for industry: {normalized_industry}")
            return template
        
        # Try case-insensitive match
        for template_industry, template_data in self.templates.items():
            if template_industry.lower() == normalized_industry.lower():
                template = template_data.copy()
                logger.info(f"Found case-insensitive template match for industry: {normalized_industry}")
                return template
        
        # Return default template if no match found
        default_template = self.templates.get('default', {}).copy()
        logger.info(f"No specific template found for industry: {normalized_industry}, using default")
        return default_template
    
    def get_available_industries(self) -> list[str]:
        """
        Get list of available industries with templates.
        
        Returns:
            List of industry names
        """
        return [industry for industry in self.templates.keys() if industry != 'default']
    
    def apply_template_to_profile_data(self, industry: str, existing_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply template to profile data, preserving existing values.
        
        Args:
            industry: Industry name
            existing_data: Existing profile data to preserve
            
        Returns:
            Profile data with template applied
        """
        template = self.get_template(industry)
        
        # Start with template data
        profile_data = template.copy()
        
        # Override with existing data if provided
        if existing_data:
            for key, value in existing_data.items():
                if value is not None and value != '':
                    profile_data[key] = value
        
        # Ensure industry is set
        profile_data['industry'] = industry
        
        logger.info(f"Applied template for industry: {industry}")
        return profile_data
    
    def get_template_suggestions(self, partial_industry: str) -> list[str]:
        """
        Get template suggestions based on partial industry name.
        
        Args:
            partial_industry: Partial industry name
            
        Returns:
            List of matching industry names
        """
        partial_lower = partial_industry.lower()
        suggestions = []
        
        for industry in self.templates.keys():
            if industry != 'default' and partial_lower in industry.lower():
                suggestions.append(industry)
        
        return sorted(suggestions)
    
    def reload_templates(self) -> bool:
        """
        Reload templates from file.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self._load_templates()
            return True
        except Exception as e:
            logger.error(f"Error reloading templates: {e}")
            return False
    
    def validate_template(self, template_data: Dict[str, Any]) -> tuple[bool, list[str]]:
        """
        Validate template data structure.
        
        Args:
            template_data: Template data to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        required_fields = [
            'business_type', 'business_size', 'target_audience',
            'products_services', 'marketing_goals', 'competitive_landscape',
            'budget_indicators', 'geographic_focus', 'business_stage', 'platforms'
        ]
        
        errors = []
        
        for field in required_fields:
            if field not in template_data:
                errors.append(f"Missing required field: {field}")
            elif not isinstance(template_data[field], str) or not template_data[field].strip():
                errors.append(f"Field '{field}' must be a non-empty string")
        
        return len(errors) == 0, errors


# Create a singleton instance
business_profile_template_service = BusinessProfileTemplateService()


def get_business_profile_template_service() -> BusinessProfileTemplateService:
    """Get the business profile template service instance."""
    return business_profile_template_service
