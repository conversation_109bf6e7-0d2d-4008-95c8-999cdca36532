"""
Message queue module for the Datagenius backend.

This module provides message queue functionality for handling chat messages.
"""

from .message_queue import message_queue, MessageTask, MESSAGE_STATUS_PENDING, MESSAGE_STATUS_PROCESSING, MESSAGE_STATUS_COMPLETED, MESSAGE_STATUS_FAILED

__all__ = [
    'message_queue',
    'MessageTask',
    'MESSAGE_STATUS_PENDING',
    'MESSAGE_STATUS_PROCESSING',
    'MESSAGE_STATUS_COMPLETED',
    'MESSAGE_STATUS_FAILED'
]
