"""
Database configuration settings.

This module provides database-specific configuration with validation
and connection pool management settings.
"""

import os
from typing import Optional, Dict, Any
from pydantic import Field, field_validator, computed_field
from .base import BaseConfig


class DatabaseConfig(BaseConfig):
    """Database configuration settings."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    url: str = Field(..., description="Database connection URL")
    echo: bool = Field(default=False, description="Enable SQL query logging")
    pool_size: int = Field(default=10, ge=1, le=50, description="Connection pool size")
    max_overflow: int = Field(default=20, ge=0, le=100, description="Maximum overflow connections")
    pool_pre_ping: bool = Field(default=True, description="Enable connection pre-ping")
    pool_recycle: int = Field(default=3600, ge=300, description="Connection recycle time in seconds")

    # Connection timeout settings
    connect_timeout: int = Field(default=30, ge=5, le=300, description="Connection timeout in seconds")
    statement_timeout: int = Field(default=30000, ge=1000, description="Statement timeout in milliseconds")

    # Migration settings
    migration_timeout: int = Field(default=300, ge=60, description="Migration timeout in seconds")
    auto_migrate: bool = Field(default=False, description="Enable automatic migrations")

    # Backup settings
    backup_enabled: bool = Field(default=False, description="Enable automatic backups")
    backup_schedule: str = Field(default="0 2 * * *", description="Backup schedule (cron format)")
    backup_retention_days: int = Field(default=30, ge=1, description="Backup retention period in days")
    
    @field_validator('url')
    @classmethod
    def validate_database_url(cls, v: str) -> str:
        """Validate database URL format."""
        if not v:
            raise ValueError("Database URL cannot be empty")
        
        # Basic validation for common database schemes
        valid_schemes = ['postgresql', 'mysql', 'sqlite', 'oracle', 'mssql']
        if not any(v.startswith(f"{scheme}://") for scheme in valid_schemes):
            raise ValueError(f"Database URL must start with one of: {valid_schemes}")
        
        return v
    
    @computed_field
    @property
    def connect_args(self) -> Dict[str, Any]:
        """Generate connection arguments for SQLAlchemy."""
        args = {
            "application_name": "datagenius",
            "options": f"-c statement_timeout={self.statement_timeout}"
        }
        
        # Add PostgreSQL-specific settings
        if self.url.startswith('postgresql'):
            args.update({
                "connect_timeout": self.connect_timeout,
                "server_settings": {
                    "jit": "off",  # Disable JIT for better performance on small queries
                    "application_name": "datagenius"
                }
            })
        
        return args
    
    @computed_field
    @property
    def engine_kwargs(self) -> Dict[str, Any]:
        """Generate engine keyword arguments for SQLAlchemy."""
        return {
            "echo": self.echo,
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "pool_pre_ping": self.pool_pre_ping,
            "pool_recycle": self.pool_recycle,
            "connect_args": self.connect_args
        }
    
    @classmethod
    def from_env(cls) -> "DatabaseConfig":
        """Create database configuration from environment variables."""
        return cls(
            url=os.getenv("DATABASE_URL", ""),
            echo=os.getenv("DATABASE_ECHO", "false").lower() == "true",
            pool_size=int(os.getenv("DATABASE_POOL_SIZE", "10")),
            max_overflow=int(os.getenv("DATABASE_MAX_OVERFLOW", "20")),
            pool_pre_ping=os.getenv("DATABASE_POOL_PRE_PING", "true").lower() == "true",
            pool_recycle=int(os.getenv("DATABASE_POOL_RECYCLE", "3600")),
            connect_timeout=int(os.getenv("DATABASE_CONNECT_TIMEOUT", "30")),
            statement_timeout=int(os.getenv("DATABASE_STATEMENT_TIMEOUT", "30000")),
            migration_timeout=int(os.getenv("DATABASE_MIGRATION_TIMEOUT", "300")),
            auto_migrate=os.getenv("DATABASE_AUTO_MIGRATE", "false").lower() == "true",
            backup_enabled=os.getenv("DATABASE_BACKUP_ENABLED", "false").lower() == "true",
            backup_schedule=os.getenv("DATABASE_BACKUP_SCHEDULE", "0 2 * * *"),
            backup_retention_days=int(os.getenv("DATABASE_BACKUP_RETENTION_DAYS", "30"))
        )


class RedisConfig(BaseConfig):
    """Redis configuration settings."""

    model_config = {"extra": "ignore"}  # Ignore extra fields during validation

    url: str = Field(default="redis://localhost:6379/0", description="Redis connection URL")
    max_connections: int = Field(default=20, ge=1, le=100, description="Maximum Redis connections")
    socket_timeout: int = Field(default=5, ge=1, le=60, description="Socket timeout in seconds")
    socket_connect_timeout: int = Field(default=5, ge=1, le=60, description="Socket connect timeout")
    retry_on_timeout: bool = Field(default=True, description="Retry on timeout")
    health_check_interval: int = Field(default=30, ge=10, description="Health check interval in seconds")
    
    # Cache settings
    default_ttl: int = Field(default=3600, ge=60, description="Default cache TTL in seconds")
    key_prefix: str = Field(default="datagenius:", description="Redis key prefix")
    
    @field_validator('url')
    @classmethod
    def validate_redis_url(cls, v: str) -> str:
        """Validate Redis URL format."""
        if not v.startswith('redis://') and not v.startswith('rediss://'):
            raise ValueError("Redis URL must start with 'redis://' or 'rediss://'")
        return v
    
    @classmethod
    def from_env(cls) -> "RedisConfig":
        """Create Redis configuration from environment variables."""
        return cls(
            url=os.getenv("REDIS_URL", "redis://localhost:6379/0"),
            max_connections=int(os.getenv("REDIS_MAX_CONNECTIONS", "20")),
            socket_timeout=int(os.getenv("REDIS_SOCKET_TIMEOUT", "5")),
            socket_connect_timeout=int(os.getenv("REDIS_SOCKET_CONNECT_TIMEOUT", "5")),
            retry_on_timeout=os.getenv("REDIS_RETRY_ON_TIMEOUT", "true").lower() == "true",
            health_check_interval=int(os.getenv("REDIS_HEALTH_CHECK_INTERVAL", "30")),
            default_ttl=int(os.getenv("REDIS_DEFAULT_TTL", "3600")),
            key_prefix=os.getenv("REDIS_KEY_PREFIX", "datagenius:")
        )
