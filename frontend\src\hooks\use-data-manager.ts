import { useState, useCallback, useRef, useEffect } from 'react';
//import { dataSourceApi, fileApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface DataState {
  dataSources: any[];
  files: any[];
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  lastRefresh: number;
}

interface RefreshOptions {
  showLoading?: boolean;
  force?: boolean;
  source?: string;
}

interface ErrorState {
  count: number;
  lastError: number;
  backoffDelay: number;
  isCircuitOpen: boolean;
}

const INITIAL_BACKOFF_DELAY = 1000; // 1 second
const MAX_BACKOFF_DELAY = 30000; // 30 seconds
const CIRCUIT_BREAKER_THRESHOLD = 3;
const CIRCUIT_BREAKER_TIMEOUT = 60000; // 1 minute
const MIN_REFRESH_INTERVAL = 2000; // 2 seconds minimum between refreshes

export function useDataManager() {
  const { toast } = useToast();
  
  // Centralized data state
  const [state, setState] = useState<DataState>({
    dataSources: [],
    files: [],
    isLoading: false,
    isRefreshing: false,
    error: null,
    lastRefresh: 0,
  });

  // Error tracking for circuit breaker pattern
  const [errorState, setErrorState] = useState<ErrorState>({
    count: 0,
    lastError: 0,
    backoffDelay: INITIAL_BACKOFF_DELAY,
    isCircuitOpen: false,
  });

  // Request deduplication
  const activeRequestsRef = useRef<Set<string>>(new Set());
  const lastRefreshRef = useRef<number>(0);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Circuit breaker reset timer
  useEffect(() => {
    if (errorState.isCircuitOpen) {
      const timer = setTimeout(() => {
        console.log('Circuit breaker: Attempting to reset after timeout');
        setErrorState(prev => ({ ...prev, isCircuitOpen: false, count: 0 }));
      }, CIRCUIT_BREAKER_TIMEOUT);

      return () => clearTimeout(timer);
    }
  }, [errorState.isCircuitOpen]);

  // Check if we should allow a refresh
  const shouldAllowRefresh = useCallback((force: boolean = false): boolean => {
    const now = Date.now();
    
    // Always allow if forced
    if (force) return true;
    
    // Check circuit breaker
    if (errorState.isCircuitOpen) {
      console.log('Circuit breaker: Blocking refresh request');
      return false;
    }
    
    // Check minimum interval
    if (now - lastRefreshRef.current < MIN_REFRESH_INTERVAL) {
      console.log('Rate limiting: Too soon since last refresh');
      return false;
    }
    
    return true;
  }, [errorState.isCircuitOpen]);

  // Handle successful refresh
  const handleSuccess = useCallback(() => {
    setErrorState({
      count: 0,
      lastError: 0,
      backoffDelay: INITIAL_BACKOFF_DELAY,
      isCircuitOpen: false,
    });
  }, []);

  // Handle failed refresh with exponential backoff
  const handleError = useCallback((error: any, showUserError: boolean = false) => {
    const now = Date.now();
    
    setErrorState(prev => {
      const newCount = prev.count + 1;
      const newBackoffDelay = Math.min(prev.backoffDelay * 2, MAX_BACKOFF_DELAY);
      const shouldOpenCircuit = newCount >= CIRCUIT_BREAKER_THRESHOLD;
      
      if (shouldOpenCircuit) {
        console.error('Circuit breaker: Opening circuit due to repeated failures');
      }
      
      return {
        count: newCount,
        lastError: now,
        backoffDelay: newBackoffDelay,
        isCircuitOpen: shouldOpenCircuit,
      };
    });

    // Check for authentication errors
    if (error?.response?.status === 403 || error?.response?.status === 401) {
      console.warn('Authentication error detected - this should trigger re-authentication flow');
      if (showUserError) {
        toast({
          title: "Authentication Required",
          description: "Please log in again to access your data.",
          variant: "destructive",
        });
      }
      return 'auth_error';
    }

    // Show user error for manual refreshes
    if (showUserError) {
      toast({
        title: "Error Loading Data",
        description: "Failed to load data sources. Please try again.",
        variant: "destructive",
      });
    }

    return 'generic_error';
  }, [toast]);

  // Debounced refresh function
  const debouncedRefresh = useCallback((options: RefreshOptions = {}) => {
    const { showLoading = false, force = false, source = 'unknown' } = options;
    
    // Clear existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Set new timeout for debouncing
    refreshTimeoutRef.current = setTimeout(async () => {
      if (!shouldAllowRefresh(force)) {
        console.log(`Refresh blocked for source: ${source}`);
        return;
      }

      const requestKey = `refresh-${Date.now()}`;
      
      // Check if similar request is already in progress
      if (activeRequestsRef.current.has('refresh') && !force) {
        console.log('Refresh already in progress, skipping duplicate request');
        return;
      }

      activeRequestsRef.current.add('refresh');
      lastRefreshRef.current = Date.now();

      console.log(`Starting data refresh from source: ${source}`);

      setState(prev => ({
        ...prev,
        isLoading: showLoading,
        isRefreshing: !showLoading,
        error: null,
      }));

      try {
        // Load data sources and files in parallel
        const [dataSourcesResponse, filesResponse] = await Promise.all([
          dataSourceApi.getDataSources(),
          fileApi.getFiles(),
        ]);

        setState(prev => ({
          ...prev,
          dataSources: dataSourcesResponse.data_sources || [],
          files: filesResponse.files || [],
          isLoading: false,
          isRefreshing: false,
          error: null,
          lastRefresh: Date.now(),
        }));

        handleSuccess();
        console.log(`Data refresh completed successfully from source: ${source}`);

      } catch (error: any) {
        console.error(`Data refresh failed from source: ${source}`, error);
        
        const errorType = handleError(error, showLoading);
        
        setState(prev => ({
          ...prev,
          isLoading: false,
          isRefreshing: false,
          error: errorType === 'auth_error' ? 'Authentication required' : 'Failed to load data',
        }));
      } finally {
        activeRequestsRef.current.delete('refresh');
      }
    }, force ? 0 : 300); // 300ms debounce, immediate if forced
  }, [shouldAllowRefresh, handleSuccess, handleError]);

  // Public refresh function
  const refresh = useCallback((options: RefreshOptions = {}) => {
    debouncedRefresh(options);
  }, [debouncedRefresh]);

  // Force refresh (bypasses all checks)
  const forceRefresh = useCallback(() => {
    refresh({ force: true, showLoading: true, source: 'manual' });
  }, [refresh]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  return {
    // Data state
    dataSources: state.dataSources,
    files: state.files,
    isLoading: state.isLoading,
    isRefreshing: state.isRefreshing,
    error: state.error,
    lastRefresh: state.lastRefresh,
    
    // Error state
    hasErrors: errorState.count > 0,
    isCircuitOpen: errorState.isCircuitOpen,
    errorCount: errorState.count,
    
    // Actions
    refresh,
    forceRefresh,
  };
}
