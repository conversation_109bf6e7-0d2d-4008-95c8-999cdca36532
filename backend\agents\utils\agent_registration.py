"""
Agent Registration Utilities

This module provides utilities for agents to easily register their identities
and system prompts with the dynamic agent identity system.
"""

import logging
from typing import Dict, Any, List, Optional
from .agent_identity import get_agent_identity_registry, AgentIdentityInfo
from .system_prompts import register_agent_system_prompt

logger = logging.getLogger(__name__)


async def register_agent(
    agent_id: str,
    identity: str,
    name: str,
    description: str,
    keywords: List[str],
    capabilities: List[str],
    system_prompt: Optional[str] = None
) -> None:
    """
    Register an agent with the identity system.
    
    Args:
        agent_id: Unique agent ID (e.g., 'composable-research-ai')
        identity: Agent identity type (e.g., 'researcher')
        name: Human-readable agent name
        description: Agent description
        keywords: Keywords for identity matching
        capabilities: List of agent capabilities
        system_prompt: Optional system prompt for the agent
    """
    try:
        # Create identity info
        identity_info = AgentIdentityInfo(
            identity=identity,
            name=name,
            description=description,
            keywords=keywords,
            capabilities=capabilities,
            system_prompt=system_prompt
        )
        
        # Register with identity registry
        registry = await get_agent_identity_registry()
        registry.register_identity(agent_id, identity_info)
        
        # Register system prompt if provided
        if system_prompt:
            await register_agent_system_prompt(identity, system_prompt)
        
        logger.info(f"Successfully registered agent: {agent_id} -> {identity}")
        
    except Exception as e:
        logger.error(f"Failed to register agent {agent_id}: {e}", exc_info=True)
        raise


async def register_agent_from_config(config: Dict[str, Any]) -> None:
    """
    Register an agent from its configuration dictionary.
    
    Args:
        config: Agent configuration dictionary
    """
    try:
        agent_id = config.get("id", "")
        if not agent_id:
            logger.warning("Cannot register agent: missing 'id' in config")
            return
        
        # Extract information from config
        name = config.get("name", "")
        description = config.get("description", "")
        industry = config.get("industry", "")
        skills = config.get("skills", [])
        capabilities = config.get("capabilities", [])
        system_prompt = config.get("system_prompts", {}).get("default")
        
        # Determine identity (let the registry figure it out)
        registry = await get_agent_identity_registry()
        identity = registry._extract_identity_from_config(
            config, agent_id, name, industry, skills
        )
        
        # Extract keywords
        keywords = registry._extract_keywords(name, description, industry, skills)
        
        # Register the agent
        await register_agent(
            agent_id=agent_id,
            identity=identity,
            name=name,
            description=description,
            keywords=keywords,
            capabilities=capabilities,
            system_prompt=system_prompt
        )
        
    except Exception as e:
        logger.error(f"Failed to register agent from config: {e}", exc_info=True)
        raise


async def auto_register_agents_from_personas_dir(personas_dir: str = "backend/personas") -> None:
    """
    Automatically register all agents from the personas directory.
    
    Args:
        personas_dir: Directory containing persona YAML files
    """
    try:
        # The identity registry already scans the personas directory during initialization
        # This function is mainly for explicit re-registration if needed
        registry = await get_agent_identity_registry()
        await registry.initialize(personas_dir)
        
        logger.info("Auto-registration of agents completed")
        
    except Exception as e:
        logger.error(f"Failed to auto-register agents: {e}", exc_info=True)
        raise


def create_agent_context(
    agent_id: str,
    persona_id: Optional[str] = None,
    additional_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a properly formatted user context for agent identity detection.
    
    Args:
        agent_id: Agent ID
        persona_id: Optional persona ID (defaults to agent_id)
        additional_context: Additional context to include
        
    Returns:
        Formatted user context dictionary
    """
    context = {
        "agent_id": agent_id,
        "persona_id": persona_id or agent_id,
        "current_persona": persona_id or agent_id
    }
    
    if additional_context:
        context.update(additional_context)
    
    return context


# Convenience functions for common agent types
async def register_analyst_agent(
    agent_id: str,
    name: str,
    description: str,
    additional_keywords: Optional[List[str]] = None,
    system_prompt: Optional[str] = None
) -> None:
    """Register an analyst-type agent."""
    keywords = ["analysis", "data", "visualization", "statistics", "insights"]
    if additional_keywords:
        keywords.extend(additional_keywords)
    
    await register_agent(
        agent_id=agent_id,
        identity="analyst",
        name=name,
        description=description,
        keywords=keywords,
        capabilities=["data_analysis", "data_visualization", "statistical_analysis"],
        system_prompt=system_prompt
    )


async def register_marketer_agent(
    agent_id: str,
    name: str,
    description: str,
    additional_keywords: Optional[List[str]] = None,
    system_prompt: Optional[str] = None
) -> None:
    """Register a marketer-type agent."""
    keywords = ["marketing", "content", "campaign", "strategy", "promotion"]
    if additional_keywords:
        keywords.extend(additional_keywords)
    
    await register_agent(
        agent_id=agent_id,
        identity="marketer",
        name=name,
        description=description,
        keywords=keywords,
        capabilities=["content_creation", "marketing_strategy", "campaign_planning"],
        system_prompt=system_prompt
    )


async def register_classifier_agent(
    agent_id: str,
    name: str,
    description: str,
    additional_keywords: Optional[List[str]] = None,
    system_prompt: Optional[str] = None
) -> None:
    """Register a classifier-type agent."""
    keywords = ["classification", "categorization", "organization", "tagging"]
    if additional_keywords:
        keywords.extend(additional_keywords)
    
    await register_agent(
        agent_id=agent_id,
        identity="classifier",
        name=name,
        description=description,
        keywords=keywords,
        capabilities=["text_classification", "data_organization", "content_categorization"],
        system_prompt=system_prompt
    )


async def register_custom_agent(
    agent_id: str,
    identity: str,
    name: str,
    description: str,
    keywords: List[str],
    capabilities: List[str],
    system_prompt: str
) -> None:
    """Register a custom agent type."""
    await register_agent(
        agent_id=agent_id,
        identity=identity,
        name=name,
        description=description,
        keywords=keywords,
        capabilities=capabilities,
        system_prompt=system_prompt
    )


# Example usage for new agent types:
"""
# Register a new researcher agent
await register_custom_agent(
    agent_id="composable-researcher-ai",
    identity="researcher",
    name="Research Assistant",
    description="Specialized in research and information gathering",
    keywords=["research", "investigation", "analysis", "information", "study"],
    capabilities=["research", "information_gathering", "fact_checking"],
    system_prompt="You are a Research Assistant specialized in..."
)

# Or use the config-based approach
config = {
    "id": "composable-researcher-ai",
    "name": "Research Assistant", 
    "description": "Specialized in research and information gathering",
    "industry": "Research",
    "skills": ["Research", "Information Gathering", "Fact Checking"],
    "capabilities": ["research", "information_gathering", "fact_checking"],
    "system_prompts": {
        "default": "You are a Research Assistant specialized in..."
    }
}
await register_agent_from_config(config)
"""
