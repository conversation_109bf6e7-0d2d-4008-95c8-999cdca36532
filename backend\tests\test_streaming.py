#!/usr/bin/env python3
"""
Test script to verify universal streaming functionality.

This script tests the StreamingMixin to ensure all agents can stream responses
without requiring custom streaming code.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add backend root to path
backend_root = Path(__file__).parent
sys.path.insert(0, str(backend_root))

from agents.mixins.streaming_mixin import StreamingMixin
from agents.enhanced_base import EnhancedBaseAgent


class TestAgent(EnhancedBaseAgent):
    """Simple test agent to verify streaming functionality."""

    def __init__(self):
        super().__init__()
        self.name = "test-agent"
        self.config = {
            "streaming": {
                "chunk_size": 20,
                "delay_ms": 50
            }
        }

    async def _initialize(self):
        """Initialize the agent (required by base class)."""
        pass

    async def _process_message(self, message: str, user_id: str, conversation_id: str, context: dict) -> dict:
        """Process a message and return a response (required by base class)."""
        response_text = f"Hello! You said: '{message}'. This is a test response from the test agent. " \
                       f"The streaming mixin should automatically stream this response in chunks without " \
                       f"requiring any custom streaming code in this agent."

        return {
            "message": response_text,
            "metadata": {
                "agent": self.name,
                "processed": True,
                "test": True
            },
            "success": True
        }

    async def process_message(self, message: str, user_id: str, conversation_id: str, context: dict) -> dict:
        """Public process_message method that calls the abstract _process_message."""
        return await self._process_message(message, user_id, conversation_id, context)


async def test_streaming():
    """Test the universal streaming functionality."""
    print("🧪 Testing Universal Streaming Functionality")
    print("=" * 50)
    
    # Create test agent
    agent = TestAgent()
    
    # Test message
    test_message = "Hello, can you stream this response?"
    test_context = {
        "test": True,
        "content_type": "medium"
    }
    
    print(f"📝 Test Message: {test_message}")
    print(f"🔧 Test Context: {test_context}")
    print("\n🚀 Starting streaming test...")
    print("-" * 30)
    
    try:
        # Test streaming capability
        print("✅ Agent supports streaming:", agent.supports_streaming())
        print("📊 Streaming capabilities:", agent.get_streaming_capabilities())
        print()
        
        # Stream the response
        full_response = ""
        chunk_count = 0
        
        async for chunk_data in agent.process_streaming_message(
            message=test_message,
            user_id="test_user",
            conversation_id="test_conversation",
            context=test_context
        ):
            chunk_type = chunk_data.get("type")
            
            if chunk_type == "content":
                content = chunk_data.get("content", "")
                full_response += content
                chunk_count += 1
                print(f"📦 Chunk {chunk_count}: '{content}'")
                
            elif chunk_type == "metadata":
                metadata = chunk_data.get("metadata", {})
                print(f"📋 Final Metadata: {metadata}")
        
        print("-" * 30)
        print(f"✅ Streaming completed successfully!")
        print(f"📊 Total chunks received: {chunk_count}")
        print(f"📝 Full response: {full_response}")
        print(f"📏 Response length: {len(full_response)} characters")
        
        # Verify the response matches what the agent would return normally
        normal_response = await agent.process_message(
            test_message, "test_user", "test_conversation", test_context
        )
        
        expected_text = normal_response.get("message", "")
        if full_response.strip() == expected_text.strip():
            print("✅ Streamed response matches normal response!")
        else:
            print("❌ Streamed response doesn't match normal response")
            print(f"Expected: {expected_text}")
            print(f"Got: {full_response}")
        
    except Exception as e:
        print(f"❌ Error during streaming test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n🎉 Universal streaming test completed successfully!")
    return True


async def test_configuration():
    """Test streaming configuration loading."""
    print("\n🔧 Testing Streaming Configuration")
    print("=" * 50)
    
    from agents.mixins.streaming_mixin import STREAMING_CONFIG
    
    print("📋 Loaded streaming configuration:")
    print(f"   Default config: {STREAMING_CONFIG.get('default', {})}")
    print(f"   Agent configs: {list(STREAMING_CONFIG.get('agents', {}).keys())}")
    print(f"   Content types: {list(STREAMING_CONFIG.get('content_types', {}).keys())}")
    print(f"   Features: {STREAMING_CONFIG.get('features', {})}")
    
    return True


if __name__ == "__main__":
    async def main():
        print("🚀 Universal Streaming Test Suite")
        print("=" * 60)
        
        # Test configuration loading
        config_success = await test_configuration()
        
        # Test streaming functionality
        streaming_success = await test_streaming()
        
        print("\n" + "=" * 60)
        if config_success and streaming_success:
            print("🎉 All tests passed! Universal streaming is working correctly.")
            return 0
        else:
            print("❌ Some tests failed. Check the output above.")
            return 1
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
