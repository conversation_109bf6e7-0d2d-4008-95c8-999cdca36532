import React, { useState, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Loader2, SearchIcon, AlertTriangle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { apiClient } from '@/services/apiClient';
import { DashboardLayout } from '@/components/DashboardLayout'; // Assuming a layout component
import { Link } from 'react-router-dom'; // For linking to results
import { SearchResultsResponse, SearchRecommendationResponse, SearchRecommendationRequest } from '@/schemas/search'; // Assuming search schemas exist

// Debounce utility
const debounce = <F extends (...args: any[]) => any>(func: F, waitFor: number) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  const debounced = (...args: Parameters<F>) => {
    if (timeout !== null) {
      clearTimeout(timeout);
      timeout = null;
    }
    timeout = setTimeout(() => func(...args), waitFor);
  };

  return debounced as (...args: Parameters<F>) => ReturnType<F>;
};

const SearchPage: React.FC = () => {
  const [query, setQuery] = useState<string>('');
  const [searchResults, setSearchResults] = useState<SearchResultsResponse | null>(null);
  const [recommendations, setRecommendations] = useState<SearchRecommendationResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const performSearch = async (currentQuery: string, offset: number = 0) => {
    if (!currentQuery.trim()) {
      setSearchResults(null);
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.get<SearchResultsResponse>('/search/', {
        params: { query: currentQuery, limit: 10, offset },
      });
      setSearchResults(response.data);
    } catch (err) {
      console.error("Search failed:", err);
      setError("Search failed. Please try again.");
      toast({ title: "Search Error", description: "Could not perform search.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRecommendations = async () => {
    setIsLoadingRecommendations(true);
    try {
      const requestData: SearchRecommendationRequest = { limit: 5 }; // Add user_id or session_id if available
      const response = await apiClient.post<SearchRecommendationResponse>('/search/recommendations', requestData);
      setRecommendations(response.data);
    } catch (err) {
      console.error("Failed to fetch recommendations:", err);
      // Optionally toast, or handle silently
    } finally {
      setIsLoadingRecommendations(false);
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(debounce(performSearch, 500), []);

  useEffect(() => {
    debouncedSearch(query);
  }, [query, debouncedSearch]);

  useEffect(() => {
    fetchRecommendations(); // Fetch initial recommendations
  }, []);

  const getResultLink = (result: any): string => {
    switch (result.type) {
      case 'persona':
        return `/ai-marketplace/persona/${result.id}`; // Assuming such a route exists
      case 'report':
        return `/reports/${result.id}`;
      case 'dashboard': // Assuming a generic dashboard item
        return `/dashboard`; // Or a more specific link if available
      default:
        return '#';
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto p-4">
        <h1 className="text-3xl font-bold mb-6">Search</h1>
        <div className="flex items-center mb-6">
          <Input
            type="search"
            placeholder="Search personas, reports, dashboards..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="flex-grow"
          />
          <Button onClick={() => performSearch(query)} className="ml-2" disabled={isLoading}>
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <SearchIcon className="h-4 w-4" />}
            <span className="ml-2 hidden sm:inline">Search</span>
          </Button>
        </div>

        {error && (
          <div className="text-red-500 bg-red-100 p-3 rounded-md flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" /> {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <h2 className="text-2xl font-semibold mb-4">Results</h2>
            {isLoading && !searchResults && <Loader2 className="h-8 w-8 animate-spin text-brand-500" />}
            {!isLoading && searchResults && searchResults.results.length === 0 && query && (
              <p>No results found for "{searchResults.query}".</p>
            )}
            {!isLoading && !query && !searchResults && (
              <p>Enter a search term to see results.</p>
            )}
            {searchResults && searchResults.results.length > 0 && (
              <div className="space-y-4">
                {searchResults.results.map((result, index) => (
                  <Card key={result.id || index}>
                    <CardHeader>
                      <CardTitle>
                        <Link to={getResultLink(result)} className="hover:underline">
                          {result.title || 'Untitled Result'}
                        </Link>
                      </CardTitle>
                      <CardDescription>Type: {result.type || 'Unknown'} - Score: {result.score?.toFixed(2) || 'N/A'}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 line-clamp-2">{result.snippet || 'No snippet available.'}</p>
                    </CardContent>
                  </Card>
                ))}
                {/* TODO: Add pagination controls */}
              </div>
            )}
          </div>

          <div className="md:col-span-1">
            <h2 className="text-2xl font-semibold mb-4">Recommendations</h2>
            {isLoadingRecommendations && <Loader2 className="h-6 w-6 animate-spin text-brand-500" />}
            {recommendations && recommendations.recommendations.length > 0 && (
              <div className="space-y-3">
                {recommendations.recommendations.map((rec) => (
                  <Card key={rec.item_id} className="p-3">
                    <p className="font-semibold text-sm">
                      <Link to={getResultLink(rec)} className="hover:underline">
                        {rec.item_id} ({rec.item_type})
                      </Link>
                    </p>
                    {rec.reasoning && <p className="text-xs text-gray-500">{rec.reasoning}</p>}
                    <p className="text-xs text-gray-400">Score: {rec.score.toFixed(2)}</p>
                  </Card>
                ))}
              </div>
            )}
            {!isLoadingRecommendations && (!recommendations || recommendations.recommendations.length === 0) && (
              <p className="text-sm text-gray-500">No recommendations available at the moment.</p>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default SearchPage;
