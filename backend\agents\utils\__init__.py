"""
Agent Utilities Package

This package provides utilities for the dynamic agent identity system.
"""

from .agent_identity import (
    AgentIdentityRegistry,
    AgentIdentityInfo,
    get_agent_identity_registry,
    detect_agent_identity
)

from .system_prompts import (
    SystemPromptRegistry,
    get_system_prompt_registry,
    get_agent_system_prompt,
    register_agent_system_prompt
)

from .agent_registration import (
    register_agent,
    register_agent_from_config,
    auto_register_agents_from_personas_dir,
    create_agent_context,
    register_analyst_agent,
    register_marketer_agent,
    register_classifier_agent,
    register_custom_agent
)

__all__ = [
    # Agent Identity
    "AgentIdentityRegistry",
    "AgentIdentityInfo",
    "get_agent_identity_registry",
    "detect_agent_identity",

    # System Prompts
    "SystemPromptRegistry",
    "get_system_prompt_registry",
    "get_agent_system_prompt",
    "register_agent_system_prompt",

    # Agent Registration
    "register_agent",
    "register_agent_from_config",
    "auto_register_agents_from_personas_dir",
    "create_agent_context",
    "register_analyst_agent",
    "register_marketer_agent",
    "register_classifier_agent",
    "register_custom_agent"
]
