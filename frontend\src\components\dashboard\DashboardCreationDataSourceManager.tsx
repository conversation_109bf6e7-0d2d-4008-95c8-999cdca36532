import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Plus, 
  Database, 
  FileText, 
  Globe, 
  Server,
  Trash2,
  AlertCircle,
  CheckCircle2,
  Info,
  Edit2,
  X
} from 'lucide-react';
import { DashboardDataSourceAssignment, DashboardDataSourceAssignmentCreate, DashboardDataSourceAssignmentUpdate, SystemDataSource } from '@/types/dashboard-customization';
import { MultiSelectDataSourceSelector } from './MultiSelectDataSourceSelector';
import { dataSourceApi } from '@/lib/dataSourceApi';
import { useToast } from '@/hooks/use-toast';

interface DashboardCreationDataSourceManagerProps {
  dataSourceAssignments: DashboardDataSourceAssignment[];
  onAddDataSourceAssignment: (assignment: DashboardDataSourceAssignmentCreate) => void;
  onUpdateDataSourceAssignment: (id: string, updates: DashboardDataSourceAssignmentUpdate) => void;
  onDeleteDataSourceAssignment: (id: string) => void;
}

const DATA_SOURCE_ICONS = {
  file: FileText,
  database: Database,
  api: Globe,
  mcp: Server,
};

const DATA_SOURCE_TYPE_LABELS = {
  file: 'File Upload',
  database: 'Database',
  api: 'API',
  mcp: 'MCP Server',
};

export const DashboardCreationDataSourceManager: React.FC<DashboardCreationDataSourceManagerProps> = ({
  dataSourceAssignments,
  onAddDataSourceAssignment,
  onUpdateDataSourceAssignment,
  onDeleteDataSourceAssignment,
}) => {
  const { toast } = useToast();
  const [showMultiSelectDialog, setShowMultiSelectDialog] = useState(false);
  const [systemDataSources, setSystemDataSources] = useState<SystemDataSource[]>([]);
  const [isLoadingDataSources, setIsLoadingDataSources] = useState(false);
  const [editingAlias, setEditingAlias] = useState<string | null>(null);
  const [aliasValue, setAliasValue] = useState('');

  // Load available system data sources
  useEffect(() => {
    loadSystemDataSources();
  }, []);

  const loadSystemDataSources = async () => {
    setIsLoadingDataSources(true);
    try {
      const response = await dataSourceApi.getDataSources();
      const activeSources = (response.data_sources || []).filter(ds => ds.is_active);
      setSystemDataSources(activeSources);
    } catch (error) {
      console.error('Failed to load system data sources:', error);
      toast({
        title: "Error",
        description: "Failed to load available data sources.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingDataSources(false);
    }
  };

  // Handle multi-select data source assignment
  const handleMultiSelectDataSources = (dataSourceIds: string[]) => {
    dataSourceIds.forEach(dataSourceId => {
      const systemDataSource = systemDataSources.find(ds => ds.id === dataSourceId);
      if (systemDataSource) {
        const assignment: DashboardDataSourceAssignmentCreate = {
          system_data_source_id: dataSourceId,
          alias: systemDataSource.name, // Default alias to the data source name
          is_active: true,
        };
        onAddDataSourceAssignment(assignment);
      }
    });

    toast({
      title: "Data Sources Added",
      description: `Successfully added ${dataSourceIds.length} data source${dataSourceIds.length !== 1 ? 's' : ''} to the dashboard.`,
    });
  };

  // Handle alias editing
  const handleStartEditAlias = (assignmentId: string, currentAlias?: string) => {
    setEditingAlias(assignmentId);
    setAliasValue(currentAlias || '');
  };

  const handleSaveAlias = (assignmentId: string) => {
    onUpdateDataSourceAssignment(assignmentId, { alias: aliasValue.trim() || undefined });
    setEditingAlias(null);
    setAliasValue('');
  };

  const handleCancelEditAlias = () => {
    setEditingAlias(null);
    setAliasValue('');
  };

  // Get existing assignment IDs to prevent duplicates
  const existingDataSourceIds = dataSourceAssignments.map(assignment => assignment.system_data_source_id);

  // Get system data source info for assignments
  const getSystemDataSourceInfo = (systemDataSourceId: string) => {
    return systemDataSources.find(ds => ds.id === systemDataSourceId);
  };

  const formatDataSourceType = (type: string) => {
    return DATA_SOURCE_TYPE_LABELS[type as keyof typeof DATA_SOURCE_TYPE_LABELS] || type;
  };

  const getDataSourceIcon = (type: string) => {
    return DATA_SOURCE_ICONS[type as keyof typeof DATA_SOURCE_ICONS] || Database;
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Data Sources</h3>
          <p className="text-sm text-muted-foreground">
            Select existing data sources to include in this dashboard
          </p>
        </div>
        <Button
          onClick={() => setShowMultiSelectDialog(true)}
          disabled={isLoadingDataSources || systemDataSources.length === 0}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Data Sources
        </Button>
      </div>

      {/* Info Alert */}
      {systemDataSources.length === 0 && !isLoadingDataSources ? (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No data sources are available. Please visit the <strong>Data Integration</strong> page to upload files or connect to databases before creating dashboards.
          </AlertDescription>
        </Alert>
      ) : (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Data sources are managed in the Data Integration page. Only existing data sources can be assigned to dashboards.
            You can assign custom aliases to data sources within this dashboard context.
          </AlertDescription>
        </Alert>
      )}

      {/* Data Source Assignments */}
      {dataSourceAssignments.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <Database className="h-12 w-12 text-muted-foreground mb-4" />
            <h4 className="font-semibold mb-2">No Data Sources Selected</h4>
            <p className="text-sm text-muted-foreground mb-4">
              This dashboard will start empty. You can add data sources now or after creating the dashboard.
            </p>
            <Button
              variant="outline"
              onClick={() => setShowMultiSelectDialog(true)}
              disabled={isLoadingDataSources || systemDataSources.length === 0}
            >
              <Plus className="h-4 w-4 mr-2" />
              {systemDataSources.length === 0 ? 'No Data Sources Available' : 'Select Data Sources'}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {dataSourceAssignments.map((assignment) => {
            const systemDataSource = getSystemDataSourceInfo(assignment.system_data_source_id);
            const Icon = systemDataSource ? getDataSourceIcon(systemDataSource.type) : Database;
            const isEditing = editingAlias === assignment.id;

            return (
              <Card key={assignment.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    <Icon className="h-8 w-8 text-muted-foreground" />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold">
                          {systemDataSource?.name || 'Unknown Data Source'}
                        </h4>
                        {systemDataSource && (
                          <Badge variant="outline">
                            {formatDataSourceType(systemDataSource.type)}
                          </Badge>
                        )}
                        {!assignment.is_active && (
                          <Badge variant="secondary">Inactive</Badge>
                        )}
                      </div>
                      
                      {/* Alias editing */}
                      <div className="mt-1">
                        {isEditing ? (
                          <div className="flex items-center space-x-2">
                            <Label className="text-xs text-muted-foreground">Alias:</Label>
                            <Input
                              value={aliasValue}
                              onChange={(e) => setAliasValue(e.target.value)}
                              placeholder="Enter alias (optional)"
                              className="h-7 text-sm flex-1"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleSaveAlias(assignment.id);
                                } else if (e.key === 'Escape') {
                                  handleCancelEditAlias();
                                }
                              }}
                              autoFocus
                            />
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleSaveAlias(assignment.id)}
                              className="h-7 px-2"
                            >
                              <CheckCircle2 className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={handleCancelEditAlias}
                              className="h-7 px-2"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-muted-foreground">
                              Alias: {assignment.alias || 'None'}
                            </span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleStartEditAlias(assignment.id, assignment.alias)}
                              className="h-5 w-5 p-0"
                            >
                              <Edit2 className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                      
                      {systemDataSource?.description && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {systemDataSource.description}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onDeleteDataSourceAssignment(assignment.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </Card>
            );
          })}
        </div>
      )}

      {/* Multi-Select Dialog */}
      <MultiSelectDataSourceSelector
        open={showMultiSelectDialog}
        onOpenChange={setShowMultiSelectDialog}
        onDataSourcesSelected={handleMultiSelectDataSources}
        existingAssignments={existingDataSourceIds}
      />
    </div>
  );
};
