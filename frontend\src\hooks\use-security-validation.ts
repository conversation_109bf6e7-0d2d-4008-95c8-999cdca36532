import { useState, useCallback, useMemo } from 'react';
import { useToast } from '@/hooks/use-toast';

export interface SecurityValidationResult {
  isValid: boolean;
  sanitizedValue: string;
  threats: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

export interface SecurityConfig {
  enableXSSProtection: boolean;
  enableSQLInjectionProtection: boolean;
  enableCSRFProtection: boolean;
  maxInputLength: number;
  allowedTags: string[];
  blockedPatterns: string[];
}

const DEFAULT_CONFIG: SecurityConfig = {
  enableXSSProtection: true,
  enableSQLInjectionProtection: true,
  enableCSRFProtection: true,
  maxInputLength: 10000,
  allowedTags: ['b', 'i', 'u', 'strong', 'em', 'br', 'p'],
  blockedPatterns: [],
};

export const useSecurityValidation = (config: Partial<SecurityConfig> = {}) => {
  const { toast } = useToast();
  const [validationHistory, setValidationHistory] = useState<Array<{
    timestamp: Date;
    input: string;
    result: SecurityValidationResult;
  }>>([]);

  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);

  // XSS patterns
  const xssPatterns = useMemo(() => [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /onload\s*=/gi,
    /onerror\s*=/gi,
    /onclick\s*=/gi,
    /onmouseover\s*=/gi,
    /onmouseout\s*=/gi,
    /onfocus\s*=/gi,
    /onblur\s*=/gi,
    /onchange\s*=/gi,
    /onsubmit\s*=/gi,
    /<iframe[^>]*>/gi,
    /<object[^>]*>/gi,
    /<embed[^>]*>/gi,
    /<link[^>]*>/gi,
    /<meta[^>]*>/gi,
    /data:text\/html/gi,
    /data:application\/javascript/gi,
  ], []);

  // SQL injection patterns
  const sqlInjectionPatterns = useMemo(() => [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)/gi,
    /(--|#|\/\*|\*\/)/g,
    /(\b(UNION|OR|AND)\b.*\b(SELECT|INSERT|UPDATE|DELETE)\b)/gi,
    /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)/gi,
    /(\b(EXEC|EXECUTE|SP_|XP_)\b)/gi,
    /('|(\\')|(;)|(\\;)|(\|)|(\*)|(%)|(<)|(>)|(\^)|(\[)|(\])|(\{)|(\})|(\$)|(\+)|(!)|(@))/g,
  ], []);

  // Path traversal patterns
  const pathTraversalPatterns = useMemo(() => [
    /\.\.\//g,
    /\.\.\\/g,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi,
    /\.\.%2f/gi,
    /\.\.%5c/gi,
  ], []);

  // Command injection patterns
  const commandInjectionPatterns = useMemo(() => [
    /(\||;|&|`|\$\()/g,
    /(\b(cat|ls|dir|type|echo|ping|wget|curl|rm|del|format|fdisk)\b)/gi,
  ], []);

  const sanitizeHTML = useCallback((input: string): string => {
    if (!input) return input;

    let sanitized = input;

    // Remove script tags and their content
    sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');

    // Remove dangerous event handlers
    sanitized = sanitized.replace(/on\w+\s*=\s*["'][^"']*["']/gi, '');

    // Remove javascript: and vbscript: protocols
    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');

    // Remove data: URLs that could contain scripts
    sanitized = sanitized.replace(/data:text\/html[^"']*/gi, '');
    sanitized = sanitized.replace(/data:application\/javascript[^"']*/gi, '');

    // Remove dangerous tags
    const dangerousTags = ['iframe', 'object', 'embed', 'link', 'meta', 'form', 'input'];
    dangerousTags.forEach(tag => {
      const regex = new RegExp(`<${tag}[^>]*>.*?<\/${tag}>`, 'gi');
      sanitized = sanitized.replace(regex, '');
      const selfClosingRegex = new RegExp(`<${tag}[^>]*\/?>`, 'gi');
      sanitized = sanitized.replace(selfClosingRegex, '');
    });

    // Only allow specific tags if configured
    if (finalConfig.allowedTags.length > 0) {
      const allowedTagsRegex = new RegExp(`<(?!\/?(?:${finalConfig.allowedTags.join('|')})\b)[^>]+>`, 'gi');
      sanitized = sanitized.replace(allowedTagsRegex, '');
    }

    return sanitized.trim();
  }, [finalConfig.allowedTags]);

  const detectThreats = useCallback((input: string): { threats: string[]; riskLevel: 'low' | 'medium' | 'high' } => {
    const threats: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    if (!input) return { threats, riskLevel };

    // Check for XSS
    if (finalConfig.enableXSSProtection) {
      for (const pattern of xssPatterns) {
        if (pattern.test(input)) {
          threats.push('Potential XSS attack detected');
          riskLevel = 'high';
          break;
        }
      }
    }

    // Check for SQL injection
    if (finalConfig.enableSQLInjectionProtection) {
      for (const pattern of sqlInjectionPatterns) {
        if (pattern.test(input)) {
          threats.push('Potential SQL injection detected');
          riskLevel = 'high';
          break;
        }
      }
    }

    // Check for path traversal
    for (const pattern of pathTraversalPatterns) {
      if (pattern.test(input)) {
        threats.push('Potential path traversal attack detected');
        riskLevel = riskLevel === 'high' ? 'high' : 'medium';
        break;
      }
    }

    // Check for command injection
    for (const pattern of commandInjectionPatterns) {
      if (pattern.test(input)) {
        threats.push('Potential command injection detected');
        riskLevel = 'high';
        break;
      }
    }

    // Check custom blocked patterns
    for (const pattern of finalConfig.blockedPatterns) {
      const regex = new RegExp(pattern, 'gi');
      if (regex.test(input)) {
        threats.push('Input contains blocked content');
        riskLevel = riskLevel === 'high' ? 'high' : 'medium';
        break;
      }
    }

    // Check input length
    if (input.length > finalConfig.maxInputLength) {
      threats.push(`Input exceeds maximum length of ${finalConfig.maxInputLength} characters`);
      riskLevel = riskLevel === 'high' ? 'high' : 'medium';
    }

    return { threats, riskLevel };
  }, [finalConfig, xssPatterns, sqlInjectionPatterns, pathTraversalPatterns, commandInjectionPatterns]);

  const validateInput = useCallback((input: string, showToasts: boolean = true): SecurityValidationResult => {
    const { threats, riskLevel } = detectThreats(input);
    const sanitizedValue = sanitizeHTML(input);
    const isValid = threats.length === 0;

    const result: SecurityValidationResult = {
      isValid,
      sanitizedValue,
      threats,
      riskLevel,
    };

    // Add to validation history
    setValidationHistory(prev => [
      ...prev.slice(-99), // Keep last 100 entries
      {
        timestamp: new Date(),
        input,
        result,
      }
    ]);

    // Show toast notifications for threats
    if (!isValid && showToasts) {
      toast({
        title: "Security Warning",
        description: `${threats.length} security threat(s) detected in input`,
        variant: riskLevel === 'high' ? 'destructive' : 'default',
      });
    }

    return result;
  }, [detectThreats, sanitizeHTML, toast]);

  const validateForm = useCallback((formData: Record<string, any>): {
    isValid: boolean;
    sanitizedData: Record<string, any>;
    threats: Record<string, string[]>;
  } => {
    const sanitizedData: Record<string, any> = {};
    const threats: Record<string, string[]> = {};
    let isValid = true;

    for (const [key, value] of Object.entries(formData)) {
      if (typeof value === 'string') {
        const validation = validateInput(value, false);
        sanitizedData[key] = validation.sanitizedValue;
        
        if (!validation.isValid) {
          threats[key] = validation.threats;
          isValid = false;
        }
      } else {
        sanitizedData[key] = value;
      }
    }

    return { isValid, sanitizedData, threats };
  }, [validateInput]);

  const generateCSRFToken = useCallback((): string => {
    // Generate a random CSRF token
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }, []);

  const validateCSRFToken = useCallback((token: string): boolean => {
    // In a real implementation, this would validate against a server-side token
    // For now, we'll just check if it's a valid format
    return /^[a-f0-9]{64}$/.test(token);
  }, []);

  const clearValidationHistory = useCallback(() => {
    setValidationHistory([]);
  }, []);

  const getSecurityReport = useCallback(() => {
    const totalValidations = validationHistory.length;
    const threatsDetected = validationHistory.filter(entry => !entry.result.isValid).length;
    const highRiskThreats = validationHistory.filter(entry => entry.result.riskLevel === 'high').length;
    
    const threatTypes = validationHistory
      .filter(entry => !entry.result.isValid)
      .flatMap(entry => entry.result.threats)
      .reduce((acc, threat) => {
        acc[threat] = (acc[threat] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    return {
      totalValidations,
      threatsDetected,
      highRiskThreats,
      threatTypes,
      securityScore: totalValidations > 0 ? Math.round(((totalValidations - threatsDetected) / totalValidations) * 100) : 100,
    };
  }, [validationHistory]);

  return {
    // Core validation functions
    validateInput,
    validateForm,
    sanitizeHTML,
    
    // CSRF protection
    generateCSRFToken,
    validateCSRFToken,
    
    // Security reporting
    getSecurityReport,
    validationHistory,
    clearValidationHistory,
    
    // Configuration
    config: finalConfig,
  };
};
