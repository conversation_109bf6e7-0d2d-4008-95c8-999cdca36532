
import React from "react";
import { motion } from "framer-motion";
import { DynamicWidget } from "./DynamicWidget";
import { WidgetResponse, LayoutConfig, SectionResponse } from "@/types/dashboard-customization";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Database, BarChart3, Settings, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useUnifiedDashboardStore } from "@/stores/unified-dashboard-store";

interface DashboardGridProps {
  // Optional props for advanced usage
  widgets?: WidgetResponse[];
  layoutConfig?: LayoutConfig;
  onWidgetUpdate?: (id: string, updates: Partial<WidgetResponse>) => void;
  onWidgetRemove?: (id: string) => void;
  onWidgetPositionUpdate?: (id: string, position: any) => void;
  onWidgetCustomize?: (widget: WidgetResponse) => void;
  onWidgetInsights?: (widget: WidgetResponse) => void;
  onWidgetMove?: (widgetId: string, targetSectionId: string) => void;
  availableSections?: SectionResponse[];
  dataSourceAssignments?: any[];

  // Simple mode (uses store directly)
  useStore?: boolean;
}

export function DashboardGrid({
  widgets: propWidgets,
  layoutConfig,
  onWidgetUpdate,
  onWidgetRemove,
  onWidgetPositionUpdate,
  onWidgetCustomize,
  onWidgetInsights,
  onWidgetMove,
  availableSections,
  dataSourceAssignments = [],
  useStore = false
}: DashboardGridProps = {}) {
  const { toast } = useToast();

  // Store-based mode (legacy support)
  const { widgets: storeWidgets, removeWidget: removeStoreWidget, isLoading } = useUnifiedDashboardStore();

  // Determine which widgets to use
  const widgets = useStore ? storeWidgets : (propWidgets || []);
  const hasDataSources = dataSourceAssignments && dataSourceAssignments.length > 0;
  const hasWidgets = widgets && widgets.length > 0;

  // Check if widgets have proper data source configuration
  const widgetsWithoutDataSources = widgets.filter(w => !w.data_config?.dataSourceId);

  const handleRemoveWidget = (id: string) => {
    if (useStore) {
      removeStoreWidget(id);
      toast({
        title: "Widget removed",
        description: "The widget has been removed from your dashboard",
      });
    } else if (onWidgetRemove) {
      onWidgetRemove(id);
    }
  };

  // Show loading state for store mode
  if (useStore && isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="col-span-full text-center text-muted-foreground py-10">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-lg">Loading real data from database...</p>
          <p>Please wait while we fetch your dashboard data.</p>
        </div>
      </div>
    );
  }

  // Enhanced empty state for advanced mode
  if (!hasWidgets && !useStore) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <Card className="max-w-md w-full">
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            {hasDataSources ? (
              <>
                <BarChart3 className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Ready to Create Widgets</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  You have {dataSourceAssignments.length} data source{dataSourceAssignments.length !== 1 ? 's' : ''} configured.
                  Create widgets to visualize your data.
                </p>
                <div className="space-y-2">
                  <p className="text-xs text-muted-foreground">
                    Use the widget buttons above to add:
                  </p>
                  <div className="flex flex-wrap gap-1 justify-center text-xs text-muted-foreground">
                    <span>• Charts</span>
                    <span>• Tables</span>
                    <span>• KPIs</span>
                    <span>• Gauges</span>
                  </div>
                </div>
              </>
            ) : (
              <>
                <Database className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Data Sources</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Add data sources to your dashboard first, then create widgets to display your data.
                </p>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure Data Sources
                </Button>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Simple empty state for store mode
  if (!hasWidgets && useStore) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="col-span-full text-center text-muted-foreground py-10">
          <p className="text-lg">No dashboard data available.</p>
          <p>Unable to load real data from the database. Please check your connection and try refreshing.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Show helpful info if some widgets lack data sources (advanced mode only) */}
      {!useStore && widgetsWithoutDataSources.length > 0 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="flex items-center justify-between py-3">
            <div className="flex items-center space-x-3">
              <Settings className="h-5 w-5 text-amber-600" />
              <div>
                <p className="text-sm font-medium text-amber-800">
                  {widgetsWithoutDataSources.length} widget{widgetsWithoutDataSources.length !== 1 ? 's' : ''} need{widgetsWithoutDataSources.length === 1 ? 's' : ''} data source configuration
                </p>
                <p className="text-xs text-amber-600">
                  Click "Configure Widget" on widgets to connect them to your data sources
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Widget Grid */}
      <div
        className={useStore ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "grid gap-4"}
        style={!useStore ? {
          gridTemplateColumns: `repeat(${layoutConfig?.columns || 12}, 1fr)`,
          gridAutoRows: 'minmax(200px, auto)',
        } : undefined}
      >
        {widgets.map((widget) => (
          <motion.div
            key={widget.id}
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className={useStore ? "col-span-1" : undefined}
            style={useStore ? {
              gridColumn: `span ${widget.position?.w || 1}`,
              gridRow: `span ${widget.position?.h || 1}`,
            } : {
              gridColumn: `span ${widget.position_config?.w || 4}`,
              gridRow: `span ${widget.position_config?.h || 3}`,
            }}
          >
            <DynamicWidget
              widget={widget}
              onRemove={() => handleRemoveWidget(widget.id)}
              onUpdate={onWidgetUpdate ? (updates) => onWidgetUpdate(widget.id, updates) : undefined}
              onCustomize={onWidgetCustomize}
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
}
