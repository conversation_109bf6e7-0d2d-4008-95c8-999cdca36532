/**
 * Dashboard Mode Store
 * 
 * Comprehensive state management for dual-mode dashboard interface with
 * persistent user preferences and mode-aware functionality.
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  DashboardMode,
  ModeState,
  ModeTransition,
  UserModePreferences,
  ModeContextData,
  SimpleModeConfig,
  AdvancedModeConfig,
  DEFAULT_USER_MODE_PREFERENCES,
  DEFAULT_SIMPLE_MODE_CONFIG,
  DEFAULT_ADVANCED_MODE_CONFIG,
} from '@/types/dashboard-mode';

interface DashboardModeStore extends ModeState {
  // Configuration
  simple_mode_config: SimpleModeConfig;
  advanced_mode_config: AdvancedModeConfig;
  context_data: ModeContextData;
  
  // Actions
  set_mode: (mode: DashboardMode, trigger?: 'user_action' | 'system_suggestion') => void;
  toggle_mode: () => void;
  update_preferences: (preferences: Partial<UserModePreferences>) => void;
  update_simple_config: (config: Partial<SimpleModeConfig>) => void;
  update_advanced_config: (config: Partial<AdvancedModeConfig>) => void;
  update_context: (context: Partial<ModeContextData>) => void;
  
  // Utility actions
  get_current_config: () => SimpleModeConfig | AdvancedModeConfig;
  can_switch_mode: () => boolean;
  get_mode_capabilities: (mode: DashboardMode) => string[];
  reset_to_defaults: () => void;
  clear_mode_history: () => void;
}

const initialState: Omit<DashboardModeStore, 'set_mode' | 'toggle_mode' | 'update_preferences' | 'update_simple_config' | 'update_advanced_config' | 'update_context' | 'get_current_config' | 'can_switch_mode' | 'get_mode_capabilities' | 'reset_to_defaults' | 'clear_mode_history'> = {
  current_mode: 'simple',
  user_preferences: DEFAULT_USER_MODE_PREFERENCES,
  mode_history: [],
  is_switching: false,
  last_mode_change: null,
  mode_capabilities: {
    simple: [
      'ai_assistant',
      'template_gallery',
      'guided_workflows',
      'natural_language_creation',
      'smart_suggestions',
      'one_click_widgets',
      'conversational_interface',
    ],
    advanced: [
      'ribbon_toolbar',
      'technical_controls',
      'custom_queries',
      'raw_data_access',
      'performance_metrics',
      'code_editor',
      'custom_components',
      'api_access',
      'advanced_visualizations',
    ],
  },
  simple_mode_config: DEFAULT_SIMPLE_MODE_CONFIG,
  advanced_mode_config: DEFAULT_ADVANCED_MODE_CONFIG,
  context_data: {
    user_expertise_level: 'beginner',
    dashboard_complexity: 'basic',
    current_task: null,
    available_features: [],
    ai_assistant_active: true,
  },
};

export const useDashboardModeStore = create<DashboardModeStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      set_mode: (mode: DashboardMode, trigger = 'user_action') => {
        const current_state = get();
        
        if (current_state.current_mode === mode) {
          return; // No change needed
        }

        // Create mode transition record
        const transition: ModeTransition = {
          from_mode: current_state.current_mode,
          to_mode: mode,
          timestamp: new Date().toISOString(),
          trigger,
          context: {
            dashboard_complexity: current_state.context_data.dashboard_complexity,
            current_task: current_state.context_data.current_task,
          },
        };

        set({
          current_mode: mode,
          is_switching: true,
          mode_history: [...current_state.mode_history, transition],
          last_mode_change: new Date().toISOString(),
        });

        // Reset switching state after a brief delay
        setTimeout(() => {
          set({ is_switching: false });
        }, 500);
      },

      toggle_mode: () => {
        const current_state = get();
        const new_mode = current_state.current_mode === 'simple' ? 'advanced' : 'simple';
        current_state.set_mode(new_mode, 'user_action');
      },

      update_preferences: (preferences: Partial<UserModePreferences>) => {
        set((state) => ({
          user_preferences: {
            ...state.user_preferences,
            ...preferences,
          },
        }));
      },

      update_simple_config: (config: Partial<SimpleModeConfig>) => {
        set((state) => ({
          simple_mode_config: {
            ...state.simple_mode_config,
            ...config,
          },
        }));
      },

      update_advanced_config: (config: Partial<AdvancedModeConfig>) => {
        set((state) => ({
          advanced_mode_config: {
            ...state.advanced_mode_config,
            ...config,
          },
        }));
      },

      update_context: (context: Partial<ModeContextData>) => {
        set((state) => ({
          context_data: {
            ...state.context_data,
            ...context,
          },
        }));
      },

      get_current_config: () => {
        const state = get();
        return state.current_mode === 'simple' 
          ? state.simple_mode_config 
          : state.advanced_mode_config;
      },

      can_switch_mode: () => {
        const state = get();
        return !state.is_switching;
      },

      get_mode_capabilities: (mode: DashboardMode) => {
        const state = get();
        return state.mode_capabilities[mode];
      },

      reset_to_defaults: () => {
        set({
          ...initialState,
          mode_history: [], // Clear history on reset
        });
      },

      clear_mode_history: () => {
        set({ mode_history: [] });
      },
    }),
    {
      name: 'dashboard-mode-store',
      partialize: (state) => ({
        current_mode: state.current_mode,
        user_preferences: state.user_preferences,
        simple_mode_config: state.simple_mode_config,
        advanced_mode_config: state.advanced_mode_config,
        context_data: state.context_data,
        last_mode_change: state.last_mode_change,
      }),
    }
  )
);

// Utility hooks for easier access to specific parts of the store
export const useDashboardMode = () => {
  const store = useDashboardModeStore();
  return {
    current_mode: store.current_mode,
    is_switching: store.is_switching,
    set_mode: store.set_mode,
    toggle_mode: store.toggle_mode,
    can_switch: store.can_switch_mode(),
  };
};

export const useModePreferences = () => {
  const store = useDashboardModeStore();
  return {
    preferences: store.user_preferences,
    update_preferences: store.update_preferences,
  };
};

export const useModeConfig = () => {
  const store = useDashboardModeStore();
  return {
    current_config: store.get_current_config(),
    simple_config: store.simple_mode_config,
    advanced_config: store.advanced_mode_config,
    update_simple_config: store.update_simple_config,
    update_advanced_config: store.update_advanced_config,
  };
};

export const useModeContext = () => {
  const store = useDashboardModeStore();
  return {
    context: store.context_data,
    update_context: store.update_context,
    capabilities: store.get_mode_capabilities(store.current_mode),
  };
};
