import React, { useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Undo2,
  Redo2,
  MoreHorizontal,
  Copy,
  Trash2,
  Eye,
  EyeOff,
  Move,
  Settings,
  CheckSquare,
  Square
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

// Undo/Redo functionality
interface UndoRedoAction {
  id: string;
  type: 'create' | 'update' | 'delete' | 'move';
  description: string;
  undo: () => void;
  redo: () => void;
  timestamp: Date;
}

export const useUndoRedo = (maxHistorySize = 50) => {
  const [history, setHistory] = useState<UndoRedoAction[]>([]);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const { toast } = useToast();

  const addAction = useCallback((action: Omit<UndoRedoAction, 'id' | 'timestamp'>) => {
    const newAction: UndoRedoAction = {
      ...action,
      id: Date.now().toString(),
      timestamp: new Date()
    };

    setHistory(prev => {
      const newHistory = prev.slice(0, currentIndex + 1);
      newHistory.push(newAction);
      
      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory.shift();
        return newHistory;
      }
      
      return newHistory;
    });
    
    setCurrentIndex(prev => Math.min(prev + 1, maxHistorySize - 1));
  }, [currentIndex, maxHistorySize]);

  const undo = useCallback(() => {
    if (currentIndex >= 0) {
      const action = history[currentIndex];
      action.undo();
      setCurrentIndex(prev => prev - 1);
      
      toast({
        title: "Action Undone",
        description: `Undid: ${action.description}`,
      });
    }
  }, [currentIndex, history, toast]);

  const redo = useCallback(() => {
    if (currentIndex < history.length - 1) {
      const action = history[currentIndex + 1];
      action.redo();
      setCurrentIndex(prev => prev + 1);
      
      toast({
        title: "Action Redone",
        description: `Redid: ${action.description}`,
      });
    }
  }, [currentIndex, history, toast]);

  const canUndo = currentIndex >= 0;
  const canRedo = currentIndex < history.length - 1;

  return { addAction, undo, redo, canUndo, canRedo, history };
};

// Bulk selection and actions
export const useBulkSelection = <T extends { id: string }>(items: T[]) => {
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());

  const toggleSelection = useCallback((id: string) => {
    setSelectedIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  }, []);

  const selectAll = useCallback(() => {
    setSelectedIds(new Set(items.map(item => item.id)));
  }, [items]);

  const clearSelection = useCallback(() => {
    setSelectedIds(new Set());
  }, []);

  const toggleSelectAll = useCallback(() => {
    if (selectedIds.size === items.length) {
      clearSelection();
    } else {
      selectAll();
    }
  }, [selectedIds.size, items.length, selectAll, clearSelection]);

  const selectedItems = items.filter(item => selectedIds.has(item.id));
  const isAllSelected = selectedIds.size === items.length && items.length > 0;
  const isPartiallySelected = selectedIds.size > 0 && selectedIds.size < items.length;

  return {
    selectedIds,
    selectedItems,
    toggleSelection,
    selectAll,
    clearSelection,
    toggleSelectAll,
    isAllSelected,
    isPartiallySelected,
    hasSelection: selectedIds.size > 0
  };
};

// Undo/Redo Toolbar Component
export const UndoRedoToolbar: React.FC<{
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  className?: string;
}> = ({ canUndo, canRedo, onUndo, onRedo, className }) => (
  <TooltipProvider>
    <div className={cn("flex items-center space-x-1", className)}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={onUndo}
            disabled={!canUndo}
            className="h-8 w-8 p-0"
          >
            <Undo2 className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Undo (Ctrl+Z)</TooltipContent>
      </Tooltip>
      
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRedo}
            disabled={!canRedo}
            className="h-8 w-8 p-0"
          >
            <Redo2 className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Redo (Ctrl+Y)</TooltipContent>
      </Tooltip>
    </div>
  </TooltipProvider>
);

// Bulk Actions Toolbar
export const BulkActionsToolbar: React.FC<{
  selectedCount: number;
  onClearSelection: () => void;
  onBulkDelete?: () => void;
  onBulkHide?: () => void;
  onBulkShow?: () => void;
  onBulkDuplicate?: () => void;
  onBulkMove?: () => void;
  className?: string;
}> = ({
  selectedCount,
  onClearSelection,
  onBulkDelete,
  onBulkHide,
  onBulkShow,
  onBulkDuplicate,
  onBulkMove,
  className
}) => (
  <AnimatePresence>
    {selectedCount > 0 && (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={cn(
          "flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",
          className
        )}
      >
        <div className="flex items-center space-x-3">
          <Badge variant="secondary" className="bg-blue-100 dark:bg-blue-800">
            {selectedCount} selected
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
            className="text-blue-600 dark:text-blue-400"
          >
            Clear selection
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          {onBulkDuplicate && (
            <Button variant="outline" size="sm" onClick={onBulkDuplicate}>
              <Copy className="h-4 w-4 mr-2" />
              Duplicate
            </Button>
          )}
          
          {onBulkMove && (
            <Button variant="outline" size="sm" onClick={onBulkMove}>
              <Move className="h-4 w-4 mr-2" />
              Move
            </Button>
          )}
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4 mr-2" />
                More
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {onBulkShow && (
                <DropdownMenuItem onClick={onBulkShow}>
                  <Eye className="h-4 w-4 mr-2" />
                  Show All
                </DropdownMenuItem>
              )}
              {onBulkHide && (
                <DropdownMenuItem onClick={onBulkHide}>
                  <EyeOff className="h-4 w-4 mr-2" />
                  Hide All
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onBulkDelete && (
                <DropdownMenuItem 
                  onClick={onBulkDelete}
                  className="text-red-600 dark:text-red-400"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete All
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </motion.div>
    )}
  </AnimatePresence>
);

// Selection Checkbox Component
export const SelectionCheckbox: React.FC<{
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  indeterminate?: boolean;
  className?: string;
}> = ({ checked, onCheckedChange, indeterminate, className }) => (
  <div className={cn("flex items-center", className)}>
    <Checkbox
      checked={indeterminate ? 'indeterminate' : checked}
      onCheckedChange={onCheckedChange}
      className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
    />
  </div>
);

// Context Menu Component
export const ContextMenu: React.FC<{
  children: React.ReactNode;
  items: Array<{
    label: string;
    icon?: React.ComponentType<{ className?: string }>;
    onClick: () => void;
    disabled?: boolean;
    destructive?: boolean;
    separator?: boolean;
  }>;
}> = ({ children, items }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const menuRef = useRef<HTMLDivElement>(null);

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setPosition({ x: e.clientX, y: e.clientY });
    setIsOpen(true);
  }, []);

  const handleClickOutside = useCallback((e: MouseEvent) => {
    if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
      setIsOpen(false);
    }
  }, []);

  React.useEffect(() => {
    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isOpen, handleClickOutside]);

  return (
    <>
      <div onContextMenu={handleContextMenu}>
        {children}
      </div>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={menuRef}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.1 }}
            className="fixed z-50 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-lg py-1 min-w-[160px]"
            style={{
              left: position.x,
              top: position.y,
            }}
          >
            {items.map((item, index) => (
              <React.Fragment key={index}>
                {item.separator && (
                  <div className="h-px bg-slate-200 dark:bg-slate-700 my-1" />
                )}
                <button
                  onClick={() => {
                    item.onClick();
                    setIsOpen(false);
                  }}
                  disabled={item.disabled}
                  className={cn(
                    "w-full px-3 py-2 text-left text-sm hover:bg-slate-100 dark:hover:bg-slate-700 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",
                    item.destructive && "text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                  )}
                >
                  {item.icon && <item.icon className="h-4 w-4" />}
                  <span>{item.label}</span>
                </button>
              </React.Fragment>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
