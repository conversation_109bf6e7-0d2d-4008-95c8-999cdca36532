"""
Component registry for the Datagenius agent system.

This module provides a registry for agent components, allowing components to be
registered, retrieved, and instantiated by name.
"""

import logging
from typing import Dict, Type, Optional, List, Any
from .base import AgentComponent

logger = logging.getLogger(__name__)


class ComponentRegistry:
    """Registry for agent components."""

    _components: Dict[str, Type[AgentComponent]] = {}

    @classmethod
    def register(cls, name: str, component_class: Type[AgentComponent]) -> None:
        """
        Register a component class.

        Args:
            name: Name of the component
            component_class: Component class to register
        """
        cls._components[name] = component_class
        logger.info(f"Registered component class {component_class.__name__} with name '{name}'")

    @classmethod
    def get_component_class(cls, name: str) -> Optional[Type[AgentComponent]]:
        """
        Get a component class by name.

        Args:
            name: Name of the component

        Returns:
            Component class if found, None otherwise
        """
        component_class = cls._components.get(name)
        if component_class is None:
            logger.warning(f"No component class found for name '{name}'")
        return component_class

    @classmethod
    def list_registered_components(cls) -> List[str]:
        """
        List all registered component names.

        Returns:
            List of registered component names
        """
        return list(cls._components.keys())

    @classmethod
    async def create_component_instance(cls, name: str, config: Dict[str, Any] = None) -> Optional[AgentComponent]:
        """
        Create an instance of a component by name and initialize it with configuration.

        Args:
            name: Name of the component
            config: Configuration dictionary for the component

        Returns:
            Initialized component instance if found, None otherwise
        """
        component_class = cls.get_component_class(name)
        if component_class is None:
            return None

        if config is None:
            config = {}

        try:
            component = component_class()
            await component.initialize(config)
            return component
        except Exception as e:
            logger.error(f"Error initializing component '{name}': {e}")
            return None
