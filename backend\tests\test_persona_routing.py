#!/usr/bin/env python3
"""
Test script to verify persona routing functionality.
This script tests that once a persona is selected, all subsequent messages go to that persona.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.orchestration.routing_component import RoutingComponent

async def test_persona_routing():
    """Test the persona routing logic."""
    print("🧪 Testing Persona Routing Logic")
    print("=" * 50)
    
    routing_component = RoutingComponent()
    
    # Test 1: No current persona - should route to concierge
    print("\n📝 Test 1: No current persona")
    context1 = {}
    result1 = await routing_component.determine_target_agent("Hello, I need help", context1)
    print(f"Result: {result1}")
    assert result1 == "concierge-agent", f"Expected concierge-agent, got {result1}"
    print("✅ PASS: Routes to concierge when no persona is set")
    
    # Test 2: Current persona is concierge - should route to concierge
    print("\n📝 Test 2: Current persona is concierge")
    context2 = {"current_persona": "concierge-agent"}
    result2 = await routing_component.determine_target_agent("I want to analyze data", context2)
    print(f"Result: {result2}")
    assert result2 == "concierge-agent", f"Expected concierge-agent, got {result2}"
    print("✅ PASS: Routes to concierge when concierge is current persona")
    
    # Test 3: Current persona is marketing agent - should stay with marketing agent
    print("\n📝 Test 3: Current persona is marketing agent")
    context3 = {"current_persona": "composable-marketing-ai"}
    result3 = await routing_component.determine_target_agent("Can you help me with data analysis?", context3)
    print(f"Result: {result3}")
    assert result3 == "composable-marketing-ai", f"Expected composable-marketing-ai, got {result3}"
    print("✅ PASS: Routes to marketing agent when marketing agent is current persona")
    
    # Test 4: Current persona is analysis agent - should stay with analysis agent
    print("\n📝 Test 4: Current persona is analysis agent")
    context4 = {"current_persona": "composable-analysis-ai"}
    result4 = await routing_component.determine_target_agent("Create a marketing strategy", context4)
    print(f"Result: {result4}")
    assert result4 == "composable-analysis-ai", f"Expected composable-analysis-ai, got {result4}"
    print("✅ PASS: Routes to analysis agent when analysis agent is current persona")
    
    # Test 5: Explicit routing override
    print("\n📝 Test 5: Explicit routing override")
    context5 = {
        "current_persona": "composable-marketing-ai",
        "route_to_persona": "composable-analysis-ai"
    }
    result5 = await routing_component.determine_target_agent("Any message", context5)
    print(f"Result: {result5}")
    assert result5 == "composable-analysis-ai", f"Expected composable-analysis-ai, got {result5}"
    print("✅ PASS: Explicit routing override works correctly")
    
    print("\n🎉 All tests passed! Persona routing is working correctly.")
    print("=" * 50)
    print("✅ CONCLUSION: Once a persona is selected, all messages will go to that persona")
    print("✅ CONCLUSION: Persona switching only happens through explicit UI actions")

if __name__ == "__main__":
    asyncio.run(test_persona_routing())
