#!/usr/bin/env python3
"""
Test script for adaptive chunking functionality.

This script tests the adaptive chunking system with sample documents
and provides performance comparisons.
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path
from typing import Dict, Any, List

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.utils.adaptive_chunking import AdaptiveChunker, ContentType
from agents.utils.chunking_performance_manager import ChunkingPerformanceManager
from agents.utils.vector_service import VectorService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdaptiveChunkingTester:
    """Test suite for adaptive chunking functionality."""
    
    def __init__(self):
        """Initialize the tester."""
        self.adaptive_chunker = AdaptiveChunker()
        self.performance_manager = ChunkingPerformanceManager()
        self.vector_service = VectorService()
        self.test_results = []
    
    def create_test_documents(self) -> Dict[str, str]:
        """Create test documents for different content types."""
        return {
            "technical_doc.md": """
# API Documentation

## Authentication
The API uses OAuth 2.0 for authentication. You need to obtain an access token
before making requests to protected endpoints.

### Getting an Access Token
```bash
curl -X POST https://api.example.com/oauth/token \\
  -H "Content-Type: application/json" \\
  -d '{
    "grant_type": "client_credentials",
    "client_id": "your_client_id",
    "client_secret": "your_client_secret"
  }'
```

## Endpoints

### GET /users
Returns a list of users in the system.

**Parameters:**
- `limit` (integer): Maximum number of users to return (default: 10)
- `offset` (integer): Number of users to skip (default: 0)
- `filter` (string): Filter users by name or email

**Response:**
```json
{
  "users": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "created_at": "2023-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "limit": 10,
  "offset": 0
}
```

### POST /users
Creates a new user in the system.

**Request Body:**
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "created_at": "2023-01-02T00:00:00Z"
}
```

## Error Handling
The API returns standard HTTP status codes and error messages in JSON format.

**Error Response Format:**
```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "The request is invalid",
    "details": "Missing required field: email"
  }
}
```
            """,
            
            "research_paper.txt": """
The Impact of Machine Learning on Healthcare Diagnostics: A Comprehensive Review

Abstract

This systematic review examines the current state and future potential of machine
learning applications in healthcare diagnostics. We analyzed 247 peer-reviewed
studies published between 2018 and 2023, focusing on diagnostic accuracy,
implementation challenges, and clinical outcomes. Our findings indicate that
machine learning algorithms, particularly deep learning models, demonstrate
significant promise in improving diagnostic accuracy across multiple medical
specialties, with some applications achieving performance comparable to or
exceeding that of experienced clinicians.

Introduction

Healthcare diagnostics has undergone significant transformation with the advent
of artificial intelligence and machine learning technologies. Traditional
diagnostic methods, while effective, often require extensive human expertise
and can be subject to variability in interpretation. Machine learning algorithms
offer the potential to standardize diagnostic processes, reduce human error,
and improve accessibility to quality healthcare services.

The integration of machine learning in healthcare diagnostics presents both
opportunities and challenges. While these technologies promise improved accuracy
and efficiency, concerns regarding data privacy, algorithm bias, regulatory
approval, and clinical integration remain significant barriers to widespread
adoption.

Methodology

We conducted a systematic literature review following PRISMA guidelines.
Our search strategy included multiple databases: PubMed, IEEE Xplore,
ACM Digital Library, and Google Scholar. The search terms included
combinations of "machine learning," "artificial intelligence," "healthcare,"
"diagnostics," "medical imaging," and related terms.

Inclusion criteria:
- Peer-reviewed articles published in English
- Studies involving machine learning applications in medical diagnostics
- Clinical validation or real-world implementation data
- Publication date between January 2018 and December 2023

Exclusion criteria:
- Review articles without original research
- Studies without clinical validation
- Non-English publications
- Conference abstracts without full papers

Results

Our analysis revealed several key findings across different medical specialties.
In radiology, convolutional neural networks achieved diagnostic accuracy
comparable to or exceeding that of experienced radiologists in specific tasks
such as mammography screening, chest X-ray interpretation, and MRI analysis.

Pathology applications showed particularly promising results, with deep learning
models demonstrating high accuracy in histopathological image analysis for
cancer detection. The sensitivity and specificity rates often exceeded 90%
in controlled studies, with some applications achieving over 95% accuracy
in specific cancer types.

Cardiology applications focused primarily on ECG interpretation and
echocardiogram analysis. Machine learning models showed excellent performance
in detecting arrhythmias, with some studies reporting accuracy rates above 98%
for common arrhythmia types.

Discussion

The results demonstrate the significant potential of machine learning in
healthcare diagnostics. However, several challenges must be addressed before
widespread clinical adoption can occur. Data quality and standardization
remain critical issues, as machine learning models are highly dependent on
the quality and representativeness of training data.

Regulatory approval processes present another significant challenge. Current
regulatory frameworks were not designed for adaptive algorithms that can
learn and evolve over time. New regulatory approaches may be needed to
accommodate the unique characteristics of machine learning systems.

Conclusion

Machine learning technologies show tremendous promise for improving healthcare
diagnostics across multiple medical specialties. While significant challenges
remain, the potential benefits in terms of improved accuracy, reduced costs,
and increased accessibility make continued research and development in this
area essential. Future work should focus on addressing regulatory challenges,
improving data standardization, and developing robust validation frameworks
for clinical implementation.
            """,
            
            "legal_contract.txt": """
SOFTWARE LICENSE AGREEMENT

This Software License Agreement ("Agreement") is entered into as of the date
of acceptance by the end user ("Effective Date") between DataGenius Inc.,
a Delaware corporation ("Company"), and the individual or entity accepting
this Agreement ("Licensee").

WHEREAS, Company has developed certain proprietary software and related
documentation; and

WHEREAS, Licensee desires to obtain a license to use such software subject
to the terms and conditions set forth herein;

NOW, THEREFORE, in consideration of the mutual covenants and agreements
contained herein, and for other good and valuable consideration, the receipt
and sufficiency of which are hereby acknowledged, the parties agree as follows:

1. DEFINITIONS

1.1 "Software" means the computer software programs and related documentation
provided by Company to Licensee under this Agreement, including any updates,
modifications, or enhancements thereto.

1.2 "Documentation" means the user manuals, technical specifications, and
other written materials provided by Company relating to the Software.

1.3 "Confidential Information" means any and all non-public, proprietary,
or confidential information disclosed by one party to the other party.

2. GRANT OF LICENSE

2.1 Subject to the terms and conditions of this Agreement, Company hereby
grants to Licensee a non-exclusive, non-transferable, revocable license
to use the Software solely for Licensee's internal business purposes.

2.2 The license granted herein does not include the right to sublicense,
distribute, or otherwise transfer the Software to any third party without
the prior written consent of Company.

3. RESTRICTIONS

3.1 Licensee shall not, and shall not permit any third party to:
(a) reverse engineer, decompile, or disassemble the Software;
(b) modify, adapt, or create derivative works based on the Software;
(c) remove or alter any proprietary notices or labels on the Software;
(d) use the Software for any unlawful purpose or in violation of any
applicable laws or regulations.

4. INTELLECTUAL PROPERTY

4.1 Company retains all right, title, and interest in and to the Software,
including all intellectual property rights therein. No title to or ownership
of the Software is transferred to Licensee.

4.2 Licensee acknowledges that the Software contains valuable trade secrets
and proprietary information of Company and agrees to maintain the
confidentiality of such information.

5. WARRANTY DISCLAIMER

THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND. COMPANY
DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED
TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
PURPOSE, AND NON-INFRINGEMENT.

6. LIMITATION OF LIABILITY

IN NO EVENT SHALL COMPANY BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL,
CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING BUT NOT LIMITED TO LOSS OF
PROFITS, DATA, OR USE, ARISING OUT OF OR RELATING TO THIS AGREEMENT OR
THE USE OF THE SOFTWARE, REGARDLESS OF THE THEORY OF LIABILITY AND WHETHER
OR NOT COMPANY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

7. TERMINATION

7.1 This Agreement shall commence on the Effective Date and shall continue
until terminated in accordance with the terms hereof.

7.2 Either party may terminate this Agreement at any time upon thirty (30)
days' written notice to the other party.

7.3 Upon termination, Licensee shall immediately cease all use of the
Software and return or destroy all copies of the Software in its possession.

8. GOVERNING LAW

This Agreement shall be governed by and construed in accordance with the
laws of the State of Delaware, without regard to its conflict of laws
principles.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the
Effective Date.

DATAGENIUS INC.

By: _________________________
Name: [Name]
Title: Chief Executive Officer
Date: _______________________

LICENSEE:

By: _________________________
Name: [Name]
Title: [Title]
Date: _______________________
            """
        }
    
    async def test_content_type_detection(self) -> Dict[str, Any]:
        """Test content type detection accuracy."""
        logger.info("Testing content type detection...")
        
        test_docs = self.create_test_documents()
        results = {}
        
        for filename, content in test_docs.items():
            file_extension = Path(filename).suffix
            detected_type = self.adaptive_chunker.analyze_content_type(content, file_extension)
            
            # Expected types based on content
            expected_types = {
                "technical_doc.md": ContentType.TECHNICAL_DOCUMENT,
                "research_paper.txt": ContentType.RESEARCH_PAPER,
                "legal_contract.txt": ContentType.LEGAL_DOCUMENT
            }
            
            expected = expected_types.get(filename, ContentType.GENERAL_TEXT)
            correct = detected_type == expected
            
            results[filename] = {
                "detected": detected_type.value,
                "expected": expected.value,
                "correct": correct,
                "content_length": len(content)
            }
            
            logger.info(f"{filename}: {detected_type.value} ({'✓' if correct else '✗'})")
        
        accuracy = sum(1 for r in results.values() if r["correct"]) / len(results)
        logger.info(f"Content type detection accuracy: {accuracy:.1%}")
        
        return {
            "accuracy": accuracy,
            "results": results
        }
    
    async def test_chunking_strategies(self) -> Dict[str, Any]:
        """Test different chunking strategies."""
        logger.info("Testing chunking strategies...")
        
        test_docs = self.create_test_documents()
        results = {}
        
        for filename, content in test_docs.items():
            logger.info(f"Testing chunking for {filename}...")
            
            # Test adaptive chunking
            start_time = time.time()
            adaptive_chunks = self.adaptive_chunker.chunk_document(content, Path(filename).suffix)
            adaptive_time = (time.time() - start_time) * 1000
            
            # Extract chunks and metadata
            chunks = [chunk for chunk, _ in adaptive_chunks]
            metadata_list = [metadata for _, metadata in adaptive_chunks]
            
            # Calculate metrics
            total_chars = sum(len(chunk) for chunk in chunks)
            avg_chunk_size = total_chars / len(chunks) if chunks else 0
            
            # Calculate semantic coherence
            avg_semantic_score = 0
            if metadata_list:
                avg_semantic_score = sum(meta.semantic_score for meta in metadata_list) / len(metadata_list)
            
            results[filename] = {
                "chunk_count": len(chunks),
                "total_characters": total_chars,
                "avg_chunk_size": avg_chunk_size,
                "processing_time_ms": adaptive_time,
                "semantic_score": avg_semantic_score,
                "content_type": metadata_list[0].content_type.value if metadata_list else "unknown",
                "chunks_preview": [chunk[:100] + "..." for chunk in chunks[:3]]
            }
        
        return results
    
    async def test_performance_profiles(self) -> Dict[str, Any]:
        """Test different performance profiles."""
        logger.info("Testing performance profiles...")
        
        profiles = ["speed_optimized", "quality_optimized", "balanced", "memory_optimized"]
        test_content = list(self.create_test_documents().values())[0]  # Use first document
        
        results = {}
        
        for profile in profiles:
            logger.info(f"Testing profile: {profile}")
            
            # Set performance profile
            self.performance_manager.performance_profile = profile
            
            # Get optimal config
            config = self.performance_manager.get_optimal_config("technical", len(test_content))
            
            start_time = time.time()
            
            # Test chunking with this profile
            chunks_with_metadata = self.adaptive_chunker.chunk_document(test_content)
            chunks = [chunk for chunk, _ in chunks_with_metadata]
            
            processing_time = (time.time() - start_time) * 1000
            
            results[profile] = {
                "chunk_count": len(chunks),
                "processing_time_ms": processing_time,
                "avg_chunk_size": sum(len(chunk) for chunk in chunks) / len(chunks) if chunks else 0,
                "config": config.get("chunking_strategy", {})
            }
        
        return results
    
    async def test_embedding_integration(self) -> Dict[str, Any]:
        """Test integration with vector service embedding."""
        logger.info("Testing embedding integration...")
        
        # Create a temporary test file
        test_content = list(self.create_test_documents().values())[0]
        test_file = "temp_test_doc.txt"
        
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            # Test traditional chunking
            start_time = time.time()
            vector_store_id_traditional, file_info_traditional = await self.vector_service.embed_document(
                file_path=test_file,
                chunk_size=1000,
                chunk_overlap=200,
                use_adaptive_chunking=False
            )
            traditional_time = (time.time() - start_time) * 1000

            # Test adaptive chunking
            start_time = time.time()
            vector_store_id_adaptive, file_info_adaptive = await self.vector_service.embed_document(
                file_path=test_file,
                use_adaptive_chunking=True
            )
            adaptive_time = (time.time() - start_time) * 1000
            
            return {
                "traditional": {
                    "processing_time_ms": traditional_time,
                    "chunks": file_info_traditional.get("total_chunks", 0),
                    "strategy": file_info_traditional.get("chunking_strategy", "traditional")
                },
                "adaptive": {
                    "processing_time_ms": adaptive_time,
                    "chunks": file_info_adaptive.get("total_chunks", 0),
                    "strategy": file_info_adaptive.get("chunking_strategy", "adaptive"),
                    "content_type": file_info_adaptive.get("content_type", "unknown")
                },
                "improvement": {
                    "time_reduction_percent": ((traditional_time - adaptive_time) / traditional_time * 100) if traditional_time > 0 else 0,
                    "chunk_difference": file_info_adaptive.get("total_chunks", 0) - file_info_traditional.get("total_chunks", 0)
                }
            }
            
        finally:
            # Clean up test file
            if os.path.exists(test_file):
                os.remove(test_file)
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive test suite."""
        logger.info("Starting comprehensive adaptive chunking test...")
        
        test_results = {}
        
        try:
            # Test content type detection
            test_results["content_type_detection"] = await self.test_content_type_detection()
            
            # Test chunking strategies
            test_results["chunking_strategies"] = await self.test_chunking_strategies()
            
            # Test performance profiles
            test_results["performance_profiles"] = await self.test_performance_profiles()
            
            # Test embedding integration
            test_results["embedding_integration"] = await self.test_embedding_integration()
            
            # Generate summary
            test_results["summary"] = self._generate_test_summary(test_results)
            
        except Exception as e:
            logger.error(f"Error during testing: {e}")
            test_results["error"] = str(e)
        
        return test_results
    
    def _generate_test_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test summary."""
        summary = {
            "overall_status": "PASS",
            "tests_run": 0,
            "tests_passed": 0,
            "key_findings": []
        }
        
        # Content type detection
        if "content_type_detection" in results:
            accuracy = results["content_type_detection"]["accuracy"]
            summary["tests_run"] += 1
            if accuracy >= 0.8:  # 80% accuracy threshold
                summary["tests_passed"] += 1
                summary["key_findings"].append(f"Content type detection: {accuracy:.1%} accuracy")
            else:
                summary["overall_status"] = "FAIL"
                summary["key_findings"].append(f"Content type detection failed: {accuracy:.1%} accuracy")
        
        # Chunking strategies
        if "chunking_strategies" in results:
            summary["tests_run"] += 1
            summary["tests_passed"] += 1
            avg_processing_time = sum(r["processing_time_ms"] for r in results["chunking_strategies"].values()) / len(results["chunking_strategies"])
            summary["key_findings"].append(f"Average chunking time: {avg_processing_time:.1f}ms")
        
        # Performance profiles
        if "performance_profiles" in results:
            summary["tests_run"] += 1
            summary["tests_passed"] += 1
            fastest_profile = min(results["performance_profiles"].items(), key=lambda x: x[1]["processing_time_ms"])
            summary["key_findings"].append(f"Fastest profile: {fastest_profile[0]} ({fastest_profile[1]['processing_time_ms']:.1f}ms)")
        
        # Embedding integration
        if "embedding_integration" in results:
            summary["tests_run"] += 1
            improvement = results["embedding_integration"]["improvement"]["time_reduction_percent"]
            if improvement > 0:
                summary["tests_passed"] += 1
                summary["key_findings"].append(f"Adaptive chunking {improvement:.1f}% faster than traditional")
            else:
                summary["key_findings"].append(f"Adaptive chunking {abs(improvement):.1f}% slower than traditional")
        
        return summary
    
    def save_test_results(self, results: Dict[str, Any], filename: str = None):
        """Save test results to file."""
        if filename is None:
            from datetime import datetime
            filename = f"adaptive_chunking_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            import json
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"Test results saved to: {filename}")
        except Exception as e:
            logger.error(f"Error saving test results: {e}")

async def main():
    """Main test execution."""
    tester = AdaptiveChunkingTester()
    
    try:
        results = await tester.run_comprehensive_test()
        
        # Print summary
        print("\n" + "="*60)
        print("ADAPTIVE CHUNKING TEST RESULTS")
        print("="*60)
        
        if "summary" in results:
            summary = results["summary"]
            print(f"Overall Status: {summary['overall_status']}")
            print(f"Tests Run: {summary['tests_run']}")
            print(f"Tests Passed: {summary['tests_passed']}")
            print("\nKey Findings:")
            for finding in summary["key_findings"]:
                print(f"  • {finding}")
        
        # Save detailed results
        tester.save_test_results(results)
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
