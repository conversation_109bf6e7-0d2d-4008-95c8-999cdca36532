"""
Statistical analysis MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for performing statistical analysis
using PandasAI.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, Optional

# PandasAI imports
import pandasai as pai

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import <PERSON>rrorHandler
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class StatisticalAnalysisTool(BaseMCPTool):
    """Tool for performing statistical analysis using PandasAI."""

    def __init__(self):
        """Initialize the statistical analysis tool."""
        super().__init__(
            name="statistical_analysis",
            description="Performs advanced statistical analysis including correlation, t-tests, ANOVA, chi-square tests, machine learning, time series analysis, and hypothesis testing using PandasAI and statistical libraries.",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV, Excel, JSON supported)."
                    },
                    "analysis_description": {
                        "type": "string",
                        "description": "Natural language description of the statistical analysis required (e.g., 'correlation between Sales and MarketingSpend', 't-test for Score between GroupA and GroupB', 'ANOVA for Rating across different Regions', 'time series analysis of monthly sales', 'machine learning model to predict customer churn')."
                    },
                    "analysis_type": {
                        "type": "string",
                        "enum": [
                            "descriptive", "correlation", "t_test", "anova", "chi_square",
                            "regression", "time_series", "machine_learning", "hypothesis_test",
                            "clustering", "classification", "forecasting", "anomaly_detection"
                        ],
                        "description": "Specific type of statistical analysis to perform"
                    },
                    "target_variable": {
                        "type": "string",
                        "description": "Target variable for supervised learning tasks"
                    },
                    "feature_variables": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of feature variables for analysis"
                    },
                    "test_parameters": {
                        "type": "object",
                        "description": "Additional parameters for statistical tests (e.g., alpha level, test type)"
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for the LLM provider."
                    },
                    "provider": {
                        "type": "string",
                        "description": "LLM provider to use (e.g., openai, groq, anthropic).",
                        "default": "openai"
                    },
                    "model": {
                        "type": "string",
                        "description": "Model name to use for the analysis."
                    }
                },
                "required": ["file_path", "analysis_description", "api_key"]
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # No additional initialization needed
        pass

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the statistical analysis tool with agent-aware capabilities.

        Args:
            arguments: Arguments for tool execution (following the inputSchema)

        Returns:
            Tool execution results in MCP format
        """
        file_path = arguments["file_path"]
        analysis_description = arguments["analysis_description"]
        api_key = arguments["api_key"]
        provider = arguments.get("provider", "openai")
        model = arguments.get("model")

        # Enhanced analysis parameters
        analysis_type = arguments.get("analysis_type", "descriptive")
        target_variable = arguments.get("target_variable")
        feature_variables = arguments.get("feature_variables", [])
        test_parameters = arguments.get("test_parameters", {})

        # Agent context for dynamic identity detection
        user_context = arguments.get("context", {})
        agent_id = arguments.get("persona_id") or arguments.get("agent_id")

        # Detect agent identity for personalized statistical analysis
        agent_identity = await detect_agent_identity(
            agent_id=agent_id,
            context=user_context,
            intent_type="statistical_analysis"
        )

        logger.info(f"Detected agent identity: {agent_identity} for statistical analysis")
        logger.info(f"PandasAI statistical analysis requested for {file_path} with description: {analysis_description}")

        # Check if we have a cached response
        cache_key = f"{file_path}:{analysis_description}:{provider}:{model}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            logger.info(f"Using cached result for statistical analysis: {cache_key}")
            return cached_result

        # Input validation
        if not provider or not api_key:
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": "Error: LLM Provider and API Key must be provided."
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "analysis_description": analysis_description,
                    "status": "error",
                    "error_type": "config_error"
                }
            }

        # Load the data
        try:
            if file_path.lower().endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.lower().endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.lower().endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ]
                }

            if df.empty:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"The dataframe loaded from {file_path} is empty."
                        }
                    ]
                }
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error loading data from {file_path}: {str(e)}"
                    }
                ]
            }

        # Perform the analysis
        try:
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)

            # Create agent with the dataframe
            if not self.pandasai.create_agent(df=df, model=model):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Error creating PandasAI Agent"
                        }
                    ]
                }

            # Run the analysis using PandasAI wrapper with agent-aware query
            enhanced_query = await self._create_agent_aware_analysis_query(
                analysis_description, agent_identity
            )
            result = self.pandasai.chat(enhanced_query)

            # Process the result
            if isinstance(result, (pd.DataFrame, pd.Series)):
                # Statistical tests might return dataframes (e.g., ANOVA table)
                result_df = result if isinstance(result, pd.DataFrame) else result.to_frame()
                rows = len(result_df)
                results_metadata = {"status": "success", "result_type": "dataframe", "rows_returned": rows}
                results_text = f"Statistical analysis results (table format):\n\n{result_df.to_string()}"
            elif isinstance(result, (str, int, float, bool)):
                # Simple results like p-value or correlation coefficient
                results_text = f"Statistical analysis result: {result}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__), "value": result}
            elif isinstance(result, dict):
                # Some tests might return dictionaries
                results_text = f"Statistical analysis results:\n\n{result}"
                results_metadata = {"status": "success", "result_type": "dict", "value": result}
            else:
                # Handle other types of results
                results_text = f"Statistical analysis completed. Result type: {type(result).__name__}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__)}

            response = {
                "content": [
                    {
                        "type": "text",
                        "text": results_text
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "analysis_description": analysis_description,
                    "implementation": "pandasai",
                    "agent_identity": agent_identity,
                    "agent_aware": True,
                    **results_metadata
                }
            }

            # Cache the response
            self.cache.set(cache_key, response)
            return response

        except Exception as e:
            error_handler = ErrorHandler()
            error_info = error_handler.handle_error(e, context={
                "operation": "statistical_analysis",
                "file_path": file_path,
                "analysis_description": analysis_description
            })

            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": error_info["message"]
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "analysis_description": analysis_description,
                    "error_type": error_info["error_type"],
                    "details": error_info["details"]
                }
            }

    async def _create_agent_aware_analysis_query(
        self,
        analysis_description: str,
        agent_identity: str
    ) -> str:
        """
        Create an agent-aware statistical analysis query.

        Args:
            analysis_description: Original analysis description
            agent_identity: Agent identity for customization

        Returns:
            Enhanced query with agent-specific context
        """
        try:
            # Get agent system prompt to extract analysis preferences
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract analysis style preferences from system prompt
            analysis_style = await self._extract_analysis_style_from_prompt(system_prompt, agent_identity)

            # Create enhanced query based on agent style
            enhanced_query = self._apply_agent_style_to_analysis_query(
                analysis_description, analysis_style, agent_identity
            )

            logger.info(f"Enhanced statistical analysis query for {agent_identity}: {enhanced_query[:100]}...")
            return enhanced_query

        except Exception as e:
            logger.warning(f"Failed to enhance analysis query with agent style: {e}")
            # Return basic query if enhancement fails
            return f"Perform the following statistical analysis on the dataframe: {analysis_description}. Provide the key results, such as p-values, test statistics, or correlation coefficients."

    async def _extract_analysis_style_from_prompt(self, system_prompt: str, agent_identity: str) -> Dict[str, Any]:
        """Extract analysis style preferences from agent system prompt."""
        style_preferences = {
            "detail_level": "moderate",
            "interpretation_style": "technical",
            "focus_areas": [],
            "output_format": "comprehensive"
        }

        if not system_prompt:
            return self._get_default_analysis_style_for_agent(agent_identity)

        # Look for analysis-related patterns in the system prompt
        import re

        # Check for detail level preferences
        if re.search(r"detailed|comprehensive|thorough", system_prompt, re.IGNORECASE):
            style_preferences["detail_level"] = "detailed"
        elif re.search(r"concise|brief|summary", system_prompt, re.IGNORECASE):
            style_preferences["detail_level"] = "concise"

        # Check for interpretation style
        if re.search(r"business|practical|actionable", system_prompt, re.IGNORECASE):
            style_preferences["interpretation_style"] = "business"
        elif re.search(r"educational|explain|teaching", system_prompt, re.IGNORECASE):
            style_preferences["interpretation_style"] = "educational"
        elif re.search(r"technical|statistical|advanced", system_prompt, re.IGNORECASE):
            style_preferences["interpretation_style"] = "technical"

        # Extract focus areas based on capabilities
        focus_areas = []
        if re.search(r"marketing|campaign|strategy", system_prompt, re.IGNORECASE):
            focus_areas.append("business_implications")
        if re.search(r"insights|patterns|trends", system_prompt, re.IGNORECASE):
            focus_areas.append("pattern_discovery")
        if re.search(r"classification|categorization", system_prompt, re.IGNORECASE):
            focus_areas.append("categorical_analysis")

        style_preferences["focus_areas"] = focus_areas

        return style_preferences

    def _get_default_analysis_style_for_agent(self, agent_identity: str) -> Dict[str, Any]:
        """Get default analysis style for specific agent types."""
        default_styles = {
            "analyst": {
                "detail_level": "detailed",
                "interpretation_style": "technical",
                "focus_areas": ["pattern_discovery", "statistical_significance"],
                "output_format": "comprehensive"
            },
            "marketer": {
                "detail_level": "moderate",
                "interpretation_style": "business",
                "focus_areas": ["business_implications", "actionable_insights"],
                "output_format": "business_focused"
            },
            "classifier": {
                "detail_level": "structured",
                "interpretation_style": "technical",
                "focus_areas": ["categorical_analysis", "pattern_discovery"],
                "output_format": "organized"
            },
            "concierge": {
                "detail_level": "accessible",
                "interpretation_style": "educational",
                "focus_areas": ["key_insights"],
                "output_format": "user_friendly"
            }
        }

        return default_styles.get(agent_identity, {
            "detail_level": "moderate",
            "interpretation_style": "technical",
            "focus_areas": ["general_analysis"],
            "output_format": "comprehensive"
        })

    def _apply_agent_style_to_analysis_query(
        self,
        analysis_description: str,
        style: Dict[str, Any],
        agent_identity: str
    ) -> str:
        """Apply agent-specific style to the statistical analysis query."""

        # Base query
        base_query = f"Perform the following statistical analysis on the dataframe: {analysis_description}."

        # Add style-specific instructions
        style_instructions = []

        # Add detail level instructions
        detail_level = style.get("detail_level", "moderate")
        if detail_level == "detailed":
            style_instructions.append("Provide comprehensive statistical details including effect sizes, confidence intervals, and assumptions checks.")
        elif detail_level == "concise":
            style_instructions.append("Provide a concise summary of the key statistical findings.")
        elif detail_level == "accessible":
            style_instructions.append("Explain the results in an accessible way that's easy to understand.")
        elif detail_level == "structured":
            style_instructions.append("Present results in a well-structured, organized format.")

        # Add interpretation style instructions
        interpretation_style = style.get("interpretation_style", "technical")
        if interpretation_style == "business":
            style_instructions.append("Focus on business implications and practical significance of the statistical findings.")
        elif interpretation_style == "educational":
            style_instructions.append("Explain the statistical concepts and what the results mean in practical terms.")
        elif interpretation_style == "technical":
            style_instructions.append("Include technical statistical details such as p-values, test statistics, and degrees of freedom.")

        # Add focus area instructions
        focus_areas = style.get("focus_areas", [])
        if "business_implications" in focus_areas:
            style_instructions.append("Emphasize the business implications and actionable insights from the analysis.")
        if "pattern_discovery" in focus_areas:
            style_instructions.append("Highlight any interesting patterns, trends, or relationships discovered in the data.")
        if "categorical_analysis" in focus_areas:
            style_instructions.append("Pay special attention to categorical variables and group differences.")
        if "statistical_significance" in focus_areas:
            style_instructions.append("Clearly indicate statistical significance and practical significance of findings.")

        # Add output format instructions
        output_format = style.get("output_format", "comprehensive")
        if output_format == "business_focused":
            style_instructions.append("Format the output for a business audience with clear recommendations.")
        elif output_format == "user_friendly":
            style_instructions.append("Present results in a user-friendly format with clear explanations.")
        elif output_format == "organized":
            style_instructions.append("Organize the output in a clear, systematic structure.")

        # Combine base query with style instructions
        if style_instructions:
            enhanced_query = f"{base_query}\n\nAnalysis Style Instructions:\n" + "\n".join([f"- {instruction}" for instruction in style_instructions])
        else:
            enhanced_query = f"{base_query} Provide the key results, such as p-values, test statistics, or correlation coefficients."

        return enhanced_query
