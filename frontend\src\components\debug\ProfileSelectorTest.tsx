import React from 'react';
import { BusinessProfileSelector } from '@/components/business-profile/BusinessProfileSelector';

export const ProfileSelectorTest: React.FC = () => {
  return (
    <div className="p-4 space-y-4">
      <h2 className="text-lg font-semibold">Business Profile Selector Test</h2>
      
      <div className="border p-4 rounded">
        <h3 className="text-md font-medium mb-2">Normal Selector</h3>
        <BusinessProfileSelector
          onProfileChange={(profile) => {
            console.log('Profile changed:', profile);
          }}
          onCreateNew={() => {
            console.log('Create new profile clicked');
          }}
        />
      </div>
      
      <div className="border p-4 rounded">
        <h3 className="text-md font-medium mb-2">Selector without Create Button</h3>
        <BusinessProfileSelector
          onProfileChange={(profile) => {
            console.log('Profile changed:', profile);
          }}
          showCreateButton={false}
        />
      </div>
    </div>
  );
};
