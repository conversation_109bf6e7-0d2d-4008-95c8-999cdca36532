/**
 * Dashboard Real-time Service
 * 
 * Manages real-time updates for dashboard widgets when underlying data changes.
 * Uses WebSocket connections and polling strategies for different data sources.
 */

import { dashboardDataService } from './dashboard-data-service';
import { DataSourceConfig } from '@/types/dashboard-customization';

export interface WidgetSubscription {
  widgetId: string;
  dataSourceId: string;
  config: DataSourceConfig;
  refreshInterval: number;
  lastUpdate: Date;
  onUpdate: (data: any) => void;
  onError: (error: string) => void;
}

export interface DataSourceUpdate {
  dataSourceId: string;
  timestamp: Date;
  changeType: 'insert' | 'update' | 'delete' | 'schema';
  affectedRecords?: number;
}

class DashboardRealtimeService {
  private subscriptions = new Map<string, WidgetSubscription>();
  private intervals = new Map<string, NodeJS.Timeout>();
  private websocket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isConnected = false;

  /**
   * Subscribe a widget to real-time updates
   */
  subscribe(subscription: WidgetSubscription): () => void {
    this.subscriptions.set(subscription.widgetId, subscription);
    
    // Set up polling interval for this widget
    this.setupPolling(subscription);
    
    // Connect to WebSocket if not already connected
    if (!this.isConnected) {
      this.connectWebSocket();
    }

    // Return unsubscribe function
    return () => this.unsubscribe(subscription.widgetId);
  }

  /**
   * Unsubscribe a widget from updates
   */
  unsubscribe(widgetId: string): void {
    const subscription = this.subscriptions.get(widgetId);
    if (subscription) {
      // Clear polling interval
      const interval = this.intervals.get(widgetId);
      if (interval) {
        clearInterval(interval);
        this.intervals.delete(widgetId);
      }
      
      this.subscriptions.delete(widgetId);
    }

    // Disconnect WebSocket if no more subscriptions
    if (this.subscriptions.size === 0 && this.websocket) {
      this.disconnectWebSocket();
    }
  }

  /**
   * Manually trigger update for a specific widget
   */
  async triggerUpdate(widgetId: string): Promise<void> {
    const subscription = this.subscriptions.get(widgetId);
    if (!subscription) return;

    try {
      const result = await dashboardDataService.executeQuery(
        subscription.dataSourceId,
        subscription.config
      );

      if (result.error) {
        subscription.onError(result.error);
      } else {
        subscription.onUpdate(result);
        subscription.lastUpdate = new Date();
      }
    } catch (error) {
      subscription.onError(error instanceof Error ? error.message : 'Update failed');
    }
  }

  /**
   * Trigger updates for all widgets using a specific data source
   */
  async triggerDataSourceUpdate(dataSourceId: string): Promise<void> {
    const affectedSubscriptions = Array.from(this.subscriptions.values())
      .filter(sub => sub.dataSourceId === dataSourceId);

    await Promise.all(
      affectedSubscriptions.map(sub => this.triggerUpdate(sub.widgetId))
    );
  }

  /**
   * Get subscription status for a widget
   */
  getSubscriptionStatus(widgetId: string): {
    isSubscribed: boolean;
    lastUpdate?: Date;
    nextUpdate?: Date;
  } {
    const subscription = this.subscriptions.get(widgetId);
    if (!subscription) {
      return { isSubscribed: false };
    }

    const nextUpdate = new Date(
      subscription.lastUpdate.getTime() + (subscription.refreshInterval * 1000)
    );

    return {
      isSubscribed: true,
      lastUpdate: subscription.lastUpdate,
      nextUpdate,
    };
  }

  /**
   * Update refresh interval for a widget
   */
  updateRefreshInterval(widgetId: string, newInterval: number): void {
    const subscription = this.subscriptions.get(widgetId);
    if (subscription) {
      subscription.refreshInterval = newInterval;
      
      // Reset polling with new interval
      const interval = this.intervals.get(widgetId);
      if (interval) {
        clearInterval(interval);
      }
      this.setupPolling(subscription);
    }
  }

  /**
   * Get all active subscriptions
   */
  getActiveSubscriptions(): WidgetSubscription[] {
    return Array.from(this.subscriptions.values());
  }

  /**
   * Clear all subscriptions and disconnect
   */
  disconnect(): void {
    // Clear all intervals
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();
    
    // Clear subscriptions
    this.subscriptions.clear();
    
    // Disconnect WebSocket
    this.disconnectWebSocket();
  }

  /**
   * Set up polling for a widget subscription
   */
  private setupPolling(subscription: WidgetSubscription): void {
    if (subscription.refreshInterval <= 0) return;

    const interval = setInterval(async () => {
      await this.triggerUpdate(subscription.widgetId);
    }, subscription.refreshInterval * 1000);

    this.intervals.set(subscription.widgetId, interval);
  }

  /**
   * Connect to WebSocket for real-time notifications
   */
  private connectWebSocket(): void {
    if (this.websocket?.readyState === WebSocket.OPEN) return;

    try {
      // Use the correct WebSocket URL pattern that matches the backend
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const token = localStorage.getItem('token');

      if (!token) {
        console.warn('No authentication token available for WebSocket connection');
        return;
      }

      const wsUrl = `${wsProtocol}//${window.location.host}/ws/dashboard?token=${token}`;

      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = () => {
        console.log('Dashboard WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
      };

      this.websocket.onmessage = (event) => {
        try {
          const update: DataSourceUpdate = JSON.parse(event.data);
          this.handleDataSourceUpdate(update);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.websocket.onclose = () => {
        console.log('Dashboard WebSocket disconnected');
        this.isConnected = false;
        this.attemptReconnect();
      };

      this.websocket.onerror = (error) => {
        console.error('Dashboard WebSocket error:', error);
        this.isConnected = false;
      };
    } catch (error) {
      console.error('Failed to connect to dashboard WebSocket:', error);
    }
  }

  /**
   * Disconnect WebSocket
   */
  private disconnectWebSocket(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
      this.isConnected = false;
    }
  }

  /**
   * Attempt to reconnect WebSocket
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max WebSocket reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff

    setTimeout(() => {
      if (this.subscriptions.size > 0) {
        console.log(`Attempting WebSocket reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connectWebSocket();
      }
    }, delay);
  }

  /**
   * Handle data source update notification
   */
  private async handleDataSourceUpdate(update: DataSourceUpdate): Promise<void> {
    console.log('Received data source update:', update);
    
    // Find all widgets that use this data source
    const affectedSubscriptions = Array.from(this.subscriptions.values())
      .filter(sub => sub.dataSourceId === update.dataSourceId);

    if (affectedSubscriptions.length === 0) return;

    // Trigger updates for affected widgets
    await Promise.all(
      affectedSubscriptions.map(async (subscription) => {
        try {
          await this.triggerUpdate(subscription.widgetId);
        } catch (error) {
          console.error(`Failed to update widget ${subscription.widgetId}:`, error);
        }
      })
    );
  }
}

// Export singleton instance
export const dashboardRealtimeService = new DashboardRealtimeService();

// Auto-cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    dashboardRealtimeService.disconnect();
  });
}
