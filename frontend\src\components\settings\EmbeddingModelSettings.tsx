import React, { useState, useEffect } from 'react';
import { Loader2, Info, Zap, Database, Globe } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

interface EmbeddingModel {
  id: string;
  name: string;
  dimensions: number;
  description: string;
  size?: string;
  languages?: string[];
}

interface EmbeddingModelSettingsProps {
  className?: string;
}

const EmbeddingModelSettings: React.FC<EmbeddingModelSettingsProps> = ({ className }) => {
  const [models, setModels] = useState<EmbeddingModel[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Load available Hugging Face embedding models
  useEffect(() => {
    const loadModels = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/embedding-config/providers/huggingface/models', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setModels(data);
          
          // Set default model if none selected
          if (!selectedModel && data.length > 0) {
            const defaultModel = data.find((m: EmbeddingModel) => m.id === 'BAAI/bge-small-en-v1.5') || data[0];
            setSelectedModel(defaultModel.id);
          }
        } else {
          console.error('Failed to load embedding models');
          toast({
            title: "Error",
            description: "Failed to load embedding models",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error loading embedding models:', error);
        toast({
          title: "Error",
          description: "Failed to load embedding models",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadModels();
  }, [toast, selectedModel]);

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      
      // Here you would typically save to user settings
      // For now, we'll just show a success message
      toast({
        title: "Settings Saved",
        description: `Embedding model updated to ${selectedModel}`,
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Error",
        description: "Failed to save embedding model settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getModelIcon = (modelId: string) => {
    if (modelId.includes('small') || modelId.includes('MiniLM')) {
      return <Zap className="h-4 w-4 text-green-500" />;
    } else if (modelId.includes('large')) {
      return <Database className="h-4 w-4 text-blue-500" />;
    } else if (modelId.includes('multilingual')) {
      return <Globe className="h-4 w-4 text-purple-500" />;
    }
    return <Database className="h-4 w-4 text-gray-500" />;
  };

  const getModelBadge = (modelId: string) => {
    if (modelId.includes('small') || modelId.includes('MiniLM')) {
      return <Badge variant="secondary" className="bg-green-100 text-green-800">Fast</Badge>;
    } else if (modelId.includes('large')) {
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">High Quality</Badge>;
    } else if (modelId.includes('multilingual')) {
      return <Badge variant="secondary" className="bg-purple-100 text-purple-800">Multilingual</Badge>;
    }
    return <Badge variant="secondary">Standard</Badge>;
  };

  const selectedModelInfo = models.find(m => m.id === selectedModel);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Embedding Model Settings
        </CardTitle>
        <CardDescription>
          Choose the Hugging Face embedding model for document processing and memory storage.
          All models run locally with no API costs.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Embedding models convert text into numerical vectors for similarity search and memory storage.
            Larger models provide better quality but use more memory and are slower.
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="embedding-model">Embedding Model</Label>
            {isLoading ? (
              <div className="flex items-center space-x-2 h-10 px-3 border rounded-md bg-gray-50">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm text-gray-600">Loading models...</span>
              </div>
            ) : (
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger id="embedding-model">
                  <SelectValue placeholder="Select embedding model" />
                </SelectTrigger>
                <SelectContent>
                  {models.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex items-center gap-2">
                        {getModelIcon(model.id)}
                        <span>{model.name}</span>
                        {getModelBadge(model.id)}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {selectedModelInfo && (
            <div className="p-4 bg-gray-50 rounded-lg space-y-2">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">{selectedModelInfo.name}</h4>
                {getModelBadge(selectedModelInfo.id)}
              </div>
              <p className="text-sm text-gray-600">{selectedModelInfo.description}</p>
              <div className="flex gap-4 text-sm text-gray-500">
                <span>Dimensions: {selectedModelInfo.dimensions}</span>
                {selectedModelInfo.size && <span>Size: {selectedModelInfo.size}</span>}
                {selectedModelInfo.languages && (
                  <span>Languages: {selectedModelInfo.languages.join(', ')}</span>
                )}
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <Button 
              onClick={handleSaveSettings} 
              disabled={isSaving || !selectedModel}
              className="min-w-[120px]"
            >
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Settings'
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EmbeddingModelSettings;
