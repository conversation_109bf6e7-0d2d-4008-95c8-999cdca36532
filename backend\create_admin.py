#!/usr/bin/env python
"""
Command-line tool to create an admin user for the Datagenius application.

Usage:
    python create_admin.py --email <EMAIL> --password secure_password [--username admin] [--first-name Admin] [--last-name User]
"""

import argparse
import sys
import os
import logging
from datetime import datetime, timezone
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from passlib.context import CryptContext

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Set up the path to import from the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import database models and config
try:
    from app.database import User, Base
    from app import config
except ImportError:
    logger.error("Failed to import required modules. Make sure you're running this script from the backend directory.")
    sys.exit(1)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    """Hash a password for storing."""
    return pwd_context.hash(password)

def get_utc_now():
    """Get current UTC datetime."""
    return datetime.now(timezone.utc)

def create_admin_user(email, password, username=None, first_name=None, last_name=None):
    """
    Create a new admin user or update an existing user to be an admin.
    
    Args:
        email: User email
        password: User password
        username: Optional username
        first_name: Optional first name
        last_name: Optional last name
    """
    # Create database engine and session
    engine = create_engine(
        config.DATABASE_URL,
        echo=False,
        connect_args={"check_same_thread": False} if config.DATABASE_URL.startswith("sqlite") else {},
    )
    
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == email).first()
        
        if existing_user:
            logger.info(f"User with email {email} already exists.")
            
            if existing_user.is_superuser:
                logger.info("This user is already an admin.")
                return
            
            # Update existing user to be an admin
            existing_user.is_superuser = True
            existing_user.updated_at = get_utc_now()
            
            if username and not existing_user.username:
                existing_user.username = username
            
            if first_name and not existing_user.first_name:
                existing_user.first_name = first_name
                
            if last_name and not existing_user.last_name:
                existing_user.last_name = last_name
            
            db.commit()
            logger.info(f"User {email} has been updated to be an admin.")
            return
        
        # Create new admin user
        hashed_password = get_password_hash(password)
        
        new_user = User(
            email=email,
            username=username,
            first_name=first_name,
            last_name=last_name,
            hashed_password=hashed_password,
            is_active=True,
            is_verified=True,
            is_superuser=True,
            created_at=get_utc_now(),
            updated_at=get_utc_now()
        )
        
        db.add(new_user)
        db.commit()
        logger.info(f"Admin user {email} created successfully.")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating admin user: {str(e)}")
        raise
    finally:
        db.close()

def main():
    """Main function to parse arguments and create admin user."""
    parser = argparse.ArgumentParser(description="Create an admin user for Datagenius")
    parser.add_argument("--email", required=True, help="Admin email address")
    parser.add_argument("--password", required=True, help="Admin password")
    parser.add_argument("--username", help="Admin username (optional)")
    parser.add_argument("--first-name", help="Admin first name (optional)")
    parser.add_argument("--last-name", help="Admin last name (optional)")
    
    args = parser.parse_args()
    
    try:
        create_admin_user(
            args.email,
            args.password,
            args.username,
            args.first_name,
            args.last_name
        )
    except Exception as e:
        logger.error(f"Failed to create admin user: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
