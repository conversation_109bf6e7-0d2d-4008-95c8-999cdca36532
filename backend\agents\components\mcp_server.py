"""
MCP server component for the Datagenius agent system.

This module provides a component for integrating MCP-compatible tools with agents.
"""

import logging
import json
import sys
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent
from ..tools.mcp import MCPTool, initialize_mcp_system

logger = logging.getLogger(__name__)


class MCPServerComponent(AgentComponent):
    """Component for integrating MCP-compatible tools with agents."""

    def __init__(self):
        """Initialize the MCP server component."""
        super().__init__()
        self.tools = {}
        self.server_name = "datagenius-mcp-server"
        self.server_version = "1.0.0"

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component with configuration.

        Args:
            config: Configuration dictionary for the component
        """
        self.server_name = config.get("server_name", self.server_name)
        self.server_version = config.get("server_version", self.server_version)

        # Initialize tools from configuration using the enhanced MCP system
        if "tools" in config:
            try:
                # Initialize the enhanced MCP system once
                registry = await initialize_mcp_system(environment="development")

                for tool_config in config["tools"]:
                    tool_type = tool_config.get("type")
                    if not tool_type:
                        logger.warning("Tool configuration missing 'type' field")
                        continue

                    logger.info(f"Creating MCP tool of type '{tool_type}'")

                    try:
                        # Try to get tool from the enhanced registry
                        if hasattr(registry, 'get_tool'):
                            tool = registry.get_tool(tool_type)
                            if tool:
                                self.tools[tool.name] = tool
                                logger.info(f"Successfully added MCP tool from enhanced registry: {tool.name}")
                                continue

                        # Fallback: try to create tool instance from AVAILABLE_TOOLS
                        from ..tools.mcp import AVAILABLE_TOOLS

                        # Map tool_type to class name if needed
                        tool_class_name = None
                        for class_name, tool_class in AVAILABLE_TOOLS.items():
                            if hasattr(tool_class, 'name') and tool_class().name == tool_type:
                                tool_class_name = class_name
                                break
                            elif class_name.lower().replace('tool', '') == tool_type.replace('_', '').lower():
                                tool_class_name = class_name
                                break

                        if tool_class_name and tool_class_name in AVAILABLE_TOOLS:
                            tool_class = AVAILABLE_TOOLS[tool_class_name]
                            tool = tool_class()
                            await tool.initialize(tool_config)
                            self.tools[tool.name] = tool
                            logger.info(f"Successfully created MCP tool from AVAILABLE_TOOLS: {tool.name}")
                        else:
                            logger.warning(f"MCP tool type '{tool_type}' not found in AVAILABLE_TOOLS")

                    except Exception as e:
                        logger.error(f"Error initializing MCP tool '{tool_type}': {e}")

            except Exception as e:
                logger.error(f"Error initializing enhanced MCP system: {e}")
                # Fallback to basic tool creation if enhanced system fails
                logger.info("Falling back to basic tool creation")

                for tool_config in config["tools"]:
                    tool_type = tool_config.get("type")
                    if not tool_type:
                        continue

                    try:
                        from ..tools.mcp import AVAILABLE_TOOLS

                        # Map tool_type to class name
                        tool_class_name = None
                        for class_name, tool_class in AVAILABLE_TOOLS.items():
                            if hasattr(tool_class, 'name') and tool_class().name == tool_type:
                                tool_class_name = class_name
                                break
                            elif class_name.lower().replace('tool', '') == tool_type.replace('_', '').lower():
                                tool_class_name = class_name
                                break

                        if tool_class_name and tool_class_name in AVAILABLE_TOOLS:
                            tool_class = AVAILABLE_TOOLS[tool_class_name]
                            tool = tool_class()
                            await tool.initialize(tool_config)
                            self.tools[tool.name] = tool
                            logger.info(f"Successfully created MCP tool (fallback): {tool.name}")
                        else:
                            logger.warning(f"MCP tool type '{tool_type}' not found")
                    except Exception as e:
                        logger.error(f"Error creating MCP tool '{tool_type}' (fallback): {e}")

        logger.info(f"Initialized MCP server with {len(self.tools)} tools")

    async def list_tools(self) -> List[Dict[str, Any]]:
        """
        List all available tools in MCP format.

        Returns:
            List of tool definitions
        """
        return [tool.definition for tool in self.tools.values()]

    async def call_tool(self, name: str, arguments: Dict[str, Any], context: Optional[Union[Any, Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Call a tool with the provided arguments.

        Args:
            name: Name of the tool to call
            arguments: Arguments for tool execution
            context: Optional context to pass to the tool

        Returns:
            Tool execution results
        """
        if name not in self.tools:
            logger.warning(f"Tool '{name}' not found")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Tool '{name}' not found"
                    }
                ]
            }

        try:
            # Log the full arguments for debugging
            logger.info(f"EXECUTING MCP TOOL '{name}'")
            logger.info(f"TOOL ARGUMENTS:")
            logger.info(f"ORIGINAL ARGUMENTS KEYS: {list(arguments.keys())}")
            logger.info(f"ORIGINAL ARGUMENTS CONTENT_TYPE: {arguments.get('content_type', 'NOT_FOUND')}")

            # Log specific arguments for content generation
            if name == "generate_content":
                logger.info(f"- content_type: '{arguments.get('content_type', '')}'")
                logger.info(f"- brand_description: '{arguments.get('brand_description', '')}'")
                logger.info(f"- target_audience: '{arguments.get('target_audience', '')}'")
                logger.info(f"- products_services: '{arguments.get('products_services', '')}'")
                logger.info(f"- marketing_goals: '{arguments.get('marketing_goals', '')}'")
                logger.info(f"- existing_content: '{arguments.get('existing_content', '')}'")
                logger.info(f"- keywords: '{arguments.get('keywords', '')}'")
                logger.info(f"- suggested_topics: '{arguments.get('suggested_topics', '')}'")
                logger.info(f"- tone: '{arguments.get('tone', '')}'")
                logger.info(f"- provider: '{arguments.get('provider', '')}'")
                logger.info(f"- model: '{arguments.get('model', '')}'")
            else:
                # For other tools, log a summary
                logger.info(f"Arguments summary: {json.dumps(arguments)[:200]}...")

            # Get the tool and execute it
            tool = self.tools[name]
            logger.info(f"Found tool: {tool.name} ({type(tool).__name__})")

            # If context is provided, inject it into the arguments
            if context is not None:
                # Extract context information for tools that need it
                if hasattr(context, 'user_id'):
                    # AgentProcessingContext object
                    # Pass user context both at top level (for security) and in params (for tool logic)
                    # Only add context fields that don't already exist to preserve original arguments
                    if "user_id" not in arguments:
                        arguments["user_id"] = context.user_id
                    if "conversation_id" not in arguments:
                        arguments["conversation_id"] = getattr(context, 'conversation_id', None)
                    if "agent_identity" not in arguments:
                        arguments["agent_identity"] = getattr(context, 'agent_identity', None)
                    if "persona_id" not in arguments:
                        arguments["persona_id"] = getattr(context, 'persona_id', None)

                    # Add context to params without overriding existing params
                    if "params" not in arguments:
                        arguments["params"] = {}
                    arguments["params"].update({
                        "user_id": context.user_id,
                        "conversation_id": getattr(context, 'conversation_id', None),
                        "context": context.initial_context if hasattr(context, 'initial_context') else {}
                    })
                elif isinstance(context, dict):
                    # Dictionary context
                    # Pass user context both at top level (for security) and in params (for tool logic)
                    # Only add context fields that don't already exist to preserve original arguments
                    if "user_id" not in arguments and context.get("user_id"):
                        arguments["user_id"] = context.get("user_id")
                    if "conversation_id" not in arguments and context.get("conversation_id"):
                        arguments["conversation_id"] = context.get("conversation_id")
                    if "agent_identity" not in arguments and context.get("agent_identity"):
                        arguments["agent_identity"] = context.get("agent_identity")
                    if "persona_id" not in arguments and context.get("persona_id"):
                        arguments["persona_id"] = context.get("persona_id")

                    # Add context to params without overriding existing params
                    if "params" not in arguments:
                        arguments["params"] = {}
                    arguments["params"].update({
                        "user_id": context.get("user_id"),
                        "conversation_id": context.get("conversation_id"),
                        "context": context
                    })
                logger.info(f"Injected context into tool arguments (user_id: {arguments.get('user_id')})")

            # Log arguments after context injection
            logger.info(f"ARGUMENTS AFTER CONTEXT INJECTION:")
            logger.info(f"FINAL ARGUMENTS KEYS: {list(arguments.keys())}")
            logger.info(f"FINAL ARGUMENTS CONTENT_TYPE: {arguments.get('content_type', 'NOT_FOUND')}")

            # Execute the tool
            result = await tool.execute(arguments)

            # Log the result
            if result.get("isError", False):
                logger.error(f"Tool execution failed: {result.get('content', [{'text': 'Unknown error'}])[0].get('text', 'Unknown error')}")
            else:
                logger.info(f"Tool execution succeeded")
                if "content" in result:
                    content_preview = str(result["content"])[:200] + "..." if len(str(result["content"])) > 200 else str(result["content"])
                    logger.info(f"Result content preview: {content_preview}")

            return result
        except Exception as e:
            logger.error(f"Error executing MCP tool '{name}': {e}")
            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": f"Error executing tool '{name}': {str(e)}"
                    }
                ]
            }

    async def process(self, context: Union[Any, Dict[str, Any]]) -> Union[Any, Dict[str, Any]]:
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        # Handle both dict and object contexts for compatibility
        if hasattr(context, 'metadata'):
            # AgentProcessingContext object
            tool_to_call = context.metadata.get("tool_to_call")
            tool_arguments_from_meta = context.metadata.get("tool_arguments", {})
        else:
            # Dictionary context (from composable agents)
            metadata = context.get("metadata", {})
            tool_to_call = metadata.get("tool_to_call")
            tool_arguments_from_meta = metadata.get("tool_arguments", {})

        # Check if a tool should be executed based on metadata
        if tool_to_call:
            logger.info(f"Executing tool '{tool_to_call}' from context metadata")
            result = await self.call_tool(tool_to_call, tool_arguments_from_meta, context=context)

            # Handle both dict and object contexts for setting results
            if hasattr(context, 'component_data'):
                # AgentProcessingContext object
                context.component_data.setdefault(self.name, {})["tool_result"] = result
                context.metadata["tool_used"] = tool_to_call # Record which tool was attempted
            else:
                # Dictionary context (from composable agents)
                if "component_data" not in context:
                    context["component_data"] = {}
                context["component_data"].setdefault(self.name, {})["tool_result"] = result
                context["metadata"]["tool_used"] = tool_to_call

            if result.get("isError", False):
                error_message = "Tool execution failed."
                if result.get("content") and isinstance(result["content"], list) and result["content"]:
                    if isinstance(result["content"][0], dict) and "text" in result["content"][0]:
                        error_message = result["content"][0]["text"]

                # Handle error reporting for both context types
                if hasattr(context, 'add_error'):
                    context.add_error(self.name, f"mcp_tool_error_{tool_to_call}", {"details": error_message})
                    context.response = f"Error executing tool '{tool_to_call}': {error_message}"
                else:
                    # Dictionary context - set error in response
                    context["response"] = f"Error executing tool '{tool_to_call}': {error_message}"
            elif "content" in result:
                # Extract text content from the result for the main response
                text_content = []
                for content_item in result.get("content", []):
                    if isinstance(content_item, dict) and content_item.get("type") == "text":
                        text_content.append(content_item.get("text", ""))

                if text_content:
                    # Handle both context types for setting response
                    if hasattr(context, 'response'):
                        context.response = "\n".join(text_content)
                    else:
                        context["response"] = "\n".join(text_content)
                # The full result (including non-text parts) is in component_data

        # Check if we need to list available tools based on user message
        elif (context.get("message", "") if isinstance(context, dict) else getattr(context, "message", "")).lower().strip() in ["list tools", "what tools do you have", "show tools"]:
            tools = await self.list_tools()
            if tools:
                tool_descriptions = [f"- {tool['name']}: {tool['description']}" for tool in tools]
                response_text = "Available tools:\n" + "\n".join(tool_descriptions)
            else:
                response_text = "No tools are currently available."

            # Handle both context types for setting response and metadata
            if hasattr(context, 'response'):
                context.response = response_text
                context.metadata["listed_tools"] = True
            else:
                context["response"] = response_text
                context["metadata"]["listed_tools"] = True

        return context

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.

        Returns:
            List of capability strings
        """
        # Each tool provides a capability
        return [f"tool:{tool.name}" for tool in self.tools.values()]
