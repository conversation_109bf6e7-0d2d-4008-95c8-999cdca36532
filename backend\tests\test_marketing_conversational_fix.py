#!/usr/bin/env python3
"""
Test script to verify the marketing agent conversational state persistence fix.

This test simulates the problem scenario:
1. User sends marketing content generation request
2. Agent generates content and saves conversational state metadata
3. User sends follow-up message
4. Agent should detect conversational state and respond conversationally (not regenerate content)
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.marketing_agent.components import MarketingParserComponent, MCPContentGeneratorComponent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockMCPServer:
    """Mock MCP server for testing."""
    
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Mock tool call that returns appropriate responses."""
        if tool_name == "handle_conversation":
            message = params.get("message", "")
            user_context = params.get("user_context", {})
            
            # Mock intent detection
            if user_context.get("task") == "intent_detection":
                if "what else" in message.lower() or "any other" in message.lower():
                    return {
                        "isError": False,
                        "content": [{"text": '{"intent": "follow_up_question", "confidence": 0.9, "reasoning": "User asking for additional suggestions"}'}]
                    }
                else:
                    return {
                        "isError": False,
                        "content": [{"text": '{"intent": "content_generation", "confidence": 0.8, "reasoning": "User requesting marketing content"}'}]
                    }
            
            # Mock conversational response
            else:
                return {
                    "isError": False,
                    "content": [{"text": "I'd be happy to help with additional marketing strategies! Here are some more ideas..."}]
                }
        
        elif tool_name == "generate_marketing_content":
            # Mock content generation
            return {
                "isError": False,
                "content": [{"text": "# Marketing Strategy\n\nHere's a comprehensive marketing strategy for your business...", "type": "text"}]
            }
        
        return {"isError": True, "content": [{"text": "Unknown tool"}]}


async def test_conversational_state_persistence():
    """Test that conversational state persists between messages."""
    logger.info("=== Testing Marketing Agent Intelligent Request Analysis ===")

    # Create components
    parser = MarketingParserComponent()
    generator = MCPContentGeneratorComponent()

    # Initialize components
    await parser._initialize({})
    await generator._initialize({})

    # Mock MCP server
    mock_mcp_server = MockMCPServer()

    # Test scenario 1: Tool-triggered content generation (button click with form data)
    logger.info("\n--- Test 1: Tool-Triggered Content Generation (Button Click) ---")

    context1 = {
        "message": "Generate marketing strategy content",
        "conversation_history": [],
        "agent_components": [mock_mcp_server],
        "marketing_form_data": {
            "content_type": "marketing_strategy",
            "brand_description": "Tech startup",
            "target_audience": "Small businesses",
            "products_services": "Software solutions",
            "marketing_goals": "Increase brand awareness",
            "existing_content": "",
            "keywords": "technology, software",
            "suggested_topics": "innovation, efficiency",
            "tone": "Professional"
        }
    }

    # Process with parser
    result1 = await parser.process(context1)
    logger.info(f"Parser result 1 - Skip content generation: {result1.get('skip_marketing_content_generation')}")
    logger.info(f"Parser result 1 - Is conversational: {result1.get('is_conversational')}")
    logger.info(f"Parser result 1 - Has marketing task: {'marketing_task' in result1}")
    logger.info(f"Parser result 1 - Request analysis: {result1.get('request_analysis', {})}")

    # Process with generator if task was created
    if 'marketing_task' in result1 and not result1.get('skip_marketing_content_generation'):
        result1 = await generator.process(result1)
        logger.info(f"Generator result 1 - Response length: {len(result1.get('response', ''))}")
        logger.info(f"Generator result 1 - Metadata keys: {list(result1.get('metadata', {}).keys())}")

        # Check if conversational state metadata was added
        conversational_state = result1.get('metadata', {}).get('conversational_state', {})
        logger.info(f"Conversational state saved: {conversational_state}")

    # Test scenario 2: Follow-up message (should be conversational)
    logger.info("\n--- Test 2: Follow-up Message (Should be Conversational) ---")

    # Simulate conversation history with the generated content
    conversation_history = [
        {
            "sender": "user",
            "content": "Generate marketing strategy content",
            "metadata": {}
        },
        {
            "sender": "ai",
            "content": "# Marketing Strategy\n\nHere's a comprehensive marketing strategy for your business...",
            "metadata": result1.get('metadata', {})  # Include the metadata from first response
        }
    ]

    context2 = {
        "message": "What else can we do to improve our marketing?",
        "conversation_history": conversation_history,
        "agent_components": [mock_mcp_server]
    }

    # Process with parser
    result2 = await parser.process(context2)
    logger.info(f"Parser result 2 - Skip content generation: {result2.get('skip_marketing_content_generation')}")
    logger.info(f"Parser result 2 - Is conversational: {result2.get('is_conversational')}")
    logger.info(f"Parser result 2 - Is follow-up question: {result2.get('is_follow_up_question')}")
    logger.info(f"Parser result 2 - Has marketing task: {'marketing_task' in result2}")
    logger.info(f"Parser result 2 - Request analysis: {result2.get('request_analysis', {})}")

    # Process with generator
    result2 = await generator.process(result2)
    logger.info(f"Generator result 2 - Response length: {len(result2.get('response', ''))}")
    logger.info(f"Generator result 2 - Metadata: {result2.get('metadata', {})}")

    # Test scenario 3: Another tool-triggered request (should still work)
    logger.info("\n--- Test 3: Another Tool-Triggered Request (Should Generate Content) ---")

    # Add the follow-up to conversation history
    conversation_history.extend([
        {
            "sender": "user",
            "content": "What else can we do to improve our marketing?",
            "metadata": {}
        },
        {
            "sender": "ai",
            "content": "I'd be happy to help with additional marketing strategies! Here are some more ideas...",
            "metadata": result2.get('metadata', {})
        }
    ])

    context3 = {
        "message": "Generate social media content",
        "conversation_history": conversation_history,
        "agent_components": [mock_mcp_server],
        "marketing_form_data": {
            "content_type": "social_media_content",
            "brand_description": "Tech startup",
            "target_audience": "Small businesses",
            "products_services": "Software solutions",
            "marketing_goals": "Increase brand awareness",
            "existing_content": "",
            "keywords": "technology, software",
            "suggested_topics": "innovation, efficiency",
            "tone": "Professional"
        }
    }

    # Process with parser
    result3 = await parser.process(context3)
    logger.info(f"Parser result 3 - Skip content generation: {result3.get('skip_marketing_content_generation')}")
    logger.info(f"Parser result 3 - Is conversational: {result3.get('is_conversational')}")
    logger.info(f"Parser result 3 - Has marketing task: {'marketing_task' in result3}")
    logger.info(f"Parser result 3 - Request analysis: {result3.get('request_analysis', {})}")

    # Test scenario 4: Tool completion state detection
    logger.info("\n--- Test 4: Tool Completion State Detection ---")

    # Simulate a context with tool completion flags
    context4 = {
        "message": "Can you explain this strategy more?",
        "conversation_history": conversation_history,
        "agent_components": [mock_mcp_server],
        "tool_completed": True,
        "auto_conversational_mode": True
    }

    # Process with parser
    result4 = await parser.process(context4)
    logger.info(f"Parser result 4 - Skip content generation: {result4.get('skip_marketing_content_generation')}")
    logger.info(f"Parser result 4 - Is conversational: {result4.get('is_conversational')}")
    logger.info(f"Parser result 4 - Request analysis: {result4.get('request_analysis', {})}")

    # Verify the fix worked
    success = True

    # Test 1: Tool-triggered request should generate content
    # Note: After content generation, the context is reset for future conversational messages
    # So we check if content was actually generated, not the final context state
    if not result1.get('response') or len(result1.get('response', '')) < 50:
        logger.error("❌ Test 1: Tool-triggered request should have generated content")
        success = False
    else:
        logger.info("✅ Test 1: Tool-triggered request correctly processed for content generation")

    if not result1.get('response') or len(result1.get('response', '')) < 50:
        logger.error("❌ Test 1: Tool-triggered request should have generated content")
        success = False
    else:
        logger.info("✅ Test 1: Tool-triggered request generated content successfully")

    # Test 2: Follow-up should be conversational
    if not result2.get('skip_marketing_content_generation'):
        logger.error("❌ Test 2: Follow-up message should have skipped content generation")
        success = False
    else:
        logger.info("✅ Test 2: Follow-up message skipped content generation (conversational)")

    if not result2.get('is_conversational'):
        logger.error("❌ Test 2: Follow-up message should be marked as conversational")
        success = False
    else:
        logger.info("✅ Test 2: Follow-up message marked as conversational")

    if not result2.get('response') or len(result2.get('response', '')) < 10:
        logger.error("❌ Test 2: Follow-up message should have generated a conversational response")
        success = False
    else:
        logger.info("✅ Test 2: Follow-up message generated conversational response")

    # Test 3: Another tool-triggered request should work
    if result3.get('skip_marketing_content_generation'):
        logger.error("❌ Test 3: Second tool-triggered request should NOT skip content generation")
        success = False
    else:
        logger.info("✅ Test 3: Second tool-triggered request correctly processed for content generation")

    if success:
        logger.info("\n🎉 ALL TESTS PASSED! The intelligent request analysis is working correctly.")
        logger.info("✅ Tool-triggered requests (button clicks) generate content")
        logger.info("✅ Follow-up messages are handled conversationally")
        logger.info("✅ Multiple tool calls work correctly")
    else:
        logger.error("\n❌ SOME TESTS FAILED! The fix needs more work.")

    return success


async def main():
    """Run the test."""
    try:
        success = await test_conversational_state_persistence()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Test failed with exception: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
