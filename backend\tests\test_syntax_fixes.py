#!/usr/bin/env python3
"""
Test script to verify the syntax and schema fixes.

This script tests:
1. Vector service syntax fix
2. Schema inference fix for DataCleaningTool
3. Pydantic namespace conflict resolution
"""

import sys
import logging
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent
sys.path.insert(0, str(backend_path))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_vector_service_syntax():
    """Test that vector service imports without syntax errors."""
    try:
        from agents.utils.vector_service import VectorService
        logger.info("✅ Vector service imports successfully - syntax error fixed")
        return True
    except SyntaxError as e:
        logger.error(f"❌ Vector service still has syntax error: {e}")
        return False
    except Exception as e:
        logger.warning(f"⚠️ Vector service import error (not syntax): {e}")
        return True  # Not a syntax error


def test_pydantic_namespace_fix():
    """Test that Pydantic namespace conflicts are resolved."""
    try:
        from pydantic import create_model
        
        # Test creating a model with model_ fields (should not warn)
        test_fields = {
            'model_provider': (str, None),
            'model_name': (str, None),
            'regular_field': (str, None)
        }
        
        # Create model config to avoid protected namespace warnings
        model_config = {
            'protected_namespaces': ()
        }
        
        TestSchema = create_model(
            'TestSchema',
            __config__=type('Config', (), model_config),
            **test_fields
        )
        
        # Test instantiation
        instance = TestSchema(
            model_provider="test_provider",
            model_name="test_model",
            regular_field="test_value"
        )
        
        logger.info("✅ Pydantic namespace conflict resolved - no warnings expected")
        return True
    except Exception as e:
        logger.error(f"❌ Pydantic namespace test failed: {e}")
        return False


def main():
    """Run all tests."""
    logger.info("🧪 Running syntax fix tests...")
    
    tests = [
        ("Vector Service Syntax", test_vector_service_syntax),
        ("Pydantic Namespace Fix", test_pydantic_namespace_fix)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 All fixes verified successfully!")
        return 0
    else:
        logger.error("💥 Some tests failed - check the logs above")
        return 1


if __name__ == "__main__":
    sys.exit(main())
