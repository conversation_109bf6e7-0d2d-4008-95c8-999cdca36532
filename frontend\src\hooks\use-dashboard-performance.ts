import { useEffect, useRef, useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

export interface PerformanceMetrics {
  renderTime: number;
  dataLoadTime: number;
  memoryUsage: number;
  componentCount: number;
  errorCount: number;
  lastUpdate: Date;
}

export interface PerformanceThresholds {
  maxRenderTime: number;
  maxDataLoadTime: number;
  maxMemoryUsage: number;
  maxErrorCount: number;
}

export interface PerformanceAlert {
  type: 'warning' | 'error';
  metric: keyof PerformanceMetrics;
  value: number;
  threshold: number;
  message: string;
  timestamp: Date;
}

const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  maxRenderTime: 1000, // 1 second
  maxDataLoadTime: 5000, // 5 seconds
  maxMemoryUsage: 100 * 1024 * 1024, // 100MB
  maxErrorCount: 5,
};

export const useDashboardPerformance = (
  thresholds: Partial<PerformanceThresholds> = {},
  enableAlerts: boolean = true
) => {
  const { toast } = useToast();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    dataLoadTime: 0,
    memoryUsage: 0,
    componentCount: 0,
    errorCount: 0,
    lastUpdate: new Date(),
  });
  
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  
  const renderStartTime = useRef<number>(0);
  const dataLoadStartTime = useRef<number>(0);
  const componentCountRef = useRef<number>(0);
  const errorCountRef = useRef<number>(0);
  const performanceObserver = useRef<PerformanceObserver | null>(null);
  
  const finalThresholds = { ...DEFAULT_THRESHOLDS, ...thresholds };

  // Start performance monitoring
  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    
    // Initialize Performance Observer for navigation and resource timing
    if ('PerformanceObserver' in window) {
      performanceObserver.current = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            const loadTime = navEntry.loadEventEnd - navEntry.loadEventStart;
            
            setMetrics(prev => ({
              ...prev,
              dataLoadTime: loadTime,
              lastUpdate: new Date(),
            }));
          }
          
          if (entry.entryType === 'measure' && entry.name.includes('dashboard')) {
            setMetrics(prev => ({
              ...prev,
              renderTime: entry.duration,
              lastUpdate: new Date(),
            }));
          }
        });
      });
      
      try {
        performanceObserver.current.observe({ 
          entryTypes: ['navigation', 'measure', 'resource'] 
        });
      } catch (error) {
        console.warn('Performance Observer not fully supported:', error);
      }
    }
  }, []);

  // Stop performance monitoring
  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
    
    if (performanceObserver.current) {
      performanceObserver.current.disconnect();
      performanceObserver.current = null;
    }
  }, []);

  // Mark render start
  const markRenderStart = useCallback(() => {
    renderStartTime.current = performance.now();
    performance.mark('dashboard-render-start');
  }, []);

  // Mark render end
  const markRenderEnd = useCallback(() => {
    const endTime = performance.now();
    const renderTime = endTime - renderStartTime.current;
    
    performance.mark('dashboard-render-end');
    performance.measure('dashboard-render', 'dashboard-render-start', 'dashboard-render-end');
    
    setMetrics(prev => ({
      ...prev,
      renderTime,
      lastUpdate: new Date(),
    }));
    
    // Check threshold
    if (enableAlerts && renderTime > finalThresholds.maxRenderTime) {
      const alert: PerformanceAlert = {
        type: renderTime > finalThresholds.maxRenderTime * 2 ? 'error' : 'warning',
        metric: 'renderTime',
        value: renderTime,
        threshold: finalThresholds.maxRenderTime,
        message: `Dashboard render time (${renderTime.toFixed(0)}ms) exceeded threshold (${finalThresholds.maxRenderTime}ms)`,
        timestamp: new Date(),
      };
      
      addAlert(alert);
    }
  }, [enableAlerts, finalThresholds.maxRenderTime]);

  // Mark data load start
  const markDataLoadStart = useCallback(() => {
    dataLoadStartTime.current = performance.now();
    performance.mark('dashboard-data-load-start');
  }, []);

  // Mark data load end
  const markDataLoadEnd = useCallback(() => {
    const endTime = performance.now();
    const loadTime = endTime - dataLoadStartTime.current;
    
    performance.mark('dashboard-data-load-end');
    performance.measure('dashboard-data-load', 'dashboard-data-load-start', 'dashboard-data-load-end');
    
    setMetrics(prev => ({
      ...prev,
      dataLoadTime: loadTime,
      lastUpdate: new Date(),
    }));
    
    // Check threshold
    if (enableAlerts && loadTime > finalThresholds.maxDataLoadTime) {
      const alert: PerformanceAlert = {
        type: loadTime > finalThresholds.maxDataLoadTime * 2 ? 'error' : 'warning',
        metric: 'dataLoadTime',
        value: loadTime,
        threshold: finalThresholds.maxDataLoadTime,
        message: `Data load time (${loadTime.toFixed(0)}ms) exceeded threshold (${finalThresholds.maxDataLoadTime}ms)`,
        timestamp: new Date(),
      };
      
      addAlert(alert);
    }
  }, [enableAlerts, finalThresholds.maxDataLoadTime]);

  // Update component count
  const updateComponentCount = useCallback((count: number) => {
    componentCountRef.current = count;
    setMetrics(prev => ({
      ...prev,
      componentCount: count,
      lastUpdate: new Date(),
    }));
  }, []);

  // Record error
  const recordError = useCallback((error: Error) => {
    errorCountRef.current += 1;
    
    setMetrics(prev => ({
      ...prev,
      errorCount: errorCountRef.current,
      lastUpdate: new Date(),
    }));
    
    // Check threshold
    if (enableAlerts && errorCountRef.current > finalThresholds.maxErrorCount) {
      const alert: PerformanceAlert = {
        type: 'error',
        metric: 'errorCount',
        value: errorCountRef.current,
        threshold: finalThresholds.maxErrorCount,
        message: `Error count (${errorCountRef.current}) exceeded threshold (${finalThresholds.maxErrorCount})`,
        timestamp: new Date(),
      };
      
      addAlert(alert);
    }
  }, [enableAlerts, finalThresholds.maxErrorCount]);

  // Add performance alert
  const addAlert = useCallback((alert: PerformanceAlert) => {
    setAlerts(prev => [...prev.slice(-9), alert]); // Keep last 10 alerts
    
    if (enableAlerts) {
      toast({
        title: `Performance ${alert.type === 'error' ? 'Error' : 'Warning'}`,
        description: alert.message,
        variant: alert.type === 'error' ? 'destructive' : 'default',
      });
    }
  }, [enableAlerts, toast]);

  // Clear alerts
  const clearAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  // Get memory usage
  const updateMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      const memoryUsage = memInfo.usedJSHeapSize;
      
      setMetrics(prev => ({
        ...prev,
        memoryUsage,
        lastUpdate: new Date(),
      }));
      
      // Check threshold
      if (enableAlerts && memoryUsage > finalThresholds.maxMemoryUsage) {
        const alert: PerformanceAlert = {
          type: memoryUsage > finalThresholds.maxMemoryUsage * 1.5 ? 'error' : 'warning',
          metric: 'memoryUsage',
          value: memoryUsage,
          threshold: finalThresholds.maxMemoryUsage,
          message: `Memory usage (${(memoryUsage / 1024 / 1024).toFixed(1)}MB) exceeded threshold (${(finalThresholds.maxMemoryUsage / 1024 / 1024).toFixed(1)}MB)`,
          timestamp: new Date(),
        };
        
        addAlert(alert);
      }
    }
  }, [enableAlerts, finalThresholds.maxMemoryUsage, addAlert]);

  // Reset metrics
  const resetMetrics = useCallback(() => {
    errorCountRef.current = 0;
    setMetrics({
      renderTime: 0,
      dataLoadTime: 0,
      memoryUsage: 0,
      componentCount: 0,
      errorCount: 0,
      lastUpdate: new Date(),
    });
    clearAlerts();
  }, [clearAlerts]);

  // Get performance summary
  const getPerformanceSummary = useCallback(() => {
    const summary = {
      overall: 'good' as 'good' | 'warning' | 'poor',
      issues: [] as string[],
      recommendations: [] as string[],
    };
    
    if (metrics.renderTime > finalThresholds.maxRenderTime) {
      summary.overall = metrics.renderTime > finalThresholds.maxRenderTime * 2 ? 'poor' : 'warning';
      summary.issues.push('Slow render performance');
      summary.recommendations.push('Consider optimizing component rendering or reducing widget complexity');
    }
    
    if (metrics.dataLoadTime > finalThresholds.maxDataLoadTime) {
      summary.overall = metrics.dataLoadTime > finalThresholds.maxDataLoadTime * 2 ? 'poor' : 'warning';
      summary.issues.push('Slow data loading');
      summary.recommendations.push('Optimize data queries or implement caching');
    }
    
    if (metrics.memoryUsage > finalThresholds.maxMemoryUsage) {
      summary.overall = 'warning';
      summary.issues.push('High memory usage');
      summary.recommendations.push('Check for memory leaks or reduce data retention');
    }
    
    if (metrics.errorCount > finalThresholds.maxErrorCount) {
      summary.overall = 'poor';
      summary.issues.push('High error rate');
      summary.recommendations.push('Review error logs and fix underlying issues');
    }
    
    return summary;
  }, [metrics, finalThresholds]);

  // Periodic memory monitoring
  useEffect(() => {
    if (!isMonitoring) return;
    
    const interval = setInterval(updateMemoryUsage, 10000); // Check every 10 seconds
    
    return () => clearInterval(interval);
  }, [isMonitoring, updateMemoryUsage]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopMonitoring();
    };
  }, [stopMonitoring]);

  return {
    // State
    metrics,
    alerts,
    isMonitoring,
    thresholds: finalThresholds,
    
    // Actions
    startMonitoring,
    stopMonitoring,
    markRenderStart,
    markRenderEnd,
    markDataLoadStart,
    markDataLoadEnd,
    updateComponentCount,
    recordError,
    clearAlerts,
    resetMetrics,
    updateMemoryUsage,
    
    // Utilities
    getPerformanceSummary,
  };
};
