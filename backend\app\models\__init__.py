"""
Pydantic models for the Datagenius backend.

This package contains the Pydantic models for the Datagenius backend.
"""

import logging

# Configure logging
logger = logging.getLogger(__name__)

logger.info("Initializing models package")

# Import SQLAlchemy models to make them accessible to Alembic and the application
# This ensures Base.metadata is populated correctly.

# It's crucial that any file defining SQLAlchemy models inheriting from Base
# is imported here, or in a module imported here.

# Example: from .auth import User, Role (if User and Role are SQLAlchemy models in auth.py)

# Import all known model modules:
from . import admin
from . import agent
from . import auth
from . import cart
from . import chat
# Dashboard metrics models removed
from . import dashboard_customization # Assuming this contains DashboardSection, DashboardWidget models
from . import data_source # Already has FileDataSourceCreate etc., ensure SQLAlchemy models are here or imported
from . import feedback # Assuming this contains Feedback related SQLAlchemy models
from . import file # Assuming this contains File related SQLAlchemy models (if not already in database.py)
from . import knowledge_graph # Assuming this contains KG related SQLAlchemy models
from . import notifications # Assuming this contains Notification related SQLAlchemy models
from . import persona # Assuming this contains Persona related SQLAlchemy models (if not already in database.py)
from . import pricing # Contains PricingTier, Subscription etc.
from . import provider # Assuming this contains Provider related SQLAlchemy models
from . import purchase # Assuming this contains Purchase related SQLAlchemy models (if not already in database.py)
from . import reports # Contains ReportModel, ReportScheduleModel
from . import search # Contains UserSearchActivity
from . import task # Assuming this contains Task related SQLAlchemy models (if not already in database.py)
from . import workflow # Assuming this contains Workflow related SQLAlchemy models
# from . import notifications # Removed duplicate import, it's already imported above

# The following line is a safeguard to ensure the modules are "used" if linters complain.
# However, the act of importing them should be enough for SQLAlchemy model registration.
__all__ = [
    "admin", "agent", "auth", "cart", "chat", "dashboard", "dashboard_customization",
    "data_source", "feedback", "file", "knowledge_graph", "notifications", "persona", # Added notifications here
    "pricing", "provider", "purchase", "reports", "search", "task", "workflow",
    # Add specific model class names here if you want to expose them directly, e.g.:
    # "ReportModel", "ReportScheduleModel", "PricingTier", "UserSearchActivity"
]

# Logging to confirm models are being "touched" by this init
logger.info(f"Models package initialized. Imported modules: {__all__}")

# Note: The actual SQLAlchemy model classes (e.g., ReportModel) should inherit 
# from Base defined in app.database.py. This __init__.py file's role is to ensure
# Python loads and executes these model files, which registers them with Base.metadata.
