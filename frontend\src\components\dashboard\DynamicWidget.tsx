import React, { useState, useEffect, useRef } from "react";
import { X, RefreshCw, <PERSON>tings, AlertCircle, Wifi, WifiOff } from "lucide-react";
import { <PERSON>, CardH<PERSON>er, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { WidgetResponse, VisualizationType } from "@/types/dashboard-customization";
import { dashboardDataService, ChartData, KPIData, TableData } from "@/services/dashboard-data-service";
import { dashboardRealtimeService } from "@/services/dashboard-realtime-service";
import { ChartVisualization } from "@/components/visualizations/ChartVisualization";
import { TableVisualization } from "@/components/visualizations/TableVisualization";

interface DynamicWidgetProps {
  widget: WidgetResponse;
  onRemove: () => void;
  onUpdate?: (updates: Partial<WidgetResponse>) => void;
  onCustomize?: (widget: WidgetResponse) => void;
}

export const DynamicWidget = React.forwardRef<HTMLDivElement, DynamicWidgetProps>(
  ({ widget, onRemove, onUpdate, onCustomize }, ref) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<ChartData | KPIData | TableData | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRealTimeConnected, setIsRealTimeConnected] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const intersectionObserverRef = useRef<IntersectionObserver | null>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (ref && typeof ref === 'object' && ref.current) {
      intersectionObserverRef.current = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          setIsVisible(entry.isIntersecting);
        },
        {
          threshold: 0.1,
          rootMargin: '50px'
        }
      );

      intersectionObserverRef.current.observe(ref.current);
    }

    return () => {
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }
    };
  }, [ref]);

  // Load widget data only when visible
  useEffect(() => {
    if (isVisible && widget.data_config?.dataSourceId) {
      loadWidgetData();
    }
  }, [isVisible, widget.data_config]);

  // Set up real-time subscription
  useEffect(() => {
    if (widget.data_config?.dataSourceId && widget.refresh_interval && widget.refresh_interval > 0) {
      const unsubscribe = dashboardRealtimeService.subscribe({
        widgetId: widget.id,
        dataSourceId: widget.data_config.dataSourceId,
        config: widget.data_config,
        refreshInterval: widget.refresh_interval,
        lastUpdate: new Date(),
        onUpdate: (result) => {
          transformAndSetData(result);
          setLastRefresh(new Date());
          setIsRealTimeConnected(true);
        },
        onError: (errorMessage) => {
          setError(errorMessage);
          setIsRealTimeConnected(false);
        },
      });

      unsubscribeRef.current = unsubscribe;
      setIsRealTimeConnected(true);

      return () => {
        if (unsubscribeRef.current) {
          unsubscribeRef.current();
          unsubscribeRef.current = null;
        }
        setIsRealTimeConnected(false);
      };
    }
  }, [widget.id, widget.data_config, widget.refresh_interval]);

  const transformAndSetData = (queryResult: any) => {
    if (queryResult.error) {
      setError(queryResult.error);
      return;
    }

    // Transform data based on widget type
    let transformedData: ChartData | KPIData | TableData;

    switch (widget.widget_type) {
      case VisualizationType.CHART:
        const chartType = widget.visualization_config?.chartType || 'line';
        const labelColumn = widget.visualization_config?.labelColumn || queryResult.columns[0];
        const valueColumns = widget.visualization_config?.valueColumns || [queryResult.columns[1]];

        transformedData = dashboardDataService.transformToChartData(
          queryResult,
          chartType,
          labelColumn,
          valueColumns
        );
        break;

      case VisualizationType.KPI:
        const valueColumn = widget.visualization_config?.valueColumn || queryResult.columns[1];
        const labelColumn2 = widget.visualization_config?.labelColumn || queryResult.columns[0];
        const format = widget.visualization_config?.format || 'number';

        transformedData = dashboardDataService.transformToKPIData(
          queryResult,
          valueColumn,
          labelColumn2,
          format
        );
        break;

      case VisualizationType.TABLE:
        const pageSize = widget.visualization_config?.pageSize || 10;
        const page = widget.visualization_config?.page || 1;

        transformedData = dashboardDataService.transformToTableData(
          queryResult,
          pageSize,
          page
        );
        break;

      default:
        transformedData = dashboardDataService.transformToTableData(queryResult);
    }

    setData(transformedData);
  };

  const loadWidgetData = async () => {
    if (!widget.data_config?.dataSourceId) {
      setError('No data source configured for this widget');
      return;
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController();

    setIsLoading(true);
    setError(null);
    setLoadingProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setLoadingProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      const queryResult = await dashboardDataService.executeQuery(
        widget.data_config.dataSourceId,
        widget.data_config,
        {
          signal: abortControllerRef.current.signal,
          timeout: 30000, // 30 second timeout
          onProgress: (progress: number) => {
            setLoadingProgress(progress);
          }
        }
      );

      clearInterval(progressInterval);
      setLoadingProgress(100);

      // Check if request was aborted
      if (abortControllerRef.current.signal.aborted) {
        return;
      }

      transformAndSetData(queryResult);
      setLastRefresh(new Date());
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        // Request was cancelled, don't show error
        return;
      }

      const errorMessage = err instanceof Error ? err.message : 'Failed to load data';
      setError(errorMessage);

      // Log error for monitoring
      console.error(`Widget ${widget.id} data loading failed:`, err);
    } finally {
      setIsLoading(false);
      setLoadingProgress(0);
      abortControllerRef.current = null;
    }
  };

  const handleRefresh = () => {
    if (!isLoading) {
      loadWidgetData();
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cancel any pending requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Cleanup real-time subscription
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }

      // Cleanup intersection observer
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }

      // Clear data to free memory
      setData(null);
    };
  }, []);

  const renderContent = () => {
    if (error) {
      const isDataSourceError = error.includes('No data source configured');

      return (
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center space-x-2">
                {widget.title}
                {widget.refresh_interval && widget.refresh_interval > 0 && (
                  <div className="flex items-center space-x-1">
                    {isRealTimeConnected ? (
                      <Wifi className="h-3 w-3 text-green-500" title="Real-time updates active" />
                    ) : (
                      <WifiOff className="h-3 w-3 text-gray-400" title="Real-time updates inactive" />
                    )}
                  </div>
                )}
              </span>
              <Badge variant={isDataSourceError ? "secondary" : "destructive"}>
                {isDataSourceError ? "Configuration Required" : "Error"}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-32 text-center">
              <div className="space-y-3">
                <AlertCircle className={`h-8 w-8 mx-auto ${isDataSourceError ? 'text-amber-500' : 'text-destructive'}`} />
                <div className="space-y-1">
                  <p className={`text-sm font-medium ${isDataSourceError ? 'text-amber-600' : 'text-destructive'}`}>
                    {isDataSourceError ? 'Configuration Required' : 'Error Loading Data'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {isDataSourceError
                      ? 'This widget needs a data source to display content. Configure it in widget settings.'
                      : error
                    }
                  </p>
                </div>
                {isDataSourceError ? (
                  <Button variant="outline" size="sm" onClick={() => onCustomize?.(widget)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Widget
                  </Button>
                ) : (
                  <Button variant="outline" size="sm" onClick={handleRefresh}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    if (isLoading) {
      return (
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center space-x-2">
                {widget.title}
                {widget.refresh_interval && widget.refresh_interval > 0 && (
                  <div className="flex items-center space-x-1">
                    {isRealTimeConnected ? (
                      <Wifi className="h-3 w-3 text-green-500" title="Real-time updates active" />
                    ) : (
                      <WifiOff className="h-3 w-3 text-gray-400" title="Real-time updates inactive" />
                    )}
                  </div>
                )}
              </span>
              <Badge variant="secondary">Loading</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-32">
              <div className="space-y-2 text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
                <p className="text-sm text-muted-foreground">Loading data...</p>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Render based on widget type
    switch (widget.widget_type) {
      case VisualizationType.CHART:
        return (
          <ChartVisualization
            visualization={{
              type: 'chart',
              title: widget.title,
              data: data as ChartData,
              config: widget.visualization_config,
            }}
          />
        );

      case VisualizationType.TABLE:
        return (
          <TableVisualization
            visualization={{
              type: 'table',
              title: widget.title,
              data: data as TableData,
              config: widget.visualization_config,
            }}
          />
        );

      case VisualizationType.KPI:
        const kpiData = data as KPIData;
        return (
          <Card className="h-full">
            <CardHeader>
              <CardTitle>{widget.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center space-y-2">
                <div className="text-3xl font-bold">{kpiData?.value || 0}</div>
                <div className="text-sm text-muted-foreground">{kpiData?.label}</div>
                {kpiData?.change !== undefined && (
                  <div className={`text-sm ${
                    kpiData.changeType === 'increase' ? 'text-green-600' :
                    kpiData.changeType === 'decrease' ? 'text-red-600' :
                    'text-gray-600'
                  }`}>
                    {kpiData.change > 0 ? '+' : ''}{kpiData.change.toFixed(1)}%
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );

      default:
        return (
          <Card className="h-full">
            <CardHeader>
              <CardTitle>{widget.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-32 text-center">
                <p className="text-muted-foreground">
                  Widget type "{widget.widget_type}" not yet supported
                </p>
              </div>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div ref={ref} className="relative h-full">
      {/* Widget Actions */}
      <div className="absolute top-2 right-2 z-10 flex space-x-1">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 rounded-full bg-white/90 shadow-sm hover:bg-gray-100"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 rounded-full bg-white/90 shadow-sm hover:bg-gray-100"
          onClick={onRemove}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Widget Content */}
      <div className="h-full">
        {renderContent()}
      </div>

      {/* Last Refresh Indicator */}
      {lastRefresh && !isLoading && (
        <div className="absolute bottom-2 left-2 text-xs text-muted-foreground bg-white/90 px-2 py-1 rounded">
          Updated {lastRefresh.toLocaleTimeString()}
        </div>
      )}
    </div>
  );
});

DynamicWidget.displayName = 'DynamicWidget';
