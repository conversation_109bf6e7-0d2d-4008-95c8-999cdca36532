"""
Production-Ready Provider Management System.

This module replaces hardcoded provider configurations with a dynamic,
secure, and scalable provider management system.
"""

import logging
import os
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio
import aiohttp
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ProviderStatus(Enum):
    """Provider availability status."""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"
    UNKNOWN = "unknown"


@dataclass
class ProviderConfig:
    """Provider configuration data structure."""
    id: str
    name: str
    display_name: str
    endpoint: str
    api_key_env_var: str
    default_model: str
    supported_models: List[str]
    max_tokens: int
    supports_streaming: bool
    rate_limit_rpm: int
    rate_limit_tpm: int
    cost_per_1k_tokens: float
    priority: int  # Lower number = higher priority
    health_check_endpoint: Optional[str] = None
    requires_auth: bool = True


class ProductionProviderManager:
    """Production-ready provider management with health monitoring and failover."""
    
    def __init__(self):
        self.providers = self._initialize_providers()
        self.provider_status = {}
        self.last_health_check = {}
        self.health_check_interval = 300  # 5 minutes
        self.rate_limit_tracking = {}
        
    def _initialize_providers(self) -> Dict[str, ProviderConfig]:
        """Initialize provider configurations."""
        return {
            "groq": ProviderConfig(
                id="groq",
                name="groq",
                display_name="Groq",
                endpoint="https://api.groq.com/openai/v1",
                api_key_env_var="GROQ_API_KEY",
                default_model="llama-3.1-70b-versatile",
                supported_models=[
                    "llama-3.1-70b-versatile",
                    "llama-3.1-8b-instant",
                    "mixtral-8x7b-32768",
                    "gemma2-9b-it"
                ],
                max_tokens=32768,
                supports_streaming=True,
                rate_limit_rpm=30,
                rate_limit_tpm=6000,
                cost_per_1k_tokens=0.0,  # Free tier
                priority=1,
                health_check_endpoint="https://api.groq.com/openai/v1/models"
            ),
            "openai": ProviderConfig(
                id="openai",
                name="openai",
                display_name="OpenAI",
                endpoint="https://api.openai.com/v1",
                api_key_env_var="OPENAI_API_KEY",
                default_model="gpt-4o-mini",
                supported_models=[
                    "gpt-4o",
                    "gpt-4o-mini",
                    "gpt-4-turbo",
                    "gpt-3.5-turbo"
                ],
                max_tokens=128000,
                supports_streaming=True,
                rate_limit_rpm=500,
                rate_limit_tpm=30000,
                cost_per_1k_tokens=0.15,
                priority=2,
                health_check_endpoint="https://api.openai.com/v1/models"
            ),
            "anthropic": ProviderConfig(
                id="anthropic",
                name="anthropic",
                display_name="Anthropic",
                endpoint="https://api.anthropic.com/v1",
                api_key_env_var="ANTHROPIC_API_KEY",
                default_model="claude-3-5-sonnet-20241022",
                supported_models=[
                    "claude-3-5-sonnet-20241022",
                    "claude-3-5-haiku-20241022",
                    "claude-3-opus-20240229"
                ],
                max_tokens=200000,
                supports_streaming=True,
                rate_limit_rpm=50,
                rate_limit_tpm=40000,
                cost_per_1k_tokens=3.0,
                priority=3,
                health_check_endpoint="https://api.anthropic.com/v1/messages"
            ),
            "gemini": ProviderConfig(
                id="gemini",
                name="gemini",
                display_name="Google Gemini",
                endpoint="https://generativelanguage.googleapis.com/v1beta",
                api_key_env_var="GEMINI_API_KEY",
                default_model="gemini-1.5-pro",
                supported_models=[
                    "gemini-1.5-pro",
                    "gemini-1.5-flash",
                    "gemini-1.0-pro"
                ],
                max_tokens=1048576,
                supports_streaming=True,
                rate_limit_rpm=60,
                rate_limit_tpm=32000,
                cost_per_1k_tokens=1.25,
                priority=4
            ),
            "openrouter": ProviderConfig(
                id="openrouter",
                name="openrouter",
                display_name="OpenRouter",
                endpoint="https://openrouter.ai/api/v1",
                api_key_env_var="OPENROUTER_API_KEY",
                default_model="anthropic/claude-3.5-sonnet",
                supported_models=[
                    "anthropic/claude-3.5-sonnet",
                    "openai/gpt-4o",
                    "meta-llama/llama-3.1-70b-instruct",
                    "google/gemini-pro-1.5"
                ],
                max_tokens=200000,
                supports_streaming=True,
                rate_limit_rpm=200,
                rate_limit_tpm=40000,
                cost_per_1k_tokens=2.0,
                priority=5,
                health_check_endpoint="https://openrouter.ai/api/v1/models"
            ),
            "ollama": ProviderConfig(
                id="ollama",
                name="ollama",
                display_name="Ollama (Local)",
                endpoint=os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434"),
                api_key_env_var="",  # No API key needed
                default_model="llama3.2:3b",
                supported_models=[
                    "llama3.2:3b",
                    "llama3.1:8b",
                    "mistral:7b",
                    "codellama:7b"
                ],
                max_tokens=4096,
                supports_streaming=True,
                rate_limit_rpm=1000,  # Local, so higher limits
                rate_limit_tpm=100000,
                cost_per_1k_tokens=0.0,  # Free local
                priority=6,
                health_check_endpoint=f"{os.getenv('OLLAMA_ENDPOINT', 'http://localhost:11434')}/api/tags",
                requires_auth=False
            )
        }
    
    async def get_available_providers(self, force_check: bool = False) -> List[ProviderConfig]:
        """Get list of available providers with health checking."""
        available_providers = []
        
        for provider_id, provider_config in self.providers.items():
            # Check if we need to perform health check
            if force_check or self._needs_health_check(provider_id):
                await self._check_provider_health(provider_id)
            
            # Check if provider is available
            status = self.provider_status.get(provider_id, ProviderStatus.UNKNOWN)
            if status == ProviderStatus.AVAILABLE:
                available_providers.append(provider_config)
        
        # Sort by priority (lower number = higher priority)
        available_providers.sort(key=lambda p: p.priority)
        
        logger.info(f"Available providers: {[p.id for p in available_providers]}")
        return available_providers
    
    async def get_best_provider(
        self, 
        requirements: Optional[Dict[str, Any]] = None
    ) -> Optional[ProviderConfig]:
        """Get the best available provider based on requirements."""
        available_providers = await self.get_available_providers()
        
        if not available_providers:
            logger.warning("No providers available")
            return None
        
        if not requirements:
            return available_providers[0]  # Return highest priority available
        
        # Filter providers based on requirements
        filtered_providers = []
        
        for provider in available_providers:
            # Check model requirement
            required_model = requirements.get("model")
            if required_model and required_model not in provider.supported_models:
                continue
            
            # Check max tokens requirement
            required_tokens = requirements.get("max_tokens", 0)
            if required_tokens > provider.max_tokens:
                continue
            
            # Check streaming requirement
            requires_streaming = requirements.get("streaming", False)
            if requires_streaming and not provider.supports_streaming:
                continue
            
            # Check cost requirement
            max_cost = requirements.get("max_cost_per_1k_tokens")
            if max_cost is not None and provider.cost_per_1k_tokens > max_cost:
                continue
            
            filtered_providers.append(provider)
        
        if not filtered_providers:
            logger.warning(f"No providers match requirements: {requirements}")
            return available_providers[0]  # Fallback to best available
        
        return filtered_providers[0]  # Return best matching provider
    
    async def get_provider_config(
        self, 
        provider_id: str, 
        user_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Get complete provider configuration including API key."""
        if provider_id not in self.providers:
            logger.error(f"Unknown provider: {provider_id}")
            return None
        
        provider = self.providers[provider_id]
        
        # Get API key
        api_key = None
        if provider.requires_auth:
            # Try user-specific API key first (from database)
            if user_id:
                api_key = await self._get_user_api_key(user_id, provider_id)
            
            # Fall back to environment variable
            if not api_key and provider.api_key_env_var:
                api_key = os.getenv(provider.api_key_env_var)
            
            if not api_key:
                logger.warning(f"No API key available for provider: {provider_id}")
                return None
        
        return {
            "id": provider.id,
            "name": provider.name,
            "display_name": provider.display_name,
            "endpoint": provider.endpoint,
            "api_key": api_key,
            "default_model": provider.default_model,
            "supported_models": provider.supported_models,
            "max_tokens": provider.max_tokens,
            "supports_streaming": provider.supports_streaming,
            "rate_limits": {
                "requests_per_minute": provider.rate_limit_rpm,
                "tokens_per_minute": provider.rate_limit_tpm
            },
            "cost_per_1k_tokens": provider.cost_per_1k_tokens
        }
    
    async def _check_provider_health(self, provider_id: str) -> ProviderStatus:
        """Check provider health and availability."""
        if provider_id not in self.providers:
            return ProviderStatus.ERROR
        
        provider = self.providers[provider_id]
        
        try:
            # Check if API key is available (if required)
            if provider.requires_auth:
                api_key = os.getenv(provider.api_key_env_var)
                if not api_key:
                    self.provider_status[provider_id] = ProviderStatus.UNAVAILABLE
                    logger.warning(f"No API key for provider {provider_id}")
                    return ProviderStatus.UNAVAILABLE
            
            # Perform health check if endpoint is available
            if provider.health_check_endpoint:
                status = await self._perform_health_check(provider)
            else:
                # If no health check endpoint, assume available if API key exists
                status = ProviderStatus.AVAILABLE
            
            self.provider_status[provider_id] = status
            self.last_health_check[provider_id] = datetime.now()
            
            logger.info(f"Provider {provider_id} health check: {status.value}")
            return status
            
        except Exception as e:
            logger.error(f"Health check failed for provider {provider_id}: {e}")
            self.provider_status[provider_id] = ProviderStatus.ERROR
            return ProviderStatus.ERROR
    
    async def _perform_health_check(self, provider: ProviderConfig) -> ProviderStatus:
        """Perform actual HTTP health check."""
        try:
            headers = {}
            if provider.requires_auth:
                api_key = os.getenv(provider.api_key_env_var)
                if provider.id == "anthropic":
                    headers["x-api-key"] = api_key
                elif provider.id == "gemini":
                    # Gemini uses API key in URL
                    pass
                else:
                    headers["Authorization"] = f"Bearer {api_key}"
            
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(
                    provider.health_check_endpoint, 
                    headers=headers
                ) as response:
                    if response.status == 200:
                        return ProviderStatus.AVAILABLE
                    elif response.status == 429:
                        return ProviderStatus.RATE_LIMITED
                    else:
                        return ProviderStatus.ERROR
                        
        except asyncio.TimeoutError:
            logger.warning(f"Health check timeout for provider {provider.id}")
            return ProviderStatus.ERROR
        except Exception as e:
            logger.error(f"Health check error for provider {provider.id}: {e}")
            return ProviderStatus.ERROR
    
    def _needs_health_check(self, provider_id: str) -> bool:
        """Check if provider needs health check."""
        if provider_id not in self.last_health_check:
            return True
        
        last_check = self.last_health_check[provider_id]
        return datetime.now() - last_check > timedelta(seconds=self.health_check_interval)
    
    async def _get_user_api_key(self, user_id: str, provider_id: str) -> Optional[str]:
        """Get user-specific API key from database."""
        try:
            # This would integrate with the database to get user-specific API keys
            # For now, return None to fall back to environment variables
            return None
        except Exception as e:
            logger.error(f"Error getting user API key: {e}")
            return None
    
    def get_provider_status_summary(self) -> Dict[str, Any]:
        """Get summary of all provider statuses."""
        summary = {
            "total_providers": len(self.providers),
            "available_providers": 0,
            "unavailable_providers": 0,
            "error_providers": 0,
            "provider_details": {}
        }
        
        for provider_id, provider in self.providers.items():
            status = self.provider_status.get(provider_id, ProviderStatus.UNKNOWN)
            
            if status == ProviderStatus.AVAILABLE:
                summary["available_providers"] += 1
            elif status == ProviderStatus.UNAVAILABLE:
                summary["unavailable_providers"] += 1
            elif status == ProviderStatus.ERROR:
                summary["error_providers"] += 1
            
            summary["provider_details"][provider_id] = {
                "name": provider.display_name,
                "status": status.value,
                "last_check": self.last_health_check.get(provider_id),
                "priority": provider.priority,
                "cost_per_1k_tokens": provider.cost_per_1k_tokens
            }
        
        return summary


# Global instance
production_provider_manager = ProductionProviderManager()
