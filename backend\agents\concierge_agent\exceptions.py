"""
Exception classes for the Concierge Agent.

This module provides specialized exception handling for concierge agent operations,
including persona routing, validation, and conversation management.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)


class ConciergeAgentException(Exception):
    """Base exception class for Concierge Agent errors."""
    
    def __init__(
        self,
        message: str,
        error_code: str = "CONCIERGE_ERROR",
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
        self.user_message = user_message or "I encountered an issue processing your request. Please try again."
        self.timestamp = datetime.utcnow()
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error": True,
            "error_code": self.error_code,
            "message": self.message,
            "user_message": self.user_message,
            "context": self.context,
            "timestamp": self.timestamp.isoformat()
        }


class PersonaNotAvailableError(ConciergeAgentException):
    """Raised when a requested persona is not available."""
    
    def __init__(
        self,
        persona_id: str,
        reason: str = "not_available",
        available_personas: Optional[List[str]] = None
    ):
        message = f"Persona '{persona_id}' is not available: {reason}"
        user_message = f"The {persona_id} persona is currently unavailable. Let me suggest some alternatives."
        
        context = {
            "persona_id": persona_id,
            "reason": reason,
            "available_personas": available_personas or []
        }
        
        super().__init__(
            message=message,
            error_code="PERSONA_NOT_AVAILABLE",
            context=context,
            user_message=user_message
        )


class ValidationError(ConciergeAgentException):
    """Raised when input validation fails."""
    
    def __init__(
        self,
        field: str,
        value: Any,
        expected: str,
        validation_errors: Optional[List[str]] = None
    ):
        message = f"Validation failed for field '{field}': expected {expected}, got {value}"
        user_message = "I need some additional information to help you better. Could you please clarify your request?"
        
        context = {
            "field": field,
            "value": str(value),
            "expected": expected,
            "validation_errors": validation_errors or []
        }
        
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            context=context,
            user_message=user_message
        )


class LLMAnalysisError(ConciergeAgentException):
    """Raised when LLM analysis fails."""
    
    def __init__(
        self,
        analysis_type: str,
        llm_response: Optional[str] = None,
        parsing_error: Optional[str] = None
    ):
        message = f"LLM analysis failed for {analysis_type}"
        user_message = "I'm having trouble understanding your request. Could you rephrase it?"
        
        context = {
            "analysis_type": analysis_type,
            "llm_response": llm_response,
            "parsing_error": parsing_error
        }
        
        super().__init__(
            message=message,
            error_code="LLM_ANALYSIS_ERROR",
            context=context,
            user_message=user_message
        )


class ConversationContextError(ConciergeAgentException):
    """Raised when conversation context management fails."""
    
    def __init__(
        self,
        conversation_id: str,
        operation: str,
        details: Optional[str] = None
    ):
        message = f"Conversation context error for {conversation_id}: {operation}"
        user_message = "I'm having trouble accessing our conversation history. Let's start fresh."
        
        context = {
            "conversation_id": conversation_id,
            "operation": operation,
            "details": details
        }
        
        super().__init__(
            message=message,
            error_code="CONVERSATION_CONTEXT_ERROR",
            context=context,
            user_message=user_message
        )


class PersonaRoutingError(ConciergeAgentException):
    """Raised when persona routing fails."""
    
    def __init__(
        self,
        source_persona: str,
        target_persona: str,
        routing_reason: str,
        available_alternatives: Optional[List[str]] = None
    ):
        message = f"Failed to route from {source_persona} to {target_persona}: {routing_reason}"
        user_message = "I'm having trouble connecting you to the right specialist. Let me try a different approach."
        
        context = {
            "source_persona": source_persona,
            "target_persona": target_persona,
            "routing_reason": routing_reason,
            "available_alternatives": available_alternatives or []
        }
        
        super().__init__(
            message=message,
            error_code="PERSONA_ROUTING_ERROR",
            context=context,
            user_message=user_message
        )


class ConciergeExceptionHandler:
    """Centralized exception handler for the Concierge Agent."""
    
    def __init__(self):
        self.error_counts = {}
        self.last_errors = []
        self.max_error_history = 100
        
    def handle_exception(
        self,
        exception: Exception,
        context: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Handle an exception and return appropriate response.
        
        Args:
            exception: The exception to handle
            context: Additional context information
            user_id: User ID for tracking
            conversation_id: Conversation ID for tracking
            
        Returns:
            Dictionary with error response
        """
        # Convert to ConciergeAgentException if needed
        if not isinstance(exception, ConciergeAgentException):
            concierge_exception = self._convert_exception(exception, context)
        else:
            concierge_exception = exception
        
        # Log the error
        self._log_error(concierge_exception, user_id, conversation_id)
        
        # Track error for monitoring
        self._track_error(concierge_exception)
        
        # Generate response
        return self._generate_error_response(concierge_exception)
    
    def _convert_exception(
        self,
        exception: Exception,
        context: Optional[Dict[str, Any]] = None
    ) -> ConciergeAgentException:
        """Convert generic exception to ConciergeAgentException."""
        error_message = str(exception)
        
        # Pattern matching for common errors
        if "validation" in error_message.lower():
            return ValidationError(
                field="unknown",
                value=error_message,
                expected="valid input"
            )
        elif "persona" in error_message.lower() and "not" in error_message.lower():
            return PersonaNotAvailableError(
                persona_id="unknown",
                reason=error_message
            )
        elif "llm" in error_message.lower() or "analysis" in error_message.lower():
            return LLMAnalysisError(
                analysis_type="unknown",
                parsing_error=error_message
            )
        else:
            return ConciergeAgentException(
                message=error_message,
                context=context
            )
    
    def _log_error(
        self,
        exception: ConciergeAgentException,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None
    ):
        """Log error with appropriate level and context."""
        log_context = {
            "error_code": exception.error_code,
            "user_id": user_id,
            "conversation_id": conversation_id,
            "context": exception.context
        }
        
        if exception.error_code in ["VALIDATION_ERROR", "PERSONA_NOT_AVAILABLE"]:
            logger.warning(f"Concierge warning: {exception.message}", extra=log_context)
        else:
            logger.error(f"Concierge error: {exception.message}", extra=log_context)
    
    def _track_error(self, exception: ConciergeAgentException):
        """Track error for monitoring and analytics."""
        error_code = exception.error_code
        self.error_counts[error_code] = self.error_counts.get(error_code, 0) + 1
        
        # Keep recent error history
        self.last_errors.append({
            "timestamp": exception.timestamp,
            "error_code": error_code,
            "message": exception.message
        })
        
        # Trim history if too long
        if len(self.last_errors) > self.max_error_history:
            self.last_errors = self.last_errors[-self.max_error_history:]
    
    def _generate_error_response(self, exception: ConciergeAgentException) -> Dict[str, Any]:
        """Generate user-friendly error response."""
        return {
            "message": exception.user_message,
            "metadata": {
                "error": True,
                "error_code": exception.error_code,
                "suggestions": self._get_error_suggestions(exception),
                "can_retry": self._can_retry(exception)
            },
            "success": False
        }
    
    def _get_error_suggestions(self, exception: ConciergeAgentException) -> List[str]:
        """Get suggestions based on error type."""
        suggestions = []
        
        if isinstance(exception, PersonaNotAvailableError):
            if exception.context.get("available_personas"):
                suggestions.append("Try selecting from available personas")
                suggestions.append("Check your persona purchases in the marketplace")
        elif isinstance(exception, ValidationError):
            suggestions.append("Please provide more specific details")
            suggestions.append("Try rephrasing your request")
        elif isinstance(exception, LLMAnalysisError):
            suggestions.append("Try using simpler language")
            suggestions.append("Break down your request into smaller parts")
        
        return suggestions
    
    def _can_retry(self, exception: ConciergeAgentException) -> bool:
        """Determine if the operation can be retried."""
        non_retryable_errors = ["VALIDATION_ERROR", "PERSONA_NOT_AVAILABLE"]
        return exception.error_code not in non_retryable_errors
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics for monitoring."""
        return {
            "error_counts": self.error_counts,
            "recent_errors": self.last_errors[-10:],  # Last 10 errors
            "total_errors": sum(self.error_counts.values())
        }
