import { useEffect, useState, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";

const GoogleCallback = () => {
  const { handleGoogleCallback } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const processedRef = useRef(false);

  useEffect(() => {
    const processCallback = async () => {
      // Prevent duplicate processing
      if (processedRef.current) {
        console.log('Google callback already processed, skipping');
        return;
      }

      console.log('Processing Google callback...');
      processedRef.current = true;

      // Parse the URL for code and state parameters
      const searchParams = new URLSearchParams(location.search);
      const code = searchParams.get("code");
      const state = searchParams.get("state");
      const errorParam = searchParams.get("error");

      if (errorParam) {
        console.error(`Google OAuth error: ${errorParam}`);
        setError(`Authentication error: ${errorParam}`);
        setTimeout(() => navigate("/login"), 3000);
        return;
      }

      if (!code) {
        console.error('No authorization code received');
        setError("No authorization code received");
        setTimeout(() => navigate("/login"), 3000);
        return;
      }

      try {
        console.log(`Processing Google OAuth code: ${code.substring(0, 10)}...`);
        if (state) {
          console.log(`With state parameter: ${state.substring(0, 10)}...`);
        }

        // Call the handler with the code and state
        await handleGoogleCallback(code, state || undefined);
        // Successful login will redirect in the handler
      } catch (err) {
        console.error('Error during Google callback processing:', err);
        setError(err instanceof Error ? err.message : "Authentication failed");
        setTimeout(() => navigate("/login"), 3000);
      }
    };

    processCallback();

    // Cleanup function
    return () => {
      console.log('Google callback component unmounting');
    };
  }, []);  // Empty dependency array to run only once

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      {error ? (
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
          <p className="mb-4">{error}</p>
          <p>Redirecting to login page...</p>
        </div>
      ) : (
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">Completing Authentication</h1>
          <p>Please wait while we complete the authentication process...</p>
        </div>
      )}
    </div>
  );
};

export default GoogleCallback;
