#!/usr/bin/env python3
"""
Test script to verify the enhanced persona switching UI integration.
This tests that the concierge agent returns proper metadata for interactive persona buttons.
"""

import asyncio
import sys
import os
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_persona_ui_metadata():
    """Test that the concierge agent returns proper metadata for UI rendering."""
    try:
        print("🧪 Testing Enhanced Persona UI Integration...")
        
        from backend.agents.concierge_agent.concierge import ConciergeAgent
        
        concierge = ConciergeAgent()
        await concierge._initialize({})
        
        # Test persona query that should trigger interactive UI
        test_query = "what personas are available"
        print(f"\n📝 Testing query: '{test_query}'")
        
        # Process the message
        result = await concierge.process_message(
            message=test_query,
            user_id="1",
            conversation_id="test_conv",
            context={"conversation_history": []}
        )
        
        if result.get("success", False):
            response = result.get("message", "")
            metadata = result.get("metadata", {})
            
            print("✅ Query processed successfully")
            print(f"   Response length: {len(response)} characters")
            print(f"   Intent: {metadata.get('intent', 'unknown')}")
            
            # Check for persona recommendation metadata
            is_persona_rec = metadata.get("is_persona_recommendation", False)
            recommended_personas = metadata.get("recommended_personas", [])
            persona_query_response = metadata.get("persona_query_response", False)
            
            print(f"   Is persona recommendation: {is_persona_rec}")
            print(f"   Has recommended personas: {len(recommended_personas) > 0}")
            print(f"   Is persona query response: {persona_query_response}")
            
            if is_persona_rec and recommended_personas:
                print(f"\n📋 Found {len(recommended_personas)} interactive personas:")
                
                # Group by category
                owned = [p for p in recommended_personas if p.get('category') == 'owned']
                free = [p for p in recommended_personas if p.get('category') == 'free']
                paid = [p for p in recommended_personas if p.get('category') == 'paid']
                
                print(f"   🎯 Owned personas: {len(owned)}")
                for persona in owned:
                    print(f"      - {persona.get('name', 'Unknown')} (ID: {persona.get('id', 'N/A')})")
                    print(f"        Available: {persona.get('is_available', False)}")
                    print(f"        Capabilities: {', '.join(persona.get('capabilities', [])[:3])}")
                
                print(f"   🆓 Free personas: {len(free)}")
                for persona in free:
                    print(f"      - {persona.get('name', 'Unknown')} (ID: {persona.get('id', 'N/A')})")
                    print(f"        Available: {persona.get('is_available', False)}")
                    print(f"        Rating: {persona.get('rating', 0.0):.1f}/5.0")
                
                print(f"   💎 Paid personas: {len(paid)}")
                for persona in paid:
                    print(f"      - {persona.get('name', 'Unknown')} (ID: {persona.get('id', 'N/A')})")
                    print(f"        Price: ${persona.get('price', 0.0):.2f}")
                    print(f"        Available: {persona.get('is_available', False)}")
                
                # Test metadata structure
                print(f"\n🔍 Metadata validation:")
                required_fields = ['id', 'name', 'description', 'category', 'is_available']
                all_valid = True
                
                for i, persona in enumerate(recommended_personas):
                    missing_fields = [field for field in required_fields if field not in persona]
                    if missing_fields:
                        print(f"   ❌ Persona {i+1} missing fields: {missing_fields}")
                        all_valid = False
                
                if all_valid:
                    print("   ✅ All personas have required metadata fields")
                
                # Test that the response includes proper UI instructions
                ui_instructions = [
                    "Click any persona below to start chatting",
                    "These personas are free to use - click to start chatting",
                    "Purchase these personas from the marketplace"
                ]
                
                has_ui_instructions = any(instruction in response for instruction in ui_instructions)
                print(f"   UI instructions present: {has_ui_instructions}")
                
                if has_ui_instructions:
                    print("   ✅ Response includes UI interaction instructions")
                else:
                    print("   ⚠️  Response may not include clear UI instructions")
                
                print("\n🎉 Enhanced persona UI integration test completed successfully!")
                print("   Frontend should now render interactive persona buttons grouped by category")
                return True
            else:
                print("   ❌ No persona recommendation metadata found")
                return False
        else:
            print(f"   ❌ Query failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ui_response_format():
    """Test that the response format is compatible with frontend rendering."""
    try:
        print("\n🎨 Testing UI Response Format Compatibility...")
        
        from backend.agents.concierge_agent.concierge import ConciergeAgent
        
        concierge = ConciergeAgent()
        await concierge._initialize({})
        
        # Test the handle_persona_query method directly
        result = await concierge.handle_persona_query("show me available personas", "1")
        
        print("✅ Direct persona query method test:")
        print(f"   Result type: {type(result)}")
        
        if isinstance(result, dict):
            message = result.get("message", "")
            metadata = result.get("metadata", {})
            
            print(f"   Has message: {len(message) > 0}")
            print(f"   Has metadata: {len(metadata) > 0}")
            
            # Check metadata structure
            expected_metadata_keys = [
                "is_persona_recommendation",
                "recommended_personas", 
                "persona_query_response"
            ]
            
            missing_keys = [key for key in expected_metadata_keys if key not in metadata]
            if missing_keys:
                print(f"   ❌ Missing metadata keys: {missing_keys}")
                return False
            else:
                print("   ✅ All expected metadata keys present")
            
            # Check personas structure
            personas = metadata.get("recommended_personas", [])
            if personas:
                sample_persona = personas[0]
                persona_keys = list(sample_persona.keys())
                print(f"   Sample persona keys: {persona_keys}")
                
                required_persona_keys = ["id", "name", "category", "is_available"]
                missing_persona_keys = [key for key in required_persona_keys if key not in sample_persona]
                
                if missing_persona_keys:
                    print(f"   ❌ Missing persona keys: {missing_persona_keys}")
                    return False
                else:
                    print("   ✅ Persona objects have required structure")
            
            print("\n🎉 UI response format is compatible with frontend!")
            return True
        else:
            print(f"   ❌ Expected dict, got {type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ UI format test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 Starting Enhanced Persona UI Integration Tests\n")
        
        # Test the metadata structure
        metadata_success = await test_persona_ui_metadata()
        
        if metadata_success:
            # Test the response format
            format_success = await test_ui_response_format()
            
            if format_success:
                print("\n✅ All tests passed! The concierge agent now provides:")
                print("   🎯 Interactive persona buttons grouped by category")
                print("   🆓 Clear distinction between owned, free, and paid personas")
                print("   💎 Direct links to marketplace for paid personas")
                print("   📱 Mobile-friendly UI with proper metadata")
                print("   🔄 Seamless persona switching functionality")
            else:
                print("\n❌ Response format tests failed.")
        else:
            print("\n❌ Metadata tests failed.")
    
    asyncio.run(main())
