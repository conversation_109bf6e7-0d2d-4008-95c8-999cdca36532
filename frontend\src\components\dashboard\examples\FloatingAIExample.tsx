/**
 * Floating AI Assistant Example Component
 * 
 * Demonstrates various usage patterns and integration scenarios
 * for the Floating AI Assistant in different dashboard contexts.
 */

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Bot,
  Sparkles,
  Settings,
  BarChart3,
  PieChart,
  LineChart,
  Database,
  Layout,
} from 'lucide-react';
import { FloatingAITrigger, useFloatingAI } from '../FloatingAITrigger';
import { FloatingAIAssistant } from '../FloatingAIAssistant';

export const FloatingAIExample: React.FC = () => {
  const [activeExample, setActiveExample] = useState('basic');
  const [widgets, setWidgets] = useState<any[]>([]);
  const [sections, setSections] = useState<any[]>([]);
  const [notifications, setNotifications] = useState<string[]>([]);

  // Example handlers for AI assistant actions
  const handleWidgetCreate = (widgetConfig: any) => {
    const newWidget = {
      id: Date.now().toString(),
      type: widgetConfig.type || 'chart',
      title: widgetConfig.title || `New ${widgetConfig.type || 'Widget'}`,
      config: widgetConfig,
      createdAt: new Date().toISOString(),
    };
    
    setWidgets(prev => [...prev, newWidget]);
    addNotification(`Created ${newWidget.title}`);
  };

  const handleSectionCreate = (sectionConfig: any) => {
    const newSection = {
      id: Date.now().toString(),
      title: sectionConfig.title || 'New Section',
      layout: sectionConfig.layout || 'grid',
      config: sectionConfig,
      createdAt: new Date().toISOString(),
    };
    
    setSections(prev => [...prev, newSection]);
    addNotification(`Created section: ${newSection.title}`);
  };

  const handleTemplateApply = (templateId: string) => {
    // Simulate template application
    const templates = {
      business: { name: 'Business Analytics', widgets: 3, sections: 2 },
      sales: { name: 'Sales Dashboard', widgets: 5, sections: 3 },
      marketing: { name: 'Marketing Metrics', widgets: 4, sections: 2 },
    };
    
    const template = templates[templateId as keyof typeof templates];
    if (template) {
      addNotification(`Applied ${template.name} template`);
    }
  };

  const handleDataConfigure = (dataConfig: any) => {
    addNotification(`Configured data source: ${dataConfig.source || 'Unknown'}`);
  };

  const addNotification = (message: string) => {
    setNotifications(prev => [message, ...prev.slice(0, 4)]);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-slate-900">Floating AI Assistant Examples</h1>
        <p className="text-slate-600">
          Interactive examples demonstrating different usage patterns and integration scenarios
        </p>
      </div>

      <Tabs value={activeExample} onValueChange={setActiveExample}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Usage</TabsTrigger>
          <TabsTrigger value="positioned">Positioning</TabsTrigger>
          <TabsTrigger value="integrated">Integrated</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        {/* Basic Usage Example */}
        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bot className="h-5 w-5 text-blue-500" />
                <span>Basic Floating AI Assistant</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-slate-600">
                The simplest implementation with default settings. The AI assistant appears 
                as a floating button in the bottom-right corner.
              </p>
              
              <div className="bg-slate-50 p-4 rounded-lg border-2 border-dashed border-slate-200 min-h-[300px] relative">
                <p className="text-center text-slate-500 mt-20">
                  Your dashboard content goes here
                </p>
                
                <FloatingAITrigger
                  onWidgetCreate={handleWidgetCreate}
                  onSectionCreate={handleSectionCreate}
                  onTemplateApply={handleTemplateApply}
                  onDataConfigure={handleDataConfigure}
                />
              </div>
              
              <div className="text-sm text-slate-600">
                <strong>Code:</strong>
                <pre className="bg-slate-100 p-2 rounded mt-1 overflow-x-auto">
{`<FloatingAITrigger
  onWidgetCreate={handleWidgetCreate}
  onSectionCreate={handleSectionCreate}
  onTemplateApply={handleTemplateApply}
  onDataConfigure={handleDataConfigure}
/>`}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Positioning Example */}
        <TabsContent value="positioned" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Layout className="h-5 w-5 text-green-500" />
                <span>Custom Positioning</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-slate-600">
                Demonstrate different positioning options for the floating AI trigger button.
              </p>
              
              <div className="grid grid-cols-2 gap-4">
                {(['bottom-right', 'bottom-left', 'top-right', 'top-left'] as const).map((position) => (
                  <div key={position} className="bg-slate-50 p-4 rounded-lg border relative h-40">
                    <p className="text-sm text-slate-600 text-center mt-12">
                      Position: {position}
                    </p>
                    <FloatingAITrigger
                      position={position}
                      onWidgetCreate={handleWidgetCreate}
                      onSectionCreate={handleSectionCreate}
                      onTemplateApply={handleTemplateApply}
                      onDataConfigure={handleDataConfigure}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Integrated Example */}
        <TabsContent value="integrated" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Sparkles className="h-5 w-5 text-purple-500" />
                <span>Dashboard Integration</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-slate-600">
                Full integration with dashboard state management and real-time updates.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Widgets Panel */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center space-x-2">
                      <BarChart3 className="h-4 w-4" />
                      <span>Widgets</span>
                      <Badge variant="secondary">{widgets.length}</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {widgets.length === 0 ? (
                      <p className="text-slate-500 text-sm">No widgets created yet</p>
                    ) : (
                      <div className="space-y-2">
                        {widgets.map((widget) => (
                          <div key={widget.id} className="flex items-center space-x-2 p-2 bg-slate-50 rounded">
                            <BarChart3 className="h-4 w-4 text-blue-500" />
                            <span className="text-sm">{widget.title}</span>
                            <Badge variant="outline" className="text-xs">{widget.type}</Badge>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Sections Panel */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center space-x-2">
                      <Layout className="h-4 w-4" />
                      <span>Sections</span>
                      <Badge variant="secondary">{sections.length}</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {sections.length === 0 ? (
                      <p className="text-slate-500 text-sm">No sections created yet</p>
                    ) : (
                      <div className="space-y-2">
                        {sections.map((section) => (
                          <div key={section.id} className="flex items-center space-x-2 p-2 bg-slate-50 rounded">
                            <Layout className="h-4 w-4 text-green-500" />
                            <span className="text-sm">{section.title}</span>
                            <Badge variant="outline" className="text-xs">{section.layout}</Badge>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Notifications */}
              {notifications.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">Recent Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1">
                      {notifications.map((notification, index) => (
                        <div key={index} className="text-sm text-slate-600 p-2 bg-green-50 rounded border-l-2 border-green-400">
                          {notification}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="bg-slate-50 p-4 rounded-lg border-2 border-dashed border-slate-200 min-h-[200px] relative">
                <p className="text-center text-slate-500 mt-16">
                  Try asking the AI to create widgets or sections!
                </p>
                
                <FloatingAITrigger
                  onWidgetCreate={handleWidgetCreate}
                  onSectionCreate={handleSectionCreate}
                  onTemplateApply={handleTemplateApply}
                  onDataConfigure={handleDataConfigure}
                  showNotifications={true}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Example */}
        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-orange-500" />
                <span>Advanced Features</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-slate-600">
                Advanced usage patterns including custom state management and programmatic control.
              </p>
              
              <AdvancedAIExample />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Advanced example with custom state management
const AdvancedAIExample: React.FC = () => {
  const [isAIOpen, setIsAIOpen] = useState(false);
  const [customMessage, setCustomMessage] = useState('');

  const handleOpenAI = () => {
    setIsAIOpen(true);
  };

  const handleCloseAI = () => {
    setIsAIOpen(false);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Button onClick={handleOpenAI} disabled={isAIOpen}>
          Open AI Assistant
        </Button>
        <Button onClick={handleCloseAI} disabled={!isAIOpen} variant="outline">
          Close AI Assistant
        </Button>
      </div>

      <div className="bg-slate-50 p-4 rounded-lg border relative min-h-[200px]">
        <p className="text-center text-slate-500 mt-16">
          Programmatically controlled AI assistant
        </p>
      </div>

      <FloatingAIAssistant
        isOpen={isAIOpen}
        onOpenChange={setIsAIOpen}
        onWidgetCreate={(config) => console.log('Widget created:', config)}
        onSectionCreate={(config) => console.log('Section created:', config)}
        onTemplateApply={(id) => console.log('Template applied:', id)}
        onDataConfigure={(config) => console.log('Data configured:', config)}
      />
    </div>
  );
};
