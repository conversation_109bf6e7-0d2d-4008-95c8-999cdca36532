/**
 * Cache utility for storing and retrieving data.
 */

interface CacheOptions {
  /** Time to live in milliseconds */
  ttl?: number;
}

interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number;
}

/**
 * A simple in-memory cache with TTL support.
 */
export class Cache {
  private static instance: Cache;
  private cache: Map<string, CacheEntry<any>> = new Map();
  private defaultTtl: number = 5 * 60 * 1000; // 5 minutes

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance of the cache.
   */
  public static getInstance(): Cache {
    if (!Cache.instance) {
      Cache.instance = new Cache();
    }
    return Cache.instance;
  }

  /**
   * Set a value in the cache.
   * @param key The cache key
   * @param value The value to cache
   * @param options Cache options
   */
  public set<T>(key: string, value: T, options: CacheOptions = {}): void {
    const ttl = options.ttl || this.defaultTtl;
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Get a value from the cache.
   * @param key The cache key
   * @returns The cached value or undefined if not found or expired
   */
  public get<T>(key: string): T | undefined {
    const entry = this.cache.get(key);
    if (!entry) {
      return undefined;
    }

    // Check if the entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return undefined;
    }

    return entry.value as T;
  }

  /**
   * Check if a key exists in the cache and is not expired.
   * @param key The cache key
   * @returns True if the key exists and is not expired
   */
  public has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    // Check if the entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Delete a key from the cache.
   * @param key The cache key
   */
  public delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all entries from the cache.
   */
  public clear(): void {
    this.cache.clear();
  }

  /**
   * Get the number of entries in the cache.
   */
  public size(): number {
    return this.cache.size;
  }

  /**
   * Clean up expired entries from the cache.
   */
  public cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Export a singleton instance
export const cache = Cache.getInstance();
