import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { 
  Settings, 
  Trash2, 
  Copy, 
  Download, 
  Upload, 
  RefreshCw,
  Palette,
  Database,
  Shield,
  Users,
  Clock,
  AlertTriangle,
  CheckCircle2,
  Info
} from 'lucide-react';
import { DashboardResponse } from '@/types/dashboard-customization';
import { useToast } from '@/hooks/use-toast';

interface DashboardSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  dashboard: DashboardResponse | null;
  onUpdate: (dashboardId: string, updates: Partial<DashboardResponse>) => void;
  onDelete: (dashboardId: string) => void;
  onDuplicate: (dashboardId: string, name?: string) => void;
  onExport: (dashboardId: string) => void;
  onImport: (file: File) => void;
}

interface DashboardFormData {
  name: string;
  description?: string;
  is_default: boolean;
  is_public: boolean;
  refresh_interval: number;
  tags: string[];
  theme_config: {
    primary_color?: string;
    background_color?: string;
    text_color?: string;
    border_color?: string;
  };
  layout_config: {
    columns?: number;
    rows?: number;
    grid_gap?: number;
    responsive?: boolean;
  };
}

const THEME_PRESETS = [
  { name: 'Default', primary: '#3b82f6', background: '#ffffff', text: '#1f2937', border: '#e5e7eb' },
  { name: 'Dark', primary: '#8b5cf6', background: '#1f2937', text: '#f9fafb', border: '#374151' },
  { name: 'Blue', primary: '#0ea5e9', background: '#f0f9ff', text: '#0c4a6e', border: '#bae6fd' },
  { name: 'Green', primary: '#10b981', background: '#f0fdf4', text: '#064e3b', border: '#bbf7d0' },
  { name: 'Purple', primary: '#8b5cf6', background: '#faf5ff', text: '#581c87', border: '#d8b4fe' },
];

export const DashboardSettingsDialog: React.FC<DashboardSettingsDialogProps> = ({
  open,
  onOpenChange,
  dashboard,
  onUpdate,
  onDelete,
  onDuplicate,
  onExport,
  onImport,
}) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<DashboardFormData>({
    name: '',
    is_default: false,
    is_public: false,
    refresh_interval: 300,
    tags: [],
    theme_config: {},
    layout_config: {},
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [duplicateName, setDuplicateName] = useState('');
  const [newTag, setNewTag] = useState('');

  // Initialize form data when dashboard changes
  useEffect(() => {
    if (dashboard) {
      setFormData({
        name: dashboard.name,
        description: dashboard.description,
        is_default: dashboard.is_default,
        is_public: dashboard.is_public,
        refresh_interval: dashboard.refresh_interval || 300,
        tags: dashboard.tags || [],
        theme_config: dashboard.theme_config || {},
        layout_config: dashboard.layout_config || {},
      });
      setDuplicateName(`${dashboard.name} (Copy)`);
    }
  }, [dashboard]);

  // Handle form field changes
  const handleFieldChange = (field: keyof DashboardFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle nested object changes
  const handleNestedChange = (parent: keyof DashboardFormData, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value,
      },
    }));
  };

  // Handle theme preset selection
  const handleThemePreset = (preset: typeof THEME_PRESETS[0]) => {
    setFormData(prev => ({
      ...prev,
      theme_config: {
        primary_color: preset.primary,
        background_color: preset.background,
        text_color: preset.text,
        border_color: preset.border,
      },
    }));
  };

  // Handle tag management
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!dashboard) return;

    setIsSubmitting(true);
    try {
      await onUpdate(dashboard.id, formData);
      onOpenChange(false);
      toast({
        title: "Dashboard Updated",
        description: "Dashboard settings have been saved successfully.",
      });
    } catch (error) {
      console.error('Error updating dashboard:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update dashboard settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle dashboard deletion
  const handleDelete = async () => {
    if (!dashboard) return;

    try {
      await onDelete(dashboard.id);
      setShowDeleteDialog(false);
      onOpenChange(false);
      toast({
        title: "Dashboard Deleted",
        description: "Dashboard has been permanently deleted.",
      });
    } catch (error) {
      console.error('Error deleting dashboard:', error);
      toast({
        title: "Delete Failed",
        description: "Failed to delete dashboard. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle dashboard duplication
  const handleDuplicate = async () => {
    if (!dashboard) return;

    try {
      await onDuplicate(dashboard.id, duplicateName);
      toast({
        title: "Dashboard Duplicated",
        description: "Dashboard has been duplicated successfully.",
      });
    } catch (error) {
      console.error('Error duplicating dashboard:', error);
      toast({
        title: "Duplicate Failed",
        description: "Failed to duplicate dashboard. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle file import
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onImport(file);
      event.target.value = ''; // Reset input
    }
  };

  if (!dashboard) return null;

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Dashboard Settings: {dashboard.name}</span>
            </DialogTitle>
            <DialogDescription>
              Configure dashboard properties, appearance, and behavior settings.
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="general" className="space-y-4">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="appearance">Appearance</TabsTrigger>
              <TabsTrigger value="layout">Layout</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
              <TabsTrigger value="actions">Actions</TabsTrigger>
            </TabsList>

            {/* General Settings */}
            <TabsContent value="general" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Dashboard Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleFieldChange('name', e.target.value)}
                    placeholder="Enter dashboard name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="refresh-interval">Auto Refresh (seconds)</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[formData.refresh_interval]}
                      onValueChange={([value]) => handleFieldChange('refresh_interval', value)}
                      min={30}
                      max={3600}
                      step={30}
                      className="w-full"
                    />
                    <div className="text-sm text-muted-foreground text-center">
                      {Math.floor(formData.refresh_interval / 60)}m {formData.refresh_interval % 60}s
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  value={formData.description || ''}
                  onChange={(e) => handleFieldChange('description', e.target.value)}
                  placeholder="Enter dashboard description"
                  rows={3}
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is-default"
                    checked={formData.is_default}
                    onCheckedChange={(checked) => handleFieldChange('is_default', checked)}
                  />
                  <Label htmlFor="is-default">Set as Default Dashboard</Label>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is-public"
                    checked={formData.is_public}
                    onCheckedChange={(checked) => handleFieldChange('is_public', checked)}
                  />
                  <Label htmlFor="is-public">Make Dashboard Public</Label>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </div>
              </div>

              {/* Tags Management */}
              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add a tag"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                  />
                  <Button type="button" onClick={handleAddTag} size="sm">
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center space-x-1">
                      <span>{tag}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => handleRemoveTag(tag)}
                      >
                        ×
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Appearance Settings */}
            <TabsContent value="appearance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Palette className="h-4 w-4" />
                    <span>Theme Configuration</span>
                  </CardTitle>
                  <CardDescription>
                    Customize the visual appearance of your dashboard.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Theme Presets</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {THEME_PRESETS.map((preset) => (
                        <Button
                          key={preset.name}
                          variant="outline"
                          className="h-16 justify-start"
                          onClick={() => handleThemePreset(preset)}
                        >
                          <div className="flex items-center space-x-2">
                            <div className="flex space-x-1">
                              <div
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: preset.primary }}
                              />
                              <div
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: preset.background }}
                              />
                              <div
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: preset.text }}
                              />
                            </div>
                            <span>{preset.name}</span>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="primary-color">Primary Color</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="primary-color"
                          type="color"
                          value={formData.theme_config.primary_color || '#3b82f6'}
                          onChange={(e) => handleNestedChange('theme_config', 'primary_color', e.target.value)}
                          className="w-16 h-10"
                        />
                        <Input
                          value={formData.theme_config.primary_color || '#3b82f6'}
                          onChange={(e) => handleNestedChange('theme_config', 'primary_color', e.target.value)}
                          placeholder="#3b82f6"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="background-color">Background Color</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="background-color"
                          type="color"
                          value={formData.theme_config.background_color || '#ffffff'}
                          onChange={(e) => handleNestedChange('theme_config', 'background_color', e.target.value)}
                          className="w-16 h-10"
                        />
                        <Input
                          value={formData.theme_config.background_color || '#ffffff'}
                          onChange={(e) => handleNestedChange('theme_config', 'background_color', e.target.value)}
                          placeholder="#ffffff"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="text-color">Text Color</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="text-color"
                          type="color"
                          value={formData.theme_config.text_color || '#1f2937'}
                          onChange={(e) => handleNestedChange('theme_config', 'text_color', e.target.value)}
                          className="w-16 h-10"
                        />
                        <Input
                          value={formData.theme_config.text_color || '#1f2937'}
                          onChange={(e) => handleNestedChange('theme_config', 'text_color', e.target.value)}
                          placeholder="#1f2937"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="border-color">Border Color</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="border-color"
                          type="color"
                          value={formData.theme_config.border_color || '#e5e7eb'}
                          onChange={(e) => handleNestedChange('theme_config', 'border_color', e.target.value)}
                          className="w-16 h-10"
                        />
                        <Input
                          value={formData.theme_config.border_color || '#e5e7eb'}
                          onChange={(e) => handleNestedChange('theme_config', 'border_color', e.target.value)}
                          placeholder="#e5e7eb"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Layout Settings */}
            <TabsContent value="layout" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Grid Layout Configuration</CardTitle>
                  <CardDescription>
                    Configure the grid system for widget positioning.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="columns">Grid Columns</Label>
                      <Input
                        id="columns"
                        type="number"
                        value={formData.layout_config.columns || 12}
                        onChange={(e) => handleNestedChange('layout_config', 'columns', parseInt(e.target.value) || 12)}
                        min={6}
                        max={24}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="rows">Grid Rows</Label>
                      <Input
                        id="rows"
                        type="number"
                        value={formData.layout_config.rows || 12}
                        onChange={(e) => handleNestedChange('layout_config', 'rows', parseInt(e.target.value) || 12)}
                        min={6}
                        max={24}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="grid-gap">Grid Gap (px)</Label>
                      <Input
                        id="grid-gap"
                        type="number"
                        value={formData.layout_config.grid_gap || 16}
                        onChange={(e) => handleNestedChange('layout_config', 'grid_gap', parseInt(e.target.value) || 16)}
                        min={0}
                        max={32}
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="responsive"
                      checked={formData.layout_config.responsive ?? true}
                      onCheckedChange={(checked) => handleNestedChange('layout_config', 'responsive', checked)}
                    />
                    <Label htmlFor="responsive">Enable Responsive Layout</Label>
                  </div>

                  <div className="p-4 bg-muted rounded-lg">
                    <div className="text-sm font-medium mb-2">Grid Preview</div>
                    <div
                      className="grid bg-background rounded border"
                      style={{
                        gridTemplateColumns: `repeat(${formData.layout_config.columns || 12}, 1fr)`,
                        gap: `${formData.layout_config.grid_gap || 16}px`,
                        height: '120px',
                      }}
                    >
                      {Array.from({ length: (formData.layout_config.columns || 12) * 3 }, (_, i) => (
                        <div
                          key={i}
                          className="bg-primary/20 rounded-sm"
                        />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Permissions Settings */}
            <TabsContent value="permissions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-4 w-4" />
                    <span>Access Control</span>
                  </CardTitle>
                  <CardDescription>
                    Manage who can view and edit this dashboard.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Users className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Public Access</div>
                          <div className="text-sm text-muted-foreground">
                            Anyone with the link can view this dashboard
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={formData.is_public}
                        onCheckedChange={(checked) => handleFieldChange('is_public', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <CheckCircle2 className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Default Dashboard</div>
                          <div className="text-sm text-muted-foreground">
                            Set as the default dashboard for new users
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={formData.is_default}
                        onCheckedChange={(checked) => handleFieldChange('is_default', checked)}
                      />
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label>Dashboard Statistics</Label>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span>Created:</span>
                        <span>{new Date(dashboard.created_at).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Last Updated:</span>
                        <span>{new Date(dashboard.updated_at).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Sections:</span>
                        <span>{dashboard.section_count || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Widgets:</span>
                        <span>{dashboard.widget_count || 0}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Actions */}
            <TabsContent value="actions" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Dashboard Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle>Dashboard Actions</CardTitle>
                    <CardDescription>
                      Manage dashboard data and configuration.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-2">
                      <Label htmlFor="duplicate-name">Duplicate Name</Label>
                      <Input
                        id="duplicate-name"
                        value={duplicateName}
                        onChange={(e) => setDuplicateName(e.target.value)}
                        placeholder="Enter name for duplicate"
                      />
                    </div>
                    <Button
                      onClick={handleDuplicate}
                      className="w-full"
                      variant="outline"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Duplicate Dashboard
                    </Button>
                    <Button
                      onClick={() => onExport(dashboard.id)}
                      className="w-full"
                      variant="outline"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export Dashboard
                    </Button>
                    <div className="space-y-2">
                      <Label htmlFor="import-file">Import Dashboard</Label>
                      <Input
                        id="import-file"
                        type="file"
                        accept=".json"
                        onChange={handleFileImport}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Danger Zone */}
                <Card className="border-destructive">
                  <CardHeader>
                    <CardTitle className="text-destructive flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4" />
                      <span>Danger Zone</span>
                    </CardTitle>
                    <CardDescription>
                      Irreversible actions that permanently affect your dashboard.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="p-4 bg-destructive/10 rounded-lg">
                      <div className="text-sm font-medium text-destructive mb-2">
                        Delete Dashboard
                      </div>
                      <div className="text-sm text-muted-foreground mb-3">
                        This action cannot be undone. All widgets, sections, and configurations will be permanently deleted.
                      </div>
                      <Button
                        onClick={() => setShowDeleteDialog(true)}
                        variant="destructive"
                        className="w-full"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Dashboard
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <span>Delete Dashboard</span>
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{dashboard?.name}"? This action cannot be undone.
              All sections, widgets, and configurations will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Dashboard
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
