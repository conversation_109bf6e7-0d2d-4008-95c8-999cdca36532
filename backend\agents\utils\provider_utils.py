"""Utility functions for provider API keys.

This module provides utility functions for managing provider API keys.
"""

import os
import logging
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session

# Configure logging
logger = logging.getLogger(__name__)

def get_api_key(provider_id: str, db: Optional[Session] = None, user_id: Optional[int] = None) -> str:
    """
    Get the API key for a provider from environment variables or database.

    This function first checks if there's a user-specific API key in the database.
    If not found, it falls back to the environment variables.

    Args:
        provider_id: The provider ID (openai, groq, etc.)
        db: Optional database session
        user_id: Optional user ID

    Returns:
        The API key or an empty string if not found
    """
    # Check if we have a database session and user ID
    if db and user_id:
        try:
            # Import here to avoid circular imports
            from app.database import get_provider_api_key

            # Try to get the API key from the database
            provider_api_key = get_provider_api_key(db, user_id, provider_id)
            if provider_api_key:
                return provider_api_key.api_key
        except Exception as e:
            logger.error(f"Error getting API key from database: {str(e)}", exc_info=True)

    # Fall back to environment variables
    env_var_name = f"{provider_id.upper()}_API_KEY"
    return os.getenv(env_var_name, "")

def get_provider_endpoint(provider_id: str) -> str:
    """
    Get the endpoint URL for a provider from environment variables.

    Args:
        provider_id: The provider ID (openai, groq, etc.)

    Returns:
        The endpoint URL or a default value if not found
    """
    # Default endpoints
    default_endpoints = {
        "openai": "https://api.openai.com/v1",
        "groq": "https://api.groq.com/openai/v1",
        "anthropic": "https://api.anthropic.com/v1",
        "gemini": "https://generativelanguage.googleapis.com",
        "openrouter": "https://openrouter.ai/api/v1",
        "requesty": "https://api.requesty.ai",
        "ollama": "http://localhost:11434",
    }

    # Check environment variables
    env_var_name = f"{provider_id.upper()}_ENDPOINT"
    return os.getenv(env_var_name, default_endpoints.get(provider_id, ""))


def validate_api_key(provider_id: str, api_key: str) -> tuple[bool, str]:
    """
    Validate an API key for a provider by making a test request.

    Args:
        provider_id: The provider ID (openai, groq, etc.)
        api_key: The API key to validate

    Returns:
        A tuple of (is_valid, message)
    """
    import requests

    # Get the endpoint URL
    endpoint = get_provider_endpoint(provider_id)

    # Validation functions for each provider
    if provider_id == "openai":
        try:
            url = f"{endpoint}/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                return True, "OpenAI API key is valid"
            else:
                return False, f"OpenAI API key is invalid: {response.status_code} {response.text}"
        except Exception as e:
            return False, f"Error validating OpenAI API key: {str(e)}"

    elif provider_id == "groq":
        try:
            url = f"{endpoint}/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                return True, "Groq API key is valid"
            else:
                return False, f"Groq API key is invalid: {response.status_code} {response.text}"
        except Exception as e:
            return False, f"Error validating Groq API key: {str(e)}"

    elif provider_id == "anthropic":
        try:
            url = f"{endpoint}/models"
            headers = {"x-api-key": api_key}
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                return True, "Anthropic API key is valid"
            else:
                return False, f"Anthropic API key is invalid: {response.status_code} {response.text}"
        except Exception as e:
            return False, f"Error validating Anthropic API key: {str(e)}"

    elif provider_id == "gemini":
        try:
            url = f"{endpoint}/v1beta/models?key={api_key}"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                return True, "Google Gemini API key is valid"
            else:
                return False, f"Google Gemini API key is invalid: {response.status_code} {response.text}"
        except Exception as e:
            return False, f"Error validating Google Gemini API key: {str(e)}"

    elif provider_id == "openrouter":
        try:
            url = f"{endpoint}/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                return True, "OpenRouter API key is valid"
            else:
                return False, f"OpenRouter API key is invalid: {response.status_code} {response.text}"
        except Exception as e:
            return False, f"Error validating OpenRouter API key: {str(e)}"

    elif provider_id == "requesty":
        try:
            url = f"{endpoint}/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                return True, "Requesty API key is valid"
            else:
                return False, f"Requesty API key is invalid: {response.status_code} {response.text}"
        except Exception as e:
            return False, f"Error validating Requesty API key: {str(e)}"

    elif provider_id == "ollama":
        try:
            url = f"{endpoint}/api/tags"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                return True, "Ollama is available"
            else:
                return False, f"Ollama is not available: {response.status_code} {response.text}"
        except Exception as e:
            return False, f"Error connecting to Ollama: {str(e)}"

    else:
        return False, f"Unsupported provider: {provider_id}"
