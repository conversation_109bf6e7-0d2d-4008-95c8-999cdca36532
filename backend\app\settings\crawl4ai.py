"""
Configuration settings for Crawl4AI integration in Datagenius.

This module provides configuration management for the Crawl4AI web scraping
service, including environment-based settings and feature toggles.
"""

import os
from typing import Dict, Any, List
from dataclasses import dataclass

@dataclass
class Crawl4AIConfig:
    """Configuration class for Crawl4AI integration."""
    
    # Feature toggles
    enabled: bool = True
    use_llm_extraction: bool = True
    use_css_fallback: bool = True
    
    # Performance settings
    timeout_seconds: int = 30
    max_retries: int = 2
    concurrent_requests: int = 3
    
    # Quality thresholds
    min_confidence_score: float = 0.3
    min_content_length: int = 100
    
    # Browser settings
    headless: bool = True
    javascript_enabled: bool = True
    ignore_https_errors: bool = True
    
    # Content filtering
    word_count_threshold: int = 10
    excluded_tags: List[str] = None
    target_elements: List[str] = None
    
    def __post_init__(self):
        """Initialize default values after dataclass creation."""
        if self.excluded_tags is None:
            self.excluded_tags = [
                "script", "style", "nav", "footer", 
                "header", "aside", "advertisement"
            ]
        
        if self.target_elements is None:
            self.target_elements = [
                "main", "article", ".content", ".main-content",
                ".about", ".company-info", ".business-info",
                "[itemtype*='Organization']", "[itemtype*='LocalBusiness']"
            ]

def load_crawl4ai_config() -> Crawl4AIConfig:
    """
    Load Crawl4AI configuration from environment variables.
    
    Returns:
        Crawl4AIConfig instance with settings from environment
    """
    return Crawl4AIConfig(
        enabled=os.getenv("CRAWL4AI_ENABLED", "true").lower() == "true",
        use_llm_extraction=os.getenv("CRAWL4AI_USE_LLM", "true").lower() == "true",
        use_css_fallback=os.getenv("CRAWL4AI_USE_CSS_FALLBACK", "true").lower() == "true",
        
        timeout_seconds=int(os.getenv("CRAWL4AI_TIMEOUT", "30")),
        max_retries=int(os.getenv("CRAWL4AI_MAX_RETRIES", "2")),
        concurrent_requests=int(os.getenv("CRAWL4AI_CONCURRENT_REQUESTS", "3")),
        
        min_confidence_score=float(os.getenv("CRAWL4AI_MIN_CONFIDENCE", "0.3")),
        min_content_length=int(os.getenv("CRAWL4AI_MIN_CONTENT_LENGTH", "100")),
        
        headless=os.getenv("CRAWL4AI_HEADLESS", "true").lower() == "true",
        javascript_enabled=os.getenv("CRAWL4AI_JAVASCRIPT", "true").lower() == "true",
        ignore_https_errors=os.getenv("CRAWL4AI_IGNORE_HTTPS", "true").lower() == "true",
        
        word_count_threshold=int(os.getenv("CRAWL4AI_WORD_THRESHOLD", "10"))
    )

def get_browser_args() -> List[str]:
    """
    Get browser arguments optimized for production deployment.
    
    Returns:
        List of browser arguments for Crawl4AI
    """
    base_args = [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding"
    ]
    
    # Add Docker-specific arguments if running in container
    if os.getenv("DOCKER_CONTAINER", "false").lower() == "true":
        base_args.extend([
            "--disable-dev-shm-usage",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-default-apps"
        ])
    
    return base_args

def get_extraction_targets() -> Dict[str, List[str]]:
    """
    Get CSS selectors for different types of business information.
    
    Returns:
        Dictionary mapping field types to CSS selectors
    """
    return {
        "business_name": [
            "h1", ".company-name", ".business-name", ".brand-name",
            "[itemProp='name']", ".site-title", ".logo-text",
            "[itemtype*='Organization'] [itemprop='name']",
            "[itemtype*='LocalBusiness'] [itemprop='name']"
        ],
        "description": [
            ".description", ".about", ".company-description",
            "[itemProp='description']", ".intro", ".summary",
            ".mission", ".vision", ".overview"
        ],
        "contact_email": [
            "[href^='mailto:']", "[itemProp='email']",
            ".email", ".contact-email"
        ],
        "contact_phone": [
            "[href^='tel:']", "[itemProp='telephone']",
            ".phone", ".contact-phone", ".tel"
        ],
        "address": [
            ".address", "[itemProp='address']", ".location",
            ".contact-address", ".office-location",
            "[itemtype*='PostalAddress']"
        ],
        "social_media": [
            "[href*='facebook.com']", "[href*='twitter.com']",
            "[href*='linkedin.com']", "[href*='instagram.com']",
            ".social-links a", ".social-media a"
        ]
    }

def get_quality_indicators() -> Dict[str, Any]:
    """
    Get indicators for assessing extraction quality.
    
    Returns:
        Dictionary with quality assessment criteria
    """
    return {
        "essential_fields": ["business_name", "description"],
        "contact_fields": ["contact_email", "contact_phone", "address"],
        "min_field_length": {
            "business_name": 2,
            "description": 20,
            "industry": 3,
            "contact_email": 5,
            "contact_phone": 7,
            "address": 10
        },
        "confidence_weights": {
            "essential_fields": 0.4,
            "contact_fields": 0.3,
            "structured_data": 0.2,
            "content_quality": 0.1
        }
    }

# Global configuration instance
crawl4ai_config = load_crawl4ai_config()
