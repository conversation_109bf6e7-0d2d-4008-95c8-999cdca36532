"""
Comprehensive monitoring and metrics collection for Datagenius.

This module provides application performance monitoring, custom metrics,
and observability features for production deployment.
"""

import time
import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from functools import wraps
from contextlib import asynccontextmanager

import psutil
from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server
from prometheus_client.core import CollectorRegistry

logger = logging.getLogger(__name__)


class MetricsCollector:
    """
    Centralized metrics collection for Datagenius application.
    
    Provides Prometheus-compatible metrics for monitoring application
    performance, user interactions, and system health.
    """

    def __init__(self):
        """Initialize the metrics collector."""
        self.registry = CollectorRegistry()
        
        # HTTP Request Metrics
        self.http_requests_total = Counter(
            'datagenius_http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status_code'],
            registry=self.registry
        )
        
        self.http_request_duration = Histogram(
            'datagenius_http_request_duration_seconds',
            'HTTP request duration in seconds',
            ['method', 'endpoint'],
            registry=self.registry
        )
        
        # Agent Metrics
        self.agent_interactions_total = Counter(
            'datagenius_agent_interactions_total',
            'Total agent interactions',
            ['agent_type', 'user_id'],
            registry=self.registry
        )
        
        self.agent_response_time = Histogram(
            'datagenius_agent_response_time_seconds',
            'Agent response time in seconds',
            ['agent_type'],
            registry=self.registry
        )
        
        self.agent_errors_total = Counter(
            'datagenius_agent_errors_total',
            'Total agent errors',
            ['agent_type', 'error_type'],
            registry=self.registry
        )
        
        # Database Metrics
        self.database_connections = Gauge(
            'datagenius_database_connections',
            'Number of active database connections',
            registry=self.registry
        )

        self.database_query_duration = Histogram(
            'datagenius_database_query_duration_seconds',
            'Database query duration in seconds',
            ['operation', 'query_type'],
            registry=self.registry
        )

        # Phase 1 Performance Metrics
        self.cache_operations = Counter(
            'datagenius_cache_operations_total',
            'Total cache operations',
            ['cache_level', 'operation'],
            registry=self.registry
        )

        self.cache_hit_ratio = Gauge(
            'datagenius_cache_hit_ratio',
            'Cache hit ratio percentage',
            ['cache_level'],
            registry=self.registry
        )

        self.dashboard_load_time = Histogram(
            'datagenius_dashboard_load_time_seconds',
            'Dashboard load time in seconds',
            ['dashboard_type'],
            registry=self.registry
        )

        self.websocket_connections = Gauge(
            'datagenius_websocket_connections',
            'Number of active WebSocket connections',
            registry=self.registry
        )

        self.websocket_reconnections = Counter(
            'datagenius_websocket_reconnections_total',
            'Total WebSocket reconnections',
            registry=self.registry
        )

        self.memory_usage = Gauge(
            'datagenius_memory_usage_bytes',
            'Memory usage in bytes',
            ['component'],
            registry=self.registry
        )
        
        # Memory and Vector Database Metrics
        self.vector_operations_total = Counter(
            'datagenius_vector_operations_total',
            'Total vector database operations',
            ['operation_type'],
            registry=self.registry
        )
        
        self.memory_operations_total = Counter(
            'datagenius_memory_operations_total',
            'Total memory operations',
            ['operation_type'],
            registry=self.registry
        )
        
        # System Metrics
        self.system_cpu_usage = Gauge(
            'datagenius_system_cpu_usage_percent',
            'System CPU usage percentage',
            registry=self.registry
        )
        
        self.system_memory_usage = Gauge(
            'datagenius_system_memory_usage_bytes',
            'System memory usage in bytes',
            registry=self.registry
        )
        
        self.system_disk_usage = Gauge(
            'datagenius_system_disk_usage_percent',
            'System disk usage percentage',
            registry=self.registry
        )
        
        # User Metrics
        self.active_users = Gauge(
            'datagenius_active_users',
            'Number of active users',
            registry=self.registry
        )
        
        self.user_sessions_total = Counter(
            'datagenius_user_sessions_total',
            'Total user sessions',
            ['session_type'],
            registry=self.registry
        )
        
        # File Processing Metrics
        self.files_processed_total = Counter(
            'datagenius_files_processed_total',
            'Total files processed',
            ['file_type', 'status'],
            registry=self.registry
        )
        
        self.file_processing_duration = Histogram(
            'datagenius_file_processing_duration_seconds',
            'File processing duration in seconds',
            ['file_type'],
            registry=self.registry
        )
        
        # Application Info
        self.app_info = Info(
            'datagenius_app_info',
            'Application information',
            registry=self.registry
        )
        
        # Initialize system monitoring
        self._start_system_monitoring()
        
        logger.info("Metrics collector initialized")

    def _start_system_monitoring(self):
        """Start background system monitoring."""
        try:
            # Only create task if there's a running event loop
            loop = asyncio.get_running_loop()
            asyncio.create_task(self._monitor_system_metrics())
        except RuntimeError:
            # No event loop running, skip system monitoring for now
            # This can happen during module imports
            logger.debug("No event loop running, skipping system monitoring initialization")

    async def _monitor_system_metrics(self):
        """Monitor system metrics continuously."""
        while True:
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                self.system_cpu_usage.set(cpu_percent)
                
                # Memory usage
                memory = psutil.virtual_memory()
                self.system_memory_usage.set(memory.used)
                
                # Disk usage
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                self.system_disk_usage.set(disk_percent)
                
                # Wait before next collection
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except Exception as e:
                logger.error(f"Error collecting system metrics: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    def record_http_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics."""
        self.http_requests_total.labels(
            method=method,
            endpoint=endpoint,
            status_code=str(status_code)
        ).inc()
        
        self.http_request_duration.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)

    def record_agent_interaction(self, agent_type: str, user_id: str, duration: float, error: Optional[str] = None):
        """Record agent interaction metrics."""
        self.agent_interactions_total.labels(
            agent_type=agent_type,
            user_id=user_id
        ).inc()
        
        self.agent_response_time.labels(
            agent_type=agent_type
        ).observe(duration)
        
        if error:
            self.agent_errors_total.labels(
                agent_type=agent_type,
                error_type=error
            ).inc()

    def record_database_operation(self, operation: str, duration: float):
        """Record database operation metrics."""
        self.database_query_duration.labels(
            operation=operation
        ).observe(duration)

    def record_vector_operation(self, operation_type: str):
        """Record vector database operation."""
        self.vector_operations_total.labels(
            operation_type=operation_type
        ).inc()

    def record_memory_operation(self, operation_type: str):
        """Record memory operation."""
        self.memory_operations_total.labels(
            operation_type=operation_type
        ).inc()

    def record_file_processing(self, file_type: str, status: str, duration: float):
        """Record file processing metrics."""
        self.files_processed_total.labels(
            file_type=file_type,
            status=status
        ).inc()
        
        self.file_processing_duration.labels(
            file_type=file_type
        ).observe(duration)

    def update_active_users(self, count: int):
        """Update active users count."""
        self.active_users.set(count)

    def record_user_session(self, session_type: str):
        """Record user session."""
        self.user_sessions_total.labels(
            session_type=session_type
        ).inc()

    def update_database_connections(self, count: int):
        """Update database connections count."""
        self.database_connections.set(count)

    def update_database_query_time(self, query_type: str, duration: float, operation: str = "query"):
        """Update database query duration with enhanced tracking."""
        self.database_query_duration.labels(operation=operation, query_type=query_type).observe(duration)

    # Phase 1 Performance Metric Updates
    def update_cache_performance(self, cache_level: str, operation: str):
        """Update cache performance metrics."""
        self.cache_operations.labels(cache_level=cache_level, operation=operation).inc()

    def update_cache_hit_ratio(self, cache_level: str, hit_ratio: float):
        """Update cache hit ratio."""
        self.cache_hit_ratio.labels(cache_level=cache_level).set(hit_ratio)

    def update_dashboard_load_time(self, dashboard_type: str, load_time: float):
        """Update dashboard load time metrics."""
        self.dashboard_load_time.labels(dashboard_type=dashboard_type).observe(load_time)

    def update_websocket_connections(self, count: int):
        """Update WebSocket connection count."""
        self.websocket_connections.set(count)

    def increment_websocket_reconnections(self):
        """Increment WebSocket reconnection counter."""
        self.websocket_reconnections.inc()

    def update_memory_usage(self, component: str, usage_bytes: int):
        """Update memory usage metrics."""
        self.memory_usage.labels(component=component).set(usage_bytes)

    def set_app_info(self, version: str, environment: str, build_date: str):
        """Set application information."""
        self.app_info.info({
            'version': version,
            'environment': environment,
            'build_date': build_date
        })


# Global metrics collector instance
metrics = MetricsCollector()


def monitor_http_request(func):
    """Decorator to monitor HTTP request metrics."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        method = kwargs.get('method', 'UNKNOWN')
        endpoint = kwargs.get('endpoint', 'UNKNOWN')
        status_code = 200
        
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            status_code = getattr(e, 'status_code', 500)
            raise
        finally:
            duration = time.time() - start_time
            metrics.record_http_request(method, endpoint, status_code, duration)
    
    return wrapper


def monitor_agent_interaction(agent_type: str):
    """Decorator to monitor agent interaction metrics."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            user_id = kwargs.get('user_id', 'anonymous')
            error = None
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                error = type(e).__name__
                raise
            finally:
                duration = time.time() - start_time
                metrics.record_agent_interaction(agent_type, user_id, duration, error)
        
        return wrapper
    return decorator


def monitor_database_operation(operation: str):
    """Decorator to monitor database operation metrics."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                metrics.record_database_operation(operation, duration)
        
        return wrapper
    return decorator


@asynccontextmanager
async def monitor_file_processing(file_type: str):
    """Context manager to monitor file processing."""
    start_time = time.time()
    status = "success"
    
    try:
        yield
    except Exception:
        status = "error"
        raise
    finally:
        duration = time.time() - start_time
        metrics.record_file_processing(file_type, status, duration)


class HealthChecker:
    """
    Health check system for monitoring application components.
    """

    def __init__(self):
        """Initialize health checker."""
        self.checks = {}
        self.last_check_time = {}

    def register_check(self, name: str, check_func, interval: int = 60):
        """
        Register a health check.
        
        Args:
            name: Name of the health check
            check_func: Function that returns True if healthy
            interval: Check interval in seconds
        """
        self.checks[name] = {
            'func': check_func,
            'interval': interval,
            'last_result': None,
            'last_error': None
        }

    async def run_check(self, name: str) -> Dict[str, Any]:
        """Run a specific health check."""
        if name not in self.checks:
            return {'status': 'unknown', 'error': 'Check not found'}

        check = self.checks[name]
        
        try:
            result = await check['func']() if asyncio.iscoroutinefunction(check['func']) else check['func']()
            check['last_result'] = True
            check['last_error'] = None
            
            return {
                'status': 'healthy' if result else 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'details': result if isinstance(result, dict) else {}
            }
        except Exception as e:
            check['last_result'] = False
            check['last_error'] = str(e)
            
            return {
                'status': 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }

    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all registered health checks."""
        results = {}
        overall_status = 'healthy'
        
        for name in self.checks:
            result = await self.run_check(name)
            results[name] = result
            
            if result['status'] != 'healthy':
                overall_status = 'unhealthy'

        return {
            'status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'checks': results
        }


# Global health checker instance
health_checker = HealthChecker()


def start_metrics_server(port: int = 8001):
    """Start Prometheus metrics server."""
    try:
        start_http_server(port, registry=metrics.registry)
        logger.info(f"Metrics server started on port {port}")
    except Exception as e:
        logger.error(f"Failed to start metrics server: {e}")


# Initialize application info
def initialize_metrics(version: str = "1.0.0", environment: str = "development"):
    """Initialize metrics with application information."""
    build_date = datetime.now().isoformat()
    metrics.set_app_info(version, environment, build_date)
    logger.info(f"Metrics initialized for {environment} environment")
