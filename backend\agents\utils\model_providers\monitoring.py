"""
Monitoring for the model provider system.

This module provides monitoring functionality for the model provider system,
including usage tracking, performance metrics, and error reporting.
"""

import logging
import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from collections import defaultdict

# Configure logging
logger = logging.getLogger(__name__)


class ModelUsageMonitor:
    """Monitor for model usage."""

    def __init__(self, max_history: int = 1000):
        """
        Initialize the monitor.

        Args:
            max_history: Maximum number of requests to keep in history
        """
        self._requests: List[Dict[str, Any]] = []
        self._max_history = max_history
        self._usage_by_provider: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            "total_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "total_latency": 0.0,
            "error_count": 0,
            "models": defaultdict(lambda: {
                "total_requests": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "total_latency": 0.0,
                "error_count": 0
            })
        })
        logger.info(f"Initialized model usage monitor with max_history={max_history}")

    def record_request(self, 
                      provider_id: str, 
                      model_id: str, 
                      prompt_tokens: int, 
                      completion_tokens: int, 
                      latency: float, 
                      cost: float = 0.0, 
                      error: Optional[str] = None) -> None:
        """
        Record a model request.

        Args:
            provider_id: Provider ID
            model_id: Model ID
            prompt_tokens: Number of prompt tokens
            completion_tokens: Number of completion tokens
            latency: Request latency in seconds
            cost: Request cost in USD
            error: Error message if the request failed
        """
        # Create a request record
        request = {
            "timestamp": datetime.now().isoformat(),
            "provider_id": provider_id,
            "model_id": model_id,
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": prompt_tokens + completion_tokens,
            "latency": latency,
            "cost": cost,
            "error": error
        }

        # Add to history
        self._requests.append(request)
        if len(self._requests) > self._max_history:
            self._requests.pop(0)

        # Update usage statistics
        provider_stats = self._usage_by_provider[provider_id]
        provider_stats["total_requests"] += 1
        provider_stats["total_tokens"] += prompt_tokens + completion_tokens
        provider_stats["total_cost"] += cost
        provider_stats["total_latency"] += latency
        if error:
            provider_stats["error_count"] += 1

        # Update model statistics
        model_stats = provider_stats["models"][model_id]
        model_stats["total_requests"] += 1
        model_stats["total_tokens"] += prompt_tokens + completion_tokens
        model_stats["total_cost"] += cost
        model_stats["total_latency"] += latency
        if error:
            model_stats["error_count"] += 1

        logger.debug(f"Recorded request for {provider_id}/{model_id}: {prompt_tokens + completion_tokens} tokens, {latency:.2f}s, ${cost:.6f}")

    def get_usage_stats(self) -> Dict[str, Any]:
        """
        Get usage statistics.

        Returns:
            Dictionary with usage statistics
        """
        # Convert defaultdict to regular dict for serialization
        usage_stats = {}
        for provider_id, provider_stats in self._usage_by_provider.items():
            usage_stats[provider_id] = dict(provider_stats)
            usage_stats[provider_id]["models"] = dict(provider_stats["models"])

        return {
            "total_requests": sum(stats["total_requests"] for stats in self._usage_by_provider.values()),
            "total_tokens": sum(stats["total_tokens"] for stats in self._usage_by_provider.values()),
            "total_cost": sum(stats["total_cost"] for stats in self._usage_by_provider.values()),
            "total_latency": sum(stats["total_latency"] for stats in self._usage_by_provider.values()),
            "error_count": sum(stats["error_count"] for stats in self._usage_by_provider.values()),
            "providers": usage_stats
        }

    def get_request_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get request history.

        Args:
            limit: Maximum number of requests to return

        Returns:
            List of request records
        """
        return self._requests[-limit:]

    def clear_history(self) -> None:
        """Clear request history."""
        self._requests.clear()
        logger.info("Cleared model usage history")


# Create a global monitor instance
usage_monitor = ModelUsageMonitor()


async def record_model_usage(
    provider_id: str, 
    model_id: str, 
    prompt_tokens: int, 
    completion_tokens: int, 
    latency: float, 
    cost: float = 0.0, 
    error: Optional[str] = None
) -> None:
    """
    Record model usage.

    Args:
        provider_id: Provider ID
        model_id: Model ID
        prompt_tokens: Number of prompt tokens
        completion_tokens: Number of completion tokens
        latency: Request latency in seconds
        cost: Request cost in USD
        error: Error message if the request failed
    """
    usage_monitor.record_request(
        provider_id=provider_id,
        model_id=model_id,
        prompt_tokens=prompt_tokens,
        completion_tokens=completion_tokens,
        latency=latency,
        cost=cost,
        error=error
    )


async def get_usage_stats() -> Dict[str, Any]:
    """
    Get usage statistics.

    Returns:
        Dictionary with usage statistics
    """
    return usage_monitor.get_usage_stats()


async def get_request_history(limit: int = 100) -> List[Dict[str, Any]]:
    """
    Get request history.

    Args:
        limit: Maximum number of requests to return

    Returns:
        List of request records
    """
    return usage_monitor.get_request_history(limit)


async def clear_usage_history() -> None:
    """Clear usage history."""
    usage_monitor.clear_history()


class ModelUsageTracker:
    """Context manager for tracking model usage."""

    def __init__(self, provider_id: str, model_id: str, prompt_tokens: int = 0):
        """
        Initialize the tracker.

        Args:
            provider_id: Provider ID
            model_id: Model ID
            prompt_tokens: Number of prompt tokens
        """
        self.provider_id = provider_id
        self.model_id = model_id
        self.prompt_tokens = prompt_tokens
        self.completion_tokens = 0
        self.start_time = 0.0
        self.error = None

    async def __aenter__(self):
        """Enter the context manager."""
        self.start_time = time.time()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager."""
        latency = time.time() - self.start_time
        
        # Calculate cost (placeholder for now)
        cost = 0.0
        
        # Record error if any
        error = str(exc_val) if exc_val else None
        
        # Record usage
        await record_model_usage(
            provider_id=self.provider_id,
            model_id=self.model_id,
            prompt_tokens=self.prompt_tokens,
            completion_tokens=self.completion_tokens,
            latency=latency,
            cost=cost,
            error=error
        )
        
        # Don't suppress exceptions
        return False
