import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import { apiClient } from '@/services/apiClient';
import { DashboardLayout } from '@/components/DashboardLayout';
import { PlusCircle, Download, Clock, Edit3, Trash2, Loader2, AlertTriangle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogTrigger, DialogClose } from "@/components/ui/dialog";
// Assuming schemas for Report will be created, e.g., in '@/schemas/reports.ts'
// For now, using placeholder types.
interface ReportConfig {
  name: string;
  data_source_id: string; // Example: ID of a connected data source
  report_type: string; // e.g., "sales_summary", "user_activity"
  filters?: Record<string, any>;
  dimensions?: string[];
  metrics?: string[];
}

interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly';
  time_of_day: string; // e.g., "09:00"
  day_of_week?: number; // 0 (Sun) - 6 (Sat) for weekly
  day_of_month?: number; // 1-31 for monthly
}

interface Report {
  id: string;
  name: string;
  config: ReportConfig;
  schedule?: ReportSchedule | null;
  last_generated_at?: string | null; // ISO date string
  status?: string; // e.g., "generating", "available", "failed"
  created_at: string; // ISO date string
  // Add other relevant fields from backend response
}

const ReportsPage: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newReportConfig, setNewReportConfig] = useState<Partial<ReportConfig>>({ name: '', report_type: 'sales_summary', data_source_id: 'dummy_ds_1' });
  const [newReportSchedule, setNewReportSchedule] = useState<Partial<ReportSchedule>>({ frequency: 'daily', time_of_day: '09:00' });

  const fetchReports = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // const response = await apiClient.get<Report[]>('/reports/'); // Endpoint to list reports
      // setReports(response.data);
      // Using dummy data as backend has dummy logic for now
      const dummyReports: Report[] = [
        { id: '1', name: 'Monthly Sales Summary', config: { name: 'Monthly Sales', data_source_id: 'ds1', report_type: 'sales_summary' }, schedule: { frequency: 'monthly', time_of_day: '03:00', day_of_month: 1 }, last_generated_at: new Date().toISOString(), status: 'available', created_at: new Date().toISOString() },
        { id: '2', name: 'Weekly User Activity', config: { name: 'User Activity', data_source_id: 'ds2', report_type: 'user_activity' }, schedule: { frequency: 'weekly', time_of_day: '01:00', day_of_week: 1 }, status: 'generating', created_at: new Date().toISOString() },
      ];
      setReports(dummyReports);
    } catch (err) {
      console.error("Failed to fetch reports:", err);
      setError("Failed to load reports.");
      toast({ title: "Error", description: "Could not fetch reports.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchReports();
  }, [fetchReports]);

  const handleCreateReport = async () => {
    if (!newReportConfig.name || !newReportConfig.report_type || !newReportConfig.data_source_id) {
      toast({ title: "Missing Fields", description: "Please fill in all required report configuration fields.", variant: "destructive" });
      return;
    }
    try {
      // const response = await apiClient.post<Report>('/reports/', { 
      //   config: newReportConfig, 
      //   schedule: Object.keys(newReportSchedule).length > 0 ? newReportSchedule : null 
      // });
      // setReports(prev => [...prev, response.data]);
      toast({ title: "Report Created (Mock)", description: `Report "${newReportConfig.name}" creation initiated.` });
      setIsCreateModalOpen(false);
      setNewReportConfig({ name: '', report_type: 'sales_summary', data_source_id: 'dummy_ds_1' }); // Reset form
      setNewReportSchedule({ frequency: 'daily', time_of_day: '09:00' });
      fetchReports(); // Refresh list
    } catch (err) {
      console.error("Failed to create report:", err);
      toast({ title: "Error", description: "Could not create report.", variant: "destructive" });
    }
  };

  const handleDownloadReport = async (reportId: string, format: 'pdf' | 'csv' | 'excel') => {
    try {
      // const response = await apiClient.get(`/reports/${reportId}/export?format=${format}`, {
      //   responseType: 'blob', // Important for file downloads
      // });
      // const url = window.URL.createObjectURL(new Blob([response.data]));
      // const link = document.createElement('a');
      // link.href = url;
      // link.setAttribute('download', `report-${reportId}.${format}`);
      // document.body.appendChild(link);
      // link.click();
      // link.remove();
      toast({ title: "Download Started (Mock)", description: `Downloading report ${reportId} as ${format}.` });
    } catch (err) {
      console.error("Failed to download report:", err);
      toast({ title: "Error", description: "Could not download report.", variant: "destructive" });
    }
  };
  
  const handleDeleteReport = async (reportId: string) => {
    // Placeholder for delete functionality
    toast({ title: "Delete (Mock)", description: `Report ${reportId} would be deleted.` });
    setReports(prev => prev.filter(r => r.id !== reportId));
  };


  if (isLoading) return <DashboardLayout><div className="p-4"><Loader2 className="h-8 w-8 animate-spin text-brand-500" /> Loading reports...</div></DashboardLayout>;
  if (error) return <DashboardLayout><div className="p-4 text-red-500 bg-red-100 rounded-md flex items-center"><AlertTriangle className="h-5 w-5 mr-2" /> {error}</div></DashboardLayout>;

  return (
    <DashboardLayout>
      <div className="container mx-auto p-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Reports</h1>
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button><PlusCircle className="mr-2 h-4 w-4" /> Create Report</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Create New Report</DialogTitle>
                <DialogDescription>Configure and schedule your new report.</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="report-name" className="text-right">Name</Label>
                  <Input id="report-name" value={newReportConfig.name || ''} onChange={(e) => setNewReportConfig(prev => ({ ...prev, name: e.target.value }))} className="col-span-3" placeholder="e.g., Quarterly Sales Analysis" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="report-type" className="text-right">Type</Label>
                  <Select value={newReportConfig.report_type} onValueChange={(value) => setNewReportConfig(prev => ({ ...prev, report_type: value }))}>
                    <SelectTrigger className="col-span-3"><SelectValue placeholder="Select report type" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sales_summary">Sales Summary</SelectItem>
                      <SelectItem value="user_activity">User Activity</SelectItem>
                      <SelectItem value="custom_query">Custom Query (Advanced)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                 <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="data-source" className="text-right">Data Source</Label>
                   <Select value={newReportConfig.data_source_id} onValueChange={(value) => setNewReportConfig(prev => ({ ...prev, data_source_id: value }))}>
                    <SelectTrigger className="col-span-3"><SelectValue placeholder="Select data source" /></SelectTrigger>
                    <SelectContent>
                      {/* TODO: Populate with actual data sources */}
                      <SelectItem value="dummy_ds_1">Main Sales Database</SelectItem>
                      <SelectItem value="dummy_ds_2">User Engagement Logs</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <h3 className="text-lg font-medium mt-2 col-span-4">Schedule (Optional)</h3>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="schedule-frequency" className="text-right">Frequency</Label>
                  <Select value={newReportSchedule.frequency} onValueChange={(value) => setNewReportSchedule(prev => ({ ...prev, frequency: value as any }))}>
                    <SelectTrigger className="col-span-3"><SelectValue placeholder="Select frequency" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="schedule-time" className="text-right">Time (UTC)</Label>
                  <Input id="schedule-time" type="time" value={newReportSchedule.time_of_day || ''} onChange={(e) => setNewReportSchedule(prev => ({ ...prev, time_of_day: e.target.value }))} className="col-span-3" />
                </div>
              </div>
              <DialogFooter>
                <DialogClose asChild><Button variant="outline">Cancel</Button></DialogClose>
                <Button onClick={handleCreateReport}>Create Report</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {reports.length === 0 ? (
          <p>No reports found. Create your first report to get started!</p>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Your Reports</CardTitle>
              <CardDescription>Manage and view your generated reports.</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Generated</TableHead>
                    <TableHead>Schedule</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell className="font-medium">{report.name}</TableCell>
                      <TableCell>{report.status || 'N/A'}</TableCell>
                      <TableCell>{report.last_generated_at ? new Date(report.last_generated_at).toLocaleString() : 'Never'}</TableCell>
                      <TableCell>{report.schedule ? `${report.schedule.frequency} at ${report.schedule.time_of_day}` : 'Not scheduled'}</TableCell>
                      <TableCell className="text-right space-x-2">
                        <Button variant="outline" size="sm" onClick={() => handleDownloadReport(report.id, 'pdf')} disabled={report.status !== 'available'}><Download className="h-4 w-4 mr-1" /> PDF</Button>
                        {/* <Button variant="outline" size="sm"><Edit3 className="h-4 w-4 mr-1" /> Edit</Button> */}
                        <Button variant="ghost" size="sm" onClick={() => handleDeleteReport(report.id)}><Trash2 className="h-4 w-4 text-red-500" /></Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ReportsPage;
