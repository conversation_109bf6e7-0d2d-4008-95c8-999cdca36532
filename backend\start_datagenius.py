#!/usr/bin/env python3
"""
Simple script to start the Datagenius server with correct environment.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Start the Datagenius server."""
    # Get the backend directory
    backend_dir = Path(__file__).parent
    
    # Change to backend directory
    os.chdir(backend_dir)
    
    # Set PYTHONPATH to include the backend directory
    env = os.environ.copy()
    env['PYTHONPATH'] = str(backend_dir)
    
    print(f"Starting Datagenius server from: {backend_dir}")
    print(f"PYTHONPATH: {env['PYTHONPATH']}")
    
    # Kill any existing servers first
    try:
        import psutil
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info.get('cmdline', [])
                if cmdline and 'uvicorn' in ' '.join(cmdline) and 'app.main:app' in ' '.join(cmdline):
                    print(f"Killing existing server process: {proc.info['pid']}")
                    proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
    except ImportError:
        print("psutil not available, skipping process cleanup")
    
    # Start the server
    cmd = [sys.executable, "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    print(f"Running: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, env=env, cwd=backend_dir)
    except KeyboardInterrupt:
        print("\nServer stopped by user")

if __name__ == "__main__":
    main()
