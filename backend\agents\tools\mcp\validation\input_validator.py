"""
Comprehensive Input Validation Framework for MCP Tools.

This module provides a robust, Pydantic-based input validation system for all MCP tools
with detailed error messages, type checking, sanitization, and security features.
"""

import logging
import re
import html
import inspect
import importlib
from typing import Dict, Any, Optional, List, Union, Type, Callable
from pathlib import Path
from pydantic import BaseModel, Field, ValidationError, field_validator, model_validator
from datetime import datetime
try:
    import bleach
except ImportError:
    bleach = None
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class ValidationResult(BaseModel):
    """Result of input validation."""
    
    is_valid: bool = Field(description="Whether the input is valid")
    validated_data: Optional[Dict[str, Any]] = Field(default=None, description="Validated and sanitized data")
    errors: List[str] = Field(default_factory=list, description="Validation error messages")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    sanitized_fields: List[str] = Field(default_factory=list, description="Fields that were sanitized")


class BaseInputSchema(BaseModel):
    """Base input schema for all MCP tools."""
    
    # Common fields
    persona_id: Optional[str] = Field(default=None, description="Agent/persona identifier")
    agent_id: Optional[str] = Field(default=None, description="Agent identifier (alternative)")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")
    
    # AI provider settings
    provider: Optional[str] = Field(default=None, description="AI provider")
    model: Optional[str] = Field(default=None, description="AI model")
    api_key: Optional[str] = Field(default=None, description="API key")
    
    # Performance settings
    timeout: Optional[int] = Field(default=None, ge=5, le=600, description="Timeout in seconds")
    max_retries: Optional[int] = Field(default=None, ge=1, le=10, description="Maximum retry attempts")
    
    @field_validator('provider')
    @classmethod
    def validate_provider(cls, v):
        if v is not None:
            allowed_providers = ["openai", "groq", "google", "ollama", "anthropic"]
            if v.lower() not in allowed_providers:
                raise ValueError(f"Provider must be one of {allowed_providers}")
        return v.lower() if v else v

    @field_validator('api_key')
    @classmethod
    def validate_api_key(cls, v):
        if v is not None:
            # Basic API key format validation
            if len(v) < 10:
                raise ValueError("API key appears to be too short")
            if not re.match(r'^[a-zA-Z0-9_-]+$', v):
                raise ValueError("API key contains invalid characters")
        return v


class FileInputSchema(BaseInputSchema):
    """Input schema for tools that work with files."""
    
    file_path: str = Field(description="Path to the data file")
    
    @field_validator('file_path')
    @classmethod
    def validate_file_path(cls, v):
        # Sanitize file path
        v = str(v).strip()

        # Security checks
        if '..' in v:
            raise ValueError("File path cannot contain '..' for security reasons")
        if v.startswith('/') and not v.startswith('/tmp/') and not v.startswith('/uploads/'):
            raise ValueError("Absolute paths are not allowed except in designated directories")

        # Path format validation
        if not re.match(r'^[a-zA-Z0-9_/.-]+$', v):
            raise ValueError("File path contains invalid characters")

        return v


class QueryInputSchema(BaseInputSchema):
    """Input schema for tools that accept queries."""
    
    query: str = Field(description="Query or prompt text")
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        # Sanitize query
        v = str(v).strip()

        # Length validation
        if len(v) < 1:
            raise ValueError("Query cannot be empty")
        if len(v) > 10000:
            raise ValueError("Query is too long (maximum 10,000 characters)")

        # Content validation
        if re.search(r'<script[^>]*>.*?</script>', v, re.IGNORECASE | re.DOTALL):
            raise ValueError("Query contains potentially malicious script content")

        return v


class IntentDetectionInputSchema(QueryInputSchema):
    """Input schema for intent detection tools."""
    
    message: str = Field(description="Message to analyze for intent")
    enable_llm_fallback: Optional[bool] = Field(default=None, description="Enable LLM fallback")
    confidence_threshold: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Confidence threshold")
    
    @field_validator('message')
    @classmethod
    def validate_message(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Message cannot be empty")
        if len(v) > 5000:
            raise ValueError("Message is too long (maximum 5,000 characters)")
        return v


class LanguageDetectionInputSchema(BaseInputSchema):
    """Input schema for language detection tools."""
    
    text: str = Field(description="Text to analyze for language")
    confidence_threshold: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Confidence threshold")
    
    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Text cannot be empty")
        if len(v) > 50000:
            raise ValueError("Text is too long (maximum 50,000 characters)")
        return v


class PandasAIInputSchema(FileInputSchema, QueryInputSchema):
    """Input schema for PandasAI tools."""

    data_source: Optional[str] = Field(default=None, description="Alternative data source")

    @model_validator(mode='after')
    def validate_data_source(self):
        if not self.file_path and not self.data_source:
            raise ValueError("Either file_path or data_source must be provided")

        return self


class PandasAIVisualizationInputSchema(BaseInputSchema):
    """Input schema specifically for PandasAI visualization tools."""

    file_path: Optional[str] = Field(default=None, description="Path to the data file")
    data_source: Optional[Union[str, Dict[str, Any]]] = Field(default=None, description="Data source information")
    query: Optional[str] = Field(default=None, description="Visualization query")
    prompt: Optional[str] = Field(default=None, description="Visualization prompt (alias for query)")
    api_key: Optional[str] = Field(default=None, description="API key for the provider")
    provider: Optional[str] = Field(default="openai", description="AI provider to use")
    model: Optional[str] = Field(default=None, description="AI model to use")
    user_id: Optional[str] = Field(default=None, description="User ID")
    persona_id: Optional[str] = Field(default=None, description="Persona ID")
    conversation_id: Optional[str] = Field(default=None, description="Conversation ID")
    store_in_memory: Optional[bool] = Field(default=True, description="Whether to store in memory")

    @model_validator(mode='after')
    def validate_visualization_input(self):
        # Ensure we have either query or prompt
        if not self.query and not self.prompt:
            raise ValueError("Either query or prompt must be provided")

        # If both are provided, use query as primary
        if not self.query and self.prompt:
            self.query = self.prompt

        # Ensure we have some way to access data
        if not self.file_path and not self.data_source:
            raise ValueError("Either file_path or data_source must be provided")

        return self


class DataVisualizationInputSchema(FileInputSchema):
    """Input schema for data visualization tools."""
    
    prompt: str = Field(description="Visualization prompt")
    chart_type: Optional[str] = Field(default=None, description="Preferred chart type")
    color_scheme: Optional[str] = Field(default=None, description="Color scheme preference")
    
    @field_validator('prompt')
    @classmethod
    def validate_prompt(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Prompt cannot be empty")
        if len(v) > 2000:
            raise ValueError("Prompt is too long (maximum 2,000 characters)")
        return v

    @field_validator('chart_type')
    @classmethod
    def validate_chart_type(cls, v):
        if v is not None:
            allowed_types = ["bar", "line", "scatter", "pie", "histogram", "box", "heatmap"]
            if v.lower() not in allowed_types:
                raise ValueError(f"Chart type must be one of {allowed_types}")
        return v.lower() if v else v


class DataStorytellingInputSchema(FileInputSchema):
    """Input schema for data storytelling tools."""
    
    topic: str = Field(description="Topic for the data story")
    narrative_style: Optional[str] = Field(default=None, description="Preferred narrative style")
    audience_level: Optional[str] = Field(default=None, description="Target audience level")
    
    @field_validator('topic')
    @classmethod
    def validate_topic(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Topic cannot be empty")
        if len(v) > 500:
            raise ValueError("Topic is too long (maximum 500 characters)")
        return v

    @field_validator('narrative_style')
    @classmethod
    def validate_narrative_style(cls, v):
        if v is not None:
            allowed_styles = ["conversational", "analytical", "engaging", "structured", "informative"]
            if v.lower() not in allowed_styles:
                raise ValueError(f"Narrative style must be one of {allowed_styles}")
        return v.lower() if v else v

    @field_validator('audience_level')
    @classmethod
    def validate_audience_level(cls, v):
        if v is not None:
            allowed_levels = ["general", "business", "technical", "academic"]
            if v.lower() not in allowed_levels:
                raise ValueError(f"Audience level must be one of {allowed_levels}")
        return v.lower() if v else v


class TextProcessingInputSchema(BaseInputSchema):
    """Input schema for text processing tools."""

    text: str = Field(description="Text to process")
    operation: str = Field(description="Processing operation")
    options: Dict[str, Any] = Field(default_factory=dict, description="Processing options")

    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Text cannot be empty")
        if len(v) > 100000:
            raise ValueError("Text is too long (maximum 100,000 characters)")
        return v

    @field_validator('operation')
    @classmethod
    def validate_operation(cls, v):
        allowed_operations = [
            "summarize", "extract_keywords", "analyze_sentiment",
            "classify", "translate", "clean", "tokenize"
        ]
        if v.lower() not in allowed_operations:
            raise ValueError(f"Operation must be one of {allowed_operations}")
        return v.lower()


class MarketingContentInputSchema(BaseInputSchema):
    """Input schema for marketing content generation tools."""

    content_type: str = Field(description="Type of marketing content to generate")
    brand_description: Optional[str] = Field(default="", description="Description of the brand")
    target_audience: Optional[str] = Field(default="", description="Description of the target audience")
    products_services: Optional[str] = Field(default="", description="Description of products or services")
    marketing_goals: Optional[str] = Field(default="", description="Marketing goals and objectives")
    existing_content: Optional[str] = Field(default="", description="Existing marketing content")
    keywords: Optional[str] = Field(default="", description="Keywords to target")
    suggested_topics: Optional[str] = Field(default="", description="Suggested topics for content")
    tone: Optional[str] = Field(default="professional", description="Tone of the content")
    temperature: Optional[float] = Field(default=0.7, ge=0.0, le=2.0, description="Temperature for content generation")
    is_regeneration: Optional[bool] = Field(default=False, description="Whether this is a regeneration request")
    prompt_override: Optional[str] = Field(default=None, description="Optional raw prompt to use instead of templates")
    file_data: Optional[Dict[str, Any]] = Field(default=None, description="Optional file data to incorporate")
    is_first_conversation: Optional[bool] = Field(default=False, description="Whether this is the first conversation")
    has_data_source: Optional[bool] = Field(default=False, description="Whether data source is available")

    @field_validator('content_type')
    @classmethod
    def validate_content_type(cls, v):
        """Validate content type with flexible marketing content validation."""
        import re

        if not v or not isinstance(v, str):
            raise ValueError("Content type must be a non-empty string")

        # Clean and normalize the content type
        normalized = v.lower().strip().replace(' ', '_')

        # Check length constraints
        if len(normalized) < 3 or len(normalized) > 50:
            raise ValueError("Content type must be between 3 and 50 characters")

        # Check for valid characters (alphanumeric, underscore, hyphen)
        if not re.match(r'^[a-z0-9_-]+$', normalized):
            raise ValueError("Content type can only contain letters, numbers, underscores, and hyphens")

        # Marketing-related keywords that indicate valid content types
        marketing_keywords = [
            'strategy', 'campaign', 'content', 'social', 'media', 'seo', 'marketing',
            'brand', 'advertising', 'promotion', 'analysis', 'plan', 'brief',
            'guide', 'template', 'report', 'audit', 'research', 'positioning',
            'messaging', 'creative', 'copy', 'email', 'newsletter', 'blog',
            'video', 'infographic', 'presentation', 'proposal', 'pitch',
            'competitive', 'market', 'customer', 'persona', 'journey',
            'funnel', 'conversion', 'engagement', 'awareness', 'lead',
            'acquisition', 'retention', 'loyalty', 'influencer', 'pr',
            'public_relations', 'crisis', 'communication', 'launch',
            'product', 'service', 'event', 'webinar', 'case_study'
        ]

        # Check if the content type contains marketing-related keywords
        if not any(keyword in normalized for keyword in marketing_keywords):
            raise ValueError(f"Content type '{v}' does not appear to be marketing-related. Please use a marketing content type.")

        return normalized

    @field_validator('tone')
    @classmethod
    def validate_tone(cls, v):
        if v is None:
            return "professional"
        return str(v).strip() or "professional"


class ConversationInputSchema(BaseInputSchema):
    """Input schema for conversation handling tools."""

    message: str = Field(description="User message to respond to")
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list, description="Previous conversation messages")
    user_context: Dict[str, Any] = Field(default_factory=dict, description="User context information")
    intent_type: Optional[str] = Field(default="general_question", description="Type of intent detected")
    confidence: Optional[float] = Field(default=0.8, ge=0.0, le=1.0, description="Confidence in intent detection")
    is_continuing_conversation: Optional[bool] = Field(default=False, description="Whether this continues an existing conversation")
    temperature: Optional[float] = Field(default=0.7, ge=0.0, le=2.0, description="Temperature for response generation")

    @field_validator('message')
    @classmethod
    def validate_message(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Message cannot be empty")
        if len(v) > 100000:
            raise ValueError("Message is too long (maximum 100,000 characters)")
        return v


class CodeExecutionInputSchema(BaseInputSchema):
    """Input schema for code execution tools."""

    code: str = Field(description="Code to execute")
    language: Optional[str] = Field(default="python", description="Programming language")
    execution_context: Dict[str, Any] = Field(default_factory=dict, description="Execution context")

    @field_validator('code')
    @classmethod
    def validate_code(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Code cannot be empty")
        if len(v) > 50000:
            raise ValueError("Code is too long (maximum 50,000 characters)")
        return v

    @field_validator('language')
    @classmethod
    def validate_language(cls, v):
        allowed_languages = ["python", "javascript", "sql", "bash"]
        if v.lower() not in allowed_languages:
            raise ValueError(f"Language must be one of {allowed_languages}")
        return v.lower()


class DataAnalysisInputSchema(FileInputSchema, QueryInputSchema):
    """Input schema for data analysis tools."""

    analysis_type: Optional[str] = Field(default="general", description="Type of analysis to perform")
    include_visualizations: Optional[bool] = Field(default=True, description="Whether to include visualizations")
    output_format: Optional[str] = Field(default="comprehensive", description="Output format preference")

    @field_validator('analysis_type')
    @classmethod
    def validate_analysis_type(cls, v):
        allowed_types = ["general", "statistical", "exploratory", "predictive", "descriptive"]
        if v.lower() not in allowed_types:
            return "general"  # Default fallback
        return v.lower()


class SentimentAnalysisInputSchema(BaseInputSchema):
    """Input schema for sentiment analysis tools."""

    text: str = Field(description="Text to analyze for sentiment")
    analysis_depth: Optional[str] = Field(default="standard", description="Depth of sentiment analysis")
    include_emotions: Optional[bool] = Field(default=False, description="Whether to include emotion detection")

    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Text cannot be empty")
        if len(v) > 50000:
            raise ValueError("Text is too long (maximum 50,000 characters)")
        return v


class TextClassificationInputSchema(BaseInputSchema):
    """Input schema for text classification tools."""

    text: str = Field(description="Text to classify")
    classification_type: Optional[str] = Field(default="general", description="Type of classification")
    categories: Optional[List[str]] = Field(default=None, description="Specific categories to classify into")
    confidence_threshold: Optional[float] = Field(default=0.5, ge=0.0, le=1.0, description="Confidence threshold")

    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Text cannot be empty")
        if len(v) > 50000:
            raise ValueError("Text is too long (maximum 50,000 characters)")
        return v


class DocumentEmbeddingInputSchema(BaseInputSchema):
    """Input schema for document embedding tools."""

    document_content: Optional[str] = Field(default=None, description="Document content to embed")
    file_path: Optional[str] = Field(default=None, description="Path to document file")
    embedding_model: Optional[str] = Field(default="default", description="Embedding model to use")
    chunk_size: Optional[int] = Field(default=1000, ge=100, le=5000, description="Chunk size for processing")

    @model_validator(mode='after')
    def validate_input_source(self):
        if not self.document_content and not self.file_path:
            raise ValueError("Either document_content or file_path must be provided")
        return self


class KnowledgeGraphInputSchema(BaseInputSchema):
    """Input schema for knowledge graph tools."""

    query: str = Field(description="Query for knowledge graph")
    graph_type: Optional[str] = Field(default="general", description="Type of knowledge graph")
    max_results: Optional[int] = Field(default=10, ge=1, le=100, description="Maximum number of results")
    include_relationships: Optional[bool] = Field(default=True, description="Whether to include relationships")

    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Query cannot be empty")
        if len(v) > 1000:
            raise ValueError("Query is too long (maximum 1,000 characters)")
        return v


class PersonaMarketplaceInputSchema(BaseInputSchema):
    """Input schema for persona marketplace tools."""

    user_id: Optional[str] = Field(default=None, description="User ID for personalized results")
    category: Optional[str] = Field(default=None, description="Persona category filter")
    include_purchased: Optional[bool] = Field(default=True, description="Whether to include purchased personas")
    include_available: Optional[bool] = Field(default=True, description="Whether to include available personas")


class UserKnowledgeGraphInputSchema(BaseInputSchema):
    """Input schema for user knowledge graph tools."""

    user_id: str = Field(description="User ID to get knowledge graph for")
    graph_depth: Optional[int] = Field(default=2, ge=1, le=5, description="Depth of knowledge graph")
    include_interactions: Optional[bool] = Field(default=True, description="Whether to include user interactions")
    time_range: Optional[str] = Field(default="all", description="Time range for data")


class NaturalLanguageQueryInputSchema(FileInputSchema):
    """Input schema for natural language query tools."""

    natural_query: str = Field(description="Natural language query")
    query_type: Optional[str] = Field(default="general", description="Type of query")
    return_sql: Optional[bool] = Field(default=False, description="Whether to return generated SQL")

    @field_validator('natural_query')
    @classmethod
    def validate_natural_query(cls, v):
        v = str(v).strip()
        if len(v) < 1:
            raise ValueError("Natural query cannot be empty")
        if len(v) > 2000:
            raise ValueError("Natural query is too long (maximum 2,000 characters)")
        return v


class DeploymentInputSchema(BaseInputSchema):
    """Input schema for deployment tools."""

    deployment_type: str = Field(description="Type of deployment")
    target_environment: Optional[str] = Field(default="staging", description="Target deployment environment")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Deployment configuration")

    @field_validator('deployment_type')
    @classmethod
    def validate_deployment_type(cls, v):
        allowed_types = ["application", "model", "service", "infrastructure"]
        if v.lower() not in allowed_types:
            raise ValueError(f"Deployment type must be one of {allowed_types}")
        return v.lower()


class InputSanitizer:
    """Input sanitization utilities."""
    
    @staticmethod
    def sanitize_html(text: str) -> str:
        """Sanitize HTML content."""
        if not text:
            return text
        
        # Remove potentially dangerous HTML tags and attributes
        allowed_tags = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li']
        allowed_attributes = {}
        
        return bleach.clean(text, tags=allowed_tags, attributes=allowed_attributes, strip=True)
    
    @staticmethod
    def sanitize_sql(text: str) -> str:
        """Sanitize potential SQL injection attempts."""
        if not text:
            return text
        
        # Remove common SQL injection patterns
        sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE)\b)",
            r"(--|#|/\*|\*/)",
            r"(\b(UNION|OR|AND)\s+\d+\s*=\s*\d+)",
            r"(\'\s*(OR|AND)\s*\'\w*\'\s*=\s*\'\w*)",
        ]
        
        for pattern in sql_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        return text
    
    @staticmethod
    def sanitize_file_path(path: str) -> str:
        """Sanitize file paths."""
        if not path:
            return path
        
        # Remove dangerous path components
        path = path.replace('..', '').replace('//', '/')
        path = re.sub(r'[<>:"|?*]', '', path)  # Remove Windows invalid chars
        
        return path.strip()
    
    @staticmethod
    def sanitize_text(text: str) -> str:
        """General text sanitization."""
        if not text:
            return text
        
        # HTML escape
        text = html.escape(text)
        
        # Remove null bytes
        text = text.replace('\x00', '')
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text


class SchemaInferenceEngine:
    """
    Engine for automatically inferring schemas from tool definitions.
    """

    @staticmethod
    def infer_schema_from_tool(tool_class) -> Type[BaseModel]:
        """
        Infer a Pydantic schema from a tool's input_schema definition.

        Args:
            tool_class: The tool class to analyze

        Returns:
            Dynamically created Pydantic schema class
        """
        try:
            # Get the tool's input schema
            if hasattr(tool_class, 'input_schema'):
                input_schema = tool_class.input_schema
            elif hasattr(tool_class, '__init__'):
                # Try to instantiate and get schema
                try:
                    instance = tool_class()
                    input_schema = getattr(instance, 'input_schema', None)
                except:
                    input_schema = None
            else:
                input_schema = None

            if not input_schema or not isinstance(input_schema, dict):
                return BaseInputSchema

            # Extract properties from JSON schema
            properties = input_schema.get('properties', {})
            required_fields = input_schema.get('required', [])

            # Create dynamic Pydantic model
            field_definitions = {}

            for field_name, field_def in properties.items():
                field_type = SchemaInferenceEngine._json_type_to_python_type(field_def)
                is_required = field_name in required_fields

                if is_required:
                    field_definitions[field_name] = (field_type, Field(description=field_def.get('description', '')))
                else:
                    default_value = SchemaInferenceEngine._get_default_value(field_def)
                    field_definitions[field_name] = (field_type, Field(default=default_value, description=field_def.get('description', '')))

            # Add base fields from BaseInputSchema
            base_fields = BaseInputSchema.__annotations__
            for field_name, field_type in base_fields.items():
                if field_name not in field_definitions:
                    # Use model_fields for Pydantic v2 compatibility
                    base_field_info = BaseInputSchema.model_fields.get(field_name)
                    if base_field_info:
                        field_definitions[field_name] = (field_type, base_field_info)
                    else:
                        field_definitions[field_name] = (field_type, Field(default=None))

            # Create dynamic class with model configuration to avoid namespace conflicts
            dynamic_class_name = f"Dynamic{tool_class.__name__}Schema"

            # Create class attributes with model config
            class_attrs = {
                '__annotations__': {k: v[0] for k, v in field_definitions.items()},
                'model_config': {'protected_namespaces': ()},  # Disable protected namespace warnings
                **{k: v[1] for k, v in field_definitions.items()}
            }

            dynamic_schema = type(dynamic_class_name, (BaseInputSchema,), class_attrs)

            return dynamic_schema

        except Exception as e:
            logger.warning(f"Failed to infer schema for {tool_class.__name__}: {e}")
            return BaseInputSchema

    @staticmethod
    def _json_type_to_python_type(field_def: Dict[str, Any]) -> Type:
        """Convert JSON schema type to Python type."""
        json_type = field_def.get('type', 'string')

        type_mapping = {
            'string': str,
            'integer': int,
            'number': float,
            'boolean': bool,
            'array': List[Any],
            'object': Dict[str, Any],
            'null': type(None)
        }

        # Handle union types (when type is a list)
        if isinstance(json_type, list):
            # Convert each type in the union
            union_types = []
            for single_type in json_type:
                if isinstance(single_type, str):
                    mapped_type = type_mapping.get(single_type, str)
                    union_types.append(mapped_type)

            # Create Union type if multiple types, otherwise use single type
            if len(union_types) > 1:
                from typing import Union
                python_type = Union[tuple(union_types)]
            elif len(union_types) == 1:
                python_type = union_types[0]
            else:
                python_type = str  # fallback
        else:
            # Handle single type
            python_type = type_mapping.get(json_type, str)

        # Handle optional fields
        if not field_def.get('required', True):
            python_type = Optional[python_type]

        return python_type

    @staticmethod
    def _get_default_value(field_def: Dict[str, Any]) -> Any:
        """Get appropriate default value for a field."""
        if 'default' in field_def:
            return field_def['default']

        json_type = field_def.get('type', 'string')
        defaults = {
            'string': "",
            'integer': 0,
            'number': 0.0,
            'boolean': False,
            'array': [],
            'object': {}
        }

        return defaults.get(json_type, None)


class ToolRegistryIntegration:
    """
    Integration with tool registries for automatic schema discovery.
    """

    @staticmethod
    def discover_tools_from_registry() -> Dict[str, Type]:
        """
        Discover tools from various registries.

        Returns:
            Dictionary mapping tool names to tool classes
        """
        discovered_tools = {}

        try:
            # Try to import and discover from AVAILABLE_TOOLS
            from .. import AVAILABLE_TOOLS
            discovered_tools.update(AVAILABLE_TOOLS)
        except ImportError:
            pass

        try:
            # Try to discover from integration registry
            from ..integration.system_integration import MCPToolRegistry as IntegrationRegistry
            # This would need additional methods to expose registered tools
        except ImportError:
            pass

        try:
            # Try to discover from __init__ AVAILABLE_TOOLS
            from .. import AVAILABLE_TOOLS
            discovered_tools.update(AVAILABLE_TOOLS)
        except ImportError:
            pass

        return discovered_tools

    @staticmethod
    def scan_tool_modules() -> Dict[str, Type]:
        """
        Scan tool modules for MCP tools.

        Returns:
            Dictionary mapping tool names to tool classes
        """
        discovered_tools = {}

        try:
            import os
            from pathlib import Path

            # Get the tools directory
            tools_dir = Path(__file__).parent.parent

            # Scan for Python files
            for py_file in tools_dir.glob("*.py"):
                if py_file.name.startswith('_') or py_file.name == 'base.py':
                    continue

                try:
                    module_name = f"agents.tools.mcp.{py_file.stem}"
                    module = importlib.import_module(module_name)

                    # Look for classes that might be MCP tools
                    for name, obj in inspect.getmembers(module, inspect.isclass):
                        if (name.endswith('Tool') and
                            hasattr(obj, 'execute') and
                            hasattr(obj, 'name')):
                            try:
                                instance = obj()
                                tool_name = getattr(instance, 'name', name.lower())
                                discovered_tools[tool_name] = obj
                            except:
                                pass

                except Exception as e:
                    logger.debug(f"Failed to scan module {py_file}: {e}")

        except Exception as e:
            logger.warning(f"Failed to scan tool modules: {e}")

        return discovered_tools


class InputValidator:
    """
    Comprehensive input validator for MCP tools with auto-discovery capabilities.

    Provides validation, sanitization, and error handling for all tool inputs.
    Features automatic schema discovery and inference for new tools.
    Integrated with Phase 2 Schema Registry System.
    """

    def __init__(self):
        """Initialize the input validator with schema registry integration."""
        from .schema_registry import MCPSchemaRegistry
        from .auto_discovery import ToolDiscoveryEngine

        # Initialize Phase 2 components
        self.schema_registry = MCPSchemaRegistry()
        self.discovery_engine = ToolDiscoveryEngine(self.schema_registry)

        # Run initial discovery
        self._initialize_with_discovery()

    # Static schema mapping for known tools (for performance and backward compatibility)
    STATIC_TOOL_SCHEMAS = {
        # Intent and Language Detection
        'intent_detection': IntentDetectionInputSchema,
        'intent_analysis': IntentDetectionInputSchema,
        'language_detection': LanguageDetectionInputSchema,

        # Data Analysis and PandasAI Tools
        'pandasai_analysis': PandasAIInputSchema,
        'pandasai_query': PandasAIInputSchema,
        'pandasai_visualization': PandasAIVisualizationInputSchema,
        'statistical_analysis': PandasAIInputSchema,
        'data_analysis': DataAnalysisInputSchema,
        'data_querying': PandasAIInputSchema,  # Uses similar schema to PandasAI
        'data_cleaning': PandasAIInputSchema,  # Uses similar schema to PandasAI
        'advanced_query': PandasAIInputSchema,  # Uses similar schema to PandasAI
        'data_filtering': PandasAIInputSchema,  # Uses similar schema to PandasAI
        'natural_language_query': NaturalLanguageQueryInputSchema,

        # Visualization and Storytelling
        'data_visualization': DataVisualizationInputSchema,
        'advanced_visualization': DataVisualizationInputSchema,
        'data_storytelling': DataStorytellingInputSchema,

        # Text Processing and Analysis
        'text_processing': TextProcessingInputSchema,
        'process_text': TextProcessingInputSchema,  # Alternative name for text processing
        'sentiment_analysis': SentimentAnalysisInputSchema,
        'text_classification': TextClassificationInputSchema,

        # Marketing and Conversation
        'generate_marketing_content': MarketingContentInputSchema,
        'handle_conversation': ConversationInputSchema,

        # Code and Execution
        'execute_code': CodeExecutionInputSchema,

        # Data Access and Files
        'data_access': FileInputSchema,
        'document_embedding': DocumentEmbeddingInputSchema,
        'mem0_document_embedding': DocumentEmbeddingInputSchema,  # Uses similar schema

        # Knowledge and User Management
        'knowledge_graph': KnowledgeGraphInputSchema,
        'get_persona_marketplace_info': PersonaMarketplaceInputSchema,
        'get_user_knowledge_graph': UserKnowledgeGraphInputSchema,

        # Deployment and Infrastructure
        'deployment': DeploymentInputSchema,
    }

    def __init__(self, enable_sanitization: bool = True, enable_auto_discovery: bool = True):
        """
        Initialize the input validator with Phase 2 schema registry integration.

        Args:
            enable_sanitization: Whether to enable input sanitization
            enable_auto_discovery: Whether to enable automatic schema discovery
        """
        self.enable_sanitization = enable_sanitization
        self.enable_auto_discovery = enable_auto_discovery
        self.sanitizer = InputSanitizer()

        # Phase 2 Schema Registry Integration
        from .schema_registry import MCPSchemaRegistry
        from .auto_discovery import ToolDiscoveryEngine

        self.schema_registry = MCPSchemaRegistry()
        self.discovery_engine = ToolDiscoveryEngine(self.schema_registry)

        # Dynamic schema cache (legacy support)
        self._dynamic_schemas: Dict[str, Type[BaseModel]] = {}
        self._discovery_attempted: set = set()

        # Legacy components for backward compatibility
        self.inference_engine = SchemaInferenceEngine()
        self.registry_integration = ToolRegistryIntegration()

        # Initialize with static schemas
        self._tool_schemas = self.STATIC_TOOL_SCHEMAS.copy()

        # Load configuration-based schemas
        self._load_configuration_schemas()

        # Perform Phase 2 discovery if enabled
        if self.enable_auto_discovery:
            self._run_phase2_discovery()
            # Also run legacy discovery for backward compatibility
            self._discover_and_register_tools()

    def _run_phase2_discovery(self):
        """Run Phase 2 schema registry discovery."""
        try:
            discovery_results = self.discovery_engine.run_full_discovery()
            logger.info(f"Phase 2 schema registry discovery completed: {discovery_results}")
        except Exception as e:
            logger.warning(f"Phase 2 discovery failed: {e}")

    def validate_input(self, tool_name: str, input_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate input data for a specific tool with automatic schema discovery.

        Args:
            tool_name: Name of the tool
            input_data: Input data to validate

        Returns:
            ValidationResult: Validation result with errors and sanitized data
        """
        try:
            # Get appropriate schema for the tool (with auto-discovery)
            schema_class = self._get_schema_for_tool(tool_name)
            
            # Sanitize input data if enabled
            if self.enable_sanitization:
                sanitized_data, sanitized_fields = self._sanitize_input_data(input_data)
            else:
                sanitized_data = input_data.copy()
                sanitized_fields = []
            
            # Validate using Pydantic schema
            validated_instance = schema_class(**sanitized_data)
            validated_data = validated_instance.model_dump(exclude_none=True)
            
            return ValidationResult(
                is_valid=True,
                validated_data=validated_data,
                errors=[],
                warnings=[],
                sanitized_fields=sanitized_fields
            )
            
        except ValidationError as e:
            # Extract detailed error messages
            errors = []
            for error in e.errors():
                field = '.'.join(str(loc) for loc in error['loc'])
                message = error['msg']
                errors.append(f"{field}: {message}")
            
            return ValidationResult(
                is_valid=False,
                validated_data=None,
                errors=errors,
                warnings=[],
                sanitized_fields=[]
            )
            
        except Exception as e:
            logger.error(f"Unexpected validation error for tool {tool_name}: {e}")
            return ValidationResult(
                is_valid=False,
                validated_data=None,
                errors=[f"Validation failed: {str(e)}"],
                warnings=[],
                sanitized_fields=[]
            )
    
    def _sanitize_input_data(self, input_data: Dict[str, Any]) -> tuple[Dict[str, Any], List[str]]:
        """
        Sanitize input data.
        
        Args:
            input_data: Raw input data
            
        Returns:
            Tuple of (sanitized_data, sanitized_fields)
        """
        sanitized_data = {}
        sanitized_fields = []
        
        for key, value in input_data.items():
            if isinstance(value, str):
                original_value = value
                
                # Apply appropriate sanitization based on field name
                if key in ['file_path', 'data_source']:
                    value = self.sanitizer.sanitize_file_path(value)
                elif key in ['query', 'prompt', 'message', 'text', 'topic']:
                    value = self.sanitizer.sanitize_text(value)
                    value = self.sanitizer.sanitize_sql(value)
                    value = self.sanitizer.sanitize_html(value)
                else:
                    value = self.sanitizer.sanitize_text(value)
                
                # Track if field was modified
                if value != original_value:
                    sanitized_fields.append(key)
                    
            sanitized_data[key] = value
        
        return sanitized_data, sanitized_fields
    
    def _get_schema_for_tool(self, tool_name: str) -> Type[BaseModel]:
        """
        Get the Pydantic schema class for a specific tool with Phase 2 registry integration.

        Args:
            tool_name: Name of the tool

        Returns:
            Appropriate schema class for the tool
        """
        # Check Phase 2 schema registry first
        if hasattr(self, 'schema_registry'):
            registry_schema = self.schema_registry.get_schema(tool_name)
            if registry_schema:
                return registry_schema

        # Check static schemas (backward compatibility)
        if tool_name in self._tool_schemas:
            return self._tool_schemas[tool_name]

        # Check dynamic cache (legacy)
        if tool_name in self._dynamic_schemas:
            return self._dynamic_schemas[tool_name]

        # Auto-discover if enabled and not already attempted (legacy fallback)
        if (self.enable_auto_discovery and
            tool_name not in self._discovery_attempted):

            self._discovery_attempted.add(tool_name)
            discovered_schema = self._discover_schema_for_tool(tool_name)

            if discovered_schema and discovered_schema != BaseInputSchema:
                self._dynamic_schemas[tool_name] = discovered_schema
                logger.info(f"Auto-discovered schema for tool: {tool_name}")
                return discovered_schema

        # Fallback to base schema
        return BaseInputSchema

    def _discover_schema_for_tool(self, tool_name: str) -> Optional[Type[BaseModel]]:
        """
        Discover schema for a specific tool.

        Args:
            tool_name: Name of the tool to discover schema for

        Returns:
            Discovered schema class or None
        """
        try:
            # Try to find the tool class
            discovered_tools = self.registry_integration.discover_tools_from_registry()

            # Check by exact name match
            if tool_name in discovered_tools:
                tool_class = discovered_tools[tool_name]
                return self.inference_engine.infer_schema_from_tool(tool_class)

            # Try module scanning as fallback
            scanned_tools = self.registry_integration.scan_tool_modules()
            if tool_name in scanned_tools:
                tool_class = scanned_tools[tool_name]
                return self.inference_engine.infer_schema_from_tool(tool_class)

            # Try pattern matching for similar tool names
            for registered_name, tool_class in {**discovered_tools, **scanned_tools}.items():
                if (tool_name.lower() in registered_name.lower() or
                    registered_name.lower() in tool_name.lower()):
                    logger.info(f"Found similar tool {registered_name} for {tool_name}")
                    return self.inference_engine.infer_schema_from_tool(tool_class)

        except Exception as e:
            logger.warning(f"Failed to discover schema for {tool_name}: {e}")

        return None

    def _load_configuration_schemas(self):
        """
        Load schemas from configuration files.
        """
        try:
            from .schema_config import get_schema_config_manager

            config_manager = get_schema_config_manager()
            if config_manager.load_configurations():
                config_manager.apply_configurations()
                logger.info("Loaded configuration-based schemas")
        except Exception as e:
            logger.debug(f"Failed to load configuration schemas: {e}")

    def _discover_and_register_tools(self):
        """
        Discover and register schemas for all available tools.
        """
        try:
            # Discover from registries
            discovered_tools = self.registry_integration.discover_tools_from_registry()

            # Discover from module scanning
            scanned_tools = self.registry_integration.scan_tool_modules()

            # Combine discoveries
            all_tools = {**discovered_tools, **scanned_tools}

            # Infer schemas for discovered tools
            for tool_name, tool_class in all_tools.items():
                if tool_name not in self._tool_schemas:
                    try:
                        inferred_schema = self.inference_engine.infer_schema_from_tool(tool_class)
                        if inferred_schema and inferred_schema != BaseInputSchema:
                            self._dynamic_schemas[tool_name] = inferred_schema
                            logger.debug(f"Auto-registered schema for: {tool_name}")
                    except Exception as e:
                        logger.debug(f"Failed to infer schema for {tool_name}: {e}")

            logger.info(f"Auto-discovery completed. Found {len(self._dynamic_schemas)} new tool schemas.")

        except Exception as e:
            logger.warning(f"Tool discovery failed: {e}")

    def get_schema_for_tool(self, tool_name: str) -> Type[BaseModel]:
        """
        Public method to get the Pydantic schema class for a specific tool.

        Args:
            tool_name: Name of the tool

        Returns:
            Schema class for the tool
        """
        return self._get_schema_for_tool(tool_name)

    def add_custom_schema(self, tool_name: str, schema_class: Type[BaseModel]):
        """
        Add a custom schema for a tool.

        Args:
            tool_name: Name of the tool
            schema_class: Pydantic schema class
        """
        self._tool_schemas[tool_name] = schema_class
        # Remove from dynamic cache if present
        self._dynamic_schemas.pop(tool_name, None)
        logger.info(f"Added custom schema for tool: {tool_name}")

    def register_tool_schema(self, tool_name: str, tool_class: Type) -> bool:
        """
        Register a schema for a tool by inferring it from the tool class.

        Args:
            tool_name: Name of the tool
            tool_class: Tool class to infer schema from

        Returns:
            True if schema was successfully registered
        """
        try:
            inferred_schema = self.inference_engine.infer_schema_from_tool(tool_class)
            if inferred_schema and inferred_schema != BaseInputSchema:
                self._tool_schemas[tool_name] = inferred_schema
                logger.info(f"Registered inferred schema for tool: {tool_name}")
                return True
        except Exception as e:
            logger.warning(f"Failed to register schema for {tool_name}: {e}")

        return False

    def refresh_discovery(self):
        """
        Refresh the auto-discovery process to find new tools.
        """
        if self.enable_auto_discovery:
            self._discovery_attempted.clear()
            self._discover_and_register_tools()
            logger.info("Schema discovery refreshed")

    def get_all_registered_tools(self) -> List[str]:
        """
        Get list of all tools with registered schemas.

        Returns:
            List of tool names
        """
        return list(set(self._tool_schemas.keys()) | set(self._dynamic_schemas.keys()))

    def get_discovery_stats(self) -> Dict[str, Any]:
        """
        Get statistics about schema discovery.

        Returns:
            Dictionary with discovery statistics
        """
        return {
            "static_schemas": len(self.STATIC_TOOL_SCHEMAS),
            "dynamic_schemas": len(self._dynamic_schemas),
            "total_schemas": len(self._tool_schemas) + len(self._dynamic_schemas),
            "discovery_attempts": len(self._discovery_attempted),
            "auto_discovery_enabled": self.enable_auto_discovery
        }


# Global input validator instance
input_validator = InputValidator()


def get_input_validator() -> InputValidator:
    """Get the global input validator instance."""
    return input_validator


def validate_tool_input(tool_name: str, input_data: Dict[str, Any]) -> ValidationResult:
    """
    Convenience function to validate tool input.
    
    Args:
        tool_name: Name of the tool
        input_data: Input data to validate
        
    Returns:
        ValidationResult: Validation result
    """
    return input_validator.validate_input(tool_name, input_data)
