#!/usr/bin/env python3
"""
Script to reset Qdrant collections for Hugging Face embeddings.

This script clears existing collections that were configured for OpenAI embeddings
and allows them to be recreated with the correct dimensions for Hugging Face models.
"""

import requests
import sys
import logging
from typing import List

logger = logging.getLogger(__name__)


def get_qdrant_collections(host: str = "localhost", port: int = 6333) -> List[str]:
    """Get list of existing Qdrant collections."""
    try:
        response = requests.get(f"http://{host}:{port}/collections")
        response.raise_for_status()
        
        data = response.json()
        collections = []
        
        if "result" in data and "collections" in data["result"]:
            collections = [col["name"] for col in data["result"]["collections"]]
        
        return collections
    except Exception as e:
        print(f"Error getting collections: {e}")
        return []


def delete_qdrant_collection(collection_name: str, host: str = "localhost", port: int = 6333) -> bool:
    """Delete a Qdrant collection."""
    try:
        response = requests.delete(f"http://{host}:{port}/collections/{collection_name}")
        response.raise_for_status()
        print(f"✓ Deleted collection: {collection_name}")
        return True
    except Exception as e:
        print(f"✗ Error deleting collection {collection_name}: {e}")
        return False


def check_qdrant_running(host: str = "localhost", port: int = 6333) -> bool:
    """Check if Qdrant is running."""
    try:
        response = requests.get(f"http://{host}:{port}")
        response.raise_for_status()
        return True
    except Exception as e:
        print(f"Qdrant is not running at {host}:{port}: {e}")
        return False


def main():
    """Main function to reset Qdrant collections."""
    print("🔧 Resetting Qdrant Collections for Hugging Face Embeddings")
    print("=" * 60)
    
    # Check if Qdrant is running
    if not check_qdrant_running():
        print("❌ Qdrant is not running. Please start Qdrant first.")
        print("\nTo start Qdrant:")
        print("docker run -p 6333:6333 qdrant/qdrant")
        return 1
    
    print("✓ Qdrant is running")
    
    # Get existing collections
    collections = get_qdrant_collections()
    
    if not collections:
        print("✓ No existing collections found")
        print("🎉 Ready for Hugging Face embeddings!")
        return 0
    
    print(f"📋 Found {len(collections)} existing collections:")
    for collection in collections:
        print(f"  - {collection}")
    
    # Ask for confirmation
    print("\n⚠️  These collections were likely created for OpenAI embeddings (1536 dimensions)")
    print("   and need to be deleted to work with Hugging Face embeddings (384 dimensions).")
    
    response = input("\nDo you want to delete all collections? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("❌ Operation cancelled")
        return 1
    
    # Delete collections
    print("\n🗑️  Deleting collections...")
    deleted_count = 0
    
    for collection in collections:
        if delete_qdrant_collection(collection):
            deleted_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   - Collections found: {len(collections)}")
    print(f"   - Collections deleted: {deleted_count}")
    
    if deleted_count == len(collections):
        print("\n🎉 All collections deleted successfully!")
        print("   New collections will be created automatically with correct dimensions")
        print("   for Hugging Face embeddings when you upload documents.")
        return 0
    else:
        print(f"\n⚠️  {len(collections) - deleted_count} collections could not be deleted")
        print("   You may need to delete them manually or restart Qdrant")
        return 1


if __name__ == "__main__":
    sys.exit(main())
