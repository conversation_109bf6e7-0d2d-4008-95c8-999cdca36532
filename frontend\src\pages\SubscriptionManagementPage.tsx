import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
// Assuming an API service exists or will be created for pricing/subscriptions
import { apiClient } from '@/services/apiClient'; 
import { SubscriptionResponse, PricingTierResponse } from '@/schemas/pricing'; // Assuming Pydantic schemas are mirrored in frontend

// Define a combined type for easier usage, as SubscriptionResponse might not directly contain full tier details initially
interface EnrichedSubscriptionResponse extends Omit<SubscriptionResponse, 'pricing_tier'> {
  pricing_tier: PricingTierResponse | null; // Allow null if tier details are fetched separately or not always present
}

const SubscriptionManagementPage: React.FC = () => {
  const [subscriptions, setSubscriptions] = useState<EnrichedSubscriptionResponse[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchSubscriptions = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // The backend /subscriptions/me endpoint returns SubscriptionResponse[]
      // We might need to enrich this with pricing_tier details if not already included.
      // For now, assuming the backend response includes a 'pricing_tier' object.
      const response = await apiClient.get<SubscriptionResponse[]>('/pricing/subscriptions/me');
      
      // Enrich subscriptions with tier details if necessary (example, if tier is just an ID)
      // This is a simplified enrichment. In a real app, you might fetch all tiers once
      // and map them, or ensure the backend sends populated tier objects.
      const enrichedSubs: EnrichedSubscriptionResponse[] = await Promise.all(
        response.data.map(async (sub) => {
          if (sub.pricing_tier && typeof sub.pricing_tier === 'string') { // If pricing_tier is an ID
            try {
              const tierDetails = await apiClient.get<PricingTierResponse>(`/pricing/tiers/${sub.pricing_tier}`);
              return { ...sub, pricing_tier: tierDetails.data };
            } catch (tierError) {
              console.error(`Failed to fetch tier details for ${sub.pricing_tier_id}`, tierError);
              return { ...sub, pricing_tier: null }; // Handle missing tier gracefully
            }
          }
          // If pricing_tier is already an object (as per Pydantic schema SubscriptionResponse)
          return sub as EnrichedSubscriptionResponse; 
        })
      );
      setSubscriptions(enrichedSubs);

    } catch (err) {
      console.error("Failed to fetch subscriptions:", err);
      setError("Failed to load subscriptions. Please try again later.");
      toast({
        title: "Error",
        description: "Could not fetch your subscriptions.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscriptions();
  }, []);

  const handleCancelSubscription = async (subscriptionId: string) => {
    try {
      await apiClient.post(`/pricing/subscriptions/${subscriptionId}/cancel`);
      toast({
        title: "Success",
        description: "Subscription cancelled successfully. It will remain active until the end of the current billing period.",
      });
      // Refresh subscriptions list
      fetchSubscriptions();
    } catch (err) {
      console.error("Failed to cancel subscription:", err);
      toast({
        title: "Error",
        description: "Could not cancel the subscription. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return <div className="container mx-auto p-4">Loading subscriptions...</div>;
  }

  if (error) {
    return <div className="container mx-auto p-4 text-red-500">{error}</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Subscription Management</h1>
      {subscriptions.length === 0 ? (
        <p>You have no active or past subscriptions.</p>
      ) : (
        <div className="space-y-6">
          {subscriptions.map((sub) => (
            <Card key={sub.id}>
              <CardHeader>
                <CardTitle className="flex justify-between items-center">
                  <span>{sub.pricing_tier?.name || 'Unknown Tier'}</span>
                  <Badge variant={sub.status === 'active' ? 'default' : sub.status === 'cancelled' ? 'secondary' : 'outline'}>
                    {sub.status.charAt(0).toUpperCase() + sub.status.slice(1)}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  {sub.pricing_tier?.description || 'No description available.'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <p><strong>Price:</strong> ${sub.pricing_tier?.base_price?.toFixed(2) || 'N/A'} / {sub.pricing_tier?.billing_period || 'N/A'}</p>
                <p><strong>Subscribed on:</strong> {new Date(sub.start_date).toLocaleDateString()}</p>
                {sub.next_billing_date && sub.status === 'active' && (
                  <p><strong>Next billing date:</strong> {new Date(sub.next_billing_date).toLocaleDateString()}</p>
                )}
                {sub.end_date && (
                  <p><strong>Ends on:</strong> {new Date(sub.end_date).toLocaleDateString()}</p>
                )}
                <p><strong>Auto-renews:</strong> {sub.auto_renew ? 'Yes' : 'No'}</p>
              </CardContent>
              <CardFooter>
                {sub.status === 'active' && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive">Cancel Subscription</Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This action will cancel your subscription. It will remain active
                          until the end of the current billing period. This cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Keep Subscription</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleCancelSubscription(sub.id)}>
                          Proceed to Cancel
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default SubscriptionManagementPage;
