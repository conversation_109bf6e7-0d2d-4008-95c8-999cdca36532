import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, HelpCircle } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import { motion } from 'framer-motion';

interface ReturnToConciergeProps {
  onReturn: () => void;
  className?: string;
  variant?: 'default' | 'minimal';
}

export const ReturnToConcierge: React.FC<ReturnToConciergeProps> = ({
  onReturn,
  className = '',
  variant = 'default'
}) => {
  if (variant === 'minimal') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 17
              }}
            >
              <Button
                variant="ghost"
                size="icon"
                onClick={onReturn}
                className={`h-8 w-8 ${className} relative overflow-hidden group`}
              >
                <motion.div
                  animate={{ x: [0, -2, 0] }}
                  transition={{
                    repeat: Infinity,
                    repeatType: "mirror",
                    duration: 1.5,
                    ease: "easeInOut"
                  }}
                >
                  <ArrowLeft className="h-4 w-4" />
                </motion.div>
                <span className="absolute inset-0 bg-blue-100 opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-md"></span>
              </Button>
            </motion.div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Return to Concierge</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        type: "spring",
        stiffness: 500,
        damping: 30
      }}
      className={`${className}`}
    >
      <motion.div
        whileHover={{ scale: 1.03 }}
        whileTap={{ scale: 0.97 }}
      >
        <Button
          variant="outline"
          size="sm"
          onClick={onReturn}
          className="flex items-center gap-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 transition-all duration-300 ease-in-out"
        >
          <motion.span
            animate={{ x: [0, -3, 0] }}
            transition={{
              repeat: Infinity,
              repeatType: "mirror",
              duration: 1.5,
              ease: "easeInOut"
            }}
          >
            <ArrowLeft className="h-4 w-4" />
          </motion.span>
          <span>Return to Concierge</span>
          <HelpCircle className="h-4 w-4 ml-1" />
        </Button>
      </motion.div>
    </motion.div>
  );
};
