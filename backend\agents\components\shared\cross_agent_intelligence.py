"""
Cross-Agent Intelligence System for Datagenius.

This component enables comprehensive cross-agent intelligence where all AI personas
can share context, insights, and collaborate within business profile scopes.
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
from threading import RLock
from ..base_component import BaseAgentComponent, AgentContext
from agents.utils.memory_service import MemoryService
from agents.utils.knowledge_graph_service import KnowledgeGraphService

logger = logging.getLogger(__name__)


class AgentInsight:
    """Represents an insight generated by an agent for cross-agent sharing."""
    
    def __init__(self,
                 insight_id: str,
                 source_agent: str,
                 insight_type: str,
                 content: str,
                 insight_metadata: Dict[str, Any],
                 relevance_tags: List[str],
                 business_profile_id: str,
                 created_at: datetime = None):
        self.insight_id = insight_id
        self.source_agent = source_agent
        self.insight_type = insight_type
        self.content = content
        self.insight_metadata = insight_metadata
        self.relevance_tags = relevance_tags
        self.business_profile_id = business_profile_id
        self.created_at = created_at or datetime.now()
        self.access_count = 0
        self.last_accessed = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert insight to dictionary for storage."""
        return {
            "insight_id": self.insight_id,
            "source_agent": self.source_agent,
            "insight_type": self.insight_type,
            "content": self.content,
            "metadata": self.metadata,
            "relevance_tags": self.relevance_tags,
            "business_profile_id": self.business_profile_id,
            "created_at": self.created_at.isoformat(),
            "access_count": self.access_count,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentInsight':
        """Create insight from dictionary."""
        insight = cls(
            insight_id=data["insight_id"],
            source_agent=data["source_agent"],
            insight_type=data["insight_type"],
            content=data["content"],
            insight_metadata=data["insight_metadata"],
            relevance_tags=data["relevance_tags"],
            business_profile_id=data["business_profile_id"],
            created_at=datetime.fromisoformat(data["created_at"])
        )
        insight.access_count = data.get("access_count", 0)
        if data.get("last_accessed"):
            insight.last_accessed = datetime.fromisoformat(data["last_accessed"])
        return insight


class CrossAgentIntelligence(BaseAgentComponent):
    """
    Cross-Agent Intelligence component that enables all AI personas to share
    context, insights, and collaborate within business profile scopes.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("cross_agent_intelligence", config)
        
        # Configuration
        self.enable_cross_agent_sharing = config.get("enable_cross_agent_sharing", True)
        self.max_insights_per_profile = config.get("max_insights_per_profile", 1000)
        self.insight_retention_days = config.get("insight_retention_days", 30)
        self.max_context_items = config.get("max_context_items", 50)
        self.relevance_threshold = config.get("relevance_threshold", 0.7)
        
        # Agent type mappings for intelligent context sharing
        self.agent_capabilities = {
            "concierge-agent": ["guidance", "coordination", "routing"],
            "composable-marketing-ai": ["marketing", "strategy", "content", "campaigns"],
            "composable-analysis-ai": ["analysis", "visualization", "insights", "data"],
            "composable-classifier-ai": ["classification", "categorization", "organization"],
            "data-assistant": ["data_processing", "analysis", "visualization"],
            "text-processor": ["text_analysis", "summarization", "extraction"]
        }
        
        # Insight type priorities for different agents
        self.insight_priorities = {
            "concierge-agent": ["user_intent", "workflow", "coordination", "general"],
            "composable-marketing-ai": ["marketing_strategy", "campaign_results", "content_performance", "audience_insights"],
            "composable-analysis-ai": ["data_insights", "trends", "patterns", "recommendations"],
            "composable-classifier-ai": ["categorization", "organization", "structure"],
            "data-assistant": ["data_quality", "processing_results", "analysis_findings"],
            "text-processor": ["text_insights", "summaries", "entities", "sentiment"]
        }
        
        # Storage for cross-agent insights (business profile scoped)
        self.insights_store: Dict[str, List[AgentInsight]] = {}  # profile_id -> insights
        self.agent_interactions: Dict[str, List[Dict[str, Any]]] = {}  # profile_id -> interactions
        self.context_cache: Dict[str, Dict[str, Any]] = {}  # profile_id -> cached context
        
        # Thread safety
        self.lock = RLock()
        
        # Services
        self.memory_service = MemoryService()
        self.kg_service = KnowledgeGraphService()
    
    async def _initialize_component(self) -> None:
        """Initialize the cross-agent intelligence component."""
        self.logger.info("Initializing CrossAgentIntelligence component")
        
        try:
            # Initialize services
            await self.memory_service.initialize()
            await self.kg_service.initialize()
            self.logger.info("Cross-agent intelligence services initialized")
        except Exception as e:
            self.logger.warning(f"Failed to initialize some services: {e}")
    
    def get_required_fields(self) -> List[str]:
        """Return list of required context fields."""
        return ["user_id", "business_profile_id", "agent_id"]
    
    async def process(self, context: AgentContext) -> AgentContext:
        """
        Process context to enable cross-agent intelligence.
        
        Args:
            context: AgentContext containing agent and business profile information
            
        Returns:
            Updated AgentContext with cross-agent intelligence data
        """
        if not self.enable_cross_agent_sharing:
            return context
        
        user_id = context.user_id
        business_profile_id = context.get_field("business_profile_id")
        agent_id = context.get_field("agent_id", "unknown")
        operation = context.get_field("cross_agent_operation", "get_context")
        
        self.logger.info(f"Processing cross-agent intelligence for agent {agent_id} in profile {business_profile_id}")
        
        try:
            if operation == "get_context":
                return await self._get_cross_agent_context(context, business_profile_id, agent_id)
            elif operation == "add_insight":
                return await self._add_agent_insight(context, business_profile_id, agent_id)
            elif operation == "record_interaction":
                return await self._record_agent_interaction(context, business_profile_id, agent_id)
            elif operation == "get_collaboration_opportunities":
                return await self._get_collaboration_opportunities(context, business_profile_id, agent_id)
            else:
                context.add_error(self.name, f"Unknown cross-agent operation: {operation}")
                context.set_status("error")
                return context
                
        except Exception as e:
            self.logger.error(f"Error in cross-agent intelligence processing: {e}")
            context.add_error(self.name, f"Cross-agent intelligence error: {str(e)}")
            context.set_field("cross_agent_status", "error")
        
        return context
    
    async def _get_cross_agent_context(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Get cross-agent context for the requesting agent."""
        with self.lock:
            # Get relevant insights from other agents
            relevant_insights = self._get_relevant_insights(business_profile_id, agent_id)
            
            # Get recent agent interactions
            recent_interactions = self._get_recent_interactions(business_profile_id, agent_id)
            
            # Get collaboration context
            collaboration_context = self._build_collaboration_context(business_profile_id, agent_id)
            
            # Build comprehensive cross-agent context
            cross_agent_context = {
                "relevant_insights": [insight.to_dict() for insight in relevant_insights],
                "recent_interactions": recent_interactions,
                "collaboration_context": collaboration_context,
                "agent_capabilities": self._get_available_agent_capabilities(business_profile_id),
                "context_summary": self._generate_context_summary(relevant_insights, recent_interactions)
            }
            
            # Cache context for performance
            cache_key = f"{business_profile_id}_{agent_id}"
            self.context_cache[cache_key] = {
                "context": cross_agent_context,
                "timestamp": datetime.now(),
                "ttl": 300  # 5 minutes
            }
            
            # Add to agent context
            context.set_field("cross_agent_context", cross_agent_context)
            context.set_field("cross_agent_status", "loaded")
            
            self.logger.info(f"Loaded cross-agent context for {agent_id}: {len(relevant_insights)} insights, {len(recent_interactions)} interactions")
            
        return context
    
    async def _add_agent_insight(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Add a new insight from an agent to the cross-agent knowledge base."""
        insight_data = context.get_field("insight_data", {})
        
        if not insight_data:
            context.add_error(self.name, "No insight data provided")
            return context
        
        # Create insight object
        insight = AgentInsight(
            insight_id=str(uuid.uuid4()),
            source_agent=agent_id,
            insight_type=insight_data.get("type", "general"),
            content=insight_data.get("content", ""),
            insight_metadata=insight_data.get("metadata", {}),
            relevance_tags=insight_data.get("tags", []),
            business_profile_id=business_profile_id
        )
        
        with self.lock:
            # Initialize profile insights if needed
            if business_profile_id not in self.insights_store:
                self.insights_store[business_profile_id] = []
            
            # Add insight
            self.insights_store[business_profile_id].append(insight)
            
            # Maintain size limits
            if len(self.insights_store[business_profile_id]) > self.max_insights_per_profile:
                # Remove oldest insights
                self.insights_store[business_profile_id] = sorted(
                    self.insights_store[business_profile_id],
                    key=lambda x: x.created_at,
                    reverse=True
                )[:self.max_insights_per_profile]
            
            # Clear related cache
            self._clear_profile_cache(business_profile_id)
        
        # Store in persistent memory
        await self._store_insight_in_memory(insight)
        
        context.set_field("insight_added", True)
        context.set_field("insight_id", insight.insight_id)
        
        self.logger.info(f"Added insight from {agent_id} to profile {business_profile_id}: {insight.insight_type}")
        
        return context
    
    async def _record_agent_interaction(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Record an agent interaction for cross-agent learning."""
        interaction_data = context.get_field("interaction_data", {})
        
        interaction = {
            "interaction_id": str(uuid.uuid4()),
            "agent_id": agent_id,
            "user_message": interaction_data.get("user_message", ""),
            "agent_response": interaction_data.get("agent_response", ""),
            "context_used": interaction_data.get("context_used", []),
            "tools_used": interaction_data.get("tools_used", []),
            "outcome": interaction_data.get("outcome", "unknown"),
            "timestamp": datetime.now().isoformat(),
            "metadata": interaction_data.get("metadata", {})
        }
        
        with self.lock:
            # Initialize profile interactions if needed
            if business_profile_id not in self.agent_interactions:
                self.agent_interactions[business_profile_id] = []
            
            # Add interaction
            self.agent_interactions[business_profile_id].append(interaction)
            
            # Maintain size limits (keep last 100 interactions per profile)
            if len(self.agent_interactions[business_profile_id]) > 100:
                self.agent_interactions[business_profile_id] = self.agent_interactions[business_profile_id][-100:]
        
        context.set_field("interaction_recorded", True)
        context.set_field("interaction_id", interaction["interaction_id"])

        return context

    def _get_relevant_insights(self, business_profile_id: str, requesting_agent: str) -> List[AgentInsight]:
        """Get insights relevant to the requesting agent."""
        if business_profile_id not in self.insights_store:
            return []

        all_insights = self.insights_store[business_profile_id]
        relevant_insights = []

        # Get agent priorities
        agent_priorities = self.insight_priorities.get(requesting_agent, ["general"])

        for insight in all_insights:
            # Skip insights from the same agent (unless specifically configured)
            if insight.source_agent == requesting_agent:
                continue

            # Check relevance based on insight type and tags
            relevance_score = self._calculate_insight_relevance(insight, agent_priorities)

            if relevance_score >= self.relevance_threshold:
                # Update access tracking
                insight.access_count += 1
                insight.last_accessed = datetime.now()
                relevant_insights.append(insight)

        # Sort by relevance and recency
        relevant_insights.sort(key=lambda x: (x.access_count, x.created_at), reverse=True)

        return relevant_insights[:self.max_context_items]

    def _calculate_insight_relevance(self, insight: AgentInsight, agent_priorities: List[str]) -> float:
        """Calculate relevance score for an insight."""
        score = 0.0

        # Type-based relevance
        if insight.insight_type in agent_priorities:
            score += 0.5

        # Tag-based relevance
        for tag in insight.relevance_tags:
            if tag in agent_priorities:
                score += 0.3

        # Recency bonus (newer insights are more relevant)
        age_days = (datetime.now() - insight.created_at).days
        if age_days < 1:
            score += 0.2
        elif age_days < 7:
            score += 0.1

        # Access frequency bonus (popular insights are more relevant)
        if insight.access_count > 5:
            score += 0.1

        return min(score, 1.0)

    def _get_recent_interactions(self, business_profile_id: str, requesting_agent: str) -> List[Dict[str, Any]]:
        """Get recent interactions from other agents."""
        if business_profile_id not in self.agent_interactions:
            return []

        all_interactions = self.agent_interactions[business_profile_id]

        # Filter out interactions from the same agent and get recent ones
        recent_interactions = [
            interaction for interaction in all_interactions
            if interaction["agent_id"] != requesting_agent
        ]

        # Sort by timestamp and get most recent
        recent_interactions.sort(key=lambda x: x["timestamp"], reverse=True)

        return recent_interactions[:20]  # Last 20 interactions

    def _build_collaboration_context(self, business_profile_id: str, requesting_agent: str) -> Dict[str, Any]:
        """Build collaboration context showing how agents can work together."""
        collaboration_context = {
            "available_agents": [],
            "collaboration_opportunities": [],
            "workflow_suggestions": []
        }

        # Get active agents for this profile
        active_agents = self._get_active_agents_for_profile(business_profile_id)

        for agent_id in active_agents:
            if agent_id != requesting_agent:
                agent_info = {
                    "agent_id": agent_id,
                    "capabilities": self.agent_capabilities.get(agent_id, []),
                    "recent_activity": self._get_agent_recent_activity(business_profile_id, agent_id)
                }
                collaboration_context["available_agents"].append(agent_info)

        # Generate collaboration opportunities
        collaboration_context["collaboration_opportunities"] = self._generate_collaboration_opportunities(
            requesting_agent, active_agents
        )

        return collaboration_context

    def _get_active_agents_for_profile(self, business_profile_id: str) -> Set[str]:
        """Get list of agents that have been active in this business profile."""
        active_agents = set()

        # From insights
        if business_profile_id in self.insights_store:
            for insight in self.insights_store[business_profile_id]:
                active_agents.add(insight.source_agent)

        # From interactions
        if business_profile_id in self.agent_interactions:
            for interaction in self.agent_interactions[business_profile_id]:
                active_agents.add(interaction["agent_id"])

        return active_agents

    def _get_agent_recent_activity(self, business_profile_id: str, agent_id: str) -> Dict[str, Any]:
        """Get recent activity summary for an agent."""
        activity = {
            "last_interaction": None,
            "interaction_count": 0,
            "insights_generated": 0,
            "tools_used": set()
        }

        # Count interactions
        if business_profile_id in self.agent_interactions:
            agent_interactions = [
                i for i in self.agent_interactions[business_profile_id]
                if i["agent_id"] == agent_id
            ]
            activity["interaction_count"] = len(agent_interactions)

            if agent_interactions:
                # Get most recent interaction
                latest = max(agent_interactions, key=lambda x: x["timestamp"])
                activity["last_interaction"] = latest["timestamp"]

                # Collect tools used
                for interaction in agent_interactions:
                    activity["tools_used"].update(interaction.get("tools_used", []))

        # Count insights
        if business_profile_id in self.insights_store:
            agent_insights = [
                i for i in self.insights_store[business_profile_id]
                if i.source_agent == agent_id
            ]
            activity["insights_generated"] = len(agent_insights)

        activity["tools_used"] = list(activity["tools_used"])
        return activity

    def _generate_collaboration_opportunities(self, requesting_agent: str, active_agents: Set[str]) -> List[Dict[str, Any]]:
        """Generate collaboration opportunities between agents."""
        opportunities = []

        requesting_capabilities = self.agent_capabilities.get(requesting_agent, [])

        for agent_id in active_agents:
            if agent_id == requesting_agent:
                continue

            agent_capabilities = self.agent_capabilities.get(agent_id, [])

            # Find complementary capabilities
            complementary = self._find_complementary_capabilities(requesting_capabilities, agent_capabilities)

            if complementary:
                opportunity = {
                    "partner_agent": agent_id,
                    "collaboration_type": complementary["type"],
                    "description": complementary["description"],
                    "suggested_workflow": complementary["workflow"]
                }
                opportunities.append(opportunity)

        return opportunities

    def _find_complementary_capabilities(self, agent1_caps: List[str], agent2_caps: List[str]) -> Optional[Dict[str, Any]]:
        """Find complementary capabilities between two agents."""
        # Define collaboration patterns
        collaboration_patterns = {
            ("marketing", "analysis"): {
                "type": "strategy_validation",
                "description": "Marketing strategies can be validated with data analysis",
                "workflow": "Marketing agent creates strategy → Analysis agent validates with data"
            },
            ("analysis", "marketing"): {
                "type": "data_driven_marketing",
                "description": "Data insights can inform marketing strategies",
                "workflow": "Analysis agent provides insights → Marketing agent creates targeted campaigns"
            },
            ("classification", "analysis"): {
                "type": "structured_analysis",
                "description": "Classified data can be analyzed more effectively",
                "workflow": "Classification agent organizes data → Analysis agent performs structured analysis"
            },
            ("guidance", "analysis"): {
                "type": "insight_explanation",
                "description": "Complex analysis can be explained in user-friendly terms",
                "workflow": "Analysis agent generates insights → Concierge explains to user"
            }
        }

        # Check for matching patterns
        for cap1 in agent1_caps:
            for cap2 in agent2_caps:
                pattern_key = (cap1, cap2)
                if pattern_key in collaboration_patterns:
                    return collaboration_patterns[pattern_key]

        return None

    def _get_available_agent_capabilities(self, business_profile_id: str) -> Dict[str, List[str]]:
        """Get capabilities of all active agents in the business profile."""
        active_agents = self._get_active_agents_for_profile(business_profile_id)
        capabilities = {}

        for agent_id in active_agents:
            capabilities[agent_id] = self.agent_capabilities.get(agent_id, [])

        return capabilities

    def _generate_context_summary(self, insights: List[AgentInsight], interactions: List[Dict[str, Any]]) -> str:
        """Generate a summary of the cross-agent context."""
        if not insights and not interactions:
            return "No cross-agent context available."

        summary_parts = []

        if insights:
            insight_types = {}
            for insight in insights:
                insight_types[insight.insight_type] = insight_types.get(insight.insight_type, 0) + 1

            insight_summary = ", ".join([f"{count} {type_}" for type_, count in insight_types.items()])
            summary_parts.append(f"Available insights: {insight_summary}")

        if interactions:
            agent_activity = {}
            for interaction in interactions:
                agent_id = interaction["agent_id"]
                agent_activity[agent_id] = agent_activity.get(agent_id, 0) + 1

            activity_summary = ", ".join([f"{agent}: {count}" for agent, count in agent_activity.items()])
            summary_parts.append(f"Recent agent activity: {activity_summary}")

        return ". ".join(summary_parts)

    async def _store_insight_in_memory(self, insight: AgentInsight) -> None:
        """Store insight in persistent memory service."""
        try:
            memory_data = {
                "content": insight.content,
                "metadata": {
                    **insight.metadata,
                    "insight_type": insight.insight_type,
                    "source_agent": insight.source_agent,
                    "relevance_tags": insight.relevance_tags,
                    "cross_agent_insight": True
                }
            }

            # Store in memory service with business profile context
            await self.memory_service.add_memory(
                content=insight.content,
                user_id=f"profile_{insight.business_profile_id}",
                metadata=memory_data["metadata"]
            )

        except Exception as e:
            self.logger.warning(f"Failed to store insight in memory service: {e}")

    def _clear_profile_cache(self, business_profile_id: str) -> None:
        """Clear cached context for a business profile."""
        keys_to_remove = [key for key in self.context_cache.keys() if key.startswith(f"{business_profile_id}_")]
        for key in keys_to_remove:
            del self.context_cache[key]

    async def _get_collaboration_opportunities(self, context: AgentContext, business_profile_id: str, agent_id: str) -> AgentContext:
        """Get specific collaboration opportunities for an agent."""
        active_agents = self._get_active_agents_for_profile(business_profile_id)
        opportunities = self._generate_collaboration_opportunities(agent_id, active_agents)

        # Add workflow suggestions based on current context
        current_task = context.get_field("current_task", "")
        if current_task:
            workflow_suggestions = self._suggest_workflows_for_task(current_task, agent_id, active_agents)
            opportunities.extend(workflow_suggestions)

        context.set_field("collaboration_opportunities", opportunities)
        context.set_field("available_agents", list(active_agents))

        return context

    def _suggest_workflows_for_task(self, task: str, requesting_agent: str, active_agents: Set[str]) -> List[Dict[str, Any]]:
        """Suggest workflows based on the current task."""
        workflows = []

        # Task-based workflow suggestions
        task_lower = task.lower()

        if "marketing" in task_lower and "composable-analysis-ai" in active_agents:
            workflows.append({
                "partner_agent": "composable-analysis-ai",
                "collaboration_type": "data_driven_marketing",
                "description": "Use data analysis to inform marketing strategy",
                "suggested_workflow": "Get data insights first, then create targeted marketing strategy"
            })

        if "analysis" in task_lower and "composable-marketing-ai" in active_agents:
            workflows.append({
                "partner_agent": "composable-marketing-ai",
                "collaboration_type": "strategy_validation",
                "description": "Validate analysis findings with marketing context",
                "suggested_workflow": "Analyze data, then check alignment with marketing goals"
            })

        if "organize" in task_lower or "classify" in task_lower and "composable-classifier-ai" in active_agents:
            workflows.append({
                "partner_agent": "composable-classifier-ai",
                "collaboration_type": "structured_processing",
                "description": "Organize data before processing",
                "suggested_workflow": "Classify and organize data first, then proceed with main task"
            })

        return workflows

    async def cleanup_expired_data(self) -> None:
        """Clean up expired insights and interactions."""
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=self.insight_retention_days)

        with self.lock:
            # Clean up expired insights
            for profile_id in list(self.insights_store.keys()):
                original_count = len(self.insights_store[profile_id])
                self.insights_store[profile_id] = [
                    insight for insight in self.insights_store[profile_id]
                    if insight.created_at > cutoff_time
                ]
                cleaned_count = original_count - len(self.insights_store[profile_id])

                if cleaned_count > 0:
                    self.logger.info(f"Cleaned up {cleaned_count} expired insights for profile {profile_id}")

            # Clean up expired cache entries
            expired_cache_keys = []
            for key, cache_data in self.context_cache.items():
                if current_time - cache_data["timestamp"] > timedelta(seconds=cache_data["ttl"]):
                    expired_cache_keys.append(key)

            for key in expired_cache_keys:
                del self.context_cache[key]

            if expired_cache_keys:
                self.logger.info(f"Cleaned up {len(expired_cache_keys)} expired cache entries")

    def get_cross_agent_stats(self, business_profile_id: str) -> Dict[str, Any]:
        """Get statistics about cross-agent intelligence for a business profile."""
        with self.lock:
            stats = {
                "total_insights": len(self.insights_store.get(business_profile_id, [])),
                "total_interactions": len(self.agent_interactions.get(business_profile_id, [])),
                "active_agents": len(self._get_active_agents_for_profile(business_profile_id)),
                "insight_types": {},
                "agent_activity": {}
            }

            # Analyze insights
            if business_profile_id in self.insights_store:
                for insight in self.insights_store[business_profile_id]:
                    stats["insight_types"][insight.insight_type] = stats["insight_types"].get(insight.insight_type, 0) + 1

            # Analyze agent activity
            if business_profile_id in self.agent_interactions:
                for interaction in self.agent_interactions[business_profile_id]:
                    agent_id = interaction["agent_id"]
                    stats["agent_activity"][agent_id] = stats["agent_activity"].get(agent_id, 0) + 1

            return stats
