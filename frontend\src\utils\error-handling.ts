/**
 * Comprehensive error handling utilities for the dashboard application.
 * Provides centralized error management, logging, and user feedback.
 */

import { toast } from '@/hooks/use-toast';

export enum ErrorType {
  VALIDATION = 'validation',
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  SERVER_ERROR = 'server_error',
  WEBSOCKET = 'websocket',
  PERFORMANCE = 'performance',
  SECURITY = 'security',
  UNKNOWN = 'unknown',
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  timestamp: Date;
  context?: {
    component?: string;
    action?: string;
    userId?: string;
    sessionId?: string;
    url?: string;
    userAgent?: string;
  };
  stack?: string;
  retryable: boolean;
  retryCount?: number;
  maxRetries?: number;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  reportError?: boolean;
  retryable?: boolean;
  maxRetries?: number;
  context?: AppError['context'];
}

class ErrorHandler {
  private errors: AppError[] = [];
  private maxStoredErrors = 100;
  private errorReportingEndpoint = '/api/errors';

  /**
   * Handle an error with comprehensive processing
   */
  handleError(
    error: Error | string | any,
    options: ErrorHandlerOptions = {}
  ): AppError {
    const {
      showToast = true,
      logError = true,
      reportError = true,
      retryable = false,
      maxRetries = 3,
      context = {},
    } = options;

    const appError = this.createAppError(error, {
      retryable,
      maxRetries,
      context,
    });

    // Store error
    this.storeError(appError);

    // Log error
    if (logError) {
      this.logError(appError);
    }

    // Show user notification
    if (showToast) {
      this.showErrorToast(appError);
    }

    // Report error to monitoring service
    if (reportError && appError.severity !== ErrorSeverity.LOW) {
      this.reportError(appError);
    }

    return appError;
  }

  /**
   * Create a standardized AppError from various error types
   */
  private createAppError(
    error: Error | string | any,
    options: {
      retryable: boolean;
      maxRetries: number;
      context: AppError['context'];
    }
  ): AppError {
    const id = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = new Date();

    let message: string;
    let type: ErrorType;
    let severity: ErrorSeverity;
    let stack: string | undefined;
    let details: any;

    if (error instanceof Error) {
      message = error.message;
      stack = error.stack;
      type = this.determineErrorType(error);
      severity = this.determineErrorSeverity(error, type);
      details = { name: error.name };
    } else if (typeof error === 'string') {
      message = error;
      type = ErrorType.UNKNOWN;
      severity = ErrorSeverity.MEDIUM;
    } else if (error?.response) {
      // Axios error
      message = error.response.data?.message || error.message || 'Network error occurred';
      type = this.determineNetworkErrorType(error.response.status);
      severity = this.determineNetworkErrorSeverity(error.response.status);
      details = {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      };
    } else {
      message = 'An unknown error occurred';
      type = ErrorType.UNKNOWN;
      severity = ErrorSeverity.MEDIUM;
      details = error;
    }

    return {
      id,
      type,
      severity,
      message,
      details,
      timestamp,
      context: {
        ...options.context,
        url: window.location.href,
        userAgent: navigator.userAgent,
      },
      stack,
      retryable: options.retryable,
      retryCount: 0,
      maxRetries: options.maxRetries,
    };
  }

  /**
   * Determine error type from Error object
   */
  private determineErrorType(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    if (name.includes('validation') || message.includes('validation')) {
      return ErrorType.VALIDATION;
    }
    if (name.includes('network') || message.includes('network') || message.includes('fetch')) {
      return ErrorType.NETWORK;
    }
    if (message.includes('unauthorized') || message.includes('authentication')) {
      return ErrorType.AUTHENTICATION;
    }
    if (message.includes('forbidden') || message.includes('permission')) {
      return ErrorType.AUTHORIZATION;
    }
    if (message.includes('not found') || message.includes('404')) {
      return ErrorType.NOT_FOUND;
    }
    if (message.includes('websocket') || message.includes('ws')) {
      return ErrorType.WEBSOCKET;
    }
    if (message.includes('performance') || message.includes('timeout')) {
      return ErrorType.PERFORMANCE;
    }
    if (message.includes('security') || message.includes('xss') || message.includes('csrf')) {
      return ErrorType.SECURITY;
    }

    return ErrorType.UNKNOWN;
  }

  /**
   * Determine error severity
   */
  private determineErrorSeverity(error: Error, type: ErrorType): ErrorSeverity {
    switch (type) {
      case ErrorType.SECURITY:
        return ErrorSeverity.CRITICAL;
      case ErrorType.AUTHENTICATION:
      case ErrorType.SERVER_ERROR:
        return ErrorSeverity.HIGH;
      case ErrorType.AUTHORIZATION:
      case ErrorType.NETWORK:
      case ErrorType.WEBSOCKET:
        return ErrorSeverity.MEDIUM;
      case ErrorType.VALIDATION:
      case ErrorType.NOT_FOUND:
        return ErrorSeverity.LOW;
      default:
        return ErrorSeverity.MEDIUM;
    }
  }

  /**
   * Determine network error type from status code
   */
  private determineNetworkErrorType(status: number): ErrorType {
    if (status === 401) return ErrorType.AUTHENTICATION;
    if (status === 403) return ErrorType.AUTHORIZATION;
    if (status === 404) return ErrorType.NOT_FOUND;
    if (status >= 400 && status < 500) return ErrorType.VALIDATION;
    if (status >= 500) return ErrorType.SERVER_ERROR;
    return ErrorType.NETWORK;
  }

  /**
   * Determine network error severity from status code
   */
  private determineNetworkErrorSeverity(status: number): ErrorSeverity {
    if (status >= 500) return ErrorSeverity.HIGH;
    if (status === 401 || status === 403) return ErrorSeverity.MEDIUM;
    return ErrorSeverity.LOW;
  }

  /**
   * Store error in memory
   */
  private storeError(error: AppError): void {
    this.errors.unshift(error);
    if (this.errors.length > this.maxStoredErrors) {
      this.errors = this.errors.slice(0, this.maxStoredErrors);
    }
  }

  /**
   * Log error to console
   */
  private logError(error: AppError): void {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = `[${error.type.toUpperCase()}] ${error.message}`;
    
    console[logLevel](logMessage, {
      id: error.id,
      timestamp: error.timestamp,
      context: error.context,
      details: error.details,
      stack: error.stack,
    });
  }

  /**
   * Get appropriate console log level
   */
  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.LOW:
        return 'info';
      default:
        return 'error';
    }
  }

  /**
   * Show error toast notification
   */
  private showErrorToast(error: AppError): void {
    const variant = error.severity === ErrorSeverity.CRITICAL || error.severity === ErrorSeverity.HIGH
      ? 'destructive'
      : 'default';

    toast({
      title: this.getErrorTitle(error.type),
      description: this.getUserFriendlyMessage(error),
      variant,
      duration: this.getToastDuration(error.severity),
    });
  }

  /**
   * Get user-friendly error title
   */
  private getErrorTitle(type: ErrorType): string {
    switch (type) {
      case ErrorType.VALIDATION:
        return 'Validation Error';
      case ErrorType.NETWORK:
        return 'Connection Error';
      case ErrorType.AUTHENTICATION:
        return 'Authentication Required';
      case ErrorType.AUTHORIZATION:
        return 'Access Denied';
      case ErrorType.NOT_FOUND:
        return 'Not Found';
      case ErrorType.SERVER_ERROR:
        return 'Server Error';
      case ErrorType.WEBSOCKET:
        return 'Connection Issue';
      case ErrorType.PERFORMANCE:
        return 'Performance Warning';
      case ErrorType.SECURITY:
        return 'Security Alert';
      default:
        return 'Error';
    }
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(error: AppError): string {
    switch (error.type) {
      case ErrorType.VALIDATION:
        return 'Please check your input and try again.';
      case ErrorType.NETWORK:
        return 'Please check your internet connection and try again.';
      case ErrorType.AUTHENTICATION:
        return 'Please log in to continue.';
      case ErrorType.AUTHORIZATION:
        return 'You don\'t have permission to perform this action.';
      case ErrorType.NOT_FOUND:
        return 'The requested resource was not found.';
      case ErrorType.SERVER_ERROR:
        return 'A server error occurred. Please try again later.';
      case ErrorType.WEBSOCKET:
        return 'Real-time connection lost. Attempting to reconnect...';
      case ErrorType.PERFORMANCE:
        return 'The operation is taking longer than expected.';
      case ErrorType.SECURITY:
        return 'A security issue was detected. Please contact support.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }

  /**
   * Get toast duration based on severity
   */
  private getToastDuration(severity: ErrorSeverity): number {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return 10000; // 10 seconds
      case ErrorSeverity.HIGH:
        return 7000;  // 7 seconds
      case ErrorSeverity.MEDIUM:
        return 5000;  // 5 seconds
      case ErrorSeverity.LOW:
        return 3000;  // 3 seconds
      default:
        return 5000;
    }
  }

  /**
   * Report error to monitoring service
   */
  private async reportError(error: AppError): Promise<void> {
    try {
      await fetch(this.errorReportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          error: {
            ...error,
            // Remove sensitive information
            context: {
              ...error.context,
              userId: error.context?.userId ? '[REDACTED]' : undefined,
            },
          },
        }),
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  /**
   * Retry a failed operation
   */
  async retry<T>(
    operation: () => Promise<T>,
    errorId: string,
    context?: AppError['context']
  ): Promise<T> {
    const error = this.errors.find(e => e.id === errorId);
    if (!error || !error.retryable) {
      throw new Error('Operation is not retryable');
    }

    if ((error.retryCount || 0) >= (error.maxRetries || 3)) {
      throw new Error('Maximum retry attempts exceeded');
    }

    try {
      error.retryCount = (error.retryCount || 0) + 1;
      const result = await operation();
      
      // Remove error from list on successful retry
      this.errors = this.errors.filter(e => e.id !== errorId);
      
      return result;
    } catch (retryError) {
      this.handleError(retryError, {
        context: { ...context, retryAttempt: error.retryCount },
        retryable: error.retryCount < (error.maxRetries || 3),
      });
      throw retryError;
    }
  }

  /**
   * Get all stored errors
   */
  getErrors(): AppError[] {
    return [...this.errors];
  }

  /**
   * Get errors by type
   */
  getErrorsByType(type: ErrorType): AppError[] {
    return this.errors.filter(error => error.type === type);
  }

  /**
   * Get errors by severity
   */
  getErrorsBySeverity(severity: ErrorSeverity): AppError[] {
    return this.errors.filter(error => error.severity === severity);
  }

  /**
   * Clear all errors
   */
  clearErrors(): void {
    this.errors = [];
  }

  /**
   * Clear errors by type
   */
  clearErrorsByType(type: ErrorType): void {
    this.errors = this.errors.filter(error => error.type !== type);
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recent: number; // Last hour
  } {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    return {
      total: this.errors.length,
      byType: Object.values(ErrorType).reduce((acc, type) => {
        acc[type] = this.errors.filter(e => e.type === type).length;
        return acc;
      }, {} as Record<ErrorType, number>),
      bySeverity: Object.values(ErrorSeverity).reduce((acc, severity) => {
        acc[severity] = this.errors.filter(e => e.severity === severity).length;
        return acc;
      }, {} as Record<ErrorSeverity, number>),
      recent: this.errors.filter(e => e.timestamp > oneHourAgo).length,
    };
  }
}

// Global error handler instance
export const errorHandler = new ErrorHandler();

// Convenience functions
export const handleError = (error: any, options?: ErrorHandlerOptions) => 
  errorHandler.handleError(error, options);

export const retryOperation = <T>(operation: () => Promise<T>, errorId: string, context?: AppError['context']) =>
  errorHandler.retry(operation, errorId, context);

export const getErrors = () => errorHandler.getErrors();
export const clearErrors = () => errorHandler.clearErrors();
export const getErrorStats = () => errorHandler.getErrorStats();

// Global error event listeners
window.addEventListener('error', (event) => {
  errorHandler.handleError(event.error, {
    context: {
      component: 'global',
      action: 'unhandled_error',
    },
  });
});

window.addEventListener('unhandledrejection', (event) => {
  errorHandler.handleError(event.reason, {
    context: {
      component: 'global',
      action: 'unhandled_promise_rejection',
    },
  });
});

export { ErrorHandler, ErrorType, ErrorSeverity };
export type { AppError, ErrorHandlerOptions };
