#!/usr/bin/env python3
"""
Test script for Phase 4 implementation.

This script validates that the hierarchical configuration system
is working correctly and all components are properly integrated.
"""

import os
import sys
import logging
import tempfile
import yaml
from pathlib import Path

# Add backend root to path
backend_root = Path(__file__).parent
sys.path.insert(0, str(backend_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_configuration_imports():
    """Test that all configuration modules can be imported."""
    logger.info("Testing configuration imports...")
    
    try:
        from app.settings.base import BaseConfig, EnvironmentConfig
        from app.settings.database import DatabaseConfig, RedisConfig
        from app.settings.security import SecurityConfig
        from app.settings.llm import LLMConfig, LLMProviderConfig
        from app.settings.app import AppConfig
        from app.settings.manager import ConfigurationManager, get_config
        
        logger.info("✅ All configuration modules imported successfully")
        return True
    except ImportError as e:
        logger.error(f"❌ Failed to import configuration modules: {e}")
        return False


def test_basic_configuration_creation():
    """Test basic configuration creation and validation."""
    logger.info("Testing basic configuration creation...")
    
    try:
        from app.settings.app import AppConfig
        from app.settings.database import DatabaseConfig
        from app.settings.security import SecurityConfig
        
        # Create a basic configuration
        config = AppConfig(
            name="Test App",
            version="1.0.0",
            database=DatabaseConfig(url="postgresql://test:test@localhost:5432/test"),
            security=SecurityConfig(jwt_secret_key="test-secret-key-that-is-long-enough-for-validation")
        )
        
        assert config.name == "Test App"
        assert config.database.url == "postgresql://test:test@localhost:5432/test"
        assert len(config.security.jwt_secret_key) >= 32
        
        logger.info("✅ Basic configuration creation successful")
        return True
    except Exception as e:
        logger.error(f"❌ Basic configuration creation failed: {e}")
        return False


def test_environment_configuration():
    """Test environment-based configuration loading."""
    logger.info("Testing environment-based configuration...")
    
    try:
        from app.settings.app import AppConfig
        
        # Set test environment variables
        test_env = {
            "APP_NAME": "Environment Test App",
            "DATABASE_URL": "postgresql://env:test@localhost:5432/env",
            "JWT_SECRET_KEY": "environment-test-secret-key-for-validation-purposes",
            "ACCESS_TOKEN_EXPIRE_MINUTES": "45",
            "DEBUG": "true"
        }
        
        # Temporarily set environment variables
        original_env = {}
        for key, value in test_env.items():
            original_env[key] = os.environ.get(key)
            os.environ[key] = value
        
        try:
            config = AppConfig.from_env()
            
            assert config.name == "Environment Test App"
            assert config.database.url == "postgresql://env:test@localhost:5432/env"
            assert config.security.access_token_expire_minutes == 45
            assert config.debug is True
            
            logger.info("✅ Environment-based configuration successful")
            return True
        finally:
            # Restore original environment
            for key, value in original_env.items():
                if value is None:
                    os.environ.pop(key, None)
                else:
                    os.environ[key] = value
    except Exception as e:
        logger.error(f"❌ Environment-based configuration failed: {e}")
        return False


def test_yaml_configuration():
    """Test YAML-based configuration loading."""
    logger.info("Testing YAML-based configuration...")
    
    try:
        from app.settings.app import AppConfig
        
        # Create test YAML configuration
        config_data = {
            "name": "YAML Test App",
            "version": "2.0.0",
            "debug": False,
            "database": {
                "url": "postgresql://yaml:test@localhost:5432/yaml",
                "pool_size": 8,
                "echo": False
            },
            "security": {
                "jwt_secret_key": "yaml-test-secret-key-for-configuration-validation",
                "access_token_expire_minutes": 30,
                "max_upload_size": 20971520
            },
            "llm": {
                "default_provider": "groq",
                "default_temperature": 0.8,
                "enable_caching": True
            }
        }
        
        # Create temporary YAML file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = AppConfig.from_yaml_file(temp_path)
            
            assert config.name == "YAML Test App"
            assert config.version == "2.0.0"
            assert config.database.pool_size == 8
            assert config.security.access_token_expire_minutes == 30
            assert config.llm.default_temperature == 0.8
            
            logger.info("✅ YAML-based configuration successful")
            return True
        finally:
            os.unlink(temp_path)
    except Exception as e:
        logger.error(f"❌ YAML-based configuration failed: {e}")
        return False


def test_configuration_manager():
    """Test the configuration manager functionality."""
    logger.info("Testing configuration manager...")
    
    try:
        from app.settings.manager import ConfigurationManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test environment config
            config_data = {
                "name": "Manager Test App",
                "database": {
                    "url": "postgresql://yaml:test@localhost:5432/yaml",
                    "pool_size": 12
                },
                "security": {
                    "jwt_secret_key": "yaml-test-secret-key-for-manager-validation-purposes"
                }
            }
            
            config_file = Path(temp_dir) / "development.yaml"
            with open(config_file, 'w') as f:
                yaml.dump(config_data, f)
            
            # Set environment variables
            test_env = {
                "ENVIRONMENT": "development",
                "DATABASE_URL": "postgresql://manager:test@localhost:5432/manager",
                "JWT_SECRET_KEY": "manager-test-secret-key-for-configuration-validation"
            }
            
            original_env = {}
            for key, value in test_env.items():
                original_env[key] = os.environ.get(key)
                os.environ[key] = value
            
            try:
                manager = ConfigurationManager(config_dir=temp_dir)
                config = manager.load_config()
                
                # Environment variables should take precedence
                assert config.database.url == "postgresql://manager:test@localhost:5432/manager"
                # YAML values should be used where env vars are not set
                assert config.name == "Manager Test App"
                assert config.database.pool_size == 12
                
                # Test caching
                config2 = manager.load_config()
                assert config is config2
                
                # Test reload
                config3 = manager.reload_config()
                assert config is not config3
                
                logger.info("✅ Configuration manager successful")
                return True
            finally:
                # Restore environment
                for key, value in original_env.items():
                    if value is None:
                        os.environ.pop(key, None)
                    else:
                        os.environ[key] = value
    except Exception as e:
        logger.error(f"❌ Configuration manager failed: {e}")
        return False


def test_backward_compatibility():
    """Test backward compatibility with existing config.py."""
    logger.info("Testing backward compatibility...")
    
    try:
        # Import the updated config.py
        from app import config
        
        # Test that old configuration variables are still available
        assert hasattr(config, 'DATABASE_URL')
        assert hasattr(config, 'JWT_SECRET_KEY')
        assert hasattr(config, 'ACCESS_TOKEN_EXPIRE_MINUTES')
        assert hasattr(config, 'MAX_UPLOAD_SIZE')
        assert hasattr(config, 'APP_NAME')
        
        # Test that the hierarchical config is available
        if hasattr(config, 'app_config') and config.app_config:
            assert hasattr(config.app_config, 'database')
            assert hasattr(config.app_config, 'security')
            assert hasattr(config.app_config, 'llm')
        
        logger.info("✅ Backward compatibility successful")
        return True
    except Exception as e:
        logger.error(f"❌ Backward compatibility failed: {e}")
        return False


def test_validation_errors():
    """Test that configuration validation catches errors."""
    logger.info("Testing configuration validation...")
    
    try:
        from app.settings.database import DatabaseConfig
        from app.settings.security import SecurityConfig
        from pydantic import ValidationError
        
        # Test database URL validation
        try:
            DatabaseConfig(url="")
            assert False, "Should have raised validation error for empty URL"
        except ValidationError:
            pass  # Expected
        
        # Test JWT secret validation
        try:
            SecurityConfig(jwt_secret_key="short")
            assert False, "Should have raised validation error for short secret"
        except ValidationError:
            pass  # Expected
        
        # Test environment validation
        from app.settings.base import EnvironmentConfig
        try:
            EnvironmentConfig(environment="invalid")
            assert False, "Should have raised validation error for invalid environment"
        except ValidationError:
            pass  # Expected
        
        logger.info("✅ Configuration validation successful")
        return True
    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        return False


def main():
    """Run all Phase 4 implementation tests."""
    logger.info("🧪 Starting Phase 4 Implementation Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Configuration Imports", test_configuration_imports),
        ("Basic Configuration Creation", test_basic_configuration_creation),
        ("Environment Configuration", test_environment_configuration),
        ("YAML Configuration", test_yaml_configuration),
        ("Configuration Manager", test_configuration_manager),
        ("Backward Compatibility", test_backward_compatibility),
        ("Validation Errors", test_validation_errors)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    logger.info("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All Phase 4 implementation tests passed!")
        return 0
    else:
        logger.error(f"💥 {total - passed} tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
