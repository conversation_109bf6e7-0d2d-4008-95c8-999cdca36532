"""
Performance monitoring and analytics for marketing agent.

This module provides comprehensive monitoring capabilities including
performance metrics, usage analytics, and health checks.
"""

import os
import time
import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from functools import wraps
import psutil
import threading

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    response_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    request_counts: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    error_counts: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    success_counts: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    memory_usage: deque = field(default_factory=lambda: deque(maxlen=100))
    cpu_usage: deque = field(default_factory=lambda: deque(maxlen=100))
    
    def add_response_time(self, operation: str, duration: float) -> None:
        """Add response time measurement."""
        self.response_times.append({
            'operation': operation,
            'duration': duration,
            'timestamp': time.time()
        })
    
    def increment_request(self, operation: str) -> None:
        """Increment request counter."""
        self.request_counts[operation] += 1
    
    def increment_error(self, operation: str) -> None:
        """Increment error counter."""
        self.error_counts[operation] += 1
    
    def increment_success(self, operation: str) -> None:
        """Increment success counter."""
        self.success_counts[operation] += 1
    
    def add_system_metrics(self) -> None:
        """Add current system metrics."""
        try:
            memory_percent = psutil.virtual_memory().percent
            cpu_percent = psutil.cpu_percent()
            
            self.memory_usage.append({
                'value': memory_percent,
                'timestamp': time.time()
            })
            
            self.cpu_usage.append({
                'value': cpu_percent,
                'timestamp': time.time()
            })
        except Exception as e:
            logger.warning(f"Failed to collect system metrics: {e}")


class MarketingAgentMonitor:
    """Comprehensive monitoring for marketing agent."""
    
    def __init__(self):
        self.metrics = PerformanceMetrics()
        self.is_monitoring = False
        self.monitor_thread = None
        self.alert_thresholds = {
            'response_time_p95': 5.0,  # seconds
            'error_rate': 0.1,  # 10%
            'memory_usage': 80.0,  # percent
            'cpu_usage': 80.0  # percent
        }
        self.alert_callbacks: List[Callable] = []
    
    def start_monitoring(self, interval: float = 30.0) -> None:
        """Start background monitoring."""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info("Marketing agent monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop background monitoring."""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        logger.info("Marketing agent monitoring stopped")
    
    def _monitoring_loop(self, interval: float) -> None:
        """Background monitoring loop."""
        while self.is_monitoring:
            try:
                # Collect system metrics
                self.metrics.add_system_metrics()
                
                # Check for alerts
                self._check_alerts()
                
                time.sleep(interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(interval)
    
    def record_operation(
        self,
        operation: str,
        duration: float,
        success: bool = True,
        error_type: Optional[str] = None
    ) -> None:
        """Record operation metrics."""
        self.metrics.add_response_time(operation, duration)
        self.metrics.increment_request(operation)
        
        if success:
            self.metrics.increment_success(operation)
        else:
            self.metrics.increment_error(f"{operation}:{error_type or 'unknown'}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        response_times = [m['duration'] for m in self.metrics.response_times]
        
        if not response_times:
            return {
                'response_time_stats': {},
                'request_counts': dict(self.metrics.request_counts),
                'error_counts': dict(self.metrics.error_counts),
                'success_counts': dict(self.metrics.success_counts),
                'system_metrics': {}
            }
        
        # Calculate response time statistics
        response_times.sort()
        n = len(response_times)
        
        response_time_stats = {
            'count': n,
            'min': min(response_times),
            'max': max(response_times),
            'mean': sum(response_times) / n,
            'p50': response_times[int(n * 0.5)],
            'p95': response_times[int(n * 0.95)],
            'p99': response_times[int(n * 0.99)]
        }
        
        # Get recent system metrics
        recent_memory = [m['value'] for m in list(self.metrics.memory_usage)[-10:]]
        recent_cpu = [m['value'] for m in list(self.metrics.cpu_usage)[-10:]]
        
        system_metrics = {}
        if recent_memory:
            system_metrics['memory_usage'] = {
                'current': recent_memory[-1],
                'average': sum(recent_memory) / len(recent_memory),
                'max': max(recent_memory)
            }
        
        if recent_cpu:
            system_metrics['cpu_usage'] = {
                'current': recent_cpu[-1],
                'average': sum(recent_cpu) / len(recent_cpu),
                'max': max(recent_cpu)
            }
        
        return {
            'response_time_stats': response_time_stats,
            'request_counts': dict(self.metrics.request_counts),
            'error_counts': dict(self.metrics.error_counts),
            'success_counts': dict(self.metrics.success_counts),
            'system_metrics': system_metrics
        }
    
    def get_error_rate(self, operation: Optional[str] = None) -> float:
        """Calculate error rate for operation or overall."""
        if operation:
            total_requests = self.metrics.request_counts.get(operation, 0)
            errors = sum(
                count for error_key, count in self.metrics.error_counts.items()
                if error_key.startswith(f"{operation}:")
            )
        else:
            total_requests = sum(self.metrics.request_counts.values())
            errors = sum(self.metrics.error_counts.values())
        
        if total_requests == 0:
            return 0.0
        
        return errors / total_requests
    
    def _check_alerts(self) -> None:
        """Check for alert conditions."""
        alerts = []
        
        # Check response time P95
        summary = self.get_performance_summary()
        response_stats = summary.get('response_time_stats', {})
        
        if response_stats.get('p95', 0) > self.alert_thresholds['response_time_p95']:
            alerts.append({
                'type': 'response_time_high',
                'message': f"P95 response time is {response_stats['p95']:.2f}s",
                'threshold': self.alert_thresholds['response_time_p95']
            })
        
        # Check error rate
        error_rate = self.get_error_rate()
        if error_rate > self.alert_thresholds['error_rate']:
            alerts.append({
                'type': 'error_rate_high',
                'message': f"Error rate is {error_rate:.2%}",
                'threshold': self.alert_thresholds['error_rate']
            })
        
        # Check system metrics
        system_metrics = summary.get('system_metrics', {})
        
        memory_usage = system_metrics.get('memory_usage', {}).get('current', 0)
        if memory_usage > self.alert_thresholds['memory_usage']:
            alerts.append({
                'type': 'memory_usage_high',
                'message': f"Memory usage is {memory_usage:.1f}%",
                'threshold': self.alert_thresholds['memory_usage']
            })
        
        cpu_usage = system_metrics.get('cpu_usage', {}).get('current', 0)
        if cpu_usage > self.alert_thresholds['cpu_usage']:
            alerts.append({
                'type': 'cpu_usage_high',
                'message': f"CPU usage is {cpu_usage:.1f}%",
                'threshold': self.alert_thresholds['cpu_usage']
            })
        
        # Trigger alert callbacks
        for alert in alerts:
            self._trigger_alert(alert)
    
    def _trigger_alert(self, alert: Dict[str, Any]) -> None:
        """Trigger alert callbacks."""
        logger.warning(f"Alert triggered: {alert['message']}")
        
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")
    
    def add_alert_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Add alert callback function."""
        self.alert_callbacks.append(callback)
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        summary = self.get_performance_summary()
        
        # Determine health status
        health_status = "healthy"
        issues = []
        
        # Check response times
        response_stats = summary.get('response_time_stats', {})
        if response_stats.get('p95', 0) > self.alert_thresholds['response_time_p95']:
            health_status = "degraded"
            issues.append("High response times")
        
        # Check error rate
        error_rate = self.get_error_rate()
        if error_rate > self.alert_thresholds['error_rate']:
            health_status = "unhealthy"
            issues.append("High error rate")
        
        # Check system resources
        system_metrics = summary.get('system_metrics', {})
        memory_usage = system_metrics.get('memory_usage', {}).get('current', 0)
        cpu_usage = system_metrics.get('cpu_usage', {}).get('current', 0)
        
        if memory_usage > self.alert_thresholds['memory_usage']:
            health_status = "degraded"
            issues.append("High memory usage")
        
        if cpu_usage > self.alert_thresholds['cpu_usage']:
            health_status = "degraded"
            issues.append("High CPU usage")
        
        return {
            'status': health_status,
            'issues': issues,
            'metrics': summary,
            'timestamp': time.time()
        }


def performance_monitor(operation_name: str):
    """Decorator to monitor function performance."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error_type = None
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error_type = type(e).__name__
                raise
            finally:
                duration = time.time() - start_time
                monitor.record_operation(operation_name, duration, success, error_type)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error_type = None
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error_type = type(e).__name__
                raise
            finally:
                duration = time.time() - start_time
                monitor.record_operation(operation_name, duration, success, error_type)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


# Global monitor instance
monitor = MarketingAgentMonitor()

# Auto-start monitoring if enabled
if os.getenv('MARKETING_AGENT_ENABLE_MONITORING', 'true').lower() == 'true':
    monitor.start_monitoring()
