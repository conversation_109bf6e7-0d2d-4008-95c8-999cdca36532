"""Add dashboard tables and enhance existing schema

Revision ID: add_dashboard_tables
Revises: 
Create Date: 2024-12-23 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_dashboard_tables'
down_revision = None
depends_on = None


def upgrade():
    """Add dashboard tables and enhance existing schema."""
    
    # Create dashboards table
    op.create_table('dashboards',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_default', sa.Boolean(), nullable=True),
        sa.Column('is_public', sa.<PERSON>an(), nullable=True),
        sa.Column('layout_config', sa.<PERSON>(), nullable=True),
        sa.Column('theme_config', sa.J<PERSON>(), nullable=True),
        sa.Column('refresh_interval', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'is_default', name='unique_user_default_dashboard')
    )
    op.create_index(op.f('ix_dashboards_id'), 'dashboards', ['id'], unique=False)
    op.create_index(op.f('ix_dashboards_user_id'), 'dashboards', ['user_id'], unique=False)

    # Create dashboard_data_sources association table
    op.create_table('dashboard_data_sources',
        sa.Column('dashboard_id', sa.String(length=36), nullable=False),
        sa.Column('data_source_id', sa.String(length=36), nullable=False),
        sa.Column('alias', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['dashboard_id'], ['dashboards.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['data_source_id'], ['data_sources.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('dashboard_id', 'data_source_id')
    )

    # Add dashboard_id to dashboard_sections if it doesn't exist
    try:
        op.add_column('dashboard_sections', sa.Column('dashboard_id', sa.String(length=36), nullable=True))
        op.create_foreign_key('fk_dashboard_sections_dashboard_id', 'dashboard_sections', 'dashboards', ['dashboard_id'], ['id'])
        op.create_index('ix_dashboard_sections_dashboard_id', 'dashboard_sections', ['dashboard_id'])
    except Exception:
        # Column might already exist
        pass

    # Add sync fields to data_sources if they don't exist
    try:
        op.add_column('data_sources', sa.Column('sync_status', sa.String(length=50), nullable=True))
        op.add_column('data_sources', sa.Column('last_sync', sa.DateTime(timezone=True), nullable=True))
    except Exception:
        # Columns might already exist
        pass

    # Set default values for new columns
    op.execute("UPDATE data_sources SET sync_status = 'active' WHERE sync_status IS NULL")
    op.execute("UPDATE dashboards SET is_default = false WHERE is_default IS NULL")
    op.execute("UPDATE dashboards SET is_public = false WHERE is_public IS NULL")
    op.execute("UPDATE dashboards SET refresh_interval = 300 WHERE refresh_interval IS NULL")


def downgrade():
    """Remove dashboard tables and revert schema changes."""
    
    # Drop indexes and foreign keys first
    try:
        op.drop_index('ix_dashboard_sections_dashboard_id', 'dashboard_sections')
        op.drop_constraint('fk_dashboard_sections_dashboard_id', 'dashboard_sections', type_='foreignkey')
        op.drop_column('dashboard_sections', 'dashboard_id')
    except Exception:
        pass

    try:
        op.drop_column('data_sources', 'last_sync')
        op.drop_column('data_sources', 'sync_status')
    except Exception:
        pass

    # Drop association table
    op.drop_table('dashboard_data_sources')

    # Drop main dashboard table
    op.drop_index(op.f('ix_dashboards_user_id'), 'dashboards')
    op.drop_index(op.f('ix_dashboards_id'), 'dashboards')
    op.drop_table('dashboards')
