#!/usr/bin/env python3
"""
Test script to verify markdown table generation works correctly.
"""

import pandas as pd
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dataframe_to_markdown():
    """Test DataFrame to markdown table conversion."""
    
    # Create sample data
    data = {
        'CustomerAge': [32, 28, 45, 24, 38],
        'UnitsSold': [15, 8, 22, 12, 18],
        'totalRevenue': [450.00, 240.00, 660.00, 360.00, 540.00],
        'Price': [30.00, 30.00, 30.00, 30.00, 30.00],
        'Category': ['Electronics', 'Clothing', 'Home', 'Electronics', 'Sports']
    }
    
    df = pd.DataFrame(data)
    
    # Test the markdown conversion function
    def dataframe_to_markdown_table(df: pd.DataFrame) -> str:
        """Convert a pandas DataFrame to a markdown table format."""
        if df.empty:
            return "*No data available*"
        
        try:
            # Get column names
            columns = df.columns.tolist()
            
            # Create header row
            header_row = "| " + " | ".join(str(col) for col in columns) + " |"
            
            # Create separator row
            separator_row = "|" + "|".join([" --- " for _ in columns]) + "|"
            
            # Create data rows
            data_rows = []
            for _, row in df.iterrows():
                row_values = []
                for col in columns:
                    value = row[col]
                    # Format the value appropriately
                    if pd.isna(value):
                        formatted_value = "N/A"
                    elif isinstance(value, float):
                        # Format floats to 2 decimal places if they have decimals
                        if value == int(value):
                            formatted_value = str(int(value))
                        else:
                            formatted_value = f"{value:.2f}"
                    else:
                        formatted_value = str(value)
                    row_values.append(formatted_value)
                
                data_row = "| " + " | ".join(row_values) + " |"
                data_rows.append(data_row)
            
            # Combine all parts
            table_lines = [header_row, separator_row] + data_rows
            return "\n".join(table_lines)
            
        except Exception as e:
            print(f"Error converting DataFrame to markdown table: {e}")
            return f"```\n{df.to_string()}\n```"
    
    # Test describe table
    def describe_to_markdown_table(desc_df: pd.DataFrame) -> str:
        """Convert a pandas describe() DataFrame to a markdown table format."""
        if desc_df.empty:
            return "*No statistical summary available*"
        
        try:
            # Get column names (the original data columns)
            columns = desc_df.columns.tolist()
            
            # Get statistic names (index of describe DataFrame)
            statistics = desc_df.index.tolist()
            
            # Create header row
            header_row = "| Statistic | " + " | ".join(str(col) for col in columns) + " |"
            
            # Create separator row
            separator_row = "|-----------|" + "|".join([" --- " for _ in columns]) + "|"
            
            # Create data rows
            data_rows = []
            for stat in statistics:
                row_values = [f"**{stat}**"]  # Bold statistic name
                for col in columns:
                    value = desc_df.loc[stat, col]
                    # Format the value appropriately
                    if pd.isna(value):
                        formatted_value = "N/A"
                    elif isinstance(value, float):
                        # Format floats to 2 decimal places if they have decimals
                        if value == int(value):
                            formatted_value = str(int(value))
                        else:
                            formatted_value = f"{value:.2f}"
                    else:
                        formatted_value = str(value)
                    row_values.append(formatted_value)
                
                data_row = "| " + " | ".join(row_values) + " |"
                data_rows.append(data_row)
            
            # Combine all parts
            table_lines = [header_row, separator_row] + data_rows
            return "\n".join(table_lines)
            
        except Exception as e:
            print(f"Error converting describe DataFrame to markdown table: {e}")
            return f"```\n{desc_df.to_string()}\n```"
    
    print("=== Testing DataFrame to Markdown Table ===")
    print("Original DataFrame:")
    print(df)
    print("\nMarkdown Table:")
    markdown_table = dataframe_to_markdown_table(df)
    print(markdown_table)
    
    print("\n=== Testing Describe to Markdown Table ===")
    desc_df = df.describe()
    print("Original Describe:")
    print(desc_df)
    print("\nMarkdown Describe Table:")
    markdown_describe = describe_to_markdown_table(desc_df)
    print(markdown_describe)
    
    print("\n=== Expected Output in Chat ===")
    print("This is how it should look in the chat interface:")
    print("\n# Data Profile for: Mobile duka")
    print("\n## Overview")
    print("\n| Property | Value |")
    print("|----------|-------|")
    print("| **Rows** | 5 |")
    print("| **Columns** | 5 |")
    print("| **Data Types** | 4 numeric, 1 categorical |")
    print("\n## Data Preview (first few rows)")
    print(f"\n{markdown_table}")
    print("\n## Statistical Summary")
    print(f"\n{markdown_describe}")

if __name__ == "__main__":
    test_dataframe_to_markdown()
