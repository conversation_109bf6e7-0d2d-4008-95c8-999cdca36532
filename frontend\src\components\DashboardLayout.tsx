
import React from 'react';
import { motion } from 'framer-motion';
import { LeftNavbar } from "@/components/LeftNavbar";
import { UnifiedHeader } from "@/components/UnifiedHeader";
import { FloatingAITrigger } from "./dashboard/FloatingAITrigger";
import { useDashboardMode } from '@/stores/dashboard-mode-store';
import { useNavbarStore } from '@/stores/navbar-store';
import { cn } from '@/lib/utils';

interface DashboardLayoutProps {
  children: React.ReactNode;
  // Header control (simplified - dashboard controls moved to ribbon)
  showHeader?: boolean;
  title?: string;
  description?: string;
  isLoading?: boolean;
  showDashboardControls?: boolean;
  // Floating AI Assistant props
  showFloatingAI?: boolean;
  onWidgetCreate?: (widget_config: any) => void;
  onSectionCreate?: (section_config: any) => void;
  onTemplateApply?: (template_id: string) => void;
  onDataConfigure?: (data_config: any) => void;
  onDataAnalyze?: (analysis_request: any) => void;
  onDashboardOptimize?: (optimization_params: any) => void;
  onCodeGenerate?: (code_request: any) => void;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  showHeader = true,
  title,
  description,
  isLoading,
  showDashboardControls = false,
  // Floating AI Assistant props
  showFloatingAI = true,
  onWidgetCreate,
  onSectionCreate,
  onTemplateApply,
  onDataConfigure,
  onDataAnalyze,
  onDashboardOptimize,
  onCodeGenerate,
}) => {
  const { current_mode } = useDashboardMode();
  const { shouldShowExpanded } = useNavbarStore();

  // Ribbon toolbar is now handled by DashboardModeWrapper in advanced mode

  // Check if navbar is expanded for CSS class application
  const isNavbarExpanded = shouldShowExpanded();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Left Navigation Bar - Fixed position with higher z-index */}
      <LeftNavbar />

      {/* Simplified Header - Dashboard controls moved to ribbon */}
      {showHeader && (
        <UnifiedHeader
          title={title}
          description={description}
          isLoading={isLoading}
          showDashboardControls={showDashboardControls}
        />
      )}

      {/* Main content area positioned below header - responsive positioning handled by child components */}
      <motion.main
        className="transition-all duration-300 ease-in-out flex justify-center"
        initial={false}
        animate={{
          marginLeft: isNavbarExpanded ? 240 : 64,
          width: `calc(100% - ${isNavbarExpanded ? 240 : 64}px)`
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        {/* Ribbon toolbar now handled by DashboardModeWrapper */}

        {/* Dashboard content with consistent padding */}
        <div className="min-h-full w-full px-6 py-2">
          {children}
        </div>
      </motion.main>

      {/* Floating AI Assistant */}
      {showFloatingAI && (
        <FloatingAITrigger
          onWidgetCreate={onWidgetCreate}
          onSectionCreate={onSectionCreate}
          onTemplateApply={onTemplateApply}
          onDataConfigure={onDataConfigure}
          onDataAnalyze={onDataAnalyze}
          onDashboardOptimize={onDashboardOptimize}
          onCodeGenerate={onCodeGenerate}
          position="bottom-right"
        />
      )}
    </div>
  );
};
