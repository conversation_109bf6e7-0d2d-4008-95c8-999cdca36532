"""
Natural language query MCP tool for the Datagenius backend.

This module provides an MCP-compatible tool for querying data using natural language
with PandasAI.
"""

import logging
import os
import pandas as pd
from typing import Dict, Any, List, Optional

# PandasAI imports
import pandasai as pai

from .base import BaseMCPTool
from ..pandasai_v3.wrapper import PandasAIWrapper
from ..pandasai_v3.cache import ResponseCache
from ..pandasai_v3.error_handler import ErrorHandler
from agents.utils.agent_identity import detect_agent_identity
from agents.utils.system_prompts import get_agent_system_prompt

logger = logging.getLogger(__name__)


class NaturalLanguageQueryTool(BaseMCPTool):
    """Tool for querying data using natural language with PandasAI."""

    def __init__(self):
        """Initialize the natural language query tool."""
        super().__init__(
            name="natural_language_query",
            description="Processes natural language queries against a data file using the PandasAI library and a configured LLM (e.g., OpenAI). Translates the query and executes it.",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV, Excel, JSON supported)."
                    },
                    "query": {
                        "type": "string",
                        "description": "The user's query in natural language."
                    },
                    "conversation_history": {
                        "type": "array",
                        "description": "List of previous conversation turns.",
                        "items": {
                            "type": "object",
                            "properties": {
                                "role": {"type": "string"},
                                "content": {"type": "string"}
                            }
                        }
                    },
                    "api_key": {
                        "type": "string",
                        "description": "API key for the LLM provider."
                    },
                    "provider": {
                        "type": "string",
                        "description": "LLM provider to use (e.g., openai, groq, anthropic).",
                        "default": "openai"
                    },
                    "model": {
                        "type": "string",
                        "description": "Model name to use for the query."
                    }
                },
                "required": ["file_path", "query", "api_key"]
            }
        )
        self.pandasai = PandasAIWrapper()
        self.cache = ResponseCache()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the tool with configuration.

        Args:
            config: Configuration dictionary for the tool
        """
        # No additional initialization needed
        pass

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the natural language query tool with the provided arguments.

        Args:
            arguments: Arguments for tool execution (following the inputSchema)

        Returns:
            Tool execution results in MCP format
        """
        try:
            file_path = arguments["file_path"]
            query = arguments["query"]
            api_key = arguments["api_key"]
            provider = arguments.get("provider", "openai")
            model = arguments.get("model")
            conversation_history = arguments.get("conversation_history", [])

            # Agent context for dynamic identity detection
            user_context = arguments.get("context", {})
            agent_id = arguments.get("persona_id") or arguments.get("agent_id")

            # Detect agent identity for personalized natural language query processing
            agent_identity = await detect_agent_identity(
                agent_id=agent_id,
                context=user_context,
                intent_type="natural_language_query"
            )

            logger.info(f"Detected agent identity: {agent_identity} for natural language query")
            logger.info(f"PandasAI NL query requested for {file_path} with query: {query}")

            # Check if we have a cached response (include agent identity in cache key)
            cache_key = f"{file_path}:{query}:{provider}:{model}:{agent_identity}"
            cached_result = self.cache.get(cache_key)
            if cached_result:
                logger.info(f"Using cached result for natural language query: {cache_key}")
                # Ensure cached result has agent metadata
                if "metadata" not in cached_result:
                    cached_result["metadata"] = {}
                cached_result["metadata"]["agent_identity"] = agent_identity
                cached_result["metadata"]["agent_aware"] = True
                return cached_result

            # Input validation
            if not provider or not api_key:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Error: LLM Provider and API Key must be provided."
                        }
                    ],
                    "metadata": {
                        "file_path": file_path,
                        "query": query,
                        "status": "error",
                        "error_type": "config_error",
                        "agent_identity": agent_identity,
                        "agent_aware": True
                    }
                }

            # Load the data
            if file_path.lower().endswith(".csv"):
                df = pd.read_csv(file_path)
            elif file_path.lower().endswith((".xls", ".xlsx")):
                df = pd.read_excel(file_path)
            elif file_path.lower().endswith(".json"):
                df = pd.read_json(file_path)
            else:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Unsupported file format: {file_path}"
                        }
                    ],
                    "metadata": {
                        "agent_identity": agent_identity,
                        "agent_aware": True,
                        "error_type": "unsupported_format"
                    }
                }

            if df.empty:
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": f"The dataframe loaded from {file_path} is empty."
                        }
                    ],
                    "metadata": {
                        "agent_identity": agent_identity,
                        "agent_aware": True,
                        "error_type": "empty_dataframe"
                    }
                }

            # Execute the query with agent awareness
            # Initialize PandasAI
            self.pandasai.initialize(api_key, provider)

            # Create agent with the dataframe
            if not self.pandasai.create_agent(df=df, model=model):
                return {
                    "isError": True,
                    "content": [
                        {
                            "type": "text",
                            "text": "Error creating PandasAI Agent"
                        }
                    ],
                    "metadata": {
                        "agent_identity": agent_identity,
                        "agent_aware": True,
                        "error_type": "pandasai_agent_creation_failed"
                    }
                }

            # Enhance query with agent-specific context
            enhanced_query = await self._enhance_query_with_agent_context(query, agent_identity, df)

            # Run the enhanced query using PandasAI wrapper
            result = self.pandasai.chat(enhanced_query)

            # Process the result
            if isinstance(result, (pd.DataFrame, pd.Series)):
                result_df = result if isinstance(result, pd.DataFrame) else result.to_frame()
                rows = len(result_df)
                results_metadata = {"status": "success", "result_type": "dataframe", "rows_returned": rows}
                if rows > 50:  # Limit output size
                    results_text = f"Query returned a table with {rows} rows. Showing the first 50:\n\n{result_df.head(50).to_string()}"
                    results_metadata["truncated"] = True
                elif rows == 0:
                    results_text = "Query executed successfully, but returned no matching rows."
                else:
                    results_text = f"Query results ({rows} rows):\n\n{result_df.to_string()}"
            elif isinstance(result, (str, int, float, bool)):
                results_text = f"Query result: {result}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__), "value": result}
            elif result is None:
                results_text = "The query was processed, but did not produce a direct textual or numerical result. It might have been an action or a query with no return value."
                results_metadata = {"status": "success", "result_type": "None"}
            else:
                # Handle unexpected result types
                results_text = f"Query returned an unexpected result type: {type(result).__name__}. Result: {str(result)}"
                results_metadata = {"status": "success", "result_type": str(type(result).__name__)}

            # Add agent-specific insights to the results
            agent_insights = await self._generate_agent_specific_insights(results_text, agent_identity, df)
            if agent_insights:
                results_text += f"\n\n{agent_insights}"

            response = {
                "content": [
                    {
                        "type": "text",
                        "text": results_text
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "query": query,
                    "enhanced_query": enhanced_query,
                    "implementation": "pandasai",
                    "agent_identity": agent_identity,
                    "agent_aware": True,
                    **results_metadata
                }
            }

            # Cache the response
            self.cache.set(cache_key, response)
            return response

        except Exception as e:
            error_handler = ErrorHandler()
            error_info = error_handler.handle_error(e, context={
                "operation": "natural_language_query",
                "file_path": file_path,
                "query": query,
                "agent_identity": agent_identity
            })

            return {
                "isError": True,
                "content": [
                    {
                        "type": "text",
                        "text": error_info["message"]
                    }
                ],
                "metadata": {
                    "file_path": file_path,
                    "query": query,
                    "error_type": error_info["error_type"],
                    "details": error_info["details"],
                    "agent_identity": agent_identity,
                    "agent_aware": True
                }
            }

    async def _enhance_query_with_agent_context(self, query: str, agent_identity: str, df: pd.DataFrame) -> str:
        """
        Enhance the natural language query with agent-specific context and preferences.

        Args:
            query: Original user query
            agent_identity: Detected agent identity
            df: DataFrame being queried

        Returns:
            Enhanced query with agent-specific context
        """
        try:
            # Get agent system prompt to extract preferences
            system_prompt = await get_agent_system_prompt(agent_identity)

            # Extract agent-specific query enhancements
            if agent_identity == "analyst" or "analysis" in system_prompt.lower():
                enhanced_query = f"""
                {query}

                Please provide detailed statistical analysis including:
                - Descriptive statistics where relevant
                - Data quality insights
                - Statistical significance if applicable
                - Confidence intervals for estimates
                """
            elif agent_identity == "marketer" or "marketing" in system_prompt.lower():
                enhanced_query = f"""
                {query}

                Please focus on business insights including:
                - Key performance indicators
                - Trends and patterns relevant to marketing
                - Actionable recommendations
                - ROI implications where applicable
                """
            elif agent_identity == "classifier" or "classification" in system_prompt.lower():
                enhanced_query = f"""
                {query}

                Please organize results systematically:
                - Clear categorization of findings
                - Distribution of categories
                - Classification metrics if applicable
                - Well-structured output format
                """
            elif agent_identity == "concierge":
                enhanced_query = f"""
                {query}

                Please provide user-friendly results:
                - Clear, accessible explanations
                - Practical insights
                - Easy-to-understand format
                - Helpful context for interpretation
                """
            else:
                # General enhancement
                enhanced_query = f"""
                {query}

                Please provide comprehensive results with clear explanations and relevant insights.
                """

            logger.info(f"Enhanced query for {agent_identity} agent")
            return enhanced_query.strip()

        except Exception as e:
            logger.warning(f"Failed to enhance query with agent context: {e}")
            return query

    async def _generate_agent_specific_insights(self, results_text: str, agent_identity: str, df: pd.DataFrame) -> str:
        """
        Generate agent-specific insights based on the query results.

        Args:
            results_text: Original query results
            agent_identity: Detected agent identity
            df: DataFrame that was queried

        Returns:
            Agent-specific insights text
        """
        try:
            insights = []

            # Get basic data info
            data_shape = df.shape
            data_info = f"Dataset: {data_shape[0]} rows, {data_shape[1]} columns"

            if agent_identity == "analyst":
                insights.extend([
                    "📊 **Analyst Insights:**",
                    f"• {data_info}",
                    "• Consider statistical significance and confidence intervals",
                    "• Validate assumptions before drawing conclusions",
                    "• Look for outliers and data quality issues"
                ])
            elif agent_identity == "marketer":
                insights.extend([
                    "🎯 **Marketing Insights:**",
                    f"• {data_info}",
                    "• Focus on actionable business metrics",
                    "• Consider customer segmentation opportunities",
                    "• Evaluate ROI and performance indicators",
                    "• Look for campaign optimization opportunities"
                ])
            elif agent_identity == "classifier":
                insights.extend([
                    "🏷️ **Classification Insights:**",
                    f"• {data_info}",
                    "• Results are systematically organized",
                    "• Consider category distributions and balance",
                    "• Evaluate classification accuracy if applicable",
                    "• Look for patterns in categorical data"
                ])
            elif agent_identity == "concierge":
                insights.extend([
                    "💡 **Key Takeaways:**",
                    f"• {data_info}",
                    "• Results are presented in user-friendly format",
                    "• Focus on practical applications",
                    "• Consider next steps for further analysis"
                ])

            if insights:
                return "\n".join(insights)

        except Exception as e:
            logger.warning(f"Failed to generate agent-specific insights: {e}")

        return ""
