import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import ProviderIcon from './ProviderIcon';

interface Provider {
  id: string;
  name: string;
}

interface ProviderSelectProps {
  providers: Provider[];
  value: string;
  onChange: (value: string) => void;
  name?: string;
}

const ProviderSelect: React.FC<ProviderSelectProps> = ({
  providers,
  value,
  onChange,
  name = 'provider',
}) => {
  return (
    <Select
      name={name}
      value={value}
      onValueChange={onChange}
    >
      <SelectTrigger className="bg-white border-gray-300 hover:bg-gray-50 transition-colors">
        {value ? (
          <div className="flex items-center">
            <div className="flex items-center justify-center h-6 w-6 rounded-full bg-gray-100 mr-2">
              <ProviderIcon providerId={value} className="h-4 w-4 text-gray-700" />
            </div>
            <span className="font-medium">{providers.find(p => p.id === value)?.name || "Select provider"}</span>
          </div>
        ) : (
          <SelectValue placeholder="Select provider" />
        )}
      </SelectTrigger>
      <SelectContent>
        {providers.map((provider) => (
          <SelectItem key={provider.id} value={provider.id} className="py-1.5">
            <div className="flex items-center">
              <div className="flex items-center justify-center h-6 w-6 rounded-full bg-gray-100 mr-2">
                <ProviderIcon providerId={provider.id} className="h-4 w-4 text-gray-700" />
              </div>
              <div className="flex flex-col">
                <span className="font-medium">{provider.name}</span>
                <span className="text-xs text-gray-500">
                  {provider.id === 'openai' && 'OpenAI API'}
                  {provider.id === 'anthropic' && 'Claude API'}
                  {provider.id === 'groq' && 'Groq API'}
                  {provider.id === 'gemini' && 'Google AI'}
                  {provider.id === 'openrouter' && 'Multiple models'}
                  {provider.id === 'requesty' && 'Custom API'}
                </span>
              </div>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default ProviderSelect;
