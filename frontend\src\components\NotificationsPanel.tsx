
import React, { useEffect, useMemo } from "react"; // Added useEffect, useMemo
import { motion, AnimatePresence } from "framer-motion";
import { X, AlertCircle, BellRing, Info, RefreshCw, CheckCheck } from "lucide-react"; // Updated icons
import { Button } from "@/components/ui/button";
import { useNotificationsManager } from "@/hooks/use-notifications";
import { Notification } from "@/lib/api"; // Import backend Notification type
import { Skeleton } from "@/components/ui/skeleton";
import { formatDistanceToNow } from 'date-fns'; // For relative time

interface NotificationsPanelProps {
  className?: string;
}

export function NotificationsPanel({ className = "" }: NotificationsPanelProps) {
  const {
    notifications: fetchedNotifications,
    newNotifications,
    isLoading,
    isError,
    error,
    markAsRead,
    markAllAsRead,
    refetch,
    isConnected, // WebSocket connection status
    clearNewNotifications
  } = useNotificationsManager();

  // Combine fetched and new real-time notifications, ensuring no duplicates and sorting
  const allNotifications = useMemo(() => {
    const combined = [...newNotifications, ...fetchedNotifications];
    const uniqueNotifications = Array.from(new Map(combined.map(n => [n.id, n])).values());
    return uniqueNotifications.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }, [fetchedNotifications, newNotifications]);

  useEffect(() => {
    // Clear newNotifications from the hook's local state when the panel is viewed,
    // as they are now part of 'allNotifications' derived from fetched data + new ones.
    // This prevents them from re-appearing as "new" if the panel is closed and reopened.
    // The hook itself will refetch and include them.
    // Or, if the panel is always visible, this might not be needed.
    // For now, let's assume newNotifications are cleared when the hook provides them.
    // The hook `useRealTimeNotifications` has `clearNewNotifications` which we can call.
    // This might be better called when the panel is opened or when notifications are "seen".
  }, [newNotifications, clearNewNotifications]);


  const getIcon = (urgency: Notification["urgency"], type: Notification["type"]) => {
    switch (urgency) {
      case "high":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case "medium":
        return <BellRing className="h-5 w-5 text-yellow-500" />;
      case "low":
        return <Info className="h-5 w-5 text-blue-500" />;
      default: // Fallback based on type if urgency is not standard
        if (type === "system_alert" || type === "critical") return <AlertCircle className="h-5 w-5 text-red-500" />;
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  const handleDismissNotification = (id: string) => {
    markAsRead(id); // This will optimistic update or refetch via React Query
  };
  
  const handleMarkAllRead = () => {
    markAllAsRead();
  };

  if (isLoading) {
    return (
      <div className={`p-4 ${className}`}>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-start p-3 rounded-md bg-muted/50 mb-2">
            <Skeleton className="h-5 w-5 mr-3 rounded-full" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-1/4" />
            </div>
            <Skeleton className="h-6 w-6 ml-2" />
          </div>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className={`p-4 text-center text-red-500 ${className}`}>
        Error loading notifications: {error instanceof Error ? error.message : "Unknown error"}
        <Button onClick={() => refetch()} variant="outline" size="sm" className="mt-2 ml-2">
          <RefreshCw className="mr-2 h-4 w-4" /> Retry
        </Button>
      </div>
    );
  }

  if (allNotifications.length === 0) {
    return (
      <div className={`p-4 text-center text-muted-foreground ${className}`}>
        No new notifications. {!isConnected && <span className="text-xs block">(Real-time updates disconnected)</span>}
      </div>
    );
  }

  return (
    <div className={`${className} p-2`}>
      <div className="flex justify-between items-center mb-2 px-1">
        <h3 className="text-sm font-semibold">Notifications</h3>
        {allNotifications.some(n => !n.is_read) && (
          <Button variant="outline" size="sm" onClick={handleMarkAllRead}>
            <CheckCheck className="h-4 w-4 mr-1" /> Mark all read
          </Button>
        )}
      </div>
      <AnimatePresence>
        {allNotifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, x: -20, transition: { duration: 0.15 } }}
            transition={{ duration: 0.2 }}
            className={`mb-2 last:mb-0 border-l-4 ${notification.is_read ? 'border-transparent' : 
              notification.urgency === 'high' ? 'border-red-500' : 
              notification.urgency === 'medium' ? 'border-yellow-500' : 'border-blue-500'
            }`}
          >
            <div className={`flex items-start p-3 rounded-r-md ${notification.is_read ? 'bg-muted/30 hover:bg-muted/50' : 'bg-muted/60 hover:bg-muted/80'} transition-colors`}>
              <div className="mr-3 mt-0.5">{getIcon(notification.urgency, notification.type)}</div>
              <div className="flex-1">
                <div className={`font-medium ${notification.is_read ? 'text-muted-foreground' : ''}`}>{notification.title}</div>
                <div className={`text-sm ${notification.is_read ? 'text-muted-foreground/80' : 'text-foreground/90'}`}>{notification.message}</div>
                <div className="text-xs text-muted-foreground mt-1">
                  {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                </div>
              </div>
              {!notification.is_read && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 -mr-1 -mt-1"
                  onClick={() => handleDismissNotification(notification.id)}
                  title="Mark as read"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
       {!isConnected && <p className="text-xs text-center text-amber-600 mt-2">Real-time updates currently disconnected.</p>}
    </div>
  );
}
