
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useSettingsState } from "@/hooks/use-page-state";
import {
  Bell,
  ChevronRight,
  LayoutDashboard,
  LogOut,
  Moon,
  Save,
  User,
  Bo<PERSON>,
} from "lucide-react";
import { LLMProviderSettings } from "@/components/settings/LLMProviderSettings";
import { LeftNavbar } from "@/components/LeftNavbar";
import { useNavbarStore } from "@/stores/navbar-store";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { toast } from "sonner";

const Settings = () => {
  const navigate = useNavigate();
  const { user, updateUser, logout } = useAuth();
  const { shouldShowExpanded, getNavbarWidth } = useNavbarStore();

  // Page state management
  const { state: pageState, setState: setPageState } = useSettingsState();

  // Initialize state from page state or defaults
  const [activeTab, setActiveTab] = useState(pageState?.activeTab || "general");
  const [theme, setTheme] = useState(pageState?.theme || user?.theme_preference || "light");
  const [notificationsEnabled, setNotificationsEnabled] = useState(
    pageState?.notifications?.email ?? true
  );
  const [emailFrequency, setEmailFrequency] = useState(
    pageState?.formData?.emailFrequency || "daily"
  );

  // User profile state - initialize from page state or user data
  const [firstName, setFirstName] = useState(
    pageState?.formData?.firstName || user?.first_name || ""
  );
  const [lastName, setLastName] = useState(
    pageState?.formData?.lastName || user?.last_name || ""
  );
  const [username, setUsername] = useState(
    pageState?.formData?.username || user?.username || ""
  );
  const [bio, setBio] = useState(
    pageState?.formData?.bio || user?.bio || ""
  );
  const [jobTitle, setJobTitle] = useState(
    pageState?.formData?.jobTitle || user?.job_title || ""
  );
  const [company, setCompany] = useState(
    pageState?.formData?.company || user?.company || ""
  );
  const [website, setWebsite] = useState(
    pageState?.formData?.website || user?.website || ""
  );
  const [location, setLocation] = useState(
    pageState?.formData?.location || user?.location || ""
  );

  // Password change state
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");

  // Update user profile when user data changes (only if no page state exists)
  useEffect(() => {
    if (user && !pageState?.formData) {
      setFirstName(user.first_name || "");
      setLastName(user.last_name || "");
      setUsername(user.username || "");
      setBio(user.bio || "");
      setJobTitle(user.job_title || "");
      setCompany(user.company || "");
      setWebsite(user.website || "");
      setLocation(user.location || "");
      setTheme(user.theme_preference || "light");
    }
  }, [user, pageState?.formData]);

  // Save page state when form values change (with debouncing to prevent infinite loops)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const hasUnsavedChanges =
        firstName !== (user?.first_name || "") ||
        lastName !== (user?.last_name || "") ||
        username !== (user?.username || "") ||
        bio !== (user?.bio || "") ||
        jobTitle !== (user?.job_title || "") ||
        company !== (user?.company || "") ||
        website !== (user?.website || "") ||
        location !== (user?.location || "") ||
        theme !== (user?.theme_preference || "light");

      setPageState({
        activeTab,
        theme,
        notifications: {
          email: notificationsEnabled,
        },
        formData: {
          firstName,
          lastName,
          username,
          bio,
          jobTitle,
          company,
          website,
          location,
          emailFrequency,
        },
        unsavedChanges: hasUnsavedChanges,
      });
    }, 100); // Debounce to prevent infinite loops

    return () => clearTimeout(timeoutId);
  }, [
    activeTab,
    theme,
    notificationsEnabled,
    emailFrequency,
    firstName,
    lastName,
    username,
    bio,
    jobTitle,
    company,
    website,
    location,
    user?.first_name,
    user?.last_name,
    user?.username,
    user?.bio,
    user?.job_title,
    user?.company,
    user?.website,
    user?.location,
    user?.theme_preference,
    // Removed setPageState from dependencies to prevent loops
  ]);

  // Save general settings
  const handleSaveSettings = async () => {
    try {
      // Update theme preference
      await updateUser({ theme_preference: theme });

      // Clear unsaved changes flag
      setPageState({
        ...(pageState || {}),
        unsavedChanges: false,
      });

      toast.success("Settings saved successfully!");
    } catch (error) {
      toast.error("Failed to save settings");
    }
  };

  // Save profile settings
  const handleSaveProfile = async () => {
    try {
      await updateUser({
        first_name: firstName,
        last_name: lastName,
        username,
        bio,
        job_title: jobTitle,
        company,
        website,
        location,
      });

      // Clear unsaved changes flag
      setPageState({
        ...(pageState || {}),
        unsavedChanges: false,
      });

      toast.success("Profile updated successfully!");
    } catch (error) {
      toast.error("Failed to update profile");
    }
  };

  // Change password
  const handleChangePassword = async () => {
    // Reset error
    setPasswordError("");

    // Validate passwords
    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords do not match");
      return;
    }

    if (newPassword.length < 8) {
      setPasswordError("Password must be at least 8 characters long");
      return;
    }

    try {
      // Call API to change password
      const response = await fetch("/api/auth/change-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          current_password: currentPassword,
          new_password: newPassword,
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.detail || "Failed to change password");
      }

      // Clear password fields
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");

      toast.success("Password changed successfully!");
    } catch (error) {
      setPasswordError(error instanceof Error ? error.message : "Failed to change password");
    }
  };

  const handleLogout = () => {
    logout();
    // The logout function in AuthContext will handle redirection
  };

  const settingsTabs = [
    {
      id: "general",
      name: "General Settings",
      icon: LayoutDashboard,
    },
    {
      id: "notifications",
      name: "Notification Preferences",
      icon: Bell,
    },
    {
      id: "account",
      name: "Account Management",
      icon: User,
    },
    {
      id: "appearance",
      name: "Theme Settings",
      icon: Moon,
    },
    {
      id: "llm",
      name: "AI Providers",
      icon: Bot,
    },
  ];

  // Get navbar state for responsive positioning
  const isNavbarExpanded = shouldShowExpanded();
  const navbarWidth = getNavbarWidth();

  return (
    <div className="flex h-screen bg-gray-50">
      <LeftNavbar />

      <motion.div
        className="flex-1 overflow-auto p-6"
        initial={false}
        animate={{
          marginLeft: navbarWidth,
          width: `calc(100% - ${navbarWidth}px)`
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold">Settings</h1>
              {pageState?.unsavedChanges && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Unsaved Changes
                </span>
              )}
            </div>
            <Button
              variant="outline"
              className="gap-2 text-red-500 hover:text-red-600 hover:bg-red-50 border-red-200"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Settings Menu */}
            <Card className="md:col-span-1">
              <CardContent className="p-0">
                <nav className="flex flex-col">
                  {settingsTabs.map((tab) => (
                    <button
                      key={tab.id}
                      className={`flex items-center justify-between p-4 text-left hover:bg-gray-100 transition-colors ${
                        activeTab === tab.id
                          ? "bg-gray-100 border-l-4 border-primary"
                          : ""
                      }`}
                      onClick={() => setActiveTab(tab.id)}
                    >
                      <div className="flex items-center gap-3">
                        <tab.icon className="h-5 w-5 text-gray-500" />
                        <span>{tab.name}</span>
                      </div>
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    </button>
                  ))}
                </nav>
              </CardContent>
            </Card>

            {/* Settings Content */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>
                  {settingsTabs.find((tab) => tab.id === activeTab)?.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {activeTab === "general" && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="font-medium">Default Dashboard</label>
                      <Select defaultValue="analytics">
                        <SelectTrigger>
                          <SelectValue placeholder="Select default dashboard" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="analytics">Analytics</SelectItem>
                          <SelectItem value="sales">Sales</SelectItem>
                          <SelectItem value="marketing">Marketing</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <label className="font-medium">Language</label>
                      <Select defaultValue="en">
                        <SelectTrigger>
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="es">Spanish</SelectItem>
                          <SelectItem value="fr">French</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {activeTab === "notifications" && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Email Notifications</h3>
                        <p className="text-sm text-gray-500">
                          Receive emails about your account activity
                        </p>
                      </div>
                      <div className="flex items-center h-6">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          checked={notificationsEnabled}
                          onChange={() =>
                            setNotificationsEnabled(!notificationsEnabled)
                          }
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="font-medium">Email Frequency</label>
                      <Select
                        value={emailFrequency}
                        onValueChange={setEmailFrequency}
                        disabled={!notificationsEnabled}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="immediate">Immediate</SelectItem>
                          <SelectItem value="daily">Daily Digest</SelectItem>
                          <SelectItem value="weekly">Weekly Summary</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {activeTab === "account" && (
                  <div className="space-y-6">
                    {/* Profile Section */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={user?.profile_picture} />
                          <AvatarFallback>{user?.first_name?.[0]}{user?.last_name?.[0]}</AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="text-lg font-medium">{user?.first_name} {user?.last_name}</h3>
                          <p className="text-sm text-gray-500">{user?.email}</p>
                          {user?.is_verified ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Verified
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Unverified
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm text-gray-500">Email</label>
                          <p>{user?.email}</p>
                        </div>
                        <div>
                          <label className="text-sm text-gray-500">
                            Member Since
                          </label>
                          <p>{new Date(user?.created_at || "").toLocaleDateString()}</p>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Profile Edit Section */}
                    <div className="space-y-4">
                      <h3 className="font-medium">Edit Profile</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="firstName">First Name</Label>
                          <Input
                            id="firstName"
                            value={firstName}
                            onChange={(e) => setFirstName(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="lastName">Last Name</Label>
                          <Input
                            id="lastName"
                            value={lastName}
                            onChange={(e) => setLastName(e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="username">Username</Label>
                        <Input
                          id="username"
                          value={username}
                          onChange={(e) => setUsername(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bio">Bio</Label>
                        <Textarea
                          id="bio"
                          value={bio}
                          onChange={(e) => setBio(e.target.value)}
                          rows={3}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="jobTitle">Job Title</Label>
                          <Input
                            id="jobTitle"
                            value={jobTitle}
                            onChange={(e) => setJobTitle(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="company">Company</Label>
                          <Input
                            id="company"
                            value={company}
                            onChange={(e) => setCompany(e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="website">Website</Label>
                          <Input
                            id="website"
                            value={website}
                            onChange={(e) => setWebsite(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="location">Location</Label>
                          <Input
                            id="location"
                            value={location}
                            onChange={(e) => setLocation(e.target.value)}
                          />
                        </div>
                      </div>

                      <Button onClick={handleSaveProfile}>
                        <Save className="mr-2 h-4 w-4" />
                        Save Profile
                      </Button>
                    </div>

                    <Separator />

                    {/* Password Change Section */}
                    <div className="space-y-4">
                      <h3 className="font-medium">Change Password</h3>

                      {passwordError && (
                        <div className="bg-red-50 text-red-500 p-3 rounded-md text-sm">
                          {passwordError}
                        </div>
                      )}

                      <div className="space-y-2">
                        <Label htmlFor="currentPassword">Current Password</Label>
                        <Input
                          id="currentPassword"
                          type="password"
                          value={currentPassword}
                          onChange={(e) => setCurrentPassword(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="newPassword">New Password</Label>
                        <Input
                          id="newPassword"
                          type="password"
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword">Confirm New Password</Label>
                        <Input
                          id="confirmPassword"
                          type="password"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                        />
                      </div>

                      <Button onClick={handleChangePassword}>
                        Change Password
                      </Button>
                    </div>

                    <Separator />

                    {/* Account Deletion */}
                    <div className="space-y-4">
                      <h3 className="font-medium text-red-600">Danger Zone</h3>
                      <p className="text-sm text-gray-500">
                        Once you delete your account, there is no going back. Please be certain.
                      </p>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="destructive">Delete Account</Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Delete Account</DialogTitle>
                            <DialogDescription>
                              Are you sure you want to delete your account? This action cannot be undone.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <Button variant="outline">Cancel</Button>
                            <Button variant="destructive">Delete Account</Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                )}

                {activeTab === "appearance" && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="font-medium">Theme</label>
                      <Select
                        value={theme}
                        onValueChange={setTheme}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select theme" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                          <SelectItem value="system">System Default</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {activeTab === "llm" && (
                  <LLMProviderSettings />
                )}

                <div className="mt-6">
                  <Button
                    className="w-full sm:w-auto"
                    onClick={handleSaveSettings}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Settings;
