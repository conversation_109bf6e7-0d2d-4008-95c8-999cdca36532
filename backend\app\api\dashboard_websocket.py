"""
Dashboard WebSocket API for real-time updates.

This module provides WebSocket endpoints for real-time dashboard updates,
including widget data updates, layout changes, and data source notifications.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Set, Optional, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query, HTTPException
from sqlalchemy.orm import Session

from app.database import get_db
from app.auth import get_current_user_from_token
from app.models.dashboard_customization import Dashboard

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ws", tags=["dashboard_websocket"])

# Additional router for the alternative endpoint path
dashboard_router = APIRouter(tags=["dashboard_websocket"])


class DashboardConnectionManager:
    """Manages WebSocket connections for dashboard real-time updates."""
    
    def __init__(self):
        # Dashboard ID -> Set of WebSocket connections
        self.dashboard_connections: Dict[str, Set[WebSocket]] = {}
        # WebSocket -> Dashboard ID mapping for cleanup
        self.connection_dashboard_map: Dict[WebSocket, str] = {}
        # User ID -> Set of WebSocket connections
        self.user_connections: Dict[str, Set[WebSocket]] = {}
        # WebSocket -> User ID mapping
        self.connection_user_map: Dict[WebSocket, str] = {}

    async def connect(self, websocket: WebSocket, dashboard_id: str, user_id: str):
        """Connect a WebSocket to a dashboard."""
        await websocket.accept()
        
        # Add to dashboard connections
        if dashboard_id not in self.dashboard_connections:
            self.dashboard_connections[dashboard_id] = set()
        self.dashboard_connections[dashboard_id].add(websocket)
        self.connection_dashboard_map[websocket] = dashboard_id
        
        # Add to user connections
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(websocket)
        self.connection_user_map[websocket] = user_id
        
        logger.info(f"WebSocket connected for dashboard {dashboard_id}, user {user_id}")
        
        # Send connection confirmation
        await websocket.send_json({
            "type": "connection_status",
            "status": "connected",
            "dashboard_id": dashboard_id,
            "timestamp": datetime.now().isoformat()
        })

    def disconnect(self, websocket: WebSocket):
        """Disconnect a WebSocket."""
        dashboard_id = self.connection_dashboard_map.get(websocket)
        user_id = self.connection_user_map.get(websocket)
        
        if dashboard_id and websocket in self.dashboard_connections.get(dashboard_id, set()):
            self.dashboard_connections[dashboard_id].discard(websocket)
            if not self.dashboard_connections[dashboard_id]:
                del self.dashboard_connections[dashboard_id]
            del self.connection_dashboard_map[websocket]
        
        if user_id and websocket in self.user_connections.get(user_id, set()):
            self.user_connections[user_id].discard(websocket)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
            del self.connection_user_map[websocket]
        
        logger.info(f"WebSocket disconnected for dashboard {dashboard_id}, user {user_id}")

    async def send_to_dashboard(self, dashboard_id: str, message: Dict[str, Any]):
        """Send a message to all connections for a specific dashboard."""
        if dashboard_id in self.dashboard_connections:
            disconnected = []
            for websocket in self.dashboard_connections[dashboard_id]:
                try:
                    await websocket.send_json(message)
                except Exception as e:
                    logger.error(f"Error sending message to WebSocket: {e}")
                    disconnected.append(websocket)
            
            # Clean up disconnected WebSockets
            for websocket in disconnected:
                self.disconnect(websocket)

    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """Send a message to all connections for a specific user."""
        if user_id in self.user_connections:
            disconnected = []
            for websocket in self.user_connections[user_id]:
                try:
                    await websocket.send_json(message)
                except Exception as e:
                    logger.error(f"Error sending message to WebSocket: {e}")
                    disconnected.append(websocket)
            
            # Clean up disconnected WebSockets
            for websocket in disconnected:
                self.disconnect(websocket)

    async def broadcast_dashboard_update(self, dashboard_id: str, update_type: str, data: Any):
        """Broadcast a dashboard update to all connected clients."""
        message = {
            "type": "dashboard_update",
            "payload": {
                "dashboard_id": dashboard_id,
                "update_type": update_type,
                "data": data
            },
            "timestamp": datetime.now().isoformat()
        }
        await self.send_to_dashboard(dashboard_id, message)

    async def broadcast_widget_update(self, dashboard_id: str, widget_id: str, data: Any, error: Optional[str] = None):
        """Broadcast a widget data update."""
        message = {
            "type": "widget_data_update",
            "payload": {
                "widget_id": widget_id,
                "dashboard_id": dashboard_id,
                "data": data,
                "error": error
            },
            "timestamp": datetime.now().isoformat()
        }
        await self.send_to_dashboard(dashboard_id, message)

    async def broadcast_data_source_update(self, data_source_id: str, dashboard_ids: list, update_type: str, data: Any):
        """Broadcast a data source update to relevant dashboards."""
        message = {
            "type": "data_source_update",
            "payload": {
                "data_source_id": data_source_id,
                "dashboard_ids": dashboard_ids,
                "update_type": update_type,
                "data": data
            },
            "timestamp": datetime.now().isoformat()
        }
        
        for dashboard_id in dashboard_ids:
            await self.send_to_dashboard(dashboard_id, message)


# Global connection manager instance
dashboard_manager = DashboardConnectionManager()


@router.websocket("/dashboard")
async def dashboard_websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(...),
    dashboard_id: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """
    WebSocket endpoint for dashboard real-time updates.
    
    Args:
        websocket: WebSocket connection
        token: JWT token for authentication
        dashboard_id: Optional dashboard ID to subscribe to
    """
    try:
        # Authenticate user
        try:
            current_user = await get_current_user_from_token(token, db)
            if not current_user:
                logger.warning("WebSocket authentication failed: invalid token")
                await websocket.close(code=1008, reason="Authentication failed")
                return
        except Exception as auth_error:
            logger.error(f"WebSocket authentication error: {auth_error}")
            await websocket.close(code=1008, reason="Authentication failed")
            return
        
        # If dashboard_id is provided, verify user has access
        if dashboard_id:
            dashboard = db.query(Dashboard).filter(
                Dashboard.id == dashboard_id,
                Dashboard.user_id == current_user.id
            ).first()
            
            if not dashboard:
                await websocket.close(code=1008, reason="Dashboard not found or access denied")
                return
        else:
            # Use a default dashboard ID for general updates
            dashboard_id = f"user_{current_user.id}"
        
        # Connect to WebSocket
        await dashboard_manager.connect(websocket, dashboard_id, str(current_user.id))
        
        try:
            while True:
                # Receive and handle messages from client
                data = await websocket.receive_json()
                await handle_client_message(websocket, data, dashboard_id, str(current_user.id))
                
        except WebSocketDisconnect:
            dashboard_manager.disconnect(websocket)
            logger.info(f"Dashboard WebSocket disconnected for user {current_user.id}")
        except Exception as e:
            logger.error(f"Error in dashboard WebSocket: {e}")
            dashboard_manager.disconnect(websocket)
            
    except Exception as e:
        logger.error(f"Error in dashboard WebSocket endpoint: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass


async def handle_client_message(websocket: WebSocket, data: Dict[str, Any], dashboard_id: str, user_id: str):
    """Handle messages from WebSocket clients."""
    message_type = data.get("type")
    
    if message_type == "heartbeat":
        await websocket.send_json({
            "type": "heartbeat_response",
            "timestamp": datetime.now().isoformat()
        })
    elif message_type == "subscribe_dashboard":
        # Handle dashboard subscription
        target_dashboard_id = data.get("payload", {}).get("dashboard_id")
        if target_dashboard_id:
            logger.info(f"User {user_id} subscribing to dashboard {target_dashboard_id}")
    elif message_type == "subscribe_data_source":
        # Handle data source subscription
        data_source_id = data.get("payload", {}).get("data_source_id")
        if data_source_id:
            logger.info(f"User {user_id} subscribing to data source {data_source_id}")
    elif message_type == "refresh_widget":
        # Handle widget refresh request
        widget_id = data.get("payload", {}).get("widget_id")
        if widget_id:
            logger.info(f"User {user_id} requesting refresh for widget {widget_id}")
            # Here you would trigger widget data refresh
    else:
        logger.warning(f"Unknown message type: {message_type}")


@dashboard_router.websocket("/dashboard/ws")
async def dashboard_websocket_endpoint_alt(
    websocket: WebSocket,
    token: str = Query(...),
    dashboard_id: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Alternative WebSocket endpoint for dashboard real-time updates.
    This endpoint matches the frontend expectation of /dashboard/ws.
    """
    # Delegate to the main endpoint
    await dashboard_websocket_endpoint(websocket, token, dashboard_id, db)


# Export the manager and routers for use in other modules
__all__ = ["dashboard_manager", "router", "dashboard_router"]
