/**
 * Global Business Profile Store
 * 
 * Centralized state management for business profiles across the entire application.
 * Provides persistent storage, profile switching, and change notifications.
 */

import React from 'react';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { BusinessProfile, businessProfileApi, BusinessProfileWithDataSources } from '@/lib/businessProfileApi';

// Event emitter for profile change notifications
class BusinessProfileEventEmitter {
  private listeners: Set<(profile: BusinessProfile | null) => void> = new Set();

  subscribe(callback: (profile: BusinessProfile | null) => void) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  emit(profile: BusinessProfile | null) {
    this.listeners.forEach(callback => {
      try {
        callback(profile);
      } catch (error) {
        console.error('Error in business profile change listener:', error);
      }
    });
  }
}

// Global event emitter instance
export const businessProfileEventEmitter = new BusinessProfileEventEmitter();

interface BusinessProfileStore {
  // State
  profiles: BusinessProfile[];
  activeProfile: BusinessProfile | null;
  profileWithDataSources: BusinessProfileWithDataSources | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number | null;

  // Actions
  loadProfiles: () => Promise<void>;
  switchProfile: (profileId: string) => Promise<void>;
  createProfile: (profileData: any) => Promise<BusinessProfile>;
  deleteProfile: (profileId: string) => Promise<void>;
  updateProfile: (profileId: string, updateData: any) => Promise<BusinessProfile>;
  refreshProfiles: () => Promise<void>;
  clearError: () => void;
  reset: () => void;

  // Getters
  getProfileById: (profileId: string) => BusinessProfile | null;
  hasProfiles: () => boolean;
  isProfileActive: (profileId: string) => boolean;
}

const initialState = {
  profiles: [],
  activeProfile: null,
  profileWithDataSources: null,
  isLoading: false,
  error: null,
  lastUpdated: null,
};

export const useBusinessProfileStore = create<BusinessProfileStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      loadProfiles: async () => {
        try {
          set({ isLoading: true, error: null });
          
          console.log('🔍 Loading business profiles...');
          const response = await businessProfileApi.listProfiles();
          console.log('🔍 Loaded profiles:', response);

          // Ensure profiles is an array
          const profilesArray = Array.isArray(response.profiles) ? response.profiles : [];
          
          // Find active profile
          const active = profilesArray.find(p => p.is_active) || null;
          console.log('🔍 Active profile:', active);

          // Load profile with data sources if active profile exists
          let profileWithSources = null;
          if (active) {
            try {
              profileWithSources = await businessProfileApi.getProfileWithDataSources(active.id);
            } catch (sourcesError) {
              console.error('Error loading profile data sources:', sourcesError);
            }
          }

          set({
            profiles: profilesArray,
            activeProfile: active,
            profileWithDataSources: profileWithSources,
            isLoading: false,
            lastUpdated: Date.now(),
          });

          // Emit profile change event
          businessProfileEventEmitter.emit(active);

        } catch (error) {
          console.error('Error loading business profiles:', error);
          set({
            profiles: [],
            activeProfile: null,
            profileWithDataSources: null,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to load profiles',
          });

          // Emit null profile on error
          businessProfileEventEmitter.emit(null);
        }
      },

      switchProfile: async (profileId: string) => {
        try {
          set({ isLoading: true, error: null });
          
          await businessProfileApi.switchActiveProfile(profileId);
          
          // Reload all profiles to get updated state
          await get().loadProfiles();
          
        } catch (error) {
          console.error('Error switching profile:', error);
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to switch profile',
          });
          throw error;
        }
      },

      createProfile: async (profileData: any) => {
        try {
          set({ isLoading: true, error: null });
          
          const newProfile = await businessProfileApi.createProfile(profileData);
          
          // Reload profiles after creation
          await get().loadProfiles();
          
          return newProfile;
        } catch (error) {
          console.error('Error creating profile:', error);
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to create profile',
          });
          throw error;
        }
      },

      deleteProfile: async (profileId: string) => {
        try {
          set({ isLoading: true, error: null });
          
          await businessProfileApi.deleteProfile(profileId);
          
          // Reload profiles after deletion
          await get().loadProfiles();
          
        } catch (error) {
          console.error('Error deleting profile:', error);
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to delete profile',
          });
          throw error;
        }
      },

      updateProfile: async (profileId: string, updateData: any) => {
        try {
          set({ isLoading: true, error: null });
          
          const updatedProfile = await businessProfileApi.updateProfile(profileId, updateData);
          
          // Reload profiles after update
          await get().loadProfiles();
          
          return updatedProfile;
        } catch (error) {
          console.error('Error updating profile:', error);
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to update profile',
          });
          throw error;
        }
      },

      refreshProfiles: async () => {
        await get().loadProfiles();
      },

      clearError: () => {
        set({ error: null });
      },

      reset: () => {
        set(initialState);
      },

      // Getters
      getProfileById: (profileId: string) => {
        const { profiles } = get();
        return profiles.find(p => p.id === profileId) || null;
      },

      hasProfiles: () => {
        const { profiles } = get();
        return profiles.length > 0;
      },

      isProfileActive: (profileId: string) => {
        const { activeProfile } = get();
        return activeProfile?.id === profileId;
      },
    }),
    {
      name: 'business-profile-store',
      version: 1,
      // Only persist essential data, not loading states
      partialize: (state) => ({
        profiles: state.profiles,
        activeProfile: state.activeProfile,
        lastUpdated: state.lastUpdated,
      }),
    }
  )
);

// Hook for subscribing to profile changes
export const useBusinessProfileChanges = (callback: (profile: BusinessProfile | null) => void) => {
  const callbackRef = React.useRef(callback);
  callbackRef.current = callback;

  React.useEffect(() => {
    return businessProfileEventEmitter.subscribe((profile) => callbackRef.current(profile));
  }, []); // Empty dependency array to prevent infinite loops
};

// Export types for use in other components
export type { BusinessProfileStore };
